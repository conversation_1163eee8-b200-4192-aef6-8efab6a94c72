import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { CreditCard, Clock, TrendingUp, AlertCircle, Plus, Search, Filter, Calendar, DollarSign, Users, Package } from 'lucide-react';
import { Card } from '../../components/common/Card';
import Button from '../../components/common/Button';
import { Input } from '../../components/common/Input';
import { Badge } from '../../components/common/Badge';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import { EmptyState } from '../../components/common/EmptyState';
import { subscriptionService, Subscription, SubscriptionPlan, SubscriptionStatus } from '../../services/subscriptionService';
import { useAuth } from '../../contexts/AuthContext';
import { UserRoleType } from '../../types/auth';
import { hasPermission } from '../../utils/permissions';
import toast from 'react-hot-toast';
import { format } from 'date-fns';
import SubscriptionDetailsModal from '../../components/modals/SubscriptionDetailsModal';
import AddSubscriptionModal from '../../components/modals/AddSubscriptionModal';
import ManagePlansModal from '../../components/modals/ManagePlansModal';

const Subscriptions: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [stats, setStats] = useState<any>(null);
  const [selectedSubscription, setSelectedSubscription] = useState<Subscription | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showPlansModal, setShowPlansModal] = useState(false);
  const [filters, setFilters] = useState({
    search: '',
    status: 'all',
    dateRange: {
      start: '',
      end: ''
    }
  });
  const [showFilters, setShowFilters] = useState(false);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);

  // Permissions
  const isManager = user?.activeRole === UserRoleType.MANAGER;
  const isClient = user?.activeRole === UserRoleType.CLIENT;
  const canManageSubscriptions = hasPermission(user?.activeRole, 'manageSubscriptions');
  const canViewAllSubscriptions = hasPermission(user?.activeRole, 'viewAllSubscriptions');

  useEffect(() => {
    loadData();
  }, [page, filters]);

  const loadData = async () => {
    setLoading(true);
    try {
      // Load subscriptions based on role
      if (isClient && user?.userId) {
        // Clients only see their own subscriptions
        const clientSubs = await subscriptionService.getClientSubscriptions(user.userId);
        setSubscriptions(clientSubs);
        setTotalItems(clientSubs.length);
        setTotalPages(1);
      } else if (canViewAllSubscriptions) {
        // Managers see all subscriptions
        const params = {
          search: filters.search || undefined,
          status: filters.status === 'all' ? undefined : filters.status as SubscriptionStatus,
          start_date: filters.dateRange.start || undefined,
          end_date: filters.dateRange.end || undefined,
          page,
          limit: 20
        };
        const response = await subscriptionService.getSubscriptions(params);
        setSubscriptions(response.items);
        setTotalItems(response.total);
        setTotalPages(response.total_pages);
      }

      // Load stats for managers
      if (isManager) {
        const statsData = await subscriptionService.getSubscriptionStats();
        setStats(statsData);
      }
    } catch (error) {
      console.error('Error loading subscriptions:', error);
      toast.error(t('billing.subscriptions.loadError'));
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value }));
    setPage(1);
  };

  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPage(1);
  };

  const handleViewDetails = (subscription: Subscription) => {
    setSelectedSubscription(subscription);
    setShowDetailsModal(true);
  };

  const handleCancelSubscription = async (subscription: Subscription) => {
    if (!confirm(t('billing.subscriptions.confirmCancel'))) return;

    try {
      await subscriptionService.cancelSubscription(subscription.subscription_id);
      toast.success(t('billing.subscriptions.cancelSuccess'));
      loadData();
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      toast.error(t('billing.subscriptions.cancelError'));
    }
  };

  const handlePauseSubscription = async (subscription: Subscription) => {
    try {
      await subscriptionService.pauseSubscription(subscription.subscription_id);
      toast.success(t('billing.subscriptions.pauseSuccess'));
      loadData();
    } catch (error) {
      console.error('Error pausing subscription:', error);
      toast.error(t('billing.subscriptions.pauseError'));
    }
  };

  const handleResumeSubscription = async (subscription: Subscription) => {
    try {
      await subscriptionService.resumeSubscription(subscription.subscription_id);
      toast.success(t('billing.subscriptions.resumeSuccess'));
      loadData();
    } catch (error) {
      console.error('Error resuming subscription:', error);
      toast.error(t('billing.subscriptions.resumeError'));
    }
  };

  const handleAddComplete = () => {
    setShowAddModal(false);
    loadData();
    toast.success(t('billing.subscriptions.addSuccess'));
  };

  const getStatusBadge = (status: SubscriptionStatus) => {
    switch (status) {
      case SubscriptionStatus.ACTIVE:
        return <Badge variant="success" size="sm">{t('billing.subscriptions.status.active')}</Badge>;
      case SubscriptionStatus.PAUSED:
        return <Badge variant="warning" size="sm">{t('billing.subscriptions.status.paused')}</Badge>;
      case SubscriptionStatus.CANCELLED:
        return <Badge variant="danger" size="sm">{t('billing.subscriptions.status.cancelled')}</Badge>;
      case SubscriptionStatus.EXPIRED:
        return <Badge variant="secondary" size="sm">{t('billing.subscriptions.status.expired')}</Badge>;
      default:
        return null;
    }
  };

  const renderSubscriptionCard = (subscription: Subscription) => (
    <Card key={subscription.subscription_id} className="hover:shadow-md transition-shadow cursor-pointer">
      <div className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{subscription.client_name}</h3>
            <p className="text-sm text-gray-600">{subscription.plan_name}</p>
          </div>
          {getStatusBadge(subscription.status)}
        </div>

        <div className="space-y-3">
          {/* Hours Info */}
          <div className="flex items-center justify-between">
            <div className="flex items-center text-gray-600">
              <Clock className="w-5 h-5 mr-2" />
              <span className="text-sm">{t('billing.subscriptions.hoursRemaining')}</span>
            </div>
            <div className="font-semibold">
              {subscription.hours_remaining} / {subscription.hours_purchased}
            </div>
          </div>

          {/* Progress Bar */}
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-accent-red h-2 rounded-full transition-all"
              style={{
                width: `${(subscription.hours_used / subscription.hours_purchased) * 100}%`
              }}
            />
          </div>

          {/* Next Billing */}
          {subscription.status === SubscriptionStatus.ACTIVE && subscription.next_billing_date && (
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">{t('billing.subscriptions.nextBilling')}</span>
              <span className="font-medium">{format(new Date(subscription.next_billing_date), 'PPP')}</span>
            </div>
          )}

          {/* Amount */}
          <div className="flex items-center justify-between">
            <span className="text-gray-600 text-sm">{t('billing.subscriptions.amount')}</span>
            <span className="font-semibold text-lg">
              ${subscription.amount} {subscription.currency}
            </span>
          </div>
        </div>

        {/* Actions */}
        <div className="mt-4 flex items-center justify-between">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleViewDetails(subscription)}
          >
            {t('common.viewDetails')}
          </Button>
          
          {canManageSubscriptions && (
            <div className="flex items-center space-x-2">
              {subscription.status === SubscriptionStatus.ACTIVE && (
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    handlePauseSubscription(subscription);
                  }}
                >
                  {t('billing.subscriptions.pause')}
                </Button>
              )}
              {subscription.status === SubscriptionStatus.PAUSED && (
                <Button
                  variant="primary"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleResumeSubscription(subscription);
                  }}
                >
                  {t('billing.subscriptions.resume')}
                </Button>
              )}
              {(subscription.status === SubscriptionStatus.ACTIVE || subscription.status === SubscriptionStatus.PAUSED) && (
                <Button
                  variant="danger"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCancelSubscription(subscription);
                  }}
                >
                  {t('billing.subscriptions.cancel')}
                </Button>
              )}
            </div>
          )}
        </div>
      </div>
    </Card>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">{t('billing.subscriptions.title')}</h1>
        <p className="text-gray-600">{t('billing.subscriptions.subtitle')}</p>
      </div>

      {/* Stats Cards (Manager Only) */}
      {isManager && stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-blue-100 rounded-lg">
                  <Users className="w-6 h-6 text-blue-600" />
                </div>
                <TrendingUp className="w-5 h-5 text-green-500" />
              </div>
              <div className="text-2xl font-bold text-gray-900">{stats.active_subscriptions}</div>
              <div className="text-sm text-gray-600">{t('billing.subscriptions.stats.activeSubscriptions')}</div>
            </div>
          </Card>

          <Card>
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-green-100 rounded-lg">
                  <DollarSign className="w-6 h-6 text-green-600" />
                </div>
              </div>
              <div className="text-2xl font-bold text-gray-900">${stats.total_revenue}</div>
              <div className="text-sm text-gray-600">{t('billing.subscriptions.stats.totalRevenue')}</div>
            </div>
          </Card>

          <Card>
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-purple-100 rounded-lg">
                  <Clock className="w-6 h-6 text-purple-600" />
                </div>
              </div>
              <div className="text-2xl font-bold text-gray-900">{stats.total_hours_used}</div>
              <div className="text-sm text-gray-600">{t('billing.subscriptions.stats.hoursUsed')}</div>
            </div>
          </Card>

          <Card>
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-orange-100 rounded-lg">
                  <TrendingUp className="w-6 h-6 text-orange-600" />
                </div>
              </div>
              <div className="text-2xl font-bold text-gray-900">{stats.average_usage_rate}%</div>
              <div className="text-sm text-gray-600">{t('billing.subscriptions.stats.usageRate')}</div>
            </div>
          </Card>
        </div>
      )}

      {/* Actions Bar */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <div className="flex items-center gap-4 w-full sm:w-auto">
          {/* Search */}
          <div className="relative flex-1 sm:flex-initial">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <Input
              type="text"
              placeholder={t('billing.subscriptions.search.placeholder')}
              value={filters.search}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10 pr-4 w-full sm:w-80"
            />
          </div>

          {/* Filters */}
          <Button
            variant="secondary"
            onClick={() => setShowFilters(!showFilters)}
            className="relative"
          >
            <Filter className="w-4 h-4 mr-2" />
            {t('common.filters')}
            {(filters.status !== 'all' || filters.dateRange.start || filters.dateRange.end) && (
              <div className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></div>
            )}
          </Button>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center space-x-3">
          {isManager && (
            <Button variant="secondary" onClick={() => setShowPlansModal(true)}>
              <Package className="w-4 h-4 mr-2" />
              {t('billing.subscriptions.managePlans')}
            </Button>
          )}
          {canManageSubscriptions && (
            <Button variant="primary" onClick={() => setShowAddModal(true)}>
              <Plus className="w-4 h-4 mr-2" />
              {t('billing.subscriptions.add')}
            </Button>
          )}
        </div>
      </div>

      {/* Filters Panel */}
      {showFilters && (
        <Card className="mb-6 animate-fadeInDown">
          <div className="p-4">
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('billing.subscriptions.filters.status')}
                </label>
                <select
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-red"
                >
                  <option value="all">{t('common.all')}</option>
                  <option value={SubscriptionStatus.ACTIVE}>{t('billing.subscriptions.status.active')}</option>
                  <option value={SubscriptionStatus.PAUSED}>{t('billing.subscriptions.status.paused')}</option>
                  <option value={SubscriptionStatus.CANCELLED}>{t('billing.subscriptions.status.cancelled')}</option>
                  <option value={SubscriptionStatus.EXPIRED}>{t('billing.subscriptions.status.expired')}</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('billing.subscriptions.filters.startDate')}
                </label>
                <Input
                  type="date"
                  value={filters.dateRange.start}
                  onChange={(e) => handleFilterChange('dateRange', { ...filters.dateRange, start: e.target.value })}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('billing.subscriptions.filters.endDate')}
                </label>
                <Input
                  type="date"
                  value={filters.dateRange.end}
                  onChange={(e) => handleFilterChange('dateRange', { ...filters.dateRange, end: e.target.value })}
                />
              </div>
            </div>

            <div className="mt-4 flex justify-end">
              <Button
                variant="ghost"
                onClick={() => {
                  setFilters({
                    search: '',
                    status: 'all',
                    dateRange: { start: '', end: '' }
                  });
                  setPage(1);
                }}
              >
                {t('common.clearFilters')}
              </Button>
            </div>
          </div>
        </Card>
      )}

      {/* Subscriptions Grid */}
      {subscriptions.length === 0 ? (
        <EmptyState
          icon={<CreditCard className="w-12 h-12" />}
          title={t('billing.subscriptions.empty.title')}
          message={t('billing.subscriptions.empty.message')}
          action={
            canManageSubscriptions ? (
              <Button variant="primary" onClick={() => setShowAddModal(true)}>
                <Plus className="w-4 h-4 mr-2" />
                {t('billing.subscriptions.add')}
              </Button>
            ) : undefined
          }
        />
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {subscriptions.map(renderSubscriptionCard)}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="mt-8 flex items-center justify-center space-x-2">
              <Button
                variant="secondary"
                size="sm"
                onClick={() => setPage(page - 1)}
                disabled={page === 1}
              >
                {t('common.previous')}
              </Button>
              <span className="text-sm text-gray-700">
                {t('common.page')} {page} {t('common.of')} {totalPages}
              </span>
              <Button
                variant="secondary"
                size="sm"
                onClick={() => setPage(page + 1)}
                disabled={page === totalPages}
              >
                {t('common.next')}
              </Button>
            </div>
          )}
        </>
      )}

      {/* Modals */}
      {showDetailsModal && selectedSubscription && (
        <SubscriptionDetailsModal
          isOpen={showDetailsModal}
          onClose={() => {
            setShowDetailsModal(false);
            setSelectedSubscription(null);
          }}
          subscription={selectedSubscription}
          onUpdate={loadData}
          canManage={canManageSubscriptions}
        />
      )}

      {showAddModal && (
        <AddSubscriptionModal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
          onSuccess={handleAddComplete}
        />
      )}

      {showPlansModal && (
        <ManagePlansModal
          isOpen={showPlansModal}
          onClose={() => setShowPlansModal(false)}
          onUpdate={loadData}
        />
      )}
    </div>
  );
};

export default Subscriptions;