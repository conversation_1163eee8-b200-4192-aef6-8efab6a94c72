# TutorAide Localization System

This directory contains the localization (i18n) system for TutorAide, supporting English and French languages.

## Structure

```
app/locales/
├── __init__.py              # Module initialization
├── constants.py             # Language constants and formatting rules
├── translation_service.py   # Core translation service
├── middleware.py           # FastAPI middleware for language detection
├── dependencies.py         # FastAPI dependencies for localization
├── example_integration.py  # Usage examples
├── en.json                 # English translations
├── fr.json                 # French translations
└── README.md              # This file
```

## Features

- **Dual Language Support**: English (default) and French
- **Automatic Language Detection**: From URL params, cookies, headers
- **Formatted Output**: Currency, dates, and times formatted per language
- **FastAPI Integration**: Middleware and dependencies for seamless use
- **Fallback Mechanism**: Falls back to default language if translation missing
- **Nested Keys**: Support for dotted notation (e.g., "auth.login.title")

## Usage

### Basic Translation

```python
from app.locales import t

# Get translation
message = t("common.yes", "en")  # Returns "Yes"
message = t("common.yes", "fr")  # Returns "Oui"

# With nested keys
title = t("auth.login.title", "fr")  # Returns "Connexion"
```

### In FastAPI Endpoints

```python
from fastapi import FastAPI
from app.locales.dependencies import CurrentLanguage, LocalizedMessages

@app.post("/users")
async def create_user(
    user_data: dict,
    language: CurrentLanguage,
    messages: LocalizedMessages
):
    if not user_data.get("email"):
        return {
            "error": messages.error("required_field", 
                                  field=messages.get("common.email"))
        }
    
    return {
        "success": True,
        "message": messages.success("user_created")
    }
```

### Formatting

```python
from app.locales import format_currency, format_date, format_time
from datetime import date, time

# Currency formatting
amount_en = format_currency(1234.56, "en")  # "$1,234.56"
amount_fr = format_currency(1234.56, "fr")  # "1 234,56 $"

# Date formatting
date_en = format_date(date(2024, 1, 15), "short", "en")  # "01/15/2024"
date_fr = format_date(date(2024, 1, 15), "short", "fr")  # "15/01/2024"

# Time formatting
time_en = format_time(time(14, 30), "short", "en")  # "2:30 PM"
time_fr = format_time(time(14, 30), "short", "fr")  # "14:30"
```

## Translation File Structure

The translation files (`en.json` and `fr.json`) are organized by feature areas:

```json
{
  "common": {
    "yes": "Yes",
    "no": "No",
    "save": "Save"
  },
  "auth": {
    "login": {
      "title": "Login",
      "email_placeholder": "Enter your email"
    }
  },
  "users": {
    "list": {
      "title": "Users",
      "add_user": "Add User"
    }
  }
}
```

### Main Sections

- **common**: Common UI elements (buttons, labels, etc.)
- **navigation**: Menu items and navigation
- **auth**: Authentication (login, register, password reset)
- **users**: User management
- **clients**: Client profiles and management
- **tutors**: Tutor profiles and management
- **appointments**: Appointment booking and management
- **billing**: Invoicing and payments
- **messages**: Communication features
- **reports**: Analytics and reporting
- **settings**: System configuration
- **tecfee**: TECFEE program specific
- **notifications**: System notifications
- **errors**: Error messages
- **success**: Success messages

## Language Detection

The system detects user language preference in this order:

1. **URL Parameter**: `?lang=fr`
2. **Cookie**: Previously set language preference
3. **User Database Setting**: Stored user preference (if authenticated)
4. **Accept-Language Header**: Browser language preference
5. **Default**: English (fallback)

## Adding New Languages

To add a new language (e.g., Spanish):

1. Add language code to `SUPPORTED_LANGUAGES` in `constants.py`
2. Create translation file `es.json` with same structure as `en.json`
3. Add formatting rules to `DATE_FORMATS`, `TIME_FORMATS`, etc.
4. Update language names in `LANGUAGE_NAMES`

## Adding New Translations

To add new translation keys:

1. Add the key to `en.json` (default language)
2. Add corresponding translation to all other language files
3. Use nested structure for organization
4. Follow naming conventions (lowercase, underscores)

Example:
```json
{
  "features": {
    "new_feature": {
      "title": "New Feature",
      "description": "Description of the new feature",
      "buttons": {
        "activate": "Activate",
        "deactivate": "Deactivate"
      }
    }
  }
}
```

## Testing

Run localization tests:

```bash
python -m pytest tests/unit/test_localization.py -v
```

The tests verify:
- Translation file structure consistency
- All languages have same keys
- Formatting functions work correctly
- Error handling and fallbacks
- Special characters and encoding

## Integration with FastAPI

The localization system integrates with FastAPI through:

### Middleware

```python
from app.locales.middleware import LocalizationMiddleware

app.add_middleware(LocalizationMiddleware)
```

### Dependencies

```python
from app.locales.dependencies import CurrentLanguage, LocalizedMessages

@app.get("/endpoint")
async def endpoint(
    language: CurrentLanguage,
    messages: LocalizedMessages
):
    return {"message": messages.get("common.success")}
```

## Best Practices

1. **Use Descriptive Keys**: `auth.login.invalid_credentials` not `auth.error1`
2. **Organize by Feature**: Group related translations together
3. **Consistent Naming**: Use lowercase with underscores
4. **Avoid Hardcoded Text**: Always use translation keys in code
5. **Test All Languages**: Ensure translations work in both languages
6. **Keep Keys Stable**: Don't change existing keys (breaks deployed code)
7. **Use Formatting**: For dynamic content, use formatting parameters

## Performance

The translation system is optimized for performance:

- Translation files loaded once at startup
- In-memory caching of all translations
- Fast dictionary lookups for translation keys
- Minimal overhead in request processing

## Security

- Translation keys are validated to prevent injection
- User input is never used directly as translation keys
- Language codes are validated against supported list
- Cookies use secure flags in production