import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../contexts/AuthContext';
import { Card } from '../../components/common/Card';
import Button from '../../components/common/Button';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import { Input } from '../../components/common/Input';
import { Select } from '../../components/common/Select';
import { Modal } from '../../components/common/Modal';
import { 
  FileText, 
  Download, 
  Calendar,
  DollarSign,
  Search,
  Filter,
  CheckCircle,
  Clock,
  AlertTriangle,
  Eye,
  CreditCard,
  User
} from 'lucide-react';
import api from '../../services/api';
import toast from 'react-hot-toast';
import { InvoiceList } from '../../components/client/invoices/InvoiceList';
import { InvoiceDetails } from '../../components/client/invoices/InvoiceDetails';
import { PaymentHistory } from '../../components/client/invoices/PaymentHistory';
import { BalanceSummary } from '../../components/client/invoices/BalanceSummary';

interface Invoice {
  invoice_id: number;
  invoice_number: string;
  client_id: number;
  amount: number;
  status: 'pending' | 'paid' | 'overdue' | 'cancelled';
  issue_date: string;
  due_date: string;
  paid_date: string | null;
  payment_method: string | null;
  paid_by_parent: 1 | 2 | null;
  created_at: string;
  updated_at: string;
  items?: InvoiceItem[];
}

interface InvoiceItem {
  description: string;
  quantity: number;
  rate: number;
  amount: number;
}

interface Payment {
  payment_id: number;
  invoice_id: number;
  amount: number;
  payment_date: string;
  payment_method: string;
  reference_number: string | null;
  paid_by_parent: 1 | 2 | null;
}

export const Invoices: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [payments, setPayments] = useState<Payment[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [activeTab, setActiveTab] = useState<'invoices' | 'payments'>('invoices');
  
  // Filters
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [dateRange, setDateRange] = useState('all');

  // Summary stats
  const [totalOutstanding, setTotalOutstanding] = useState(0);
  const [totalPaid, setTotalPaid] = useState(0);
  const [overdueCount, setOverdueCount] = useState(0);

  useEffect(() => {
    fetchInvoices();
    fetchPayments();
  }, []);

  const fetchInvoices = async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      const response = await api.get(`/api/v1/clients/${user.id}/invoices`);
      setInvoices(response.data);
      calculateSummary(response.data);
    } catch (error) {
      console.error('Error fetching invoices:', error);
      toast.error(t('client.invoices.fetchError'));
    } finally {
      setLoading(false);
    }
  };

  const fetchPayments = async () => {
    if (!user) return;
    
    try {
      const response = await api.get(`/api/v1/clients/${user.id}/payment-history`);
      setPayments(response.data);
    } catch (error) {
      console.error('Error fetching payments:', error);
    }
  };

  const calculateSummary = (invoiceList: Invoice[]) => {
    let outstanding = 0;
    let paid = 0;
    let overdue = 0;
    
    invoiceList.forEach(invoice => {
      if (invoice.status === 'paid') {
        paid += invoice.amount;
      } else if (invoice.status === 'pending' || invoice.status === 'overdue') {
        outstanding += invoice.amount;
        if (invoice.status === 'overdue') {
          overdue++;
        }
      }
    });
    
    setTotalOutstanding(outstanding);
    setTotalPaid(paid);
    setOverdueCount(overdue);
  };

  const handleDownloadInvoice = async (invoice: Invoice) => {
    try {
      const response = await api.get(
        `/api/v1/invoices/${invoice.invoice_id}/download`,
        { responseType: 'blob' }
      );
      
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `invoice-${invoice.invoice_number}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      
      toast.success(t('client.invoices.downloadSuccess'));
    } catch (error) {
      console.error('Error downloading invoice:', error);
      toast.error(t('client.invoices.downloadError'));
    }
  };

  const filteredInvoices = invoices.filter(invoice => {
    // Search filter
    if (searchTerm && !invoice.invoice_number.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false;
    }
    
    // Status filter
    if (statusFilter !== 'all' && invoice.status !== statusFilter) {
      return false;
    }
    
    // Date range filter
    if (dateRange !== 'all') {
      const issueDate = new Date(invoice.issue_date);
      const now = new Date();
      const daysDiff = Math.floor((now.getTime() - issueDate.getTime()) / (1000 * 60 * 60 * 24));
      
      if (dateRange === 'last30' && daysDiff > 30) return false;
      if (dateRange === 'last60' && daysDiff > 60) return false;
      if (dateRange === 'last90' && daysDiff > 90) return false;
    }
    
    return true;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  const tabs = [
    { id: 'invoices', label: t('client.invoices.tabs.invoices'), icon: FileText },
    { id: 'payments', label: t('client.invoices.tabs.payments'), icon: CreditCard }
  ];

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-text-primary mb-2">
          {t('client.invoices.title')}
        </h1>
        <p className="text-text-secondary">
          {t('client.invoices.subtitle')}
        </p>
      </div>

      {/* Balance Summary */}
      <BalanceSummary
        totalOutstanding={totalOutstanding}
        totalPaid={totalPaid}
        overdueCount={overdueCount}
      />

      {/* Main Content */}
      <Card className="shadow-elevated">
        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6" aria-label="Tabs">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`
                    py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2
                    ${activeTab === tab.id
                      ? 'border-accent-red text-accent-red'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }
                  `}
                >
                  <Icon className="w-4 h-4" />
                  {tab.label}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'invoices' && (
            <>
              {/* Filters */}
              <div className="mb-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                <Input
                  placeholder={t('client.invoices.searchPlaceholder')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  leftIcon={<Search className="w-4 h-4" />}
                />
                
                <Select
                  value={statusFilter}
                  onChange={setStatusFilter}
                  options={[
                    { value: 'all', label: t('client.invoices.status.all') },
                    { value: 'pending', label: t('client.invoices.status.pending') },
                    { value: 'paid', label: t('client.invoices.status.paid') },
                    { value: 'overdue', label: t('client.invoices.status.overdue') },
                    { value: 'cancelled', label: t('client.invoices.status.cancelled') }
                  ]}
                  leftIcon={<Filter className="w-4 h-4" />}
                />
                
                <Select
                  value={dateRange}
                  onChange={setDateRange}
                  options={[
                    { value: 'all', label: t('client.invoices.dateRange.all') },
                    { value: 'last30', label: t('client.invoices.dateRange.last30') },
                    { value: 'last60', label: t('client.invoices.dateRange.last60') },
                    { value: 'last90', label: t('client.invoices.dateRange.last90') }
                  ]}
                  leftIcon={<Calendar className="w-4 h-4" />}
                />
              </div>

              {/* Invoice List */}
              <InvoiceList
                invoices={filteredInvoices}
                onView={(invoice) => {
                  setSelectedInvoice(invoice);
                  setShowDetailsModal(true);
                }}
                onDownload={handleDownloadInvoice}
              />
            </>
          )}

          {activeTab === 'payments' && (
            <PaymentHistory payments={payments} />
          )}
        </div>
      </Card>

      {/* Invoice Details Modal */}
      <Modal
        isOpen={showDetailsModal}
        onClose={() => {
          setShowDetailsModal(false);
          setSelectedInvoice(null);
        }}
        title={t('client.invoices.invoiceDetails')}
        size="lg"
      >
        {selectedInvoice && (
          <InvoiceDetails
            invoice={selectedInvoice}
            onDownload={handleDownloadInvoice}
            onClose={() => {
              setShowDetailsModal(false);
              setSelectedInvoice(null);
            }}
          />
        )}
      </Modal>
    </div>
  );
};

export default Invoices;