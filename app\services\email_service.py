"""
Email service for sending transactional emails.
"""

import os
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Optional, List, Dict, Any
import aiosmtplib
from aiosmtplib import <PERSON>TPAuthenticationError, SMTPConnectError, SMTPTimeoutError
from jinja2 import Environment, FileSystemLoader, select_autoescape

from app.config.settings import settings
from app.core.logging import <PERSON><PERSON><PERSON><PERSON><PERSON>ogger
from app.locales.translation_service import TranslationService

logger = TutorAideLogger.get_logger(__name__)


class EmailService:
    """Service for sending emails with template support."""
    
    def __init__(self):
        # Initialize Jinja2 environment for templates
        self.env = Environment(
            loader=FileSystemLoader('app/templates/emails'),
            autoescape=select_autoescape(['html', 'xml'])
        )
        self.translation_service = TranslationService()
        
        # Log SMTP configuration (without password)
        logger.info(f"EmailService initialized with SMTP config:")
        logger.info(f"  SMTP_HOST: {settings.SMTP_HOST}")
        logger.info(f"  SMTP_PORT: {settings.SMTP_PORT}")
        logger.info(f"  SMTP_USERNAME: {settings.SMTP_USERNAME}")
        logger.info(f"  SMTP_TLS: {settings.SMTP_TLS}")
        logger.info(f"  SMTP_PASSWORD: {'*' * len(settings.SMTP_PASSWORD) if settings.SMTP_PASSWORD else 'Not set'}")
        
    async def send_email(
        self,
        to_email: str,
        subject: str,
        template_name: str,
        context: Dict[str, Any],
        language: str = 'en',
        from_email: Optional[str] = None
    ) -> bool:
        """
        Send an email using a template.
        
        Args:
            to_email: Recipient email address
            subject: Email subject
            template_name: Name of the email template
            context: Context variables for the template
            language: Language for the email (en/fr)
            from_email: Sender email (defaults to support email)
            
        Returns:
            True if sent successfully
        """
        try:
            # Get translated subject if needed
            if subject.startswith('email.'):
                subject = self.translation_service.get_translation(subject, language)
            
            # Add common context
            context.update({
                'language': language,
                'company_name': 'TutorAide',
                'support_email': settings.SUPPORT_EMAIL,
                'year': now_est().year
            })
            
            # Load and render template
            template = self.env.get_template(f"{template_name}_{language}.html")
            html_content = template.render(**context)
            
            # Create message
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = from_email or settings.SUPPORT_EMAIL
            msg['To'] = to_email
            
            # Add HTML part
            html_part = MIMEText(html_content, 'html')
            msg.attach(html_part)
            
            # Send email
            if settings.SMTP_HOST:
                logger.info(f"Attempting to send email via SMTP to {to_email}")
                await self._send_smtp_email(msg)
                logger.info(f"Email sent successfully to {to_email}: {subject}")
                return True
            else:
                # Log email in development
                logger.warning(f"SMTP_HOST not configured. Email NOT sent to {to_email}")
                logger.info(f"Email (dev mode) to {to_email}: {subject}")
                logger.debug(f"Email content: {html_content}")
                return True
                
        except Exception as e:
            logger.error(f"Error sending email: {e}")
            return False
    
    async def _send_smtp_email(self, msg: MIMEMultipart):
        """Send email via SMTP with proper timeout and error handling."""
        smtp = None
        try:
            logger.info("=== SMTP EMAIL SEND ATTEMPT ===")
            logger.info(f"SMTP Configuration:")
            logger.info(f"  Host: {settings.SMTP_HOST}")
            logger.info(f"  Port: {settings.SMTP_PORT}")
            logger.info(f"  Username: {settings.SMTP_USERNAME}")
            logger.info(f"  TLS: {settings.SMTP_TLS}")
            logger.info(f"  To: {msg['To']}")
            logger.info(f"  Subject: {msg['Subject']}")
            
            # Gmail and most SMTP servers use STARTTLS on port 587
            # use_tls=True means SSL/TLS from the start (port 465)
            # For port 587, we need starttls after connection
            use_tls = settings.SMTP_PORT == 465
            
            smtp = aiosmtplib.SMTP(
                hostname=settings.SMTP_HOST,
                port=settings.SMTP_PORT,
                use_tls=use_tls,
                timeout=30  # 30 second timeout
            )
            
            logger.info(f"Connecting to SMTP server {settings.SMTP_HOST}:{settings.SMTP_PORT}")
            await smtp.connect()
            logger.info("SMTP connection established")
            
            # For port 587, start TLS after connection
            if settings.SMTP_PORT == 587 and settings.SMTP_TLS:
                try:
                    logger.info("Starting TLS for secure connection")
                    await smtp.starttls()
                    logger.info("TLS started successfully")
                except aiosmtplib.SMTPException as e:
                    if "Connection already using TLS" in str(e):
                        logger.info("Connection already using TLS, continuing...")
                    else:
                        raise
            
            if settings.SMTP_USERNAME and settings.SMTP_PASSWORD:
                logger.info(f"Authenticating as {settings.SMTP_USERNAME}")
                await smtp.login(settings.SMTP_USERNAME, settings.SMTP_PASSWORD)
                logger.info("SMTP authentication successful")
            
            logger.info(f"Sending email to {msg['To']}")
            await smtp.send_message(msg)
            logger.info("Email sent successfully via SMTP")
            logger.info("=== SMTP EMAIL SEND COMPLETE ===")
            
        except aiosmtplib.SMTPAuthenticationError as e:
            logger.error(f"SMTP Authentication failed: {e}")
            logger.error(f"Username: {settings.SMTP_USERNAME}")
            logger.error("Check your app-specific password for Gmail")
            raise
        except aiosmtplib.SMTPConnectError as e:
            logger.error(f"Failed to connect to SMTP server: {e}")
            logger.error(f"Host: {settings.SMTP_HOST}, Port: {settings.SMTP_PORT}")
            raise
        except aiosmtplib.SMTPTimeoutError as e:
            logger.error(f"SMTP connection timed out: {e}")
            logger.error("This might be a network/firewall issue")
            raise
        except Exception as e:
            logger.error(f"Unexpected error sending email: {type(e).__name__}: {e}")
            import traceback
            logger.error(f"Full SMTP error traceback: {traceback.format_exc()}")
            raise
        finally:
            if smtp and smtp.is_connected:
                logger.info("Closing SMTP connection")
                await smtp.quit()
    
    async def send_password_reset_email(
        self,
        email: str,
        reset_token: str,
        language: str = 'en'
    ) -> bool:
        """
        Send password reset email.
        
        Args:
            email: Recipient email
            reset_token: Password reset token
            language: Email language
            
        Returns:
            True if sent successfully
        """
        # Generate reset link
        frontend_url = settings.CORS_ORIGINS[0] if settings.CORS_ORIGINS else "http://localhost:3000"
        reset_link = f"{frontend_url}/auth/reset-password?token={reset_token}"
        
        context = {
            'reset_link': reset_link,
            'expiry_hours': 1,
            'user_email': email
        }
        
        return await self.send_email(
            to_email=email,
            subject='email.password_reset.subject',
            template_name='password_reset',
            context=context,
            language=language
        )
    
    async def send_password_changed_email(
        self,
        email: str,
        language: str = 'en'
    ) -> bool:
        """
        Send password changed notification email.
        
        Args:
            email: Recipient email
            language: Email language
            
        Returns:
            True if sent successfully
        """
        context = {
            'user_email': email,
            'change_time': now_est().strftime('%Y-%m-%d %H:%M EST')
        }
        
        return await self.send_email(
            to_email=email,
            subject='email.password_changed.subject',
            template_name='password_changed',
            context=context,
            language=language
        )
    
    async def send_email_verification(
        self,
        email: str,
        verification_token: str,
        language: str = 'en'
    ) -> bool:
        """
        Send email verification email.
        
        Args:
            email: Recipient email
            verification_token: Email verification token
            language: Email language
            
        Returns:
            True if sent successfully
        """
        # Generate verification link
        frontend_url = settings.CORS_ORIGINS[0] if settings.CORS_ORIGINS else "http://localhost:3000"
        verify_link = f"{frontend_url}/auth/verify-email?token={verification_token}"
        
        context = {
            'verify_link': verify_link,
            'user_email': email
        }
        
        return await self.send_email(
            to_email=email,
            subject='email.verification.subject',
            template_name='email_verification',
            context=context,
            language=language
        )


# Import at the end to avoid circular imports
from app.core.timezone import now_est


# Singleton instance (lazy initialization)
_email_service = None


def get_email_service() -> EmailService:
    """Get the email service instance (lazy initialization)."""
    global _email_service
    
    if _email_service is None:
        logger.info("Initializing EmailService for the first time...")
        
        # Log raw environment variables
        logger.info("Raw environment variables:")
        logger.info(f"  SMTP_HOST env: {os.environ.get('SMTP_HOST', 'Not set')}")
        logger.info(f"  SMTP_PORT env: {os.environ.get('SMTP_PORT', 'Not set')}")
        logger.info(f"  SMTP_USERNAME env: {os.environ.get('SMTP_USERNAME', 'Not set')}")
        logger.info(f"  SMTP_PASSWORD env: {'Set' if os.environ.get('SMTP_PASSWORD') else 'Not set'}")
        logger.info(f"  RAILWAY_ENVIRONMENT: {os.environ.get('RAILWAY_ENVIRONMENT', 'Not set')}")
        
        # Force reload settings to ensure we have latest env vars
        from app.config.settings import get_settings
        settings_fresh = get_settings()
        
        logger.info("Settings after reload:")
        logger.info(f"  SMTP_HOST setting: {settings_fresh.SMTP_HOST}")
        logger.info(f"  SMTP_PORT setting: {settings_fresh.SMTP_PORT}")
        logger.info(f"  SMTP_USERNAME setting: {settings_fresh.SMTP_USERNAME}")
        
        _email_service = EmailService()
        
    return _email_service