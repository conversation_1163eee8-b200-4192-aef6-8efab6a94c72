"""
Automated reminders API endpoints.
"""

from typing import Dict, Any, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, Query, HTTPException, status

from app.core.dependencies import get_current_user
from app.core.auth_decorators import require_roles
from app.models.user_models import User
from app.models.base import UserRoleType
from app.services.automated_reminder_service import AutomatedReminderService

router = APIRouter()
reminder_service = AutomatedReminderService()


@router.post("/appointments/{appointment_id}/schedule")
@require_roles([UserRoleType.MANAGER, UserRoleType.TUTOR])
async def schedule_appointment_reminders(
    appointment_id: int,
    reminder_data: Dict[str, Any],
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Schedule all reminders for an appointment.
    
    Request body:
    {
        "appointment_datetime": "2024-01-15T14:00:00",
        "tutor_id": 123,
        "client_id": 456,
        "dependant_id": 789  // Optional
    }
    """
    try:
        # Parse appointment datetime
        appointment_datetime = datetime.fromisoformat(reminder_data["appointment_datetime"])
        
        result = await reminder_service.schedule_appointment_reminders(
            appointment_id=appointment_id,
            appointment_datetime=appointment_datetime,
            tutor_id=reminder_data["tutor_id"],
            client_id=reminder_data["client_id"],
            dependant_id=reminder_data.get("dependant_id")
        )
        
        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error scheduling reminders: {str(e)}"
        )


@router.delete("/appointments/{appointment_id}/reminders")
@require_roles([UserRoleType.MANAGER, UserRoleType.TUTOR])
async def cancel_appointment_reminders(
    appointment_id: int,
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Cancel all reminders for a cancelled appointment.
    """
    try:
        success = await reminder_service.cancel_appointment_reminders(appointment_id)
        
        return {
            "success": success,
            "appointment_id": appointment_id,
            "message": "All reminders cancelled"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error cancelling reminders: {str(e)}"
        )


@router.put("/appointments/{appointment_id}/reschedule")
@require_roles([UserRoleType.MANAGER, UserRoleType.TUTOR])
async def reschedule_appointment_reminders(
    appointment_id: int,
    reschedule_data: Dict[str, Any],
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Reschedule reminders when appointment time changes.
    
    Request body:
    {
        "old_datetime": "2024-01-15T14:00:00",
        "new_datetime": "2024-01-15T16:00:00",
        "tutor_id": 123,
        "client_id": 456,
        "dependant_id": 789  // Optional
    }
    """
    try:
        old_datetime = datetime.fromisoformat(reschedule_data["old_datetime"])
        new_datetime = datetime.fromisoformat(reschedule_data["new_datetime"])
        
        result = await reminder_service.reschedule_appointment_reminders(
            appointment_id=appointment_id,
            old_datetime=old_datetime,
            new_datetime=new_datetime,
            tutor_id=reschedule_data["tutor_id"],
            client_id=reschedule_data["client_id"],
            dependant_id=reschedule_data.get("dependant_id")
        )
        
        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error rescheduling reminders: {str(e)}"
        )


@router.post("/process-due")
@require_roles([UserRoleType.MANAGER])
async def process_due_reminders(
    current_user: User = Depends(get_current_user),
    check_time: Optional[datetime] = Query(None, description="Time to check for due reminders")
) -> Dict[str, Any]:
    """
    Manually trigger processing of due reminders.
    
    Normally handled automatically by background tasks.
    """
    try:
        result = await reminder_service.process_due_reminders(check_time)
        
        return {
            "processed_at": datetime.now(),
            "results": result
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing reminders: {str(e)}"
        )


@router.get("/statistics")
@require_roles([UserRoleType.MANAGER])
async def get_reminder_statistics(
    current_user: User = Depends(get_current_user),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None)
) -> Dict[str, Any]:
    """
    Get reminder statistics for dashboard.
    
    Includes delivery rates, response rates, and breakdown by type.
    """
    try:
        date_range = None
        if start_date and end_date:
            date_range = (start_date, end_date)
        
        stats = await reminder_service.get_reminder_statistics(date_range)
        
        return stats
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving statistics: {str(e)}"
        )


@router.get("/health")
async def reminder_system_health(
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Check the health of the reminder system.
    
    Returns status of background tasks, queue health, etc.
    """
    try:
        # Check Celery connection
        from app.services.automated_reminder_service import celery_app
        
        # Get active tasks
        inspect = celery_app.control.inspect()
        active_tasks = inspect.active()
        scheduled_tasks = inspect.scheduled()
        
        # Get recent statistics
        stats = await reminder_service.get_reminder_statistics()
        
        return {
            "status": "healthy",
            "celery_connected": active_tasks is not None,
            "active_tasks": len(active_tasks) if active_tasks else 0,
            "scheduled_tasks": len(scheduled_tasks) if scheduled_tasks else 0,
            "recent_stats": {
                "total_scheduled": stats.get("total_scheduled", 0),
                "delivery_rate": stats.get("delivery_rate", 0.0),
                "pending": stats.get("pending", 0)
            },
            "checked_at": datetime.now()
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "checked_at": datetime.now()
        }