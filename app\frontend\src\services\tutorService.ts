/**
 * Tutor service for managing tutor data
 */

import api from './api';

export interface Tutor {
  tutor_id: number;
  user_id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone_number?: string;
  profile_picture?: string;
  bio?: string;
  specializations?: string[];
  languages?: string[];
  hourly_rate?: number;
  experience_years?: number;
  is_active: boolean;
  is_verified: boolean;
  verification_status: 'pending' | 'verified' | 'rejected';
  verification_documents?: VerificationDocument[];
  city?: string;
  province?: string;
  postal_code?: string;
  service_areas?: ServiceArea[];
  education?: Education[];
  experience?: Experience[];
  references?: Reference[];
  created_at: string;
  updated_at: string;
}

export interface VerificationDocument {
  document_id: number;
  document_type: 'id' | 'degree' | 'certification' | 'background_check' | 'other';
  document_name: string;
  document_url: string;
  status: 'pending' | 'approved' | 'rejected';
  uploaded_at: string;
  reviewed_at?: string;
  reviewer_notes?: string;
}

export interface ServiceArea {
  postal_code: string;
  radius_km: number;
  is_primary: boolean;
}

export interface Education {
  education_id: number;
  institution: string;
  degree: string;
  field_of_study: string;
  start_year: number;
  end_year?: number;
  is_current: boolean;
}

export interface Experience {
  experience_id: number;
  organization: string;
  position: string;
  description?: string;
  start_date: string;
  end_date?: string;
  is_current: boolean;
}

export interface Reference {
  reference_id: number;
  name: string;
  relationship: string;
  email: string;
  phone_number: string;
  has_contacted: boolean;
  contact_date?: string;
  notes?: string;
}

export interface TutorSearchParams {
  search?: string;
  subject_area?: string;
  is_active?: boolean;
  city?: string;
  province?: string;
  min_rate?: number;
  max_rate?: number;
  languages?: string[];
  page?: number;
  limit?: number;
}

export interface TutorListResponse {
  items: Tutor[];
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
}

export interface TutorCreateRequest {
  user_id?: number;
  first_name: string;
  last_name: string;
  email: string;
  phone_number?: string;
  bio?: string;
  specializations?: string[];
  languages?: string[];
  hourly_rate?: number;
  experience_years?: number;
  city?: string;
  province?: string;
  postal_code?: string;
}

export interface TutorUpdateRequest {
  first_name?: string;
  last_name?: string;
  phone_number?: string;
  profile_picture?: string;
  bio?: string;
  specializations?: string[];
  languages?: string[];
  hourly_rate?: number;
  experience_years?: number;
  city?: string;
  province?: string;
  postal_code?: string;
}

export interface TutorInvitation {
  invitation_id: number;
  email: string;
  first_name: string;
  last_name: string;
  token: string;
  status: 'pending' | 'accepted' | 'expired';
  invited_by: number;
  invited_at: string;
  accepted_at?: string;
  expires_at: string;
}

export const tutorService = {
  // Search tutors
  async searchTutors(params?: TutorSearchParams): Promise<TutorListResponse> {
    const response = await api.get<TutorListResponse>('/tutors', { params });
    return response.data;
  },

  // Get tutor by ID
  async getTutor(tutorId: number): Promise<Tutor> {
    const response = await api.get<Tutor>(`/tutors/${tutorId}`);
    return response.data;
  },

  // Get tutor by user ID
  async getTutorByUserId(userId: number): Promise<Tutor> {
    const response = await api.get<Tutor>(`/tutors/user/${userId}`);
    return response.data;
  },

  // Create tutor profile
  async createTutor(data: TutorCreateRequest): Promise<Tutor> {
    const response = await api.post<Tutor>('/tutors', data);
    return response.data;
  },

  // Update tutor profile
  async updateTutor(tutorId: number, data: TutorUpdateRequest): Promise<Tutor> {
    const response = await api.put<Tutor>(`/tutors/${tutorId}`, data);
    return response.data;
  },

  // Delete tutor (soft delete)
  async deleteTutor(tutorId: number): Promise<void> {
    await api.delete(`/tutors/${tutorId}`);
  },

  // Get tutor availability
  async getTutorAvailability(tutorId: number): Promise<any> {
    const response = await api.get(`/tutors/${tutorId}/availability`);
    return response.data;
  },

  // Update tutor availability
  async updateTutorAvailability(tutorId: number, availability: any[]): Promise<any> {
    const response = await api.put(`/tutors/${tutorId}/availability`, { availability });
    return response.data;
  },

  // Get tutor service areas
  async getTutorServiceAreas(tutorId: number): Promise<ServiceArea[]> {
    const response = await api.get<ServiceArea[]>(`/tutors/${tutorId}/service-areas`);
    return response.data;
  },

  // Update tutor service areas
  async updateTutorServiceAreas(tutorId: number, serviceAreas: ServiceArea[]): Promise<ServiceArea[]> {
    const response = await api.put<ServiceArea[]>(`/tutors/${tutorId}/service-areas`, { service_areas: serviceAreas });
    return response.data;
  },

  // Upload verification document
  async uploadVerificationDocument(tutorId: number, documentType: string, file: File): Promise<VerificationDocument> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('document_type', documentType);
    
    const response = await api.post<VerificationDocument>(
      `/tutors/${tutorId}/verification-documents`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return response.data;
  },

  // Get verification documents
  async getVerificationDocuments(tutorId: number): Promise<VerificationDocument[]> {
    const response = await api.get<VerificationDocument[]>(`/tutors/${tutorId}/verification-documents`);
    return response.data;
  },

  // Update tutor verification status (manager only)
  async updateVerificationStatus(tutorId: number, status: 'verified' | 'rejected', notes?: string): Promise<Tutor> {
    const response = await api.put<Tutor>(`/tutors/${tutorId}/verification-status`, { status, notes });
    return response.data;
  },

  // Approve verification document (manager only)
  async approveDocument(tutorId: number, documentId: number, approved: boolean, notes?: string): Promise<VerificationDocument> {
    const response = await api.put<VerificationDocument>(
      `/tutors/${tutorId}/verification-documents/${documentId}/review`,
      { approved, notes }
    );
    return response.data;
  },

  // Get tutor invitations (manager only)
  async getTutorInvitations(params?: { status?: string; page?: number; limit?: number }): Promise<{
    items: TutorInvitation[];
    total: number;
  }> {
    const response = await api.get('/tutor-invitations', { params });
    return response.data;
  },

  // Send tutor invitation (manager only)
  async sendTutorInvitation(data: {
    email: string;
    first_name: string;
    last_name: string;
  }): Promise<TutorInvitation> {
    const response = await api.post<TutorInvitation>('/tutor-invitations', data);
    return response.data;
  },

  // Resend tutor invitation (manager only)
  async resendTutorInvitation(invitationId: number): Promise<TutorInvitation> {
    const response = await api.post<TutorInvitation>(`/tutor-invitations/${invitationId}/resend`);
    return response.data;
  },

  // Cancel tutor invitation (manager only)
  async cancelTutorInvitation(invitationId: number): Promise<void> {
    await api.delete(`/tutor-invitations/${invitationId}`);
  },

  // Get tutor stats
  async getTutorStats(tutorId: number): Promise<{
    total_sessions: number;
    completed_sessions: number;
    cancelled_sessions: number;
    average_rating: number;
    total_students: number;
    active_students: number;
    revenue_this_month: number;
    hours_this_month: number;
  }> {
    const response = await api.get(`/tutors/${tutorId}/stats`);
    return response.data;
  },

  // Get tutor schedule
  async getTutorSchedule(tutorId: number, startDate: string, endDate: string): Promise<any[]> {
    const response = await api.get(`/tutors/${tutorId}/schedule`, {
      params: { start_date: startDate, end_date: endDate }
    });
    return response.data;
  }
};