"""
Example protected endpoints demonstrating authorization decorators.
"""

from typing import Annotated
from fastapi import APIRouter, Depends
import asyncpg

from app.core.dependencies import get_current_active_user, get_database
from app.core.authorization import (
    require_role, require_any_role, require_all_roles,
    check_resource_ownership, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Own<PERSON><PERSON><PERSON><PERSON>cker
)
from app.models.user_models import User, UserRoleType

router = APIRouter(prefix="/protected", tags=["protected-examples"])


# Example 1: Single role requirement using decorator
@router.get("/managers-only")
@require_role(UserRoleType.MANAGER)
async def managers_only_endpoint(
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    """Only managers can access this endpoint."""
    return {
        "message": "Welcome, manager!",
        "user_id": current_user.user_id,
        "email": current_user.email
    }


# Example 2: Any role requirement using decorator
@router.get("/staff-area")
@require_any_role([UserRoleType.MANAGER, UserRoleType.TUTOR])
async def staff_area_endpoint(
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    """Managers or tutors can access this endpoint."""
    return {
        "message": "Welcome to the staff area!",
        "user_roles": [role.value for role in current_user.roles]
    }


# Example 3: All roles requirement using decorator
@router.get("/multi-role")
@require_all_roles([UserRoleType.MANAGER, UserRoleType.TUTOR])
async def multi_role_endpoint(
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    """Only users who are both managers AND tutors can access this."""
    return {
        "message": "You have multiple roles!",
        "roles": [role.value for role in current_user.roles]
    }


# Example 4: Using RoleChecker dependency
@router.get(
    "/tutors-only",
    dependencies=[Depends(RoleChecker(UserRoleType.TUTOR))]
)
async def tutors_only_endpoint():
    """Only tutors can access this endpoint (using dependency)."""
    return {"message": "Tutor-specific content"}


# Example 5: Resource ownership check with decorator
@router.put("/appointments/{appointment_id}")
@check_resource_ownership(
    "appointment_sessions",
    "appointment_id",
    allow_roles=[UserRoleType.MANAGER]
)
async def update_own_appointment(
    appointment_id: int,
    current_user: Annotated[User, Depends(get_current_active_user)],
    db: Annotated[asyncpg.Connection, Depends(get_database)]
):
    """
    Users can only update their own appointments.
    Managers can update any appointment.
    """
    return {
        "message": f"Appointment {appointment_id} updated",
        "updated_by": current_user.user_id
    }


# Example 6: Using ResourceOwnershipChecker dependency
@router.delete(
    "/appointments/{appointment_id}",
    dependencies=[
        Depends(ResourceOwnershipChecker(
            "appointment_sessions",
            "appointment_id",
            allow_roles=[UserRoleType.MANAGER]
        ))
    ]
)
async def delete_own_appointment(
    appointment_id: int,
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    """
    Users can only delete their own appointments.
    Managers can delete any appointment.
    """
    return {
        "message": f"Appointment {appointment_id} deleted",
        "deleted_by": current_user.user_id
    }


# Example 7: Multiple authorization checks
@router.get(
    "/admin/reports",
    dependencies=[
        Depends(RoleChecker([UserRoleType.MANAGER], require_all=False))
    ]
)
async def admin_reports(
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    """
    Advanced endpoint with multiple authorization checks.
    Only managers can access reports.
    """
    return {
        "message": "Admin reports",
        "access_level": "manager",
        "user": current_user.email
    }


# Example 8: Client-specific endpoint
@router.get("/my-sessions")
@require_role(UserRoleType.CLIENT)
async def client_sessions(
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    """Only clients can access their session history."""
    return {
        "message": "Your tutoring sessions",
        "client_id": current_user.user_id
    }