#!/usr/bin/env python3
"""
Create a test user for password reset testing.

This script creates a user <NAME_EMAIL>
to test the password reset functionality.
"""

import asyncio
import asyncpg
import os
import sys
from datetime import datetime
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.config.settings import settings
from app.core.security import get_password_hash
from app.models.base import UserRoleType

async def create_test_user():
    """Create a test user in the database."""
    
    # Database connection parameters
    database_url = settings.DATABASE_URL
    
    print(f"Connecting to database...")
    print(f"Database URL: {database_url.replace(database_url.split('@')[0].split('//')[1], '***:***')}")
    
    try:
        # Connect to database
        conn = await asyncpg.connect(database_url)
        
        # Check if user already exists
        existing_user = await conn.fetchrow(
            "SELECT user_id, email FROM auth_users WHERE email = $1",
            "<EMAIL>"
        )
        
        if existing_user:
            print(f"User already exists: {existing_user['email']} (ID: {existing_user['user_id']})")
            return
        
        # Create user
        print("Creating test user...")
        
        # Hash password
        password_hash = get_password_hash("TestPassword123!")
        
        # Insert user
        user_id = await conn.fetchval(
            """
            INSERT INTO auth_users (
                email, 
                password_hash, 
                is_email_verified,
                created_at,
                updated_at
            ) VALUES ($1, $2, $3, $4, $5)
            RETURNING user_id
            """,
            "<EMAIL>",
            password_hash,
            True,  # Mark as verified for testing
            datetime.utcnow(),
            datetime.utcnow()
        )
        
        print(f"Created user with ID: {user_id}")
        
        # Add user role
        await conn.execute(
            """
            INSERT INTO auth_user_roles (
                user_id,
                role,
                created_at
            ) VALUES ($1, $2, $3)
            """,
            user_id,
            UserRoleType.MANAGER.value,  # Give manager role for testing
            datetime.utcnow()
        )
        
        print(f"Added MANAGER role to user")
        
        # Close connection
        await conn.close()
        
        print("\nTest user created successfully!")
        print("Email: <EMAIL>")
        print("Password: TestPassword123!")
        print("Role: MANAGER")
        
    except Exception as e:
        print(f"Error creating test user: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(create_test_user())