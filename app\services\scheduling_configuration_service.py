"""
Service for managing scheduling frequencies and duration configurations.
"""

import logging
from datetime import datetime, date, time, timedelta
from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from app.models.service_configuration_models import (
    SessionFrequency, DurationOption, TimeSlotPreference, DayOfWeekPreference,
    ServiceFrequencyConfiguration, ServiceDurationConfiguration,
    SchedulingPreferences, RecurringSchedulePattern,
    UpdateSchedulingPreferencesRequest, SchedulingPreferencesResponse,
    CreateRecurringPatternRequest, RecurringPatternResponse,
    AvailableScheduleOptionsResponse, ScheduleOptimizationRequest, ScheduleOptimizationResponse
)
from app.database.repositories.service_repository import ServiceCatalogRepository
from app.core.exceptions import ResourceNotFoundError, ValidationError, BusinessRuleError
from app.core.timezone import now_est

logger = logging.getLogger(__name__)


class SchedulingConfigurationService:
    """Service for managing scheduling configurations."""
    
    def __init__(self, db_session: AsyncSession):
        self.db = db_session
        self.catalog_repo = ServiceCatalogRepository(db_session)
    
    async def get_available_schedule_options(
        self,
        service_catalog_id: int
    ) -> AvailableScheduleOptionsResponse:
        """Get available scheduling options for a service."""
        try:
            # Get service details
            service = await self.catalog_repo.get_by_id(service_catalog_id)
            if not service:
                raise ResourceNotFoundError(f"Service {service_catalog_id} not found")
            
            # Determine available frequencies based on service type
            available_frequencies = self._get_frequencies_for_service(service)
            default_frequency = SessionFrequency.WEEKLY if service.service_level != "adult" else SessionFrequency.ONCE
            
            # Determine available durations
            available_durations = self._get_durations_for_service(service)
            default_duration = DurationOption(str(service.default_duration_minutes))
            
            # Format duration options
            duration_list = []
            for duration in available_durations:
                duration_list.append({
                    'value': duration.value,
                    'minutes': duration.minutes,
                    'display_name': duration.display_name
                })
            
            # Suggest time slots based on service level
            suggested_time_slots = self._get_suggested_time_slots(service)
            popular_days = self._get_popular_days(service)
            
            # Calculate pricing impact
            duration_pricing = self._calculate_duration_pricing(available_durations)
            frequency_discounts = self._calculate_frequency_discounts()
            
            return AvailableScheduleOptionsResponse(
                service_catalog_id=service_catalog_id,
                service_name=service.service_name,
                available_frequencies=available_frequencies,
                default_frequency=default_frequency,
                available_durations=duration_list,
                default_duration=default_duration,
                suggested_time_slots=suggested_time_slots,
                popular_days=popular_days,
                min_duration_minutes=service.min_duration_minutes,
                max_duration_minutes=service.max_duration_minutes,
                earliest_start_time=time(8, 0),
                latest_end_time=time(22, 0),
                duration_pricing=duration_pricing,
                frequency_discounts=frequency_discounts
            )
            
        except Exception as e:
            logger.error(f"Error getting schedule options: {e}")
            raise
    
    async def save_scheduling_preferences(
        self,
        entity_type: str,  # 'user', 'client', or 'tutor'
        entity_id: int,
        preferences: UpdateSchedulingPreferencesRequest
    ) -> SchedulingPreferencesResponse:
        """Save or update scheduling preferences."""
        try:
            # Check if preferences exist
            check_query = text(f"""
                SELECT scheduling_preference_id 
                FROM scheduling_preferences
                WHERE {entity_type}_id = :{entity_type}_id
            """)
            
            result = await self.db.execute(check_query, {f'{entity_type}_id': entity_id})
            existing = result.fetchone()
            
            if existing:
                # Update existing preferences
                return await self._update_preferences(
                    existing.scheduling_preference_id,
                    preferences
                )
            else:
                # Create new preferences
                return await self._create_preferences(
                    entity_type,
                    entity_id,
                    preferences
                )
                
        except Exception as e:
            logger.error(f"Error saving scheduling preferences: {e}")
            raise
    
    async def create_recurring_pattern(
        self,
        request: CreateRecurringPatternRequest,
        created_by: int
    ) -> RecurringPatternResponse:
        """Create a recurring schedule pattern."""
        try:
            # Validate pattern
            self._validate_recurring_pattern(request)
            
            # Create pattern in database
            query = text("""
                INSERT INTO recurring_patterns (
                    pattern_name, frequency, days_of_week, day_of_month,
                    week_of_month, start_time, duration, start_date,
                    end_date, total_sessions, client_id, tutor_id,
                    service_catalog_id, allow_reschedule, require_confirmation,
                    auto_confirm_hours_before, created_by, created_at, updated_at
                ) VALUES (
                    :pattern_name, :frequency, :days_of_week, :day_of_month,
                    :week_of_month, :start_time, :duration, :start_date,
                    :end_date, :total_sessions, :client_id, :tutor_id,
                    :service_catalog_id, :allow_reschedule, :require_confirmation,
                    :auto_confirm_hours_before, :created_by, :created_at, :updated_at
                )
                RETURNING *
            """)
            
            params = {
                **request.model_dump(),
                'days_of_week': [d.value for d in request.days_of_week] if request.days_of_week else None,
                'duration': request.duration.value,
                'created_by': created_by,
                'created_at': now_est(),
                'updated_at': now_est()
            }
            
            result = await self.db.execute(query, params)
            pattern = result.fetchone()
            
            if not pattern:
                raise BusinessRuleError("Failed to create recurring pattern")
            
            await self.db.commit()
            
            # Calculate next session date
            next_session = self._calculate_next_session_date(
                request.frequency,
                request.start_date,
                request.days_of_week,
                request.day_of_month,
                request.week_of_month
            )
            
            pattern_dict = dict(pattern._mapping)
            pattern_dict['next_session_date'] = next_session
            pattern_dict['sessions_remaining'] = request.total_sessions
            
            return RecurringPatternResponse(**pattern_dict)
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error creating recurring pattern: {e}")
            raise
    
    async def optimize_schedule(
        self,
        request: ScheduleOptimizationRequest
    ) -> ScheduleOptimizationResponse:
        """Generate optimized schedule suggestions."""
        try:
            # Analyze client's needs
            hours_per_subject = self._distribute_hours(
                request.subject_areas,
                request.total_hours_per_week
            )
            
            # Generate suggested patterns
            suggested_patterns = []
            
            for subject, hours in hours_per_subject.items():
                if hours >= 2:  # Multiple sessions per week
                    pattern = self._create_multi_session_pattern(
                        subject, hours, request.optimize_for
                    )
                    suggested_patterns.append(pattern)
                else:  # Single session per week
                    pattern = self._create_single_session_pattern(
                        subject, hours, request.optimize_for
                    )
                    suggested_patterns.append(pattern)
            
            # Calculate optimization score
            optimization_score = self._calculate_optimization_score(
                suggested_patterns,
                request.optimize_for
            )
            
            # Estimate costs (would integrate with pricing service)
            estimated_cost = request.total_hours_per_week * 45.0 * 4  # $45/hour * 4 weeks
            
            return ScheduleOptimizationResponse(
                optimization_score=optimization_score,
                suggested_patterns=suggested_patterns,
                estimated_monthly_cost=estimated_cost,
                time_distribution=hours_per_subject,
                conflict_warnings=[],
                alternative_options=[]
            )
            
        except Exception as e:
            logger.error(f"Error optimizing schedule: {e}")
            raise
    
    def _get_frequencies_for_service(self, service) -> List[SessionFrequency]:
        """Determine available frequencies based on service type."""
        if service.service_level == "elementary":
            return [SessionFrequency.WEEKLY, SessionFrequency.BIWEEKLY]
        elif service.service_level == "high_school":
            return [SessionFrequency.ONCE, SessionFrequency.WEEKLY, SessionFrequency.BIWEEKLY]
        elif service.service_level in ["college", "university"]:
            return [SessionFrequency.ONCE, SessionFrequency.WEEKLY, SessionFrequency.BIWEEKLY, SessionFrequency.MONTHLY]
        else:  # adult
            return list(SessionFrequency)
    
    def _get_durations_for_service(self, service) -> List[DurationOption]:
        """Get available duration options for a service."""
        durations = []
        
        # Map service duration range to DurationOption enum
        for option in DurationOption:
            if service.min_duration_minutes <= option.minutes <= service.max_duration_minutes:
                durations.append(option)
        
        return durations
    
    def _get_suggested_time_slots(self, service) -> List[TimeSlotPreference]:
        """Suggest time slots based on service level."""
        if service.service_level == "elementary":
            return [TimeSlotPreference.AFTERNOON, TimeSlotPreference.EARLY_AFTERNOON]
        elif service.service_level == "high_school":
            return [TimeSlotPreference.AFTERNOON, TimeSlotPreference.EVENING]
        elif service.service_level in ["college", "university"]:
            return [TimeSlotPreference.EVENING, TimeSlotPreference.AFTERNOON]
        else:  # adult
            return [TimeSlotPreference.EVENING, TimeSlotPreference.MORNING]
    
    def _get_popular_days(self, service) -> List[DayOfWeekPreference]:
        """Get popular days based on service level."""
        if service.service_level == "elementary":
            return [DayOfWeekPreference.TUESDAY, DayOfWeekPreference.WEDNESDAY, DayOfWeekPreference.THURSDAY]
        elif service.service_level in ["high_school", "college", "university"]:
            return [DayOfWeekPreference.TUESDAY, DayOfWeekPreference.THURSDAY, DayOfWeekPreference.SUNDAY]
        else:  # adult
            return [DayOfWeekPreference.SATURDAY, DayOfWeekPreference.SUNDAY, DayOfWeekPreference.WEDNESDAY]
    
    def _calculate_duration_pricing(self, durations: List[DurationOption]) -> Dict[str, float]:
        """Calculate pricing multipliers for different durations."""
        pricing = {}
        base_duration = 60  # 1 hour as base
        
        for duration in durations:
            if duration.minutes <= 45:
                # Shorter sessions have slightly higher per-minute rate
                pricing[duration.value] = (duration.minutes / base_duration) * 1.1
            elif duration.minutes >= 120:
                # Longer sessions have slight discount
                pricing[duration.value] = (duration.minutes / base_duration) * 0.95
            else:
                # Standard pricing
                pricing[duration.value] = duration.minutes / base_duration
        
        return pricing
    
    def _calculate_frequency_discounts(self) -> Dict[str, float]:
        """Calculate discounts for different frequencies."""
        return {
            SessionFrequency.ONCE.value: 0.0,
            SessionFrequency.WEEKLY.value: 5.0,      # 5% discount
            SessionFrequency.BIWEEKLY.value: 3.0,    # 3% discount
            SessionFrequency.MONTHLY.value: 0.0,
            SessionFrequency.CUSTOM.value: 0.0
        }
    
    def _validate_recurring_pattern(self, pattern: CreateRecurringPatternRequest):
        """Validate recurring pattern configuration."""
        if pattern.frequency == SessionFrequency.WEEKLY:
            if not pattern.days_of_week:
                raise ValidationError("Weekly pattern requires at least one day of week")
            
            # Check for duplicate days
            if len(pattern.days_of_week) != len(set(pattern.days_of_week)):
                raise ValidationError("Duplicate days in weekly pattern")
        
        elif pattern.frequency == SessionFrequency.MONTHLY:
            if not pattern.day_of_month and not pattern.week_of_month:
                raise ValidationError("Monthly pattern requires day_of_month or week_of_month")
            
            if pattern.day_of_month and pattern.day_of_month > 28:
                logger.warning("Day of month > 28 may not exist in all months")
        
        # Validate date range
        if pattern.end_date and pattern.end_date <= pattern.start_date:
            raise ValidationError("End date must be after start date")
        
        # Validate total sessions vs date range
        if pattern.total_sessions and pattern.end_date:
            # Calculate maximum possible sessions
            weeks = (pattern.end_date - pattern.start_date).days / 7
            if pattern.frequency == SessionFrequency.WEEKLY:
                max_sessions = int(weeks * len(pattern.days_of_week))
            elif pattern.frequency == SessionFrequency.BIWEEKLY:
                max_sessions = int(weeks / 2 * len(pattern.days_of_week))
            else:
                max_sessions = int(weeks * 4)  # Rough estimate
            
            if pattern.total_sessions > max_sessions:
                raise ValidationError(f"Total sessions ({pattern.total_sessions}) exceeds possible sessions in date range")
    
    def _calculate_next_session_date(
        self,
        frequency: SessionFrequency,
        start_date: date,
        days_of_week: Optional[List[DayOfWeekPreference]],
        day_of_month: Optional[int],
        week_of_month: Optional[int]
    ) -> date:
        """Calculate the next session date based on pattern."""
        today = date.today()
        
        if frequency == SessionFrequency.ONCE:
            return start_date if start_date >= today else None
        
        elif frequency == SessionFrequency.WEEKLY:
            # Find next occurrence of specified weekday
            current = max(start_date, today)
            weekday_numbers = [d.weekday_number for d in days_of_week]
            
            for _ in range(7):  # Check next 7 days
                if current.weekday() in weekday_numbers:
                    return current
                current += timedelta(days=1)
        
        elif frequency == SessionFrequency.BIWEEKLY:
            # Similar to weekly but skip alternate weeks
            weeks_since_start = (today - start_date).days // 7
            if weeks_since_start % 2 == 1:
                # Skip this week
                current = today + timedelta(days=7 - today.weekday())
            else:
                current = max(start_date, today)
            
            weekday_numbers = [d.weekday_number for d in days_of_week]
            for _ in range(14):  # Check next 14 days
                if current.weekday() in weekday_numbers:
                    return current
                current += timedelta(days=1)
        
        elif frequency == SessionFrequency.MONTHLY:
            # Find next occurrence in month
            current = max(start_date, today)
            
            if day_of_month:
                # Specific day of month
                if current.day <= day_of_month:
                    try:
                        return current.replace(day=day_of_month)
                    except ValueError:
                        # Day doesn't exist in this month, go to next month
                        pass
                
                # Go to next month
                if current.month == 12:
                    next_month = current.replace(year=current.year + 1, month=1, day=1)
                else:
                    next_month = current.replace(month=current.month + 1, day=1)
                
                try:
                    return next_month.replace(day=day_of_month)
                except ValueError:
                    # Use last day of month if day doesn't exist
                    import calendar
                    last_day = calendar.monthrange(next_month.year, next_month.month)[1]
                    return next_month.replace(day=min(day_of_month, last_day))
        
        return start_date
    
    def _distribute_hours(
        self,
        subjects: List[str],
        total_hours: float
    ) -> Dict[str, float]:
        """Distribute total hours among subjects."""
        if not subjects:
            return {}
        
        # Simple equal distribution for now
        hours_per_subject = total_hours / len(subjects)
        
        # Round to nearest 0.5 hour
        hours_per_subject = round(hours_per_subject * 2) / 2
        
        return {subject: hours_per_subject for subject in subjects}
    
    def _create_multi_session_pattern(
        self,
        subject: str,
        hours_per_week: float,
        optimize_for: str
    ) -> RecurringSchedulePattern:
        """Create pattern for multiple sessions per week."""
        # Determine session count and duration
        if hours_per_week <= 2:
            sessions = 2
            duration = DurationOption.SIXTY_MIN
        elif hours_per_week <= 3:
            sessions = 2
            duration = DurationOption.NINETY_MIN
        else:
            sessions = 3
            duration = DurationOption.SIXTY_MIN
        
        # Select days based on optimization
        if optimize_for == "consistency":
            days = [DayOfWeekPreference.TUESDAY, DayOfWeekPreference.THURSDAY]
        elif optimize_for == "flexibility":
            days = [DayOfWeekPreference.MONDAY, DayOfWeekPreference.WEDNESDAY]
        else:  # cost
            days = [DayOfWeekPreference.SATURDAY]  # One longer session
            duration = DurationOption.ONE_TWENTY_MIN
        
        return RecurringSchedulePattern(
            pattern_name=f"{subject} - {sessions}x/week",
            frequency=SessionFrequency.WEEKLY,
            days_of_week=days[:sessions],
            start_time=time(16, 0),  # 4 PM default
            duration=duration,
            start_date=date.today() + timedelta(days=7),
            allow_reschedule=True
        )
    
    def _create_single_session_pattern(
        self,
        subject: str,
        hours_per_week: float,
        optimize_for: str
    ) -> RecurringSchedulePattern:
        """Create pattern for single session per week."""
        # Determine duration
        if hours_per_week <= 1:
            duration = DurationOption.SIXTY_MIN
        elif hours_per_week <= 1.5:
            duration = DurationOption.NINETY_MIN
        else:
            duration = DurationOption.ONE_TWENTY_MIN
        
        # Select day based on optimization
        if optimize_for == "consistency":
            day = DayOfWeekPreference.WEDNESDAY
        elif optimize_for == "flexibility":
            day = DayOfWeekPreference.THURSDAY
        else:  # cost
            day = DayOfWeekPreference.SUNDAY  # Weekend might be cheaper
        
        return RecurringSchedulePattern(
            pattern_name=f"{subject} - Weekly",
            frequency=SessionFrequency.WEEKLY,
            days_of_week=[day],
            start_time=time(17, 0),  # 5 PM default
            duration=duration,
            start_date=date.today() + timedelta(days=7),
            allow_reschedule=True
        )
    
    def _calculate_optimization_score(
        self,
        patterns: List[RecurringSchedulePattern],
        optimize_for: str
    ) -> float:
        """Calculate how well the schedule meets optimization criteria."""
        if optimize_for == "consistency":
            # Check for regular spacing and same time slots
            if all(p.frequency == SessionFrequency.WEEKLY for p in patterns):
                return 0.9
            return 0.7
        
        elif optimize_for == "flexibility":
            # Check for spread across different days/times
            unique_days = set()
            for p in patterns:
                unique_days.update(p.days_of_week)
            
            return min(len(unique_days) / 5, 1.0)  # More days = more flexible
        
        else:  # cost
            # Check for grouped sessions and off-peak times
            weekend_sessions = sum(
                1 for p in patterns
                for d in p.days_of_week
                if d in [DayOfWeekPreference.SATURDAY, DayOfWeekPreference.SUNDAY]
            )
            
            return weekend_sessions / len(patterns) if patterns else 0.5
    
    async def _create_preferences(
        self,
        entity_type: str,
        entity_id: int,
        preferences: UpdateSchedulingPreferencesRequest
    ) -> SchedulingPreferencesResponse:
        """Create new scheduling preferences."""
        query = text(f"""
            INSERT INTO scheduling_preferences (
                {entity_type}_id, preferred_frequency, sessions_per_week,
                sessions_per_month, preferred_duration, preferred_time_slots,
                preferred_days, earliest_start_time, latest_end_time,
                is_flexible_on_time, is_flexible_on_duration,
                is_flexible_on_frequency, scheduling_notes,
                created_at, updated_at
            ) VALUES (
                :{entity_type}_id, :preferred_frequency, :sessions_per_week,
                :sessions_per_month, :preferred_duration, :preferred_time_slots,
                :preferred_days, :earliest_start_time, :latest_end_time,
                :is_flexible_on_time, :is_flexible_on_duration,
                :is_flexible_on_frequency, :scheduling_notes,
                :created_at, :updated_at
            )
            RETURNING *
        """)
        
        params = {
            f'{entity_type}_id': entity_id,
            **preferences.model_dump(exclude_unset=True),
            'created_at': now_est(),
            'updated_at': now_est()
        }
        
        # Convert lists to PostgreSQL arrays
        if 'preferred_time_slots' in params and params['preferred_time_slots']:
            params['preferred_time_slots'] = [s.value for s in params['preferred_time_slots']]
        if 'preferred_days' in params and params['preferred_days']:
            params['preferred_days'] = [d.value for d in params['preferred_days']]
        
        result = await self.db.execute(query, params)
        row = result.fetchone()
        
        await self.db.commit()
        return SchedulingPreferencesResponse(**dict(row._mapping))
    
    async def _update_preferences(
        self,
        preference_id: int,
        updates: UpdateSchedulingPreferencesRequest
    ) -> SchedulingPreferencesResponse:
        """Update existing scheduling preferences."""
        # Build update query
        update_fields = []
        params = {'preference_id': preference_id, 'updated_at': now_est()}
        
        for field, value in updates.model_dump(exclude_unset=True).items():
            update_fields.append(f"{field} = :{field}")
            
            # Convert enums to values
            if field in ['preferred_time_slots', 'preferred_days'] and value:
                params[field] = [item.value for item in value]
            else:
                params[field] = value
        
        if not update_fields:
            # No updates, return existing
            query = text("""
                SELECT * FROM scheduling_preferences
                WHERE scheduling_preference_id = :preference_id
            """)
            result = await self.db.execute(query, {'preference_id': preference_id})
            row = result.fetchone()
            return SchedulingPreferencesResponse(**dict(row._mapping))
        
        update_fields.append("updated_at = :updated_at")
        
        query = text(f"""
            UPDATE scheduling_preferences
            SET {', '.join(update_fields)}
            WHERE scheduling_preference_id = :preference_id
            RETURNING *
        """)
        
        result = await self.db.execute(query, params)
        row = result.fetchone()
        
        await self.db.commit()
        return SchedulingPreferencesResponse(**dict(row._mapping))