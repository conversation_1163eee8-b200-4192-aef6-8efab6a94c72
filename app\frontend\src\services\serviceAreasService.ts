import api from './api';
import { Coordinates } from '../../../services/geocodingService';

// Service Area Types and Interfaces
export interface ServiceArea {
  area_id: number;
  tutor_id: number;
  area_type: 'postal_code' | 'radius' | 'polygon' | 'city';
  postal_codes?: string[];
  center_postal_code?: string;
  radius_km?: number;
  polygon_points?: Coordinates[];
  city_names?: string[];
  is_active: boolean;
  max_distance_km?: number;
  travel_fee_per_km?: number;
  created_at: string;
  updated_at: string;
}

export interface ServiceAreaRequest {
  area_type: 'postal_code' | 'radius' | 'polygon' | 'city';
  postal_codes?: string[];
  center_postal_code?: string;
  radius_km?: number;
  polygon_points?: Coordinates[];
  city_names?: string[];
  max_distance_km?: number;
  travel_fee_per_km?: number;
}

export interface ServiceAreaCoverage {
  postal_code: string;
  is_covered: boolean;
  tutors_available: number;
  average_distance_km?: number;
  estimated_travel_fee?: number;
  nearby_areas?: Array<{
    postal_code: string;
    distance_km: number;
    tutors_available: number;
  }>;
}

export interface ServiceAreaStats {
  total_areas: number;
  total_coverage_km2: number;
  postal_codes_covered: number;
  cities_covered: string[];
  tutors_by_area: Record<string, number>;
  popular_areas: Array<{
    area: string;
    tutor_count: number;
    client_count: number;
  }>;
}

export interface TutorServiceAreaSummary {
  tutor_id: number;
  tutor_name: string;
  areas: ServiceArea[];
  total_coverage_km2: number;
  postal_codes_count: number;
  primary_area?: string;
  will_travel: boolean;
  max_travel_distance_km?: number;
}

export interface BulkServiceAreaUpdate {
  tutor_ids: number[];
  operation: 'add' | 'remove' | 'replace';
  areas: ServiceAreaRequest[];
}

export interface ServiceAreaValidation {
  is_valid: boolean;
  errors?: string[];
  warnings?: string[];
  suggested_alternatives?: ServiceArea[];
  overlap_with_existing?: Array<{
    area_id: number;
    overlap_percentage: number;
  }>;
}

class ServiceAreasService {
  /**
   * Get service areas for a tutor
   */
  async getTutorServiceAreas(tutorId: number): Promise<ServiceArea[]> {
    try {
      const response = await api.get<ServiceArea[]>(`/service-areas/tutor/${tutorId}`);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching tutor service areas:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch service areas');
    }
  }

  /**
   * Create or update service area
   */
  async upsertServiceArea(tutorId: number, area: ServiceAreaRequest): Promise<ServiceArea> {
    try {
      const response = await api.post<ServiceArea>(`/service-areas/tutor/${tutorId}`, area);
      return response.data;
    } catch (error: any) {
      console.error('Error creating/updating service area:', error);
      throw new Error(error.response?.data?.detail || 'Failed to save service area');
    }
  }

  /**
   * Delete service area
   */
  async deleteServiceArea(areaId: number): Promise<{ message: string }> {
    try {
      const response = await api.delete<{ message: string }>(`/service-areas/${areaId}`);
      return response.data;
    } catch (error: any) {
      console.error('Error deleting service area:', error);
      throw new Error(error.response?.data?.detail || 'Failed to delete service area');
    }
  }

  /**
   * Check if postal code is covered
   */
  async checkCoverage(postalCode: string): Promise<ServiceAreaCoverage> {
    try {
      const response = await api.get<ServiceAreaCoverage>('/service-areas/coverage', {
        params: { postal_code: postalCode }
      });
      return response.data;
    } catch (error: any) {
      console.error('Error checking coverage:', error);
      throw new Error(error.response?.data?.detail || 'Failed to check coverage');
    }
  }

  /**
   * Batch check coverage for multiple postal codes
   */
  async batchCheckCoverage(postalCodes: string[]): Promise<Record<string, ServiceAreaCoverage>> {
    try {
      const response = await api.post<Record<string, ServiceAreaCoverage>>('/service-areas/coverage/batch', {
        postal_codes: postalCodes
      });
      return response.data;
    } catch (error: any) {
      console.error('Error batch checking coverage:', error);
      throw new Error(error.response?.data?.detail || 'Failed to check coverage');
    }
  }

  /**
   * Get service area statistics
   */
  async getServiceAreaStats(): Promise<ServiceAreaStats> {
    try {
      const response = await api.get<ServiceAreaStats>('/service-areas/stats');
      return response.data;
    } catch (error: any) {
      console.error('Error fetching service area stats:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch stats');
    }
  }

  /**
   * Search tutors by service area
   */
  async searchTutorsByArea(postalCode: string, radiusKm?: number): Promise<TutorServiceAreaSummary[]> {
    try {
      const response = await api.get<TutorServiceAreaSummary[]>('/service-areas/search', {
        params: { postal_code: postalCode, radius_km: radiusKm }
      });
      return response.data;
    } catch (error: any) {
      console.error('Error searching tutors by area:', error);
      throw new Error(error.response?.data?.detail || 'Failed to search tutors');
    }
  }

  /**
   * Bulk update service areas
   */
  async bulkUpdateServiceAreas(update: BulkServiceAreaUpdate): Promise<{
    success_count: number;
    error_count: number;
    errors?: Record<number, string>;
  }> {
    try {
      const response = await api.post<{
        success_count: number;
        error_count: number;
        errors?: Record<number, string>;
      }>('/service-areas/bulk-update', update);
      return response.data;
    } catch (error: any) {
      console.error('Error bulk updating service areas:', error);
      throw new Error(error.response?.data?.detail || 'Failed to bulk update');
    }
  }

  /**
   * Validate service area before creation
   */
  async validateServiceArea(area: ServiceAreaRequest): Promise<ServiceAreaValidation> {
    try {
      const response = await api.post<ServiceAreaValidation>('/service-areas/validate', area);
      return response.data;
    } catch (error: any) {
      console.error('Error validating service area:', error);
      throw new Error(error.response?.data?.detail || 'Failed to validate area');
    }
  }

  /**
   * Get suggested service areas based on tutor location
   */
  async getSuggestedAreas(tutorPostalCode: string): Promise<ServiceArea[]> {
    try {
      const response = await api.get<ServiceArea[]>('/service-areas/suggestions', {
        params: { postal_code: tutorPostalCode }
      });
      return response.data;
    } catch (error: any) {
      console.error('Error getting suggested areas:', error);
      return [];
    }
  }

  /**
   * Calculate area coverage in km²
   */
  calculateAreaCoverage(area: ServiceArea): number {
    if (area.area_type === 'radius' && area.radius_km) {
      // Area of circle = πr²
      return Math.PI * area.radius_km * area.radius_km;
    } else if (area.area_type === 'postal_code' && area.postal_codes) {
      // Rough estimate: average Canadian postal code covers ~10 km²
      return area.postal_codes.length * 10;
    } else if (area.area_type === 'polygon' && area.polygon_points) {
      // Calculate polygon area using shoelace formula
      return this.calculatePolygonArea(area.polygon_points);
    }
    return 0;
  }

  /**
   * Calculate polygon area using shoelace formula
   */
  private calculatePolygonArea(points: Coordinates[]): number {
    if (points.length < 3) return 0;

    let area = 0;
    for (let i = 0; i < points.length; i++) {
      const j = (i + 1) % points.length;
      area += points[i].longitude * points[j].latitude;
      area -= points[j].longitude * points[i].latitude;
    }
    area = Math.abs(area) / 2;

    // Convert to km² (rough approximation)
    const kmPerDegree = 111; // Average km per degree of latitude
    return area * kmPerDegree * kmPerDegree;
  }

  /**
   * Format service area for display
   */
  formatServiceArea(area: ServiceArea): string {
    switch (area.area_type) {
      case 'postal_code':
        const count = area.postal_codes?.length || 0;
        return `${count} postal code${count !== 1 ? 's' : ''}`;
      
      case 'radius':
        return `${area.radius_km}km radius from ${area.center_postal_code}`;
      
      case 'city':
        const cities = area.city_names?.join(', ') || '';
        return `Cities: ${cities}`;
      
      case 'polygon':
        const coverage = this.calculateAreaCoverage(area);
        return `Custom area (~${Math.round(coverage)} km²)`;
      
      default:
        return 'Custom service area';
    }
  }

  /**
   * Check if postal code is within service area
   */
  isPostalCodeInArea(postalCode: string, area: ServiceArea): boolean {
    if (area.area_type === 'postal_code' && area.postal_codes) {
      return area.postal_codes.includes(postalCode.toUpperCase());
    }
    
    // For other area types, would need to use geocoding service
    // to convert postal code to coordinates and check
    return false;
  }

  /**
   * Merge overlapping service areas
   */
  mergeServiceAreas(areas: ServiceArea[]): ServiceArea[] {
    // Group by area type
    const grouped = areas.reduce((acc, area) => {
      const key = area.area_type;
      if (!acc[key]) acc[key] = [];
      acc[key].push(area);
      return acc;
    }, {} as Record<string, ServiceArea[]>);

    const merged: ServiceArea[] = [];

    // Merge postal code areas
    if (grouped.postal_code) {
      const allPostalCodes = new Set<string>();
      grouped.postal_code.forEach(area => {
        area.postal_codes?.forEach(pc => allPostalCodes.add(pc));
      });

      if (allPostalCodes.size > 0) {
        merged.push({
          ...grouped.postal_code[0],
          postal_codes: Array.from(allPostalCodes)
        });
      }
    }

    // Add other area types without merging (more complex logic needed)
    Object.entries(grouped).forEach(([type, areas]) => {
      if (type !== 'postal_code') {
        merged.push(...areas);
      }
    });

    return merged;
  }

  /**
   * Estimate travel time based on distance
   */
  estimateTravelTime(distanceKm: number, transportMode: 'car' | 'transit' | 'bike' = 'car'): number {
    const speeds = {
      car: 50,     // 50 km/h average in city
      transit: 30, // 30 km/h average for public transit
      bike: 15     // 15 km/h average cycling speed
    };

    const speedKmh = speeds[transportMode];
    return Math.round((distanceKm / speedKmh) * 60); // Return minutes
  }
}

export const serviceAreasService = new ServiceAreasService();