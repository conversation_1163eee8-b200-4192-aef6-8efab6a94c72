import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import api from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import { UserRoleType } from '../types/auth';

interface ConsentValidation {
  is_valid: boolean;
  missing_mandatory: string[];
  expired_consents: string[];
  message: string;
}

interface UseConsentCheckOptions {
  redirectTo?: string;
  checkOnMount?: boolean;
  blockAccess?: boolean;
}

export const useConsentCheck = (options: UseConsentCheckOptions = {}) => {
  const {
    redirectTo = '/consent-required',
    checkOnMount = true,
    blockAccess = false
  } = options;
  
  const navigate = useNavigate();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [validation, setValidation] = useState<ConsentValidation | null>(null);
  const [needsConsent, setNeedsConsent] = useState(false);

  useEffect(() => {
    if (checkOnMount && user) {
      checkConsents();
    }
  }, [checkOnMount, user]);

  const checkConsents = async () => {
    try {
      setLoading(true);
      const response = await api.get<ConsentValidation>('/consent/validate');
      setValidation(response.data);
      
      const hasIssues = !response.data.is_valid && 
        (response.data.missing_mandatory.length > 0 || response.data.expired_consents.length > 0);
      
      setNeedsConsent(hasIssues);
      
      // If blocking access and has issues, redirect
      if (blockAccess && hasIssues) {
        navigate(redirectTo);
      }
      
      return response.data;
    } catch (error) {
      console.error('Error checking consents:', error);
      return null;
    } finally {
      setLoading(false);
    }
  };

  const getTutorSpecificConsents = () => {
    if (user?.activeRole === UserRoleType.TUTOR) {
      return ['terms_of_service', 'privacy_policy', 'tutor_employment_agreement'];
    }
    return ['terms_of_service', 'privacy_policy'];
  };

  const hasRequiredConsents = () => {
    if (!validation) return false;
    
    const requiredConsents = getTutorSpecificConsents();
    const missingRequired = validation.missing_mandatory.filter(
      consent => requiredConsents.includes(consent)
    );
    
    return missingRequired.length === 0;
  };

  return {
    loading,
    validation,
    needsConsent,
    checkConsents,
    hasRequiredConsents,
    getTutorSpecificConsents
  };
};