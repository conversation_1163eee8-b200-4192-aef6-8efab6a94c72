-- Rollback Migration 018: Remove User Formatting Preferences
-- 
-- This rollback script removes all formatting preference tables,
-- functions, triggers, and views created in migration 018.

-- Drop analytics view
DROP VIEW IF EXISTS formatting_preferences_analytics;

-- Drop main view
DROP VIEW IF EXISTS user_formatting_preferences_with_language;

-- Drop triggers
DROP TRIGGER IF EXISTS trigger_log_formatting_preference_change ON user_formatting_preferences;
DROP TRIGGER IF EXISTS trigger_update_formatting_preferences_updated_at ON user_formatting_preferences;

-- Drop functions
DROP FUNCTION IF EXISTS log_formatting_preference_change();
DROP FUNCTION IF EXISTS update_formatting_preferences_updated_at();

-- Drop indexes
DROP INDEX IF EXISTS idx_formatting_preferences_history_field_name;
DROP INDEX IF EXISTS idx_formatting_preferences_history_created_at;
DROP INDEX IF EXISTS idx_formatting_preferences_history_user_id;
DROP INDEX IF EXISTS idx_formatting_preferences_updated_at;
DROP INDEX IF EXISTS idx_formatting_preferences_user_id;

-- Drop tables (history table first due to foreign key)
DROP TABLE IF EXISTS user_formatting_preferences_history;
DROP TABLE IF EXISTS user_formatting_preferences;

-- Drop sequences if they exist
DROP SEQUENCE IF EXISTS user_formatting_preferences_formatting_preference_id_seq;
DROP SEQUENCE IF EXISTS user_formatting_preferences_history_history_id_seq;