import React from 'react';
import { clsx } from 'clsx';
import { Avatar } from '../common/Avatar';
import { Badge } from '../common/Badge';
import { Mail, Phone, MapPin, Calendar } from 'lucide-react';

interface UserCardProps {
  user: {
    id: string;
    name: string;
    email: string;
    phone?: string;
    avatar?: string;
    role?: string;
    location?: string;
    status?: 'online' | 'offline' | 'busy' | 'away';
    joinedDate?: string;
    bio?: string;
  };
  variant?: 'compact' | 'detailed';
  onClick?: () => void;
  actions?: React.ReactNode;
  className?: string;
}

export const UserCard: React.FC<UserCardProps> = ({
  user,
  variant = 'compact',
  onClick,
  actions,
  className,
}) => {
  if (variant === 'compact') {
    return (
      <div
        onClick={onClick}
        className={clsx(
          'flex items-center gap-4 p-4 bg-white border border-border-primary rounded-lg',
          'transition-all duration-200',
          onClick && 'cursor-pointer hover:shadow-soft hover:border-accent-red',
          className
        )}
      >
        <Avatar
          src={user.avatar}
          name={user.name}
          size="md"
          status={user.status}
        />
        
        <div className="flex-1 min-w-0">
          <h3 className="text-sm font-medium text-text-primary truncate">
            {user.name}
          </h3>
          <p className="text-sm text-text-secondary truncate">
            {user.email}
          </p>
        </div>
        
        {user.role && (
          <Badge variant="secondary" size="sm">
            {user.role}
          </Badge>
        )}
        
        {actions && (
          <div className="flex-shrink-0">
            {actions}
          </div>
        )}
      </div>
    );
  }

  return (
    <div
      onClick={onClick}
      className={clsx(
        'bg-white border border-border-primary rounded-lg shadow-subtle',
        'transition-all duration-200',
        onClick && 'cursor-pointer hover:shadow-soft hover:border-accent-red',
        className
      )}
    >
      <div className="p-6">
        <div className="flex items-start gap-4">
          <Avatar
            src={user.avatar}
            name={user.name}
            size="lg"
            status={user.status}
          />
          
          <div className="flex-1">
            <div className="flex items-start justify-between">
              <div>
                <h3 className="text-lg font-semibold text-text-primary">
                  {user.name}
                </h3>
                {user.role && (
                  <Badge variant="secondary" size="sm" className="mt-1">
                    {user.role}
                  </Badge>
                )}
              </div>
              {actions}
            </div>
            
            {user.bio && (
              <p className="mt-3 text-sm text-text-secondary">
                {user.bio}
              </p>
            )}
            
            <div className="mt-4 space-y-2">
              <div className="flex items-center gap-2 text-sm text-text-secondary">
                <Mail className="w-4 h-4 text-text-muted" />
                {user.email}
              </div>
              
              {user.phone && (
                <div className="flex items-center gap-2 text-sm text-text-secondary">
                  <Phone className="w-4 h-4 text-text-muted" />
                  {user.phone}
                </div>
              )}
              
              {user.location && (
                <div className="flex items-center gap-2 text-sm text-text-secondary">
                  <MapPin className="w-4 h-4 text-text-muted" />
                  {user.location}
                </div>
              )}
              
              {user.joinedDate && (
                <div className="flex items-center gap-2 text-sm text-text-secondary">
                  <Calendar className="w-4 h-4 text-text-muted" />
                  Joined {user.joinedDate}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

interface UserGridProps {
  children: React.ReactNode;
  columns?: 1 | 2 | 3 | 4;
  className?: string;
}

export const UserGrid: React.FC<UserGridProps> = ({
  children,
  columns = 3,
  className,
}) => {
  const columnClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  };

  return (
    <div className={clsx('grid gap-4', columnClasses[columns], className)}>
      {children}
    </div>
  );
};

export default UserCard;