"""
Notification service for sending SMS and push notifications.
"""

import logging
from typing import Optional
from datetime import date
from decimal import Decimal

logger = logging.getLogger(__name__)


class NotificationService:
    """Service for sending notifications."""
    
    def __init__(self):
        pass
    
    async def send_invoice_sms(
        self,
        client_id: int,
        invoice_number: str,
        amount: Decimal,
        due_date: date
    ) -> None:
        """Send SMS notification for new invoice."""
        # TODO: Implement SMS sending via Twilio
        logger.info(
            f"Would send invoice SMS to client {client_id}: "
            f"Invoice {invoice_number} for ${amount:.2f} due {due_date}"
        )
    
    async def send_payment_confirmation(
        self,
        client_id: int,
        invoice_number: str,
        amount: Decimal
    ) -> None:
        """Send payment confirmation notification."""
        # TODO: Implement payment confirmation notification
        logger.info(
            f"Would send payment confirmation to client {client_id}: "
            f"Payment of ${amount:.2f} received for invoice {invoice_number}"
        )
    
    async def send_overdue_invoice_sms(
        self,
        phone_number: str,
        invoice_number: str,
        amount: Decimal,
        days_overdue: int
    ) -> None:
        """Send SMS for overdue invoice."""
        # TODO: Implement overdue SMS sending
        logger.info(
            f"Would send overdue SMS to {phone_number}: "
            f"Invoice {invoice_number} for ${amount:.2f} is {days_overdue} days overdue"
        )
    
    async def send_payment_confirmation_to_tutor(
        self,
        tutor_id: int,
        amount: Decimal,
        week_start: date,
        week_end: date
    ) -> None:
        """Send payment confirmation to tutor."""
        # TODO: Implement tutor payment confirmation
        logger.info(
            f"Would send payment confirmation to tutor {tutor_id}: "
            f"Payment of ${amount:.2f} for week {week_start} to {week_end}"
        )


# Dependency injection
def get_notification_service() -> NotificationService:
    """Get notification service instance."""
    return NotificationService()