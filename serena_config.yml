# Serena Configuration for TutorAide App

# Project settings
project:
  path: "."
  name: "TutorAide App"
  
# Tool settings
tools:
  # Enable code retrieval and editing
  code_retrieval:
    enabled: true
    max_results: 20
    
  code_editing:
    enabled: true
    auto_format: true
    
  # File operations
  file_operations:
    enabled: true
    allowed_extensions:
      - .py
      - .md
      - .txt
      - .json
      - .yml
      - .yaml
      - .sql
      - .env
      - .gitignore
      
  # Shell commands
  shell:
    enabled: true
    allowed_commands:
      - uv
      - git
      - pytest
      - black
      - ruff
      - mypy
      - uvicorn
      
# Context settings
context:
  max_file_size: 100000  # 100KB
  include_tests: true
  include_docs: true
  
# Memory settings
memory:
  enabled: true
  storage_path: "./serena/memories"
  
# Logging
logging:
  level: INFO
  file: "./logs/serena.log"