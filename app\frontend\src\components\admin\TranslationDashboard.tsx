import React, { useState, useEffect } from 'react';
import { 
  Languages, 
  FileText, 
  AlertTriangle, 
  CheckCircle, 
  Upload, 
  Download, 
  Search,
  Filter,
  Edit3,
  Mail,
  MessageSquare,
  BarChart3,
  RefreshCw
} from 'lucide-react';
import { useTranslation } from '../../hooks/useTranslation';
import { useLanguage } from '../../hooks/useLanguage';
import { TranslationEditor } from './TranslationEditor';
import { TemplateManager } from './TemplateManager';

interface TranslationStats {
  languages: string[];
  total_keys_by_language: Record<string, number>;
  completion_percentage: Record<string, number>;
  missing_keys_count: Record<string, number>;
  categories: string[];
}

interface ValidationResult {
  is_valid: boolean;
  errors: string[];
  warnings: string[];
  missing_keys: Record<string, string[]>;
  statistics: Record<string, any>;
}

export const TranslationDashboard: React.FC = () => {
  const { t } = useTranslation();
  const { getTranslationStats, validateTranslations, reloadTranslations } = useLanguage();
  
  const [stats, setStats] = useState<TranslationStats | null>(null);
  const [validation, setValidation] = useState<ValidationResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'editor' | 'templates' | 'validation'>('overview');

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      const [statsData, validationData] = await Promise.all([
        getTranslationStats(),
        validateTranslations()
      ]);
      
      if (statsData) setStats(statsData);
      if (validationData) setValidation(validationData);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await reloadTranslations();
      await loadDashboardData();
    } catch (error) {
      console.error('Failed to refresh translations:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const getCompletionColor = (percentage: number) => {
    if (percentage >= 95) return 'text-green-600 bg-green-50 border-green-200';
    if (percentage >= 80) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    return 'text-red-600 bg-red-50 border-red-200';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
        <span className="ml-3 text-gray-600">{t('common.loading')}</span>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Languages className="w-6 h-6 text-red-600" />
          <h1 className="text-2xl font-bold text-gray-900">
            Translation Management
          </h1>
        </div>
        
        <button
          onClick={handleRefresh}
          disabled={refreshing}
          className={`
            flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg font-medium 
            hover:bg-red-700 transition-colors
            ${refreshing ? 'opacity-50 cursor-not-allowed' : ''}
          `}
        >
          <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
          {refreshing ? 'Refreshing...' : 'Refresh'}
        </button>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8">
          {[
            { id: 'overview', label: 'Overview', icon: BarChart3 },
            { id: 'editor', label: 'Translation Editor', icon: Edit3 },
            { id: 'templates', label: 'Email & SMS Templates', icon: Mail },
            { id: 'validation', label: 'Validation & Quality', icon: CheckCircle }
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`
                  flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm
                  ${activeTab === tab.id
                    ? 'border-red-500 text-red-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                `}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Overview Tab */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {stats && Object.entries(stats.total_keys_by_language).map(([language, count]) => {
              const completion = stats.completion_percentage[language] || 0;
              const missing = stats.missing_keys_count[language] || 0;
              
              return (
                <div key={language} className="bg-white rounded-lg border border-gray-200 p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-900">
                      {language === 'en' ? 'English' : 'Français (Québec)'}
                    </h3>
                    <span className="text-2xl">{language === 'en' ? '🇨🇦' : '🇨🇦'}</span>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Total Keys:</span>
                      <span className="font-medium">{count}</span>
                    </div>
                    
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Missing:</span>
                      <span className={`font-medium ${missing > 0 ? 'text-red-600' : 'text-green-600'}`}>
                        {missing}
                      </span>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Completion:</span>
                        <span className="font-medium">{completion.toFixed(1)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full transition-all duration-300 ${
                            completion >= 95 ? 'bg-green-500' :
                            completion >= 80 ? 'bg-yellow-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${Math.min(completion, 100)}%` }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Recent Issues */}
          {validation && (validation.errors.length > 0 || validation.warnings.length > 0) && (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <AlertTriangle className="w-5 h-5 text-orange-500" />
                Translation Issues
              </h2>
              
              <div className="space-y-4">
                {validation.errors.length > 0 && (
                  <div>
                    <h3 className="font-medium text-red-600 mb-2">Errors ({validation.errors.length})</h3>
                    <ul className="space-y-1">
                      {validation.errors.slice(0, 5).map((error, index) => (
                        <li key={index} className="text-sm text-red-700 bg-red-50 p-2 rounded">
                          {error}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
                
                {validation.warnings.length > 0 && (
                  <div>
                    <h3 className="font-medium text-yellow-600 mb-2">Warnings ({validation.warnings.length})</h3>
                    <ul className="space-y-1">
                      {validation.warnings.slice(0, 5).map((warning, index) => (
                        <li key={index} className="text-sm text-yellow-700 bg-yellow-50 p-2 rounded">
                          {warning}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Quick Actions */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <button
                onClick={() => setActiveTab('editor')}
                className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:border-red-300 hover:bg-red-50 transition-colors"
              >
                <Edit3 className="w-5 h-5 text-red-600" />
                <div className="text-left">
                  <div className="font-medium text-gray-900">Edit Translations</div>
                  <div className="text-sm text-gray-600">Modify existing translations</div>
                </div>
              </button>
              
              <button
                onClick={() => setActiveTab('templates')}
                className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors"
              >
                <Mail className="w-5 h-5 text-green-600" />
                <div className="text-left">
                  <div className="font-medium text-gray-900">Manage Templates</div>
                  <div className="text-sm text-gray-600">Email & SMS templates</div>
                </div>
              </button>
              
              <button
                onClick={() => setActiveTab('validation')}
                className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:border-yellow-300 hover:bg-yellow-50 transition-colors"
              >
                <CheckCircle className="w-5 h-5 text-yellow-600" />
                <div className="text-left">
                  <div className="font-medium text-gray-900">Quality Check</div>
                  <div className="text-sm text-gray-600">Validate translations</div>
                </div>
              </button>
              
              <button
                className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-colors"
              >
                <Download className="w-5 h-5 text-purple-600" />
                <div className="text-left">
                  <div className="font-medium text-gray-900">Export Data</div>
                  <div className="text-sm text-gray-600">Download translations</div>
                </div>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Editor Tab */}
      {activeTab === 'editor' && <TranslationEditor />}

      {/* Templates Tab */}
      {activeTab === 'templates' && <TemplateManager />}

      {/* Validation Tab */}
      {activeTab === 'validation' && validation && (
        <div className="space-y-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <CheckCircle className="w-5 h-5 text-red-600" />
              Translation Validation Report
            </h2>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Overall Status */}
              <div className={`p-4 rounded-lg border ${validation.is_valid ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
                <div className="flex items-center gap-2 mb-2">
                  {validation.is_valid ? (
                    <CheckCircle className="w-5 h-5 text-green-600" />
                  ) : (
                    <AlertTriangle className="w-5 h-5 text-red-600" />
                  )}
                  <span className={`font-medium ${validation.is_valid ? 'text-green-800' : 'text-red-800'}`}>
                    {validation.is_valid ? 'All validations passed' : 'Validation issues found'}
                  </span>
                </div>
                <div className="text-sm text-gray-600">
                  {validation.errors.length} errors, {validation.warnings.length} warnings
                </div>
              </div>

              {/* Missing Keys Summary */}
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <h3 className="font-medium text-red-800 mb-2">Missing Translations</h3>
                {Object.entries(validation.missing_keys).map(([language, keys]) => (
                  <div key={language} className="flex justify-between text-sm">
                    <span className="text-red-700">
                      {language === 'en' ? 'English' : 'Français'}:
                    </span>
                    <span className="text-red-900 font-medium">{keys.length} missing</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Detailed Issues */}
            {(validation.errors.length > 0 || validation.warnings.length > 0) && (
              <div className="mt-6 space-y-4">
                {validation.errors.length > 0 && (
                  <div>
                    <h3 className="font-medium text-red-600 mb-3">Errors Requiring Attention</h3>
                    <div className="space-y-2">
                      {validation.errors.map((error, index) => (
                        <div key={index} className="p-3 bg-red-50 border border-red-200 rounded text-sm text-red-800">
                          {error}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {validation.warnings.length > 0 && (
                  <div>
                    <h3 className="font-medium text-yellow-600 mb-3">Warnings</h3>
                    <div className="space-y-2">
                      {validation.warnings.map((warning, index) => (
                        <div key={index} className="p-3 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-800">
                          {warning}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
