import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import Button from '../common/Button';
import { Card } from '../common/Card';
import { Shield, Smartphone, Mail, Key, ArrowLeft } from 'lucide-react';
import toast from 'react-hot-toast';
import { motion, AnimatePresence } from 'framer-motion';
import clsx from 'clsx';

interface TwoFactorVerificationProps {
  method: '2fa' | 'sms' | 'email';
  onVerify: (code: string) => Promise<void>;
  onResend?: () => Promise<void>;
  onBack?: () => void;
  phoneNumber?: string;
  email?: string;
}

export const TwoFactorVerification: React.FC<TwoFactorVerificationProps> = ({
  method,
  onVerify,
  onResend,
  onBack,
  phoneNumber,
  email,
}) => {
  const { t } = useTranslation();
  const [code, setCode] = useState(['', '', '', '', '', '']);
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [resendTimer, setResendTimer] = useState(0);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  useEffect(() => {
    // Focus first input on mount
    inputRefs.current[0]?.focus();
  }, []);

  useEffect(() => {
    // Resend timer countdown
    if (resendTimer > 0) {
      const timer = setTimeout(() => setResendTimer(resendTimer - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [resendTimer]);

  const handleChange = (index: number, value: string) => {
    if (value.length > 1) {
      // Handle paste
      const pastedCode = value.slice(0, 6).split('');
      const newCode = [...code];
      pastedCode.forEach((digit, i) => {
        if (index + i < 6) {
          newCode[index + i] = digit;
        }
      });
      setCode(newCode);
      
      // Focus last filled input or last input
      const lastIndex = Math.min(index + pastedCode.length - 1, 5);
      inputRefs.current[lastIndex]?.focus();
    } else {
      // Single character input
      const newCode = [...code];
      newCode[index] = value;
      setCode(newCode);

      // Auto-focus next input
      if (value && index < 5) {
        inputRefs.current[index + 1]?.focus();
      }
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !code[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const verificationCode = code.join('');
    
    if (verificationCode.length !== 6) {
      toast.error(t('auth.twoFactor.invalidCode'));
      return;
    }

    setIsLoading(true);
    try {
      await onVerify(verificationCode);
    } catch (error) {
      // Error handling done in parent component
    } finally {
      setIsLoading(false);
    }
  };

  const handleResend = async () => {
    if (!onResend || resendTimer > 0) return;

    setIsResending(true);
    try {
      await onResend();
      toast.success(t('auth.twoFactor.codeSent'));
      setResendTimer(60); // 60 second cooldown
    } catch (error) {
      toast.error(t('auth.twoFactor.resendError'));
    } finally {
      setIsResending(false);
    }
  };

  const getMethodIcon = () => {
    switch (method) {
      case '2fa':
        return <Shield className="w-12 h-12 text-accent-red" />;
      case 'sms':
        return <Smartphone className="w-12 h-12 text-accent-red" />;
      case 'email':
        return <Mail className="w-12 h-12 text-accent-red" />;
    }
  };

  const getMethodTitle = () => {
    switch (method) {
      case '2fa':
        return t('auth.twoFactor.authenticatorTitle');
      case 'sms':
        return t('auth.twoFactor.smsTitle');
      case 'email':
        return t('auth.twoFactor.emailTitle');
    }
  };

  const getMethodDescription = () => {
    switch (method) {
      case '2fa':
        return t('auth.twoFactor.authenticatorDescription');
      case 'sms':
        return phoneNumber 
          ? t('auth.twoFactor.smsDescription', { phone: phoneNumber })
          : t('auth.twoFactor.smsDescriptionGeneric');
      case 'email':
        return email
          ? t('auth.twoFactor.emailDescription', { email })
          : t('auth.twoFactor.emailDescriptionGeneric');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-background-primary to-background-secondary flex items-center justify-center px-4">
      <div className="max-w-md w-full">
        <Card className="p-8 shadow-elevated backdrop-blur-md bg-white/95">
          {/* Back button */}
          {onBack && (
            <button
              onClick={onBack}
              className="mb-4 flex items-center gap-2 text-sm text-text-secondary hover:text-text-primary transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              {t('common.back')}
            </button>
          )}

          {/* Icon and Title */}
          <div className="text-center mb-8">
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.3 }}
              className="w-20 h-20 bg-gradient-to-br from-accent-red/10 to-accent-red/20 rounded-2xl shadow-soft mx-auto mb-4 flex items-center justify-center"
            >
              {getMethodIcon()}
            </motion.div>
            <h1 className="text-2xl font-semibold text-text-primary">
              {getMethodTitle()}
            </h1>
            <p className="text-sm text-text-secondary mt-2">
              {getMethodDescription()}
            </p>
          </div>

          {/* Verification Code Input */}
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-text-primary mb-4">
                {t('auth.twoFactor.enterCode')}
              </label>
              <div className="flex gap-2 justify-center">
                {code.map((digit, index) => (
                  <input
                    key={index}
                    ref={(el) => (inputRefs.current[index] = el)}
                    type="text"
                    inputMode="numeric"
                    pattern="[0-9]"
                    maxLength={1}
                    value={digit}
                    onChange={(e) => handleChange(index, e.target.value.replace(/\D/g, ''))}
                    onKeyDown={(e) => handleKeyDown(index, e)}
                    className={clsx(
                      'w-12 h-14 text-center text-xl font-semibold',
                      'border-2 rounded-xl transition-all',
                      'focus:outline-none focus:ring-2 focus:ring-accent-red/20 focus:border-accent-red',
                      digit ? 'border-accent-red bg-accent-red/5' : 'border-border-primary bg-background-secondary'
                    )}
                  />
                ))}
              </div>
            </div>

            {/* Trust Device Option */}
            <label className="flex items-center justify-center">
              <input
                type="checkbox"
                className="w-4 h-4 text-accent-red border-border-primary rounded focus:ring-accent-red accent-accent-red"
              />
              <span className="ml-2 text-sm text-text-primary">
                {t('auth.twoFactor.trustDevice')}
              </span>
            </label>

            <Button
              type="submit"
              disabled={isLoading || code.join('').length !== 6}
              loading={isLoading}
              className="w-full py-3"
              size="lg"
            >
              {t('auth.twoFactor.verify')}
            </Button>
          </form>

          {/* Resend Code */}
          {onResend && (
            <div className="mt-6 text-center">
              <p className="text-sm text-text-secondary mb-2">
                {t('auth.twoFactor.didntReceive')}
              </p>
              <button
                onClick={handleResend}
                disabled={isResending || resendTimer > 0}
                className={clsx(
                  'text-sm font-medium transition-colors',
                  resendTimer > 0
                    ? 'text-text-muted cursor-not-allowed'
                    : 'text-accent-red hover:text-accent-red-dark'
                )}
              >
                {resendTimer > 0
                  ? t('auth.twoFactor.resendIn', { seconds: resendTimer })
                  : t('auth.twoFactor.resendCode')}
              </button>
            </div>
          )}

          {/* Alternative Options */}
          <div className="mt-6 pt-6 border-t border-border-primary">
            <p className="text-sm text-text-secondary text-center mb-3">
              {t('auth.twoFactor.havingTrouble')}
            </p>
            <button className="w-full text-sm text-accent-red hover:text-accent-red-dark font-medium transition-colors">
              {t('auth.twoFactor.useBackupCode')}
            </button>
          </div>
        </Card>
      </div>
    </div>
  );
};