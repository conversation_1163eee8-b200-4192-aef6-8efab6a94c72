import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { MasterDetailLayout } from '../../components/layout/MasterDetailLayout';
import { Plus, Search, Filter, MoreVertical, Mail, Phone, Calendar, Edit, Trash2 } from 'lucide-react';
import Button from '../../components/common/Button';
import { SearchInput } from '../../components/forms/SearchInput';
import { Card } from '../../components/common/Card';
import { Badge } from '../../components/common/Badge';
import { UserCard } from '../../components/cards/UserCard';
import { Dropdown } from '../../components/common/Dropdown';
import { AddUserModal } from '../../components/modals/AddUserModal';
import clsx from 'clsx';
import { useAuth } from '../../contexts/AuthContext';
import { UserRoleType } from '../../types/auth';
import { hasPermission } from '../../utils/permissions';
import { ClientProfileView } from '../../components/client/ClientProfileView';
import { PasswordResetModal } from '../../components/modals/PasswordResetModal';
import { Key } from 'lucide-react';
import { ClientProfileModal } from '../../components/manager/ClientProfileModal';
import { ClientEditModal } from '../../components/modals/ClientEditModal';
import { useApi } from '../../hooks/useApi';
import toast from 'react-hot-toast';

interface Client {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  status: 'active' | 'inactive';
  createdAt: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  emergencyContactRelationship?: string;
  preferredLanguage?: string;
  postalCode?: string;
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  province?: string;
  secondaryPhone?: string;
  timezone?: string;
  communicationPreferences?: {
    email: boolean;
    sms: boolean;
    push: boolean;
  };
  internalNotes?: string;
  assignedTutorIds?: number[];
}


const ClientsPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { get, del, loading } = useApi();
  
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [clients, setClients] = useState<Client[]>([]);
  const [showPasswordResetModal, setShowPasswordResetModal] = useState(false);
  const [passwordResetUser, setPasswordResetUser] = useState<any>(null);
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [clientToEdit, setClientToEdit] = useState<Client | null>(null);
  
  const isClient = user?.activeRole === UserRoleType.CLIENT;
  const isTutor = user?.activeRole === UserRoleType.TUTOR;
  const canAddClients = hasPermission(user?.activeRole, 'addUsers');
  const canViewAllClients = hasPermission(user?.activeRole, 'viewAllClients');
  const canResetPassword = hasPermission(user?.activeRole, 'resetUserPassword');
  const canEditClients = hasPermission(user?.activeRole, 'editUsers');
  const canDeleteClients = hasPermission(user?.activeRole, 'deleteUsers');
  
  // Load clients data
  const loadClients = async () => {
    try {
      let clientsData: Client[];
      
      if (isClient) {
        // Load only current user's profile
        const profileResponse = await get<Client>('/users/profile');
        clientsData = [profileResponse];
      } else if (isTutor) {
        // Load tutor's assigned clients
        clientsData = await get<Client[]>('/tutors/assigned-clients');
      } else {
        // Load all clients for managers
        clientsData = await get<Client[]>('/clients', {
          params: {
            search: searchQuery || undefined,
            status: 'all'
          }
        });
      }
      
      setClients(clientsData);
      
    } catch (error) {
      console.error('Error loading clients:', error);
      toast.error('Failed to load clients data');
    }
  };
  
  useEffect(() => {
    loadClients();
  }, [searchQuery, isClient, isTutor]);
  
  const handleDeleteClient = async (clientId: number) => {
    try {
      await del(`/clients/${clientId}`);
      toast.success('Client deleted successfully');
      await loadClients();
      if (selectedClient?.id === clientId) {
        setSelectedClient(null);
      }
    } catch (error) {
      console.error('Error deleting client:', error);
      toast.error('Failed to delete client');
    }
  };

  // Clients are already filtered from the API based on user role
  const filteredClients = clients;

  const handleAddClient = async (newClient: any) => {
    // Reload clients to get the newly added one
    await loadClients();
    
    // Find and select the newly added client
    const addedClient = clients.find(c => c.email === newClient.email);
    if (addedClient) {
      setSelectedClient(addedClient);
    }
  };

  const handleEditClient = (client: Client) => {
    setClientToEdit(client);
    setShowEditModal(true);
  };

  const handleEditSuccess = async (updatedClient: Client) => {
    // Reload clients to get the updated data
    await loadClients();
    
    // Update selected client if it's the one that was edited
    if (selectedClient?.id === updatedClient.id) {
      setSelectedClient(updatedClient);
    }
    
    toast.success('Client updated successfully');
  };

  const masterContent = (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-6 border-b border-primary-200">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-xl font-semibold text-text-primary">
            {isClient ? t('clients.myProfile', 'My Profile') : 
             isTutor ? t('clients.myClients', 'My Clients') :
             t('clients.title', 'Clients')}
          </h1>
          {canAddClients && (
            <Button 
              leftIcon={<Plus className="w-4 h-4" />}
              onClick={() => setShowAddModal(true)}
              className="bg-accent-red text-white hover:bg-accent-red-dark"
            >
              {t('clients.addClient', 'Add Client')}
            </Button>
          )}
        </div>

        {/* Search and filters */}
        <div className="flex gap-3">
          <SearchInput
            value={searchQuery}
            onChange={setSearchQuery}
            placeholder={t('common.search', 'Search')}
            className="flex-1"
          />
          <Button variant="secondary" leftIcon={<Filter className="w-4 h-4" />}>
            {t('common.filter', 'Filter')}
          </Button>
        </div>
      </div>

      {/* Client list */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3">
        {loading && clients.length === 0 ? (
          // Loading skeleton
          [...Array(5)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="flex items-center p-4 border rounded-lg">
                <div className="w-10 h-10 bg-gray-200 rounded-full mr-3"></div>
                <div className="flex-1">
                  <div className="w-32 h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="w-48 h-3 bg-gray-200 rounded"></div>
                </div>
              </div>
            </div>
          ))
        ) : filteredClients.length > 0 ? (
          filteredClients.map((client) => (
            <UserCard
              key={client.id}
              user={{
                id: client.id.toString(),
                name: `${client.firstName} ${client.lastName}`,
                email: client.email,
                phone: client.phone,
                status: client.status === 'active' ? 'online' : 'offline',
                role: 'Client',
                joinedDate: client.createdAt,
              }}
              variant="compact"
              onClick={() => setSelectedClient(client)}
              className={clsx(
                selectedClient?.id === client.id && 'border-accent-red bg-red-50'
              )}
              actions={
                !isClient && !isTutor && (canEditClients || canDeleteClients) && (
                  <Dropdown
                    trigger={
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <MoreVertical className="w-4 h-4" />
                      </Button>
                    }
                    items={[
                      ...(canEditClients ? [{ 
                        label: t('common.edit', 'Edit'), 
                        onClick: () => handleEditClient(client),
                        icon: <Edit className="w-4 h-4" />
                      }] : []),
                      ...(canDeleteClients ? [{ 
                        label: t('common.delete', 'Delete'), 
                        onClick: () => {
                          if (window.confirm(t('clients.confirmDelete', 'Are you sure you want to delete this client? This action cannot be undone.'))) {
                            handleDeleteClient(client.id);
                          }
                        }, 
                        variant: 'danger' as const,
                        icon: <Trash2 className="w-4 h-4" />
                      }] : []),
                    ]}
                  />
                )
              }
            />
          ))
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">
              {searchQuery 
                ? t('clients.noClientsFound', 'No clients found matching your search')
                : t('clients.noClients', 'No clients available')
              }
            </p>
          </div>
        )}
      </div>
    </div>
  );

  const detailContent = selectedClient ? (
    <div className="h-full p-6">
      <Card className="h-full">
        <div className="p-6">
          <h2 className="text-xl font-semibold text-text-primary mb-6">
            {t('clients.clientProfile', 'Client Profile')}
          </h2>

          {/* Client details */}
          <div className="space-y-6">
            {/* Personal info */}
            <div>
              <h3 className="text-sm font-medium text-text-secondary mb-3">
                {t('users.personalInfo', 'Personal Information')}
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm text-text-secondary">
                    {t('users.firstName', 'First Name')}
                  </label>
                  <p className="font-medium text-text-primary">
                    {selectedClient.firstName}
                  </p>
                </div>
                <div>
                  <label className="text-sm text-text-secondary">
                    {t('users.lastName', 'Last Name')}
                  </label>
                  <p className="font-medium text-text-primary">
                    {selectedClient.lastName}
                  </p>
                </div>
                {selectedClient.postalCode && (
                  <div>
                    <label className="text-sm text-text-secondary">
                      {t('users.postalCode', 'Postal Code')}
                    </label>
                    <p className="font-medium text-text-primary">
                      {selectedClient.postalCode}
                    </p>
                  </div>
                )}
                <div>
                  <label className="text-sm text-text-secondary">
                    {t('users.preferredLanguage', 'Preferred Language')}
                  </label>
                  <p className="font-medium text-text-primary">
                    {selectedClient.preferredLanguage === 'fr' ? 'Français' : 'English'}
                  </p>
                </div>
              </div>
            </div>

            {/* Contact info */}
            <div>
              <h3 className="text-sm font-medium text-text-secondary mb-3">
                {t('users.contactInfo', 'Contact Information')}
              </h3>
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Mail className="w-4 h-4 text-gray-500" />
                  <div>
                    <label className="text-sm text-text-secondary">
                      {t('common.email', 'Email')}
                    </label>
                    <p className="font-medium text-text-primary">
                      {selectedClient.email}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="w-4 h-4 text-gray-500" />
                  <div>
                    <label className="text-sm text-text-secondary">
                      {t('common.phone', 'Phone')}
                    </label>
                    <p className="font-medium text-text-primary">
                      {selectedClient.phone}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Emergency Contact */}
            {selectedClient.emergencyContactName && (
              <div>
                <h3 className="text-sm font-medium text-text-secondary mb-3">
                  {t('users.emergencyContact', 'Emergency Contact')}
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm text-text-secondary">
                      {t('users.contactName', 'Contact Name')}
                    </label>
                    <p className="font-medium text-text-primary">
                      {selectedClient.emergencyContactName}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm text-text-secondary">
                      {t('users.contactPhone', 'Contact Phone')}
                    </label>
                    <p className="font-medium text-text-primary">
                      {selectedClient.emergencyContactPhone}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Account info */}
            <div>
              <h3 className="text-sm font-medium text-text-secondary mb-3">
                {t('users.accountInfo', 'Account Information')}
              </h3>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4 text-gray-500" />
                  <div>
                    <label className="text-sm text-text-secondary">
                      {t('users.memberSince', 'Member Since')}
                    </label>
                    <p className="font-medium text-text-primary">
                      {new Date(selectedClient.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>
                <div>
                  <label className="text-sm text-text-secondary">
                    {t('users.status', 'Status')}
                  </label>
                  <Badge className={
                    selectedClient.status === 'active' 
                      ? 'bg-green-100 text-green-700' 
                      : 'bg-gray-100 text-gray-700'
                  }>
                    {selectedClient.status === 'active' ? 'Active' : 'Inactive'}
                  </Badge>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex gap-3 pt-4 border-t">
              {(isClient && selectedClient?.email === user?.email) && (
                <Button variant="primary">
                  {t('common.edit', 'Edit Profile')}
                </Button>
              )}
              {isTutor && (
                <>
                  <Button variant="secondary">
                    {t('messages.sendMessage', 'Send Message')}
                  </Button>
                  <Button variant="secondary">
                    {t('appointments.viewSchedule', 'View Schedule')}
                  </Button>
                  <Button variant="secondary">
                    {t('common.viewProgress', 'View Progress')}
                  </Button>
                </>
              )}
              {!isClient && !isTutor && (
                <>
                  <Button 
                    variant="primary"
                    onClick={() => setShowProfileModal(true)}
                  >
                    {t('common.viewFullProfile', 'View Full Profile')}
                  </Button>
                  {canEditClients && (
                    <Button 
                      variant="secondary"
                      onClick={() => handleEditClient(selectedClient)}
                    >
                      {t('common.edit', 'Edit')}
                    </Button>
                  )}
                  <Button variant="secondary">
                    {t('messages.sendMessage', 'Send Message')}
                  </Button>
                  <Button variant="secondary">
                    {t('appointments.bookSession', 'Book Session')}
                  </Button>
                  {canResetPassword && (
                    <Button 
                      variant="secondary"
                      onClick={() => {
                        setPasswordResetUser({
                          id: selectedClient.id,
                          email: selectedClient.email,
                          firstName: selectedClient.firstName,
                          lastName: selectedClient.lastName,
                          userType: 'client'
                        });
                        setShowPasswordResetModal(true);
                      }}
                    >
                      <Key className="w-4 h-4 mr-2" />
                      {t('auth.resetPassword', 'Reset Password')}
                    </Button>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </Card>
    </div>
  ) : (
    <div className="h-full flex items-center justify-center p-6">
      <div className="text-center">
        <p className="text-text-secondary">
          {t('clients.selectClient', 'Select a client to view details')}
        </p>
      </div>
    </div>
  );

  // If user is a client, show their profile view directly
  if (isClient) {
    return <ClientProfileView />;
  }
  
  // Otherwise show the full client management interface
  return (
    <>
      <MasterDetailLayout
        masterContent={masterContent}
        detailContent={detailContent}
        masterWidth="400px"
      />
      
      {/* Add Client Modal */}
      <AddUserModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        userType="client"
        onSuccess={handleAddClient}
      />
      
      {/* Password Reset Modal */}
      {passwordResetUser && (
        <PasswordResetModal
          isOpen={showPasswordResetModal}
          onClose={() => {
            setShowPasswordResetModal(false);
            setPasswordResetUser(null);
          }}
          user={passwordResetUser}
        />
      )}
      
      {/* Client Profile Modal */}
      {selectedClient && (
        <ClientProfileModal
          isOpen={showProfileModal}
          onClose={() => setShowProfileModal(false)}
          client={selectedClient}
        />
      )}
      
      {/* Client Edit Modal */}
      {clientToEdit && (
        <ClientEditModal
          isOpen={showEditModal}
          onClose={() => {
            setShowEditModal(false);
            setClientToEdit(null);
          }}
          client={clientToEdit}
          onSuccess={handleEditSuccess}
        />
      )}
    </>
  );
};

export default ClientsPage;