-- Migration 014: Add search analytics table for monitoring search usage
-- This table tracks search queries for optimization and analytics

-- Create search analytics table
CREATE TABLE search_analytics (
    search_id VARCHAR(255) PRIMARY KEY,
    user_id INTEGER NOT NULL,
    query TEXT,
    entity_type VARCHAR(50) NOT NULL,
    results_count INTEGER NOT NULL DEFAULT 0,
    duration_ms NUMERIC(10,2) NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    filters_used JSONB,
    clicked_result_id VARCHAR(255),
    session_id VARCHAR(255),
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_search_analytics_user_id FOREIGN KEY (user_id) REFERENCES user_accounts(user_id) ON DELETE CASCADE
);

-- Create indexes for search analytics
CREATE INDEX idx_search_analytics_user_id ON search_analytics(user_id);
CREATE INDEX idx_search_analytics_timestamp ON search_analytics(timestamp);
CREATE INDEX idx_search_analytics_entity_type ON search_analytics(entity_type);
CREATE INDEX idx_search_analytics_query ON search_analytics USING gin(to_tsvector('english', query)) WHERE query IS NOT NULL;
CREATE INDEX idx_search_analytics_duration ON search_analytics(duration_ms);
CREATE INDEX idx_search_analytics_results_count ON search_analytics(results_count);

-- Add comments
COMMENT ON TABLE search_analytics IS 'Analytics data for search operations to monitor usage and optimize performance';
COMMENT ON COLUMN search_analytics.search_id IS 'Unique identifier for the search operation';
COMMENT ON COLUMN search_analytics.user_id IS 'User who performed the search';
COMMENT ON COLUMN search_analytics.query IS 'Search query text (null for empty searches)';
COMMENT ON COLUMN search_analytics.entity_type IS 'Type of entity searched (user, client, tutor, dependant, all)';
COMMENT ON COLUMN search_analytics.results_count IS 'Number of results returned';
COMMENT ON COLUMN search_analytics.duration_ms IS 'Search execution time in milliseconds';
COMMENT ON COLUMN search_analytics.filters_used IS 'JSON object containing filters applied to the search';
COMMENT ON COLUMN search_analytics.clicked_result_id IS 'ID of result clicked by user (for click-through tracking)';
COMMENT ON COLUMN search_analytics.session_id IS 'Session identifier for grouping related searches';

-- Create a view for search analytics summary
CREATE VIEW search_analytics_summary AS
SELECT 
    entity_type,
    COUNT(*) as total_searches,
    AVG(results_count) as avg_results,
    AVG(duration_ms) as avg_duration_ms,
    PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY duration_ms) as median_duration_ms,
    PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY duration_ms) as p95_duration_ms,
    COUNT(CASE WHEN results_count = 0 THEN 1 END) as zero_result_searches,
    COUNT(CASE WHEN clicked_result_id IS NOT NULL THEN 1 END) as searches_with_clicks,
    DATE_TRUNC('day', timestamp) as search_date
FROM search_analytics
WHERE timestamp >= CURRENT_TIMESTAMP - INTERVAL '30 days'
GROUP BY entity_type, DATE_TRUNC('day', timestamp)
ORDER BY search_date DESC, entity_type;

COMMENT ON VIEW search_analytics_summary IS 'Daily summary of search performance metrics for the last 30 days';

-- Create function to clean old search analytics (retention policy)
CREATE OR REPLACE FUNCTION cleanup_old_search_analytics()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Delete search analytics older than 90 days
    DELETE FROM search_analytics 
    WHERE timestamp < CURRENT_TIMESTAMP - INTERVAL '90 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION cleanup_old_search_analytics() IS 'Clean up search analytics data older than 90 days to maintain performance';

-- Grant permissions (skip if role doesn't exist)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'tutoraide_app') THEN
    END IF;
END $$;