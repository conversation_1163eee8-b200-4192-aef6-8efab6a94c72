"""
Advanced appointment scheduling service with comprehensive conflict detection and business logic.
"""

import asyncpg
import logging
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, date, time, timedelta
from decimal import Decimal

from app.database.repositories.appointment_repository import AppointmentRepository
from app.database.repositories.base import BaseRepository
from app.core.exceptions import ResourceNotFoundError, BusinessLogicError, ValidationError
from app.core.timezone import now_est
from app.core.dependencies import get_db_connection
from app.models.appointment_models import (
    Appointment, AppointmentCreate, AppointmentUpdate, 
    TutorAvailability, TimeOffRequest, AppointmentConflict,
    ConflictCheck, ConflictResult, SchedulingSuggestion,
    AppointmentStatus, RecurringAppointment,
    TimeSlot
)
from app.database.repositories.tutor_repository import TutorRepository
from app.database.repositories.client_repository import ClientRepository

logger = logging.getLogger(__name__)


class AppointmentSchedulingService:
    """Advanced appointment scheduling service with conflict detection."""
    
    def __init__(self, appointment_repo=None, tutor_repo=None, client_repo=None):
        # Allow dependency injection or create default instances
        self.appointment_repo = appointment_repo or AppointmentRepository()
        self.tutor_repo = tutor_repo
        self.client_repo = client_repo
        self.availability_repo = TutorAvailabilityRepository()
        self.time_off_repo = TimeOffRequestRepository()
        self.conflict_repo = AppointmentConflictRepository()
    
    async def create_appointment(
        self,
        appointment_data: AppointmentCreate,
        created_by: int,
        bypass_conflict_check: bool = False
    ) -> Appointment:
        """
        Create a new appointment with comprehensive validation and conflict detection.
        
        Args:
            appointment_data: Appointment creation data
            created_by: User ID creating the appointment
            bypass_conflict_check: Whether to bypass conflict checking (manager override)
        
        Returns:
            Created appointment
        
        Raises:
            BusinessLogicError: If conflicts exist or business rules violated
            ValidationError: If appointment data is invalid
        """
        logger.info(f"Creating appointment for tutor {appointment_data.tutor_id} on {appointment_data.scheduled_date}")
        
        async with get_db_connection() as conn:
            # Validate appointment timing
            await self._validate_appointment_timing(appointment_data)
            
            # Check for conflicts unless bypassed
            if not bypass_conflict_check:
                conflict_result = await self.check_conflicts(
                    ConflictCheck(
                        tutor_id=appointment_data.tutor_id,
                        proposed_date=appointment_data.scheduled_date,
                        proposed_start=appointment_data.start_time,
                        proposed_end=appointment_data.end_time,
                        include_buffer=True
                    )
                )
                
                if conflict_result.has_conflict:
                    raise BusinessLogicError(
                        f"Appointment conflict detected: {conflict_result.message}",
                        {
                            "conflict_type": conflict_result.conflict_type,
                            "suggested_times": conflict_result.suggested_times
                        }
                    )
            
            # Check tutor availability
            is_available = await self._check_tutor_availability(
                conn, appointment_data.tutor_id, appointment_data.scheduled_date,
                appointment_data.start_time, appointment_data.end_time
            )
            
            if not is_available:
                raise BusinessLogicError(
                    "Tutor is not available during the requested time slot",
                    {"tutor_id": appointment_data.tutor_id}
                )
            
            # Validate business hours
            await self._validate_business_hours(appointment_data.start_time, appointment_data.end_time)
            
            # Calculate duration and total cost
            duration_hours = self._calculate_duration_hours(
                appointment_data.start_time, appointment_data.end_time
            )
            total_cost = appointment_data.hourly_rate * Decimal(str(duration_hours))
            
            # Prepare appointment data for database
            db_data = {
                "tutor_id": appointment_data.tutor_id,
                "client_id": appointment_data.client_id,
                "dependant_id": appointment_data.dependant_id,
                "scheduled_date": appointment_data.scheduled_date,
                "start_time": appointment_data.start_time,
                "end_time": appointment_data.end_time,
                "subject_area": appointment_data.subject_area,
                "location_details": appointment_data.location_details.dict(),
                "notes": appointment_data.notes,
                "session_type": appointment_data.session_type or "individual",
                "hourly_rate": appointment_data.hourly_rate,
                "currency": "CAD",
                "status": AppointmentStatus.SCHEDULED,
                "confirmed_by_tutor": False,
                "requires_confirmation": True,
                "auto_confirm_hours": 24,
                "buffer_time_before": 15,
                "buffer_time_after": 15,
                "created_by": created_by,
                "created_at": now_est(),
                "updated_at": now_est()
            }
            
            # Create appointment
            appointment_record = await self.appointment_repo.create(conn, db_data)
            
            # Schedule automatic reminders
            await self._schedule_appointment_reminders(conn, appointment_record['appointment_id'])
            
            # Log appointment creation
            logger.info(f"Created appointment {appointment_record['appointment_id']} successfully")
            
            return Appointment(**appointment_record)
    
    async def check_conflicts(self, conflict_check: ConflictCheck) -> ConflictResult:
        """
        Comprehensive conflict checking including appointments, time-off, and availability.
        
        Args:
            conflict_check: Conflict check parameters
        
        Returns:
            Detailed conflict result with suggestions
        """
        logger.debug(f"Checking conflicts for tutor {conflict_check.tutor_id}")
        
        async with get_db_connection() as conn:
            # Calculate buffer times if requested
            if conflict_check.include_buffer:
                start_with_buffer = (
                    datetime.combine(conflict_check.proposed_date, conflict_check.proposed_start) 
                    - timedelta(minutes=15)
                ).time()
                end_with_buffer = (
                    datetime.combine(conflict_check.proposed_date, conflict_check.proposed_end) 
                    + timedelta(minutes=15)
                ).time()
            else:
                start_with_buffer = conflict_check.proposed_start
                end_with_buffer = conflict_check.proposed_end
            
            # Check appointment conflicts
            appointment_conflict = await self._check_appointment_conflicts(
                conn, conflict_check.tutor_id, conflict_check.proposed_date,
                start_with_buffer, end_with_buffer, conflict_check.exclude_appointment_id
            )
            
            if appointment_conflict:
                return ConflictResult(
                    has_conflict=True,
                    conflict_type="appointment_overlap",
                    conflicting_appointment_id=appointment_conflict['appointment_id'],
                    message=f"Conflicts with existing appointment at {appointment_conflict['start_time']}-{appointment_conflict['end_time']}",
                    suggested_times=await self._get_suggested_times(conn, conflict_check)
                )
            
            # Check time-off conflicts
            time_off_conflict = await self._check_time_off_conflicts(
                conn, conflict_check.tutor_id, conflict_check.proposed_date,
                conflict_check.proposed_start, conflict_check.proposed_end
            )
            
            if time_off_conflict:
                return ConflictResult(
                    has_conflict=True,
                    conflict_type="time_off_overlap",
                    conflicting_time_off_id=time_off_conflict['request_id'],
                    message=f"Conflicts with approved time-off: {time_off_conflict['reason']}",
                    suggested_times=await self._get_suggested_times(conn, conflict_check)
                )
            
            # Check availability conflicts
            availability_conflict = await self._check_availability_conflicts(
                conn, conflict_check.tutor_id, conflict_check.proposed_date,
                conflict_check.proposed_start, conflict_check.proposed_end
            )
            
            if availability_conflict:
                return ConflictResult(
                    has_conflict=True,
                    conflict_type="availability_conflict",
                    message="Tutor is not available during the requested time",
                    suggested_times=await self._get_suggested_times(conn, conflict_check)
                )
            
            return ConflictResult(
                has_conflict=False,
                message="No conflicts detected"
            )
    
    async def reschedule_appointment(
        self,
        appointment_id: int,
        new_date: date,
        new_start_time: time,
        new_end_time: time,
        reason: str,
        updated_by: int
    ) -> Appointment:
        """
        Reschedule an existing appointment with conflict checking.
        
        Args:
            appointment_id: Appointment to reschedule
            new_date: New appointment date
            new_start_time: New start time
            new_end_time: New end time
            reason: Reason for rescheduling
            updated_by: User making the change
        
        Returns:
            Updated appointment
        """
        async with get_db_connection() as conn:
            # Get existing appointment
            appointment = await self.appointment_repo.find_by_id(conn, appointment_id)
            if not appointment:
                raise ResourceNotFoundError(f"Appointment {appointment_id} not found")
            
            # Check conflicts for new time slot
            conflict_result = await self.check_conflicts(
                ConflictCheck(
                    tutor_id=appointment['tutor_id'],
                    proposed_date=new_date,
                    proposed_start=new_start_time,
                    proposed_end=new_end_time,
                    exclude_appointment_id=appointment_id,
                    include_buffer=True
                )
            )
            
            if conflict_result.has_conflict:
                raise BusinessLogicError(
                    f"Cannot reschedule: {conflict_result.message}",
                    {"suggested_times": conflict_result.suggested_times}
                )
            
            # Update appointment
            update_data = {
                "scheduled_date": new_date,
                "start_time": new_start_time,
                "end_time": new_end_time,
                "status": AppointmentStatus.SCHEDULED,
                "confirmed_by_tutor": False,  # Reset confirmation
                "confirmed_at": None,
                "updated_by": updated_by,
                "updated_at": now_est()
            }
            
            updated_appointment = await self.appointment_repo.update(conn, appointment_id, update_data)
            
            # Log rescheduling
            await self._log_appointment_change(
                conn, appointment_id, "rescheduled", reason, updated_by
            )
            
            # Update reminders
            await self._reschedule_appointment_reminders(conn, appointment_id)
            
            logger.info(f"Rescheduled appointment {appointment_id} to {new_date} {new_start_time}-{new_end_time}")
            
            return Appointment(**updated_appointment)
    
    async def cancel_appointment(
        self,
        appointment_id: int,
        reason: str,
        cancelled_by: int,
        notify_participants: bool = True
    ) -> Appointment:
        """
        Cancel an appointment with proper notifications and billing adjustments.
        
        Args:
            appointment_id: Appointment to cancel
            reason: Cancellation reason
            cancelled_by: User cancelling the appointment
            notify_participants: Whether to send notifications
        
        Returns:
            Cancelled appointment
        """
        async with get_db_connection() as conn:
            appointment = await self.appointment_repo.find_by_id(conn, appointment_id)
            if not appointment:
                raise ResourceNotFoundError(f"Appointment {appointment_id} not found")
            
            if appointment['status'] in [AppointmentStatus.CANCELLED, AppointmentStatus.COMPLETED]:
                raise BusinessLogicError(
                    f"Cannot cancel appointment with status: {appointment['status']}"
                )
            
            # Check cancellation policy
            await self._validate_cancellation_policy(appointment)
            
            # Update appointment status
            update_data = {
                "status": AppointmentStatus.CANCELLED,
                "cancellation_reason": reason,
                "cancelled_by": cancelled_by,
                "cancelled_at": now_est(),
                "updated_at": now_est()
            }
            
            updated_appointment = await self.appointment_repo.update(conn, appointment_id, update_data)
            
            # Log cancellation
            await self._log_appointment_change(
                conn, appointment_id, "cancelled", reason, cancelled_by
            )
            
            # Cancel reminders
            await self._cancel_appointment_reminders(conn, appointment_id)
            
            # Handle billing adjustments if needed
            await self._process_cancellation_billing(conn, appointment_id, reason)
            
            logger.info(f"Cancelled appointment {appointment_id}: {reason}")
            
            return Appointment(**updated_appointment)
    
    async def confirm_appointment(
        self,
        appointment_id: int,
        confirmed_by: int,
        notes: Optional[str] = None
    ) -> Appointment:
        """
        Confirm an appointment (typically by tutor).
        
        Args:
            appointment_id: Appointment to confirm
            confirmed_by: User confirming the appointment
            notes: Optional confirmation notes
        
        Returns:
            Confirmed appointment
        """
        async with get_db_connection() as conn:
            appointment = await self.appointment_repo.find_by_id(conn, appointment_id)
            if not appointment:
                raise ResourceNotFoundError(f"Appointment {appointment_id} not found")
            
            if appointment['status'] != AppointmentStatus.SCHEDULED:
                raise BusinessLogicError(
                    f"Cannot confirm appointment with status: {appointment['status']}"
                )
            
            # Update appointment
            update_data = {
                "status": AppointmentStatus.CONFIRMED,
                "confirmed_by_tutor": True,
                "confirmed_at": now_est(),
                "updated_at": now_est()
            }
            
            if notes:
                update_data["notes"] = f"{appointment.get('notes', '') or ''}\n\nConfirmation notes: {notes}".strip()
            
            updated_appointment = await self.appointment_repo.update(conn, appointment_id, update_data)
            
            # Log confirmation
            await self._log_appointment_change(
                conn, appointment_id, "confirmed", notes or "Confirmed by tutor", confirmed_by
            )
            
            logger.info(f"Confirmed appointment {appointment_id}")
            
            return Appointment(**updated_appointment)
    
    async def complete_appointment(
        self,
        appointment_id: int,
        status: AppointmentStatus,
        completion_notes: Optional[str],
        tutor_no_show: bool,
        client_no_show: bool,
        completed_by: int
    ) -> Appointment:
        """
        Complete an appointment and trigger billing process.
        
        Args:
            appointment_id: Appointment to complete
            status: Final status (completed or no_show)
            completion_notes: Optional completion notes
            tutor_no_show: Whether tutor was a no-show
            client_no_show: Whether client was a no-show
            completed_by: User completing the appointment
        
        Returns:
            Completed appointment
        """
        async with get_db_connection() as conn:
            appointment = await self.appointment_repo.find_by_id(conn, appointment_id)
            if not appointment:
                raise ResourceNotFoundError(f"Appointment {appointment_id} not found")
            
            # Update appointment status
            update_data = {
                "status": status,
                "completed_at": now_est(),
                "completed_by": completed_by,
                "updated_at": now_est()
            }
            
            if completion_notes:
                update_data["notes"] = f"{appointment.get('notes', '') or ''}\n\nCompletion notes: {completion_notes}".strip()
            
            if tutor_no_show:
                update_data["tutor_no_show"] = True
            if client_no_show:
                update_data["client_no_show"] = True
            
            updated_appointment = await self.appointment_repo.update(conn, appointment_id, update_data)
            
            # Log completion
            await self._log_appointment_change(
                conn, appointment_id, "completed", 
                f"Status: {status.value}, Tutor no-show: {tutor_no_show}, Client no-show: {client_no_show}",
                completed_by
            )
            
            # Trigger billing if appointment was completed successfully
            if status == AppointmentStatus.COMPLETED and not tutor_no_show and not client_no_show:
                await self._trigger_billing_for_appointment(conn, appointment_id)
            
            # Send SMS confirmation to tutor
            await self._send_tutor_confirmation_sms(conn, appointment_id)
            
            logger.info(f"Completed appointment {appointment_id} with status {status}")
            
            return Appointment(**updated_appointment)
    
    async def process_sms_confirmation(
        self,
        appointment_id: int,
        confirmation_code: str,
        tutor_response: str
    ) -> Dict[str, Any]:
        """
        Process tutor's SMS confirmation response.
        
        Args:
            appointment_id: Appointment ID from SMS
            confirmation_code: Verification code from SMS
            tutor_response: YES or NO response
        
        Returns:
            Processing result with status and billing updates
        """
        async with get_db_connection() as conn:
            # Verify confirmation code
            is_valid = await self._verify_sms_confirmation_code(
                conn, appointment_id, confirmation_code
            )
            
            if not is_valid:
                raise BusinessLogicError(
                    "Invalid confirmation code or expired",
                    {"appointment_id": appointment_id}
                )
            
            appointment = await self.appointment_repo.find_by_id(conn, appointment_id)
            if not appointment:
                raise ResourceNotFoundError(f"Appointment {appointment_id} not found")
            
            # Process based on response
            if tutor_response == "YES":
                # Confirm appointment completion
                update_data = {
                    "sms_confirmed": True,
                    "sms_confirmed_at": now_est(),
                    "updated_at": now_est()
                }
                
                # If appointment wasn't already marked as completed, do it now
                if appointment['status'] not in [AppointmentStatus.COMPLETED, AppointmentStatus.NO_SHOW]:
                    update_data["status"] = AppointmentStatus.COMPLETED
                    update_data["completed_at"] = now_est()
                    
                    # Trigger billing
                    await self._trigger_billing_for_appointment(conn, appointment_id)
                
                result = {
                    "status": "confirmed",
                    "billing_updated": True
                }
                
            else:  # NO response
                # Mark as no-show or update existing status
                update_data = {
                    "sms_confirmed": False,
                    "sms_confirmed_at": now_est(),
                    "status": AppointmentStatus.NO_SHOW,
                    "tutor_no_show": True,
                    "updated_at": now_est()
                }
                
                # Cancel any pending billing
                await self._cancel_billing_for_appointment(conn, appointment_id)
                
                result = {
                    "status": "no_show",
                    "billing_updated": True
                }
            
            # Update appointment
            await self.appointment_repo.update(conn, appointment_id, update_data)
            
            # Log SMS confirmation
            await self._log_appointment_change(
                conn, appointment_id, "sms_confirmed",
                f"Tutor response: {tutor_response}",
                appointment['tutor_id']  # Tutor confirmed via SMS
            )
            
            logger.info(f"Processed SMS confirmation for appointment {appointment_id}: {tutor_response}")
            
            return result
    
    async def get_global_stats(self, start_date: date, end_date: date) -> Dict[str, Any]:
        """Get global appointment statistics for managers."""
        async with get_db_connection() as conn:
            query = """
                SELECT 
                    COUNT(*) as total_appointments,
                    COUNT(*) FILTER (WHERE status = 'completed') as completed,
                    COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled,
                    COUNT(*) FILTER (WHERE status = 'no_show') as no_shows,
                    COUNT(DISTINCT tutor_id) as active_tutors,
                    COUNT(DISTINCT client_id) as active_clients,
                    AVG(EXTRACT(EPOCH FROM (end_time - start_time)) / 3600.0) as avg_duration_hours,
                    SUM(CASE WHEN status = 'completed' THEN 
                        EXTRACT(EPOCH FROM (end_time - start_time)) / 3600.0 * hourly_rate 
                        ELSE 0 END) as total_revenue
                FROM appointment_sessions
                WHERE scheduled_date BETWEEN $1 AND $2
                AND deleted_at IS NULL
            """
            
            stats = await conn.fetchrow(query, start_date, end_date)
            
            return {
                "date_range": {"start": start_date, "end": end_date},
                "total_appointments": stats['total_appointments'] or 0,
                "completed": stats['completed'] or 0,
                "cancelled": stats['cancelled'] or 0,
                "no_shows": stats['no_shows'] or 0,
                "active_tutors": stats['active_tutors'] or 0,
                "active_clients": stats['active_clients'] or 0,
                "avg_duration_hours": float(stats['avg_duration_hours'] or 0),
                "total_revenue": float(stats['total_revenue'] or 0)
            }
    
    async def get_tutor_stats(self, tutor_id: int, start_date: date, end_date: date) -> Dict[str, Any]:
        """Get appointment statistics for a specific tutor."""
        async with get_db_connection() as conn:
            query = """
                SELECT 
                    COUNT(*) as total_appointments,
                    COUNT(*) FILTER (WHERE status = 'completed') as completed,
                    COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled,
                    COUNT(*) FILTER (WHERE status = 'no_show') as no_shows,
                    COUNT(DISTINCT client_id) as unique_clients,
                    AVG(EXTRACT(EPOCH FROM (end_time - start_time)) / 3600.0) as avg_duration_hours,
                    SUM(CASE WHEN status = 'completed' THEN 
                        EXTRACT(EPOCH FROM (end_time - start_time)) / 3600.0 * hourly_rate 
                        ELSE 0 END) as total_earnings
                FROM appointment_sessions
                WHERE tutor_id = $1
                AND scheduled_date BETWEEN $2 AND $3
                AND deleted_at IS NULL
            """
            
            stats = await conn.fetchrow(query, tutor_id, start_date, end_date)
            
            return {
                "date_range": {"start": start_date, "end": end_date},
                "total_appointments": stats['total_appointments'] or 0,
                "completed": stats['completed'] or 0,
                "cancelled": stats['cancelled'] or 0,
                "no_shows": stats['no_shows'] or 0,
                "unique_clients": stats['unique_clients'] or 0,
                "avg_duration_hours": float(stats['avg_duration_hours'] or 0),
                "total_earnings": float(stats['total_earnings'] or 0)
            }
    
    async def get_client_stats(self, client_id: int, start_date: date, end_date: date) -> Dict[str, Any]:
        """Get appointment statistics for a specific client."""
        async with get_db_connection() as conn:
            query = """
                SELECT 
                    COUNT(*) as total_appointments,
                    COUNT(*) FILTER (WHERE status = 'completed') as completed,
                    COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled,
                    COUNT(*) FILTER (WHERE status = 'no_show') as no_shows,
                    COUNT(DISTINCT tutor_id) as unique_tutors,
                    COUNT(DISTINCT subject_area) as subjects_studied,
                    AVG(EXTRACT(EPOCH FROM (end_time - start_time)) / 3600.0) as avg_duration_hours,
                    SUM(CASE WHEN status = 'completed' THEN 
                        EXTRACT(EPOCH FROM (end_time - start_time)) / 3600.0 * hourly_rate 
                        ELSE 0 END) as total_cost
                FROM appointment_sessions
                WHERE client_id = $1
                AND scheduled_date BETWEEN $2 AND $3
                AND deleted_at IS NULL
            """
            
            stats = await conn.fetchrow(query, client_id, start_date, end_date)
            
            return {
                "date_range": {"start": start_date, "end": end_date},
                "total_appointments": stats['total_appointments'] or 0,
                "completed": stats['completed'] or 0,
                "cancelled": stats['cancelled'] or 0,
                "no_shows": stats['no_shows'] or 0,
                "unique_tutors": stats['unique_tutors'] or 0,
                "subjects_studied": stats['subjects_studied'] or 0,
                "avg_duration_hours": float(stats['avg_duration_hours'] or 0),
                "total_cost": float(stats['total_cost'] or 0)
            }
    
    async def list_appointments(
        self,
        start_date: date,
        end_date: date,
        tutor_id: Optional[int] = None,
        client_id: Optional[int] = None,
        status: Optional[AppointmentStatus] = None,
        session_type: Optional[str] = None,
        page: int = 1,
        page_size: int = 20
    ) -> Dict[str, Any]:
        """List appointments with filtering and pagination."""
        async with get_db_connection() as conn:
            # Build WHERE conditions
            conditions = ["scheduled_date BETWEEN $1 AND $2", "deleted_at IS NULL"]
            params = [start_date, end_date]
            param_count = 2
            
            if tutor_id:
                param_count += 1
                conditions.append(f"tutor_id = ${param_count}")
                params.append(tutor_id)
            
            if client_id:
                param_count += 1
                conditions.append(f"client_id = ${param_count}")
                params.append(client_id)
            
            if status:
                param_count += 1
                conditions.append(f"status = ${param_count}")
                params.append(status.value if hasattr(status, 'value') else status)
            
            if session_type:
                param_count += 1
                conditions.append(f"session_type = ${param_count}")
                params.append(session_type)
            
            where_clause = " AND ".join(conditions)
            
            # Count total
            count_query = f"SELECT COUNT(*) FROM appointment_sessions WHERE {where_clause}"
            total_count = await conn.fetchval(count_query, *params)
            
            # Get paginated results
            offset = (page - 1) * page_size
            param_count += 1
            params.append(page_size)
            param_count += 1
            params.append(offset)
            
            query = f"""
                SELECT 
                    a.*,
                    t.first_name || ' ' || t.last_name as tutor_name,
                    c.first_name || ' ' || c.last_name as client_name,
                    d.first_name || ' ' || d.last_name as dependant_name
                FROM appointment_sessions a
                JOIN tutor_profiles t ON a.tutor_id = t.tutor_id
                JOIN client_profiles c ON a.client_id = c.client_id
                LEFT JOIN client_dependants d ON a.dependant_id = d.dependant_id
                WHERE {where_clause}
                ORDER BY a.scheduled_date, a.start_time
                LIMIT ${param_count-1} OFFSET ${param_count}
            """
            
            appointments = await conn.fetch(query, *params)
            
            # Convert to response format
            results = []
            for apt in appointments:
                results.append({
                    "appointment_id": apt['appointment_id'],
                    "tutor_id": apt['tutor_id'],
                    "tutor_name": apt['tutor_name'],
                    "client_id": apt['client_id'],
                    "client_name": apt['client_name'],
                    "dependant_id": apt['dependant_id'],
                    "dependant_name": apt['dependant_name'],
                    "scheduled_date": apt['scheduled_date'],
                    "start_time": apt['start_time'],
                    "end_time": apt['end_time'],
                    "status": apt['status'],
                    "subject_area": apt['subject_area'],
                    "session_type": apt.get('session_type', 'individual'),
                    "location_type": apt['location_details'].get('type') if apt['location_details'] else 'online',
                    "hourly_rate": float(apt['hourly_rate']),
                    "currency": apt['currency'],
                    "confirmed_by_tutor": apt.get('confirmed_by_tutor', False),
                    "created_at": apt['created_at'],
                    "updated_at": apt['updated_at']
                })
            
            return {
                "items": results,
                "total": total_count,
                "page": page,
                "page_size": page_size,
                "total_pages": (total_count + page_size - 1) // page_size
            }
    
    async def bulk_update_appointments(
        self,
        appointment_ids: List[int],
        updates: Dict[str, Any],
        updated_by: int
    ) -> Dict[str, Any]:
        """Bulk update multiple appointments."""
        async with get_db_connection() as conn:
            updated_count = 0
            failed_count = 0
            errors = []
            
            async with conn.transaction():
                for appointment_id in appointment_ids:
                    try:
                        # Update each appointment
                        await self.appointment_repo.update(
                            conn, appointment_id, {**updates, "updated_by": updated_by}
                        )
                        updated_count += 1
                    except Exception as e:
                        failed_count += 1
                        errors.append({
                            "appointment_id": appointment_id,
                            "error": str(e)
                        })
                        logger.error(f"Failed to update appointment {appointment_id}: {e}")
            
            return {
                "updated_count": updated_count,
                "failed_count": failed_count,
                "errors": errors
            }
    
    async def get_scheduling_suggestions(
        self,
        tutor_id: Optional[int],
        client_id: int,
        subject_area: str,
        preferred_date: Optional[date] = None,
        duration_hours: float = 1.0,
        session_type: str = "individual"
    ) -> List[SchedulingSuggestion]:
        """
        Get AI-powered scheduling suggestions based on availability patterns and preferences.
        
        Args:
            tutor_id: Specific tutor ID (optional for auto-matching)
            client_id: Client requesting the appointment
            subject_area: Subject area for the session
            preferred_date: Preferred date (optional)
            duration_hours: Session duration in hours
            session_type: Type of session
        
        Returns:
            List of scheduling suggestions
        """
        async with get_db_connection() as conn:
            suggestions = []
            
            # Get available tutors if not specified
            if tutor_id:
                tutor_ids = [tutor_id]
            else:
                tutor_ids = await self._find_qualified_tutors(conn, subject_area, session_type)
            
            # Get date range to check
            start_date = preferred_date or date.today()
            end_date = start_date + timedelta(days=14)  # Check next 2 weeks
            
            for tid in tutor_ids:
                tutor_suggestions = await self._get_tutor_suggestions(
                    conn, tid, start_date, end_date, duration_hours, client_id
                )
                suggestions.extend(tutor_suggestions)
            
            # Sort by confidence score and date
            suggestions.sort(key=lambda x: (-x.confidence_score, x.suggested_date, x.suggested_start))
            
            return suggestions[:10]  # Return top 10 suggestions
    
    # ============================================
    # Private Helper Methods
    # ============================================
    
    async def _validate_appointment_timing(self, appointment_data: AppointmentCreate) -> None:
        """Validate appointment timing rules."""
        # Check minimum session duration (15 minutes)
        duration = datetime.combine(date.today(), appointment_data.end_time) - \
                  datetime.combine(date.today(), appointment_data.start_time)
        
        if duration.total_seconds() < 900:  # 15 minutes
            raise ValidationError("Minimum appointment duration is 15 minutes")
        
        # Check maximum session duration (4 hours)
        if duration.total_seconds() > 14400:  # 4 hours
            raise ValidationError("Maximum appointment duration is 4 hours")
        
        # Check if appointment is in the past
        appointment_datetime = datetime.combine(appointment_data.scheduled_date, appointment_data.start_time)
        if appointment_datetime <= now_est():
            raise ValidationError("Cannot schedule appointments in the past")
        
        # Check advance booking limit (90 days)
        if (appointment_data.scheduled_date - date.today()).days > 90:
            raise ValidationError("Cannot schedule appointments more than 90 days in advance")
    
    async def _check_tutor_availability(
        self,
        conn: asyncpg.Connection,
        tutor_id: int,
        appointment_date: date,
        start_time: time,
        end_time: time
    ) -> bool:
        """Check if tutor is available during the requested time."""
        day_of_week = appointment_date.weekday() + 1  # Convert to 1-7 format
        
        query = """
            SELECT availability_id FROM tutor_availability
            WHERE tutor_id = $1 
            AND day_of_week = $2
            AND is_available = true
            AND start_time <= $3
            AND end_time >= $4
            AND effective_date <= $5
            AND (expiry_date IS NULL OR expiry_date >= $5)
            AND deleted_at IS NULL
        """
        
        result = await conn.fetchrow(
            query, tutor_id, day_of_week, start_time, end_time, appointment_date
        )
        
        return result is not None
    
    async def _validate_business_hours(self, start_time: time, end_time: time) -> None:
        """Validate appointment is within business hours."""
        business_start = time(8, 0)  # 8:00 AM
        business_end = time(22, 0)   # 10:00 PM
        
        if start_time < business_start or end_time > business_end:
            raise ValidationError(
                f"Appointments must be between {business_start} and {business_end}"
            )
    
    def _calculate_duration_hours(self, start_time: time, end_time: time) -> float:
        """Calculate appointment duration in hours."""
        duration = datetime.combine(date.today(), end_time) - \
                  datetime.combine(date.today(), start_time)
        return duration.total_seconds() / 3600.0
    
    async def _check_appointment_conflicts(
        self,
        conn: asyncpg.Connection,
        tutor_id: int,
        appointment_date: date,
        start_time: time,
        end_time: time,
        exclude_appointment_id: Optional[int] = None
    ) -> Optional[asyncpg.Record]:
        """Check for appointment time conflicts."""
        query = """
            SELECT appointment_id, start_time, end_time, subject_area
            FROM appointment_sessions
            WHERE tutor_id = $1 
            AND scheduled_date = $2
            AND status NOT IN ('cancelled', 'no_show')
            AND (
                (start_time < $4 AND end_time > $3) OR
                (start_time < $3 AND end_time > $3) OR
                (start_time < $4 AND end_time > $4) OR
                (start_time >= $3 AND end_time <= $4)
            )
        """
        
        params = [tutor_id, appointment_date, start_time, end_time]
        
        if exclude_appointment_id:
            query += " AND appointment_id != $5"
            params.append(exclude_appointment_id)
        
        query += " LIMIT 1"
        
        return await conn.fetchrow(query, *params)
    
    async def _check_time_off_conflicts(
        self,
        conn: asyncpg.Connection,
        tutor_id: int,
        appointment_date: date,
        start_time: time,
        end_time: time
    ) -> Optional[asyncpg.Record]:
        """Check for time-off conflicts."""
        query = """
            SELECT request_id, reason, start_date, end_date, start_time as off_start, end_time as off_end
            FROM time_off_requests
            WHERE tutor_id = $1
            AND status = 'approved'
            AND start_date <= $2
            AND end_date >= $2
            AND (
                (start_time IS NULL AND end_time IS NULL) OR
                (start_time <= $4 AND end_time >= $3)
            )
            AND deleted_at IS NULL
            LIMIT 1
        """
        
        return await conn.fetchrow(query, tutor_id, appointment_date, start_time, end_time)
    
    async def _check_availability_conflicts(
        self,
        conn: asyncpg.Connection,
        tutor_id: int,
        appointment_date: date,
        start_time: time,
        end_time: time
    ) -> bool:
        """Check if tutor is available during the requested time."""
        available = await self._check_tutor_availability(
            conn, tutor_id, appointment_date, start_time, end_time
        )
        return not available
    
    async def _get_suggested_times(
        self,
        conn: asyncpg.Connection,
        conflict_check: ConflictCheck
    ) -> List[TimeSlot]:
        """Get alternative time slot suggestions."""
        suggestions = []
        current_date = conflict_check.proposed_date
        
        # Check next 7 days for alternatives
        for days_ahead in range(7):
            check_date = current_date + timedelta(days=days_ahead)
            day_suggestions = await self._get_daily_suggestions(
                conn, conflict_check.tutor_id, check_date,
                conflict_check.proposed_start, conflict_check.proposed_end
            )
            suggestions.extend(day_suggestions)
        
        return suggestions[:5]  # Return top 5 suggestions
    
    async def _get_daily_suggestions(
        self,
        conn: asyncpg.Connection,
        tutor_id: int,
        check_date: date,
        preferred_start: time,
        preferred_end: time
    ) -> List[TimeSlot]:
        """Get available time slots for a specific day."""
        suggestions = []
        
        # Get tutor's availability for this day
        day_of_week = check_date.weekday() + 1
        
        query = """
            SELECT start_time, end_time, break_start, break_end
            FROM tutor_availability
            WHERE tutor_id = $1 
            AND day_of_week = $2
            AND is_available = true
            AND effective_date <= $3
            AND (expiry_date IS NULL OR expiry_date >= $3)
            AND deleted_at IS NULL
        """
        
        availability = await conn.fetchrow(query, tutor_id, day_of_week, check_date)
        if not availability:
            return suggestions
        
        # Check for conflicts in available time slots
        # This is a simplified version - real implementation would be more sophisticated
        duration = datetime.combine(date.today(), preferred_end) - \
                  datetime.combine(date.today(), preferred_start)
        
        # Try different time slots within availability
        current_time = availability['start_time']
        end_time = availability['end_time']
        
        while current_time < end_time:
            proposed_end = (datetime.combine(date.today(), current_time) + duration).time()
            
            if proposed_end <= end_time:
                # Check if this slot is free
                conflict = await self._check_appointment_conflicts(
                    conn, tutor_id, check_date, current_time, proposed_end
                )
                
                if not conflict:
                    suggestions.append(TimeSlot(
                        start_time=datetime.combine(check_date, current_time),
                        end_time=datetime.combine(check_date, proposed_end),
                        is_available=True
                    ))
            
            # Move to next 30-minute slot
            current_time = (datetime.combine(date.today(), current_time) + timedelta(minutes=30)).time()
        
        return suggestions
    
    async def _schedule_appointment_reminders(
        self,
        conn: asyncpg.Connection,
        appointment_id: int
    ) -> None:
        """Schedule automatic reminders for an appointment."""
        # Implementation would create reminder records
        # This is a placeholder for the actual implementation
        logger.debug(f"Scheduled reminders for appointment {appointment_id}")
    
    async def _log_appointment_change(
        self,
        conn: asyncpg.Connection,
        appointment_id: int,
        action: str,
        reason: str,
        changed_by: int,
        field_name: Optional[str] = None,
        old_value: Optional[Any] = None,
        new_value: Optional[Any] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> None:
        """Log appointment changes for audit trail."""
        # Get user role
        user_query = """
            SELECT role_type FROM user_roles 
            WHERE user_id = $1 AND is_active = TRUE
            ORDER BY created_at DESC LIMIT 1
        """
        user_role_result = await conn.fetchrow(user_query, changed_by)
        user_role = user_role_result['role_type'] if user_role_result else 'client'
        
        # Convert values to JSON strings if they're not None
        old_value_json = None if old_value is None else str(old_value)
        new_value_json = None if new_value is None else str(new_value)
        
        # Insert audit log entry
        audit_query = """
            INSERT INTO appointment_audit_logs (
                appointment_id, action, performed_by, performed_by_role,
                field_name, old_value, new_value, change_reason,
                ip_address, user_agent, created_at
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11
            )
        """
        
        await conn.execute(
            audit_query,
            appointment_id, action, changed_by, user_role,
            field_name, old_value_json, new_value_json, reason,
            ip_address, user_agent, now_est()
        )
        
        logger.info(f"Appointment {appointment_id} {action} by user {changed_by}: {reason}")
    
    async def _validate_cancellation_policy(self, appointment: asyncpg.Record) -> None:
        """Validate cancellation policy (24-hour notice, etc.)."""
        appointment_datetime = datetime.combine(
            appointment['scheduled_date'], appointment['start_time']
        )
        
        # Require 24-hour notice for cancellation
        if appointment_datetime - now_est() < timedelta(hours=24):
            raise BusinessLogicError(
                "Cancellations require 24-hour advance notice",
                {"hours_until_appointment": (appointment_datetime - now_est()).total_seconds() / 3600}
            )
    
    async def _reschedule_appointment_reminders(
        self,
        conn: asyncpg.Connection,
        appointment_id: int
    ) -> None:
        """Update reminder schedules for rescheduled appointment."""
        logger.debug(f"Rescheduled reminders for appointment {appointment_id}")
    
    async def _cancel_appointment_reminders(
        self,
        conn: asyncpg.Connection,
        appointment_id: int
    ) -> None:
        """Cancel reminders for cancelled appointment."""
        logger.debug(f"Cancelled reminders for appointment {appointment_id}")
    
    async def _process_cancellation_billing(
        self,
        conn: asyncpg.Connection,
        appointment_id: int,
        reason: str
    ) -> None:
        """Process billing adjustments for cancelled appointment."""
        logger.debug(f"Processing cancellation billing for appointment {appointment_id}")
    
    async def _trigger_billing_for_appointment(
        self,
        conn: asyncpg.Connection,
        appointment_id: int
    ) -> None:
        """Trigger billing process for completed appointment."""
        # This will integrate with the billing service
        # For now, we'll create a billing record
        query = """
            INSERT INTO billing_invoice_items (
                appointment_id, description, quantity, unit_price, 
                currency, status, created_at, updated_at
            )
            SELECT 
                $1, 
                CONCAT('Tutoring session - ', subject_area),
                EXTRACT(EPOCH FROM (end_time - start_time)) / 3600.0,
                hourly_rate,
                currency,
                'pending',
                NOW(),
                NOW()
            FROM appointment_sessions
            WHERE appointment_id = $1
            ON CONFLICT (appointment_id) DO NOTHING
        """
        
        await conn.execute(query, appointment_id)
        logger.info(f"Triggered billing for appointment {appointment_id}")
    
    async def _cancel_billing_for_appointment(
        self,
        conn: asyncpg.Connection,
        appointment_id: int
    ) -> None:
        """Cancel any pending billing for appointment."""
        query = """
            UPDATE billing_invoice_items
            SET status = 'cancelled', updated_at = NOW()
            WHERE appointment_id = $1 AND status = 'pending'
        """
        
        await conn.execute(query, appointment_id)
        logger.info(f"Cancelled billing for appointment {appointment_id}")
    
    async def _send_tutor_confirmation_sms(
        self,
        conn: asyncpg.Connection,
        appointment_id: int
    ) -> None:
        """Send SMS to tutor for appointment confirmation."""
        # Generate confirmation code
        import random
        import string
        confirmation_code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))
        
        # Store confirmation code
        query = """
            INSERT INTO appointment_sms_confirmations (
                appointment_id, confirmation_code, sent_at, expires_at
            ) VALUES ($1, $2, NOW(), NOW() + INTERVAL '24 hours')
        """
        
        await conn.execute(query, appointment_id, confirmation_code)
        
        # Get appointment and tutor details
        appointment_query = """
            SELECT a.*, t.phone_number, t.first_name
            FROM appointment_sessions a
            JOIN tutor_profiles t ON a.tutor_id = t.tutor_id
            WHERE a.appointment_id = $1
        """
        
        appointment = await conn.fetchrow(appointment_query, appointment_id)
        
        if appointment and appointment['phone_number']:
            # Send SMS via notification service
            message = (
                f"Hi {appointment['first_name']}, please confirm your tutoring session "
                f"on {appointment['scheduled_date']} at {appointment['start_time']}. "
                f"Reply YES {confirmation_code} if completed or NO {confirmation_code} if not."
            )
            
            # This would integrate with the SMS service
            logger.info(f"SMS sent to tutor for appointment {appointment_id} with code {confirmation_code}")
    
    async def _verify_sms_confirmation_code(
        self,
        conn: asyncpg.Connection,
        appointment_id: int,
        confirmation_code: str
    ) -> bool:
        """Verify SMS confirmation code is valid and not expired."""
        query = """
            SELECT COUNT(*) FROM appointment_sms_confirmations
            WHERE appointment_id = $1 
            AND confirmation_code = $2
            AND expires_at > NOW()
            AND used_at IS NULL
        """
        
        count = await conn.fetchval(query, appointment_id, confirmation_code)
        
        if count > 0:
            # Mark as used
            update_query = """
                UPDATE appointment_sms_confirmations
                SET used_at = NOW()
                WHERE appointment_id = $1 AND confirmation_code = $2
            """
            await conn.execute(update_query, appointment_id, confirmation_code)
            return True
        
        return False
    
    async def _find_qualified_tutors(
        self,
        conn: asyncpg.Connection,
        subject_area: str,
        session_type: str
    ) -> List[int]:
        """Find tutors qualified for the subject area and session type."""
        # Placeholder implementation
        query = """
            SELECT tutor_id FROM tutor_profiles
            WHERE subject_areas @> $1
            AND is_active = true
            AND deleted_at IS NULL
            ORDER BY average_rating DESC
            LIMIT 10
        """
        
        results = await conn.fetch(query, [subject_area])
        return [r['tutor_id'] for r in results]
    
    async def _get_tutor_suggestions(
        self,
        conn: asyncpg.Connection,
        tutor_id: int,
        start_date: date,
        end_date: date,
        duration_hours: float,
        client_id: int
    ) -> List[SchedulingSuggestion]:
        """Get scheduling suggestions for a specific tutor."""
        # Placeholder implementation - real version would use ML/AI
        suggestions = []
        
        # Simple heuristic: find available slots in the next week
        current_date = start_date
        while current_date <= end_date and len(suggestions) < 3:
            day_slots = await self._get_daily_suggestions(
                conn, tutor_id, current_date, time(9, 0), time(17, 0)
            )
            
            for slot in day_slots:
                suggestions.append(SchedulingSuggestion(
                    tutor_id=tutor_id,
                    tutor_name=f"Tutor {tutor_id}",  # Would get real name
                    suggested_date=current_date,
                    suggested_start=slot.start_time.time(),
                    suggested_end=slot.end_time.time(),
                    confidence_score=0.8,  # Would calculate based on patterns
                    reasons=["Available time slot", "Good match for subject"],
                    hourly_rate=Decimal("50.00")  # Would get real rate
                ))
            
            current_date += timedelta(days=1)
        
        return suggestions
    
    async def complete_appointment_with_confirmation(
        self,
        appointment_id: int,
        status: AppointmentStatus,
        actual_duration_minutes: Optional[int] = None,
        completion_notes: Optional[str] = None,
        tutor_no_show: bool = False,
        client_no_show: bool = False,
        completed_by: int = None,
        send_sms_confirmation: bool = True
    ) -> Dict[str, Any]:
        """
        Complete an appointment with optional SMS confirmation to tutor.
        
        Args:
            appointment_id: Appointment to complete
            status: Final status (COMPLETED or NO_SHOW)
            actual_duration_minutes: Actual duration if different from scheduled
            completion_notes: Optional completion notes
            tutor_no_show: Whether tutor was a no-show
            client_no_show: Whether client was a no-show
            completed_by: User completing the appointment
            send_sms_confirmation: Whether to send SMS confirmation to tutor
        
        Returns:
            Dict with appointment, billing result, and SMS confirmation details
        """
        async with get_db_connection() as conn:
            async with conn.transaction():
                # Get appointment details
                appointment = await self.appointment_repo.find_by_id(conn, appointment_id)
                if not appointment:
                    raise ResourceNotFoundError(f"Appointment {appointment_id} not found")
                
                # Calculate actual duration if not provided
                if actual_duration_minutes is None:
                    # Use scheduled duration
                    scheduled_start = datetime.combine(appointment['scheduled_date'], appointment['start_time'])
                    scheduled_end = datetime.combine(appointment['scheduled_date'], appointment['end_time'])
                    actual_duration_minutes = int((scheduled_end - scheduled_start).total_seconds() / 60)
                
                # Update appointment status
                update_data = {
                    "status": status,
                    "actual_duration_minutes": actual_duration_minutes,
                    "duration_adjusted": actual_duration_minutes != appointment.get('original_duration_minutes'),
                    "completed_at": now_est() if status == AppointmentStatus.COMPLETED else None,
                    "completion_notes": completion_notes,
                    "updated_at": now_est(),
                    "updated_by": completed_by
                }
                
                # Add no-show flags
                if tutor_no_show:
                    update_data["tutor_no_show"] = True
                if client_no_show:
                    update_data["client_no_show"] = True
                
                updated_appointment = await self.appointment_repo.update(conn, appointment_id, update_data)
                
                # Generate confirmation token for SMS
                confirmation_token = None
                sms_sent = False
                
                if send_sms_confirmation and status == AppointmentStatus.COMPLETED:
                    # Import locally to avoid circular dependency
                    from app.services.appointment_confirmation_service import AppointmentConfirmationService
                    from app.services.twilio_service import TwilioService
                    
                    confirmation_service = AppointmentConfirmationService()
                    twilio_service = TwilioService()
                    
                    # Create confirmation request
                    result = await confirmation_service.request_completion_confirmation(
                        appointment_id=appointment_id,
                        send_immediately=False  # We'll send manually
                    )
                    
                    confirmation_token = result.get('confirmation_token')
                    
                    # Get tutor phone number
                    tutor_query = "SELECT phone_number FROM tutors WHERE tutor_id = $1"
                    tutor_record = await conn.fetchrow(tutor_query, appointment['tutor_id'])
                    
                    if tutor_record and tutor_record['phone_number']:
                        # Prepare SMS message
                        client_query = "SELECT first_name, last_name FROM clients WHERE client_id = $1"
                        client_record = await conn.fetchrow(client_query, appointment['client_id'])
                        client_name = f"{client_record['first_name']} {client_record['last_name']}" if client_record else "Client"
                        
                        message = (
                            f"TutorAide: Please confirm completion of your {appointment['subject_area']} "
                            f"session with {client_name} on {appointment['scheduled_date'].strftime('%Y-%m-%d')}. "
                            f"Reply DONE if completed, NOSHOW if client didn't attend. "
                            f"Code: {confirmation_token[:8]}"
                        )
                        
                        try:
                            # Send SMS
                            sms_result = await twilio_service.send_sms(
                                to_number=tutor_record['phone_number'],
                                message=message,
                                user_id=appointment['tutor_id'],
                                metadata={
                                    'appointment_id': appointment_id,
                                    'confirmation_token': confirmation_token,
                                    'type': 'completion_confirmation'
                                }
                            )
                            sms_sent = True
                            logger.info(f"Sent completion SMS for appointment {appointment_id}")
                        except Exception as e:
                            logger.error(f"Failed to send completion SMS: {e}")
                
                # Trigger billing if completed and not a no-show
                billing_triggered = False
                billing_result = None
                
                if status == AppointmentStatus.COMPLETED and not tutor_no_show and not client_no_show:
                    try:
                        # Import billing service locally
                        from app.services.billing_service import BillingService
                        billing_service = BillingService()
                        
                        # Create invoice for completed appointment
                        invoice_result = await billing_service.create_invoice_for_appointment(
                            appointment_id=appointment_id,
                            actual_duration_minutes=actual_duration_minutes
                        )
                        
                        billing_triggered = True
                        billing_result = {
                            'invoice_id': invoice_result.get('invoice_id'),
                            'amount': invoice_result.get('total_amount'),
                            'status': 'created'
                        }
                        
                        logger.info(f"Created invoice for appointment {appointment_id}")
                    except Exception as e:
                        logger.error(f"Failed to create invoice for appointment {appointment_id}: {e}")
                
                return {
                    'appointment': Appointment(**updated_appointment),
                    'billing_triggered': billing_triggered,
                    'billing_result': billing_result,
                    'sms_sent': sms_sent,
                    'confirmation_token': confirmation_token[:8] if confirmation_token else None
                }
    
    async def process_completion_confirmation(
        self,
        phone_number: str,
        response: str,
        message_sid: str
    ) -> Dict[str, Any]:
        """
        Process appointment completion confirmation from tutor via SMS.
        
        Args:
            phone_number: Tutor's phone number
            response: Response type (COMPLETED, NOSHOW)
            message_sid: Twilio message SID
        
        Returns:
            Processing result
        """
        async with get_db_connection() as conn:
            # Find tutor by phone number
            tutor_query = """
                SELECT tutor_id FROM tutors 
                WHERE phone_number = $1 AND deleted_at IS NULL
            """
            tutor_record = await conn.fetchrow(tutor_query, phone_number)
            
            if not tutor_record:
                raise ResourceNotFoundError(f"No tutor found with phone number {phone_number}")
            
            tutor_id = tutor_record['tutor_id']
            
            # Find most recent pending completion confirmation for this tutor
            appointment_query = """
                SELECT a.appointment_id, a.scheduled_date, a.start_time, a.end_time, a.status
                FROM appointment_sessions a
                WHERE a.tutor_id = $1
                AND a.status IN ('scheduled', 'confirmed', 'in_progress')
                AND a.scheduled_date <= CURRENT_DATE
                AND (a.scheduled_date + a.end_time::interval) <= NOW()
                ORDER BY a.scheduled_date DESC, a.end_time DESC
                LIMIT 1
            """
            appointment = await conn.fetchrow(appointment_query, tutor_id)
            
            if not appointment:
                raise BusinessLogicError("No recent appointment found to confirm")
            
            appointment_id = appointment['appointment_id']
            
            # Process based on response
            if response == 'COMPLETED':
                # Mark as completed
                result = await self.complete_appointment_with_confirmation(
                    appointment_id=appointment_id,
                    status=AppointmentStatus.COMPLETED,
                    completed_by=tutor_id,
                    send_sms_confirmation=False  # Don't send another SMS
                )
                
                return {
                    'appointment_id': appointment_id,
                    'status': 'completed',
                    'message': 'Appointment marked as completed',
                    'billing_triggered': result['billing_triggered']
                }
                
            elif response == 'NOSHOW':
                # Mark as no-show
                result = await self.complete_appointment_with_confirmation(
                    appointment_id=appointment_id,
                    status=AppointmentStatus.NO_SHOW,
                    client_no_show=True,
                    completed_by=tutor_id,
                    send_sms_confirmation=False
                )
                
                return {
                    'appointment_id': appointment_id,
                    'status': 'no_show',
                    'message': 'Appointment marked as client no-show',
                    'billing_triggered': False
                }
            
            else:
                raise BusinessLogicError(f"Invalid response type: {response}")
    
    async def get_appointment_audit_logs(
        self,
        appointment_id: int,
        limit: int = 50,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """
        Get audit logs for a specific appointment.
        
        Args:
            appointment_id: ID of the appointment
            limit: Maximum number of logs to return
            offset: Number of logs to skip
            
        Returns:
            List of audit log entries
        """
        async with await get_db_connection() as conn:
            query = """
                SELECT 
                    al.audit_id,
                    al.appointment_id,
                    al.action,
                    al.performed_by,
                    al.performed_by_role,
                    u.first_name || ' ' || u.last_name as performed_by_name,
                    al.field_name,
                    al.old_value,
                    al.new_value,
                    al.change_reason,
                    al.ip_address,
                    al.user_agent,
                    al.created_at
                FROM appointment_audit_logs al
                JOIN user_accounts u ON al.performed_by = u.user_id
                WHERE al.appointment_id = $1
                ORDER BY al.created_at DESC
                LIMIT $2 OFFSET $3
            """
            
            rows = await conn.fetch(query, appointment_id, limit, offset)
            
            logs = []
            for row in rows:
                logs.append({
                    'audit_id': row['audit_id'],
                    'appointment_id': row['appointment_id'],
                    'action': row['action'],
                    'performed_by': row['performed_by'],
                    'performed_by_role': row['performed_by_role'],
                    'performed_by_name': row['performed_by_name'],
                    'field_name': row['field_name'],
                    'old_value': row['old_value'],
                    'new_value': row['new_value'],
                    'change_reason': row['change_reason'],
                    'ip_address': str(row['ip_address']) if row['ip_address'] else None,
                    'user_agent': row['user_agent'],
                    'created_at': row['created_at'].isoformat() if row['created_at'] else None
                })
            
            return logs


# ============================================
# Additional Repository Classes
# ============================================

class TutorAvailabilityRepository(BaseRepository):
    """Repository for tutor availability management."""
    
    def __init__(self):
        super().__init__(table_name="tutor_availability", id_column="availability_id")


class TimeOffRequestRepository(BaseRepository):
    """Repository for time-off request management."""
    
    def __init__(self):
        super().__init__(table_name="time_off_requests", id_column="request_id")


class AppointmentConflictRepository(BaseRepository):
    """Repository for appointment conflict tracking."""
    
    def __init__(self):
        super().__init__(table_name="appointment_conflicts", id_column="conflict_id")


class RecurringAppointmentService:
    """Service for managing recurring appointment series."""
    
    def __init__(self):
        self.appointment_repo = AppointmentRepository()
        self.availability_repo = TutorAvailabilityRepository()
    
    async def create_recurring_series(
        self, recurring_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Create a recurring appointment series with automatic scheduling.
        
        Args:
            recurring_data: Recurring appointment configuration
        
        Returns:
            Created recurring series with appointment IDs
        """
        try:
            async with get_db_connection() as conn:
                async with conn.transaction():
                    # Create recurring appointment template
                    recurring_appointment = await self._create_recurring_template(conn, recurring_data)
                    
                    # Generate individual appointments
                    appointments = await self._generate_series_appointments(
                        conn, recurring_appointment, recurring_data
                    )
                    
                    logger.info(f"Created recurring series {recurring_appointment['recurring_id']} with {len(appointments)} appointments")
                    
                    return {
                        'recurring_id': recurring_appointment['recurring_id'],
                        'series_name': f"{recurring_data.get('subject_area', 'Session')} - {recurring_data.get('frequency_weeks', 1)} week(s)",
                        'total_appointments': len(appointments),
                        'appointment_ids': [apt['appointment_id'] for apt in appointments],
                        'start_date': recurring_data['start_date'],
                        'end_date': recurring_data.get('end_date'),
                        'conflicts': []  # Will be populated if conflicts found
                    }
                    
        except Exception as e:
            logger.error(f"Error creating recurring appointment series: {e}")
            raise BusinessLogicError(
                "Failed to create recurring appointment series",
                recurring_data
            )
    
    async def _create_recurring_template(
        self, conn: asyncpg.Connection, recurring_data: Dict[str, Any]
    ) -> asyncpg.Record:
        """Create the recurring appointment template."""
        query = """
            INSERT INTO recurring_appointments (
                tutor_id, client_id, dependant_id, day_of_week, start_time, end_time,
                subject_area, location_details, hourly_rate, currency, start_date, end_date,
                frequency_weeks, is_active, created_at, updated_at
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16
            ) RETURNING *
        """
        
        now = now_est()
        
        result = await conn.fetchrow(
            query,
            recurring_data['tutor_id'],
            recurring_data['client_id'],
            recurring_data.get('dependant_id'),
            recurring_data['day_of_week'],
            recurring_data['start_time'],
            recurring_data['end_time'],
            recurring_data['subject_area'],
            recurring_data.get('location_details', {}),
            recurring_data['hourly_rate'],
            recurring_data.get('currency', 'CAD'),
            recurring_data['start_date'],
            recurring_data.get('end_date'),
            recurring_data.get('frequency_weeks', 1),
            True,
            now,
            now
        )
        
        return result
    
    async def _generate_series_appointments(
        self, conn: asyncpg.Connection, recurring_template: asyncpg.Record, recurring_data: Dict[str, Any]
    ) -> List[asyncpg.Record]:
        """Generate individual appointments from recurring template."""
        appointments = []
        current_date = recurring_template['start_date']
        end_date = recurring_template['end_date'] or (current_date + timedelta(weeks=52))  # Default 1 year
        frequency_days = recurring_template['frequency_weeks'] * 7
        
        # Calculate target day of week
        target_weekday = recurring_template['day_of_week'] - 1  # Convert to 0-6 format
        
        # Find first occurrence of target weekday
        days_ahead = target_weekday - current_date.weekday()
        if days_ahead <= 0:  # Target day already happened this week
            days_ahead += 7
        current_date = current_date + timedelta(days=days_ahead)
        
        while current_date <= end_date:
            # Check for conflicts before creating
            conflict = await self._check_appointment_conflict_simple(
                conn,
                recurring_template['tutor_id'],
                current_date,
                recurring_template['start_time'],
                recurring_template['end_time']
            )
            
            if not conflict:
                appointment_data = {
                    'tutor_id': recurring_template['tutor_id'],
                    'client_id': recurring_template['client_id'],
                    'dependant_id': recurring_template['dependant_id'],
                    'scheduled_date': current_date,
                    'start_time': recurring_template['start_time'],
                    'end_time': recurring_template['end_time'],
                    'subject_area': recurring_template['subject_area'],
                    'location_details': recurring_template['location_details'],
                    'hourly_rate': recurring_template['hourly_rate'],
                    'currency': recurring_template['currency'],
                    'status': 'scheduled',
                    'session_type': 'individual',
                    'recurring_appointment_id': recurring_template['recurring_id'],
                    'is_exception': False,
                    'created_at': now_est(),
                    'updated_at': now_est()
                }
                
                appointment = await self.appointment_repo.create(conn, appointment_data)
                appointments.append(appointment)
            else:
                logger.warning(f"Skipped appointment on {current_date} due to conflict")
            
            # Move to next occurrence
            current_date += timedelta(days=frequency_days)
        
        return appointments
    
    async def _check_appointment_conflict_simple(
        self, conn: asyncpg.Connection, tutor_id: int, appointment_date: date, start_time: time, end_time: time
    ) -> bool:
        """Simple conflict check for recurring appointment generation."""
        query = """
            SELECT COUNT(*) FROM appointment_sessions
            WHERE tutor_id = $1 
            AND scheduled_date = $2
            AND status NOT IN ('cancelled', 'no_show')
            AND (
                (start_time < $4 AND end_time > $3) OR
                (start_time < $3 AND end_time > $3) OR
                (start_time < $4 AND end_time > $4) OR
                (start_time >= $3 AND end_time <= $4)
            )
        """
        
        count = await conn.fetchval(query, tutor_id, appointment_date, start_time, end_time)
        return count > 0
    
    async def update_recurring_series(
        self, recurring_id: int, update_data: Dict[str, Any], update_mode: str = 'future'
    ) -> Dict[str, Any]:
        """
        Update a recurring appointment series.
        
        Args:
            recurring_id: Recurring appointment ID
            update_data: Updates to apply
            update_mode: 'this_only', 'future', or 'all'
        
        Returns:
            Update results
        """
        try:
            async with get_db_connection() as conn:
                async with conn.transaction():
                    # Get recurring template
                    recurring = await self._get_recurring_template(conn, recurring_id)
                    if not recurring:
                        raise ResourceNotFoundError(f"Recurring appointment {recurring_id} not found")
                    
                    updated_appointments = []
                    
                    if update_mode == 'all':
                        # Update template and all future appointments
                        await self._update_recurring_template(conn, recurring_id, update_data)
                        updated_appointments = await self._update_all_series_appointments(
                            conn, recurring_id, update_data
                        )
                    
                    elif update_mode == 'future':
                        # Update template and future appointments only
                        cutoff_date = update_data.get('effective_date', date.today())
                        await self._update_recurring_template(conn, recurring_id, update_data)
                        updated_appointments = await self._update_future_series_appointments(
                            conn, recurring_id, update_data, cutoff_date
                        )
                    
                    elif update_mode == 'this_only':
                        # Create exception for specific appointment
                        appointment_date = update_data.get('appointment_date')
                        if not appointment_date:
                            raise ValueError("appointment_date required for 'this_only' mode")
                        
                        updated_appointments = await self._create_series_exception(
                            conn, recurring_id, appointment_date, update_data
                        )
                    
                    logger.info(f"Updated recurring series {recurring_id} (mode: {update_mode})")
                    
                    return {
                        'recurring_id': recurring_id,
                        'update_mode': update_mode,
                        'updated_appointments': len(updated_appointments),
                        'appointment_ids': [apt['appointment_id'] for apt in updated_appointments]
                    }
                    
        except Exception as e:
            logger.error(f"Error updating recurring series: {e}")
            raise BusinessLogicError(
                "Failed to update recurring appointment series",
                {"recurring_id": recurring_id, "update_mode": update_mode}
            )
    
    async def cancel_recurring_series(
        self, recurring_id: int, cancel_mode: str = 'future', effective_date: Optional[date] = None
    ) -> Dict[str, Any]:
        """
        Cancel a recurring appointment series.
        
        Args:
            recurring_id: Recurring appointment ID
            cancel_mode: 'future', 'all', or 'this_only'
            effective_date: Effective date for cancellation
        
        Returns:
            Cancellation results
        """
        try:
            async with get_db_connection() as conn:
                async with conn.transaction():
                    effective_date = effective_date or date.today()
                    cancelled_appointments = []
                    
                    if cancel_mode == 'all':
                        # Cancel entire series
                        await self._deactivate_recurring_template(conn, recurring_id)
                        cancelled_appointments = await self._cancel_all_series_appointments(conn, recurring_id)
                    
                    elif cancel_mode == 'future':
                        # Cancel from effective date forward
                        await self._set_recurring_end_date(conn, recurring_id, effective_date)
                        cancelled_appointments = await self._cancel_future_series_appointments(
                            conn, recurring_id, effective_date
                        )
                    
                    elif cancel_mode == 'this_only':
                        # Cancel specific appointment
                        if not effective_date:
                            raise ValueError("effective_date required for 'this_only' mode")
                        
                        cancelled_appointments = await self._cancel_specific_series_appointment(
                            conn, recurring_id, effective_date
                        )
                    
                    logger.info(f"Cancelled recurring series {recurring_id} (mode: {cancel_mode})")
                    
                    return {
                        'recurring_id': recurring_id,
                        'cancel_mode': cancel_mode,
                        'cancelled_appointments': len(cancelled_appointments),
                        'effective_date': effective_date
                    }
                    
        except Exception as e:
            logger.error(f"Error cancelling recurring series: {e}")
            raise BusinessLogicError(
                "Failed to cancel recurring appointment series",
                {"recurring_id": recurring_id, "cancel_mode": cancel_mode}
            )
    
    async def get_recurring_series_info(self, recurring_id: int) -> Dict[str, Any]:
        """Get comprehensive information about a recurring series."""
        try:
            async with get_db_connection() as conn:
                # Get recurring template
                recurring = await self._get_recurring_template(conn, recurring_id)
                if not recurring:
                    raise ResourceNotFoundError(f"Recurring appointment {recurring_id} not found")
                
                # Get all appointments in series
                appointments = await self._get_series_appointments(conn, recurring_id)
                
                # Calculate statistics
                stats = self._calculate_series_statistics(appointments)
                
                return {
                    'recurring_id': recurring_id,
                    'template': dict(recurring),
                    'total_appointments': len(appointments),
                    'completed_appointments': stats['completed'],
                    'upcoming_appointments': stats['upcoming'],
                    'cancelled_appointments': stats['cancelled'],
                    'next_appointment': stats['next_appointment'],
                    'last_appointment': stats['last_appointment'],
                    'appointments': [dict(apt) for apt in appointments]
                }
                
        except Exception as e:
            logger.error(f"Error getting recurring series info: {e}")
            raise
    
    # Helper methods for recurring appointment management
    async def _get_recurring_template(self, conn: asyncpg.Connection, recurring_id: int) -> Optional[asyncpg.Record]:
        """Get recurring appointment template."""
        query = "SELECT * FROM recurring_appointments WHERE recurring_id = $1 AND deleted_at IS NULL"
        return await conn.fetchrow(query, recurring_id)
    
    async def _update_recurring_template(self, conn: asyncpg.Connection, recurring_id: int, update_data: Dict[str, Any]) -> None:
        """Update recurring appointment template."""
        fields = []
        values = []
        param_count = 0
        
        for field, value in update_data.items():
            if field in ['start_time', 'end_time', 'subject_area', 'hourly_rate', 'location_details']:
                param_count += 1
                fields.append(f"{field} = ${param_count}")
                values.append(value)
        
        if fields:
            param_count += 1
            fields.append(f"updated_at = ${param_count}")
            values.append(now_est())
            
            param_count += 1
            query = f"UPDATE recurring_appointments SET {', '.join(fields)} WHERE recurring_id = ${param_count}"
            values.append(recurring_id)
            
            await conn.execute(query, *values)
    
    async def _get_series_appointments(self, conn: asyncpg.Connection, recurring_id: int) -> List[asyncpg.Record]:
        """Get all appointments in a recurring series."""
        query = """
            SELECT * FROM appointment_sessions 
            WHERE recurring_appointment_id = $1 
            ORDER BY scheduled_date, start_time
        """
        return await conn.fetch(query, recurring_id)
    
    async def _calculate_series_statistics(self, appointments: List[asyncpg.Record]) -> Dict[str, Any]:
        """Calculate statistics for a recurring series."""
        today = date.today()
        completed = sum(1 for apt in appointments if apt['status'] == 'completed')
        cancelled = sum(1 for apt in appointments if apt['status'] == 'cancelled')
        upcoming = sum(1 for apt in appointments if apt['scheduled_date'] >= today and apt['status'] in ['scheduled', 'confirmed'])
        
        # Find next and last appointments
        future_appointments = [apt for apt in appointments if apt['scheduled_date'] >= today and apt['status'] in ['scheduled', 'confirmed']]
        past_appointments = [apt for apt in appointments if apt['scheduled_date'] < today]
        
        next_appointment = min(future_appointments, key=lambda x: x['scheduled_date']) if future_appointments else None
        last_appointment = max(past_appointments, key=lambda x: x['scheduled_date']) if past_appointments else None
        
        return {
            'completed': completed,
            'cancelled': cancelled,
            'upcoming': upcoming,
            'next_appointment': dict(next_appointment) if next_appointment else None,
            'last_appointment': dict(last_appointment) if last_appointment else None
        }
    
    async def _cancel_future_series_appointments(self, conn: asyncpg.Connection, recurring_id: int, from_date: date) -> List[asyncpg.Record]:
        """Cancel future appointments in a series."""
        query = """
            UPDATE appointment_sessions 
            SET status = 'cancelled', 
                notes = COALESCE(notes, '') || ' - Cancelled as part of recurring series',
                updated_at = $3
            WHERE recurring_appointment_id = $1 
            AND scheduled_date >= $2
            AND status NOT IN ('completed', 'cancelled')
            RETURNING *
        """
        return await conn.fetch(query, recurring_id, from_date, now_est())
    
    async def _deactivate_recurring_template(self, conn: asyncpg.Connection, recurring_id: int) -> None:
        """Deactivate a recurring appointment template."""
        query = """
            UPDATE recurring_appointments 
            SET is_active = false, updated_at = $2
            WHERE recurring_id = $1
        """
        await conn.execute(query, recurring_id, now_est())