import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Search, Filter, Clock, CheckCircle, XCircle, AlertCircle, Calendar, MapPin, BookOpen } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import api from '../../services/api';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import Button from '../../components/common/Button';

interface ApplicationSummary {
  invitation_id: number;
  email: string;
  first_name: string;
  last_name: string;
  application_submitted_at: string;
  review_status: string;
  application_status: string;
  parsed_education: {
    current_level: string;
    institution: string;
  };
  parsed_experience: {
    years_teaching_total?: number;
  };
  parsed_preferences: {
    subjects: string[];
    preferred_location: string;
  };
}

interface ApplicationStats {
  pending_applications: number;
  approved_this_week: number;
  rejected_this_week: number;
  waitlisted: number;
  avg_review_time_hours: number;
}

const ApplicationsList: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  
  const [applications, setApplications] = useState<ApplicationSummary[]>([]);
  const [stats, setStats] = useState<ApplicationStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('under_review');
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    fetchApplications();
    fetchStats();
  }, [filter]);

  const fetchApplications = async () => {
    try {
      const response = await api.get('/tutor-applications/pending', {
        params: { status: filter }
      });
      setApplications(response.data);
    } catch (error) {
      console.error('Error fetching applications:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await api.get('/tutor-applications/stats/summary');
      setStats(response.data);
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'rejected':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'waitlisted':
        return <AlertCircle className="w-5 h-5 text-yellow-500" />;
      default:
        return <Clock className="w-5 h-5 text-blue-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'waitlisted':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-blue-100 text-blue-800';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return t('common.today');
    if (diffDays === 1) return t('common.yesterday');
    if (diffDays < 7) return `${diffDays} ${t('common.daysAgo')}`;
    
    return date.toLocaleDateString();
  };

  const filteredApplications = applications.filter(app => {
    const query = searchQuery.toLowerCase();
    return (
      app.first_name.toLowerCase().includes(query) ||
      app.last_name.toLowerCase().includes(query) ||
      app.email.toLowerCase().includes(query) ||
      app.parsed_preferences.subjects.some(s => s.toLowerCase().includes(query))
    );
  });

  if (loading) return <LoadingSpinner />;

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">{t('tutors.applications.title')}</h1>
        <p className="text-gray-600">{t('tutors.applications.subtitle')}</p>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <div className="bg-white rounded-xl shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">{t('tutors.stats.pending')}</p>
                <p className="text-2xl font-bold text-blue-600">{stats.pending_applications}</p>
              </div>
              <Clock className="w-8 h-8 text-blue-200" />
            </div>
          </div>
          
          <div className="bg-white rounded-xl shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">{t('tutors.stats.approvedWeek')}</p>
                <p className="text-2xl font-bold text-green-600">{stats.approved_this_week}</p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-200" />
            </div>
          </div>
          
          <div className="bg-white rounded-xl shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">{t('tutors.stats.waitlisted')}</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.waitlisted}</p>
              </div>
              <AlertCircle className="w-8 h-8 text-yellow-200" />
            </div>
          </div>
          
          <div className="bg-white rounded-xl shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">{t('tutors.stats.avgReview')}</p>
                <p className="text-2xl font-bold text-gray-600">{stats.avg_review_time_hours.toFixed(1)}h</p>
              </div>
              <Calendar className="w-8 h-8 text-gray-200" />
            </div>
          </div>
        </div>
      )}

      {/* Filters and Search */}
      <div className="bg-white rounded-xl shadow-sm p-4 mb-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder={t('tutors.applications.searchPlaceholder')}
                className="w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>
          </div>
          
          <div className="flex gap-2">
            <Button
              variant={filter === 'under_review' ? 'primary' : 'secondary'}
              onClick={() => setFilter('under_review')}
              size="sm"
            >
              {t('tutors.filter.pending')}
            </Button>
            <Button
              variant={filter === 'approved' ? 'primary' : 'secondary'}
              onClick={() => setFilter('approved')}
              size="sm"
            >
              {t('tutors.filter.approved')}
            </Button>
            <Button
              variant={filter === 'waitlisted' ? 'primary' : 'secondary'}
              onClick={() => setFilter('waitlisted')}
              size="sm"
            >
              {t('tutors.filter.waitlisted')}
            </Button>
            <Button
              variant={filter === 'rejected' ? 'primary' : 'secondary'}
              onClick={() => setFilter('rejected')}
              size="sm"
            >
              {t('tutors.filter.rejected')}
            </Button>
          </div>
        </div>
      </div>

      {/* Applications List */}
      <div className="space-y-4">
        {filteredApplications.length === 0 ? (
          <div className="bg-white rounded-xl shadow-sm p-8 text-center">
            <p className="text-gray-500">{t('tutors.applications.noApplications')}</p>
          </div>
        ) : (
          filteredApplications.map((app) => (
            <div
              key={app.invitation_id}
              className="bg-white rounded-xl shadow-sm p-6 hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => navigate(`/tutors/applications/${app.invitation_id}`)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-4 mb-3">
                    <h3 className="text-lg font-semibold">
                      {app.first_name} {app.last_name}
                    </h3>
                    <span className={`px-3 py-1 rounded-full text-sm ${getStatusColor(app.application_status)}`}>
                      {app.review_status}
                    </span>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div className="flex items-center gap-2 text-gray-600">
                      <Calendar className="w-4 h-4" />
                      <span>{formatDate(app.application_submitted_at)}</span>
                    </div>
                    
                    <div className="flex items-center gap-2 text-gray-600">
                      <MapPin className="w-4 h-4" />
                      <span>{app.parsed_preferences.preferred_location}</span>
                    </div>
                    
                    <div className="flex items-center gap-2 text-gray-600">
                      <BookOpen className="w-4 h-4" />
                      <span>{app.parsed_education.current_level} - {app.parsed_education.institution}</span>
                    </div>
                  </div>
                  
                  <div className="mt-3 flex flex-wrap gap-2">
                    {app.parsed_preferences.subjects.slice(0, 4).map((subject, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-primary-light/30 text-primary rounded-full text-xs"
                      >
                        {subject}
                      </span>
                    ))}
                    {app.parsed_preferences.subjects.length > 4 && (
                      <span className="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-xs">
                        +{app.parsed_preferences.subjects.length - 4} {t('common.more')}
                      </span>
                    )}
                  </div>
                </div>
                
                <div className="ml-4">
                  {getStatusIcon(app.application_status)}
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default ApplicationsList;