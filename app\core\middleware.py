"""
Enhanced middleware for logging, error handling, and security.
"""

import time
import uuid
from typing import Callable, Dict, Any
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from starlette.responses import Response
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.responses import <PERSON><PERSON><PERSON>esponse

from app.core.logging import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ErrorContext
from app.core.exceptions import (
    TutorAideException, ErrorHandler, ValidationError,
    AuthenticationError, AuthorizationError, RateLimitError
)


class LoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for request/response logging and performance monitoring."""
    
    def __init__(self, app: FastAPI):
        """Initialize logging middleware."""
        super().__init__(app)
        self.logger = TutorAideLogger("middleware")
        self.error_handler = ErrorHandler()
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request with logging and performance monitoring."""
        # Generate request ID if not present
        request_id = request.headers.get("X-Request-ID") or str(uuid.uuid4())
        
        # Create error context
        context = ErrorContext.from_request(request, operation="http_request")
        context.request_id = request_id
        
        # Log request start
        start_time = time.time()
        
        self.logger.info(f"Request started: {request.method} {request.url.path}", extra={
            "request_id": request_id,
            "method": request.method,
            "path": str(request.url.path),
            "query_params": dict(request.query_params),
            "user_agent": request.headers.get("User-Agent"),
            "ip_address": getattr(request.client, 'host', None),
            "log_type": "request_start"
        })
        
        try:
            # Process request
            response = await call_next(request)
            
            # Calculate duration
            duration_ms = (time.time() - start_time) * 1000
            
            # Log request completion
            self.logger.info(f"Request completed: {request.method} {request.url.path}", extra={
                "request_id": request_id,
                "status_code": response.status_code,
                "duration_ms": duration_ms,
                "log_type": "request_complete"
            })
            
            # Log performance metric
            self.error_handler.log_performance_metric(
                "http_request",
                duration_ms=duration_ms,
                details={
                    "method": request.method,
                    "path": str(request.url.path),
                    "status_code": response.status_code
                },
                context=context
            )
            
            # Add request ID to response headers
            response.headers["X-Request-ID"] = request_id
            
            return response
            
        except Exception as e:
            # Calculate duration even for errors
            duration_ms = (time.time() - start_time) * 1000
            
            # Handle and log the exception
            error_response = self.error_handler.handle_exception(e, context)
            
            # Log request error
            self.logger.error(f"Request failed: {request.method} {request.url.path}", extra={
                "request_id": request_id,
                "error_code": error_response.get("error_code"),
                "duration_ms": duration_ms,
                "log_type": "request_error"
            })
            
            # Determine HTTP status code
            if isinstance(e, TutorAideException):
                status_code = e.http_status_code
            elif isinstance(e, HTTPException):
                status_code = e.status_code
            else:
                status_code = 500
            
            # Create error response
            response = JSONResponse(
                content=error_response,
                status_code=status_code
            )
            response.headers["X-Request-ID"] = request_id
            
            return response


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Middleware for adding security headers."""
    
    def __init__(self, app: FastAPI):
        """Initialize security headers middleware."""
        super().__init__(app)
        self.logger = TutorAideLogger("security_middleware")
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Add security headers to response."""
        response = await call_next(request)
        
        # Add security headers
        security_headers = {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Referrer-Policy": "strict-origin-when-cross-origin",
            "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';",
            "Permissions-Policy": "camera=(), microphone=(), geolocation=()"
        }
        
        # Only add HSTS for HTTPS requests
        if request.url.scheme == "https":
            security_headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        
        for header, value in security_headers.items():
            response.headers[header] = value
        
        return response


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Basic rate limiting middleware."""
    
    def __init__(self, app: FastAPI, requests_per_minute: int = 60):
        """Initialize rate limiting middleware."""
        super().__init__(app)
        self.requests_per_minute = requests_per_minute
        self.request_counts: Dict[str, Dict[str, Any]] = {}
        self.logger = TutorAideLogger("rate_limit_middleware")
        self.error_handler = ErrorHandler()
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Check rate limits before processing request."""
        client_ip = getattr(request.client, 'host', 'unknown')
        current_time = time.time()
        
        # Clean old entries (older than 1 minute)
        cutoff_time = current_time - 60
        self.request_counts = {
            ip: data for ip, data in self.request_counts.items()
            if data["last_request"] > cutoff_time
        }
        
        # Check current IP
        if client_ip in self.request_counts:
            ip_data = self.request_counts[client_ip]
            
            # Reset count if more than a minute has passed
            if current_time - ip_data["first_request"] >= 60:
                ip_data["count"] = 1
                ip_data["first_request"] = current_time
            else:
                ip_data["count"] += 1
            
            ip_data["last_request"] = current_time
            
            # Check if rate limit exceeded
            if ip_data["count"] > self.requests_per_minute:
                # Log rate limit violation
                self.error_handler.log_security_event(
                    "rate_limit_exceeded",
                    details={
                        "ip_address": client_ip,
                        "requests_count": ip_data["count"],
                        "limit": self.requests_per_minute,
                        "path": str(request.url.path)
                    }
                )
                
                # Create rate limit error
                error = RateLimitError(
                    f"Rate limit exceeded: {ip_data['count']} requests in the last minute",
                    limit=self.requests_per_minute,
                    window_seconds=60,
                    retry_after=60
                )
                
                context = ErrorContext.from_request(request)
                error_response = self.error_handler.handle_exception(error, context)
                
                response = JSONResponse(
                    content=error_response,
                    status_code=429
                )
                response.headers["Retry-After"] = "60"
                return response
        else:
            # First request from this IP
            self.request_counts[client_ip] = {
                "count": 1,
                "first_request": current_time,
                "last_request": current_time
            }
        
        # Process request normally
        return await call_next(request)


class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """Global error handling middleware."""
    
    def __init__(self, app: FastAPI):
        """Initialize error handling middleware."""
        super().__init__(app)
        self.logger = TutorAideLogger("error_middleware")
        self.error_handler = ErrorHandler()
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Handle errors globally."""
        try:
            return await call_next(request)
        except Exception as e:
            # Create error context
            context = ErrorContext.from_request(
                request,
                operation="error_handling"
            )
            
            # Handle the exception
            error_response = self.error_handler.handle_exception(e, context)
            
            # Determine status code
            if isinstance(e, TutorAideException):
                status_code = e.http_status_code
            elif isinstance(e, HTTPException):
                status_code = e.status_code
            else:
                status_code = 500
            
            # Log security events for certain errors
            if isinstance(e, (AuthenticationError, AuthorizationError)):
                self.error_handler.log_security_event(
                    "authentication_failure" if isinstance(e, AuthenticationError) else "authorization_failure",
                    details={
                        "path": str(request.url.path),
                        "method": request.method,
                        "error_details": getattr(e, 'details', {})
                    },
                    context=context
                )
            
            return JSONResponse(
                content=error_response,
                status_code=status_code
            )


def setup_middleware(app: FastAPI) -> None:
    """Set up all middleware for the FastAPI application."""
    # Add middleware in reverse order (they wrap each other)
    app.add_middleware(ErrorHandlingMiddleware)
    app.add_middleware(RateLimitMiddleware, requests_per_minute=120)  # 2 requests per second
    app.add_middleware(SecurityHeadersMiddleware)
    app.add_middleware(LoggingMiddleware)
    
    # Log middleware setup
    logger = TutorAideLogger("middleware_setup")
    logger.info("Middleware setup completed", extra={
        "middleware_stack": [
            "LoggingMiddleware",
            "SecurityHeadersMiddleware", 
            "RateLimitMiddleware",
            "ErrorHandlingMiddleware"
        ],
        "rate_limit": "120 requests/minute"
    })