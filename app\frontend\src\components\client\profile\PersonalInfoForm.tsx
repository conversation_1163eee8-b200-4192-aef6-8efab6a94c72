import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Input } from '../../common/Input';
import Button from '../../common/Button';
import { User, Calendar, Phone, Mail } from 'lucide-react';

interface PersonalInfoFormProps {
  profile: {
    first_name: string;
    last_name: string;
    date_of_birth: string | null;
    phone_number: string;
    email: string;
  };
  onUpdate: (data: any) => void;
  saving: boolean;
}

export const PersonalInfoForm: React.FC<PersonalInfoFormProps> = ({
  profile,
  onUpdate,
  saving
}) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    first_name: profile.first_name || '',
    last_name: profile.last_name || '',
    date_of_birth: profile.date_of_birth || '',
    phone_number: profile.phone_number || ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  const formatPhoneNumber = (value: string) => {
    // Remove all non-digits
    const cleaned = value.replace(/\D/g, '');
    
    // Format as (XXX) XXX-XXXX
    if (cleaned.length <= 3) {
      return cleaned;
    } else if (cleaned.length <= 6) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3)}`;
    } else {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6, 10)}`;
    }
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPhoneNumber(e.target.value);
    setFormData({ ...formData, phone_number: formatted });
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.first_name.trim()) {
      newErrors.first_name = t('client.profile.errors.firstNameRequired');
    }
    
    if (!formData.last_name.trim()) {
      newErrors.last_name = t('client.profile.errors.lastNameRequired');
    }
    
    if (formData.phone_number && formData.phone_number.replace(/\D/g, '').length !== 10) {
      newErrors.phone_number = t('client.profile.errors.invalidPhone');
    }
    
    if (formData.date_of_birth) {
      const birthDate = new Date(formData.date_of_birth);
      const today = new Date();
      if (birthDate > today) {
        newErrors.date_of_birth = t('client.profile.errors.futureDateOfBirth');
      }
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      onUpdate(formData);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-text-primary mb-2">
            {t('client.profile.fields.firstName')}
          </label>
          <Input
            leftIcon={<User className="w-4 h-4" />}
            value={formData.first_name}
            onChange={(e) => setFormData({ ...formData, first_name: e.target.value })}
            placeholder={t('client.profile.placeholders.firstName')}
            error={errors.first_name}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-text-primary mb-2">
            {t('client.profile.fields.lastName')}
          </label>
          <Input
            leftIcon={<User className="w-4 h-4" />}
            value={formData.last_name}
            onChange={(e) => setFormData({ ...formData, last_name: e.target.value })}
            placeholder={t('client.profile.placeholders.lastName')}
            error={errors.last_name}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-text-primary mb-2">
            {t('client.profile.fields.dateOfBirth')}
          </label>
          <Input
            type="date"
            leftIcon={<Calendar className="w-4 h-4" />}
            value={formData.date_of_birth}
            onChange={(e) => setFormData({ ...formData, date_of_birth: e.target.value })}
            max={new Date().toISOString().split('T')[0]}
            error={errors.date_of_birth}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-text-primary mb-2">
            {t('client.profile.fields.phoneNumber')}
          </label>
          <Input
            type="tel"
            leftIcon={<Phone className="w-4 h-4" />}
            value={formData.phone_number}
            onChange={handlePhoneChange}
            placeholder="(*************"
            error={errors.phone_number}
          />
        </div>

        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-text-primary mb-2">
            {t('client.profile.fields.email')}
          </label>
          <div className="flex items-center gap-2">
            <Mail className="w-4 h-4 text-text-secondary" />
            <span className="text-text-secondary">{profile.email}</span>
            <span className="text-xs text-text-muted">
              ({t('client.profile.emailCannotBeChanged')})
            </span>
          </div>
        </div>
      </div>

      <div className="flex justify-end gap-3 pt-4">
        <Button
          type="button"
          variant="ghost"
          onClick={() => setFormData({
            first_name: profile.first_name || '',
            last_name: profile.last_name || '',
            date_of_birth: profile.date_of_birth || '',
            phone_number: profile.phone_number || ''
          })}
          disabled={saving}
        >
          {t('common.cancel')}
        </Button>
        <Button
          type="submit"
          loading={saving}
          disabled={saving}
        >
          {t('common.save')}
        </Button>
      </div>
    </form>
  );
};