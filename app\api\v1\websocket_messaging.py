"""
WebSocket Messaging Endpoints

Real-time messaging endpoints for in-app conversations with WebSocket support.
"""

from fastapi import API<PERSON>out<PERSON>, WebSocket, WebSocketDisconnect, Depends, HTTPException, Query
from fastapi.security import HTT<PERSON><PERSON>earer
from typing import Optional
import jwt
from app.services.websocket_messaging_service import websocket_messaging_service
from app.core.dependencies import get_current_user
from app.core.logging import logger
from app.config.settings import settings

router = APIRouter(prefix="/ws", tags=["WebSocket Messaging"])
security = HTTPBearer()


async def get_websocket_user(websocket: WebSocket, token: Optional[str] = Query(None)):
    """Extract user from WebSocket connection token"""
    if not token:
        await websocket.close(code=4001, reason="Authentication required")
        raise HTTPException(status_code=401, detail="Authentication required")
    
    try:
        # Decode JWT token
        payload = jwt.decode(
            token, 
            settings.SECRET_KEY, 
            algorithms=[settings.ALGORITHM]
        )
        user_id = payload.get("sub")
        if not user_id:
            await websocket.close(code=4001, reason="Invalid token")
            raise HTTPException(status_code=401, detail="Invalid token")
        
        return int(user_id)
    except jwt.ExpiredSignatureError:
        await websocket.close(code=4001, reason="Token expired")
        raise HTTPException(status_code=401, detail="Token expired")
    except jwt.InvalidTokenError:
        await websocket.close(code=4001, reason="Invalid token")
        raise HTTPException(status_code=401, detail="Invalid token")


@router.websocket("/messaging")
async def websocket_messaging_endpoint(
    websocket: WebSocket,
    token: Optional[str] = Query(None)
):
    """
    WebSocket endpoint for real-time messaging
    
    Clients should connect with their JWT token as a query parameter:
    ws://localhost:8000/api/v1/ws/messaging?token=your_jwt_token
    """
    try:
        # Authenticate user
        user_id = await get_websocket_user(websocket, token)
        
        # Handle the connection
        await websocket_messaging_service.handle_websocket_connection(websocket, user_id)
        
    except WebSocketDisconnect:
        logger.info(f"WebSocket client disconnected")
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        try:
            await websocket.close(code=4000, reason="Internal server error")
        except:
            pass


@router.get("/status")
async def get_messaging_status(
    current_user = Depends(get_current_user)
):
    """Get WebSocket messaging status and statistics"""
    try:
        online_users = websocket_messaging_service.get_online_users()
        user_online = websocket_messaging_service.is_user_online(current_user.user_id)
        
        return {
            "user_online": user_online,
            "total_online_users": len(online_users),
            "service_status": "active"
        }
        
    except Exception as e:
        logger.error(f"Error getting messaging status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get messaging status")


@router.get("/conversations/{conversation_id}/participants/online")
async def get_online_conversation_participants(
    conversation_id: int,
    current_user = Depends(get_current_user)
):
    """Get online participants in a specific conversation"""
    try:
        # Get all participants in the conversation
        all_participants = websocket_messaging_service.get_conversation_participants(conversation_id)
        
        # Filter for online participants
        online_participants = [
            user_id for user_id in all_participants
            if websocket_messaging_service.is_user_online(user_id)
        ]
        
        return {
            "conversation_id": conversation_id,
            "online_participants": online_participants,
            "total_online": len(online_participants),
            "total_participants": len(all_participants)
        }
        
    except Exception as e:
        logger.error(f"Error getting online participants: {e}")
        raise HTTPException(status_code=500, detail="Failed to get online participants")


@router.post("/broadcast/system")
async def broadcast_system_message(
    message: str,
    conversation_id: Optional[int] = None,
    current_user = Depends(get_current_user)
):
    """Broadcast a system message (manager only)"""
    try:
        # Check if user is manager
        if "manager" not in current_user.roles:
            raise HTTPException(status_code=403, detail="Only managers can broadcast system messages")
        
        await websocket_messaging_service.broadcast_system_message(message, conversation_id)
        
        return {
            "success": True,
            "message": "System message broadcasted successfully",
            "conversation_id": conversation_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error broadcasting system message: {e}")
        raise HTTPException(status_code=500, detail="Failed to broadcast system message")


@router.post("/notify/{user_id}")
async def send_direct_notification(
    user_id: int,
    notification: dict,
    current_user = Depends(get_current_user)
):
    """Send a direct notification to a specific user (manager/agent only)"""
    try:
        # Check permissions
        if "manager" not in current_user.roles and "agent" not in current_user.roles:
            raise HTTPException(status_code=403, detail="Insufficient permissions")
        
        await websocket_messaging_service.send_notification(user_id, notification)
        
        return {
            "success": True,
            "message": f"Notification sent to user {user_id}",
            "user_online": websocket_messaging_service.is_user_online(user_id)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending direct notification: {e}")
        raise HTTPException(status_code=500, detail="Failed to send notification")