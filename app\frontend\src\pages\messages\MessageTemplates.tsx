import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Plus, Edit2, Trash2, Copy, Search, Filter,
  FileText, Globe, Hash, Clock, CheckCircle
} from 'lucide-react';

interface Template {
  id: string;
  name: string;
  category: 'appointment' | 'billing' | 'general' | 'onboarding' | 'reminder';
  language: 'en' | 'fr' | 'both';
  content: {
    en?: string;
    fr?: string;
  };
  variables: string[];
  usageCount: number;
  lastUsed?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Mock data
const mockTemplates: Template[] = [
  {
    id: '1',
    name: 'Appointment Confirmation',
    category: 'appointment',
    language: 'both',
    content: {
      en: 'Hi {{clientName}}, this is to confirm your tutoring session with {{tutorName}} on {{date}} at {{time}}. Location: {{location}}. Please reply YES to confirm.',
      fr: 'Bonjour {{clientName}}, ceci est pour confirmer votre séance de tutorat avec {{tutorName}} le {{date}} à {{time}}. Lieu: {{location}}. Veuillez répondre OUI pour confirmer.',
    },
    variables: ['clientName', 'tutorName', 'date', 'time', 'location'],
    usageCount: 245,
    lastUsed: new Date(Date.now() - 1000 * 60 * 30),
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-05-20'),
  },
  {
    id: '2',
    name: 'Payment Reminder',
    category: 'billing',
    language: 'en',
    content: {
      en: 'Hello {{clientName}}, this is a friendly reminder that your invoice #{{invoiceNumber}} for ${{amount}} is due on {{dueDate}}. Please ensure timely payment to avoid service interruption.',
    },
    variables: ['clientName', 'invoiceNumber', 'amount', 'dueDate'],
    usageCount: 89,
    lastUsed: new Date(Date.now() - 1000 * 60 * 60 * 24),
    createdAt: new Date('2024-02-01'),
    updatedAt: new Date('2024-03-15'),
  },
  {
    id: '3',
    name: 'Welcome Message',
    category: 'onboarding',
    language: 'both',
    content: {
      en: 'Welcome to TutorAide, {{userName}}! We\'re excited to have you join our community. Your account is now active. Download our app to get started: {{appLink}}',
      fr: 'Bienvenue chez TutorAide, {{userName}}! Nous sommes ravis de vous accueillir dans notre communauté. Votre compte est maintenant actif. Téléchargez notre application: {{appLink}}',
    },
    variables: ['userName', 'appLink'],
    usageCount: 156,
    lastUsed: new Date(Date.now() - 1000 * 60 * 60 * 2),
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-10'),
  },
];

const MessageTemplates: React.FC = () => {
  const { t } = useTranslation();
  const [templates, setTemplates] = useState<Template[]>(mockTemplates);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [filterLanguage, setFilterLanguage] = useState<string>('all');
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.content.en?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.content.fr?.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = filterCategory === 'all' || template.category === filterCategory;
    const matchesLanguage = filterLanguage === 'all' || template.language === filterLanguage || 
                           (template.language === 'both' && (filterLanguage === 'en' || filterLanguage === 'fr'));
    return matchesSearch && matchesCategory && matchesLanguage;
  });

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'appointment': return 'bg-accent-red text-white';
      case 'billing': return 'bg-accent-green text-white';
      case 'general': return 'bg-primary-400 text-white';
      case 'onboarding': return 'bg-purple-500 text-white';
      case 'reminder': return 'bg-accent-orange text-white';
      default: return 'bg-primary-300 text-text-primary';
    }
  };

  const handleDuplicate = (template: Template) => {
    const newTemplate: Template = {
      ...template,
      id: Date.now().toString(),
      name: `${template.name} (Copy)`,
      usageCount: 0,
      lastUsed: undefined,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    setTemplates([newTemplate, ...templates]);
  };

  const handleDelete = (templateId: string) => {
    if (window.confirm('Are you sure you want to delete this template?')) {
      setTemplates(templates.filter(t => t.id !== templateId));
      if (selectedTemplate?.id === templateId) {
        setSelectedTemplate(null);
      }
    }
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-text-primary">Message Templates</h1>
            <p className="text-text-secondary mt-1">Create and manage reusable message templates</p>
          </div>
          <button
            onClick={() => setIsEditing(true)}
            className="flex items-center gap-2 px-4 py-2 bg-accent-red text-white rounded-soft hover:bg-accent-red-dark transition-colors"
          >
            <Plus className="w-4 h-4" />
            New Template
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-large shadow-soft p-4 mb-6">
        <div className="flex flex-wrap items-center gap-4">
          <div className="flex-1 min-w-[200px]">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-text-secondary" />
              <input
                type="text"
                placeholder="Search templates..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-background-secondary rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
              />
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4 text-text-secondary" />
            <select
              value={filterCategory}
              onChange={(e) => setFilterCategory(e.target.value)}
              className="px-3 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
            >
              <option value="all">All Categories</option>
              <option value="appointment">Appointment</option>
              <option value="billing">Billing</option>
              <option value="general">General</option>
              <option value="onboarding">Onboarding</option>
              <option value="reminder">Reminder</option>
            </select>
            <select
              value={filterLanguage}
              onChange={(e) => setFilterLanguage(e.target.value)}
              className="px-3 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
            >
              <option value="all">All Languages</option>
              <option value="en">English</option>
              <option value="fr">French</option>
              <option value="both">Bilingual</option>
            </select>
          </div>
        </div>
      </div>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {filteredTemplates.map(template => (
          <div
            key={template.id}
            className="bg-white rounded-large shadow-soft p-6 hover:shadow-medium transition-shadow cursor-pointer"
            onClick={() => setSelectedTemplate(template)}
          >
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <h3 className="font-semibold text-text-primary mb-2">{template.name}</h3>
                <div className="flex flex-wrap items-center gap-2">
                  <span className={`px-2 py-1 rounded-full text-xs ${getCategoryColor(template.category)}`}>
                    {template.category}
                  </span>
                  <span className="flex items-center gap-1 text-xs text-text-secondary">
                    <Globe className="w-3 h-3" />
                    {template.language === 'both' ? 'EN/FR' : template.language.toUpperCase()}
                  </span>
                  <span className="flex items-center gap-1 text-xs text-text-secondary">
                    <Hash className="w-3 h-3" />
                    {template.usageCount} uses
                  </span>
                </div>
              </div>
              <div className="flex items-center gap-1 ml-4">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedTemplate(template);
                    setIsEditing(true);
                  }}
                  className="p-1.5 hover:bg-background-secondary rounded-medium transition-colors"
                >
                  <Edit2 className="w-4 h-4 text-text-secondary" />
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDuplicate(template);
                  }}
                  className="p-1.5 hover:bg-background-secondary rounded-medium transition-colors"
                >
                  <Copy className="w-4 h-4 text-text-secondary" />
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDelete(template.id);
                  }}
                  className="p-1.5 hover:bg-background-secondary rounded-medium transition-colors"
                >
                  <Trash2 className="w-4 h-4 text-accent-red" />
                </button>
              </div>
            </div>

            <div className="space-y-3">
              {template.content.en && (
                <div>
                  <p className="text-xs font-medium text-text-secondary mb-1">English</p>
                  <p className="text-sm text-text-primary line-clamp-2">{template.content.en}</p>
                </div>
              )}
              {template.content.fr && (
                <div>
                  <p className="text-xs font-medium text-text-secondary mb-1">French</p>
                  <p className="text-sm text-text-primary line-clamp-2">{template.content.fr}</p>
                </div>
              )}
            </div>

            {template.variables.length > 0 && (
              <div className="mt-4">
                <p className="text-xs font-medium text-text-secondary mb-1">Variables</p>
                <div className="flex flex-wrap gap-1">
                  {template.variables.map(variable => (
                    <span
                      key={variable}
                      className="px-2 py-0.5 bg-primary-100 text-text-primary rounded-full text-xs"
                    >
                      {`{{${variable}}}`}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {template.lastUsed && (
              <div className="mt-4 flex items-center gap-1 text-xs text-text-secondary">
                <Clock className="w-3 h-3" />
                Last used {new Date(template.lastUsed).toLocaleDateString()}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Empty State */}
      {filteredTemplates.length === 0 && (
        <div className="text-center py-12">
          <FileText className="w-12 h-12 text-text-secondary mx-auto mb-3" />
          <p className="text-text-secondary">No templates found</p>
          <button
            onClick={() => setIsEditing(true)}
            className="mt-4 text-accent-red hover:underline"
          >
            Create your first template
          </button>
        </div>
      )}

      {/* Template Preview Modal */}
      {selectedTemplate && !isEditing && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-large shadow-soft max-w-2xl w-full max-h-[80vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-start justify-between mb-4">
                <h2 className="text-xl font-semibold text-text-primary">{selectedTemplate.name}</h2>
                <button
                  onClick={() => setSelectedTemplate(null)}
                  className="text-text-secondary hover:text-text-primary"
                >
                  ×
                </button>
              </div>

              <div className="space-y-4">
                {selectedTemplate.content.en && (
                  <div className="p-4 bg-background-secondary rounded-medium">
                    <p className="text-sm font-medium text-text-secondary mb-2">English Version</p>
                    <p className="text-text-primary whitespace-pre-wrap">{selectedTemplate.content.en}</p>
                  </div>
                )}
                {selectedTemplate.content.fr && (
                  <div className="p-4 bg-background-secondary rounded-medium">
                    <p className="text-sm font-medium text-text-secondary mb-2">French Version</p>
                    <p className="text-text-primary whitespace-pre-wrap">{selectedTemplate.content.fr}</p>
                  </div>
                )}

                <div className="flex items-center justify-between pt-4">
                  <div className="text-sm text-text-secondary">
                    <p>Created: {selectedTemplate.createdAt.toLocaleDateString()}</p>
                    <p>Updated: {selectedTemplate.updatedAt.toLocaleDateString()}</p>
                    <p>Used: {selectedTemplate.usageCount} times</p>
                  </div>
                  <div className="flex items-center gap-3">
                    <button
                      onClick={() => {
                        navigator.clipboard.writeText(
                          selectedTemplate.content.en || selectedTemplate.content.fr || ''
                        );
                      }}
                      className="flex items-center gap-2 px-4 py-2 border border-primary-300 rounded-soft hover:bg-background-secondary"
                    >
                      <Copy className="w-4 h-4" />
                      Copy
                    </button>
                    <button
                      onClick={() => setIsEditing(true)}
                      className="flex items-center gap-2 px-4 py-2 bg-accent-red text-white rounded-soft hover:bg-accent-red-dark"
                    >
                      <Edit2 className="w-4 h-4" />
                      Edit
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MessageTemplates;