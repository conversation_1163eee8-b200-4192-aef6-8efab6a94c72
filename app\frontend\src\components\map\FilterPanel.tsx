import React from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';

interface FilterPanelProps {
  filters: {
    subjectAreas: string[];
    serviceTypes: string[];
    maxDistance: number;
    minRating: number;
    maxHourlyRate: number;
    availability: 'all' | 'available' | 'limited';
  };
  onFilterChange: (filters: any) => void;
  tutorCount: number;
}

const SUBJECT_AREAS = [
  { value: 'mathematics', label: 'Mathematics' },
  { value: 'science', label: 'Science' },
  { value: 'physics', label: 'Physics' },
  { value: 'chemistry', label: 'Chemistry' },
  { value: 'biology', label: 'Biology' },
  { value: 'french', label: 'French' },
  { value: 'english', label: 'English' },
  { value: 'history', label: 'History' },
  { value: 'economics', label: 'Economics' },
  { value: 'computer_science', label: 'Computer Science' }
];

const SERVICE_TYPES = [
  { value: 'online', label: 'Online Sessions' },
  { value: 'in_person', label: 'In-Person Sessions' },
  { value: 'library', label: 'Library Sessions' },
  { value: 'hybrid', label: 'Hybrid Sessions' }
];

const AVAILABILITY_OPTIONS = [
  { value: 'all', label: 'All Tutors' },
  { value: 'available', label: 'Available Now' },
  { value: 'limited', label: 'Limited Availability' }
];

export const FilterPanel: React.FC<FilterPanelProps> = ({
  filters,
  onFilterChange,
  tutorCount
}) => {
  const [expandedSections, setExpandedSections] = React.useState({
    subjects: true,
    serviceType: true,
    distance: true,
    rating: false,
    price: false,
    availability: false
  });

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section as keyof typeof prev]
    }));
  };

  const handleSubjectChange = (subject: string, checked: boolean) => {
    const newSubjects = checked
      ? [...filters.subjectAreas, subject]
      : filters.subjectAreas.filter(s => s !== subject);
    
    onFilterChange({ subjectAreas: newSubjects });
  };

  const handleServiceTypeChange = (serviceType: string, checked: boolean) => {
    const newTypes = checked
      ? [...filters.serviceTypes, serviceType]
      : filters.serviceTypes.filter(t => t !== serviceType);
    
    onFilterChange({ serviceTypes: newTypes });
  };

  const clearAllFilters = () => {
    onFilterChange({
      subjectAreas: [],
      serviceTypes: ['online', 'in_person'],
      maxDistance: 15,
      minRating: 0,
      maxHourlyRate: 100,
      availability: 'all'
    });
  };

  const hasActiveFilters = (
    filters.subjectAreas.length > 0 ||
    filters.minRating > 0 ||
    filters.maxHourlyRate < 100 ||
    filters.availability !== 'all' ||
    filters.maxDistance < 15 ||
    filters.serviceTypes.length < 4
  );

  const FilterSection: React.FC<{
    title: string;
    sectionKey: string;
    children: React.ReactNode;
  }> = ({ title, sectionKey, children }) => {
    const isExpanded = expandedSections[sectionKey as keyof typeof expandedSections];
    
    return (
      <div className="border-b border-gray-200 last:border-b-0">
        <button
          onClick={() => toggleSection(sectionKey)}
          className="w-full flex items-center justify-between p-3 text-left hover:bg-gray-50 transition-colors"
        >
          <span className="font-medium text-gray-900">{title}</span>
          {isExpanded ? (
            <ChevronUp className="w-4 h-4 text-gray-500" />
          ) : (
            <ChevronDown className="w-4 h-4 text-gray-500" />
          )}
        </button>
        {isExpanded && (
          <div className="px-3 pb-3">
            {children}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-sm max-w-md">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div>
          <h3 className="font-medium text-gray-900">Filters</h3>
          <p className="text-sm text-gray-500">{tutorCount} tutors found</p>
        </div>
        {hasActiveFilters && (
          <button
            onClick={clearAllFilters}
            className="text-sm text-accent-red hover:text-red-700 font-medium"
          >
            Clear All
          </button>
        )}
      </div>

      {/* Quick Filters */}
      <div className="p-3 bg-gray-50 border-b border-gray-200">
        <h4 className="text-sm font-medium text-gray-900 mb-2">Quick Filters</h4>
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => onFilterChange({ minRating: 4.5, maxDistance: 10 })}
            className="px-3 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full hover:bg-yellow-200 transition-colors"
          >
            ⭐ Top Rated Nearby
          </button>
          <button
            onClick={() => onFilterChange({ availability: 'available', maxDistance: 5 })}
            className="px-3 py-1 text-xs bg-green-100 text-green-800 rounded-full hover:bg-green-200 transition-colors"
          >
            🟢 Available Now
          </button>
          <button
            onClick={() => onFilterChange({ maxHourlyRate: 50, maxDistance: 15 })}
            className="px-3 py-1 text-xs bg-red-100 text-accent-red rounded-full hover:bg-red-200 transition-colors"
          >
            💰 Budget Friendly
          </button>
          <button
            onClick={() => onFilterChange({ serviceTypes: ['online'] })}
            className="px-3 py-1 text-xs bg-purple-100 text-purple-800 rounded-full hover:bg-purple-200 transition-colors"
          >
            💻 Online Only
          </button>
        </div>
      </div>

      {/* Filter Sections */}
      <div className="divide-y divide-gray-200">
        {/* Subject Areas */}
        <FilterSection title="Subject Areas" sectionKey="subjects">
          <div className="space-y-2">
            {SUBJECT_AREAS.map((subject) => (
              <label
                key={subject.value}
                className="flex items-center space-x-2 cursor-pointer"
              >
                <input
                  type="checkbox"
                  checked={filters.subjectAreas.includes(subject.value)}
                  onChange={(e) => handleSubjectChange(subject.value, e.target.checked)}
                  className="w-4 h-4 text-accent-red border-gray-300 rounded focus:ring-accent-red accent-accent-red"
                />
                <span className="text-sm text-gray-700">{subject.label}</span>
              </label>
            ))}
          </div>
        </FilterSection>

        {/* Service Types */}
        <FilterSection title="Service Types" sectionKey="serviceType">
          <div className="space-y-2">
            {SERVICE_TYPES.map((type) => (
              <label
                key={type.value}
                className="flex items-center space-x-2 cursor-pointer"
              >
                <input
                  type="checkbox"
                  checked={filters.serviceTypes.includes(type.value)}
                  onChange={(e) => handleServiceTypeChange(type.value, e.target.checked)}
                  className="w-4 h-4 text-accent-red border-gray-300 rounded focus:ring-accent-red accent-accent-red"
                />
                <span className="text-sm text-gray-700">{type.label}</span>
              </label>
            ))}
          </div>
        </FilterSection>

        {/* Distance */}
        <FilterSection title="Distance" sectionKey="distance">
          <div className="space-y-3">
            <div>
              <label className="block text-sm text-gray-700 mb-1">
                Maximum Distance: {filters.maxDistance} km
              </label>
              <input
                type="range"
                min="1"
                max="50"
                value={filters.maxDistance}
                onChange={(e) => onFilterChange({ maxDistance: parseInt(e.target.value) })}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>1 km</span>
                <span>50 km</span>
              </div>
            </div>
          </div>
        </FilterSection>

        {/* Rating */}
        <FilterSection title="Minimum Rating" sectionKey="rating">
          <div className="space-y-3">
            <div>
              <label className="block text-sm text-gray-700 mb-2">
                Minimum Rating: {filters.minRating > 0 ? `${filters.minRating}/5` : 'Any'}
              </label>
              <div className="flex space-x-2">
                {[0, 3, 3.5, 4, 4.5, 5].map((rating) => (
                  <button
                    key={rating}
                    onClick={() => onFilterChange({ minRating: rating })}
                    className={`px-2 py-1 text-xs rounded-md border transition-colors ${
                      filters.minRating === rating
                        ? 'bg-red-100 border-accent-red text-accent-red'
                        : 'bg-white border-gray-300 text-gray-600 hover:bg-gray-50'
                    }`}
                  >
                    {rating === 0 ? 'Any' : `${rating}+`}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </FilterSection>

        {/* Price Range */}
        <FilterSection title="Hourly Rate" sectionKey="price">
          <div className="space-y-3">
            <div>
              <label className="block text-sm text-gray-700 mb-1">
                Maximum Rate: ${filters.maxHourlyRate}/hour
              </label>
              <input
                type="range"
                min="20"
                max="150"
                step="5"
                value={filters.maxHourlyRate}
                onChange={(e) => onFilterChange({ maxHourlyRate: parseInt(e.target.value) })}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>$20</span>
                <span>$150+</span>
              </div>
            </div>
          </div>
        </FilterSection>

        {/* Availability */}
        <FilterSection title="Availability" sectionKey="availability">
          <div className="space-y-2">
            {AVAILABILITY_OPTIONS.map((option) => (
              <label
                key={option.value}
                className="flex items-center space-x-2 cursor-pointer"
              >
                <input
                  type="radio"
                  name="availability"
                  value={option.value}
                  checked={filters.availability === option.value}
                  onChange={(e) => onFilterChange({ availability: e.target.value })}
                  className="w-4 h-4 text-accent-red border-gray-300 focus:ring-accent-red accent-accent-red"
                />
                <span className="text-sm text-gray-700">{option.label}</span>
              </label>
            ))}
          </div>
        </FilterSection>
      </div>

      {/* Active Filters Summary */}
      {hasActiveFilters && (
        <div className="p-3 bg-gray-50 border-t border-gray-200">
          <div className="text-xs text-gray-600 mb-2">Active Filters:</div>
          <div className="flex flex-wrap gap-1">
            {filters.subjectAreas.map((subject) => (
              <span
                key={subject}
                className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-red-100 text-accent-red"
              >
                {SUBJECT_AREAS.find(s => s.value === subject)?.label}
                <button
                  onClick={() => handleSubjectChange(subject, false)}
                  className="ml-1 hover:text-red-900"
                >
                  ×
                </button>
              </span>
            ))}
            {filters.minRating > 0 && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-700">
                {filters.minRating}+ rating
                <button
                  onClick={() => onFilterChange({ minRating: 0 })}
                  className="ml-1 hover:text-yellow-900"
                >
                  ×
                </button>
              </span>
            )}
            {filters.maxHourlyRate < 100 && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-700">
                Under ${filters.maxHourlyRate}/hr
                <button
                  onClick={() => onFilterChange({ maxHourlyRate: 100 })}
                  className="ml-1 hover:text-green-900"
                >
                  ×
                </button>
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};