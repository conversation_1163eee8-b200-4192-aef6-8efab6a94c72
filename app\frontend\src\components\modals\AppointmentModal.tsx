import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  X, Save, Calendar, Clock, MapPin, Users, User, 
  Book, DollarSign, AlertCircle, RefreshCw, Plus, Trash2 
} from 'lucide-react';
import { Modal } from '../common/Modal';
import { Input } from '../common/Input';
import { Select } from '../common/Select';
import { Textarea } from '../common/Textarea';
import Button from '../common/Button';
import { Badge } from '../common/Badge';
import { SearchableSelect } from '../common/SearchableSelect';
import { DatePicker } from '../common/DatePicker';
import { TimePicker } from '../common/TimePicker';
import { useApi } from '../../hooks/useApi';
import toast from 'react-hot-toast';
import { appointmentService, Appointment, CreateAppointmentRequest, UpdateAppointmentRequest, LocationDetails, ConflictResult } from '../../services/appointmentService';
import { tutorApi } from '../../services/tutorApi';
import { clientApi } from '../../services/clientApi';
import { serviceApi } from '../../services/serviceApi';
import { AppointmentStatus, LocationType } from '../../types/appointment';
import { format, addWeeks, addMonths, parse, isBefore, isAfter } from 'date-fns';

interface AppointmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  appointment?: Appointment | null;
  onSuccess: () => void;
  defaultDate?: Date;
  defaultTutorId?: number;
  defaultClientId?: number;
}

interface Participant {
  client_id: number;
  client_name: string;
  dependant_id?: number;
  dependant_name?: string;
}

export const AppointmentModal: React.FC<AppointmentModalProps> = ({
  isOpen,
  onClose,
  appointment: existingAppointment,
  onSuccess,
  defaultDate,
  defaultTutorId,
  defaultClientId
}) => {
  const { t } = useTranslation();
  const { post, put, loading } = useApi();
  const isEdit = !!existingAppointment;

  // Form state
  const [formData, setFormData] = useState({
    tutor_id: defaultTutorId || 0,
    client_id: defaultClientId || 0,
    dependant_id: null as number | null,
    scheduled_date: defaultDate ? format(defaultDate, 'yyyy-MM-dd') : format(new Date(), 'yyyy-MM-dd'),
    start_time: '09:00',
    end_time: '10:00',
    subject_area: '',
    service_catalog_id: 0,
    package_id: null as number | null,
    session_type: 'individual',
    location_type: LocationType.ONLINE,
    location_details: {} as LocationDetails,
    notes: '',
    max_participants: 1,
    participants: [] as Participant[],
    // Recurring fields
    is_recurring: false,
    frequency_weeks: 1,
    recurring_end_date: '',
    num_occurrences: 12,
    recurring_end_type: 'date' as 'date' | 'occurrences',
    day_of_week: new Date().getDay()
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [tutors, setTutors] = useState<any[]>([]);
  const [clients, setClients] = useState<any[]>([]);
  const [services, setServices] = useState<any[]>([]);
  const [packages, setPackages] = useState<any[]>([]);
  const [dependants, setDependants] = useState<any[]>([]);
  const [tutorServices, setTutorServices] = useState<any[]>([]);
  const [hourlyRate, setHourlyRate] = useState<number>(0);
  const [conflictResult, setConflictResult] = useState<ConflictResult | null>(null);
  const [checkingConflict, setCheckingConflict] = useState(false);

  // Load initial data
  useEffect(() => {
    if (isOpen) {
      loadTutors();
      loadClients();
      loadServices();
      loadPackages();
    }
  }, [isOpen]);

  // Initialize form with existing appointment data
  useEffect(() => {
    if (existingAppointment && isOpen) {
      const locationDetails = existingAppointment.location_details || {};
      setFormData({
        tutor_id: existingAppointment.tutor_id,
        client_id: existingAppointment.client_id,
        dependant_id: existingAppointment.dependant_id || null,
        scheduled_date: existingAppointment.scheduled_date,
        start_time: existingAppointment.start_time,
        end_time: existingAppointment.end_time,
        subject_area: existingAppointment.subject_area,
        service_catalog_id: 0, // Need to fetch from backend
        package_id: null,
        session_type: existingAppointment.session_type,
        location_type: existingAppointment.location_type,
        location_details: locationDetails,
        notes: existingAppointment.notes || '',
        max_participants: existingAppointment.max_participants || 1,
        participants: [],
        is_recurring: false,
        frequency_weeks: 1,
        recurring_end_date: '',
        num_occurrences: 12,
        recurring_end_type: 'date',
        day_of_week: new Date(existingAppointment.scheduled_date).getDay()
      });
      setHourlyRate(existingAppointment.hourly_rate || 0);
    }
    setErrors({});
  }, [existingAppointment, isOpen]);

  // Load dependants when client changes
  useEffect(() => {
    if (formData.client_id > 0) {
      loadDependants(formData.client_id);
    }
  }, [formData.client_id]);

  // Load tutor services when tutor changes
  useEffect(() => {
    if (formData.tutor_id > 0) {
      loadTutorServices(formData.tutor_id);
    }
  }, [formData.tutor_id]);

  // Check conflicts when date/time/tutor changes
  useEffect(() => {
    if (formData.tutor_id > 0 && formData.scheduled_date && formData.start_time && formData.end_time) {
      checkConflicts();
    }
  }, [formData.tutor_id, formData.scheduled_date, formData.start_time, formData.end_time]);

  const loadTutors = async () => {
    try {
      const data = await tutorApi.getAllTutors({ is_active: true });
      setTutors(data.items || []);
    } catch (error) {
      console.error('Error loading tutors:', error);
    }
  };

  const loadClients = async () => {
    try {
      const data = await clientApi.getClients({ page_size: 100 });
      setClients(data.items || []);
    } catch (error) {
      console.error('Error loading clients:', error);
    }
  };

  const loadServices = async () => {
    try {
      const data = await serviceApi.getServicePresets({ is_active_only: true });
      setServices(data || []);
    } catch (error) {
      console.error('Error loading services:', error);
    }
  };

  const loadPackages = async () => {
    try {
      const data = await serviceApi.getServicePackages();
      setPackages(data || []);
    } catch (error) {
      console.error('Error loading packages:', error);
    }
  };

  const loadDependants = async (clientId: number) => {
    try {
      const data = await clientApi.getDependants(clientId);
      setDependants(data || []);
    } catch (error) {
      console.error('Error loading dependants:', error);
      setDependants([]);
    }
  };

  const loadTutorServices = async (tutorId: number) => {
    try {
      // This would need a backend endpoint to get tutor's service rates
      // For now, filtering services based on some logic
      setTutorServices(services);
      
      // Get tutor's hourly rate for the selected service
      const tutor = tutors.find(t => t.tutor_id === tutorId);
      if (tutor) {
        setHourlyRate(tutor.hourly_rate || 50);
      }
    } catch (error) {
      console.error('Error loading tutor services:', error);
    }
  };

  const checkConflicts = async () => {
    setCheckingConflict(true);
    try {
      const result = await appointmentService.checkConflicts({
        tutor_id: formData.tutor_id,
        proposed_date: formData.scheduled_date,
        proposed_start: formData.start_time,
        proposed_end: formData.end_time,
        exclude_appointment_id: existingAppointment?.appointment_id
      });
      setConflictResult(result);
    } catch (error) {
      console.error('Error checking conflicts:', error);
    } finally {
      setCheckingConflict(false);
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.tutor_id) {
      newErrors.tutor_id = 'Tutor is required';
    }
    if (!formData.client_id) {
      newErrors.client_id = 'Client is required';
    }
    if (!formData.subject_area.trim()) {
      newErrors.subject_area = 'Subject area is required';
    }
    if (!formData.scheduled_date) {
      newErrors.scheduled_date = 'Date is required';
    }
    if (!formData.start_time) {
      newErrors.start_time = 'Start time is required';
    }
    if (!formData.end_time) {
      newErrors.end_time = 'End time is required';
    }

    // Validate time range
    const start = parse(formData.start_time, 'HH:mm', new Date());
    const end = parse(formData.end_time, 'HH:mm', new Date());
    if (isAfter(start, end) || format(start, 'HH:mm') === format(end, 'HH:mm')) {
      newErrors.end_time = 'End time must be after start time';
    }

    // Location validation
    if (formData.location_type === LocationType.ONLINE) {
      if (!formData.location_details.platform) {
        newErrors.platform = 'Platform is required for online sessions';
      }
    } else if (formData.location_type !== LocationType.ONLINE) {
      if (!formData.location_details.address) {
        newErrors.address = 'Address is required for in-person sessions';
      }
    }

    // Group session validation
    if (formData.session_type === 'group' && formData.participants.length === 0) {
      newErrors.participants = 'At least one participant is required for group sessions';
    }

    // Recurring validation
    if (formData.is_recurring) {
      if (formData.recurring_end_type === 'date' && !formData.recurring_end_date) {
        newErrors.recurring_end_date = 'End date is required for recurring appointments';
      } else if (formData.recurring_end_type === 'occurrences' && formData.num_occurrences < 1) {
        newErrors.num_occurrences = 'Number of occurrences must be at least 1';
      }
    }

    // Conflict validation
    if (conflictResult?.has_conflict) {
      newErrors.conflict = conflictResult.message || 'Schedule conflict detected';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      const locationDetails: LocationDetails = {
        type: formData.location_type,
        ...(formData.location_type === LocationType.ONLINE
          ? {
              platform: formData.location_details.platform,
              meeting_link: formData.location_details.meeting_link
            }
          : {
              address: formData.location_details.address,
              room_number: formData.location_details.room_number
            })
      };

      if (formData.is_recurring) {
        // Create recurring series
        const recurringData = {
          tutor_id: formData.tutor_id,
          client_id: formData.client_id,
          dependant_id: formData.dependant_id,
          day_of_week: formData.day_of_week,
          start_time: formData.start_time,
          end_time: formData.end_time,
          subject_area: formData.subject_area,
          location_details: locationDetails,
          hourly_rate: hourlyRate,
          start_date: formData.scheduled_date,
          end_date: formData.recurring_end_type === 'date' ? formData.recurring_end_date : undefined,
          frequency_weeks: formData.frequency_weeks
        };

        await appointmentService.createRecurringSeries(recurringData);
        toast.success('Recurring appointment series created successfully');
      } else {
        // Create or update single appointment
        const appointmentData: CreateAppointmentRequest = {
          tutor_id: formData.tutor_id,
          client_id: formData.client_id,
          dependant_id: formData.dependant_id,
          scheduled_date: formData.scheduled_date,
          start_time: formData.start_time,
          end_time: formData.end_time,
          subject_area: formData.subject_area,
          session_type: formData.session_type,
          location_type: formData.location_type,
          location_details: JSON.stringify(locationDetails),
          max_participants: formData.session_type === 'group' ? formData.max_participants : undefined,
          notes: formData.notes.trim() || undefined,
          send_notifications: true,
          require_confirmation: true
        };

        if (isEdit && existingAppointment) {
          const updateData: UpdateAppointmentRequest = {
            scheduled_date: appointmentData.scheduled_date,
            start_time: appointmentData.start_time,
            end_time: appointmentData.end_time,
            location_type: appointmentData.location_type,
            location_details: appointmentData.location_details,
            notes: appointmentData.notes,
            send_notifications: true
          };
          await appointmentService.updateAppointment(existingAppointment.appointment_id, updateData);
          toast.success('Appointment updated successfully');
        } else {
          await appointmentService.createAppointment(appointmentData);
          toast.success('Appointment created successfully');
        }
      }

      onSuccess();
      onClose();
    } catch (error: any) {
      console.error('Error saving appointment:', error);
      if (error.response?.data?.detail) {
        toast.error(error.response.data.detail);
      } else {
        toast.error(`Failed to ${isEdit ? 'update' : 'create'} appointment`);
      }
    }
  };

  const addParticipant = (clientId: number, dependantId?: number) => {
    const client = clients.find(c => c.client_id === clientId);
    const dependant = dependantId ? dependants.find(d => d.dependant_id === dependantId) : null;
    
    const newParticipant: Participant = {
      client_id: clientId,
      client_name: `${client?.first_name} ${client?.last_name}`,
      dependant_id: dependantId,
      dependant_name: dependant ? `${dependant.first_name} ${dependant.last_name}` : undefined
    };

    setFormData({
      ...formData,
      participants: [...formData.participants, newParticipant]
    });
  };

  const removeParticipant = (index: number) => {
    setFormData({
      ...formData,
      participants: formData.participants.filter((_, i) => i !== index)
    });
  };

  const calculateDuration = () => {
    const start = parse(formData.start_time, 'HH:mm', new Date());
    const end = parse(formData.end_time, 'HH:mm', new Date());
    const diffMinutes = (end.getTime() - start.getTime()) / (1000 * 60);
    return diffMinutes > 0 ? diffMinutes : 0;
  };

  const calculateTotalCost = () => {
    const durationHours = calculateDuration() / 60;
    return (hourlyRate * durationHours).toFixed(2);
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={isEdit ? 'Edit Appointment' : 'Create Appointment'}
      size="xl"
    >
      <form onSubmit={(e) => { e.preventDefault(); handleSubmit(); }} className="space-y-6">
        {/* Conflict Warning */}
        {conflictResult?.has_conflict && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-start gap-3">
              <AlertCircle className="w-5 h-5 text-red-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-red-800">Schedule Conflict</p>
                <p className="text-sm text-red-700 mt-1">{conflictResult.message}</p>
                {conflictResult.suggested_times && conflictResult.suggested_times.length > 0 && (
                  <div className="mt-2">
                    <p className="text-sm font-medium text-red-800">Suggested times:</p>
                    <div className="mt-1 space-y-1">
                      {conflictResult.suggested_times.map((time, idx) => (
                        <button
                          key={idx}
                          type="button"
                          onClick={() => {
                            setFormData({
                              ...formData,
                              start_time: time.start_time,
                              end_time: time.end_time
                            });
                          }}
                          className="text-sm text-red-700 hover:text-red-900 underline"
                        >
                          {time.start_time} - {time.end_time}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Basic Information */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
            <User className="w-5 h-5" />
            Basic Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Tutor <span className="text-red-500">*</span>
              </label>
              <SearchableSelect
                value={formData.tutor_id.toString()}
                onChange={(value) => setFormData({ ...formData, tutor_id: parseInt(value) })}
                options={tutors.map(t => ({
                  value: t.tutor_id.toString(),
                  label: `${t.user.first_name} ${t.user.last_name}`
                }))}
                placeholder="Search for a tutor..."
                error={errors.tutor_id}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Client <span className="text-red-500">*</span>
              </label>
              <SearchableSelect
                value={formData.client_id.toString()}
                onChange={(value) => setFormData({ ...formData, client_id: parseInt(value) })}
                options={clients.map(c => ({
                  value: c.client_id.toString(),
                  label: `${c.first_name} ${c.last_name}`
                }))}
                placeholder="Search for a client..."
                error={errors.client_id}
              />
            </div>

            {dependants.length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Dependant (Optional)
                </label>
                <Select
                  value={formData.dependant_id?.toString() || ''}
                  onChange={(e) => setFormData({ ...formData, dependant_id: e.target.value ? parseInt(e.target.value) : null })}
                  options={[
                    { value: '', label: 'Select a dependant...' },
                    ...dependants.map(d => ({
                      value: d.dependant_id.toString(),
                      label: `${d.first_name} ${d.last_name}`
                    }))
                  ]}
                />
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Subject Area <span className="text-red-500">*</span>
              </label>
              <Select
                value={formData.subject_area}
                onChange={(e) => setFormData({ ...formData, subject_area: e.target.value })}
                options={[
                  { value: '', label: 'Select a subject...' },
                  { value: 'mathematics', label: 'Mathematics' },
                  { value: 'science', label: 'Science' },
                  { value: 'french', label: 'French' },
                  { value: 'english', label: 'English' },
                  { value: 'physics', label: 'Physics' },
                  { value: 'chemistry', label: 'Chemistry' },
                  { value: 'biology', label: 'Biology' },
                  { value: 'history', label: 'History' },
                  { value: 'geography', label: 'Geography' },
                  { value: 'computer_science', label: 'Computer Science' },
                  { value: 'other', label: 'Other' }
                ]}
                error={errors.subject_area}
              />
            </div>
          </div>
        </div>

        {/* Schedule Information */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
            <Calendar className="w-5 h-5" />
            Schedule Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date <span className="text-red-500">*</span>
              </label>
              <DatePicker
                value={formData.scheduled_date}
                onChange={(date) => setFormData({ ...formData, scheduled_date: date })}
                minDate={format(new Date(), 'yyyy-MM-dd')}
                error={errors.scheduled_date}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Start Time <span className="text-red-500">*</span>
              </label>
              <TimePicker
                value={formData.start_time}
                onChange={(time) => setFormData({ ...formData, start_time: time })}
                error={errors.start_time}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                End Time <span className="text-red-500">*</span>
              </label>
              <TimePicker
                value={formData.end_time}
                onChange={(time) => setFormData({ ...formData, end_time: time })}
                error={errors.end_time}
              />
            </div>
          </div>

          {/* Duration and Cost Display */}
          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div>
                  <Clock className="w-5 h-5 text-gray-500" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Duration</p>
                  <p className="font-semibold">{calculateDuration()} minutes</p>
                </div>
              </div>
              <div className="flex items-center gap-4">
                <div>
                  <DollarSign className="w-5 h-5 text-gray-500" />
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-600">Estimated Cost</p>
                  <p className="font-semibold">${calculateTotalCost()} CAD</p>
                  <p className="text-xs text-gray-500">${hourlyRate}/hour</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Location Information */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
            <MapPin className="w-5 h-5" />
            Location Information
          </h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Location Type <span className="text-red-500">*</span>
              </label>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
                {Object.values(LocationType).map((type) => (
                  <label key={type} className="flex items-center">
                    <input
                      type="radio"
                      name="location_type"
                      value={type}
                      checked={formData.location_type === type}
                      onChange={(e) => setFormData({ ...formData, location_type: e.target.value as LocationType })}
                      className="mr-2"
                    />
                    <span className="text-sm capitalize">{type.replace('_', ' ')}</span>
                  </label>
                ))}
              </div>
            </div>

            {formData.location_type === LocationType.ONLINE ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Platform <span className="text-red-500">*</span>
                  </label>
                  <Select
                    value={formData.location_details.platform || ''}
                    onChange={(e) => setFormData({
                      ...formData,
                      location_details: { ...formData.location_details, platform: e.target.value }
                    })}
                    options={[
                      { value: '', label: 'Select platform...' },
                      { value: 'zoom', label: 'Zoom' },
                      { value: 'teams', label: 'Microsoft Teams' },
                      { value: 'meet', label: 'Google Meet' },
                      { value: 'skype', label: 'Skype' },
                      { value: 'other', label: 'Other' }
                    ]}
                    error={errors.platform}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Meeting Link (Optional)
                  </label>
                  <Input
                    type="url"
                    value={formData.location_details.meeting_link || ''}
                    onChange={(e) => setFormData({
                      ...formData,
                      location_details: { ...formData.location_details, meeting_link: e.target.value }
                    })}
                    placeholder="https://..."
                  />
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Address <span className="text-red-500">*</span>
                  </label>
                  <Textarea
                    value={formData.location_details.address || ''}
                    onChange={(e) => setFormData({
                      ...formData,
                      location_details: { ...formData.location_details, address: e.target.value }
                    })}
                    placeholder="Enter the full address..."
                    rows={2}
                    error={errors.address}
                  />
                </div>
                {(formData.location_type === 'library' || formData.location_type === 'public_space') && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Room/Area (Optional)
                    </label>
                    <Input
                      value={formData.location_details.room_number || ''}
                      onChange={(e) => setFormData({
                        ...formData,
                        location_details: { ...formData.location_details, room_number: e.target.value }
                      })}
                      placeholder="e.g., Study Room 3, Meeting Area B"
                    />
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Session Type and Participants */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
            <Users className="w-5 h-5" />
            Session Type
          </h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Session Type
              </label>
              <div className="flex gap-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="session_type"
                    value="individual"
                    checked={formData.session_type === 'individual'}
                    onChange={(e) => setFormData({ ...formData, session_type: e.target.value })}
                    className="mr-2"
                  />
                  <span className="text-sm">Individual</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="session_type"
                    value="group"
                    checked={formData.session_type === 'group'}
                    onChange={(e) => setFormData({ ...formData, session_type: e.target.value })}
                    className="mr-2"
                  />
                  <span className="text-sm">Group</span>
                </label>
              </div>
            </div>

            {formData.session_type === 'group' && (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Maximum Participants
                    </label>
                    <Input
                      type="number"
                      value={formData.max_participants}
                      onChange={(e) => setFormData({ ...formData, max_participants: parseInt(e.target.value) || 1 })}
                      min="2"
                      max="20"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Participants {errors.participants && <span className="text-red-500 text-sm ml-2">{errors.participants}</span>}
                  </label>
                  
                  {/* Add Participant Form */}
                  <div className="p-4 bg-gray-50 rounded-lg mb-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <SearchableSelect
                        value=""
                        onChange={(value) => {
                          const clientId = parseInt(value);
                          addParticipant(clientId);
                        }}
                        options={clients.map(c => ({
                          value: c.client_id.toString(),
                          label: `${c.first_name} ${c.last_name}`
                        }))}
                        placeholder="Add participant..."
                      />
                    </div>
                  </div>

                  {/* Participants List */}
                  {formData.participants.length > 0 && (
                    <div className="space-y-2">
                      {formData.participants.map((participant, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-white border rounded-lg">
                          <div>
                            <p className="font-medium">{participant.client_name}</p>
                            {participant.dependant_name && (
                              <p className="text-sm text-gray-500">{participant.dependant_name}</p>
                            )}
                          </div>
                          <button
                            type="button"
                            onClick={() => removeParticipant(index)}
                            className="text-red-600 hover:text-red-800"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </>
            )}
          </div>
        </div>

        {/* Recurring Appointment */}
        {!isEdit && (
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
              <RefreshCw className="w-5 h-5" />
              Recurring Appointment
            </h3>
            
            <div className="space-y-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="is_recurring"
                  checked={formData.is_recurring}
                  onChange={(e) => setFormData({ ...formData, is_recurring: e.target.checked })}
                  className="rounded border-gray-300 text-accent-red focus:ring-accent-red"
                />
                <label htmlFor="is_recurring" className="ml-2 text-sm font-medium text-gray-700">
                  Make this a recurring appointment
                </label>
              </div>

              {formData.is_recurring && (
                <div className="p-4 bg-gray-50 rounded-lg space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Frequency
                      </label>
                      <Select
                        value={formData.frequency_weeks.toString()}
                        onChange={(e) => setFormData({ ...formData, frequency_weeks: parseInt(e.target.value) })}
                        options={[
                          { value: '1', label: 'Weekly' },
                          { value: '2', label: 'Biweekly' },
                          { value: '4', label: 'Monthly' }
                        ]}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        End Recurring
                      </label>
                      <Select
                        value={formData.recurring_end_type}
                        onChange={(e) => setFormData({ ...formData, recurring_end_type: e.target.value as 'date' | 'occurrences' })}
                        options={[
                          { value: 'date', label: 'On specific date' },
                          { value: 'occurrences', label: 'After occurrences' }
                        ]}
                      />
                    </div>
                  </div>

                  {formData.recurring_end_type === 'date' ? (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        End Date <span className="text-red-500">*</span>
                      </label>
                      <DatePicker
                        value={formData.recurring_end_date}
                        onChange={(date) => setFormData({ ...formData, recurring_end_date: date })}
                        minDate={format(addWeeks(new Date(formData.scheduled_date), 1), 'yyyy-MM-dd')}
                        error={errors.recurring_end_date}
                      />
                    </div>
                  ) : (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Number of Occurrences <span className="text-red-500">*</span>
                      </label>
                      <Input
                        type="number"
                        value={formData.num_occurrences}
                        onChange={(e) => setFormData({ ...formData, num_occurrences: parseInt(e.target.value) || 1 })}
                        min="1"
                        max="52"
                        error={errors.num_occurrences}
                      />
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Notes */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Notes (Optional)
          </label>
          <Textarea
            value={formData.notes}
            onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
            placeholder="Add any additional notes or special instructions..."
            rows={3}
          />
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-3 pt-4 border-t">
          <Button
            type="button"
            variant="ghost"
            onClick={onClose}
            disabled={loading}
          >
            <X className="w-4 h-4 mr-2" />
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={loading || checkingConflict}
            className="bg-accent-red text-white hover:bg-accent-red-dark"
          >
            <Save className="w-4 h-4 mr-2" />
            {loading ? 'Saving...' : isEdit ? 'Update Appointment' : 'Create Appointment'}
          </Button>
        </div>
      </form>
    </Modal>
  );
};