# Authorization Guide for TutorAide

This guide explains the role-based authorization system implemented in TutorAide.

## Overview

TutorAide uses a comprehensive role-based access control (RBAC) system with:
- Role-based decorators for endpoint protection
- Resource ownership verification
- Dependency injection for clean authorization
- Middleware for global authorization checks

## User Roles

TutorAide supports three main roles:
- **MANAGER**: Full administrative access
- **TUTOR**: Access to tutoring-related features
- **CLIENT**: Access to client features and personal data

Users can have multiple roles and switch between them.

## Authorization Decorators

### 1. Single Role Requirement

Use `@require_role` to restrict access to users with a specific role:

```python
from app.core.authorization import require_role
from app.models.user_models import UserRoleType

@router.get("/admin/dashboard")
@require_role(UserRoleType.MANAGER)
async def admin_dashboard(
    current_user: User = Depends(get_current_active_user)
):
    return {"message": "Admin dashboard"}
```

### 2. Any Role Requirement

Use `@require_any_role` when users need at least one of several roles:

```python
@router.get("/staff/resources")
@require_any_role([UserRoleType.MANAGER, UserRoleType.TUTOR])
async def staff_resources(
    current_user: User = Depends(get_current_active_user)
):
    return {"message": "Staff resources"}
```

### 3. All Roles Requirement

Use `@require_all_roles` when users must have all specified roles:

```python
@router.get("/special/feature")
@require_all_roles([UserRoleType.MANAGER, UserRoleType.TUTOR])
async def special_feature(
    current_user: User = Depends(get_current_active_user)
):
    return {"message": "Special feature for manager-tutors"}
```

## Resource Ownership Verification

### Using Decorators

Verify that users can only access their own resources:

```python
@router.put("/appointments/{appointment_id}")
@check_resource_ownership(
    table_name="appointment_sessions",
    resource_id_param="appointment_id",
    owner_field="user_id",
    allow_roles=[UserRoleType.MANAGER]  # Managers can access any resource
)
async def update_appointment(
    appointment_id: int,
    current_user: User = Depends(get_current_active_user),
    db: asyncpg.Connection = Depends(get_database)
):
    return {"message": "Appointment updated"}
```

## Dependency-Based Authorization

### RoleChecker Dependency

Use as a FastAPI dependency for cleaner code:

```python
from app.core.authorization import RoleChecker

@router.get(
    "/tutors/schedule",
    dependencies=[Depends(RoleChecker(UserRoleType.TUTOR))]
)
async def tutor_schedule():
    return {"message": "Tutor schedule"}

# Multiple roles with any match
@router.get(
    "/staff/area",
    dependencies=[Depends(RoleChecker([UserRoleType.MANAGER, UserRoleType.TUTOR]))]
)
async def staff_area():
    return {"message": "Staff area"}

# Require all roles
@router.get(
    "/special/access",
    dependencies=[Depends(RoleChecker(
        [UserRoleType.MANAGER, UserRoleType.TUTOR],
        require_all=True
    ))]
)
async def special_access():
    return {"message": "Special access"}
```

### ResourceOwnershipChecker Dependency

Check resource ownership using dependencies:

```python
from app.core.authorization import ResourceOwnershipChecker

@router.delete(
    "/appointments/{appointment_id}",
    dependencies=[
        Depends(ResourceOwnershipChecker(
            table_name="appointment_sessions",
            resource_id_param="appointment_id",
            owner_field="user_id",
            allow_roles=[UserRoleType.MANAGER]
        ))
    ]
)
async def delete_appointment(appointment_id: int):
    return {"message": "Appointment deleted"}
```

## Authorization Middleware

The `AuthorizationMiddleware` automatically:
1. Identifies public vs protected endpoints
2. Validates JWT tokens for protected endpoints
3. Returns 401 for missing/invalid tokens
4. Allows public endpoints through

Public endpoints include:
- Authentication endpoints (`/api/v1/auth/*`)
- Documentation (`/docs`, `/redoc`, `/openapi.json`)
- Root endpoints (`/`, `/api/v1/`)

## Best Practices

### 1. Choose the Right Authorization Method

- **Decorators**: Best for single endpoints with specific requirements
- **Dependencies**: Better for routers with consistent requirements
- **Middleware**: For global authorization policies

### 2. Resource Ownership

Always verify resource ownership for user-specific data:

```python
# Good - verifies ownership
@check_resource_ownership("user_profiles", "profile_id")
async def update_profile(profile_id: int, ...):
    ...

# Bad - no ownership check
async def update_profile(profile_id: int, ...):
    # Anyone could update any profile!
```

### 3. Manager Bypass

Allow managers to bypass ownership checks for administrative tasks:

```python
@check_resource_ownership(
    "billing_invoices",
    "invoice_id",
    allow_roles=[UserRoleType.MANAGER]  # Managers can view all invoices
)
```

### 4. Combine Authorization Checks

Layer multiple authorization requirements:

```python
@router.put(
    "/tutors/{tutor_id}/availability",
    dependencies=[Depends(RoleChecker(UserRoleType.TUTOR))]
)
@check_resource_ownership("tutor_profiles", "tutor_id")
async def update_availability(tutor_id: int, ...):
    # Must be a tutor AND own this profile
```

## Error Responses

Authorization failures return appropriate HTTP status codes:

- **401 Unauthorized**: Missing or invalid authentication
- **403 Forbidden**: Authenticated but lacks required permissions
- **404 Not Found**: Resource doesn't exist (or user can't access it)

Example error response:
```json
{
    "detail": "Insufficient permissions. manager role required."
}
```

## Testing Authorization

### Unit Tests

```python
@pytest.mark.asyncio
async def test_manager_only_endpoint():
    # Mock manager user
    manager = Mock(spec=User)
    manager.roles = [UserRoleType.MANAGER]
    
    # Test authorized access
    response = await manager_endpoint(current_user=manager)
    assert response is not None
    
    # Test unauthorized access
    client = Mock(spec=User)
    client.roles = [UserRoleType.CLIENT]
    
    with pytest.raises(HTTPException) as exc:
        await manager_endpoint(current_user=client)
    assert exc.value.status_code == 403
```

### Integration Tests

```python
async def test_protected_endpoint(client, auth_headers):
    response = await client.get(
        "/api/v1/protected/managers-only",
        headers=auth_headers
    )
    assert response.status_code == 200
```

## Security Considerations

1. **Always verify tokens**: The middleware ensures all protected endpoints verify JWT tokens
2. **Check ownership**: Don't assume users can access all resources of a type
3. **Log authorization failures**: Track unauthorized access attempts
4. **Fail securely**: Return generic errors to avoid information leakage
5. **Manager oversight**: Allow managers to bypass ownership for administration

## Migration Guide

To add authorization to existing endpoints:

### Before:
```python
@router.get("/my-endpoint")
async def my_endpoint(current_user: User = Depends(get_current_active_user)):
    return {"data": "..."}
```

### After (with role requirement):
```python
@router.get("/my-endpoint")
@require_role(UserRoleType.MANAGER)
async def my_endpoint(current_user: User = Depends(get_current_active_user)):
    return {"data": "..."}
```

### After (with ownership check):
```python
@router.get("/my-resource/{resource_id}")
@check_resource_ownership("resources", "resource_id")
async def my_endpoint(
    resource_id: int,
    current_user: User = Depends(get_current_active_user),
    db: asyncpg.Connection = Depends(get_database)
):
    return {"data": "..."}
```