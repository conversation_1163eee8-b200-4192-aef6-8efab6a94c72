import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  <PERSON><PERSON>hart, Bar, LineChart, Line, RadarChart, Radar, PolarGrid, PolarAngleAxis, PolarRadiusAxis,
  XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer
} from 'recharts';
import { TrendingUp, Users, Star, Clock, Award } from 'lucide-react';

// Mock data
const tutorPerformance = [
  { name: '<PERSON>', sessions: 45, rating: 4.8, completionRate: 98, students: 12 },
  { name: '<PERSON>', sessions: 38, rating: 4.9, completionRate: 100, students: 10 },
  { name: '<PERSON>', sessions: 42, rating: 4.7, completionRate: 95, students: 15 },
  { name: '<PERSON>', sessions: 35, rating: 4.9, completionRate: 97, students: 8 },
  { name: '<PERSON>', sessions: 40, rating: 4.6, completionRate: 94, students: 11 },
];

const monthlyTrends = [
  { month: 'Jan', sessions: 145, avgRating: 4.7, newStudents: 23 },
  { month: 'Feb', sessions: 168, avgRating: 4.8, newStudents: 28 },
  { month: 'Mar', sessions: 152, avgRating: 4.7, newStudents: 25 },
  { month: 'Apr', sessions: 189, avgRating: 4.8, newStudents: 32 },
  { month: 'May', sessions: 175, avgRating: 4.9, newStudents: 30 },
  { month: 'Jun', sessions: 198, avgRating: 4.8, newStudents: 35 },
];

const subjectPerformance = [
  { subject: 'Mathematics', value: 85, fullMark: 100 },
  { subject: 'Science', value: 78, fullMark: 100 },
  { subject: 'French', value: 92, fullMark: 100 },
  { subject: 'English', value: 88, fullMark: 100 },
  { subject: 'History', value: 75, fullMark: 100 },
];

const PerformanceReport: React.FC = () => {
  const { t } = useTranslation();
  const [timeRange, setTimeRange] = useState('thisMonth');

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-semibold text-text-primary">
            {t('sidebar.performance')} Report
          </h1>
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
          >
            <option value="thisWeek">This Week</option>
            <option value="thisMonth">This Month</option>
            <option value="thisQuarter">This Quarter</option>
            <option value="thisYear">This Year</option>
          </select>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-large shadow-soft p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-accent-red bg-opacity-10 rounded-medium">
              <Users className="w-6 h-6 text-accent-red" />
            </div>
            <span className="text-sm text-accent-green">+15%</span>
          </div>
          <h3 className="text-2xl font-semibold text-text-primary">45</h3>
          <p className="text-sm text-text-secondary mt-1">Active Tutors</p>
        </div>

        <div className="bg-white rounded-large shadow-soft p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-accent-green bg-opacity-10 rounded-medium">
              <Star className="w-6 h-6 text-accent-green" />
            </div>
            <span className="text-sm text-accent-green">+0.2</span>
          </div>
          <h3 className="text-2xl font-semibold text-text-primary">4.8</h3>
          <p className="text-sm text-text-secondary mt-1">Average Rating</p>
        </div>

        <div className="bg-white rounded-large shadow-soft p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-accent-orange bg-opacity-10 rounded-medium">
              <Clock className="w-6 h-6 text-accent-orange" />
            </div>
            <span className="text-sm text-accent-green">+12%</span>
          </div>
          <h3 className="text-2xl font-semibold text-text-primary">1,027</h3>
          <p className="text-sm text-text-secondary mt-1">Total Sessions</p>
        </div>

        <div className="bg-white rounded-large shadow-soft p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-purple-500 bg-opacity-10 rounded-medium">
              <Award className="w-6 h-6 text-purple-500" />
            </div>
            <span className="text-sm text-accent-green">+3%</span>
          </div>
          <h3 className="text-2xl font-semibold text-text-primary">97%</h3>
          <p className="text-sm text-text-secondary mt-1">Completion Rate</p>
        </div>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Performing Tutors */}
        <div className="bg-white rounded-large shadow-soft p-6">
          <h2 className="text-lg font-semibold text-text-primary mb-4">
            Top Performing Tutors
          </h2>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={tutorPerformance} layout="horizontal">
              <CartesianGrid strokeDasharray="3 3" stroke="#E9ECEF" />
              <XAxis type="number" stroke="#6C757D" />
              <YAxis dataKey="name" type="category" stroke="#6C757D" width={100} />
              <Tooltip />
              <Bar dataKey="sessions" fill="#007AFF" radius={[0, 8, 8, 0]} />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Subject Performance */}
        <div className="bg-white rounded-large shadow-soft p-6">
          <h2 className="text-lg font-semibold text-text-primary mb-4">
            Subject Performance
          </h2>
          <ResponsiveContainer width="100%" height={300}>
            <RadarChart data={subjectPerformance}>
              <PolarGrid stroke="#E9ECEF" />
              <PolarAngleAxis dataKey="subject" stroke="#6C757D" />
              <PolarRadiusAxis angle={90} domain={[0, 100]} stroke="#6C757D" />
              <Radar 
                name="Performance" 
                dataKey="value" 
                stroke="#007AFF" 
                fill="#007AFF" 
                fillOpacity={0.6} 
              />
              <Tooltip />
            </RadarChart>
          </ResponsiveContainer>
        </div>

        {/* Monthly Trends */}
        <div className="bg-white rounded-large shadow-soft p-6 lg:col-span-2">
          <h2 className="text-lg font-semibold text-text-primary mb-4">
            Monthly Performance Trends
          </h2>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={monthlyTrends}>
              <CartesianGrid strokeDasharray="3 3" stroke="#E9ECEF" />
              <XAxis dataKey="month" stroke="#6C757D" />
              <YAxis yAxisId="left" stroke="#6C757D" />
              <YAxis yAxisId="right" orientation="right" stroke="#6C757D" />
              <Tooltip />
              <Legend />
              <Line 
                yAxisId="left"
                type="monotone" 
                dataKey="sessions" 
                stroke="#007AFF" 
                strokeWidth={2}
                name="Sessions"
              />
              <Line 
                yAxisId="left"
                type="monotone" 
                dataKey="newStudents" 
                stroke="#34C759" 
                strokeWidth={2}
                name="New Students"
              />
              <Line 
                yAxisId="right"
                type="monotone" 
                dataKey="avgRating" 
                stroke="#FF9500" 
                strokeWidth={2}
                name="Avg Rating"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Tutor Leaderboard */}
      <div className="mt-8 bg-white rounded-large shadow-soft p-6">
        <h2 className="text-lg font-semibold text-text-primary mb-4">
          Tutor Leaderboard
        </h2>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-primary-200">
                <th className="text-left py-3 px-4 text-sm font-medium text-text-secondary">
                  Rank
                </th>
                <th className="text-left py-3 px-4 text-sm font-medium text-text-secondary">
                  Tutor
                </th>
                <th className="text-center py-3 px-4 text-sm font-medium text-text-secondary">
                  Sessions
                </th>
                <th className="text-center py-3 px-4 text-sm font-medium text-text-secondary">
                  Rating
                </th>
                <th className="text-center py-3 px-4 text-sm font-medium text-text-secondary">
                  Completion
                </th>
                <th className="text-center py-3 px-4 text-sm font-medium text-text-secondary">
                  Students
                </th>
              </tr>
            </thead>
            <tbody>
              {tutorPerformance.map((tutor, index) => (
                <tr key={index} className="border-b border-primary-100 hover:bg-background-secondary">
                  <td className="py-3 px-4 text-sm text-text-primary">
                    {index + 1}
                  </td>
                  <td className="py-3 px-4 text-sm text-text-primary font-medium">
                    {tutor.name}
                  </td>
                  <td className="py-3 px-4 text-sm text-text-primary text-center">
                    {tutor.sessions}
                  </td>
                  <td className="py-3 px-4 text-sm text-center">
                    <div className="flex items-center justify-center gap-1">
                      <Star className="w-4 h-4 text-accent-orange fill-current" />
                      <span>{tutor.rating}</span>
                    </div>
                  </td>
                  <td className="py-3 px-4 text-sm text-text-primary text-center">
                    {tutor.completionRate}%
                  </td>
                  <td className="py-3 px-4 text-sm text-text-primary text-center">
                    {tutor.students}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default PerformanceReport;