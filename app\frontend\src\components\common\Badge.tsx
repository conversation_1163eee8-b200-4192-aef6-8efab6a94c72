import React from 'react';
import { clsx } from 'clsx';

interface BadgeProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  size?: 'sm' | 'md' | 'lg';
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  className?: string;
}

export const Badge: React.FC<BadgeProps> = ({
  children,
  variant = 'secondary',
  size = 'md',
  icon,
  iconPosition = 'left',
  className,
}) => {
  const baseStyles = 'inline-flex items-center font-medium rounded-full transition-colors';
  
  const variants = {
    primary: 'bg-accent-red bg-opacity-10 text-accent-red',
    secondary: 'bg-background-tertiary text-text-secondary',
    success: 'bg-semantic-success bg-opacity-10 text-semantic-success',
    warning: 'bg-semantic-warning bg-opacity-10 text-semantic-warning',
    error: 'bg-semantic-error bg-opacity-10 text-semantic-error',
    info: 'bg-semantic-info bg-opacity-10 text-semantic-info',
  };

  const sizes = {
    sm: 'px-2 py-0.5 text-xs gap-1',
    md: 'px-2.5 py-1 text-sm gap-1.5',
    lg: 'px-3 py-1.5 text-base gap-2',
  };

  const iconSizes = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5',
  };

  return (
    <span
      className={clsx(
        baseStyles,
        variants[variant],
        sizes[size],
        className
      )}
    >
      {icon && iconPosition === 'left' && (
        <span className={iconSizes[size]}>{icon}</span>
      )}
      {children}
      {icon && iconPosition === 'right' && (
        <span className={iconSizes[size]}>{icon}</span>
      )}
    </span>
  );
};

interface StatusBadgeProps {
  status: 'online' | 'offline' | 'busy' | 'away';
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  className?: string;
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  size = 'md',
  showLabel = true,
  className,
}) => {
  const statusConfig = {
    online: {
      color: 'bg-semantic-success',
      label: 'Online',
    },
    offline: {
      color: 'bg-text-muted',
      label: 'Offline',
    },
    busy: {
      color: 'bg-semantic-error',
      label: 'Busy',
    },
    away: {
      color: 'bg-semantic-warning',
      label: 'Away',
    },
  };

  const sizes = {
    sm: 'w-2 h-2',
    md: 'w-2.5 h-2.5',
    lg: 'w-3 h-3',
  };

  const config = statusConfig[status];

  return (
    <div className={clsx('inline-flex items-center gap-2', className)}>
      <span className={clsx('rounded-full', config.color, sizes[size])} />
      {showLabel && (
        <span className="text-sm text-text-secondary">{config.label}</span>
      )}
    </div>
  );
};

interface CountBadgeProps {
  count: number;
  max?: number;
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const CountBadge: React.FC<CountBadgeProps> = ({
  count,
  max = 99,
  variant = 'primary',
  size = 'md',
  className,
}) => {
  const displayCount = count > max ? `${max}+` : count;
  
  const baseStyles = 'inline-flex items-center justify-center font-semibold rounded-full';
  
  const variants = {
    primary: 'bg-accent-red text-white',
    secondary: 'bg-background-tertiary text-text-secondary',
    success: 'bg-semantic-success text-white',
    warning: 'bg-semantic-warning text-white',
    error: 'bg-semantic-error text-white',
  };

  const sizes = {
    sm: 'min-w-[18px] h-[18px] text-xs px-1',
    md: 'min-w-[22px] h-[22px] text-sm px-1.5',
    lg: 'min-w-[28px] h-[28px] text-base px-2',
  };

  return (
    <span
      className={clsx(
        baseStyles,
        variants[variant],
        sizes[size],
        className
      )}
    >
      {displayCount}
    </span>
  );
};

export default Badge;