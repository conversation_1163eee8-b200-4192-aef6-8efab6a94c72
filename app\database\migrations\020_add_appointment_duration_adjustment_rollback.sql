-- Rollback appointment duration adjustment fields
-- Version: 020
-- Description: Rollback script for appointment duration adjustment feature

-- Drop the audit table
DROP TABLE IF EXISTS appointment_duration_adjustments;

-- Drop indexes
DROP INDEX IF EXISTS idx_appointments_duration_adjusted;
DROP INDEX IF EXISTS idx_appointments_actual_duration;

-- Drop constraints
ALTER TABLE appointment_sessions DROP CONSTRAINT IF EXISTS check_actual_times;
ALTER TABLE appointment_sessions DROP CONSTRAINT IF EXISTS check_actual_duration_consistency;

-- Drop columns from appointment_sessions
ALTER TABLE appointment_sessions
    DROP COLUMN IF EXISTS actual_start_time,
    DROP COLUMN IF EXISTS actual_end_time,
    DROP COLUMN IF EXISTS actual_duration_minutes,
    DROP COLUMN IF EXISTS duration_adjusted,
    DROP COLUMN IF EXISTS duration_adjusted_by,
    DROP COLUMN IF EXISTS duration_adjusted_at,
    DROP COLUMN IF EXISTS duration_adjustment_reason,
    DROP COLUMN IF EXISTS original_duration_minutes,
    DROP COLUMN IF EXISTS duration_difference_minutes;