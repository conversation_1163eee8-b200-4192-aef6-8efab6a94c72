# Password Reset Debugging Guide

## Current Issue
Password reset returns 200 OK but no emails are being sent. The logs show no email sending attempts, suggesting the token creation is returning an empty dictionary.

## Debug Steps

### 1. Deploy Debug Logging
```bash
git push origin main
```

Wait for Railway to deploy, then check logs.

### 2. Test Password Reset with Debug Logging
```bash
curl -X POST https://tutoraide-production.up.railway.app/api/v1/auth/password/reset-request \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'
```

**Check logs for:**
- `=== PASSWORD RESET REQUEST DEBUG ===`
- `Password reset token creation result: {}`
- `=== AUTH TOKEN SERVICE: create_password_reset_token ===`
- `User lookup result: False` (if user doesn't exist)

### 3. Test Direct Email Sending (Bypasses User Check)
```bash
curl -X POST https://tutoraide-production.up.railway.app/api/v1/auth/test-email-direct \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'
```

This will:
- Skip user validation
- Send a test password reset email directly
- Help identify if the issue is with user lookup or email sending

### 4. Create Test User (If Needed)

If logs show "User lookup result: False", the user doesn't exist. Options:

#### Option A: Register via API
```bash
curl -X POST https://tutoraide-production.up.railway.app/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "TestPassword123!",
    "roles": ["manager"]
  }'
```

#### Option B: Run Script Locally (with production database)
```bash
cd /mnt/c/Users/<USER>/OneDrive/Documents/TutorAide_app
python scripts/create_test_user.py
```

### 5. Check Database Connection

If user creation fails, test database connectivity:

```bash
# Check if DATABASE_URL is set in Railway
curl https://tutoraide-production.up.railway.app/api/v1/auth/email/config-check
```
(Requires authentication, but shows if app can connect to services)

## Possible Issues and Solutions

### Issue 1: User Doesn't Exist
**Symptom:** Logs show "User lookup result: False"
**Solution:** Create user using registration endpoint or script

### Issue 2: Database Connection Failed
**Symptom:** Exception in logs about database connection
**Solution:** Check DATABASE_URL in Railway environment variables

### Issue 3: SMTP Configuration Wrong
**Symptom:** Direct email test fails with SMTP error
**Solution:** Verify Gmail app-specific password and settings

### Issue 4: Rate Limiting
**Symptom:** "Password reset rate limit exceeded" in logs
**Solution:** Wait 1 hour or test with different email

## Testing Sequence

1. **First**: Test direct email endpoint to verify SMTP works
2. **If SMTP works**: Check if user exists with password reset endpoint
3. **If user doesn't exist**: Create user via registration
4. **If user exists but no token created**: Check for exceptions in logs
5. **Final test**: Try password reset again after fixing issues

## Expected Success Logs

When everything works, you should see:
```
=== PASSWORD RESET REQUEST DEBUG ===
Requesting password reset for email: <EMAIL>
=== AUTH TOKEN SERVICE: create_password_reset_token ===
Looking up user with email: <EMAIL>
User lookup result: True
Created password reset token for user 1
Password reset token creation result: {'token': '...', 'expires_at': ..., 'user_id': 1, 'email': '<EMAIL>'}
Result type: <class 'dict'>, Has token: True
=== SYNCHRONOUS EMAIL SEND ===
Sending password reset email to: <EMAIL>
=== SMTP EMAIL SEND ATTEMPT ===
Email sent successfully via SMTP
Password reset email sent <NAME_EMAIL>
Added delay of X.XXs for timing protection
```