"""
Subscription models for pre-paid hour packages and usage tracking.
"""

from datetime import datetime, date
from typing import List, Optional, Dict, Any
from decimal import Decimal
from pydantic import BaseModel, Field, field_validator, ConfigDict
from enum import Enum

from app.models.base import (
    BaseEntity,
    IdentifiedEntity,
    SearchFilters
)


class SubscriptionStatus(str, Enum):
    """Subscription status enumeration."""
    ACTIVE = "active"
    EXPIRED = "expired"
    SUSPENDED = "suspended"
    CANCELLED = "cancelled"
    DEPLETED = "depleted"  # No hours remaining


class SubscriptionType(str, Enum):
    """Subscription type enumeration."""
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    SEMESTER = "semester"
    ANNUAL = "annual"
    CUSTOM = "custom"
    PAY_AS_YOU_GO = "pay_as_you_go"


class BillingFrequency(str, Enum):
    """Billing frequency for recurring subscriptions."""
    ONE_TIME = "one_time"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    ANNUALLY = "annually"


class Subscription(IdentifiedEntity):
    """Client subscription model for pre-paid hours."""
    
    subscription_id: int = Field(..., description="Unique subscription identifier")
    client_id: int = Field(..., description="Client who owns the subscription")
    package_id: int = Field(..., description="Subscription package purchased")
    hours_purchased: Decimal = Field(..., description="Total hours purchased")
    hours_used: Decimal = Field(default=Decimal("0.00"), description="Hours consumed")
    hours_remaining: Decimal = Field(..., description="Hours remaining")
    purchase_price: Decimal = Field(..., description="Amount paid for subscription")
    currency: str = Field(default="CAD", description="Currency")
    status: SubscriptionStatus = Field(default=SubscriptionStatus.ACTIVE, description="Subscription status")
    purchase_date: date = Field(..., description="Date of purchase")
    activation_date: date = Field(..., description="Date subscription became active")
    expiry_date: date = Field(..., description="Subscription expiry date")
    auto_renew: bool = Field(default=False, description="Whether to auto-renew")
    stripe_subscription_id: Optional[str] = Field(None, description="Stripe subscription ID if recurring")
    notes: Optional[str] = Field(None, description="Admin notes")
    
    @field_validator('hours_purchased', 'hours_used', 'hours_remaining', 'purchase_price')
    @classmethod
    def validate_amounts(cls, v: Decimal) -> Decimal:
        """Validate amounts are non-negative."""
        if v < 0:
            raise ValueError('Amount cannot be negative')
        return v
    
    @field_validator('expiry_date')
    @classmethod
    def validate_expiry_date(cls, v: date, info) -> date:
        """Validate expiry date is after activation date."""
        if 'activation_date' in info.data and v < info.data['activation_date']:
            raise ValueError('Expiry date cannot be before activation date')
        return v
    
    def is_active(self) -> bool:
        """Check if subscription is currently active."""
        today = date.today()
        return (
            self.status == SubscriptionStatus.ACTIVE and
            self.activation_date <= today <= self.expiry_date and
            self.hours_remaining > 0
        )
    
    def can_deduct_hours(self, hours: Decimal) -> bool:
        """Check if subscription has enough hours for deduction."""
        return self.is_active() and self.hours_remaining >= hours


class SubscriptionPackage(IdentifiedEntity):
    """Pre-defined subscription packages."""
    
    package_id: int = Field(..., description="Unique package identifier")
    package_name: str = Field(..., description="Package display name")
    package_code: str = Field(..., description="Internal package code")
    description: Optional[str] = Field(None, description="Package description")
    hours_included: Decimal = Field(..., description="Hours included in package")
    base_price: Decimal = Field(..., description="Regular price")
    discount_price: Optional[Decimal] = Field(None, description="Discounted price if applicable")
    currency: str = Field(default="CAD", description="Currency")
    subscription_type: SubscriptionType = Field(..., description="Type of subscription")
    validity_days: int = Field(..., description="Days until expiry from activation")
    billing_frequency: BillingFrequency = Field(default=BillingFrequency.ONE_TIME, description="Billing frequency")
    is_active: bool = Field(default=True, description="Whether package is available for purchase")
    is_featured: bool = Field(default=False, description="Whether to highlight this package")
    subject_restrictions: Optional[List[str]] = Field(None, description="Restricted to specific subjects")
    min_purchase_hours: Optional[Decimal] = Field(None, description="Minimum hours for custom packages")
    max_purchase_hours: Optional[Decimal] = Field(None, description="Maximum hours for custom packages")
    
    @field_validator('hours_included', 'base_price')
    @classmethod
    def validate_positive_amounts(cls, v: Decimal) -> Decimal:
        """Validate amounts are positive."""
        if v <= 0:
            raise ValueError('Amount must be positive')
        return v
    
    @field_validator('validity_days')
    @classmethod
    def validate_validity(cls, v: int) -> int:
        """Validate validity period."""
        if v <= 0:
            raise ValueError('Validity days must be positive')
        return v
    
    def get_current_price(self) -> Decimal:
        """Get current price (discounted or regular)."""
        return self.discount_price if self.discount_price else self.base_price
    
    def get_price_per_hour(self) -> Decimal:
        """Calculate price per hour."""
        return self.get_current_price() / self.hours_included


class SubscriptionUsage(IdentifiedEntity):
    """Track subscription hour usage per session."""
    
    usage_id: int = Field(..., description="Unique usage identifier")
    subscription_id: int = Field(..., description="Associated subscription")
    appointment_id: int = Field(..., description="Appointment that consumed hours")
    hours_deducted: Decimal = Field(..., description="Hours deducted")
    usage_date: datetime = Field(..., description="When hours were deducted")
    remaining_hours_after: Decimal = Field(..., description="Hours remaining after deduction")
    rollback_reason: Optional[str] = Field(None, description="Reason if usage was rolled back")
    rolled_back_at: Optional[datetime] = Field(None, description="When usage was rolled back")
    
    @field_validator('hours_deducted')
    @classmethod
    def validate_hours_deducted(cls, v: Decimal) -> Decimal:
        """Validate hours deducted is positive."""
        if v <= 0:
            raise ValueError('Hours deducted must be positive')
        return v


class SubscriptionRenewal(IdentifiedEntity):
    """Subscription renewal history."""
    
    renewal_id: int = Field(..., description="Unique renewal identifier")
    subscription_id: int = Field(..., description="Renewed subscription")
    previous_expiry_date: date = Field(..., description="Previous expiry date")
    new_expiry_date: date = Field(..., description="New expiry date")
    hours_added: Decimal = Field(..., description="Hours added in renewal")
    renewal_price: Decimal = Field(..., description="Price paid for renewal")
    currency: str = Field(default="CAD", description="Currency")
    payment_method: str = Field(..., description="Payment method used")
    stripe_payment_intent_id: Optional[str] = Field(None, description="Stripe payment ID")
    renewed_by: int = Field(..., description="User who processed renewal")
    renewal_date: datetime = Field(..., description="When renewal was processed")


# ============================================
# Create/Update Schemas
# ============================================

class SubscriptionCreate(BaseModel):
    """Schema for creating subscription."""
    
    model_config = ConfigDict(from_attributes=True)
    
    client_id: int = Field(..., description="Client purchasing subscription")
    package_id: int = Field(..., description="Package to purchase")
    payment_method: str = Field(..., description="Payment method")
    stripe_payment_method_id: Optional[str] = Field(None, description="Stripe payment method ID")
    auto_renew: Optional[bool] = Field(False, description="Enable auto-renewal")
    activation_date: Optional[date] = Field(None, description="Custom activation date")
    notes: Optional[str] = Field(None, description="Admin notes")


class SubscriptionUpdate(BaseModel):
    """Schema for updating subscription."""
    
    model_config = ConfigDict(from_attributes=True)
    
    status: Optional[SubscriptionStatus] = Field(None, description="Update status")
    auto_renew: Optional[bool] = Field(None, description="Update auto-renewal")
    expiry_date: Optional[date] = Field(None, description="Extend expiry date")
    notes: Optional[str] = Field(None, description="Update notes")


class SubscriptionUsageCreate(BaseModel):
    """Schema for deducting subscription hours."""
    
    model_config = ConfigDict(from_attributes=True)
    
    subscription_id: int = Field(..., description="Subscription to deduct from")
    appointment_id: int = Field(..., description="Appointment consuming hours")
    hours_to_deduct: Decimal = Field(..., gt=0, description="Hours to deduct")


class SubscriptionRenewalCreate(BaseModel):
    """Schema for renewing subscription."""
    
    model_config = ConfigDict(from_attributes=True)
    
    subscription_id: int = Field(..., description="Subscription to renew")
    package_id: Optional[int] = Field(None, description="New package (or same)")
    payment_method: str = Field(..., description="Payment method")
    stripe_payment_method_id: Optional[str] = Field(None, description="Stripe payment method ID")


class SubscriptionPackageCreate(BaseModel):
    """Schema for creating subscription package."""
    
    model_config = ConfigDict(from_attributes=True)
    
    package_name: str = Field(..., min_length=1, max_length=100)
    package_code: str = Field(..., min_length=1, max_length=50)
    description: Optional[str] = Field(None, max_length=500)
    hours_included: Decimal = Field(..., gt=0)
    base_price: Decimal = Field(..., gt=0)
    discount_price: Optional[Decimal] = Field(None, gt=0)
    subscription_type: SubscriptionType
    validity_days: int = Field(..., gt=0)
    billing_frequency: Optional[BillingFrequency] = Field(BillingFrequency.ONE_TIME)
    is_featured: Optional[bool] = Field(False)
    subject_restrictions: Optional[List[str]] = Field(None)


# ============================================
# Response Schemas
# ============================================

class SubscriptionResponse(BaseModel):
    """Subscription response schema."""
    
    model_config = ConfigDict(from_attributes=True)
    
    subscription_id: int
    client_id: int
    package_id: int
    hours_purchased: Decimal
    hours_used: Decimal
    hours_remaining: Decimal
    purchase_price: Decimal
    currency: str
    status: SubscriptionStatus
    purchase_date: date
    activation_date: date
    expiry_date: date
    auto_renew: bool
    stripe_subscription_id: Optional[str]
    notes: Optional[str]
    package_name: Optional[str]  # Joined from package
    client_name: Optional[str]   # Joined from client
    is_active: bool             # Computed property
    days_until_expiry: Optional[int]  # Computed
    usage_percentage: float     # Computed
    created_at: datetime
    updated_at: datetime


class SubscriptionPackageResponse(BaseModel):
    """Subscription package response schema."""
    
    model_config = ConfigDict(from_attributes=True)
    
    package_id: int
    package_name: str
    package_code: str
    description: Optional[str]
    hours_included: Decimal
    base_price: Decimal
    discount_price: Optional[Decimal]
    current_price: Decimal  # Computed
    price_per_hour: Decimal  # Computed
    currency: str
    subscription_type: SubscriptionType
    validity_days: int
    billing_frequency: BillingFrequency
    is_active: bool
    is_featured: bool
    subject_restrictions: Optional[List[str]]
    savings_amount: Optional[Decimal]  # If discounted
    savings_percentage: Optional[float]  # If discounted
    created_at: datetime
    updated_at: datetime


class SubscriptionUsageResponse(BaseModel):
    """Subscription usage response schema."""
    
    model_config = ConfigDict(from_attributes=True)
    
    usage_id: int
    subscription_id: int
    appointment_id: int
    hours_deducted: Decimal
    usage_date: datetime
    remaining_hours_after: Decimal
    appointment_details: Optional[Dict[str, Any]]  # Joined data
    tutor_name: Optional[str]
    subject: Optional[str]
    session_date: Optional[date]
    rollback_reason: Optional[str]
    rolled_back_at: Optional[datetime]


class SubscriptionSummary(BaseModel):
    """Client subscription summary."""
    
    model_config = ConfigDict(from_attributes=True)
    
    client_id: int
    active_subscriptions: int
    total_hours_purchased: Decimal
    total_hours_used: Decimal
    total_hours_remaining: Decimal
    total_spent: Decimal
    upcoming_renewals: List[Dict[str, Any]]
    usage_history: List[SubscriptionUsageResponse]
    available_packages: List[SubscriptionPackageResponse]


class SubscriptionSearchFilters(SearchFilters):
    """Subscription-specific search filters."""
    
    client_id: Optional[int] = Field(None, description="Filter by client")
    status: Optional[SubscriptionStatus] = Field(None, description="Filter by status")
    package_id: Optional[int] = Field(None, description="Filter by package")
    expiring_within_days: Optional[int] = Field(None, description="Expiring within N days")
    auto_renew: Optional[bool] = Field(None, description="Filter by auto-renewal")
    has_hours_remaining: Optional[bool] = Field(None, description="Has hours left")