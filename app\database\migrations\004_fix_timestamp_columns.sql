-- Fix timestamp columns to use timezone
-- Version: 004
-- Description: Convert TIMESTAMP columns to TIMESTAMP WITH TIME ZONE
-- Author: TutorAide Development Team
-- Date: 2025-06-10

-- Update all tables with timestamp columns dynamically
DO $$ 
DECLARE
    r RECORD;
BEGIN
    FOR r IN (
        SELECT table_name, column_name
        FROM information_schema.columns
        WHERE data_type = 'timestamp without time zone'
        AND table_schema = 'public'
        AND table_name NOT IN ('migrations') -- Exclude migration tracking table
    )
    LOOP
        EXECUTE format('ALTER TABLE %I ALTER COLUMN %I TYPE TIMESTAMP WITH TIME ZONE', 
                      r.table_name, r.column_name);
        RAISE NOTICE 'Updated %.% to TIMESTAMP WITH TIME ZONE', r.table_name, r.column_name;
    END LOOP;
END $$;