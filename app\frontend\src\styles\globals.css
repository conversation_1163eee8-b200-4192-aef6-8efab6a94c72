@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Import Inter font - temporarily disabled due to CSP issues */
/* @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap'); */

/* Root CSS Variables from Design Tokens */
@layer base {
  :root {
    /* Primary Colors */
    --color-primary: #1f2937;
    --color-primary-light: #374151;
    --color-primary-dark: #111827;
    
    /* Secondary Colors */
    --color-secondary: #f9fafb;
    --color-secondary-light: #ffffff;
    --color-secondary-dark: #f3f4f6;
    
    /* Accent Colors */
    --color-accent-red: #f87171;
    --color-accent-red-light: #fca5a5;
    --color-accent-red-dark: #ef4444;
    --color-accent-highlight: #dc2626;
    --color-accent-gold: #fbbf24;
    
    /* Semantic Colors */
    --color-semantic-success: #10b981;
    --color-semantic-warning: #f59e0b;
    --color-semantic-error: #ef4444;
    --color-semantic-info: #3b82f6;
    
    /* Text Colors */
    --color-text-primary: #1f2937;
    --color-text-secondary: #6b7280;
    --color-text-muted: #9ca3af;
    --color-text-inverse: #ffffff;
    
    /* Background Colors */
    --color-background-primary: #ffffff;
    --color-background-secondary: #f9fafb;
    --color-background-tertiary: #f3f4f6;
    
    /* Border Colors */
    --color-border-primary: #e5e7eb;
    --color-border-secondary: #d1d5db;
    
    /* Shadows */
    --shadow-subtle: 0 1px 3px rgba(0, 0, 0, 0.05);
    --shadow-soft: 0 4px 12px rgba(0, 0, 0, 0.08);
    --shadow-elevated: 0 8px 25px rgba(0, 0, 0, 0.12);
    --shadow-focus: 0 0 0 3px rgba(248, 113, 113, 0.1);
    --shadow-error: 0 0 0 3px rgba(239, 68, 68, 0.1);
    
    /* Border Radius */
    --radius-none: 0px;
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 24px;
    --radius-full: 9999px;
    
    /* Spacing */
    --space-xs: 4px;
    --space-sm: 8px;
    --space-md: 16px;
    --space-lg: 24px;
    --space-xl: 32px;
    --space-2xl: 48px;
  }
}

/* Global Styles */
@layer base {
  * {
    @apply border-border-primary;
  }
  
  html {
    @apply antialiased;
  }
  
  body {
    @apply bg-background-primary text-text-primary;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
  }
  
  /* Focus Visible Styles */
  *:focus-visible {
    @apply outline-none ring-2 ring-accent-red ring-offset-2 ring-offset-white;
  }
  
  /* Scrollbar Styles */
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-background-secondary;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-border-secondary rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-text-muted;
  }
}

/* Component Layer - Reusable Styles */
@layer components {
  /* Card Styles */
  .card {
    @apply bg-white rounded-xl shadow-subtle border border-border-primary;
  }
  
  .card-hover {
    @apply hover:shadow-soft hover:-translate-y-0.5 transition-all duration-200;
  }
  
  /* Button Base */
  .btn {
    @apply inline-flex items-center justify-center font-medium transition-all duration-200;
    @apply focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2;
    @apply disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  /* Form Elements */
  .form-input {
    @apply w-full px-4 py-3 bg-background-secondary border border-border-primary;
    @apply rounded-xl text-text-primary placeholder-text-muted;
    @apply transition-all duration-200 focus:outline-none;
    @apply focus:bg-white focus:border-accent-red focus:ring-2 focus:ring-accent-red/10;
  }
  
  .form-label {
    @apply block text-sm font-medium text-text-primary mb-2;
  }
  
  .form-error {
    @apply text-sm text-semantic-error mt-1;
  }
  
  .form-hint {
    @apply text-sm text-text-muted mt-1;
  }
}

/* Utility Layer - Custom Utilities */
@layer utilities {
  /* Text Utilities */
  .text-balance {
    text-wrap: balance;
  }
  
  /* Animation Utilities */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }
  
  /* Gradient Utilities */
  .gradient-primary {
    @apply bg-gradient-to-r from-accent-red to-accent-highlight;
  }
  
  .gradient-subtle {
    @apply bg-gradient-to-br from-background-primary to-background-secondary;
  }
}

/* Keyframe Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Loading Spinner Animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Transitions */
.transition-basic {
  @apply transition-all duration-200 ease-in-out;
}

.transition-smooth {
  @apply transition-all duration-300 ease-in-out;
}

/* Accessibility Utilities */
.focus-ring {
  @apply focus:outline-none focus-visible:ring-2 focus-visible:ring-accent-red focus-visible:ring-offset-2;
}

.sr-only {
  @apply absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0;
}

/* Print Styles */
@media print {
  body {
    @apply bg-white;
  }
  
  .no-print {
    @apply hidden;
  }
  
  .print-break-after {
    page-break-after: always;
  }
}