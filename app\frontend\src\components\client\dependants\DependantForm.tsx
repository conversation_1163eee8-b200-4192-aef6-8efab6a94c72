import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Input } from '../../common/Input';
import Button from '../../common/Button';
import { Select } from '../../common/Select';
import { 
  User, 
  Calendar, 
  School, 
  GraduationCap,
  Target,
  Heart,
  AlertTriangle,
  Pill
} from 'lucide-react';

interface DependantFormProps {
  dependant?: {
    dependant_id: number;
    first_name: string;
    last_name: string;
    date_of_birth: string;
    grade_level: string | null;
    school_name: string | null;
    special_needs: string | null;
    allergies: string | null;
    medications: string | null;
    learning_goals: string | null;
  } | null;
  onSave: (data: any) => void;
  onCancel: () => void;
}

export const DependantForm: React.FC<DependantFormProps> = ({
  dependant,
  onSave,
  onCancel
}) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    first_name: dependant?.first_name || '',
    last_name: dependant?.last_name || '',
    date_of_birth: dependant?.date_of_birth || '',
    grade_level: dependant?.grade_level || '',
    school_name: dependant?.school_name || '',
    special_needs: dependant?.special_needs || '',
    allergies: dependant?.allergies || '',
    medications: dependant?.medications || '',
    learning_goals: dependant?.learning_goals || ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [activeTab, setActiveTab] = useState<'basic' | 'education' | 'health'>('basic');

  const gradeOptions = [
    { value: 'Pre-K', label: t('client.dependants.grades.preK') },
    { value: 'K', label: t('client.dependants.grades.kindergarten') },
    { value: '1', label: t('client.dependants.grades.grade1') },
    { value: '2', label: t('client.dependants.grades.grade2') },
    { value: '3', label: t('client.dependants.grades.grade3') },
    { value: '4', label: t('client.dependants.grades.grade4') },
    { value: '5', label: t('client.dependants.grades.grade5') },
    { value: '6', label: t('client.dependants.grades.grade6') },
    { value: '7', label: t('client.dependants.grades.grade7') },
    { value: '8', label: t('client.dependants.grades.grade8') },
    { value: '9', label: t('client.dependants.grades.grade9') },
    { value: '10', label: t('client.dependants.grades.grade10') },
    { value: '11', label: t('client.dependants.grades.grade11') },
    { value: '12', label: t('client.dependants.grades.grade12') },
    { value: 'College', label: t('client.dependants.grades.college') },
    { value: 'University', label: t('client.dependants.grades.university') }
  ];

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.first_name.trim()) {
      newErrors.first_name = t('client.dependants.errors.firstNameRequired');
    }
    
    if (!formData.last_name.trim()) {
      newErrors.last_name = t('client.dependants.errors.lastNameRequired');
    }
    
    if (!formData.date_of_birth) {
      newErrors.date_of_birth = t('client.dependants.errors.dateOfBirthRequired');
    } else {
      const birthDate = new Date(formData.date_of_birth);
      const today = new Date();
      if (birthDate > today) {
        newErrors.date_of_birth = t('client.dependants.errors.futureDateOfBirth');
      }
      
      // Check if age is reasonable (0-25 years)
      const age = today.getFullYear() - birthDate.getFullYear();
      if (age > 25) {
        newErrors.date_of_birth = t('client.dependants.errors.tooOld');
      }
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      // Clean up empty strings to null
      const dataToSave = Object.entries(formData).reduce((acc, [key, value]) => {
        acc[key] = value === '' ? null : value;
        return acc;
      }, {} as any);
      
      onSave(dataToSave);
    }
  };

  const tabs = [
    { id: 'basic', label: t('client.dependants.tabs.basic'), icon: User },
    { id: 'education', label: t('client.dependants.tabs.education'), icon: School },
    { id: 'health', label: t('client.dependants.tabs.health'), icon: Heart }
  ];

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8" aria-label="Tabs">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                type="button"
                onClick={() => setActiveTab(tab.id as any)}
                className={`
                  py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2
                  ${activeTab === tab.id
                    ? 'border-accent-red text-accent-red'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                `}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {activeTab === 'basic' && (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  {t('client.dependants.fields.firstName')}
                </label>
                <Input
                  leftIcon={<User className="w-4 h-4" />}
                  value={formData.first_name}
                  onChange={(e) => setFormData({ ...formData, first_name: e.target.value })}
                  placeholder={t('client.dependants.placeholders.firstName')}
                  error={errors.first_name}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  {t('client.dependants.fields.lastName')}
                </label>
                <Input
                  leftIcon={<User className="w-4 h-4" />}
                  value={formData.last_name}
                  onChange={(e) => setFormData({ ...formData, last_name: e.target.value })}
                  placeholder={t('client.dependants.placeholders.lastName')}
                  error={errors.last_name}
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                {t('client.dependants.fields.dateOfBirth')}
              </label>
              <Input
                type="date"
                leftIcon={<Calendar className="w-4 h-4" />}
                value={formData.date_of_birth}
                onChange={(e) => setFormData({ ...formData, date_of_birth: e.target.value })}
                max={new Date().toISOString().split('T')[0]}
                error={errors.date_of_birth}
              />
            </div>
          </>
        )}

        {activeTab === 'education' && (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  {t('client.dependants.fields.gradeLevel')}
                </label>
                <Select
                  value={formData.grade_level}
                  onChange={(value) => setFormData({ ...formData, grade_level: value })}
                  options={gradeOptions}
                  placeholder={t('client.dependants.placeholders.selectGrade')}
                  leftIcon={<GraduationCap className="w-4 h-4" />}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  {t('client.dependants.fields.school')}
                </label>
                <Input
                  leftIcon={<School className="w-4 h-4" />}
                  value={formData.school_name}
                  onChange={(e) => setFormData({ ...formData, school_name: e.target.value })}
                  placeholder={t('client.dependants.placeholders.school')}
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                {t('client.dependants.fields.learningGoals')}
              </label>
              <textarea
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent-red focus:border-accent-red resize-none"
                rows={4}
                value={formData.learning_goals}
                onChange={(e) => setFormData({ ...formData, learning_goals: e.target.value })}
                placeholder={t('client.dependants.placeholders.learningGoals')}
              />
              <p className="text-sm text-text-secondary mt-1">
                {t('client.dependants.learningGoalsHelp')}
              </p>
            </div>
          </>
        )}

        {activeTab === 'health' && (
          <>
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-6">
              <div className="flex items-start gap-3">
                <AlertTriangle className="w-5 h-5 text-amber-600 flex-shrink-0 mt-0.5" />
                <div>
                  <p className="text-sm text-amber-800 font-medium">
                    {t('client.dependants.healthInfo')}
                  </p>
                  <p className="text-sm text-amber-700 mt-1">
                    {t('client.dependants.healthInfoDescription')}
                  </p>
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                {t('client.dependants.fields.specialNeeds')}
              </label>
              <textarea
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent-red focus:border-accent-red resize-none"
                rows={3}
                value={formData.special_needs}
                onChange={(e) => setFormData({ ...formData, special_needs: e.target.value })}
                placeholder={t('client.dependants.placeholders.specialNeeds')}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                {t('client.dependants.fields.allergies')}
              </label>
              <textarea
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent-red focus:border-accent-red resize-none"
                rows={2}
                value={formData.allergies}
                onChange={(e) => setFormData({ ...formData, allergies: e.target.value })}
                placeholder={t('client.dependants.placeholders.allergies')}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                {t('client.dependants.fields.medications')}
              </label>
              <textarea
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent-red focus:border-accent-red resize-none"
                rows={2}
                value={formData.medications}
                onChange={(e) => setFormData({ ...formData, medications: e.target.value })}
                placeholder={t('client.dependants.placeholders.medications')}
              />
            </div>
          </>
        )}
      </div>

      {/* Actions */}
      <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
        <Button
          type="button"
          variant="ghost"
          onClick={onCancel}
        >
          {t('common.cancel')}
        </Button>
        <Button
          type="submit"
        >
          {dependant ? t('common.save') : t('common.create')}
        </Button>
      </div>
    </form>
  );
};