import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Shield, UserCheck, Lock, Clock, AlertCircle,
  Save, Plus, Trash2, Edit2, Search
} from 'lucide-react';

interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  userCount: number;
  isSystem: boolean;
}

interface UserConfig {
  registration: {
    allowSelfRegistration: boolean;
    requireEmailVerification: boolean;
    requireManagerApproval: boolean;
    defaultRole: string;
    allowedDomains: string[];
  };
  authentication: {
    requireStrongPassword: boolean;
    enableTwoFactor: boolean;
    sessionDuration: number;
    rememberMeDuration: number;
    maxConcurrentSessions: number;
  };
  profileSettings: {
    allowProfilePhotoUpload: boolean;
    requireCompleteProfile: boolean;
    editableFields: string[];
    privacySettings: string[];
  };
}

// Mock data
const mockRoles: Role[] = [
  {
    id: '1',
    name: 'Manager',
    description: 'Full system access with administrative privileges',
    permissions: ['all'],
    userCount: 3,
    isSystem: true,
  },
  {
    id: '2',
    name: '<PERSON><PERSON>',
    description: 'Can manage their schedule, view assigned students, and update session notes',
    permissions: ['view_schedule', 'manage_sessions', 'view_students', 'edit_profile'],
    userCount: 78,
    isSystem: true,
  },
  {
    id: '3',
    name: 'Client',
    description: 'Can book sessions, view invoices, and manage dependants',
    permissions: ['book_sessions', 'view_invoices', 'manage_dependants', 'view_tutors'],
    userCount: 248,
    isSystem: true,
  },
  {
    id: '4',
    name: 'Staff',
    description: 'Limited administrative access for customer support',
    permissions: ['view_users', 'manage_messages', 'view_reports'],
    userCount: 5,
    isSystem: false,
  },
];

const allPermissions = [
  { category: 'Users', permissions: ['view_users', 'create_users', 'edit_users', 'delete_users'] },
  { category: 'Sessions', permissions: ['view_sessions', 'book_sessions', 'manage_sessions', 'cancel_sessions'] },
  { category: 'Billing', permissions: ['view_invoices', 'create_invoices', 'manage_payments', 'view_financial_reports'] },
  { category: 'Reports', permissions: ['view_reports', 'export_reports', 'create_reports'] },
  { category: 'Settings', permissions: ['view_settings', 'manage_settings', 'manage_roles'] },
  { category: 'Messages', permissions: ['view_messages', 'send_messages', 'manage_templates', 'send_broadcasts'] },
];

const UserSettings: React.FC = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<'general' | 'roles'>('general');
  const [roles, setRoles] = useState<Role[]>(mockRoles);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [isEditingRole, setIsEditingRole] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [hasChanges, setHasChanges] = useState(false);

  const [config, setConfig] = useState<UserConfig>({
    registration: {
      allowSelfRegistration: true,
      requireEmailVerification: true,
      requireManagerApproval: false,
      defaultRole: 'Client',
      allowedDomains: ['tutoraide.ca', 'gmail.com', 'outlook.com'],
    },
    authentication: {
      requireStrongPassword: true,
      enableTwoFactor: false,
      sessionDuration: 30,
      rememberMeDuration: 30,
      maxConcurrentSessions: 3,
    },
    profileSettings: {
      allowProfilePhotoUpload: true,
      requireCompleteProfile: true,
      editableFields: ['name', 'email', 'phone', 'address', 'bio'],
      privacySettings: ['email', 'phone'],
    },
  });

  const updateConfig = (section: keyof UserConfig, field: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value,
      },
    }));
    setHasChanges(true);
  };

  const handleDeleteRole = (roleId: string) => {
    const role = roles.find(r => r.id === roleId);
    if (role?.isSystem) {
      alert('System roles cannot be deleted');
      return;
    }
    if (window.confirm('Are you sure you want to delete this role?')) {
      setRoles(roles.filter(r => r.id !== roleId));
      if (selectedRole?.id === roleId) {
        setSelectedRole(null);
      }
    }
  };

  const handleSave = async () => {
    console.log('Saving user settings:', config);
    await new Promise(resolve => setTimeout(resolve, 1500));
    setHasChanges(false);
  };

  return (
    <div className="p-6 max-w-6xl mx-auto">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-text-primary">User Settings</h1>
        <p className="text-text-secondary mt-1">Manage user registration, authentication, and role permissions</p>
      </div>

      {/* Tabs */}
      <div className="flex items-center gap-6 border-b border-primary-200 mb-6">
        <button
          onClick={() => setActiveTab('general')}
          className={`pb-3 px-1 font-medium transition-colors ${
            activeTab === 'general'
              ? 'text-accent-red border-b-2 border-accent-red'
              : 'text-text-secondary hover:text-text-primary'
          }`}
        >
          General Settings
        </button>
        <button
          onClick={() => setActiveTab('roles')}
          className={`pb-3 px-1 font-medium transition-colors ${
            activeTab === 'roles'
              ? 'text-accent-red border-b-2 border-accent-red'
              : 'text-text-secondary hover:text-text-primary'
          }`}
        >
          Roles & Permissions
        </button>
      </div>

      {activeTab === 'general' ? (
        <>
          {/* Registration Settings */}
          <div className="bg-white rounded-large shadow-soft p-6 mb-6">
            <div className="flex items-center gap-3 mb-6">
              <UserCheck className="w-5 h-5 text-accent-red" />
              <h2 className="text-lg font-semibold text-text-primary">Registration Settings</h2>
            </div>

            <div className="space-y-4">
              <label className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-text-primary">Allow Self-Registration</p>
                  <p className="text-sm text-text-secondary">Users can create their own accounts</p>
                </div>
                <input
                  type="checkbox"
                  checked={config.registration.allowSelfRegistration}
                  onChange={(e) => updateConfig('registration', 'allowSelfRegistration', e.target.checked)}
                  className="w-5 h-5 rounded accent-accent-red"
                />
              </label>

              <label className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-text-primary">Require Email Verification</p>
                  <p className="text-sm text-text-secondary">Users must verify their email before accessing the platform</p>
                </div>
                <input
                  type="checkbox"
                  checked={config.registration.requireEmailVerification}
                  onChange={(e) => updateConfig('registration', 'requireEmailVerification', e.target.checked)}
                  className="w-5 h-5 rounded accent-accent-red"
                />
              </label>

              <label className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-text-primary">Require Manager Approval</p>
                  <p className="text-sm text-text-secondary">New accounts must be approved by a manager</p>
                </div>
                <input
                  type="checkbox"
                  checked={config.registration.requireManagerApproval}
                  onChange={(e) => updateConfig('registration', 'requireManagerApproval', e.target.checked)}
                  className="w-5 h-5 rounded accent-accent-red"
                />
              </label>

              <div className="pt-4">
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Default Role for New Users
                </label>
                <select
                  value={config.registration.defaultRole}
                  onChange={(e) => updateConfig('registration', 'defaultRole', e.target.value)}
                  className="w-full md:w-1/2 px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                >
                  <option value="Client">Client</option>
                  <option value="Tutor">Tutor</option>
                </select>
              </div>

              <div className="pt-4">
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Allowed Email Domains
                </label>
                <p className="text-sm text-text-secondary mb-3">
                  Leave empty to allow all domains. Add domains to restrict registration.
                </p>
                <div className="space-y-2">
                  {config.registration.allowedDomains.map((domain, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <input
                        type="text"
                        value={domain}
                        onChange={(e) => {
                          const newDomains = [...config.registration.allowedDomains];
                          newDomains[index] = e.target.value;
                          updateConfig('registration', 'allowedDomains', newDomains);
                        }}
                        className="flex-1 px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                        placeholder="example.com"
                      />
                      <button
                        onClick={() => {
                          const newDomains = config.registration.allowedDomains.filter((_, i) => i !== index);
                          updateConfig('registration', 'allowedDomains', newDomains);
                        }}
                        className="p-2 text-accent-red hover:bg-accent-red hover:bg-opacity-10 rounded-medium transition-colors"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                  <button
                    onClick={() => {
                      updateConfig('registration', 'allowedDomains', [...config.registration.allowedDomains, '']);
                    }}
                    className="flex items-center gap-2 text-accent-red hover:underline"
                  >
                    <Plus className="w-4 h-4" />
                    Add Domain
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Authentication Settings */}
          <div className="bg-white rounded-large shadow-soft p-6 mb-6">
            <div className="flex items-center gap-3 mb-6">
              <Lock className="w-5 h-5 text-accent-red" />
              <h2 className="text-lg font-semibold text-text-primary">Authentication Settings</h2>
            </div>

            <div className="space-y-4">
              <label className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-text-primary">Require Strong Passwords</p>
                  <p className="text-sm text-text-secondary">Enforce complexity requirements for passwords</p>
                </div>
                <input
                  type="checkbox"
                  checked={config.authentication.requireStrongPassword}
                  onChange={(e) => updateConfig('authentication', 'requireStrongPassword', e.target.checked)}
                  className="w-5 h-5 rounded accent-accent-red"
                />
              </label>

              <label className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-text-primary">Enable Two-Factor Authentication</p>
                  <p className="text-sm text-text-secondary">Users can enable 2FA for added security</p>
                </div>
                <input
                  type="checkbox"
                  checked={config.authentication.enableTwoFactor}
                  onChange={(e) => updateConfig('authentication', 'enableTwoFactor', e.target.checked)}
                  className="w-5 h-5 rounded accent-accent-red"
                />
              </label>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pt-4">
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Session Duration (minutes)
                  </label>
                  <input
                    type="number"
                    value={config.authentication.sessionDuration}
                    onChange={(e) => updateConfig('authentication', 'sessionDuration', parseInt(e.target.value))}
                    className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                    min={5}
                    max={1440}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Remember Me Duration (days)
                  </label>
                  <input
                    type="number"
                    value={config.authentication.rememberMeDuration}
                    onChange={(e) => updateConfig('authentication', 'rememberMeDuration', parseInt(e.target.value))}
                    className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                    min={1}
                    max={365}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Max Concurrent Sessions
                  </label>
                  <input
                    type="number"
                    value={config.authentication.maxConcurrentSessions}
                    onChange={(e) => updateConfig('authentication', 'maxConcurrentSessions', parseInt(e.target.value))}
                    className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                    min={1}
                    max={10}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Save Button */}
          <div className="flex justify-end">
            <button
              onClick={handleSave}
              disabled={!hasChanges}
              className={`
                flex items-center gap-2 px-6 py-2 rounded-soft text-white
                ${hasChanges
                  ? 'bg-accent-red hover:bg-accent-red-dark'
                  : 'bg-primary-300 cursor-not-allowed'
                }
              `}
            >
              <Save className="w-4 h-4" />
              Save Changes
            </button>
          </div>
        </>
      ) : (
        <>
          {/* Roles & Permissions */}
          <div className="bg-white rounded-large shadow-soft p-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <Shield className="w-5 h-5 text-accent-red" />
                <h2 className="text-lg font-semibold text-text-primary">Roles & Permissions</h2>
              </div>
              <button
                onClick={() => {
                  setSelectedRole(null);
                  setIsEditingRole(true);
                }}
                className="flex items-center gap-2 px-4 py-2 bg-accent-red text-white rounded-soft hover:bg-accent-red-dark"
              >
                <Plus className="w-4 h-4" />
                New Role
              </button>
            </div>

            {/* Search */}
            <div className="relative mb-4">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-text-secondary" />
              <input
                type="text"
                placeholder="Search roles..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-background-secondary rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
              />
            </div>

            {/* Roles List */}
            <div className="space-y-3">
              {roles
                .filter(role => role.name.toLowerCase().includes(searchQuery.toLowerCase()))
                .map(role => (
                  <div
                    key={role.id}
                    className="p-4 border border-primary-200 rounded-medium hover:border-accent-red transition-colors cursor-pointer"
                    onClick={() => setSelectedRole(role)}
                  >
                    <div className="flex items-start justify-between">
                      <div>
                        <div className="flex items-center gap-2">
                          <h3 className="font-medium text-text-primary">{role.name}</h3>
                          {role.isSystem && (
                            <span className="px-2 py-0.5 bg-primary-200 text-text-secondary text-xs rounded-full">
                              System
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-text-secondary mt-1">{role.description}</p>
                        <p className="text-xs text-text-secondary mt-2">
                          {role.userCount} users • {role.permissions.length} permissions
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedRole(role);
                            setIsEditingRole(true);
                          }}
                          className="p-1.5 hover:bg-background-secondary rounded-medium transition-colors"
                        >
                          <Edit2 className="w-4 h-4 text-text-secondary" />
                        </button>
                        {!role.isSystem && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteRole(role.id);
                            }}
                            className="p-1.5 hover:bg-background-secondary rounded-medium transition-colors"
                          >
                            <Trash2 className="w-4 h-4 text-accent-red" />
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          </div>

          {/* Warning */}
          <div className="mt-6 bg-accent-orange bg-opacity-10 p-4 rounded-medium">
            <div className="flex items-start gap-3">
              <AlertCircle className="w-5 h-5 text-accent-orange mt-0.5" />
              <div>
                <p className="font-medium text-text-primary">Important</p>
                <p className="text-sm text-text-secondary mt-1">
                  Changes to roles and permissions affect all users immediately. System roles (Manager, Tutor, Client) 
                  cannot be deleted but their permissions can be modified.
                </p>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default UserSettings;