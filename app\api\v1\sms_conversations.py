"""
SMS conversation management API endpoints.
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, Query, HTTPException, status

from app.core.dependencies import get_current_user
from app.core.auth_decorators import require_roles
from app.models.user_models import User
from app.models.base import UserRoleType
from app.services.sms_conversation_service import SMSConversationService

router = APIRouter()
sms_conversation_service = SMSConversationService()


@router.get("/")
@require_roles([UserRoleType.MANAGER])
async def get_conversations(
    current_user: User = Depends(get_current_user),
    status: Optional[str] = Query(None, description="Filter by status"),
    unread_only: bool = Query(False, description="Show only unread"),
    search: Optional[str] = Query(None, description="Search query"),
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0)
) -> Dict[str, Any]:
    """
    Get SMS conversations for manager dashboard.
    
    Features:
    - Latest-first ordering
    - Unread message indicators
    - Search by phone/name/message
    - Status filtering
    - Pagination
    """
    try:
        return await sms_conversation_service.get_conversations(
            manager_id=current_user.user_id,
            status=status,
            unread_only=unread_only,
            search_query=search,
            limit=limit,
            offset=offset
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving conversations: {str(e)}"
        )


@router.get("/{phone_number}/messages")
@require_roles([UserRoleType.MANAGER])
async def get_conversation_messages(
    phone_number: str,
    current_user: User = Depends(get_current_user),
    limit: int = Query(100, ge=1, le=500),
    before: Optional[datetime] = Query(None, description="Get messages before this timestamp")
) -> List[Dict[str, Any]]:
    """
    Get messages in a conversation.
    
    Returns messages in reverse chronological order (latest first).
    Automatically marks inbound messages as read.
    """
    try:
        return await sms_conversation_service.get_conversation_messages(
            phone_number=phone_number,
            manager_id=current_user.user_id,
            limit=limit,
            before_timestamp=before
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving messages: {str(e)}"
        )


@router.post("/{phone_number}/messages")
@require_roles([UserRoleType.MANAGER])
async def send_message(
    phone_number: str,
    message_data: Dict[str, Any],
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Send SMS message in conversation.
    
    Request body:
    {
        "message": "Message content",
        "template_id": null,  // Optional template
        "metadata": {}        // Optional metadata
    }
    """
    try:
        return await sms_conversation_service.send_message(
            phone_number=phone_number,
            message=message_data["message"],
            manager_id=current_user.user_id,
            template_id=message_data.get("template_id"),
            metadata=message_data.get("metadata")
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error sending message: {str(e)}"
        )


@router.patch("/{phone_number}/status")
@require_roles([UserRoleType.MANAGER])
async def update_conversation_status(
    phone_number: str,
    status_data: Dict[str, Any],
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Update conversation status.
    
    Request body:
    {
        "status": "active|resolved|waiting|escalated|closed",
        "notes": "Optional status change notes"
    }
    """
    try:
        success = await sms_conversation_service.update_conversation_status(
            phone_number=phone_number,
            status=status_data["status"],
            manager_id=current_user.user_id,
            notes=status_data.get("notes")
        )
        return {"success": success}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating status: {str(e)}"
        )


@router.patch("/{phone_number}/assign")
@require_roles([UserRoleType.MANAGER])
async def assign_conversation(
    phone_number: str,
    assignment_data: Dict[str, Any],
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Assign conversation to a manager.
    
    Request body:
    {
        "assignee_id": 123
    }
    """
    try:
        success = await sms_conversation_service.assign_conversation(
            phone_number=phone_number,
            assignee_id=assignment_data["assignee_id"],
            manager_id=current_user.user_id
        )
        return {"success": success}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error assigning conversation: {str(e)}"
        )


@router.post("/{phone_number}/tags")
@require_roles([UserRoleType.MANAGER])
async def add_conversation_tags(
    phone_number: str,
    tags_data: Dict[str, Any],
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Add tags to conversation.
    
    Request body:
    {
        "tags": ["billing", "urgent", "technical"]
    }
    """
    try:
        success = await sms_conversation_service.add_conversation_tags(
            phone_number=phone_number,
            tags=tags_data["tags"],
            manager_id=current_user.user_id
        )
        return {"success": success}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error adding tags: {str(e)}"
        )


@router.get("/statistics")
@require_roles([UserRoleType.MANAGER])
async def get_conversation_statistics(
    current_user: User = Depends(get_current_user),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None)
) -> Dict[str, Any]:
    """
    Get conversation statistics for dashboard.
    
    Includes:
    - Total/active conversations
    - Response time metrics
    - Status breakdown
    - Manager activity
    """
    try:
        date_range = None
        if start_date and end_date:
            date_range = (start_date, end_date)
        
        return await sms_conversation_service.get_conversation_statistics(
            manager_id=current_user.user_id,
            date_range=date_range
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving statistics: {str(e)}"
        )


@router.get("/{phone_number}/export")
@require_roles([UserRoleType.MANAGER])
async def export_conversation(
    phone_number: str,
    current_user: User = Depends(get_current_user),
    format: str = Query("json", pattern="^(json|csv|pdf)$")
) -> Dict[str, Any]:
    """
    Export conversation history.
    
    Formats: json, csv, pdf
    """
    try:
        return await sms_conversation_service.export_conversation(
            phone_number=phone_number,
            manager_id=current_user.user_id,
            format=format
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error exporting conversation: {str(e)}"
        )