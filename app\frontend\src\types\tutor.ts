export interface Tutor {
  tutor_id: number;
  user_id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  profile_picture?: string;
  bio?: string;
  specialties?: string[];
  average_rating?: number;
  total_sessions?: number;
  hourly_rate?: number;
  availability_status?: 'available' | 'limited' | 'busy';
  experience_years?: number;
  education?: string;
  languages?: string[];
  service_types?: string[];
  next_available?: string;
  verification_status?: 'pending' | 'verified' | 'rejected';
  created_at?: string;
  updated_at?: string;
}

export interface TutorLocation extends Tutor {
  lat: number;
  lng: number;
  distance_km?: number;
  postal_code?: string;
  city?: string;
  province?: string;
}

export interface TutorServiceRate {
  rate_id: number;
  tutor_id: number;
  service_type: 'online' | 'in_person' | 'library' | 'hybrid';
  subject_area: string;
  hourly_rate: number;
  group_rate?: number;
  is_active: boolean;
}

export interface TutorAvailability {
  availability_id: number;
  tutor_id: number;
  day_of_week: number; // 0-6 (Sunday-Saturday)
  start_time: string; // HH:MM format
  end_time: string; // HH:MM format
  is_available: boolean;
}

export interface TutorFilters {
  subjectAreas: string[];
  serviceTypes: string[];
  maxDistance: number;
  minRating: number;
  maxHourlyRate: number;
  availability: 'all' | 'available' | 'limited';
}