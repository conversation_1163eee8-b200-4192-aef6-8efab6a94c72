import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';
import { 
  Radio, Send, Users, Filter, Calendar, Clock,
  AlertCircle, CheckCircle, XCircle, BarChart3,
  FileText, Plus, Eye, Pause, Play
} from 'lucide-react';

interface Broadcast {
  id: string;
  name: string;
  message: string;
  targetAudience: {
    type: 'all' | 'clients' | 'tutors' | 'custom';
    filters?: {
      location?: string[];
      subject?: string[];
      status?: string[];
    };
    count: number;
  };
  scheduledAt?: Date;
  sentAt?: Date;
  status: 'draft' | 'scheduled' | 'sending' | 'sent' | 'failed' | 'paused';
  stats?: {
    sent: number;
    delivered: number;
    failed: number;
    responses: number;
  };
  createdBy: string;
  createdAt: Date;
}

// Mock data
const mockBroadcasts: Broadcast[] = [
  {
    id: '1',
    name: 'Summer Program Announcement',
    message: 'Exciting news! Our summer tutoring program is now open for registration. Early bird discount available until June 30th!',
    targetAudience: {
      type: 'all',
      count: 450,
    },
    sentAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2), // 2 days ago
    status: 'sent',
    stats: {
      sent: 450,
      delivered: 442,
      failed: 8,
      responses: 67,
    },
    createdBy: 'Admin',
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3),
  },
  {
    id: '2',
    name: 'Tutor Training Reminder',
    message: 'Reminder: Mandatory training session this Saturday at 10 AM. Please confirm your attendance.',
    targetAudience: {
      type: 'tutors',
      count: 78,
    },
    scheduledAt: new Date(Date.now() + 1000 * 60 * 60 * 24), // Tomorrow
    status: 'scheduled',
    createdBy: 'Admin',
    createdAt: new Date(Date.now() - 1000 * 60 * 60),
  },
  {
    id: '3',
    name: 'Math Students - Special Offer',
    message: 'Special offer for math students! Get 20% off your next package when you refer a friend.',
    targetAudience: {
      type: 'custom',
      filters: {
        subject: ['Mathematics'],
      },
      count: 156,
    },
    status: 'draft',
    createdBy: 'Marketing',
    createdAt: new Date(),
  },
];

const Broadcasts: React.FC = () => {
  const { t } = useTranslation();
  const [broadcasts, setBroadcasts] = useState<Broadcast[]>(mockBroadcasts);
  const [selectedBroadcast, setSelectedBroadcast] = useState<Broadcast | null>(null);
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [isCreating, setIsCreating] = useState(false);

  // Form state for new broadcast
  const [formData, setFormData] = useState({
    name: '',
    message: '',
    targetType: 'all' as const,
    scheduleType: 'now' as const,
    scheduledDate: '',
    scheduledTime: '',
  });

  const filteredBroadcasts = broadcasts.filter(broadcast => {
    if (filterStatus === 'all') return true;
    return broadcast.status === filterStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent': return 'text-accent-green';
      case 'scheduled': return 'text-accent-red';
      case 'sending': return 'text-accent-orange';
      case 'failed': return 'text-accent-red';
      case 'paused': return 'text-primary-500';
      default: return 'text-text-secondary';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent': return <CheckCircle className="w-4 h-4" />;
      case 'scheduled': return <Clock className="w-4 h-4" />;
      case 'sending': return <Radio className="w-4 h-4 animate-pulse" />;
      case 'failed': return <XCircle className="w-4 h-4" />;
      case 'paused': return <Pause className="w-4 h-4" />;
      default: return <FileText className="w-4 h-4" />;
    }
  };

  const handleSendBroadcast = () => {
    if (!formData.name || !formData.message) return;

    const newBroadcast: Broadcast = {
      id: Date.now().toString(),
      name: formData.name,
      message: formData.message,
      targetAudience: {
        type: formData.targetType,
        count: formData.targetType === 'all' ? 450 : formData.targetType === 'clients' ? 248 : 78,
      },
      scheduledAt: formData.scheduleType === 'later' 
        ? new Date(`${formData.scheduledDate} ${formData.scheduledTime}`)
        : undefined,
      status: formData.scheduleType === 'later' ? 'scheduled' : 'sending',
      createdBy: 'Current User',
      createdAt: new Date(),
    };

    setBroadcasts([newBroadcast, ...broadcasts]);
    setIsCreating(false);
    setFormData({
      name: '',
      message: '',
      targetType: 'all',
      scheduleType: 'now',
      scheduledDate: '',
      scheduledTime: '',
    });

    // Simulate sending process
    if (formData.scheduleType === 'now') {
      setTimeout(() => {
        setBroadcasts(prev => 
          prev.map(b => 
            b.id === newBroadcast.id 
              ? { 
                  ...b, 
                  status: 'sent', 
                  sentAt: new Date(),
                  stats: {
                    sent: b.targetAudience.count,
                    delivered: Math.floor(b.targetAudience.count * 0.95),
                    failed: Math.floor(b.targetAudience.count * 0.05),
                    responses: Math.floor(b.targetAudience.count * 0.15),
                  }
                } 
              : b
          )
        );
      }, 3000);
    }
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-text-primary">Broadcast Messages</h1>
            <p className="text-text-secondary mt-1">Send messages to multiple recipients at once</p>
          </div>
          <button
            onClick={() => setIsCreating(true)}
            className="flex items-center gap-2 px-4 py-2 bg-accent-red text-white rounded-soft hover:bg-accent-red-dark transition-colors"
          >
            <Plus className="w-4 h-4" />
            New Broadcast
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-large shadow-soft p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-accent-red bg-opacity-10 rounded-medium">
              <Radio className="w-6 h-6 text-accent-red" />
            </div>
          </div>
          <h3 className="text-2xl font-semibold text-text-primary">12</h3>
          <p className="text-sm text-text-secondary mt-1">Total Broadcasts</p>
        </div>

        <div className="bg-white rounded-large shadow-soft p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-accent-green bg-opacity-10 rounded-medium">
              <CheckCircle className="w-6 h-6 text-accent-green" />
            </div>
          </div>
          <h3 className="text-2xl font-semibold text-text-primary">3,456</h3>
          <p className="text-sm text-text-secondary mt-1">Messages Sent</p>
        </div>

        <div className="bg-white rounded-large shadow-soft p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-accent-orange bg-opacity-10 rounded-medium">
              <Users className="w-6 h-6 text-accent-orange" />
            </div>
          </div>
          <h3 className="text-2xl font-semibold text-text-primary">95.2%</h3>
          <p className="text-sm text-text-secondary mt-1">Delivery Rate</p>
        </div>

        <div className="bg-white rounded-large shadow-soft p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-purple-500 bg-opacity-10 rounded-medium">
              <BarChart3 className="w-6 h-6 text-purple-500" />
            </div>
          </div>
          <h3 className="text-2xl font-semibold text-text-primary">18%</h3>
          <p className="text-sm text-text-secondary mt-1">Response Rate</p>
        </div>
      </div>

      {/* Filter */}
      <div className="bg-white rounded-large shadow-soft p-4 mb-6">
        <div className="flex items-center gap-4">
          <Filter className="w-4 h-4 text-text-secondary" />
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="px-3 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
          >
            <option value="all">All Status</option>
            <option value="draft">Draft</option>
            <option value="scheduled">Scheduled</option>
            <option value="sending">Sending</option>
            <option value="sent">Sent</option>
            <option value="failed">Failed</option>
          </select>
        </div>
      </div>

      {/* Broadcast List */}
      <div className="space-y-4">
        {filteredBroadcasts.map(broadcast => (
          <div
            key={broadcast.id}
            className="bg-white rounded-large shadow-soft p-6 hover:shadow-medium transition-shadow"
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <h3 className="font-semibold text-text-primary">{broadcast.name}</h3>
                  <span className={`flex items-center gap-1 text-sm ${getStatusColor(broadcast.status)}`}>
                    {getStatusIcon(broadcast.status)}
                    {broadcast.status.charAt(0).toUpperCase() + broadcast.status.slice(1)}
                  </span>
                </div>
                
                <p className="text-sm text-text-secondary mb-3">{broadcast.message}</p>

                <div className="flex flex-wrap items-center gap-4 text-sm text-text-secondary">
                  <span className="flex items-center gap-1">
                    <Users className="w-4 h-4" />
                    {broadcast.targetAudience.count} recipients
                    {broadcast.targetAudience.type !== 'all' && ` (${broadcast.targetAudience.type})`}
                  </span>
                  {broadcast.sentAt && (
                    <span className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      Sent {format(broadcast.sentAt, 'MMM dd, yyyy HH:mm')}
                    </span>
                  )}
                  {broadcast.scheduledAt && (
                    <span className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      Scheduled for {format(broadcast.scheduledAt, 'MMM dd, yyyy HH:mm')}
                    </span>
                  )}
                  <span>By {broadcast.createdBy}</span>
                </div>

                {broadcast.stats && (
                  <div className="mt-4 grid grid-cols-4 gap-4">
                    <div className="text-center p-3 bg-background-secondary rounded-medium">
                      <p className="text-lg font-semibold text-text-primary">{broadcast.stats.sent}</p>
                      <p className="text-xs text-text-secondary">Sent</p>
                    </div>
                    <div className="text-center p-3 bg-background-secondary rounded-medium">
                      <p className="text-lg font-semibold text-accent-green">{broadcast.stats.delivered}</p>
                      <p className="text-xs text-text-secondary">Delivered</p>
                    </div>
                    <div className="text-center p-3 bg-background-secondary rounded-medium">
                      <p className="text-lg font-semibold text-accent-red">{broadcast.stats.failed}</p>
                      <p className="text-xs text-text-secondary">Failed</p>
                    </div>
                    <div className="text-center p-3 bg-background-secondary rounded-medium">
                      <p className="text-lg font-semibold text-accent-red">{broadcast.stats.responses}</p>
                      <p className="text-xs text-text-secondary">Responses</p>
                    </div>
                  </div>
                )}
              </div>

              <div className="flex items-center gap-2 ml-4">
                <button
                  onClick={() => setSelectedBroadcast(broadcast)}
                  className="p-2 hover:bg-background-secondary rounded-medium transition-colors"
                >
                  <Eye className="w-4 h-4 text-text-secondary" />
                </button>
                {broadcast.status === 'sending' && (
                  <button className="p-2 hover:bg-background-secondary rounded-medium transition-colors">
                    <Pause className="w-4 h-4 text-text-secondary" />
                  </button>
                )}
                {broadcast.status === 'paused' && (
                  <button className="p-2 hover:bg-background-secondary rounded-medium transition-colors">
                    <Play className="w-4 h-4 text-text-secondary" />
                  </button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Create Broadcast Modal */}
      {isCreating && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-large shadow-soft max-w-2xl w-full max-h-[80vh] overflow-y-auto">
            <div className="p-6">
              <h2 className="text-xl font-semibold text-text-primary mb-6">Create New Broadcast</h2>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Broadcast Name
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                    placeholder="e.g., Summer Program Announcement"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Message
                  </label>
                  <textarea
                    value={formData.message}
                    onChange={(e) => setFormData({ ...formData, message: e.target.value })}
                    className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                    rows={4}
                    placeholder="Type your message here..."
                  />
                  <p className="text-xs text-text-secondary mt-1">
                    {formData.message.length}/160 characters (1 SMS)
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Target Audience
                  </label>
                  <select
                    value={formData.targetType}
                    onChange={(e) => setFormData({ ...formData, targetType: e.target.value as any })}
                    className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                  >
                    <option value="all">All Users (450)</option>
                    <option value="clients">All Clients (248)</option>
                    <option value="tutors">All Tutors (78)</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    When to Send
                  </label>
                  <div className="space-y-2">
                    <label className="flex items-center gap-2">
                      <input
                        type="radio"
                        name="scheduleType"
                        value="now"
                        checked={formData.scheduleType === 'now'}
                        onChange={(e) => setFormData({ ...formData, scheduleType: 'now' })}
                      />
                      <span className="text-sm">Send immediately</span>
                    </label>
                    <label className="flex items-center gap-2">
                      <input
                        type="radio"
                        name="scheduleType"
                        value="later"
                        checked={formData.scheduleType === 'later'}
                        onChange={(e) => setFormData({ ...formData, scheduleType: 'later' })}
                      />
                      <span className="text-sm">Schedule for later</span>
                    </label>
                  </div>

                  {formData.scheduleType === 'later' && (
                    <div className="mt-3 grid grid-cols-2 gap-3">
                      <input
                        type="date"
                        value={formData.scheduledDate}
                        onChange={(e) => setFormData({ ...formData, scheduledDate: e.target.value })}
                        className="px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                      />
                      <input
                        type="time"
                        value={formData.scheduledTime}
                        onChange={(e) => setFormData({ ...formData, scheduledTime: e.target.value })}
                        className="px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                      />
                    </div>
                  )}
                </div>

                <div className="bg-primary-50 p-4 rounded-medium">
                  <div className="flex items-start gap-2">
                    <AlertCircle className="w-4 h-4 text-accent-orange mt-0.5" />
                    <div className="text-sm text-text-secondary">
                      <p className="font-medium mb-1">Important Notes:</p>
                      <ul className="list-disc list-inside space-y-1">
                        <li>SMS charges apply based on your plan</li>
                        <li>Messages longer than 160 characters will be split</li>
                        <li>Recipients can opt-out by replying STOP</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-end gap-3 pt-4">
                  <button
                    onClick={() => {
                      setIsCreating(false);
                      setFormData({
                        name: '',
                        message: '',
                        targetType: 'all',
                        scheduleType: 'now',
                        scheduledDate: '',
                        scheduledTime: '',
                      });
                    }}
                    className="px-4 py-2 border border-primary-300 rounded-soft hover:bg-background-secondary"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleSendBroadcast}
                    disabled={!formData.name || !formData.message}
                    className={`
                      flex items-center gap-2 px-4 py-2 rounded-soft
                      ${formData.name && formData.message
                        ? 'bg-accent-red text-white hover:bg-accent-red-dark'
                        : 'bg-primary-200 text-text-secondary cursor-not-allowed'
                      }
                    `}
                  >
                    <Send className="w-4 h-4" />
                    {formData.scheduleType === 'now' ? 'Send Now' : 'Schedule'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Broadcasts;