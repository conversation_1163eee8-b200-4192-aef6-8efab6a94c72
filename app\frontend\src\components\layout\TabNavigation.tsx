import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import clsx from 'clsx';

export interface Tab {
  id: string;
  label: string;
  path: string;
  icon?: React.ReactNode;
}

interface TabNavigationProps {
  tabs: Tab[];
  className?: string;
}

export const TabNavigation: React.FC<TabNavigationProps> = ({ tabs, className }) => {
  const location = useLocation();

  return (
    <div className={clsx('border-b border-primary-200', className)}>
      <nav className="flex space-x-8 px-6" aria-label="Tabs">
        {tabs.map((tab) => {
          const isActive = location.pathname === tab.path;
          
          return (
            <Link
              key={tab.id}
              to={tab.path}
              className={clsx(
                'flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors',
                isActive
                  ? 'border-accent-red text-accent-red'
                  : 'border-transparent text-text-secondary hover:text-text-primary hover:border-primary-300'
              )}
            >
              {tab.icon && <span className="w-4 h-4">{tab.icon}</span>}
              {tab.label}
            </Link>
          );
        })}
      </nav>
    </div>
  );
};