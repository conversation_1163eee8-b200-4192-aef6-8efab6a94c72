# How to Restart Railway Service to Fix Connection Issues

## Quick Restart Steps

1. **Go to Railway Dashboard**
   - Navigate to https://railway.app/dashboard
   - Select your project: **tutoraide-production**

2. **Restart the Service**
   - Click on your **web** service
   - Click the three dots menu (⋮) in the top right
   - Select **Restart**
   - Wait for the service to restart (usually takes 1-2 minutes)

3. **Verify Restart**
   - Check the deployment logs to ensure it started successfully
   - Look for the startup messages:
     ```
     === APPLICATION STARTUP ===
     Environment: production
     ```

4. **Test Password Reset Again**
   ```bash
   curl -X POST "https://tutoraide-production.up.railway.app/api/v1/auth/password/reset-request" \
     -H "Content-Type: application/json" \
     -d '{"email": "<EMAIL>"}'
   ```

## Why This Should Work

- Restarting clears the connection pool
- Forces new connections to the database
- Ensures the app sees the current database state
- Resolves any stale connection issues

## If Restart Doesn't Work

Try adding search_path to your DATABASE_URL in Railway:

1. Go to Railway Variables tab
2. Edit DATABASE_URL
3. Add `?options=-csearch_path=public` to the end
4. Example:
   ```
   *****************************************/railway?options=-csearch_path=public
   ```

## Alternative Quick Fix

You can also trigger a redeploy by:
1. Making a small change (like adding a comment)
2. Pushing to git
3. Railway will automatically redeploy

This achieves the same result as a restart.