[project]
name = "tutoraide"
version = "0.1.0"
description = "TutorAide - Professional Tutoring Management Platform"
requires-python = ">=3.12"
dependencies = [
    "fastapi==0.104.1",
    "uvicorn[standard]==0.24.0",
    "pydantic[email,dotenv]==2.5.0",
    "pydantic-settings==2.1.0",
    "asyncpg==0.29.0",
    "python-jose[cryptography]==3.3.0",
    "passlib[bcrypt]==1.7.4",
    "python-multipart==0.0.6",
    "jinja2==3.1.2",
    "python-decouple==3.8",
    "aiofiles==23.2.1",
    "httpx==0.25.2",
    "redis==5.0.1",
    "celery==5.3.4",
    "stripe==7.8.0",
    "twilio==8.10.3",
    "onesignal-sdk>=1.0.0",
    "googlemaps==4.10.0",
    "python-magic==0.4.27",
    "Pillow==10.1.0",
    "python-slugify==8.0.1",
    "email-validator==2.1.0",
    "aiohttp==3.9.1",
    "sentry-sdk>=2.32.0",
]

[project.optional-dependencies]
dev = [
    "black==23.11.0",
    "ruff==0.1.9",
    "mypy==1.7.1",
    "pre-commit==3.5.0",
]


[tool.ruff]
line-length = 100
target-version = "py312"
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = []
fixable = ["ALL"]
unfixable = []
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
]

[tool.black]
line-length = 100
target-version = ['py312']
include = '\.pyi?$'


[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
ignore_missing_imports = true
strict = true

