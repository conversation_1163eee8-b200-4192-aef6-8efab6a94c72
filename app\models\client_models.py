"""
Client domain models for client profiles, dependants, and learning needs.
"""

from datetime import datetime, date
from typing import List, Optional, Dict, Any
from decimal import Decimal
from pydantic import BaseModel, Field, field_validator, ConfigDict
from typing import Annotated
from pydantic.types import constr
import re

# Enhanced validation imports
from app.core.validation import DataValidator, InputSanitizer

# Use a simple string pattern instead of EmailStr to avoid email-validator dependency
EmailStr = Annotated[str, constr(pattern=r'^[\w\.-]+@[\w\.-]+\.\w+$')]

# Canadian phone number pattern
PhoneStr = Annotated[str, constr(pattern=r'^(?:\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})$')]
# Canadian postal code pattern
PostalCodeStr = Annotated[str, constr(pattern=r'^[A-Za-z]\d[A-Za-z]\s?\d[A-Za-z]\d$')]

from app.models.base import (
    BaseEntity,
    IdentifiedEntity,
    SearchFilters,
    AddressModel,
    ContactModel
)


class ClientProfile(IdentifiedEntity):
    """Client profile model."""
    
    client_id: int = Field(..., description="Unique client identifier")
    user_id: int = Field(..., description="Associated user account")
    first_name: str = Field(..., description="Client first name")
    last_name: str = Field(..., description="Client last name")
    phone: str = Field(..., description="Client phone number")
    address: Dict[str, Any] = Field(..., description="Client address")
    emergency_contact: Optional[Dict[str, Any]] = Field(None, description="Emergency contact information")
    
    @field_validator('first_name', 'last_name')
    @classmethod
    def validate_names(cls, v: str) -> str:
        """Validate name fields with enhanced security."""
        return DataValidator.validate_name(v, "name")
    
    @field_validator('phone')
    @classmethod
    def validate_phone(cls, v: str) -> str:
        """Validate phone number format with enhanced security."""
        # Sanitize input first
        v = InputSanitizer.sanitize_text(v)
        return InputSanitizer.normalize_phone(v)


class Dependant(IdentifiedEntity):
    """Dependant model for client's children/students."""
    
    dependant_id: int = Field(..., description="Unique dependant identifier")
    primary_client_id: int = Field(..., description="Primary client (parent/guardian)")
    secondary_client_id: Optional[int] = Field(None, description="Secondary client (separated families)")
    first_name: str = Field(..., description="Dependant first name")
    last_name: str = Field(..., description="Dependant last name")
    birth_date: date = Field(..., description="Date of birth")
    relationship_type: str = Field(..., description="Relationship to client")
    
    @field_validator('first_name', 'last_name')
    @classmethod
    def validate_names(cls, v: str) -> str:
        """Validate dependant name fields with enhanced security."""
        return DataValidator.validate_name(v, "dependant name")
    
    @field_validator('birth_date')
    @classmethod
    def validate_birth_date(cls, v: date) -> date:
        """Validate birth date with enhanced security."""
        return DataValidator.validate_date_field(v, "birth_date", allow_future=False)
    
    @field_validator('relationship_type')
    @classmethod
    def validate_relationship_type(cls, v: str) -> str:
        """Validate relationship type with enhanced security."""
        valid_types = ['parent', 'guardian', 'other']
        return DataValidator.validate_choice_field(v, "relationship_type", valid_types)


class LearningNeeds(IdentifiedEntity):
    """Learning needs assessment for dependants."""
    
    learning_needs_id: int = Field(..., description="Unique learning needs identifier")
    dependant_id: int = Field(..., description="Associated dependant")
    subject_area: str = Field(..., description="Subject area")
    current_level: str = Field(..., description="Current academic level")
    goals: str = Field(..., description="Learning goals")
    challenges: Optional[str] = Field(None, description="Learning challenges")
    special_requirements: Optional[str] = Field(None, description="Special requirements")
    assessment_date: date = Field(..., description="Assessment date")


class ClientSubscription(IdentifiedEntity):
    """Client subscription model for pre-paid hours."""
    
    subscription_id: int = Field(..., description="Unique subscription identifier")
    client_id: int = Field(..., description="Associated client")
    total_hours: Decimal = Field(..., description="Total purchased hours")
    remaining_hours: Decimal = Field(..., description="Remaining hours")
    price_per_hour: Decimal = Field(..., description="Price per hour in CAD")
    currency: str = Field(default="CAD", description="Currency")
    purchase_date: date = Field(..., description="Purchase date")
    expiry_date: Optional[date] = Field(None, description="Subscription expiry date")
    is_active: bool = Field(True, description="Whether subscription is active")
    
    @field_validator('remaining_hours')
    @classmethod
    def validate_remaining_hours(cls, v: Decimal, info) -> Decimal:
        """Validate remaining hours doesn't exceed total."""
        if 'total_hours' in info.data and v > info.data['total_hours']:
            raise ValueError('Remaining hours cannot exceed total hours')
        return v
    
    @field_validator('price_per_hour')
    @classmethod
    def validate_price_per_hour(cls, v: Decimal) -> Decimal:
        """Validate price is positive."""
        if v <= 0:
            raise ValueError('Price per hour must be positive')
        return v


class ClientMessage(IdentifiedEntity):
    """Client message model for communication tracking."""
    
    message_id: int = Field(..., description="Unique message identifier")
    client_id: int = Field(..., description="Associated client")
    sender_role: str = Field(..., description="Sender role (client/manager)")
    message_text: str = Field(..., description="Message content")
    message_type: str = Field(..., description="Message type (sms/in_app)")
    thread_id: Optional[str] = Field(None, description="SMS thread identifier")
    sent_at: datetime = Field(..., description="When message was sent")
    delivered_at: Optional[datetime] = Field(None, description="When message was delivered")
    read_at: Optional[datetime] = Field(None, description="When message was read")
    
    @field_validator('sender_role')
    @classmethod
    def validate_sender_role(cls, v: str) -> str:
        """Validate sender role."""
        valid_roles = {'client', 'manager', 'tutor'}
        if v.lower() not in valid_roles:
            raise ValueError(f'Sender role must be one of: {", ".join(valid_roles)}')
        return v.lower()
    
    @field_validator('message_type')
    @classmethod
    def validate_message_type(cls, v: str) -> str:
        """Validate message type."""
        valid_types = {'sms', 'in_app', 'email'}
        if v.lower() not in valid_types:
            raise ValueError(f'Message type must be one of: {", ".join(valid_types)}')
        return v.lower()


# ============================================
# Create/Update Schemas
# ============================================

class ClientProfileCreate(BaseModel):
    """Schema for creating client profile."""
    
    model_config = ConfigDict(from_attributes=True)
    
    user_id: int = Field(..., description="Associated user account")
    first_name: str = Field(..., min_length=1, description="Client first name")
    last_name: str = Field(..., min_length=1, description="Client last name")
    phone: str = Field(..., description="Client phone number")
    address: AddressModel = Field(..., description="Client address")
    emergency_contact: Optional[ContactModel] = Field(None, description="Emergency contact")


class ClientProfileUpdate(BaseModel):
    """Schema for updating client profile."""
    
    model_config = ConfigDict(from_attributes=True)
    
    first_name: Optional[str] = Field(None, min_length=1, description="Client first name")
    last_name: Optional[str] = Field(None, min_length=1, description="Client last name")
    phone: Optional[str] = Field(None, description="Client phone number")
    address: Optional[AddressModel] = Field(None, description="Client address")
    emergency_contact: Optional[ContactModel] = Field(None, description="Emergency contact")


class DependantCreate(BaseModel):
    """Schema for creating dependant."""
    
    model_config = ConfigDict(from_attributes=True)
    
    primary_client_id: int = Field(..., description="Primary client")
    secondary_client_id: Optional[int] = Field(None, description="Secondary client")
    first_name: str = Field(..., min_length=1, description="Dependant first name")
    last_name: str = Field(..., min_length=1, description="Dependant last name")
    birth_date: date = Field(..., description="Date of birth")
    relationship_type: str = Field(..., description="Relationship to client")
    
    @field_validator('birth_date')
    @classmethod
    def validate_birth_date(cls, v: date) -> date:
        """Validate birth date is not in the future."""
        if v > date.today():
            raise ValueError('Birth date cannot be in the future')
        return v


class DependantUpdate(BaseModel):
    """Schema for updating dependant."""
    
    model_config = ConfigDict(from_attributes=True)
    
    first_name: Optional[str] = Field(None, min_length=1, description="Dependant first name")
    last_name: Optional[str] = Field(None, min_length=1, description="Dependant last name")
    birth_date: Optional[date] = Field(None, description="Date of birth")
    relationship_type: Optional[str] = Field(None, description="Relationship to client")
    secondary_client_id: Optional[int] = Field(None, description="Secondary client")


class LearningNeedsCreate(BaseModel):
    """Schema for creating learning needs assessment."""
    
    model_config = ConfigDict(from_attributes=True)
    
    dependant_id: int = Field(..., description="Associated dependant")
    subject_area: str = Field(..., description="Subject area")
    current_level: str = Field(..., description="Current academic level")
    goals: str = Field(..., min_length=10, description="Learning goals")
    challenges: Optional[str] = Field(None, description="Learning challenges")
    special_requirements: Optional[str] = Field(None, description="Special requirements")


class LearningNeedsUpdate(BaseModel):
    """Schema for updating learning needs."""
    
    model_config = ConfigDict(from_attributes=True)
    
    subject_area: Optional[str] = Field(None, description="Subject area")
    current_level: Optional[str] = Field(None, description="Current academic level")
    goals: Optional[str] = Field(None, min_length=10, description="Learning goals")
    challenges: Optional[str] = Field(None, description="Learning challenges")
    special_requirements: Optional[str] = Field(None, description="Special requirements")


class SubscriptionCreate(BaseModel):
    """Schema for creating subscription."""
    
    model_config = ConfigDict(from_attributes=True)
    
    client_id: int = Field(..., description="Associated client")
    total_hours: Decimal = Field(..., gt=0, description="Total purchased hours")
    price_per_hour: Decimal = Field(..., gt=0, description="Price per hour")
    expiry_date: Optional[date] = Field(None, description="Subscription expiry date")


class SubscriptionUpdate(BaseModel):
    """Schema for updating subscription."""
    
    model_config = ConfigDict(from_attributes=True)
    
    remaining_hours: Optional[Decimal] = Field(None, ge=0, description="Remaining hours")
    is_active: Optional[bool] = Field(None, description="Whether subscription is active")


class MessageCreate(BaseModel):
    """Schema for creating message."""
    
    model_config = ConfigDict(from_attributes=True)
    
    client_id: int = Field(..., description="Associated client")
    sender_role: str = Field(..., description="Sender role")
    message_text: str = Field(..., min_length=1, description="Message content")
    message_type: str = Field(..., description="Message type")
    thread_id: Optional[str] = Field(None, description="SMS thread identifier")


# ============================================
# Response Schemas
# ============================================

class ClientProfileResponse(BaseModel):
    """Client profile response schema."""
    
    model_config = ConfigDict(from_attributes=True)
    
    client_id: int
    user_id: int
    first_name: str
    last_name: str
    phone: str
    address: Dict[str, Any]
    emergency_contact: Optional[Dict[str, Any]]
    created_at: datetime
    updated_at: datetime


class DependantResponse(BaseModel):
    """Dependant response schema."""
    
    model_config = ConfigDict(from_attributes=True)
    
    dependant_id: int
    primary_client_id: int
    secondary_client_id: Optional[int]
    first_name: str
    last_name: str
    birth_date: date
    relationship_type: str
    age: int
    created_at: datetime
    updated_at: datetime


class LearningNeedsResponse(BaseModel):
    """Learning needs response schema."""
    
    model_config = ConfigDict(from_attributes=True)
    
    learning_needs_id: int
    dependant_id: int
    subject_area: str
    current_level: str
    goals: str
    challenges: Optional[str]
    special_requirements: Optional[str]
    assessment_date: date
    created_at: datetime
    updated_at: datetime


class SubscriptionResponse(BaseModel):
    """Subscription response schema."""
    
    model_config = ConfigDict(from_attributes=True)
    
    subscription_id: int
    client_id: int
    total_hours: Decimal
    remaining_hours: Decimal
    price_per_hour: Decimal
    currency: str
    purchase_date: date
    expiry_date: Optional[date]
    is_active: bool
    utilization_percentage: float
    created_at: datetime
    updated_at: datetime


class MessageResponse(BaseModel):
    """Message response schema."""
    
    model_config = ConfigDict(from_attributes=True)
    
    message_id: int
    client_id: int
    sender_role: str
    message_text: str
    message_type: str
    thread_id: Optional[str]
    sent_at: datetime
    delivered_at: Optional[datetime]
    read_at: Optional[datetime]
    is_read: bool


class ClientSearchFilters(SearchFilters):
    """Client-specific search filters."""
    
    name: Optional[str] = Field(None, description="Search by name")
    phone: Optional[str] = Field(None, description="Search by phone")
    postal_code: Optional[str] = Field(None, description="Search by postal code")
    has_active_subscription: Optional[bool] = Field(None, description="Filter by subscription status")