import api from './api';

// Consent types and enums
export enum ConsentType {
  TERMS_OF_SERVICE = 'terms_of_service',
  PRIVACY_POLICY = 'privacy_policy',
  TUTOR_EMPLOYMENT_AGREEMENT = 'tutor_employment_agreement',
  MARKETING_COMMUNICATIONS = 'marketing_communications',
  USAGE_ANALYTICS = 'usage_analytics'
}

export enum ConsentLevel {
  LEVEL_1_MANDATORY = 'level_1_mandatory',
  LEVEL_2_OPTIONAL = 'level_2_optional'
}

export enum ConsentStatus {
  NOT_PRESENTED = 'not_presented',
  ACCEPTED = 'accepted',
  WITHDRAWN = 'withdrawn',
  EXPIRED = 'expired'
}

// Consent interfaces
export interface ConsentDocument {
  consent_type: ConsentType;
  version: string;
  level: ConsentLevel;
  title: string;
  content: string;
  summary: string;
  effective_date: string;
  language: string;
  is_active: boolean;
  requires_signature: boolean;
  retention_days?: number;
}

export interface ConsentAcceptanceRequest {
  consent_type: ConsentType;
  version: string;
  acceptance_method: 'click' | 'signature' | 'verbal';
  ip_address?: string;
  user_agent?: string;
  signature_data?: string;
}

export interface ConsentWithdrawalRequest {
  consent_type: ConsentType;
  reason?: string;
  feedback?: string;
}

export interface ConsentRecord {
  user_id: number;
  consent_type: ConsentType;
  version: string;
  status: ConsentStatus;
  accepted_at?: string;
  withdrawn_at?: string;
  expires_at?: string;
  ip_address?: string;
  user_agent?: string;
}

export interface ConsentStatusResponse {
  user_id: number;
  consents: ConsentRecord[];
  missing_mandatory: ConsentType[];
  can_access_platform: boolean;
}

export interface ConsentHistoryResponse {
  user_id: number;
  history: Array<{
    consent_type: ConsentType;
    version: string;
    action: 'accepted' | 'withdrawn' | 'expired';
    timestamp: string;
    ip_address?: string;
  }>;
}

export interface ConsentValidationResult {
  is_valid: boolean;
  missing_consents: ConsentType[];
  expired_consents: ConsentType[];
  can_proceed: boolean;
  redirect_to?: string;
}

export interface BulkConsentRequest {
  consents: ConsentAcceptanceRequest[];
}

export interface BulkConsentResponse {
  accepted: ConsentType[];
  failed: Array<{
    consent_type: ConsentType;
    error: string;
  }>;
  total_accepted: number;
  total_failed: number;
}

export interface ConsentSummaryResponse {
  mandatory_consents: Array<{
    consent_type: ConsentType;
    title: string;
    status: ConsentStatus;
    version: string;
  }>;
  optional_consents: Array<{
    consent_type: ConsentType;
    title: string;
    status: ConsentStatus;
    version: string;
  }>;
  last_updated: string;
}

class ConsentService {
  /**
   * Get available consent documents
   */
  async getConsentDocuments(params?: {
    language?: string;
    consent_type?: ConsentType;
    level?: ConsentLevel;
  }): Promise<ConsentDocument[]> {
    try {
      const response = await api.get<ConsentDocument[]>('/consent/documents', { params });
      return response.data;
    } catch (error: any) {
      console.error('Error fetching consent documents:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch consent documents');
    }
  }

  /**
   * Get specific consent document
   */
  async getConsentDocument(consentType: ConsentType, language: string = 'en'): Promise<ConsentDocument> {
    try {
      const documents = await this.getConsentDocuments({ 
        consent_type: consentType, 
        language 
      });
      
      if (documents.length === 0) {
        throw new Error('Consent document not found');
      }
      
      return documents[0];
    } catch (error) {
      throw error;
    }
  }

  /**
   * Accept a consent
   */
  async acceptConsent(request: ConsentAcceptanceRequest): Promise<ConsentRecord> {
    try {
      // Add browser info if not provided
      if (!request.user_agent && typeof window !== 'undefined') {
        request.user_agent = window.navigator.userAgent;
      }

      const response = await api.post<ConsentRecord>('/consent/accept', request);
      return response.data;
    } catch (error: any) {
      console.error('Error accepting consent:', error);
      throw new Error(error.response?.data?.detail || 'Failed to accept consent');
    }
  }

  /**
   * Accept multiple consents at once
   */
  async acceptBulkConsents(request: BulkConsentRequest): Promise<BulkConsentResponse> {
    try {
      // Add browser info to all consents if not provided
      if (typeof window !== 'undefined') {
        request.consents = request.consents.map(consent => ({
          ...consent,
          user_agent: consent.user_agent || window.navigator.userAgent
        }));
      }

      const response = await api.post<BulkConsentResponse>('/consent/accept-bulk', request);
      return response.data;
    } catch (error: any) {
      console.error('Error accepting bulk consents:', error);
      throw new Error(error.response?.data?.detail || 'Failed to accept consents');
    }
  }

  /**
   * Withdraw a consent
   */
  async withdrawConsent(request: ConsentWithdrawalRequest): Promise<ConsentRecord> {
    try {
      const response = await api.post<ConsentRecord>('/consent/withdraw', request);
      return response.data;
    } catch (error: any) {
      console.error('Error withdrawing consent:', error);
      throw new Error(error.response?.data?.detail || 'Failed to withdraw consent');
    }
  }

  /**
   * Get user's consent status
   */
  async getConsentStatus(userId?: number): Promise<ConsentStatusResponse> {
    try {
      const params = userId ? { user_id: userId } : {};
      const response = await api.get<ConsentStatusResponse>('/consent/status', { params });
      return response.data;
    } catch (error: any) {
      console.error('Error fetching consent status:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch consent status');
    }
  }

  /**
   * Get consent summary
   */
  async getConsentSummary(): Promise<ConsentSummaryResponse> {
    try {
      const response = await api.get<ConsentSummaryResponse>('/consent/summary');
      return response.data;
    } catch (error: any) {
      console.error('Error fetching consent summary:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch consent summary');
    }
  }

  /**
   * Get consent history
   */
  async getConsentHistory(userId?: number): Promise<ConsentHistoryResponse> {
    try {
      const params = userId ? { user_id: userId } : {};
      const response = await api.get<ConsentHistoryResponse>('/consent/history', { params });
      return response.data;
    } catch (error: any) {
      console.error('Error fetching consent history:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch consent history');
    }
  }

  /**
   * Validate user's consents
   */
  async validateConsents(): Promise<ConsentValidationResult> {
    try {
      const response = await api.get<ConsentValidationResult>('/consent/validate');
      return response.data;
    } catch (error: any) {
      console.error('Error validating consents:', error);
      throw new Error(error.response?.data?.detail || 'Failed to validate consents');
    }
  }

  /**
   * Check if user has accepted mandatory consents
   */
  async hasAcceptedMandatoryConsents(): Promise<boolean> {
    try {
      const status = await this.getConsentStatus();
      return status.can_access_platform;
    } catch (error) {
      console.error('Error checking mandatory consents:', error);
      return false;
    }
  }

  /**
   * Get mandatory consent types for a user role
   */
  getMandatoryConsentsForRole(isTutor: boolean): ConsentType[] {
    const mandatory = [
      ConsentType.TERMS_OF_SERVICE,
      ConsentType.PRIVACY_POLICY
    ];

    if (isTutor) {
      mandatory.push(ConsentType.TUTOR_EMPLOYMENT_AGREEMENT);
    }

    return mandatory;
  }

  /**
   * Format consent type for display
   */
  formatConsentType(consentType: ConsentType): string {
    const labels: Record<ConsentType, string> = {
      [ConsentType.TERMS_OF_SERVICE]: 'Terms of Service',
      [ConsentType.PRIVACY_POLICY]: 'Privacy Policy',
      [ConsentType.TUTOR_EMPLOYMENT_AGREEMENT]: 'Tutor Employment Agreement',
      [ConsentType.MARKETING_COMMUNICATIONS]: 'Marketing Communications',
      [ConsentType.USAGE_ANALYTICS]: 'Usage Analytics'
    };

    return labels[consentType] || consentType;
  }

  /**
   * Check if consent is mandatory
   */
  isConsentMandatory(consentType: ConsentType): boolean {
    const mandatoryTypes = [
      ConsentType.TERMS_OF_SERVICE,
      ConsentType.PRIVACY_POLICY,
      ConsentType.TUTOR_EMPLOYMENT_AGREEMENT
    ];

    return mandatoryTypes.includes(consentType);
  }
}

export const consentService = new ConsentService();