import api from './api';
import { 
  UserRoleType, 
  SearchEntityType, 
  SearchSortBy, 
  SearchSortOrder 
} from '../types/auth';

// Search request/response interfaces
export interface UserSearchRequest {
  query?: string;
  entity_type?: SearchEntityType;
  roles?: UserRoleType[];
  is_active?: boolean;
  is_verified?: boolean;
  has_profile?: boolean;
  postal_code?: string;
  radius_km?: number;
  date_from?: string;
  date_to?: string;
  limit?: number;
  offset?: number;
  sort_by?: SearchSortBy;
  sort_order?: SearchSortOrder;
}

export interface UserSearchResult {
  user_id: number;
  email: string;
  first_name: string;
  last_name: string;
  roles: UserRoleType[];
  entity_type: SearchEntityType;
  is_active: boolean;
  is_verified: boolean;
  created_at: string;
  profile_picture?: string;
  postal_code?: string;
  phone_number?: string;
  last_login?: string;
  match_score?: number;
  highlight_fields?: Record<string, string>;
}

export interface UserSearchResponse {
  results: UserSearchResult[];
  total: number;
  limit: number;
  offset: number;
  query: string;
  filters_applied: Record<string, any>;
}

export interface AutocompleteRequest {
  query: string;
  entity_type?: SearchEntityType;
  limit?: number;
}

export interface AutocompleteSuggestion {
  value: string;
  display: string;
  entity_type: SearchEntityType;
  entity_id?: number;
  metadata?: Record<string, any>;
}

export interface AutocompleteResponse {
  suggestions: AutocompleteSuggestion[];
  query: string;
}

// Search enums (matching backend)
export enum SearchEntityTypeEnum {
  ALL = 'all',
  USER = 'user',
  CLIENT = 'client',
  TUTOR = 'tutor',
  DEPENDANT = 'dependant'
}

export enum SearchSortByEnum {
  RELEVANCE = 'relevance',
  NAME = 'name',
  EMAIL = 'email',
  CREATED_AT = 'created_at',
  LAST_LOGIN = 'last_login'
}

export enum SearchSortOrderEnum {
  ASC = 'asc',
  DESC = 'desc'
}

class SearchService {
  /**
   * Search users with advanced filtering and pagination
   */
  async searchUsers(params: UserSearchRequest): Promise<UserSearchResponse> {
    try {
      const response = await api.post<UserSearchResponse>('/search/users', params);
      return response.data;
    } catch (error: any) {
      console.error('Error searching users:', error);
      throw new Error(error.response?.data?.detail || 'Failed to search users');
    }
  }

  /**
   * Get autocomplete suggestions for search
   */
  async getAutocompleteSuggestions(params: AutocompleteRequest): Promise<AutocompleteResponse> {
    try {
      const response = await api.get<AutocompleteResponse>('/search/autocomplete', { params });
      return response.data;
    } catch (error: any) {
      console.error('Error getting autocomplete suggestions:', error);
      throw new Error(error.response?.data?.detail || 'Failed to get suggestions');
    }
  }

  /**
   * Search tutors by subject and location
   */
  async searchTutorsBySubject(params: {
    subject: string;
    postal_code?: string;
    radius_km?: number;
    limit?: number;
    offset?: number;
  }): Promise<UserSearchResponse> {
    try {
      const searchParams: UserSearchRequest = {
        query: params.subject,
        entity_type: SearchEntityTypeEnum.TUTOR as any,
        postal_code: params.postal_code,
        radius_km: params.radius_km,
        limit: params.limit,
        offset: params.offset,
        is_active: true,
        is_verified: true
      };

      return await this.searchUsers(searchParams);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Search clients by name or email
   */
  async searchClients(params: {
    query: string;
    limit?: number;
    offset?: number;
  }): Promise<UserSearchResponse> {
    try {
      const searchParams: UserSearchRequest = {
        query: params.query,
        entity_type: SearchEntityTypeEnum.CLIENT as any,
        limit: params.limit,
        offset: params.offset,
        is_active: true
      };

      return await this.searchUsers(searchParams);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Search dependants by name or parent
   */
  async searchDependants(params: {
    query: string;
    client_id?: number;
    limit?: number;
    offset?: number;
  }): Promise<UserSearchResponse> {
    try {
      const searchParams: UserSearchRequest = {
        query: params.query,
        entity_type: SearchEntityTypeEnum.DEPENDANT as any,
        limit: params.limit,
        offset: params.offset
      };

      // If client_id provided, add it to the query
      if (params.client_id) {
        searchParams.query = `${params.query} parent:${params.client_id}`;
      }

      return await this.searchUsers(searchParams);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Quick search across all entities
   */
  async quickSearch(query: string, limit: number = 10): Promise<UserSearchResponse> {
    try {
      return await this.searchUsers({
        query,
        limit,
        sort_by: SearchSortByEnum.RELEVANCE as any
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Build search filters for UI
   */
  buildSearchFilters(params: UserSearchRequest): string {
    const filters: string[] = [];

    if (params.entity_type && params.entity_type !== SearchEntityTypeEnum.ALL) {
      filters.push(`type:${params.entity_type}`);
    }

    if (params.roles && params.roles.length > 0) {
      filters.push(`roles:${params.roles.join(',')}`);
    }

    if (params.is_active !== undefined) {
      filters.push(`active:${params.is_active}`);
    }

    if (params.is_verified !== undefined) {
      filters.push(`verified:${params.is_verified}`);
    }

    if (params.postal_code) {
      filters.push(`location:${params.postal_code}`);
      if (params.radius_km) {
        filters.push(`radius:${params.radius_km}km`);
      }
    }

    return filters.join(' ');
  }

  /**
   * Parse search query for advanced search syntax
   */
  parseSearchQuery(query: string): {
    searchText: string;
    filters: Record<string, string>;
  } {
    const filters: Record<string, string> = {};
    let searchText = query;

    // Extract filters like "type:tutor" or "active:true"
    const filterPattern = /(\w+):(\S+)/g;
    let match;

    while ((match = filterPattern.exec(query)) !== null) {
      filters[match[1]] = match[2];
      searchText = searchText.replace(match[0], '').trim();
    }

    return { searchText, filters };
  }
}

export const searchService = new SearchService();