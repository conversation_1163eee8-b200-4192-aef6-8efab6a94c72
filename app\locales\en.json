{"common": {"yes": "Yes", "no": "No", "ok": "OK", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "create": "Create", "update": "Update", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "clear": "Clear", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "required": "Required", "optional": "Optional", "page": "Page", "of": "of", "total": "Total", "actions": "Actions", "status": "Status", "name": "Name", "email": "Email", "phone": "Phone", "address": "Address", "date": "Date", "time": "Time", "amount": "Amount", "description": "Description", "notes": "Notes"}, "navigation": {"dashboard": "Dashboard", "users": "Users", "clients": "Clients", "tutors": "Tutors", "dependants": "Dependants", "appointments": "Appointments", "calendar": "Calendar", "billing": "Billing", "invoices": "Invoices", "payments": "Payments", "subscriptions": "Subscriptions", "reports": "Reports", "messages": "Messages", "settings": "Settings", "logout": "Logout", "profile": "Profile"}, "auth": {"login": {"title": "<PERSON><PERSON>", "email_placeholder": "Enter your email", "password_placeholder": "Enter your password", "submit": "Sign In", "forgot_password": "Forgot Password?", "google_signin": "Sign in with Google", "invalid_credentials": "Invalid email or password", "account_locked": "Account locked. Please contact support."}, "register": {"title": "Create Account", "first_name": "First Name", "last_name": "Last Name", "email": "Email Address", "password": "Password", "confirm_password": "Confirm Password", "submit": "Create Account", "password_requirements": "Password must be at least 8 characters with uppercase, lowercase, and number", "passwords_must_match": "Passwords must match", "email_already_exists": "Email address already registered"}, "password_reset": {"title": "Reset Password", "description": "Enter your email address and we'll send you a link to reset your password", "email_placeholder": "Enter your email", "submit": "Send Reset Link", "success": "Password reset link sent to your email", "invalid_email": "Email address not found"}, "password_change": {"title": "Change Password", "current_password": "Current Password", "new_password": "New Password", "confirm_password": "Confirm New Password", "submit": "Update Password", "success": "Password updated successfully", "invalid_current": "Current password is incorrect"}, "consent": {"title": "Consent Required", "level_1_required": "You must accept the mandatory terms to continue", "terms_of_service": "I accept the Terms of Service", "privacy_policy": "I accept the Privacy Policy", "marketing": "I agree to receive marketing communications (optional)", "submit": "Accept and Continue"}}, "users": {"list": {"title": "Users", "add_user": "Add User", "search_placeholder": "Search by name or email", "no_users": "No users found", "role": "Role", "status": "Status", "last_login": "Last Login", "created": "Created"}, "roles": {"manager": "Manager", "tutor": "Tutor", "client": "Client"}, "profile": {"title": "User Profile", "personal_info": "Personal Information", "contact_info": "Contact Information", "account_settings": "Account <PERSON><PERSON>", "notification_preferences": "Notification Preferences"}}, "clients": {"list": {"title": "Clients", "add_client": "Add Client", "search_placeholder": "Search clients", "no_clients": "No clients found"}, "profile": {"title": "Client Profile", "basic_info": "Basic Information", "first_name": "First Name", "last_name": "Last Name", "phone": "Phone Number", "emergency_contact": "Emergency Contact", "subscription_info": "Subscription Information", "active_subscriptions": "Active Subscriptions", "hours_remaining": "Hours Remaining"}, "dependants": {"title": "Dependants", "add_dependant": "Add Dependant", "birth_date": "Date of Birth", "age": "Age", "relationship": "Relationship", "learning_needs": "Learning Needs", "no_dependants": "No dependants found"}}, "tutors": {"list": {"title": "Tutors", "add_tutor": "<PERSON><PERSON>", "invite_tutor": "<PERSON><PERSON><PERSON>", "search_placeholder": "Search tutors", "no_tutors": "No tutors found", "verification_status": "Verification", "rating": "Rating", "total_sessions": "Sessions"}, "profile": {"title": "Tutor Profile", "basic_info": "Basic Information", "bio": "Biography", "specializations": "Specializations", "location": "Location", "availability": "Availability", "rates": "Rates", "verification": "Verification", "documents": "Documents", "reviews": "Reviews"}, "verification": {"pending": "Pending", "documents_submitted": "Documents Submitted", "background_check": "Background Check", "fully_verified": "Fully Verified", "suspended": "Suspended"}, "invite": {"title": "<PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON>", "message": "Personal Message (Optional)", "expires_hours": "Invitation Expires (Hours)", "submit": "Send Invitation", "success": "Invitation sent successfully"}}, "appointments": {"list": {"title": "Appointments", "book_appointment": "Book Appointment", "search_placeholder": "Search appointments", "no_appointments": "No appointments found", "tutor": "Tutor", "client": "Client", "subject": "Subject", "duration": "Duration", "location": "Location"}, "book": {"title": "Book Appointment", "select_tutor": "Select Tutor", "select_client": "Select Client", "select_dependant": "Select Dependant (Optional)", "date": "Date", "start_time": "Start Time", "end_time": "End Time", "subject_area": "Subject Area", "session_type": "Session Type", "location_type": "Location Type", "notes": "Notes", "submit": "Book Appointment"}, "status": {"scheduled": "Scheduled", "confirmed": "Confirmed", "in_progress": "In Progress", "completed": "Completed", "cancelled": "Cancelled", "no_show": "No Show"}, "location_types": {"online": "Online", "in_person": "In Person", "library": "Library"}, "session_types": {"individual": "Individual", "group": "Group", "tecfee_program": "TECFEE Program"}, "calendar": {"title": "Calendar", "today": "Today", "week": "Week", "month": "Month", "day": "Day", "no_appointments": "No appointments scheduled"}}, "billing": {"invoices": {"title": "Invoices", "create_invoice": "Create Invoice", "invoice_number": "Invoice Number", "issue_date": "Issue Date", "due_date": "Due Date", "total": "Total", "status": "Status", "paid_date": "Paid <PERSON>", "overdue": "Overdue"}, "payments": {"title": "Payments", "tutor_payments": "Tutor Payments", "payment_period": "Payment Period", "hours_worked": "Hours Worked", "base_amount": "Base Amount", "bonus": "Bonus", "approve": "Approve", "approved": "Approved", "pending": "Pending"}, "subscriptions": {"title": "Subscriptions", "create_subscription": "Create Subscription", "total_hours": "Total Hours", "remaining_hours": "Remaining Hours", "price_per_hour": "Price per Hour", "purchase_date": "Purchase Date", "expiry_date": "Expiry Date", "utilization": "Utilization"}, "status": {"pending": "Pending", "processing": "Processing", "paid": "Paid", "failed": "Failed", "refunded": "Refunded", "cancelled": "Cancelled"}}, "messages": {"title": "Messages", "sms_threads": "SMS Threads", "in_app_chat": "In-<PERSON><PERSON>", "templates": "Templates", "broadcasts": "Broadcasts", "send_message": "Send Message", "message_placeholder": "Type your message...", "send": "Send", "delivered": "Delivered", "read": "Read", "failed": "Failed"}, "reports": {"title": "Reports", "financial": "Financial Reports", "performance": "Performance Reports", "analytics": "Analytics", "usage": "Usage Reports", "monthly": "Monthly Reports", "generate": "Generate Report", "export": "Export", "date_range": "Date Range", "from": "From", "to": "To"}, "settings": {"title": "Settings", "system": "System Settings", "users": "User Settings", "payments": "Payment Settings", "notifications": "Notification Settings", "api": "API Settings", "language": "Language", "timezone": "Timezone"}, "tecfee": {"title": "TECFEE Program", "programs": "Programs", "enrollments": "Enrollments", "create_program": "Create Program", "enroll": "Enroll", "modules": "<PERSON><PERSON><PERSON>", "completion": "Completion", "certificate": "Certificate", "progress": "Progress"}, "notifications": {"appointment_reminder": "Appointment Reminder", "appointment_confirmed": "Appointment Confirmed", "appointment_cancelled": "Appointment Cancelled", "payment_received": "Payment Received", "invoice_generated": "Invoice Generated", "tutor_invited": "<PERSON><PERSON> In<PERSON>ted", "account_created": "Account Created"}, "email": {"password_reset": {"subject": "Reset Your Password - TutorAide"}, "password_changed": {"subject": "Password Changed - Security Alert"}, "verification": {"subject": "Verify Your Email - TutorAide"}}, "errors": {"required_field": "This field is required", "invalid_email": "Please enter a valid email address", "invalid_phone": "Please enter a valid phone number", "invalid_postal_code": "Please enter a valid postal code", "password_too_short": "Password must be at least 8 characters", "passwords_dont_match": "Passwords do not match", "date_in_past": "Date cannot be in the past", "end_time_before_start": "End time must be after start time", "amount_negative": "Amount cannot be negative", "file_too_large": "File size exceeds maximum limit", "unsupported_file_type": "Unsupported file type", "network_error": "Network error. Please try again.", "server_error": "Server error. Please contact support.", "unauthorized": "You are not authorized to perform this action", "not_found": "The requested resource was not found", "validation_failed": "Validation failed. Please check your input."}, "success": {"user_created": "User created successfully", "user_updated": "User updated successfully", "appointment_booked": "Appointment booked successfully", "appointment_confirmed": "Appointment confirmed successfully", "payment_processed": "Payment processed successfully", "invoice_sent": "Invoice sent successfully", "message_sent": "Message sent successfully", "settings_saved": "Setting<PERSON> saved successfully", "file_uploaded": "File uploaded successfully", "password_updated": "Password updated successfully"}, "sidebar": {"search": "Search", "users": "Users", "clients": "Clients", "tutors": "Tutors", "dependants": "Dependants", "calendar": "Calendar", "map": "Tutor <PERSON>", "billing": "Billing", "invoices": "Invoices", "tutorPayments": "Tutor Payments", "subscriptions": "Subscriptions", "packages": "Packages", "reports": "Reports", "financial": "Financial", "performance": "Performance", "analytics": "Analytics", "usage": "Usage", "monthly": "Monthly", "messages": "Messages", "smsThreads": "SMS Threads", "inAppChat": "In-<PERSON><PERSON>", "templates": "Templates", "broadcasts": "Broadcasts", "settings": "Settings", "system": "System", "userSettings": "Users", "payments": "Payments", "notifications": "Notifications", "api": "API"}, "plurals": {"appointment": {"one": "{count} appointment", "other": "{count} appointments"}, "client": {"one": "{count} client", "other": "{count} clients"}, "tutor": {"one": "{count} tutor", "other": "{count} tutors"}, "hour": {"one": "{count} hour", "other": "{count} hours"}, "minute": {"one": "{count} minute", "other": "{count} minutes"}, "session": {"one": "{count} session", "other": "{count} sessions"}, "message": {"one": "{count} message", "other": "{count} messages"}, "payment": {"one": "{count} payment", "other": "{count} payments"}}, "quebec": {"province_name": "Quebec", "currency_symbol": "$", "tax_gst": "GST", "tax_qst": "QST", "phone_format": "(XXX) XXX-XXXX", "postal_code_format": "XXX XXX", "time_zone": "Eastern Time", "business_hours": "9:00 AM - 5:00 PM", "languages": {"english": "English", "french": "French"}}, "templates": {"appointment_reminder": "Hi {first_name}, you have a {subject} session {when} with {tutor_name}. Location: {location}", "appointment_confirmation": "Your {subject} session on {date} at {time} has been confirmed with {tutor_name}.", "payment_receipt": "Payment of {amount} received for {description}. Thank you!", "welcome_message": "Welcome to TutorAide! We're excited to help you on your learning journey.", "session_completed": "Session completed with {tutor_name}. Please rate your experience.", "password_reset": "Click this link to reset your password: {reset_link}. Link expires in 24 hours.", "tutor_invitation": "You've been invited to join TutorA<PERSON> as a tutor. Click here to get started: {invitation_link}"}, "validation": {"required": "This field is required", "email_format": "Please enter a valid email address", "phone_format": "Please enter a valid phone number", "postal_code_format": "Please enter a valid postal code (XXX XXX)", "password_strength": "Password must be at least 8 characters with uppercase, lowercase, and number", "date_future": "Date must be in the future", "time_valid": "Please enter a valid time", "amount_positive": "Amount must be greater than zero", "file_size": "File size must be less than {max_size}", "file_type": "File type not supported. Allowed types: {allowed_types}", "length_min": "Must be at least {min} characters", "length_max": "Must be no more than {max} characters"}, "accessibility": {"skip_to_content": "Skip to main content", "close_dialog": "Close dialog", "open_menu": "Open menu", "close_menu": "Close menu", "sort_ascending": "Sort ascending", "sort_descending": "Sort descending", "loading_content": "Loading content", "page_of_pages": "Page {current} of {total}", "selected": "Selected", "required_field": "Required field", "optional_field": "Optional field", "expand": "Expand", "collapse": "Collapse"}}