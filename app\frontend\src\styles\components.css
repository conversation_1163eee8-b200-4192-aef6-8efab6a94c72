/* Base Component Styles */

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-medium);
  transition: all var(--transition-base);
  cursor: pointer;
  border: none;
  font-family: var(--font-sans);
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-pill {
  border-radius: var(--radius-full);
}

.btn-rounded {
  border-radius: var(--radius-md);
}

/* Button Variants */
.btn-primary {
  background-color: var(--color-highlight);
  color: var(--text-inverse);
  box-shadow: var(--shadow-soft);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--color-accent);
  box-shadow: var(--shadow-elevated);
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--border-primary);
}

.btn-ghost {
  background-color: transparent;
  color: var(--text-secondary);
}

.btn-ghost:hover:not(:disabled) {
  background-color: rgba(248, 113, 113, 0.08);
  color: var(--color-highlight);
}

/* Button Sizes */
.btn-sm {
  padding: var(--space-sm) var(--space-md);
  font-size: var(--font-size-sm);
  gap: var(--space-xs);
}

.btn-md {
  padding: var(--space-md) var(--space-lg);
  font-size: var(--font-size-base);
  gap: var(--space-sm);
}

.btn-lg {
  padding: var(--space-lg) var(--space-xl);
  font-size: var(--font-size-lg);
  gap: var(--space-md);
}

/* Cards */
.card {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-subtle);
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all var(--transition-base);
}

.card-hover:hover {
  box-shadow: var(--shadow-soft);
  transform: translateY(-2px);
}

.card-padding {
  padding: var(--space-lg);
}

.card-header {
  padding: var(--space-lg);
  border-bottom: 1px solid var(--border-primary);
  background-color: var(--bg-secondary);
}

.card-body {
  padding: var(--space-lg);
}

.card-footer {
  padding: var(--space-lg);
  border-top: 1px solid var(--border-primary);
  background-color: var(--bg-secondary);
}

/* Inputs */
.input {
  width: 100%;
  padding: var(--space-md);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background-color: var(--bg-secondary);
  font-size: var(--font-size-base);
  transition: all var(--transition-base);
  font-family: var(--font-sans);
}

.input:hover:not(:disabled) {
  border-color: var(--border-secondary);
}

.input:focus {
  border-color: var(--color-accent);
  background-color: var(--bg-primary);
  box-shadow: var(--shadow-focus);
}

.input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.input-error {
  border-color: var(--color-error);
}

.input-error:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Select */
.select {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right var(--space-md) center;
  background-size: 20px;
  padding-right: var(--space-2xl);
}

/* Textarea */
.textarea {
  min-height: 120px;
  resize: vertical;
}

/* Labels */
.label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
}

/* Form Groups */
.form-group {
  margin-bottom: var(--space-lg);
}

.form-row {
  display: flex;
  gap: var(--space-lg);
  flex-wrap: wrap;
}

.form-row > * {
  flex: 1;
  min-width: 200px;
}

/* Badges */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-xs) var(--space-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-medium);
  border-radius: var(--radius-full);
  gap: var(--space-xs);
}

.badge-primary {
  background-color: var(--color-accent);
  color: white;
}

.badge-secondary {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
}

.badge-success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--color-success);
}

.badge-warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--color-warning);
}

.badge-error {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--color-error);
}

/* Avatars */
.avatar {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-full);
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  font-weight: var(--font-medium);
  overflow: hidden;
  position: relative;
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-sm {
  width: 32px;
  height: 32px;
  font-size: var(--font-size-sm);
}

.avatar-md {
  width: 40px;
  height: 40px;
  font-size: var(--font-size-base);
}

.avatar-lg {
  width: 56px;
  height: 56px;
  font-size: var(--font-size-lg);
}

.avatar-xl {
  width: 80px;
  height: 80px;
  font-size: var(--font-size-xl);
}

/* Modals */
.modal-backdrop {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: var(--z-modal-backdrop);
  animation: fadeIn var(--transition-base);
}

.modal {
  position: fixed;
  inset: 0;
  z-index: var(--z-modal);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-lg);
}

.modal-content {
  background-color: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-elevated);
  max-width: 100%;
  max-height: 90vh;
  overflow: auto;
  animation: slideUp var(--transition-slow);
}

.modal-header {
  padding: var(--space-lg);
  border-bottom: 1px solid var(--border-primary);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-body {
  padding: var(--space-lg);
}

.modal-footer {
  padding: var(--space-lg);
  border-top: 1px solid var(--border-primary);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--space-md);
}

/* Dropdowns */
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: var(--space-xs);
  background-color: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-soft);
  min-width: 200px;
  z-index: var(--z-dropdown);
  animation: fadeIn var(--transition-fast);
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: var(--space-sm) var(--space-md);
  text-align: left;
  background: none;
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  color: var(--text-primary);
  text-decoration: none;
}

.dropdown-item:hover {
  background-color: var(--bg-secondary);
  color: var(--color-highlight);
}

.dropdown-divider {
  height: 1px;
  margin: var(--space-sm) 0;
  background-color: var(--border-primary);
}

/* Tabs */
.tabs {
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--border-primary);
  gap: var(--space-xs);
}

.tab {
  padding: var(--space-md) var(--space-lg);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  background: none;
  border: none;
  cursor: pointer;
  transition: all var(--transition-base);
  position: relative;
  border-radius: var(--radius-md) var(--radius-md) 0 0;
}

.tab:hover {
  color: var(--text-primary);
  background-color: var(--bg-tertiary);
}

.tab.active {
  color: var(--color-highlight);
  background-color: var(--bg-primary);
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--color-highlight);
}

/* Toggle Switch */
.toggle {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 28px;
}

.toggle-input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bg-tertiary);
  transition: all var(--transition-base);
  border-radius: var(--radius-full);
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: all var(--transition-base);
  border-radius: var(--radius-full);
  box-shadow: var(--shadow-subtle);
}

.toggle-input:checked + .toggle-slider {
  background-color: var(--color-highlight);
}

.toggle-input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

/* Loading States */
.skeleton {
  background: linear-gradient(90deg, var(--bg-tertiary) 25%, var(--border-primary) 50%, var(--bg-tertiary) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: var(--radius-md);
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}