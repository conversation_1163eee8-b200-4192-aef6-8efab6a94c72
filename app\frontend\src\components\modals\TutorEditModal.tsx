import React, { useState, useEffect } from 'react';
import { Modal } from '../common/Modal';
import Button from '../common/Button';
import { Input } from '../common/Input';
import { Select } from '../common/Select';
import { Badge } from '../common/Badge';
import { 
  X, Save, User, Mail, Phone, MapPin, GraduationCap, 
  Briefcase, Globe, Award, Shield, Camera, BookOpen,
  AlertTriangle, CheckCircle, Clock, Calendar, FileText,
  Settings
} from 'lucide-react';
import toast from 'react-hot-toast';
import { useApi } from '../../hooks/useApi';

// Import education and experience components
import { DegreeSelector } from '../forms/education/DegreeSelector';
import { UniversityAutocomplete } from '../forms/education/UniversityAutocomplete';
import { CertificationSelector } from '../forms/education/CertificationSelector';
import { LanguageProficiencyGrid } from '../forms/experience/LanguageProficiencyGrid';
import { SubjectExpertiseSelector } from '../forms/experience/SubjectExpertiseSelector';

interface TutorEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  tutor: any; // Using any for now to match existing pattern
  onSuccess?: (updatedTutor: any) => void;
}

type TabType = 'personal' | 'education' | 'experience' | 'verification' | 'preferences';

export const TutorEditModal: React.FC<TutorEditModalProps> = ({
  isOpen,
  onClose,
  tutor,
  onSuccess
}) => {
  const { put, loading } = useApi();
  const [activeTab, setActiveTab] = useState<TabType>('personal');
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  const [formData, setFormData] = useState({
    // Personal Information
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    postal_code: '',
    bio: '',
    profile_picture: '',
    
    // Address (for future)
    address_line1: '',
    address_line2: '',
    city: '',
    province: '',
    
    // Education
    highest_degree_level: '',
    highest_degree_name: '',
    degree_major: '',
    university_name: '',
    graduation_year: '',
    teaching_certifications: [] as string[],
    
    // Experience
    years_of_experience: '',
    years_online_teaching: '',
    years_tutoring: '',
    current_occupation: '',
    subject_expertise: {} as Record<string, string>,
    language_proficiency: {} as Record<string, string>,
    teaching_methodology: [] as string[],
    special_needs_experience: false,
    age_groups_experience: [] as string[],
    specialties: [] as string[],
    languages: [] as string[],
    
    // Portfolio
    portfolio_url: '',
    achievement_highlights: '',
    
    // Verification
    background_check_status: '',
    has_references: false,
    reference_check_completed: false,
    working_with_children_clearance: false,
    has_liability_insurance: false,
    work_permit_status: '',
    
    // Preferences
    availability_status: 'available',
    preferred_communication: 'email',
    timezone: 'America/Toronto',
    service_types: [] as string[],
    
    // Internal
    verification_status: 'pending',
    internal_notes: ''
  });

  useEffect(() => {
    if (tutor && isOpen) {
      setFormData({
        // Personal Information
        first_name: tutor.first_name || '',
        last_name: tutor.last_name || '',
        email: tutor.email || '',
        phone: tutor.phone || '',
        postal_code: tutor.postal_code || '',
        bio: tutor.bio || '',
        profile_picture: tutor.profile_picture || '',
        
        // Address
        address_line1: tutor.address_line1 || '',
        address_line2: tutor.address_line2 || '',
        city: tutor.city || '',
        province: tutor.province || '',
        
        // Education
        highest_degree_level: tutor.highest_degree_level || '',
        highest_degree_name: tutor.highest_degree_name || '',
        degree_major: tutor.degree_major || '',
        university_name: tutor.university_name || '',
        graduation_year: tutor.graduation_year?.toString() || '',
        teaching_certifications: tutor.teaching_certifications || [],
        
        // Experience
        years_of_experience: tutor.years_of_experience?.toString() || '',
        years_online_teaching: tutor.years_online_teaching?.toString() || '',
        years_tutoring: tutor.years_tutoring?.toString() || '',
        current_occupation: tutor.current_occupation || '',
        subject_expertise: tutor.subject_expertise || {},
        language_proficiency: tutor.language_proficiency || {},
        teaching_methodology: tutor.teaching_methodology || [],
        special_needs_experience: tutor.special_needs_experience || false,
        age_groups_experience: tutor.age_groups_experience || [],
        specialties: tutor.specialties || [],
        languages: tutor.languages || [],
        
        // Portfolio
        portfolio_url: tutor.portfolio_url || '',
        achievement_highlights: tutor.achievement_highlights || '',
        
        // Verification
        background_check_status: tutor.background_check_status || '',
        has_references: tutor.has_references || false,
        reference_check_completed: tutor.reference_check_completed || false,
        working_with_children_clearance: tutor.working_with_children_clearance || false,
        has_liability_insurance: tutor.has_liability_insurance || false,
        work_permit_status: tutor.work_permit_status || '',
        
        // Preferences
        availability_status: tutor.availability_status || 'available',
        preferred_communication: tutor.preferred_communication || 'email',
        timezone: tutor.timezone || 'America/Toronto',
        service_types: tutor.service_types || [],
        
        // Internal
        verification_status: tutor.verification_status || 'pending',
        internal_notes: tutor.internal_notes || ''
      });
      setActiveTab('personal');
      setErrors({});
    }
  }, [tutor, isOpen]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.first_name) newErrors.first_name = 'First name is required';
    if (!formData.last_name) newErrors.last_name = 'Last name is required';
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }
    if (!formData.phone) newErrors.phone = 'Phone is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      // Prepare data for API
      const updateData = {
        first_name: formData.first_name,
        last_name: formData.last_name,
        email: formData.email,
        phone: formData.phone,
        postal_code: formData.postal_code || null,
        bio: formData.bio || null,
        profile_picture: formData.profile_picture || null,
        
        // Education
        highest_degree_level: formData.highest_degree_level || null,
        highest_degree_name: formData.highest_degree_name || null,
        degree_major: formData.degree_major || null,
        university_name: formData.university_name || null,
        graduation_year: formData.graduation_year ? parseInt(formData.graduation_year) : null,
        teaching_certifications: formData.teaching_certifications,
        
        // Experience
        years_of_experience: formData.years_of_experience ? parseInt(formData.years_of_experience) : 0,
        years_online_teaching: formData.years_online_teaching ? parseInt(formData.years_online_teaching) : 0,
        years_tutoring: formData.years_tutoring ? parseInt(formData.years_tutoring) : 0,
        current_occupation: formData.current_occupation || null,
        subject_expertise: formData.subject_expertise,
        language_proficiency: formData.language_proficiency,
        teaching_methodology: formData.teaching_methodology,
        special_needs_experience: formData.special_needs_experience,
        age_groups_experience: formData.age_groups_experience,
        specialties: formData.specialties,
        languages: formData.languages,
        
        // Portfolio
        portfolio_url: formData.portfolio_url || null,
        achievement_highlights: formData.achievement_highlights || null,
        
        // Verification
        background_check_status: formData.background_check_status || null,
        has_references: formData.has_references,
        reference_check_completed: formData.reference_check_completed,
        working_with_children_clearance: formData.working_with_children_clearance,
        has_liability_insurance: formData.has_liability_insurance,
        work_permit_status: formData.work_permit_status || null,
        
        // Preferences
        availability_status: formData.availability_status,
        timezone: formData.timezone,
        
        // Internal
        verification_status: formData.verification_status,
        internal_notes: formData.internal_notes || null
      };

      const updatedTutor = await put(`/tutors/${tutor.tutor_id}`, updateData);
      
      toast.success('Tutor updated successfully!');
      
      if (onSuccess) {
        onSuccess(updatedTutor);
      }
      
      onClose();
    } catch (error: any) {
      console.error('Error updating tutor:', error);
      if (error.response?.data?.detail) {
        toast.error(error.response.data.detail);
      } else {
        toast.error('Failed to update tutor');
      }
    }
  };

  const getTabBadge = (tab: TabType) => {
    switch (tab) {
      case 'personal':
        return errors.first_name || errors.last_name || errors.email || errors.phone ? 
          <Badge className="ml-2 bg-red-100 text-red-700">!</Badge> : null;
      case 'verification':
        return formData.verification_status === 'verified' ? 
          <Badge className="ml-2 bg-green-100 text-green-700">✓</Badge> : 
          <Badge className="ml-2 bg-yellow-100 text-yellow-700">Pending</Badge>;
      default:
        return null;
    }
  };

  const tabs: { id: TabType; label: string; icon: React.ReactNode }[] = [
    { id: 'personal', label: 'Personal Info', icon: <User className="w-4 h-4" /> },
    { id: 'education', label: 'Education', icon: <GraduationCap className="w-4 h-4" /> },
    { id: 'experience', label: 'Experience', icon: <Briefcase className="w-4 h-4" /> },
    { id: 'verification', label: 'Verification', icon: <Shield className="w-4 h-4" /> },
    { id: 'preferences', label: 'Preferences', icon: <Settings className="w-4 h-4" /> }
  ];

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Edit Tutor Profile"
      size="xl"
    >
      <div className="flex h-[600px]">
        {/* Sidebar */}
        <div className="w-48 border-r border-gray-200 p-4">
          <nav className="space-y-2">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`w-full flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-accent-red text-white'
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                {tab.icon}
                {tab.label}
                {getTabBadge(tab.id)}
              </button>
            ))}
          </nav>
        </div>

        {/* Content */}
        <div className="flex-1 p-6 overflow-y-auto">
          {activeTab === 'personal' && (
            <div className="space-y-6">
              {/* Basic Information */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      First Name <span className="text-red-500">*</span>
                    </label>
                    <Input
                      value={formData.first_name}
                      onChange={(e) => setFormData({ ...formData, first_name: e.target.value })}
                      error={errors.first_name}
                      icon={User}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Last Name <span className="text-red-500">*</span>
                    </label>
                    <Input
                      value={formData.last_name}
                      onChange={(e) => setFormData({ ...formData, last_name: e.target.value })}
                      error={errors.last_name}
                    />
                  </div>
                </div>
              </div>

              {/* Contact Information */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Email <span className="text-red-500">*</span>
                    </label>
                    <Input
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                      error={errors.email}
                      icon={Mail}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Phone <span className="text-red-500">*</span>
                    </label>
                    <Input
                      value={formData.phone}
                      onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                      error={errors.phone}
                      icon={Phone}
                    />
                  </div>
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Postal Code
                    </label>
                    <Input
                      value={formData.postal_code}
                      onChange={(e) => setFormData({ ...formData, postal_code: e.target.value })}
                      icon={MapPin}
                    />
                  </div>
                </div>
              </div>

              {/* Bio */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Bio
                </label>
                <textarea
                  value={formData.bio}
                  onChange={(e) => setFormData({ ...formData, bio: e.target.value })}
                  placeholder="Tell us about your teaching philosophy and approach..."
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-red"
                />
              </div>

              {/* Profile Picture */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Profile Picture URL
                </label>
                <div className="flex items-center gap-3">
                  <Input
                    value={formData.profile_picture}
                    onChange={(e) => setFormData({ ...formData, profile_picture: e.target.value })}
                    placeholder="https://example.com/photo.jpg"
                    icon={Camera}
                    className="flex-1"
                  />
                  {formData.profile_picture && (
                    <img
                      src={formData.profile_picture}
                      alt="Profile"
                      className="w-12 h-12 rounded-full object-cover"
                      onError={(e) => {
                        (e.target as HTMLImageElement).style.display = 'none';
                      }}
                    />
                  )}
                </div>
              </div>

              {/* Specialties */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Specialties
                </label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {[
                    { value: 'mathematics', label: 'Mathematics' },
                    { value: 'physics', label: 'Physics' },
                    { value: 'chemistry', label: 'Chemistry' },
                    { value: 'biology', label: 'Biology' },
                    { value: 'english', label: 'English' },
                    { value: 'french', label: 'French' },
                    { value: 'history', label: 'History' },
                    { value: 'geography', label: 'Geography' },
                    { value: 'computer_science', label: 'Computer Science' }
                  ].map(specialty => (
                    <label key={specialty.value} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={formData.specialties.includes(specialty.value)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setFormData({
                              ...formData,
                              specialties: [...formData.specialties, specialty.value]
                            });
                          } else {
                            setFormData({
                              ...formData,
                              specialties: formData.specialties.filter(s => s !== specialty.value)
                            });
                          }
                        }}
                        className="rounded border-gray-300 text-accent-red focus:ring-accent-red"
                      />
                      <span className="text-sm">{specialty.label}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Languages */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Teaching Languages
                </label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {[
                    { value: 'en', label: 'English' },
                    { value: 'fr', label: 'French' },
                    { value: 'es', label: 'Spanish' },
                    { value: 'zh', label: 'Chinese' },
                    { value: 'ar', label: 'Arabic' }
                  ].map(language => (
                    <label key={language.value} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={formData.languages.includes(language.value)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setFormData({
                              ...formData,
                              languages: [...formData.languages, language.value]
                            });
                          } else {
                            setFormData({
                              ...formData,
                              languages: formData.languages.filter(l => l !== language.value)
                            });
                          }
                        }}
                        className="rounded border-gray-300 text-accent-red focus:ring-accent-red"
                      />
                      <span className="text-sm">{language.label}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'education' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Education Information</h3>
              
              {/* Degree Selector */}
              <DegreeSelector
                value={{
                  level: formData.highest_degree_level,
                  name: formData.highest_degree_name
                }}
                onChange={(value) => setFormData({
                  ...formData,
                  highest_degree_level: value.level || '',
                  highest_degree_name: value.name || ''
                })}
              />

              {/* Major and Graduation Year */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Major/Field of Study
                  </label>
                  <Input
                    value={formData.degree_major}
                    onChange={(e) => setFormData({ ...formData, degree_major: e.target.value })}
                    placeholder="e.g., Mathematics, Education"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Graduation Year
                  </label>
                  <Input
                    type="number"
                    value={formData.graduation_year}
                    onChange={(e) => setFormData({ ...formData, graduation_year: e.target.value })}
                    placeholder={new Date().getFullYear().toString()}
                    min="1950"
                    max={new Date().getFullYear()}
                  />
                </div>
              </div>

              {/* University */}
              <UniversityAutocomplete
                value={formData.university_name}
                onChange={(value) => setFormData({ ...formData, university_name: value })}
              />

              {/* Certifications */}
              <CertificationSelector
                value={formData.teaching_certifications}
                onChange={(value) => setFormData({ ...formData, teaching_certifications: value })}
              />
            </div>
          )}

          {activeTab === 'experience' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Teaching Experience</h3>
              
              {/* Experience Years */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Total Years of Experience
                  </label>
                  <Input
                    type="number"
                    value={formData.years_of_experience}
                    onChange={(e) => setFormData({ ...formData, years_of_experience: e.target.value })}
                    placeholder="0"
                    min="0"
                    max="50"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Years Teaching Online
                  </label>
                  <Input
                    type="number"
                    value={formData.years_online_teaching}
                    onChange={(e) => setFormData({ ...formData, years_online_teaching: e.target.value })}
                    placeholder="0"
                    min="0"
                    max="50"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Years Tutoring
                  </label>
                  <Input
                    type="number"
                    value={formData.years_tutoring}
                    onChange={(e) => setFormData({ ...formData, years_tutoring: e.target.value })}
                    placeholder="0"
                    min="0"
                    max="50"
                  />
                </div>
              </div>

              {/* Current Occupation */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Current Occupation
                </label>
                <Input
                  value={formData.current_occupation}
                  onChange={(e) => setFormData({ ...formData, current_occupation: e.target.value })}
                  placeholder="e.g., Full-time Tutor, High School Teacher"
                />
              </div>

              {/* Subject Expertise */}
              <SubjectExpertiseSelector
                value={formData.subject_expertise}
                onChange={(value) => setFormData({ ...formData, subject_expertise: value })}
              />

              {/* Language Proficiency */}
              <LanguageProficiencyGrid
                value={formData.language_proficiency}
                onChange={(value) => setFormData({ ...formData, language_proficiency: value })}
              />

              {/* Teaching Methodology */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Teaching Methodologies
                </label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {[
                    { value: 'traditional', label: 'Traditional' },
                    { value: 'montessori', label: 'Montessori' },
                    { value: 'project_based', label: 'Project-Based' },
                    { value: 'flipped_classroom', label: 'Flipped Classroom' },
                    { value: 'socratic', label: 'Socratic Method' },
                    { value: 'experiential', label: 'Experiential Learning' }
                  ].map(method => (
                    <label key={method.value} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={formData.teaching_methodology.includes(method.value)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setFormData({
                              ...formData,
                              teaching_methodology: [...formData.teaching_methodology, method.value]
                            });
                          } else {
                            setFormData({
                              ...formData,
                              teaching_methodology: formData.teaching_methodology.filter(m => m !== method.value)
                            });
                          }
                        }}
                        className="rounded border-gray-300 text-accent-red focus:ring-accent-red"
                      />
                      <span className="text-sm">{method.label}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Special Needs */}
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="special_needs"
                  checked={formData.special_needs_experience}
                  onChange={(e) => setFormData({ ...formData, special_needs_experience: e.target.checked })}
                  className="rounded border-gray-300 text-accent-red focus:ring-accent-red"
                />
                <label htmlFor="special_needs" className="text-sm font-medium text-gray-700">
                  Experience teaching students with special needs
                </label>
              </div>

              {/* Age Groups */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Age Groups Experience
                </label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                  {[
                    { value: 'preschool', label: 'Preschool (3-5)' },
                    { value: 'elementary', label: 'Elementary (6-11)' },
                    { value: 'middle_school', label: 'Middle School (12-14)' },
                    { value: 'high_school', label: 'High School (15-18)' },
                    { value: 'college', label: 'College/CEGEP' },
                    { value: 'university', label: 'University' },
                    { value: 'adult', label: 'Adult Learners' },
                    { value: 'senior', label: 'Seniors' }
                  ].map(ageGroup => (
                    <label key={ageGroup.value} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={formData.age_groups_experience.includes(ageGroup.value)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setFormData({
                              ...formData,
                              age_groups_experience: [...formData.age_groups_experience, ageGroup.value]
                            });
                          } else {
                            setFormData({
                              ...formData,
                              age_groups_experience: formData.age_groups_experience.filter(a => a !== ageGroup.value)
                            });
                          }
                        }}
                        className="rounded border-gray-300 text-accent-red focus:ring-accent-red"
                      />
                      <span className="text-sm">{ageGroup.label}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Portfolio */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Portfolio URL
                </label>
                <Input
                  type="url"
                  value={formData.portfolio_url}
                  onChange={(e) => setFormData({ ...formData, portfolio_url: e.target.value })}
                  placeholder="https://your-portfolio.com"
                  icon={Globe}
                />
              </div>

              {/* Achievement Highlights */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Achievement Highlights
                </label>
                <textarea
                  value={formData.achievement_highlights}
                  onChange={(e) => setFormData({ ...formData, achievement_highlights: e.target.value })}
                  placeholder="Notable achievements, awards, or success stories..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-red"
                />
              </div>
            </div>
          )}

          {activeTab === 'verification' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Verification & Compliance</h3>
              
              {/* Verification Progress */}
              <div className="mb-6">
                <div className="flex justify-between text-sm text-gray-600 mb-2">
                  <span>Verification Progress</span>
                  <span className="font-medium">
                    {[
                      formData.has_references && formData.reference_check_completed,
                      formData.background_check_status === 'passed',
                      formData.working_with_children_clearance,
                      formData.has_liability_insurance
                    ].filter(Boolean).length} / 4 Complete
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-accent-red h-2 rounded-full transition-all"
                    style={{
                      width: `${([
                        formData.has_references && formData.reference_check_completed,
                        formData.background_check_status === 'passed',
                        formData.working_with_children_clearance,
                        formData.has_liability_insurance
                      ].filter(Boolean).length / 4) * 100}%`
                    }}
                  />
                </div>
              </div>
              
              {/* Quick Verification Workflow */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-3">Quick Verification Checklist</h4>
                <div className="space-y-2">
                  <label className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      checked={formData.has_references && formData.reference_check_completed}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setFormData({
                            ...formData,
                            has_references: true,
                            reference_check_completed: true
                          });
                        }
                      }}
                      className="rounded border-gray-300 text-accent-red focus:ring-accent-red"
                    />
                    <span className="text-sm">References verified</span>
                  </label>
                  <label className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      checked={formData.background_check_status === 'passed'}
                      onChange={(e) => {
                        setFormData({
                          ...formData,
                          background_check_status: e.target.checked ? 'passed' : 'pending'
                        });
                      }}
                      className="rounded border-gray-300 text-accent-red focus:ring-accent-red"
                    />
                    <span className="text-sm">Background check passed</span>
                  </label>
                  <label className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      checked={formData.working_with_children_clearance}
                      onChange={(e) => setFormData({ ...formData, working_with_children_clearance: e.target.checked })}
                      className="rounded border-gray-300 text-accent-red focus:ring-accent-red"
                    />
                    <span className="text-sm">Working with children clearance</span>
                  </label>
                  <label className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      checked={formData.has_liability_insurance}
                      onChange={(e) => setFormData({ ...formData, has_liability_insurance: e.target.checked })}
                      className="rounded border-gray-300 text-accent-red focus:ring-accent-red"
                    />
                    <span className="text-sm">Liability insurance verified</span>
                  </label>
                </div>
                <div className="mt-3 pt-3 border-t border-blue-200">
                  <Button
                    size="sm"
                    variant={
                      formData.has_references && formData.reference_check_completed &&
                      formData.background_check_status === 'passed' &&
                      formData.working_with_children_clearance &&
                      formData.has_liability_insurance
                        ? "primary"
                        : "secondary"
                    }
                    onClick={() => {
                      if (formData.has_references && formData.reference_check_completed &&
                          formData.background_check_status === 'passed' &&
                          formData.working_with_children_clearance &&
                          formData.has_liability_insurance) {
                        setFormData({ ...formData, verification_status: 'verified' });
                        toast.success('Tutor marked as verified!');
                      } else {
                        toast.error('Please complete all verification requirements');
                      }
                    }}
                    className="w-full"
                  >
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Mark as Fully Verified
                  </Button>
                </div>
              </div>
              
              {/* Verification Status */}
              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <span className="font-medium text-gray-700">Verification Status</span>
                  <Select
                    value={formData.verification_status}
                    onChange={(e) => setFormData({ ...formData, verification_status: e.target.value })}
                    options={[
                      { value: 'pending', label: 'Pending' },
                      { value: 'verified', label: 'Verified' },
                      { value: 'rejected', label: 'Rejected' }
                    ]}
                    className="w-40"
                  />
                </div>
                <div className="text-sm text-gray-600">
                  {formData.verification_status === 'pending' && (
                    <p className="flex items-center gap-2">
                      <Clock className="w-4 h-4 text-yellow-500" />
                      Verification is pending review
                    </p>
                  )}
                  {formData.verification_status === 'verified' && (
                    <p className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      Tutor has been verified
                    </p>
                  )}
                  {formData.verification_status === 'rejected' && (
                    <p className="flex items-center gap-2">
                      <AlertTriangle className="w-4 h-4 text-red-500" />
                      Verification was rejected
                    </p>
                  )}
                </div>
              </div>

              {/* Background Check */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Background Check Status
                </label>
                <Select
                  value={formData.background_check_status}
                  onChange={(e) => setFormData({ ...formData, background_check_status: e.target.value })}
                  options={[
                    { value: '', label: 'Not Started' },
                    { value: 'not_started', label: 'Not Started' },
                    { value: 'pending', label: 'Pending' },
                    { value: 'passed', label: 'Passed' },
                    { value: 'requires_review', label: 'Requires Review' },
                    { value: 'failed', label: 'Failed' }
                  ]}
                />
              </div>

              {/* Work Permit */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Work Permit Status
                </label>
                <Select
                  value={formData.work_permit_status}
                  onChange={(e) => setFormData({ ...formData, work_permit_status: e.target.value })}
                  options={[
                    { value: '', label: 'Select Status' },
                    { value: 'citizen', label: 'Canadian Citizen' },
                    { value: 'permanent_resident', label: 'Permanent Resident' },
                    { value: 'work_permit', label: 'Work Permit' },
                    { value: 'student_visa', label: 'Student Visa' },
                    { value: 'other', label: 'Other' }
                  ]}
                />
              </div>

              {/* Verification Checkboxes */}
              <div className="space-y-3">
                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={formData.has_references}
                    onChange={(e) => setFormData({ ...formData, has_references: e.target.checked })}
                    className="rounded border-gray-300 text-accent-red focus:ring-accent-red"
                  />
                  <span className="text-sm">Has provided references</span>
                </label>
                
                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={formData.reference_check_completed}
                    onChange={(e) => setFormData({ ...formData, reference_check_completed: e.target.checked })}
                    disabled={!formData.has_references}
                    className="rounded border-gray-300 text-accent-red focus:ring-accent-red disabled:opacity-50"
                  />
                  <span className="text-sm">Reference check completed</span>
                </label>
                
                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={formData.working_with_children_clearance}
                    onChange={(e) => setFormData({ ...formData, working_with_children_clearance: e.target.checked })}
                    className="rounded border-gray-300 text-accent-red focus:ring-accent-red"
                  />
                  <span className="text-sm">Working with children clearance</span>
                </label>
                
                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={formData.has_liability_insurance}
                    onChange={(e) => setFormData({ ...formData, has_liability_insurance: e.target.checked })}
                    className="rounded border-gray-300 text-accent-red focus:ring-accent-red"
                  />
                  <span className="text-sm">Has liability insurance</span>
                </label>
              </div>

              {/* Internal Notes */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Internal Notes
                </label>
                <textarea
                  value={formData.internal_notes}
                  onChange={(e) => setFormData({ ...formData, internal_notes: e.target.value })}
                  placeholder="Internal notes about verification, compliance, etc."
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-red"
                />
                <p className="text-xs text-gray-500 mt-1">
                  These notes are internal and not visible to the tutor
                </p>
              </div>
            </div>
          )}

          {activeTab === 'preferences' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Preferences & Settings</h3>
              
              {/* Availability Status */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Availability Status
                </label>
                <Select
                  value={formData.availability_status}
                  onChange={(e) => setFormData({ ...formData, availability_status: e.target.value })}
                  options={[
                    { value: 'available', label: 'Available' },
                    { value: 'limited', label: 'Limited Availability' },
                    { value: 'busy', label: 'Busy' },
                    { value: 'inactive', label: 'Inactive' }
                  ]}
                />
              </div>

              {/* Timezone */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Timezone
                </label>
                <Select
                  value={formData.timezone}
                  onChange={(e) => setFormData({ ...formData, timezone: e.target.value })}
                  options={[
                    { value: 'America/Toronto', label: 'Eastern Time (Toronto)' },
                    { value: 'America/Montreal', label: 'Eastern Time (Montreal)' },
                    { value: 'America/Vancouver', label: 'Pacific Time (Vancouver)' },
                    { value: 'America/Calgary', label: 'Mountain Time (Calgary)' },
                    { value: 'America/Winnipeg', label: 'Central Time (Winnipeg)' }
                  ]}
                />
              </div>

              {/* Service Types */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Preferred Service Types
                </label>
                <div className="space-y-2">
                  {[
                    { value: 'online', label: 'Online Tutoring' },
                    { value: 'in_person', label: 'In-Person Tutoring' },
                    { value: 'library', label: 'Library Sessions' },
                    { value: 'hybrid', label: 'Hybrid (Mixed)' }
                  ].map(service => (
                    <label key={service.value} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={formData.service_types.includes(service.value)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setFormData({
                              ...formData,
                              service_types: [...formData.service_types, service.value]
                            });
                          } else {
                            setFormData({
                              ...formData,
                              service_types: formData.service_types.filter(s => s !== service.value)
                            });
                          }
                        }}
                        className="rounded border-gray-300 text-accent-red focus:ring-accent-red"
                      />
                      <span className="text-sm">{service.label}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Communication Preference */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Preferred Communication Method
                </label>
                <Select
                  value={formData.preferred_communication}
                  onChange={(e) => setFormData({ ...formData, preferred_communication: e.target.value })}
                  options={[
                    { value: 'email', label: 'Email' },
                    { value: 'sms', label: 'SMS' },
                    { value: 'phone', label: 'Phone Call' },
                    { value: 'in_app', label: 'In-App Messages' }
                  ]}
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Footer */}
      <div className="flex justify-end gap-3 px-6 py-4 border-t">
        <Button
          variant="ghost"
          onClick={onClose}
          disabled={loading}
        >
          <X className="w-4 h-4 mr-2" />
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          disabled={loading}
          className="bg-accent-red text-white hover:bg-accent-red-dark"
        >
          <Save className="w-4 h-4 mr-2" />
          {loading ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>
    </Modal>
  );
};