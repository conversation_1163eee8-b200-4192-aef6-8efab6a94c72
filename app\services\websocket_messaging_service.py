"""
WebSocket Messaging Service

Real-time messaging service for in-app conversations with typing indicators,
presence updates, and message delivery notifications.
"""

import json
import asyncio
from typing import Dict, Set, List, Optional, Any
from datetime import datetime
from fastapi import WebSocket, WebSocketDisconnect
from app.core.logging import logger
from app.core.exceptions import ValidationError


class ConnectionManager:
    """Manages WebSocket connections for real-time messaging"""
    
    def __init__(self):
        # Active connections by user_id
        self.active_connections: Dict[int, WebSocket] = {}
        
        # Users by conversation_id for broadcasting
        self.conversation_users: Dict[int, Set[int]] = {}
        
        # Typing indicators by conversation_id
        self.typing_users: Dict[int, Set[int]] = {}
        
        # User presence information
        self.user_presence: Dict[int, Dict[str, Any]] = {}

    async def connect(self, websocket: WebSocket, user_id: int):
        """Accept a new WebSocket connection"""
        await websocket.accept()
        
        # Store connection
        self.active_connections[user_id] = websocket
        
        # Update presence
        self.user_presence[user_id] = {
            'status': 'online',
            'last_seen': datetime.utcnow(),
            'connected_at': datetime.utcnow()
        }
        
        logger.info(f"User {user_id} connected to WebSocket")
        
        # Notify others about presence change
        await self._broadcast_presence_update(user_id, 'online')

    async def disconnect(self, user_id: int):
        """Handle WebSocket disconnection"""
        if user_id in self.active_connections:
            del self.active_connections[user_id]
        
        # Update presence
        if user_id in self.user_presence:
            self.user_presence[user_id]['status'] = 'offline'
            self.user_presence[user_id]['last_seen'] = datetime.utcnow()
        
        # Remove from all typing indicators
        for conversation_id in self.typing_users:
            self.typing_users[conversation_id].discard(user_id)
        
        # Remove from conversation users
        for conversation_id in list(self.conversation_users.keys()):
            if user_id in self.conversation_users[conversation_id]:
                self.conversation_users[conversation_id].discard(user_id)
                if not self.conversation_users[conversation_id]:
                    del self.conversation_users[conversation_id]
        
        logger.info(f"User {user_id} disconnected from WebSocket")
        
        # Notify others about presence change
        await self._broadcast_presence_update(user_id, 'offline')

    async def join_conversation(self, user_id: int, conversation_id: int):
        """Add user to a conversation for targeted messaging"""
        if conversation_id not in self.conversation_users:
            self.conversation_users[conversation_id] = set()
        
        self.conversation_users[conversation_id].add(user_id)
        logger.debug(f"User {user_id} joined conversation {conversation_id}")

    async def leave_conversation(self, user_id: int, conversation_id: int):
        """Remove user from a conversation"""
        if conversation_id in self.conversation_users:
            self.conversation_users[conversation_id].discard(user_id)
            
            # Remove typing indicator
            if conversation_id in self.typing_users:
                self.typing_users[conversation_id].discard(user_id)
            
            if not self.conversation_users[conversation_id]:
                del self.conversation_users[conversation_id]
        
        logger.debug(f"User {user_id} left conversation {conversation_id}")

    async def send_personal_message(self, user_id: int, message: Dict[str, Any]):
        """Send a message to a specific user"""
        if user_id in self.active_connections:
            try:
                websocket = self.active_connections[user_id]
                await websocket.send_text(json.dumps(message))
                logger.debug(f"Sent personal message to user {user_id}: {message.get('type', 'unknown')}")
            except Exception as e:
                logger.error(f"Error sending personal message to user {user_id}: {e}")
                await self.disconnect(user_id)

    async def broadcast_to_conversation(self, conversation_id: int, message: Dict[str, Any], exclude_user: Optional[int] = None):
        """Broadcast a message to all users in a conversation"""
        if conversation_id not in self.conversation_users:
            return
        
        # Add conversation context to message
        message['conversation_id'] = conversation_id
        message['timestamp'] = datetime.utcnow().isoformat()
        
        users_to_notify = self.conversation_users[conversation_id].copy()
        if exclude_user:
            users_to_notify.discard(exclude_user)
        
        disconnected_users = []
        
        for user_id in users_to_notify:
            if user_id in self.active_connections:
                try:
                    websocket = self.active_connections[user_id]
                    await websocket.send_text(json.dumps(message))
                except Exception as e:
                    logger.error(f"Error broadcasting to user {user_id}: {e}")
                    disconnected_users.append(user_id)
        
        # Clean up disconnected users
        for user_id in disconnected_users:
            await self.disconnect(user_id)
        
        logger.debug(f"Broadcasted to conversation {conversation_id}: {message.get('type', 'unknown')} to {len(users_to_notify)} users")

    async def broadcast_to_all(self, message: Dict[str, Any], exclude_user: Optional[int] = None):
        """Broadcast a message to all connected users"""
        message['timestamp'] = datetime.utcnow().isoformat()
        
        users_to_notify = list(self.active_connections.keys())
        if exclude_user:
            users_to_notify = [u for u in users_to_notify if u != exclude_user]
        
        disconnected_users = []
        
        for user_id in users_to_notify:
            try:
                websocket = self.active_connections[user_id]
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error broadcasting to user {user_id}: {e}")
                disconnected_users.append(user_id)
        
        # Clean up disconnected users
        for user_id in disconnected_users:
            await self.disconnect(user_id)
        
        logger.debug(f"Broadcasted to all users: {message.get('type', 'unknown')} to {len(users_to_notify)} users")

    async def handle_typing_indicator(self, user_id: int, conversation_id: int, is_typing: bool, user_name: str = "User"):
        """Handle typing indicator updates"""
        if conversation_id not in self.typing_users:
            self.typing_users[conversation_id] = set()
        
        if is_typing:
            self.typing_users[conversation_id].add(user_id)
        else:
            self.typing_users[conversation_id].discard(user_id)
        
        # Broadcast typing indicator to conversation participants
        await self.broadcast_to_conversation(
            conversation_id,
            {
                'type': 'typing_indicator',
                'user_id': user_id,
                'user_name': user_name,
                'is_typing': is_typing
            },
            exclude_user=user_id
        )

    async def handle_message_update(self, conversation_id: int, message_data: Dict[str, Any]):
        """Handle new message notifications"""
        await self.broadcast_to_conversation(
            conversation_id,
            {
                'type': 'new_message',
                'message': message_data
            }
        )

    async def handle_conversation_update(self, conversation_id: int, update_data: Dict[str, Any]):
        """Handle conversation status updates"""
        await self.broadcast_to_conversation(
            conversation_id,
            {
                'type': 'conversation_update',
                'update': update_data
            }
        )

    async def handle_presence_update(self, user_id: int, status: str):
        """Handle user presence updates"""
        if user_id in self.user_presence:
            self.user_presence[user_id]['status'] = status
            self.user_presence[user_id]['last_seen'] = datetime.utcnow()
        
        await self._broadcast_presence_update(user_id, status)

    async def _broadcast_presence_update(self, user_id: int, status: str):
        """Broadcast presence update to relevant users"""
        # Find all conversations this user is part of
        relevant_conversations = []
        for conversation_id, users in self.conversation_users.items():
            if user_id in users:
                relevant_conversations.append(conversation_id)
        
        # Broadcast to users in those conversations
        notified_users = set()
        for conversation_id in relevant_conversations:
            for other_user_id in self.conversation_users[conversation_id]:
                if other_user_id != user_id and other_user_id not in notified_users:
                    await self.send_personal_message(
                        other_user_id,
                        {
                            'type': 'presence_update',
                            'user_id': user_id,
                            'status': status,
                            'conversation_id': conversation_id
                        }
                    )
                    notified_users.add(other_user_id)

    def get_conversation_participants(self, conversation_id: int) -> List[int]:
        """Get list of users currently in a conversation"""
        return list(self.conversation_users.get(conversation_id, set()))

    def get_online_users(self) -> List[int]:
        """Get list of currently online users"""
        return [
            user_id for user_id, presence in self.user_presence.items()
            if presence.get('status') == 'online'
        ]

    def get_user_presence(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get presence information for a specific user"""
        return self.user_presence.get(user_id)

    def is_user_online(self, user_id: int) -> bool:
        """Check if a user is currently online"""
        return user_id in self.active_connections

    def get_typing_users(self, conversation_id: int) -> List[int]:
        """Get list of users currently typing in a conversation"""
        return list(self.typing_users.get(conversation_id, set()))


class WebSocketMessagingService:
    """Service for managing WebSocket messaging operations"""
    
    def __init__(self):
        self.connection_manager = ConnectionManager()

    async def handle_websocket_connection(self, websocket: WebSocket, user_id: int):
        """Handle a new WebSocket connection"""
        await self.connection_manager.connect(websocket, user_id)
        
        try:
            while True:
                # Receive message from client
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # Handle different message types
                await self._handle_client_message(user_id, message)
                
        except WebSocketDisconnect:
            await self.connection_manager.disconnect(user_id)
        except Exception as e:
            logger.error(f"WebSocket error for user {user_id}: {e}")
            await self.connection_manager.disconnect(user_id)

    async def _handle_client_message(self, user_id: int, message: Dict[str, Any]):
        """Handle incoming message from client"""
        message_type = message.get('type')
        
        if message_type == 'join_conversation':
            conversation_id = message.get('conversation_id')
            if conversation_id:
                await self.connection_manager.join_conversation(user_id, conversation_id)
        
        elif message_type == 'leave_conversation':
            conversation_id = message.get('conversation_id')
            if conversation_id:
                await self.connection_manager.leave_conversation(user_id, conversation_id)
        
        elif message_type == 'typing_indicator':
            conversation_id = message.get('conversation_id')
            is_typing = message.get('is_typing', False)
            user_name = message.get('user_name', 'User')
            if conversation_id is not None:
                await self.connection_manager.handle_typing_indicator(
                    user_id, conversation_id, is_typing, user_name
                )
        
        elif message_type == 'presence_update':
            status = message.get('status', 'online')
            await self.connection_manager.handle_presence_update(user_id, status)
        
        else:
            logger.warning(f"Unknown message type from user {user_id}: {message_type}")

    # Public API methods for other services to use

    async def notify_new_message(self, conversation_id: int, message_data: Dict[str, Any]):
        """Notify users of a new message in a conversation"""
        await self.connection_manager.handle_message_update(conversation_id, message_data)

    async def notify_conversation_update(self, conversation_id: int, update_data: Dict[str, Any]):
        """Notify users of conversation status changes"""
        await self.connection_manager.handle_conversation_update(conversation_id, update_data)

    async def send_notification(self, user_id: int, notification: Dict[str, Any]):
        """Send a notification to a specific user"""
        await self.connection_manager.send_personal_message(user_id, {
            'type': 'notification',
            'notification': notification
        })

    async def broadcast_system_message(self, message: str, conversation_id: Optional[int] = None):
        """Broadcast a system message"""
        system_message = {
            'type': 'system_message',
            'message': message,
            'severity': 'info'
        }
        
        if conversation_id:
            await self.connection_manager.broadcast_to_conversation(conversation_id, system_message)
        else:
            await self.connection_manager.broadcast_to_all(system_message)

    def get_online_users(self) -> List[int]:
        """Get list of currently online users"""
        return self.connection_manager.get_online_users()

    def is_user_online(self, user_id: int) -> bool:
        """Check if a user is currently online"""
        return self.connection_manager.is_user_online(user_id)

    def get_conversation_participants(self, conversation_id: int) -> List[int]:
        """Get active participants in a conversation"""
        return self.connection_manager.get_conversation_participants(conversation_id)


# Global instance
websocket_messaging_service = WebSocketMessagingService()