"""
Billing repository for invoices, payments, subscriptions, and tutor payouts.
"""

import asyncpg
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, date, timedelta
from decimal import Decimal

from app.database.repositories.base import BaseRepository
from app.core.exceptions import ResourceNotFoundError, BusinessLogicError
from app.core.timezone import now_est

logger = logging.getLogger(__name__)


class BillingRepository(BaseRepository):
    """Repository for billing and payment management."""
    
    def __init__(self):
        super().__init__(table_name="billing_invoices", id_column="invoice_id")
    
    async def generate_invoice_number(self, conn: asyncpg.Connection) -> str:
        """
        Generate unique invoice number.
        
        Args:
            conn: Database connection
        
        Returns:
            Formatted invoice number
        """
        query = """
            SELECT COALESCE(MAX(
                CAST(SPLIT_PART(invoice_number, '-', 3) AS INTEGER)
            ), 0) + 1 as next_number
            FROM billing_invoices
            WHERE invoice_number LIKE $1
        """
        
        current_year = datetime.now().year
        prefix = f"TUTORAIDE-{current_year}-%"
        
        try:
            next_number = await conn.fetchval(query, prefix)
            invoice_number = f"TUTORAIDE-{current_year}-{next_number:06d}"
            return invoice_number
        except Exception as e:
            logger.error(f"Error generating invoice number: {e}")
            raise
    
    async def create_invoice_for_appointment(
        self,
        conn: asyncpg.Connection,
        client_id: int,
        appointment_id: int,
        amount: Decimal,
        due_days: int = 30
    ) -> asyncpg.Record:
        """
        Create invoice for a completed appointment.
        
        Args:
            conn: Database connection
            client_id: Client ID
            appointment_id: Appointment ID
            amount: Invoice amount
            due_days: Days until due date
        
        Returns:
            Created invoice record
        """
        try:
            async with conn.transaction():
                # Generate invoice number
                invoice_number = await self.generate_invoice_number(conn)
                
                # Calculate due date
                due_date = date.today() + timedelta(days=due_days)
                
                # Create invoice
                invoice_data = {
                    "invoice_number": invoice_number,
                    "client_id": client_id,
                    "appointment_id": appointment_id,
                    "amount": amount,
                    "currency": "CAD",
                    "status": "pending",
                    "due_date": due_date
                }
                
                invoice = await self.create(conn, invoice_data)
                
                logger.info(f"Created invoice {invoice_number} for appointment {appointment_id}")
                return invoice
                
        except Exception as e:
            logger.error(f"Error creating invoice for appointment: {e}")
            raise
    
    async def find_pending_invoices(
        self,
        conn: asyncpg.Connection,
        client_id: Optional[int] = None,
        overdue_only: bool = False
    ) -> List[asyncpg.Record]:
        """
        Find pending invoices for a client or all clients.
        
        Args:
            conn: Database connection
            client_id: Optional client ID filter
            overdue_only: Whether to return only overdue invoices
        
        Returns:
            List of pending invoice records
        """
        query = f"""
            SELECT 
                bi.*,
                cp.first_name as client_first_name,
                cp.last_name as client_last_name,
                ap.scheduled_date,
                sc.service_name
            FROM {self.table_name} bi
            JOIN client_profiles cp ON bi.client_id = cp.client_id
            LEFT JOIN appointment_sessions ap ON bi.appointment_id = ap.appointment_id
            LEFT JOIN service_catalog sc ON ap.service_id = sc.service_id
            WHERE bi.status = 'pending'
        """
        
        params = []
        
        if client_id:
            params.append(client_id)
            query += f" AND bi.client_id = ${len(params)}"
        
        if overdue_only:
            query += " AND bi.due_date < CURRENT_DATE"
        
        query += " ORDER BY bi.due_date, bi.created_at"
        
        try:
            results = await conn.fetch(query, *params)
            return results
        except Exception as e:
            logger.error(f"Error finding pending invoices: {e}")
            raise
    
    async def record_payment(
        self,
        conn: asyncpg.Connection,
        invoice_id: int,
        paying_client_id: int,
        amount: Decimal,
        stripe_payment_id: Optional[str] = None,
        payment_method: str = "card"
    ) -> asyncpg.Record:
        """
        Record a payment against an invoice.
        
        Args:
            conn: Database connection
            invoice_id: Invoice ID
            paying_client_id: ID of client who paid
            amount: Payment amount
            stripe_payment_id: Stripe payment ID
            payment_method: Payment method
        
        Returns:
            Payment record
        """
        try:
            async with conn.transaction():
                # Create payment record
                payment_data = {
                    "invoice_id": invoice_id,
                    "paying_client_id": paying_client_id,
                    "stripe_payment_id": stripe_payment_id,
                    "amount": amount,
                    "payment_method": payment_method,
                    "status": "paid"
                }
                
                payment_query = """
                    INSERT INTO billing_payments 
                    (invoice_id, paying_client_id, stripe_payment_id, amount, 
                     payment_date, payment_method, status, created_at)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                    RETURNING *
                """
                
                now = now_est()
                payment = await conn.fetchrow(
                    payment_query,
                    payment_data["invoice_id"],
                    payment_data["paying_client_id"],
                    payment_data["stripe_payment_id"],
                    payment_data["amount"],
                    now,
                    payment_data["payment_method"],
                    payment_data["status"],
                    now
                )
                
                # Update invoice status
                await self.update(conn, invoice_id, {
                    "status": "paid",
                    "paid_at": now
                })
                
                logger.info(f"Recorded payment {payment['payment_id']} for invoice {invoice_id}")
                return payment
                
        except Exception as e:
            logger.error(f"Error recording payment: {e}")
            raise
    
    async def get_client_subscription(
        self,
        conn: asyncpg.Connection,
        client_id: int,
        service_id: int
    ) -> Optional[asyncpg.Record]:
        """
        Get active subscription for a client and service.
        
        Args:
            conn: Database connection
            client_id: Client ID
            service_id: Service ID
        
        Returns:
            Active subscription record if found
        """
        query = """
            SELECT bs.*, sc.service_name
            FROM billing_subscriptions bs
            JOIN service_catalog sc ON bs.service_id = sc.service_id
            WHERE bs.client_id = $1 
            AND bs.service_id = $2
            AND bs.is_active = true
            AND bs.expires_at > CURRENT_DATE
            AND bs.hours_remaining > 0
        """
        
        try:
            result = await conn.fetchrow(query, client_id, service_id)
            return result
        except Exception as e:
            logger.error(f"Error getting client subscription: {e}")
            raise
    
    async def deduct_subscription_hours(
        self,
        conn: asyncpg.Connection,
        subscription_id: int,
        hours_used: Decimal
    ) -> Optional[asyncpg.Record]:
        """
        Deduct hours from a subscription.
        
        Args:
            conn: Database connection
            subscription_id: Subscription ID
            hours_used: Hours to deduct
        
        Returns:
            Updated subscription record
        """
        query = """
            UPDATE billing_subscriptions
            SET 
                hours_remaining = hours_remaining - $1,
                updated_at = $2,
                is_active = CASE 
                    WHEN hours_remaining - $1 <= 0 THEN false 
                    ELSE true 
                END
            WHERE subscription_id = $3
            AND hours_remaining >= $1
            RETURNING *
        """
        
        try:
            result = await conn.fetchrow(
                query, hours_used, now_est(), subscription_id
            )
            if result:
                logger.info(f"Deducted {hours_used} hours from subscription {subscription_id}")
            return result
        except Exception as e:
            logger.error(f"Error deducting subscription hours: {e}")
            raise
    
    async def create_subscription(
        self,
        conn: asyncpg.Connection,
        client_id: int,
        service_id: int,
        hours_purchased: Decimal,
        expires_at: date
    ) -> asyncpg.Record:
        """
        Create a new subscription.
        
        Args:
            conn: Database connection
            client_id: Client ID
            service_id: Service ID
            hours_purchased: Number of hours purchased
            expires_at: Expiration date
        
        Returns:
            Created subscription record
        """
        subscription_data = {
            "client_id": client_id,
            "service_id": service_id,
            "hours_purchased": hours_purchased,
            "hours_remaining": hours_purchased,
            "expires_at": expires_at,
            "is_active": True
        }
        
        query = """
            INSERT INTO billing_subscriptions 
            (client_id, service_id, hours_purchased, hours_remaining, 
             expires_at, is_active, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            RETURNING *
        """
        
        now = now_est()
        
        try:
            result = await conn.fetchrow(
                query,
                subscription_data["client_id"],
                subscription_data["service_id"],
                subscription_data["hours_purchased"],
                subscription_data["hours_remaining"],
                subscription_data["expires_at"],
                subscription_data["is_active"],
                now,
                now
            )
            
            logger.info(f"Created subscription {result['subscription_id']} for client {client_id}")
            return result
            
        except Exception as e:
            logger.error(f"Error creating subscription: {e}")
            raise
    
    async def get_tutor_earnings_for_period(
        self,
        conn: asyncpg.Connection,
        tutor_id: int,
        start_date: date,
        end_date: date
    ) -> Dict[str, Any]:
        """
        Calculate tutor earnings for a period.
        
        Args:
            conn: Database connection
            tutor_id: Tutor ID
            start_date: Period start date
            end_date: Period end date
        
        Returns:
            Dictionary with earnings summary
        """
        query = """
            SELECT 
                COUNT(ap.appointment_id) as session_count,
                SUM(
                    EXTRACT(EPOCH FROM (ap.end_time - ap.start_time)) / 3600.0 * tsr.hourly_rate
                ) as total_earnings,
                SUM(
                    EXTRACT(EPOCH FROM (ap.end_time - ap.start_time)) / 3600.0
                ) as total_hours
            FROM appointment_sessions ap
            JOIN appointment_confirmations ac ON ap.appointment_id = ac.appointment_id
            JOIN tutor_service_rates tsr ON ap.tutor_id = tsr.tutor_id AND ap.service_id = tsr.service_id
            WHERE ap.tutor_id = $1
            AND ap.scheduled_date BETWEEN $2 AND $3
            AND ac.is_completed = true
            AND ap.status = 'completed'
        """
        
        try:
            result = await conn.fetchrow(query, tutor_id, start_date, end_date)
            
            return {
                "tutor_id": tutor_id,
                "period_start": start_date,
                "period_end": end_date,
                "session_count": result["session_count"] or 0,
                "total_hours": float(result["total_hours"] or 0),
                "total_earnings": float(result["total_earnings"] or 0)
            }
            
        except Exception as e:
            logger.error(f"Error calculating tutor earnings: {e}")
            raise
    
    async def create_tutor_payment_record(
        self,
        conn: asyncpg.Connection,
        tutor_id: int,
        period_start: date,
        period_end: date,
        total_hours: Decimal,
        total_amount: Decimal
    ) -> asyncpg.Record:
        """
        Create tutor payment record for a period.
        
        Args:
            conn: Database connection
            tutor_id: Tutor ID
            period_start: Payment period start
            period_end: Payment period end
            total_hours: Total hours worked
            total_amount: Total amount to pay
        
        Returns:
            Created payment record
        """
        query = """
            INSERT INTO billing_tutor_payments 
            (tutor_id, period_start, period_end, total_hours, total_amount, 
             status, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, 'pending', $6, $7)
            ON CONFLICT (tutor_id, period_start, period_end)
            DO UPDATE SET 
                total_hours = $4,
                total_amount = $5,
                status = 'pending',
                updated_at = $7
            RETURNING *
        """
        
        now = now_est()
        
        try:
            result = await conn.fetchrow(
                query, tutor_id, period_start, period_end, total_hours, 
                total_amount, now, now
            )
            
            logger.info(f"Created payment record for tutor {tutor_id}: ${total_amount}")
            return result
            
        except Exception as e:
            logger.error(f"Error creating tutor payment record: {e}")
            raise
    
    async def get_pending_tutor_payments(
        self,
        conn: asyncpg.Connection,
        period_start: Optional[date] = None,
        period_end: Optional[date] = None
    ) -> List[asyncpg.Record]:
        """
        Get pending tutor payments for approval.
        
        Args:
            conn: Database connection
            period_start: Optional period filter
            period_end: Optional period filter
        
        Returns:
            List of pending payment records
        """
        query = """
            SELECT 
                btp.*,
                tp.first_name,
                tp.last_name,
                ua.email
            FROM billing_tutor_payments btp
            JOIN tutor_profiles tp ON btp.tutor_id = tp.tutor_id
            JOIN user_accounts ua ON tp.user_id = ua.user_id
            WHERE btp.status = 'pending'
        """
        
        params = []
        
        if period_start:
            params.append(period_start)
            query += f" AND btp.period_start >= ${len(params)}"
        
        if period_end:
            params.append(period_end)
            query += f" AND btp.period_end <= ${len(params)}"
        
        query += " ORDER BY btp.period_end DESC, tp.last_name, tp.first_name"
        
        try:
            results = await conn.fetch(query, *params)
            return results
        except Exception as e:
            logger.error(f"Error getting pending tutor payments: {e}")
            raise
    
    async def approve_tutor_payment(
        self,
        conn: asyncpg.Connection,
        tutor_payment_id: int,
        stripe_payout_id: Optional[str] = None
    ) -> Optional[asyncpg.Record]:
        """
        Approve and mark tutor payment as paid.
        
        Args:
            conn: Database connection
            tutor_payment_id: Payment record ID
            stripe_payout_id: Stripe payout ID
        
        Returns:
            Updated payment record
        """
        query = """
            UPDATE billing_tutor_payments
            SET 
                status = 'paid',
                stripe_payout_id = $1,
                paid_at = $2,
                updated_at = $3
            WHERE tutor_payment_id = $4
            AND status = 'pending'
            RETURNING *
        """
        
        now = now_est()
        
        try:
            result = await conn.fetchrow(
                query, stripe_payout_id, now, now, tutor_payment_id
            )
            if result:
                logger.info(f"Approved payment {tutor_payment_id}")
            return result
        except Exception as e:
            logger.error(f"Error approving tutor payment: {e}")
            raise
    
    async def get_billing_summary(
        self,
        conn: asyncpg.Connection,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> Dict[str, Any]:
        """
        Get billing summary for a period.
        
        Args:
            conn: Database connection
            start_date: Optional start date
            end_date: Optional end date
        
        Returns:
            Dictionary with billing summary
        """
        # Default to current month
        if not start_date:
            today = date.today()
            start_date = today.replace(day=1)
        
        if not end_date:
            if start_date.month == 12:
                end_date = start_date.replace(year=start_date.year + 1, month=1, day=1) - timedelta(days=1)
            else:
                end_date = start_date.replace(month=start_date.month + 1, day=1) - timedelta(days=1)
        
        query = """
            SELECT 
                COUNT(DISTINCT bi.invoice_id) as total_invoices,
                COUNT(DISTINCT CASE WHEN bi.status = 'paid' THEN bi.invoice_id END) as paid_invoices,
                COUNT(DISTINCT CASE WHEN bi.status = 'pending' THEN bi.invoice_id END) as pending_invoices,
                COALESCE(SUM(bi.amount), 0) as total_revenue,
                COALESCE(SUM(CASE WHEN bi.status = 'paid' THEN bi.amount ELSE 0 END), 0) as paid_revenue,
                COALESCE(SUM(CASE WHEN bi.status = 'pending' THEN bi.amount ELSE 0 END), 0) as pending_revenue,
                COUNT(DISTINCT btp.tutor_payment_id) as tutor_payments,
                COALESCE(SUM(btp.total_amount), 0) as tutor_payout_total
            FROM billing_invoices bi
            LEFT JOIN billing_tutor_payments btp ON (
                btp.period_start >= $1 AND btp.period_end <= $2
            )
            WHERE bi.created_at::date BETWEEN $1 AND $2
        """
        
        try:
            result = await conn.fetchrow(query, start_date, end_date)
            
            return {
                "period_start": start_date,
                "period_end": end_date,
                "invoices": {
                    "total": result["total_invoices"],
                    "paid": result["paid_invoices"],
                    "pending": result["pending_invoices"]
                },
                "revenue": {
                    "total": float(result["total_revenue"]),
                    "paid": float(result["paid_revenue"]),
                    "pending": float(result["pending_revenue"])
                },
                "tutor_payouts": {
                    "count": result["tutor_payments"],
                    "total": float(result["tutor_payout_total"])
                },
                "net_profit": float(result["paid_revenue"]) - float(result["tutor_payout_total"])
            }
            
        except Exception as e:
            logger.error(f"Error getting billing summary: {e}")
            raise