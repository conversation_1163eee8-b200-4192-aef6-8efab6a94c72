"""
Tutor Matching API endpoints for finding the nearest tutors based on location and preferences.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel, Field, field_validator
import logging

from app.services.tutor_matching_service import TutorMatchingService, get_tutor_matching_service, TutorSearchCriteria
from app.core.dependencies import get_current_user
from app.core.exceptions import ValidationError, ResourceNotFoundError

logger = logging.getLogger(__name__)

router = APIRouter()

# Request/Response Models
class TutorMatchResponse(BaseModel):
    """Tutor match response model."""
    tutor_id: int
    first_name: str
    last_name: str
    email: str
    postal_code: str
    distance_km: float
    average_rating: Optional[float]
    total_sessions: int
    hourly_rate: Optional[float]
    specialties: List[str]
    service_types: List[str]
    availability_status: str
    profile_picture: Optional[str]
    bio: Optional[str]
    experience_years: Optional[int]
    education: Optional[str]
    languages: List[str]
    latitude: float
    longitude: float
    relevance_score: float

class NearestTutorsRequest(BaseModel):
    """Request model for finding nearest tutors."""
    client_postal_code: str = Field(..., description="Client's postal code")
    subject_areas: List[str] = Field(..., description="Subject areas to search for")
    service_types: Optional[List[str]] = Field(None, description="Preferred service types")
    max_distance_km: float = Field(15.0, ge=1, le=100, description="Maximum distance in kilometers")
    max_results: int = Field(5, ge=1, le=20, description="Maximum number of results")
    min_rating: Optional[float] = Field(None, ge=0, le=5, description="Minimum rating filter")
    max_hourly_rate: Optional[float] = Field(None, ge=0, description="Maximum hourly rate filter")
    availability_filter: Optional[str] = Field(None, pattern="^(all|available|limited)$", description="Availability filter")
    
    @field_validator('client_postal_code')
    def validate_postal_code(cls, v):
        if not v or len(v.strip()) < 3:
            raise ValueError('Postal code must be at least 3 characters')
        return v.strip().upper()
    
    @field_validator('subject_areas')
    def validate_subject_areas(cls, v):
        if not v:
            raise ValueError('At least one subject area is required')
        return [area.strip() for area in v if area.strip()]

class TutorAvailabilityResponse(BaseModel):
    """Response model for tutor availability in an area."""
    postal_code: str
    radius_km: float
    available: int
    limited: int
    busy: int
    total: int

class NearestTutorsResponse(BaseModel):
    """Response model for nearest tutors search."""
    matches: List[TutorMatchResponse]
    total_found: int
    search_criteria: dict
    center_coordinates: dict

# API Endpoints
@router.post("/nearest", response_model=NearestTutorsResponse)
async def find_nearest_tutors(
    request: NearestTutorsRequest,
    matching_service: TutorMatchingService = Depends(get_tutor_matching_service),
    current_user = Depends(get_current_user)
):
    """
    Find the nearest tutors based on client location and preferences.
    
    This endpoint implements the core tutor matching algorithm that finds
    the 5 nearest tutors (or specified max_results) based on:
    - Geographic proximity to client postal code
    - Subject area expertise
    - Service type preferences
    - Rating and rate filters
    - Availability status
    
    The results are scored by relevance using a weighted algorithm that
    considers distance, rating, subject match, and experience.
    """
    try:
        # Convert request to search criteria
        criteria = TutorSearchCriteria(
            client_postal_code=request.client_postal_code,
            subject_areas=request.subject_areas,
            service_types=request.service_types,
            max_distance_km=request.max_distance_km,
            max_results=request.max_results,
            min_rating=request.min_rating,
            max_hourly_rate=request.max_hourly_rate,
            availability_filter=request.availability_filter
        )
        
        # Find matching tutors
        matches = await matching_service.find_nearest_tutors(criteria)
        
        # Get center coordinates for reference
        from app.services.geocoding_service import get_geocoding_service
        geocoding_service = await get_geocoding_service()
        center_coords = await geocoding_service.geocode_postal_code(request.client_postal_code)
        
        # Convert to response format
        tutor_matches = [
            TutorMatchResponse(
                tutor_id=match.tutor_id,
                first_name=match.first_name,
                last_name=match.last_name,
                email=match.email,
                postal_code=match.postal_code,
                distance_km=round(match.distance_km, 2),
                average_rating=match.average_rating,
                total_sessions=match.total_sessions,
                hourly_rate=float(match.hourly_rate) if match.hourly_rate else None,
                specialties=match.specialties,
                service_types=match.service_types,
                availability_status=match.availability_status,
                profile_picture=match.profile_picture,
                bio=match.bio,
                experience_years=match.experience_years,
                education=match.education,
                languages=match.languages,
                latitude=match.latitude,
                longitude=match.longitude,
                relevance_score=match.relevance_score
            )
            for match in matches
        ]
        
        response = NearestTutorsResponse(
            matches=tutor_matches,
            total_found=len(tutor_matches),
            search_criteria={
                "postal_code": request.client_postal_code,
                "subject_areas": request.subject_areas,
                "max_distance_km": request.max_distance_km,
                "max_results": request.max_results
            },
            center_coordinates=center_coords or {}
        )
        
        logger.info(f"Found {len(tutor_matches)} tutor matches for postal code {request.client_postal_code}")
        return response
        
    except ValidationError as e:
        logger.warning(f"Validation error in nearest tutors search: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except ResourceNotFoundError as e:
        logger.warning(f"No tutors found: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error finding nearest tutors: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/by-subject", response_model=List[TutorMatchResponse])
async def find_tutors_by_subject(
    postal_code: str = Query(..., description="Client postal code"),
    subject: str = Query(..., description="Subject area"),
    max_results: int = Query(5, ge=1, le=10, description="Maximum results"),
    matching_service: TutorMatchingService = Depends(get_tutor_matching_service),
    current_user = Depends(get_current_user)
):
    """
    Simple endpoint to find tutors by subject area near a postal code.
    
    This is a simplified version of the nearest tutors endpoint that
    only requires a postal code and subject area.
    """
    try:
        matches = await matching_service.find_tutors_by_subject(
            postal_code=postal_code.strip().upper(),
            subject_area=subject.strip(),
            max_results=max_results
        )
        
        return [
            TutorMatchResponse(
                tutor_id=match.tutor_id,
                first_name=match.first_name,
                last_name=match.last_name,
                email=match.email,
                postal_code=match.postal_code,
                distance_km=round(match.distance_km, 2),
                average_rating=match.average_rating,
                total_sessions=match.total_sessions,
                hourly_rate=float(match.hourly_rate) if match.hourly_rate else None,
                specialties=match.specialties,
                service_types=match.service_types,
                availability_status=match.availability_status,
                profile_picture=match.profile_picture,
                bio=match.bio,
                experience_years=match.experience_years,
                education=match.education,
                languages=match.languages,
                latitude=match.latitude,
                longitude=match.longitude,
                relevance_score=match.relevance_score
            )
            for match in matches
        ]
        
    except ValidationError as e:
        logger.warning(f"Validation error in subject search: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except ResourceNotFoundError as e:
        logger.warning(f"No tutors found for subject search: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error finding tutors by subject: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/availability/{postal_code}", response_model=TutorAvailabilityResponse)
async def get_tutor_availability_in_area(
    postal_code: str,
    radius_km: float = Query(10.0, ge=1, le=50, description="Search radius in kilometers"),
    matching_service: TutorMatchingService = Depends(get_tutor_matching_service),
    current_user = Depends(get_current_user)
):
    """
    Get tutor availability statistics within a radius of a postal code.
    
    Returns counts of tutors by availability status in the specified area.
    Useful for understanding tutor density and availability in different regions.
    """
    try:
        availability_stats = await matching_service.get_tutor_availability_in_radius(
            postal_code=postal_code.strip().upper(),
            radius_km=radius_km
        )
        
        return TutorAvailabilityResponse(
            postal_code=postal_code.strip().upper(),
            radius_km=radius_km,
            available=availability_stats.get('available', 0),
            limited=availability_stats.get('limited', 0),
            busy=availability_stats.get('busy', 0),
            total=availability_stats.get('total', 0)
        )
        
    except ValidationError as e:
        logger.warning(f"Validation error in availability check: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error getting tutor availability: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/quick-match")
async def quick_tutor_match(
    postal_code: str = Query(..., description="Client postal code"),
    subject: str = Query(..., description="Subject area"),
    service_type: Optional[str] = Query(None, description="Preferred service type"),
    matching_service: TutorMatchingService = Depends(get_tutor_matching_service),
    current_user = Depends(get_current_user)
):
    """
    Quick tutor matching endpoint for simple use cases.
    
    Returns the single best tutor match for a postal code and subject.
    Useful for widgets or quick recommendations.
    """
    try:
        criteria = TutorSearchCriteria(
            client_postal_code=postal_code.strip().upper(),
            subject_areas=[subject.strip()],
            service_types=[service_type] if service_type else None,
            max_results=1
        )
        
        matches = await matching_service.find_nearest_tutors(criteria)
        
        if not matches:
            raise HTTPException(status_code=404, detail="No tutors found for the specified criteria")
        
        best_match = matches[0]
        
        return {
            "tutor_id": best_match.tutor_id,
            "name": f"{best_match.first_name} {best_match.last_name}",
            "distance_km": round(best_match.distance_km, 2),
            "rating": best_match.average_rating,
            "hourly_rate": float(best_match.hourly_rate) if best_match.hourly_rate else None,
            "availability": best_match.availability_status,
            "relevance_score": best_match.relevance_score
        }
        
    except ValidationError as e:
        logger.warning(f"Validation error in quick match: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in quick tutor match: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")