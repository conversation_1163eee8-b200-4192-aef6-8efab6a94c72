import React, { useState } from 'react';
import { Modal } from '../common/Modal';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '../common/TabsAdapter';
import { Badge } from '../common/Badge';
import Button from '../common/Button';
import { Card } from '../common/Card';
import { 
  User, Baby, Calendar, DollarSign, MessageSquare, MapPin,
  Mail, Phone, Globe, Clock, CreditCard, FileText, AlertCircle,
  Edit2, X, Users, BookOpen, TrendingUp, Award, Home, UserCheck
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import toast from 'react-hot-toast';

interface ClientProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
  client: any; // Will be properly typed with Client interface
}

export const ClientProfileModal: React.FC<ClientProfileModalProps> = ({ isOpen, onClose, client }) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('overview');

  if (!client) return null;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD',
    }).format(amount);
  };

  const formatPhoneNumber = (phone: string) => {
    if (!phone) return 'Not provided';
    // Format as (XXX) XXX-XXXX
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length === 10) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    }
    return phone;
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`${client.firstName} ${client.lastName} - Client Profile`}
      size="xl"
    >
      <div className="h-[700px] flex flex-col">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
          <TabsList className="grid grid-cols-6 w-full">
            <TabsTrigger value="overview">
              <User className="w-4 h-4 mr-2" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="dependants">
              <Baby className="w-4 h-4 mr-2" />
              Dependants
            </TabsTrigger>
            <TabsTrigger value="appointments">
              <Calendar className="w-4 h-4 mr-2" />
              Appointments
            </TabsTrigger>
            <TabsTrigger value="billing">
              <DollarSign className="w-4 h-4 mr-2" />
              Billing
            </TabsTrigger>
            <TabsTrigger value="progress">
              <TrendingUp className="w-4 h-4 mr-2" />
              Progress
            </TabsTrigger>
            <TabsTrigger value="messages">
              <MessageSquare className="w-4 h-4 mr-2" />
              Messages
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="mt-6 space-y-6 overflow-y-auto max-h-[580px]">
            {/* Profile Header */}
            <div className="flex items-start gap-6">
              {client.profilePicture ? (
                <img 
                  src={client.profilePicture} 
                  alt={`${client.firstName} ${client.lastName}`}
                  className="w-24 h-24 rounded-full object-cover border-4 border-gray-200"
                />
              ) : (
                <div className="w-24 h-24 bg-accent-red bg-opacity-10 rounded-full flex items-center justify-center">
                  <User className="w-12 h-12 text-accent-red" />
                </div>
              )}
              
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <h3 className="text-2xl font-semibold text-gray-900">
                    {client.firstName} {client.lastName}
                  </h3>
                  <Badge className={client.status === 'active' ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'}>
                    {client.status || 'Active'}
                  </Badge>
                </div>
                
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div className="flex items-center gap-2 text-gray-600">
                    <Mail className="w-4 h-4" />
                    {client.email}
                  </div>
                  <div className="flex items-center gap-2 text-gray-600">
                    <Phone className="w-4 h-4" />
                    {formatPhoneNumber(client.phone)}
                  </div>
                  <div className="flex items-center gap-2 text-gray-600">
                    <MapPin className="w-4 h-4" />
                    {client.postalCode || 'Not provided'}
                  </div>
                  <div className="flex items-center gap-2 text-gray-600">
                    <Calendar className="w-4 h-4" />
                    Member since {new Date(client.createdAt || Date.now()).toLocaleDateString()}
                  </div>
                </div>

                {/* Stats Row */}
                <div className="flex items-center gap-6 mt-4">
                  <div className="text-gray-600">
                    <span className="font-semibold">{client.totalSessions || 0}</span> sessions
                  </div>
                  <div className="text-gray-600">
                    <span className="font-semibold">{client.activeDependants || 0}</span> dependants
                  </div>
                  <div className="text-gray-600">
                    <span className="font-semibold">{client.assignedTutors || 0}</span> tutors
                  </div>
                </div>
              </div>
            </div>

            {/* Contact Information */}
            <Card className="p-4">
              <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                <Phone className="w-5 h-5 mr-2" />
                Contact Information
              </h4>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-600">Primary Email</p>
                  <p className="font-medium">{client.email}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Phone Number</p>
                  <p className="font-medium">{formatPhoneNumber(client.phone)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Preferred Language</p>
                  <p className="font-medium">{client.preferredLanguage === 'fr' ? 'Français' : 'English'}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Communication Preference</p>
                  <p className="font-medium">{client.communicationPreference || 'Email'}</p>
                </div>
              </div>
            </Card>

            {/* Address Information */}
            <Card className="p-4">
              <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                <Home className="w-5 h-5 mr-2" />
                Address Information
              </h4>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-600">Street Address</p>
                  <p className="font-medium">{client.address || 'Not provided'}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">City</p>
                  <p className="font-medium">{client.city || 'Montreal'}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Province</p>
                  <p className="font-medium">{client.province || 'Quebec'}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Postal Code</p>
                  <p className="font-medium">{client.postalCode || 'Not provided'}</p>
                </div>
              </div>
            </Card>

            {/* Emergency Contact */}
            <Card className="p-4">
              <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                <AlertCircle className="w-5 h-5 mr-2" />
                Emergency Contact
              </h4>
              
              {client.emergencyContactName ? (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">Contact Name</p>
                    <p className="font-medium">{client.emergencyContactName}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Contact Phone</p>
                    <p className="font-medium">{formatPhoneNumber(client.emergencyContactPhone)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Relationship</p>
                    <p className="font-medium">{client.emergencyContactRelationship || 'Not specified'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Alternate Phone</p>
                    <p className="font-medium">{client.emergencyContactAltPhone || 'Not provided'}</p>
                  </div>
                </div>
              ) : (
                <p className="text-gray-500">No emergency contact information provided</p>
              )}
            </Card>

            {/* Account Settings */}
            <Card className="p-4">
              <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                <UserCheck className="w-5 h-5 mr-2" />
                Account Settings
              </h4>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Email Notifications</span>
                  <Badge className={client.emailNotifications ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'}>
                    {client.emailNotifications ? 'Enabled' : 'Disabled'}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">SMS Notifications</span>
                  <Badge className={client.smsNotifications ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'}>
                    {client.smsNotifications ? 'Enabled' : 'Disabled'}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Marketing Communications</span>
                  <Badge className={client.marketingConsent ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'}>
                    {client.marketingConsent ? 'Opted In' : 'Opted Out'}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Two-Factor Authentication</span>
                  <Badge className={client.twoFactorEnabled ? 'bg-green-100 text-green-700' : 'bg-yellow-100 text-yellow-700'}>
                    {client.twoFactorEnabled ? 'Enabled' : 'Not Enabled'}
                  </Badge>
                </div>
              </div>
            </Card>
          </TabsContent>

          {/* Dependants Tab */}
          <TabsContent value="dependants" className="mt-6 space-y-6">
            <Card className="p-6">
              <h4 className="font-medium text-gray-900 mb-4 flex items-center">
                <Baby className="w-5 h-5 mr-2" />
                Registered Dependants
              </h4>
              
              {/* Mock dependants data - replace with actual data */}
              <div className="space-y-4">
                <div className="border rounded-lg p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <h5 className="font-semibold text-gray-900">Emma Dubois</h5>
                      <p className="text-sm text-gray-600">Age 10 • Grade 5</p>
                    </div>
                    <Badge className="bg-green-100 text-green-700">Active</Badge>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600">Primary Subject</p>
                      <p className="font-medium">Mathematics</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Learning Needs</p>
                      <p className="font-medium">Homework support, Test preparation</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Assigned Tutor</p>
                      <p className="font-medium">Sarah Johnson</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Sessions Completed</p>
                      <p className="font-medium">24 sessions</p>
                    </div>
                  </div>
                  
                  <div className="mt-3">
                    <p className="text-sm text-gray-600">Notes</p>
                    <p className="text-sm">Prefers visual learning methods. Shows improvement in problem-solving skills.</p>
                  </div>
                </div>
                
                <div className="border rounded-lg p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <h5 className="font-semibold text-gray-900">Lucas Dubois</h5>
                      <p className="text-sm text-gray-600">Age 8 • Grade 3</p>
                    </div>
                    <Badge className="bg-green-100 text-green-700">Active</Badge>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600">Primary Subject</p>
                      <p className="font-medium">French Reading</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Learning Needs</p>
                      <p className="font-medium">Reading comprehension, Vocabulary</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Assigned Tutor</p>
                      <p className="font-medium">Michael Chen</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Sessions Completed</p>
                      <p className="font-medium">18 sessions</p>
                    </div>
                  </div>
                  
                  <div className="mt-3">
                    <p className="text-sm text-gray-600">Notes</p>
                    <p className="text-sm">Enjoys interactive reading activities. Parents report increased confidence.</p>
                  </div>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <h4 className="font-medium text-gray-900 mb-4">Parent/Guardian Information</h4>
              
              <div className="space-y-4">
                <div className="p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <p className="font-medium">Primary Guardian</p>
                    <Badge className="bg-blue-100 text-blue-700">Account Holder</Badge>
                  </div>
                  <p className="text-sm text-gray-600">
                    {client.firstName} {client.lastName} • {formatPhoneNumber(client.phone)}
                  </p>
                </div>
                
                {client.secondaryGuardianName && (
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <p className="font-medium mb-2">Secondary Guardian</p>
                    <p className="text-sm text-gray-600">
                      {client.secondaryGuardianName} • {formatPhoneNumber(client.secondaryGuardianPhone)}
                    </p>
                  </div>
                )}
              </div>
            </Card>
          </TabsContent>

          {/* Appointments Tab */}
          <TabsContent value="appointments" className="mt-6 space-y-6">
            <div className="grid grid-cols-4 gap-4">
              <Card className="p-4">
                <p className="text-sm text-gray-600">Total Sessions</p>
                <p className="text-2xl font-semibold">{client.totalSessions || 0}</p>
              </Card>
              <Card className="p-4">
                <p className="text-sm text-gray-600">This Month</p>
                <p className="text-2xl font-semibold">12</p>
              </Card>
              <Card className="p-4">
                <p className="text-sm text-gray-600">Upcoming</p>
                <p className="text-2xl font-semibold">3</p>
              </Card>
              <Card className="p-4">
                <p className="text-sm text-gray-600">Attendance Rate</p>
                <p className="text-2xl font-semibold">96%</p>
              </Card>
            </div>

            <Card className="p-6">
              <h4 className="font-medium text-gray-900 mb-4 flex items-center">
                <Calendar className="w-5 h-5 mr-2" />
                Upcoming Appointments
              </h4>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">Math Tutoring - Emma</p>
                    <p className="text-sm text-gray-600">Nov 28, 2024 • 4:00 PM - 5:00 PM</p>
                    <p className="text-sm text-gray-600">Tutor: Sarah Johnson</p>
                  </div>
                  <Badge className="bg-green-100 text-green-700">Confirmed</Badge>
                </div>
                
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">French Reading - Lucas</p>
                    <p className="text-sm text-gray-600">Nov 29, 2024 • 3:30 PM - 4:30 PM</p>
                    <p className="text-sm text-gray-600">Tutor: Michael Chen</p>
                  </div>
                  <Badge className="bg-green-100 text-green-700">Confirmed</Badge>
                </div>
                
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">Math Tutoring - Emma</p>
                    <p className="text-sm text-gray-600">Dec 1, 2024 • 4:00 PM - 5:00 PM</p>
                    <p className="text-sm text-gray-600">Tutor: Sarah Johnson</p>
                  </div>
                  <Badge className="bg-yellow-100 text-yellow-700">Pending</Badge>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <h4 className="font-medium text-gray-900 mb-4">Recent Sessions</h4>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium">Math Tutoring - Emma</p>
                    <p className="text-sm text-gray-600">Nov 21, 2024 • Completed</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">{formatCurrency(55)}</p>
                    <p className="text-sm text-green-600">Paid</p>
                  </div>
                </div>
                
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium">French Reading - Lucas</p>
                    <p className="text-sm text-gray-600">Nov 20, 2024 • Completed</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">{formatCurrency(50)}</p>
                    <p className="text-sm text-green-600">Paid</p>
                  </div>
                </div>
              </div>
            </Card>
          </TabsContent>

          {/* Billing Tab */}
          <TabsContent value="billing" className="mt-6 space-y-6">
            <div className="grid grid-cols-4 gap-4">
              <Card className="p-4">
                <p className="text-sm text-gray-600">Total Spent</p>
                <p className="text-2xl font-semibold">{formatCurrency(client.totalSpent || 2450)}</p>
              </Card>
              <Card className="p-4">
                <p className="text-sm text-gray-600">This Month</p>
                <p className="text-2xl font-semibold">{formatCurrency(420)}</p>
              </Card>
              <Card className="p-4">
                <p className="text-sm text-gray-600">Outstanding</p>
                <p className="text-2xl font-semibold">{formatCurrency(0)}</p>
              </Card>
              <Card className="p-4">
                <p className="text-sm text-gray-600">Next Invoice</p>
                <p className="text-2xl font-semibold">{formatCurrency(210)}</p>
              </Card>
            </div>

            <Card className="p-6">
              <h4 className="font-medium text-gray-900 mb-4 flex items-center">
                <CreditCard className="w-5 h-5 mr-2" />
                Payment Methods
              </h4>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <CreditCard className="w-5 h-5 text-gray-500" />
                    <div>
                      <p className="font-medium">Visa ending in 4242</p>
                      <p className="text-sm text-gray-600">Expires 12/2025</p>
                    </div>
                  </div>
                  <Badge className="bg-green-100 text-green-700">Default</Badge>
                </div>
                
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <CreditCard className="w-5 h-5 text-gray-500" />
                    <div>
                      <p className="font-medium">Mastercard ending in 5555</p>
                      <p className="text-sm text-gray-600">Expires 06/2024</p>
                    </div>
                  </div>
                  <Button size="sm" variant="ghost">Set as Default</Button>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <h4 className="font-medium text-gray-900 mb-4">Recent Invoices</h4>
              
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead>
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Invoice #
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Amount
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Action
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    <tr>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">INV-2024-0145</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">Nov 15, 2024</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">{formatCurrency(210)}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Badge className="bg-green-100 text-green-700">Paid</Badge>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Button size="sm" variant="ghost">
                          <FileText className="w-4 h-4" />
                        </Button>
                      </td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">INV-2024-0112</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">Oct 15, 2024</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">{formatCurrency(315)}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Badge className="bg-green-100 text-green-700">Paid</Badge>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Button size="sm" variant="ghost">
                          <FileText className="w-4 h-4" />
                        </Button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </Card>

            <Card className="p-6">
              <h4 className="font-medium text-gray-900 mb-4">Subscription & Packages</h4>
              
              <div className="space-y-3">
                <div className="p-4 border rounded-lg bg-blue-50 border-blue-200">
                  <div className="flex items-center justify-between mb-2">
                    <h5 className="font-medium">TECFEE Math Program - Emma</h5>
                    <Badge className="bg-blue-100 text-blue-700">Active</Badge>
                  </div>
                  <p className="text-sm text-gray-600">12-session package • 8 sessions remaining</p>
                  <div className="mt-3 bg-blue-100 rounded-full overflow-hidden h-2">
                    <div className="bg-blue-600 h-full" style={{ width: '33%' }}></div>
                  </div>
                </div>
              </div>
            </Card>
          </TabsContent>

          {/* Progress Tab */}
          <TabsContent value="progress" className="mt-6 space-y-6">
            <Card className="p-6">
              <h4 className="font-medium text-gray-900 mb-4 flex items-center">
                <TrendingUp className="w-5 h-5 mr-2" />
                Learning Progress Overview
              </h4>
              
              <div className="space-y-6">
                {/* Emma's Progress */}
                <div>
                  <h5 className="font-medium text-gray-900 mb-3">Emma Dubois - Mathematics</h5>
                  
                  <div className="grid grid-cols-3 gap-4 mb-4">
                    <div className="text-center">
                      <p className="text-sm text-gray-600">Current Grade</p>
                      <p className="text-2xl font-semibold text-green-600">B+</p>
                      <p className="text-xs text-gray-500">Up from B</p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-gray-600">Improvement</p>
                      <p className="text-2xl font-semibold text-green-600">+15%</p>
                      <p className="text-xs text-gray-500">Last 3 months</p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-gray-600">Sessions</p>
                      <p className="text-2xl font-semibold">24</p>
                      <p className="text-xs text-gray-500">Total completed</p>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Problem Solving</span>
                        <span className="text-green-600">85%</span>
                      </div>
                      <div className="bg-gray-200 rounded-full h-2">
                        <div className="bg-green-500 h-2 rounded-full" style={{ width: '85%' }}></div>
                      </div>
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Algebra</span>
                        <span className="text-yellow-600">72%</span>
                      </div>
                      <div className="bg-gray-200 rounded-full h-2">
                        <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '72%' }}></div>
                      </div>
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Geometry</span>
                        <span className="text-green-600">90%</span>
                      </div>
                      <div className="bg-gray-200 rounded-full h-2">
                        <div className="bg-green-500 h-2 rounded-full" style={{ width: '90%' }}></div>
                      </div>
                    </div>
                  </div>
                </div>

                <hr className="border-gray-200" />

                {/* Lucas's Progress */}
                <div>
                  <h5 className="font-medium text-gray-900 mb-3">Lucas Dubois - French Reading</h5>
                  
                  <div className="grid grid-cols-3 gap-4 mb-4">
                    <div className="text-center">
                      <p className="text-sm text-gray-600">Reading Level</p>
                      <p className="text-2xl font-semibold text-blue-600">3.5</p>
                      <p className="text-xs text-gray-500">Grade equivalent</p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-gray-600">Words/Min</p>
                      <p className="text-2xl font-semibold text-green-600">85</p>
                      <p className="text-xs text-gray-500">Up from 65</p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-gray-600">Sessions</p>
                      <p className="text-2xl font-semibold">18</p>
                      <p className="text-xs text-gray-500">Total completed</p>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Comprehension</span>
                        <span className="text-green-600">78%</span>
                      </div>
                      <div className="bg-gray-200 rounded-full h-2">
                        <div className="bg-green-500 h-2 rounded-full" style={{ width: '78%' }}></div>
                      </div>
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Vocabulary</span>
                        <span className="text-yellow-600">65%</span>
                      </div>
                      <div className="bg-gray-200 rounded-full h-2">
                        <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '65%' }}></div>
                      </div>
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Fluency</span>
                        <span className="text-green-600">82%</span>
                      </div>
                      <div className="bg-gray-200 rounded-full h-2">
                        <div className="bg-green-500 h-2 rounded-full" style={{ width: '82%' }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <h4 className="font-medium text-gray-900 mb-4 flex items-center">
                <Award className="w-5 h-5 mr-2" />
                Achievements & Milestones
              </h4>
              
              <div className="space-y-3">
                <div className="flex items-center gap-3 p-3 bg-yellow-50 rounded-lg">
                  <Award className="w-8 h-8 text-yellow-600" />
                  <div>
                    <p className="font-medium">Math Champion</p>
                    <p className="text-sm text-gray-600">Emma scored 95% on her latest test!</p>
                    <p className="text-xs text-gray-500">Nov 20, 2024</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                  <BookOpen className="w-8 h-8 text-blue-600" />
                  <div>
                    <p className="font-medium">Reading Milestone</p>
                    <p className="text-sm text-gray-600">Lucas read his first chapter book!</p>
                    <p className="text-xs text-gray-500">Nov 15, 2024</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                  <TrendingUp className="w-8 h-8 text-green-600" />
                  <div>
                    <p className="font-medium">Consistent Progress</p>
                    <p className="text-sm text-gray-600">3 months of continuous improvement</p>
                    <p className="text-xs text-gray-500">Nov 1, 2024</p>
                  </div>
                </div>
              </div>
            </Card>
          </TabsContent>

          {/* Messages Tab */}
          <TabsContent value="messages" className="mt-6 space-y-6">
            <Card className="p-6">
              <h4 className="font-medium text-gray-900 mb-4">Recent Communications</h4>
              
              <div className="space-y-4">
                <div className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <MessageSquare className="w-5 h-5 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <p className="font-medium text-sm">Sarah Johnson (Tutor)</p>
                      <span className="text-xs text-gray-500">2 hours ago</span>
                    </div>
                    <p className="text-sm text-gray-600">
                      Emma did excellent work today! She mastered long division and is ready for the next topic.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                  <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                    <MessageSquare className="w-5 h-5 text-green-600" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <p className="font-medium text-sm">Platform Admin</p>
                      <span className="text-xs text-gray-500">Yesterday</span>
                    </div>
                    <p className="text-sm text-gray-600">
                      Your November invoice is now available in the billing section.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                  <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                    <MessageSquare className="w-5 h-5 text-purple-600" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <p className="font-medium text-sm">Michael Chen (Tutor)</p>
                      <span className="text-xs text-gray-500">3 days ago</span>
                    </div>
                    <p className="text-sm text-gray-600">
                      Lucas is showing great improvement in reading fluency. I recommend continuing with the current program.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3 p-3 bg-yellow-50 rounded-lg">
                  <div className="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                    <AlertCircle className="w-5 h-5 text-yellow-600" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <p className="font-medium text-sm">System Reminder</p>
                      <span className="text-xs text-gray-500">1 week ago</span>
                    </div>
                    <p className="text-sm text-gray-600">
                      Don't forget to book next month's sessions. Early booking ensures your preferred time slots.
                    </p>
                  </div>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <h4 className="font-medium text-gray-900 mb-4">Communication Preferences</h4>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-sm">Session Reminders</p>
                    <p className="text-sm text-gray-600">24 hours before each session</p>
                  </div>
                  <Badge className="bg-green-100 text-green-700">SMS & Email</Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-sm">Progress Reports</p>
                    <p className="text-sm text-gray-600">Monthly summary of learning progress</p>
                  </div>
                  <Badge className="bg-blue-100 text-blue-700">Email Only</Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-sm">Billing Notifications</p>
                    <p className="text-sm text-gray-600">Invoices and payment confirmations</p>
                  </div>
                  <Badge className="bg-blue-100 text-blue-700">Email Only</Badge>
                </div>
              </div>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Action Buttons */}
        <div className="flex justify-between items-center pt-4 border-t">
          <div className="flex gap-2">
            <Button variant="secondary" size="sm" onClick={() => toast.info('Edit profile coming soon')}>
              <Edit2 className="w-4 h-4 mr-2" />
              Edit Profile
            </Button>
            <Button variant="secondary" size="sm" onClick={() => toast.info('Send message coming soon')}>
              <MessageSquare className="w-4 h-4 mr-2" />
              Send Message
            </Button>
            <Button variant="secondary" size="sm" onClick={() => toast.info('Book session coming soon')}>
              <Calendar className="w-4 h-4 mr-2" />
              Book Session
            </Button>
          </div>
          <Button variant="ghost" onClick={onClose}>
            Close
          </Button>
        </div>
      </div>
    </Modal>
  );
};