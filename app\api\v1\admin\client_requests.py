"""
Admin API endpoints for managing client tutor requests.
Provides interfaces for managers to view, process, and convert requests.
"""

from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel, Field
from datetime import datetime
from app.core.dependencies import get_current_user
from app.core.auth_decorators import require_roles
from app.models.user_models import User
from app.services.client_request_processor import ClientRequestProcessor
from app.core.logging import logger

router = APIRouter()

# Initialize service
request_processor = ClientRequestProcessor()

class RequestAssignment(BaseModel):
    """Schema for assigning a request to a manager."""
    request_id: int = Field(..., description="ID of the request to assign")

class RequestConversion(BaseModel):
    """Schema for converting a request to a client account."""
    request_id: int = Field(..., description="ID of the request to convert")
    create_account: bool = Field(True, description="Whether to create a user account")
    send_welcome_email: bool = Field(True, description="Whether to send welcome email")
    notes: Optional[str] = Field(None, description="Additional notes about the conversion")

class RequestCancellation(BaseModel):
    """Schema for cancelling a request."""
    request_id: int = Field(..., description="ID of the request to cancel")
    reason: str = Field(..., min_length=10, description="Reason for cancellation")

@router.get("/client-requests", response_model=Dict[str, Any])
async def get_pending_requests(
    current_user: User = Depends(require_roles(['manager'])),
    region: Optional[str] = Query(None, description="Filter by region"),
    city: Optional[str] = Query(None, description="Filter by city"),
    limit: int = Query(50, ge=1, le=100, description="Maximum results"),
    offset: int = Query(0, ge=0, description="Pagination offset")
) -> Dict[str, Any]:
    """
    Get pending client requests with optional filtering.
    
    Managers can view all pending requests and filter by region or city.
    This supports the future feature of viewing clients by region awaiting matches.
    """
    try:
        requests = await request_processor.get_pending_requests(
            region=region,
            city=city,
            limit=limit,
            offset=offset
        )
        
        # Get regional statistics
        regional_stats = await request_processor.get_requests_by_region()
        
        return {
            "success": True,
            "data": {
                "requests": requests,
                "total": len(requests),
                "regional_distribution": regional_stats,
                "filters": {
                    "region": region,
                    "city": city
                }
            }
        }
        
    except Exception as e:
        logger.error(f"Error fetching pending requests: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to fetch pending requests"
        )

@router.get("/client-requests/{request_id}", response_model=Dict[str, Any])
async def get_request_details(
    request_id: int,
    current_user: User = Depends(require_roles(['manager']))
) -> Dict[str, Any]:
    """Get detailed information about a specific request."""
    try:
        request = await request_processor.get_request_by_id(request_id)
        
        if not request:
            raise HTTPException(
                status_code=404,
                detail=f"Request {request_id} not found"
            )
        
        return {
            "success": True,
            "data": request
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching request {request_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to fetch request details"
        )

@router.post("/client-requests/assign", response_model=Dict[str, Any])
async def assign_request(
    assignment: RequestAssignment,
    current_user: User = Depends(require_roles(['manager']))
) -> Dict[str, Any]:
    """
    Assign a request to the current manager for processing.
    
    This marks the request as 'processing' and assigns it to the manager.
    """
    try:
        success = await request_processor.assign_request(
            assignment.request_id,
            current_user.user_id
        )
        
        if not success:
            raise HTTPException(
                status_code=400,
                detail="Failed to assign request. It may already be assigned or not in pending status."
            )
        
        logger.info(
            f"Request {assignment.request_id} assigned to manager {current_user.user_id}",
            extra={
                "request_id": assignment.request_id,
                "manager_id": current_user.user_id,
                "manager_email": current_user.email
            }
        )
        
        return {
            "success": True,
            "message": f"Request {assignment.request_id} successfully assigned to you",
            "data": {
                "request_id": assignment.request_id,
                "assigned_to": current_user.user_id,
                "assigned_at": datetime.utcnow().isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error assigning request: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to assign request"
        )

@router.post("/client-requests/convert", response_model=Dict[str, Any])
async def convert_request(
    conversion: RequestConversion,
    current_user: User = Depends(require_roles(['manager']))
) -> Dict[str, Any]:
    """
    Convert a client request into actual client and dependant accounts.
    
    This creates:
    - User account (optional)
    - Client profile
    - Dependant profile
    - Learning needs
    """
    try:
        # Convert the request
        client_id, dependant_id, temp_password = await request_processor.convert_to_client(
            request_id=conversion.request_id,
            processed_by=current_user.user_id,
            create_account=conversion.create_account
        )
        
        if not client_id:
            raise HTTPException(
                status_code=400,
                detail="Failed to convert request"
            )
        
        logger.info(
            f"Request {conversion.request_id} converted to client {client_id}",
            extra={
                "request_id": conversion.request_id,
                "client_id": client_id,
                "dependant_id": dependant_id,
                "manager_id": current_user.user_id,
                "account_created": conversion.create_account
            }
        )
        
        response_data = {
            "success": True,
            "message": "Request successfully converted to client account",
            "data": {
                "request_id": conversion.request_id,
                "client_id": client_id,
                "dependant_id": dependant_id,
                "account_created": conversion.create_account
            }
        }
        
        # Include temporary password if account was created
        if temp_password and conversion.create_account:
            response_data["data"]["temporary_password"] = temp_password
            response_data["data"]["password_note"] = "Please share this temporary password securely with the client"
        
        return response_data
        
    except ValueError as e:
        raise HTTPException(
            status_code=400,
            detail=str(e)
        )
    except Exception as e:
        logger.error(
            f"Error converting request {conversion.request_id}: {e}",
            extra={"request_id": conversion.request_id, "error": str(e)}
        )
        raise HTTPException(
            status_code=500,
            detail="Failed to convert request"
        )

@router.post("/client-requests/cancel", response_model=Dict[str, Any])
async def cancel_request(
    cancellation: RequestCancellation,
    current_user: User = Depends(require_roles(['manager']))
) -> Dict[str, Any]:
    """Cancel a client request with a reason."""
    try:
        success = await request_processor.cancel_request(
            request_id=cancellation.request_id,
            cancelled_by=current_user.user_id,
            reason=cancellation.reason
        )
        
        if not success:
            raise HTTPException(
                status_code=400,
                detail="Failed to cancel request. It may already be processed."
            )
        
        logger.info(
            f"Request {cancellation.request_id} cancelled by manager {current_user.user_id}",
            extra={
                "request_id": cancellation.request_id,
                "manager_id": current_user.user_id,
                "reason": cancellation.reason
            }
        )
        
        return {
            "success": True,
            "message": f"Request {cancellation.request_id} has been cancelled",
            "data": {
                "request_id": cancellation.request_id,
                "cancelled_by": current_user.user_id,
                "cancelled_at": datetime.utcnow().isoformat(),
                "reason": cancellation.reason
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cancelling request: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to cancel request"
        )

@router.get("/client-requests/stats/regional", response_model=Dict[str, Any])
async def get_regional_statistics(
    current_user: User = Depends(require_roles(['manager']))
) -> Dict[str, Any]:
    """
    Get statistics on pending requests by region.
    
    This supports the manager view of clients awaiting matches by region.
    """
    try:
        regional_stats = await request_processor.get_requests_by_region()
        
        # Calculate total
        total_pending = sum(regional_stats.values())
        
        # Sort by count
        sorted_regions = sorted(
            regional_stats.items(),
            key=lambda x: x[1],
            reverse=True
        )
        
        return {
            "success": True,
            "data": {
                "total_pending": total_pending,
                "by_region": dict(sorted_regions),
                "regions": [
                    {
                        "region": region,
                        "count": count,
                        "percentage": round((count / total_pending * 100), 1) if total_pending > 0 else 0
                    }
                    for region, count in sorted_regions
                ],
                "generated_at": datetime.utcnow().isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"Error fetching regional statistics: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to fetch regional statistics"
        )