"""
Customer Service

Core service for managing customer service operations including conversations,
messages, agent assignments, and analytics. Integrates with notification system.
"""

from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, date, timedelta
from decimal import Decimal
import re
import asyncio
from app.database.repositories.customer_service_repository import CustomerServiceRepository
from app.services.notification_orchestrator import NotificationOrchestrator
from app.services.twilio_service import TwilioService
from app.services.email_service import EmailService
from app.locales.translation_service import TranslationService
from app.models.customer_service_models import *
from app.core.logging import logger
from app.core.exceptions import ValidationError, ResourceNotFoundError, BusinessLogicError


class CustomerServiceService:
    """Core customer service management service"""

    def __init__(self):
        self.repository = CustomerServiceRepository()
        self.notification_orchestrator = NotificationOrchestrator()
        self.twilio_service = TwilioService()
        self.email_service = EmailService()
        self.translation_service = TranslationService()

    # Agent Management

    async def get_agent_by_user_id(self, user_id: int) -> Optional[CustomerServiceAgent]:
        """Get customer service agent by user ID"""
        return await self.repository.get_agent_by_user_id(user_id)

    async def get_all_agents(self, active_only: bool = True) -> List[CustomerServiceAgent]:
        """Get all customer service agents"""
        return await self.repository.get_all_agents(active_only)

    async def update_agent_status(self, agent_id: int, status: AgentStatus) -> bool:
        """Update agent availability status"""
        success = await self.repository.update_agent_status(agent_id, status)
        
        if success:
            logger.info(f"Agent {agent_id} status updated to {status.value}")
            
            # Notify other agents about status change if going offline
            if status == AgentStatus.OFFLINE:
                await self._redistribute_conversations_if_needed(agent_id)
        
        return success

    async def get_available_agents(
        self,
        specialties: Optional[List[str]] = None,
        exclude_overloaded: bool = True
    ) -> List[CustomerServiceAgent]:
        """Get available agents, optionally filtered by specialties"""
        agents = await self.repository.get_available_agents(specialties)
        
        if exclude_overloaded:
            # Filter out agents at capacity
            filtered_agents = []
            for agent in agents:
                workload = await self.repository.get_agent_workload(agent.agent_id)
                if workload.get('active_conversations', 0) < agent.max_concurrent_conversations:
                    filtered_agents.append(agent)
            return filtered_agents
        
        return agents

    async def assign_conversation_automatically(
        self,
        conversation_id: int,
        required_specialties: Optional[List[str]] = None
    ) -> Optional[CustomerServiceAgent]:
        """Automatically assign conversation to best available agent"""
        available_agents = await self.get_available_agents(required_specialties, exclude_overloaded=True)
        
        if not available_agents:
            logger.warning(f"No available agents for conversation {conversation_id}")
            return None
        
        # Score agents based on workload and specialties
        agent_scores = []
        for agent in available_agents:
            workload = await self.repository.get_agent_workload(agent.agent_id)
            active_conversations = workload.get('active_conversations', 0)
            
            # Base score on available capacity
            capacity_score = (agent.max_concurrent_conversations - active_conversations) / agent.max_concurrent_conversations
            
            # Bonus for matching specialties
            specialty_score = 0
            if required_specialties:
                matching_specialties = set(agent.specialties) & set(required_specialties)
                specialty_score = len(matching_specialties) / len(required_specialties)
            
            total_score = capacity_score * 0.7 + specialty_score * 0.3
            agent_scores.append((agent, total_score))
        
        # Select agent with highest score
        best_agent = max(agent_scores, key=lambda x: x[1])[0]
        
        # Assign conversation
        success = await self.repository.assign_conversation(
            conversation_id, best_agent.agent_id, auto_assigned=True,
            assignment_reason="Automatic assignment based on availability and specialties"
        )
        
        if success:
            logger.info(f"Conversation {conversation_id} auto-assigned to agent {best_agent.agent_id}")
            
            # Notify agent of new assignment
            await self._notify_agent_assignment(conversation_id, best_agent.agent_id)
            
            return best_agent
        
        return None

    # Conversation Management

    async def create_conversation(
        self,
        participant_user_id: Optional[int] = None,
        participant_phone: Optional[str] = None,
        participant_email: Optional[str] = None,
        participant_name: Optional[str] = None,
        conversation_type: ConversationType = ConversationType.SMS,
        subject: Optional[str] = None,
        priority: Priority = Priority.NORMAL,
        category: Optional[str] = None,
        initial_message: Optional[str] = None,
        language: str = "en",
        auto_assign: bool = True
    ) -> CSConversation:
        """Create a new customer service conversation"""
        
        # Validate input
        if not any([participant_user_id, participant_phone, participant_email]):
            raise ValidationError("Must provide at least one participant identifier")
        
        # Determine source channel
        source_channel = self._determine_source_channel(conversation_type, participant_phone, participant_email)
        
        # Create conversation
        conversation = await self.repository.create_conversation(
            participant_user_id=participant_user_id,
            participant_phone=participant_phone,
            participant_email=participant_email,
            participant_name=participant_name,
            conversation_type=conversation_type,
            subject=subject,
            priority=priority,
            category=category,
            source_channel=source_channel,
            language=language
        )
        
        # Add initial message if provided
        if initial_message:
            await self.add_message(
                conversation_id=conversation.conversation_id,
                message_content=initial_message,
                message_direction=MessageDirection.INBOUND,
                channel=source_channel,
                sender_user_id=participant_user_id,
                sender_phone=participant_phone,
                sender_name=participant_name,
                language=language
            )
        
        # Auto-assign if requested and high priority
        if auto_assign and priority in [Priority.HIGH, Priority.URGENT]:
            specialties = self._determine_required_specialties(category, initial_message)
            await self.assign_conversation_automatically(conversation.conversation_id, specialties)
        
        logger.info(f"Created conversation {conversation.conversation_id} for {participant_name or participant_phone or participant_email}")
        
        return conversation

    async def get_conversation_details(self, conversation_id: int) -> Optional[ConversationResponse]:
        """Get comprehensive conversation details"""
        conversation = await self.repository.get_conversation_by_id(conversation_id)
        if not conversation:
            return None
        
        # Get related data
        messages = await self.repository.get_conversation_messages(conversation_id)
        
        agent = None
        if conversation.assigned_agent_id:
            agents = await self.repository.get_all_agents()
            agent = next((a for a in agents if a.agent_id == conversation.assigned_agent_id), None)
        
        # Get participant info
        participant_info = await self._get_participant_info(conversation)
        
        return ConversationResponse(
            conversation=conversation,
            messages=messages,
            agent=agent,
            participant_info=participant_info
        )

    async def add_message(
        self,
        conversation_id: int,
        message_content: str,
        message_direction: MessageDirection,
        channel: str,
        sender_user_id: Optional[int] = None,
        sender_agent_id: Optional[int] = None,
        sender_phone: Optional[str] = None,
        sender_name: Optional[str] = None,
        sender_role: Optional[str] = None,
        message_type: str = "text",
        template_id: Optional[int] = None,
        automated: bool = False,
        internal_note: bool = False,
        attachments: Optional[Dict[str, Any]] = None,
        language: str = "en",
        send_notification: bool = True
    ) -> CSMessage:
        """Add a message to a conversation"""
        
        # Get conversation
        conversation = await self.repository.get_conversation_by_id(conversation_id)
        if not conversation:
            raise ResourceNotFoundError(f"Conversation {conversation_id} not found")
        
        # Validate message content
        if not message_content.strip():
            raise ValidationError("Message content cannot be empty")
        
        # Create message
        message = await self.repository.create_message(
            conversation_id=conversation_id,
            thread_id=conversation.thread_id,
            message_content=message_content,
            message_direction=message_direction,
            channel=channel,
            sender_user_id=sender_user_id,
            sender_agent_id=sender_agent_id,
            sender_phone=sender_phone,
            sender_name=sender_name,
            sender_role=sender_role,
            message_type=message_type,
            template_id=template_id,
            automated=automated,
            internal_note=internal_note,
            attachments=attachments,
            language=language
        )
        
        # Update template usage if applicable
        if template_id:
            await self.repository.increment_template_usage(template_id)
        
        # Send notifications if not internal note
        if send_notification and not internal_note:
            await self._send_message_notifications(conversation, message)
        
        logger.info(f"Added message to conversation {conversation_id}: {message_direction.value}")
        
        return message

    async def send_template_message(
        self,
        conversation_id: int,
        template_key: str,
        variables: Optional[Dict[str, Any]] = None,
        sender_agent_id: Optional[int] = None,
        language: str = "en"
    ) -> CSMessage:
        """Send a message using a template"""
        
        # Get template
        template = await self.repository.get_template_by_key(template_key, language)
        if not template:
            raise ResourceNotFoundError(f"Template {template_key} not found for language {language}")
        
        # Render template content
        rendered_content = await self._render_template_content(template.message_content, variables or {})
        
        # Send message
        return await self.add_message(
            conversation_id=conversation_id,
            message_content=rendered_content,
            message_direction=MessageDirection.OUTBOUND,
            channel=template.channel or "in_app",
            sender_agent_id=sender_agent_id,
            template_id=template.template_id,
            language=language
        )

    async def assign_conversation_manually(
        self,
        conversation_id: int,
        agent_id: int,
        assigned_by: int,
        assignment_reason: Optional[str] = None
    ) -> bool:
        """Manually assign conversation to an agent"""
        
        # Verify agent availability
        agent = await self.repository.get_agent_by_user_id(agent_id)  # Note: this might need adjustment
        if not agent or agent.status == AgentStatus.OFFLINE:
            raise BusinessLogicError("Agent is not available for assignment")
        
        # Check agent capacity
        workload = await self.repository.get_agent_workload(agent_id)
        if workload.get('active_conversations', 0) >= agent.max_concurrent_conversations:
            raise BusinessLogicError("Agent has reached maximum conversation capacity")
        
        # Assign conversation
        success = await self.repository.assign_conversation(
            conversation_id, agent_id, assigned_by, assignment_reason, auto_assigned=False
        )
        
        if success:
            # Notify agent
            await self._notify_agent_assignment(conversation_id, agent_id)
            logger.info(f"Conversation {conversation_id} manually assigned to agent {agent_id} by {assigned_by}")
        
        return success

    async def update_conversation_status(
        self,
        conversation_id: int,
        status: ConversationStatus,
        agent_id: Optional[int] = None,
        satisfaction_rating: Optional[int] = None,
        customer_feedback: Optional[str] = None
    ) -> bool:
        """Update conversation status"""
        
        success = await self.repository.update_conversation(
            conversation_id=conversation_id,
            status=status,
            satisfaction_rating=satisfaction_rating,
            customer_feedback=customer_feedback
        )
        
        if success:
            # Send system message for status change
            status_message = await self._get_status_change_message(status)
            await self.add_message(
                conversation_id=conversation_id,
                message_content=status_message,
                message_direction=MessageDirection.OUTBOUND,
                channel="in_app",
                sender_agent_id=agent_id,
                system_generated=True,
                send_notification=False
            )
            
            logger.info(f"Conversation {conversation_id} status updated to {status.value}")
        
        return success

    # Template Management

    async def create_template(
        self,
        template_name: str,
        template_key: str,
        category: str,
        message_content: str,
        created_by: int,
        **kwargs
    ) -> CSMessageTemplate:
        """Create a new message template"""
        
        # Validate template key uniqueness
        existing = await self.repository.get_template_by_key(template_key, kwargs.get('language', 'en'))
        if existing:
            raise ValidationError(f"Template key {template_key} already exists")
        
        # Validate template content
        self._validate_template_content(message_content)
        
        return await self.repository.create_template(
            template_name=template_name,
            template_key=template_key,
            category=category,
            message_content=message_content,
            created_by=created_by,
            **kwargs
        )

    async def get_available_templates(
        self,
        category: Optional[str] = None,
        language: str = "en",
        department: Optional[str] = None,
        search: Optional[str] = None
    ) -> List[CSMessageTemplate]:
        """Get available templates for agents"""
        templates = await self.repository.get_templates(
            category=category,
            language=language,
            status=TemplateStatus.APPROVED,
            department=department
        )
        
        # Apply search filter if provided
        if search:
            search_lower = search.lower()
            templates = [
                template for template in templates
                if (search_lower in template.template_name.lower() or
                    search_lower in template.message_content.lower() or
                    search_lower in (template.subject or '').lower())
            ]
        
        return templates

    async def update_template(
        self,
        template_id: int,
        template_name: Optional[str] = None,
        template_key: Optional[str] = None,
        category: Optional[str] = None,
        subject: Optional[str] = None,
        message_content: Optional[str] = None,
        variables: Optional[Dict[str, Any]] = None,
        language: Optional[str] = None,
        channel: Optional[str] = None,
        department: Optional[str] = None,
        updated_by: int = None
    ) -> bool:
        """Update message template"""
        
        # Get existing template
        existing = await self.repository.get_template_by_id(template_id)
        if not existing:
            return False
        
        # Validate template key uniqueness if changed
        if template_key and template_key != existing.template_key:
            existing_with_key = await self.repository.get_template_by_key(
                template_key, language or existing.language
            )
            if existing_with_key and existing_with_key.template_id != template_id:
                raise ValidationError(f"Template key {template_key} already exists")
        
        # Validate template content if changed
        if message_content:
            self._validate_template_content(message_content)
        
        # Update template
        updates = {}
        if template_name is not None:
            updates['template_name'] = template_name
        if template_key is not None:
            updates['template_key'] = template_key
        if category is not None:
            updates['category'] = category
        if subject is not None:
            updates['subject'] = subject
        if message_content is not None:
            updates['message_content'] = message_content
        if variables is not None:
            updates['variables'] = variables
        if language is not None:
            updates['language'] = language
        if channel is not None:
            updates['channel'] = channel
        if department is not None:
            updates['department'] = department
        
        success = await self.repository.update_template(template_id, **updates)
        
        if success:
            logger.info(f"Template {template_id} updated by user {updated_by}")
        
        return success

    async def delete_template(self, template_id: int, deleted_by: int) -> bool:
        """Delete message template"""
        
        # Check if template exists
        template = await self.repository.get_template_by_id(template_id)
        if not template:
            return False
        
        # Check if template is in use (has recent usage)
        if template.usage_count > 0:
            # In production, might want to soft delete or require confirmation
            logger.warning(f"Deleting template {template_id} with {template.usage_count} usages")
        
        success = await self.repository.delete_template(template_id)
        
        if success:
            logger.info(f"Template {template_id} deleted by user {deleted_by}")
        
        return success

    async def approve_template(self, template_id: int, approved_by: int) -> bool:
        """Approve a template for use"""
        
        template = await self.repository.get_template_by_id(template_id)
        if not template:
            return False
        
        success = await self.repository.update_template_status(
            template_id, TemplateStatus.APPROVED, approved_by
        )
        
        if success:
            logger.info(f"Template {template_id} approved by user {approved_by}")
        
        return success

    async def get_quick_responses(
        self,
        category: Optional[str] = None,
        language: str = "en"
    ) -> List[CSQuickResponse]:
        """Get quick responses for agents"""
        return await self.repository.get_quick_responses(category, language)

    async def create_quick_response(
        self,
        response_text: str,
        trigger_keywords: List[str],
        category: str,
        language: str,
        created_by: int
    ) -> CSQuickResponse:
        """Create a new quick response"""
        
        # Validate inputs
        if not response_text.strip():
            raise ValidationError("Response text cannot be empty")
        
        if not trigger_keywords:
            raise ValidationError("At least one trigger keyword is required")
        
        # Clean and validate keywords
        cleaned_keywords = [kw.strip().lower() for kw in trigger_keywords if kw.strip()]
        if not cleaned_keywords:
            raise ValidationError("Valid trigger keywords are required")
        
        response = await self.repository.create_quick_response(
            response_text=response_text.strip(),
            trigger_keywords=cleaned_keywords,
            category=category,
            language=language,
            created_by=created_by
        )
        
        logger.info(f"Created quick response for category {category} by user {created_by}")
        
        return response

    async def update_quick_response(
        self,
        response_id: int,
        response_text: Optional[str] = None,
        trigger_keywords: Optional[List[str]] = None,
        category: Optional[str] = None,
        language: Optional[str] = None,
        is_active: Optional[bool] = None,
        updated_by: int = None
    ) -> bool:
        """Update quick response"""
        
        # Get existing response
        existing = await self.repository.get_quick_response_by_id(response_id)
        if not existing:
            return False
        
        # Validate inputs
        if response_text is not None and not response_text.strip():
            raise ValidationError("Response text cannot be empty")
        
        if trigger_keywords is not None:
            cleaned_keywords = [kw.strip().lower() for kw in trigger_keywords if kw.strip()]
            if not cleaned_keywords:
                raise ValidationError("Valid trigger keywords are required")
        else:
            cleaned_keywords = None
        
        # Update response
        updates = {}
        if response_text is not None:
            updates['response_text'] = response_text.strip()
        if cleaned_keywords is not None:
            updates['trigger_keywords'] = cleaned_keywords
        if category is not None:
            updates['category'] = category
        if language is not None:
            updates['language'] = language
        if is_active is not None:
            updates['is_active'] = is_active
        
        success = await self.repository.update_quick_response(response_id, **updates)
        
        if success:
            logger.info(f"Quick response {response_id} updated by user {updated_by}")
        
        return success

    async def delete_quick_response(self, response_id: int, deleted_by: int) -> bool:
        """Delete quick response"""
        
        # Check if response exists
        response = await self.repository.get_quick_response_by_id(response_id)
        if not response:
            return False
        
        success = await self.repository.delete_quick_response(response_id)
        
        if success:
            logger.info(f"Quick response {response_id} deleted by user {deleted_by}")
        
        return success

    async def suggest_quick_responses(
        self,
        message_content: str,
        category: Optional[str] = None,
        language: str = "en",
        max_suggestions: int = 5
    ) -> List[CSQuickResponse]:
        """Get suggested quick responses based on message content"""
        
        # Get all quick responses for the language/category
        all_responses = await self.repository.get_quick_responses(category, language)
        
        if not all_responses:
            return []
        
        # Score responses based on keyword matches
        message_words = set(message_content.lower().split())
        scored_responses = []
        
        for response in all_responses:
            score = 0
            matched_keywords = []
            
            for keyword in response.trigger_keywords:
                if keyword in message_content.lower():
                    score += 2  # Exact match
                    matched_keywords.append(keyword)
                elif any(keyword in word for word in message_words):
                    score += 1  # Partial match
                    matched_keywords.append(keyword)
            
            if score > 0:
                # Boost score based on usage count (popular responses)
                popularity_boost = min(response.usage_count / 10, 2)
                final_score = score + popularity_boost
                
                scored_responses.append({
                    'response': response,
                    'score': final_score,
                    'matched_keywords': matched_keywords
                })
        
        # Sort by score and return top suggestions
        scored_responses.sort(key=lambda x: x['score'], reverse=True)
        
        return [item['response'] for item in scored_responses[:max_suggestions]]

    async def get_template_categories(self) -> List[Dict[str, Any]]:
        """Get template categories with counts"""
        return await self.repository.get_template_categories()

    async def get_template_variables(self) -> List[Dict[str, Any]]:
        """Get available template variables"""
        
        # Standard variables available in all templates
        variables = [
            {
                "name": "user_name",
                "description": "Customer's full name",
                "example": "John Smith",
                "required": False
            },
            {
                "name": "user_first_name",
                "description": "Customer's first name",
                "example": "John",
                "required": False
            },
            {
                "name": "agent_name",
                "description": "Agent's full name",
                "example": "Sarah Johnson",
                "required": False
            },
            {
                "name": "company_name",
                "description": "Company name",
                "example": "TutorAide",
                "required": False
            },
            {
                "name": "current_date",
                "description": "Current date",
                "example": "December 6, 2024",
                "required": False
            },
            {
                "name": "conversation_id",
                "description": "Conversation reference number",
                "example": "CS-12345",
                "required": False
            },
            {
                "name": "appointment_date",
                "description": "Appointment date and time",
                "example": "December 8, 2024 at 2:00 PM",
                "required": False
            },
            {
                "name": "tutor_name",
                "description": "Assigned tutor's name",
                "example": "Dr. Emily Chen",
                "required": False
            },
            {
                "name": "subject",
                "description": "Subject or service",
                "example": "Mathematics Tutoring",
                "required": False
            },
            {
                "name": "support_email",
                "description": "Support email address",
                "example": "<EMAIL>",
                "required": False
            },
            {
                "name": "support_phone",
                "description": "Support phone number",
                "example": "(*************",
                "required": False
            }
        ]
        
        return variables

    # Analytics and Dashboard

    async def get_dashboard_data(
        self,
        agent_id: Optional[int] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> DashboardResponse:
        """Get comprehensive dashboard data"""
        
        # Get statistics
        conversation_stats_data = await self.repository.get_conversation_stats(start_date, end_date, agent_id)
        message_stats_data = await self.repository.get_message_stats(start_date, end_date, agent_id)
        
        # Convert to models
        conversation_stats = ConversationStats(**conversation_stats_data)
        message_stats = MessageStats(**message_stats_data)
        
        # Get agent summaries
        agents = await self.repository.get_all_agents()
        agent_summaries = []
        for agent in agents:
            workload = await self.repository.get_agent_workload(agent.agent_id)
            summary = AgentSummary(
                agent_id=agent.agent_id,
                agent_name=agent.agent_name,
                status=agent.status,
                current_conversations=workload.get('active_conversations', 0),
                max_conversations=agent.max_concurrent_conversations,
                specialties=agent.specialties,
                languages=agent.languages
            )
            agent_summaries.append(summary)
        
        # Get recent and overdue conversations
        recent_conversations = await self._get_conversation_summaries(limit=10, agent_id=agent_id)
        overdue_conversations = await self._get_overdue_conversations(agent_id=agent_id)
        
        # Get templates and quick responses
        templates = await self.get_available_templates(language="en")
        quick_responses = await self.get_quick_responses(language="en")
        
        return DashboardResponse(
            conversation_stats=conversation_stats,
            message_stats=message_stats,
            agent_summaries=agent_summaries,
            recent_conversations=recent_conversations,
            overdue_conversations=overdue_conversations,
            templates=templates,
            quick_responses=quick_responses
        )

    async def search_conversations(
        self,
        search_term: str,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[CSConversation]:
        """Search conversations with filters"""
        
        return await self.repository.search_conversations(
            search_term=search_term,
            agent_id=filters.get('agent_id') if filters else None,
            status=ConversationStatus(filters.get('status')) if filters and filters.get('status') else None,
            start_date=filters.get('start_date') if filters else None,
            end_date=filters.get('end_date') if filters else None,
            limit=filters.get('limit', 50) if filters else 50
        )

    # SMS Integration

    async def handle_incoming_sms(
        self,
        from_phone: str,
        message_content: str,
        external_message_id: str
    ) -> CSConversation:
        """Handle incoming SMS message"""
        
        # Find or create conversation
        conversation = await self._find_or_create_sms_conversation(from_phone)
        
        # Add message
        await self.add_message(
            conversation_id=conversation.conversation_id,
            message_content=message_content,
            message_direction=MessageDirection.INBOUND,
            channel="sms",
            sender_phone=from_phone,
            sender_name=conversation.participant_name
        )
        
        # Auto-assign if conversation is unassigned and high priority
        if not conversation.assigned_agent_id:
            # Determine if urgent based on keywords
            is_urgent = self._detect_urgent_keywords(message_content)
            if is_urgent:
                await self.repository.update_conversation(
                    conversation.conversation_id, priority=Priority.URGENT
                )
                await self.assign_conversation_automatically(conversation.conversation_id)
        
        return conversation

    async def send_sms_response(
        self,
        conversation_id: int,
        message_content: str,
        agent_id: int
    ) -> bool:
        """Send SMS response to customer"""
        
        conversation = await self.repository.get_conversation_by_id(conversation_id)
        if not conversation or not conversation.participant_phone:
            raise ValidationError("Invalid conversation or missing phone number")
        
        # Send via Twilio
        try:
            external_message_id = await self.twilio_service.send_sms(
                to_phone=conversation.participant_phone,
                message=message_content
            )
            
            # Record message
            await self.add_message(
                conversation_id=conversation_id,
                message_content=message_content,
                message_direction=MessageDirection.OUTBOUND,
                channel="sms",
                sender_agent_id=agent_id,
                send_notification=False  # Already sent via SMS
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to send SMS for conversation {conversation_id}: {e}")
            return False

    # Helper Methods

    def _determine_source_channel(
        self,
        conversation_type: ConversationType,
        participant_phone: Optional[str],
        participant_email: Optional[str]
    ) -> str:
        """Determine the source channel based on conversation type and participant info"""
        if conversation_type == ConversationType.SMS and participant_phone:
            return "sms"
        elif conversation_type == ConversationType.EMAIL and participant_email:
            return "email"
        elif conversation_type == ConversationType.IN_APP:
            return "in_app"
        else:
            return "unknown"

    def _determine_required_specialties(
        self,
        category: Optional[str],
        initial_message: Optional[str]
    ) -> List[str]:
        """Determine required agent specialties based on category and message content"""
        specialties = []
        
        if category:
            specialties.append(category)
        
        if initial_message:
            content_lower = initial_message.lower()
            if any(word in content_lower for word in ['bill', 'payment', 'invoice', 'charge']):
                specialties.append('billing')
            elif any(word in content_lower for word in ['login', 'password', 'technical', 'bug']):
                specialties.append('technical')
            elif any(word in content_lower for word in ['appointment', 'schedule', 'tutor']):
                specialties.append('scheduling')
        
        return specialties or ['general']

    def _detect_urgent_keywords(self, message_content: str) -> bool:
        """Detect if message contains urgent keywords"""
        urgent_keywords = [
            'urgent', 'emergency', 'asap', 'immediately', 'help',
            'problem', 'issue', 'broken', 'not working', 'error'
        ]
        content_lower = message_content.lower()
        return any(keyword in content_lower for keyword in urgent_keywords)

    def _validate_template_content(self, content: str) -> None:
        """Validate template content for proper variable syntax"""
        # Check for balanced braces
        if content.count('{') != content.count('}'):
            raise ValidationError("Template contains unbalanced variable braces")
        
        # Check for valid variable names
        import re
        variables = re.findall(r'\{([^}]+)\}', content)
        for var in variables:
            if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', var):
                raise ValidationError(f"Invalid variable name: {var}")

    async def _render_template_content(self, content: str, variables: Dict[str, Any]) -> str:
        """Render template content with variables"""
        try:
            return content.format(**variables)
        except KeyError as e:
            raise ValidationError(f"Missing template variable: {e}")
        except Exception as e:
            raise ValidationError(f"Template rendering error: {e}")

    async def _get_status_change_message(self, status: ConversationStatus) -> str:
        """Get system message for status change"""
        messages = {
            ConversationStatus.ASSIGNED: "Conversation has been assigned to an agent.",
            ConversationStatus.RESOLVED: "Conversation has been resolved.",
            ConversationStatus.CLOSED: "Conversation has been closed.",
            ConversationStatus.ESCALATED: "Conversation has been escalated."
        }
        return messages.get(status, f"Conversation status changed to {status.value}.")

    async def _get_participant_info(self, conversation: CSConversation) -> Optional[Dict[str, Any]]:
        """Get additional participant information"""
        info = {}
        
        if conversation.participant_user_id:
            # Get user details from user repository
            # This would integrate with your existing user system
            pass
        
        # Add phone/email info
        if conversation.participant_phone:
            info['phone'] = conversation.participant_phone
        if conversation.participant_email:
            info['email'] = conversation.participant_email
        if conversation.participant_name:
            info['name'] = conversation.participant_name
        
        return info if info else None

    async def _get_conversation_summaries(
        self,
        limit: int = 10,
        agent_id: Optional[int] = None
    ) -> List[ConversationSummary]:
        """Get conversation summaries for dashboard"""
        # This would be implemented based on your specific needs
        # For now, returning empty list
        return []

    async def _get_overdue_conversations(
        self,
        agent_id: Optional[int] = None
    ) -> List[ConversationSummary]:
        """Get overdue conversations"""
        # This would identify conversations past SLA
        return []

    async def _find_or_create_sms_conversation(self, from_phone: str) -> CSConversation:
        """Find existing SMS conversation or create new one"""
        # Look for existing open conversation
        conversations = await self.repository.search_conversations(
            search_term=from_phone,
            status=ConversationStatus.OPEN,
            limit=1
        )
        
        if conversations:
            return conversations[0]
        
        # Create new conversation
        return await self.create_conversation(
            participant_phone=from_phone,
            conversation_type=ConversationType.SMS,
            priority=Priority.NORMAL,
            category="general",
            auto_assign=False
        )

    async def _send_message_notifications(
        self,
        conversation: CSConversation,
        message: CSMessage
    ) -> None:
        """Send notifications for new messages"""
        
        if message.message_direction == MessageDirection.INBOUND and conversation.assigned_agent_id:
            # Notify assigned agent of new customer message
            await self.notification_orchestrator.send_notification(
                user_id=conversation.assigned_agent_id,
                title="New Customer Message",
                message=f"New message in conversation {conversation.thread_id}",
                channels=["push", "in_app"],
                data={"conversation_id": conversation.conversation_id}
            )
        elif message.message_direction == MessageDirection.OUTBOUND and conversation.participant_user_id:
            # Notify customer of agent response
            await self.notification_orchestrator.send_notification(
                user_id=conversation.participant_user_id,
                title="Support Response",
                message="You have a new response from support",
                channels=["push", "email"],
                data={"conversation_id": conversation.conversation_id}
            )

    async def _notify_agent_assignment(self, conversation_id: int, agent_id: int) -> None:
        """Notify agent of new conversation assignment"""
        await self.notification_orchestrator.send_notification(
            user_id=agent_id,
            title="New Conversation Assigned",
            message=f"You have been assigned conversation {conversation_id}",
            channels=["push", "in_app"],
            data={"conversation_id": conversation_id}
        )

    async def _redistribute_conversations_if_needed(self, offline_agent_id: int) -> None:
        """Redistribute conversations when agent goes offline"""
        # Get agent's active conversations
        conversations = await self.repository.get_conversations_by_agent(
            offline_agent_id, ConversationStatus.ASSIGNED
        )
        
        if not conversations:
            return
        
        logger.info(f"Redistributing {len(conversations)} conversations from offline agent {offline_agent_id}")
        
        # Try to reassign each conversation
        for conversation in conversations:
            await self.assign_conversation_automatically(conversation.conversation_id)

    # Threading and History Management

    async def merge_conversations(
        self,
        primary_conversation_id: int,
        conversation_ids: List[int],
        merged_by: int
    ) -> Dict[str, Any]:
        """Merge multiple conversations into a single thread"""
        
        # Validate conversations exist and can be merged
        primary_conversation = await self.repository.get_conversation_by_id(primary_conversation_id)
        if not primary_conversation:
            raise ValidationError("Primary conversation not found")
        
        conversations_to_merge = []
        for conv_id in conversation_ids:
            conv = await self.repository.get_conversation_by_id(conv_id)
            if not conv:
                raise ValidationError(f"Conversation {conv_id} not found")
            if conv.status in ['archived', 'closed']:
                raise ValidationError(f"Cannot merge {conv.status} conversation {conv_id}")
            conversations_to_merge.append(conv)
        
        # Use primary conversation's thread_id as the target
        target_thread_id = primary_conversation.thread_id
        
        # Update all conversations to use the same thread_id
        success = await self.repository.merge_conversations_into_thread(
            target_thread_id, conversation_ids, merged_by
        )
        
        if success:
            logger.info(f"Merged {len(conversation_ids)} conversations into thread {target_thread_id}")
            
            # Create system messages in each merged conversation
            for conv in conversations_to_merge:
                await self.add_message(
                    conversation_id=conv.conversation_id,
                    message_content=f"This conversation has been merged into thread {target_thread_id}",
                    message_direction=MessageDirection.OUTBOUND,
                    channel="in_app",
                    system_generated=True,
                    send_notification=False
                )
            
            return {"thread_id": target_thread_id, "merged_count": len(conversation_ids)}
        
        raise BusinessLogicError("Failed to merge conversations")

    async def export_thread(
        self,
        thread_id: str,
        format: str,
        include_messages: bool = True,
        exported_by: int = None
    ) -> bytes:
        """Export thread data in specified format"""
        
        # Get thread details
        thread_details = await self.repository.get_thread_details(thread_id)
        if not thread_details:
            raise ResourceNotFoundError(f"Thread {thread_id} not found")
        
        # Get thread history
        thread_history = await self.repository.get_thread_history(thread_id)
        
        export_data = {
            "thread_details": thread_details,
            "history": thread_history,
            "exported_at": datetime.utcnow().isoformat(),
            "exported_by": exported_by
        }
        
        if include_messages:
            # Get all messages for conversations in thread
            conversations = await self.repository.get_conversation_threads(
                search_term=thread_id, limit=1
            )
            if conversations:
                all_messages = []
                for conv_data in conversations[0].get('conversations', []):
                    messages = await self.repository.get_conversation_messages(
                        conv_data['conversation_id'], include_internal=True
                    )
                    all_messages.extend(messages)
                export_data["messages"] = [
                    {
                        "message_id": msg.message_id,
                        "conversation_id": msg.conversation_id,
                        "content": msg.message_content,
                        "direction": msg.message_direction.value,
                        "sender": msg.sender_name,
                        "sent_at": msg.sent_at.isoformat() if msg.sent_at else None,
                        "internal_note": msg.internal_note
                    }
                    for msg in all_messages
                ]
        
        # Generate export based on format
        if format == 'json':
            import json
            return json.dumps(export_data, indent=2, default=str).encode('utf-8')
        
        elif format == 'csv':
            import csv
            import io
            output = io.StringIO()
            
            # Export conversations summary
            writer = csv.writer(output)
            writer.writerow(['Conversation ID', 'Participant', 'Status', 'Created', 'Messages', 'Agent'])
            
            for conv in thread_details.get('conversations', []):
                writer.writerow([
                    conv.get('conversation_id'),
                    conv.get('participant_name', ''),
                    conv.get('status'),
                    conv.get('created_at'),
                    conv.get('message_count', 0),
                    conv.get('agent_name', '')
                ])
            
            if include_messages and 'messages' in export_data:
                writer.writerow([])  # Empty row
                writer.writerow(['Message ID', 'Conversation', 'Direction', 'Sender', 'Content', 'Sent At'])
                for msg in export_data['messages']:
                    writer.writerow([
                        msg['message_id'],
                        msg['conversation_id'],
                        msg['direction'],
                        msg['sender'],
                        msg['content'][:100] + '...' if len(msg['content']) > 100 else msg['content'],
                        msg['sent_at']
                    ])
            
            return output.getvalue().encode('utf-8')
        
        elif format == 'pdf':
            # For PDF generation, you'd typically use a library like reportlab
            # This is a simplified implementation
            content = f"""
Thread Export Report
===================

Thread ID: {thread_id}
Export Date: {export_data['exported_at']}

Thread Summary:
- Conversations: {thread_details.get('conversation_count', 0)}
- Total Messages: {thread_details.get('message_count', 0)}
- Participants: {', '.join(thread_details.get('participants', []))}
- Status: {thread_details.get('status', 'Unknown')}

Conversations:
"""
            for conv in thread_details.get('conversations', []):
                content += f"""
- Conversation {conv.get('conversation_id')}
  Participant: {conv.get('participant_name', 'Unknown')}
  Status: {conv.get('status')}
  Messages: {conv.get('message_count', 0)}
  Created: {conv.get('created_at')}
"""
            
            if include_messages and 'messages' in export_data:
                content += "\n\nMessages:\n"
                for msg in export_data['messages'][:50]:  # Limit for PDF
                    content += f"""
{msg['sent_at']} - {msg['sender']} ({msg['direction']}):
{msg['content'][:200]}{'...' if len(msg['content']) > 200 else ''}
---
"""
            
            return content.encode('utf-8')
        
        else:
            raise ValidationError(f"Unsupported export format: {format}")

    # Broadcast Messaging Methods

    async def create_broadcast_message(
        self,
        title: str,
        content: str,
        message_type: str,
        priority: str,
        channels: List[str],
        target_audience: Dict[str, Any],
        created_by: int,
        scheduled_at: Optional[datetime] = None,
        template_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """Create a new broadcast message"""
        
        # Validate inputs
        if not title.strip():
            raise ValidationError("Broadcast title cannot be empty")
        
        if not content.strip():
            raise ValidationError("Broadcast content cannot be empty")
        
        if not channels:
            raise ValidationError("At least one delivery channel must be selected")
        
        valid_types = ['announcement', 'alert', 'promotion', 'update']
        if message_type not in valid_types:
            raise ValidationError(f"Invalid message type. Must be one of: {valid_types}")
        
        valid_priorities = ['low', 'normal', 'high', 'urgent']
        if priority not in valid_priorities:
            raise ValidationError(f"Invalid priority. Must be one of: {valid_priorities}")
        
        valid_channels = ['in_app', 'push', 'email', 'sms']
        invalid_channels = [ch for ch in channels if ch not in valid_channels]
        if invalid_channels:
            raise ValidationError(f"Invalid channels: {invalid_channels}")
        
        # Validate target audience
        self._validate_target_audience(target_audience)
        
        # Create broadcast
        broadcast = await self.repository.create_broadcast_message(
            title=title,
            content=content,
            message_type=message_type,
            priority=priority,
            channels=channels,
            target_audience=target_audience,
            created_by=created_by,
            scheduled_at=scheduled_at,
            template_id=template_id
        )
        
        logger.info(f"Created broadcast message {broadcast.get('broadcast_id')} by user {created_by}")
        
        return broadcast

    async def send_broadcast_message(
        self,
        broadcast_id: int,
        sent_by: int
    ) -> Dict[str, Any]:
        """Send a broadcast message to all recipients"""
        
        # Get broadcast details
        broadcast = await self.repository.get_broadcast_message_by_id(broadcast_id)
        if not broadcast:
            raise ResourceNotFoundError(f"Broadcast {broadcast_id} not found")
        
        if broadcast['status'] not in ['draft', 'scheduled']:
            raise BusinessLogicError("Broadcast cannot be sent in current status")
        
        # Get target audience
        audience = await self.repository.get_audience_preview(broadcast['target_audience'])
        if not audience:
            raise BusinessLogicError("No recipients found for broadcast")
        
        # Update status to sending
        await self.repository.update_broadcast_status(broadcast_id, 'sending')
        
        # Send to each recipient
        delivery_results = await self._send_to_recipients(
            broadcast_id, broadcast, audience
        )
        
        # Update final status
        final_status = 'sent' if delivery_results['sent_count'] > 0 else 'failed'
        await self.repository.update_broadcast_status(
            broadcast_id, final_status, delivery_results
        )
        
        logger.info(f"Broadcast {broadcast_id} sent to {delivery_results['sent_count']} recipients")
        
        return {
            "broadcast_id": broadcast_id,
            "status": final_status,
            "delivery_stats": delivery_results
        }

    async def schedule_broadcast_processing(self) -> int:
        """Process scheduled broadcasts (would be called by background task)"""
        
        scheduled_broadcasts = await self.repository.get_scheduled_broadcasts()
        processed_count = 0
        
        for broadcast in scheduled_broadcasts:
            try:
                await self.send_broadcast_message(
                    broadcast['broadcast_id'],
                    broadcast['created_by']
                )
                processed_count += 1
            except Exception as e:
                logger.error(f"Failed to send scheduled broadcast {broadcast['broadcast_id']}: {e}")
                await self.repository.update_broadcast_status(
                    broadcast['broadcast_id'], 'failed'
                )
        
        return processed_count

    def _validate_target_audience(self, target_audience: Dict[str, Any]) -> None:
        """Validate target audience configuration"""
        
        audience_type = target_audience.get('audience_type')
        valid_types = ['all', 'role_based', 'custom', 'conversation_participants']
        
        if audience_type not in valid_types:
            raise ValidationError(f"Invalid audience type. Must be one of: {valid_types}")
        
        if audience_type == 'role_based':
            roles = target_audience.get('roles', [])
            if not roles:
                raise ValidationError("Roles must be specified for role-based targeting")
        
        elif audience_type == 'custom':
            user_ids = target_audience.get('user_ids', [])
            if not user_ids:
                raise ValidationError("User IDs must be specified for custom targeting")
        
        elif audience_type == 'conversation_participants':
            conversation_ids = target_audience.get('conversation_ids', [])
            if not conversation_ids:
                raise ValidationError("Conversation IDs must be specified for participant targeting")

    async def _send_to_recipients(
        self,
        broadcast_id: int,
        broadcast: Dict[str, Any],
        audience: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Send broadcast message to all recipients"""
        
        total_recipients = len(audience)
        sent_count = 0
        failed_count = 0
        delivered_count = 0
        
        channels = broadcast['channels']
        title = broadcast['title']
        content = broadcast['content']
        priority = broadcast['priority']
        
        for recipient in audience:
            user_id = recipient['user_id']
            
            # Send via each requested channel
            for channel in channels:
                try:
                    if channel == 'in_app':
                        # Send in-app notification
                        await self.notification_orchestrator.send_notification(
                            user_id=user_id,
                            title=title,
                            message=content,
                            channels=['in_app'],
                            priority=priority,
                            data={
                                'broadcast_id': broadcast_id,
                                'type': 'broadcast',
                                'message_type': broadcast['message_type']
                            }
                        )
                        
                    elif channel == 'push':
                        # Send push notification
                        await self.notification_orchestrator.send_notification(
                            user_id=user_id,
                            title=title,
                            message=content,
                            channels=['push'],
                            priority=priority,
                            data={
                                'broadcast_id': broadcast_id,
                                'type': 'broadcast'
                            }
                        )
                        
                    elif channel == 'email':
                        # Send email (simplified)
                        try:
                            recipient_email = recipient.get('email')
                            if recipient_email:
                                await self.email_service.send_email(
                                    to_email=recipient_email,
                                    subject=title,
                                    html_content=content,
                                    text_content=content
                                )
                        except Exception as e:
                            logger.warning(f"Failed to send email to {user_id}: {e}")
                            continue
                    
                    elif channel == 'sms':
                        # Send SMS (simplified)
                        # Would need phone number from user profile
                        pass
                    
                    # Log successful delivery
                    await self.repository.create_broadcast_delivery_log(
                        broadcast_id=broadcast_id,
                        user_id=user_id,
                        channel=channel,
                        status='sent'
                    )
                    
                except Exception as e:
                    logger.error(f"Failed to send {channel} to user {user_id}: {e}")
                    
                    # Log failed delivery
                    await self.repository.create_broadcast_delivery_log(
                        broadcast_id=broadcast_id,
                        user_id=user_id,
                        channel=channel,
                        status='failed',
                        error_message=str(e)
                    )
                    failed_count += 1
                    continue
            
            sent_count += 1
            delivered_count += 1  # Simplified - would track actual delivery confirmations
        
        return {
            "total_recipients": total_recipients,
            "sent_count": sent_count,
            "delivered_count": delivered_count,
            "failed_count": failed_count,
            "opened_count": 0,  # Would be tracked via callbacks
            "clicked_count": 0   # Would be tracked via link analytics
        }