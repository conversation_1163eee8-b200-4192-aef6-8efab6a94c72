import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Calendar, FileText, Users, AlertCircle } from 'lucide-react';
import { billingService, GenerateInvoicesRequest } from '../../services/billingService';
import { Modal } from '../common/Modal';
import Button from '../common/Button';
import { Input } from '../common/Input';
import { Card } from '../common/Card';
import { Checkbox } from '../common/Checkbox';
import toast from 'react-hot-toast';
import api from '../../services/api';

interface GenerateInvoicesModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

interface Client {
  client_id: number;
  first_name: string;
  last_name: string;
  email: string;
}

export const GenerateInvoicesModal: React.FC<GenerateInvoicesModalProps> = ({
  isOpen,
  onClose,
  onSuccess
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [autoSend, setAutoSend] = useState(false);
  const [selectedClients, setSelectedClients] = useState<number[]>([]);
  const [clients, setClients] = useState<Client[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  useEffect(() => {
    if (isOpen) {
      loadClients();
      // Set default dates (last month)
      const now = new Date();
      const firstDay = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      const lastDay = new Date(now.getFullYear(), now.getMonth(), 0);
      
      setStartDate(firstDay.toISOString().split('T')[0]);
      setEndDate(lastDay.toISOString().split('T')[0]);
    }
  }, [isOpen]);

  const loadClients = async () => {
    try {
      const response = await api.get<Client[]>('/clients?has_sessions=true');
      setClients(response.data);
    } catch (error) {
      console.error('Error loading clients:', error);
      toast.error('Failed to load clients');
    }
  };

  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedClients([]);
    } else {
      setSelectedClients(clients.map(c => c.client_id));
    }
    setSelectAll(!selectAll);
  };

  const toggleClient = (clientId: number) => {
    setSelectedClients(prev => {
      if (prev.includes(clientId)) {
        return prev.filter(id => id !== clientId);
      } else {
        return [...prev, clientId];
      }
    });
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);

      const request: GenerateInvoicesRequest = {
        start_date: startDate,
        end_date: endDate,
        client_ids: selectedClients.length > 0 ? selectedClients : undefined,
        auto_send: autoSend
      };

      const result = await billingService.generateInvoices(request);
      
      toast.success(`Generated ${result.generated_count} invoices successfully`);
      onSuccess();
    } catch (error) {
      console.error('Error generating invoices:', error);
      toast.error('Failed to generate invoices');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={t('billing.generateInvoices')}
      size="lg"
    >
      <div className="space-y-6">
        {/* Date Range */}
        <div>
          <h3 className="text-sm font-medium text-gray-700 mb-3">
            {t('billing.billingPeriod')}
          </h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm text-gray-600 mb-1">
                {t('billing.startDate')}
              </label>
              <Input
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                required
              />
            </div>
            <div>
              <label className="block text-sm text-gray-600 mb-1">
                {t('billing.endDate')}
              </label>
              <Input
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                required
              />
            </div>
          </div>
        </div>

        {/* Client Selection */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-gray-700">
              {t('billing.selectClients')}
            </h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleSelectAll}
            >
              {selectAll ? t('common.deselectAll') : t('common.selectAll')}
            </Button>
          </div>
          
          <Card className="max-h-64 overflow-y-auto p-4">
            {clients.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Users className="w-12 h-12 mx-auto mb-2 text-gray-400" />
                <p>{t('billing.noClientsWithSessions')}</p>
              </div>
            ) : (
              <div className="space-y-2">
                {clients.map((client) => (
                  <label
                    key={client.client_id}
                    className="flex items-center p-3 rounded-lg hover:bg-gray-50 cursor-pointer"
                  >
                    <input
                      type="checkbox"
                      checked={selectedClients.includes(client.client_id)}
                      onChange={() => toggleClient(client.client_id)}
                      className="rounded border-gray-300 text-accent-red focus:ring-accent-red mr-3"
                    />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">
                        {client.first_name} {client.last_name}
                      </div>
                      <div className="text-sm text-gray-500">
                        {client.email}
                      </div>
                    </div>
                  </label>
                ))}
              </div>
            )}
          </Card>
          
          {selectedClients.length > 0 && (
            <p className="mt-2 text-sm text-gray-600">
              {t('billing.selectedClients', { count: selectedClients.length })}
            </p>
          )}
        </div>

        {/* Options */}
        <div>
          <h3 className="text-sm font-medium text-gray-700 mb-3">
            {t('billing.options')}
          </h3>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={autoSend}
              onChange={(e) => setAutoSend(e.target.checked)}
              className="rounded border-gray-300 text-accent-red focus:ring-accent-red mr-3"
            />
            <div>
              <div className="font-medium text-gray-900">
                {t('billing.autoSendInvoices')}
              </div>
              <div className="text-sm text-gray-500">
                {t('billing.autoSendDescription')}
              </div>
            </div>
          </label>
        </div>

        {/* Info */}
        <Card className="p-4 bg-blue-50 border-blue-200">
          <div className="flex items-start gap-3">
            <AlertCircle className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
            <div className="text-sm text-blue-900">
              <p className="font-medium mb-1">{t('billing.invoiceGenerationInfo')}</p>
              <p>{t('billing.invoiceGenerationDescription')}</p>
            </div>
          </div>
        </Card>

        {/* Actions */}
        <div className="flex justify-end gap-3">
          <Button
            variant="secondary"
            onClick={onClose}
            disabled={loading}
          >
            {t('common.cancel')}
          </Button>
          <Button
            variant="primary"
            onClick={handleSubmit}
            loading={loading}
            leftIcon={<FileText className="w-4 h-4" />}
          >
            {t('billing.generateInvoices')}
          </Button>
        </div>
      </div>
    </Modal>
  );
};