import React from 'react';
import { clsx } from 'clsx';

interface FormFieldProps {
  children: React.ReactNode;
  label?: string;
  required?: boolean;
  error?: string;
  hint?: string;
  className?: string;
}

export const FormField: React.FC<FormFieldProps> = ({
  children,
  label,
  required = false,
  error,
  hint,
  className,
}) => {
  return (
    <div className={clsx('space-y-2', className)}>
      {label && (
        <label className="block text-sm font-medium text-text-primary">
          {label}
          {required && <span className="ml-1 text-accent-red">*</span>}
        </label>
      )}
      
      {children}
      
      {error && (
        <p className="text-sm text-semantic-error">
          {error}
        </p>
      )}
      
      {hint && !error && (
        <p className="text-sm text-text-muted">
          {hint}
        </p>
      )}
    </div>
  );
};

interface FormRowProps {
  children: React.ReactNode;
  columns?: 1 | 2 | 3 | 4;
  className?: string;
}

export const FormRow: React.FC<FormRowProps> = ({
  children,
  columns = 2,
  className,
}) => {
  const columnClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
  };

  return (
    <div className={clsx('grid gap-6', columnClasses[columns], className)}>
      {children}
    </div>
  );
};

interface FormActionsProps {
  children: React.ReactNode;
  align?: 'left' | 'center' | 'right';
  className?: string;
}

export const FormActions: React.FC<FormActionsProps> = ({
  children,
  align = 'right',
  className,
}) => {
  const alignClasses = {
    left: 'justify-start',
    center: 'justify-center',
    right: 'justify-end',
  };

  return (
    <div className={clsx(
      'flex items-center gap-3 pt-6 mt-6 border-t border-border-primary',
      alignClasses[align],
      className
    )}>
      {children}
    </div>
  );
};

export default FormField;