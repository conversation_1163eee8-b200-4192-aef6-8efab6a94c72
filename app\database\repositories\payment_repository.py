"""
Repository for tutor payment database operations.
"""

import asyncpg
from typing import List, Optional, Dict, Any
from datetime import date, datetime, timedelta
from decimal import Decimal

from app.models.payment_models import (
    TutorPayment, PaymentBatch, PaymentFilter, 
    PaymentStatus, BatchStatus, WeeklyPaymentReport
)
from app.database.repositories.base import BaseRepository
from app.core.exceptions import ResourceNotFoundError, DatabaseError


class PaymentRepository(BaseRepository):
    """Repository for tutor payment operations."""
    
    async def create_payment(self, db: asyncpg.Connection, payment: TutorPayment) -> TutorPayment:
        """Create a new tutor payment record."""
        try:
            query = """
            INSERT INTO billing_tutor_payments (
                tutor_id, appointment_id, service_amount, transport_amount,
                bonus_amount, total_amount, payment_week_start, payment_week_end,
                status, payment_batch_id
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            RETURNING payment_id, created_at
            """
            
            row = await db.fetchrow(
                query,
                payment.tutor_id,
                payment.appointment_id,
                payment.service_amount,
                payment.transport_amount,
                payment.bonus_amount,
                payment.total_amount,
                payment.payment_week_start,
                payment.payment_week_end,
                payment.status.value,
                payment.payment_batch_id
            )
            
            payment.payment_id = row["payment_id"]
            payment.created_at = row["created_at"]
            
            return payment
            
        except asyncpg.UniqueViolationError:
            raise DatabaseError(f"Payment already exists for appointment {payment.appointment_id}")
        except Exception as e:
            raise DatabaseError(f"Failed to create payment: {str(e)}")
    
    async def create_payment_batch(self, db: asyncpg.Connection, batch: PaymentBatch) -> PaymentBatch:
        """Create a new payment batch."""
        query = """
        INSERT INTO billing_payment_batches (
            week_start, week_end, total_payments, total_amount, status, notes
        ) VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING batch_id, batch_number, created_at
        """
        
        row = await db.fetchrow(
            query,
            batch.week_start,
            batch.week_end,
            batch.total_payments,
            batch.total_amount,
            batch.status.value,
            batch.notes
        )
        
        batch.batch_id = row["batch_id"]
        batch.batch_number = row["batch_number"]
        batch.created_at = row["created_at"]
        
        return batch
    
    async def get_payment_by_id(self, db: asyncpg.Connection, payment_id: int) -> Optional[TutorPayment]:
        """Get payment by ID."""
        query = """
        SELECT p.*, 
               t.first_name || ' ' || t.last_name as tutor_name,
               a.start_time as appointment_date
        FROM billing_tutor_payments p
        JOIN tutor_profiles t ON p.tutor_id = t.tutor_id
        JOIN appointment_appointments a ON p.appointment_id = a.appointment_id
        WHERE p.payment_id = $1
        """
        
        row = await db.fetchrow(query, payment_id)
        if not row:
            return None
        
        return TutorPayment(
            payment_id=row["payment_id"],
            tutor_id=row["tutor_id"],
            appointment_id=row["appointment_id"],
            service_amount=row["service_amount"],
            transport_amount=row["transport_amount"],
            bonus_amount=row["bonus_amount"],
            total_amount=row["total_amount"],
            payment_week_start=row["payment_week_start"],
            payment_week_end=row["payment_week_end"],
            status=PaymentStatus(row["status"]),
            payment_date=row["payment_date"],
            payment_batch_id=row["payment_batch_id"],
            stripe_payout_id=row["stripe_payout_id"],
            stripe_payout_status=row["stripe_payout_status"],
            created_at=row["created_at"],
            updated_at=row["updated_at"]
        )
    
    async def get_batch_by_id(self, db: asyncpg.Connection, batch_id: int) -> Optional[PaymentBatch]:
        """Get payment batch by ID with payments."""
        # Get batch
        batch_query = """
        SELECT * FROM billing_payment_batches WHERE batch_id = $1
        """
        
        batch_row = await db.fetchrow(batch_query, batch_id)
        if not batch_row:
            return None
        
        # Get payments in batch
        payments_query = """
        SELECT p.*, 
               t.first_name || ' ' || t.last_name as tutor_name
        FROM billing_tutor_payments p
        JOIN tutor_profiles t ON p.tutor_id = t.tutor_id
        WHERE p.payment_batch_id = $1
        ORDER BY t.last_name, t.first_name
        """
        
        payment_rows = await db.fetch(payments_query, batch_id)
        
        batch = PaymentBatch(
            batch_id=batch_row["batch_id"],
            batch_number=batch_row["batch_number"],
            week_start=batch_row["week_start"],
            week_end=batch_row["week_end"],
            total_payments=batch_row["total_payments"],
            total_amount=batch_row["total_amount"],
            status=BatchStatus(batch_row["status"]),
            approved_by=batch_row["approved_by"],
            approved_at=batch_row["approved_at"],
            processed_at=batch_row["processed_at"],
            processed_count=batch_row["processed_count"],
            failed_count=batch_row["failed_count"],
            notes=batch_row["notes"],
            created_at=batch_row["created_at"],
            updated_at=batch_row["updated_at"],
            payments=[
                TutorPayment(
                    payment_id=row["payment_id"],
                    tutor_id=row["tutor_id"],
                    appointment_id=row["appointment_id"],
                    service_amount=row["service_amount"],
                    transport_amount=row["transport_amount"],
                    bonus_amount=row["bonus_amount"],
                    total_amount=row["total_amount"],
                    payment_week_start=row["payment_week_start"],
                    payment_week_end=row["payment_week_end"],
                    status=PaymentStatus(row["status"]),
                    payment_date=row["payment_date"],
                    payment_batch_id=row["payment_batch_id"],
                    stripe_payout_id=row["stripe_payout_id"],
                    stripe_payout_status=row["stripe_payout_status"],
                    created_at=row["created_at"]
                )
                for row in payment_rows
            ]
        )
        
        return batch
    
    async def update_payment_status(
        self,
        db: asyncpg.Connection,
        payment_id: int,
        status: PaymentStatus,
        stripe_payout_id: Optional[str] = None,
        stripe_payout_status: Optional[str] = None
    ) -> bool:
        """Update payment status."""
        query = """
        UPDATE billing_tutor_payments
        SET status = $2,
            payment_date = CASE WHEN $2 = 'paid' THEN CURRENT_TIMESTAMP ELSE payment_date END,
            stripe_payout_id = COALESCE($3, stripe_payout_id),
            stripe_payout_status = COALESCE($4, stripe_payout_status),
            updated_at = CURRENT_TIMESTAMP
        WHERE payment_id = $1
        RETURNING payment_id
        """
        
        result = await db.fetchrow(
            query,
            payment_id,
            status.value,
            stripe_payout_id,
            stripe_payout_status
        )
        
        return result is not None
    
    async def update_batch_status(
        self,
        db: asyncpg.Connection,
        batch_id: int,
        status: BatchStatus,
        approved_by: Optional[int] = None,
        processed_count: Optional[int] = None,
        failed_count: Optional[int] = None
    ) -> bool:
        """Update batch status."""
        query = """
        UPDATE billing_payment_batches
        SET status = $2,
            approved_by = COALESCE($3, approved_by),
            approved_at = CASE WHEN $2 = 'approved' THEN CURRENT_TIMESTAMP ELSE approved_at END,
            processed_at = CASE WHEN $2 = 'processing' THEN CURRENT_TIMESTAMP ELSE processed_at END,
            processed_count = COALESCE($4, processed_count),
            failed_count = COALESCE($5, failed_count),
            updated_at = CURRENT_TIMESTAMP
        WHERE batch_id = $1
        RETURNING batch_id
        """
        
        result = await db.fetchrow(
            query,
            batch_id,
            status.value,
            approved_by,
            processed_count,
            failed_count
        )
        
        return result is not None
    
    async def assign_payments_to_batch(
        self,
        db: asyncpg.Connection,
        payment_ids: List[int],
        batch_id: int
    ) -> int:
        """Assign multiple payments to a batch."""
        query = """
        UPDATE billing_tutor_payments
        SET payment_batch_id = $1,
            updated_at = CURRENT_TIMESTAMP
        WHERE payment_id = ANY($2::int[])
        AND payment_batch_id IS NULL
        """
        
        result = await db.execute(query, batch_id, payment_ids)
        # Extract number of affected rows from result string "UPDATE N"
        affected = int(result.split()[1]) if result else 0
        
        return affected
    
    async def find_payments(self, db: asyncpg.Connection, filter_params: PaymentFilter) -> List[TutorPayment]:
        """Find payments with filtering."""
        query = """
        SELECT p.*, 
               t.first_name || ' ' || t.last_name as tutor_name,
               COUNT(*) OVER() as total_count
        FROM billing_tutor_payments p
        JOIN tutor_profiles t ON p.tutor_id = t.tutor_id
        WHERE 1=1
        """
        
        params = []
        conditions = []
        param_count = 0
        
        if filter_params.tutor_id:
            param_count += 1
            conditions.append(f"p.tutor_id = ${param_count}")
            params.append(filter_params.tutor_id)
        
        if filter_params.status:
            param_count += 1
            conditions.append(f"p.status = ${param_count}")
            params.append(filter_params.status.value)
        
        if filter_params.week_start:
            param_count += 1
            conditions.append(f"p.payment_week_start >= ${param_count}")
            params.append(filter_params.week_start)
        
        if filter_params.week_end:
            param_count += 1
            conditions.append(f"p.payment_week_end <= ${param_count}")
            params.append(filter_params.week_end)
        
        if filter_params.batch_id is not None:
            param_count += 1
            if filter_params.batch_id == 0:
                conditions.append("p.payment_batch_id IS NULL")
            else:
                conditions.append(f"p.payment_batch_id = ${param_count}")
                params.append(filter_params.batch_id)
        
        if filter_params.min_amount:
            param_count += 1
            conditions.append(f"p.total_amount >= ${param_count}")
            params.append(filter_params.min_amount)
        
        if filter_params.max_amount:
            param_count += 1
            conditions.append(f"p.total_amount <= ${param_count}")
            params.append(filter_params.max_amount)
        
        if conditions:
            query += " AND " + " AND ".join(conditions)
        
        query += " ORDER BY p.payment_week_start DESC, t.last_name, t.first_name"
        
        param_count += 1
        query += f" LIMIT ${param_count}"
        params.append(filter_params.limit)
        
        param_count += 1
        query += f" OFFSET ${param_count}"
        params.append(filter_params.offset)
        
        rows = await db.fetch(query, *params)
        
        return [
            TutorPayment(
                payment_id=row["payment_id"],
                tutor_id=row["tutor_id"],
                appointment_id=row["appointment_id"],
                service_amount=row["service_amount"],
                transport_amount=row["transport_amount"],
                bonus_amount=row["bonus_amount"],
                total_amount=row["total_amount"],
                payment_week_start=row["payment_week_start"],
                payment_week_end=row["payment_week_end"],
                status=PaymentStatus(row["status"]),
                payment_date=row["payment_date"],
                payment_batch_id=row["payment_batch_id"],
                stripe_payout_id=row["stripe_payout_id"],
                stripe_payout_status=row["stripe_payout_status"],
                created_at=row["created_at"],
                updated_at=row["updated_at"]
            )
            for row in rows
        ]
    
    async def get_unpaid_appointments_for_week(
        self,
        db: asyncpg.Connection,
        week_start: date,
        week_end: date
    ) -> List[Dict[str, Any]]:
        """Get completed appointments that haven't been paid for a specific week."""
        query = """
        SELECT a.appointment_id, a.tutor_id, a.client_id, a.service_id,
               a.start_time, a.end_time, a.duration_minutes,
               a.individual_rate, a.transport_fee, 
               a.surge_multiplier, a.surge_amount,
               t.first_name || ' ' || t.last_name as tutor_name,
               c.first_name || ' ' || c.last_name as client_name,
               s.name as service_name,
               ts.tutor_rate as base_rate
        FROM appointment_appointments a
        JOIN tutor_profiles t ON a.tutor_id = t.tutor_id
        JOIN client_profiles c ON a.client_id = c.client_id
        JOIN service_catalog s ON a.service_id = s.service_id
        JOIN tutor_service_rates ts ON ts.tutor_id = a.tutor_id AND ts.service_id = a.service_id
        WHERE a.status = 'completed'
        AND DATE(a.start_time) >= $1
        AND DATE(a.start_time) <= $2
        AND a.appointment_id NOT IN (
            SELECT appointment_id 
            FROM billing_tutor_payments
        )
        ORDER BY a.tutor_id, a.start_time
        """
        
        rows = await db.fetch(query, week_start, week_end)
        return [dict(row) for row in rows]
    
    async def get_weekly_payment_report(
        self,
        db: asyncpg.Connection,
        week_start: date,
        week_end: date
    ) -> WeeklyPaymentReport:
        """Generate weekly payment report."""
        # Check if batch exists
        batch_query = """
        SELECT * FROM billing_payment_batches
        WHERE week_start = $1 AND week_end = $2
        ORDER BY created_at DESC
        LIMIT 1
        """
        
        batch_row = await db.fetchrow(batch_query, week_start, week_end)
        
        report = WeeklyPaymentReport(
            week_start=week_start,
            week_end=week_end
        )
        
        if batch_row:
            report.batch = await self.get_batch_by_id(db, batch_row["batch_id"])
            
            # Add payments to report
            for payment in report.batch.payments:
                report.add_payment(payment)
        else:
            # Get pending payments for the week
            pending_payments = await self.find_payments(
                db,
                PaymentFilter(
                    week_start=week_start,
                    week_end=week_end,
                    status=PaymentStatus.PENDING,
                    limit=1000  # Get all payments for the week
                )
            )
            
            for payment in pending_payments:
                report.add_payment(payment)
        
        # Calculate summaries
        report.calculate_summaries()
        
        return report
    
    async def get_tutor_payment_history(
        self,
        db: asyncpg.Connection,
        tutor_id: int,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """Get payment history for a tutor."""
        query = """
        SELECT p.*, 
               a.start_time as appointment_date,
               c.first_name || ' ' || c.last_name as client_name,
               s.name as service_name,
               b.batch_number
        FROM billing_tutor_payments p
        JOIN appointment_appointments a ON p.appointment_id = a.appointment_id
        JOIN client_profiles c ON a.client_id = c.client_id
        JOIN service_catalog s ON a.service_id = s.service_id
        LEFT JOIN billing_payment_batches b ON p.payment_batch_id = b.batch_id
        WHERE p.tutor_id = $1
        ORDER BY p.created_at DESC
        LIMIT $2
        """
        
        rows = await db.fetch(query, tutor_id, limit)
        return [dict(row) for row in rows]
    
    async def get_payment_stats_for_period(
        self,
        db: asyncpg.Connection,
        start_date: date,
        end_date: date
    ) -> Dict[str, Any]:
        """Get payment statistics for a period."""
        query = """
        SELECT 
            COUNT(DISTINCT tutor_id) as total_tutors,
            COUNT(*) as total_payments,
            SUM(service_amount) as total_service_amount,
            SUM(transport_amount) as total_transport_amount,
            SUM(bonus_amount) as total_bonus_amount,
            SUM(total_amount) as grand_total,
            COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid_count,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
            COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count
        FROM billing_tutor_payments
        WHERE payment_week_start >= $1 AND payment_week_end <= $2
        """
        
        row = await db.fetchrow(query, start_date, end_date)
        
        return {
            "total_tutors": row["total_tutors"] or 0,
            "total_payments": row["total_payments"] or 0,
            "total_service_amount": float(row["total_service_amount"] or 0),
            "total_transport_amount": float(row["total_transport_amount"] or 0),
            "total_bonus_amount": float(row["total_bonus_amount"] or 0),
            "grand_total": float(row["grand_total"] or 0),
            "paid_count": row["paid_count"] or 0,
            "pending_count": row["pending_count"] or 0,
            "failed_count": row["failed_count"] or 0
        }