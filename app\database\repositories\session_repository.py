"""
Session repository for database operations.
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from uuid import UUID
import asyncpg

from app.database.repositories.base import BaseRepository
from app.core.timezone import now_est
from app.core.logging import TutorAideLogger

logger = TutorAideLogger.get_logger(__name__)


class SessionRepository(BaseRepository):
    """Repository for session database operations."""
    
    def __init__(self):
        """Initialize session repository."""
        super().__init__("user_sessions", "session_id")
    
    async def create(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        token_hash: str,
        role_type: str,
        expires_at: datetime,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> Dict[str, Any]:
        """Create a new session."""
        query = """
            INSERT INTO user_sessions (
                user_id, token_hash, role_type, ip_address, 
                user_agent, expires_at, last_activity
            ) VALUES ($1, $2, $3, $4, $5, $6, $7)
            RETURNING 
                session_id, user_id, token_hash, role_type,
                ip_address, user_agent, expires_at, last_activity,
                is_active, created_at, updated_at
        """
        
        now = now_est()
        row = await conn.fetchrow(
            query, user_id, token_hash, role_type, 
            ip_address, user_agent, expires_at, now
        )
        
        return dict(row) if row else None
    
    async def find_by_token_hash(
        self, 
        conn: asyncpg.Connection, 
        token_hash: str
    ) -> Optional[Dict[str, Any]]:
        """Find session by token hash."""
        query = """
            SELECT 
                session_id, user_id, token_hash, role_type,
                ip_address, user_agent, expires_at, last_activity,
                is_active, created_at, updated_at
            FROM user_sessions
            WHERE token_hash = $1 AND is_active = true
        """
        
        row = await conn.fetchrow(query, token_hash)
        return dict(row) if row else None
    
    async def find_by_id(
        self, 
        conn: asyncpg.Connection, 
        session_id: UUID
    ) -> Optional[Dict[str, Any]]:
        """Find session by ID."""
        query = """
            SELECT 
                session_id, user_id, token_hash, role_type,
                ip_address, user_agent, expires_at, last_activity,
                is_active, created_at, updated_at
            FROM user_sessions
            WHERE session_id = $1
        """
        
        row = await conn.fetchrow(query, session_id)
        return dict(row) if row else None
    
    async def update(
        self,
        conn: asyncpg.Connection,
        session_id: UUID,
        update_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Update session information."""
        # Build dynamic update query
        set_clauses = []
        values = []
        param_count = 1
        
        for field, value in update_data.items():
            set_clauses.append(f"{field} = ${param_count}")
            values.append(value)
            param_count += 1
        
        values.append(session_id)
        
        query = f"""
            UPDATE user_sessions
            SET {', '.join(set_clauses)}
            WHERE session_id = ${param_count}
            RETURNING 
                session_id, user_id, token_hash, role_type,
                ip_address, user_agent, expires_at, last_activity,
                is_active, created_at, updated_at
        """
        
        row = await conn.fetchrow(query, *values)
        return dict(row) if row else None
    
    async def invalidate_session(
        self,
        conn: asyncpg.Connection,
        session_id: UUID
    ) -> bool:
        """Invalidate a specific session."""
        query = """
            UPDATE user_sessions
            SET is_active = false, updated_at = $1
            WHERE session_id = $2 AND is_active = true
        """
        
        result = await conn.execute(query, now_est(), session_id)
        return result.split()[-1] != "0"
    
    async def invalidate_user_sessions(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        except_session_id: Optional[UUID] = None
    ) -> int:
        """Invalidate all sessions for a user, optionally except one."""
        if except_session_id:
            query = """
                UPDATE user_sessions
                SET is_active = false, updated_at = $1
                WHERE user_id = $2 AND is_active = true 
                AND session_id != $3
            """
            result = await conn.execute(query, now_est(), user_id, except_session_id)
        else:
            query = """
                UPDATE user_sessions
                SET is_active = false, updated_at = $1
                WHERE user_id = $2 AND is_active = true
            """
            result = await conn.execute(query, now_est(), user_id)
        
        return int(result.split()[-1])
    
    async def get_user_sessions(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        only_active: bool = True
    ) -> List[Dict[str, Any]]:
        """Get all sessions for a user."""
        query = """
            SELECT 
                session_id, user_id, role_type,
                ip_address, user_agent, expires_at, 
                last_activity, is_active, created_at
            FROM user_sessions
            WHERE user_id = $1
        """
        
        if only_active:
            query += " AND is_active = true"
        
        query += " ORDER BY last_activity DESC"
        
        rows = await conn.fetch(query, user_id)
        return [dict(row) for row in rows]
    
    async def count_active_sessions(
        self,
        conn: asyncpg.Connection,
        user_id: int
    ) -> int:
        """Count active sessions for a user."""
        query = """
            SELECT COUNT(*) as count
            FROM user_sessions
            WHERE user_id = $1 AND is_active = true
            AND expires_at > $2
        """
        
        row = await conn.fetchrow(query, user_id, now_est())
        return row['count'] if row else 0
    
    async def cleanup_expired(
        self,
        conn: asyncpg.Connection
    ) -> int:
        """Clean up expired sessions."""
        query = """
            UPDATE user_sessions
            SET is_active = false, updated_at = $1
            WHERE is_active = true AND expires_at < $1
        """
        
        result = await conn.execute(query, now_est())
        return int(result.split()[-1])
    
    async def cleanup_inactive(
        self,
        conn: asyncpg.Connection,
        inactive_minutes: int = 60
    ) -> int:
        """Clean up sessions inactive for specified minutes."""
        query = """
            UPDATE user_sessions
            SET is_active = false, updated_at = $1
            WHERE is_active = true 
            AND last_activity < $1 - INTERVAL '%s minutes'
        """
        
        result = await conn.execute(query % inactive_minutes, now_est())
        return int(result.split()[-1])