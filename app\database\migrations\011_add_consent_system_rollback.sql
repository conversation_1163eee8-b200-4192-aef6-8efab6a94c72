-- Rollback Migration: Remove consent management system
-- Description: Drop all consent-related tables and types

-- Drop triggers first
DROP TRIGGER IF EXISTS trigger_user_consents_history ON user_consents;
DROP TRIGGER IF EXISTS trigger_user_consents_timestamps ON user_consents;
DROP TRIGGER IF EXISTS trigger_user_consents_updated_at ON user_consents;
DROP TRIGGER IF EXISTS trigger_consent_documents_updated_at ON consent_documents;

-- Drop functions
DROP FUNCTION IF EXISTS log_consent_history();
DROP FUNCTION IF EXISTS update_consent_timestamps();
DROP FUNCTION IF EXISTS update_user_consents_updated_at();
DROP FUNCTION IF EXISTS update_consent_documents_updated_at();

-- Drop indexes
DROP INDEX IF EXISTS idx_consent_history_user_type;
DROP INDEX IF EXISTS idx_consent_history_created_at;
DROP INDEX IF EXISTS idx_consent_history_consent_type;
DROP INDEX IF EXISTS idx_consent_history_user_id;

DROP INDEX IF EXISTS idx_user_consents_user_document;
DROP INDEX IF EXISTS idx_user_consents_expires_at;
DROP INDEX IF EXISTS idx_user_consents_granted_at;
DROP INDEX IF EXISTS idx_user_consents_status;
DROP INDEX IF EXISTS idx_user_consents_document_id;
DROP INDEX IF EXISTS idx_user_consents_user_id;

DROP INDEX IF EXISTS idx_consent_documents_category;
DROP INDEX IF EXISTS idx_consent_documents_level;
DROP INDEX IF EXISTS idx_consent_documents_active;
DROP INDEX IF EXISTS idx_consent_documents_type_lang;

-- Drop tables (in reverse dependency order)
DROP TABLE IF EXISTS consent_history;
DROP TABLE IF EXISTS user_consents;
DROP TABLE IF EXISTS consent_documents;

-- Drop custom types
DROP TYPE IF EXISTS consent_category_type;
DROP TYPE IF EXISTS consent_status_type;
DROP TYPE IF EXISTS consent_level_type;