-- Rollback: 026_add_messaging_system.sql
-- Description: Remove messaging system tables
-- Author: System
-- Date: 2025-01-20

-- Drop indexes first
DROP INDEX IF EXISTS idx_messaging_broadcast_recipients_user;
DROP INDEX IF EXISTS idx_messaging_broadcast_recipients_broadcast;
DROP INDEX IF EXISTS idx_messaging_broadcasts_scheduled;
DROP INDEX IF EXISTS idx_messaging_broadcasts_status;
DROP INDEX IF EXISTS idx_messaging_templates_category;
DROP INDEX IF EXISTS idx_messaging_templates_active;
DROP INDEX IF EXISTS idx_messaging_messages_unread;
DROP INDEX IF EXISTS idx_messaging_messages_created;
DROP INDEX IF EXISTS idx_messaging_messages_conversation;
DROP INDEX IF EXISTS idx_messaging_conversations_last_message;
DROP INDEX IF EXISTS idx_messaging_conversations_phone;
DROP INDEX IF EXISTS idx_messaging_conversations_tutor;
DROP INDEX IF EXISTS idx_messaging_conversations_client;

-- Drop tables in reverse order of dependencies
DROP TABLE IF EXISTS messaging_attachments CASCADE;
DROP TABLE IF EXISTS messaging_broadcast_recipients CASCADE;
DROP TABLE IF EXISTS messaging_broadcasts CASCADE;
DROP TABLE IF EXISTS messaging_templates CASCADE;
DROP TABLE IF EXISTS messaging_messages CASCADE;
DROP TABLE IF EXISTS messaging_conversations CASCADE;