import React, { useState, useEffect } from 'react';
import { X, FileText, Calendar, DollarSign, User, Clock, ChevronRight } from 'lucide-react';
import { format } from 'date-fns';
import { useTranslation } from 'react-i18next';
import api from '../../utils/api';

interface InvoiceDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  invoice: any;
}

interface AuditLog {
  audit_id: number;
  action: string;
  performed_by: number;
  performed_by_name: string;
  performed_by_role: string;
  field_name?: string;
  old_value?: string;
  new_value?: string;
  change_reason?: string;
  amount_before?: number;
  amount_after?: number;
  stripe_transaction_id?: string;
  created_at: string;
}

const InvoiceDetailsModal: React.FC<InvoiceDetailsModalProps> = ({
  isOpen,
  onClose,
  invoice
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<'details' | 'audit'>('details');
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isOpen && invoice && activeTab === 'audit') {
      fetchAuditLogs();
    }
  }, [isOpen, invoice, activeTab]);

  const fetchAuditLogs = async () => {
    if (!invoice?.invoice_id) return;
    
    setLoading(true);
    try {
      const response = await api.get(`/billing/invoices/${invoice.invoice_id}/audit-logs`);
      setAuditLogs(response.data.logs || []);
    } catch (error) {
      console.error('Error fetching audit logs:', error);
    } finally {
      setLoading(false);
    }
  };

  const getActionBadgeColor = (action: string) => {
    switch (action) {
      case 'created': return 'bg-green-100 text-green-800';
      case 'updated': return 'bg-blue-100 text-blue-800';
      case 'paid': return 'bg-purple-100 text-purple-800';
      case 'refunded': return 'bg-orange-100 text-orange-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'manager': return 'bg-indigo-100 text-indigo-800';
      case 'client': return 'bg-green-100 text-green-800';
      case 'system': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD'
    }).format(amount);
  };

  if (!isOpen || !invoice) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20">
        <div className="fixed inset-0 transition-opacity" onClick={onClose}>
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <div className="relative bg-white rounded-lg max-w-4xl w-full shadow-xl transform transition-all">
          {/* Header */}
          <div className="bg-gray-50 px-6 py-4 rounded-t-lg border-b">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <FileText className="h-6 w-6 text-gray-600" />
                <h3 className="text-lg font-semibold text-gray-900">
                  {t('billing.invoiceDetails')} - {invoice.invoice_number}
                </h3>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-500 transition-colors"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="flex border-b">
            <button
              onClick={() => setActiveTab('details')}
              className={`px-6 py-3 text-sm font-medium transition-colors ${
                activeTab === 'details'
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              {t('billing.details')}
            </button>
            <button
              onClick={() => setActiveTab('audit')}
              className={`px-6 py-3 text-sm font-medium transition-colors ${
                activeTab === 'audit'
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              {t('billing.auditLog')}
            </button>
          </div>

          {/* Content */}
          <div className="p-6 max-h-96 overflow-y-auto">
            {activeTab === 'details' ? (
              <div className="space-y-6">
                {/* Invoice Summary */}
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-sm font-medium text-gray-500 mb-2">
                      {t('billing.clientInfo')}
                    </h4>
                    <div className="space-y-1">
                      <p className="text-sm font-medium">{invoice.client_name}</p>
                      {invoice.billing_address && (
                        <div className="text-sm text-gray-600">
                          <p>{invoice.billing_address.address_line_1}</p>
                          {invoice.billing_address.address_line_2 && (
                            <p>{invoice.billing_address.address_line_2}</p>
                          )}
                          <p>
                            {invoice.billing_address.city}, {invoice.billing_address.province} {invoice.billing_address.postal_code}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">{t('billing.status')}:</span>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                        invoice.status === 'paid' ? 'bg-green-100 text-green-800' :
                        invoice.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        invoice.status === 'overdue' ? 'bg-red-100 text-red-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {t(`billing.status.${invoice.status}`)}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">{t('billing.issueDate')}:</span>
                      <span className="text-sm">{format(new Date(invoice.issue_date), 'MMM dd, yyyy')}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">{t('billing.dueDate')}:</span>
                      <span className="text-sm">{format(new Date(invoice.due_date), 'MMM dd, yyyy')}</span>
                    </div>
                    {invoice.paid_date && (
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-500">{t('billing.paidDate')}:</span>
                        <span className="text-sm">{format(new Date(invoice.paid_date), 'MMM dd, yyyy')}</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Line Items */}
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-3">
                    {t('billing.lineItems')}
                  </h4>
                  <div className="bg-gray-50 rounded-lg overflow-hidden">
                    <table className="min-w-full">
                      <thead>
                        <tr className="bg-gray-100">
                          <th className="px-4 py-2 text-left text-xs font-medium text-gray-700">
                            {t('billing.description')}
                          </th>
                          <th className="px-4 py-2 text-right text-xs font-medium text-gray-700">
                            {t('billing.quantity')}
                          </th>
                          <th className="px-4 py-2 text-right text-xs font-medium text-gray-700">
                            {t('billing.rate')}
                          </th>
                          <th className="px-4 py-2 text-right text-xs font-medium text-gray-700">
                            {t('billing.amount')}
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {invoice.line_items?.map((item: any, index: number) => (
                          <tr key={index} className="border-t border-gray-200">
                            <td className="px-4 py-2 text-sm">{item.description}</td>
                            <td className="px-4 py-2 text-sm text-right">{item.quantity}h</td>
                            <td className="px-4 py-2 text-sm text-right">{formatCurrency(item.unit_price)}</td>
                            <td className="px-4 py-2 text-sm text-right font-medium">
                              {formatCurrency(item.line_total)}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                      <tfoot>
                        <tr className="border-t-2 border-gray-300">
                          <td colSpan={3} className="px-4 py-2 text-sm text-right font-medium">
                            {t('billing.subtotal')}:
                          </td>
                          <td className="px-4 py-2 text-sm text-right font-medium">
                            {formatCurrency(invoice.amount)}
                          </td>
                        </tr>
                        <tr>
                          <td colSpan={3} className="px-4 py-2 text-sm text-right font-medium">
                            {t('billing.tax')}:
                          </td>
                          <td className="px-4 py-2 text-sm text-right font-medium">
                            {formatCurrency(invoice.tax_amount)}
                          </td>
                        </tr>
                        <tr className="bg-gray-100">
                          <td colSpan={3} className="px-4 py-3 text-sm text-right font-semibold">
                            {t('billing.total')}:
                          </td>
                          <td className="px-4 py-3 text-sm text-right font-semibold text-gray-900">
                            {formatCurrency(invoice.total_amount)}
                          </td>
                        </tr>
                      </tfoot>
                    </table>
                  </div>
                </div>

                {/* Payment Information */}
                {invoice.payment_method && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-2">
                      {t('billing.paymentInfo')}
                    </h4>
                    <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">{t('billing.method')}:</span>
                        <span className="text-sm font-medium">{invoice.payment_method}</span>
                      </div>
                      {invoice.payment_reference && (
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">{t('billing.reference')}:</span>
                          <span className="text-sm font-mono">{invoice.payment_reference}</span>
                        </div>
                      )}
                      {invoice.paid_by_parent !== null && (
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">{t('billing.paidBy')}:</span>
                          <span className="text-sm">
                            {invoice.paid_by_parent === 1 ? t('billing.parent1') : t('billing.parent2')}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="space-y-4">
                {loading ? (
                  <div className="text-center py-8">
                    <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <p className="mt-2 text-sm text-gray-600">{t('common.loading')}</p>
                  </div>
                ) : auditLogs.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Clock className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                    <p>{t('billing.noAuditLogs')}</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {auditLogs.map((log) => (
                      <div key={log.audit_id} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            <span className={`px-2 py-1 text-xs font-medium rounded ${getActionBadgeColor(log.action)}`}>
                              {t(`billing.actions.${log.action}`)}
                            </span>
                            <span className={`px-2 py-1 text-xs font-medium rounded ${getRoleBadgeColor(log.performed_by_role)}`}>
                              {t(`roles.${log.performed_by_role}`)}
                            </span>
                          </div>
                          <span className="text-xs text-gray-500">
                            {format(new Date(log.created_at), 'MMM dd, yyyy HH:mm')}
                          </span>
                        </div>

                        <div className="flex items-center text-sm text-gray-700 mb-2">
                          <User className="h-4 w-4 mr-1 text-gray-500" />
                          <span className="font-medium">{log.performed_by_name}</span>
                        </div>

                        {log.field_name && (
                          <div className="text-sm space-y-1">
                            <div className="flex items-center text-gray-600">
                              <ChevronRight className="h-4 w-4 mr-1" />
                              <span className="font-medium">{t(`billing.fields.${log.field_name}`)}:</span>
                            </div>
                            {log.old_value && (
                              <div className="ml-5 text-gray-500">
                                <span className="text-red-600 line-through">{log.old_value}</span>
                              </div>
                            )}
                            {log.new_value && (
                              <div className="ml-5 text-gray-700">
                                <span className="text-green-600">{log.new_value}</span>
                              </div>
                            )}
                          </div>
                        )}

                        {(log.amount_before !== null || log.amount_after !== null) && (
                          <div className="mt-2 text-sm">
                            <span className="text-gray-600">{t('billing.amount')}:</span>
                            {log.amount_before !== null && (
                              <span className="text-red-600 line-through ml-2">
                                {formatCurrency(log.amount_before)}
                              </span>
                            )}
                            {log.amount_after !== null && (
                              <span className="text-green-600 ml-2">
                                {formatCurrency(log.amount_after)}
                              </span>
                            )}
                          </div>
                        )}

                        {log.change_reason && (
                          <div className="mt-2 text-sm text-gray-600 italic">
                            "{log.change_reason}"
                          </div>
                        )}

                        {log.stripe_transaction_id && (
                          <div className="mt-2 text-xs text-gray-500 font-mono">
                            Stripe: {log.stripe_transaction_id}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="bg-gray-50 px-6 py-4 rounded-b-lg border-t">
            <div className="flex justify-end">
              <button
                onClick={onClose}
                className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
              >
                {t('common.close')}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InvoiceDetailsModal;