import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Search, X, User, GraduationCap, Baby, Calendar, FileText, MapPin, Clock } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { UserRoleType } from '../../types/auth';
import { useTranslation } from '../../hooks/useTranslation';
import { motion, AnimatePresence } from 'framer-motion';
import clsx from 'clsx';

interface SearchResult {
  id: string;
  type: 'client' | 'tutor' | 'dependant' | 'appointment' | 'invoice' | 'location';
  title: string;
  subtitle: string;
  path: string;
  icon: React.ReactNode;
  metadata?: Record<string, any>;
}

interface GlobalSearchProps {
  isOpen: boolean;
  onClose: () => void;
}

export const GlobalSearch: React.FC<GlobalSearchProps> = ({ isOpen, onClose }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const debounceTimerRef = useRef<NodeJS.Timeout>();

  // Mock search function - replace with actual API call
  const performSearch = useCallback(async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults([]);
      return;
    }

    setIsLoading(true);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));

    // Mock results based on user role and query
    const mockResults: SearchResult[] = [];

    // Search clients
    if (user?.activeRole === UserRoleType.MANAGER || searchQuery.toLowerCase().includes('client')) {
      mockResults.push(
        {
          id: '1',
          type: 'client',
          title: 'Sarah Johnson',
          subtitle: 'Client • <EMAIL>',
          path: '/users/clients/1',
          icon: <User className="w-4 h-4" />,
          metadata: { email: '<EMAIL>', phone: '(*************' }
        },
        {
          id: '2',
          type: 'client',
          title: 'Michael Chen',
          subtitle: 'Client • <EMAIL>',
          path: '/users/clients/2',
          icon: <User className="w-4 h-4" />,
          metadata: { email: '<EMAIL>', phone: '(*************' }
        }
      );
    }

    // Search tutors
    if (searchQuery.toLowerCase().includes('tutor') || searchQuery.toLowerCase().includes('math')) {
      mockResults.push(
        {
          id: '3',
          type: 'tutor',
          title: 'Emma Wilson',
          subtitle: 'Tutor • Mathematics, Physics',
          path: '/users/tutors/3',
          icon: <GraduationCap className="w-4 h-4" />,
          metadata: { subjects: ['Mathematics', 'Physics'], rating: 4.8 }
        },
        {
          id: '4',
          type: 'tutor',
          title: 'James Brown',
          subtitle: 'Tutor • French, English',
          path: '/users/tutors/4',
          icon: <GraduationCap className="w-4 h-4" />,
          metadata: { subjects: ['French', 'English'], rating: 4.9 }
        }
      );
    }

    // Search dependants
    if (searchQuery.toLowerCase().includes('child') || searchQuery.toLowerCase().includes('dependant')) {
      mockResults.push(
        {
          id: '5',
          type: 'dependant',
          title: 'Sophie Johnson',
          subtitle: 'Dependant • Grade 8 • Parent: Sarah Johnson',
          path: '/users/dependants/5',
          icon: <Baby className="w-4 h-4" />,
          metadata: { grade: 8, parent: 'Sarah Johnson' }
        }
      );
    }

    // Search appointments
    if (searchQuery.toLowerCase().includes('appointment') || searchQuery.toLowerCase().includes('session')) {
      mockResults.push(
        {
          id: '6',
          type: 'appointment',
          title: 'Math Tutoring Session',
          subtitle: 'Today at 3:00 PM • Emma Wilson with Sophie Johnson',
          path: '/calendar/day?appointment=6',
          icon: <Calendar className="w-4 h-4" />,
          metadata: { date: new Date(), tutor: 'Emma Wilson', client: 'Sophie Johnson' }
        }
      );
    }

    // Search invoices
    if (user?.activeRole === UserRoleType.MANAGER && searchQuery.toLowerCase().includes('invoice')) {
      mockResults.push(
        {
          id: '7',
          type: 'invoice',
          title: 'Invoice #INV-2024-001',
          subtitle: '$120.00 • Sarah Johnson • Due Jan 31',
          path: '/billing/invoices/7',
          icon: <FileText className="w-4 h-4" />,
          metadata: { amount: 120, client: 'Sarah Johnson', dueDate: '2024-01-31' }
        }
      );
    }

    // Search locations
    if (searchQuery.toLowerCase().includes('montreal') || searchQuery.toLowerCase().includes('location')) {
      mockResults.push(
        {
          id: '8',
          type: 'location',
          title: 'Downtown Montreal',
          subtitle: '5 tutors available • 3.2 km away',
          path: '/map?location=downtown-montreal',
          icon: <MapPin className="w-4 h-4" />,
          metadata: { tutorCount: 5, distance: 3.2 }
        }
      );
    }

    // Filter results based on search query
    const filteredResults = mockResults.filter(result => 
      result.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      result.subtitle.toLowerCase().includes(searchQuery.toLowerCase())
    );

    setResults(filteredResults.slice(0, 8)); // Limit to 8 results
    setIsLoading(false);
    setSelectedIndex(0);
  }, [user?.activeRole]);

  // Debounced search
  useEffect(() => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    debounceTimerRef.current = setTimeout(() => {
      performSearch(query);
    }, 300);

    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [query, performSearch]);

  // Focus search input when modal opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev => (prev + 1) % results.length);
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => (prev - 1 + results.length) % results.length);
          break;
        case 'Enter':
          e.preventDefault();
          if (results[selectedIndex]) {
            handleResultClick(results[selectedIndex]);
          }
          break;
        case 'Escape':
          e.preventDefault();
          onClose();
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, results, selectedIndex, onClose]);

  const handleResultClick = (result: SearchResult) => {
    navigate(result.path);
    onClose();
    setQuery('');
    setResults([]);
  };

  const getRecentSearches = (): SearchResult[] => {
    // Mock recent searches - in production, this would come from localStorage or API
    return [
      {
        id: 'recent-1',
        type: 'client',
        title: 'Sarah Johnson',
        subtitle: 'Recently viewed',
        path: '/users/clients/1',
        icon: <Clock className="w-4 h-4" />
      },
      {
        id: 'recent-2',
        type: 'appointment',
        title: 'Math Tutoring Session',
        subtitle: 'Recently viewed',
        path: '/calendar/day?appointment=6',
        icon: <Clock className="w-4 h-4" />
      }
    ];
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50">
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          onClick={onClose}
        />

        {/* Search Modal */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: -20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: -20 }}
          transition={{ duration: 0.2, ease: 'easeOut' }}
          className="absolute top-20 left-1/2 -translate-x-1/2 w-full max-w-2xl"
        >
          <div className="bg-white rounded-2xl shadow-elevated overflow-hidden">
            {/* Search Input */}
            <div className="p-4 border-b border-border-primary">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-text-muted" />
                <input
                  ref={searchInputRef}
                  type="text"
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  placeholder={t('search.placeholder')}
                  className="w-full pl-12 pr-12 py-3 text-lg bg-background-secondary rounded-xl focus:outline-none focus:ring-2 focus:ring-accent-red/20"
                />
                {query && (
                  <button
                    onClick={() => setQuery('')}
                    className="absolute right-4 top-1/2 -translate-y-1/2 p-1 hover:bg-background-tertiary rounded-lg transition-colors"
                  >
                    <X className="w-4 h-4 text-text-muted" />
                  </button>
                )}
              </div>
            </div>

            {/* Search Results */}
            <div className="max-h-[60vh] overflow-y-auto">
              {isLoading ? (
                <div className="p-8 text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-accent-red mx-auto"></div>
                  <p className="mt-4 text-text-muted">{t('search.searching')}</p>
                </div>
              ) : results.length > 0 ? (
                <div className="py-2">
                  {results.map((result, index) => (
                    <button
                      key={result.id}
                      onClick={() => handleResultClick(result)}
                      onMouseEnter={() => setSelectedIndex(index)}
                      className={clsx(
                        'w-full px-4 py-3 flex items-start gap-3 hover:bg-background-secondary transition-colors text-left',
                        selectedIndex === index && 'bg-background-secondary'
                      )}
                    >
                      <div className={clsx(
                        'w-10 h-10 rounded-xl flex items-center justify-center',
                        result.type === 'client' && 'bg-blue-100 text-blue-600',
                        result.type === 'tutor' && 'bg-green-100 text-green-600',
                        result.type === 'dependant' && 'bg-purple-100 text-purple-600',
                        result.type === 'appointment' && 'bg-orange-100 text-orange-600',
                        result.type === 'invoice' && 'bg-red-100 text-red-600',
                        result.type === 'location' && 'bg-indigo-100 text-indigo-600'
                      )}>
                        {result.icon}
                      </div>
                      <div className="flex-1 text-left">
                        <p className="font-medium text-text-primary">{result.title}</p>
                        <p className="text-sm text-text-muted">{result.subtitle}</p>
                      </div>
                      {selectedIndex === index && (
                        <div className="text-xs text-text-muted bg-background-tertiary px-2 py-1 rounded-lg">
                          Press Enter
                        </div>
                      )}
                    </button>
                  ))}
                </div>
              ) : query ? (
                <div className="p-8 text-center">
                  <p className="text-text-muted">{t('search.noResults')}</p>
                </div>
              ) : (
                <div className="py-4">
                  <p className="px-4 text-sm font-medium text-text-muted mb-2">{t('search.recent')}</p>
                  {getRecentSearches().map((result, index) => (
                    <button
                      key={result.id}
                      onClick={() => handleResultClick(result)}
                      className="w-full px-4 py-3 flex items-start gap-3 hover:bg-background-secondary transition-colors text-left"
                    >
                      <div className="w-10 h-10 rounded-xl bg-background-tertiary flex items-center justify-center">
                        {result.icon}
                      </div>
                      <div className="flex-1 text-left">
                        <p className="font-medium text-text-primary">{result.title}</p>
                        <p className="text-sm text-text-muted">{result.subtitle}</p>
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Quick Actions */}
            <div className="p-4 border-t border-border-primary bg-background-secondary">
              <p className="text-xs text-text-muted mb-2">{t('search.shortcuts')}</p>
              <div className="flex gap-2 text-xs">
                <kbd className="px-2 py-1 bg-white rounded-lg border border-border-primary">↑↓</kbd>
                <span className="text-text-muted">Navigate</span>
                <kbd className="px-2 py-1 bg-white rounded-lg border border-border-primary ml-4">Enter</kbd>
                <span className="text-text-muted">Select</span>
                <kbd className="px-2 py-1 bg-white rounded-lg border border-border-primary ml-4">Esc</kbd>
                <span className="text-text-muted">Close</span>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};