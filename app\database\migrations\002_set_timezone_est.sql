-- Set timezone to Eastern Standard Time
-- Version: 002
-- Description: Configure database to use EST timezone for all timestamp operations
-- Author: TutorAide Development Team
-- Date: 2025-06-11

-- For the current session and all new connections
SET timezone = 'America/New_York';

-- Create a function to get current time in EST
CREATE OR REPLACE FUNCTION current_timestamp_est()
RETURNS TIMESTAMP WITH TIME ZONE AS $$
BEGIN
    RETURN CURRENT_TIMESTAMP AT TIME ZONE 'America/New_York';
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Create a function to convert UTC to EST
CREATE OR REPLACE FUNCTION utc_to_est(timestamp_utc TIMESTAMP)
RETURNS TIMESTAMP WITH TIME ZONE AS $$
BEGIN
    RETURN timestamp_utc AT TIME ZONE 'UTC' AT TIME ZONE 'America/New_York';
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Create a function to convert EST to UTC
CREATE OR REPLACE FUNCTION est_to_utc(timestamp_est TIMESTAMP)
RETURNS TIMESTAMP WITH TIME ZONE AS $$
BEGIN
    RETURN timestamp_est AT TIME ZONE 'America/New_York' AT TIME ZONE 'UTC';
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Add comment to track migration
COMMENT ON FUNCTION current_timestamp_est() IS 'Returns current timestamp in EST/EDT timezone';
COMMENT ON FUNCTION utc_to_est(TIMESTAMP) IS 'Converts UTC timestamp to EST/EDT timezone';
COMMENT ON FUNCTION est_to_utc(TIMESTAMP) IS 'Converts EST/EDT timestamp to UTC timezone';