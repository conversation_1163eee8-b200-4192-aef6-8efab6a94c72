# Security Notes - Email Sending Implementation

## Password Reset Email Security

### Current Implementation (Synchronous with Timing Protection)

Due to FastAPI BackgroundTasks not executing in Railway production, we've implemented synchronous email sending with security mitigations.

#### Security Measures:

1. **Consistent Response Timing**
   - All password reset requests take exactly 2 seconds to respond
   - Prevents timing attacks to enumerate valid email addresses
   - Code: `await asyncio.sleep(2.0 - elapsed)` if response is faster than 2s

2. **Generic Response Messages**
   - Always return: "If the email exists, a password reset link has been sent"
   - Never reveal whether email exists in database

3. **Rate Limiting**
   - 3 password reset requests per hour per email/IP address
   - Prevents brute force attempts and reduces DoS risk

4. **Secure Token Generation**
   - Tokens are cryptographically secure random strings
   - Tokens expire after 1 hour
   - One-time use only

#### Trade-offs:

**Security Risks of Synchronous Sending:**
- Potential for more sophisticated timing attacks (mitigated by 2s delay)
- Each request holds SMTP connection (mitigated by rate limiting)
- Slightly more vulnerable to resource exhaustion

**Why This is Acceptable:**
- The alternative (no emails) is worse for users
- Rate limiting prevents most abuse scenarios
- 2-second delay makes timing attacks impractical
- Can upgrade to proper async queue later

### Future Improvements:

1. **Short Term: Add SendGrid**
   - API calls are faster than SMTP
   - More reliable in cloud environments
   - See TECFEE_quiz implementation for reference

2. **Long Term: Proper Task Queue**
   - Implement Redis + Celery
   - Maintains all async security benefits
   - Industry-standard solution

### Comparison with TECFEE Quiz

TECFEE quiz can use simple synchronous sending because:
- Quiz results aren't security-sensitive
- No risk of account takeover
- No timing attack concerns

Password reset requires more care due to:
- Reset tokens grant account access
- User enumeration risks
- Higher value target for attackers

### Testing Security

To verify security measures:
1. Test timing consistency with multiple requests
2. Verify rate limiting works correctly
3. Ensure generic messages for all scenarios
4. Monitor for unusual patterns in logs