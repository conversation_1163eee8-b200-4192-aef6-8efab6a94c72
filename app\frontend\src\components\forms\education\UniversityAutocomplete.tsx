import React, { useState, useRef, useEffect } from 'react';
import { Input } from '../../common/Input';
import { School } from 'lucide-react';

interface UniversityAutocompleteProps {
  value: string;
  onChange: (value: string) => void;
  error?: string;
  placeholder?: string;
  required?: boolean;
}

// Popular Canadian universities for autocomplete
const UNIVERSITIES = [
  // Quebec Universities
  'McGill University',
  'Université de Montréal',
  'Concordia University',
  'Université Laval',
  'Université du Québec à Montréal (UQAM)',
  'École Polytechnique de Montréal',
  'HEC Montréal',
  'Université de Sherbrooke',
  'Bishop\'s University',
  
  // Ontario Universities
  'University of Toronto',
  'University of Ottawa',
  'Queen\'s University',
  'Western University',
  'York University',
  'Ryerson University',
  'Carleton University',
  'University of Waterloo',
  'McMaster University',
  
  // Other Canadian Universities
  'University of British Columbia',
  'University of Alberta',
  'University of Calgary',
  'Simon Fraser University',
  'University of Manitoba',
  'University of Saskatchewan',
  'Dalhousie University',
  'Memorial University of Newfoundland',
  
  // Quebec CEGEPs
  'Dawson College',
  'Vanier College',
  'John <PERSON> College',
  'Marianopolis College',
  'Collège de Maisonneuve',
  'Cégep du Vieux Montréal',
  'Cégep Marie-Victorin',
  'Cégep de Saint-Laurent'
];

export const UniversityAutocomplete: React.FC<UniversityAutocompleteProps> = ({
  value,
  onChange,
  error,
  placeholder = "Type to search universities...",
  required = false
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const wrapperRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Filter universities based on input
  useEffect(() => {
    if (value.length >= 2) {
      const filtered = UNIVERSITIES.filter(uni =>
        uni.toLowerCase().includes(value.toLowerCase())
      );
      setSuggestions(filtered.slice(0, 8)); // Limit to 8 suggestions
      setIsOpen(filtered.length > 0);
    } else {
      setSuggestions([]);
      setIsOpen(false);
    }
    setHighlightedIndex(-1);
  }, [value]);

  // Handle clicks outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (wrapperRef.current && !wrapperRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSelect = (university: string) => {
    onChange(university);
    setIsOpen(false);
    setHighlightedIndex(-1);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setHighlightedIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setHighlightedIndex(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Enter':
        e.preventDefault();
        if (highlightedIndex >= 0 && highlightedIndex < suggestions.length) {
          handleSelect(suggestions[highlightedIndex]);
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setHighlightedIndex(-1);
        break;
    }
  };

  return (
    <div ref={wrapperRef} className="relative">
      <label className="block text-sm font-medium text-gray-700 mb-1">
        University/Institution {required && <span className="text-red-500">*</span>}
      </label>
      <div className="relative">
        <Input
          ref={inputRef}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          error={error}
          icon={School}
          autoComplete="off"
        />
        
        {isOpen && suggestions.length > 0 && (
          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-auto">
            {suggestions.map((university, index) => {
              const isHighlighted = index === highlightedIndex;
              const matchIndex = university.toLowerCase().indexOf(value.toLowerCase());
              const beforeMatch = university.slice(0, matchIndex);
              const match = university.slice(matchIndex, matchIndex + value.length);
              const afterMatch = university.slice(matchIndex + value.length);

              return (
                <button
                  key={university}
                  type="button"
                  className={`w-full px-4 py-2 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none ${
                    isHighlighted ? 'bg-gray-50' : ''
                  }`}
                  onClick={() => handleSelect(university)}
                  onMouseEnter={() => setHighlightedIndex(index)}
                >
                  <span className="text-sm">
                    {beforeMatch}
                    <span className="font-semibold text-red-600">{match}</span>
                    {afterMatch}
                  </span>
                </button>
              );
            })}
          </div>
        )}
      </div>
      
      {value && !UNIVERSITIES.includes(value) && (
        <p className="mt-1 text-xs text-gray-500">
          Custom institution - make sure the name is spelled correctly
        </p>
      )}
    </div>
  );
};