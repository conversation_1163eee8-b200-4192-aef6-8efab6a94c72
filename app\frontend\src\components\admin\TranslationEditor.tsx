import React, { useState, useEffect } from 'react';
import { 
  Edit3, 
  Search, 
  Filter, 
  Save, 
  X, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  CheckCircle,
  <PERSON>py,
  Eye,
  FileText,
  Plus,
  Trash2,
  Globe
} from 'lucide-react';
import { useTranslation } from '../../hooks/useTranslation';

interface TranslationKey {
  key: string;
  category: string;
  en: string;
  fr: string;
  description?: string;
  is_template?: boolean;
  last_modified: string;
  modified_by: string;
}

interface TranslationStats {
  total_keys: number;
  translated_keys: number;
  missing_keys: number;
  categories: string[];
}

export const TranslationEditor: React.FC = () => {
  const { t } = useTranslation();
  
  const [translations, setTranslations] = useState<TranslationKey[]>([]);
  const [filteredTranslations, setFilteredTranslations] = useState<TranslationKey[]>([]);
  const [stats, setStats] = useState<TranslationStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  
  // Filters and search
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showOnlyMissing, setShowOnlyMissing] = useState(false);
  
  // Editing state
  const [editingKey, setEditingKey] = useState<string | null>(null);
  const [editValues, setEditValues] = useState<{en: string; fr: string}>({en: '', fr: ''});
  const [newKeyModal, setNewKeyModal] = useState(false);
  const [newKey, setNewKey] = useState({
    key: '',
    category: '',
    en: '',
    fr: '',
    description: ''
  });

  // Mock data - in real implementation, this would come from API
  useEffect(() => {
    loadTranslations();
  }, []);

  // Filter translations based on search and filters
  useEffect(() => {
    let filtered = translations;

    // Search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(trans => 
        trans.key.toLowerCase().includes(term) ||
        trans.en.toLowerCase().includes(term) ||
        trans.fr.toLowerCase().includes(term) ||
        trans.category.toLowerCase().includes(term)
      );
    }

    // Category filter
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(trans => trans.category === selectedCategory);
    }

    // Missing translations filter
    if (showOnlyMissing) {
      filtered = filtered.filter(trans => !trans.en || !trans.fr);
    }

    setFilteredTranslations(filtered);
  }, [translations, searchTerm, selectedCategory, showOnlyMissing]);

  const loadTranslations = async () => {
    setLoading(true);
    
    // Mock data
    const mockTranslations: TranslationKey[] = [
      {
        key: 'common.loading',
        category: 'common',
        en: 'Loading...',
        fr: 'Chargement...',
        description: 'Loading indicator text',
        last_modified: '2024-01-15T10:30:00Z',
        modified_by: '<EMAIL>'
      },
      {
        key: 'auth.login',
        category: 'auth',
        en: 'Login',
        fr: 'Connexion',
        description: 'Login button text',
        last_modified: '2024-01-14T15:20:00Z',
        modified_by: '<EMAIL>'
      },
      {
        key: 'sidebar.appointments',
        category: 'sidebar',
        en: 'Appointments',
        fr: 'Rendez-vous',
        description: 'Sidebar navigation for appointments',
        last_modified: '2024-01-13T09:15:00Z',
        modified_by: '<EMAIL>'
      },
      {
        key: 'billing.invoice',
        category: 'billing',
        en: 'Invoice',
        fr: 'Facture',
        description: 'Invoice label in billing section',
        last_modified: '2024-01-12T14:45:00Z',
        modified_by: '<EMAIL>'
      },
      {
        key: 'tutors.specializations',
        category: 'tutors',
        en: 'Specializations',
        fr: '', // Missing translation
        description: 'Tutor specializations field label',
        last_modified: '2024-01-10T11:00:00Z',
        modified_by: '<EMAIL>'
      },
      {
        key: 'reports.performance',
        category: 'reports',
        en: 'Performance',
        fr: 'Performance',
        description: 'Performance reports section',
        last_modified: '2024-01-09T16:30:00Z',
        modified_by: '<EMAIL>'
      }
    ];

    const mockStats: TranslationStats = {
      total_keys: mockTranslations.length,
      translated_keys: mockTranslations.filter(t => t.en && t.fr).length,
      missing_keys: mockTranslations.filter(t => !t.en || !t.fr).length,
      categories: Array.from(new Set(mockTranslations.map(t => t.category)))
    };

    setTranslations(mockTranslations);
    setStats(mockStats);
    setLoading(false);
  };

  const handleEdit = (translationKey: TranslationKey) => {
    setEditingKey(translationKey.key);
    setEditValues({
      en: translationKey.en,
      fr: translationKey.fr
    });
  };

  const handleSave = async (key: string) => {
    setSaving(true);
    
    // Mock save operation
    setTimeout(() => {
      setTranslations(prev => prev.map(trans => 
        trans.key === key 
          ? { 
              ...trans, 
              en: editValues.en, 
              fr: editValues.fr,
              last_modified: new Date().toISOString(),
              modified_by: '<EMAIL>'
            }
          : trans
      ));
      setEditingKey(null);
      setSaving(false);
    }, 1000);
  };

  const handleCancel = () => {
    setEditingKey(null);
    setEditValues({en: '', fr: ''});
  };

  const handleAddNew = async () => {
    if (!newKey.key || !newKey.category) return;

    const translation: TranslationKey = {
      key: newKey.key,
      category: newKey.category,
      en: newKey.en,
      fr: newKey.fr,
      description: newKey.description,
      last_modified: new Date().toISOString(),
      modified_by: '<EMAIL>'
    };

    setTranslations(prev => [...prev, translation]);
    setNewKeyModal(false);
    setNewKey({key: '', category: '', en: '', fr: '', description: ''});
  };

  const handleDelete = async (key: string) => {
    if (confirm(`Are you sure you want to delete the translation key "${key}"?`)) {
      setTranslations(prev => prev.filter(trans => trans.key !== key));
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const validateTranslation = (translation: TranslationKey) => {
    const issues: string[] = [];
    
    if (!translation.en) issues.push('Missing English translation');
    if (!translation.fr) issues.push('Missing French translation');
    
    // Check for placeholder consistency
    const enPlaceholders = (translation.en.match(/\{\{[^}]+\}\}/g) || []).sort();
    const frPlaceholders = (translation.fr.match(/\{\{[^}]+\}\}/g) || []).sort();
    
    if (JSON.stringify(enPlaceholders) !== JSON.stringify(frPlaceholders)) {
      issues.push('Placeholder mismatch between languages');
    }

    return issues;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
        <span className="ml-3 text-gray-600">{t('common.loading')}</span>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      {/* Header with Stats */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Edit3 className="w-6 h-6 text-red-600" />
          <h1 className="text-2xl font-bold text-gray-900">Translation Editor</h1>
        </div>
        
        <button
          onClick={() => setNewKeyModal(true)}
          className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg font-medium hover:bg-red-700 transition-colors"
        >
          <Plus className="w-4 h-4" />
          Add Translation
        </button>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center gap-2">
              <FileText className="w-5 h-5 text-red-600" />
              <span className="text-sm font-medium text-gray-600">Total Keys</span>
            </div>
            <div className="text-2xl font-bold text-gray-900 mt-1">{stats.total_keys}</div>
          </div>
          
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="w-5 h-5 text-green-600" />
              <span className="text-sm font-medium text-gray-600">Translated</span>
            </div>
            <div className="text-2xl font-bold text-green-600 mt-1">{stats.translated_keys}</div>
          </div>
          
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center gap-2">
              <AlertTriangle className="w-5 h-5 text-red-600" />
              <span className="text-sm font-medium text-gray-600">Missing</span>
            </div>
            <div className="text-2xl font-bold text-red-600 mt-1">{stats.missing_keys}</div>
          </div>
          
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center gap-2">
              <Globe className="w-5 h-5 text-purple-600" />
              <span className="text-sm font-medium text-gray-600">Categories</span>
            </div>
            <div className="text-2xl font-bold text-purple-600 mt-1">{stats.categories.length}</div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex flex-wrap gap-4 items-end">
          {/* Search */}
          <div className="flex-1 min-w-64">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Search Translations
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search by key, text, or category..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:ring-red-500 focus:border-red-500"
              />
            </div>
          </div>

          {/* Category Filter */}
          <div className="min-w-48">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Category
            </label>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-red-500 focus:border-red-500"
            >
              <option value="all">All Categories</option>
              {stats?.categories.map(category => (
                <option key={category} value={category}>
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </option>
              ))}
            </select>
          </div>

          {/* Missing Filter */}
          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              id="show-missing"
              checked={showOnlyMissing}
              onChange={(e) => setShowOnlyMissing(e.target.checked)}
              className="h-4 w-4 text-red-600 border-gray-300 rounded focus:ring-red-500"
            />
            <label htmlFor="show-missing" className="text-sm font-medium text-gray-700">
              Show only missing
            </label>
          </div>
        </div>
      </div>

      {/* Translation List */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Translation Key
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  English
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Français (Québec)
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {filteredTranslations.map((translation) => {
                const isEditing = editingKey === translation.key;
                const validationIssues = validateTranslation(translation);
                
                return (
                  <tr key={translation.key} className="hover:bg-gray-50">
                    {/* Key Column */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {translation.key}
                        </div>
                        <div className="text-xs text-gray-500">
                          {translation.category}
                        </div>
                        {translation.description && (
                          <div className="text-xs text-gray-400 mt-1">
                            {translation.description}
                          </div>
                        )}
                      </div>
                    </td>

                    {/* English Column */}
                    <td className="px-6 py-4">
                      {isEditing ? (
                        <textarea
                          value={editValues.en}
                          onChange={(e) => setEditValues(prev => ({...prev, en: e.target.value}))}
                          className="w-full p-2 border border-gray-300 rounded text-sm resize-vertical"
                          rows={2}
                        />
                      ) : (
                        <div className="text-sm text-gray-900">
                          {translation.en || (
                            <span className="text-red-500 italic">Missing translation</span>
                          )}
                        </div>
                      )}
                    </td>

                    {/* French Column */}
                    <td className="px-6 py-4">
                      {isEditing ? (
                        <textarea
                          value={editValues.fr}
                          onChange={(e) => setEditValues(prev => ({...prev, fr: e.target.value}))}
                          className="w-full p-2 border border-gray-300 rounded text-sm resize-vertical"
                          rows={2}
                        />
                      ) : (
                        <div className="text-sm text-gray-900">
                          {translation.fr || (
                            <span className="text-red-500 italic">Traduction manquante</span>
                          )}
                        </div>
                      )}
                    </td>

                    {/* Status Column */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      {validationIssues.length === 0 ? (
                        <span className="inline-flex items-center gap-1 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                          <CheckCircle className="w-3 h-3" />
                          Complete
                        </span>
                      ) : (
                        <span className="inline-flex items-center gap-1 px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">
                          <AlertTriangle className="w-3 h-3" />
                          {validationIssues.length} issue{validationIssues.length > 1 ? 's' : ''}
                        </span>
                      )}
                    </td>

                    {/* Actions Column */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      {isEditing ? (
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => handleSave(translation.key)}
                            disabled={saving}
                            className="text-green-600 hover:text-green-800 disabled:opacity-50"
                          >
                            <Save className="w-4 h-4" />
                          </button>
                          <button
                            onClick={handleCancel}
                            className="text-gray-600 hover:text-gray-800"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => handleEdit(translation)}
                            className="text-red-600 hover:text-red-800"
                          >
                            <Edit3 className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => copyToClipboard(translation.key)}
                            className="text-gray-600 hover:text-gray-800"
                          >
                            <Copy className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleDelete(translation.key)}
                            className="text-red-600 hover:text-red-800"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      )}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>

        {filteredTranslations.length === 0 && (
          <div className="text-center py-12">
            <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No translations found</h3>
            <p className="text-gray-600">Try adjusting your search or filter criteria.</p>
          </div>
        )}
      </div>

      {/* Add New Translation Modal */}
      {newKeyModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b">
              <h3 className="text-lg font-semibold">Add New Translation</h3>
              <button
                onClick={() => setNewKeyModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            
            <div className="p-6 space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Translation Key
                  </label>
                  <input
                    type="text"
                    value={newKey.key}
                    onChange={(e) => setNewKey(prev => ({...prev, key: e.target.value}))}
                    placeholder="e.g., common.new_feature"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-red-500 focus:border-red-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Category
                  </label>
                  <select
                    value={newKey.category}
                    onChange={(e) => setNewKey(prev => ({...prev, category: e.target.value}))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-red-500 focus:border-red-500"
                  >
                    <option value="">Select category</option>
                    {stats?.categories.map(category => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                    <option value="new">+ New Category</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <input
                  type="text"
                  value={newKey.description}
                  onChange={(e) => setNewKey(prev => ({...prev, description: e.target.value}))}
                  placeholder="Brief description of this translation"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-red-500 focus:border-red-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  English Text
                </label>
                <textarea
                  value={newKey.en}
                  onChange={(e) => setNewKey(prev => ({...prev, en: e.target.value}))}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-red-500 focus:border-red-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  French Text (Québec)
                </label>
                <textarea
                  value={newKey.fr}
                  onChange={(e) => setNewKey(prev => ({...prev, fr: e.target.value}))}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-red-500 focus:border-red-500"
                />
              </div>
            </div>

            <div className="flex items-center justify-end gap-3 p-6 border-t bg-gray-50">
              <button
                onClick={() => setNewKeyModal(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 font-medium"
              >
                Cancel
              </button>
              <button
                onClick={handleAddNew}
                disabled={!newKey.key || !newKey.category}
                className="px-4 py-2 bg-red-600 text-white rounded-lg font-medium hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Add Translation
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
