/* mobile.css - Mobile-specific responsive styles */

/* Mobile Navigation */
@media (max-width: 768px) {
  /* Hamburger Menu */
  .mobile-menu-button {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    width: 28px;
    height: 24px;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 0;
  }

  .mobile-menu-button span {
    width: 28px;
    height: 3px;
    background-color: var(--color-primary);
    border-radius: 2px;
    transition: all 0.3s ease;
  }

  .mobile-menu-button.active span:nth-child(1) {
    transform: rotate(45deg) translate(7px, 7px);
  }

  .mobile-menu-button.active span:nth-child(2) {
    opacity: 0;
  }

  .mobile-menu-button.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -7px);
  }

  /* Mobile Sidebar */
  .sidebar {
    position: fixed;
    left: -100%;
    top: 0;
    width: 280px;
    height: 100vh;
    background: var(--color-background-primary);
    box-shadow: var(--shadow-elevated);
    transition: left 0.3s ease;
    z-index: 1000;
  }

  .sidebar.open {
    left: 0;
  }

  .sidebar-overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.5);
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    z-index: 999;
  }

  .sidebar-overlay.active {
    opacity: 1;
    pointer-events: auto;
  }

  /* Mobile Header */
  .header {
    padding: 12px 16px;
    position: sticky;
    top: 0;
    z-index: 100;
    background: var(--color-background-primary);
    border-bottom: 1px solid var(--color-border-primary);
  }

  /* Mobile Search */
  .search-input {
    font-size: 16px; /* Prevents zoom on iOS */
  }

  .global-search {
    width: 100%;
    margin-top: 12px;
  }

  /* Mobile Tabs */
  .tab-navigation {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
  }

  .tab-navigation::-webkit-scrollbar {
    display: none;
  }

  .tab-button {
    white-space: nowrap;
    padding: 12px 20px;
  }

  /* Mobile Cards */
  .card {
    border-radius: var(--radius-medium);
    margin: 0 -16px;
    border-left: none;
    border-right: none;
  }

  .stat-grid {
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }

  /* Mobile Forms */
  .form-section {
    padding: 16px;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  /* Mobile Buttons */
  .button-group {
    flex-direction: column;
    width: 100%;
  }

  .button-group .btn {
    width: 100%;
    justify-content: center;
  }

  /* Mobile Modals */
  .modal {
    margin: 16px;
    max-height: calc(100vh - 32px);
    width: calc(100% - 32px);
  }

  .modal-content {
    max-height: calc(100vh - 64px);
    overflow-y: auto;
  }

  /* Mobile Tables */
  .data-table {
    display: block;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .table-wrapper {
    min-width: 600px;
  }

  /* Mobile List Items */
  .list-item {
    padding: 16px;
    border-radius: 0;
    border-left: none;
    border-right: none;
    margin: 0 -16px;
  }

  /* Mobile Calendar */
  .calendar-day-view {
    flex-direction: column;
  }

  .calendar-tutor-column {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid var(--color-border-primary);
  }

  /* Mobile Dashboard */
  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .quick-actions {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  /* Mobile Dropdowns */
  .dropdown-menu {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    top: auto;
    transform: translateY(100%);
    border-radius: var(--radius-large) var(--radius-large) 0 0;
    max-height: 70vh;
    animation: slideUp 0.3s ease-out forwards;
  }

  @keyframes slideUp {
    to {
      transform: translateY(0);
    }
  }

  /* Mobile Typography */
  h1 {
    font-size: 24px;
  }

  h2 {
    font-size: 20px;
  }

  h3 {
    font-size: 18px;
  }

  /* Mobile Spacing */
  .page-container {
    padding: 16px;
  }

  .section-spacing {
    margin-bottom: 24px;
  }

  /* Mobile Floating Action Button */
  .fab {
    position: fixed;
    bottom: 24px;
    right: 24px;
    width: 56px;
    height: 56px;
    border-radius: 28px;
    background: var(--color-accent-red);
    color: white;
    box-shadow: var(--shadow-elevated);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
  }

  /* Mobile Bottom Navigation */
  .bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--color-background-primary);
    border-top: 1px solid var(--color-border-primary);
    display: flex;
    justify-content: space-around;
    padding: 8px 0;
    z-index: 100;
  }

  .bottom-nav-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: 8px;
    color: var(--color-text-secondary);
    transition: color 0.2s ease;
  }

  .bottom-nav-item.active {
    color: var(--color-accent-red);
  }

  .bottom-nav-item svg {
    width: 24px;
    height: 24px;
  }

  .bottom-nav-item span {
    font-size: 12px;
  }
}

/* Tablet Specific */
@media (min-width: 769px) and (max-width: 1024px) {
  .sidebar {
    width: 240px;
  }

  .main-content {
    margin-left: 240px;
  }

  .stat-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .form-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Touch-specific styles */
@media (hover: none) and (pointer: coarse) {
  /* Larger touch targets */
  .btn,
  .input,
  .select,
  .checkbox,
  .radio {
    min-height: 44px;
  }

  /* Remove hover effects on touch devices */
  .card:hover,
  .list-item:hover {
    transform: none;
    box-shadow: var(--shadow-soft);
  }

  /* Improved scrolling */
  * {
    -webkit-tap-highlight-color: transparent;
  }

  /* Disable text selection on interactive elements */
  .btn,
  .tab-button,
  .nav-item {
    user-select: none;
    -webkit-user-select: none;
  }
}

/* Landscape Mode Adjustments */
@media (max-width: 768px) and (orientation: landscape) {
  .header {
    padding: 8px 16px;
  }

  .modal {
    margin: 8px;
    max-height: calc(100vh - 16px);
  }

  .fab {
    bottom: 16px;
    right: 16px;
  }
}

/* Small Phone Adjustments */
@media (max-width: 380px) {
  .btn {
    padding: 10px 16px;
    font-size: 14px;
  }

  .card {
    padding: 12px;
  }

  .stat-grid {
    grid-template-columns: 1fr;
  }

  .quick-actions {
    grid-template-columns: 1fr;
  }
}

/* Safe Area Insets for Modern Phones */
@supports (padding: env(safe-area-inset-left)) {
  .sidebar {
    padding-left: env(safe-area-inset-left);
  }

  .bottom-nav {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .fab {
    bottom: calc(24px + env(safe-area-inset-bottom));
  }
}

/* Print Styles */
@media print {
  .sidebar,
  .header,
  .bottom-nav,
  .fab,
  .mobile-menu-button {
    display: none !important;
  }

  .main-content {
    margin: 0 !important;
  }

  .card {
    break-inside: avoid;
    page-break-inside: avoid;
  }
}