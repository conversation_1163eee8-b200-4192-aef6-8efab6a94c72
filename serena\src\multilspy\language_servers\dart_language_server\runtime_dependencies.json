{"_description": "Used to download the runtime dependencies for running Dart Language Server, downloaded from https://dart.dev/get-dart/archive", "runtimeDependencies": [{"id": "DartLanguageServer", "description": "Dart Language Server for Linux (x64)", "url": "https://storage.googleapis.com/dart-archive/channels/stable/release/3.7.1/sdk/dartsdk-linux-x64-release.zip", "platformId": "linux-x64", "archiveType": "zip", "binaryName": "dart-sdk/bin/dart"}, {"id": "DartLanguageServer", "description": "Dart Language Server for Windows (x64)", "url": "https://storage.googleapis.com/dart-archive/channels/stable/release/3.7.1/sdk/dartsdk-windows-x64-release.zip", "platformId": "win-x64", "archiveType": "zip", "binaryName": "dart-sdk/bin/dart.exe"}, {"id": "DartLanguageServer", "description": "Dart Language Server for Windows (arm64)", "url": "https://storage.googleapis.com/dart-archive/channels/stable/release/3.7.1/sdk/dartsdk-windows-arm64-release.zip", "platformId": "win-arm64", "archiveType": "zip", "binaryName": "dart-sdk/bin/dart.exe"}, {"id": "DartLanguageServer", "description": "Dart Language Server for macOS (x64)", "url": "https://storage.googleapis.com/dart-archive/channels/stable/release/3.7.1/sdk/dartsdk-macos-x64-release.zip", "platformId": "osx-x64", "archiveType": "zip", "binaryName": "dart-sdk/bin/dart"}, {"id": "DartLanguageServer", "description": "Dart Language Server for macOS (arm64)", "url": "https://storage.googleapis.com/dart-archive/channels/stable/release/3.7.1/sdk/dartsdk-macos-arm64-release.zip", "platformId": "osx-arm64", "archiveType": "zip", "binaryName": "dart-sdk/bin/dart"}]}