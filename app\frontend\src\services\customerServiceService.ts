import api from './api';
import { UserRoleType } from '../types/auth';

// Customer Service Types and Interfaces
export enum TicketStatus {
  OPEN = 'open',
  IN_PROGRESS = 'in_progress',
  WAITING_CUSTOMER = 'waiting_customer',
  WAITING_INTERNAL = 'waiting_internal',
  RESOLVED = 'resolved',
  CLOSED = 'closed',
  CANCELLED = 'cancelled'
}

export enum TicketPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent'
}

export enum TicketCategory {
  BILLING = 'billing',
  APPOINTMENT = 'appointment',
  TECHNICAL = 'technical',
  TUTOR_COMPLAINT = 'tutor_complaint',
  SERVICE_QUALITY = 'service_quality',
  ACCOUNT = 'account',
  GENERAL = 'general',
  FEEDBACK = 'feedback'
}

export enum TicketChannel {
  EMAIL = 'email',
  SMS = 'sms',
  IN_APP = 'in_app',
  PHONE = 'phone',
  CHAT = 'chat'
}

export interface SupportTicket {
  ticket_id: number;
  ticket_number: string;
  subject: string;
  description: string;
  status: TicketStatus;
  priority: TicketPriority;
  category: TicketCategory;
  channel: TicketChannel;
  requester: {
    user_id: number;
    name: string;
    email: string;
    phone?: string;
    role: UserRoleType;
  };
  assigned_to?: {
    user_id: number;
    name: string;
  };
  created_at: string;
  updated_at: string;
  resolved_at?: string;
  closed_at?: string;
  first_response_at?: string;
  tags: string[];
  attachments?: Array<{
    id: number;
    name: string;
    url: string;
    size: number;
    type: string;
  }>;
  related_entities?: {
    appointment_id?: number;
    invoice_id?: number;
    tutor_id?: number;
    client_id?: number;
  };
  satisfaction_rating?: number;
  resolution_notes?: string;
}

export interface TicketMessage {
  message_id: number;
  ticket_id: number;
  sender: {
    user_id: number;
    name: string;
    role: UserRoleType;
    is_staff: boolean;
  };
  message: string;
  created_at: string;
  is_internal_note: boolean;
  attachments?: Array<{
    id: number;
    name: string;
    url: string;
    size: number;
  }>;
  status_change?: {
    from: TicketStatus;
    to: TicketStatus;
  };
}

export interface CreateTicketRequest {
  subject: string;
  description: string;
  category: TicketCategory;
  priority?: TicketPriority;
  channel?: TicketChannel;
  requester_id?: number;
  requester_email?: string;
  requester_phone?: string;
  tags?: string[];
  attachments?: File[];
  related_entities?: {
    appointment_id?: number;
    invoice_id?: number;
    tutor_id?: number;
    client_id?: number;
  };
}

export interface UpdateTicketRequest {
  status?: TicketStatus;
  priority?: TicketPriority;
  category?: TicketCategory;
  assigned_to?: number;
  tags?: string[];
  resolution_notes?: string;
}

export interface TicketReplyRequest {
  message: string;
  is_internal_note?: boolean;
  status_change?: TicketStatus;
  attachments?: File[];
}

export interface TicketListParams {
  status?: TicketStatus | TicketStatus[];
  priority?: TicketPriority | TicketPriority[];
  category?: TicketCategory | TicketCategory[];
  assigned_to?: number;
  requester_id?: number;
  tags?: string[];
  search?: string;
  date_from?: string;
  date_to?: string;
  limit?: number;
  offset?: number;
  sort_by?: 'created' | 'updated' | 'priority';
  sort_order?: 'asc' | 'desc';
}

export interface TicketStats {
  total_tickets: number;
  open_tickets: number;
  overdue_tickets: number;
  avg_resolution_time_hours: number;
  avg_first_response_time_minutes: number;
  satisfaction_rating: number;
  by_status: Record<TicketStatus, number>;
  by_priority: Record<TicketPriority, number>;
  by_category: Record<TicketCategory, number>;
  recent_activity: Array<{
    date: string;
    created: number;
    resolved: number;
    closed: number;
  }>;
  agent_performance: Array<{
    agent_id: number;
    agent_name: string;
    tickets_resolved: number;
    avg_resolution_time_hours: number;
    satisfaction_rating: number;
  }>;
}

export interface ResponseTemplate {
  template_id: number;
  name: string;
  subject?: string;
  message: string;
  category: TicketCategory;
  language: 'en' | 'fr';
  variables: string[];
  usage_count: number;
  is_active: boolean;
}

export interface CustomerServiceSettings {
  auto_assignment_enabled: boolean;
  business_hours: {
    timezone: string;
    schedule: Array<{
      day: string;
      start_time: string;
      end_time: string;
      is_open: boolean;
    }>;
  };
  sla_settings: {
    first_response_minutes: Record<TicketPriority, number>;
    resolution_hours: Record<TicketPriority, number>;
  };
  auto_responses: {
    new_ticket: boolean;
    first_response: boolean;
    resolution: boolean;
  };
  escalation_rules: Array<{
    condition: string;
    action: string;
    notify: string[];
  }>;
}

class CustomerServiceService {
  /**
   * Get tickets list
   */
  async getTickets(params?: TicketListParams): Promise<{
    tickets: SupportTicket[];
    total: number;
    limit: number;
    offset: number;
  }> {
    try {
      const response = await api.get<{
        tickets: SupportTicket[];
        total: number;
        limit: number;
        offset: number;
      }>('/customer-service/tickets', { params });
      return response.data;
    } catch (error: any) {
      console.error('Error fetching tickets:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch tickets');
    }
  }

  /**
   * Get specific ticket
   */
  async getTicket(ticketId: number): Promise<SupportTicket> {
    try {
      const response = await api.get<SupportTicket>(`/customer-service/tickets/${ticketId}`);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching ticket:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch ticket');
    }
  }

  /**
   * Create new ticket
   */
  async createTicket(request: CreateTicketRequest): Promise<SupportTicket> {
    try {
      const formData = new FormData();
      
      // Add text fields
      Object.entries(request).forEach(([key, value]) => {
        if (key !== 'attachments' && value !== undefined) {
          if (typeof value === 'object') {
            formData.append(key, JSON.stringify(value));
          } else {
            formData.append(key, String(value));
          }
        }
      });

      // Add attachments
      if (request.attachments) {
        request.attachments.forEach((file, index) => {
          formData.append(`attachments`, file);
        });
      }

      const response = await api.post<SupportTicket>('/customer-service/tickets', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });
      return response.data;
    } catch (error: any) {
      console.error('Error creating ticket:', error);
      throw new Error(error.response?.data?.detail || 'Failed to create ticket');
    }
  }

  /**
   * Update ticket
   */
  async updateTicket(ticketId: number, request: UpdateTicketRequest): Promise<SupportTicket> {
    try {
      const response = await api.patch<SupportTicket>(
        `/customer-service/tickets/${ticketId}`,
        request
      );
      return response.data;
    } catch (error: any) {
      console.error('Error updating ticket:', error);
      throw new Error(error.response?.data?.detail || 'Failed to update ticket');
    }
  }

  /**
   * Get ticket messages
   */
  async getTicketMessages(ticketId: number): Promise<TicketMessage[]> {
    try {
      const response = await api.get<TicketMessage[]>(
        `/customer-service/tickets/${ticketId}/messages`
      );
      return response.data;
    } catch (error: any) {
      console.error('Error fetching messages:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch messages');
    }
  }

  /**
   * Reply to ticket
   */
  async replyToTicket(ticketId: number, request: TicketReplyRequest): Promise<TicketMessage> {
    try {
      const formData = new FormData();
      
      // Add text fields
      formData.append('message', request.message);
      if (request.is_internal_note !== undefined) {
        formData.append('is_internal_note', String(request.is_internal_note));
      }
      if (request.status_change) {
        formData.append('status_change', request.status_change);
      }

      // Add attachments
      if (request.attachments) {
        request.attachments.forEach((file) => {
          formData.append('attachments', file);
        });
      }

      const response = await api.post<TicketMessage>(
        `/customer-service/tickets/${ticketId}/reply`,
        formData,
        { headers: { 'Content-Type': 'multipart/form-data' } }
      );
      return response.data;
    } catch (error: any) {
      console.error('Error replying to ticket:', error);
      throw new Error(error.response?.data?.detail || 'Failed to reply to ticket');
    }
  }

  /**
   * Assign ticket to agent
   */
  async assignTicket(ticketId: number, userId: number): Promise<SupportTicket> {
    try {
      const response = await api.post<SupportTicket>(
        `/customer-service/tickets/${ticketId}/assign`,
        { user_id: userId }
      );
      return response.data;
    } catch (error: any) {
      console.error('Error assigning ticket:', error);
      throw new Error(error.response?.data?.detail || 'Failed to assign ticket');
    }
  }

  /**
   * Add tags to ticket
   */
  async addTags(ticketId: number, tags: string[]): Promise<SupportTicket> {
    try {
      const response = await api.post<SupportTicket>(
        `/customer-service/tickets/${ticketId}/tags`,
        { tags }
      );
      return response.data;
    } catch (error: any) {
      console.error('Error adding tags:', error);
      throw new Error(error.response?.data?.detail || 'Failed to add tags');
    }
  }

  /**
   * Rate ticket resolution
   */
  async rateTicket(ticketId: number, rating: number, feedback?: string): Promise<{
    message: string;
  }> {
    try {
      const response = await api.post<{ message: string }>(
        `/customer-service/tickets/${ticketId}/rate`,
        { rating, feedback }
      );
      return response.data;
    } catch (error: any) {
      console.error('Error rating ticket:', error);
      throw new Error(error.response?.data?.detail || 'Failed to rate ticket');
    }
  }

  /**
   * Get ticket statistics
   */
  async getStats(dateFrom?: string, dateTo?: string): Promise<TicketStats> {
    try {
      const params = { date_from: dateFrom, date_to: dateTo };
      const response = await api.get<TicketStats>('/customer-service/stats', { params });
      return response.data;
    } catch (error: any) {
      console.error('Error fetching stats:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch stats');
    }
  }

  /**
   * Get response templates
   */
  async getTemplates(category?: TicketCategory, language?: 'en' | 'fr'): Promise<ResponseTemplate[]> {
    try {
      const params = { category, language };
      const response = await api.get<ResponseTemplate[]>('/customer-service/templates', { params });
      return response.data;
    } catch (error: any) {
      console.error('Error fetching templates:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch templates');
    }
  }

  /**
   * Create response template
   */
  async createTemplate(template: Omit<ResponseTemplate, 'template_id' | 'usage_count'>): Promise<ResponseTemplate> {
    try {
      const response = await api.post<ResponseTemplate>('/customer-service/templates', template);
      return response.data;
    } catch (error: any) {
      console.error('Error creating template:', error);
      throw new Error(error.response?.data?.detail || 'Failed to create template');
    }
  }

  /**
   * Get service settings
   */
  async getSettings(): Promise<CustomerServiceSettings> {
    try {
      const response = await api.get<CustomerServiceSettings>('/customer-service/settings');
      return response.data;
    } catch (error: any) {
      console.error('Error fetching settings:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch settings');
    }
  }

  /**
   * Update service settings
   */
  async updateSettings(settings: Partial<CustomerServiceSettings>): Promise<CustomerServiceSettings> {
    try {
      const response = await api.put<CustomerServiceSettings>('/customer-service/settings', settings);
      return response.data;
    } catch (error: any) {
      console.error('Error updating settings:', error);
      throw new Error(error.response?.data?.detail || 'Failed to update settings');
    }
  }

  /**
   * Search tickets
   */
  async searchTickets(query: string, params?: {
    category?: TicketCategory;
    status?: TicketStatus;
    limit?: number;
  }): Promise<SupportTicket[]> {
    try {
      const response = await api.get<SupportTicket[]>('/customer-service/search', {
        params: { query, ...params }
      });
      return response.data;
    } catch (error: any) {
      console.error('Error searching tickets:', error);
      throw new Error(error.response?.data?.detail || 'Failed to search tickets');
    }
  }

  /**
   * Export tickets
   */
  async exportTickets(params: TicketListParams & { format: 'csv' | 'pdf' }): Promise<Blob> {
    try {
      const response = await api.get('/customer-service/export', {
        params,
        responseType: 'blob'
      });
      return response.data;
    } catch (error: any) {
      console.error('Error exporting tickets:', error);
      throw new Error(error.response?.data?.detail || 'Failed to export tickets');
    }
  }

  /**
   * Calculate SLA status
   */
  calculateSLAStatus(ticket: SupportTicket, slaSettings: CustomerServiceSettings['sla_settings']): {
    first_response: {
      status: 'on_time' | 'overdue' | 'pending';
      minutes_remaining?: number;
    };
    resolution: {
      status: 'on_time' | 'overdue' | 'pending';
      hours_remaining?: number;
    };
  } {
    const now = new Date();
    const created = new Date(ticket.created_at);
    
    // First response SLA
    const firstResponseSLA = slaSettings.first_response_minutes[ticket.priority];
    let firstResponseStatus: 'on_time' | 'overdue' | 'pending' = 'pending';
    let firstResponseRemaining: number | undefined;

    if (ticket.first_response_at) {
      const responseTime = new Date(ticket.first_response_at);
      const minutesElapsed = (responseTime.getTime() - created.getTime()) / 60000;
      firstResponseStatus = minutesElapsed <= firstResponseSLA ? 'on_time' : 'overdue';
    } else {
      const minutesElapsed = (now.getTime() - created.getTime()) / 60000;
      firstResponseRemaining = Math.max(0, firstResponseSLA - minutesElapsed);
      firstResponseStatus = firstResponseRemaining > 0 ? 'pending' : 'overdue';
    }

    // Resolution SLA
    const resolutionSLA = slaSettings.resolution_hours[ticket.priority];
    let resolutionStatus: 'on_time' | 'overdue' | 'pending' = 'pending';
    let resolutionRemaining: number | undefined;

    if (ticket.resolved_at) {
      const resolvedTime = new Date(ticket.resolved_at);
      const hoursElapsed = (resolvedTime.getTime() - created.getTime()) / 3600000;
      resolutionStatus = hoursElapsed <= resolutionSLA ? 'on_time' : 'overdue';
    } else if (ticket.status !== TicketStatus.RESOLVED && ticket.status !== TicketStatus.CLOSED) {
      const hoursElapsed = (now.getTime() - created.getTime()) / 3600000;
      resolutionRemaining = Math.max(0, resolutionSLA - hoursElapsed);
      resolutionStatus = resolutionRemaining > 0 ? 'pending' : 'overdue';
    }

    return {
      first_response: {
        status: firstResponseStatus,
        minutes_remaining: firstResponseRemaining
      },
      resolution: {
        status: resolutionStatus,
        hours_remaining: resolutionRemaining
      }
    };
  }

  /**
   * Format ticket number
   */
  formatTicketNumber(ticketNumber: string): string {
    return `#${ticketNumber}`;
  }

  /**
   * Get priority color
   */
  getPriorityColor(priority: TicketPriority): string {
    const colors: Record<TicketPriority, string> = {
      [TicketPriority.LOW]: 'gray',
      [TicketPriority.MEDIUM]: 'blue',
      [TicketPriority.HIGH]: 'orange',
      [TicketPriority.URGENT]: 'red'
    };

    return colors[priority] || 'gray';
  }

  /**
   * Get status color
   */
  getStatusColor(status: TicketStatus): string {
    const colors: Record<TicketStatus, string> = {
      [TicketStatus.OPEN]: 'blue',
      [TicketStatus.IN_PROGRESS]: 'yellow',
      [TicketStatus.WAITING_CUSTOMER]: 'orange',
      [TicketStatus.WAITING_INTERNAL]: 'purple',
      [TicketStatus.RESOLVED]: 'green',
      [TicketStatus.CLOSED]: 'gray',
      [TicketStatus.CANCELLED]: 'red'
    };

    return colors[status] || 'gray';
  }

  /**
   * Get category icon
   */
  getCategoryIcon(category: TicketCategory): string {
    const icons: Record<TicketCategory, string> = {
      [TicketCategory.BILLING]: 'DollarSign',
      [TicketCategory.APPOINTMENT]: 'Calendar',
      [TicketCategory.TECHNICAL]: 'Wrench',
      [TicketCategory.TUTOR_COMPLAINT]: 'AlertTriangle',
      [TicketCategory.SERVICE_QUALITY]: 'Star',
      [TicketCategory.ACCOUNT]: 'User',
      [TicketCategory.GENERAL]: 'MessageCircle',
      [TicketCategory.FEEDBACK]: 'ThumbsUp'
    };

    return icons[category] || 'HelpCircle';
  }
}

export const customerServiceService = new CustomerServiceService();