-- Add timestamp columns to user_consents table
-- Version: 006
-- Description: Add created_at, updated_at, and deleted_at to user_consents
-- Author: TutorAide Development Team
-- Date: 2025-06-10

-- Add missing columns to user_consents
ALTER TABLE user_consents
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE;

-- Create trigger for user_consents
DROP TRIGGER IF EXISTS update_user_consents_updated_at ON user_consents;
CREATE TRIGGER update_user_consents_updated_at 
BEFORE UPDATE ON user_consents 
FOR EACH ROW 
EXECUTE FUNCTION update_updated_at_column();

-- Update existing records to set created_at and updated_at from accepted_at
UPDATE user_consents 
SET created_at = accepted_at, 
    updated_at = accepted_at 
WHERE created_at = CURRENT_TIMESTAMP;