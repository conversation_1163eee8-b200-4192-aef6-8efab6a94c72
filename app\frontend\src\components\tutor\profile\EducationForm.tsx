import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Input } from '../../common/Input';
import Button from '../../common/Button';
import { Select } from '../../common/Select';
import { 
  GraduationCap,
  Award,
  Calendar,
  School,
  Plus,
  Trash2,
  Save
} from 'lucide-react';

interface EducationFormProps {
  profile: {
    highest_degree_level: string | null;
    highest_degree_name: string | null;
    degree_major: string | null;
    university_name: string | null;
    graduation_year: number | null;
    gpa: number | null;
    teaching_certifications: string[];
    additional_education?: any[];
  };
  onSave: (data: any) => Promise<void>;
}

interface AdditionalEducation {
  degree: string;
  major: string;
  university: string;
  year: number;
}

export const EducationForm: React.FC<EducationFormProps> = ({
  profile,
  onSave
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    highest_degree_level: profile.highest_degree_level || '',
    highest_degree_name: profile.highest_degree_name || '',
    degree_major: profile.degree_major || '',
    university_name: profile.university_name || '',
    graduation_year: profile.graduation_year?.toString() || '',
    gpa: profile.gpa?.toString() || '',
    teaching_certifications: profile.teaching_certifications || [],
    additional_education: profile.additional_education || []
  });

  const [newCertification, setNewCertification] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  const degreeOptions = [
    { value: 'high_school', label: t('tutor.education.degrees.highSchool') },
    { value: 'associate', label: t('tutor.education.degrees.associate') },
    { value: 'bachelor', label: t('tutor.education.degrees.bachelor') },
    { value: 'master', label: t('tutor.education.degrees.master') },
    { value: 'phd', label: t('tutor.education.degrees.phd') },
    { value: 'professional', label: t('tutor.education.degrees.professional') },
    { value: 'other', label: t('tutor.education.degrees.other') }
  ];

  const currentYear = new Date().getFullYear();
  const yearOptions = Array.from({ length: 50 }, (_, i) => ({
    value: (currentYear - i).toString(),
    label: (currentYear - i).toString()
  }));

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.highest_degree_level) {
      newErrors.highest_degree_level = t('tutor.education.errors.degreeRequired');
    }
    
    if (!formData.highest_degree_name.trim()) {
      newErrors.highest_degree_name = t('tutor.education.errors.degreeNameRequired');
    }
    
    if (!formData.degree_major.trim()) {
      newErrors.degree_major = t('tutor.education.errors.majorRequired');
    }
    
    if (!formData.university_name.trim()) {
      newErrors.university_name = t('tutor.education.errors.universityRequired');
    }
    
    if (!formData.graduation_year) {
      newErrors.graduation_year = t('tutor.education.errors.yearRequired');
    }
    
    if (formData.gpa) {
      const gpaValue = parseFloat(formData.gpa);
      if (isNaN(gpaValue) || gpaValue < 0 || gpaValue > 4.0) {
        newErrors.gpa = t('tutor.education.errors.invalidGPA');
      }
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setLoading(true);
    try {
      await onSave({
        ...formData,
        graduation_year: parseInt(formData.graduation_year),
        gpa: formData.gpa ? parseFloat(formData.gpa) : null
      });
    } catch (error) {
      // Error is handled in parent component
    } finally {
      setLoading(false);
    }
  };

  const addCertification = () => {
    if (newCertification.trim()) {
      setFormData({
        ...formData,
        teaching_certifications: [...formData.teaching_certifications, newCertification.trim()]
      });
      setNewCertification('');
    }
  };

  const removeCertification = (index: number) => {
    setFormData({
      ...formData,
      teaching_certifications: formData.teaching_certifications.filter((_, i) => i !== index)
    });
  };

  const addAdditionalEducation = () => {
    setFormData({
      ...formData,
      additional_education: [...formData.additional_education, {
        degree: '',
        major: '',
        university: '',
        year: currentYear
      }]
    });
  };

  const updateAdditionalEducation = (index: number, field: keyof AdditionalEducation, value: string | number) => {
    const updated = [...formData.additional_education];
    updated[index] = { ...updated[index], [field]: value };
    setFormData({ ...formData, additional_education: updated });
  };

  const removeAdditionalEducation = (index: number) => {
    setFormData({
      ...formData,
      additional_education: formData.additional_education.filter((_, i) => i !== index)
    });
  };

  return (
    <form onSubmit={handleSubmit} className="p-6 space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-text-primary mb-4">
          {t('tutor.education.title')}
        </h3>
        
        {/* Primary Education */}
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <h4 className="text-sm font-medium text-text-primary mb-4">
            {t('tutor.education.primaryDegree')}
          </h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                {t('tutor.education.fields.degreeLevel')}
              </label>
              <Select
                value={formData.highest_degree_level}
                onChange={(value) => setFormData({ ...formData, highest_degree_level: value })}
                options={degreeOptions}
                placeholder={t('tutor.education.placeholders.selectDegree')}
                leftIcon={<GraduationCap className="w-4 h-4" />}
                error={errors.highest_degree_level}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                {t('tutor.education.fields.degreeName')}
              </label>
              <Input
                value={formData.highest_degree_name}
                onChange={(e) => setFormData({ ...formData, highest_degree_name: e.target.value })}
                placeholder={t('tutor.education.placeholders.degreeName')}
                error={errors.highest_degree_name}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                {t('tutor.education.fields.major')}
              </label>
              <Input
                value={formData.degree_major}
                onChange={(e) => setFormData({ ...formData, degree_major: e.target.value })}
                placeholder={t('tutor.education.placeholders.major')}
                error={errors.degree_major}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                {t('tutor.education.fields.university')}
              </label>
              <Input
                leftIcon={<School className="w-4 h-4" />}
                value={formData.university_name}
                onChange={(e) => setFormData({ ...formData, university_name: e.target.value })}
                placeholder={t('tutor.education.placeholders.university')}
                error={errors.university_name}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                {t('tutor.education.fields.graduationYear')}
              </label>
              <Select
                value={formData.graduation_year}
                onChange={(value) => setFormData({ ...formData, graduation_year: value })}
                options={yearOptions}
                placeholder={t('tutor.education.placeholders.selectYear')}
                leftIcon={<Calendar className="w-4 h-4" />}
                error={errors.graduation_year}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                {t('tutor.education.fields.gpa')} <span className="text-text-secondary">{t('common.optional')}</span>
              </label>
              <Input
                type="number"
                step="0.01"
                min="0"
                max="4.0"
                value={formData.gpa}
                onChange={(e) => setFormData({ ...formData, gpa: e.target.value })}
                placeholder="3.75"
                error={errors.gpa}
              />
            </div>
          </div>
        </div>

        {/* Additional Education */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-sm font-medium text-text-primary">
              {t('tutor.education.additionalDegrees')}
            </h4>
            <Button
              type="button"
              variant="secondary"
              size="sm"
              onClick={addAdditionalEducation}
              leftIcon={<Plus className="w-4 h-4" />}
            >
              {t('tutor.education.addDegree')}
            </Button>
          </div>
          
          {formData.additional_education.map((edu, index) => (
            <div key={index} className="bg-gray-50 rounded-lg p-4 mb-3">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-3 items-end">
                <Input
                  value={edu.degree}
                  onChange={(e) => updateAdditionalEducation(index, 'degree', e.target.value)}
                  placeholder={t('tutor.education.placeholders.degree')}
                />
                <Input
                  value={edu.major}
                  onChange={(e) => updateAdditionalEducation(index, 'major', e.target.value)}
                  placeholder={t('tutor.education.placeholders.major')}
                />
                <Input
                  value={edu.university}
                  onChange={(e) => updateAdditionalEducation(index, 'university', e.target.value)}
                  placeholder={t('tutor.education.placeholders.university')}
                />
                <div className="flex gap-2">
                  <Select
                    value={edu.year.toString()}
                    onChange={(value) => updateAdditionalEducation(index, 'year', parseInt(value))}
                    options={yearOptions}
                    placeholder={t('tutor.education.placeholders.year')}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeAdditionalEducation(index)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Teaching Certifications */}
        <div>
          <h4 className="text-sm font-medium text-text-primary mb-4">
            {t('tutor.education.certifications')}
          </h4>
          
          <div className="flex gap-2 mb-3">
            <Input
              leftIcon={<Award className="w-4 h-4" />}
              value={newCertification}
              onChange={(e) => setNewCertification(e.target.value)}
              placeholder={t('tutor.education.placeholders.certification')}
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  addCertification();
                }
              }}
            />
            <Button
              type="button"
              variant="secondary"
              onClick={addCertification}
            >
              {t('common.add')}
            </Button>
          </div>
          
          <div className="space-y-2">
            {formData.teaching_certifications.map((cert, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="text-sm text-text-primary">{cert}</span>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeCertification(index)}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-end pt-4 border-t border-gray-200">
        <Button
          type="submit"
          loading={loading}
          leftIcon={<Save className="w-4 h-4" />}
        >
          {t('common.save')}
        </Button>
      </div>
    </form>
  );
};