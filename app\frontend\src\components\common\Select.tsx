import React from 'react';
import { clsx } from 'clsx';

interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

interface SelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'children'> {
  label?: string;
  error?: string;
  hint?: string;
  options: SelectOption[];
  placeholder?: string;
  fullWidth?: boolean;
}

export const Select: React.FC<SelectProps> = ({
  label,
  error,
  hint,
  options = [],
  placeholder = 'Select an option',
  fullWidth = true,
  className,
  id,
  value,
  ...props
}) => {
  const selectId = id || label?.toLowerCase().replace(/\s+/g, '-');
  
  // Add defensive check
  const safeOptions = options || [];
  
  const baseStyles = `
    w-full px-4 py-3 pr-10 border rounded-lg bg-background-secondary text-text-primary 
    transition-all duration-200 focus:outline-none focus:border-accent-red focus:bg-white 
    focus:shadow-focus appearance-none cursor-pointer
    bg-[url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3e%3c/path%3e%3c/svg%3e")]
    bg-[length:20px_20px] bg-[right_12px_center] bg-no-repeat
  `;

  return (
    <div className={clsx(fullWidth ? 'w-full' : 'inline-block', className)}>
      {label && (
        <label
          htmlFor={selectId}
          className="block text-sm font-medium text-text-primary mb-2"
        >
          {label}
        </label>
      )}
      
      <div className="relative">
        <select
          id={selectId}
          value={value}
          className={clsx(
            baseStyles,
            error ? 'border-semantic-error focus:border-semantic-error focus:shadow-error' : 'border-border-primary hover:border-border-secondary',
            props.disabled && 'opacity-60 cursor-not-allowed bg-background-tertiary',
            !value && 'text-text-muted'
          )}
          aria-invalid={Boolean(error)}
          aria-describedby={error ? `${selectId}-error` : hint ? `${selectId}-hint` : undefined}
          {...props}
        >
          <option value="" disabled>
            {placeholder}
          </option>
          {safeOptions.map((option) => (
            <option
              key={option.value}
              value={option.value}
              disabled={option.disabled}
            >
              {option.label}
            </option>
          ))}
        </select>
      </div>
      
      {error && (
        <p id={`${selectId}-error`} className="mt-2 text-sm text-semantic-error">
          {error}
        </p>
      )}
      
      {hint && !error && (
        <p id={`${selectId}-hint`} className="mt-2 text-sm text-text-muted">
          {hint}
        </p>
      )}
    </div>
  );
};

interface MultiSelectProps {
  label?: string;
  error?: string;
  hint?: string;
  options: SelectOption[];
  value: string[];
  onChange: (values: string[]) => void;
  placeholder?: string;
  fullWidth?: boolean;
  className?: string;
  disabled?: boolean;
}

export const MultiSelect: React.FC<MultiSelectProps> = ({
  label,
  error,
  hint,
  options,
  value = [],
  onChange,
  placeholder = 'Select options',
  fullWidth = true,
  className,
  disabled,
}) => {
  const [isOpen, setIsOpen] = React.useState(false);
  const containerRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const toggleOption = (optionValue: string) => {
    if (value.includes(optionValue)) {
      onChange(value.filter(v => v !== optionValue));
    } else {
      onChange([...value, optionValue]);
    }
  };

  const selectedLabels = options
    .filter(opt => value.includes(opt.value))
    .map(opt => opt.label);

  return (
    <div ref={containerRef} className={clsx(fullWidth ? 'w-full' : 'inline-block', 'relative', className)}>
      {label && (
        <label className="block text-sm font-medium text-text-primary mb-2">
          {label}
        </label>
      )}
      
      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        className={clsx(
          'w-full px-4 py-3 pr-10 border rounded-lg bg-background-secondary text-left',
          'transition-all duration-200 focus:outline-none focus:border-accent-red focus:bg-white focus:shadow-focus',
          error ? 'border-semantic-error' : 'border-border-primary hover:border-border-secondary',
          disabled && 'opacity-60 cursor-not-allowed bg-background-tertiary',
          !selectedLabels.length && 'text-text-muted'
        )}
        disabled={disabled}
      >
        <span className="block truncate">
          {selectedLabels.length > 0 ? selectedLabels.join(', ') : placeholder}
        </span>
        <span className="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
          <svg className="w-5 h-5 text-text-muted" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </span>
      </button>

      {isOpen && (
        <div className="absolute z-10 mt-1 w-full bg-white border border-border-primary rounded-lg shadow-soft overflow-hidden">
          <div className="max-h-60 overflow-y-auto">
            {options.map((option) => (
              <label
                key={option.value}
                className={clsx(
                  'flex items-center px-4 py-2 hover:bg-background-secondary cursor-pointer',
                  option.disabled && 'opacity-50 cursor-not-allowed'
                )}
              >
                <input
                  type="checkbox"
                  className="mr-3 rounded border-border-primary text-accent-red focus:ring-accent-red"
                  checked={value.includes(option.value)}
                  onChange={() => !option.disabled && toggleOption(option.value)}
                  disabled={option.disabled}
                />
                <span className="text-text-primary">{option.label}</span>
              </label>
            ))}
          </div>
        </div>
      )}
      
      {error && (
        <p className="mt-2 text-sm text-semantic-error">
          {error}
        </p>
      )}
      
      {hint && !error && (
        <p className="mt-2 text-sm text-text-muted">
          {hint}
        </p>
      )}
    </div>
  );
};

export default Select;