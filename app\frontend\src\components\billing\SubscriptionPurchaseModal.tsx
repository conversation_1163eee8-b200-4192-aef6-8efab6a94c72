import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Package, CreditCard, Check, AlertCircle } from 'lucide-react';
import api from '../../services/api';
import { Modal } from '../common/Modal';
import Button from '../common/Button';
import { Card } from '../common/Card';
import { Badge } from '../common/Badge';
import LoadingSpinner from '../ui/LoadingSpinner';
import { useAuth } from '../../contexts/AuthContext';
import toast from 'react-hot-toast';

interface SubscriptionPackage {
  package_id: number;
  name: string;
  description?: string;
  hours_included: number;
  price: number;
  billing_frequency: 'one_time' | 'monthly' | 'quarterly' | 'annual';
  is_active: boolean;
  subject_restrictions?: string[];
}

interface SubscriptionPurchaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  onPurchaseComplete: () => void;
}

const SubscriptionPurchaseModal: React.FC<SubscriptionPurchaseModalProps> = ({
  isOpen,
  onClose,
  onPurchaseComplete
}) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [packages, setPackages] = useState<SubscriptionPackage[]>([]);
  const [selectedPackage, setSelectedPackage] = useState<SubscriptionPackage | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isPurchasing, setIsPurchasing] = useState(false);
  const [step, setStep] = useState<'select' | 'payment'>('select');

  useEffect(() => {
    if (isOpen) {
      fetchPackages();
    }
  }, [isOpen]);

  const fetchPackages = async () => {
    try {
      setIsLoading(true);
      const response = await api.get<SubscriptionPackage[]>('/subscriptions/packages');
      setPackages(response.data);
    } catch (error) {
      console.error('Error fetching packages:', error);
      toast.error(t('billing.errors.fetchPackages'));
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD'
    }).format(amount);
  };

  const getBillingFrequencyLabel = (frequency: string) => {
    const labels = {
      one_time: t('billing.subscriptions.oneTime'),
      monthly: t('billing.subscriptions.monthly'),
      quarterly: t('billing.subscriptions.quarterly'),
      annual: t('billing.subscriptions.annual')
    };
    return labels[frequency] || frequency;
  };

  const handleSelectPackage = (pkg: SubscriptionPackage) => {
    setSelectedPackage(pkg);
    setStep('payment');
  };

  const handlePurchase = async () => {
    if (!selectedPackage || !user) return;

    try {
      setIsPurchasing(true);
      
      // For now, just simulate purchase - integrate with Stripe later
      await api.post(`/packages/${selectedPackage.package_id}/purchase`, {
        client_id: user.userId,
        payment_method: 'stripe',
        notes: ''
      });

      toast.success(t('billing.subscriptions.purchaseSuccess'));
      onPurchaseComplete();
      onClose();
    } catch (error) {
      console.error('Error purchasing package:', error);
      toast.error(t('billing.errors.paymentFailed'));
    } finally {
      setIsPurchasing(false);
    }
  };

  const handleClose = () => {
    setStep('select');
    setSelectedPackage(null);
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={
        step === 'select' 
          ? t('billing.subscriptions.selectPackage')
          : t('billing.subscriptions.completePurchase')
      }
      size="xl"
    >
      <div className="p-6">
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <LoadingSpinner size="lg" />
          </div>
        ) : step === 'select' ? (
          <div className="space-y-4">
            {packages.map((pkg) => (
              <Card
                key={pkg.package_id}
                className={`p-4 cursor-pointer hover:shadow-md transition-shadow ${
                  selectedPackage?.package_id === pkg.package_id ? 'ring-2 ring-accent-red' : ''
                }`}
                onClick={() => handleSelectPackage(pkg)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center">
                      <h3 className="text-lg font-semibold text-gray-900">{pkg.name}</h3>
                      {pkg.billing_frequency === 'monthly' && (
                        <Badge variant="info" size="sm" className="ml-2">
                          {t('billing.subscriptions.popular')}
                        </Badge>
                      )}
                    </div>
                    {pkg.description && (
                      <p className="text-sm text-gray-600 mt-1">{pkg.description}</p>
                    )}
                    <div className="flex items-center space-x-4 mt-2">
                      <span className="text-sm text-gray-500">
                        {t('billing.subscriptions.validFor')} {getBillingFrequencyLabel(pkg.billing_frequency)}
                      </span>
                      <span className="text-sm text-gray-500">
                        {pkg.hours_included} {t('billing.subscriptions.hoursIncluded')}
                      </span>
                    </div>
                    {pkg.subject_restrictions && pkg.subject_restrictions.length > 0 && (
                      <p className="text-xs text-yellow-600 mt-2">
                        {t('billing.subscriptions.subjectRestrictions')}: {pkg.subject_restrictions.join(', ')}
                      </p>
                    )}
                  </div>
                  <div className="text-right ml-4">
                    <p className="text-2xl font-bold text-accent-red">
                      {formatCurrency(pkg.price)}
                    </p>
                    <p className="text-sm text-gray-500">
                      {formatCurrency(pkg.price / pkg.hours_included)}/hr
                    </p>
                  </div>
                </div>
              </Card>
            ))}

            {/* Benefits */}
            <div className="mt-6 bg-red-50 rounded-lg p-4">
              <h4 className="font-medium text-red-900 mb-2">
                {t('billing.subscriptions.benefits')}
              </h4>
              <ul className="space-y-1">
                <li className="flex items-start">
                  <Check className="w-4 h-4 text-red-600 mr-2 mt-0.5 flex-shrink-0" />
                  <span className="text-sm text-red-800">
                    {t('billing.subscriptions.benefit1')}
                  </span>
                </li>
                <li className="flex items-start">
                  <Check className="w-4 h-4 text-red-600 mr-2 mt-0.5 flex-shrink-0" />
                  <span className="text-sm text-red-800">
                    {t('billing.subscriptions.benefit2')}
                  </span>
                </li>
                <li className="flex items-start">
                  <Check className="w-4 h-4 text-red-600 mr-2 mt-0.5 flex-shrink-0" />
                  <span className="text-sm text-red-800">
                    {t('billing.subscriptions.benefit3')}
                  </span>
                </li>
              </ul>
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Selected Package Summary */}
            {selectedPackage && (
              <Card className="p-4 bg-gray-50">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-semibold text-gray-900">{selectedPackage.name}</h4>
                    <p className="text-sm text-gray-600">
                      {selectedPackage.hours_included} {t('billing.subscriptions.hoursIncluded')} • {' '}
                      {getBillingFrequencyLabel(selectedPackage.billing_frequency)}
                    </p>
                  </div>
                  <p className="text-xl font-bold text-gray-900">
                    {formatCurrency(selectedPackage.price)}
                  </p>
                </div>
              </Card>
            )}

            {/* Payment Method Placeholder */}
            <div className="space-y-4">
              <h4 className="font-medium text-gray-900">{t('billing.subscriptions.payment')}</h4>
              <Card className="p-4">
                <div className="flex items-center">
                  <CreditCard className="w-8 h-8 text-gray-400 mr-3" />
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">
                      Credit Card Payment
                    </p>
                    <p className="text-xs text-gray-500">
                      Secure payment via Stripe
                    </p>
                  </div>
                </div>
              </Card>
              
              <div className="bg-yellow-50 rounded-lg p-4">
                <div className="flex items-start">
                  <AlertCircle className="w-5 h-5 text-yellow-600 mr-2 mt-0.5 flex-shrink-0" />
                  <p className="text-sm text-yellow-800">
                    Payment integration is coming soon. For now, contact support to complete your purchase.
                  </p>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-between pt-4 border-t">
              <Button
                variant="secondary"
                onClick={() => setStep('select')}
              >
                {t('common.previous')}
              </Button>
              <Button
                variant="primary"
                leftIcon={<CreditCard className="w-4 h-4" />}
                onClick={handlePurchase}
                isLoading={isPurchasing}
                disabled={isPurchasing}
              >
                {t('billing.subscriptions.proceedToPayment')}
              </Button>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default SubscriptionPurchaseModal;
