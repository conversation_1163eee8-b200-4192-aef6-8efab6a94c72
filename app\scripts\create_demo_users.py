#!/usr/bin/env python3
"""
<PERSON>ript to create demo users for TutorAide application.
Creates manager, tutor, and client users with appropriate roles.
"""

import asyncio
import logging
import sys
from pathlib import Path
import json

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from app.config.database import get_db_connection
from app.core.security import get_password_hash
from app.core.timezone import now_est

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def create_demo_users():
    """Create demo users with appropriate roles using direct SQL."""
    
    # Demo users data
    demo_users = [
        {
            "email": "<EMAIL>",
            "password": "password123",
            "role": "manager",
            "first_name": "<PERSON>",
            "last_name": "Quan"
        },
        {
            "email": "<EMAIL>",
            "password": "password123",
            "role": "tutor",
            "first_name": "<PERSON><PERSON><PERSON>",
            "last_name": "<PERSON><PERSON>"
        },
        {
            "email": "<EMAIL>",
            "password": "password123",
            "role": "client",
            "first_name": "Keng",
            "last_name": "Shing"
        }
    ]
    
    async with get_db_connection() as conn:
        for user_data in demo_users:
            try:
                # Check if user already exists
                existing_user = await conn.fetchrow(
                    "SELECT user_id FROM user_accounts WHERE email = $1 AND deleted_at IS NULL",
                    user_data["email"].lower().strip()
                )
                
                if existing_user:
                    logger.info(f"User {user_data['email']} already exists, skipping...")
                    continue
                
                # Create user with role in a transaction
                async with conn.transaction():
                    # Create user account using direct SQL
                    now = now_est()
                    user_record = await conn.fetchrow("""
                        INSERT INTO user_accounts (
                            email, password_hash, is_email_verified, 
                            created_at, updated_at
                        ) VALUES ($1, $2, $3, $4, $5)
                        RETURNING user_id
                    """, 
                        user_data["email"].lower().strip(),
                        get_password_hash(user_data["password"]),
                        True,  # Demo users are pre-verified
                        now,
                        now
                    )
                    
                    user_id = user_record['user_id']
                    
                    # Add role
                    await conn.execute("""
                        INSERT INTO user_roles (user_id, role_type, is_active, created_at)
                        VALUES ($1, $2, true, $3)
                        ON CONFLICT (user_id, role_type) 
                        DO UPDATE SET is_active = true, created_at = $3
                    """, user_id, user_data["role"], now)
                    
                    # Skip consents for now - table structure seems different
                    # We'll add consents manually later if needed
                    
                    # Skip profile creation for now - table structure different
                    # Users can complete profiles through the app
                    
                    logger.info(f"Successfully created demo user: {user_data['email']} with role {user_data['role']}")
                    
            except Exception as e:
                logger.error(f"Error creating user {user_data['email']}: {e}")
                continue
    
    logger.info("Demo user creation complete!")


async def main():
    """Main entry point."""
    try:
        await create_demo_users()
    except Exception as e:
        logger.error(f"Failed to create demo users: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())