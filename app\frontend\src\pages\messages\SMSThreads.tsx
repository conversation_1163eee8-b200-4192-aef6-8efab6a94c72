import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { format, parseISO } from 'date-fns';
import { 
  Search, Send, Phone, User, Calendar, 
  CheckCheck, Check, Clock, Filter, MessageSquare
} from 'lucide-react';
import api from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import toast from 'react-hot-toast';

interface Conversation {
  conversation_id: number;
  conversation_type: string;
  client_id: number | null;
  tutor_id: number | null;
  manager_id: number | null;
  phone_number: string | null;
  subject: string | null;
  status: string;
  last_message_at: string | null;
  unread_count: number;
  created_at: string;
  participant_name: string;
  participant_role: string;
  last_message_content: string | null;
  last_message_sender_type: string | null;
  last_message_created_at: string | null;
}

interface Message {
  message_id: number;
  sender_type: string;
  sender_id: number;
  message_type: string;
  content: string;
  role_indicator: string | null;
  is_read: boolean;
  read_at: string | null;
  delivery_status: string;
  delivered_at: string | null;
  created_at: string;
  edited_at: string | null;
  sender_name: string;
  is_outgoing: boolean;
}

const SMSThreads: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [messageText, setMessageText] = useState('');
  const [filterRole, setFilterRole] = useState<'all' | 'client' | 'tutor'>('all');
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingMessages, setIsLoadingMessages] = useState(false);
  const [isSending, setIsSending] = useState(false);

  useEffect(() => {
    fetchConversations();
  }, []);

  useEffect(() => {
    if (selectedConversation) {
      fetchMessages(selectedConversation.conversation_id);
    }
  }, [selectedConversation]);

  const fetchConversations = async () => {
    try {
      setIsLoading(true);
      const response = await api.get<{
        conversations: Conversation[];
        total: number;
      }>('/messaging/conversations?conversation_type=sms');
      
      setConversations(response.data.conversations);
      if (response.data.conversations.length > 0 && !selectedConversation) {
        setSelectedConversation(response.data.conversations[0]);
      }
    } catch (error) {
      console.error('Error fetching conversations:', error);
      toast.error(t('messages.errors.fetchConversations'));
    } finally {
      setIsLoading(false);
    }
  };

  const fetchMessages = async (conversationId: number) => {
    try {
      setIsLoadingMessages(true);
      const response = await api.get<{
        messages: Message[];
      }>(`/messaging/conversations/${conversationId}/messages`);
      
      setMessages(response.data.messages);
    } catch (error) {
      console.error('Error fetching messages:', error);
      toast.error(t('messages.errors.fetchMessages'));
    } finally {
      setIsLoadingMessages(false);
    }
  };

  const sendMessage = async () => {
    if (!messageText.trim() || !selectedConversation) return;

    try {
      setIsSending(true);
      const response = await api.post<{ message: Message }>(
        `/messaging/conversations/${selectedConversation.conversation_id}/messages`,
        {
          content: messageText,
          message_type: 'text'
        }
      );

      // Add the new message to the list
      setMessages([...messages, response.data.message]);
      setMessageText('');

      // Update conversation's last message
      const updatedConversations = conversations.map(conv => {
        if (conv.conversation_id === selectedConversation.conversation_id) {
          return {
            ...conv,
            last_message_content: messageText,
            last_message_sender_type: user?.active_role || 'manager',
            last_message_created_at: new Date().toISOString(),
            last_message_at: new Date().toISOString()
          };
        }
        return conv;
      });
      setConversations(updatedConversations);

    } catch (error) {
      console.error('Error sending message:', error);
      toast.error(t('messages.errors.sendMessage'));
    } finally {
      setIsSending(false);
    }
  };

  const filteredConversations = conversations.filter(conv => {
    const matchesSearch = conv.participant_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         (conv.phone_number && conv.phone_number.includes(searchQuery));
    const matchesRole = filterRole === 'all' || conv.participant_role === filterRole;
    return matchesSearch && matchesRole;
  });

  const getRoleIcon = (role: string) => {
    return role === 'tutor' ? '👨‍🏫' : '👤';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'read':
        return <CheckCheck className="w-4 h-4 text-accent-red" />;
      case 'delivered':
        return <CheckCheck className="w-4 h-4 text-text-secondary" />;
      case 'sent':
        return <Check className="w-4 h-4 text-text-secondary" />;
      default:
        return <Clock className="w-4 h-4 text-text-secondary" />;
    }
  };

  const formatTime = (dateString: string | null) => {
    if (!dateString) return '';
    try {
      const date = parseISO(dateString);
      const now = new Date();
      const isToday = date.toDateString() === now.toDateString();
      return isToday ? format(date, 'HH:mm') : format(date, 'MMM d');
    } catch {
      return '';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="h-full flex">
      {/* Thread List */}
      <div className="w-1/3 bg-white border-r border-primary-200 flex flex-col">
        {/* Search and Filter */}
        <div className="p-4 border-b border-primary-200">
          <div className="relative mb-3">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-text-secondary" />
            <input
              type="text"
              placeholder={t('messages.searchConversations')}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-background-secondary rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
            />
          </div>
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4 text-text-secondary" />
            <select
              value={filterRole}
              onChange={(e) => setFilterRole(e.target.value as any)}
              className="flex-1 px-3 py-1 text-sm border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
            >
              <option value="all">{t('common.all')}</option>
              <option value="client">{t('sidebar.clients')}</option>
              <option value="tutor">{t('sidebar.tutors')}</option>
            </select>
          </div>
        </div>

        {/* Thread List */}
        <div className="flex-1 overflow-y-auto">
          {filteredConversations.length === 0 ? (
            <div className="p-4 text-center text-text-secondary">
              {t('messages.noConversations')}
            </div>
          ) : (
            filteredConversations.map(conversation => (
              <div
                key={conversation.conversation_id}
                onClick={() => setSelectedConversation(conversation)}
                className={`
                  p-4 border-b border-primary-100 cursor-pointer transition-colors
                  ${selectedConversation?.conversation_id === conversation.conversation_id ? 'bg-accent-red bg-opacity-5' : 'hover:bg-background-secondary'}
                `}
              >
                <div className="flex items-start gap-3">
                  <div className="w-10 h-10 bg-accent-red bg-opacity-10 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-lg">{getRoleIcon(conversation.participant_role)}</span>
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <h3 className="font-medium text-text-primary truncate">
                        {conversation.participant_name}
                      </h3>
                      <span className="text-xs text-text-secondary">
                        {formatTime(conversation.last_message_at || conversation.created_at)}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <p className="text-sm text-text-secondary truncate pr-2">
                        {conversation.last_message_sender_type === user?.active_role && 'You: '}
                        {conversation.last_message_content || t('messages.noMessages')}
                      </p>
                      <div className="flex items-center gap-1">
                        {conversation.unread_count > 0 && (
                          <span className="bg-accent-red text-white text-xs rounded-full px-2 py-0.5">
                            {conversation.unread_count}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Conversation View */}
      {selectedConversation ? (
        <div className="flex-1 flex flex-col bg-background-secondary">
          {/* Header */}
          <div className="bg-white p-4 border-b border-primary-200 shadow-sm">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-accent-red bg-opacity-10 rounded-full flex items-center justify-center">
                  <span className="text-lg">{getRoleIcon(selectedConversation.participant_role)}</span>
                </div>
                <div>
                  <h2 className="font-semibold text-text-primary">
                    {selectedConversation.participant_name}
                  </h2>
                  <div className="flex items-center gap-2 text-sm text-text-secondary">
                    {selectedConversation.phone_number && (
                      <>
                        <Phone className="w-3 h-3" />
                        {selectedConversation.phone_number}
                      </>
                    )}
                    <span className="text-xs bg-primary-200 px-2 py-0.5 rounded-full capitalize">
                      {selectedConversation.participant_role}
                    </span>
                  </div>
                </div>
              </div>
              <button className="p-2 hover:bg-background-secondary rounded-medium transition-colors">
                <Phone className="w-5 h-5 text-text-secondary" />
              </button>
            </div>
          </div>

          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {isLoadingMessages ? (
              <div className="flex items-center justify-center h-full">
                <LoadingSpinner />
              </div>
            ) : messages.length === 0 ? (
              <div className="text-center text-text-secondary">
                {t('messages.startConversation')}
              </div>
            ) : (
              messages.map(message => (
                <div
                  key={message.message_id}
                  className={`flex ${message.is_outgoing ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`
                      max-w-[70%] px-4 py-2 rounded-large
                      ${message.is_outgoing 
                        ? 'bg-accent-red text-white' 
                        : 'bg-white text-text-primary shadow-sm'
                      }
                    `}
                  >
                    {message.role_indicator && (
                      <p className={`text-xs mb-1 ${message.is_outgoing ? 'text-red-100' : 'text-text-secondary'}`}>
                        {message.role_indicator}
                      </p>
                    )}
                    <p className="text-sm">{message.content}</p>
                    <div className={`flex items-center gap-1 mt-1 ${message.is_outgoing ? 'justify-end' : ''}`}>
                      <span className={`text-xs ${message.is_outgoing ? 'text-red-100' : 'text-text-secondary'}`}>
                        {format(parseISO(message.created_at), 'HH:mm')}
                      </span>
                      {message.is_outgoing && (
                        <span className="text-red-100">
                          {getStatusIcon(message.delivery_status)}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Message Input */}
          <div className="bg-white p-4 border-t border-primary-200">
            <div className="flex items-end gap-3">
              <textarea
                value={messageText}
                onChange={(e) => setMessageText(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                  }
                }}
                placeholder={t('messages.typeMessage')}
                className="flex-1 px-4 py-2 bg-background-secondary rounded-soft resize-none focus:outline-none focus:ring-2 focus:ring-accent-red"
                rows={1}
                disabled={isSending}
              />
              <button
                onClick={sendMessage}
                disabled={!messageText.trim() || isSending}
                className={`
                  p-3 rounded-soft transition-colors
                  ${messageText.trim() && !isSending
                    ? 'bg-accent-red text-white hover:bg-accent-red-dark'
                    : 'bg-primary-200 text-text-secondary cursor-not-allowed'
                  }
                `}
              >
                {isSending ? <LoadingSpinner size="sm" /> : <Send className="w-5 h-5" />}
              </button>
            </div>
            <p className="text-xs text-text-secondary mt-2">
              {t('messages.smsCharges')}
            </p>
          </div>
        </div>
      ) : (
        <div className="flex-1 flex items-center justify-center bg-background-secondary">
          <div className="text-center">
            <MessageSquare className="w-12 h-12 text-text-secondary mx-auto mb-3" />
            <p className="text-text-secondary">{t('messages.selectConversation')}</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default SMSThreads;