import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { BookOpen, Plus, Search, Filter, DollarSign, Users, Clock, MapPin, Monitor, Building, Library, Shuffle } from 'lucide-react';
import { Card } from '../../components/common/Card';
import Button from '../../components/common/Button';
import { Input } from '../../components/common/Input';
import { Badge } from '../../components/common/Badge';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import { EmptyState } from '../../components/common/EmptyState';
import { serviceService, Service, ServiceType, SubjectArea, ServicePackage } from '../../services/serviceService';
import { useAuth } from '../../contexts/AuthContext';
import { UserRoleType } from '../../types/auth';
import { hasPermission } from '../../utils/permissions';
import toast from 'react-hot-toast';
import ServiceDetailsModal from '../../components/modals/ServiceDetailsModal';
import AddServiceModal from '../../components/modals/AddServiceModal';
import ManagePackagesModal from '../../components/modals/ManagePackagesModal';

const ServiceCatalog: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [services, setServices] = useState<Service[]>([]);
  const [packages, setPackages] = useState<ServicePackage[]>([]);
  const [selectedService, setSelectedService] = useState<Service | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showPackagesModal, setShowPackagesModal] = useState(false);
  const [activeTab, setActiveTab] = useState<'services' | 'packages'>('services');
  const [filters, setFilters] = useState({
    search: '',
    subject_area: 'all',
    service_type: 'all',
    grade_level: ''
  });
  const [showFilters, setShowFilters] = useState(false);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);

  // Permissions
  const isManager = user?.activeRole === UserRoleType.MANAGER;
  const canManageServices = hasPermission(user?.activeRole, 'manageServices');

  useEffect(() => {
    loadData();
  }, [activeTab, page, filters]);

  const loadData = async () => {
    setLoading(true);
    try {
      if (activeTab === 'services') {
        const params = {
          search: filters.search || undefined,
          subject_area: filters.subject_area === 'all' ? undefined : filters.subject_area as SubjectArea,
          service_type: filters.service_type === 'all' ? undefined : filters.service_type as ServiceType,
          grade_level: filters.grade_level || undefined,
          page,
          limit: 12
        };
        const response = await serviceService.getServices(params);
        setServices(response.items);
        setTotalItems(response.total);
        setTotalPages(response.total_pages);
      } else {
        const packagesData = await serviceService.getServicePackages();
        setPackages(packagesData);
        setTotalItems(packagesData.length);
        setTotalPages(1);
      }
    } catch (error) {
      console.error('Error loading data:', error);
      toast.error(t('services.loadError'));
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value }));
    setPage(1);
  };

  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPage(1);
  };

  const handleViewDetails = (service: Service) => {
    setSelectedService(service);
    setShowDetailsModal(true);
  };

  const handleAddComplete = () => {
    setShowAddModal(false);
    loadData();
    toast.success(t('services.addSuccess'));
  };

  const getServiceTypeIcon = (type: ServiceType) => {
    switch (type) {
      case ServiceType.ONLINE:
        return <Monitor className="w-5 h-5" />;
      case ServiceType.IN_PERSON:
        return <Building className="w-5 h-5" />;
      case ServiceType.LIBRARY:
        return <Library className="w-5 h-5" />;
      case ServiceType.HYBRID:
        return <Shuffle className="w-5 h-5" />;
      default:
        return null;
    }
  };

  const getServiceTypeLabel = (type: ServiceType) => {
    switch (type) {
      case ServiceType.ONLINE:
        return t('services.types.online');
      case ServiceType.IN_PERSON:
        return t('services.types.inPerson');
      case ServiceType.LIBRARY:
        return t('services.types.library');
      case ServiceType.HYBRID:
        return t('services.types.hybrid');
      default:
        return type;
    }
  };

  const getSubjectAreaLabel = (area: SubjectArea) => {
    switch (area) {
      case SubjectArea.MATH:
        return t('services.subjects.math');
      case SubjectArea.SCIENCE:
        return t('services.subjects.science');
      case SubjectArea.FRENCH:
        return t('services.subjects.french');
      case SubjectArea.ENGLISH:
        return t('services.subjects.english');
      case SubjectArea.OTHERS:
        return t('services.subjects.others');
      default:
        return area;
    }
  };

  const getSubjectAreaColor = (area: SubjectArea) => {
    switch (area) {
      case SubjectArea.MATH:
        return 'bg-blue-100 text-blue-800';
      case SubjectArea.SCIENCE:
        return 'bg-green-100 text-green-800';
      case SubjectArea.FRENCH:
        return 'bg-purple-100 text-purple-800';
      case SubjectArea.ENGLISH:
        return 'bg-orange-100 text-orange-800';
      case SubjectArea.OTHERS:
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const renderServiceCard = (service: Service) => (
    <Card key={service.service_id} className="hover:shadow-md transition-shadow cursor-pointer" onClick={() => handleViewDetails(service)}>
      <div className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{service.name}</h3>
            <div className="flex items-center space-x-2 mt-1">
              <Badge className={getSubjectAreaColor(service.subject_area)} size="sm">
                {getSubjectAreaLabel(service.subject_area)}
              </Badge>
              <div className="flex items-center text-gray-600">
                {getServiceTypeIcon(service.service_type)}
                <span className="text-sm ml-1">{getServiceTypeLabel(service.service_type)}</span>
              </div>
            </div>
          </div>
          <Badge variant={service.is_active ? 'success' : 'secondary'} size="sm">
            {service.is_active ? t('common.active') : t('common.inactive')}
          </Badge>
        </div>

        {service.description && (
          <p className="text-sm text-gray-600 mb-4 line-clamp-2">{service.description}</p>
        )}

        <div className="space-y-2">
          {/* Duration Options */}
          <div className="flex items-center text-sm text-gray-600">
            <Clock className="w-4 h-4 mr-2" />
            <span>{t('services.duration')}: {service.duration_options.map(d => `${d}min`).join(', ')}</span>
          </div>

          {/* Student Capacity */}
          <div className="flex items-center text-sm text-gray-600">
            <Users className="w-4 h-4 mr-2" />
            <span>
              {service.min_students === service.max_students
                ? t('services.students', { count: service.max_students })
                : t('services.studentsRange', { min: service.min_students, max: service.max_students })}
            </span>
          </div>

          {/* Location Requirement */}
          {service.requires_location && (
            <div className="flex items-center text-sm text-gray-600">
              <MapPin className="w-4 h-4 mr-2" />
              <span>{t('services.locationRequired')}</span>
            </div>
          )}

          {/* Default Rates */}
          {service.default_client_rate && (
            <div className="flex items-center justify-between pt-2 border-t">
              <span className="text-sm text-gray-600">{t('services.defaultRate')}</span>
              <span className="font-semibold">${service.default_client_rate}/hr</span>
            </div>
          )}
        </div>

        {/* Tags */}
        {service.tags && service.tags.length > 0 && (
          <div className="mt-4 flex flex-wrap gap-2">
            {service.tags.slice(0, 3).map((tag, idx) => (
              <Badge key={idx} variant="secondary" size="sm">{tag}</Badge>
            ))}
            {service.tags.length > 3 && (
              <Badge variant="secondary" size="sm">+{service.tags.length - 3}</Badge>
            )}
          </div>
        )}
      </div>
    </Card>
  );

  const renderPackageCard = (pkg: ServicePackage) => (
    <Card key={pkg.package_id} className="hover:shadow-md transition-shadow">
      <div className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{pkg.name}</h3>
            <p className="text-sm text-gray-600">{pkg.service_name}</p>
          </div>
          <div className="text-right">
            {pkg.is_tecfee && (
              <Badge variant="warning" size="sm" className="mb-1">TECFEE</Badge>
            )}
            <Badge variant={pkg.is_active ? 'success' : 'secondary'} size="sm">
              {pkg.is_active ? t('common.active') : t('common.inactive')}
            </Badge>
          </div>
        </div>

        {pkg.description && (
          <p className="text-sm text-gray-600 mb-4">{pkg.description}</p>
        )}

        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">{t('services.sessions')}</span>
            <span className="font-semibold">{pkg.session_count} sessions</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">{t('services.totalHours')}</span>
            <span className="font-semibold">{pkg.total_hours} hrs</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">{t('services.validity')}</span>
            <span className="font-semibold">{pkg.valid_days} days</span>
          </div>
        </div>

        <div className="mt-4 pt-4 border-t">
          <div className="flex items-center justify-between">
            <div>
              <span className="text-2xl font-bold text-gray-900">${pkg.price}</span>
              {pkg.discount_percentage && (
                <Badge variant="success" size="sm" className="ml-2">
                  {pkg.discount_percentage}% {t('common.discount')}
                </Badge>
              )}
            </div>
            <Button variant="primary" size="sm">
              {t('common.viewDetails')}
            </Button>
          </div>
        </div>

        {pkg.features && pkg.features.length > 0 && (
          <div className="mt-4 space-y-1">
            {pkg.features.slice(0, 3).map((feature, idx) => (
              <div key={idx} className="flex items-start text-sm text-gray-600">
                <span className="text-green-500 mr-1">✓</span>
                {feature}
              </div>
            ))}
          </div>
        )}
      </div>
    </Card>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">{t('services.title')}</h1>
        <p className="text-gray-600">{t('services.subtitle')}</p>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => {
              setActiveTab('services');
              setPage(1);
            }}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'services'
                ? 'border-accent-red text-accent-red'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            {t('services.tabs.services')}
            <Badge variant="secondary" size="sm" className="ml-2">
              {totalItems}
            </Badge>
          </button>
          <button
            onClick={() => {
              setActiveTab('packages');
              setPage(1);
            }}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'packages'
                ? 'border-accent-red text-accent-red'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            {t('services.tabs.packages')}
            <Badge variant="secondary" size="sm" className="ml-2">
              {packages.length}
            </Badge>
          </button>
        </nav>
      </div>

      {/* Actions Bar */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <div className="flex items-center gap-4 w-full sm:w-auto">
          {/* Search */}
          <div className="relative flex-1 sm:flex-initial">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <Input
              type="text"
              placeholder={t('services.search.placeholder')}
              value={filters.search}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10 pr-4 w-full sm:w-80"
            />
          </div>

          {/* Filters */}
          {activeTab === 'services' && (
            <Button
              variant="secondary"
              onClick={() => setShowFilters(!showFilters)}
              className="relative"
            >
              <Filter className="w-4 h-4 mr-2" />
              {t('common.filters')}
              {(filters.subject_area !== 'all' || filters.service_type !== 'all' || filters.grade_level) && (
                <div className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></div>
              )}
            </Button>
          )}
        </div>

        {/* Action Buttons */}
        {canManageServices && (
          <div className="flex items-center space-x-3">
            {activeTab === 'packages' && (
              <Button variant="secondary" onClick={() => setShowPackagesModal(true)}>
                <Package className="w-4 h-4 mr-2" />
                {t('services.managePackages')}
              </Button>
            )}
            <Button variant="primary" onClick={() => setShowAddModal(true)}>
              <Plus className="w-4 h-4 mr-2" />
              {t(`services.${activeTab === 'services' ? 'addService' : 'addPackage'}`)}
            </Button>
          </div>
        )}
      </div>

      {/* Filters Panel */}
      {showFilters && activeTab === 'services' && (
        <Card className="mb-6 animate-fadeInDown">
          <div className="p-4">
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('services.filters.subjectArea')}
                </label>
                <select
                  value={filters.subject_area}
                  onChange={(e) => handleFilterChange('subject_area', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-red"
                >
                  <option value="all">{t('common.all')}</option>
                  <option value={SubjectArea.MATH}>{t('services.subjects.math')}</option>
                  <option value={SubjectArea.SCIENCE}>{t('services.subjects.science')}</option>
                  <option value={SubjectArea.FRENCH}>{t('services.subjects.french')}</option>
                  <option value={SubjectArea.ENGLISH}>{t('services.subjects.english')}</option>
                  <option value={SubjectArea.OTHERS}>{t('services.subjects.others')}</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('services.filters.serviceType')}
                </label>
                <select
                  value={filters.service_type}
                  onChange={(e) => handleFilterChange('service_type', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-red"
                >
                  <option value="all">{t('common.all')}</option>
                  <option value={ServiceType.ONLINE}>{t('services.types.online')}</option>
                  <option value={ServiceType.IN_PERSON}>{t('services.types.inPerson')}</option>
                  <option value={ServiceType.LIBRARY}>{t('services.types.library')}</option>
                  <option value={ServiceType.HYBRID}>{t('services.types.hybrid')}</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('services.filters.gradeLevel')}
                </label>
                <Input
                  type="text"
                  placeholder={t('services.filters.gradeLevelPlaceholder')}
                  value={filters.grade_level}
                  onChange={(e) => handleFilterChange('grade_level', e.target.value)}
                />
              </div>
            </div>

            <div className="mt-4 flex justify-end">
              <Button
                variant="ghost"
                onClick={() => {
                  setFilters({
                    search: '',
                    subject_area: 'all',
                    service_type: 'all',
                    grade_level: ''
                  });
                  setPage(1);
                }}
              >
                {t('common.clearFilters')}
              </Button>
            </div>
          </div>
        </Card>
      )}

      {/* Content Grid */}
      {activeTab === 'services' ? (
        services.length === 0 ? (
          <EmptyState
            icon={<BookOpen className="w-12 h-12" />}
            title={t('services.empty.title')}
            message={t('services.empty.message')}
            action={
              canManageServices ? (
                <Button variant="primary" onClick={() => setShowAddModal(true)}>
                  <Plus className="w-4 h-4 mr-2" />
                  {t('services.addService')}
                </Button>
              ) : undefined
            }
          />
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {services.map(renderServiceCard)}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="mt-8 flex items-center justify-center space-x-2">
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => setPage(page - 1)}
                  disabled={page === 1}
                >
                  {t('common.previous')}
                </Button>
                <span className="text-sm text-gray-700">
                  {t('common.page')} {page} {t('common.of')} {totalPages}
                </span>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => setPage(page + 1)}
                  disabled={page === totalPages}
                >
                  {t('common.next')}
                </Button>
              </div>
            )}
          </>
        )
      ) : (
        packages.length === 0 ? (
          <EmptyState
            icon={<Package className="w-12 h-12" />}
            title={t('services.packages.empty.title')}
            message={t('services.packages.empty.message')}
            action={
              canManageServices ? (
                <Button variant="primary" onClick={() => setShowPackagesModal(true)}>
                  <Plus className="w-4 h-4 mr-2" />
                  {t('services.addPackage')}
                </Button>
              ) : undefined
            }
          />
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {packages.map(renderPackageCard)}
          </div>
        )
      )}

      {/* Modals */}
      {showDetailsModal && selectedService && (
        <ServiceDetailsModal
          isOpen={showDetailsModal}
          onClose={() => {
            setShowDetailsModal(false);
            setSelectedService(null);
          }}
          service={selectedService}
          onUpdate={loadData}
          canManage={canManageServices}
        />
      )}

      {showAddModal && (
        <AddServiceModal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
          onSuccess={handleAddComplete}
          mode={activeTab}
        />
      )}

      {showPackagesModal && (
        <ManagePackagesModal
          isOpen={showPackagesModal}
          onClose={() => setShowPackagesModal(false)}
          onUpdate={loadData}
        />
      )}
    </div>
  );
};

export default ServiceCatalog;