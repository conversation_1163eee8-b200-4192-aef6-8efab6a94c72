import React from 'react';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Settings, Users, CreditCard, Bell, Key, Shield, Lock, DollarSign } from 'lucide-react';

interface Tab {
  id: string;
  label: string;
  path: string;
  icon: React.ReactNode;
}

const SettingsLayout: React.FC = () => {
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();

  const tabs: Tab[] = [
    { 
      id: 'system', 
      label: 'System', 
      path: '/settings/system',
      icon: <Settings className="w-4 h-4" />
    },
    { 
      id: 'users', 
      label: 'Users', 
      path: '/settings/users',
      icon: <Users className="w-4 h-4" />
    },
    { 
      id: 'security', 
      label: 'Security', 
      path: '/settings/security',
      icon: <Lock className="w-4 h-4" />
    },
    { 
      id: 'payments', 
      label: 'Payments', 
      path: '/settings/payments',
      icon: <CreditCard className="w-4 h-4" />
    },
    { 
      id: 'notifications', 
      label: 'Notifications', 
      path: '/settings/notifications',
      icon: <Bell className="w-4 h-4" />
    },
    { 
      id: 'api', 
      label: 'API', 
      path: '/settings/api',
      icon: <Key className="w-4 h-4" />
    },
    { 
      id: 'pricing', 
      label: 'Service Pricing', 
      path: '/settings/pricing',
      icon: <DollarSign className="w-4 h-4" />
    },
    { 
      id: 'consent', 
      label: 'Privacy & Consent', 
      path: '/settings/consent',
      icon: <Shield className="w-4 h-4" />
    },
  ];

  const currentTab = tabs.find(tab => location.pathname === tab.path) || tabs[0];

  return (
    <div className="h-full flex flex-col">
      {/* Tab Navigation */}
      <div className="bg-white shadow-soft px-6 py-4">
        <div className="flex items-center space-x-8 overflow-x-auto">
          {tabs.map(tab => (
            <button
              key={tab.id}
              onClick={() => navigate(tab.path)}
              className={`
                flex items-center gap-2 px-4 py-2 rounded-soft text-sm font-medium transition-all
                ${currentTab.id === tab.id
                  ? 'bg-accent-red text-white'
                  : 'text-text-secondary hover:bg-background-secondary'
                }
              `}
            >
              {tab.icon}
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-auto">
        <Outlet />
      </div>
    </div>
  );
};

export default SettingsLayout;