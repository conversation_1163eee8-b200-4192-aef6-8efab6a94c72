"""
Twilio SMS service for critical alerts and notifications.
"""

import logging
import re
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from twilio.rest import Client
from twilio.base.exceptions import TwilioRestException
import phonenumbers
from phonenumbers import NumberParseException
import asyncio
from collections import defaultdict

from app.config.settings import settings
from app.core.exceptions import ExternalServiceError, ValidationError
from app.models.notification_models import SMSStatus
from app.database.repositories.sms_repository import SMSRepository

logger = logging.getLogger(__name__)


class TwilioService:
    """
    Service for sending SMS notifications via Twilio.
    
    Features:
    - Canadian phone number validation
    - SMS rate limiting per user
    - Delivery status tracking
    - Conversation threading
    - Template management
    - Opt-out handling
    """
    
    def __init__(self):
        # Initialize Twilio client only if credentials are available
        self.client = None
        self.from_number = None
        
        if hasattr(settings, 'TWILIO_ACCOUNT_SID') and settings.TWILIO_ACCOUNT_SID:
            try:
                self.client = Client(
                    settings.TWILIO_ACCOUNT_SID,
                    settings.TWILIO_AUTH_TOKEN
                )
                self.from_number = settings.TWILIO_PHONE_NUMBER
            except Exception as e:
                logger.warning(f"Failed to initialize Twilio client: {e}")
                
        self.sms_repository = SMSRepository()
        
        # Rate limiting configuration
        self.rate_limits = {
            'per_user_per_hour': 10,
            'per_user_per_day': 50,
            'global_per_minute': 100
        }
        self._rate_limit_cache = defaultdict(list)
    
    async def send_sms(
        self,
        to_number: str,
        message: str,
        user_id: Optional[int] = None,
        media_urls: Optional[List[str]] = None,
        callback_url: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        force: bool = False
    ) -> Dict[str, Any]:
        """
        Send SMS message with Canadian phone number support.
        
        Args:
            to_number: Recipient phone number
            message: SMS message content
            user_id: Associated user ID
            media_urls: Optional MMS media URLs
            callback_url: Status callback URL
            metadata: Additional metadata
            force: Bypass rate limiting (for critical alerts)
            
        Returns:
            SMS send result with message SID
        """
        try:
            # Check if Twilio client is initialized
            if not self.client:
                logger.error("Twilio client not initialized - missing credentials")
                raise ExternalServiceError("SMS service not configured")
            # Validate and format phone number
            formatted_number = self._validate_phone_number(to_number)
            
            # Check rate limits unless forced
            if not force:
                await self._check_rate_limits(user_id, formatted_number)
            
            # Check opt-out status
            if user_id and await self._is_opted_out(user_id, formatted_number):
                raise ExternalServiceError(f"User {user_id} has opted out of SMS")
            
            # Validate message length (160 chars for SMS, 1600 for MMS)
            max_length = 1600 if media_urls else 160
            if len(message) > max_length:
                message = message[:max_length-3] + "..."
            
            # Build message parameters
            params = {
                'body': message,
                'from_': self.from_number,
                'to': formatted_number
            }
            
            if media_urls:
                params['media_url'] = media_urls[:10]  # Max 10 media items
            
            if callback_url:
                params['status_callback'] = callback_url
            
            # Send message
            message_obj = self.client.messages.create(**params)
            
            # Store message in database
            sms_record = await self.sms_repository.create_sms_message(
                user_id=user_id,
                phone_number=formatted_number,
                message=message,
                direction='outbound',
                status=SMSStatus.SENT,
                twilio_sid=message_obj.sid,
                metadata=metadata
            )
            
            # Update rate limit cache
            await self._update_rate_limits(user_id, formatted_number)
            
            logger.info(f"SMS sent to {formatted_number}: {message_obj.sid}")
            
            return {
                'message_sid': message_obj.sid,
                'sms_id': sms_record.sms_id,
                'to': formatted_number,
                'status': message_obj.status,
                'segments': message_obj.num_segments,
                'price': message_obj.price,
                'price_unit': message_obj.price_unit,
                'queued_at': datetime.now().isoformat()
            }
            
        except TwilioRestException as e:
            logger.error(f"Twilio error sending SMS: {e}")
            raise ExternalServiceError(f"Failed to send SMS: {e.msg}")
        except Exception as e:
            logger.error(f"Error sending SMS: {e}")
            raise ExternalServiceError(f"SMS send failed: {str(e)}")
    
    async def send_bulk_sms(
        self,
        recipients: List[Tuple[str, int]],  # [(phone_number, user_id), ...]
        message: str,
        metadata: Optional[Dict[str, Any]] = None,
        schedule_time: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        Send SMS to multiple recipients.
        
        Args:
            recipients: List of (phone_number, user_id) tuples
            message: SMS message content
            metadata: Additional metadata
            schedule_time: Optional scheduled send time
            
        Returns:
            Bulk send results
        """
        results = {
            'successful': [],
            'failed': [],
            'total': len(recipients)
        }
        
        for phone_number, user_id in recipients:
            try:
                # Add delay for rate limiting
                await asyncio.sleep(0.1)
                
                result = await self.send_sms(
                    to_number=phone_number,
                    message=message,
                    user_id=user_id,
                    metadata=metadata
                )
                
                results['successful'].append({
                    'phone_number': phone_number,
                    'user_id': user_id,
                    'message_sid': result['message_sid']
                })
                
            except Exception as e:
                logger.error(f"Failed to send SMS to {phone_number}: {e}")
                results['failed'].append({
                    'phone_number': phone_number,
                    'user_id': user_id,
                    'error': str(e)
                })
        
        return results
    
    async def handle_status_callback(
        self,
        message_sid: str,
        status: str,
        error_code: Optional[str] = None,
        error_message: Optional[str] = None
    ) -> None:
        """
        Handle Twilio status callback.
        
        Args:
            message_sid: Twilio message SID
            status: Message status
            error_code: Optional error code
            error_message: Optional error message
        """
        try:
            # Map Twilio status to our status
            status_map = {
                'queued': SMSStatus.QUEUED,
                'sending': SMSStatus.SENT,
                'sent': SMSStatus.SENT,
                'delivered': SMSStatus.DELIVERED,
                'undelivered': SMSStatus.FAILED,
                'failed': SMSStatus.FAILED
            }
            
            sms_status = status_map.get(status, SMSStatus.FAILED)
            
            # Update message status
            await self.sms_repository.update_sms_status(
                twilio_sid=message_sid,
                status=sms_status,
                error_code=error_code,
                error_message=error_message,
                delivered_at=datetime.now() if status == 'delivered' else None
            )
            
            logger.info(f"SMS status updated: {message_sid} -> {status}")
            
        except Exception as e:
            logger.error(f"Error handling status callback: {e}")
    
    async def handle_incoming_sms(
        self,
        from_number: str,
        body: str,
        message_sid: str,
        media_urls: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Handle incoming SMS message.
        
        Args:
            from_number: Sender's phone number
            body: Message body
            message_sid: Twilio message SID
            media_urls: Optional media URLs
            
        Returns:
            Processing result with auto-response if applicable
        """
        try:
            # Format phone number
            formatted_number = self._validate_phone_number(from_number)
            
            # Find associated user
            user = await self.sms_repository.get_user_by_phone(formatted_number)
            user_id = user.user_id if user else None
            
            # Store incoming message
            sms_record = await self.sms_repository.create_sms_message(
                user_id=user_id,
                phone_number=formatted_number,
                message=body,
                direction='inbound',
                status=SMSStatus.RECEIVED,
                twilio_sid=message_sid,
                metadata={'media_urls': media_urls} if media_urls else None
            )
            
            # Process commands (STOP, START, HELP, etc.)
            response = await self._process_sms_commands(body, user_id, formatted_number)
            
            # Handle appointment confirmations (YES/NO)
            if not response and user_id:
                response = await self._process_appointment_confirmation(
                    body, user_id, formatted_number
                )
            
            logger.info(f"Incoming SMS from {formatted_number}: {body[:50]}...")
            
            return {
                'sms_id': sms_record.sms_id,
                'user_id': user_id,
                'auto_response': response,
                'processed': True
            }
            
        except Exception as e:
            logger.error(f"Error handling incoming SMS: {e}")
            raise ExternalServiceError(f"Failed to process incoming SMS: {str(e)}")
    
    async def get_conversation(
        self,
        user_id: int,
        phone_number: Optional[str] = None,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """
        Get SMS conversation history.
        
        Args:
            user_id: User ID
            phone_number: Optional specific phone number
            limit: Number of messages to retrieve
            
        Returns:
            List of messages in conversation
        """
        try:
            messages = await self.sms_repository.get_conversation(
                user_id=user_id,
                phone_number=phone_number,
                limit=limit
            )
            
            return [
                {
                    'sms_id': msg.sms_id,
                    'message': msg.message,
                    'direction': msg.direction,
                    'status': msg.status,
                    'created_at': msg.created_at,
                    'delivered_at': msg.delivered_at,
                    'phone_number': msg.phone_number
                }
                for msg in messages
            ]
            
        except Exception as e:
            logger.error(f"Error getting conversation: {e}")
            raise
    
    def _validate_phone_number(self, phone_number: str) -> str:
        """
        Validate and format Canadian phone number.
        
        Args:
            phone_number: Phone number to validate
            
        Returns:
            Formatted phone number in E.164 format
        """
        try:
            # Remove all non-numeric characters
            cleaned = re.sub(r'\D', '', phone_number)
            
            # Add country code if missing
            if len(cleaned) == 10:
                cleaned = '1' + cleaned
            
            # Parse with phonenumbers library
            parsed = phonenumbers.parse('+' + cleaned, 'CA')
            
            # Validate it's a valid Canadian number
            if not phonenumbers.is_valid_number(parsed):
                raise ValidationError(f"Invalid phone number: {phone_number}")
            
            # Return in E.164 format
            return phonenumbers.format_number(
                parsed,
                phonenumbers.PhoneNumberFormat.E164
            )
            
        except NumberParseException:
            raise ValidationError(f"Cannot parse phone number: {phone_number}")
    
    async def _check_rate_limits(
        self,
        user_id: Optional[int],
        phone_number: str
    ) -> None:
        """Check and enforce rate limits."""
        now = datetime.now()
        
        # Check per-user limits
        if user_id:
            user_key = f"user_{user_id}"
            user_sends = self._rate_limit_cache[user_key]
            
            # Clean old entries
            user_sends = [
                ts for ts in user_sends 
                if now - ts < timedelta(hours=24)
            ]
            self._rate_limit_cache[user_key] = user_sends
            
            # Check hourly limit
            hour_sends = [
                ts for ts in user_sends 
                if now - ts < timedelta(hours=1)
            ]
            if len(hour_sends) >= self.rate_limits['per_user_per_hour']:
                raise ExternalServiceError("SMS rate limit exceeded (hourly)")
            
            # Check daily limit
            if len(user_sends) >= self.rate_limits['per_user_per_day']:
                raise ExternalServiceError("SMS rate limit exceeded (daily)")
        
        # Check global rate limit
        global_key = "global"
        global_sends = self._rate_limit_cache[global_key]
        global_sends = [
            ts for ts in global_sends 
            if now - ts < timedelta(minutes=1)
        ]
        self._rate_limit_cache[global_key] = global_sends
        
        if len(global_sends) >= self.rate_limits['global_per_minute']:
            raise ExternalServiceError("Global SMS rate limit exceeded")
    
    async def _update_rate_limits(
        self,
        user_id: Optional[int],
        phone_number: str
    ) -> None:
        """Update rate limit counters."""
        now = datetime.now()
        
        if user_id:
            self._rate_limit_cache[f"user_{user_id}"].append(now)
        
        self._rate_limit_cache["global"].append(now)
    
    async def _is_opted_out(
        self,
        user_id: int,
        phone_number: str
    ) -> bool:
        """Check if user has opted out of SMS."""
        return await self.sms_repository.is_opted_out(user_id, phone_number)
    
    async def _process_sms_commands(
        self,
        message: str,
        user_id: Optional[int],
        phone_number: str
    ) -> Optional[str]:
        """Process SMS commands like STOP, START, HELP."""
        command = message.strip().upper()
        
        if command in ['STOP', 'ARRET', 'UNSUBSCRIBE', 'DÉSABONNER']:
            if user_id:
                await self.sms_repository.update_opt_out_status(
                    user_id, phone_number, opted_out=True
                )
            return "You have been unsubscribed from SMS notifications. Reply START to re-subscribe."
        
        elif command in ['START', 'COMMENCER', 'SUBSCRIBE', 'ABONNER']:
            if user_id:
                await self.sms_repository.update_opt_out_status(
                    user_id, phone_number, opted_out=False
                )
            return "You have been subscribed to SMS notifications from TutorAide."
        
        elif command in ['HELP', 'AIDE', 'INFO']:
            return (
                "TutorAide SMS Commands:\n"
                "STOP - Unsubscribe from messages\n"
                "START - Subscribe to messages\n"
                "YES/NO - Confirm appointments\n"
                "Reply to messages normally to chat with support."
            )
        
        return None
    
    async def _process_appointment_confirmation(
        self,
        message: str,
        user_id: int,
        phone_number: str
    ) -> Optional[str]:
        """Process appointment confirmation responses."""
        response = message.strip().upper()
        
        if response in ['YES', 'OUI', 'Y', 'O', 'CONFIRM']:
            # Check for pending confirmations
            confirmation = await self.sms_repository.get_pending_confirmation(
                user_id, phone_number
            )
            
            if confirmation:
                # Process confirmation
                # This would integrate with appointment service
                return "Thank you! Your appointment has been confirmed."
        
        elif response in ['NO', 'NON', 'N', 'CANCEL', 'ANNULER']:
            # Check for pending confirmations
            confirmation = await self.sms_repository.get_pending_confirmation(
                user_id, phone_number
            )
            
            if confirmation:
                # Process cancellation
                return "Your appointment has been cancelled. We'll notify the other party."
        
        return None
    
    async def send_template_sms(
        self,
        to_number: str,
        template_name: str,
        variables: Dict[str, str],
        user_id: Optional[int] = None,
        language: str = 'en'
    ) -> Dict[str, Any]:
        """
        Send SMS using a template.
        
        Args:
            to_number: Recipient phone number
            template_name: Name of the template
            variables: Template variables
            user_id: Associated user ID
            language: Language code (en/fr)
            
        Returns:
            SMS send result
        """
        # Get template from repository
        template = await self.sms_repository.get_template(template_name, language)
        
        if not template:
            raise ExternalServiceError(f"Template '{template_name}' not found")
        
        # Render template with variables
        message = template.content
        for key, value in variables.items():
            message = message.replace(f"{{{key}}}", str(value))
        
        # Send SMS
        return await self.send_sms(
            to_number=to_number,
            message=message,
            user_id=user_id,
            metadata={'template': template_name, 'language': language}
        )