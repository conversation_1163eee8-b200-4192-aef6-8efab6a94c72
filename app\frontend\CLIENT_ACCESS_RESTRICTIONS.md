# Client Access Restrictions

## Overview
When logged in as a CLIENT role, users have limited access to ensure they only see their own data and relevant features.

## Implemented Restrictions

### 1. Sidebar Navigation
- **Visible Sections for Clients:**
  - Services (view available services)
  - Users → My Profile (only their own profile)
  - Calendar (only their appointments)
  - Tutor Map (view available tutors)
  - Billing → My Invoices
  - Billing → My Subscription
  - Messages → Chat (not SMS or broadcasts)
  - Settings → Notifications

- **Hidden Sections for Clients:**
  - Reports (all)
  - Users → Tutors management
  - Users → Dependants management (they see this in their profile)
  - Billing → Tutor Payments
  - Billing → Packages
  - Messages → SMS Threads, Templates, Broadcasts
  - Settings → System, Users, Payments, API, Services

### 2. Data Filtering

#### Profile Access
- Clients see a dedicated profile view at `/users/clients`
- Can edit their own profile information
- Can manage their dependants
- Cannot see other clients' information

#### Tutor Information
- Can only see tutor names and specialties
- Cannot see tutor contact info (email, phone)
- Cannot see tutor rates or financial information
- Cannot see verification status
- Can book sessions with tutors

#### Calendar/Appointments
- Only see their own appointments
- Cannot see other clients' appointments
- Cannot see all tutor schedules

#### Billing
- Only see their own invoices
- Only see their own subscription
- Cannot create invoices
- Cannot see tutor payments or manage packages

### 3. Actions Restricted

#### Cannot:
- Add new users (clients or tutors)
- Edit tutor information or rates
- View financial reports
- Send broadcast messages
- Access system settings
- View other clients' data

#### Can:
- Edit their own profile
- Manage their dependants
- Book appointments
- View their invoices
- Pay invoices
- View their subscription
- Send messages via chat
- View available services
- View tutor map

## Implementation Details

### Permission System
Located in `/app/frontend/src/utils/permissions.ts`

Key functions:
- `hasPermission()` - Check single permission
- `hasAllPermissions()` - Check multiple permissions (AND)
- `hasAnyPermission()` - Check multiple permissions (OR)
- `filterDataByRole()` - Filter data based on user role

### Protected Components
- `ProtectedRoute` - Route-level protection
- Individual page components check permissions
- Data filtering in API calls and component state

### Client-Specific Views
- `ClientProfileView` - Dedicated profile management for clients
- Simplified invoice list showing only their invoices
- Filtered calendar showing only their appointments
- Limited tutor information display

## Testing Client Access

1. Login with a CLIENT role account
2. Verify sidebar only shows allowed sections
3. Check that:
   - Profile page shows only their information
   - Calendar shows only their appointments
   - Invoices show only their invoices
   - Tutors page shows limited information
   - Cannot access admin features

## Future Enhancements

1. Add parent/guardian relationship management
2. Implement payment method management for clients
3. Add learning progress tracking
4. Implement appointment request workflow
5. Add client-specific notifications preferences