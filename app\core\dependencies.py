from typing import Generator, Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import asyncpg

from app.config.database import get_db, DatabaseManager, get_db_connection
from app.core.security import verify_token, get_current_user_from_token
from app.models.user_models import User, UserRoleType
# Removed to avoid circular import - import require_roles directly where needed
from app.services.analytics_service import AnalyticsService


security = HTTPBearer()


# Database dependency
async def get_database() -> Generator[asyncpg.Connection, None, None]:
    """Database connection dependency."""
    async for conn in get_db():
        yield conn


# Authentication dependencies
async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: asyncpg.Connection = Depends(get_database)
) -> User:
    """Get current authenticated user."""
    token = credentials.credentials
    
    try:
        payload = verify_token(token)
        user = await get_current_user_from_token(payload, db)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
        return user
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """Get current active user."""
    if current_user.deleted_at:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user


# Role-based dependencies
async def require_manager(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """Require manager role."""
    if UserRoleType.MANAGER not in current_user.roles:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions. Manager role required."
        )
    return current_user


async def require_tutor(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """Require tutor role."""
    if UserRoleType.TUTOR not in current_user.roles:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions. Tutor role required."
        )
    return current_user


async def require_client(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """Require client role."""
    if UserRoleType.CLIENT not in current_user.roles:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions. Client role required."
        )
    return current_user


async def require_manager_or_tutor(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """Require manager or tutor role."""
    if not (UserRoleType.MANAGER in current_user.roles or UserRoleType.TUTOR in current_user.roles):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions. Manager or Tutor role required."
        )
    return current_user


async def require_manager_or_client(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """Require manager or client role."""
    if not (UserRoleType.MANAGER in current_user.roles or UserRoleType.CLIENT in current_user.roles):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions. Manager or Client role required."
        )
    return current_user


# Optional authentication
async def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: asyncpg.Connection = Depends(get_database)
) -> Optional[User]:
    """Get current user if authenticated, None otherwise."""
    if not credentials:
        return None
    
    try:
        return await get_current_user(credentials, db)
    except HTTPException:
        return None


# Database manager dependency
def get_db_manager() -> DatabaseManager:
    """Get database manager instance."""
    return DatabaseManager()


# Rate limiting dependency
async def check_rate_limit():
    """Check rate limiting (placeholder implementation)."""
    # TODO: Implement actual rate limiting
    pass


# Analytics service dependency
async def get_analytics_service(
    db_manager: DatabaseManager = Depends(get_db_manager)
) -> AnalyticsService:
    """Get analytics service instance with dependencies."""
    return AnalyticsService(db_manager)