import React from 'react';
import { clsx } from 'clsx';
import { Card } from '../common/Card';

interface FormSectionProps {
  title: string;
  subtitle?: string;
  children: React.ReactNode;
  icon?: React.ReactNode;
  actions?: React.ReactNode;
  collapsible?: boolean;
  defaultOpen?: boolean;
  className?: string;
}

export const FormSection: React.FC<FormSectionProps> = ({
  title,
  subtitle,
  children,
  icon,
  actions,
  collapsible = false,
  defaultOpen = true,
  className,
}) => {
  const [isOpen, setIsOpen] = React.useState(defaultOpen);

  const handleToggle = () => {
    if (collapsible) {
      setIsOpen(!isOpen);
    }
  };

  return (
    <Card className={className}>
      <div className="space-y-6">
        <div
          className={clsx(
            'flex items-start justify-between',
            collapsible && 'cursor-pointer'
          )}
          onClick={handleToggle}
        >
          <div className="flex items-start gap-3">
            {icon && (
              <div className="p-2 bg-red-50 rounded-lg">
                <span className="w-5 h-5 text-accent-red">{icon}</span>
              </div>
            )}
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-text-primary">
                {title}
              </h3>
              {subtitle && (
                <p className="mt-1 text-sm text-text-secondary">
                  {subtitle}
                </p>
              )}
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {actions}
            {collapsible && (
              <button
                type="button"
                className="p-1 hover:bg-background-secondary rounded-md transition-colors"
              >
                <svg
                  className={clsx(
                    'w-5 h-5 text-text-muted transition-transform',
                    !isOpen && 'rotate-180'
                  )}
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 15l7-7 7 7"
                  />
                </svg>
              </button>
            )}
          </div>
        </div>

        {(!collapsible || isOpen) && (
          <div className="animate-fadeIn">
            {children}
          </div>
        )}
      </div>
    </Card>
  );
};

interface FormGroupProps {
  children: React.ReactNode;
  className?: string;
}

export const FormGroup: React.FC<FormGroupProps> = ({
  children,
  className,
}) => {
  return (
    <div className={clsx('space-y-6', className)}>
      {children}
    </div>
  );
};

export default FormSection;