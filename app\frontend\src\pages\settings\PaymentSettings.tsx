import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  CreditCard, DollarSign, Calendar, Percent, Shield,
  Save, AlertCircle, CheckCircle, XCircle, RefreshCw
} from 'lucide-react';

interface PaymentConfig {
  stripe: {
    enabled: boolean;
    publicKey: string;
    secretKey: string;
    webhookSecret: string;
    testMode: boolean;
  };
  paymentMethods: {
    creditCard: boolean;
    bankTransfer: boolean;
    interacETransfer: boolean;
    cash: boolean;
  };
  invoicing: {
    autoGenerate: boolean;
    dueDays: number;
    lateFeeEnabled: boolean;
    lateFeePercentage: number;
    lateFeeGraceDays: number;
    taxRate: number;
    invoicePrefix: string;
    startingNumber: number;
  };
  tutorPayments: {
    paymentSchedule: 'weekly' | 'biweekly' | 'monthly';
    paymentDay: string;
    minimumPayout: number;
    processingFee: number;
    holdbackPercentage: number;
  };
  subscriptions: {
    enabled: boolean;
    trialPeriodDays: number;
    autoRenew: boolean;
    prorationEnabled: boolean;
    gracePeriodDays: number;
  };
}

const PaymentSettings: React.FC = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<'gateway' | 'invoicing' | 'payouts' | 'subscriptions'>('gateway');
  const [hasChanges, setHasChanges] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [testResult, setTestResult] = useState<'success' | 'error' | null>(null);

  const [config, setConfig] = useState<PaymentConfig>({
    stripe: {
      enabled: true,
      publicKey: 'pk_test_51234567890abcdef',
      secretKey: 'sk_test_••••••••••••••••',
      webhookSecret: 'whsec_••••••••••••••••',
      testMode: true,
    },
    paymentMethods: {
      creditCard: true,
      bankTransfer: true,
      interacETransfer: true,
      cash: false,
    },
    invoicing: {
      autoGenerate: true,
      dueDays: 30,
      lateFeeEnabled: true,
      lateFeePercentage: 2,
      lateFeeGraceDays: 7,
      taxRate: 14.975, // Quebec tax rate
      invoicePrefix: 'INV',
      startingNumber: 1001,
    },
    tutorPayments: {
      paymentSchedule: 'weekly',
      paymentDay: 'Friday',
      minimumPayout: 50,
      processingFee: 2.5,
      holdbackPercentage: 0,
    },
    subscriptions: {
      enabled: true,
      trialPeriodDays: 14,
      autoRenew: true,
      prorationEnabled: true,
      gracePeriodDays: 3,
    },
  });

  const updateConfig = (section: keyof PaymentConfig, field: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value,
      },
    }));
    setHasChanges(true);
  };

  const handleTestConnection = async () => {
    setIsTesting(true);
    setTestResult(null);
    
    // Simulate API test
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Random success/failure for demo
    const success = Math.random() > 0.3;
    setTestResult(success ? 'success' : 'error');
    setIsTesting(false);
  };

  const handleSave = async () => {
    console.log('Saving payment settings:', config);
    await new Promise(resolve => setTimeout(resolve, 1500));
    setHasChanges(false);
  };

  return (
    <div className="p-6 max-w-6xl mx-auto">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-text-primary">Payment Settings</h1>
        <p className="text-text-secondary mt-1">Configure payment gateway, invoicing, and financial settings</p>
      </div>

      {/* Tabs */}
      <div className="flex items-center gap-6 border-b border-primary-200 mb-6">
        <button
          onClick={() => setActiveTab('gateway')}
          className={`pb-3 px-1 font-medium transition-colors ${
            activeTab === 'gateway'
              ? 'text-accent-red border-b-2 border-accent-red'
              : 'text-text-secondary hover:text-text-primary'
          }`}
        >
          Payment Gateway
        </button>
        <button
          onClick={() => setActiveTab('invoicing')}
          className={`pb-3 px-1 font-medium transition-colors ${
            activeTab === 'invoicing'
              ? 'text-accent-red border-b-2 border-accent-red'
              : 'text-text-secondary hover:text-text-primary'
          }`}
        >
          Invoicing
        </button>
        <button
          onClick={() => setActiveTab('payouts')}
          className={`pb-3 px-1 font-medium transition-colors ${
            activeTab === 'payouts'
              ? 'text-accent-red border-b-2 border-accent-red'
              : 'text-text-secondary hover:text-text-primary'
          }`}
        >
          Tutor Payouts
        </button>
        <button
          onClick={() => setActiveTab('subscriptions')}
          className={`pb-3 px-1 font-medium transition-colors ${
            activeTab === 'subscriptions'
              ? 'text-accent-red border-b-2 border-accent-red'
              : 'text-text-secondary hover:text-text-primary'
          }`}
        >
          Subscriptions
        </button>
      </div>

      {activeTab === 'gateway' && (
        <>
          {/* Stripe Configuration */}
          <div className="bg-white rounded-large shadow-soft p-6 mb-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <CreditCard className="w-5 h-5 text-accent-red" />
                <h2 className="text-lg font-semibold text-text-primary">Stripe Configuration</h2>
              </div>
              <div className="flex items-center gap-2">
                {testResult === 'success' && (
                  <span className="flex items-center gap-1 text-accent-green text-sm">
                    <CheckCircle className="w-4 h-4" />
                    Connected
                  </span>
                )}
                {testResult === 'error' && (
                  <span className="flex items-center gap-1 text-accent-red text-sm">
                    <XCircle className="w-4 h-4" />
                    Connection Failed
                  </span>
                )}
                <button
                  onClick={handleTestConnection}
                  disabled={isTesting}
                  className="flex items-center gap-2 px-4 py-2 border border-primary-300 rounded-soft hover:bg-background-secondary disabled:opacity-50"
                >
                  <RefreshCw className={`w-4 h-4 ${isTesting ? 'animate-spin' : ''}`} />
                  Test Connection
                </button>
              </div>
            </div>

            <div className="space-y-4">
              <label className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-text-primary">Enable Stripe Payments</p>
                  <p className="text-sm text-text-secondary">Accept credit card payments through Stripe</p>
                </div>
                <input
                  type="checkbox"
                  checked={config.stripe.enabled}
                  onChange={(e) => updateConfig('stripe', 'enabled', e.target.checked)}
                  className="w-5 h-5 rounded accent-accent-red"
                />
              </label>

              <label className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-text-primary">Test Mode</p>
                  <p className="text-sm text-text-secondary">Use Stripe test environment</p>
                </div>
                <input
                  type="checkbox"
                  checked={config.stripe.testMode}
                  onChange={(e) => updateConfig('stripe', 'testMode', e.target.checked)}
                  className="w-5 h-5 rounded accent-accent-red"
                />
              </label>

              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Publishable Key
                </label>
                <input
                  type="text"
                  value={config.stripe.publicKey}
                  onChange={(e) => updateConfig('stripe', 'publicKey', e.target.value)}
                  className="w-full px-4 py-2 font-mono text-sm border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                  placeholder="pk_test_..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Secret Key
                </label>
                <input
                  type="password"
                  value={config.stripe.secretKey}
                  onChange={(e) => updateConfig('stripe', 'secretKey', e.target.value)}
                  className="w-full px-4 py-2 font-mono text-sm border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                  placeholder="sk_test_..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Webhook Secret
                </label>
                <input
                  type="password"
                  value={config.stripe.webhookSecret}
                  onChange={(e) => updateConfig('stripe', 'webhookSecret', e.target.value)}
                  className="w-full px-4 py-2 font-mono text-sm border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                  placeholder="whsec_..."
                />
              </div>
            </div>
          </div>

          {/* Payment Methods */}
          <div className="bg-white rounded-large shadow-soft p-6">
            <h2 className="text-lg font-semibold text-text-primary mb-6">Accepted Payment Methods</h2>

            <div className="space-y-4">
              <label className="flex items-center justify-between p-4 bg-background-secondary rounded-medium hover:bg-primary-100 transition-colors cursor-pointer">
                <div className="flex items-center gap-3">
                  <CreditCard className="w-5 h-5 text-text-secondary" />
                  <div>
                    <p className="font-medium text-text-primary">Credit/Debit Cards</p>
                    <p className="text-sm text-text-secondary">Visa, Mastercard, Amex</p>
                  </div>
                </div>
                <input
                  type="checkbox"
                  checked={config.paymentMethods.creditCard}
                  onChange={(e) => updateConfig('paymentMethods', 'creditCard', e.target.checked)}
                  className="w-5 h-5 rounded accent-accent-red"
                />
              </label>

              <label className="flex items-center justify-between p-4 bg-background-secondary rounded-medium hover:bg-primary-100 transition-colors cursor-pointer">
                <div className="flex items-center gap-3">
                  <DollarSign className="w-5 h-5 text-text-secondary" />
                  <div>
                    <p className="font-medium text-text-primary">Bank Transfer</p>
                    <p className="text-sm text-text-secondary">Direct bank deposits</p>
                  </div>
                </div>
                <input
                  type="checkbox"
                  checked={config.paymentMethods.bankTransfer}
                  onChange={(e) => updateConfig('paymentMethods', 'bankTransfer', e.target.checked)}
                  className="w-5 h-5 rounded accent-accent-red"
                />
              </label>

              <label className="flex items-center justify-between p-4 bg-background-secondary rounded-medium hover:bg-primary-100 transition-colors cursor-pointer">
                <div className="flex items-center gap-3">
                  <Shield className="w-5 h-5 text-text-secondary" />
                  <div>
                    <p className="font-medium text-text-primary">Interac e-Transfer</p>
                    <p className="text-sm text-text-secondary">Canadian email money transfer</p>
                  </div>
                </div>
                <input
                  type="checkbox"
                  checked={config.paymentMethods.interacETransfer}
                  onChange={(e) => updateConfig('paymentMethods', 'interacETransfer', e.target.checked)}
                  className="w-5 h-5 rounded accent-accent-red"
                />
              </label>

              <label className="flex items-center justify-between p-4 bg-background-secondary rounded-medium hover:bg-primary-100 transition-colors cursor-pointer">
                <div className="flex items-center gap-3">
                  <DollarSign className="w-5 h-5 text-text-secondary" />
                  <div>
                    <p className="font-medium text-text-primary">Cash</p>
                    <p className="text-sm text-text-secondary">In-person cash payments</p>
                  </div>
                </div>
                <input
                  type="checkbox"
                  checked={config.paymentMethods.cash}
                  onChange={(e) => updateConfig('paymentMethods', 'cash', e.target.checked)}
                  className="w-5 h-5 rounded accent-accent-red"
                />
              </label>
            </div>
          </div>
        </>
      )}

      {activeTab === 'invoicing' && (
        <>
          {/* Invoice Settings */}
          <div className="bg-white rounded-large shadow-soft p-6 mb-6">
            <div className="flex items-center gap-3 mb-6">
              <Calendar className="w-5 h-5 text-accent-red" />
              <h2 className="text-lg font-semibold text-text-primary">Invoice Settings</h2>
            </div>

            <div className="space-y-6">
              <label className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-text-primary">Auto-Generate Invoices</p>
                  <p className="text-sm text-text-secondary">Automatically create invoices after completed sessions</p>
                </div>
                <input
                  type="checkbox"
                  checked={config.invoicing.autoGenerate}
                  onChange={(e) => updateConfig('invoicing', 'autoGenerate', e.target.checked)}
                  className="w-5 h-5 rounded accent-accent-red"
                />
              </label>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Payment Due (days)
                  </label>
                  <input
                    type="number"
                    value={config.invoicing.dueDays}
                    onChange={(e) => updateConfig('invoicing', 'dueDays', parseInt(e.target.value))}
                    className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                    min={0}
                    max={90}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Tax Rate (%)
                  </label>
                  <input
                    type="number"
                    value={config.invoicing.taxRate}
                    onChange={(e) => updateConfig('invoicing', 'taxRate', parseFloat(e.target.value))}
                    className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                    min={0}
                    max={100}
                    step={0.001}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Invoice Prefix
                  </label>
                  <input
                    type="text"
                    value={config.invoicing.invoicePrefix}
                    onChange={(e) => updateConfig('invoicing', 'invoicePrefix', e.target.value)}
                    className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                    placeholder="INV"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Starting Number
                  </label>
                  <input
                    type="number"
                    value={config.invoicing.startingNumber}
                    onChange={(e) => updateConfig('invoicing', 'startingNumber', parseInt(e.target.value))}
                    className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                    min={1}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Late Fee Settings */}
          <div className="bg-white rounded-large shadow-soft p-6">
            <h2 className="text-lg font-semibold text-text-primary mb-6">Late Fee Settings</h2>

            <div className="space-y-6">
              <label className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-text-primary">Enable Late Fees</p>
                  <p className="text-sm text-text-secondary">Charge fees for overdue invoices</p>
                </div>
                <input
                  type="checkbox"
                  checked={config.invoicing.lateFeeEnabled}
                  onChange={(e) => updateConfig('invoicing', 'lateFeeEnabled', e.target.checked)}
                  className="w-5 h-5 rounded accent-accent-red"
                />
              </label>

              {config.invoicing.lateFeeEnabled && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Late Fee (%)
                    </label>
                    <input
                      type="number"
                      value={config.invoicing.lateFeePercentage}
                      onChange={(e) => updateConfig('invoicing', 'lateFeePercentage', parseFloat(e.target.value))}
                      className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                      min={0}
                      max={50}
                      step={0.1}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Grace Period (days)
                    </label>
                    <input
                      type="number"
                      value={config.invoicing.lateFeeGraceDays}
                      onChange={(e) => updateConfig('invoicing', 'lateFeeGraceDays', parseInt(e.target.value))}
                      className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                      min={0}
                      max={30}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </>
      )}

      {activeTab === 'payouts' && (
        <div className="bg-white rounded-large shadow-soft p-6">
          <div className="flex items-center gap-3 mb-6">
            <DollarSign className="w-5 h-5 text-accent-red" />
            <h2 className="text-lg font-semibold text-text-primary">Tutor Payment Settings</h2>
          </div>

          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Payment Schedule
                </label>
                <select
                  value={config.tutorPayments.paymentSchedule}
                  onChange={(e) => updateConfig('tutorPayments', 'paymentSchedule', e.target.value)}
                  className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                >
                  <option value="weekly">Weekly</option>
                  <option value="biweekly">Bi-weekly</option>
                  <option value="monthly">Monthly</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Payment Day
                </label>
                <select
                  value={config.tutorPayments.paymentDay}
                  onChange={(e) => updateConfig('tutorPayments', 'paymentDay', e.target.value)}
                  className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                >
                  <option value="Monday">Monday</option>
                  <option value="Tuesday">Tuesday</option>
                  <option value="Wednesday">Wednesday</option>
                  <option value="Thursday">Thursday</option>
                  <option value="Friday">Friday</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Minimum Payout ($)
                </label>
                <input
                  type="number"
                  value={config.tutorPayments.minimumPayout}
                  onChange={(e) => updateConfig('tutorPayments', 'minimumPayout', parseFloat(e.target.value))}
                  className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                  min={0}
                  step={10}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Processing Fee (%)
                </label>
                <input
                  type="number"
                  value={config.tutorPayments.processingFee}
                  onChange={(e) => updateConfig('tutorPayments', 'processingFee', parseFloat(e.target.value))}
                  className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                  min={0}
                  max={10}
                  step={0.1}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Holdback Percentage (%)
                </label>
                <input
                  type="number"
                  value={config.tutorPayments.holdbackPercentage}
                  onChange={(e) => updateConfig('tutorPayments', 'holdbackPercentage', parseFloat(e.target.value))}
                  className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                  min={0}
                  max={50}
                  step={1}
                />
                <p className="text-xs text-text-secondary mt-1">
                  Percentage of earnings held for dispute resolution
                </p>
              </div>
            </div>

            <div className="bg-primary-50 p-4 rounded-medium">
              <p className="text-sm text-text-secondary">
                <strong>Payment Cycle:</strong> Sessions completed from Thursday to Wednesday are paid on the following {config.tutorPayments.paymentDay}.
              </p>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'subscriptions' && (
        <div className="bg-white rounded-large shadow-soft p-6">
          <div className="flex items-center gap-3 mb-6">
            <Calendar className="w-5 h-5 text-accent-red" />
            <h2 className="text-lg font-semibold text-text-primary">Subscription Settings</h2>
          </div>

          <div className="space-y-6">
            <label className="flex items-center justify-between">
              <div>
                <p className="font-medium text-text-primary">Enable Subscriptions</p>
                <p className="text-sm text-text-secondary">Allow clients to purchase subscription packages</p>
              </div>
              <input
                type="checkbox"
                checked={config.subscriptions.enabled}
                onChange={(e) => updateConfig('subscriptions', 'enabled', e.target.checked)}
                className="w-5 h-5 rounded accent-accent-red"
              />
            </label>

            {config.subscriptions.enabled && (
              <>
                <label className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-text-primary">Auto-Renewal</p>
                    <p className="text-sm text-text-secondary">Automatically renew subscriptions at the end of term</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={config.subscriptions.autoRenew}
                    onChange={(e) => updateConfig('subscriptions', 'autoRenew', e.target.checked)}
                    className="w-5 h-5 rounded accent-accent-red"
                  />
                </label>

                <label className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-text-primary">Enable Proration</p>
                    <p className="text-sm text-text-secondary">Prorate charges when upgrading/downgrading plans</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={config.subscriptions.prorationEnabled}
                    onChange={(e) => updateConfig('subscriptions', 'prorationEnabled', e.target.checked)}
                    className="w-5 h-5 rounded accent-accent-red"
                  />
                </label>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Trial Period (days)
                    </label>
                    <input
                      type="number"
                      value={config.subscriptions.trialPeriodDays}
                      onChange={(e) => updateConfig('subscriptions', 'trialPeriodDays', parseInt(e.target.value))}
                      className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                      min={0}
                      max={30}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Grace Period (days)
                    </label>
                    <input
                      type="number"
                      value={config.subscriptions.gracePeriodDays}
                      onChange={(e) => updateConfig('subscriptions', 'gracePeriodDays', parseInt(e.target.value))}
                      className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                      min={0}
                      max={14}
                    />
                    <p className="text-xs text-text-secondary mt-1">
                      Days to retry payment before cancelling subscription
                    </p>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      )}

      {/* Security Warning */}
      <div className="mt-6 bg-accent-orange bg-opacity-10 p-4 rounded-medium">
        <div className="flex items-start gap-3">
          <AlertCircle className="w-5 h-5 text-accent-orange mt-0.5" />
          <div>
            <p className="font-medium text-text-primary">Security Notice</p>
            <p className="text-sm text-text-secondary mt-1">
              Keep your API keys secure. Never share them or commit them to version control. 
              Consider using environment variables for production deployments.
            </p>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="mt-6 flex justify-end">
        <button
          onClick={handleSave}
          disabled={!hasChanges}
          className={`
            flex items-center gap-2 px-6 py-2 rounded-soft text-white
            ${hasChanges
              ? 'bg-accent-red hover:bg-accent-red-dark'
              : 'bg-primary-300 cursor-not-allowed'
            }
          `}
        >
          <Save className="w-4 h-4" />
          Save Changes
        </button>
      </div>
    </div>
  );
};

export default PaymentSettings;