"""
Enhanced validation and security middleware for appointment endpoints.
"""

import logging
from datetime import datetime, timedelta, date, time
from typing import Dict, Any, List, Optional
from fastapi import HTTPException
import asyncpg

from app.models.appointment_models import AppointmentStatus
from app.models.service_models import ServiceType, LocationType, PackageType
from app.database.repositories.appointment_repository import AppointmentRepository
from app.database.repositories.tutor_repository import TutorRepository
from app.database.repositories.client_repository import ClientRepository
from app.core.validation import ValidationError

logger = logging.getLogger(__name__)


class AppointmentValidator:
    """Comprehensive validation for appointment operations."""
    
    def __init__(self, db_connection: asyncpg.Connection):
        self.db = db_connection
        self.appointment_repo = AppointmentRepository(db_connection)
        self.tutor_repo = TutorRepository(db_connection)
        self.client_repo = ClientRepository(db_connection)
    
    async def validate_appointment_creation(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Comprehensive validation for appointment creation.
        
        Validates:
        - User existence and status
        - Time conflicts
        - Business rules
        - Security constraints
        """
        errors = []
        
        try:
            # Validate tutor exists and is active
            tutor = await self.tutor_repo.get_by_id(request_data['tutor_id'])
            if not tutor:
                errors.append("Tutor not found")
            elif not tutor.is_active:
                errors.append("Tutor is not active")
            
            # Validate client exists and is active
            client = await self.client_repo.get_by_id(request_data['client_id'])
            if not client:
                errors.append("Client not found")
            elif not client.is_active:
                errors.append("Client is not active")
            
            # Validate dependant if provided
            if request_data.get('dependant_id'):
                # Check dependant relationship
                dependant_valid = await self._validate_dependant_relationship(
                    request_data['client_id'], 
                    request_data['dependant_id']
                )
                if not dependant_valid:
                    errors.append("Invalid dependant relationship")
            
            # Validate time slot availability
            appointment_datetime = datetime.combine(
                request_data['scheduled_date'], 
                request_data['start_time']
            )
            
            conflicts = await self._check_time_conflicts(
                tutor_id=request_data['tutor_id'],
                appointment_datetime=appointment_datetime,
                end_time=request_data['end_time'],
                exclude_appointment_id=None
            )
            
            if conflicts:
                errors.append(f"Time conflict detected: {conflicts}")
            
            # Validate business hours and advance booking
            business_validation = self._validate_business_constraints(
                appointment_datetime,
                request_data['start_time'],
                request_data['end_time']
            )
            errors.extend(business_validation)
            
            # Validate session-specific constraints
            session_validation = self._validate_session_constraints(request_data)
            errors.extend(session_validation)
            
            # Validate tutor specialization
            if tutor:
                specialization_valid = await self._validate_tutor_specialization(
                    tutor, request_data['subject_area']
                )
                if not specialization_valid:
                    errors.append(f"Tutor is not qualified for subject: {request_data['subject_area']}")
            
            if errors:
                raise ValidationError(f"Validation failed: {'; '.join(errors)}")
            
            return {
                'valid': True,
                'tutor': tutor,
                'client': client,
                'warnings': []
            }
            
        except Exception as e:
            logger.error(f"Validation error: {e}")
            raise ValidationError(f"Appointment validation failed: {str(e)}")
    
    async def validate_appointment_update(
        self, 
        appointment_id: int, 
        update_data: Dict[str, Any], 
        current_appointment: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Validate appointment update operations."""
        errors = []
        warnings = []
        
        try:
            # Check if appointment can be modified
            if current_appointment['status'] == AppointmentStatus.COMPLETED:
                errors.append("Cannot modify completed appointments")
            
            if current_appointment['status'] == AppointmentStatus.CANCELLED:
                if 'status' not in update_data or update_data['status'] != AppointmentStatus.SCHEDULED:
                    errors.append("Cannot modify cancelled appointments except to reschedule")
            
            # Validate time changes
            if any(field in update_data for field in ['scheduled_date', 'start_time', 'end_time']):
                new_datetime = datetime.combine(
                    update_data.get('scheduled_date', current_appointment['scheduled_date']),
                    update_data.get('start_time', current_appointment['start_time'])
                )
                
                # Check for conflicts (excluding current appointment)
                conflicts = await self._check_time_conflicts(
                    tutor_id=current_appointment['tutor_id'],
                    appointment_datetime=new_datetime,
                    end_time=update_data.get('end_time', current_appointment['end_time']),
                    exclude_appointment_id=appointment_id
                )
                
                if conflicts:
                    errors.append(f"Time conflict detected: {conflicts}")
                
                # Check advance notice for changes
                current_time = datetime.now()
                original_datetime = datetime.combine(
                    current_appointment['scheduled_date'],
                    current_appointment['start_time']
                )
                
                # Require 24-hour notice for changes (except for managers)
                if original_datetime - current_time < timedelta(hours=24):
                    warnings.append("Less than 24 hours notice for appointment change")
            
            # Validate status transitions
            if 'status' in update_data:
                valid_transition = self._validate_status_transition(
                    current_appointment['status'],
                    update_data['status']
                )
                if not valid_transition:
                    errors.append(f"Invalid status transition from {current_appointment['status']} to {update_data['status']}")
            
            if errors:
                raise ValidationError(f"Update validation failed: {'; '.join(errors)}")
            
            return {
                'valid': True,
                'warnings': warnings
            }
            
        except Exception as e:
            logger.error(f"Update validation error: {e}")
            raise ValidationError(f"Appointment update validation failed: {str(e)}")
    
    async def validate_bulk_operation(
        self, 
        appointment_ids: List[int], 
        operation: str, 
        user_id: int
    ) -> Dict[str, Any]:
        """Validate bulk operations on appointments."""
        errors = []
        warnings = []
        
        try:
            # Check appointment count limits
            if len(appointment_ids) > 50:
                errors.append("Cannot process more than 50 appointments in bulk")
            
            # Validate all appointments exist and user has access
            accessible_appointments = []
            for appointment_id in appointment_ids:
                appointment = await self.appointment_repo.get_by_id(appointment_id)
                if not appointment:
                    errors.append(f"Appointment {appointment_id} not found")
                    continue
                
                # Check user access (simplified - would use proper authorization)
                accessible_appointments.append(appointment)
            
            # Validate operation type
            valid_bulk_operations = ['cancel', 'confirm', 'reschedule', 'update_notes']
            if operation not in valid_bulk_operations:
                errors.append(f"Invalid bulk operation: {operation}")
            
            # Operation-specific validation
            if operation == 'cancel':
                for appointment in accessible_appointments:
                    if appointment.status in [AppointmentStatus.COMPLETED, AppointmentStatus.CANCELLED]:
                        warnings.append(f"Appointment {appointment.appointment_id} cannot be cancelled (status: {appointment.status})")
            
            if errors:
                raise ValidationError(f"Bulk operation validation failed: {'; '.join(errors)}")
            
            return {
                'valid': True,
                'accessible_appointments': accessible_appointments,
                'warnings': warnings
            }
            
        except Exception as e:
            logger.error(f"Bulk validation error: {e}")
            raise ValidationError(f"Bulk operation validation failed: {str(e)}")
    
    async def _check_time_conflicts(
        self, 
        tutor_id: int, 
        appointment_datetime: datetime, 
        end_time: time,
        exclude_appointment_id: Optional[int] = None
    ) -> Optional[str]:
        """Check for scheduling conflicts."""
        try:
            end_datetime = datetime.combine(appointment_datetime.date(), end_time)
            
            # Get overlapping appointments
            conflicts = await self.appointment_repo.get_overlapping_appointments(
                tutor_id=tutor_id,
                start_datetime=appointment_datetime,
                end_datetime=end_datetime,
                exclude_id=exclude_appointment_id
            )
            
            if conflicts:
                conflict_times = [
                    f"{c.start_time.strftime('%H:%M')}-{c.end_time.strftime('%H:%M')}"
                    for c in conflicts
                ]
                return f"Overlaps with existing appointments: {', '.join(conflict_times)}"
            
            return None
            
        except Exception as e:
            logger.error(f"Conflict check error: {e}")
            return "Unable to verify conflicts"
    
    def _validate_business_constraints(
        self, 
        appointment_datetime: datetime, 
        start_time: time, 
        end_time: time
    ) -> List[str]:
        """Validate business rules and constraints."""
        errors = []
        
        try:
            current_time = datetime.now()
            
            # Minimum advance booking (2 hours)
            if appointment_datetime < current_time + timedelta(hours=2):
                errors.append("Appointments must be booked at least 2 hours in advance")
            
            # Maximum advance booking (1 year)
            if appointment_datetime > current_time + timedelta(days=365):
                errors.append("Appointments cannot be booked more than 1 year in advance")
            
            # Business hours validation (8 AM to 10 PM)
            if start_time.hour < 8 or start_time.hour >= 22:
                errors.append("Appointments must be scheduled between 8 AM and 10 PM")
            
            if end_time.hour > 22 or (end_time.hour == 22 and end_time.minute > 0):
                errors.append("Appointments must end by 10 PM")
            
            # Duration limits
            duration_minutes = (
                datetime.combine(datetime.today(), end_time) - 
                datetime.combine(datetime.today(), start_time)
            ).total_seconds() / 60
            
            if duration_minutes < 15:
                errors.append("Appointments must be at least 15 minutes long")
            
            if duration_minutes > 240:  # 4 hours
                errors.append("Appointments cannot exceed 4 hours")
            
            # Weekend restrictions (example)
            if appointment_datetime.weekday() >= 5:  # Saturday or Sunday
                if start_time.hour < 9 or end_time.hour > 18:
                    errors.append("Weekend appointments must be between 9 AM and 6 PM")
            
            return errors
            
        except Exception as e:
            logger.error(f"Business constraint validation error: {e}")
            return ["Business rule validation failed"]
    
    def _validate_session_constraints(self, request_data: Dict[str, Any]) -> List[str]:
        """Validate session-specific constraints."""
        errors = []
        
        try:
            session_type = request_data.get('session_type', PackageType.INDIVIDUAL)
            max_participants = request_data.get('max_participants')
            location_type = request_data.get('location_type', LocationType.ONLINE)
            location_details = request_data.get('location_details')
            
            # Individual session constraints
            if session_type == PackageType.INDIVIDUAL:
                if max_participants and max_participants > 1:
                    errors.append("Individual sessions cannot have more than 1 participant")
            
            # Group session constraints
            elif session_type in [ServiceType.GROUP, ServiceType.TECFEE]:
                if not max_participants:
                    errors.append("Group sessions must specify maximum participants")
                elif max_participants < 2:
                    errors.append("Group sessions must allow at least 2 participants")
                elif max_participants > 20:
                    errors.append("Group sessions cannot exceed 20 participants")
            
            # Location constraints
            if location_type == LocationType.IN_PERSON:
                if not location_details or not location_details.strip():
                    errors.append("In-person sessions require location details")
            
            # TECFEE program constraints
            if session_type == ServiceType.TECFEE:
                subject_area = request_data.get('subject_area', '').lower()
                if 'tecfee' not in subject_area and 'french' not in subject_area:
                    errors.append("TECFEE sessions must be for French language preparation")
            
            return errors
            
        except Exception as e:
            logger.error(f"Session constraint validation error: {e}")
            return ["Session validation failed"]
    
    async def _validate_dependant_relationship(self, client_id: int, dependant_id: int) -> bool:
        """Validate that dependant belongs to client."""
        try:
            # This would check the dependant-parent relationship in the database
            # For now, return True as placeholder
            return True
            
        except Exception as e:
            logger.error(f"Dependant validation error: {e}")
            return False
    
    async def _validate_tutor_specialization(self, tutor: Any, subject_area: str) -> bool:
        """Validate tutor is qualified for the subject area."""
        try:
            # Check tutor's specializations/qualifications
            # For now, return True as placeholder
            return True
            
        except Exception as e:
            logger.error(f"Specialization validation error: {e}")
            return False
    
    def _validate_status_transition(
        self, 
        current_status: AppointmentStatus, 
        new_status: AppointmentStatus
    ) -> bool:
        """Validate appointment status transitions."""
        valid_transitions = {
            AppointmentStatus.SCHEDULED: [
                AppointmentStatus.CONFIRMED,
                AppointmentStatus.CANCELLED,
                AppointmentStatus.IN_PROGRESS
            ],
            AppointmentStatus.CONFIRMED: [
                AppointmentStatus.IN_PROGRESS,
                AppointmentStatus.CANCELLED,
                AppointmentStatus.SCHEDULED
            ],
            AppointmentStatus.IN_PROGRESS: [
                AppointmentStatus.COMPLETED,
                AppointmentStatus.CANCELLED
            ],
            AppointmentStatus.COMPLETED: [],  # No transitions from completed
            AppointmentStatus.CANCELLED: [
                AppointmentStatus.SCHEDULED  # Allow rescheduling
            ]
        }
        
        return new_status in valid_transitions.get(current_status, [])


class AppointmentSecurityValidator:
    """Security-focused validation for appointment operations."""
    
    @staticmethod
    def validate_rate_limits(user_id: int, operation: str) -> bool:
        """Validate rate limits for appointment operations."""
        # Implement rate limiting logic
        # For example: max 10 appointment creations per hour per user
        return True
    
    @staticmethod
    def validate_input_sanitization(data: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize input data to prevent injection attacks."""
        sanitized = {}
        
        for key, value in data.items():
            if isinstance(value, str):
                # Remove potential SQL injection characters
                sanitized[key] = value.replace("'", "").replace('"', '').replace(';', '').strip()
            else:
                sanitized[key] = value
        
        return sanitized
    
    @staticmethod
    def validate_data_access_patterns(user_id: int, requested_data: List[int]) -> bool:
        """Detect unusual data access patterns that might indicate abuse."""
        # Implement anomaly detection
        # For example: accessing too many different clients' data
        return True
    
    @staticmethod
    def log_security_event(user_id: int, operation: str, details: str):
        """Log security-relevant events for audit trail."""
        logger.warning(f"Security event - User {user_id}, Operation: {operation}, Details: {details}")


# Decorator for comprehensive appointment validation
def validate_appointment_operation(operation_type: str):
    """Decorator to add comprehensive validation to appointment endpoints."""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            try:
                # Extract request data and user info
                # This would be implemented based on FastAPI context
                
                # Perform validation based on operation type
                if operation_type == 'create':
                    # Run creation validation
                    pass
                elif operation_type == 'update':
                    # Run update validation
                    pass
                elif operation_type == 'delete':
                    # Run deletion validation
                    pass
                
                # Call original function
                return await func(*args, **kwargs)
                
            except ValidationError as e:
                raise HTTPException(status_code=400, detail=str(e))
            except Exception as e:
                logger.error(f"Validation decorator error: {e}")
                raise HTTPException(status_code=500, detail="Validation failed")
        
        return wrapper
    return decorator