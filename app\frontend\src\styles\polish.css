/* polish.css - Final refinements and edge case handling */

/* Perfect corners on pill buttons */
.btn-pill {
  border-radius: 9999px !important;
}

/* Smooth hover state for all interactive elements */
[role="button"],
button,
a,
.clickable {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Refined shadows for depth */
.shadow-ultra-soft {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
}

.shadow-glow-red {
  box-shadow: 0 0 20px rgba(220, 38, 38, 0.15);
}

.shadow-glow-gold {
  box-shadow: 0 0 20px rgba(251, 191, 36, 0.15);
}

/* Glass morphism refinements */
.glass-card {
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(31, 41, 55, 0.85);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Gradient text */
.gradient-text-red {
  background: linear-gradient(135deg, var(--color-accent-red) 0%, var(--color-accent-red-dark) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-gold {
  background: linear-gradient(135deg, var(--color-accent-gold) 0%, var(--color-accent-gold-dark) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Subtle gradients for backgrounds */
.gradient-bg-subtle {
  background: linear-gradient(135deg, var(--color-background-primary) 0%, var(--color-background-secondary) 100%);
}

.gradient-bg-red-subtle {
  background: linear-gradient(135deg, #fff5f5 0%, #fee0e0 100%);
}

/* Enhanced focus states */
*:focus-visible {
  outline: 2px solid var(--color-accent-red);
  outline-offset: 2px;
  border-radius: inherit;
}

/* Improved disabled states */
:disabled,
[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
  filter: grayscale(20%);
}

/* Selection colors */
::selection {
  background-color: var(--color-accent-red);
  color: white;
}

::-moz-selection {
  background-color: var(--color-accent-red);
  color: white;
}

/* Loading states */
.skeleton-loading {
  position: relative;
  overflow: hidden;
  background-color: var(--color-skeleton-base);
}

.skeleton-loading::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  transform: translateX(-100%);
  background: linear-gradient(
    90deg,
    transparent,
    var(--color-skeleton-shine),
    transparent
  );
  animation: shimmer 1.5s infinite;
  content: '';
}

/* Improved scroll behavior */
.smooth-scroll-container {
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: var(--color-accent-red) var(--color-background-secondary);
}

.smooth-scroll-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.smooth-scroll-container::-webkit-scrollbar-track {
  background: var(--color-background-secondary);
  border-radius: 4px;
}

.smooth-scroll-container::-webkit-scrollbar-thumb {
  background: var(--color-accent-red);
  border-radius: 4px;
}

.smooth-scroll-container::-webkit-scrollbar-thumb:hover {
  background: var(--color-accent-red-dark);
}

/* Image loading states */
.img-loading {
  background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
  animation: pulse 2s ease-in-out infinite;
}

/* Status indicators */
.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.status-online {
  background-color: var(--color-semantic-success);
  box-shadow: 0 0 0 2px rgba(52, 199, 89, 0.3);
}

.status-offline {
  background-color: var(--color-text-muted);
}

.status-busy {
  background-color: var(--color-accent-gold);
  box-shadow: 0 0 0 2px rgba(251, 191, 36, 0.3);
}

/* Notification badges */
.notification-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  min-width: 18px;
  height: 18px;
  padding: 0 6px;
  background-color: var(--color-accent-red);
  color: white;
  font-size: 11px;
  font-weight: 600;
  border-radius: 9px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(220, 38, 38, 0.3);
}

/* Hover lift effect */
.hover-lift {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-elevated);
}

/* Active press effect */
.active-press {
  transition: transform 0.1s ease;
}

.active-press:active {
  transform: scale(0.98);
}

/* Border animations */
.border-gradient {
  position: relative;
  background: white;
  border-radius: var(--radius-large);
}

.border-gradient::before {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: inherit;
  padding: 2px;
  background: linear-gradient(135deg, var(--color-accent-red), var(--color-accent-gold));
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.border-gradient:hover::before {
  opacity: 1;
}

/* Responsive typography scaling */
@media (max-width: 640px) {
  html {
    font-size: 14px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --color-accent-red: #dc2626;
    --color-text-primary: #000000;
    --color-background-primary: #ffffff;
  }
  
  .card {
    border: 2px solid var(--color-border-primary);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Dark mode preparation (future enhancement) */
@media (prefers-color-scheme: dark) {
  /* Dark mode styles can be added here in the future */
}

/* Print optimizations */
@media print {
  .no-print {
    display: none !important;
  }
  
  .page-break {
    page-break-after: always;
  }
  
  * {
    background: white !important;
    color: black !important;
  }
}