import React from 'react';
import { clsx } from 'clsx';
import { designTokens, componentTokens, ButtonVariant, Size } from '../../styles/design-tokens';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariant;
  size?: Size;
  pill?: boolean;
  loading?: boolean;
  isLoading?: boolean; // Support both loading and isLoading
  icon?: React.ReactNode;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  children: React.ReactNode;
}

export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  pill = true,
  loading = false,
  isLoading = false,
  icon,
  leftIcon,
  rightIcon,
  iconPosition = 'left',
  fullWidth = false,
  children,
  className,
  disabled,
  ...props
}) => {
  const baseStyles = 'inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2';
  
  const variantConfigs = componentTokens.button.variants;
  const variants = {
    primary: `bg-accent-highlight text-white hover:bg-accent-red shadow-soft hover:shadow-elevated hover:-translate-y-0.5 focus-visible:ring-accent-red`,
    secondary: `bg-background-tertiary text-text-primary hover:bg-background-secondary focus-visible:ring-accent-red`,
    ghost: `bg-transparent text-text-secondary hover:text-accent-red hover:bg-accent-red/[0.08] focus-visible:ring-accent-red`,
    error: `bg-semantic-error text-white hover:bg-red-700 shadow-soft hover:shadow-elevated focus-visible:ring-semantic-error`,
  };

  const sizeConfigs = componentTokens.button.sizes;
  const sizes = {
    sm: `px-4 py-2 text-sm gap-1.5`,
    md: `px-6 py-3 text-base gap-2`,
    lg: `px-8 py-4 text-lg gap-2.5`,
  };

  const iconSizes = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
  };

  const isDisabled = disabled || loading || isLoading;
  const showLoading = loading || isLoading;
  
  // Determine which icons to show
  const actualLeftIcon = leftIcon || (icon && iconPosition === 'left' ? icon : undefined);
  const actualRightIcon = rightIcon || (icon && iconPosition === 'right' ? icon : undefined);

  return (
    <button
      className={clsx(
        baseStyles,
        variants[variant],
        sizes[size],
        pill ? 'rounded-full' : 'rounded-xl',
        fullWidth && 'w-full',
        isDisabled && 'opacity-50 cursor-not-allowed',
        className
      )}
      disabled={isDisabled}
      {...props}
    >
      {showLoading ? (
        <>
          <svg
            className={clsx('animate-spin', iconSizes[size])}
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
          <span>Loading...</span>
        </>
      ) : (
        <>
          {actualLeftIcon && (
            <span className={iconSizes[size]}>{actualLeftIcon}</span>
          )}
          {children}
          {actualRightIcon && (
            <span className={iconSizes[size]}>{actualRightIcon}</span>
          )}
        </>
      )}
    </button>
  );
};

export default Button;