import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { I18nextProvider } from 'react-i18next';
import PerformanceReport from './PerformanceReport';
import i18n from '../../i18n';

// Mock recharts components
vi.mock('recharts', () => ({
  BarChart: ({ children, data }: any) => (
    <div data-testid="bar-chart" data-chart-data={JSON.stringify(data)}>
      {children}
    </div>
  ),
  Bar: ({ dataKey }: any) => <div data-testid={`bar-${dataKey}`} />,
  LineChart: ({ children, data }: any) => (
    <div data-testid="line-chart" data-chart-data={JSON.stringify(data)}>
      {children}
    </div>
  ),
  Line: ({ dataKey }: any) => <div data-testid={`line-${dataKey}`} />,
  RadarChart: ({ children, data }: any) => (
    <div data-testid="radar-chart" data-chart-data={JSON.stringify(data)}>
      {children}
    </div>
  ),
  Radar: ({ dataKey }: any) => <div data-testid={`radar-${dataKey}`} />,
  PolarGrid: () => <div data-testid="polar-grid" />,
  PolarAngleAxis: () => <div data-testid="polar-angle-axis" />,
  PolarRadiusAxis: () => <div data-testid="polar-radius-axis" />,
  XAxis: () => <div data-testid="x-axis" />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="cartesian-grid" />,
  Tooltip: () => <div data-testid="tooltip" />,
  Legend: () => <div data-testid="legend" />,
  ResponsiveContainer: ({ children }: any) => (
    <div data-testid="responsive-container">{children}</div>
  ),
}));

const renderPerformanceReport = () => {
  return render(
    <I18nextProvider i18n={i18n}>
      <PerformanceReport />
    </I18nextProvider>
  );
};

describe('PerformanceReport', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders performance report header', () => {
    renderPerformanceReport();
    
    expect(screen.getByText('Performance Report')).toBeInTheDocument();
  });

  it('displays key performance metrics', () => {
    renderPerformanceReport();
    
    expect(screen.getByText('Average Rating')).toBeInTheDocument();
    expect(screen.getByText('Session Completion')).toBeInTheDocument();
    expect(screen.getByText('Student Satisfaction')).toBeInTheDocument();
    expect(screen.getByText('Response Time')).toBeInTheDocument();
  });

  it('shows metric values with proper formatting', () => {
    renderPerformanceReport();
    
    expect(screen.getByText('4.8/5')).toBeInTheDocument(); // Average rating
    expect(screen.getByText('96.5%')).toBeInTheDocument(); // Completion rate
    expect(screen.getByText('94.2%')).toBeInTheDocument(); // Satisfaction
    expect(screen.getByText('2.3h')).toBeInTheDocument(); // Response time
  });

  it('displays performance trends', () => {
    renderPerformanceReport();
    
    expect(screen.getByText('+0.2')).toBeInTheDocument(); // Rating trend
    expect(screen.getByText('+1.5%')).toBeInTheDocument(); // Completion trend
    expect(screen.getByText('+2.1%')).toBeInTheDocument(); // Satisfaction trend
    expect(screen.getByText('-0.5h')).toBeInTheDocument(); // Response time improvement
  });

  it('renders tutor performance ranking chart', () => {
    renderPerformanceReport();
    
    expect(screen.getByText('Top Performing Tutors')).toBeInTheDocument();
    expect(screen.getByTestId('bar-chart')).toBeInTheDocument();
    expect(screen.getByTestId('bar-rating')).toBeInTheDocument();
  });

  it('displays session completion trends', () => {
    renderPerformanceReport();
    
    expect(screen.getByText('Session Completion Trends')).toBeInTheDocument();
    expect(screen.getByTestId('line-chart')).toBeInTheDocument();
    expect(screen.getByTestId('line-completed')).toBeInTheDocument();
    expect(screen.getByTestId('line-cancelled')).toBeInTheDocument();
  });

  it('shows subject performance breakdown', () => {
    renderPerformanceReport();
    
    expect(screen.getByText('Performance by Subject')).toBeInTheDocument();
    expect(screen.getByTestId('radar-chart')).toBeInTheDocument();
    expect(screen.getByTestId('radar-performance')).toBeInTheDocument();
  });

  it('displays tutor performance table', () => {
    renderPerformanceReport();
    
    expect(screen.getByText('Tutor Performance Details')).toBeInTheDocument();
    
    // Check table headers
    expect(screen.getByText('Tutor')).toBeInTheDocument();
    expect(screen.getByText('Sessions')).toBeInTheDocument();
    expect(screen.getByText('Rating')).toBeInTheDocument();
    expect(screen.getByText('Completion Rate')).toBeInTheDocument();
    expect(screen.getByText('Students')).toBeInTheDocument();
  });

  it('shows tutor performance data', () => {
    renderPerformanceReport();
    
    // Check for sample tutor data
    expect(screen.getByText('Marie Dubois')).toBeInTheDocument();
    expect(screen.getByText('John Smith')).toBeInTheDocument();
    expect(screen.getByText('Sophie Martin')).toBeInTheDocument();
    
    // Check performance metrics
    expect(screen.getByText('4.9')).toBeInTheDocument(); // Marie's rating
    expect(screen.getByText('98%')).toBeInTheDocument(); // John's completion rate
  });

  it('handles date range filtering', async () => {
    renderPerformanceReport();
    
    const dateSelector = screen.getByDisplayValue('Last 3 months');
    fireEvent.change(dateSelector, { target: { value: 'last6months' } });
    
    await waitFor(() => {
      expect(dateSelector).toHaveValue('last6months');
    });
  });

  it('filters by tutor category', async () => {
    renderPerformanceReport();
    
    const categoryFilter = screen.getByDisplayValue('All Tutors');
    fireEvent.change(categoryFilter, { target: { value: 'top-performers' } });
    
    await waitFor(() => {
      expect(categoryFilter).toHaveValue('top-performers');
    });
  });

  it('sorts tutor performance table', async () => {
    renderPerformanceReport();
    
    const ratingHeader = screen.getByText('Rating');
    fireEvent.click(ratingHeader);
    
    // Should trigger sorting functionality
    expect(ratingHeader).toBeInTheDocument();
  });

  it('displays performance badges', () => {
    renderPerformanceReport();
    
    // Check for performance status indicators
    const excellentBadge = screen.getByText('Excellent');
    const goodBadge = screen.getByText('Good');
    
    expect(excellentBadge).toBeInTheDocument();
    expect(goodBadge).toBeInTheDocument();
    
    expect(excellentBadge).toHaveClass('text-accent-green');
    expect(goodBadge).toHaveClass('text-accent-red');
  });

  it('shows student feedback summary', () => {
    renderPerformanceReport();
    
    expect(screen.getByText('Student Feedback Summary')).toBeInTheDocument();
    expect(screen.getByText('Positive Reviews')).toBeInTheDocument();
    expect(screen.getByText('Areas for Improvement')).toBeInTheDocument();
  });

  it('displays feedback categories', () => {
    renderPerformanceReport();
    
    expect(screen.getByText('Teaching Quality')).toBeInTheDocument();
    expect(screen.getByText('Communication')).toBeInTheDocument();
    expect(screen.getByText('Punctuality')).toBeInTheDocument();
    expect(screen.getByText('Subject Knowledge')).toBeInTheDocument();
  });

  it('handles performance alerts', () => {
    renderPerformanceReport();
    
    expect(screen.getByText('Performance Alerts')).toBeInTheDocument();
    expect(screen.getByText('2 tutors need attention')).toBeInTheDocument();
    expect(screen.getByText('1 tutor improved significantly')).toBeInTheDocument();
  });

  it('exports performance data', async () => {
    renderPerformanceReport();
    
    const exportButton = screen.getByRole('button', { name: /export/i });
    fireEvent.click(exportButton);
    
    // Should trigger export functionality
    expect(exportButton).toBeInTheDocument();
  });

  it('shows performance goals section', () => {
    renderPerformanceReport();
    
    expect(screen.getByText('Performance Goals')).toBeInTheDocument();
    expect(screen.getByText('Target Rating: 4.5+')).toBeInTheDocument();
    expect(screen.getByText('Target Completion: 95%+')).toBeInTheDocument();
    expect(screen.getByText('Target Response: <4h')).toBeInTheDocument();
  });

  it('displays goal achievement status', () => {
    renderPerformanceReport();
    
    // Check goal achievement indicators
    const achievedGoals = screen.getAllByTestId('check-circle');
    expect(achievedGoals.length).toBeGreaterThan(0);
  });

  it('handles tutor detail view', async () => {
    renderPerformanceReport();
    
    const tutorRow = screen.getByText('Marie Dubois').closest('tr');
    fireEvent.click(tutorRow!);
    
    await waitFor(() => {
      expect(screen.getByText('Tutor Performance Details')).toBeInTheDocument();
    });
  });

  it('shows session statistics', () => {
    renderPerformanceReport();
    
    expect(screen.getByText('Total Sessions: 1,234')).toBeInTheDocument();
    expect(screen.getByText('Completed: 1,191')).toBeInTheDocument();
    expect(screen.getByText('Cancelled: 43')).toBeInTheDocument();
  });

  it('displays monthly comparison', () => {
    renderPerformanceReport();
    
    expect(screen.getByText('Monthly Comparison')).toBeInTheDocument();
    expect(screen.getByText('This Month vs Last Month')).toBeInTheDocument();
  });

  it('handles performance insights', () => {
    renderPerformanceReport();
    
    expect(screen.getByText('Performance Insights')).toBeInTheDocument();
    expect(screen.getByText('Math tutors show highest satisfaction')).toBeInTheDocument();
    expect(screen.getByText('Morning sessions have better completion rates')).toBeInTheDocument();
  });

  it('shows tutor growth metrics', () => {
    renderPerformanceReport();
    
    expect(screen.getByText('Tutor Growth')).toBeInTheDocument();
    expect(screen.getByText('New Tutors: 5')).toBeInTheDocument();
    expect(screen.getByText('Improved Ratings: 8')).toBeInTheDocument();
  });

  it('displays performance by time period', () => {
    renderPerformanceReport();
    
    expect(screen.getByText('Performance by Time')).toBeInTheDocument();
    expect(screen.getByText('Best Performance: Weekdays 2-6 PM')).toBeInTheDocument();
  });

  it('handles loading states', async () => {
    renderPerformanceReport();
    
    // Should render without errors during loading
    expect(screen.getByText('Performance Report')).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.getByText('Average Rating')).toBeInTheDocument();
    });
  });
});