-- Migration: Add Two-Factor Authentication tables
-- Created: 2024-06-20
-- Description: Adds tables for 2FA setup, challenges, and trusted devices

-- Create 2FA methods enum
CREATE TYPE two_factor_method AS ENUM ('sms', 'email', 'totp');

-- Two-factor authentication setups
CREATE TABLE IF NOT EXISTS two_factor_setups (
    setup_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    method two_factor_method NOT NULL,
    secret TEXT NOT NULL, -- Encrypted secret for TOTP or backup codes hash
    backup_codes TEXT, -- Encrypted JSON array of backup codes
    phone_number VARCHAR(20), -- For SMS method
    email VARCHAR(255), -- For email method
    is_primary BOOLEAN DEFAULT true,
    is_enabled BOOLEAN DEFAULT false,
    verified_at TIMESTAMP WITH TIME ZONE,
    last_used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Constraints
    CONSTRAINT unique_user_method UNIQUE (user_id, method),
    CONSTRAINT phone_required_for_sms CHECK (
        method != 'sms' OR phone_number IS NOT NULL
    ),
    CONSTRAINT email_required_for_email CHECK (
        method != 'email' OR email IS NOT NULL
    )
);

-- Two-factor authentication challenges
CREATE TABLE IF NOT EXISTS two_factor_challenges (
    challenge_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    setup_id INTEGER NOT NULL REFERENCES two_factor_setups(setup_id) ON DELETE CASCADE,
    method two_factor_method NOT NULL,
    code TEXT NOT NULL, -- Hashed challenge code
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    attempts INTEGER DEFAULT 0,
    verified_at TIMESTAMP WITH TIME ZONE,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints and checks only
    CHECK (attempts >= 0)
);

-- Trusted devices for remember me functionality
CREATE TABLE IF NOT EXISTS trusted_devices (
    device_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    device_fingerprint TEXT NOT NULL, -- Hash of device info
    device_name VARCHAR(255),
    last_used_at TIMESTAMP WITH TIME ZONE NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Constraints
    CONSTRAINT unique_user_device UNIQUE (user_id, device_fingerprint)
);

-- Add 2FA requirement fields to user_accounts table
ALTER TABLE user_accounts 
ADD COLUMN IF NOT EXISTS two_factor_required BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS two_factor_grace_ends_at TIMESTAMP WITH TIME ZONE;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_2fa_setup_user ON two_factor_setups(user_id) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_2fa_setup_enabled ON two_factor_setups(user_id, is_enabled) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_2fa_setup_primary ON two_factor_setups(user_id, is_primary) WHERE deleted_at IS NULL AND is_enabled = true;

-- Additional indexes for challenges and devices
CREATE INDEX IF NOT EXISTS idx_challenge_user_expires ON two_factor_challenges(user_id, expires_at);
CREATE INDEX IF NOT EXISTS idx_challenge_verified ON two_factor_challenges(verified_at);
CREATE INDEX IF NOT EXISTS idx_device_user_expires ON trusted_devices(user_id, expires_at);
CREATE INDEX IF NOT EXISTS idx_device_fingerprint ON trusted_devices(device_fingerprint);

-- Add comments for documentation
COMMENT ON TABLE two_factor_setups IS 'Stores 2FA configuration for users';
COMMENT ON TABLE two_factor_challenges IS 'Temporary 2FA challenge codes sent to users';
COMMENT ON TABLE trusted_devices IS 'Devices that can skip 2FA for a period';

COMMENT ON COLUMN two_factor_setups.method IS 'Type of 2FA: sms, email, or totp (authenticator app)';
COMMENT ON COLUMN two_factor_setups.secret IS 'Encrypted secret key for TOTP or token generation';
COMMENT ON COLUMN two_factor_setups.backup_codes IS 'Encrypted JSON array of one-time backup codes';
COMMENT ON COLUMN two_factor_challenges.code IS 'Hashed challenge code sent to user';
COMMENT ON COLUMN trusted_devices.device_fingerprint IS 'SHA256 hash of device identifying information';

-- Function to clean up expired challenges
CREATE OR REPLACE FUNCTION cleanup_expired_2fa_challenges() RETURNS void AS $$
BEGIN
    DELETE FROM two_factor_challenges 
    WHERE expires_at < CURRENT_TIMESTAMP 
    AND verified_at IS NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to check if user needs 2FA
CREATE OR REPLACE FUNCTION user_requires_2fa(p_user_id INTEGER) RETURNS BOOLEAN AS $$
DECLARE
    v_role VARCHAR(50);
    v_has_2fa BOOLEAN;
    v_grace_ends TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Get user role and grace period from user_roles
    SELECT r.role_type INTO v_role
    FROM user_roles r 
    WHERE r.user_id = p_user_id 
    AND r.is_active = true
    LIMIT 1;
    
    -- Get grace period from user_accounts
    SELECT two_factor_grace_ends_at 
    INTO v_grace_ends
    FROM user_accounts
    WHERE user_id = p_user_id;
    
    -- Check if user has active 2FA
    SELECT EXISTS(
        SELECT 1 FROM two_factor_setups 
        WHERE user_id = p_user_id 
        AND is_enabled = true 
        AND deleted_at IS NULL
    ) INTO v_has_2fa;
    
    -- Managers always require 2FA
    IF v_role = 'manager' AND NOT v_has_2fa THEN
        RETURN v_grace_ends IS NULL OR v_grace_ends < CURRENT_TIMESTAMP;
    END IF;
    
    -- Other roles check the requirement flag
    SELECT two_factor_required INTO v_has_2fa
    FROM user_accounts WHERE user_id = p_user_id;
    
    RETURN v_has_2fa;
END;
$$ LANGUAGE plpgsql;

-- Rollback script
-- DROP FUNCTION IF EXISTS user_requires_2fa(INTEGER);
-- DROP FUNCTION IF EXISTS cleanup_expired_2fa_challenges();
-- DROP TABLE IF EXISTS trusted_devices;
-- DROP TABLE IF EXISTS two_factor_challenges;
-- DROP TABLE IF EXISTS two_factor_setups;
-- DROP TYPE IF EXISTS two_factor_method;
-- ALTER TABLE user_accounts DROP COLUMN IF EXISTS two_factor_required;
-- ALTER TABLE user_accounts DROP COLUMN IF EXISTS two_factor_grace_ends_at;