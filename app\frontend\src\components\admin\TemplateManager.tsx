import React, { useState, useEffect } from 'react';
import { 
  Mail, 
  MessageSquare, 
  Eye, 
  Edit3, 
  Save, 
  X, 
  AlertCircle, 
  AlertTriangle,
  CheckCircle,
  FileText,
  Send,
  Copy,
  Globe
} from 'lucide-react';
import { useTranslation } from '../../hooks/useTranslation';

interface TemplateInfo {
  name: string;
  category: string;
  type: 'email' | 'sms';
  languages: string[];
  variables: string[];
  description: string;
  subject_template?: string;
}

interface TemplateContent {
  template_name: string;
  language: string;
  content: string;
  subject?: string;
  variables_used: string[];
}

export const TemplateManager: React.FC = () => {
  const { t } = useTranslation();
  
  const [templates, setTemplates] = useState<TemplateInfo[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<TemplateInfo | null>(null);
  const [templateContent, setTemplateContent] = useState<Record<string, TemplateContent>>({});
  const [editingLanguage, setEditingLanguage] = useState<string | null>(null);
  const [previewData, setPreviewData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [filter, setFilter] = useState<'all' | 'email' | 'sms'>('all');

  // Mock data - in real implementation, this would come from API
  useEffect(() => {
    // Simulate loading templates
    const mockTemplates: TemplateInfo[] = [
      {
        name: 'email_verification',
        category: 'authentication',
        type: 'email',
        languages: ['en', 'fr'],
        variables: ['company_name', 'verify_link', 'support_email', 'user_email', 'year'],
        description: 'Email verification template for new user accounts',
        subject_template: 'Verify Your Email - {{ company_name }}'
      },
      {
        name: 'appointment_reminder',
        category: 'appointments',
        type: 'email',
        languages: ['en', 'fr'],
        variables: ['appointment_date', 'appointment_time', 'tutor_name', 'subject', 'location', 'confirm_link'],
        description: '24-hour appointment reminder email',
        subject_template: 'Appointment Reminder - {{ appointment_date }}'
      },
      {
        name: 'appointment_reminder',
        category: 'appointments',
        type: 'sms',
        languages: ['en', 'fr'],
        variables: ['tutor_name', 'appointment_time', 'location', 'confirm_link'],
        description: '24-hour appointment reminder SMS'
      },
      {
        name: 'tutor_invitation',
        category: 'system',
        type: 'email',
        languages: ['en', 'fr'],
        variables: ['tutor_name', 'invitation_link', 'expiry_days', 'company_name'],
        description: 'Tutor invitation email template',
        subject_template: 'Join Our Tutoring Team - {{ company_name }}'
      },
      {
        name: 'invoice',
        category: 'billing',
        type: 'email',
        languages: ['en', 'fr'],
        variables: ['invoice_number', 'client_name', 'total_amount', 'due_date', 'payment_link'],
        description: 'Invoice email with Quebec tax formatting',
        subject_template: 'Invoice #{{ invoice_number }} - {{ company_name }}'
      }
    ];

    setTemplates(mockTemplates);
    setLoading(false);
  }, []);

  const filteredTemplates = templates.filter(template => 
    filter === 'all' || template.type === filter
  );

  const loadTemplateContent = async (template: TemplateInfo) => {
    // Mock loading template content
    const mockContent: Record<string, TemplateContent> = {
      'en': {
        template_name: template.name,
        language: 'en',
        content: template.type === 'email' ? 
          `<h1>{{ company_name }}</h1>\n<p>Hello {{ user_name }},</p>\n<p>This is a sample template with variables.</p>` :
          `📅 REMINDER: Your session with {{ tutor_name }} is tomorrow at {{ appointment_time }}.\n\nConfirm: {{ confirm_link }}`,
        subject: template.subject_template,
        variables_used: template.variables
      },
      'fr': {
        template_name: template.name,
        language: 'fr',
        content: template.type === 'email' ? 
          `<h1>{{ company_name }}</h1>\n<p>Bonjour {{ user_name }},</p>\n<p>Ceci est un modèle d'exemple avec des variables.</p>` :
          `📅 RAPPEL: Votre séance avec {{ tutor_name }} est demain à {{ appointment_time }}.\n\nConfirmer: {{ confirm_link }}`,
        subject: template.subject_template?.replace('Verify Your Email', 'Vérifiez votre courriel'),
        variables_used: template.variables
      }
    };

    setTemplateContent(mockContent);
  };

  const handleTemplateSelect = (template: TemplateInfo) => {
    setSelectedTemplate(template);
    setEditingLanguage(null);
    loadTemplateContent(template);
  };

  const handleEdit = (language: string) => {
    setEditingLanguage(language);
  };

  const handleSave = async () => {
    setSaving(true);
    // Mock save operation
    setTimeout(() => {
      setSaving(false);
      setEditingLanguage(null);
    }, 1000);
  };

  const handleCancel = () => {
    setEditingLanguage(null);
    // Reload original content
    if (selectedTemplate) {
      loadTemplateContent(selectedTemplate);
    }
  };

  const generatePreview = async (template: TemplateInfo, language: string) => {
    // Mock preview data
    const sampleData = {
      company_name: 'TutorAide',
      user_name: language === 'fr' ? 'Jean Dupont' : 'John Doe',
      tutor_name: language === 'fr' ? 'Marie Tremblay' : 'Sarah Johnson',
      appointment_date: language === 'fr' ? '15 juin 2024' : 'June 15, 2024',
      appointment_time: language === 'fr' ? '14h00' : '2:00 PM',
      location: language === 'fr' ? 'Bibliothèque centrale' : 'Central Library',
      confirm_link: 'https://tutoraide.ca/confirm/abc123',
      verify_link: 'https://tutoraide.ca/verify/xyz789',
      support_email: '<EMAIL>',
      user_email: language === 'fr' ? '<EMAIL>' : '<EMAIL>'
    };

    setPreviewData({ template: template.name, language, data: sampleData });
  };

  const renderVariableList = (variables: string[]) => (
    <div className="mt-4">
      <h4 className="text-sm font-medium text-gray-700 mb-2">Available Variables:</h4>
      <div className="flex flex-wrap gap-2">
        {variables.map(variable => (
          <button
            key={variable}
            onClick={() => {
              // Copy variable to clipboard
              navigator.clipboard.writeText(`{{ ${variable} }}`);
            }}
            className="inline-flex items-center gap-1 px-2 py-1 bg-red-100 text-red-800 text-xs rounded hover:bg-red-200 transition-colors"
          >
            <span>{`{{ ${variable} }}`}</span>
            <Copy className="w-3 h-3" />
          </button>
        ))}
      </div>
    </div>
  );

  const validateTemplate = (content: string, variables: string[]) => {
    const issues: string[] = [];
    
    // Check for undefined variables
    const usedVariables = [...content.matchAll(/\{\{\s*(\w+)\s*\}\}/g)].map(match => match[1]);
    const undefinedVars = usedVariables.filter(v => !variables.includes(v));
    if (undefinedVars.length > 0) {
      issues.push(`Undefined variables: ${undefinedVars.join(', ')}`);
    }

    // Check for missing required variables (example)
    const requiredVars = ['company_name'];
    const missingRequired = requiredVars.filter(v => !usedVariables.includes(v));
    if (missingRequired.length > 0) {
      issues.push(`Missing required variables: ${missingRequired.join(', ')}`);
    }

    return issues;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
        <span className="ml-3 text-gray-600">{t('common.loading')}</span>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="grid grid-cols-12 gap-6 h-[calc(100vh-200px)]">
        {/* Template List */}
        <div className="col-span-4 bg-white rounded-lg border border-gray-200 p-4 overflow-y-auto">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Templates</h2>
            
            {/* Filter */}
            <div className="flex rounded-lg border border-gray-200 overflow-hidden">
              {(['all', 'email', 'sms'] as const).map(type => (
                <button
                  key={type}
                  onClick={() => setFilter(type)}
                  className={`px-3 py-1 text-xs font-medium transition-colors ${
                    filter === type
                      ? 'bg-red-600 text-white'
                      : 'bg-white text-gray-600 hover:bg-gray-50'
                  }`}
                >
                  {type === 'all' ? 'All' : type.toUpperCase()}
                </button>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            {filteredTemplates.map(template => (
              <button
                key={`${template.name}_${template.type}`}
                onClick={() => handleTemplateSelect(template)}
                className={`w-full text-left p-3 rounded-lg border transition-colors ${
                  selectedTemplate?.name === template.name && selectedTemplate?.type === template.type
                    ? 'border-red-500 bg-red-50'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                }`}
              >
                <div className="flex items-start gap-2">
                  {template.type === 'email' ? (
                    <Mail className="w-4 h-4 text-red-600 mt-0.5 flex-shrink-0" />
                  ) : (
                    <MessageSquare className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                  )}
                  <div className="min-w-0 flex-1">
                    <div className="font-medium text-gray-900 truncate">
                      {template.name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </div>
                    <div className="text-xs text-gray-600 mt-1">
                      {template.category} • {template.languages.join(', ')}
                    </div>
                    <div className="text-xs text-gray-500 mt-1 line-clamp-2">
                      {template.description}
                    </div>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Template Editor */}
        <div className="col-span-8 bg-white rounded-lg border border-gray-200 overflow-hidden">
          {selectedTemplate ? (
            <div className="h-full flex flex-col">
              {/* Header */}
              <div className="p-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {selectedTemplate.type === 'email' ? (
                      <Mail className="w-5 h-5 text-red-600" />
                    ) : (
                      <MessageSquare className="w-5 h-5 text-green-600" />
                    )}
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">
                        {selectedTemplate.name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </h3>
                      <p className="text-sm text-gray-600">{selectedTemplate.description}</p>
                    </div>
                  </div>
                  
                  {editingLanguage && (
                    <div className="flex items-center gap-2">
                      <button
                        onClick={handleSave}
                        disabled={saving}
                        className="flex items-center gap-2 px-3 py-1.5 bg-green-600 text-white rounded-lg text-sm font-medium hover:bg-green-700 transition-colors disabled:opacity-50"
                      >
                        <Save className="w-4 h-4" />
                        {saving ? 'Saving...' : 'Save'}
                      </button>
                      <button
                        onClick={handleCancel}
                        className="flex items-center gap-2 px-3 py-1.5 bg-gray-600 text-white rounded-lg text-sm font-medium hover:bg-gray-700 transition-colors"
                      >
                        <X className="w-4 h-4" />
                        Cancel
                      </button>
                    </div>
                  )}
                </div>
              </div>

              {/* Language Tabs */}
              <div className="flex border-b border-gray-200">
                {selectedTemplate.languages.map(language => (
                  <button
                    key={language}
                    onClick={() => editingLanguage !== language && setEditingLanguage(null)}
                    className={`flex items-center gap-2 px-4 py-3 border-b-2 font-medium text-sm transition-colors ${
                      Object.keys(templateContent).length > 0
                        ? 'text-gray-700 border-transparent hover:text-gray-900 hover:border-gray-300'
                        : 'text-gray-400 border-transparent'
                    }`}
                  >
                    <Globe className="w-4 h-4" />
                    {language === 'en' ? 'English' : 'Français (Québec)'}
                  </button>
                ))}
              </div>

              {/* Content Editor */}
              <div className="flex-1 overflow-hidden">
                <div className="grid grid-cols-2 h-full">
                  {selectedTemplate.languages.map(language => {
                    const content = templateContent[language];
                    const isEditing = editingLanguage === language;
                    const validationIssues = content ? validateTemplate(content.content, selectedTemplate.variables) : [];

                    return (
                      <div key={language} className="border-r border-gray-200 last:border-r-0 flex flex-col">
                        {/* Language Header */}
                        <div className="p-3 bg-gray-50 border-b border-gray-200 flex items-center justify-between">
                          <span className="font-medium text-gray-900">
                            {language === 'en' ? 'English' : 'Français (Québec)'}
                          </span>
                          <div className="flex items-center gap-2">
                            {validationIssues.length > 0 && (
                              <AlertCircle className="w-4 h-4 text-red-500" />
                            )}
                            <button
                              onClick={() => generatePreview(selectedTemplate, language)}
                              className="flex items-center gap-1 px-2 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
                            >
                              <Eye className="w-3 h-3" />
                              Preview
                            </button>
                            <button
                              onClick={() => handleEdit(language)}
                              disabled={isEditing}
                              className="flex items-center gap-1 px-2 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors disabled:opacity-50"
                            >
                              <Edit3 className="w-3 h-3" />
                              Edit
                            </button>
                          </div>
                        </div>

                        {/* Content */}
                        <div className="flex-1 p-4 overflow-y-auto">
                          {content ? (
                            <div className="space-y-4">
                              {/* Subject (for emails) */}
                              {selectedTemplate.type === 'email' && content.subject && (
                                <div>
                                  <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Subject:
                                  </label>
                                  {isEditing ? (
                                    <input
                                      type="text"
                                      value={content.subject}
                                      onChange={(e) => setTemplateContent(prev => ({
                                        ...prev,
                                        [language]: { ...content, subject: e.target.value }
                                      }))}
                                      className="w-full p-2 border border-gray-300 rounded text-sm"
                                    />
                                  ) : (
                                    <div className="p-2 bg-gray-50 rounded text-sm">
                                      {content.subject}
                                    </div>
                                  )}
                                </div>
                              )}

                              {/* Content */}
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                  Content:
                                </label>
                                {isEditing ? (
                                  <textarea
                                    value={content.content}
                                    onChange={(e) => setTemplateContent(prev => ({
                                      ...prev,
                                      [language]: { ...content, content: e.target.value }
                                    }))}
                                    className="w-full h-64 p-3 border border-gray-300 rounded text-sm font-mono"
                                    style={{ resize: 'vertical' }}
                                  />
                                ) : (
                                  <div className="p-3 bg-gray-50 rounded text-sm font-mono whitespace-pre-wrap border min-h-64">
                                    {content.content}
                                  </div>
                                )}
                              </div>

                              {/* Validation Issues */}
                              {validationIssues.length > 0 && (
                                <div className="p-3 bg-red-50 border border-red-200 rounded">
                                  <div className="flex items-center gap-2 mb-2">
                                    <AlertTriangle className="w-4 h-4 text-red-600" />
                                    <span className="text-sm font-medium text-red-800">Validation Issues</span>
                                  </div>
                                  <ul className="text-sm text-red-700 space-y-1">
                                    {validationIssues.map((issue, index) => (
                                      <li key={index}>• {issue}</li>
                                    ))}
                                  </ul>
                                </div>
                              )}

                              {/* Variables */}
                              {renderVariableList(selectedTemplate.variables)}
                            </div>
                          ) : (
                            <div className="flex items-center justify-center h-full text-gray-500">
                              Loading content...
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center h-full text-gray-500">
              <div className="text-center">
                <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Select a Template</h3>
                <p className="text-gray-600">
                  Choose a template from the list to start editing
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Preview Modal */}
      {previewData && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden">
            <div className="flex items-center justify-between p-4 border-b">
              <h3 className="text-lg font-semibold">Template Preview</h3>
              <button
                onClick={() => setPreviewData(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            <div className="p-4 overflow-y-auto">
              <div className="space-y-4">
                <div className="text-sm text-gray-600">
                  Template: {previewData.template} ({previewData.language})
                </div>
                {/* Preview content would be rendered here with sample data */}
                <div className="p-4 bg-gray-50 rounded border">
                  <div className="text-sm font-mono">
                    Preview will be generated with sample data...
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
