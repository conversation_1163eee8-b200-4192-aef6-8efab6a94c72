-- Rollback: <PERSON><PERSON> Profile Enhancements

-- Drop views
DROP VIEW IF EXISTS tutor_verification_status;

-- Drop triggers
DROP TRIGGER IF EXISTS update_completeness_on_tutor_documents_change ON tutor_documents;
DROP TRIGGER IF EXISTS update_completeness_on_tutor_specializations_change ON tutor_specializations;
DROP TRIGGER IF EXISTS update_completeness_on_tutor_experience_change ON tutor_experience;
DROP TRIGGER IF EXISTS update_completeness_on_tutor_education_change ON tutor_education;

DROP TRIGGER IF EXISTS update_tutor_performance_metrics_modtime ON tutor_performance_metrics;
DROP TRIGGER IF EXISTS update_tutor_specializations_modtime ON tutor_specializations;
DROP TRIGGER IF EXISTS update_tutor_experience_modtime ON tutor_experience;
DROP TRIGGER IF EXISTS update_tutor_education_modtime ON tutor_education;
DROP TRIGGER IF EXISTS update_tutor_documents_modtime ON tutor_documents;

-- Drop functions
DROP FUNCTION IF EXISTS update_tutor_profile_completeness();
DROP FUNCTION IF EXISTS calculate_tutor_profile_completeness(INTEGER);

-- Drop indexes
DROP INDEX IF EXISTS idx_tutor_performance_metrics_period;
DROP INDEX IF EXISTS idx_tutor_performance_metrics_tutor_id;
DROP INDEX IF EXISTS idx_tutor_verification_log_tutor_id;
DROP INDEX IF EXISTS idx_tutor_specializations_subject;
DROP INDEX IF EXISTS idx_tutor_specializations_tutor_id;
DROP INDEX IF EXISTS idx_tutor_experience_tutor_id;
DROP INDEX IF EXISTS idx_tutor_education_tutor_id;
DROP INDEX IF EXISTS idx_tutor_documents_verification_status;
DROP INDEX IF EXISTS idx_tutor_documents_type;
DROP INDEX IF EXISTS idx_tutor_documents_tutor_id;

-- Drop tables
DROP TABLE IF EXISTS tutor_performance_metrics;
DROP TABLE IF EXISTS tutor_verification_log;
DROP TABLE IF EXISTS tutor_specializations;
DROP TABLE IF EXISTS tutor_experience;
DROP TABLE IF EXISTS tutor_education;
DROP TABLE IF EXISTS tutor_documents;

-- Remove columns from tutor_profiles
ALTER TABLE tutor_profiles 
DROP COLUMN IF EXISTS headline,
DROP COLUMN IF EXISTS professional_summary,
DROP COLUMN IF EXISTS teaching_philosophy,
DROP COLUMN IF EXISTS linkedin_url,
DROP COLUMN IF EXISTS website_url,
DROP COLUMN IF EXISTS years_of_experience,
DROP COLUMN IF EXISTS total_students_taught,
DROP COLUMN IF EXISTS preferred_student_age_min,
DROP COLUMN IF EXISTS preferred_student_age_max,
DROP COLUMN IF EXISTS accepts_special_needs,
DROP COLUMN IF EXISTS special_needs_experience,
DROP COLUMN IF EXISTS languages_spoken,
DROP COLUMN IF EXISTS availability_notes,
DROP COLUMN IF EXISTS background_check_status,
DROP COLUMN IF EXISTS background_check_date,
DROP COLUMN IF EXISTS background_check_expiry,
DROP COLUMN IF EXISTS profile_completeness,
DROP COLUMN IF EXISTS profile_views,
DROP COLUMN IF EXISTS last_active_date,
DROP COLUMN IF EXISTS is_featured,
DROP COLUMN IF EXISTS featured_until;