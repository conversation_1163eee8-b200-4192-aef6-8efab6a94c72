# Tutor Access Restrictions

## Overview
When logged in as a TUTOR role, users have restricted access to ensure they only see their assigned clients, their own appointments, and their earnings.

## Implemented Restrictions

### 1. Sidebar Navigation
- **Visible Sections for Tutors:**
  - Services (view available services)
  - Users → My Clients (only their assigned clients)
  - Calendar (only their appointments)
  - Tutor Map
  - Payment History (their earnings)
  - Messages → Chat
  - Settings → Notifications

- **Hidden Sections for Tu<PERSON>:**
  - All Billing sections (invoices, subscriptions, packages)
  - Reports (all)
  - Messages → SMS Threads, Templates, Broadcasts
  - Settings → System, Users, Payments, API, Services
  - Users → Tutors, Dependants

### 2. Data Filtering

#### Client Access
- Only see clients assigned to them
- Can view client basic information
- Cannot edit client information
- Cannot see client financial information
- Cannot add or delete clients

#### Calendar/Appointments
- Only see their own appointments
- Cannot see other tutors' schedules
- Can confirm appointments
- Can view their schedule
- Cannot see all appointments

#### Payment History
- See their total earnings
- See hours worked
- See average hourly rate
- View pending payments
- Download payment statements
- View payment history by period
- Cannot see client invoices
- Cannot see what clients pay (only what they earn)

### 3. Actions Restricted

#### Cannot:
- Add or edit users
- View or manage invoices
- See client billing information
- Access financial reports
- Send broadcast messages
- Access system settings
- View other tutors' information
- See company-wide statistics

#### Can:
- View assigned clients
- Message clients via chat
- View their appointment schedule
- Confirm appointments
- View their payment history
- Download payment statements
- Update notification preferences
- View their earnings and hours

## Implementation Details

### Permission System Updates
```typescript
// Tutor-specific permissions added:
viewAssignedClients: { roles: [UserRoleType.TUTOR] }
viewOwnPayments: { roles: [UserRoleType.TUTOR] }
confirmAppointment: { roles: [UserRoleType.TUTOR] }
viewTutorSchedule: { roles: [UserRoleType.TUTOR] }
```

### Data Filtering Examples

#### Clients Page
```typescript
// Filter clients for tutors
if (isTutor) {
  const tutorId = user?.userId || 101;
  filtered = clients.filter(client => 
    client.assignedTutorIds?.includes(tutorId)
  );
}
```

#### Calendar
```typescript
// Filter appointments for tutors
if (isTutor) {
  const tutorId = user?.userId || 1;
  filtered = filtered.filter(apt => apt.tutorId === tutorId);
}
```

### Payment History Features
- **Total Earned**: Shows cumulative earnings
- **Total Hours**: Shows total hours worked
- **Average Rate**: Calculated average hourly rate
- **Pending Amount**: Shows unpaid earnings
- **Payment Details**: Period, hours, amount, status
- **Download Options**: Payment statements and annual reports

## Testing Tutor Access

1. Login with a TUTOR role account
2. Verify sidebar shows only allowed sections
3. Check that:
   - Clients page shows only assigned clients
   - Calendar shows only their appointments
   - Payment History shows their earnings
   - Cannot access billing/invoices
   - Cannot see admin features

## Key Business Rules

1. **Payment Transparency**: Tutors see what they earn, not what clients pay
2. **Client Privacy**: Tutors only see their assigned clients
3. **Schedule Isolation**: Tutors only see their own schedule
4. **Financial Separation**: No access to invoicing or client billing

## Future Enhancements

1. Add performance metrics for tutors
2. Implement availability management
3. Add session notes and progress tracking
4. Enable tutor-to-tutor substitution requests
5. Add training resources section