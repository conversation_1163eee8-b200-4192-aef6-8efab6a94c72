/**
 * Formatted Display Components
 * 
 * Pre-built components that automatically format common data types
 * according to the user's locale and Quebec preferences.
 */

import React from 'react';
import { useFormatting } from '../../hooks/useFormatting';
import { AddressFormat, PhoneFormat } from '../../services/formatters';

interface FormattedCurrencyProps {
  amount: number;
  showCode?: boolean;
  precision?: number;
  className?: string;
  'aria-label'?: string;
}

export const FormattedCurrency: React.FC<FormattedCurrencyProps> = ({
  amount,
  showCode = false,
  precision = 2,
  className = '',
  'aria-label': ariaLabel
}) => {
  const { formatCurrency, formatForAccessibility } = useFormatting();
  
  const formatted = formatCurrency(amount, { showCode, precision });
  const accessibleText = ariaLabel || formatForAccessibility(formatted, 'currency');
  
  return (
    <span className={className} aria-label={accessibleText}>
      {formatted}
    </span>
  );
};

interface FormattedDateProps {
  date: Date | string | number;
  format?: 'short' | 'medium' | 'long' | 'full' | string;
  includeTime?: boolean;
  timeFormat24h?: boolean;
  relative?: boolean;
  className?: string;
  'aria-label'?: string;
}

export const FormattedDate: React.FC<FormattedDateProps> = ({
  date,
  format = 'medium',
  includeTime = false,
  timeFormat24h,
  relative = false,
  className = '',
  'aria-label': ariaLabel
}) => {
  const { 
    formatDate, 
    formatDateTime, 
    formatRelativeTime, 
    formatForAccessibility 
  } = useFormatting();
  
  let formatted: string;
  
  if (relative) {
    formatted = formatRelativeTime(date);
  } else if (includeTime) {
    formatted = formatDateTime(date, format as any, timeFormat24h);
  } else {
    formatted = formatDate(date, format);
  }
  
  const accessibleText = ariaLabel || formatForAccessibility(formatted, 'date');
  
  return (
    <time 
      className={className} 
      dateTime={new Date(date).toISOString()}
      aria-label={accessibleText}
    >
      {formatted}
    </time>
  );
};

interface FormattedNumberProps {
  value: number;
  style?: 'decimal' | 'percent';
  precision?: number;
  abbreviate?: boolean;
  className?: string;
  'aria-label'?: string;
}

export const FormattedNumber: React.FC<FormattedNumberProps> = ({
  value,
  style = 'decimal',
  precision,
  abbreviate = false,
  className = '',
  'aria-label': ariaLabel
}) => {
  const { 
    formatNumber, 
    formatLargeNumber, 
    formatPercentage, 
    formatForAccessibility 
  } = useFormatting();
  
  let formatted: string;
  
  if (style === 'percent') {
    formatted = formatPercentage(value, precision);
  } else if (abbreviate) {
    formatted = formatLargeNumber(value);
  } else {
    formatted = formatNumber(value, { precision, style });
  }
  
  const accessibleText = ariaLabel || formatForAccessibility(formatted, 'number');
  
  return (
    <span className={className} aria-label={accessibleText}>
      {formatted}
    </span>
  );
};

interface FormattedPhoneProps {
  phone: string | PhoneFormat;
  international?: boolean;
  showQuebecBadge?: boolean;
  className?: string;
  'aria-label'?: string;
}

export const FormattedPhone: React.FC<FormattedPhoneProps> = ({
  phone,
  international = false,
  showQuebecBadge = false,
  className = '',
  'aria-label': ariaLabel
}) => {
  const { 
    formatPhone, 
    isQuebecAreaCode, 
    formatForAccessibility,
    currentLocale 
  } = useFormatting();
  
  const phoneStr = typeof phone === 'string' ? phone : phone.number;
  const formatted = formatPhone(phone, international);
  const isQuebec = showQuebecBadge && isQuebecAreaCode(phoneStr);
  const accessibleText = ariaLabel || formatForAccessibility(formatted, 'phone');
  
  return (
    <span className={`inline-flex items-center gap-2 ${className}`}>
      <span aria-label={accessibleText}>{formatted}</span>
      {isQuebec && (
        <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
          QC
        </span>
      )}
    </span>
  );
};

interface FormattedAddressProps {
  address: AddressFormat;
  multiline?: boolean;
  showCountry?: boolean;
  className?: string;
  'aria-label'?: string;
}

export const FormattedAddress: React.FC<FormattedAddressProps> = ({
  address,
  multiline = false,
  showCountry = false,
  className = '',
  'aria-label': ariaLabel
}) => {
  const { formatAddress, formatPostalCode } = useFormatting();
  
  const addressWithCountry = showCountry 
    ? { ...address, country: address.country || 'Canada' }
    : address;
    
  const formatted = formatAddress(addressWithCountry, multiline);
  
  return (
    <address 
      className={className}
      aria-label={ariaLabel || formatted}
    >
      {multiline ? (
        formatted.split('\n').map((line, index) => (
          <div key={index}>{line}</div>
        ))
      ) : (
        formatted
      )}
    </address>
  );
};

// Business-specific formatted components for TutorAide

interface FormattedSessionDurationProps {
  minutes: number;
  className?: string;
}

export const FormattedSessionDuration: React.FC<FormattedSessionDurationProps> = ({
  minutes,
  className = ''
}) => {
  const { formatSessionDuration } = useFormatting();
  
  return (
    <span className={className}>
      {formatSessionDuration(minutes)}
    </span>
  );
};

interface FormattedAppointmentTimeProps {
  startTime: Date | string;
  endTime: Date | string;
  format24h?: boolean;
  className?: string;
}

export const FormattedAppointmentTime: React.FC<FormattedAppointmentTimeProps> = ({
  startTime,
  endTime,
  format24h,
  className = ''
}) => {
  const { formatAppointmentTime } = useFormatting();
  
  return (
    <span className={className}>
      {formatAppointmentTime(startTime, endTime, format24h)}
    </span>
  );
};

interface FormattedGradeProps {
  grade: string | number;
  className?: string;
}

export const FormattedGrade: React.FC<FormattedGradeProps> = ({
  grade,
  className = ''
}) => {
  const { formatGrade } = useFormatting();
  
  return (
    <span className={className}>
      {formatGrade(grade)}
    </span>
  );
};

interface FormattedTutorRateProps {
  rate: number;
  per?: 'hour' | 'session';
  className?: string;
}

export const FormattedTutorRate: React.FC<FormattedTutorRateProps> = ({
  rate,
  per = 'hour',
  className = ''
}) => {
  const { formatTutorRate } = useFormatting();
  
  return (
    <span className={className}>
      {formatTutorRate(rate, per)}
    </span>
  );
};

interface FormattedDistanceProps {
  kilometers: number;
  className?: string;
}

export const FormattedDistance: React.FC<FormattedDistanceProps> = ({
  kilometers,
  className = ''
}) => {
  const { formatDistance } = useFormatting();
  
  return (
    <span className={className}>
      {formatDistance(kilometers)}
    </span>
  );
};

interface FormattedSubjectsProps {
  subjects: string[];
  maxDisplay?: number;
  className?: string;
}

export const FormattedSubjects: React.FC<FormattedSubjectsProps> = ({
  subjects,
  maxDisplay = 3,
  className = ''
}) => {
  const { formatSubjects, currentLocale } = useFormatting();
  
  const displaySubjects = subjects.slice(0, maxDisplay);
  const hasMore = subjects.length > maxDisplay;
  const remainingCount = subjects.length - maxDisplay;
  
  return (
    <span className={className}>
      {formatSubjects(displaySubjects)}
      {hasMore && (
        <span className="text-gray-500 ml-1">
          {currentLocale === 'fr' ? `et ${remainingCount} autres` : `and ${remainingCount} more`}
        </span>
      )}
    </span>
  );
};

interface FormattedInvoiceNumberProps {
  invoiceId: number;
  prefix?: string;
  className?: string;
}

export const FormattedInvoiceNumber: React.FC<FormattedInvoiceNumberProps> = ({
  invoiceId,
  prefix = 'INV',
  className = ''
}) => {
  const { formatInvoiceNumber } = useFormatting();
  
  return (
    <span className={`font-mono ${className}`}>
      {formatInvoiceNumber(invoiceId, prefix)}
    </span>
  );
};

interface FormattedTaxesProps {
  amount: number;
  quebecTax?: number;
  federalTax?: number;
  className?: string;
}

export const FormattedTaxes: React.FC<FormattedTaxesProps> = ({
  amount,
  quebecTax,
  federalTax,
  className = ''
}) => {
  const { formatTaxes } = useFormatting();
  
  return (
    <span className={className}>
      {formatTaxes(amount, quebecTax, federalTax)}
    </span>
  );
};

// Input components with formatting

interface CurrencyInputProps {
  value: number;
  onChange: (value: number) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export const CurrencyInput: React.FC<CurrencyInputProps> = ({
  value,
  onChange,
  placeholder,
  className = '',
  disabled = false
}) => {
  const { formatCurrency, getSeparators } = useFormatting();
  const separators = getSeparators();
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    // Remove currency symbols and convert to number
    const cleanValue = inputValue
      .replace(/[$\s]/g, '')
      .replace(separators.thousands, '')
      .replace(separators.decimal, '.');
    
    const numValue = parseFloat(cleanValue) || 0;
    onChange(numValue);
  };
  
  return (
    <input
      type="text"
      value={value > 0 ? formatCurrency(value) : ''}
      onChange={handleChange}
      placeholder={placeholder}
      className={className}
      disabled={disabled}
    />
  );
};

interface PostalCodeInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export const PostalCodeInput: React.FC<PostalCodeInputProps> = ({
  value,
  onChange,
  placeholder,
  className = '',
  disabled = false
}) => {
  const { formatPostalCode, validatePostalCode } = useFormatting();
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value.toUpperCase();
    onChange(inputValue);
  };
  
  const handleBlur = () => {
    if (validatePostalCode(value)) {
      onChange(formatPostalCode(value));
    }
  };
  
  return (
    <input
      type="text"
      value={value}
      onChange={handleChange}
      onBlur={handleBlur}
      placeholder={placeholder || 'H1A 1A1'}
      maxLength={7}
      className={className}
      disabled={disabled}
    />
  );
};

interface PhoneInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export const PhoneInput: React.FC<PhoneInputProps> = ({
  value,
  onChange,
  placeholder,
  className = '',
  disabled = false
}) => {
  const { formatPhone, validatePhoneNumber } = useFormatting();
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    // Allow only digits and basic formatting characters
    const cleanValue = inputValue.replace(/[^\d\s().-]/g, '');
    onChange(cleanValue);
  };
  
  const handleBlur = () => {
    if (validatePhoneNumber(value)) {
      onChange(formatPhone(value));
    }
  };
  
  return (
    <input
      type="tel"
      value={value}
      onChange={handleChange}
      onBlur={handleBlur}
      placeholder={placeholder || '(*************'}
      className={className}
      disabled={disabled}
    />
  );
};
