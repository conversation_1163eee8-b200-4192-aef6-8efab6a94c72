import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { I18nextProvider } from 'react-i18next';
import SystemSettings from './SystemSettings';
import i18n from '../../i18n';

const renderSystemSettings = () => {
  return render(
    <I18nextProvider i18n={i18n}>
      <SystemSettings />
    </I18nextProvider>
  );
};

describe('SystemSettings', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders system settings page', () => {
    renderSystemSettings();
    
    expect(screen.getByText('System Settings')).toBeInTheDocument();
    expect(screen.getByText('Configure system-wide settings and preferences')).toBeInTheDocument();
  });

  it('displays general settings section', () => {
    renderSystemSettings();
    
    expect(screen.getByText('General Settings')).toBeInTheDocument();
    expect(screen.getByDisplayValue('TutorAide')).toBeInTheDocument(); // Platform name
    expect(screen.getByDisplayValue('America/Montreal')).toBeInTheDocument(); // Timezone
  });

  it('displays company information section', () => {
    renderSystemSettings();
    
    expect(screen.getByText('Company Information')).toBeInTheDocument();
    expect(screen.getByDisplayValue('TutorAide Inc.')).toBeInTheDocument();
    expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByDisplayValue('+****************')).toBeInTheDocument();
  });

  it('shows feature toggles section', () => {
    renderSystemSettings();
    
    expect(screen.getByText('Feature Toggles')).toBeInTheDocument();
    expect(screen.getByText('Google Authentication')).toBeInTheDocument();
    expect(screen.getByText('SMS Notifications')).toBeInTheDocument();
    expect(screen.getByText('Email Notifications')).toBeInTheDocument();
  });

  it('displays system limits section', () => {
    renderSystemSettings();
    
    expect(screen.getByText('System Limits')).toBeInTheDocument();
    expect(screen.getByDisplayValue('10')).toBeInTheDocument(); // Max file upload size
    expect(screen.getByDisplayValue('30')).toBeInTheDocument(); // Session timeout
  });

  it('updates platform name when changed', async () => {
    const user = userEvent.setup();
    renderSystemSettings();
    
    const platformNameInput = screen.getByDisplayValue('TutorAide');
    
    await user.clear(platformNameInput);
    await user.type(platformNameInput, 'New Platform Name');
    
    expect(platformNameInput).toHaveValue('New Platform Name');
  });

  it('changes timezone selection', async () => {
    renderSystemSettings();
    
    const timezoneSelect = screen.getByDisplayValue('America/Montreal');
    fireEvent.change(timezoneSelect, { target: { value: 'America/Toronto' } });
    
    expect(timezoneSelect).toHaveValue('America/Toronto');
  });

  it('toggles feature switches', async () => {
    renderSystemSettings();
    
    const googleAuthToggle = screen.getByRole('checkbox', { name: /google authentication/i });
    expect(googleAuthToggle).toBeChecked();
    
    fireEvent.click(googleAuthToggle);
    expect(googleAuthToggle).not.toBeChecked();
  });

  it('shows maintenance mode warning', () => {
    renderSystemSettings();
    
    const maintenanceToggle = screen.getByRole('checkbox', { name: /maintenance mode/i });
    const warningText = screen.getByText(/Platform is under maintenance/);
    
    expect(maintenanceToggle).toBeInTheDocument();
    expect(warningText).toBeInTheDocument();
    expect(warningText).toHaveClass('text-accent-red');
  });

  it('validates system limits input ranges', async () => {
    const user = userEvent.setup();
    renderSystemSettings();
    
    const fileUploadInput = screen.getByDisplayValue('10');
    
    await user.clear(fileUploadInput);
    await user.type(fileUploadInput, '150'); // Above max
    
    // Should be constrained by HTML max attribute
    expect(fileUploadInput).toHaveAttribute('max', '100');
  });

  it('updates company information fields', async () => {
    const user = userEvent.setup();
    renderSystemSettings();
    
    const companyNameInput = screen.getByDisplayValue('TutorAide Inc.');
    
    await user.clear(companyNameInput);
    await user.type(companyNameInput, 'New Company Name');
    
    expect(companyNameInput).toHaveValue('New Company Name');
  });

  it('handles province selection', () => {
    renderSystemSettings();
    
    const provinceSelect = screen.getByDisplayValue('Quebec');
    fireEvent.change(provinceSelect, { target: { value: 'Ontario' } });
    
    expect(provinceSelect).toHaveValue('Ontario');
  });

  it('shows save button when changes are made', async () => {
    const user = userEvent.setup();
    renderSystemSettings();
    
    // Initially save button should be disabled
    const saveButton = screen.getByRole('button', { name: /save changes/i });
    expect(saveButton).toBeDisabled();
    
    // Make a change
    const platformNameInput = screen.getByDisplayValue('TutorAide');
    await user.type(platformNameInput, ' Modified');
    
    // Save button should now be enabled
    expect(saveButton).not.toBeDisabled();
  });

  it('handles save operation', async () => {
    const user = userEvent.setup();
    renderSystemSettings();
    
    // Make a change
    const platformNameInput = screen.getByDisplayValue('TutorAide');
    await user.type(platformNameInput, ' Modified');
    
    const saveButton = screen.getByRole('button', { name: /save changes/i });
    fireEvent.click(saveButton);
    
    await waitFor(() => {
      expect(screen.getByText('Saving...')).toBeInTheDocument();
    });
  });

  it('shows reset changes button', () => {
    renderSystemSettings();
    
    const resetButton = screen.getByRole('button', { name: /reset changes/i });
    expect(resetButton).toBeInTheDocument();
    expect(resetButton).toBeDisabled(); // Initially disabled
  });

  it('validates email format in company settings', async () => {
    const user = userEvent.setup();
    renderSystemSettings();
    
    const emailInput = screen.getByDisplayValue('<EMAIL>');
    
    await user.clear(emailInput);
    await user.type(emailInput, 'invalid-email');
    
    // HTML5 email validation should apply
    expect(emailInput).toHaveAttribute('type', 'email');
  });

  it('validates phone number format', async () => {
    const user = userEvent.setup();
    renderSystemSettings();
    
    const phoneInput = screen.getByDisplayValue('+****************');
    
    await user.clear(phoneInput);
    await user.type(phoneInput, '+****************');
    
    expect(phoneInput).toHaveValue('+****************');
  });

  it('handles postal code validation', async () => {
    const user = userEvent.setup();
    renderSystemSettings();
    
    const postalCodeInput = screen.getByDisplayValue('H1A 1A1');
    
    await user.clear(postalCodeInput);
    await user.type(postalCodeInput, 'K1A 0A6');
    
    expect(postalCodeInput).toHaveValue('K1A 0A6');
  });

  it('shows proper currency and date format options', () => {
    renderSystemSettings();
    
    expect(screen.getByDisplayValue('CAD')).toBeInTheDocument();
    expect(screen.getByDisplayValue('YYYY-MM-DD')).toBeInTheDocument();
    expect(screen.getByDisplayValue('24h')).toBeInTheDocument();
  });
});