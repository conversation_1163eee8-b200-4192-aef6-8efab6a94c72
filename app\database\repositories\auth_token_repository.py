"""
Authentication Token Repository for database operations.
"""

import json
from typing import Optional, List, Dict, Any
from datetime import datetime
from uuid import UUID
import asyncpg

from app.models.auth_token_models import (
    AuthToken, AuthTokenType, AuthTokenCreate
)
from app.core.logging import Tu<PERSON><PERSON>ideLogger
from app.core.exceptions import DatabaseError, ResourceNotFoundError
from app.core.timezone import now_est

logger = TutorAideLogger.get_logger(__name__)


class AuthTokenRepository:
    """Repository for authentication token database operations."""
    
    async def create_token(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        token_type: AuthTokenType,
        token_hash: str,
        expires_at: datetime,
        metadata: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> AuthToken:
        """Create a new authentication token."""
        
        query = """
            INSERT INTO auth_tokens (
                user_id, token_type, token_hash, expires_at,
                metadata, ip_address, user_agent
            ) VALUES ($1, $2, $3, $4, $5, $6, $7)
            RETURNING *
        """
        
        logger.info(f"=== AUTH TOKEN REPOSITORY: create_token ===")
        logger.info(f"Parameters: user_id={user_id}, token_type={token_type.value}, hash_len={len(token_hash)}")
        logger.info(f"Metadata: {metadata}")
        
        try:
            row = await conn.fetchrow(
                query,
                user_id,
                token_type.value,
                token_hash,
                expires_at,
                json.dumps(metadata or {}),  # JSONB requires JSON string
                ip_address,
                user_agent
            )
            
            logger.info(f"Token created successfully: token_id={row['token_id']}")
            return self._row_to_auth_token(row)
            
        except Exception as e:
            logger.error(f"Failed to create auth token: {type(e).__name__}: {e}")
            logger.error(f"Query parameters: user_id={user_id}, token_type={token_type.value}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            raise DatabaseError(f"Failed to create auth token: {str(e)}")
    
    async def get_token_by_hash(
        self,
        conn: asyncpg.Connection,
        token_hash: str,
        token_type: Optional[AuthTokenType] = None
    ) -> Optional[AuthToken]:
        """Get token by hash, optionally filtered by type."""
        
        logger.info("=== GET TOKEN BY HASH ===")
        logger.info(f"Looking for token_hash: {token_hash}")
        logger.info(f"Token type filter: {token_type.value if token_type else 'None'}")
        
        if token_type:
            query = """
                SELECT * FROM auth_tokens
                WHERE token_hash = $1 AND token_type = $2
            """
            params = [token_hash, token_type.value]
            logger.info(f"Query with type filter: token_type={token_type.value}")
        else:
            query = """
                SELECT * FROM auth_tokens
                WHERE token_hash = $1
            """
            params = [token_hash]
            logger.info("Query without type filter")
        
        try:
            # Log all password reset tokens for debugging
            if token_type and token_type == AuthTokenType.PASSWORD_RESET:
                debug_query = "SELECT token_id, token_hash, expires_at, used_at FROM auth_tokens WHERE token_type = 'password_reset' ORDER BY created_at DESC LIMIT 5"
                debug_rows = await conn.fetch(debug_query)
                logger.info("Recent password reset tokens in database:")
                for row in debug_rows:
                    logger.info(f"  token_id={row['token_id']}, hash={row['token_hash'][:20]}..., expires={row['expires_at']}, used={row['used_at']}")
            
            row = await conn.fetchrow(query, *params)
            
            if not row:
                logger.warning(f"No token found with hash: {token_hash}")
                # Try to find similar hashes for debugging
                similar_query = "SELECT token_hash FROM auth_tokens WHERE token_type = $1 AND LENGTH(token_hash) = LENGTH($2) LIMIT 5"
                if token_type:
                    similar_rows = await conn.fetch(similar_query, token_type.value, token_hash)
                    logger.info("Similar token hashes in database:")
                    for similar in similar_rows:
                        logger.info(f"  {similar['token_hash'][:20]}...")
                return None
            
            logger.info(f"Token found: token_id={row['token_id']}, user_id={row['user_id']}, type={row['token_type']}")
            logger.info(f"Token details: expires_at={row['expires_at']}, used_at={row['used_at']}")
            
            token = self._row_to_auth_token(row)
            logger.info("=== TOKEN RETRIEVED SUCCESSFULLY ===")
            return token
            
        except Exception as e:
            logger.error(f"Failed to get token by hash: {type(e).__name__}: {e}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            raise DatabaseError(f"Failed to get token: {str(e)}")
    
    async def get_token_by_id(
        self,
        conn: asyncpg.Connection,
        token_id: int
    ) -> Optional[AuthToken]:
        """Get token by ID."""
        
        query = "SELECT * FROM auth_tokens WHERE token_id = $1"
        
        try:
            row = await conn.fetchrow(query, token_id)
            
            if not row:
                return None
            
            return self._row_to_auth_token(row)
            
        except Exception as e:
            logger.error(f"Failed to get token by ID: {e}")
            raise DatabaseError(f"Failed to get token: {str(e)}")
    
    async def update_token(
        self,
        conn: asyncpg.Connection,
        token_id: int,
        metadata: Optional[Dict[str, Any]] = None,
        expires_at: Optional[datetime] = None
    ) -> Optional[AuthToken]:
        """Update token metadata or expiry."""
        
        updates = []
        params = [token_id]
        param_count = 2
        
        if metadata is not None:
            updates.append(f"metadata = ${param_count}::jsonb")
            params.append(json.dumps(metadata))
            param_count += 1
        
        if expires_at is not None:
            updates.append(f"expires_at = ${param_count}")
            params.append(expires_at)
            param_count += 1
        
        if not updates:
            return await self.get_token_by_id(conn, token_id)
        
        query = f"""
            UPDATE auth_tokens
            SET {', '.join(updates)}
            WHERE token_id = $1
            RETURNING *
        """
        
        try:
            row = await conn.fetchrow(query, *params)
            
            if not row:
                return None
            
            return self._row_to_auth_token(row)
            
        except Exception as e:
            logger.error(f"Failed to update token: {e}")
            raise DatabaseError(f"Failed to update token: {str(e)}")
    
    async def mark_token_used(
        self,
        conn: asyncpg.Connection,
        token_hash: str,
        token_type: Optional[AuthTokenType] = None,
        used_ip: Optional[str] = None
    ) -> bool:
        """Mark a token as used."""
        
        if token_type:
            query = """
                UPDATE auth_tokens
                SET used_at = $1, ip_address = COALESCE($2, ip_address)
                WHERE token_hash = $3 AND token_type = $4 AND used_at IS NULL
            """
            params = [now_est(), used_ip, token_hash, token_type.value]
        else:
            query = """
                UPDATE auth_tokens
                SET used_at = $1, ip_address = COALESCE($2, ip_address)
                WHERE token_hash = $3 AND used_at IS NULL
            """
            params = [now_est(), used_ip, token_hash]
        
        try:
            result = await conn.execute(query, *params)
            return result.split()[-1] == '1'  # Check if 1 row was updated
            
        except Exception as e:
            logger.error(f"Failed to mark token as used: {e}")
            return False
    
    async def invalidate_user_tokens(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        token_type: AuthTokenType,
        except_hash: Optional[str] = None
    ) -> int:
        """Invalidate all tokens of a type for a user."""
        
        if except_hash:
            query = """
                UPDATE auth_tokens
                SET used_at = $1
                WHERE user_id = $2 AND token_type = $3 
                  AND token_hash != $4 AND used_at IS NULL
            """
            params = [now_est(), user_id, token_type.value, except_hash]
        else:
            query = """
                UPDATE auth_tokens
                SET used_at = $1
                WHERE user_id = $2 AND token_type = $3 AND used_at IS NULL
            """
            params = [now_est(), user_id, token_type.value]
        
        try:
            result = await conn.execute(query, *params)
            count = int(result.split()[-1])
            return count
            
        except Exception as e:
            logger.error(f"Failed to invalidate user tokens: {e}")
            return 0
    
    async def get_user_tokens(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        token_type: Optional[AuthTokenType] = None,
        include_used: bool = False,
        include_expired: bool = False
    ) -> List[AuthToken]:
        """Get all tokens for a user."""
        
        conditions = ["user_id = $1"]
        params = [user_id]
        param_count = 2
        
        if token_type:
            conditions.append(f"token_type = ${param_count}")
            params.append(token_type.value)
            param_count += 1
        
        if not include_used:
            conditions.append("used_at IS NULL")
        
        if not include_expired:
            conditions.append(f"expires_at > ${param_count}")
            params.append(now_est())
        
        query = f"""
            SELECT * FROM auth_tokens
            WHERE {' AND '.join(conditions)}
            ORDER BY created_at DESC
        """
        
        try:
            rows = await conn.fetch(query, *params)
            return [self._row_to_auth_token(row) for row in rows]
            
        except Exception as e:
            logger.error(f"Failed to get user tokens: {e}")
            raise DatabaseError(f"Failed to get user tokens: {str(e)}")
    
    async def get_most_recent_token(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        token_type: AuthTokenType
    ) -> Optional[AuthToken]:
        """Get the most recent token of a type for a user."""
        
        query = """
            SELECT * FROM auth_tokens
            WHERE user_id = $1 AND token_type = $2
            ORDER BY created_at DESC
            LIMIT 1
        """
        
        try:
            row = await conn.fetchrow(query, user_id, token_type.value)
            
            if not row:
                return None
            
            return self._row_to_auth_token(row)
            
        except Exception as e:
            logger.error(f"Failed to get most recent token: {e}")
            raise DatabaseError(f"Failed to get token: {str(e)}")
    
    async def delete_expired_tokens(
        self,
        conn: asyncpg.Connection,
        batch_size: int = 1000
    ) -> int:
        """Delete expired tokens in batches."""
        
        total_deleted = 0
        
        while True:
            query = """
                DELETE FROM auth_tokens
                WHERE token_id IN (
                    SELECT token_id FROM auth_tokens
                    WHERE expires_at < $1
                    LIMIT $2
                )
            """
            
            try:
                result = await conn.execute(query, now_est(), batch_size)
                deleted = int(result.split()[-1])
                total_deleted += deleted
                
                if deleted < batch_size:
                    break
                    
            except Exception as e:
                logger.error(f"Failed to delete expired tokens: {e}")
                break
        
        return total_deleted
    
    async def count_active_tokens(
        self,
        conn: asyncpg.Connection,
        token_type: AuthTokenType,
        user_id: Optional[int] = None
    ) -> int:
        """Count active tokens."""
        
        conditions = [
            "token_type = $1",
            "used_at IS NULL",
            "expires_at > $2"
        ]
        params = [token_type.value, now_est()]
        
        if user_id:
            conditions.append("user_id = $3")
            params.append(user_id)
        
        query = f"""
            SELECT COUNT(*) FROM auth_tokens
            WHERE {' AND '.join(conditions)}
        """
        
        try:
            count = await conn.fetchval(query, *params)
            return count or 0
            
        except Exception as e:
            logger.error(f"Failed to count active tokens: {e}")
            return 0
    
    async def get_token_statistics(
        self,
        conn: asyncpg.Connection,
        user_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """Get token statistics."""
        
        base_conditions = []
        params = []
        
        if user_id:
            base_conditions.append("user_id = $1")
            params.append(user_id)
        
        where_clause = f"WHERE {' AND '.join(base_conditions)}" if base_conditions else ""
        
        query = f"""
            SELECT 
                token_type,
                COUNT(*) as total,
                COUNT(CASE WHEN used_at IS NULL THEN 1 END) as unused,
                COUNT(CASE WHEN used_at IS NOT NULL THEN 1 END) as used,
                COUNT(CASE WHEN expires_at < CURRENT_TIMESTAMP THEN 1 END) as expired,
                COUNT(CASE WHEN expires_at > CURRENT_TIMESTAMP AND used_at IS NULL THEN 1 END) as active
            FROM auth_tokens
            {where_clause}
            GROUP BY token_type
        """
        
        try:
            rows = await conn.fetch(query, *params)
            
            stats = {}
            for row in rows:
                stats[row['token_type']] = {
                    'total': row['total'],
                    'unused': row['unused'],
                    'used': row['used'],
                    'expired': row['expired'],
                    'active': row['active']
                }
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get token statistics: {e}")
            return {}
    
    def _row_to_auth_token(self, row: asyncpg.Record) -> AuthToken:
        """Convert database row to AuthToken model."""
        
        # Handle metadata - it might be a string or dict depending on how asyncpg returns it
        metadata = row['metadata']
        if isinstance(metadata, str):
            metadata = json.loads(metadata) if metadata else {}
        elif metadata is None:
            metadata = {}
        
        # Handle ip_address - convert IPv4Address to string
        ip_address = row['ip_address']
        if ip_address is not None:
            ip_address = str(ip_address)
        
        return AuthToken(
            token_id=row['token_id'],
            user_id=row['user_id'],
            token_type=AuthTokenType(row['token_type']),
            token_hash=row['token_hash'],
            expires_at=row['expires_at'],
            metadata=metadata,
            used_at=row['used_at'],
            ip_address=ip_address,
            user_agent=row['user_agent'],
            created_at=row['created_at']
        )