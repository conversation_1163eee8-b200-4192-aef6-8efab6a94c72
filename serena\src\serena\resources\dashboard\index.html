<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Serena Dashboard</title>
    <link rel="icon" type="image/png" sizes="16x16" href="serena-icon-16.png">
    <link rel="icon" type="image/png" sizes="32x32" href="serena-icon-32.png">
    <link rel="icon" type="image/png" sizes="48x48" href="serena-icon-48.png">
    <script src="jquery.min.js"></script>
    <script src="dashboard.js"></script>
    <style>
        body {
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .log-container {
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            height: 600px;
            overflow-y: auto;
            overflow-x: auto;
            padding: 10px;
            white-space: pre-wrap;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .controls {
            margin-bottom: 10px;
            text-align: center;
        }

        .logo {
            margin-bottom: 10px;
            text-align: center;
        }

        .btn {
            background-color: #eaa45d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn:hover {
            background-color: #dca662;
        }
        
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        .log-debug {
            color: #808080; /* Gray */
        }
        .log-info {
            color: #000000; /* Black */
        }
        .log-warning {
            color: #FF8C00; /* Dark Orange */
        }
        .log-error {
            color: #FF0000; /* Red */
        }
        .log-default {
            color: #000000; /* Black */
        }
        
        /* Tool name highlighting */
        .tool-name {
            background-color: #ffff00; /* Yellow background */
            font-weight: bold;
        }
        
        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
        }
        
        .error-message {
            color: #FF0000;
            text-align: center;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <img src="serena-logs.png" alt="Serena" style="max-width: 400px; height: auto;">
    </div>
    
    <div class="controls">
        <button id="load-logs" class="btn">Reload Log</button>
        <button id="shutdown" class="btn">Shutdown Server</button>
    </div>
    
    <div id="error-container"></div>
    <div id="log-container" class="log-container"></div>

    <script>
        $(document).ready(function() {
            const dashboard = new Dashboard();
        });
    </script>
</body>
</html>