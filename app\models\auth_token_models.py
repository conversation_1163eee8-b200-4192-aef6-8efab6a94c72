"""
Authentication Token Models for the unified token system.
"""

from typing import Optional, Dict, Any, List
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, Field, field_validator
from enum import Enum


class AuthTokenType(str, Enum):
    """Types of authentication tokens."""
    
    SESSION = "session"
    PASSWORD_RESET = "password_reset"
    EMAIL_VERIFY = "email_verify"
    API_KEY = "api_key"


class AuthToken(BaseModel):
    """Complete auth token model."""
    
    token_id: int
    user_id: int
    token_type: AuthTokenType
    token_hash: str  # SHA256 hash of the actual token
    expires_at: datetime
    
    # Flexible metadata for different token types
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    # Usage tracking
    used_at: Optional[datetime] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    
    # Timestamp
    created_at: datetime
    
    class Config:
        use_enum_values = False


class AuthTokenCreate(BaseModel):
    """Model for creating auth tokens."""
    
    user_id: int
    token_type: AuthTokenType
    expires_in_seconds: Optional[int] = None  # Override default expiry
    metadata: Dict[str, Any] = Field(default_factory=dict)
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None


class AuthTokenUpdate(BaseModel):
    """Model for updating auth tokens."""
    
    metadata: Optional[Dict[str, Any]] = None
    expires_at: Optional[datetime] = None


class AuthTokenResponse(BaseModel):
    """Response model for auth token operations."""
    
    token: Optional[str] = None  # Only included when creating new tokens
    token_id: Optional[int] = None
    expires_at: Optional[datetime] = None
    success: bool
    message: Optional[str] = None


class SessionInfo(BaseModel):
    """Session information model."""
    
    session_id: UUID
    user_id: int
    role_type: str  # UserRoleType value
    expires_at: datetime
    created_at: datetime
    last_activity: datetime
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    device_info: Optional[Dict[str, Any]] = None


class TokenValidationResult(BaseModel):
    """Result of token validation."""
    
    valid: bool
    user_id: Optional[int] = None
    token_type: Optional[AuthTokenType] = None
    expires_at: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class PasswordResetRequest(BaseModel):
    """Request model for password reset."""
    
    email: str
    initiated_by: str = 'user'  # user, admin, system
    initiated_by_user_id: Optional[int] = None


class PasswordResetConfirm(BaseModel):
    """Confirm password reset with new password."""
    
    token: str
    new_password: str
    confirm_password: Optional[str] = None  # Frontend validates this, backend doesn't need it
    
    @field_validator('confirm_password')
    @classmethod
    def passwords_match(cls, v, info):
        if 'new_password' in info.data and v != info.data['new_password']:
            raise ValueError('Passwords do not match')
        return v


class EmailVerificationRequest(BaseModel):
    """Request model for email verification."""
    
    user_id: int
    email: str


class EmailVerificationConfirm(BaseModel):
    """Confirm email verification."""
    
    token: str


class APIKeyCreate(BaseModel):
    """Model for creating API keys."""
    
    name: str
    permissions: List[str]
    expires_in_days: int = 90
    ip_whitelist: Optional[List[str]] = None


class APIKeyInfo(BaseModel):
    """API key information (without the actual key)."""
    
    token_id: int
    name: str
    permissions: List[str]
    created_at: datetime
    expires_at: datetime
    last_used_at: Optional[datetime] = None
    usage_count: int = 0
    ip_whitelist: List[str] = Field(default_factory=list)


class TokenStatistics(BaseModel):
    """Statistics about tokens."""
    
    token_type: AuthTokenType
    total: int
    active: int
    expired: int
    used: int
    average_lifetime_hours: Optional[float] = None


# Re-import for compatibility
from app.models.user_models import UserRoleType