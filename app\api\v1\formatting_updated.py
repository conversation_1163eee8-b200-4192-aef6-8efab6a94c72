"""
Formatting Preferences API Endpoints - Updated to use unified preference service

Manages user preferences for locale-specific formatting including
date formats, currency display, number formatting, and Quebec-specific
preferences.
"""

from fastapi import APIRouter, Depends, HTTPException, Body, status
from typing import Optional, Dict, Any
from datetime import datetime, timezone
import asyncpg

from app.core.dependencies import get_current_user, get_db
from app.models.user_models import User
from app.models.user_preference_models import UserPreferenceUpdate
from app.services.user_preference_service import get_user_preference_service
from app.core.logging import TutorAideLogger

logger = TutorAideLogger.get_logger(__name__)
router = APIRouter()


@router.get("/preferences")
async def get_formatting_preferences(
    current_user: User = Depends(get_current_user),
    db: asyncpg.Connection = Depends(get_db)
) -> Dict[str, Any]:
    """Get current user's formatting preferences."""
    
    preference_service = get_user_preference_service()
    
    try:
        # Get user preferences
        result = await preference_service.get_user_preferences(
            user_id=current_user.user_id
        )
        
        if not result.preferences:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Formatting preferences not found"
            )
        
        # Extract formatting-related preferences
        prefs = result.preferences
        formatting_data = {
            "user_id": prefs.user_id,
            "date_format": prefs.date_format,
            "time_format": prefs.time_format,
            "currency_display": prefs.currency_display,
            "number_precision": prefs.number_precision,
            "show_quebec_indicators": prefs.quebec_french_preference,
            "use_relative_dates": prefs.ui_settings.get("use_relative_dates", True),
            "phone_format": prefs.ui_settings.get("phone_format", "national"),
            "address_format": prefs.ui_settings.get("address_format", "standard"),
            "created_at": prefs.created_at.isoformat() if prefs.created_at else None,
            "updated_at": prefs.updated_at.isoformat() if prefs.updated_at else None,
            "effective_timezone": "America/New_York"
        }
        
        return {
            "success": True,
            "data": formatting_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to retrieve formatting preferences for user {current_user.user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve formatting preferences"
        )


@router.put("/preferences")
async def update_formatting_preferences(
    preferences: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user),
    db: asyncpg.Connection = Depends(get_db)
) -> Dict[str, Any]:
    """Update user's formatting preferences."""
    
    preference_service = get_user_preference_service()
    
    try:
        # Get current preferences first
        current_result = await preference_service.get_user_preferences(
            user_id=current_user.user_id
        )
        
        current_ui_settings = {}
        if current_result.preferences:
            current_ui_settings = current_result.preferences.ui_settings or {}
        
        # Prepare update data
        update_data = UserPreferenceUpdate()
        
        # Direct formatting fields
        if "date_format" in preferences:
            update_data.date_format = preferences["date_format"]
        if "time_format" in preferences:
            update_data.time_format = preferences["time_format"]
        if "currency_display" in preferences:
            update_data.currency_display = preferences["currency_display"]
        if "number_precision" in preferences:
            update_data.number_precision = preferences["number_precision"]
        if "show_quebec_indicators" in preferences:
            update_data.quebec_french_preference = preferences["show_quebec_indicators"]
        
        # UI settings fields
        ui_updates = {}
        if "use_relative_dates" in preferences:
            ui_updates["use_relative_dates"] = preferences["use_relative_dates"]
        if "phone_format" in preferences:
            ui_updates["phone_format"] = preferences["phone_format"]
        if "address_format" in preferences:
            ui_updates["address_format"] = preferences["address_format"]
        
        if ui_updates:
            # Merge with current UI settings
            current_ui_settings.update(ui_updates)
            update_data.ui_settings = current_ui_settings
        
        # Update preferences
        result = await preference_service.update_user_preferences(
            user_id=current_user.user_id,
            update_data=update_data
        )
        
        if not result.success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to update formatting preferences"
            )
        
        # Extract formatting data for response
        prefs = result.preferences
        formatting_data = {
            "user_id": prefs.user_id,
            "date_format": prefs.date_format,
            "time_format": prefs.time_format,
            "currency_display": prefs.currency_display,
            "number_precision": prefs.number_precision,
            "show_quebec_indicators": prefs.quebec_french_preference,
            "use_relative_dates": prefs.ui_settings.get("use_relative_dates", True),
            "phone_format": prefs.ui_settings.get("phone_format", "national"),
            "address_format": prefs.ui_settings.get("address_format", "standard"),
            "updated_at": prefs.updated_at.isoformat() if prefs.updated_at else None
        }
        
        logger.info(f"Updated formatting preferences for user {current_user.user_id}")
        
        return {
            "success": True,
            "message": "Formatting preferences updated successfully",
            "data": formatting_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update formatting preferences for user {current_user.user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update formatting preferences"
        )


@router.post("/preferences/reset")
async def reset_formatting_preferences(
    current_user: User = Depends(get_current_user),
    db: asyncpg.Connection = Depends(get_db)
) -> Dict[str, Any]:
    """Reset formatting preferences to defaults."""
    
    preference_service = get_user_preference_service()
    
    try:
        # Set default values
        update_data = UserPreferenceUpdate(
            date_format='medium',
            time_format='auto',
            currency_display='symbol',
            number_precision=2,
            quebec_french_preference=True,
            ui_settings={
                "use_relative_dates": True,
                "phone_format": "national",
                "address_format": "standard"
            }
        )
        
        # Update to defaults
        result = await preference_service.update_user_preferences(
            user_id=current_user.user_id,
            update_data=update_data
        )
        
        if not result.success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to reset formatting preferences"
            )
        
        logger.info(f"Reset formatting preferences for user {current_user.user_id}")
        
        return {
            "success": True,
            "message": "Formatting preferences reset to defaults"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to reset formatting preferences for user {current_user.user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to reset formatting preferences"
        )


@router.get("/examples")
async def get_formatting_examples(
    current_user: User = Depends(get_current_user),
    db: asyncpg.Connection = Depends(get_db)
) -> Dict[str, Any]:
    """Get examples of how data would be formatted with current preferences."""
    
    preference_service = get_user_preference_service()
    
    try:
        # Get user preferences
        result = await preference_service.get_user_preferences(
            user_id=current_user.user_id
        )
        
        if not result.preferences:
            # Use defaults
            date_format = 'medium'
            time_format = 'auto'
            currency_display = 'symbol'
            number_precision = 2
        else:
            prefs = result.preferences
            date_format = prefs.date_format
            time_format = prefs.time_format
            currency_display = prefs.currency_display
            number_precision = prefs.number_precision
        
        # Generate examples
        now = datetime.now(timezone.utc)
        examples = {
            "date": {
                "short": now.strftime("%m/%d/%y"),
                "medium": now.strftime("%b %d, %Y"),
                "long": now.strftime("%B %d, %Y"),
                "full": now.strftime("%A, %B %d, %Y"),
                "current": _format_date(now, date_format)
            },
            "time": {
                "12h": now.strftime("%I:%M %p"),
                "24h": now.strftime("%H:%M"),
                "auto": _format_time(now, time_format, is_quebec=result.preferences.quebec_french_preference if result.preferences else True),
                "current": _format_time(now, time_format, is_quebec=result.preferences.quebec_french_preference if result.preferences else True)
            },
            "currency": {
                "symbol": "$125.99",
                "code": "125.99 CAD",
                "name": "125.99 Canadian dollars",
                "current": _format_currency(125.99, currency_display)
            },
            "number": {
                "precision_0": "1,234",
                "precision_2": "1,234.56",
                "precision_4": "1,234.5678",
                f"current_precision_{number_precision}": _format_number(1234.56789, number_precision)
            }
        }
        
        return {
            "success": True,
            "current_preferences": {
                "date_format": date_format,
                "time_format": time_format,
                "currency_display": currency_display,
                "number_precision": number_precision
            },
            "examples": examples
        }
        
    except Exception as e:
        logger.error(f"Failed to generate formatting examples: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate formatting examples"
        )


def _format_date(date: datetime, format_type: str) -> str:
    """Format date according to preference."""
    formats = {
        "short": "%m/%d/%y",
        "medium": "%b %d, %Y",
        "long": "%B %d, %Y",
        "full": "%A, %B %d, %Y"
    }
    return date.strftime(formats.get(format_type, formats["medium"]))


def _format_time(time: datetime, format_type: str, is_quebec: bool = True) -> str:
    """Format time according to preference."""
    if format_type == "12h":
        return time.strftime("%I:%M %p")
    elif format_type == "24h":
        return time.strftime("%H:%M")
    else:  # auto
        # In Quebec, 24h format is more common
        return time.strftime("%H:%M" if is_quebec else "%I:%M %p")


def _format_currency(amount: float, display_type: str) -> str:
    """Format currency according to preference."""
    if display_type == "symbol":
        return f"${amount:,.2f}"
    elif display_type == "code":
        return f"{amount:,.2f} CAD"
    elif display_type == "name":
        return f"{amount:,.2f} Canadian dollars"
    else:
        return f"${amount:,.2f}"


def _format_number(number: float, precision: int) -> str:
    """Format number with specified precision."""
    return f"{number:,.{precision}f}"