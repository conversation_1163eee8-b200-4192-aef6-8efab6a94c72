"""
Dependant Repository - Handles all database operations for dependants
"""
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, date
from loguru import logger

from app.database import Database
from app.domain.dependant.models import (
    DependantProfile, Dependant, DependantParent,
    DependantMedicalInfo, DependantEducation,
    DependantLearningProfile, DependantEmergencyContact,
    DependantCreate, DependantUpdate, DependantParentCreate,
    DependantParentUpdate, MedicalInfoCreate, MedicalInfoUpdate,
    EducationCreate, EducationUpdate, LearningProfileCreate,
    LearningProfileUpdate, EmergencyContactCreate, EmergencyContactUpdate
)
from app.repositories.base import BaseRepository


class DependantRepository(BaseRepository):
    """Repository for dependant-related database operations"""
    
    def __init__(self, db: Database):
        super().__init__(db)
    
    # ==================== Dependant Operations ====================
    
    async def create_dependant(self, data: DependantCreate, created_by: int) -> DependantProfile:
        """Create a new dependant"""
        async with self.db.transaction():
            # Create the dependant
            query = """
                INSERT INTO dependants (
                    first_name, last_name, date_of_birth, gender,
                    profile_photo_url, preferred_name, pronouns, created_by
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                RETURNING *
            """
            
            row = await self.db.fetchrow(
                query, data.first_name, data.last_name, data.date_of_birth,
                data.gender, data.profile_photo_url, data.preferred_name,
                data.pronouns, created_by
            )
            
            dependant_id = row['dependant_id']
            
            # Create parent relationships if provided
            if data.parent_relationships:
                for parent_data in data.parent_relationships:
                    await self.add_parent_relationship(dependant_id, parent_data)
            
            return await self._get_full_dependant_profile(dependant_id)
    
    async def get_dependant_by_id(self, dependant_id: int) -> Optional[DependantProfile]:
        """Get dependant by ID with all relationships"""
        return await self._get_full_dependant_profile(dependant_id)
    
    async def update_dependant(self, dependant_id: int, data: DependantUpdate) -> Optional[DependantProfile]:
        """Update dependant"""
        update_fields = []
        params = []
        param_count = 1
        
        update_dict = data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            update_fields.append(f"{field} = ${param_count}")
            params.append(value)
            param_count += 1
        
        if not update_fields:
            return await self.get_dependant_by_id(dependant_id)
        
        params.append(dependant_id)
        query = f"""
            UPDATE dependants
            SET {', '.join(update_fields)}, updated_at = CURRENT_TIMESTAMP
            WHERE dependant_id = ${param_count} AND deleted_at IS NULL
            RETURNING *
        """
        
        await self.db.execute(query, *params)
        return await self._get_full_dependant_profile(dependant_id)
    
    async def delete_dependant(self, dependant_id: int) -> bool:
        """Soft delete dependant"""
        query = """
            UPDATE dependants
            SET deleted_at = CURRENT_TIMESTAMP
            WHERE dependant_id = $1 AND deleted_at IS NULL
            RETURNING dependant_id
        """
        result = await self.db.fetchrow(query, dependant_id)
        return result is not None
    
    async def search_dependants(
        self,
        query: Optional[str] = None,
        client_id: Optional[int] = None,
        grade_level: Optional[str] = None,
        age_min: Optional[int] = None,
        age_max: Optional[int] = None,
        school_name: Optional[str] = None,
        offset: int = 0,
        limit: int = 50,
        sort_by: str = "created_at",
        sort_order: str = "DESC"
    ) -> Tuple[List[DependantProfile], int]:
        """Search dependants with filters and pagination"""
        where_clauses = ["d.deleted_at IS NULL"]
        params = []
        param_count = 1
        
        if query:
            where_clauses.append(f"""
                (d.first_name ILIKE ${param_count} OR 
                 d.last_name ILIKE ${param_count} OR 
                 d.preferred_name ILIKE ${param_count})
            """)
            params.append(f"%{query}%")
            param_count += 1
        
        if client_id:
            where_clauses.append(f"""
                EXISTS (
                    SELECT 1 FROM dependant_parents dp
                    WHERE dp.dependant_id = d.dependant_id 
                    AND dp.client_id = ${param_count}
                    AND dp.deleted_at IS NULL
                )
            """)
            params.append(client_id)
            param_count += 1
        
        if grade_level:
            where_clauses.append(f"""
                EXISTS (
                    SELECT 1 FROM dependant_education de
                    WHERE de.dependant_id = d.dependant_id 
                    AND de.grade_level = ${param_count}
                )
            """)
            params.append(grade_level)
            param_count += 1
        
        if age_min is not None:
            where_clauses.append(f"EXTRACT(YEAR FROM AGE(d.date_of_birth)) >= ${param_count}")
            params.append(age_min)
            param_count += 1
        
        if age_max is not None:
            where_clauses.append(f"EXTRACT(YEAR FROM AGE(d.date_of_birth)) <= ${param_count}")
            params.append(age_max)
            param_count += 1
        
        if school_name:
            where_clauses.append(f"""
                EXISTS (
                    SELECT 1 FROM dependant_education de
                    WHERE de.dependant_id = d.dependant_id 
                    AND de.school_name ILIKE ${param_count}
                )
            """)
            params.append(f"%{school_name}%")
            param_count += 1
        
        where_clause = " AND ".join(where_clauses)
        
        # Count query
        count_query = f"""
            SELECT COUNT(*) as total
            FROM dependants d
            WHERE {where_clause}
        """
        count_result = await self.db.fetchrow(count_query, *params)
        total_count = count_result['total']
        
        # Data query
        allowed_sort_fields = {
            "created_at": "d.created_at",
            "updated_at": "d.updated_at",
            "last_name": "d.last_name",
            "first_name": "d.first_name",
            "date_of_birth": "d.date_of_birth"
        }
        sort_field = allowed_sort_fields.get(sort_by, "d.created_at")
        sort_direction = "ASC" if sort_order.upper() == "ASC" else "DESC"
        
        params.extend([limit, offset])
        data_query = f"""
            SELECT d.dependant_id
            FROM dependants d
            WHERE {where_clause}
            ORDER BY {sort_field} {sort_direction}
            LIMIT ${param_count} OFFSET ${param_count + 1}
        """
        
        rows = await self.db.fetch(data_query, *params)
        dependants = []
        for row in rows:
            dependant = await self._get_full_dependant_profile(row['dependant_id'])
            if dependant:
                dependants.append(dependant)
        
        return dependants, total_count
    
    async def get_dependants_by_client(self, client_id: int) -> List[DependantProfile]:
        """Get all dependants for a client"""
        query = """
            SELECT DISTINCT d.dependant_id
            FROM dependants d
            JOIN dependant_parents dp ON d.dependant_id = dp.dependant_id
            WHERE dp.client_id = $1 AND dp.deleted_at IS NULL AND d.deleted_at IS NULL
            ORDER BY d.first_name, d.last_name
        """
        
        rows = await self.db.fetch(query, client_id)
        dependants = []
        for row in rows:
            dependant = await self._get_full_dependant_profile(row['dependant_id'])
            if dependant:
                dependants.append(dependant)
        
        return dependants
    
    # ==================== Parent Relationship Operations ====================
    
    async def add_parent_relationship(
        self, dependant_id: int, data: DependantParentCreate
    ) -> DependantParent:
        """Add parent relationship"""
        # If this is set as primary, unset other primary contacts
        if data.is_primary_contact:
            await self.db.execute("""
                UPDATE dependant_parents 
                SET is_primary_contact = FALSE 
                WHERE dependant_id = $1 AND deleted_at IS NULL
            """, dependant_id)
        
        query = """
            INSERT INTO dependant_parents (
                dependant_id, client_id, relationship_type, is_primary_contact,
                has_legal_custody, custody_arrangement, can_make_medical_decisions,
                can_make_educational_decisions, can_pickup,
                financial_responsibility_percent, notes
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
            RETURNING *
        """
        
        row = await self.db.fetchrow(
            query, dependant_id, data.client_id, data.relationship_type,
            data.is_primary_contact, data.has_legal_custody,
            data.custody_arrangement, data.can_make_medical_decisions,
            data.can_make_educational_decisions, data.can_pickup,
            data.financial_responsibility_percent, data.notes
        )
        
        return DependantParent(**dict(row))
    
    async def update_parent_relationship(
        self, relationship_id: int, data: DependantParentUpdate
    ) -> Optional[DependantParent]:
        """Update parent relationship"""
        update_fields = []
        params = []
        param_count = 1
        
        update_dict = data.model_dump(exclude_unset=True)
        
        # Handle primary contact logic
        if 'is_primary_contact' in update_dict and update_dict['is_primary_contact']:
            # Get dependant_id first
            rel_row = await self.db.fetchrow(
                "SELECT dependant_id FROM dependant_parents WHERE relationship_id = $1",
                relationship_id
            )
            if rel_row:
                await self.db.execute("""
                    UPDATE dependant_parents 
                    SET is_primary_contact = FALSE 
                    WHERE dependant_id = $1 AND relationship_id != $2 AND deleted_at IS NULL
                """, rel_row['dependant_id'], relationship_id)
        
        for field, value in update_dict.items():
            update_fields.append(f"{field} = ${param_count}")
            params.append(value)
            param_count += 1
        
        if not update_fields:
            return await self.get_parent_relationship_by_id(relationship_id)
        
        params.append(relationship_id)
        query = f"""
            UPDATE dependant_parents
            SET {', '.join(update_fields)}, updated_at = CURRENT_TIMESTAMP
            WHERE relationship_id = ${param_count} AND deleted_at IS NULL
            RETURNING *
        """
        
        row = await self.db.fetchrow(query, *params)
        return DependantParent(**dict(row)) if row else None
    
    async def remove_parent_relationship(self, relationship_id: int) -> bool:
        """Remove parent relationship"""
        query = """
            UPDATE dependant_parents
            SET deleted_at = CURRENT_TIMESTAMP
            WHERE relationship_id = $1 AND deleted_at IS NULL
            RETURNING relationship_id
        """
        result = await self.db.fetchrow(query, relationship_id)
        return result is not None
    
    async def get_parent_relationship_by_id(self, relationship_id: int) -> Optional[DependantParent]:
        """Get parent relationship by ID"""
        query = """
            SELECT * FROM dependant_parents
            WHERE relationship_id = $1 AND deleted_at IS NULL
        """
        row = await self.db.fetchrow(query, relationship_id)
        return DependantParent(**dict(row)) if row else None
    
    # ==================== Medical Info Operations ====================
    
    async def set_medical_info(
        self, dependant_id: int, data: MedicalInfoCreate
    ) -> DependantMedicalInfo:
        """Set or update medical info"""
        query = """
            INSERT INTO dependant_medical_info (
                dependant_id, blood_type, allergies, medications,
                medical_conditions, dietary_restrictions, emergency_medical_info,
                physician_name, physician_phone, physician_address,
                health_card_number, insurance_provider, insurance_policy_number,
                last_physical_exam_date, immunization_up_to_date,
                special_needs, assistive_devices, behavioral_notes
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18)
            ON CONFLICT (dependant_id) DO UPDATE SET
                blood_type = EXCLUDED.blood_type,
                allergies = EXCLUDED.allergies,
                medications = EXCLUDED.medications,
                medical_conditions = EXCLUDED.medical_conditions,
                dietary_restrictions = EXCLUDED.dietary_restrictions,
                emergency_medical_info = EXCLUDED.emergency_medical_info,
                physician_name = EXCLUDED.physician_name,
                physician_phone = EXCLUDED.physician_phone,
                physician_address = EXCLUDED.physician_address,
                health_card_number = EXCLUDED.health_card_number,
                insurance_provider = EXCLUDED.insurance_provider,
                insurance_policy_number = EXCLUDED.insurance_policy_number,
                last_physical_exam_date = EXCLUDED.last_physical_exam_date,
                immunization_up_to_date = EXCLUDED.immunization_up_to_date,
                special_needs = EXCLUDED.special_needs,
                assistive_devices = EXCLUDED.assistive_devices,
                behavioral_notes = EXCLUDED.behavioral_notes,
                updated_at = CURRENT_TIMESTAMP
            RETURNING *
        """
        
        row = await self.db.fetchrow(
            query, dependant_id, data.blood_type, data.allergies,
            data.medications, data.medical_conditions, data.dietary_restrictions,
            data.emergency_medical_info, data.physician_name, data.physician_phone,
            data.physician_address, data.health_card_number, data.insurance_provider,
            data.insurance_policy_number, data.last_physical_exam_date,
            data.immunization_up_to_date, data.special_needs,
            data.assistive_devices, data.behavioral_notes
        )
        
        return DependantMedicalInfo(**dict(row))
    
    async def get_medical_info(self, dependant_id: int) -> Optional[DependantMedicalInfo]:
        """Get medical info"""
        query = """
            SELECT * FROM dependant_medical_info
            WHERE dependant_id = $1
        """
        row = await self.db.fetchrow(query, dependant_id)
        return DependantMedicalInfo(**dict(row)) if row else None
    
    # ==================== Education Info Operations ====================
    
    async def set_education_info(
        self, dependant_id: int, data: EducationCreate
    ) -> DependantEducation:
        """Set or update education info"""
        query = """
            INSERT INTO dependant_education (
                dependant_id, school_name, school_address, school_phone,
                grade_level, academic_year, teacher_name, teacher_email,
                school_start_time, school_end_time, transportation_method,
                locker_number, student_id, special_education_plan,
                gifted_program, french_immersion, extracurricular_activities,
                academic_strengths, academic_challenges, homework_routine
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20)
            ON CONFLICT (dependant_id) DO UPDATE SET
                school_name = EXCLUDED.school_name,
                school_address = EXCLUDED.school_address,
                school_phone = EXCLUDED.school_phone,
                grade_level = EXCLUDED.grade_level,
                academic_year = EXCLUDED.academic_year,
                teacher_name = EXCLUDED.teacher_name,
                teacher_email = EXCLUDED.teacher_email,
                school_start_time = EXCLUDED.school_start_time,
                school_end_time = EXCLUDED.school_end_time,
                transportation_method = EXCLUDED.transportation_method,
                locker_number = EXCLUDED.locker_number,
                student_id = EXCLUDED.student_id,
                special_education_plan = EXCLUDED.special_education_plan,
                gifted_program = EXCLUDED.gifted_program,
                french_immersion = EXCLUDED.french_immersion,
                extracurricular_activities = EXCLUDED.extracurricular_activities,
                academic_strengths = EXCLUDED.academic_strengths,
                academic_challenges = EXCLUDED.academic_challenges,
                homework_routine = EXCLUDED.homework_routine,
                updated_at = CURRENT_TIMESTAMP
            RETURNING *
        """
        
        row = await self.db.fetchrow(
            query, dependant_id, data.school_name, data.school_address,
            data.school_phone, data.grade_level, data.academic_year,
            data.teacher_name, data.teacher_email, data.school_start_time,
            data.school_end_time, data.transportation_method, data.locker_number,
            data.student_id, data.special_education_plan, data.gifted_program,
            data.french_immersion, data.extracurricular_activities,
            data.academic_strengths, data.academic_challenges, data.homework_routine
        )
        
        return DependantEducation(**dict(row))
    
    async def get_education_info(self, dependant_id: int) -> Optional[DependantEducation]:
        """Get education info"""
        query = """
            SELECT * FROM dependant_education
            WHERE dependant_id = $1
        """
        row = await self.db.fetchrow(query, dependant_id)
        return DependantEducation(**dict(row)) if row else None
    
    # ==================== Learning Profile Operations ====================
    
    async def set_learning_profile(
        self, dependant_id: int, data: LearningProfileCreate
    ) -> DependantLearningProfile:
        """Set or update learning profile"""
        query = """
            INSERT INTO dependant_learning_profiles (
                dependant_id, learning_style, attention_span_minutes,
                preferred_subjects, struggling_subjects, learning_goals,
                motivators, learning_barriers, preferred_reward_system,
                homework_habits, study_environment_preferences,
                technology_comfort_level, parent_involvement_needed,
                peer_learning_preference, assessment_results, progress_tracking
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
            ON CONFLICT (dependant_id) DO UPDATE SET
                learning_style = EXCLUDED.learning_style,
                attention_span_minutes = EXCLUDED.attention_span_minutes,
                preferred_subjects = EXCLUDED.preferred_subjects,
                struggling_subjects = EXCLUDED.struggling_subjects,
                learning_goals = EXCLUDED.learning_goals,
                motivators = EXCLUDED.motivators,
                learning_barriers = EXCLUDED.learning_barriers,
                preferred_reward_system = EXCLUDED.preferred_reward_system,
                homework_habits = EXCLUDED.homework_habits,
                study_environment_preferences = EXCLUDED.study_environment_preferences,
                technology_comfort_level = EXCLUDED.technology_comfort_level,
                parent_involvement_needed = EXCLUDED.parent_involvement_needed,
                peer_learning_preference = EXCLUDED.peer_learning_preference,
                assessment_results = EXCLUDED.assessment_results,
                progress_tracking = EXCLUDED.progress_tracking,
                updated_at = CURRENT_TIMESTAMP
            RETURNING *
        """
        
        row = await self.db.fetchrow(
            query, dependant_id, data.learning_style, data.attention_span_minutes,
            data.preferred_subjects, data.struggling_subjects, data.learning_goals,
            data.motivators, data.learning_barriers, data.preferred_reward_system,
            data.homework_habits, data.study_environment_preferences,
            data.technology_comfort_level, data.parent_involvement_needed,
            data.peer_learning_preference, data.assessment_results,
            data.progress_tracking
        )
        
        return DependantLearningProfile(**dict(row))
    
    async def get_learning_profile(self, dependant_id: int) -> Optional[DependantLearningProfile]:
        """Get learning profile"""
        query = """
            SELECT * FROM dependant_learning_profiles
            WHERE dependant_id = $1
        """
        row = await self.db.fetchrow(query, dependant_id)
        return DependantLearningProfile(**dict(row)) if row else None
    
    # ==================== Emergency Contact Operations ====================
    
    async def add_emergency_contact(
        self, dependant_id: int, data: EmergencyContactCreate
    ) -> DependantEmergencyContact:
        """Add emergency contact for dependant"""
        query = """
            INSERT INTO dependant_emergency_contacts (
                dependant_id, contact_name, relationship, phone_primary,
                phone_secondary, email, address, is_authorized_pickup,
                priority_order, notes
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            RETURNING *
        """
        
        row = await self.db.fetchrow(
            query, dependant_id, data.contact_name, data.relationship,
            data.phone_primary, data.phone_secondary, data.email,
            data.address, data.is_authorized_pickup, data.priority_order,
            data.notes
        )
        
        return DependantEmergencyContact(**dict(row))
    
    async def update_emergency_contact(
        self, contact_id: int, data: EmergencyContactUpdate
    ) -> Optional[DependantEmergencyContact]:
        """Update emergency contact"""
        update_fields = []
        params = []
        param_count = 1
        
        update_dict = data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            update_fields.append(f"{field} = ${param_count}")
            params.append(value)
            param_count += 1
        
        if not update_fields:
            return await self.get_emergency_contact_by_id(contact_id)
        
        params.append(contact_id)
        query = f"""
            UPDATE dependant_emergency_contacts
            SET {', '.join(update_fields)}, updated_at = CURRENT_TIMESTAMP
            WHERE contact_id = ${param_count} AND deleted_at IS NULL
            RETURNING *
        """
        
        row = await self.db.fetchrow(query, *params)
        return DependantEmergencyContact(**dict(row)) if row else None
    
    async def delete_emergency_contact(self, contact_id: int) -> bool:
        """Delete emergency contact"""
        query = """
            UPDATE dependant_emergency_contacts
            SET deleted_at = CURRENT_TIMESTAMP
            WHERE contact_id = $1 AND deleted_at IS NULL
            RETURNING contact_id
        """
        result = await self.db.fetchrow(query, contact_id)
        return result is not None
    
    async def get_emergency_contact_by_id(self, contact_id: int) -> Optional[DependantEmergencyContact]:
        """Get emergency contact by ID"""
        query = """
            SELECT * FROM dependant_emergency_contacts
            WHERE contact_id = $1 AND deleted_at IS NULL
        """
        row = await self.db.fetchrow(query, contact_id)
        return DependantEmergencyContact(**dict(row)) if row else None
    
    # ==================== Helper Methods ====================
    
    async def _get_full_dependant_profile(self, dependant_id: int) -> Optional[DependantProfile]:
        """Get complete dependant profile with all relationships"""
        # Get base dependant
        dependant_query = """
            SELECT * FROM dependants
            WHERE dependant_id = $1 AND deleted_at IS NULL
        """
        dependant_row = await self.db.fetchrow(dependant_query, dependant_id)
        if not dependant_row:
            return None
        
        dependant = Dependant(**dict(dependant_row))
        
        # Get parents with client info
        parents_query = """
            SELECT dp.*, cp.first_name, cp.last_name, cp.email, cp.phone
            FROM dependant_parents dp
            JOIN client_profiles cp ON dp.client_id = cp.client_id
            WHERE dp.dependant_id = $1 AND dp.deleted_at IS NULL
            ORDER BY dp.is_primary_contact DESC, dp.created_at
        """
        parent_rows = await self.db.fetch(parents_query, dependant_id)
        parents = []
        for row in parent_rows:
            parent_data = dict(row)
            parent = DependantParent(**parent_data)
            # Add client info to the parent object
            parent.client_name = f"{row['first_name']} {row['last_name']}"
            parent.client_email = row['email']
            parent.client_phone = row['phone']
            parents.append(parent)
        
        # Get medical info
        medical_info = await self.get_medical_info(dependant_id)
        
        # Get education info
        education_info = await self.get_education_info(dependant_id)
        
        # Get learning profile
        learning_profile = await self.get_learning_profile(dependant_id)
        
        # Get emergency contacts
        contacts_query = """
            SELECT * FROM dependant_emergency_contacts
            WHERE dependant_id = $1 AND deleted_at IS NULL
            ORDER BY priority_order, created_at
        """
        contact_rows = await self.db.fetch(contacts_query, dependant_id)
        emergency_contacts = [DependantEmergencyContact(**dict(row)) for row in contact_rows]
        
        # Get appointment count
        appointment_query = """
            SELECT COUNT(*) as count
            FROM appointments
            WHERE dependant_id = $1 AND deleted_at IS NULL
        """
        appointment_result = await self.db.fetchrow(appointment_query, dependant_id)
        appointment_count = appointment_result['count']
        
        # Build complete profile
        return DependantProfile(
            dependant=dependant,
            parents=parents,
            medical_info=medical_info,
            education_info=education_info,
            learning_profile=learning_profile,
            emergency_contacts=emergency_contacts,
            appointment_count=appointment_count
        )
    
    async def get_dependants_by_ids(self, dependant_ids: List[int]) -> List[DependantProfile]:
        """Get multiple dependants by IDs"""
        if not dependant_ids:
            return []
        
        dependants = []
        for dependant_id in dependant_ids:
            dependant = await self._get_full_dependant_profile(dependant_id)
            if dependant:
                dependants.append(dependant)
        
        return dependants
    
    async def validate_parent_access(self, dependant_id: int, client_id: int) -> bool:
        """Check if a client has access to a dependant"""
        query = """
            SELECT 1 FROM dependant_parents
            WHERE dependant_id = $1 AND client_id = $2 AND deleted_at IS NULL
        """
        result = await self.db.fetchrow(query, dependant_id, client_id)
        return result is not None