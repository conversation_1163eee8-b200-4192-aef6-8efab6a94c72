"""
Appointment repository for scheduling, participants, and confirmations.
"""

import asyncpg
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, date, time, timedelta

from app.database.repositories.base import BaseRepository
from app.core.exceptions import ResourceNotFoundError, BusinessLogicError, DatabaseOperationError
from app.core.timezone import now_est

logger = logging.getLogger(__name__)


class AppointmentRepository(BaseRepository):
    """Repository for appointment management."""
    
    def __init__(self):
        super().__init__(table_name="appointment_sessions", id_column="appointment_id")
    
    async def find_by_tutor_and_date(
        self,
        conn: asyncpg.Connection,
        tutor_id: int,
        appointment_date: date,
        include_cancelled: bool = False
    ) -> List[asyncpg.Record]:
        """
        Find appointments for a tutor on a specific date.
        
        Args:
            conn: Database connection
            tutor_id: Tutor ID
            appointment_date: Date to search for
            include_cancelled: Whether to include cancelled appointments
        
        Returns:
            List of appointment records
        """
        query = f"""
            SELECT 
                ap.*,
                sc.service_name,
                sc.subject_area,
                ARRAY_AGG(
                    JSON_BUILD_OBJECT(
                        'user_id', part.user_id,
                        'participant_type', part.participant_type,
                        'attendance_status', part.attendance_status
                    )
                ) as participants
            FROM {self.table_name} ap
            JOIN service_catalog sc ON ap.service_id = sc.service_id
            LEFT JOIN appointment_participants part ON ap.appointment_id = part.appointment_id
            WHERE ap.tutor_id = $1 AND ap.scheduled_date = $2
        """
        
        if not include_cancelled:
            query += " AND ap.status != 'cancelled'"
        
        query += """
            GROUP BY ap.appointment_id, sc.service_name, sc.subject_area
            ORDER BY ap.start_time
        """
        
        try:
            results = await conn.fetch(query, tutor_id, appointment_date)
            return results
        except Exception as e:
            logger.error(f"Error finding appointments by tutor and date: {e}")
            raise
    
    async def check_time_conflict(
        self,
        conn: asyncpg.Connection,
        tutor_id: int,
        appointment_date: date,
        start_time: time,
        end_time: time,
        exclude_appointment_id: Optional[int] = None
    ) -> bool:
        """
        Check if there's a time conflict for a tutor.
        
        Args:
            conn: Database connection
            tutor_id: Tutor ID
            appointment_date: Date of appointment
            start_time: Start time
            end_time: End time
            exclude_appointment_id: Appointment ID to exclude from check (for updates)
        
        Returns:
            True if there's a conflict, False otherwise
        """
        query = f"""
            SELECT COUNT(*) FROM {self.table_name}
            WHERE tutor_id = $1 
            AND scheduled_date = $2
            AND status NOT IN ('cancelled', 'no_show')
            AND (
                (start_time < $4 AND end_time > $3) OR
                (start_time < $3 AND end_time > $3) OR
                (start_time < $4 AND end_time > $4) OR
                (start_time >= $3 AND end_time <= $4)
            )
        """
        
        params = [tutor_id, appointment_date, start_time, end_time]
        
        if exclude_appointment_id:
            query += " AND appointment_id != $5"
            params.append(exclude_appointment_id)
        
        try:
            count = await conn.fetchval(query, *params)
            return count > 0
        except Exception as e:
            logger.error(f"Error checking time conflict: {e}")
            raise
    
    async def create_appointment_with_participants(
        self,
        conn: asyncpg.Connection,
        appointment_data: Dict[str, Any],
        participant_ids: List[int],
        participant_types: List[str]
    ) -> asyncpg.Record:
        """
        Create appointment with participants in a transaction.
        
        Args:
            conn: Database connection
            appointment_data: Appointment information
            participant_ids: List of participant user IDs
            participant_types: List of participant types
        
        Returns:
            Created appointment record
        """
        if len(participant_ids) != len(participant_types):
            raise BusinessLogicError(
                "Participant IDs and types must have same length",
                {"participant_ids": len(participant_ids), "participant_types": len(participant_types)}
            )
        
        # Check for time conflict
        conflict = await self.check_time_conflict(
            conn,
            appointment_data['tutor_id'],
            appointment_data['scheduled_date'],
            appointment_data['start_time'],
            appointment_data['end_time']
        )
        
        if conflict:
            raise DatabaseOperationError(
                "Time slot already booked for this tutor",
                appointment_data
            )
        
        try:
            async with conn.transaction():
                # Create appointment
                appointment = await self.create(conn, appointment_data)
                
                # Add participants
                for user_id, participant_type in zip(participant_ids, participant_types):
                    await self._add_participant(
                        conn, appointment['appointment_id'], user_id, participant_type
                    )
                
                logger.info(f"Created appointment {appointment['appointment_id']} with {len(participant_ids)} participants")
                return appointment
                
        except Exception as e:
            logger.error(f"Error creating appointment with participants: {e}")
            raise
    
    async def _add_participant(
        self,
        conn: asyncpg.Connection,
        appointment_id: int,
        user_id: int,
        participant_type: str
    ) -> asyncpg.Record:
        """Add participant to appointment."""
        query = """
            INSERT INTO appointment_participants 
            (appointment_id, user_id, participant_type, attendance_status, created_at, updated_at)
            VALUES ($1, $2, $3, 'scheduled', $4, $5)
            RETURNING *
        """
        
        now = now_est()
        
        result = await conn.fetchrow(
            query, appointment_id, user_id, participant_type, now, now
        )
        
        return result
    
    async def get_appointment_participants(
        self,
        conn: asyncpg.Connection,
        appointment_id: int
    ) -> List[asyncpg.Record]:
        """
        Get all participants for an appointment.
        
        Args:
            conn: Database connection
            appointment_id: Appointment ID
        
        Returns:
            List of participant records with user details
        """
        query = """
            SELECT 
                ap.*,
                ua.email,
                COALESCE(cp.first_name, tp.first_name, cd.first_name) as first_name,
                COALESCE(cp.last_name, tp.last_name, cd.last_name) as last_name,
                COALESCE(cp.phone, tp.phone) as phone
            FROM appointment_participants ap
            JOIN user_accounts ua ON ap.user_id = ua.user_id
            LEFT JOIN client_profiles cp ON ua.user_id = cp.user_id
            LEFT JOIN tutor_profiles tp ON ua.user_id = tp.user_id
            LEFT JOIN client_dependants cd ON ua.user_id = cd.user_id
            WHERE ap.appointment_id = $1
            ORDER BY ap.participant_type, ap.created_at
        """
        
        try:
            results = await conn.fetch(query, appointment_id)
            return results
        except Exception as e:
            logger.error(f"Error getting appointment participants: {e}")
            raise
    
    async def update_participant_attendance(
        self,
        conn: asyncpg.Connection,
        appointment_id: int,
        user_id: int,
        attendance_status: str
    ) -> Optional[asyncpg.Record]:
        """
        Update participant attendance status.
        
        Args:
            conn: Database connection
            appointment_id: Appointment ID
            user_id: Participant user ID
            attendance_status: New attendance status
        
        Returns:
            Updated participant record
        """
        query = """
            UPDATE appointment_participants
            SET attendance_status = $1, updated_at = $2
            WHERE appointment_id = $3 AND user_id = $4
            RETURNING *
        """
        
        try:
            result = await conn.fetchrow(
                query, attendance_status, now_est(), appointment_id, user_id
            )
            if result:
                logger.info(f"Updated attendance for user {user_id} in appointment {appointment_id}")
            return result
        except Exception as e:
            logger.error(f"Error updating participant attendance: {e}")
            raise
    
    async def confirm_appointment_completion(
        self,
        conn: asyncpg.Connection,
        appointment_id: int,
        tutor_id: int,
        is_completed: bool,
        notes: Optional[str] = None
    ) -> asyncpg.Record:
        """
        Record tutor confirmation of appointment completion.
        
        Args:
            conn: Database connection
            appointment_id: Appointment ID
            tutor_id: Tutor ID
            is_completed: Whether session was completed
            notes: Optional notes about the session
        
        Returns:
            Confirmation record
        """
        try:
            async with conn.transaction():
                # Record confirmation
                confirmation = await self._create_confirmation(
                    conn, appointment_id, tutor_id, is_completed, notes
                )
                
                # Update appointment status
                new_status = 'completed' if is_completed else 'no_show'
                await self.update(conn, appointment_id, {
                    'status': new_status,
                    'updated_at': now_est()
                })
                
                logger.info(f"Confirmed appointment {appointment_id} as {new_status}")
                return confirmation
                
        except Exception as e:
            logger.error(f"Error confirming appointment completion: {e}")
            raise
    
    async def _create_confirmation(
        self,
        conn: asyncpg.Connection,
        appointment_id: int,
        tutor_id: int,
        is_completed: bool,
        notes: Optional[str]
    ) -> asyncpg.Record:
        """Create appointment confirmation record."""
        query = """
            INSERT INTO appointment_confirmations 
            (appointment_id, tutor_id, is_completed, confirmed_at, notes)
            VALUES ($1, $2, $3, $4, $5)
            ON CONFLICT (appointment_id)
            DO UPDATE SET 
                is_completed = $3,
                confirmed_at = $4,
                notes = $5
            RETURNING *
        """
        
        result = await conn.fetchrow(
            query, appointment_id, tutor_id, is_completed, now_est(), notes
        )
        
        return result
    
    async def get_upcoming_appointments(
        self,
        conn: asyncpg.Connection,
        user_id: Optional[int] = None,
        tutor_id: Optional[int] = None,
        days_ahead: int = 7
    ) -> List[asyncpg.Record]:
        """
        Get upcoming appointments for a user or tutor.
        
        Args:
            conn: Database connection
            user_id: Optional user ID (for participants)
            tutor_id: Optional tutor ID
            days_ahead: Number of days to look ahead
        
        Returns:
            List of upcoming appointment records
        """
        if not user_id and not tutor_id:
            raise ValueError("Either user_id or tutor_id must be provided")
        
        base_query = f"""
            SELECT DISTINCT
                ap.*,
                sc.service_name,
                sc.subject_area,
                tp.first_name as tutor_first_name,
                tp.last_name as tutor_last_name
            FROM {self.table_name} ap
            JOIN service_catalog sc ON ap.service_id = sc.service_id
            JOIN tutor_profiles tp ON ap.tutor_id = tp.tutor_id
        """
        
        params = []
        where_conditions = [
            "ap.scheduled_date >= CURRENT_DATE",
            f"ap.scheduled_date <= CURRENT_DATE + INTERVAL '{days_ahead} days'",
            "ap.status IN ('scheduled', 'confirmed')"
        ]
        
        if user_id:
            base_query += " JOIN appointment_participants part ON ap.appointment_id = part.appointment_id"
            where_conditions.append("part.user_id = $1")
            params.append(user_id)
        
        if tutor_id:
            param_num = len(params) + 1
            where_conditions.append(f"ap.tutor_id = ${param_num}")
            params.append(tutor_id)
        
        query = base_query + " WHERE " + " AND ".join(where_conditions)
        query += " ORDER BY ap.scheduled_date, ap.start_time"
        
        try:
            results = await conn.fetch(query, *params)
            return results
        except Exception as e:
            logger.error(f"Error getting upcoming appointments: {e}")
            raise
    
    async def cancel_appointment(
        self,
        conn: asyncpg.Connection,
        appointment_id: int,
        cancelled_by_user_id: int,
        reason: Optional[str] = None
    ) -> Optional[asyncpg.Record]:
        """
        Cancel an appointment.
        
        Args:
            conn: Database connection
            appointment_id: Appointment ID
            cancelled_by_user_id: ID of user who cancelled
            reason: Optional cancellation reason
        
        Returns:
            Updated appointment record
        """
        query = f"""
            UPDATE {self.table_name}
            SET 
                status = 'cancelled',
                notes = COALESCE(notes, '') || 
                    CASE WHEN notes IS NOT NULL AND LENGTH(notes) > 0 
                         THEN E'\\n' ELSE '' END ||
                    'Cancelled by user ' || $2 ||
                    CASE WHEN $3 IS NOT NULL 
                         THEN ': ' || $3 
                         ELSE '' END,
                updated_at = $4
            WHERE appointment_id = $1
            AND status NOT IN ('completed', 'cancelled')
            RETURNING *
        """
        
        try:
            result = await conn.fetchrow(
                query, appointment_id, cancelled_by_user_id, reason, now_est()
            )
            if result:
                logger.info(f"Cancelled appointment {appointment_id}")
            return result
        except Exception as e:
            logger.error(f"Error cancelling appointment: {e}")
            raise
    
    async def get_appointment_history(
        self,
        conn: asyncpg.Connection,
        user_id: Optional[int] = None,
        tutor_id: Optional[int] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[asyncpg.Record]:
        """
        Get appointment history for a user or tutor.
        
        Args:
            conn: Database connection
            user_id: Optional user ID
            tutor_id: Optional tutor ID
            limit: Maximum number of records
            offset: Number of records to skip
        
        Returns:
            List of historical appointment records
        """
        if not user_id and not tutor_id:
            raise ValueError("Either user_id or tutor_id must be provided")
        
        base_query = f"""
            SELECT DISTINCT
                ap.*,
                sc.service_name,
                sc.subject_area,
                tp.first_name as tutor_first_name,
                tp.last_name as tutor_last_name,
                ac.is_completed,
                ac.confirmed_at,
                ac.notes as confirmation_notes
            FROM {self.table_name} ap
            JOIN service_catalog sc ON ap.service_id = sc.service_id
            JOIN tutor_profiles tp ON ap.tutor_id = tp.tutor_id
            LEFT JOIN appointment_confirmations ac ON ap.appointment_id = ac.appointment_id
        """
        
        params = []
        where_conditions = ["ap.scheduled_date < CURRENT_DATE OR ap.status IN ('completed', 'cancelled', 'no_show')"]
        
        if user_id:
            base_query += " JOIN appointment_participants part ON ap.appointment_id = part.appointment_id"
            where_conditions.append("part.user_id = $1")
            params.append(user_id)
        
        if tutor_id:
            param_num = len(params) + 1
            where_conditions.append(f"ap.tutor_id = ${param_num}")
            params.append(tutor_id)
        
        query = base_query + " WHERE " + " AND ".join(where_conditions)
        query += f" ORDER BY ap.scheduled_date DESC, ap.start_time DESC LIMIT ${len(params) + 1} OFFSET ${len(params) + 2}"
        
        params.extend([limit, offset])
        
        try:
            results = await conn.fetch(query, *params)
            return results
        except Exception as e:
            logger.error(f"Error getting appointment history: {e}")
            raise
    
    async def get_tutor_schedule_conflicts(
        self,
        conn: asyncpg.Connection,
        tutor_id: int,
        start_date: date,
        end_date: date
    ) -> List[asyncpg.Record]:
        """
        Get schedule conflicts for a tutor in a date range.
        
        Args:
            conn: Database connection
            tutor_id: Tutor ID
            start_date: Start date to check
            end_date: End date to check
        
        Returns:
            List of conflicting appointments
        """
        query = f"""
            SELECT 
                ap1.appointment_id as appointment1_id,
                ap1.scheduled_date,
                ap1.start_time as start1,
                ap1.end_time as end1,
                ap2.appointment_id as appointment2_id,
                ap2.start_time as start2,
                ap2.end_time as end2
            FROM {self.table_name} ap1
            JOIN {self.table_name} ap2 ON (
                ap1.tutor_id = ap2.tutor_id 
                AND ap1.scheduled_date = ap2.scheduled_date
                AND ap1.appointment_id < ap2.appointment_id
                AND (
                    (ap1.start_time < ap2.end_time AND ap1.end_time > ap2.start_time)
                )
            )
            WHERE ap1.tutor_id = $1
            AND ap1.scheduled_date BETWEEN $2 AND $3
            AND ap1.status NOT IN ('cancelled', 'no_show')
            AND ap2.status NOT IN ('cancelled', 'no_show')
            ORDER BY ap1.scheduled_date, ap1.start_time
        """
        
        try:
            results = await conn.fetch(query, tutor_id, start_date, end_date)
            return results
        except Exception as e:
            logger.error(f"Error getting schedule conflicts: {e}")
            raise