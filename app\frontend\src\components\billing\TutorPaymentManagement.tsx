import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';
import { 
  DollarSign, Calendar, CheckCircle, Clock, XCircle,
  AlertCircle, Download, Eye, User, CreditCard, Building,
  ChevronLeft, ChevronRight, Filter, RefreshCw
} from 'lucide-react';
import { billingService, TutorPayment } from '../../services/billingService';
import { Card } from '../common/Card';
import Button from '../common/Button';
import { Badge } from '../common/Badge';
import { Input } from '../common/Input';
import { Select } from '../common/Select';
import { Modal } from '../common/Modal';
import LoadingSpinner from '../ui/LoadingSpinner';
import { EmptyState } from '../common/EmptyState';
import { useAuth } from '../../contexts/AuthContext';
import { UserRoleType } from '../../types/auth';
import toast from 'react-hot-toast';

interface TutorPaymentManagementProps {
  tutorId?: number;
  showApprovalActions?: boolean;
}

export const TutorPaymentManagement: React.FC<TutorPaymentManagementProps> = ({
  tutorId,
  showApprovalActions = false
}) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [payments, setPayments] = useState<TutorPayment[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [hasMore, setHasMore] = useState(false);
  const [selectedPayments, setSelectedPayments] = useState<Set<number>>(new Set());
  
  // Filters
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [dateRange, setDateRange] = useState({ start: '', end: '' });
  
  // Modals
  const [showBankSetup, setShowBankSetup] = useState(false);
  const [processingPayments, setProcessingPayments] = useState(false);
  const [generatingWeekly, setGeneratingWeekly] = useState(false);
  
  const limit = 20;
  const isManager = user?.activeRole === UserRoleType.MANAGER;
  const isTutor = user?.activeRole === UserRoleType.TUTOR;

  useEffect(() => {
    loadPayments();
  }, [page, statusFilter, dateRange, tutorId]);

  const loadPayments = async () => {
    try {
      setLoading(true);
      
      const response = await billingService.getTutorPayments({
        tutor_id: tutorId || (isTutor ? user?.userId : undefined),
        status: statusFilter || undefined,
        start_date: dateRange.start || undefined,
        end_date: dateRange.end || undefined,
        page,
        limit
      });

      setPayments(response.payments);
      setTotal(response.total);
      setHasMore(response.has_more);
    } catch (error) {
      console.error('Error loading tutor payments:', error);
      toast.error('Failed to load payments');
    } finally {
      setLoading(false);
    }
  };

  const handleApprovePayments = async () => {
    if (selectedPayments.size === 0) {
      toast.error('Please select payments to approve');
      return;
    }

    try {
      setProcessingPayments(true);
      
      const result = await billingService.approveTutorPayments({
        payment_ids: Array.from(selectedPayments),
        approved_by_notes: `Approved by ${user?.name || 'Manager'}`
      });

      toast.success(`${result.approved_count} payments approved successfully`);
      setSelectedPayments(new Set());
      loadPayments();
    } catch (error) {
      console.error('Error approving payments:', error);
      toast.error('Failed to approve payments');
    } finally {
      setProcessingPayments(false);
    }
  };

  const handleGenerateWeeklyPayments = async () => {
    try {
      setGeneratingWeekly(true);
      
      // Calculate the last Thursday
      const today = new Date();
      const dayOfWeek = today.getDay();
      const daysToThursday = dayOfWeek >= 4 ? dayOfWeek - 4 : dayOfWeek + 3;
      const lastThursday = new Date(today);
      lastThursday.setDate(today.getDate() - daysToThursday);
      
      const result = await billingService.generateWeeklyPayments({
        week_ending_date: format(lastThursday, 'yyyy-MM-dd')
      });

      toast.success(`Generated ${result.generated_count} payments totaling ${formatCurrency(result.total_amount)}`);
      loadPayments();
    } catch (error) {
      console.error('Error generating weekly payments:', error);
      toast.error('Failed to generate weekly payments');
    } finally {
      setGeneratingWeekly(false);
    }
  };

  const handleProcessApprovedPayments = async () => {
    try {
      setProcessingPayments(true);
      
      const result = await billingService.processApprovedPayments();
      
      toast.success(`Processed ${result.processed_count} payments totaling ${formatCurrency(result.total_amount)}`);
      if (result.failed_count > 0) {
        toast.error(`${result.failed_count} payments failed to process`);
      }
      
      loadPayments();
    } catch (error) {
      console.error('Error processing payments:', error);
      toast.error('Failed to process payments');
    } finally {
      setProcessingPayments(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'approved':
        return <Clock className="w-5 h-5 text-blue-500" />;
      case 'processing':
        return <RefreshCw className="w-5 h-5 text-yellow-500 animate-spin" />;
      case 'failed':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <AlertCircle className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'approved':
        return 'bg-blue-100 text-blue-800';
      case 'processing':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-600';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD'
    }).format(amount);
  };

  const togglePaymentSelection = (paymentId: number) => {
    const newSelection = new Set(selectedPayments);
    if (newSelection.has(paymentId)) {
      newSelection.delete(paymentId);
    } else {
      newSelection.add(paymentId);
    }
    setSelectedPayments(newSelection);
  };

  const selectAllPayments = () => {
    if (selectedPayments.size === payments.filter(p => p.status === 'pending').length) {
      setSelectedPayments(new Set());
    } else {
      const pendingIds = payments
        .filter(p => p.status === 'pending')
        .map(p => p.payment_id);
      setSelectedPayments(new Set(pendingIds));
    }
  };

  if (loading && page === 1) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Actions */}
      {isManager && showApprovalActions && (
        <Card className="p-6">
          <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Payment Management</h3>
              <p className="text-sm text-gray-500 mt-1">
                Manage tutor payments for completed sessions
              </p>
            </div>
            <div className="flex gap-3">
              <Button
                variant="secondary"
                leftIcon={<RefreshCw className="w-4 h-4" />}
                onClick={handleGenerateWeeklyPayments}
                loading={generatingWeekly}
              >
                Generate Weekly
              </Button>
              <Button
                variant="primary"
                leftIcon={<CheckCircle className="w-4 h-4" />}
                onClick={handleApprovePayments}
                disabled={selectedPayments.size === 0}
                loading={processingPayments}
              >
                Approve Selected ({selectedPayments.size})
              </Button>
              <Button
                variant="primary"
                leftIcon={<DollarSign className="w-4 h-4" />}
                onClick={handleProcessApprovedPayments}
                loading={processingPayments}
              >
                Process Approved
              </Button>
            </div>
          </div>
        </Card>
      )}

      {/* Filters */}
      <Card className="p-4">
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <Select
            value={statusFilter}
            onChange={(e) => {
              setStatusFilter(e.target.value);
              setPage(1);
            }}
          >
            <option value="">All Statuses</option>
            <option value="pending">Pending</option>
            <option value="approved">Approved</option>
            <option value="processing">Processing</option>
            <option value="completed">Completed</option>
            <option value="failed">Failed</option>
          </Select>

          <Input
            type="date"
            value={dateRange.start}
            onChange={(e) => {
              setDateRange(prev => ({ ...prev, start: e.target.value }));
              setPage(1);
            }}
            placeholder="Start Date"
          />

          <Input
            type="date"
            value={dateRange.end}
            onChange={(e) => {
              setDateRange(prev => ({ ...prev, end: e.target.value }));
              setPage(1);
            }}
            placeholder="End Date"
          />
        </div>
      </Card>

      {/* Payments List */}
      {payments.length === 0 ? (
        <Card>
          <EmptyState
            icon={<DollarSign className="w-12 h-12 text-gray-400" />}
            title="No payments found"
            description="No tutor payments match your current filters"
          />
        </Card>
      ) : (
        <Card className="overflow-hidden p-0">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {isManager && showApprovalActions && (
                    <th className="px-6 py-3">
                      <input
                        type="checkbox"
                        checked={selectedPayments.size === payments.filter(p => p.status === 'pending').length && payments.some(p => p.status === 'pending')}
                        onChange={selectAllPayments}
                        className="rounded border-gray-300 text-accent-red focus:ring-accent-red"
                      />
                    </th>
                  )}
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tutor
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Period
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Sessions
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Bank Account
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {payments.map((payment) => (
                  <tr key={payment.payment_id} className="hover:bg-gray-50">
                    {isManager && showApprovalActions && (
                      <td className="px-6 py-4">
                        {payment.status === 'pending' && (
                          <input
                            type="checkbox"
                            checked={selectedPayments.has(payment.payment_id)}
                            onChange={() => togglePaymentSelection(payment.payment_id)}
                            className="rounded border-gray-300 text-accent-red focus:ring-accent-red"
                          />
                        )}
                      </td>
                    )}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <User className="w-4 h-4 text-gray-400 mr-2" />
                        <span className="text-sm font-medium text-gray-900">
                          {payment.tutor_name}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {format(new Date(payment.period_start), 'MMM d')} - {format(new Date(payment.period_end), 'MMM d, yyyy')}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {payment.sessions_count} sessions
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {formatCurrency(payment.amount)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(payment.status)}
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(payment.status)}`}>
                          {payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {payment.bank_account_last4 ? (
                        <div className="flex items-center text-sm text-gray-500">
                          <CreditCard className="w-4 h-4 mr-1" />
                          •••• {payment.bank_account_last4}
                        </div>
                      ) : (
                        <span className="text-sm text-gray-400">Not set up</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          leftIcon={<Eye className="w-4 h-4" />}
                          onClick={() => {/* View details */}}
                        >
                          View
                        </Button>
                        {payment.stripe_payout_id && (
                          <Button
                            variant="ghost"
                            size="sm"
                            leftIcon={<Download className="w-4 h-4" />}
                            onClick={() => {/* Download receipt */}}
                          >
                            Receipt
                          </Button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {total > limit && (
            <div className="bg-gray-50 px-6 py-3 flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Showing {((page - 1) * limit) + 1} to {Math.min(page * limit, total)} of {total} payments
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => setPage(prev => Math.max(1, prev - 1))}
                  disabled={page === 1}
                  className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronLeft className="w-5 h-5" />
                </button>
                <span className="text-sm text-gray-700">
                  Page {page} of {Math.ceil(total / limit)}
                </span>
                <button
                  onClick={() => setPage(prev => prev + 1)}
                  disabled={!hasMore}
                  className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronRight className="w-5 h-5" />
                </button>
              </div>
            </div>
          )}
        </Card>
      )}

      {/* Bank Setup Modal for Tutors */}
      {isTutor && !payments.some(p => p.bank_account_last4) && (
        <Card className="p-6 bg-yellow-50 border-yellow-200">
          <div className="flex items-start gap-4">
            <AlertCircle className="w-6 h-6 text-yellow-600 flex-shrink-0 mt-0.5" />
            <div className="flex-1">
              <h4 className="text-sm font-medium text-yellow-900">Bank Account Required</h4>
              <p className="text-sm text-yellow-700 mt-1">
                Set up your bank account to receive payments for completed sessions.
              </p>
              <Button
                variant="primary"
                size="sm"
                leftIcon={<Building className="w-4 h-4" />}
                onClick={() => setShowBankSetup(true)}
                className="mt-3"
              >
                Set Up Bank Account
              </Button>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};