"""
Admin API Router

Consolidated router for all administrative endpoints including
translation management, user administration, and system settings.
"""

from fastapi import APIRouter
from . import translations, client_requests

router = APIRouter()

# Include admin sub-routers
router.include_router(
    translations.router,
    prefix="/translations",
    tags=["admin-translations"]
)

router.include_router(
    client_requests.router,
    prefix="/requests",
    tags=["admin-client-requests"]
)