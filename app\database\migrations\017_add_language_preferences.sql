-- Migration 017: Add Language Preferences System (Fixed for user_accounts)
-- Creates language preference management for users with Quebec French support

-- Add language preference columns to user_accounts table
ALTER TABLE user_accounts 
ADD COLUMN IF NOT EXISTS preferred_language VARCHAR(5) DEFAULT 'en' CHECK (preferred_language IN ('en', 'fr')),
ADD COLUMN IF NOT EXISTS language_auto_detect BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS language_source VARCHAR(20) DEFAULT 'auto' CHECK (language_source IN ('auto', 'manual', 'browser', 'system')),
ADD COLUMN IF NOT EXISTS language_updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS quebec_french_preference BOOLEAN DEFAULT true;

-- Drop and recreate the users view to include new columns
DROP VIEW IF EXISTS users CASCADE;

CREATE OR REPLACE VIEW users AS
SELECT 
    user_id,
    email,
    password_hash,
    google_id,
    created_at,
    updated_at,
    deleted_at,
    preferred_language,
    language_auto_detect,
    language_source,
    language_updated_at,
    quebec_french_preference
FROM user_accounts;

-- Create language preference history table for tracking changes
CREATE TABLE IF NOT EXISTS user_language_preferences_history (
    preference_history_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    previous_language VARCHAR(5),
    new_language VARCHAR(5) NOT NULL,
    change_source VARCHAR(20) NOT NULL CHECK (change_source IN ('user_manual', 'auto_detect', 'browser_detect', 'admin_override', 'system_migration')),
    change_reason TEXT,
    browser_language VARCHAR(100),
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(100),
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES user_accounts(user_id)
);

-- Create indexes on the history table
CREATE INDEX IF NOT EXISTS idx_language_history_user_id ON user_language_preferences_history(user_id);
CREATE INDEX IF NOT EXISTS idx_language_history_created_at ON user_language_preferences_history(created_at);
CREATE INDEX IF NOT EXISTS idx_language_history_change_source ON user_language_preferences_history(change_source);

-- Create language preference analytics table for system insights
CREATE TABLE IF NOT EXISTS language_usage_analytics (
    analytics_id SERIAL PRIMARY KEY,
    date_recorded DATE NOT NULL DEFAULT CURRENT_DATE,
    language_code VARCHAR(5) NOT NULL,
    total_users INTEGER DEFAULT 0,
    new_users INTEGER DEFAULT 0,
    active_sessions INTEGER DEFAULT 0,
    manual_switches INTEGER DEFAULT 0,
    auto_detections INTEGER DEFAULT 0,
    quebec_french_users INTEGER DEFAULT 0,
    browser_preferences JSONB,
    geographic_data JSONB,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    -- Ensure one record per date per language
    UNIQUE(date_recorded, language_code)
);

-- Create indexes on analytics table
CREATE INDEX IF NOT EXISTS idx_language_analytics_date ON language_usage_analytics(date_recorded);
CREATE INDEX IF NOT EXISTS idx_language_analytics_language ON language_usage_analytics(language_code);

-- Add comments for documentation
COMMENT ON COLUMN user_accounts.preferred_language IS 'User preferred language (en=English, fr=French Quebec)';
COMMENT ON COLUMN user_accounts.language_auto_detect IS 'Whether to automatically detect language from browser/context';
COMMENT ON COLUMN user_accounts.language_source IS 'Source of current language preference';
COMMENT ON COLUMN user_accounts.quebec_french_preference IS 'Preference for Quebec French vs International French';

COMMENT ON TABLE user_language_preferences_history IS 'Tracks all language preference changes for users';
COMMENT ON TABLE language_usage_analytics IS 'Daily analytics for language usage patterns';

-- Create function to automatically update language_updated_at timestamp
CREATE OR REPLACE FUNCTION update_language_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    IF OLD.preferred_language IS DISTINCT FROM NEW.preferred_language OR
       OLD.language_auto_detect IS DISTINCT FROM NEW.language_auto_detect OR
       OLD.quebec_french_preference IS DISTINCT FROM NEW.quebec_french_preference THEN
        NEW.language_updated_at = CURRENT_TIMESTAMP;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update timestamp on language changes
DROP TRIGGER IF EXISTS trigger_update_language_timestamp ON user_accounts;
CREATE TRIGGER trigger_update_language_timestamp
    BEFORE UPDATE ON user_accounts
    FOR EACH ROW
    EXECUTE FUNCTION update_language_timestamp();

-- Create function to log language preference changes
CREATE OR REPLACE FUNCTION log_language_preference_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Only log if language actually changed
    IF OLD.preferred_language IS DISTINCT FROM NEW.preferred_language THEN
        INSERT INTO user_language_preferences_history (
            user_id,
            previous_language,
            new_language,
            change_source,
            change_reason
        ) VALUES (
            NEW.user_id,
            OLD.preferred_language,
            NEW.preferred_language,
            COALESCE(NEW.language_source, 'user_manual'),
            CASE 
                WHEN NEW.language_source = 'auto' THEN 'Automatic detection based on browser/context'
                WHEN NEW.language_source = 'manual' THEN 'Manual user selection'
                WHEN NEW.language_source = 'browser' THEN 'Browser language preference detection'
                ELSE 'Language preference updated'
            END
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically log preference changes
DROP TRIGGER IF EXISTS trigger_log_language_preference_change ON user_accounts;
CREATE TRIGGER trigger_log_language_preference_change
    AFTER UPDATE ON user_accounts
    FOR EACH ROW
    EXECUTE FUNCTION log_language_preference_change();

-- Update existing users to have default language preferences
UPDATE user_accounts 
SET preferred_language = 'en',
    language_auto_detect = true,
    language_source = 'system',
    quebec_french_preference = true
WHERE preferred_language IS NULL;

-- Create indexes for performance optimization on the actual table
CREATE INDEX IF NOT EXISTS idx_user_accounts_preferred_language ON user_accounts(preferred_language);
CREATE INDEX IF NOT EXISTS idx_user_accounts_language_updated_at ON user_accounts(language_updated_at);
CREATE INDEX IF NOT EXISTS idx_user_accounts_language_auto_detect ON user_accounts(language_auto_detect);

-- Insert initial analytics record
INSERT INTO language_usage_analytics (date_recorded, language_code, total_users, quebec_french_users)
SELECT 
    CURRENT_DATE,
    preferred_language,
    COUNT(*),
    COUNT(*) FILTER (WHERE quebec_french_preference = true)
FROM user_accounts 
WHERE preferred_language IS NOT NULL
GROUP BY preferred_language
ON CONFLICT (date_recorded, language_code) DO NOTHING;