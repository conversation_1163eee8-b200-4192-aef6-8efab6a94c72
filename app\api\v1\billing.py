"""
Billing API endpoints for invoice generation, payment processing, and financial management.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, Path, Request, Header, Body
from typing import List, Optional, Dict, Any
from datetime import date, datetime, timedelta
from decimal import Decimal

from app.services.billing_service import BillingService
from app.services.stripe_service import StripeService
from app.services.tutor_payment_service import TutorPaymentService
from app.services.parent_payment_service import ParentPaymentService
from app.services.financial_validation_service import FinancialValidationService
from app.core.auth_decorators import require_auth, require_roles
from app.core.dependencies import get_current_user
from app.core.exceptions import ValidationError
from app.models.billing_models import (
    InvoiceCreate, InvoiceUpdate, PaymentRecord,
    TutorPaymentCreate, TutorPaymentApproval, BulkPaymentApproval,
    ReportGeneration, InvoiceResponse, TutorPaymentResponse,
    BillingSearchFilters
)
from app.models.user_models import User
from app.models.base import PaymentStatus, TutorPaymentStatus

router = APIRouter(prefix="/billing")


@router.post("/invoices", response_model=InvoiceResponse)
@require_roles(["manager"])
async def create_invoice(
    invoice_data: InvoiceCreate,
    current_user: User = Depends(get_current_user)
):
    """
    Create a new invoice with comprehensive validation.
    
    Args:
        invoice_data: Invoice creation data
        current_user: Current authenticated user
    
    Returns:
        Created invoice
    """
    try:
        billing_service = BillingService()
        validation_service = FinancialValidationService()
        
        # Validate invoice data
        validated_data = validation_service.validate_invoice_data({
            'client_id': invoice_data.client_id,
            'currency': invoice_data.currency,
            'subtotal': invoice_data.subtotal,
            'tax_amount': invoice_data.tax_amount,
            'total_amount': invoice_data.total_amount,
            'due_date': invoice_data.due_date,
            'line_items': invoice_data.line_items
        })
        
        # Calculate taxes if needed
        if invoice_data.calculate_tax and invoice_data.province:
            tax_calculation = validation_service.validate_tax_calculation(
                subtotal=validated_data['subtotal'],
                province=invoice_data.province,
                tax_exempt=invoice_data.tax_exempt or False
            )
            validated_data['tax_amount'] = tax_calculation['total_tax']
            validated_data['total_amount'] = validated_data['subtotal'] + tax_calculation['total_tax']
        
        # Create invoice with validated data
        invoice = await billing_service.create_invoice(
            invoice_data=validated_data,
            created_by=current_user.user_id
        )
        
        return invoice
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to create invoice: {str(e)}"
        )


@router.post("/invoices/generate", response_model=Optional[InvoiceResponse])
@require_roles(["manager"])
async def generate_invoice_from_sessions(
    client_id: int,
    session_date_start: date,
    session_date_end: date,
    auto_generate: bool = True,
    current_user: User = Depends(get_current_user)
):
    """
    Generate invoice from completed tutoring sessions.
    
    Args:
        client_id: Client to bill
        session_date_start: Start date for session range
        session_date_end: End date for session range  
        auto_generate: Whether to actually create invoice or just preview
        current_user: Current authenticated user
    
    Returns:
        Generated invoice or preview data
    """
    try:
        billing_service = BillingService()
        
        invoice = await billing_service.generate_invoice_from_completed_sessions(
            client_id=client_id,
            session_date_start=session_date_start,
            session_date_end=session_date_end,
            created_by=current_user.user_id,
            auto_generate=auto_generate
        )
        
        if not invoice:
            return None
        
        return invoice
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to generate invoice: {str(e)}"
        )


@router.get("/invoices/{invoice_id}", response_model=InvoiceResponse)
@require_auth
async def get_invoice(
    invoice_id: int = Path(..., description="Invoice ID"),
    current_user: User = Depends(get_current_user)
):
    """Get invoice by ID."""
    try:
        billing_service = BillingService()
        invoice = await billing_service.get_invoice_by_id(invoice_id)
        
        if not invoice:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Invoice not found"
            )
        
        # Authorization check - clients can only see their own invoices
        if current_user.role == "client" and invoice.client_id != current_user.user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        return invoice
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to get invoice: {str(e)}"
        )


@router.post("/invoices/{invoice_id}/payments", response_model=InvoiceResponse)
@require_roles(["manager", "client"])
async def record_payment(
    invoice_id: int = Path(..., description="Invoice ID"),
    payment_data: PaymentRecord = ...,
    current_user: User = Depends(get_current_user)
):
    """Record payment against an invoice."""
    try:
        billing_service = BillingService()
        validation_service = FinancialValidationService()
        
        # Validate payment data
        if payment_data.invoice_id != invoice_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invoice ID mismatch"
            )
        
        # Validate financial data
        validated_payment = validation_service.validate_payment_data({
            'amount': payment_data.amount,
            'currency': payment_data.currency,
            'payment_method': payment_data.payment_method,
            'invoice_id': payment_data.invoice_id
        })
        
        # Update payment data with validated values
        payment_data.amount = validated_payment['amount']
        payment_data.currency = validated_payment['currency']
        
        invoice = await billing_service.record_payment(
            payment_data=payment_data,
            recorded_by=current_user.user_id
        )
        
        return invoice
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to record payment: {str(e)}"
        )


@router.get("/invoices/{invoice_id}/download")
@require_auth
async def download_invoice(
    invoice_id: int = Path(..., description="Invoice ID"),
    current_user: User = Depends(get_current_user)
):
    """
    Download invoice as PDF.
    
    Args:
        invoice_id: Invoice ID to download
        current_user: Current authenticated user
        
    Returns:
        PDF file stream
    """
    try:
        billing_service = BillingService()
        
        # Get invoice to check permissions
        invoice = await billing_service.get_invoice_by_id(invoice_id)
        if not invoice:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Invoice not found"
            )
        
        # Authorization check - clients can only download their own invoices
        if current_user.role == "client" and invoice.client_id != current_user.user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # For now, return a JSON response indicating PDF generation would happen
        # In production, this would generate and return an actual PDF
        return {
            "message": "PDF generation not yet implemented",
            "invoice_id": invoice_id,
            "invoice_number": invoice.invoice_number,
            "client_name": invoice.client_name,
            "total_amount": float(invoice.total_amount),
            "status": invoice.status
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to download invoice: {str(e)}"
        )


@router.post("/tutor-payments", response_model=TutorPaymentResponse)
@require_roles(["manager"])
async def create_tutor_payment(
    payment_data: TutorPaymentCreate,
    current_user: User = Depends(get_current_user)
):
    """Create tutor payment for completed sessions."""
    try:
        billing_service = BillingService()
        
        payment = await billing_service.create_tutor_payment(
            payment_data=payment_data,
            created_by=current_user.user_id
        )
        
        return payment
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to create tutor payment: {str(e)}"
        )


@router.get("/tutor-payments/{payment_id}", response_model=TutorPaymentResponse)
@require_auth
async def get_tutor_payment(
    payment_id: int = Path(..., description="Payment ID"),
    current_user: User = Depends(get_current_user)
):
    """Get tutor payment by ID."""
    try:
        billing_service = BillingService()
        payment = await billing_service.get_tutor_payment_by_id(payment_id)
        
        if not payment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Payment not found"
            )
        
        # Authorization check - tutors can only see their own payments
        if current_user.role == "tutor" and payment.tutor_id != current_user.user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        return payment
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to get payment: {str(e)}"
        )


@router.post("/tutor-payments/approve", response_model=List[TutorPaymentResponse])
@require_roles(["manager"])
async def approve_tutor_payments(
    approval_data: BulkPaymentApproval,
    current_user: User = Depends(get_current_user)
):
    """Approve multiple tutor payments."""
    try:
        billing_service = BillingService()
        
        approved_payments = await billing_service.approve_tutor_payments(
            approval_data=approval_data,
            approved_by=current_user.user_id
        )
        
        return approved_payments
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to approve payments: {str(e)}"
        )


@router.get("/payment-periods/{year}", response_model=List[dict])
@require_roles(["manager"])
async def get_weekly_payment_periods(
    year: int = Path(..., description="Year to get periods for"),
    current_user: User = Depends(get_current_user)
):
    """Get all weekly payment periods for a year (Thursday to Wednesday)."""
    try:
        billing_service = BillingService()
        periods = await billing_service.get_weekly_payment_periods(year)
        return periods
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to get payment periods: {str(e)}"
        )


@router.get("/reports/summary")
@require_roles(["manager"])
async def get_billing_summary(
    period_start: Optional[date] = Query(None, description="Start date for summary"),
    period_end: Optional[date] = Query(None, description="End date for summary"),
    current_user: User = Depends(get_current_user)
):
    """Get billing summary for a period."""
    try:
        billing_service = BillingService()
        
        # Use current month if no dates provided
        if not period_start:
            today = date.today()
            period_start = today.replace(day=1)
        
        if not period_end:
            if period_start.month == 12:
                period_end = period_start.replace(year=period_start.year + 1, month=1, day=1) - timedelta(days=1)
            else:
                period_end = period_start.replace(month=period_start.month + 1, day=1) - timedelta(days=1)
        
        # Implementation depends on service method
        summary = {
            "period_start": period_start,
            "period_end": period_end,
            "message": "Billing summary implementation needed"
        }
        
        return summary
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to get billing summary: {str(e)}"
        )


@router.get("/invoices")
@require_auth
async def list_invoices(
    client_id: Optional[int] = Query(None, description="Filter by client ID"),
    status: Optional[PaymentStatus] = Query(None, description="Filter by status"),
    date_from: Optional[date] = Query(None, description="Filter from date"),
    date_to: Optional[date] = Query(None, description="Filter to date"),
    limit: int = Query(50, le=100, description="Number of results"),
    offset: int = Query(0, ge=0, description="Results offset"),
    current_user: User = Depends(get_current_user)
):
    """
    List invoices with filtering and pagination.
    
    Clients can only see their own invoices, while managers can see all invoices.
    
    Args:
        client_id: Filter by client ID
        status: Filter by payment status (pending, paid, overdue, cancelled)
        date_from: Filter from issue date
        date_to: Filter to issue date
        limit: Number of results (max 100)
        offset: Results offset for pagination
        current_user: Current authenticated user
        
    Returns:
        Dictionary containing:
        - invoices: List of invoice objects
        - total: Total count matching filters
        - limit: Number of results per page
        - offset: Current offset
        - has_more: Whether more results exist
    """
    try:
        billing_service = BillingService()
        
        # For clients, restrict to their own invoices
        if current_user.role == "client":
            client_id = current_user.user_id
        
        # Get invoices from service
        result = await billing_service.list_invoices(
            client_id=client_id,
            status=status,
            date_from=date_from,
            date_to=date_to,
            limit=limit,
            offset=offset
        )
        
        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to list invoices: {str(e)}"
        )


@router.get("/tutor-payments")
@require_auth
async def list_tutor_payments(
    tutor_id: Optional[int] = Query(None, description="Filter by tutor ID"),
    status: Optional[TutorPaymentStatus] = Query(None, description="Filter by status"),
    period_start: Optional[date] = Query(None, description="Filter by period start"),
    period_end: Optional[date] = Query(None, description="Filter by period end"),
    limit: int = Query(50, le=100, description="Number of results"),
    offset: int = Query(0, ge=0, description="Results offset"),
    current_user: User = Depends(get_current_user)
):
    """
    List tutor payments with filtering and pagination.
    
    Tutors can only see their own payments, while managers can see all payments.
    
    Args:
        tutor_id: Filter by tutor ID
        status: Filter by payment status (pending, approved, processing, paid, rejected)
        period_start: Filter by payment period start date
        period_end: Filter by payment period end date
        limit: Number of results (max 100)
        offset: Results offset for pagination
        current_user: Current authenticated user
        
    Returns:
        Dictionary containing:
        - payments: List of tutor payment objects
        - total: Total count matching filters
        - limit: Number of results per page
        - offset: Current offset
        - has_more: Whether more results exist
    """
    try:
        billing_service = BillingService()
        
        # For tutors, restrict to their own payments
        if current_user.role == "tutor":
            tutor_id = current_user.user_id
        
        # Get payments from service
        result = await billing_service.list_tutor_payments(
            tutor_id=tutor_id,
            status=status,
            period_start=period_start,
            period_end=period_end,
            limit=limit,
            offset=offset
        )
        
        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to list tutor payments: {str(e)}"
        )


# Stripe Payment Endpoints

@router.post("/stripe/payment-intents")
@require_auth
async def create_payment_intent(
    amount: Decimal,
    currency: str = "CAD",
    invoice_id: Optional[int] = None,
    payment_method_types: Optional[List[str]] = None,
    current_user: User = Depends(get_current_user)
):
    """Create Stripe PaymentIntent for invoice payment."""
    try:
        stripe_service = StripeService()
        
        payment_intent = await stripe_service.create_payment_intent(
            amount=amount,
            currency=currency,
            client_id=current_user.user_id,
            invoice_id=invoice_id,
            payment_method_types=payment_method_types or ["card", "acss_debit"]
        )
        
        return payment_intent
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to create payment intent: {str(e)}"
        )


@router.post("/stripe/payment-intents/{payment_intent_id}/confirm")
@require_auth
async def confirm_payment(
    payment_intent_id: str = Path(..., description="PaymentIntent ID"),
    payment_method_id: str = ...,
    current_user: User = Depends(get_current_user)
):
    """Confirm Stripe payment with payment method."""
    try:
        stripe_service = StripeService()
        
        result = await stripe_service.confirm_payment(
            payment_intent_id=payment_intent_id,
            payment_method_id=payment_method_id
        )
        
        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to confirm payment: {str(e)}"
        )


@router.post("/stripe/webhooks")
async def handle_stripe_webhook(
    request: Request,
    stripe_signature: str = Header(None, alias="Stripe-Signature")
):
    """Handle Stripe webhook events."""
    try:
        payload = await request.body()
        
        if not stripe_signature:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Missing Stripe signature"
            )
        
        stripe_service = StripeService()
        result = await stripe_service.handle_webhook(payload, stripe_signature)
        
        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Webhook processing failed: {str(e)}"
        )


@router.post("/stripe/tutors/{tutor_id}/bank-account")
@require_roles(["manager", "tutor"])
async def setup_tutor_bank_account(
    tutor_id: int = Path(..., description="Tutor ID"),
    account_holder_name: str = ...,
    institution_number: str = ...,
    transit_number: str = ...,
    account_number: str = ...,
    account_holder_type: str = "individual",
    current_user: User = Depends(get_current_user)
):
    """Set up Canadian bank account for tutor payouts."""
    try:
        # Authorization check - tutors can only set up their own accounts
        if current_user.role == "tutor" and tutor_id != current_user.user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        stripe_service = StripeService()
        
        # Validate banking info
        validation = stripe_service.validate_canadian_banking_info(
            institution_number=institution_number,
            transit_number=transit_number,
            account_number=account_number
        )
        
        if not validation['valid']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid banking information: {', '.join(validation['errors'])}"
            )
        
        bank_account = await stripe_service.setup_canadian_bank_account(
            tutor_id=tutor_id,
            account_holder_name=account_holder_name,
            institution_number=institution_number,
            transit_number=transit_number,
            account_number=account_number,
            account_holder_type=account_holder_type
        )
        
        return bank_account
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to set up bank account: {str(e)}"
        )


@router.post("/stripe/tutors/{tutor_id}/payouts")
@require_roles(["manager"])
async def create_tutor_payout(
    tutor_id: int = Path(..., description="Tutor ID"),
    amount: Decimal = ...,
    bank_account_id: str = ...,
    description: str = "Weekly tutor payment",
    current_user: User = Depends(get_current_user)
):
    """Create payout to tutor's bank account."""
    try:
        stripe_service = StripeService()
        
        payout = await stripe_service.create_payout_to_tutor(
            tutor_id=tutor_id,
            amount=amount,
            currency="CAD",
            bank_account_id=bank_account_id,
            description=description
        )
        
        return payout
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to create payout: {str(e)}"
        )


@router.get("/stripe/clients/{client_id}/payment-methods")
@require_auth
async def get_client_payment_methods(
    client_id: int = Path(..., description="Client ID"),
    current_user: User = Depends(get_current_user)
):
    """Get saved payment methods for a client."""
    try:
        # Authorization check - clients can only see their own payment methods
        if current_user.role == "client" and client_id != current_user.user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        stripe_service = StripeService()
        payment_methods = await stripe_service.get_payment_methods_for_client(client_id)
        
        return {"payment_methods": payment_methods}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to get payment methods: {str(e)}"
        )


@router.post("/stripe/refunds")
@require_roles(["manager"])
async def create_refund(
    payment_intent_id: str,
    amount: Optional[Decimal] = None,
    reason: str = "requested_by_customer",
    current_user: User = Depends(get_current_user)
):
    """Create refund for a payment."""
    try:
        stripe_service = StripeService()
        
        refund = await stripe_service.refund_payment(
            payment_intent_id=payment_intent_id,
            amount=amount,
            reason=reason
        )
        
        return refund
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to create refund: {str(e)}"
        )


@router.get("/invoices/{invoice_id}/audit-logs")
@require_auth
async def get_invoice_audit_logs(
    invoice_id: int = Path(..., gt=0, description="ID of the invoice"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of logs to return"),
    offset: int = Query(0, ge=0, description="Number of logs to skip"),
    current_user: User = Depends(get_current_user)
):
    """
    Get audit logs for a specific invoice.
    
    Only managers can view audit logs for all invoices.
    Clients can only view audit logs for their own invoices.
    
    Returns:
        List of audit log entries in reverse chronological order
    """
    try:
        billing_service = BillingService()
        
        # Check authorization
        if current_user.role == "client":
            # Verify client owns the invoice
            invoice = await billing_service.get_invoice_by_id(invoice_id)
            if not invoice or invoice.client_id != current_user.user_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access denied"
                )
        elif current_user.role != "manager":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions"
            )
        
        # Get audit logs
        logs = await billing_service.get_billing_audit_logs(
            entity_type='invoice',
            entity_id=invoice_id,
            limit=limit,
            offset=offset
        )
        
        return {
            "logs": logs,
            "total": len(logs),
            "limit": limit,
            "offset": offset
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to get audit logs: {str(e)}"
        )


@router.get("/tutor-payments/{payment_id}/audit-logs")
@require_auth
async def get_tutor_payment_audit_logs(
    payment_id: int = Path(..., gt=0, description="ID of the tutor payment"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of logs to return"),
    offset: int = Query(0, ge=0, description="Number of logs to skip"),
    current_user: User = Depends(get_current_user)
):
    """
    Get audit logs for a specific tutor payment.
    
    Only managers can view audit logs for all payments.
    Tutors can only view audit logs for their own payments.
    
    Returns:
        List of audit log entries in reverse chronological order
    """
    try:
        billing_service = BillingService()
        
        # Check authorization
        if current_user.role == "tutor":
            # Verify tutor owns the payment
            payment = await billing_service.get_tutor_payment_by_id(payment_id)
            if not payment or payment.tutor_id != current_user.user_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access denied"
                )
        elif current_user.role != "manager":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions"
            )
        
        # Get audit logs
        logs = await billing_service.get_billing_audit_logs(
            entity_type='tutor_payment',
            entity_id=payment_id,
            limit=limit,
            offset=offset
        )
        
        return {
            "logs": logs,
            "total": len(logs),
            "limit": limit,
            "offset": offset
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to get audit logs: {str(e)}"
        )


@router.get("/subscriptions/{subscription_id}/audit-logs")
@require_auth
async def get_subscription_audit_logs(
    subscription_id: int = Path(..., gt=0, description="ID of the subscription"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of logs to return"),
    offset: int = Query(0, ge=0, description="Number of logs to skip"),
    current_user: User = Depends(get_current_user)
):
    """
    Get audit logs for a specific subscription.
    
    Only managers can view audit logs for all subscriptions.
    Clients can only view audit logs for their own subscriptions.
    
    Returns:
        List of audit log entries in reverse chronological order
    """
    try:
        billing_service = BillingService()
        
        # For authorization, we would need to verify ownership
        # For now, only managers can view subscription audit logs
        if current_user.role != "manager":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Only managers can view subscription audit logs"
            )
        
        # Get audit logs
        logs = await billing_service.get_billing_audit_logs(
            entity_type='subscription',
            entity_id=subscription_id,
            limit=limit,
            offset=offset
        )
        
        return {
            "logs": logs,
            "total": len(logs),
            "limit": limit,
            "offset": offset
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to get audit logs: {str(e)}"
        )


@router.get("/tutor-payments/current-period")
@require_roles(["manager"])
async def get_current_payment_period(
    reference_date: Optional[date] = Query(None, description="Reference date for period calculation"),
    current_user: User = Depends(get_current_user)
):
    """Get the current tutor payment period (Thursday to Wednesday)."""
    try:
        payment_service = TutorPaymentService()
        period = payment_service.get_current_payment_period(reference_date)
        return period
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to get payment period: {str(e)}"
        )


@router.get("/tutor-payments/upcoming-summary")
@require_roles(["manager"])
async def get_upcoming_payment_summary(
    current_user: User = Depends(get_current_user)
):
    """Get summary of the upcoming payment period."""
    try:
        payment_service = TutorPaymentService()
        summary = await payment_service.get_upcoming_payment_period_summary()
        return summary
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to get payment summary: {str(e)}"
        )


@router.post("/tutor-payments/generate-weekly")
@require_roles(["manager"])
async def generate_weekly_tutor_payments(
    period_start: Optional[date] = Query(None, description="Payment period start (Thursday)"),
    period_end: Optional[date] = Query(None, description="Payment period end (Wednesday)"),
    current_user: User = Depends(get_current_user)
):
    """
    Generate weekly tutor payments for all tutors.
    
    This will:
    1. Find all tutors with completed sessions in the period
    2. Calculate total hours and amounts for each tutor
    3. Create payment records in pending status
    4. Mark appointments as included in payments
    
    Args:
        period_start: Start of payment period (must be Thursday)
        period_end: End of payment period (must be Wednesday)
        current_user: Current authenticated user
    
    Returns:
        List of generated payments
    """
    try:
        payment_service = TutorPaymentService()
        
        payments = await payment_service.generate_weekly_payments(
            period_start=period_start,
            period_end=period_end,
            created_by=current_user.user_id
        )
        
        return {
            'payments_generated': len(payments),
            'payments': payments
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to generate payments: {str(e)}"
        )


@router.get("/tutor-payments/pending")
@require_roles(["manager"])
async def get_pending_tutor_payments(
    period_start: date = Query(..., description="Payment period start"),
    period_end: date = Query(..., description="Payment period end"),
    current_user: User = Depends(get_current_user)
):
    """Get all pending tutor payments for a specific period."""
    try:
        payment_service = TutorPaymentService()
        
        payments = await payment_service.get_pending_payments_for_period(
            period_start=period_start,
            period_end=period_end
        )
        
        return {
            'period': f"{period_start} to {period_end}",
            'payment_count': len(payments),
            'payments': payments
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to get pending payments: {str(e)}"
        )


@router.post("/tutor-payments/process-approved")
@require_roles(["manager"])
async def process_approved_payments(
    payment_ids: List[int] = Body(..., description="List of approved payment IDs to process"),
    current_user: User = Depends(get_current_user)
):
    """
    Process approved tutor payments through Stripe.
    
    This will:
    1. Validate each payment is approved and has bank info
    2. Create Stripe payouts to tutor bank accounts
    3. Update payment status to processing
    4. Return processing results
    
    Args:
        payment_ids: List of payment IDs to process
        current_user: Current authenticated user
    
    Returns:
        Processing results for each payment
    """
    try:
        payment_service = TutorPaymentService()
        
        results = await payment_service.process_approved_payments(
            payment_ids=payment_ids,
            processed_by=current_user.user_id
        )
        
        successful = sum(1 for r in results if r['success'])
        failed = len(results) - successful
        
        return {
            'total_processed': len(results),
            'successful': successful,
            'failed': failed,
            'results': results
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to process payments: {str(e)}"
        )


@router.get("/tutors/{tutor_id}/payment-history")
@require_auth
async def get_tutor_payment_history(
    tutor_id: int = Path(..., description="Tutor ID"),
    limit: int = Query(50, le=100, description="Number of results"),
    offset: int = Query(0, ge=0, description="Results offset"),
    current_user: User = Depends(get_current_user)
):
    """
    Get payment history for a specific tutor.
    
    Args:
        tutor_id: Tutor ID
        limit: Number of results to return
        offset: Results offset for pagination
        current_user: Current authenticated user
    
    Returns:
        Payment history with statistics
    """
    try:
        # Authorization check - tutors can only see their own history
        if current_user.role == "tutor" and tutor_id != current_user.user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        payment_service = TutorPaymentService()
        
        history = await payment_service.get_payment_history_for_tutor(
            tutor_id=tutor_id,
            limit=limit,
            offset=offset
        )
        
        return history
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to get payment history: {str(e)}"
        )


@router.post("/appointments/{appointment_id}/process-billing")
@require_roles(["manager"])
async def process_appointment_billing(
    appointment_id: int = Path(..., description="Appointment ID"),
    force_invoice: bool = Query(False, description="Force invoice even if subscription available"),
    current_user: User = Depends(get_current_user)
):
    """
    Process billing for a completed appointment using dual billing logic.
    
    The system will:
    1. Check if client has active subscription with enough hours
    2. If yes and not force_invoice, deduct from subscription
    3. If no or force_invoice, create invoice or add to pending invoice
    
    Args:
        appointment_id: Completed appointment to bill
        force_invoice: Force invoice creation even if subscription available
        current_user: Current authenticated user
    
    Returns:
        Billing result with method used and details
    """
    try:
        billing_service = BillingService()
        
        result = await billing_service.process_appointment_billing(
            appointment_id=appointment_id,
            force_invoice=force_invoice,
            performed_by=current_user.user_id
        )
        
        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to process billing: {str(e)}"
        )


@router.post("/appointments/{appointment_id}/rollback-billing")
@require_roles(["manager"])
async def rollback_appointment_billing(
    appointment_id: int = Path(..., description="Appointment ID"),
    reason: str = Body(..., description="Reason for rollback"),
    current_user: User = Depends(get_current_user)
):
    """
    Rollback billing for an appointment (subscription or invoice).
    
    This will:
    - For subscription billing: Restore the deducted hours
    - For invoice billing: Remove from invoice (if not paid)
    
    Args:
        appointment_id: Appointment to rollback billing
        reason: Reason for rollback
        current_user: Current authenticated user
    
    Returns:
        Rollback result
    """
    try:
        billing_service = BillingService()
        
        result = await billing_service.rollback_appointment_billing(
            appointment_id=appointment_id,
            reason=reason,
            rolled_back_by=current_user.user_id
        )
        
        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to rollback billing: {str(e)}"
        )


# Parent Payment Tracking Endpoints

@router.post("/invoices/{invoice_id}/split-by-parent")
@require_roles(["manager"])
async def split_invoice_by_parent(
    invoice_id: int = Path(..., description="Invoice ID"),
    parent_splits: List[Dict[str, Any]] = Body(..., description="Parent split configuration"),
    current_user: User = Depends(get_current_user)
):
    """
    Split an invoice between parents for separated families.
    
    Each split should contain:
    - parent_id: Parent ID
    - Either percentage (0-100) or amount
    
    The total must equal the invoice amount.
    
    Args:
        invoice_id: Invoice to split
        parent_splits: List of parent splits
        current_user: Current authenticated user
    
    Returns:
        Split result with parent assignments
    """
    try:
        parent_service = ParentPaymentService()
        
        result = await parent_service.split_invoice_by_parent(
            invoice_id=invoice_id,
            parent_splits=parent_splits,
            created_by=current_user.user_id
        )
        
        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to split invoice: {str(e)}"
        )


@router.post("/invoices/{invoice_id}/parent-payment")
@require_roles(["manager", "client"])
async def record_parent_payment(
    invoice_id: int = Path(..., description="Invoice ID"),
    parent_id: int = Body(..., description="Parent making payment"),
    amount: Decimal = Body(..., description="Payment amount"),
    payment_method: str = Body(..., description="Payment method"),
    payment_reference: Optional[str] = Body(None, description="Payment reference"),
    current_user: User = Depends(get_current_user)
):
    """
    Record payment from a specific parent.
    
    Args:
        invoice_id: Invoice being paid
        parent_id: Parent making payment
        amount: Payment amount
        payment_method: Payment method used
        payment_reference: Optional payment reference
        current_user: Current authenticated user
    
    Returns:
        Payment record details
    """
    try:
        parent_service = ParentPaymentService()
        
        result = await parent_service.record_parent_payment(
            invoice_id=invoice_id,
            parent_id=parent_id,
            amount=amount,
            payment_method=payment_method,
            payment_reference=payment_reference,
            recorded_by=current_user.user_id
        )
        
        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to record payment: {str(e)}"
        )


@router.get("/parents/{parent_id}/payment-history")
@require_auth
async def get_parent_payment_history(
    parent_id: int = Path(..., description="Parent ID"),
    date_from: Optional[date] = Query(None, description="Start date"),
    date_to: Optional[date] = Query(None, description="End date"),
    limit: int = Query(50, le=100, description="Number of results"),
    offset: int = Query(0, ge=0, description="Results offset"),
    current_user: User = Depends(get_current_user)
):
    """
    Get payment history for a specific parent.
    
    Args:
        parent_id: Parent ID
        date_from: Optional start date filter
        date_to: Optional end date filter
        limit: Number of results
        offset: Results offset
        current_user: Current authenticated user
    
    Returns:
        Payment history with statistics
    """
    try:
        # TODO: Add authorization check based on user role and relationship
        
        parent_service = ParentPaymentService()
        
        history = await parent_service.get_parent_payment_history(
            parent_id=parent_id,
            date_from=date_from,
            date_to=date_to,
            limit=limit,
            offset=offset
        )
        
        return history
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to get payment history: {str(e)}"
        )


@router.get("/invoices/{invoice_id}/payment-breakdown")
@require_auth
async def get_invoice_payment_breakdown(
    invoice_id: int = Path(..., description="Invoice ID"),
    current_user: User = Depends(get_current_user)
):
    """
    Get detailed payment breakdown for an invoice by parent.
    
    Shows:
    - Invoice splits if configured
    - Payments received from each parent
    - Outstanding amounts per parent
    
    Args:
        invoice_id: Invoice ID
        current_user: Current authenticated user
    
    Returns:
        Payment breakdown with parent details
    """
    try:
        parent_service = ParentPaymentService()
        
        breakdown = await parent_service.get_invoice_payment_breakdown(
            invoice_id=invoice_id
        )
        
        return breakdown
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to get payment breakdown: {str(e)}"
        )


@router.get("/parents/{parent_id}/balance-summary")
@require_auth
async def get_parent_balance_summary(
    parent_id: int = Path(..., description="Parent ID"),
    current_user: User = Depends(get_current_user)
):
    """
    Get balance summary for a parent across all their children.
    
    Shows:
    - Total amount owed based on splits
    - Total amount paid
    - Outstanding balance
    - List of outstanding invoices
    
    Args:
        parent_id: Parent ID
        current_user: Current authenticated user
    
    Returns:
        Balance summary with outstanding amounts
    """
    try:
        # TODO: Add authorization check
        
        parent_service = ParentPaymentService()
        
        summary = await parent_service.get_parent_balance_summary(
            parent_id=parent_id
        )
        
        return summary
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to get balance summary: {str(e)}"
        )