-- Check what tables exist in the database
-- This will help us understand the actual database structure

-- List all tables in the public schema
SELECT 
    table_name,
    table_type
FROM information_schema.tables
WHERE table_schema = 'public'
ORDER BY table_name;

-- Check if user_accounts table has any data
SELECT 
    COUNT(*) as user_count,
    COUNT(CASE WHEN email_verified = true THEN 1 END) as verified_count
FROM user_accounts;

-- Show first few users (without sensitive data)
SELECT 
    user_id,
    email,
    created_at,
    email_verified,
    deleted_at
FROM user_accounts
ORDER BY created_at DESC
LIMIT 5;

-- Check the structure of user_accounts table
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_name = 'user_accounts'
ORDER BY ordinal_position;

-- Check if there are any foreign key constraints on auth_tokens
SELECT
    conname AS constraint_name,
    pg_get_constraintdef(oid) AS constraint_definition
FROM pg_constraint
WHERE conrelid = 'auth_tokens'::regclass;