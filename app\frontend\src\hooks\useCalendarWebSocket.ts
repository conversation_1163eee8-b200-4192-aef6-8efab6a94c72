import { useCallback, useEffect, useRef } from 'react';
import { toast } from 'react-hot-toast';
import { useWebSocket, WebSocketMessage } from './useWebSocket';
import { useAuth } from '../contexts/AuthContext';

export interface CalendarWebSocketConfig {
  room?: string;
  onAppointmentCreated?: (appointment: any) => void;
  onAppointmentUpdated?: (appointment: any, changes: any) => void;
  onAppointmentCancelled?: (appointment: any, reason?: string) => void;
  onAppointmentConfirmed?: (appointment: any) => void;
  onConflictDetected?: (conflict: any) => void;
  onAvailabilityUpdated?: (tutorId: number, availability: any) => void;
  onTimeOffUpdated?: (timeOff: any) => void;
  enableNotifications?: boolean;
  enableToasts?: boolean;
}

export const useCalendarWebSocket = (config: CalendarWebSocketConfig = {}) => {
  const { user } = useAuth();
  const {
    enableNotifications = true,
    enableToasts = true,
    ...restConfig
  } = config;

  const websocket = useWebSocket({
    room: config.room,
    autoConnect: true,
    reconnectAttempts: 5,
    reconnectInterval: 2000,
  });

  const configRef = useRef(restConfig);
  configRef.current = restConfig;

  // Handle appointment created
  const handleAppointmentCreated = useCallback((message: WebSocketMessage) => {
    const { appointment, created_by } = message;
    
    if (configRef.current.onAppointmentCreated) {
      configRef.current.onAppointmentCreated(appointment);
    }

    if (enableToasts && created_by !== user?.id) {
      toast.success(
        `New appointment scheduled: ${appointment.subject_area} on ${appointment.scheduled_date}`,
        { duration: 4000 }
      );
    }

    if (enableNotifications && 'Notification' in window && Notification.permission === 'granted') {
      if (created_by !== user?.id) {
        new Notification('New Appointment Scheduled', {
          body: `${appointment.subject_area} on ${appointment.scheduled_date} at ${appointment.start_time}`,
          icon: '/favicon_tutoraide.png',
          tag: `appointment-${appointment.appointment_id}`,
        });
      }
    }
  }, [enableToasts, enableNotifications, user?.id]);

  // Handle appointment updated
  const handleAppointmentUpdated = useCallback((message: WebSocketMessage) => {
    const { appointment, changes, updated_by } = message;
    
    if (configRef.current.onAppointmentUpdated) {
      configRef.current.onAppointmentUpdated(appointment, changes);
    }

    if (enableToasts && updated_by !== user?.id) {
      const changesList = Object.keys(changes).join(', ');
      toast.info(
        `Appointment updated: ${appointment.subject_area} (${changesList})`,
        { duration: 4000 }
      );
    }

    if (enableNotifications && 'Notification' in window && Notification.permission === 'granted') {
      if (updated_by !== user?.id) {
        new Notification('Appointment Updated', {
          body: `${appointment.subject_area} has been modified`,
          icon: '/favicon_tutoraide.png',
          tag: `appointment-${appointment.appointment_id}`,
        });
      }
    }
  }, [enableToasts, enableNotifications, user?.id]);

  // Handle appointment cancelled
  const handleAppointmentCancelled = useCallback((message: WebSocketMessage) => {
    const { appointment, cancelled_by, reason } = message;
    
    if (configRef.current.onAppointmentCancelled) {
      configRef.current.onAppointmentCancelled(appointment, reason);
    }

    if (enableToasts && cancelled_by !== user?.id) {
      toast.error(
        `Appointment cancelled: ${appointment.subject_area}${reason ? ` - ${reason}` : ''}`,
        { duration: 5000 }
      );
    }

    if (enableNotifications && 'Notification' in window && Notification.permission === 'granted') {
      if (cancelled_by !== user?.id) {
        new Notification('Appointment Cancelled', {
          body: `${appointment.subject_area} on ${appointment.scheduled_date} has been cancelled`,
          icon: '/favicon_tutoraide.png',
          tag: `appointment-${appointment.appointment_id}`,
        });
      }
    }
  }, [enableToasts, enableNotifications, user?.id]);

  // Handle appointment confirmed
  const handleAppointmentConfirmed = useCallback((message: WebSocketMessage) => {
    const { appointment, confirmed_by } = message;
    
    if (configRef.current.onAppointmentConfirmed) {
      configRef.current.onAppointmentConfirmed(appointment);
    }

    if (enableToasts) {
      toast.success(
        `Appointment confirmed: ${appointment.subject_area}`,
        { duration: 3000 }
      );
    }

    if (enableNotifications && 'Notification' in window && Notification.permission === 'granted') {
      new Notification('Appointment Confirmed', {
        body: `${appointment.subject_area} on ${appointment.scheduled_date} has been confirmed`,
        icon: '/favicon_tutoraide.png',
        tag: `appointment-${appointment.appointment_id}`,
      });
    }
  }, [enableToasts, enableNotifications]);

  // Handle conflict detected
  const handleConflictDetected = useCallback((message: WebSocketMessage) => {
    const { conflict, severity } = message;
    
    if (configRef.current.onConflictDetected) {
      configRef.current.onConflictDetected(conflict);
    }

    if (enableToasts) {
      const toastOptions = { duration: 6000 };
      const conflictMessage = `Scheduling conflict detected: ${conflict.description || 'Time slot conflict'}`;
      
      if (severity === 'high') {
        toast.error(conflictMessage, toastOptions);
      } else if (severity === 'medium') {
        toast.error(conflictMessage, toastOptions);
      } else {
        toast.error(conflictMessage, toastOptions);
      }
    }

    if (enableNotifications && 'Notification' in window && Notification.permission === 'granted') {
      new Notification('Scheduling Conflict', {
        body: conflict.description || 'A scheduling conflict has been detected',
        icon: '/favicon_tutoraide.png',
        tag: `conflict-${conflict.conflict_id}`,
      });
    }
  }, [enableToasts, enableNotifications]);

  // Handle availability updated
  const handleAvailabilityUpdated = useCallback((message: WebSocketMessage) => {
    const { tutor_id, availability, updated_by } = message;
    
    if (configRef.current.onAvailabilityUpdated) {
      configRef.current.onAvailabilityUpdated(tutor_id, availability);
    }

    if (enableToasts && updated_by !== user?.id) {
      toast.info(
        `Tutor availability updated`,
        { duration: 3000 }
      );
    }
  }, [enableToasts, user?.id]);

  // Handle time-off updated
  const handleTimeOffUpdated = useCallback((message: WebSocketMessage) => {
    const { time_off } = message;
    
    if (configRef.current.onTimeOffUpdated) {
      configRef.current.onTimeOffUpdated(time_off);
    }

    if (enableToasts) {
      const action = message.type.includes('approved') ? 'approved' : 
                    message.type.includes('denied') ? 'denied' : 'requested';
      
      toast.info(
        `Time-off ${action}: ${time_off.reason}`,
        { duration: 4000 }
      );
    }
  }, [enableToasts]);

  // Request notification permission
  const requestNotificationPermission = useCallback(async () => {
    if ('Notification' in window && Notification.permission === 'default') {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    }
    return Notification.permission === 'granted';
  }, []);

  // Setup message handlers
  useEffect(() => {
    const unsubscribers = [
      websocket.addMessageHandler('appointment_created', handleAppointmentCreated),
      websocket.addMessageHandler('appointment_updated', handleAppointmentUpdated),
      websocket.addMessageHandler('appointment_cancelled', handleAppointmentCancelled),
      websocket.addMessageHandler('appointment_confirmed', handleAppointmentConfirmed),
      websocket.addMessageHandler('conflict_detected', handleConflictDetected),
      websocket.addMessageHandler('availability_updated', handleAvailabilityUpdated),
      websocket.addMessageHandler('time_off_requested', handleTimeOffUpdated),
      websocket.addMessageHandler('time_off_approved', handleTimeOffUpdated),
      websocket.addMessageHandler('time_off_denied', handleTimeOffUpdated),
    ];

    return () => {
      unsubscribers.forEach(unsubscribe => unsubscribe());
    };
  }, [
    websocket.addMessageHandler,
    handleAppointmentCreated,
    handleAppointmentUpdated,
    handleAppointmentCancelled,
    handleAppointmentConfirmed,
    handleConflictDetected,
    handleAvailabilityUpdated,
    handleTimeOffUpdated,
  ]);

  // Auto-request notification permission
  useEffect(() => {
    if (enableNotifications) {
      requestNotificationPermission();
    }
  }, [enableNotifications, requestNotificationPermission]);

  // Helper functions for sending calendar-specific messages
  const requestCalendarSync = useCallback(() => {
    websocket.sendMessage({
      type: 'request_calendar_sync',
      timestamp: new Date().toISOString(),
    });
  }, [websocket.sendMessage]);

  const joinDateRoom = useCallback((date: string) => {
    websocket.sendMessage({
      type: 'join_room',
      room_id: `date_${date}`,
      timestamp: new Date().toISOString(),
    });
  }, [websocket.sendMessage]);

  const leaveRoom = useCallback((roomId: string) => {
    websocket.sendMessage({
      type: 'leave_room',
      room_id: roomId,
      timestamp: new Date().toISOString(),
    });
  }, [websocket.sendMessage]);

  const reportConflict = useCallback((conflictData: any) => {
    websocket.sendMessage({
      type: 'report_conflict',
      conflict: conflictData,
      timestamp: new Date().toISOString(),
    });
  }, [websocket.sendMessage]);

  return {
    ...websocket,
    requestNotificationPermission,
    requestCalendarSync,
    joinDateRoom,
    leaveRoom,
    reportConflict,
  };
};

export default useCalendarWebSocket;