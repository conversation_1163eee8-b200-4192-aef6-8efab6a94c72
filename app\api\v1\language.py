"""
Language switching API endpoints for TutorAide.
"""

from typing import Dict, Any, List
from fastapi import APIRouter, Request, Response, Depends, HTTPException, status
from pydantic import BaseModel

from app.locales.constants import SUPPORTED_LANGUAGES, DEFAULT_LANGUAGE
from app.locales.dependencies import (
    CurrentLanguage, LanguageInfo, LanguageContext, 
    EnhancedLocalizedMessages, ValidationFunctions
)
from app.locales.middleware import (
    get_request_language, get_request_language_info,
    create_localized_json_response
)
from app.locales.translation_service import get_translation_service
from app.locales.validation import get_translation_validator
from app.core.dependencies import get_current_user
from app.core.auth_decorators import require_roles
from app.models.user_models import User
from app.models.base import UserRoleType
from app.models.user_preference_models import (
    UserPreferenceUpdate, UserPreferenceResponse,
    UserPreferenceStats, BulkPreferenceUpdate, BulkPreferenceUpdateResult
)
from app.models.language_preference_models import (
    LanguagePreferenceUpdateRequest, LanguagePreferenceResponse,
    LanguagePreferenceStats, BulkLanguageUpdate, BulkLanguageUpdateResult
)
from app.services.user_preference_service import get_user_preference_service
from app.services.language_preference_service import get_language_preference_service


router = APIRouter(prefix="/language", tags=["Language"])


class LanguageSwitchRequest(BaseModel):
    """Request model for language switching."""
    language: str
    persist: bool = True


class LanguageResponse(BaseModel):
    """Response model for language information."""
    current_language: str
    supported_languages: List[str]
    language_info: Dict[str, Any]
    available_translations: Dict[str, str]


class TranslationStatsResponse(BaseModel):
    """Response model for translation statistics."""
    languages: List[str]
    total_keys_by_language: Dict[str, int]
    completion_percentage: Dict[str, float]
    missing_keys_count: Dict[str, int]
    categories: List[str]


@router.get("/current", response_model=LanguageResponse)
async def get_current_language_info(
    request: Request,
    current_language: CurrentLanguage,
    language_info: LanguageInfo
) -> Dict[str, Any]:
    """Get current language information and available options."""
    translation_service = get_translation_service()
    
    # Get available languages with display names
    available_translations = {}
    for lang in SUPPORTED_LANGUAGES:
        # Get language name in current language
        name_key = f"quebec.languages.{'english' if lang == 'en' else 'french'}"
        available_translations[lang] = translation_service.get_translation(
            name_key, current_language
        )
    
    response_data = {
        "current_language": current_language,
        "supported_languages": SUPPORTED_LANGUAGES,
        "language_info": language_info,
        "available_translations": available_translations
    }
    
    return create_localized_json_response(request, response_data)


@router.post("/switch")
async def switch_language(
    request: Request,
    response: Response,
    switch_request: LanguageSwitchRequest,
    messages: EnhancedLocalizedMessages,
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """Switch user language preference with persistence."""
    
    preference_service = get_user_preference_service()
    
    # Validate requested language
    if switch_request.language not in SUPPORTED_LANGUAGES:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Unsupported language: {switch_request.language}. Supported: {SUPPORTED_LANGUAGES}"
        )
    
    # Set language cookie
    if switch_request.persist:
        response.set_cookie(
            key="tutoraide_language",
            value=switch_request.language,
            max_age=365 * 24 * 60 * 60,  # 1 year
            httponly=True,
            secure=request.url.scheme == "https",
            samesite="lax"
        )
    
    # Update user preference in database if authenticated
    preference_response = None
    if current_user and switch_request.persist:
        try:
            update_request = UserPreferenceUpdate(
                preferred_language=switch_request.language
            )
            preference_response = await preference_service.update_user_preferences(
                user_id=current_user.user_id,
                update_data=update_request
            )
        except Exception as e:
            # Log error but don't fail the request
            import logging
            logging.error(f"Failed to update user language preference: {e}")
    
    # Update request state for immediate effect
    request.state.language = switch_request.language
    request.state.language_info = {
        'language': switch_request.language,
        'source': 'manual_switch',
        'persisted': switch_request.persist,
        'user_preference_updated': preference_response is not None
    }
    
    response_data = {
        "success": True,
        "new_language": switch_request.language,
        "message": messages.success("language_switched", language=switch_request.language),
        "persisted": switch_request.persist,
        "preference_updated": preference_response is not None
    }
    
    if preference_response and preference_response.preferences:
        response_data["preference_info"] = {
            "quebec_french_preference": preference_response.preferences.quebec_french_preference,
            "updated_at": preference_response.preferences.updated_at.isoformat()
        }
    
    return create_localized_json_response(request, response_data)


@router.get("/translations/{namespace}")
async def get_namespace_translations(
    namespace: str,
    request: Request,
    current_language: CurrentLanguage
) -> Dict[str, Any]:
    """Get all translations for a specific namespace."""
    translation_service = get_translation_service()
    
    # Validate namespace
    valid_namespaces = [
        "common", "navigation", "auth", "users", "clients", "tutors",
        "appointments", "billing", "messages", "reports", "settings",
        "tecfee", "notifications", "email", "errors", "success",
        "plurals", "quebec", "templates", "validation", "accessibility"
    ]
    
    if namespace not in valid_namespaces:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid namespace: {namespace}. Valid namespaces: {valid_namespaces}"
        )
    
    # Get translations for namespace
    translations = translation_service.get_translations_for_namespace(namespace, current_language)
    
    response_data = {
        "namespace": namespace,
        "language": current_language,
        "translations": translations
    }
    
    return create_localized_json_response(request, response_data)


@router.get("/detect")
async def detect_language_info(
    request: Request,
    language_info: LanguageInfo
) -> Dict[str, Any]:
    """Get detailed language detection information for debugging."""
    
    response_data = {
        "detected_language": language_info.get('language', DEFAULT_LANGUAGE),
        "detection_source": language_info.get('source', 'unknown'),
        "quebec_french_detected": language_info.get('quebec_french', False),
        "fallback_used": language_info.get('fallback_used', False),
        "browser_preferences": language_info.get('browser_preferences', {}),
        "headers": {
            "accept_language": request.headers.get('accept-language', ''),
            "user_agent": request.headers.get('user-agent', '')
        },
        "cookies": {
            "language_cookie": request.cookies.get('tutoraide_language', None)
        }
    }
    
    return create_localized_json_response(request, response_data)


@router.get("/stats")
@require_roles([UserRoleType.MANAGER])
async def get_translation_statistics(
    request: Request,
    validation_functions: ValidationFunctions
) -> TranslationStatsResponse:
    """Get comprehensive translation statistics (manager only)."""
    
    stats = validation_functions["get_stats"]()
    
    response_data = {
        "languages": stats["languages"],
        "total_keys_by_language": stats["total_keys_by_language"],
        "completion_percentage": stats["completion_percentage"],
        "missing_keys_count": stats["missing_keys_by_language"],
        "categories": stats["categories"]
    }
    
    return create_localized_json_response(request, response_data)


@router.get("/validate")
@require_roles([UserRoleType.MANAGER])
async def validate_translations(
    request: Request,
    validation_functions: ValidationFunctions
) -> Dict[str, Any]:
    """Validate translation completeness and quality (manager only)."""
    
    validation_result = validation_functions["validate_translations"]()
    
    response_data = {
        "is_valid": validation_result.is_valid,
        "errors": validation_result.errors,
        "warnings": validation_result.warnings,
        "missing_keys": validation_result.missing_keys,
        "statistics": validation_result.statistics,
        "timestamp": validation_result.timestamp.isoformat()
    }
    
    return create_localized_json_response(request, response_data)


@router.post("/reload")
@require_roles([UserRoleType.MANAGER])
async def reload_translations(
    request: Request,
    messages: EnhancedLocalizedMessages
) -> Dict[str, Any]:
    """Reload translation files from disk (manager only)."""
    
    translation_service = get_translation_service()
    translation_service.reload_translations()
    
    response_data = {
        "success": True,
        "message": messages.success("translations_reloaded"),
        "timestamp": translation_service._translations.get('_reload_timestamp', 'unknown')
    }
    
    return create_localized_json_response(request, response_data)


@router.put("/preferences")
async def update_language_preferences(
    request: Request,
    update_request: LanguagePreferenceUpdateRequest,
    messages: EnhancedLocalizedMessages,
    current_user: User = Depends(get_current_user)
) -> LanguagePreferenceResponse:
    """Update user's comprehensive language preferences."""
    
    language_service = get_language_preference_service()
    
    try:
        response = await language_service.update_user_language_preference(
            user_id=current_user.user_id,
            update_request=update_request,
            request=request
        )
        return response
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/preferences")
async def get_language_preferences(
    request: Request,
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """Get user's current language preferences and context."""
    
    language_service = get_language_preference_service()
    
    try:
        language_context = await language_service.get_user_language_context(
            user_id=current_user.user_id,
            request=request
        )
        
        response_data = {
            "user_preference": language_context.user_preference.dict() if language_context.user_preference else None,
            "detected_language": language_context.detected_language.dict(),
            "effective_language": language_context.effective_language,
            "auto_detect_enabled": language_context.auto_detect_enabled,
            "quebec_french_indicators": language_context.quebec_french_indicators,
            "session_language": language_context.session_language
        }
        
        return create_localized_json_response(request, response_data)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get language preferences: {str(e)}"
        )


@router.post("/auto-detect")
async def auto_detect_language(
    request: Request,
    messages: EnhancedLocalizedMessages,
    current_user: User = Depends(get_current_user)
) -> LanguagePreferenceResponse:
    """Auto-detect and set language preference based on request context."""
    
    language_service = get_language_preference_service()
    
    try:
        response = await language_service.detect_and_set_language_preference(
            user_id=current_user.user_id,
            request=request
        )
        return response
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/preferences/history")
async def get_language_preference_history(
    request: Request,
    current_user: User = Depends(get_current_user),
    limit: int = 50,
    offset: int = 0
) -> Dict[str, Any]:
    """Get user's language preference change history."""
    
    language_service = get_language_preference_service()
    
    try:
        history, total_count = await language_service.repository.get_language_preference_history(
            user_id=current_user.user_id,
            limit=limit,
            offset=offset
        )
        
        response_data = {
            "history": [item.dict() for item in history],
            "total_count": total_count,
            "limit": limit,
            "offset": offset,
            "has_more": offset + len(history) < total_count
        }
        
        return create_localized_json_response(request, response_data)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get preference history: {str(e)}"
        )


@router.get("/preferences/stats")
@require_roles([UserRoleType.MANAGER])
async def get_language_preference_statistics(
    request: Request,
    current_user: User = Depends(get_current_user)
) -> LanguagePreferenceStats:
    """Get comprehensive language preference statistics (manager only)."""
    
    language_service = get_language_preference_service()
    
    try:
        stats = await language_service.get_language_preference_stats()
        return stats
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get preference statistics: {str(e)}"
        )


@router.post("/preferences/bulk-update")
@require_roles([UserRoleType.MANAGER])
async def bulk_update_language_preferences(
    request: Request,
    bulk_update: BulkLanguageUpdate,
    current_user: User = Depends(get_current_user)
) -> BulkLanguageUpdateResult:
    """Bulk update language preferences for multiple users (manager only)."""
    
    language_service = get_language_preference_service()
    
    try:
        result = await language_service.bulk_update_language_preferences(
            bulk_update=bulk_update,
            updated_by=current_user.user_id
        )
        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/users-by-language/{language}")
@require_roles([UserRoleType.MANAGER])
async def get_users_by_language_preference(
    language: str,
    request: Request,
    auto_detect_only: bool = False,
    quebec_french_only: bool = False,
    limit: int = 100,
    offset: int = 0,
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """Get users filtered by language preferences (manager only)."""
    
    language_service = get_language_preference_service()
    
    try:
        user_ids, total_count = await language_service.get_users_by_language(
            language=language,
            auto_detect_only=auto_detect_only,
            quebec_french_only=quebec_french_only,
            limit=limit,
            offset=offset
        )
        
        response_data = {
            "user_ids": user_ids,
            "total_count": total_count,
            "language": language,
            "filters": {
                "auto_detect_only": auto_detect_only,
                "quebec_french_only": quebec_french_only
            },
            "pagination": {
                "limit": limit,
                "offset": offset,
                "has_more": offset + len(user_ids) < total_count
            }
        }
        
        return create_localized_json_response(request, response_data)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/missing/{language}")
@require_roles([UserRoleType.MANAGER])
async def get_missing_translations(
    language: str,
    request: Request,
    validation_functions: ValidationFunctions
) -> Dict[str, Any]:
    """Get missing translation suggestions for a language (manager only)."""
    
    if language not in SUPPORTED_LANGUAGES:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Unsupported language: {language}"
        )
    
    suggestions = validation_functions["suggest_missing"](language)
    
    response_data = {
        "language": language,
        "missing_count": len(suggestions),
        "suggestions": suggestions[:50],  # Limit to 50 for UI performance
        "total_available": len(suggestions)
    }
    
    return create_localized_json_response(request, response_data)