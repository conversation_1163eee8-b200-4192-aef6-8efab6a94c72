"""
Service layer for messaging system (SMS, in-app chat, templates, broadcasts).
"""

from typing import List, Dict, Any, Optional
from datetime import datetime
import asyncpg
from decimal import Decimal

from app.core.exceptions import ValidationError, ResourceNotFoundError, BusinessLogicError
from app.core.logging import <PERSON><PERSON><PERSON><PERSON><PERSON>ogger
from app.services.twilio_service import TwilioService
from app.services.notification_service import NotificationService

logger = TutorAideLogger.get_logger(__name__)


class MessagingService:
    def __init__(self, twilio_service: TwilioService, notification_service: NotificationService):
        self.twilio_service = twilio_service
        self.notification_service = notification_service
    
    # Conversation Management
    async def get_conversations(
        self,
        db: asyncpg.Connection,
        user_id: int,
        user_role: str,
        conversation_type: Optional[str] = None,
        status: Optional[str] = None,
        limit: int = 50,
        offset: int = 0
    ) -> Dict[str, Any]:
        """Get conversations for a user based on their role."""
        
        # Build WHERE clause based on user role
        where_conditions = ["c.status != 'archived'"]
        params = []
        param_count = 0
        
        if user_role == 'manager':
            # Managers can see all conversations
            pass
        elif user_role == 'client':
            param_count += 1
            where_conditions.append(f"c.client_id = (SELECT client_id FROM client_profiles WHERE user_id = ${param_count})")
            params.append(user_id)
        elif user_role == 'tutor':
            param_count += 1
            where_conditions.append(f"c.tutor_id = (SELECT tutor_id FROM tutor_profiles WHERE user_id = ${param_count})")
            params.append(user_id)
        
        if conversation_type:
            param_count += 1
            where_conditions.append(f"c.conversation_type = ${param_count}")
            params.append(conversation_type)
        
        if status:
            param_count += 1
            where_conditions.append(f"c.status = ${param_count}")
            params.append(status)
        
        where_clause = " AND ".join(where_conditions)
        
        # Get total count
        count_query = f"""
            SELECT COUNT(*) 
            FROM messaging_conversations c
            WHERE {where_clause}
        """
        total_count = await db.fetchval(count_query, *params)
        
        # Get conversations with participant details
        param_count += 1
        params.append(limit)
        param_count += 1
        params.append(offset)
        
        query = f"""
            SELECT 
                c.conversation_id,
                c.conversation_type,
                c.client_id,
                c.tutor_id,
                c.manager_id,
                c.phone_number,
                c.subject,
                c.status,
                c.last_message_at,
                c.unread_count,
                c.created_at,
                -- Client details
                cp.first_name as client_first_name,
                cp.last_name as client_last_name,
                -- Tutor details  
                tp.first_name as tutor_first_name,
                tp.last_name as tutor_last_name,
                -- Manager details
                u.email as manager_email,
                -- Last message
                m.content as last_message_content,
                m.sender_type as last_message_sender_type,
                m.created_at as last_message_created_at
            FROM messaging_conversations c
            LEFT JOIN client_profiles cp ON c.client_id = cp.client_id
            LEFT JOIN tutor_profiles tp ON c.tutor_id = tp.tutor_id
            LEFT JOIN user_accounts u ON c.manager_id = u.user_id
            LEFT JOIN LATERAL (
                SELECT content, sender_type, created_at
                FROM messaging_messages
                WHERE conversation_id = c.conversation_id
                ORDER BY created_at DESC
                LIMIT 1
            ) m ON TRUE
            WHERE {where_clause}
            ORDER BY COALESCE(c.last_message_at, c.created_at) DESC
            LIMIT ${param_count - 1}
            OFFSET ${param_count}
        """
        
        rows = await db.fetch(query, *params)
        
        conversations = []
        for row in rows:
            conversation = dict(row)
            
            # Format participant info
            if conversation['client_id']:
                conversation['participant_name'] = f"{conversation['client_first_name']} {conversation['client_last_name']}"
                conversation['participant_role'] = 'client'
            elif conversation['tutor_id']:
                conversation['participant_name'] = f"{conversation['tutor_first_name']} {conversation['tutor_last_name']}"
                conversation['participant_role'] = 'tutor'
            else:
                conversation['participant_name'] = conversation['manager_email']
                conversation['participant_role'] = 'manager'
            
            conversations.append(conversation)
        
        return {
            "conversations": conversations,
            "total": total_count,
            "limit": limit,
            "offset": offset,
            "has_more": offset + limit < total_count
        }
    
    async def get_conversation_messages(
        self,
        db: asyncpg.Connection,
        conversation_id: int,
        user_id: int,
        user_role: str,
        limit: int = 50,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """Get messages in a conversation."""
        
        # Verify user has access to conversation
        if not await self._user_can_access_conversation(db, conversation_id, user_id, user_role):
            raise ResourceNotFoundError("Conversation not found")
        
        # Mark messages as read
        await self._mark_messages_as_read(db, conversation_id, user_id)
        
        # Get messages
        query = """
            SELECT 
                m.message_id,
                m.sender_type,
                m.sender_id,
                m.message_type,
                m.content,
                m.role_indicator,
                m.is_read,
                m.read_at,
                m.delivery_status,
                m.delivered_at,
                m.created_at,
                m.edited_at,
                -- Sender details
                CASE 
                    WHEN m.sender_type = 'client' THEN cp.first_name || ' ' || cp.last_name
                    WHEN m.sender_type = 'tutor' THEN tp.first_name || ' ' || tp.last_name
                    WHEN m.sender_type = 'manager' THEN u.email
                    ELSE 'System'
                END as sender_name
            FROM messaging_messages m
            LEFT JOIN user_accounts u ON m.sender_id = u.user_id
            LEFT JOIN client_profiles cp ON u.user_id = cp.user_id AND m.sender_type = 'client'
            LEFT JOIN tutor_profiles tp ON u.user_id = tp.user_id AND m.sender_type = 'tutor'
            WHERE m.conversation_id = $1 AND m.deleted_at IS NULL
            ORDER BY m.created_at DESC
            LIMIT $2 OFFSET $3
        """
        
        rows = await db.fetch(query, conversation_id, limit, offset)
        
        messages = []
        for row in rows:
            message = dict(row)
            
            # Determine if message is outgoing for current user
            if user_role == 'manager':
                message['is_outgoing'] = message['sender_type'] == 'manager' and message['sender_id'] == user_id
            elif user_role == 'client':
                message['is_outgoing'] = message['sender_type'] == 'client'
            elif user_role == 'tutor':
                message['is_outgoing'] = message['sender_type'] == 'tutor'
            else:
                message['is_outgoing'] = False
            
            messages.append(message)
        
        # Return in chronological order for display
        return list(reversed(messages))
    
    async def send_message(
        self,
        db: asyncpg.Connection,
        conversation_id: int,
        sender_id: int,
        sender_type: str,
        content: str,
        message_type: str = 'text'
    ) -> Dict[str, Any]:
        """Send a message in a conversation."""
        
        # Verify conversation exists and get details
        conversation = await db.fetchrow(
            "SELECT * FROM messaging_conversations WHERE conversation_id = $1",
            conversation_id
        )
        if not conversation:
            raise ResourceNotFoundError("Conversation not found")
        
        # Set role indicator
        role_indicator = None
        if sender_type == 'tutor':
            role_indicator = '👨‍🏫 TUTOR'
        elif sender_type == 'client':
            role_indicator = '👤 CLIENT'
        
        # Insert message
        message_id = await db.fetchval("""
            INSERT INTO messaging_messages (
                conversation_id, sender_type, sender_id,
                message_type, content, role_indicator,
                delivery_status
            ) VALUES ($1, $2, $3, $4, $5, $6, 'sent')
            RETURNING message_id
        """, conversation_id, sender_type, sender_id, message_type, content, role_indicator)
        
        # Update conversation last message
        await db.execute("""
            UPDATE messaging_conversations 
            SET last_message_at = NOW(), 
                unread_count = unread_count + 1,
                updated_at = NOW()
            WHERE conversation_id = $1
        """, conversation_id)
        
        # Send SMS if conversation type is SMS
        if conversation['conversation_type'] == 'sms' and conversation['phone_number']:
            try:
                twilio_response = await self.twilio_service.send_sms(
                    to_phone=conversation['phone_number'],
                    message=content
                )
                
                # Update message with Twilio details
                await db.execute("""
                    UPDATE messaging_messages
                    SET twilio_message_sid = $1,
                        sms_status = 'sent',
                        delivery_status = 'delivered',
                        delivered_at = NOW()
                    WHERE message_id = $2
                """, twilio_response.sid, message_id)
                
            except Exception as e:
                logger.error(f"Failed to send SMS: {str(e)}")
                await db.execute("""
                    UPDATE messaging_messages
                    SET delivery_status = 'failed',
                        sms_error_message = $1
                    WHERE message_id = $2
                """, str(e), message_id)
        
        # Send push notification for in-app messages
        elif conversation['conversation_type'] == 'in_app':
            recipient_id = None
            if sender_type != 'client' and conversation['client_id']:
                recipient_id = await db.fetchval(
                    "SELECT user_id FROM client_profiles WHERE client_id = $1",
                    conversation['client_id']
                )
            elif sender_type != 'tutor' and conversation['tutor_id']:
                recipient_id = await db.fetchval(
                    "SELECT user_id FROM tutor_profiles WHERE tutor_id = $1",
                    conversation['tutor_id']
                )
            
            if recipient_id:
                await self.notification_service.send_push_notification(
                    user_id=recipient_id,
                    title="New Message",
                    body=content[:100] + "..." if len(content) > 100 else content,
                    data={"conversation_id": str(conversation_id)}
                )
        
        # Return the created message
        return await db.fetchrow("""
            SELECT * FROM messaging_messages WHERE message_id = $1
        """, message_id)
    
    async def create_conversation(
        self,
        db: asyncpg.Connection,
        conversation_type: str,
        client_id: Optional[int] = None,
        tutor_id: Optional[int] = None,
        manager_id: Optional[int] = None,
        phone_number: Optional[str] = None,
        subject: Optional[str] = None
    ) -> int:
        """Create a new conversation."""
        
        # Validate at least one participant
        if not any([client_id, tutor_id, manager_id]):
            raise ValidationError("At least one participant is required")
        
        # For SMS conversations, phone number is required
        if conversation_type == 'sms' and not phone_number:
            raise ValidationError("Phone number is required for SMS conversations")
        
        # Check if conversation already exists
        existing_conditions = ["conversation_type = $1"]
        existing_params = [conversation_type]
        param_count = 1
        
        if client_id:
            param_count += 1
            existing_conditions.append(f"client_id = ${param_count}")
            existing_params.append(client_id)
        
        if tutor_id:
            param_count += 1
            existing_conditions.append(f"tutor_id = ${param_count}")
            existing_params.append(tutor_id)
        
        if phone_number:
            param_count += 1
            existing_conditions.append(f"phone_number = ${param_count}")
            existing_params.append(phone_number)
        
        existing_query = f"""
            SELECT conversation_id FROM messaging_conversations
            WHERE {' AND '.join(existing_conditions)} AND status != 'archived'
        """
        
        existing_id = await db.fetchval(existing_query, *existing_params)
        if existing_id:
            return existing_id
        
        # Create new conversation
        conversation_id = await db.fetchval("""
            INSERT INTO messaging_conversations (
                conversation_type, client_id, tutor_id, manager_id,
                phone_number, subject, status
            ) VALUES ($1, $2, $3, $4, $5, $6, 'active')
            RETURNING conversation_id
        """, conversation_type, client_id, tutor_id, manager_id, phone_number, subject)
        
        logger.info(f"Created conversation {conversation_id}")
        return conversation_id
    
    # Template Management
    async def get_templates(
        self,
        db: asyncpg.Connection,
        template_type: Optional[str] = None,
        category: Optional[str] = None,
        is_active: bool = True
    ) -> List[Dict[str, Any]]:
        """Get message templates."""
        
        where_conditions = []
        params = []
        param_count = 0
        
        if template_type:
            param_count += 1
            where_conditions.append(f"template_type = ${param_count}")
            params.append(template_type)
        
        if category:
            param_count += 1
            where_conditions.append(f"category = ${param_count}")
            params.append(category)
        
        if is_active is not None:
            param_count += 1
            where_conditions.append(f"is_active = ${param_count}")
            params.append(is_active)
        
        where_clause = " AND ".join(where_conditions) if where_conditions else "TRUE"
        
        query = f"""
            SELECT * FROM messaging_templates
            WHERE {where_clause}
            ORDER BY category, template_name
        """
        
        rows = await db.fetch(query, *params)
        return [dict(row) for row in rows]
    
    async def create_template(
        self,
        db: asyncpg.Connection,
        template_data: Dict[str, Any],
        created_by: int
    ) -> int:
        """Create a new message template."""
        
        # Check if template name already exists
        existing = await db.fetchval("""
            SELECT template_id FROM messaging_templates
            WHERE template_name = $1 AND template_type = $2
        """, template_data['template_name'], template_data['template_type'])
        
        if existing:
            raise ValidationError("Template with this name and type already exists")
        
        template_id = await db.fetchval("""
            INSERT INTO messaging_templates (
                template_name, template_type, category,
                subject, content_en, content_fr,
                variables, created_by
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            RETURNING template_id
        """, 
            template_data['template_name'],
            template_data['template_type'],
            template_data['category'],
            template_data.get('subject'),
            template_data['content_en'],
            template_data['content_fr'],
            template_data.get('variables', {}),
            created_by
        )
        
        logger.info(f"Created template {template_id}")
        return template_id
    
    # Broadcast Management
    async def get_broadcasts(
        self,
        db: asyncpg.Connection,
        status: Optional[str] = None,
        limit: int = 20,
        offset: int = 0
    ) -> Dict[str, Any]:
        """Get broadcast messages."""
        
        where_conditions = []
        params = []
        
        if status:
            where_conditions.append("status = $1")
            params.append(status)
        
        where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""
        
        # Get total count
        count_query = f"SELECT COUNT(*) FROM messaging_broadcasts{where_clause}"
        total_count = await db.fetchval(count_query, *params)
        
        # Get broadcasts
        params.extend([limit, offset])
        query = f"""
            SELECT 
                b.*,
                u.email as created_by_email,
                a.email as approved_by_email
            FROM messaging_broadcasts b
            JOIN user_accounts u ON b.created_by = u.user_id
            LEFT JOIN user_accounts a ON b.approved_by = a.user_id
            {where_clause}
            ORDER BY b.created_at DESC
            LIMIT ${len(params) - 1} OFFSET ${len(params)}
        """
        
        rows = await db.fetch(query, *params)
        
        return {
            "broadcasts": [dict(row) for row in rows],
            "total": total_count,
            "limit": limit,
            "offset": offset,
            "has_more": offset + limit < total_count
        }
    
    async def create_broadcast(
        self,
        db: asyncpg.Connection,
        broadcast_data: Dict[str, Any],
        created_by: int
    ) -> int:
        """Create a new broadcast message."""
        
        broadcast_id = await db.fetchval("""
            INSERT INTO messaging_broadcasts (
                title, message_type, content_en, content_fr,
                recipient_type, recipient_filters,
                status, scheduled_at, created_by
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            RETURNING broadcast_id
        """,
            broadcast_data['title'],
            broadcast_data['message_type'],
            broadcast_data['content_en'],
            broadcast_data['content_fr'],
            broadcast_data['recipient_type'],
            broadcast_data.get('recipient_filters', {}),
            'draft',
            broadcast_data.get('scheduled_at'),
            created_by
        )
        
        # Calculate recipient count
        recipient_count = await self._calculate_broadcast_recipients(
            db,
            broadcast_data['recipient_type'],
            broadcast_data.get('recipient_filters', {})
        )
        
        await db.execute("""
            UPDATE messaging_broadcasts
            SET recipient_count = $1
            WHERE broadcast_id = $2
        """, recipient_count, broadcast_id)
        
        logger.info(f"Created broadcast {broadcast_id} with {recipient_count} recipients")
        return broadcast_id
    
    # Helper methods
    async def _user_can_access_conversation(
        self,
        db: asyncpg.Connection,
        conversation_id: int,
        user_id: int,
        user_role: str
    ) -> bool:
        """Check if user can access a conversation."""
        
        if user_role == 'manager':
            return True
        
        conversation = await db.fetchrow(
            "SELECT client_id, tutor_id FROM messaging_conversations WHERE conversation_id = $1",
            conversation_id
        )
        
        if not conversation:
            return False
        
        if user_role == 'client':
            client_id = await db.fetchval(
                "SELECT client_id FROM client_profiles WHERE user_id = $1",
                user_id
            )
            return client_id == conversation['client_id']
        
        elif user_role == 'tutor':
            tutor_id = await db.fetchval(
                "SELECT tutor_id FROM tutor_profiles WHERE user_id = $1",
                user_id
            )
            return tutor_id == conversation['tutor_id']
        
        return False
    
    async def _mark_messages_as_read(
        self,
        db: asyncpg.Connection,
        conversation_id: int,
        user_id: int
    ) -> None:
        """Mark messages in a conversation as read."""
        
        await db.execute("""
            UPDATE messaging_messages
            SET is_read = TRUE, read_at = NOW()
            WHERE conversation_id = $1 
              AND sender_id != $2
              AND is_read = FALSE
        """, conversation_id, user_id)
        
        # Reset unread count
        await db.execute("""
            UPDATE messaging_conversations
            SET unread_count = 0
            WHERE conversation_id = $1
        """, conversation_id)
    
    async def _calculate_broadcast_recipients(
        self,
        db: asyncpg.Connection,
        recipient_type: str,
        filters: Dict[str, Any]
    ) -> int:
        """Calculate number of recipients for a broadcast."""
        
        if recipient_type == 'all':
            return await db.fetchval("SELECT COUNT(*) FROM user_accounts WHERE is_active = TRUE")
        
        elif recipient_type == 'clients':
            query = """
                SELECT COUNT(DISTINCT u.user_id)
                FROM user_accounts u
                JOIN user_roles ur ON u.user_id = ur.user_id
                WHERE u.is_active = TRUE AND ur.role_name = 'client'
            """
            return await db.fetchval(query)
        
        elif recipient_type == 'tutors':
            query = """
                SELECT COUNT(DISTINCT u.user_id)
                FROM user_accounts u
                JOIN user_roles ur ON u.user_id = ur.user_id
                WHERE u.is_active = TRUE AND ur.role_name = 'tutor'
            """
            return await db.fetchval(query)
        
        elif recipient_type == 'managers':
            query = """
                SELECT COUNT(DISTINCT u.user_id)
                FROM user_accounts u
                JOIN user_roles ur ON u.user_id = ur.user_id
                WHERE u.is_active = TRUE AND ur.role_name = 'manager'
            """
            return await db.fetchval(query)
        
        return 0


