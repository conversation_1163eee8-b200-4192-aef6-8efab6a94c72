import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Card } from '../../common/Card';
import Button from '../../common/Button';
import { Input } from '../../common/Input';
import { MapPin, Plus, Trash2, Save, AlertCircle, Navigation } from 'lucide-react';
import toast from 'react-hot-toast';

interface ServiceArea {
  areaId?: number;
  postalCode: string;
  maxDistanceKm: number;
  isEditing?: boolean;
}

interface ServiceAreasFormProps {
  tutorId: number;
}

export const ServiceAreasForm: React.FC<ServiceAreasFormProps> = ({ tutorId }) => {
  const { t } = useTranslation();
  const [areas, setAreas] = useState<ServiceArea[]>([]);
  const [newArea, setNewArea] = useState<ServiceArea>({ postalCode: '', maxDistanceKm: 10 });
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);

  // Mock data - replace with API calls
  useEffect(() => {
    const mockAreas = [
      { areaId: 1, postalCode: 'H3H 2R9', maxDistanceKm: 15 },
      { areaId: 2, postalCode: 'H4A', maxDistanceKm: 10 },
      { areaId: 3, postalCode: 'H3A', maxDistanceKm: 20 },
    ];
    setAreas(mockAreas);
  }, [tutorId]);

  const handleAddArea = () => {
    if (!newArea.postalCode.trim()) {
      toast.error(t('tutor.areas.errors.postalCodeRequired'));
      return;
    }

    if (newArea.maxDistanceKm <= 0 || newArea.maxDistanceKm > 50) {
      toast.error(t('tutor.areas.errors.invalidDistance'));
      return;
    }

    // Check for duplicates
    const isDuplicate = areas.some(
      area => area.postalCode.toUpperCase() === newArea.postalCode.toUpperCase()
    );
    if (isDuplicate) {
      toast.error(t('tutor.areas.errors.duplicatePostalCode'));
      return;
    }

    setAreas([...areas, { ...newArea, isEditing: true }]);
    setNewArea({ postalCode: '', maxDistanceKm: 10 });
    setShowAddForm(false);
  };

  const handleRemoveArea = (index: number) => {
    setAreas(areas.filter((_, i) => i !== index));
  };

  const handleDistanceChange = (index: number, value: string) => {
    const updatedAreas = [...areas];
    updatedAreas[index].maxDistanceKm = parseInt(value) || 0;
    setAreas(updatedAreas);
  };

  const formatPostalCode = (value: string) => {
    // Canadian postal code format: A1A 1A1
    const cleaned = value.toUpperCase().replace(/[^A-Z0-9]/g, '');
    if (cleaned.length <= 3) return cleaned;
    return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)}`;
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      // TODO: Implement API call to save areas
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success(t('tutor.areas.saveSuccess'));
      
      // Mark all as not editing
      setAreas(areas.map(a => ({ ...a, isEditing: false })));
    } catch (error) {
      toast.error(t('tutor.areas.saveError'));
    } finally {
      setIsSaving(false);
    }
  };

  const getDistanceColor = (distance: number) => {
    if (distance <= 5) return 'text-green-600 bg-green-50';
    if (distance <= 15) return 'text-yellow-600 bg-yellow-50';
    return 'text-orange-600 bg-orange-50';
  };

  return (
    <div className="space-y-6">
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">
            {t('tutor.areas.title')}
          </h3>
          <MapPin className="h-5 w-5 text-gray-400" />
        </div>
        
        <p className="text-sm text-gray-500 mb-6">
          {t('tutor.areas.subtitle')}
        </p>

        {/* Current Service Areas */}
        <div className="space-y-4 mb-6">
          {areas.map((area, index) => (
            <div key={`${area.areaId}-${index}`} className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
              <div className="w-12 h-12 bg-accent-red bg-opacity-10 rounded-full flex items-center justify-center">
                <MapPin className="h-6 w-6 text-accent-red" />
              </div>
              <div className="flex-1">
                <p className="font-medium text-gray-900 text-lg">
                  {area.postalCode}
                </p>
                <div className="flex items-center gap-2 mt-1">
                  <Navigation className="h-4 w-4 text-gray-400" />
                  <span className="text-sm text-gray-500">
                    {t('tutor.areas.maxDistance')}:
                  </span>
                  <span className={`px-2 py-1 text-xs rounded-full ${getDistanceColor(area.maxDistanceKm)}`}>
                    {area.maxDistanceKm} km
                  </span>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <div className="flex items-center gap-1">
                  <Input
                    type="number"
                    min="1"
                    max="50"
                    value={area.maxDistanceKm}
                    onChange={(e) => handleDistanceChange(index, e.target.value)}
                    className="w-20"
                  />
                  <span className="text-sm text-gray-500">km</span>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleRemoveArea(index)}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>

        {/* Add New Area */}
        {showAddForm ? (
          <div className="border-t pt-6">
            <h4 className="text-sm font-medium text-gray-900 mb-3">
              {t('tutor.areas.addArea')}
            </h4>
            <div className="flex gap-3">
              <div className="flex-1">
                <Input
                  placeholder={t('tutor.areas.postalCodePlaceholder')}
                  value={newArea.postalCode}
                  onChange={(e) => setNewArea({
                    ...newArea,
                    postalCode: formatPostalCode(e.target.value)
                  })}
                  maxLength={7}
                />
              </div>
              <div className="flex items-center gap-1">
                <Input
                  type="number"
                  min="1"
                  max="50"
                  value={newArea.maxDistanceKm}
                  onChange={(e) => setNewArea({
                    ...newArea,
                    maxDistanceKm: parseInt(e.target.value) || 0
                  })}
                  className="w-20"
                />
                <span className="text-sm text-gray-500">km</span>
              </div>
              <Button onClick={handleAddArea}>
                {t('common.add')}
              </Button>
              <Button
                variant="secondary"
                onClick={() => {
                  setShowAddForm(false);
                  setNewArea({ postalCode: '', maxDistanceKm: 10 });
                }}
              >
                {t('common.cancel')}
              </Button>
            </div>
            <p className="text-xs text-gray-500 mt-2">
              {t('tutor.areas.distanceHelp')}
            </p>
          </div>
        ) : (
          <Button
            variant="secondary"
            onClick={() => setShowAddForm(true)}
            className="w-full flex items-center justify-center gap-2"
          >
            <Plus className="h-4 w-4" />
            {t('tutor.areas.addServiceArea')}
          </Button>
        )}

        {areas.length === 0 && !showAddForm && (
          <div className="text-center py-8">
            <MapPin className="h-12 w-12 text-gray-300 mx-auto mb-3" />
            <p className="text-gray-500">{t('tutor.areas.noAreas')}</p>
            <p className="text-sm text-gray-400 mt-1">
              {t('tutor.areas.noAreasDescription')}
            </p>
          </div>
        )}
      </Card>

      {/* Map Preview Placeholder */}
      <Card className="p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          {t('tutor.areas.mapPreview')}
        </h3>
        <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
          <div className="text-center">
            <MapPin className="h-12 w-12 text-gray-300 mx-auto mb-3" />
            <p className="text-gray-500">{t('tutor.areas.mapComingSoon')}</p>
          </div>
        </div>
      </Card>

      {/* Info Box */}
      <Card className="p-4 bg-blue-50 border-blue-200">
        <div className="flex">
          <AlertCircle className="h-5 w-5 text-blue-600 flex-shrink-0 mt-0.5" />
          <div className="ml-3">
            <p className="text-sm text-blue-800">
              {t('tutor.areas.info')}
            </p>
          </div>
        </div>
      </Card>

      {/* Save Button */}
      {areas.length > 0 && (
        <div className="flex justify-end">
          <Button
            onClick={handleSave}
            disabled={isSaving}
            className="flex items-center gap-2"
          >
            <Save className="h-4 w-4" />
            {isSaving ? t('common.saving') : t('common.save')}
          </Button>
        </div>
      )}
    </div>
  );
};