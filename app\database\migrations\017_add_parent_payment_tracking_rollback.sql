-- Rollback Migration: 017_add_parent_payment_tracking_rollback.sql
-- Description: Rollback parent payment tracking tables
-- Author: System
-- Date: 2024-01-15

-- Drop indexes
DROP INDEX IF EXISTS idx_invoice_payments_date;
DROP INDEX IF EXISTS idx_invoice_payments_parent;
DROP INDEX IF EXISTS idx_invoice_payments_invoice;
DROP INDEX IF EXISTS idx_invoice_splits_parent;
DROP INDEX IF EXISTS idx_invoice_splits_invoice;
DROP INDEX IF EXISTS idx_client_parents_parent;
DROP INDEX IF EXISTS idx_client_parents_client;
DROP INDEX IF EXISTS idx_parents_phone;
DROP INDEX IF EXISTS idx_parents_email;

-- Drop dependant_id column from invoices
ALTER TABLE invoices
DROP COLUMN IF EXISTS dependant_id;

-- Drop tables in reverse order of dependencies
DROP TABLE IF EXISTS invoice_parent_payments;
DROP TABLE IF EXISTS invoice_parent_splits;
DROP TABLE IF EXISTS client_parents;
DROP TABLE IF EXISTS parents;