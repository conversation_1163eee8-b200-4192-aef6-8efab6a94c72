-- Rollback: Dependant Management System

-- Drop views
DROP VIEW IF EXISTS dependant_summary;

-- Drop functions
DROP FUNCTION IF EXISTS get_primary_parent(INTEGER);
DROP FUNCTION IF EXISTS calculate_dependant_age(INTEGER);

-- Drop triggers
DROP TRIGGER IF EXISTS update_dependant_emergency_contacts_modtime ON dependant_emergency_contacts;
DROP TRIGGER IF EXISTS update_dependant_learning_profiles_modtime ON dependant_learning_profiles;
DROP TRIGGER IF EXISTS update_dependant_education_modtime ON dependant_education;
DROP TRIGGER IF EXISTS update_dependant_medical_info_modtime ON dependant_medical_info;
DROP TRIGGER IF EXISTS update_dependant_parents_modtime ON dependant_parents;
DROP TRIGGER IF EXISTS update_dependants_modtime ON dependants;

-- Drop indexes
DROP INDEX IF EXISTS idx_dependant_emergency_contacts_dependant_id;
DROP INDEX IF EXISTS idx_dependant_learning_profiles_dependant_id;
DROP INDEX IF EXISTS idx_dependant_education_grade;
DROP INDEX IF EXISTS idx_dependant_education_dependant_id;
DROP INDEX IF EXISTS idx_dependant_medical_info_dependant_id;
DROP INDEX IF EXISTS idx_dependant_parents_client_id;
DROP INDEX IF EXISTS idx_dependant_parents_dependant_id;
DROP INDEX IF EXISTS idx_dependants_fulltext;
DROP INDEX IF EXISTS idx_dependants_dob;
DROP INDEX IF EXISTS idx_dependants_last_name;

-- Drop tables in correct order (reverse of creation)
DROP TABLE IF EXISTS dependant_emergency_contacts;
DROP TABLE IF EXISTS dependant_learning_profiles;
DROP TABLE IF EXISTS dependant_education;
DROP TABLE IF EXISTS dependant_medical_info;
DROP TABLE IF EXISTS dependant_parents;
DROP TABLE IF EXISTS dependants;