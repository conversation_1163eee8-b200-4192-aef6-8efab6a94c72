"""
Cron job management API endpoints for manual triggering and monitoring.
"""

import logging
from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from app.core.dependencies import get_current_user
from app.core.authorization import require_role
from app.models.auth_models import UserRoleType
from app.services.appointment_cron_service import (
    run_appointment_cron_jobs,
    process_completed_appointments_job,
    send_24h_reminders_job,
    send_2h_reminders_job,
    auto_cancel_unconfirmed_job,
    process_no_shows_job,
    generate_missing_invoices_job
)
from app.core.dependencies import get_database
from app.core.service_dependencies import get_invoice_service, get_tutor_payment_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/cron", tags=["cron"])


@router.post("/run-all")
async def run_all_cron_jobs(
    current_user = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Manually run all appointment cron jobs.
    
    Only managers can trigger cron jobs.
    
    Returns results from all job executions.
    """
    try:
        # Only managers can run cron jobs
        require_role(current_user, [UserRoleType.MANAGER])
        
        logger.info(f"Manager {current_user.user_id} triggered all cron jobs")
        
        results = await run_appointment_cron_jobs()
        
        return {
            "message": "All cron jobs executed successfully",
            "results": results
        }
        
    except Exception as e:
        logger.error(f"Error running cron jobs: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to run cron jobs: {str(e)}")


@router.post("/process-completed")
async def process_completed_appointments(
    current_user = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Process completed appointments and send confirmation requests.
    
    Only managers can trigger this job.
    """
    try:
        require_role(current_user, [UserRoleType.MANAGER])
        
        logger.info(f"Manager {current_user.user_id} triggered completed appointments processing")
        
        result = await process_completed_appointments_job()
        
        return {
            "message": "Completed appointments processed",
            "result": result
        }
        
    except Exception as e:
        logger.error(f"Error processing completed appointments: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to process completed appointments: {str(e)}")


@router.post("/send-reminders")
async def send_reminders(
    hours_before: int = Query(..., description="Hours before appointment (24 or 2)"),
    current_user = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Send appointment reminders.
    
    Only managers can trigger this job.
    """
    try:
        require_role(current_user, [UserRoleType.MANAGER])
        
        if hours_before not in [24, 2]:
            raise HTTPException(status_code=400, detail="hours_before must be 24 or 2")
        
        logger.info(f"Manager {current_user.user_id} triggered {hours_before}h reminders")
        
        if hours_before == 24:
            result = await send_24h_reminders_job()
        else:
            result = await send_2h_reminders_job()
        
        return {
            "message": f"{hours_before}-hour reminders sent",
            "result": result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending reminders: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to send reminders: {str(e)}")


@router.post("/auto-cancel")
async def auto_cancel_appointments(
    current_user = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Auto-cancel unconfirmed appointments.
    
    Only managers can trigger this job.
    """
    try:
        require_role(current_user, [UserRoleType.MANAGER])
        
        logger.info(f"Manager {current_user.user_id} triggered auto-cancel job")
        
        result = await auto_cancel_unconfirmed_job()
        
        return {
            "message": "Auto-cancellation completed",
            "result": result
        }
        
    except Exception as e:
        logger.error(f"Error auto-cancelling appointments: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to auto-cancel appointments: {str(e)}")


@router.post("/process-no-shows")
async def process_no_shows(
    current_user = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Process no-show appointments.
    
    Only managers can trigger this job.
    """
    try:
        require_role(current_user, [UserRoleType.MANAGER])
        
        logger.info(f"Manager {current_user.user_id} triggered no-show processing")
        
        result = await process_no_shows_job()
        
        return {
            "message": "No-shows processed",
            "result": result
        }
        
    except Exception as e:
        logger.error(f"Error processing no-shows: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to process no-shows: {str(e)}")


@router.post("/generate-invoices")
async def generate_missing_invoices(
    current_user = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Generate missing invoices for completed appointments.
    
    Only managers can trigger this job.
    """
    try:
        require_role(current_user, [UserRoleType.MANAGER])
        
        logger.info(f"Manager {current_user.user_id} triggered invoice generation")
        
        result = await generate_missing_invoices_job()
        
        return {
            "message": "Invoices generated",
            "result": result
        }
        
    except Exception as e:
        logger.error(f"Error generating invoices: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to generate invoices: {str(e)}")


@router.get("/schedule")
async def get_cron_schedule(
    current_user = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get the current cron job schedule.
    
    Only managers can view the schedule.
    """
    try:
        require_role(current_user, [UserRoleType.MANAGER])
        
        # Return the cron schedule configuration
        return {
            "schedule": {
                "process_completed_appointments": {
                    "frequency": "Every 30 minutes",
                    "cron": "*/30 * * * *",
                    "description": "Check for completed appointments and request confirmation"
                },
                "send_24h_reminders": {
                    "frequency": "Every hour",
                    "cron": "0 * * * *",
                    "description": "Send 24-hour appointment reminders"
                },
                "send_2h_reminders": {
                    "frequency": "Every 30 minutes",
                    "cron": "*/30 * * * *",
                    "description": "Send 2-hour appointment reminders"
                },
                "auto_cancel_unconfirmed": {
                    "frequency": "Every 2 hours",
                    "cron": "0 */2 * * *",
                    "description": "Cancel appointments not confirmed by tutor within 24 hours"
                },
                "process_no_shows": {
                    "frequency": "Every hour",
                    "cron": "0 * * * *",
                    "description": "Mark old incomplete appointments as no-show"
                },
                "generate_missing_invoices": {
                    "frequency": "Every 4 hours",
                    "cron": "0 */4 * * *",
                    "description": "Generate invoices for completed appointments"
                }
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting cron schedule: {e}")
        raise HTTPException(status_code=500, detail="Failed to get cron schedule")


# Billing Cron Jobs

@router.post("/billing/generate-daily-invoices")
async def generate_daily_invoices(
    invoice_date: str = Query(None, description="Date to generate invoices for (YYYY-MM-DD, defaults to yesterday)"),
    current_user = Depends(get_current_user),
    db = Depends(get_database),
    invoice_service = Depends(get_invoice_service)
) -> Dict[str, Any]:
    """
    Generate daily invoices for completed appointments.
    
    This should be run daily (typically early morning) to process
    all completed appointments from the previous day.
    
    Only managers can trigger this job.
    """
    try:
        require_role(current_user, [UserRoleType.MANAGER])
        
        from datetime import date, datetime
        
        # Parse invoice_date if provided
        target_date = None
        if invoice_date:
            try:
                target_date = datetime.strptime(invoice_date, "%Y-%m-%d").date()
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid date format. Use YYYY-MM-DD")
        
        logger.info(f"Manager {current_user.user_id} triggered daily invoice generation for {target_date or 'yesterday'}")
        
        result = await invoice_service.generate_daily_invoices(db, target_date)
        
        return {
            "message": "Daily invoice generation completed",
            "result": {
                "invoice_date": result.invoice_date,
                "appointments_processed": result.appointments_processed,
                "invoices_created": result.invoices_created,
                "subscription_deductions": result.subscription_deductions,
                "total_amount": float(result.total_amount),
                "errors": result.errors,
                "created_invoices": [
                    {
                        "invoice_id": inv.invoice_id,
                        "invoice_number": inv.invoice_number,
                        "client_id": inv.client_id,
                        "total_amount": float(inv.total_amount)
                    }
                    for inv in result.invoices
                ]
            }
        }
        
    except Exception as e:
        logger.error(f"Error generating daily invoices: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to generate daily invoices: {str(e)}")


@router.post("/billing/calculate-weekly-payments")
async def calculate_weekly_payments(
    week_date: str = Query(None, description="Reference date for week calculation (YYYY-MM-DD, defaults to current week)"),
    current_user = Depends(get_current_user),
    db = Depends(get_database),
    payment_service = Depends(get_tutor_payment_service)
) -> Dict[str, Any]:
    """
    Calculate weekly tutor payments for Thursday-Wednesday cycle.
    
    This should be run weekly (typically on Thursday morning) to calculate
    payments for all tutors for the completed week.
    
    Only managers can trigger this job.
    """
    try:
        require_role(current_user, [UserRoleType.MANAGER])
        
        from datetime import date, datetime
        
        # Parse week_date if provided
        target_date = None
        if week_date:
            try:
                target_date = datetime.strptime(week_date, "%Y-%m-%d").date()
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid date format. Use YYYY-MM-DD")
        
        logger.info(f"Manager {current_user.user_id} triggered weekly payment calculation for {target_date or 'current week'}")
        
        report = await payment_service.calculate_weekly_payments(db, target_date)
        
        return {
            "message": "Weekly payment calculation completed",
            "result": {
                "week_start": report.week_start,
                "week_end": report.week_end,
                "total_tutors": report.total_tutors,
                "total_appointments": report.total_appointments,
                "total_service_amount": float(report.total_service_amount),
                "total_transport_amount": float(report.total_transport_amount),
                "total_bonus_amount": float(report.total_bonus_amount),
                "grand_total": float(report.grand_total),
                "batch_created": report.batch is not None,
                "batch_id": report.batch.batch_id if report.batch else None,
                "batch_number": report.batch.batch_number if report.batch else None,
                "tutor_summaries": report.tutor_summaries
            }
        }
        
    except Exception as e:
        logger.error(f"Error calculating weekly payments: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to calculate weekly payments: {str(e)}")


@router.post("/billing/send-overdue-reminders")
async def send_overdue_reminders(
    current_user = Depends(get_current_user),
    db = Depends(get_database),
    invoice_service = Depends(get_invoice_service)
) -> Dict[str, Any]:
    """
    Send reminders for overdue invoices.
    
    This should be run daily to send reminders for invoices
    that are past their due date.
    
    Only managers can trigger this job.
    """
    try:
        require_role(current_user, [UserRoleType.MANAGER])
        
        logger.info(f"Manager {current_user.user_id} triggered overdue invoice reminders")
        
        sent_count = await invoice_service.send_overdue_reminders(db)
        
        return {
            "message": "Overdue reminders sent",
            "result": {
                "reminders_sent": sent_count
            }
        }
        
    except Exception as e:
        logger.error(f"Error sending overdue reminders: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to send overdue reminders: {str(e)}")


@router.get("/billing/schedule")
async def get_billing_cron_schedule(
    current_user = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get the billing cron job schedule.
    
    Only managers can view the schedule.
    """
    try:
        require_role(current_user, [UserRoleType.MANAGER])
        
        # Return the billing cron schedule configuration
        return {
            "billing_schedule": {
                "generate_daily_invoices": {
                    "frequency": "Daily at 6:00 AM",
                    "cron": "0 6 * * *",
                    "description": "Generate invoices for all completed appointments from previous day"
                },
                "calculate_weekly_payments": {
                    "frequency": "Weekly on Thursday at 8:00 AM",
                    "cron": "0 8 * * 4",
                    "description": "Calculate tutor payments for Thursday-Wednesday cycle"
                },
                "send_overdue_reminders": {
                    "frequency": "Daily at 10:00 AM",
                    "cron": "0 10 * * *",
                    "description": "Send reminders for overdue invoices"
                }
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting billing cron schedule: {e}")
        raise HTTPException(status_code=500, detail="Failed to get billing cron schedule")