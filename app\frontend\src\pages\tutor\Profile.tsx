import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../contexts/AuthContext';
import { Card } from '../../components/common/Card';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import { 
  User,
  GraduationCap,
  Briefcase,
  Calendar,
  DollarSign,
  MapPin,
  Users,
  FileText,
  Shield
} from 'lucide-react';
import api from '../../services/api';
import toast from 'react-hot-toast';
import { PersonalInfoForm } from '../../components/tutor/profile/PersonalInfoForm';
import { EducationForm } from '../../components/tutor/profile/EducationForm';
import { ExperienceForm } from '../../components/tutor/profile/ExperienceForm';
import { AvailabilitySchedule } from '../../components/tutor/profile/AvailabilitySchedule';
import { ServiceRatesForm } from '../../components/tutor/profile/ServiceRatesForm';
import { ServiceAreasForm } from '../../components/tutor/profile/ServiceAreasForm';
import { ReferencesForm } from '../../components/tutor/profile/ReferencesForm';
import { DocumentsUpload } from '../../components/tutor/profile/DocumentsUpload';

interface TutorProfile {
  tutor_id: number;
  user_id: number;
  verification_status: string;
  bio: string;
  subjects: string[];
  languages_spoken: string[];
  hourly_rate: number;
  // Education fields
  highest_degree_level: string | null;
  highest_degree_name: string | null;
  degree_major: string | null;
  university_name: string | null;
  graduation_year: number | null;
  gpa: number | null;
  teaching_certifications: string[];
  // Experience fields
  years_of_experience: number;
  years_online_teaching: number;
  years_tutoring: number;
  students_taught_total: number;
  current_occupation: string | null;
  // Professional background
  teaching_languages: string[];
  teaching_methodology: string[];
  special_needs_experience: boolean;
  age_groups_experience: string[];
  // Verification fields
  background_check_status: string | null;
  background_check_date: string | null;
  working_with_children_clearance: boolean;
  police_check_expiry: string | null;
  // User info
  user: {
    first_name: string;
    last_name: string;
    email: string;
    phone_number: string;
    date_of_birth: string;
  };
}

export const Profile: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [profile, setProfile] = useState<TutorProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('personal');

  useEffect(() => {
    if (user?.id) {
      fetchProfile();
    }
  }, [user]);

  const fetchProfile = async () => {
    if (!user?.id) return;
    
    try {
      setLoading(true);
      const response = await api.get(`/api/v1/tutors/${user.id}/profile`);
      setProfile(response.data);
    } catch (error) {
      console.error('Error fetching profile:', error);
      toast.error(t('tutor.profile.fetchError'));
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = async (section: string, data: any) => {
    if (!user?.id) return;
    
    try {
      const response = await api.patch(`/api/v1/tutors/${user.id}/profile/${section}`, data);
      setProfile(response.data);
      toast.success(t('tutor.profile.updateSuccess'));
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error(t('tutor.profile.updateError'));
      throw error;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="p-8 text-center">
          <p className="text-text-secondary">{t('tutor.profile.notFound')}</p>
        </Card>
      </div>
    );
  }

  const tabs = [
    { id: 'personal', label: t('tutor.profile.tabs.personal'), icon: User },
    { id: 'education', label: t('tutor.profile.tabs.education'), icon: GraduationCap },
    { id: 'experience', label: t('tutor.profile.tabs.experience'), icon: Briefcase },
    { id: 'availability', label: t('tutor.profile.tabs.availability'), icon: Calendar },
    { id: 'rates', label: t('tutor.profile.tabs.rates'), icon: DollarSign },
    { id: 'areas', label: t('tutor.profile.tabs.areas'), icon: MapPin },
    { id: 'references', label: t('tutor.profile.tabs.references'), icon: Users },
    { id: 'documents', label: t('tutor.profile.tabs.documents'), icon: FileText },
  ];

  const getVerificationBadge = () => {
    const statusMap = {
      pending: { color: 'bg-yellow-100 text-yellow-800', icon: Shield },
      documents_submitted: { color: 'bg-blue-100 text-blue-800', icon: Shield },
      under_review: { color: 'bg-purple-100 text-purple-800', icon: Shield },
      verified: { color: 'bg-green-100 text-green-800', icon: Shield },
    };

    const status = statusMap[profile.verification_status as keyof typeof statusMap] || statusMap.pending;
    const Icon = status.icon;

    return (
      <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium ${status.color}`}>
        <Icon className="w-4 h-4" />
        {t(`tutor.profile.verificationStatus.${profile.verification_status}`)}
      </div>
    );
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-2">
          <h1 className="text-3xl font-bold text-text-primary">
            {t('tutor.profile.title')}
          </h1>
          {getVerificationBadge()}
        </div>
        <p className="text-text-secondary">
          {t('tutor.profile.subtitle')}
        </p>
      </div>

      {/* Tabs */}
      <div className="mb-6 border-b border-gray-200">
        <nav className="flex space-x-8 overflow-x-auto" aria-label="Tabs">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`
                  py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 whitespace-nowrap
                  ${activeTab === tab.id
                    ? 'border-accent-red text-accent-red'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                `}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <Card className="shadow-elevated">
        {activeTab === 'personal' && (
          <PersonalInfoForm
            profile={profile}
            onSave={(data) => updateProfile('personal', data)}
          />
        )}
        
        {activeTab === 'education' && (
          <EducationForm
            profile={profile}
            onSave={(data) => updateProfile('education', data)}
          />
        )}
        
        {activeTab === 'experience' && (
          <ExperienceForm
            profile={profile}
            onSave={(data) => updateProfile('experience', data)}
          />
        )}
        
        {activeTab === 'availability' && (
          <AvailabilitySchedule
            tutorId={profile.tutor_id}
          />
        )}
        
        {activeTab === 'rates' && (
          <ServiceRatesForm
            tutorId={profile.tutor_id}
          />
        )}
        
        {activeTab === 'areas' && (
          <ServiceAreasForm
            tutorId={profile.tutor_id}
          />
        )}
        
        {activeTab === 'references' && (
          <ReferencesForm
            tutorId={profile.tutor_id}
          />
        )}
        
        {activeTab === 'documents' && (
          <DocumentsUpload
            tutorId={profile.tutor_id}
            profile={profile}
          />
        )}
      </Card>
    </div>
  );
};

export default Profile;