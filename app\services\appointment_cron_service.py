"""
Cron service for automatic appointment status transitions and reminders.
"""

import asyncio
import logging
from datetime import datetime, timedelta, date
from typing import List, Dict, Any, Optional
import asyncpg

from app.config.database import get_db_connection
from app.core.timezone import now_est
from app.database.repositories.appointment_repository import AppointmentRepository
from app.models.appointment_models import AppointmentStatus
from app.services.appointment_service import AppointmentSchedulingService
from app.services.appointment_confirmation_service import AppointmentConfirmationService
from app.services.twilio_service import TwilioService
from app.services.billing_service import BillingService

logger = logging.getLogger(__name__)


class AppointmentCronService:
    """
    Service for automated appointment tasks:
    - Check past appointments and request completion confirmation
    - Send appointment reminders (24h and 2h before)
    - Auto-cancel unconfirmed appointments
    - Process no-shows
    - Generate invoices for completed appointments
    """
    
    def __init__(self):
        self.appointment_repo = AppointmentRepository()
        self.appointment_service = AppointmentSchedulingService()
        self.confirmation_service = AppointmentConfirmationService()
        self.twilio_service = TwilioService()
        self.billing_service = BillingService()
    
    async def run_all_jobs(self) -> Dict[str, Any]:
        """Run all cron jobs and return results."""
        results = {}
        
        try:
            # Process completed appointments
            results['completed_appointments'] = await self.process_completed_appointments()
            
            # Send 24-hour reminders
            results['reminders_24h'] = await self.send_24h_reminders()
            
            # Send 2-hour reminders
            results['reminders_2h'] = await self.send_2h_reminders()
            
            # Auto-cancel unconfirmed appointments
            results['auto_cancelled'] = await self.auto_cancel_unconfirmed()
            
            # Process no-shows
            results['no_shows'] = await self.process_no_shows()
            
            # Generate missing invoices
            results['invoices_generated'] = await self.generate_missing_invoices()
            
            logger.info(f"Cron jobs completed: {results}")
            return results
            
        except Exception as e:
            logger.error(f"Error running cron jobs: {e}")
            raise
    
    async def process_completed_appointments(self) -> Dict[str, Any]:
        """
        Find appointments that have ended and request completion confirmation.
        Runs every 30 minutes.
        """
        try:
            async with get_db_connection() as conn:
                # Find appointments that ended in the last 4 hours but haven't been marked complete
                cutoff_time = now_est() - timedelta(hours=4)
                
                query = """
                    SELECT a.appointment_id, a.tutor_id, a.client_id, a.subject_area,
                           a.scheduled_date, a.start_time, a.end_time, a.status,
                           t.phone_number as tutor_phone, t.first_name as tutor_name,
                           c.first_name || ' ' || c.last_name as client_name
                    FROM appointment_sessions a
                    JOIN tutors t ON a.tutor_id = t.tutor_id
                    JOIN clients c ON a.client_id = c.client_id
                    WHERE a.status IN ('scheduled', 'confirmed', 'in_progress')
                    AND (a.scheduled_date + a.end_time::interval) <= NOW()
                    AND (a.scheduled_date + a.end_time::interval) >= $1
                    AND NOT EXISTS (
                        SELECT 1 FROM appointment_confirmations ac
                        WHERE ac.appointment_id = a.appointment_id
                        AND ac.confirmation_type = 'completion'
                        AND ac.created_at >= $1
                    )
                    AND a.deleted_at IS NULL
                    ORDER BY a.scheduled_date, a.end_time
                    LIMIT 50
                """
                
                appointments = await conn.fetch(query, cutoff_time)
                
                sent_count = 0
                failed_count = 0
                
                for appointment in appointments:
                    try:
                        # Mark as in_progress if still scheduled
                        if appointment['status'] == AppointmentStatus.SCHEDULED:
                            await self.appointment_repo.update(
                                conn, 
                                appointment['appointment_id'],
                                {'status': AppointmentStatus.IN_PROGRESS}
                            )
                        
                        # Request completion confirmation
                        result = await self.confirmation_service.request_completion_confirmation(
                            appointment_id=appointment['appointment_id'],
                            send_immediately=False
                        )
                        
                        confirmation_token = result.get('confirmation_token', '')
                        
                        # Send SMS to tutor
                        if appointment['tutor_phone']:
                            message = (
                                f"Hi {appointment['tutor_name']}, your {appointment['subject_area']} "
                                f"session with {appointment['client_name']} has ended. "
                                f"Please confirm: Reply DONE if completed, NOSHOW if client didn't attend. "
                                f"Code: {confirmation_token[:8]}"
                            )
                            
                            await self.twilio_service.send_sms(
                                to_number=appointment['tutor_phone'],
                                message=message,
                                user_id=appointment['tutor_id'],
                                metadata={
                                    'appointment_id': appointment['appointment_id'],
                                    'type': 'completion_request'
                                }
                            )
                            
                            sent_count += 1
                            logger.info(f"Sent completion request for appointment {appointment['appointment_id']}")
                        
                    except Exception as e:
                        logger.error(f"Failed to process appointment {appointment['appointment_id']}: {e}")
                        failed_count += 1
                
                return {
                    'processed': len(appointments),
                    'sent': sent_count,
                    'failed': failed_count
                }
                
        except Exception as e:
            logger.error(f"Error processing completed appointments: {e}")
            raise
    
    async def send_24h_reminders(self) -> Dict[str, Any]:
        """Send 24-hour appointment reminders."""
        return await self._send_reminders(hours_before=24)
    
    async def send_2h_reminders(self) -> Dict[str, Any]:
        """Send 2-hour appointment reminders."""
        return await self._send_reminders(hours_before=2)
    
    async def _send_reminders(self, hours_before: int) -> Dict[str, Any]:
        """Send appointment reminders."""
        try:
            async with get_db_connection() as conn:
                # Find appointments needing reminders
                reminder_time_start = now_est() + timedelta(hours=hours_before, minutes=-15)
                reminder_time_end = now_est() + timedelta(hours=hours_before, minutes=15)
                
                reminder_field = f"reminder_sent_{hours_before}h"
                
                query = f"""
                    SELECT a.appointment_id, a.tutor_id, a.client_id, a.subject_area,
                           a.scheduled_date, a.start_time, a.location_details,
                           t.phone_number as tutor_phone, t.first_name as tutor_name,
                           c.phone_number as client_phone, c.first_name as client_name
                    FROM appointment_sessions a
                    JOIN tutors t ON a.tutor_id = t.tutor_id
                    JOIN clients c ON a.client_id = c.client_id
                    WHERE a.status IN ('scheduled', 'confirmed')
                    AND (a.scheduled_date + a.start_time::interval) BETWEEN $1 AND $2
                    AND (a.{reminder_field} IS FALSE OR a.{reminder_field} IS NULL)
                    AND a.deleted_at IS NULL
                    LIMIT 100
                """
                
                appointments = await conn.fetch(query, reminder_time_start, reminder_time_end)
                
                sent_count = 0
                
                for appointment in appointments:
                    try:
                        # Prepare reminder message
                        appointment_time = appointment['start_time'].strftime('%I:%M %p')
                        
                        if hours_before == 24:
                            message_template = (
                                "Reminder: You have a {subject} appointment tomorrow at {time}. "
                                "Location: {location}"
                            )
                        else:
                            message_template = (
                                "Reminder: Your {subject} appointment is in 2 hours at {time}. "
                                "Location: {location}"
                            )
                        
                        location = self._format_location(appointment['location_details'])
                        
                        # Send to tutor
                        if appointment['tutor_phone']:
                            tutor_message = message_template.format(
                                subject=appointment['subject_area'],
                                time=appointment_time,
                                location=location
                            )
                            
                            await self.twilio_service.send_sms(
                                to_number=appointment['tutor_phone'],
                                message=f"TutorAide: {tutor_message}",
                                user_id=appointment['tutor_id'],
                                metadata={
                                    'appointment_id': appointment['appointment_id'],
                                    'type': f'reminder_{hours_before}h'
                                }
                            )
                        
                        # Send to client
                        if appointment['client_phone']:
                            client_message = message_template.format(
                                subject=appointment['subject_area'],
                                time=appointment_time,
                                location=location
                            )
                            
                            await self.twilio_service.send_sms(
                                to_number=appointment['client_phone'],
                                message=f"TutorAide: {client_message}",
                                user_id=appointment['client_id'],
                                metadata={
                                    'appointment_id': appointment['appointment_id'],
                                    'type': f'reminder_{hours_before}h'
                                }
                            )
                        
                        # Mark reminder as sent
                        await conn.execute(
                            f"UPDATE appointment_sessions SET {reminder_field} = TRUE WHERE appointment_id = $1",
                            appointment['appointment_id']
                        )
                        
                        sent_count += 1
                        
                    except Exception as e:
                        logger.error(f"Failed to send reminder for appointment {appointment['appointment_id']}: {e}")
                
                return {
                    'appointments_found': len(appointments),
                    'reminders_sent': sent_count,
                    'hours_before': hours_before
                }
                
        except Exception as e:
            logger.error(f"Error sending {hours_before}h reminders: {e}")
            raise
    
    async def auto_cancel_unconfirmed(self) -> Dict[str, Any]:
        """
        Auto-cancel appointments that haven't been confirmed by tutor
        within 24 hours of scheduling.
        """
        try:
            async with get_db_connection() as conn:
                # Find unconfirmed appointments older than 24 hours
                cutoff_time = now_est() - timedelta(hours=24)
                
                query = """
                    SELECT appointment_id, tutor_id, client_id
                    FROM appointment_sessions
                    WHERE status = 'scheduled'
                    AND confirmed_by_tutor = FALSE
                    AND created_at <= $1
                    AND scheduled_date >= CURRENT_DATE
                    AND requires_confirmation = TRUE
                    AND deleted_at IS NULL
                    LIMIT 50
                """
                
                appointments = await conn.fetch(query, cutoff_time)
                
                cancelled_count = 0
                
                for appointment in appointments:
                    try:
                        await self.appointment_service.cancel_appointment(
                            appointment_id=appointment['appointment_id'],
                            reason="Auto-cancelled: Tutor did not confirm within 24 hours",
                            cancelled_by=0  # System user
                        )
                        
                        cancelled_count += 1
                        logger.info(f"Auto-cancelled unconfirmed appointment {appointment['appointment_id']}")
                        
                    except Exception as e:
                        logger.error(f"Failed to auto-cancel appointment {appointment['appointment_id']}: {e}")
                
                return {
                    'checked': len(appointments),
                    'cancelled': cancelled_count
                }
                
        except Exception as e:
            logger.error(f"Error auto-cancelling appointments: {e}")
            raise
    
    async def process_no_shows(self) -> Dict[str, Any]:
        """
        Mark appointments as no-show if they weren't completed or cancelled
        and are more than 2 hours past their end time.
        """
        try:
            async with get_db_connection() as conn:
                # Find appointments that should be marked as no-show
                cutoff_time = now_est() - timedelta(hours=2)
                
                query = """
                    SELECT appointment_id, tutor_id, client_id
                    FROM appointment_sessions
                    WHERE status IN ('scheduled', 'confirmed', 'in_progress')
                    AND (scheduled_date + end_time::interval) <= $1
                    AND deleted_at IS NULL
                    LIMIT 50
                """
                
                appointments = await conn.fetch(query, cutoff_time)
                
                no_show_count = 0
                
                for appointment in appointments:
                    try:
                        # Mark as no-show
                        await self.appointment_repo.update(
                            conn,
                            appointment['appointment_id'],
                            {
                                'status': AppointmentStatus.NO_SHOW,
                                'updated_at': now_est(),
                                'notes': 'Auto-marked as no-show: No completion confirmation received'
                            }
                        )
                        
                        no_show_count += 1
                        logger.info(f"Marked appointment {appointment['appointment_id']} as no-show")
                        
                    except Exception as e:
                        logger.error(f"Failed to mark appointment {appointment['appointment_id']} as no-show: {e}")
                
                return {
                    'checked': len(appointments),
                    'marked_no_show': no_show_count
                }
                
        except Exception as e:
            logger.error(f"Error processing no-shows: {e}")
            raise
    
    async def generate_missing_invoices(self) -> Dict[str, Any]:
        """
        Generate invoices for completed appointments that don't have one yet.
        """
        try:
            async with get_db_connection() as conn:
                # Find completed appointments without invoices
                query = """
                    SELECT a.appointment_id, a.tutor_id, a.client_id,
                           a.actual_duration_minutes, a.hourly_rate
                    FROM appointment_sessions a
                    WHERE a.status = 'completed'
                    AND a.completed_at >= NOW() - INTERVAL '7 days'
                    AND NOT EXISTS (
                        SELECT 1 FROM billing_invoices bi
                        WHERE bi.appointment_id = a.appointment_id
                    )
                    AND a.deleted_at IS NULL
                    LIMIT 50
                """
                
                appointments = await conn.fetch(query)
                
                invoices_created = 0
                
                for appointment in appointments:
                    try:
                        # Create invoice
                        result = await self.billing_service.create_invoice_for_appointment(
                            appointment_id=appointment['appointment_id'],
                            actual_duration_minutes=appointment['actual_duration_minutes']
                        )
                        
                        invoices_created += 1
                        logger.info(f"Created invoice for appointment {appointment['appointment_id']}")
                        
                    except Exception as e:
                        logger.error(f"Failed to create invoice for appointment {appointment['appointment_id']}: {e}")
                
                return {
                    'appointments_found': len(appointments),
                    'invoices_created': invoices_created
                }
                
        except Exception as e:
            logger.error(f"Error generating missing invoices: {e}")
            raise
    
    def _format_location(self, location_details: Dict[str, Any]) -> str:
        """Format location details for SMS."""
        if not location_details:
            return "TBD"
        
        location_type = location_details.get('type', 'online')
        
        if location_type == 'online':
            return "Online"
        elif location_type == 'in_person':
            address = location_details.get('address', 'TBD')
            return f"{address}"
        elif location_type == 'library':
            library = location_details.get('library_name', 'Library')
            return f"{library}"
        else:
            return "See appointment details"


# Standalone function to run as cron job
async def run_appointment_cron_jobs():
    """Run all appointment cron jobs."""
    try:
        service = AppointmentCronService()
        results = await service.run_all_jobs()
        logger.info(f"Appointment cron jobs completed: {results}")
        return results
    except Exception as e:
        logger.error(f"Error in appointment cron jobs: {e}")
        raise


# Individual job functions for granular scheduling
async def process_completed_appointments_job():
    """Process completed appointments - run every 30 minutes."""
    service = AppointmentCronService()
    return await service.process_completed_appointments()


async def send_24h_reminders_job():
    """Send 24-hour reminders - run every hour."""
    service = AppointmentCronService()
    return await service.send_24h_reminders()


async def send_2h_reminders_job():
    """Send 2-hour reminders - run every 30 minutes."""
    service = AppointmentCronService()
    return await service.send_2h_reminders()


async def auto_cancel_unconfirmed_job():
    """Auto-cancel unconfirmed appointments - run every 2 hours."""
    service = AppointmentCronService()
    return await service.auto_cancel_unconfirmed()


async def process_no_shows_job():
    """Process no-shows - run every hour."""
    service = AppointmentCronService()
    return await service.process_no_shows()


async def generate_missing_invoices_job():
    """Generate missing invoices - run every 4 hours."""
    service = AppointmentCronService()
    return await service.generate_missing_invoices()