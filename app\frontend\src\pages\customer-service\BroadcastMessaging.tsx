/**
 * Broadcast Messaging System
 * 
 * Comprehensive system for sending announcements and broadcast messages
 * to multiple users with scheduling, targeting, and delivery tracking.
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Typography,
  TextField,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  ListItemIcon,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Paper,
  IconButton,
  Tooltip,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  LinearProgress,
  Switch,
  FormControlLabel,
  Checkbox,
  FormGroup,
  Tabs,
  Tab,
  Badge,
  Avatar,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  Send as SendIcon,
  Schedule as ScheduleIcon,
  Group as GroupIcon,
  Notifications as NotificationsIcon,
  ExpandMore as ExpandMoreIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Add as AddIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Campaign as CampaignIcon,
  Target as TargetIcon,
  Analytics as AnalyticsIcon,
  Save as SaveIcon,
  Preview as PreviewIcon,
  History as HistoryIcon,
  Pause as PauseIcon,
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Filter as FilterIcon
} from '@mui/icons-material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { formatDistanceToNow, format } from 'date-fns';

import api from '../../services/api';
import { useTranslation } from '../../hooks/useTranslation';
import { useNotifications } from '../../hooks/useNotifications';

// Types
interface BroadcastMessage {
  broadcast_id: number;
  title: string;
  content: string;
  message_type: 'announcement' | 'alert' | 'promotion' | 'update';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  channels: string[];
  target_audience: TargetAudience;
  scheduled_at?: string;
  status: 'draft' | 'scheduled' | 'sending' | 'sent' | 'failed' | 'cancelled';
  created_by: string;
  created_at: string;
  sent_at?: string;
  delivery_stats?: DeliveryStats;
  template_id?: number;
}

interface TargetAudience {
  audience_type: 'all' | 'role_based' | 'custom' | 'conversation_participants';
  roles?: string[];
  user_ids?: number[];
  conversation_ids?: number[];
  filters?: AudienceFilters;
}

interface AudienceFilters {
  active_only?: boolean;
  languages?: string[];
  location?: string;
  subscription_status?: string;
  last_activity_days?: number;
}

interface DeliveryStats {
  total_recipients: number;
  sent_count: number;
  delivered_count: number;
  failed_count: number;
  opened_count: number;
  clicked_count: number;
}

interface BroadcastTemplate {
  template_id: number;
  template_name: string;
  content: string;
  message_type: string;
  variables: string[];
  usage_count: number;
}

// Main Component
const BroadcastMessaging: React.FC = () => {
  const { t } = useTranslation();
  const { showNotification } = useNotifications();
  const queryClient = useQueryClient();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // State
  const [currentTab, setCurrentTab] = useState(0);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editingBroadcast, setEditingBroadcast] = useState<BroadcastMessage | null>(null);
  const [previewDialogOpen, setPreviewDialogOpen] = useState(false);
  const [analyticsDialogOpen, setAnalyticsDialogOpen] = useState(false);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  
  // Form state
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    message_type: 'announcement',
    priority: 'normal',
    channels: ['in_app'],
    target_audience: {
      audience_type: 'all',
      roles: [],
      user_ids: [],
      conversation_ids: [],
      filters: {}
    },
    scheduled_at: null as Date | null,
    template_id: null as number | null
  });

  const [activeStep, setActiveStep] = useState(0);
  const [audiencePreview, setAudiencePreview] = useState<any[]>([]);

  // Queries
  const { data: broadcasts = [], isLoading, refetch } = useQuery({
    queryKey: ['broadcasts', statusFilter],
    queryFn: async () => {
      const params = new URLSearchParams();
      if (statusFilter !== 'all') params.append('status', statusFilter);
      return api.get(`/customer-service/broadcasts?${params}`);
    },
    refetchInterval: 30000
  });

  const { data: templates = [] } = useQuery({
    queryKey: ['broadcast-templates'],
    queryFn: () => api.get('/customer-service/broadcast-templates')
  });

  const { data: audienceStats } = useQuery({
    queryKey: ['audience-stats'],
    queryFn: () => api.get('/customer-service/audience-stats')
  });

  // Mutations
  const createBroadcastMutation = useMutation({
    mutationFn: (data: any) => api.post('/customer-service/broadcasts', data),
    onSuccess: () => {
      setCreateDialogOpen(false);
      resetForm();
      queryClient.invalidateQueries(['broadcasts']);
      showNotification('Broadcast created successfully', 'success');
    }
  });

  const updateBroadcastMutation = useMutation({
    mutationFn: ({ id, ...data }: any) => api.put(`/customer-service/broadcasts/${id}`, data),
    onSuccess: () => {
      setEditingBroadcast(null);
      queryClient.invalidateQueries(['broadcasts']);
      showNotification('Broadcast updated successfully', 'success');
    }
  });

  const sendBroadcastMutation = useMutation({
    mutationFn: (id: number) => api.post(`/customer-service/broadcasts/${id}/send`),
    onSuccess: () => {
      queryClient.invalidateQueries(['broadcasts']);
      showNotification('Broadcast sent successfully', 'success');
    }
  });

  const cancelBroadcastMutation = useMutation({
    mutationFn: (id: number) => api.post(`/customer-service/broadcasts/${id}/cancel`),
    onSuccess: () => {
      queryClient.invalidateQueries(['broadcasts']);
      showNotification('Broadcast cancelled', 'info');
    }
  });

  const previewAudienceMutation = useMutation({
    mutationFn: (audience: TargetAudience) => api.post('/customer-service/audience-preview', audience),
    onSuccess: (data) => {
      setAudiencePreview(data.users);
    }
  });

  // Handlers
  const resetForm = useCallback(() => {
    setFormData({
      title: '',
      content: '',
      message_type: 'announcement',
      priority: 'normal',
      channels: ['in_app'],
      target_audience: {
        audience_type: 'all',
        roles: [],
        user_ids: [],
        conversation_ids: [],
        filters: {}
      },
      scheduled_at: null,
      template_id: null
    });
    setActiveStep(0);
    setAudiencePreview([]);
  }, []);

  const handleCreateBroadcast = useCallback(() => {
    if (!formData.title.trim() || !formData.content.trim()) {
      showNotification('Please fill in all required fields', 'error');
      return;
    }

    createBroadcastMutation.mutate({
      ...formData,
      scheduled_at: formData.scheduled_at?.toISOString()
    });
  }, [formData, createBroadcastMutation, showNotification]);

  const handleEditBroadcast = useCallback((broadcast: BroadcastMessage) => {
    setEditingBroadcast(broadcast);
    setFormData({
      title: broadcast.title,
      content: broadcast.content,
      message_type: broadcast.message_type,
      priority: broadcast.priority,
      channels: broadcast.channels,
      target_audience: broadcast.target_audience,
      scheduled_at: broadcast.scheduled_at ? new Date(broadcast.scheduled_at) : null,
      template_id: broadcast.template_id || null
    });
    setCreateDialogOpen(true);
  }, []);

  const handleSendBroadcast = useCallback((id: number) => {
    sendBroadcastMutation.mutate(id);
  }, [sendBroadcastMutation]);

  const handlePreviewAudience = useCallback(() => {
    previewAudienceMutation.mutate(formData.target_audience);
  }, [formData.target_audience, previewAudienceMutation]);

  const handleTemplateSelect = useCallback((templateId: number) => {
    const template = templates.find((t: BroadcastTemplate) => t.template_id === templateId);
    if (template) {
      setFormData(prev => ({
        ...prev,
        content: template.content,
        message_type: template.message_type,
        template_id: templateId
      }));
    }
  }, [templates]);

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent': return 'success';
      case 'sending': return 'info';
      case 'scheduled': return 'warning';
      case 'failed': return 'error';
      case 'cancelled': return 'default';
      default: return 'default';
    }
  };

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'error';
      case 'high': return 'warning';
      case 'normal': return 'info';
      case 'low': return 'default';
      default: return 'default';
    }
  };

  // Render broadcast form steps
  const renderBroadcastForm = () => {
    const steps = [
      'Message Content',
      'Target Audience',
      'Scheduling & Channels',
      'Review & Send'
    ];

    return (
      <Dialog
        open={createDialogOpen}
        onClose={() => setCreateDialogOpen(false)}
        maxWidth="md"
        fullWidth
        fullScreen={isMobile}
      >
        <DialogTitle>
          {editingBroadcast ? 'Edit Broadcast' : 'Create New Broadcast'}
        </DialogTitle>
        
        <DialogContent>
          <Stepper activeStep={activeStep} orientation="vertical">
            {/* Step 1: Message Content */}
            <Step>
              <StepLabel>{t('broadcast.message_content')}</StepLabel>
              <StepContent>
                <Box sx={{ mb: 2 }}>
                  <FormControl fullWidth sx={{ mb: 2 }}>
                    <InputLabel>{t('broadcast.select_template')}</InputLabel>
                    <Select
                      value={formData.template_id || ''}
                      onChange={(e) => handleTemplateSelect(e.target.value as number)}
                    >
                      <MenuItem value="">{t('broadcast.custom_message')}</MenuItem>
                      {templates.map((template: BroadcastTemplate) => (
                        <MenuItem key={template.template_id} value={template.template_id}>
                          {template.template_name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>

                  <TextField
                    fullWidth
                    label={t('broadcast.title')}
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    sx={{ mb: 2 }}
                    required
                  />

                  <TextField
                    fullWidth
                    multiline
                    rows={6}
                    label={t('broadcast.content')}
                    value={formData.content}
                    onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                    sx={{ mb: 2 }}
                    required
                  />

                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <FormControl fullWidth>
                        <InputLabel>{t('broadcast.message_type')}</InputLabel>
                        <Select
                          value={formData.message_type}
                          onChange={(e) => setFormData(prev => ({ ...prev, message_type: e.target.value }))}
                        >
                          <MenuItem value="announcement">{t('broadcast.type.announcement')}</MenuItem>
                          <MenuItem value="alert">{t('broadcast.type.alert')}</MenuItem>
                          <MenuItem value="promotion">{t('broadcast.type.promotion')}</MenuItem>
                          <MenuItem value="update">{t('broadcast.type.update')}</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                    <Grid item xs={6}>
                      <FormControl fullWidth>
                        <InputLabel>{t('broadcast.priority')}</InputLabel>
                        <Select
                          value={formData.priority}
                          onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value }))}
                        >
                          <MenuItem value="low">{t('broadcast.priority.low')}</MenuItem>
                          <MenuItem value="normal">{t('broadcast.priority.normal')}</MenuItem>
                          <MenuItem value="high">{t('broadcast.priority.high')}</MenuItem>
                          <MenuItem value="urgent">{t('broadcast.priority.urgent')}</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                  </Grid>
                </Box>
                
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    variant="contained"
                    onClick={() => setActiveStep(1)}
                    disabled={!formData.title.trim() || !formData.content.trim()}
                  >
                    {t('common.next')}
                  </Button>
                </Box>
              </StepContent>
            </Step>

            {/* Step 2: Target Audience */}
            <Step>
              <StepLabel>{t('broadcast.target_audience')}</StepLabel>
              <StepContent>
                <Box sx={{ mb: 2 }}>
                  <FormControl fullWidth sx={{ mb: 2 }}>
                    <InputLabel>{t('broadcast.audience_type')}</InputLabel>
                    <Select
                      value={formData.target_audience.audience_type}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        target_audience: {
                          ...prev.target_audience,
                          audience_type: e.target.value as any
                        }
                      }))}
                    >
                      <MenuItem value="all">{t('broadcast.audience.all_users')}</MenuItem>
                      <MenuItem value="role_based">{t('broadcast.audience.by_role')}</MenuItem>
                      <MenuItem value="custom">{t('broadcast.audience.custom_selection')}</MenuItem>
                      <MenuItem value="conversation_participants">{t('broadcast.audience.conversation_participants')}</MenuItem>
                    </Select>
                  </FormControl>

                  {formData.target_audience.audience_type === 'role_based' && (
                    <FormGroup sx={{ mb: 2 }}>
                      <Typography variant="subtitle2" gutterBottom>
                        {t('broadcast.select_roles')}
                      </Typography>
                      {['client', 'tutor', 'manager', 'agent'].map((role) => (
                        <FormControlLabel
                          key={role}
                          control={
                            <Checkbox
                              checked={formData.target_audience.roles?.includes(role) || false}
                              onChange={(e) => {
                                const roles = formData.target_audience.roles || [];
                                const newRoles = e.target.checked
                                  ? [...roles, role]
                                  : roles.filter(r => r !== role);
                                setFormData(prev => ({
                                  ...prev,
                                  target_audience: {
                                    ...prev.target_audience,
                                    roles: newRoles
                                  }
                                }));
                              }}
                            />
                          }
                          label={t(`roles.${role}`)}
                        />
                      ))}
                    </FormGroup>
                  )}

                  <Button
                    variant="outlined"
                    onClick={handlePreviewAudience}
                    startIcon={<PreviewIcon />}
                    sx={{ mb: 2 }}
                  >
                    {t('broadcast.preview_audience')} 
                    {audiencePreview.length > 0 && ` (${audiencePreview.length} users)`}
                  </Button>

                  {audiencePreview.length > 0 && (
                    <Paper sx={{ p: 2, maxHeight: 200, overflow: 'auto' }}>
                      <Typography variant="subtitle2" gutterBottom>
                        {t('broadcast.audience_preview')}
                      </Typography>
                      <List dense>
                        {audiencePreview.slice(0, 10).map((user, index) => (
                          <ListItem key={index}>
                            <ListItemIcon>
                              <Avatar sx={{ width: 24, height: 24 }}>
                                {user.name[0]}
                              </Avatar>
                            </ListItemIcon>
                            <ListItemText
                              primary={user.name}
                              secondary={user.email}
                            />
                          </ListItem>
                        ))}
                        {audiencePreview.length > 10 && (
                          <Typography variant="caption" color="text.secondary" sx={{ pl: 2 }}>
                            ... and {audiencePreview.length - 10} more users
                          </Typography>
                        )}
                      </List>
                    </Paper>
                  )}
                </Box>

                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button onClick={() => setActiveStep(0)}>
                    {t('common.back')}
                  </Button>
                  <Button
                    variant="contained"
                    onClick={() => setActiveStep(2)}
                    disabled={audiencePreview.length === 0}
                  >
                    {t('common.next')}
                  </Button>
                </Box>
              </StepContent>
            </Step>

            {/* Step 3: Scheduling & Channels */}
            <Step>
              <StepLabel>{t('broadcast.scheduling_channels')}</StepLabel>
              <StepContent>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    {t('broadcast.delivery_channels')}
                  </Typography>
                  <FormGroup row sx={{ mb: 2 }}>
                    {['in_app', 'push', 'email', 'sms'].map((channel) => (
                      <FormControlLabel
                        key={channel}
                        control={
                          <Checkbox
                            checked={formData.channels.includes(channel)}
                            onChange={(e) => {
                              const channels = e.target.checked
                                ? [...formData.channels, channel]
                                : formData.channels.filter(c => c !== channel);
                              setFormData(prev => ({ ...prev, channels }));
                            }}
                          />
                        }
                        label={t(`broadcast.channels.${channel}`)}
                      />
                    ))}
                  </FormGroup>

                  <FormControlLabel
                    control={
                      <Switch
                        checked={!!formData.scheduled_at}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          scheduled_at: e.target.checked ? new Date() : null
                        }))}
                      />
                    }
                    label={t('broadcast.schedule_for_later')}
                    sx={{ mb: 2 }}
                  />

                  {formData.scheduled_at && (
                    <LocalizationProvider dateAdapter={AdapterDateFns}>
                      <DateTimePicker
                        label={t('broadcast.schedule_time')}
                        value={formData.scheduled_at}
                        onChange={(date) => setFormData(prev => ({ ...prev, scheduled_at: date }))}
                        minDateTime={new Date()}
                        sx={{ mb: 2 }}
                      />
                    </LocalizationProvider>
                  )}
                </Box>

                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button onClick={() => setActiveStep(1)}>
                    {t('common.back')}
                  </Button>
                  <Button
                    variant="contained"
                    onClick={() => setActiveStep(3)}
                    disabled={formData.channels.length === 0}
                  >
                    {t('common.next')}
                  </Button>
                </Box>
              </StepContent>
            </Step>

            {/* Step 4: Review & Send */}
            <Step>
              <StepLabel>{t('broadcast.review_send')}</StepLabel>
              <StepContent>
                <Paper sx={{ p: 2, mb: 2 }}>
                  <Typography variant="h6" gutterBottom>
                    {formData.title}
                  </Typography>
                  <Typography variant="body2" paragraph>
                    {formData.content}
                  </Typography>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="caption" color="text.secondary">
                        {t('broadcast.recipients')}
                      </Typography>
                      <Typography variant="body2">
                        {audiencePreview.length} users
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="caption" color="text.secondary">
                        {t('broadcast.channels')}
                      </Typography>
                      <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                        {formData.channels.map((channel) => (
                          <Chip key={channel} size="small" label={t(`broadcast.channels.${channel}`)} />
                        ))}
                      </Box>
                    </Grid>
                  </Grid>
                  
                  {formData.scheduled_at && (
                    <Box sx={{ mt: 1 }}>
                      <Typography variant="caption" color="text.secondary">
                        {t('broadcast.scheduled_for')}
                      </Typography>
                      <Typography variant="body2">
                        {format(formData.scheduled_at, 'PPP p')}
                      </Typography>
                    </Box>
                  )}
                </Paper>

                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button onClick={() => setActiveStep(2)}>
                    {t('common.back')}
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={() => setCreateDialogOpen(false)}
                  >
                    {t('broadcast.save_draft')}
                  </Button>
                  <Button
                    variant="contained"
                    onClick={handleCreateBroadcast}
                    disabled={createBroadcastMutation.isLoading}
                  >
                    {formData.scheduled_at ? t('broadcast.schedule') : t('broadcast.send_now')}
                  </Button>
                </Box>
              </StepContent>
            </Step>
          </Stepper>
        </DialogContent>
      </Dialog>
    );
  };

  // Render broadcast list
  const renderBroadcastList = () => (
    <Card>
      <CardHeader
        title={t('broadcast.broadcast_messages')}
        action={
          <Box sx={{ display: 'flex', gap: 1 }}>
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>{t('broadcast.status')}</InputLabel>
              <Select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                label={t('broadcast.status')}
              >
                <MenuItem value="all">{t('common.all')}</MenuItem>
                <MenuItem value="draft">{t('broadcast.status.draft')}</MenuItem>
                <MenuItem value="scheduled">{t('broadcast.status.scheduled')}</MenuItem>
                <MenuItem value="sent">{t('broadcast.status.sent')}</MenuItem>
                <MenuItem value="failed">{t('broadcast.status.failed')}</MenuItem>
              </Select>
            </FormControl>
            
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setCreateDialogOpen(true)}
            >
              {t('broadcast.create_broadcast')}
            </Button>
          </Box>
        }
      />
      
      <CardContent>
        {isLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : broadcasts.length === 0 ? (
          <Alert severity="info">
            {t('broadcast.no_broadcasts')}
          </Alert>
        ) : (
          <List>
            {broadcasts.map((broadcast: BroadcastMessage, index: number) => (
              <React.Fragment key={broadcast.broadcast_id}>
                <ListItem>
                  <ListItemIcon>
                    <Avatar sx={{ bgcolor: getStatusColor(broadcast.status) }}>
                      <CampaignIcon />
                    </Avatar>
                  </ListItemIcon>
                  
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="subtitle1">
                          {broadcast.title}
                        </Typography>
                        <Chip
                          size="small"
                          label={t(`broadcast.status.${broadcast.status}`)}
                          color={getStatusColor(broadcast.status) as any}
                          variant="outlined"
                        />
                        <Chip
                          size="small"
                          label={t(`broadcast.priority.${broadcast.priority}`)}
                          color={getPriorityColor(broadcast.priority) as any}
                          variant="outlined"
                        />
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                          {broadcast.content.substring(0, 100)}...
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <Typography variant="caption">
                            {t('broadcast.created_by')}: {broadcast.created_by}
                          </Typography>
                          <Typography variant="caption">
                            {formatDistanceToNow(new Date(broadcast.created_at), { addSuffix: true })}
                          </Typography>
                          {broadcast.delivery_stats && (
                            <Typography variant="caption">
                              {broadcast.delivery_stats.sent_count}/{broadcast.delivery_stats.total_recipients} sent
                            </Typography>
                          )}
                        </Box>
                        
                        {broadcast.scheduled_at && broadcast.status === 'scheduled' && (
                          <Typography variant="caption" color="warning.main">
                            {t('broadcast.scheduled_for')}: {format(new Date(broadcast.scheduled_at), 'PPP p')}
                          </Typography>
                        )}
                      </Box>
                    }
                  />
                  
                  <ListItemSecondaryAction>
                    <Box sx={{ display: 'flex', gap: 0.5 }}>
                      {broadcast.status === 'draft' && (
                        <>
                          <IconButton
                            size="small"
                            onClick={() => handleEditBroadcast(broadcast)}
                          >
                            <EditIcon />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() => handleSendBroadcast(broadcast.broadcast_id)}
                          >
                            <SendIcon />
                          </IconButton>
                        </>
                      )}
                      
                      {broadcast.status === 'scheduled' && (
                        <IconButton
                          size="small"
                          onClick={() => cancelBroadcastMutation.mutate(broadcast.broadcast_id)}
                        >
                          <StopIcon />
                        </IconButton>
                      )}
                      
                      {broadcast.delivery_stats && (
                        <IconButton
                          size="small"
                          onClick={() => setAnalyticsDialogOpen(true)}
                        >
                          <AnalyticsIcon />
                        </IconButton>
                      )}
                      
                      <IconButton size="small">
                        <ViewIcon />
                      </IconButton>
                    </Box>
                  </ListItemSecondaryAction>
                </ListItem>
                {index < broadcasts.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        )}
      </CardContent>
    </Card>
  );

  const tabLabels = ['broadcast.broadcasts', 'broadcast.templates', 'broadcast.analytics'];

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        {t('broadcast.broadcast_messaging')}
      </Typography>

      <Tabs value={currentTab} onChange={(_, newValue) => setCurrentTab(newValue)} sx={{ mb: 3 }}>
        {tabLabels.map((label, index) => (
          <Tab key={index} label={t(label)} />
        ))}
      </Tabs>

      <Box sx={{ mt: 3 }}>
        {currentTab === 0 && renderBroadcastList()}
        {currentTab === 1 && (
          <Card>
            <CardHeader title={t('broadcast.message_templates')} />
            <CardContent>
              <Typography variant="body2" color="text.secondary">
                {t('broadcast.templates_coming_soon')}
              </Typography>
            </CardContent>
          </Card>
        )}
        {currentTab === 2 && (
          <Card>
            <CardHeader title={t('broadcast.broadcast_analytics')} />
            <CardContent>
              <Typography variant="body2" color="text.secondary">
                {t('broadcast.analytics_coming_soon')}
              </Typography>
            </CardContent>
          </Card>
        )}
      </Box>

      {renderBroadcastForm()}
    </Box>
  );
};

export default BroadcastMessaging;