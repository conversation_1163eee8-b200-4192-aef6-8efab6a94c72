"""
Service for processing client tutor requests.
Handles conversion of requests into client accounts and matching with tutors.
"""

from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import asyncpg
from app.core.logging import logger
from app.models.user_models import UserCreate
from app.models.client_models import ClientP<PERSON><PERSON>leCreate, DependantCreate, LearningNeeds
from app.services.auth_service import AuthService
from app.services.client_service import ClientService
from app.services.tutor_matching_service import TutorMatchingService
from app.services.notification_service import NotificationService
from app.services.geocoding_service import GeocodingService
from app.database.repositories.tutor_repository import TutorRepository
from app.config.database import get_db, DatabaseManager
from app.core.security import get_password_hash
import secrets
import string

# Helper function to get database connection
async def get_db_connection():
    """Get database connection."""
    async for db in get_db():
        return db


class ClientRequestProcessor:
    """Service for processing client tutor requests."""
    
    def __init__(self):
        self.auth_service = AuthService()
        self.db_manager = DatabaseManager()
        self.client_service = ClientService(self.db_manager)
        # Initialize TutorMatchingService with its dependencies
        self.tutor_repository = TutorRepository()
        self.geocoding_service = GeocodingService()
        self.tutor_matching_service = TutorMatchingService(
            tutor_repository=self.tutor_repository,
            geocoding_service=self.geocoding_service
        )
        self.notification_service = NotificationService()
    
    async def get_pending_requests(
        self,
        region: Optional[str] = None,
        city: Optional[str] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """
        Get pending client requests with optional filtering by region or city.
        
        Args:
            region: Optional region filter
            city: Optional city filter
            limit: Maximum number of results
            offset: Pagination offset
            
        Returns:
            List of pending requests
        """
        conn = await get_db_connection()
        try:
            query = """
                SELECT 
                    ctr.*,
                    COUNT(*) OVER() as total_count
                FROM client_tutor_requests ctr
                WHERE ctr.status = 'pending'
                AND ($1::TEXT IS NULL OR ctr.region = $1)
                AND ($2::TEXT IS NULL OR ctr.city = $2)
                ORDER BY ctr.created_at DESC
                LIMIT $3 OFFSET $4
            """
            
            rows = await conn.fetch(query, region, city, limit, offset)
            
            requests = []
            for row in rows:
                request_dict = dict(row)
                request_dict.pop('total_count')
                requests.append(request_dict)
            
            total = rows[0]['total_count'] if rows else 0
            
            return {
                'requests': requests,
                'total': total,
                'limit': limit,
                'offset': offset
            }
            
        except Exception as e:
            logger.error(f"Error fetching pending requests: {e}")
            raise
        finally:
            await conn.close()
    
    async def get_request_stats(self) -> Dict[str, Any]:
        """Get statistics about client requests."""
        conn = await get_db_connection()
        try:
            query = """
                SELECT 
                    COUNT(*) FILTER (WHERE status = 'pending') as pending_count,
                    COUNT(*) FILTER (WHERE status = 'assigned') as assigned_count,
                    COUNT(*) FILTER (WHERE status = 'processed') as processed_count,
                    COUNT(*) FILTER (WHERE status = 'converted') as converted_count,
                    COUNT(*) FILTER (WHERE status = 'rejected') as rejected_count,
                    COUNT(*) as total_count,
                    COUNT(DISTINCT region) as unique_regions,
                    COUNT(DISTINCT city) as unique_cities
                FROM client_tutor_requests
            """
            
            row = await conn.fetchrow(query)
            return dict(row)
            
        except Exception as e:
            logger.error(f"Error fetching request stats: {e}")
            raise
        finally:
            await conn.close()
    
    async def get_request_by_id(self, request_id: int) -> Optional[Dict[str, Any]]:
        """Get a specific request by ID."""
        conn = await get_db_connection()
        try:
            query = """
                SELECT * FROM client_tutor_requests
                WHERE request_id = $1
            """
            
            row = await conn.fetchrow(query, request_id)
            return dict(row) if row else None
            
        except Exception as e:
            logger.error(f"Error fetching request {request_id}: {e}")
            raise
        finally:
            await conn.close()
    
    async def assign_request(self, request_id: int, manager_id: int) -> bool:
        """Assign a request to a manager for processing."""
        conn = await get_db_connection()
        try:
            query = """
                UPDATE client_tutor_requests
                SET status = 'assigned',
                    assigned_to = $2,
                    assigned_at = NOW(),
                    updated_at = NOW()
                WHERE request_id = $1
                AND status = 'pending'
                RETURNING request_id
            """
            
            result = await conn.fetchrow(query, request_id, manager_id)
            
            if result:
                logger.info(f"Request {request_id} assigned to manager {manager_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error assigning request {request_id}: {e}")
            raise
        finally:
            await conn.close()
    
    async def process_request(
        self,
        request_id: int,
        manager_id: int
    ) -> Tuple[Optional[int], Optional[int], Optional[str]]:
        """
        Process a client request and convert to client account.
        
        Args:
            request_id: ID of the request to process
            manager_id: ID of the manager processing the request
            
        Returns:
            Tuple of (client_id, dependant_id, generated_password)
        """
        conn = await get_db_connection()
        try:
            # Get request details
            request = await self.get_request_by_id(request_id)
            if not request:
                raise ValueError(f"Request {request_id} not found")
            
            if request['status'] != 'assigned':
                raise ValueError(f"Request {request_id} is not in assigned status")
            
            # Generate temporary password
            password = self._generate_password()
            
            # Create user account
            user_data = UserCreate(
                email=request['parent_email'],
                password=password,
                first_name=request['parent_first_name'],
                last_name=request['parent_last_name'],
                phone=request['parent_phone'],
                roles=['client']
            )
            
            # Note: In a real implementation, you would:
            # 1. Create the user account
            # 2. Create the client profile
            # 3. Create the dependant if applicable
            # 4. Send welcome email with credentials
            # 5. Update request status to 'converted'
            
            # For now, just update the request status
            update_query = """
                UPDATE client_tutor_requests
                SET status = 'processed',
                    processed_by = $2,
                    processed_at = NOW(),
                    updated_at = NOW()
                WHERE request_id = $1
                RETURNING request_id
            """
            
            await conn.fetchrow(update_query, request_id, manager_id)
            
            logger.info(f"Request {request_id} processed by manager {manager_id}")
            
            # Return dummy values for now
            return (None, None, password)
            
        except Exception as e:
            logger.error(f"Error processing request {request_id}: {e}")
            raise
        finally:
            await conn.close()
    
    def _generate_password(self, length: int = 12) -> str:
        """Generate a secure temporary password."""
        alphabet = string.ascii_letters + string.digits + "!@#$%^&*()"
        password = ''.join(secrets.choice(alphabet) for _ in range(length))
        return password
    
    async def reject_request(
        self,
        request_id: int,
        manager_id: int,
        reason: str
    ) -> bool:
        """Reject a request with a reason."""
        conn = await get_db_connection()
        try:
            query = """
                UPDATE client_tutor_requests
                SET status = 'rejected',
                    processed_by = $2,
                    processed_at = NOW(),
                    notes = COALESCE(notes || E'\\n', '') || 'Rejection reason: ' || $3,
                    updated_at = NOW()
                WHERE request_id = $1
                AND status IN ('pending', 'assigned')
                RETURNING request_id
            """
            
            result = await conn.fetchrow(query, request_id, manager_id, reason)
            
            if result:
                logger.info(f"Request {request_id} rejected by manager {manager_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error rejecting request {request_id}: {e}")
            raise
        finally:
            await conn.close()
    
    async def search_requests(
        self,
        search_term: str,
        status: Optional[str] = None,
        limit: int = 20
    ) -> List[Dict[str, Any]]:
        """Search requests by name, email, or phone."""
        conn = await get_db_connection()
        try:
            query = """
                SELECT * FROM client_tutor_requests
                WHERE (
                    parent_first_name ILIKE $1
                    OR parent_last_name ILIKE $1
                    OR parent_email ILIKE $1
                    OR parent_phone ILIKE $1
                    OR child_first_name ILIKE $1
                    OR child_last_name ILIKE $1
                )
                AND ($2::TEXT IS NULL OR status = $2)
                ORDER BY created_at DESC
                LIMIT $3
            """
            
            search_pattern = f"%{search_term}%"
            rows = await conn.fetch(query, search_pattern, status, limit)
            
            return [dict(row) for row in rows]
            
        except Exception as e:
            logger.error(f"Error searching requests: {e}")
            raise
        finally:
            await conn.close()
    
    async def get_matched_tutors(
        self,
        request_id: int,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """Get matched tutors for a request based on subject and location."""
        conn = await get_db_connection()
        try:
            # Get request details
            request = await self.get_request_by_id(request_id)
            if not request:
                raise ValueError(f"Request {request_id} not found")
            
            # Use tutor matching service if postal code is available
            if request.get('parent_postal_code') and request.get('subjects'):
                from app.services.tutor_matching_service import TutorSearchCriteria
                
                criteria = TutorSearchCriteria(
                    client_postal_code=request['parent_postal_code'],
                    subject_areas=request['subjects'],
                    max_results=limit
                )
                
                matches = await self.tutor_matching_service.find_nearest_tutors(criteria)
                
                # Convert TutorMatch objects to dictionaries
                return [
                    {
                        'tutor_id': match.tutor_id,
                        'first_name': match.first_name,
                        'last_name': match.last_name,
                        'email': match.email,
                        'distance_km': round(match.distance_km, 1),
                        'average_rating': match.average_rating,
                        'hourly_rate': float(match.hourly_rate) if match.hourly_rate else None,
                        'specialties': match.specialties,
                        'service_types': match.service_types,
                        'availability_status': match.availability_status,
                        'relevance_score': round(match.relevance_score, 2)
                    }
                    for match in matches
                ]
            
            # Fallback to empty list if no location data
            return []
            
        except Exception as e:
            logger.error(f"Error getting matched tutors: {e}")
            raise
        finally:
            await conn.close()
    
    async def send_processing_notification(
        self,
        request_id: int,
        client_email: str,
        temp_password: str
    ) -> bool:
        """Send email notification about processed request."""
        try:
            # For now, just log
            logger.info(
                f"Would send processing notification to {client_email} "
                f"for request {request_id}"
            )
            return True
            
        except Exception as e:
            logger.error(f"Error sending notification: {e}")
            return False