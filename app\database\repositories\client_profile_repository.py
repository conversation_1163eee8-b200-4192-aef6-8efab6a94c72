"""
Repository for client profile database operations.
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
import json

from asyncpg import Connection, Record

from app.database.repositories.base import BaseRepository
from app.models.client_profile_models import (
    ClientProfile,
    ClientProfileCreate,
    ClientProfileUpdate,
    ClientProfileWithUser,
    ClientProfilePublic,
    CommunicationPreferences
)
from app.core.exceptions import (
    ResourceNotFoundError,
    DatabaseOperationError,
    DuplicateResourceError
)
from app.core.logging import TutorAideLogger


logger = TutorAideLogger(__name__)


class ClientProfileRepository(BaseRepository[ClientProfile]):
    """Repository for client profile database operations."""
    
    def __init__(self):
        """Initialize client profile repository."""
        super().__init__("client_profiles", "client_id")
        self.model_class = ClientProfile
    
    async def create(
        self, 
        conn: Connection, 
        data: ClientProfileCreate
    ) -> ClientProfile:
        """Create a new client profile."""
        try:
            # Check if profile already exists for user
            existing = await conn.fetchone(
                """
                SELECT profile_id 
                FROM client_profiles 
                WHERE user_id = $1 AND deleted_at IS NULL
                """,
                data.user_id
            )
            
            if existing:
                raise DuplicateResourceError(
                    "Client profile already exists for this user"
                )
            
            # Convert communication preferences to JSON
            comm_prefs = data.communication_preferences.dict() if data.communication_preferences else {
                "email": True,
                "sms": True,
                "push": True
            }
            
            query = """
                INSERT INTO client_profiles (
                    user_id,
                    emergency_contact_name,
                    emergency_contact_phone,
                    emergency_contact_relationship,
                    address_line1,
                    address_line2,
                    city,
                    province,
                    postal_code,
                    phone,
                    secondary_phone,
                    preferred_language,
                    timezone,
                    communication_preferences,
                    internal_notes
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
                RETURNING *
            """
            
            record = await conn.fetchrow(
                query,
                data.user_id,
                data.emergency_contact_name,
                data.emergency_contact_phone,
                data.emergency_contact_relationship,
                data.address_line1,
                data.address_line2,
                data.city,
                data.province.value if data.province else None,
                data.postal_code,
                data.phone,
                data.secondary_phone,
                data.preferred_language,
                data.timezone,
                json.dumps(comm_prefs),
                data.internal_notes
            )
            
            return self._record_to_model(record)
            
        except DuplicateResourceError:
            raise
        except Exception as e:
            logger.error(f"Error creating client profile: {str(e)}")
            raise DatabaseOperationError(f"Failed to create client profile: {str(e)}")
    
    async def get_by_user_id(
        self,
        conn: Connection,
        user_id: int
    ) -> Optional[ClientProfile]:
        """Get client profile by user ID."""
        try:
            query = """
                SELECT * FROM client_profiles
                WHERE user_id = $1 AND deleted_at IS NULL
            """
            
            record = await conn.fetchrow(query, user_id)
            return self._record_to_model(record) if record else None
            
        except Exception as e:
            logger.error(f"Error fetching client profile: {str(e)}")
            raise DatabaseOperationError(f"Failed to fetch client profile: {str(e)}")
    
    async def get_with_user(
        self,
        conn: Connection,
        profile_id: int
    ) -> Optional[ClientProfileWithUser]:
        """Get client profile with user information."""
        try:
            query = """
                SELECT 
                    cp.*,
                    u.email as user_email,
                    u.first_name as user_first_name,
                    u.last_name as user_last_name,
                    u.status as user_status
                FROM client_profiles cp
                JOIN user_accounts u ON cp.user_id = u.user_id
                WHERE cp.profile_id = $1 AND cp.deleted_at IS NULL
            """
            
            record = await conn.fetchrow(query, profile_id)
            if not record:
                return None
            
            # Convert record to dict and parse communication preferences
            data = dict(record)
            if data.get('communication_preferences'):
                data['communication_preferences'] = CommunicationPreferences(
                    **json.loads(data['communication_preferences'])
                )
            
            return ClientProfileWithUser(**data)
            
        except Exception as e:
            logger.error(f"Error fetching client profile with user: {str(e)}")
            raise DatabaseOperationError(f"Failed to fetch client profile: {str(e)}")
    
    async def get_public_profile(
        self,
        conn: Connection,
        profile_id: int
    ) -> Optional[ClientProfilePublic]:
        """Get public view of client profile (for tutors)."""
        try:
            query = """
                SELECT 
                    cp.profile_id,
                    u.first_name,
                    u.last_name,
                    cp.city,
                    cp.preferred_language
                FROM client_profiles cp
                JOIN user_accounts u ON cp.user_id = u.user_id
                WHERE cp.profile_id = $1 AND cp.deleted_at IS NULL
            """
            
            record = await conn.fetchrow(query, profile_id)
            return ClientProfilePublic(**dict(record)) if record else None
            
        except Exception as e:
            logger.error(f"Error fetching public client profile: {str(e)}")
            raise DatabaseOperationError(f"Failed to fetch client profile: {str(e)}")
    
    async def search(
        self,
        conn: Connection,
        query: str,
        limit: int = 10
    ) -> List[ClientProfileWithUser]:
        """Search client profiles by name, email, phone, or city."""
        try:
            search_query = """
                SELECT 
                    cp.*,
                    u.email as user_email,
                    u.first_name as user_first_name,
                    u.last_name as user_last_name,
                    u.status as user_status
                FROM client_profiles cp
                JOIN user_accounts u ON cp.user_id = u.user_id
                WHERE cp.deleted_at IS NULL
                    AND (
                        u.first_name ILIKE $1
                        OR u.last_name ILIKE $1
                        OR u.email ILIKE $1
                        OR cp.phone ILIKE $1
                        OR cp.city ILIKE $1
                        OR cp.postal_code ILIKE $1
                    )
                ORDER BY u.last_name, u.first_name
                LIMIT $2
            """
            
            search_pattern = f"%{query}%"
            records = await conn.fetch(search_query, search_pattern, limit)
            
            results = []
            for record in records:
                data = dict(record)
                if data.get('communication_preferences'):
                    data['communication_preferences'] = CommunicationPreferences(
                        **json.loads(data['communication_preferences'])
                    )
                results.append(ClientProfileWithUser(**data))
            
            return results
            
        except Exception as e:
            logger.error(f"Error searching client profiles: {str(e)}")
            raise DatabaseOperationError(f"Failed to search client profiles: {str(e)}")
    
    async def update(
        self,
        conn: Connection,
        profile_id: int,
        data: ClientProfileUpdate
    ) -> Optional[ClientProfile]:
        """Update a client profile."""
        try:
            # Build update query dynamically
            update_fields = []
            values = []
            param_count = 1
            
            update_dict = data.dict(exclude_unset=True)
            
            for field, value in update_dict.items():
                if field == "communication_preferences" and value is not None:
                    update_fields.append(f"{field} = ${param_count}")
                    values.append(json.dumps(value.dict() if hasattr(value, 'dict') else value))
                elif field == "province" and value is not None:
                    update_fields.append(f"{field} = ${param_count}")
                    values.append(value.value if hasattr(value, 'value') else value)
                else:
                    update_fields.append(f"{field} = ${param_count}")
                    values.append(value)
                param_count += 1
            
            if not update_fields:
                # No fields to update
                return await self.get(conn, profile_id)
            
            # Add updated_at
            update_fields.append(f"updated_at = ${param_count}")
            values.append(datetime.utcnow())
            param_count += 1
            
            # Add profile_id for WHERE clause
            values.append(profile_id)
            
            query = f"""
                UPDATE client_profiles
                SET {', '.join(update_fields)}
                WHERE profile_id = ${param_count} AND deleted_at IS NULL
                RETURNING *
            """
            
            record = await conn.fetchrow(query, *values)
            return self._record_to_model(record) if record else None
            
        except Exception as e:
            logger.error(f"Error updating client profile: {str(e)}")
            raise DatabaseOperationError(f"Failed to update client profile: {str(e)}")
    
    def _record_to_model(self, record: Optional[Record]) -> Optional[ClientProfile]:
        """Convert database record to model."""
        if not record:
            return None
        
        data = dict(record)
        
        # Parse communication preferences JSON
        if data.get('communication_preferences'):
            data['communication_preferences'] = CommunicationPreferences(
                **json.loads(data['communication_preferences'])
            )
        
        return ClientProfile(**data)