# TutorAide App Rewrite - Claude Implementation Guide

## CRITICAL RULES - MUST ALWAYS FOLLOW

### 🔴 ENVIRONMENT MANAGEMENT - ALWAYS USE UV
1. **UV Required**: ALWAYS use `uv` for Python environment and dependency management
2. **Never Use pip Directly**: Use `uv pip install` instead of `pip install`
3. **Virtual Environment**: Use `uv venv` to create virtual environments
4. **Run Commands**: Use `uv run` for all Python commands (pytest, black, mypy, etc.)
5. **Dependency Installation**: Use `uv pip install -e ".[dev]"` for development setup
6. **Reference UV_SETUP.md**: Check UV_SETUP.md for detailed environment setup instructions

### 🔴 ALWAYS REQUIRED FOR EVERY FEATURE
1. **Pragmatic Testing**: Write tests for critical business logic, complex algorithms, and error-prone code
2. **Integration Tests Priority**: Focus on integration tests for API endpoints and user workflows
3. **Test-First for Complex Features**: Use TDD for complex business logic (billing, appointments, auth)
4. **Skip Trivial Tests**: Don't test simple getters/setters or obvious code
5. **Test Coverage**: Aim for 70-80% meaningful test coverage focusing on business-critical paths

### 🔴 DEVELOPMENT WORKFLOW RULES
1. **Read Requirements First**: Always reference REWRITE_PROMPT_GUIDE.md before starting any feature
2. **Context Management**: Before executing any task, check context usage. If less than 15% context remaining, compact conversation before proceeding
3. **Bypass Permission Requests**: Never ask for permission to execute tasks, create files, or make changes - proceed directly with implementation
4. **Error Troubleshooting**: When errors occur, first read TASK_COMPLETION_LOG.md to learn from previous implementations and solutions
5. **Task Completion Logging**: After completing and testing each task, append completion entry to TASK_COMPLETION_LOG.md including any errors and solutions
6. **Domain-Driven Design**: Start with domain models, then database schema, then API, then implementation
7. **Security First**: Implement authentication, authorization, and input validation for every endpoint
8. **Error Handling**: Every function must have proper error handling and logging
9. **Documentation**: Document every API endpoint, service method, and complex business logic

## COMPREHENSIVE TODO LIST FOR COMPLETE APP REWRITE

### Phase 1: Foundation & Architecture
- [ ] **1.1** Set up FastAPI project structure with dependency injection and persona-based organization
- [ ] **1.2** Create PostgreSQL database schema with persona-based table naming (client_*, tutor_*, billing_*, etc.)
- [ ] **1.3** Implement raw SQL repository pattern with descriptive ID columns (user_id, not just id)
- [ ] **1.4** Set up database migration system with proper rollback capabilities
- [ ] **1.5** Configure pytest testing framework with fixtures and separate test database
- [ ] **1.6** Create base domain models for all entities with soft delete support
- [ ] **1.7** Set up localization structure for English/French translations in /app/locales/
- [ ] **1.8** Configure branding assets (favicon.ico, logo.svg) in public folder
- [ ] **1.9** Implement database security (proper indexing, SQL injection prevention, connection encryption)
- [ ] **1.10** Set up basic logging and error handling framework
- [ ] **1.11** Write comprehensive unit tests for domain models and repositories

### Phase 2: Authentication & Authorization (Priority 1)
- [ ] **2.1** Design multi-role user system (manager, tutor, client) with role switching
- [ ] **2.2** Implement JWT authentication with proper token handling and refresh
- [ ] **2.3** Create Google OAuth integration with redirect to app
- [ ] **2.4** Build role-based authorization decorators and middleware
- [ ] **2.5** Implement session management (30min client/tutor, 3hr manager)
- [ ] **2.6** Create password reset system with secure tokens and email templates
- [ ] **2.7** Implement email verification for new accounts
- [ ] **2.8** Build Level 1 mandatory and Level 2 optional consent system
- [ ] **2.9** Implement comprehensive input validation and sanitization for auth endpoints
- [ ] **2.10** Add rate limiting and brute force protection for login attempts
- [ ] **2.11** Write comprehensive auth unit tests covering all flows
- [ ] **2.12** Write auth integration tests (login/logout/reset flows)
- [ ] **2.13** Test security vulnerabilities and edge cases

### Phase 3: Core User Management (Priority 1)
- [ ] **3.1** Create User account system with multi-role support and Google auth
- [ ] **3.2** Implement client profile management with emergency contacts and address
- [ ] **3.3** Build dependant management system supporting 2 parents (separated families)
- [ ] **3.4** Create tutor invitation system with manager approval and secure tokens
- [ ] **3.5** Implement tutor profile system with verification states and document upload
- [ ] **3.6** Build user search and filtering with autocomplete dropdown (alphabetical)
- [ ] **3.7** ~~Create learning needs assessment system for clients/dependants~~ (REMOVED - Feature deprecated)
- [ ] **3.8** Implement progressive onboarding flows for clients and tutors
- [ ] **3.9** Add input validation and sanitization for all user management endpoints
- [ ] **3.10** Implement authorization checks for user data access and modifications
- [ ] **3.11** Write unit tests for all user management services
- [ ] **3.12** Write integration tests for user API endpoints and onboarding flows
- [ ] **3.13** Test user role switching and multi-role permissions

### Phase 4: Global Search & Quick Actions (Priority 1)
- [ ] **4.1** Implement global search bar with autocomplete across all entities
- [ ] **4.2** Create context-aware search that adapts based on user role
- [ ] **4.3** Build quick action buttons for each user role (Add User, Book Session, etc.)
- [ ] **4.4** Implement type-ahead suggestions with alphabetical sorting
- [ ] **4.5** Create search result navigation and entity deep-linking
- [ ] **4.6** Write unit tests for search algorithms and filtering
- [ ] **4.7** Write integration tests for search performance and accuracy

### Phase 5: Sidebar Navigation & Master-Detail UI (Priority 1)
- [ ] **5.1** Build collapsible sidebar with hamburger menu and role-specific sections
- [ ] **5.2** Implement master-detail layout with horizontal tabs (Users: Clients/Tutors/Dependants)
- [ ] **5.3** Create Users section with detailed attribute displays for each entity type
- [ ] **5.4** Build Billing section with tabs (Invoices/Tutor Payments/Subscriptions/Packages)
- [ ] **5.5** Create Reports section with tabs (Financial/Performance/Analytics/Usage/Monthly)
- [ ] **5.6** Implement Messages section with tabs (SMS Threads/In-App Chat/Templates/Broadcasts)
- [ ] **5.7** Build Settings section with tabs (System/Users/<USER>/Notifications/API)
- [ ] **5.8** Apply light gray/pastel red design system with rounded shapes and shadows
- [ ] **5.9** Write unit tests for UI component logic and state management
- [ ] **5.10** Write integration tests for navigation and tab switching

### Phase 6: Appointment System (Priority 1)
- [ ] **6.1** Design appointment models supporting individual and group sessions
- [ ] **6.2** Implement appointment scheduling service with conflict detection
- [ ] **6.3** Create calendar day view with tutor columns and profile pictures
- [ ] **6.4** Build tutor availability management with weekly schedules
- [ ] **6.5** Implement time-off request system for tutors
- [ ] **6.6** Create recurring appointment system
- [ ] **6.7** Build appointment confirmation workflow (tutor SMS confirmation)
- [ ] **6.8** Implement WebSocket real-time calendar updates with proper authentication
- [ ] **6.9** Add calendar filtering by tutor and date ranges
- [ ] **6.10** Implement AI-powered scheduling suggestions based on availability patterns
- [ ] **6.11** Add input validation and authorization for all appointment endpoints
- [ ] **6.12** Optimize appointment queries with proper database indexing
- [ ] **6.13** Write unit tests for scheduling logic and conflict detection
- [ ] **6.14** Write integration tests for appointment workflows and WebSocket updates
- [ ] **6.15** Test edge cases (conflicts, cancellations, no-shows)

### Phase 7: Service Catalog & Rate Management (Priority 1)
- [ ] **7.1** Create service catalog with subject areas (math, science, french, english, others)
- [ ] **7.2** Implement service types (online, in-person, library, hybrid)
- [ ] **7.3** Build tutor service rate management with different rates per service
- [ ] **7.4** Create service packages for TECFEE programs (12-session packages)
- [ ] **7.5** Implement location preferences and distance calculations
- [ ] **7.6** Build frequency and duration options management
- [ ] **7.7** Create service recommendation system
- [ ] **7.8** Write unit tests for pricing calculations and service matching
- [ ] **7.9** Write integration tests for rate management workflows
- [ ] **7.10** Test pricing edge cases and service availability

### Phase 8: Tutor Mapping & Geolocation (Priority 1)
- [ ] **8.1** Integrate geocoding API for postal code to coordinates conversion
- [ ] **8.2** Build interactive map showing tutors by location
- [ ] **8.3** Implement 5 nearest tutors algorithm by client postal code and subject
- [ ] **8.4** Create tutor service area management (postal codes and radius)
- [ ] **8.5** Build distance calculation service for tutor matching
- [ ] **8.6** Implement map filtering and tutor selection interface
- [ ] **8.7** Write unit tests for geolocation and distance algorithms
- [ ] **8.8** Write integration tests for map functionality and tutor matching

### Phase 9: Billing & Invoicing System (Priority 2)
- [ ] **9.1** Design billing domain models for CAD currency with decimal precision
- [ ] **9.2** Implement invoice generation after completed sessions
- [ ] **9.3** Create Stripe payment integration with Canadian banking direct deposits
- [ ] **9.4** Build subscription system with pre-paid hours deduction
- [ ] **9.5** Implement dual billing logic (invoice OR subscription hour deduction)
- [ ] **9.6** Create weekly tutor payment system (Thursday to Wednesday cycle)
- [ ] **9.7** Build bulk payment approval system for managers
- [ ] **9.8** Implement payment tracking by which parent paid (for separated families)
- [ ] **9.9** Create pricing management system where platform sets client rates and pays tutors base rates (keeping the difference)
- [ ] **9.10** Implement secure payment processing with PCI compliance measures
- [ ] **9.11** Add comprehensive input validation for all financial data
- [ ] **9.12** Implement financial data encryption and secure storage
- [ ] **9.13** Write unit tests for billing calculations and payment processing
- [ ] **9.14** Write integration tests for Stripe webhooks and payment workflows
- [ ] **9.15** Test payment edge cases and error handling scenarios

### Phase 10: Dual Notification System (Priority 2)
- [ ] **10.1** Integrate OneSignal for push notifications (free tier for app users)
- [ ] **10.2** Integrate Twilio SMS for critical alerts and non-app users
- [ ] **10.3** Create unified notification orchestration service
- [ ] **10.4** Implement WebSocket real-time messaging with messenger-style bubble interface
- [ ] **10.5** Build SMS conversation management with latest-first ordering
- [ ] **10.6** Create notification preferences system (push/SMS/email toggles)
- [ ] **10.7** Implement automated reminders (24-hour appointment reminders)
- [ ] **10.8** Build email service integration (facturation@, support@, <EMAIL>)
- [ ] **10.9** Create template management system for emails and SMS (EN/FR)
- [ ] **10.10** Add role indicators in messages (👨‍🏫 TUTOR, 👤 CLIENT)
- [ ] **10.11** Write unit tests for notification logic and template rendering
- [ ] **10.12** Write integration tests for OneSignal, Twilio, and WebSocket flows

### Phase 11: Multi-Language Support (Priority 2)
- [ ] **11.1** Set up i18n framework for English/French with file structure
- [ ] **11.2** Create translation files for all domains (auth, appointments, billing, etc.)
- [ ] **11.3** Implement language switching in API and frontend
- [ ] **11.4** Translate all email and SMS templates in both languages
- [ ] **11.5** Build language preference management for users
- [ ] **11.6** Create admin interface for translation management
- [ ] **11.7** Write unit tests for translation functions and language switching
- [ ] **11.8** Write integration tests for multi-language workflows
- [ ] **11.9** Test language switching edge cases and fallbacks

### Phase 12: TECFEE Program Management (Priority 3)
- [ ] **12.1** Design TECFEE program as group appointments with curriculum tracking
- [ ] **12.2** Implement 12-module program structure with package pricing
- [ ] **12.3** Create group session management with min/max participant limits
- [ ] **12.4** Build progress monitoring without enrollment tracking (appointment-based)
- [ ] **12.5** Implement TECFEE-specific billing and package payments
- [ ] **12.6** Create completion tracking and certificate generation
- [ ] **12.7** Write unit tests for TECFEE program logic and progression
- [ ] **12.8** Write integration tests for group session workflows
- [ ] **12.9** Test TECFEE billing integration and package completion scenarios

### Phase 13: Advanced Reporting & Analytics (Priority 3)
- [ ] **13.1** Build financial reporting with revenue/expense breakdown
- [ ] **13.2** Create performance analytics for tutors (ratings, session counts)
- [ ] **13.3** Implement usage analytics (booking patterns, popular subjects)
- [ ] **13.4** Build monthly reporting cycles with automated generation
- [ ] **13.5** Create downloadable reports with email distribution
- [ ] **13.6** Implement real-time dashboard with key metrics
- [ ] **13.7** Build advanced filtering and date range selection
- [ ] **13.8** Write unit tests for reporting calculations and data aggregation
- [ ] **13.9** Write integration tests for report generation and distribution
- [ ] **13.10** Test performance under load with large datasets

### Phase 14: Customer Service Interface (Priority 3)
- [ ] **14.1** Build SMS conversation management interface for managers
- [ ] **14.2** Create in-app messaging system with real-time updates
- [ ] **14.3** Implement conversation threading and history tracking
- [ ] **14.4** Build customer service statistics dashboard
- [ ] **14.5** Create broadcast messaging system for announcements
- [ ] **14.6** Implement message templates and quick responses
- [ ] **14.7** Write unit tests for messaging logic and conversation management
- [ ] **14.8** Write integration tests for customer service workflows

### Phase 15: Mobile & Progressive Web App (Priority 4)
- [ ] **15.1** Implement mobile-first responsive design system
- [ ] **15.2** Create progressive web app features (offline support, app installation)
- [ ] **15.3** Build touch-optimized interfaces for mobile devices
- [ ] **15.4** Implement push notification support for mobile browsers
- [ ] **15.5** Create mobile-specific navigation patterns
- [ ] **15.6** Optimize performance for mobile networks
- [ ] **15.7** Write unit tests for mobile-specific features
- [ ] **15.8** Write integration tests for PWA functionality

### Phase 16: API Infrastructure & Versioning (Priority 4)
- [ ] **16.1** Design REST API endpoints with proper versioning (v1, v2)
- [ ] **16.2** Implement API rate limiting and throttling
- [ ] **16.3** Create comprehensive API documentation with OpenAPI/Swagger
- [ ] **16.4** Build API authentication and authorization middleware
- [ ] **16.5** Implement pagination, filtering, and sorting for all endpoints
- [ ] **16.6** Create API monitoring and usage analytics
- [ ] **16.7** Write unit tests for API utilities and middleware
- [ ] **16.8** Write integration tests for complete API functionality
- [ ] **16.9** Test API versioning and backward compatibility

### Phase 17: Production Infrastructure & Optimization
- [ ] **17.1** Set up comprehensive monitoring, logging, and alerting systems
- [ ] **17.2** Implement advanced caching strategies (Redis) for frequently accessed data
- [ ] **17.3** Conduct final security audit and penetration testing
- [ ] **17.4** Implement DDoS protection and advanced rate limiting
- [ ] **17.5** Create comprehensive backup and disaster recovery procedures
- [ ] **17.6** Set up performance monitoring and optimization alerts
- [ ] **17.7** Conduct load testing to validate 1000+ concurrent user capacity
- [ ] **17.8** Write performance integration tests for load handling scenarios
- [ ] **17.9** Test disaster recovery procedures and data backup integrity
- [ ] **17.10** Finalize production security configurations and certificates

### Phase 18: Deployment Infrastructure
- [ ] **18.1** Set up CI/CD pipeline with automated testing and deployment
- [ ] **18.2** Configure Railway deployment with environment management
- [ ] **18.3** Implement blue-green deployment strategy for production releases
- [ ] **18.4** Create monitoring and health check endpoints
- [ ] **18.5** Set up automated backup procedures
- [ ] **18.6** Write deployment integration tests
- [ ] **18.7** Test complete deployment process and rollback procedures

## QUALITY GATES - NEVER SKIP

### Before Each Feature Completion:
1. ✅ Context usage verified (must have >15% remaining or compact first)
2. ✅ Critical paths tested (focus on business logic, not trivial code)
3. ✅ Integration tests for user-facing features
4. ✅ Error handling for edge cases and failures
5. ✅ Input validation for security-sensitive endpoints
6. ✅ Security review for auth, payments, and data access
7. ✅ API documentation for public endpoints
8. ✅ Performance verified for database queries and API responses
9. ✅ Multi-language support for user-facing content (EN/FR)
10. ✅ Mobile responsiveness for customer interfaces
11. ✅ **Log Task Completion**: Append completion entry to TASK_COMPLETION_LOG.md before proceeding to next task

### Before Phase Completion:
1. ✅ All phase tests passing
2. ✅ Integration with previous phases tested
3. ✅ End-to-end user workflows tested
4. ✅ Performance benchmarks met (< 200ms API response)
5. ✅ Security validation completed
6. ✅ Documentation updated
7. ✅ Translation completeness verified
8. ✅ Accessibility standards met

## TECHNICAL REQUIREMENTS

### Stack Decisions:
- **Backend**: FastAPI with dependency injection
- **Database**: PostgreSQL with raw SQL (no ORM)
- **API**: REST API with versioning
- **Frontend**: SPA with mobile-first design
- **Authentication**: JWT tokens + Google OAuth
- **Multi-tenancy**: Yes, designed from start
- **Soft Deletes**: Yes, for audit trails
- **Currency**: CAD only with decimal precision
- **Real-time**: WebSockets for calendar and messaging

## Design System (Apple-Inspired)

### Color Palette
```css
:root {
  /* Primary Colors */
  --color-primary: #1f2937;        /* Black/Dark Gray */
  --color-secondary: #f9fafb;      /* Light Gray */
  --color-accent: #f87171;         /* Pastel Red */
  --color-highlight: #dc2626;      /* Red */
  --color-gold: #fbbf24;           /* Gold accent */
  
  /* Semantic Colors */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;
  
  /* Text Colors */
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-muted: #9ca3af;
  --text-inverse: #ffffff;
  
  /* Rounded & Spacing */
  --radius-sm: 8px;               /* Small elements */
  --radius-md: 12px;              /* Medium elements */
  --radius-lg: 16px;              /* Large elements */
  --radius-xl: 24px;              /* Extra large elements */
  --radius-full: 9999px;          /* Pills/oval shapes */
  
  /* Shadows (Apple-inspired) */
  --shadow-subtle: 0 1px 3px rgba(0, 0, 0, 0.05);
  --shadow-soft: 0 4px 12px rgba(0, 0, 0, 0.08);
  --shadow-elevated: 0 8px 25px rgba(0, 0, 0, 0.12);
  
  /* Spacing (consistent rhythm) */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 16px;
  --space-lg: 24px;
  --space-xl: 32px;
  --space-2xl: 48px;
}
```

### Design Philosophy: Serene Minimalism

#### Apple-Inspired Principles
- **Generous whitespace**: Breathing room between elements
- **Rounded corners**: Soft, approachable feel (8px-24px radius)
- **Subtle shadows**: Depth without harshness
- **Oval buttons**: Pill-shaped CTAs for friendliness
- **Clean typography**: Inter font for clarity and warmth
- **Minimal visual noise**: Focus on content, not decoration

#### Component Design Guidelines
```css
/* Primary Button - Oval/Pill Shape */
.btn-primary {
  background: var(--color-highlight);
  color: white;
  border-radius: var(--radius-full);
  padding: 12px 32px;
  box-shadow: var(--shadow-soft);
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background: var(--color-accent);
  box-shadow: var(--shadow-elevated);
  transform: translateY(-1px);
}

/* Card Component - Rounded & Elevated */
.card {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-subtle);
  padding: var(--space-lg);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

/* Input Fields - Soft Rounded */
.input {
  border-radius: var(--radius-md);
  border: 1px solid #e5e7eb;
  padding: 12px 16px;
  background: var(--color-secondary);
  transition: all 0.2s ease;
}

.input:focus {
  border-color: var(--color-accent);
  box-shadow: 0 0 0 3px rgba(248, 113, 113, 0.1);
}

/* Sidebar Navigation - Apple-Inspired */
.sidebar {
  width: 280px;
  background: rgba(249, 250, 251, 0.95);
  backdrop-filter: blur(12px);
  border-right: 1px solid rgba(0, 0, 0, 0.05);
  height: 100vh;
  padding: var(--space-lg);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 40;
}

.sidebar-item {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  padding: 12px 16px;
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.2s ease;
  font-weight: 500;
}

.sidebar-item:hover {
  background: rgba(248, 113, 113, 0.08);
  color: var(--color-highlight);
}

.sidebar-item.active {
  background: var(--color-highlight);
  color: white;
  box-shadow: var(--shadow-soft);
}

### Integration Requirements:
- **Stripe**: Payment processing + Canadian banking direct deposits
- **Twilio**: SMS notifications for critical alerts
- **OneSignal**: Push notifications for app users
- **Google OAuth**: Authentication integration
- **Geocoding API**: Postal code to coordinates conversion
- **Railway**: Deployment platform

### Testing Requirements:
- **Framework**: pytest with comprehensive fixtures
- **Coverage**: 70-80% meaningful coverage (focus on business logic)
- **Types**: Integration tests priority, unit tests for complex logic
- **Approach**: Pragmatic TDD for complex features, test-after for simple code
- **Database**: Separate test database with transaction rollback
- **Mocking**: External services (Stripe, Twilio, OAuth) and third-party APIs

### Performance Requirements:
- **API Response**: < 200ms for standard requests
- **Database**: Optimized queries with proper indexing
- **Caching**: Redis for frequently accessed data
- **Load Testing**: Support 1000+ concurrent users
- **Mobile**: Optimized for 3G/4G networks

## REMINDER: PRAGMATIC TESTING AND CONTEXT MANAGEMENT

Every feature should have:
1. **Context Check**: Verify >15% context remaining before starting any task
2. **Business Logic Tests**: Test complex calculations, workflows, and critical paths
3. **Integration Tests**: Focus on API endpoints and user journeys
4. **Security Tests**: Test auth, authorization, and data access controls
5. **Error Handling Tests**: Verify proper error responses and recovery
6. **Task Logging**: Append completion entry to TASK_COMPLETION_LOG.md after task completion

**FOCUS ON VALUE: Test what matters, skip the trivial. Quality over quantity.**

## TASK COMPLETION WORKFLOW

For each task in the TODO list:
1. **Execute** the task implementation
2. **Test** thoroughly (unit + integration tests)
3. **Verify** all quality gates are met
4. **Log Completion** by appending to TASK_COMPLETION_LOG.md using Write tool
5. **Proceed** to next task

## ERROR HANDLING & TROUBLESHOOTING WORKFLOW

When encountering errors during implementation or testing:

### 1. **Read Completion Log First**
```
Use Read tool on TASK_COMPLETION_LOG.md to:
- Review recently completed tasks that might be related
- Check for similar errors documented in previous entries
- Understand the current state of implemented components
- Identify dependencies that might be causing issues
```

### 2. **Analyze Error Context**
```
- Compare error against previous implementation notes
- Check if missing dependencies from completed tasks
- Review test patterns from successful similar tasks
- Look for environment/configuration issues from log
```

### 3. **Fix and Document**
```
- Implement fix based on log insights
- Test the fix thoroughly
- Document the error and solution in task completion entry
- Update any related configuration or dependencies
```

### 4. **Learn and Prevent**
```
- Add error prevention notes to current task log entry
- Update implementation approach based on lessons learned
- Consider if other pending tasks might have similar issues
```

## TASK_COMPLETION_LOG.md FORMAT

Use this format when logging completed tasks:

```markdown
## ✅ Task 1.1 - Set up FastAPI project structure
**Completed:** 2024-01-15 14:30 EST
**Duration:** ~2 hours
**Files Created/Modified:**
- /app/main.py
- /app/config/settings.py
- /app/config/database.py
**Tests Added:**
- test_main.py (5 unit tests)
- test_config.py (3 integration tests)
**Implementation Notes:**
- FastAPI app with dependency injection configured
- PostgreSQL connection pool established
- Environment configuration system implemented
**Errors Encountered:**
- Import error with pydantic settings (fixed by updating requirements.txt)
- Database connection timeout (resolved by adjusting pool settings)
**Solutions Applied:**
- Added pydantic[dotenv] to requirements
- Set pool_timeout=30 in database config
**Prevention for Future Tasks:**
- Always verify pydantic version compatibility
- Test database connections with realistic timeouts
**Quality Gates:** ✅ All passed (92% test coverage achieved)

---
```

### Log Entry Requirements:
- **Task ID and Description**
- **Completion timestamp**
- **Estimated duration**
- **Files created/modified**
- **Tests written**
- **Implementation notes**
- **Errors encountered and solutions**
- **Prevention notes for future tasks**
- **Quality gate confirmation**

## EMAIL CONFIGURATION

- **Billing**: <EMAIL>
- **Support**: <EMAIL>  
- **General**: <EMAIL>
- **Logo Redirect**: www.tutoraide.ca

## DATABASE NAMING CONVENTIONS

- **Tables**: Persona-based prefixes (client_*, tutor_*, billing_*, appointment_*)
- **IDs**: Descriptive names (user_id, tutor_id, appointment_id, not just 'id')
- **Timestamps**: created_at, updated_at, deleted_at for soft deletes
- **Foreign Keys**: Always use descriptive names matching the referenced table