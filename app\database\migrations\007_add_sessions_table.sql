-- Add sessions table for persistent session management
-- Version: 007
-- Description: Creates sessions table to track user sessions with tokens
-- Author: TutorAide Development Team
-- Date: 2025-01-10

-- Create sessions table
CREATE TABLE user_sessions (
    session_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL UNIQUE,
    role_type user_role_type NOT NULL,
    ip_address INET,
    user_agent TEXT,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    last_activity TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance
CREATE INDEX idx_user_sessions_token_hash ON user_sessions(token_hash) WHERE is_active = true;
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id) WHERE is_active = true;
CREATE INDEX idx_user_sessions_expires_at ON user_sessions(expires_at) WHERE is_active = true;
CREATE INDEX idx_user_sessions_last_activity ON user_sessions(last_activity) WHERE is_active = true;

-- Trigger to update updated_at
CREATE TRIGGER update_user_sessions_updated_at BEFORE UPDATE ON user_sessions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Comments for documentation
COMMENT ON TABLE user_sessions IS 'Persistent session storage for authenticated users';
COMMENT ON COLUMN user_sessions.token_hash IS 'SHA256 hash of the session token';
COMMENT ON COLUMN user_sessions.role_type IS 'The role the user is currently acting as';
COMMENT ON COLUMN user_sessions.expires_at IS 'When this session expires';
COMMENT ON COLUMN user_sessions.last_activity IS 'Last time this session was used';