"""
Repository for SMS message data access.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from asyncpg import Connection
import json

from app.database.repositories.base import BaseRepository
from app.models.notification_models import SMSStatus, SMSMessage, SMSConversation
from app.models.user_models import User
from app.core.exceptions import DatabaseOperationError

logger = logging.getLogger(__name__)


class SMSRepository(BaseRepository):
    """Repository for SMS message operations."""
    
    def __init__(self):
        """Initialize SMS repository."""
        super().__init__(table_name="sms_messages", id_column="sms_id")
    
    async def create_sms_message(
        self,
        user_id: Optional[int],
        phone_number: str,
        message: str,
        direction: str,
        status: SMSStatus,
        twilio_sid: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        conn: Optional[Connection] = None
    ) -> SMSMessage:
        """Create SMS message record."""
        query = """
            INSERT INTO sms_messages (
                user_id, phone_number, message, direction, 
                status, twilio_sid, metadata, created_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            RETURNING *
        """
        
        async with self.acquire_connection(conn) as db_conn:
            row = await db_conn.fetchrow(
                query,
                user_id,
                phone_number,
                message,
                direction,
                status.value,
                twilio_sid,
                json.dumps(metadata) if metadata else None,
                datetime.now()
            )
            
        return SMSMessage(**dict(row))
    
    async def update_sms_status(
        self,
        twilio_sid: str,
        status: SMSStatus,
        error_code: Optional[str] = None,
        error_message: Optional[str] = None,
        delivered_at: Optional[datetime] = None,
        conn: Optional[Connection] = None
    ) -> bool:
        """Update SMS message status from webhook."""
        query = """
            UPDATE sms_messages
            SET status = $2,
                error_code = $3,
                error_message = $4,
                delivered_at = $5,
                updated_at = $6
            WHERE twilio_sid = $1
        """
        
        async with self.acquire_connection(conn) as db_conn:
            result = await db_conn.execute(
                query,
                twilio_sid,
                status.value,
                error_code,
                error_message,
                delivered_at,
                datetime.now()
            )
            
        return result != "UPDATE 0"
    
    async def get_user_by_phone(
        self,
        phone_number: str,
        conn: Optional[Connection] = None
    ) -> Optional[User]:
        """Get user by phone number."""
        query = """
            SELECT u.* FROM user_accounts u
            INNER JOIN client_profiles cp ON u.user_id = cp.user_id
            WHERE cp.phone_number = $1 AND u.deleted_at IS NULL
            UNION
            SELECT u.* FROM user_accounts u
            INNER JOIN tutor_profiles tp ON u.user_id = tp.user_id
            WHERE tp.phone_number = $1 AND u.deleted_at IS NULL
            LIMIT 1
        """
        
        async with self.acquire_connection(conn) as db_conn:
            row = await db_conn.fetchrow(query, phone_number)
            
        return User(**dict(row)) if row else None
    
    async def get_conversation(
        self,
        user_id: int,
        phone_number: Optional[str] = None,
        limit: int = 50,
        conn: Optional[Connection] = None
    ) -> List[SMSMessage]:
        """Get SMS conversation history."""
        if phone_number:
            query = """
                SELECT * FROM sms_messages
                WHERE user_id = $1 AND phone_number = $2
                ORDER BY created_at DESC
                LIMIT $3
            """
            params = [user_id, phone_number, limit]
        else:
            query = """
                SELECT * FROM sms_messages
                WHERE user_id = $1
                ORDER BY created_at DESC
                LIMIT $2
            """
            params = [user_id, limit]
        
        async with self.acquire_connection(conn) as db_conn:
            rows = await db_conn.fetch(query, *params)
            
        return [SMSMessage(**dict(row)) for row in rows]
    
    async def get_conversations(
        self,
        status: Optional[str] = None,
        unread_only: bool = False,
        search_query: Optional[str] = None,
        limit: int = 50,
        offset: int = 0,
        conn: Optional[Connection] = None
    ) -> List[SMSConversation]:
        """Get conversations for manager view."""
        query = """
            WITH latest_messages AS (
                SELECT DISTINCT ON (phone_number)
                    phone_number,
                    user_id,
                    message as last_message,
                    direction as last_direction,
                    created_at as last_timestamp,
                    status,
                    metadata
                FROM sms_messages
                ORDER BY phone_number, created_at DESC
            ),
            conversation_stats AS (
                SELECT 
                    phone_number,
                    COUNT(CASE WHEN direction = 'inbound' AND read_at IS NULL THEN 1 END) as unread_count,
                    ARRAY_AGG(DISTINCT tags) FILTER (WHERE tags IS NOT NULL) as all_tags
                FROM sms_messages
                GROUP BY phone_number
            )
            SELECT 
                lm.*,
                cs.unread_count > 0 as has_unread,
                cs.all_tags as tags,
                sc.status as conversation_status,
                sc.assigned_to,
                sc.conversation_id
            FROM latest_messages lm
            LEFT JOIN conversation_stats cs ON lm.phone_number = cs.phone_number
            LEFT JOIN sms_conversations sc ON lm.phone_number = sc.phone_number
            WHERE 1=1
        """
        
        params = []
        param_count = 0
        
        if status:
            param_count += 1
            query += f" AND sc.status = ${param_count}"
            params.append(status)
        
        if unread_only:
            query += " AND cs.unread_count > 0"
        
        if search_query:
            param_count += 1
            query += f"""
                AND (
                    lm.phone_number ILIKE ${param_count}
                    OR lm.message ILIKE ${param_count}
                    OR EXISTS (
                        SELECT 1 FROM user_accounts u
                        WHERE u.user_id = lm.user_id
                        AND (u.first_name || ' ' || u.last_name) ILIKE ${param_count}
                    )
                )
            """
            params.append(f"%{search_query}%")
        
        query += f"""
            ORDER BY lm.last_timestamp DESC
            LIMIT ${param_count + 1} OFFSET ${param_count + 2}
        """
        params.extend([limit, offset])
        
        async with self.acquire_connection(conn) as db_conn:
            rows = await db_conn.fetch(query, *params)
            
        return [SMSConversation(**dict(row)) for row in rows]
    
    async def get_conversation_count(
        self,
        status: Optional[str] = None,
        unread_only: bool = False,
        search_query: Optional[str] = None,
        conn: Optional[Connection] = None
    ) -> int:
        """Get total conversation count for pagination."""
        query = """
            WITH latest_messages AS (
                SELECT DISTINCT phone_number, user_id, message
                FROM sms_messages
            ),
            conversation_stats AS (
                SELECT 
                    phone_number,
                    COUNT(CASE WHEN direction = 'inbound' AND read_at IS NULL THEN 1 END) as unread_count
                FROM sms_messages
                GROUP BY phone_number
            )
            SELECT COUNT(DISTINCT lm.phone_number)
            FROM latest_messages lm
            LEFT JOIN conversation_stats cs ON lm.phone_number = cs.phone_number
            LEFT JOIN sms_conversations sc ON lm.phone_number = sc.phone_number
            WHERE 1=1
        """
        
        params = []
        param_count = 0
        
        if status:
            param_count += 1
            query += f" AND sc.status = ${param_count}"
            params.append(status)
        
        if unread_only:
            query += " AND cs.unread_count > 0"
        
        if search_query:
            param_count += 1
            query += f"""
                AND (
                    lm.phone_number ILIKE ${param_count}
                    OR lm.message ILIKE ${param_count}
                    OR EXISTS (
                        SELECT 1 FROM user_accounts u
                        WHERE u.user_id = lm.user_id
                        AND (u.first_name || ' ' || u.last_name) ILIKE ${param_count}
                    )
                )
            """
            params.append(f"%{search_query}%")
        
        async with self.acquire_connection(conn) as db_conn:
            count = await db_conn.fetchval(query, *params)
            
        return count or 0
    
    async def get_conversation_messages(
        self,
        phone_number: str,
        limit: int = 100,
        before_timestamp: Optional[datetime] = None,
        conn: Optional[Connection] = None
    ) -> List[SMSMessage]:
        """Get messages in a conversation."""
        query = """
            SELECT sm.*, 
                   CASE 
                       WHEN sm.direction = 'outbound' AND sm.metadata->>'sent_by_name' IS NOT NULL 
                       THEN sm.metadata->>'sent_by_name'
                       ELSE NULL
                   END as sent_by_name
            FROM sms_messages sm
            WHERE phone_number = $1
        """
        
        params = [phone_number]
        param_count = 1
        
        if before_timestamp:
            param_count += 1
            query += f" AND created_at < ${param_count}"
            params.append(before_timestamp)
        
        query += f"""
            ORDER BY created_at DESC
            LIMIT ${param_count + 1}
        """
        params.append(limit)
        
        async with self.acquire_connection(conn) as db_conn:
            rows = await db_conn.fetch(query, *params)
            
        return [SMSMessage(**dict(row)) for row in rows]
    
    async def mark_messages_read(
        self,
        message_ids: List[int],
        read_by: int,
        conn: Optional[Connection] = None
    ) -> None:
        """Mark messages as read."""
        query = """
            UPDATE sms_messages
            SET read_at = $1,
                read_by = $2
            WHERE sms_id = ANY($3)
            AND direction = 'inbound'
            AND read_at IS NULL
        """
        
        async with self.acquire_connection(conn) as db_conn:
            await db_conn.execute(
                query,
                datetime.now(),
                read_by,
                message_ids
            )
    
    async def update_conversation_status(
        self,
        phone_number: str,
        status: str,
        assigned_to: Optional[int] = None,
        notes: Optional[str] = None,
        conn: Optional[Connection] = None
    ) -> None:
        """Update or create conversation status."""
        query = """
            INSERT INTO sms_conversations (phone_number, status, assigned_to, notes, updated_at)
            VALUES ($1, $2, $3, $4, $5)
            ON CONFLICT (phone_number) DO UPDATE
            SET status = $2,
                assigned_to = COALESCE($3, sms_conversations.assigned_to),
                notes = COALESCE($4, sms_conversations.notes),
                updated_at = $5
        """
        
        async with self.acquire_connection(conn) as db_conn:
            await db_conn.execute(
                query,
                phone_number,
                status,
                assigned_to,
                notes,
                datetime.now()
            )
    
    async def update_conversation_assignment(
        self,
        phone_number: str,
        assigned_to: int,
        conn: Optional[Connection] = None
    ) -> None:
        """Update conversation assignment."""
        await self.update_conversation_status(
            phone_number=phone_number,
            status='active',
            assigned_to=assigned_to,
            conn=conn
        )
    
    async def add_conversation_tags(
        self,
        phone_number: str,
        tags: List[str],
        conn: Optional[Connection] = None
    ) -> None:
        """Add tags to conversation."""
        query = """
            UPDATE sms_conversations
            SET tags = array_cat(COALESCE(tags, ARRAY[]::text[]), $2::text[]),
                updated_at = $3
            WHERE phone_number = $1
        """
        
        async with self.acquire_connection(conn) as db_conn:
            await db_conn.execute(
                query,
                phone_number,
                tags,
                datetime.now()
            )
    
    async def log_conversation_event(
        self,
        phone_number: str,
        event_type: str,
        event_data: Dict[str, Any],
        conn: Optional[Connection] = None
    ) -> None:
        """Log conversation event."""
        query = """
            INSERT INTO conversation_events (
                phone_number, event_type, event_data, created_at
            ) VALUES ($1, $2, $3, $4)
        """
        
        async with self.acquire_connection(conn) as db_conn:
            await db_conn.execute(
                query,
                phone_number,
                event_type,
                json.dumps(event_data),
                datetime.now()
            )
    
    async def get_conversation_statistics(
        self,
        date_range: Tuple[datetime, datetime],
        conn: Optional[Connection] = None
    ) -> Dict[str, Any]:
        """Get conversation statistics."""
        start_date, end_date = date_range
        
        query = """
            WITH conversation_counts AS (
                SELECT 
                    COUNT(DISTINCT phone_number) as total_conversations,
                    COUNT(DISTINCT CASE WHEN status = 'active' THEN phone_number END) as active_conversations,
                    COUNT(CASE WHEN direction = 'inbound' AND read_at IS NULL THEN 1 END) as unread_messages,
                    COUNT(CASE WHEN direction = 'outbound' THEN 1 END) as messages_sent,
                    COUNT(CASE WHEN direction = 'inbound' THEN 1 END) as messages_received
                FROM sms_messages
                WHERE created_at BETWEEN $1 AND $2
            ),
            status_breakdown AS (
                SELECT status, COUNT(*) as count
                FROM sms_conversations
                GROUP BY status
            ),
            top_tags AS (
                SELECT unnest(tags) as tag, COUNT(*) as count
                FROM sms_conversations
                WHERE tags IS NOT NULL
                GROUP BY tag
                ORDER BY count DESC
                LIMIT 10
            ),
            busiest_hours AS (
                SELECT 
                    EXTRACT(HOUR FROM created_at) as hour,
                    COUNT(*) as message_count
                FROM sms_messages
                WHERE created_at BETWEEN $1 AND $2
                GROUP BY hour
                ORDER BY hour
            ),
            manager_activity AS (
                SELECT 
                    u.user_id,
                    u.first_name || ' ' || u.last_name as manager_name,
                    COUNT(DISTINCT sm.phone_number) as conversations_handled,
                    COUNT(sm.sms_id) as messages_sent
                FROM sms_messages sm
                JOIN user_accounts u ON u.user_id = CAST(sm.metadata->>'sent_by' AS INTEGER)
                WHERE sm.direction = 'outbound'
                AND sm.created_at BETWEEN $1 AND $2
                GROUP BY u.user_id, manager_name
            )
            SELECT 
                cc.*,
                (SELECT json_object_agg(status, count) FROM status_breakdown) as status_breakdown,
                (SELECT json_agg(json_build_object('tag', tag, 'count', count)) FROM top_tags) as top_tags,
                (SELECT json_agg(json_build_object('hour', hour, 'count', message_count)) FROM busiest_hours) as busiest_hours,
                (SELECT json_agg(json_build_object('user_id', user_id, 'name', manager_name, 'conversations', conversations_handled, 'messages', messages_sent)) FROM manager_activity) as manager_activity
            FROM conversation_counts cc
        """
        
        async with self.acquire_connection(conn) as db_conn:
            row = await db_conn.fetchrow(query, start_date, end_date)
            
        return dict(row) if row else {}
    
    async def get_response_time_metrics(
        self,
        date_range: Tuple[datetime, datetime],
        conn: Optional[Connection] = None
    ) -> Dict[str, Any]:
        """Get response time metrics."""
        start_date, end_date = date_range
        
        query = """
            WITH response_times AS (
                SELECT 
                    sm1.phone_number,
                    sm1.created_at as inbound_time,
                    MIN(sm2.created_at) as response_time,
                    EXTRACT(EPOCH FROM (MIN(sm2.created_at) - sm1.created_at)) / 60 as response_minutes
                FROM sms_messages sm1
                LEFT JOIN sms_messages sm2 
                    ON sm1.phone_number = sm2.phone_number
                    AND sm2.direction = 'outbound'
                    AND sm2.created_at > sm1.created_at
                    AND sm2.created_at < sm1.created_at + INTERVAL '24 hours'
                WHERE sm1.direction = 'inbound'
                AND sm1.created_at BETWEEN $1 AND $2
                GROUP BY sm1.phone_number, sm1.created_at
            )
            SELECT 
                AVG(response_minutes) as average,
                PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY response_minutes) as median,
                COUNT(CASE WHEN response_time IS NOT NULL THEN 1 END)::float / COUNT(*) as response_rate
            FROM response_times
        """
        
        async with self.acquire_connection(conn) as db_conn:
            row = await db_conn.fetchrow(query, start_date, end_date)
            
        return dict(row) if row else {'average': 0, 'median': 0, 'response_rate': 0}
    
    async def get_conversation_metrics(
        self,
        phone_number: str,
        user_id: Optional[int],
        conn: Optional[Connection] = None
    ) -> Dict[str, Any]:
        """Get metrics for a specific conversation."""
        query = """
            SELECT 
                COUNT(*) as total_messages,
                COUNT(CASE WHEN direction = 'inbound' THEN 1 END) as inbound_messages,
                COUNT(CASE WHEN direction = 'outbound' THEN 1 END) as outbound_messages,
                MIN(created_at) as first_message,
                MAX(created_at) as last_message,
                AVG(LENGTH(message)) as avg_message_length
            FROM sms_messages
            WHERE phone_number = $1
        """
        
        async with self.acquire_connection(conn) as db_conn:
            row = await db_conn.fetchrow(query, phone_number)
            
        return dict(row) if row else {}
    
    async def get_all_conversation_messages(
        self,
        phone_number: str,
        conn: Optional[Connection] = None
    ) -> List[SMSMessage]:
        """Get all messages for export."""
        query = """
            SELECT sm.*, 
                   CASE 
                       WHEN sm.direction = 'outbound' AND sm.metadata->>'sent_by_name' IS NOT NULL 
                       THEN sm.metadata->>'sent_by_name'
                       ELSE NULL
                   END as sent_by_name
            FROM sms_messages sm
            WHERE phone_number = $1
            ORDER BY created_at ASC
        """
        
        async with self.acquire_connection(conn) as db_conn:
            rows = await db_conn.fetch(query, phone_number)
            
        return [SMSMessage(**dict(row)) for row in rows]
    
    async def get_conversation_by_phone(
        self,
        phone_number: str,
        conn: Optional[Connection] = None
    ) -> Optional[SMSConversation]:
        """Get conversation by phone number."""
        query = """
            SELECT * FROM sms_conversations
            WHERE phone_number = $1
        """
        
        async with self.acquire_connection(conn) as db_conn:
            row = await db_conn.fetchrow(query, phone_number)
            
        return SMSConversation(**dict(row)) if row else None
    
    async def is_opted_out(
        self,
        user_id: int,
        phone_number: str,
        conn: Optional[Connection] = None
    ) -> bool:
        """Check if user has opted out of SMS."""
        query = """
            SELECT sms_opted_out FROM notification_preferences
            WHERE user_id = $1
        """
        
        async with self.acquire_connection(conn) as db_conn:
            opted_out = await db_conn.fetchval(query, user_id)
            
        return opted_out or False
    
    async def update_opt_out_status(
        self,
        user_id: int,
        phone_number: str,
        opted_out: bool,
        conn: Optional[Connection] = None
    ) -> None:
        """Update user's SMS opt-out status."""
        query = """
            UPDATE notification_preferences
            SET sms_opted_out = $2,
                updated_at = $3
            WHERE user_id = $1
        """
        
        async with self.acquire_connection(conn) as db_conn:
            await db_conn.execute(
                query,
                user_id,
                opted_out,
                datetime.now()
            )
    
    async def get_pending_confirmation(
        self,
        user_id: int,
        phone_number: str,
        conn: Optional[Connection] = None
    ) -> Optional[Dict[str, Any]]:
        """Get pending appointment confirmation for user."""
        query = """
            SELECT ac.* FROM appointment_confirmations ac
            JOIN appointments a ON a.appointment_id = ac.appointment_id
            WHERE a.tutor_id = $1
            AND ac.status = 'pending'
            AND ac.expires_at > $2
            ORDER BY ac.created_at DESC
            LIMIT 1
        """
        
        async with self.acquire_connection(conn) as db_conn:
            row = await db_conn.fetchrow(query, user_id, datetime.now())
            
        return dict(row) if row else None
    
    async def get_template(
        self,
        template_name: str,
        language: str,
        conn: Optional[Connection] = None
    ) -> Optional[Dict[str, Any]]:
        """Get SMS template."""
        query = """
            SELECT * FROM sms_templates
            WHERE name = $1 AND language = $2 AND active = true
        """
        
        async with self.acquire_connection(conn) as db_conn:
            row = await db_conn.fetchrow(query, template_name, language)
            
        return dict(row) if row else None