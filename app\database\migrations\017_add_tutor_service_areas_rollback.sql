-- Rollback Migration 017: Remove tutor service areas table

-- Drop function
DROP FUNCTION IF EXISTS is_postal_code_covered(TEXT);

-- Drop view
DROP VIEW IF EXISTS tutor_service_coverage;

-- Drop trigger and function
DROP TRIGGER IF EXISTS trigger_update_tutor_service_areas_updated_at ON tutor_service_areas;
DROP FUNCTION IF EXISTS update_tutor_service_areas_updated_at();

-- Drop indexes
DROP INDEX IF EXISTS idx_tutor_service_areas_service_types;
DROP INDEX IF EXISTS idx_tutor_service_areas_covered_postal;
DROP INDEX IF EXISTS idx_tutor_service_areas_active;
DROP INDEX IF EXISTS idx_tutor_service_areas_center_postal;
DROP INDEX IF EXISTS idx_tutor_service_areas_tutor_id;

-- Drop table
DROP TABLE IF EXISTS tutor_service_areas;