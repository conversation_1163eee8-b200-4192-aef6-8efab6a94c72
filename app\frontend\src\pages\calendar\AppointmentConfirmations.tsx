import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { format, formatDistanceToNow, isAfter, isBefore, addHours } from 'date-fns';
import { 
  MessageSquare, Clock, CheckCircle, XCircle, AlertTriangle, Send, 
  Eye, Filter, Download, RefreshCw, Users, Calendar, Phone
} from 'lucide-react';

interface Confirmation {
  confirmation_id: number;
  appointment_id: number;
  appointment_date: Date;
  appointment_time: string;
  tutor_id: number;
  tutor_name: string;
  client_id: number;
  client_name: string;
  subject_area: string;
  status: 'pending' | 'sent' | 'confirmed' | 'declined' | 'expired' | 'cancelled';
  confirmation_type: 'appointment' | 'completion';
  created_at: Date;
  sent_at?: Date;
  confirmed_at?: Date;
  expires_at: Date;
  response?: string;
  notification_count: number;
  last_reminder_sent?: Date;
  tutor_phone?: string;
}

interface ConfirmationStats {
  total_appointments: number;
  confirmations_requested: number;
  confirmations_received: number;
  confirmations_declined: number;
  confirmations_expired: number;
  confirmation_rate: number;
  avg_response_time_hours: number;
  breakdown_by_status: Record<string, number>;
}

interface AppointmentConfirmationsProps {
  tutorId?: number;
  appointmentId?: number;
  isModal?: boolean;
  onClose?: () => void;
}

const AppointmentConfirmations: React.FC<AppointmentConfirmationsProps> = ({
  tutorId,
  appointmentId,
  isModal = false,
  onClose
}) => {
  const { t } = useTranslation();
  const [confirmations, setConfirmations] = useState<Confirmation[]>([]);
  const [stats, setStats] = useState<ConfirmationStats | null>(null);
  const [selectedConfirmation, setSelectedConfirmation] = useState<Confirmation | null>(null);
  const [filter, setFilter] = useState<string>('all');
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [statsLoading, setStatsLoading] = useState(false);

  // Mock data - replace with actual API calls
  useEffect(() => {
    const mockConfirmations: Confirmation[] = [
      {
        confirmation_id: 1,
        appointment_id: 1,
        appointment_date: new Date(2024, 2, 15),
        appointment_time: '14:00',
        tutor_id: 1,
        tutor_name: 'Marie Dubois',
        client_id: 1,
        client_name: 'Emma Johnson',
        subject_area: 'Mathematics',
        status: 'pending',
        confirmation_type: 'appointment',
        created_at: new Date(2024, 2, 12, 10, 30),
        expires_at: new Date(2024, 2, 13, 10, 30),
        notification_count: 1,
        last_reminder_sent: new Date(2024, 2, 12, 10, 30),
        tutor_phone: '+****************'
      },
      {
        confirmation_id: 2,
        appointment_id: 2,
        appointment_date: new Date(2024, 2, 14),
        appointment_time: '16:00',
        tutor_id: 2,
        tutor_name: 'John Smith',
        client_id: 2,
        client_name: 'Lucas Martin',
        subject_area: 'Physics',
        status: 'confirmed',
        confirmation_type: 'appointment',
        created_at: new Date(2024, 2, 11, 9, 15),
        sent_at: new Date(2024, 2, 11, 9, 15),
        confirmed_at: new Date(2024, 2, 11, 14, 20),
        expires_at: new Date(2024, 2, 12, 9, 15),
        response: 'YES',
        notification_count: 1,
        tutor_phone: '+****************'
      },
      {
        confirmation_id: 3,
        appointment_id: 3,
        appointment_date: new Date(2024, 2, 13),
        appointment_time: '10:00',
        tutor_id: 3,
        tutor_name: 'Sophie Martin',
        client_id: 3,
        client_name: 'Sophia Chen',
        subject_area: 'Chemistry',
        status: 'expired',
        confirmation_type: 'appointment',
        created_at: new Date(2024, 2, 10, 16, 45),
        sent_at: new Date(2024, 2, 10, 16, 45),
        expires_at: new Date(2024, 2, 11, 16, 45),
        notification_count: 2,
        last_reminder_sent: new Date(2024, 2, 11, 12, 0),
        tutor_phone: '+****************'
      }
    ];

    const mockStats: ConfirmationStats = {
      total_appointments: 50,
      confirmations_requested: 45,
      confirmations_received: 38,
      confirmations_declined: 3,
      confirmations_expired: 4,
      confirmation_rate: 84.4,
      avg_response_time_hours: 3.2,
      breakdown_by_status: {
        'pending': 8,
        'confirmed': 38,
        'declined': 3,
        'expired': 4,
        'cancelled': 2
      }
    };

    setConfirmations(mockConfirmations);
    setStats(mockStats);
    setLoading(false);
  }, []);

  const handleSendConfirmation = async (appointmentId: number) => {
    setSending(true);
    try {
      // API call would go here
      console.log('Sending confirmation for appointment:', appointmentId);
      
      // Update local state
      setConfirmations(prev => prev.map(conf => 
        conf.appointment_id === appointmentId 
          ? { ...conf, status: 'sent' as const, sent_at: new Date(), notification_count: conf.notification_count + 1 }
          : conf
      ));
      
      alert(t('confirmations.confirmationSent'));
    } catch (error) {
      console.error('Error sending confirmation:', error);
      alert(t('confirmations.sendError'));
    } finally {
      setSending(false);
    }
  };

  const handleSendReminder = async (confirmationId: number) => {
    setSending(true);
    try {
      // API call would go here
      console.log('Sending reminder for confirmation:', confirmationId);
      
      // Update local state
      setConfirmations(prev => prev.map(conf => 
        conf.confirmation_id === confirmationId 
          ? { ...conf, notification_count: conf.notification_count + 1, last_reminder_sent: new Date() }
          : conf
      ));
      
      alert(t('confirmations.reminderSent'));
    } catch (error) {
      console.error('Error sending reminder:', error);
      alert(t('confirmations.reminderError'));
    } finally {
      setSending(false);
    }
  };

  const handleBulkReminders = async () => {
    const pendingConfirmations = confirmations.filter(c => c.status === 'pending');
    
    if (pendingConfirmations.length === 0) {
      alert(t('confirmations.noPendingConfirmations'));
      return;
    }

    if (!confirm(t('confirmations.confirmBulkReminders', { count: pendingConfirmations.length }))) {
      return;
    }

    setSending(true);
    try {
      // API call would go here
      console.log('Sending bulk reminders for', pendingConfirmations.length, 'confirmations');
      
      // Update local state
      setConfirmations(prev => prev.map(conf => 
        conf.status === 'pending' 
          ? { ...conf, notification_count: conf.notification_count + 1, last_reminder_sent: new Date() }
          : conf
      ));
      
      alert(t('confirmations.bulkRemindersSent', { count: pendingConfirmations.length }));
    } catch (error) {
      console.error('Error sending bulk reminders:', error);
      alert(t('confirmations.bulkRemindersError'));
    } finally {
      setSending(false);
    }
  };

  const getStatusColor = (status: string): string => {
    const colors = {
      pending: 'text-yellow-600 bg-yellow-100',
      sent: 'text-red-600 bg-red-100',
      confirmed: 'text-green-600 bg-green-100',
      declined: 'text-red-600 bg-red-100',
      expired: 'text-gray-600 bg-gray-100',
      cancelled: 'text-gray-500 bg-gray-50'
    };
    return colors[status as keyof typeof colors] || colors.pending;
  };

  const getStatusIcon = (status: string) => {
    const icons = {
      pending: <Clock className="w-4 h-4" />,
      sent: <MessageSquare className="w-4 h-4" />,
      confirmed: <CheckCircle className="w-4 h-4" />,
      declined: <XCircle className="w-4 h-4" />,
      expired: <AlertTriangle className="w-4 h-4" />,
      cancelled: <XCircle className="w-4 h-4" />
    };
    return icons[status as keyof typeof icons] || icons.pending;
  };

  const isExpiringSoon = (confirmation: Confirmation): boolean => {
    return confirmation.status === 'pending' && 
           isBefore(confirmation.expires_at, addHours(new Date(), 2));
  };

  const filteredConfirmations = confirmations.filter(confirmation => {
    if (filter === 'all') return true;
    if (filter === 'pending') return confirmation.status === 'pending';
    if (filter === 'confirmed') return confirmation.status === 'confirmed';
    if (filter === 'declined') return confirmation.status === 'declined';
    if (filter === 'expired') return confirmation.status === 'expired';
    if (filter === 'expiring') return isExpiringSoon(confirmation);
    return true;
  });

  const renderStats = () => (
    <div className="bg-white rounded-lg p-6 border border-gray-200 mb-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        {t('confirmations.statistics')}
      </h3>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="text-center">
          <div className="text-2xl font-bold text-red-600">{stats?.confirmation_rate.toFixed(1)}%</div>
          <div className="text-sm text-gray-600">{t('confirmations.confirmationRate')}</div>
        </div>
        
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">{stats?.confirmations_received}</div>
          <div className="text-sm text-gray-600">{t('confirmations.confirmed')}</div>
        </div>
        
        <div className="text-center">
          <div className="text-2xl font-bold text-yellow-600">{stats?.breakdown_by_status.pending || 0}</div>
          <div className="text-sm text-gray-600">{t('confirmations.pending')}</div>
        </div>
        
        <div className="text-center">
          <div className="text-2xl font-bold text-gray-600">{stats?.avg_response_time_hours.toFixed(1)}h</div>
          <div className="text-sm text-gray-600">{t('confirmations.avgResponseTime')}</div>
        </div>
      </div>
    </div>
  );

  const renderConfirmationCard = (confirmation: Confirmation) => (
    <div
      key={confirmation.confirmation_id}
      className={`border rounded-lg p-4 hover:bg-gray-50 ${
        isExpiringSoon(confirmation) ? 'border-orange-300 bg-orange-50' : 'border-gray-200'
      }`}
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-1">
            <span className="font-medium text-gray-900">
              {confirmation.subject_area}
            </span>
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(confirmation.status)}`}>
              {getStatusIcon(confirmation.status)}
              <span className="ml-1">{t(`confirmations.status.${confirmation.status}`)}</span>
            </span>
            {isExpiringSoon(confirmation) && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-orange-600 bg-orange-100">
                <AlertTriangle className="w-3 h-3 mr-1" />
                {t('confirmations.expiringSoon')}
              </span>
            )}
          </div>
          
          <p className="text-sm text-gray-600 mb-1">
            {confirmation.tutor_name} → {confirmation.client_name}
          </p>
          
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <div className="flex items-center">
              <Calendar className="w-3 h-3 mr-1" />
              {format(confirmation.appointment_date, 'MMM dd, yyyy')}
            </div>
            <div className="flex items-center">
              <Clock className="w-3 h-3 mr-1" />
              {confirmation.appointment_time}
            </div>
            <div className="flex items-center">
              <MessageSquare className="w-3 h-3 mr-1" />
              {confirmation.notification_count} {t('confirmations.notifications')}
            </div>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {confirmation.status === 'pending' && (
            <button
              onClick={() => handleSendReminder(confirmation.confirmation_id)}
              disabled={sending}
              className="p-1 text-red-600 hover:text-red-800"
              title={t('confirmations.sendReminder')}
            >
              <Send className="w-4 h-4" />
            </button>
          )}
          
          <button
            onClick={() => setSelectedConfirmation(confirmation)}
            className="p-1 text-gray-400 hover:text-gray-600"
            title={t('confirmations.viewDetails')}
          >
            <Eye className="w-4 h-4" />
          </button>
        </div>
      </div>
      
      {/* Additional Details */}
      <div className="space-y-1 text-xs text-gray-500">
        {confirmation.created_at && (
          <div>
            {t('confirmations.requested')}: {formatDistanceToNow(confirmation.created_at)} ago
          </div>
        )}
        
        {confirmation.confirmed_at && (
          <div className="text-green-600">
            {t('confirmations.confirmedAt')}: {format(confirmation.confirmed_at, 'MMM dd, HH:mm')}
          </div>
        )}
        
        {confirmation.status === 'pending' && (
          <div className={isExpiringSoon(confirmation) ? 'text-orange-600' : 'text-gray-500'}>
            {t('confirmations.expires')}: {format(confirmation.expires_at, 'MMM dd, HH:mm')}
          </div>
        )}
        
        {confirmation.response && (
          <div className="font-medium">
            {t('confirmations.response')}: "{confirmation.response}"
          </div>
        )}
      </div>
    </div>
  );

  const renderConfirmationsList = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">
          {t('confirmations.list')}
        </h3>
        
        <div className="flex items-center space-x-3">
          {/* Filter */}
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="text-sm border border-gray-300 rounded px-2 py-1"
          >
            <option value="all">{t('confirmations.filters.all')}</option>
            <option value="pending">{t('confirmations.filters.pending')}</option>
            <option value="confirmed">{t('confirmations.filters.confirmed')}</option>
            <option value="declined">{t('confirmations.filters.declined')}</option>
            <option value="expired">{t('confirmations.filters.expired')}</option>
            <option value="expiring">{t('confirmations.filters.expiring')}</option>
          </select>
          
          {/* Bulk Actions */}
          <button
            onClick={handleBulkReminders}
            disabled={sending}
            className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
          >
            <Send className="w-4 h-4 mr-2" />
            {sending ? t('common.sending') : t('confirmations.sendBulkReminders')}
          </button>
          
          <button
            onClick={() => window.location.reload()}
            className="p-2 text-gray-400 hover:text-gray-600"
            title={t('common.refresh')}
          >
            <RefreshCw className="w-4 h-4" />
          </button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {filteredConfirmations.map(renderConfirmationCard)}
      </div>
      
      {filteredConfirmations.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <MessageSquare className="w-12 h-12 mx-auto mb-3 text-gray-300" />
          <p>{t('confirmations.noConfirmations')}</p>
        </div>
      )}
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
      </div>
    );
  }

  const content = (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">
            {t('confirmations.title')}
          </h2>
          <p className="text-sm text-gray-500">
            {t('confirmations.description')}
          </p>
        </div>
      </div>

      {/* Statistics */}
      {stats && renderStats()}

      {/* Confirmations List */}
      {renderConfirmationsList()}
    </div>
  );

  if (isModal) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
          <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">
              {t('confirmations.title')}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XCircle className="w-6 h-6" />
            </button>
          </div>
          <div className="p-6">
            {content}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      {content}
    </div>
  );
};

export default AppointmentConfirmations;
