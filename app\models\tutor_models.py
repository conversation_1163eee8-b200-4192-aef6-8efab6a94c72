"""
Tutor domain models for tutor profiles, availability, rates, and services.
"""

from datetime import datetime, date, time
from typing import List, Optional, Dict, Any
from decimal import Decimal
from pydantic import BaseModel, Field, field_validator, ConfigDict

from app.models.base import (
    BaseEntity,
    IdentifiedEntity,
    AddressModel,
    Currency,
    SearchFilters
)

# Enhanced validation imports
from app.core.validation import DataValidator, InputSanitizer


class TutorProfile(IdentifiedEntity):
    """Tutor profile model."""
    
    tutor_id: int = Field(..., description="Unique tutor identifier")
    user_id: int = Field(..., description="Associated user account")
    first_name: str = Field(..., description="Tutor first name")
    last_name: str = Field(..., description="Tutor last name")
    phone: str = Field(..., description="Tutor phone number")
    bio: Optional[str] = Field(None, description="Tutor biography")
    specializations: List[str] = Field(default_factory=list, description="Subject specializations")
    postal_code: str = Field(..., description="Tutor postal code")
    latitude: Optional[Decimal] = Field(None, description="Latitude coordinate")
    longitude: Optional[Decimal] = Field(None, description="Longitude coordinate")
    verification_status: str = Field(default="pending", description="Verification status")
    is_available: bool = Field(True, description="Whether tutor is available for bookings")
    rating: Optional[Decimal] = Field(None, description="Average rating")
    total_sessions: int = Field(default=0, description="Total completed sessions")
    
    # Education fields
    highest_degree_level: Optional[str] = Field(None, description="Degree level for filtering")
    highest_degree_name: Optional[str] = Field(None, description="Full degree name")
    degree_major: Optional[str] = Field(None, description="Major field of study")
    university_name: Optional[str] = Field(None, description="University/institution name")
    graduation_year: Optional[int] = Field(None, description="Year of graduation")
    gpa: Optional[Decimal] = Field(None, description="Grade point average")
    additional_education: List[Dict[str, Any]] = Field(default_factory=list, description="Additional degrees/certifications")
    teaching_certifications: List[str] = Field(default_factory=list, description="Teaching certifications")
    certification_details: Dict[str, Any] = Field(default_factory=dict, description="Detailed certification info")
    
    # Experience fields
    years_of_experience: int = Field(default=0, description="Total years of teaching experience")
    years_online_teaching: int = Field(default=0, description="Years of online teaching")
    years_tutoring: int = Field(default=0, description="Years of tutoring experience")
    students_taught_total: int = Field(default=0, description="Total number of students taught")
    current_occupation: Optional[str] = Field(None, description="Current occupation")
    previous_teaching_positions: List[Dict[str, Any]] = Field(default_factory=list, description="Previous teaching positions")
    subject_expertise: Dict[str, str] = Field(default_factory=dict, description="Subject expertise levels")
    success_stories: Optional[str] = Field(None, description="Notable student success stories")
    
    # Professional background
    teaching_languages: List[str] = Field(default_factory=list, description="Languages can teach in")
    language_proficiency: Dict[str, str] = Field(default_factory=dict, description="Language proficiency levels")
    teaching_methodology: List[str] = Field(default_factory=list, description="Teaching methodologies")
    special_needs_experience: bool = Field(default=False, description="Experience with special needs")
    age_groups_experience: List[str] = Field(default_factory=list, description="Age groups taught")
    last_training_date: Optional[date] = Field(None, description="Last professional development")
    professional_memberships: List[str] = Field(default_factory=list, description="Professional associations")
    
    # Portfolio and references
    portfolio_url: Optional[str] = Field(None, description="Teaching portfolio URL")
    sample_lesson_urls: List[str] = Field(default_factory=list, description="Sample lesson URLs")
    achievement_highlights: Optional[str] = Field(None, description="Key achievements")
    has_references: bool = Field(default=False, description="Has provided references")
    reference_check_completed: bool = Field(default=False, description="References verified")
    reference_summary: Optional[str] = Field(None, description="Manager's reference summary")
    
    # Background verification
    background_check_date: Optional[date] = Field(None, description="Last background check date")
    background_check_provider: Optional[str] = Field(None, description="Background check provider")
    background_check_status: Optional[str] = Field(None, description="Background check status")
    working_with_children_clearance: bool = Field(default=False, description="Clearance to work with minors")
    police_check_expiry: Optional[date] = Field(None, description="Police check expiry date")
    has_liability_insurance: bool = Field(default=False, description="Has liability insurance")
    insurance_provider: Optional[str] = Field(None, description="Insurance provider")
    insurance_expiry: Optional[date] = Field(None, description="Insurance expiry date")
    work_permit_status: Optional[str] = Field(None, description="Work authorization status")
    work_permit_expiry: Optional[date] = Field(None, description="Work permit expiry date")
    
    @field_validator('first_name', 'last_name')
    @classmethod
    def validate_names(cls, v: str) -> str:
        """Validate name fields with enhanced security."""
        return DataValidator.validate_name(v, "tutor name")
    
    @field_validator('phone')
    @classmethod
    def validate_phone(cls, v: str) -> str:
        """Validate phone number format with enhanced security."""
        v = InputSanitizer.sanitize_text(v)
        return InputSanitizer.normalize_phone(v)
    
    @field_validator('bio')
    @classmethod
    def validate_bio(cls, v: Optional[str]) -> Optional[str]:
        """Validate bio with enhanced security."""
        if v is None:
            return v
        return DataValidator.validate_text_field(v, "bio", max_length=2000)
    
    @field_validator('postal_code')
    @classmethod
    def validate_postal_code(cls, v: str) -> str:
        """Validate postal code with enhanced security."""
        v = InputSanitizer.sanitize_text(v)
        return InputSanitizer.normalize_postal_code(v)
    
    @field_validator('verification_status')
    @classmethod
    def validate_verification_status(cls, v: str) -> str:
        """Validate verification status with enhanced security."""
        valid_statuses = ['pending', 'documents_submitted', 'background_check', 'fully_verified', 'suspended']
        return DataValidator.validate_choice_field(v, "verification_status", valid_statuses)
    
    @field_validator('rating')
    @classmethod
    def validate_rating(cls, v: Optional[Decimal]) -> Optional[Decimal]:
        """Validate rating with enhanced security."""
        if v is not None:
            return DataValidator.validate_numeric_field(v, "rating", min_value=1.0, max_value=5.0)
        return v
    
    @field_validator('highest_degree_level')
    @classmethod
    def validate_degree_level(cls, v: Optional[str]) -> Optional[str]:
        """Validate degree level."""
        if v is None:
            return v
        valid_levels = ['high_school', 'associate', 'bachelor', 'master', 'phd', 'professional', 'other']
        return DataValidator.validate_choice_field(v, "highest_degree_level", valid_levels)
    
    @field_validator('highest_degree_name', 'degree_major', 'university_name', 'current_occupation')
    @classmethod
    def validate_education_text_fields(cls, v: Optional[str]) -> Optional[str]:
        """Validate education text fields."""
        if v is None:
            return v
        return DataValidator.validate_text_field(v, "education_field", max_length=200)
    
    @field_validator('graduation_year')
    @classmethod
    def validate_graduation_year(cls, v: Optional[int]) -> Optional[int]:
        """Validate graduation year."""
        if v is None:
            return v
        current_year = datetime.now().year
        return DataValidator.validate_numeric_field(v, "graduation_year", min_value=1950, max_value=current_year)
    
    @field_validator('gpa')
    @classmethod
    def validate_gpa(cls, v: Optional[Decimal]) -> Optional[Decimal]:
        """Validate GPA."""
        if v is None:
            return v
        return DataValidator.validate_numeric_field(v, "gpa", min_value=0.0, max_value=4.0)
    
    @field_validator('years_of_experience', 'years_online_teaching', 'years_tutoring', 'students_taught_total')
    @classmethod
    def validate_experience_years(cls, v: int) -> int:
        """Validate experience year fields."""
        return DataValidator.validate_numeric_field(v, "experience_years", min_value=0, max_value=50)
    
    @field_validator('success_stories', 'achievement_highlights', 'reference_summary')
    @classmethod
    def validate_text_stories(cls, v: Optional[str]) -> Optional[str]:
        """Validate text story fields."""
        if v is None:
            return v
        return DataValidator.validate_text_field(v, "story_field", max_length=2000)
    
    @field_validator('portfolio_url')
    @classmethod
    def validate_portfolio_url(cls, v: Optional[str]) -> Optional[str]:
        """Validate portfolio URL."""
        if v is None:
            return v
        v = InputSanitizer.sanitize_text(v)
        # Basic URL validation
        if not (v.startswith('http://') or v.startswith('https://')):
            raise ValueError("Portfolio URL must start with http:// or https://")
        return DataValidator.validate_text_field(v, "portfolio_url", max_length=500)
    
    @field_validator('background_check_status')
    @classmethod
    def validate_background_check_status(cls, v: Optional[str]) -> Optional[str]:
        """Validate background check status."""
        if v is None:
            return v
        valid_statuses = ['not_started', 'pending', 'passed', 'requires_review', 'failed']
        return DataValidator.validate_choice_field(v, "background_check_status", valid_statuses)
    
    @field_validator('work_permit_status')
    @classmethod
    def validate_work_permit_status(cls, v: Optional[str]) -> Optional[str]:
        """Validate work permit status."""
        if v is None:
            return v
        valid_statuses = ['citizen', 'permanent_resident', 'work_permit', 'student_visa', 'other']
        return DataValidator.validate_choice_field(v, "work_permit_status", valid_statuses)


class TutorAvailability(IdentifiedEntity):
    """Tutor weekly availability model."""
    
    availability_id: int = Field(..., description="Unique availability identifier")
    tutor_id: int = Field(..., description="Associated tutor")
    day_of_week: int = Field(..., description="Day of week (1=Monday, 7=Sunday)")
    start_time: time = Field(..., description="Start time")
    end_time: time = Field(..., description="End time")
    is_active: bool = Field(True, description="Whether this availability slot is active")
    
    @field_validator('day_of_week')
    @classmethod
    def validate_day_of_week(cls, v: int) -> int:
        """Validate day of week with enhanced security."""
        return DataValidator.validate_numeric_field(v, "day_of_week", min_value=1, max_value=7)
    
    @field_validator('end_time')
    @classmethod
    def validate_end_time(cls, v: time, info) -> time:
        """Validate end time is after start time."""
        if 'start_time' in info.data and v <= info.data['start_time']:
            raise ValueError('End time must be after start time')
        return v


class TutorTimeOff(IdentifiedEntity):
    """Tutor time-off request model."""
    
    time_off_id: int = Field(..., description="Unique time-off identifier")
    tutor_id: int = Field(..., description="Associated tutor")
    start_date: date = Field(..., description="Start date of time off")
    end_date: date = Field(..., description="End date of time off")
    reason: Optional[str] = Field(None, description="Reason for time off")
    is_approved: Optional[bool] = Field(None, description="Manager approval status")
    approved_by: Optional[int] = Field(None, description="Manager who approved")
    approved_at: Optional[datetime] = Field(None, description="When approved")
    
    @field_validator('end_date')
    @classmethod
    def validate_end_date(cls, v: date, info) -> date:
        """Validate end date is not before start date."""
        if 'start_date' in info.data and v < info.data['start_date']:
            raise ValueError('End date cannot be before start date')
        return v
    
    @field_validator('reason')
    @classmethod
    def validate_reason(cls, v: Optional[str]) -> Optional[str]:
        """Validate reason with enhanced security."""
        if v is None:
            return v
        return DataValidator.validate_text_field(v, "time_off_reason", max_length=500)


class TutorServiceRate(IdentifiedEntity):
    """Tutor service rates model."""
    
    rate_id: int = Field(..., description="Unique rate identifier")
    tutor_id: int = Field(..., description="Associated tutor")
    subject_area: str = Field(..., description="Subject area")
    service_type: str = Field(..., description="Service type")
    base_rate: Decimal = Field(..., description="Tutor's base hourly rate")
    client_rate: Decimal = Field(..., description="Rate charged to clients")
    currency: str = Field(default="CAD", description="Currency")
    is_active: bool = Field(True, description="Whether rate is active")
    
    @field_validator('subject_area')
    @classmethod
    def validate_subject_area(cls, v: str) -> str:
        """Validate subject area with enhanced security."""
        valid_subjects = ['math', 'science', 'french', 'english', 'history', 'geography', 'physics', 'chemistry', 'biology', 'other']
        return DataValidator.validate_choice_field(v, "subject_area", valid_subjects)
    
    @field_validator('service_type')
    @classmethod
    def validate_service_type(cls, v: str) -> str:
        """Validate service type with enhanced security."""
        valid_types = ['online', 'in_person', 'library', 'hybrid']
        return DataValidator.validate_choice_field(v, "service_type", valid_types)
    
    @field_validator('base_rate', 'client_rate')
    @classmethod
    def validate_rates(cls, v: Decimal) -> Decimal:
        """Validate rates with enhanced security."""
        return DataValidator.validate_numeric_field(v, "rate", min_value=0.01, max_value=1000.0)
    
    @field_validator('client_rate')
    @classmethod
    def validate_client_rate(cls, v: Decimal, info) -> Decimal:
        """Validate client rate is higher than base rate."""
        if 'base_rate' in info.data and v < info.data['base_rate']:
            raise ValueError('Client rate must be higher than or equal to base rate')
        return v


class TutorDocument(IdentifiedEntity):
    """Tutor verification documents model."""
    
    document_id: int = Field(..., description="Unique document identifier")
    tutor_id: int = Field(..., description="Associated tutor")
    document_type: str = Field(..., description="Type of document")
    file_path: str = Field(..., description="Path to uploaded file")
    original_filename: str = Field(..., description="Original filename")
    file_size: int = Field(..., description="File size in bytes")
    mime_type: str = Field(..., description="MIME type of file")
    verification_status: str = Field(default="pending", description="Verification status")
    verified_by: Optional[int] = Field(None, description="Manager who verified")
    verified_at: Optional[datetime] = Field(None, description="When verified")
    notes: Optional[str] = Field(None, description="Verification notes")
    
    @field_validator('document_type')
    @classmethod
    def validate_document_type(cls, v: str) -> str:
        """Validate document type with enhanced security."""
        valid_types = ['photo_id', 'background_check', 'education_certificate', 'reference_letter', 'other']
        return DataValidator.validate_choice_field(v, "document_type", valid_types)
    
    @field_validator('verification_status')
    @classmethod
    def validate_verification_status(cls, v: str) -> str:
        """Validate verification status with enhanced security."""
        valid_statuses = ['pending', 'approved', 'rejected', 'expired']
        return DataValidator.validate_choice_field(v, "verification_status", valid_statuses)
    
    @field_validator('original_filename')
    @classmethod
    def validate_filename(cls, v: str) -> str:
        """Validate filename with enhanced security."""
        v = InputSanitizer.sanitize_text(v)
        # Check for path traversal and malicious patterns
        if '..' in v or '/' in v or '\\' in v:
            raise ValueError("Filename contains invalid path characters")
        return DataValidator.validate_text_field(v, "filename", max_length=255)
    
    @field_validator('notes')
    @classmethod
    def validate_notes(cls, v: Optional[str]) -> Optional[str]:
        """Validate notes with enhanced security."""
        if v is None:
            return v
        return DataValidator.validate_text_field(v, "document_notes", max_length=1000)


class TutorRating(IdentifiedEntity):
    """Tutor rating and review model."""
    
    rating_id: int = Field(..., description="Unique rating identifier")
    tutor_id: int = Field(..., description="Associated tutor")
    client_id: int = Field(..., description="Client who gave rating")
    appointment_id: int = Field(..., description="Associated appointment")
    rating: Decimal = Field(..., description="Rating (1-5)")
    review_text: Optional[str] = Field(None, description="Review text")
    is_public: bool = Field(True, description="Whether review is public")
    
    @field_validator('rating')
    @classmethod
    def validate_rating(cls, v: Decimal) -> Decimal:
        """Validate rating with enhanced security."""
        return DataValidator.validate_numeric_field(v, "rating", min_value=1.0, max_value=5.0)
    
    @field_validator('review_text')
    @classmethod
    def validate_review_text(cls, v: Optional[str]) -> Optional[str]:
        """Validate review text with enhanced security."""
        if v is None:
            return v
        return DataValidator.validate_text_field(v, "review_text", max_length=1000)


class TutorReference(IdentifiedEntity):
    """Tutor reference model - secure storage for reference contacts."""
    
    reference_id: int = Field(..., description="Unique reference identifier")
    tutor_id: int = Field(..., description="Associated tutor")
    reference_name: str = Field(..., description="Reference person's name")
    reference_title: Optional[str] = Field(None, description="Reference person's title")
    reference_organization: Optional[str] = Field(None, description="Reference person's organization")
    reference_email: Optional[str] = Field(None, description="Reference email")
    reference_phone: Optional[str] = Field(None, description="Reference phone")
    relationship: str = Field(..., description="Relationship to tutor")
    years_known: Optional[int] = Field(None, description="Years known")
    verified_date: Optional[date] = Field(None, description="When reference was verified")
    verified_by: Optional[int] = Field(None, description="Manager who verified")
    verification_status: str = Field(default="pending", description="Verification status")
    verification_notes: Optional[str] = Field(None, description="Verification notes")
    is_active: bool = Field(default=True, description="Whether reference is active")
    
    @field_validator('reference_name')
    @classmethod
    def validate_reference_name(cls, v: str) -> str:
        """Validate reference name."""
        return DataValidator.validate_name(v, "reference_name")
    
    @field_validator('reference_email')
    @classmethod
    def validate_reference_email(cls, v: Optional[str]) -> Optional[str]:
        """Validate reference email."""
        if v is None:
            return v
        return DataValidator.validate_email(v)
    
    @field_validator('reference_phone')
    @classmethod
    def validate_reference_phone(cls, v: Optional[str]) -> Optional[str]:
        """Validate reference phone."""
        if v is None:
            return v
        v = InputSanitizer.sanitize_text(v)
        return InputSanitizer.normalize_phone(v)
    
    @field_validator('relationship')
    @classmethod
    def validate_relationship(cls, v: str) -> str:
        """Validate relationship type."""
        valid_relationships = ['supervisor', 'colleague', 'client', 'mentor', 'professor', 'other']
        return DataValidator.validate_choice_field(v, "relationship", valid_relationships)
    
    @field_validator('verification_status')
    @classmethod
    def validate_verification_status(cls, v: str) -> str:
        """Validate verification status."""
        valid_statuses = ['pending', 'contacted', 'verified', 'unable_to_verify']
        return DataValidator.validate_choice_field(v, "verification_status", valid_statuses)


# ============================================
# Create/Update Schemas
# ============================================

class TutorProfileCreate(BaseModel):
    """Schema for creating tutor profile."""
    
    model_config = ConfigDict(from_attributes=True)
    
    user_id: int = Field(..., description="Associated user account")
    first_name: str = Field(..., min_length=1, description="Tutor first name")
    last_name: str = Field(..., min_length=1, description="Tutor last name")
    phone: str = Field(..., description="Tutor phone number")
    bio: Optional[str] = Field(None, description="Tutor biography")
    specializations: List[str] = Field(default_factory=list, description="Subject specializations")
    postal_code: str = Field(..., description="Tutor postal code")
    
    # Education fields
    highest_degree_level: Optional[str] = Field(None, description="Degree level")
    highest_degree_name: Optional[str] = Field(None, description="Full degree name")
    degree_major: Optional[str] = Field(None, description="Major field of study")
    university_name: Optional[str] = Field(None, description="University name")
    graduation_year: Optional[int] = Field(None, description="Graduation year")
    teaching_certifications: Optional[List[str]] = Field(default_factory=list, description="Teaching certifications")
    
    # Experience fields
    years_of_experience: Optional[int] = Field(0, description="Years of experience")
    years_online_teaching: Optional[int] = Field(0, description="Years online teaching")
    years_tutoring: Optional[int] = Field(0, description="Years tutoring")
    current_occupation: Optional[str] = Field(None, description="Current occupation")
    
    # Professional background
    teaching_languages: Optional[List[str]] = Field(default_factory=list, description="Teaching languages")
    teaching_methodology: Optional[List[str]] = Field(default_factory=list, description="Teaching methodologies")
    special_needs_experience: Optional[bool] = Field(False, description="Special needs experience")
    age_groups_experience: Optional[List[str]] = Field(default_factory=list, description="Age groups")


class TutorProfileUpdate(BaseModel):
    """Schema for updating tutor profile."""
    
    model_config = ConfigDict(from_attributes=True)
    
    first_name: Optional[str] = Field(None, min_length=1, description="Tutor first name")
    last_name: Optional[str] = Field(None, min_length=1, description="Tutor last name")
    phone: Optional[str] = Field(None, description="Tutor phone number")
    bio: Optional[str] = Field(None, description="Tutor biography")
    specializations: Optional[List[str]] = Field(None, description="Subject specializations")
    postal_code: Optional[str] = Field(None, description="Tutor postal code")
    is_available: Optional[bool] = Field(None, description="Availability status")
    
    # Education fields
    highest_degree_level: Optional[str] = Field(None, description="Degree level")
    highest_degree_name: Optional[str] = Field(None, description="Full degree name")
    degree_major: Optional[str] = Field(None, description="Major field of study")
    university_name: Optional[str] = Field(None, description="University name")
    graduation_year: Optional[int] = Field(None, description="Graduation year")
    gpa: Optional[Decimal] = Field(None, description="GPA")
    additional_education: Optional[List[Dict[str, Any]]] = Field(None, description="Additional education")
    teaching_certifications: Optional[List[str]] = Field(None, description="Teaching certifications")
    certification_details: Optional[Dict[str, Any]] = Field(None, description="Certification details")
    
    # Experience fields
    years_of_experience: Optional[int] = Field(None, description="Years of experience")
    years_online_teaching: Optional[int] = Field(None, description="Years online teaching")
    years_tutoring: Optional[int] = Field(None, description="Years tutoring")
    students_taught_total: Optional[int] = Field(None, description="Total students taught")
    current_occupation: Optional[str] = Field(None, description="Current occupation")
    previous_teaching_positions: Optional[List[Dict[str, Any]]] = Field(None, description="Previous positions")
    subject_expertise: Optional[Dict[str, str]] = Field(None, description="Subject expertise")
    success_stories: Optional[str] = Field(None, description="Success stories")
    
    # Professional background
    teaching_languages: Optional[List[str]] = Field(None, description="Teaching languages")
    language_proficiency: Optional[Dict[str, str]] = Field(None, description="Language proficiency")
    teaching_methodology: Optional[List[str]] = Field(None, description="Teaching methodologies")
    special_needs_experience: Optional[bool] = Field(None, description="Special needs experience")
    age_groups_experience: Optional[List[str]] = Field(None, description="Age groups")
    last_training_date: Optional[date] = Field(None, description="Last training date")
    professional_memberships: Optional[List[str]] = Field(None, description="Professional memberships")
    
    # Portfolio and references
    portfolio_url: Optional[str] = Field(None, description="Portfolio URL")
    sample_lesson_urls: Optional[List[str]] = Field(None, description="Sample lesson URLs")
    achievement_highlights: Optional[str] = Field(None, description="Achievement highlights")
    
    # Background verification (usually updated by managers only)
    background_check_date: Optional[date] = Field(None, description="Background check date")
    background_check_provider: Optional[str] = Field(None, description="Background check provider")
    background_check_status: Optional[str] = Field(None, description="Background check status")
    working_with_children_clearance: Optional[bool] = Field(None, description="Children clearance")
    police_check_expiry: Optional[date] = Field(None, description="Police check expiry")
    has_liability_insurance: Optional[bool] = Field(None, description="Has insurance")
    insurance_provider: Optional[str] = Field(None, description="Insurance provider")
    insurance_expiry: Optional[date] = Field(None, description="Insurance expiry")
    work_permit_status: Optional[str] = Field(None, description="Work permit status")
    work_permit_expiry: Optional[date] = Field(None, description="Work permit expiry")


class AvailabilityCreate(BaseModel):
    """Schema for creating availability."""
    
    model_config = ConfigDict(from_attributes=True)
    
    tutor_id: int = Field(..., description="Associated tutor")
    day_of_week: int = Field(..., ge=1, le=7, description="Day of week")
    start_time: time = Field(..., description="Start time")
    end_time: time = Field(..., description="End time")


class AvailabilityUpdate(BaseModel):
    """Schema for updating availability."""
    
    model_config = ConfigDict(from_attributes=True)
    
    start_time: Optional[time] = Field(None, description="Start time")
    end_time: Optional[time] = Field(None, description="End time")
    is_active: Optional[bool] = Field(None, description="Active status")


class TimeOffCreate(BaseModel):
    """Schema for creating time-off request."""
    
    model_config = ConfigDict(from_attributes=True)
    
    tutor_id: int = Field(..., description="Associated tutor")
    start_date: date = Field(..., description="Start date")
    end_date: date = Field(..., description="End date")
    reason: Optional[str] = Field(None, description="Reason for time off")


class TimeOffUpdate(BaseModel):
    """Schema for updating time-off request."""
    
    model_config = ConfigDict(from_attributes=True)
    
    start_date: Optional[date] = Field(None, description="Start date")
    end_date: Optional[date] = Field(None, description="End date")
    reason: Optional[str] = Field(None, description="Reason for time off")
    is_approved: Optional[bool] = Field(None, description="Approval status")


class ServiceRateCreate(BaseModel):
    """Schema for creating service rate."""
    
    model_config = ConfigDict(from_attributes=True)
    
    tutor_id: int = Field(..., description="Associated tutor")
    subject_area: str = Field(..., description="Subject area")
    service_type: str = Field(..., description="Service type")
    base_rate: Decimal = Field(..., gt=0, description="Base hourly rate")
    client_rate: Decimal = Field(..., gt=0, description="Client hourly rate")


class ServiceRateUpdate(BaseModel):
    """Schema for updating service rate."""
    
    model_config = ConfigDict(from_attributes=True)
    
    base_rate: Optional[Decimal] = Field(None, gt=0, description="Base hourly rate")
    client_rate: Optional[Decimal] = Field(None, gt=0, description="Client hourly rate")
    is_active: Optional[bool] = Field(None, description="Active status")


class DocumentUpload(BaseModel):
    """Schema for document upload."""
    
    model_config = ConfigDict(from_attributes=True)
    
    tutor_id: int = Field(..., description="Associated tutor")
    document_type: str = Field(..., description="Document type")
    original_filename: str = Field(..., description="Original filename")


class RatingCreate(BaseModel):
    """Schema for creating rating."""
    
    model_config = ConfigDict(from_attributes=True)
    
    tutor_id: int = Field(..., description="Associated tutor")
    client_id: int = Field(..., description="Client giving rating")
    appointment_id: int = Field(..., description="Associated appointment")
    rating: Decimal = Field(..., ge=1, le=5, description="Rating (1-5)")
    review_text: Optional[str] = Field(None, description="Review text")
    is_public: Optional[bool] = Field(True, description="Whether review is public")


class TutorReferenceCreate(BaseModel):
    """Schema for creating tutor reference."""
    
    model_config = ConfigDict(from_attributes=True)
    
    tutor_id: int = Field(..., description="Associated tutor")
    reference_name: str = Field(..., min_length=1, description="Reference name")
    reference_title: Optional[str] = Field(None, description="Reference title")
    reference_organization: Optional[str] = Field(None, description="Reference organization")
    reference_email: Optional[str] = Field(None, description="Reference email")
    reference_phone: Optional[str] = Field(None, description="Reference phone")
    relationship: str = Field(..., description="Relationship type")
    years_known: Optional[int] = Field(None, ge=1, description="Years known")


class TutorReferenceUpdate(BaseModel):
    """Schema for updating tutor reference."""
    
    model_config = ConfigDict(from_attributes=True)
    
    reference_name: Optional[str] = Field(None, min_length=1, description="Reference name")
    reference_title: Optional[str] = Field(None, description="Reference title")
    reference_organization: Optional[str] = Field(None, description="Reference organization")
    reference_email: Optional[str] = Field(None, description="Reference email")
    reference_phone: Optional[str] = Field(None, description="Reference phone")
    relationship: Optional[str] = Field(None, description="Relationship type")
    years_known: Optional[int] = Field(None, ge=1, description="Years known")
    verification_status: Optional[str] = Field(None, description="Verification status")
    verification_notes: Optional[str] = Field(None, description="Verification notes")
    is_active: Optional[bool] = Field(None, description="Active status")


# ============================================
# Response Schemas
# ============================================

class TutorProfileResponse(BaseModel):
    """Tutor profile response schema."""
    
    model_config = ConfigDict(from_attributes=True)
    
    tutor_id: int
    user_id: int
    first_name: str
    last_name: str
    phone: str
    bio: Optional[str]
    specializations: List[str]
    postal_code: str
    latitude: Optional[Decimal]
    longitude: Optional[Decimal]
    verification_status: str
    is_available: bool
    rating: Optional[Decimal]
    total_sessions: int
    distance_km: Optional[float]  # For search results
    created_at: datetime
    updated_at: datetime
    
    # Education fields
    highest_degree_level: Optional[str]
    highest_degree_name: Optional[str]
    degree_major: Optional[str]
    university_name: Optional[str]
    graduation_year: Optional[int]
    gpa: Optional[Decimal]
    additional_education: List[Dict[str, Any]]
    teaching_certifications: List[str]
    certification_details: Dict[str, Any]
    
    # Experience fields
    years_of_experience: int
    years_online_teaching: int
    years_tutoring: int
    students_taught_total: int
    current_occupation: Optional[str]
    previous_teaching_positions: List[Dict[str, Any]]
    subject_expertise: Dict[str, str]
    success_stories: Optional[str]
    
    # Professional background
    teaching_languages: List[str]
    language_proficiency: Dict[str, str]
    teaching_methodology: List[str]
    special_needs_experience: bool
    age_groups_experience: List[str]
    last_training_date: Optional[date]
    professional_memberships: List[str]
    
    # Portfolio and references
    portfolio_url: Optional[str]
    sample_lesson_urls: List[str]
    achievement_highlights: Optional[str]
    has_references: bool
    reference_check_completed: bool
    reference_summary: Optional[str]
    
    # Background verification
    background_check_date: Optional[date]
    background_check_provider: Optional[str]
    background_check_status: Optional[str]
    working_with_children_clearance: bool
    police_check_expiry: Optional[date]
    has_liability_insurance: bool
    insurance_provider: Optional[str]
    insurance_expiry: Optional[date]
    work_permit_status: Optional[str]
    work_permit_expiry: Optional[date]


class AvailabilityResponse(BaseModel):
    """Availability response schema."""
    
    model_config = ConfigDict(from_attributes=True)
    
    availability_id: int
    tutor_id: int
    day_of_week: int
    day_name: str
    start_time: time
    end_time: time
    is_active: bool
    created_at: datetime
    updated_at: datetime


class TimeOffResponse(BaseModel):
    """Time-off response schema."""
    
    model_config = ConfigDict(from_attributes=True)
    
    time_off_id: int
    tutor_id: int
    start_date: date
    end_date: date
    reason: Optional[str]
    is_approved: Optional[bool]
    approved_by: Optional[int]
    approved_at: Optional[datetime]
    days_count: int
    created_at: datetime
    updated_at: datetime


class ServiceRateResponse(BaseModel):
    """Service rate response schema."""
    
    model_config = ConfigDict(from_attributes=True)
    
    rate_id: int
    tutor_id: int
    subject_area: str
    service_type: str
    base_rate: Decimal
    client_rate: Decimal
    currency: str
    platform_margin: Decimal  # client_rate - base_rate
    is_active: bool
    created_at: datetime
    updated_at: datetime


class DocumentResponse(BaseModel):
    """Document response schema."""
    
    model_config = ConfigDict(from_attributes=True)
    
    document_id: int
    tutor_id: int
    document_type: str
    original_filename: str
    file_size: int
    verification_status: str
    verified_by: Optional[int]
    verified_at: Optional[datetime]
    notes: Optional[str]
    created_at: datetime
    updated_at: datetime


class RatingResponse(BaseModel):
    """Rating response schema."""
    
    model_config = ConfigDict(from_attributes=True)
    
    rating_id: int
    tutor_id: int
    client_id: int
    appointment_id: int
    rating: Decimal
    review_text: Optional[str]
    is_public: bool
    client_name: Optional[str]  # For display
    created_at: datetime


class TutorReferenceResponse(BaseModel):
    """Tutor reference response schema."""
    
    model_config = ConfigDict(from_attributes=True)
    
    reference_id: int
    tutor_id: int
    reference_name: str
    reference_title: Optional[str]
    reference_organization: Optional[str]
    reference_email: Optional[str]  # Only shown to managers
    reference_phone: Optional[str]  # Only shown to managers
    relationship: str
    years_known: Optional[int]
    verified_date: Optional[date]
    verified_by: Optional[int]
    verification_status: str
    verification_notes: Optional[str]  # Only shown to managers
    is_active: bool
    created_at: datetime
    updated_at: datetime


class TutorSearchFilters(SearchFilters):
    """Tutor-specific search filters."""
    
    name: Optional[str] = Field(None, description="Search by name")
    specialization: Optional[str] = Field(None, description="Filter by specialization")
    postal_code: Optional[str] = Field(None, description="Search by postal code")
    verification_status: Optional[str] = Field(None, description="Filter by verification status")
    is_available: Optional[bool] = Field(None, description="Filter by availability")
    min_rating: Optional[Decimal] = Field(None, description="Minimum rating filter")
    max_distance_km: Optional[float] = Field(None, description="Maximum distance for geolocation search")
    latitude: Optional[float] = Field(None, description="Client latitude for distance search")
    longitude: Optional[float] = Field(None, description="Client longitude for distance search")
    
    # Education filters
    degree_level: Optional[str] = Field(None, description="Filter by degree level")
    university: Optional[str] = Field(None, description="Filter by university")
    certification: Optional[str] = Field(None, description="Filter by certification")
    
    # Experience filters
    min_experience_years: Optional[int] = Field(None, description="Minimum years of experience")
    teaching_language: Optional[str] = Field(None, description="Filter by teaching language")
    special_needs: Optional[bool] = Field(None, description="Filter by special needs experience")
    age_group: Optional[str] = Field(None, description="Filter by age group experience")
    
    # Verification filters
    background_check: Optional[bool] = Field(None, description="Filter by background check status")
    has_insurance: Optional[bool] = Field(None, description="Filter by insurance status")