-- Migration: Add comprehensive consent management system
-- Description: Create tables for GDPR-compliant consent tracking with document versioning

-- Create consent document types
CREATE TYPE consent_level_type AS ENUM ('level_1_mandatory', 'level_2_optional');
CREATE TYPE consent_status_type AS ENUM ('granted', 'withdrawn', 'expired');
CREATE TYPE consent_category_type AS ENUM ('legal', 'marketing', 'analytics', 'functional');

-- Create consent_documents table for storing consent document versions
CREATE TABLE IF NOT EXISTS consent_documents (
    document_id SERIAL PRIMARY KEY,
    consent_type VARCHAR(100) NOT NULL,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    version VARCHAR(20) NOT NULL,
    level consent_level_type NOT NULL,
    category consent_category_type NOT NULL,
    language VARCHAR(5) NOT NULL DEFAULT 'en',
    is_active BOOLEAN NOT NULL DEFAULT true,
    effective_date TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT current_timestamp_est(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT current_timestamp_est(),
    deleted_at TIMESTAMP WITH TIME ZONE NULL,
    
    -- Ensure unique active version per consent type and language
    CONSTRAINT unique_active_consent_version UNIQUE (consent_type, language, version) DEFERRABLE INITIALLY DEFERRED
);

-- Create user_consents table for tracking user consent status
CREATE TABLE IF NOT EXISTS user_consents (
    consent_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    document_id INTEGER NOT NULL REFERENCES consent_documents(document_id) ON DELETE CASCADE,
    status consent_status_type NOT NULL DEFAULT 'granted',
    granted_at TIMESTAMP WITH TIME ZONE NULL,
    withdrawn_at TIMESTAMP WITH TIME ZONE NULL,
    expires_at TIMESTAMP WITH TIME ZONE NULL,
    ip_address INET NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT current_timestamp_est(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT current_timestamp_est(),
    deleted_at TIMESTAMP WITH TIME ZONE NULL
);

-- Create consent_history table for audit trail
CREATE TABLE IF NOT EXISTS consent_history (
    history_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    consent_type VARCHAR(100) NOT NULL,
    document_id INTEGER NOT NULL REFERENCES consent_documents(document_id) ON DELETE CASCADE,
    action VARCHAR(50) NOT NULL, -- 'granted', 'withdrawn', 'expired'
    previous_status consent_status_type NULL,
    new_status consent_status_type NOT NULL,
    document_version VARCHAR(20) NOT NULL,
    ip_address INET NULL,
    user_agent TEXT NULL,
    reason TEXT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT current_timestamp_est()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_consent_documents_type_lang 
    ON consent_documents(consent_type, language);
CREATE INDEX IF NOT EXISTS idx_consent_documents_active 
    ON consent_documents(is_active, effective_date);
CREATE INDEX IF NOT EXISTS idx_consent_documents_level 
    ON consent_documents(level);
CREATE INDEX IF NOT EXISTS idx_consent_documents_category 
    ON consent_documents(category);

CREATE INDEX IF NOT EXISTS idx_user_consents_user_id 
    ON user_consents(user_id);
CREATE INDEX IF NOT EXISTS idx_user_consents_document_id 
    ON user_consents(document_id);
CREATE INDEX IF NOT EXISTS idx_user_consents_status 
    ON user_consents(status);
CREATE INDEX IF NOT EXISTS idx_user_consents_granted_at 
    ON user_consents(granted_at);
CREATE INDEX IF NOT EXISTS idx_user_consents_expires_at 
    ON user_consents(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_consents_user_document 
    ON user_consents(user_id, document_id);

CREATE INDEX IF NOT EXISTS idx_consent_history_user_id 
    ON consent_history(user_id);
CREATE INDEX IF NOT EXISTS idx_consent_history_consent_type 
    ON consent_history(consent_type);
CREATE INDEX IF NOT EXISTS idx_consent_history_created_at 
    ON consent_history(created_at);
CREATE INDEX IF NOT EXISTS idx_consent_history_user_type 
    ON consent_history(user_id, consent_type);

-- Create trigger for updated_at on consent_documents
CREATE OR REPLACE FUNCTION update_consent_documents_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = current_timestamp_est();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_consent_documents_updated_at
    BEFORE UPDATE ON consent_documents
    FOR EACH ROW
    EXECUTE FUNCTION update_consent_documents_updated_at();

-- Create trigger for updated_at on user_consents
CREATE OR REPLACE FUNCTION update_user_consents_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = current_timestamp_est();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_user_consents_updated_at
    BEFORE UPDATE ON user_consents
    FOR EACH ROW
    EXECUTE FUNCTION update_user_consents_updated_at();

-- Create trigger to automatically populate granted_at/withdrawn_at timestamps
CREATE OR REPLACE FUNCTION update_consent_timestamps()
RETURNS TRIGGER AS $$
BEGIN
    -- Set granted_at when status changes to granted
    IF NEW.status = 'granted' AND (OLD.status IS NULL OR OLD.status != 'granted') THEN
        NEW.granted_at = current_timestamp_est();
        NEW.withdrawn_at = NULL;
    END IF;
    
    -- Set withdrawn_at when status changes to withdrawn
    IF NEW.status = 'withdrawn' AND (OLD.status IS NULL OR OLD.status != 'withdrawn') THEN
        NEW.withdrawn_at = current_timestamp_est();
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_user_consents_timestamps
    BEFORE INSERT OR UPDATE ON user_consents
    FOR EACH ROW
    EXECUTE FUNCTION update_consent_timestamps();

-- Create trigger to log consent changes to history table
-- Note: This trigger is created after all tables are in place
CREATE OR REPLACE FUNCTION log_consent_history()
RETURNS TRIGGER AS $$
DECLARE
    doc_record RECORD;
BEGIN
    -- Get document information
    SELECT consent_type, version INTO doc_record
    FROM consent_documents 
    WHERE document_id = NEW.document_id;
    
    -- Log the consent change
    INSERT INTO consent_history (
        user_id, consent_type, document_id, action, 
        previous_status, new_status, document_version,
        ip_address, user_agent
    ) VALUES (
        NEW.user_id,
        doc_record.consent_type,
        NEW.document_id,
        NEW.status::text,
        CASE WHEN TG_OP = 'UPDATE' THEN OLD.status ELSE NULL END,
        NEW.status,
        doc_record.version,
        NEW.ip_address,
        NEW.user_agent
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Insert default consent documents for TutorAide
INSERT INTO consent_documents (
    consent_type, title, content, version, level, category, language, effective_date
) VALUES 
-- Terms of Service (Level 1 Mandatory)
(
    'terms_of_service',
    'Terms of Service',
    'By using TutorAide services, you agree to these terms and conditions. You must comply with all applicable laws and regulations when using our platform. We reserve the right to terminate accounts that violate these terms.',
    '1.0',
    'level_1_mandatory',
    'legal',
    'en',
    current_timestamp_est()
),
(
    'terms_of_service',
    'Conditions d''utilisation',
    'En utilisant les services TutorAide, vous acceptez ces termes et conditions. Vous devez respecter toutes les lois et réglementations applicables lors de l''utilisation de notre plateforme. Nous nous réservons le droit de résilier les comptes qui violent ces conditions.',
    '1.0',
    'level_1_mandatory',
    'legal',
    'fr',
    current_timestamp_est()
),

-- Privacy Policy (Level 1 Mandatory)
(
    'privacy_policy',
    'Privacy Policy',
    'We collect and process your personal data in accordance with applicable privacy laws. Your data is used to provide tutoring services, process payments, and improve our platform. We do not sell your personal information to third parties.',
    '1.0',
    'level_1_mandatory',
    'legal',
    'en',
    current_timestamp_est()
),
(
    'privacy_policy',
    'Politique de confidentialité',
    'Nous collectons et traitons vos données personnelles conformément aux lois applicables sur la confidentialité. Vos données sont utilisées pour fournir des services de tutorat, traiter les paiements et améliorer notre plateforme. Nous ne vendons pas vos informations personnelles à des tiers.',
    '1.0',
    'level_1_mandatory',
    'legal',
    'fr',
    current_timestamp_est()
),

-- Marketing Emails (Level 2 Optional)
(
    'marketing_emails',
    'Marketing Communications',
    'Receive email updates about new tutors, special offers, educational tips, and platform improvements. You can unsubscribe at any time by clicking the unsubscribe link in our emails or updating your preferences.',
    '1.0',
    'level_2_optional',
    'marketing',
    'en',
    current_timestamp_est()
),
(
    'marketing_emails',
    'Communications marketing',
    'Recevez des mises à jour par courriel sur les nouveaux tuteurs, les offres spéciales, les conseils éducatifs et les améliorations de la plateforme. Vous pouvez vous désabonner à tout moment en cliquant sur le lien de désabonnement dans nos courriels ou en mettant à jour vos préférences.',
    '1.0',
    'level_2_optional',
    'marketing',
    'fr',
    current_timestamp_est()
),

-- Usage Analytics (Level 2 Optional)
(
    'usage_analytics',
    'Usage Analytics',
    'Allow us to collect anonymous usage data to improve our platform. This includes page views, feature usage, and performance metrics. No personally identifiable information is collected for analytics purposes.',
    '1.0',
    'level_2_optional',
    'analytics',
    'en',
    current_timestamp_est()
),
(
    'usage_analytics',
    'Analytiques d''utilisation',
    'Permettez-nous de collecter des données d''utilisation anonymes pour améliorer notre plateforme. Cela inclut les pages vues, l''utilisation des fonctionnalités et les métriques de performance. Aucune information personnellement identifiable n''est collectée à des fins analytiques.',
    '1.0',
    'level_2_optional',
    'analytics',
    'fr',
    current_timestamp_est()
);

-- Now create the history trigger after all tables and data are in place
CREATE TRIGGER trigger_user_consents_history
    AFTER INSERT OR UPDATE ON user_consents
    FOR EACH ROW
    EXECUTE FUNCTION log_consent_history();