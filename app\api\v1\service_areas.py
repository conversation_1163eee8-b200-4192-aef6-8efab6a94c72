"""
Service Area Management API endpoints for tutor coverage zone management.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel, Field, field_validator
import logging

from app.services.service_area_service import ServiceAreaService, get_service_area_service
from app.core.dependencies import get_current_user
from app.core.exceptions import ValidationError, ResourceNotFoundError, BusinessLogicError

logger = logging.getLogger(__name__)

router = APIRouter()

# Request/Response Models
class ServiceAreaCreateRequest(BaseModel):
    """Request model for creating a service area."""
    center_postal_code: str = Field(..., description="Center postal code")
    radius_km: float = Field(..., ge=1, le=100, description="Radius in kilometers")
    service_types: List[str] = Field(..., description="Service types offered")
    
    @field_validator('center_postal_code')
    def validate_postal_code(cls, v):
        if not v or len(v.strip()) < 6:
            raise ValueError('Postal code must be at least 6 characters')
        return v.strip().upper()
    
    @field_validator('service_types')
    def validate_service_types(cls, v):
        valid_types = {'online', 'in_person', 'library', 'hybrid'}
        if not v:
            raise ValueError('At least one service type is required')
        for service_type in v:
            if service_type not in valid_types:
                raise ValueError(f'Invalid service type: {service_type}. Must be one of: {valid_types}')
        return v

class ServiceAreaUpdateRequest(BaseModel):
    """Request model for updating a service area."""
    center_postal_code: Optional[str] = Field(None, description="New center postal code")
    radius_km: Optional[float] = Field(None, ge=1, le=100, description="New radius in kilometers")
    service_types: Optional[List[str]] = Field(None, description="New service types")
    is_active: Optional[bool] = Field(None, description="Active status")
    
    @field_validator('center_postal_code')
    def validate_postal_code(cls, v):
        if v and len(v.strip()) < 6:
            raise ValueError('Postal code must be at least 6 characters')
        return v.strip().upper() if v else v
    
    @field_validator('service_types')
    def validate_service_types(cls, v):
        if v is not None:
            valid_types = {'online', 'in_person', 'library', 'hybrid'}
            for service_type in v:
                if service_type not in valid_types:
                    raise ValueError(f'Invalid service type: {service_type}. Must be one of: {valid_types}')
        return v

class ServiceAreaResponse(BaseModel):
    """Response model for service area data."""
    tutor_id: int
    center_postal_code: str
    radius_km: float
    covered_postal_codes: List[str]
    service_types: List[str]
    is_active: bool
    auto_expand: bool = False
    max_travel_time_minutes: Optional[int] = None

class ServiceAreaAnalysisResponse(BaseModel):
    """Response model for service area analysis."""
    total_postal_codes: int
    estimated_population: int
    competition_level: str
    demand_score: float
    coverage_gaps: List[str]
    optimization_suggestions: List[str]

class PostalCodeCoverageResponse(BaseModel):
    """Response model for postal code coverage check."""
    postal_code: str
    is_covered: bool
    covering_tutors: List[dict]

# API Endpoints
@router.post("/create", response_model=ServiceAreaResponse)
async def create_service_area(
    request: ServiceAreaCreateRequest,
    service_area_service: ServiceAreaService = Depends(get_service_area_service),
    current_user = Depends(get_current_user)
):
    """
    Create a new service area for a tutor.
    
    Creates a service area centered on a postal code with a specified radius.
    Automatically calculates all postal codes within the radius and stores
    them for efficient lookup.
    """
    try:
        # Get tutor ID from current user
        # This assumes the current user is a tutor - you might need to adjust based on your auth system
        tutor_id = current_user.get('tutor_id')
        if not tutor_id:
            raise HTTPException(status_code=400, detail="User is not associated with a tutor profile")
        
        service_area = await service_area_service.create_service_area(
            tutor_id=tutor_id,
            center_postal_code=request.center_postal_code,
            radius_km=request.radius_km,
            service_types=request.service_types
        )
        
        return ServiceAreaResponse(
            tutor_id=service_area.tutor_id,
            center_postal_code=service_area.center_postal_code,
            radius_km=service_area.radius_km,
            covered_postal_codes=service_area.covered_postal_codes,
            service_types=service_area.service_types,
            is_active=service_area.is_active,
            auto_expand=service_area.auto_expand,
            max_travel_time_minutes=service_area.max_travel_time_minutes
        )
        
    except ValidationError as e:
        logger.warning(f"Validation error creating service area: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except BusinessLogicError as e:
        logger.warning(f"Business logic error creating service area: {str(e)}")
        raise HTTPException(status_code=422, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating service area: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.put("/update", response_model=ServiceAreaResponse)
async def update_service_area(
    request: ServiceAreaUpdateRequest,
    service_area_service: ServiceAreaService = Depends(get_service_area_service),
    current_user = Depends(get_current_user)
):
    """
    Update an existing service area.
    
    Allows updating center postal code, radius, service types, and active status.
    If center or radius changes, the covered postal codes are recalculated.
    """
    try:
        tutor_id = current_user.get('tutor_id')
        if not tutor_id:
            raise HTTPException(status_code=400, detail="User is not associated with a tutor profile")
        
        service_area = await service_area_service.update_service_area(
            tutor_id=tutor_id,
            center_postal_code=request.center_postal_code,
            radius_km=request.radius_km,
            service_types=request.service_types,
            is_active=request.is_active
        )
        
        return ServiceAreaResponse(
            tutor_id=service_area.tutor_id,
            center_postal_code=service_area.center_postal_code,
            radius_km=service_area.radius_km,
            covered_postal_codes=service_area.covered_postal_codes,
            service_types=service_area.service_types,
            is_active=service_area.is_active,
            auto_expand=service_area.auto_expand,
            max_travel_time_minutes=service_area.max_travel_time_minutes
        )
        
    except ValidationError as e:
        logger.warning(f"Validation error updating service area: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except ResourceNotFoundError as e:
        logger.warning(f"Service area not found: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error updating service area: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/my-area", response_model=ServiceAreaResponse)
async def get_my_service_area(
    service_area_service: ServiceAreaService = Depends(get_service_area_service),
    current_user = Depends(get_current_user)
):
    """
    Get the current user's service area.
    
    Returns the service area configuration for the authenticated tutor.
    """
    try:
        tutor_id = current_user.get('tutor_id')
        if not tutor_id:
            raise HTTPException(status_code=400, detail="User is not associated with a tutor profile")
        
        service_area = await service_area_service.get_service_area(tutor_id)
        if not service_area:
            raise HTTPException(status_code=404, detail="No service area configured")
        
        return ServiceAreaResponse(
            tutor_id=service_area.tutor_id,
            center_postal_code=service_area.center_postal_code,
            radius_km=service_area.radius_km,
            covered_postal_codes=service_area.covered_postal_codes,
            service_types=service_area.service_types,
            is_active=service_area.is_active,
            auto_expand=service_area.auto_expand,
            max_travel_time_minutes=service_area.max_travel_time_minutes
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting service area: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/analyze", response_model=ServiceAreaAnalysisResponse)
async def analyze_service_area(
    service_area_service: ServiceAreaService = Depends(get_service_area_service),
    current_user = Depends(get_current_user)
):
    """
    Analyze the current user's service area for optimization opportunities.
    
    Provides insights into coverage, competition, and optimization suggestions.
    """
    try:
        tutor_id = current_user.get('tutor_id')
        if not tutor_id:
            raise HTTPException(status_code=400, detail="User is not associated with a tutor profile")
        
        analysis = await service_area_service.analyze_service_area(tutor_id)
        
        return ServiceAreaAnalysisResponse(
            total_postal_codes=analysis.total_postal_codes,
            estimated_population=analysis.estimated_population,
            competition_level=analysis.competition_level,
            demand_score=analysis.demand_score,
            coverage_gaps=analysis.coverage_gaps,
            optimization_suggestions=analysis.optimization_suggestions
        )
        
    except ResourceNotFoundError as e:
        logger.warning(f"Service area not found for analysis: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error analyzing service area: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/optimize", response_model=ServiceAreaResponse)
async def optimize_service_area(
    service_area_service: ServiceAreaService = Depends(get_service_area_service),
    current_user = Depends(get_current_user)
):
    """
    Automatically optimize the current user's service area.
    
    Applies optimization recommendations to improve coverage and competitiveness.
    """
    try:
        tutor_id = current_user.get('tutor_id')
        if not tutor_id:
            raise HTTPException(status_code=400, detail="User is not associated with a tutor profile")
        
        optimized_area = await service_area_service.optimize_service_area(tutor_id)
        
        return ServiceAreaResponse(
            tutor_id=optimized_area.tutor_id,
            center_postal_code=optimized_area.center_postal_code,
            radius_km=optimized_area.radius_km,
            covered_postal_codes=optimized_area.covered_postal_codes,
            service_types=optimized_area.service_types,
            is_active=optimized_area.is_active,
            auto_expand=optimized_area.auto_expand,
            max_travel_time_minutes=optimized_area.max_travel_time_minutes
        )
        
    except ResourceNotFoundError as e:
        logger.warning(f"Service area not found for optimization: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error optimizing service area: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/check-coverage/{postal_code}", response_model=PostalCodeCoverageResponse)
async def check_postal_code_coverage(
    postal_code: str,
    service_area_service: ServiceAreaService = Depends(get_service_area_service),
    current_user = Depends(get_current_user)
):
    """
    Check which tutors cover a specific postal code.
    
    Returns a list of tutors that have the specified postal code in their service area.
    """
    try:
        covering_tutors = await service_area_service.check_postal_code_coverage(
            postal_code.strip().upper()
        )
        
        return PostalCodeCoverageResponse(
            postal_code=postal_code.strip().upper(),
            is_covered=len(covering_tutors) > 0,
            covering_tutors=covering_tutors
        )
        
    except Exception as e:
        logger.error(f"Error checking postal code coverage: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/coverage-map")
async def get_service_area_coverage_map(
    tutor_id: Optional[int] = Query(None, description="Specific tutor ID (admin only)"),
    service_area_service: ServiceAreaService = Depends(get_service_area_service),
    current_user = Depends(get_current_user)
):
    """
    Get service area coverage data for map visualization.
    
    Returns geospatial data suitable for rendering service areas on a map.
    """
    try:
        # Use provided tutor_id or current user's tutor_id
        target_tutor_id = tutor_id if tutor_id else current_user.get('tutor_id')
        
        if not target_tutor_id:
            raise HTTPException(status_code=400, detail="No tutor ID specified")
        
        # Check permissions - only allow viewing own area unless admin
        if tutor_id and tutor_id != current_user.get('tutor_id'):
            user_role = current_user.get('role', '').lower()
            if user_role != 'manager':
                raise HTTPException(status_code=403, detail="Insufficient permissions")
        
        service_area = await service_area_service.get_service_area(target_tutor_id)
        if not service_area:
            raise HTTPException(status_code=404, detail="No service area found")
        
        # Get center coordinates for map display
        from app.services.geocoding_service import get_geocoding_service
        geocoding_service = await get_geocoding_service()
        center_coords = await geocoding_service.geocode_postal_code(service_area.center_postal_code)
        
        return {
            "tutor_id": service_area.tutor_id,
            "center": {
                "postal_code": service_area.center_postal_code,
                "latitude": center_coords.get('latitude') if center_coords else None,
                "longitude": center_coords.get('longitude') if center_coords else None
            },
            "radius_km": service_area.radius_km,
            "service_types": service_area.service_types,
            "is_active": service_area.is_active,
            "coverage_count": len(service_area.covered_postal_codes)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting coverage map data: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")