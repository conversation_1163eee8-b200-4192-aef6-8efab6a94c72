import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { X, Clock, Users, MapPin, DollarSign, Tag, Edit, Trash2, UserPlus } from 'lucide-react';
import { Modal } from '../common/Modal';
import Button from '../common/Button';
import { Badge } from '../common/Badge';
import { Input } from '../common/Input';
import { serviceService, Service, ServiceRate, TutorService, ServiceType, SubjectArea } from '../../services/serviceService';
import toast from 'react-hot-toast';

interface ServiceDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  service: Service;
  onUpdate: () => void;
  canManage: boolean;
}

const ServiceDetailsModal: React.FC<ServiceDetailsModalProps> = ({
  isOpen,
  onClose,
  service,
  onUpdate,
  canManage
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [rates, setRates] = useState<ServiceRate[]>([]);
  const [tutors, setTutors] = useState<TutorService[]>([]);
  const [activeTab, setActiveTab] = useState<'details' | 'rates' | 'tutors'>('details');
  const [editingRate, setEditingRate] = useState<ServiceRate | null>(null);
  const [rateForm, setRateForm] = useState({
    base_rate: 0,
    client_rate: 0
  });

  useEffect(() => {
    if (isOpen) {
      loadRates();
      loadTutors();
    }
  }, [isOpen, service.service_id]);

  const loadRates = async () => {
    try {
      const ratesData = await serviceService.getServiceRates(service.service_id);
      setRates(ratesData);
    } catch (error) {
      console.error('Error loading rates:', error);
    }
  };

  const loadTutors = async () => {
    try {
      const response = await serviceService.findTutorsForService(service.service_id);
      setTutors(response.items);
    } catch (error) {
      console.error('Error loading tutors:', error);
    }
  };

  const getServiceTypeIcon = (type: ServiceType) => {
    const icons = {
      [ServiceType.ONLINE]: '💻',
      [ServiceType.IN_PERSON]: '🏢',
      [ServiceType.LIBRARY]: '📚',
      [ServiceType.HYBRID]: '🔄'
    };
    return icons[type] || '📍';
  };

  const getServiceTypeLabel = (type: ServiceType) => {
    switch (type) {
      case ServiceType.ONLINE:
        return t('services.types.online');
      case ServiceType.IN_PERSON:
        return t('services.types.inPerson');
      case ServiceType.LIBRARY:
        return t('services.types.library');
      case ServiceType.HYBRID:
        return t('services.types.hybrid');
      default:
        return type;
    }
  };

  const getSubjectAreaLabel = (area: SubjectArea) => {
    switch (area) {
      case SubjectArea.MATH:
        return t('services.subjects.math');
      case SubjectArea.SCIENCE:
        return t('services.subjects.science');
      case SubjectArea.FRENCH:
        return t('services.subjects.french');
      case SubjectArea.ENGLISH:
        return t('services.subjects.english');
      case SubjectArea.OTHERS:
        return t('services.subjects.others');
      default:
        return area;
    }
  };

  const handleEditRate = (rate: ServiceRate) => {
    setEditingRate(rate);
    setRateForm({
      base_rate: rate.base_rate,
      client_rate: rate.client_rate
    });
  };

  const handleSaveRate = async () => {
    if (!editingRate) return;

    setLoading(true);
    try {
      await serviceService.updateServiceRate(service.service_id, editingRate.tutor_id, rateForm);
      toast.success(t('services.rates.updateSuccess'));
      setEditingRate(null);
      loadRates();
    } catch (error) {
      console.error('Error updating rate:', error);
      toast.error(t('services.rates.updateError'));
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteService = async () => {
    if (!confirm(t('services.confirmDelete'))) return;

    setLoading(true);
    try {
      await serviceService.deleteService(service.service_id);
      toast.success(t('services.deleteSuccess'));
      onUpdate();
      onClose();
    } catch (error) {
      console.error('Error deleting service:', error);
      toast.error(t('services.deleteError'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">{service.name}</h2>
            <div className="flex items-center space-x-3 mt-2">
              <Badge variant="info">{getSubjectAreaLabel(service.subject_area)}</Badge>
              <div className="flex items-center text-gray-600">
                <span className="mr-1">{getServiceTypeIcon(service.service_type)}</span>
                <span className="text-sm">{getServiceTypeLabel(service.service_type)}</span>
              </div>
              <Badge variant={service.is_active ? 'success' : 'secondary'}>
                {service.is_active ? t('common.active') : t('common.inactive')}
              </Badge>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 mb-6">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('details')}
              className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'details'
                  ? 'border-accent-red text-accent-red'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              {t('services.tabs.details')}
            </button>
            <button
              onClick={() => setActiveTab('rates')}
              className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'rates'
                  ? 'border-accent-red text-accent-red'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              {t('services.tabs.rates')}
              <Badge variant="secondary" size="sm" className="ml-2">
                {rates.length}
              </Badge>
            </button>
            <button
              onClick={() => setActiveTab('tutors')}
              className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'tutors'
                  ? 'border-accent-red text-accent-red'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              {t('services.tabs.tutors')}
              <Badge variant="secondary" size="sm" className="ml-2">
                {tutors.length}
              </Badge>
            </button>
          </nav>
        </div>

        {/* Content */}
        {activeTab === 'details' && (
          <div className="space-y-6">
            {/* Description */}
            {service.description && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {t('services.description')}
                </h3>
                <p className="text-gray-600">{service.description}</p>
              </div>
            )}

            {/* Service Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  {t('services.details.title')}
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center text-gray-600">
                    <Clock className="w-5 h-5 mr-3" />
                    <span>{t('services.duration')}: {service.duration_options.map(d => `${d}min`).join(', ')}</span>
                  </div>
                  <div className="flex items-center text-gray-600">
                    <Users className="w-5 h-5 mr-3" />
                    <span>
                      {service.min_students === service.max_students
                        ? t('services.students', { count: service.max_students })
                        : t('services.studentsRange', { min: service.min_students, max: service.max_students })}
                    </span>
                  </div>
                  {service.requires_location && (
                    <div className="flex items-center text-gray-600">
                      <MapPin className="w-5 h-5 mr-3" />
                      <span>{t('services.locationRequired')}</span>
                    </div>
                  )}
                  {service.default_client_rate && (
                    <div className="flex items-center text-gray-600">
                      <DollarSign className="w-5 h-5 mr-3" />
                      <span>
                        {t('services.defaultClientRate')}: ${service.default_client_rate}/hr
                      </span>
                    </div>
                  )}
                  {service.default_tutor_rate && (
                    <div className="flex items-center text-gray-600">
                      <DollarSign className="w-5 h-5 mr-3" />
                      <span>
                        {t('services.defaultTutorRate')}: ${service.default_tutor_rate}/hr
                      </span>
                    </div>
                  )}
                </div>
              </div>

              <div>
                {/* Grade Levels */}
                {service.grade_levels && service.grade_levels.length > 0 && (
                  <div className="mb-4">
                    <h4 className="font-medium text-gray-900 mb-2">{t('services.gradeLevels')}</h4>
                    <div className="flex flex-wrap gap-2">
                      {service.grade_levels.map((level, idx) => (
                        <Badge key={idx} variant="secondary" size="sm">{level}</Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Frequency Options */}
                {service.frequency_options && service.frequency_options.length > 0 && (
                  <div className="mb-4">
                    <h4 className="font-medium text-gray-900 mb-2">{t('services.frequencyOptions')}</h4>
                    <div className="flex flex-wrap gap-2">
                      {service.frequency_options.map((freq, idx) => (
                        <Badge key={idx} variant="secondary" size="sm">{freq}</Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Tags */}
                {service.tags && service.tags.length > 0 && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">{t('services.tags')}</h4>
                    <div className="flex flex-wrap gap-2">
                      {service.tags.map((tag, idx) => (
                        <Badge key={idx} variant="info" size="sm">
                          <Tag className="w-3 h-3 mr-1" />
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'rates' && (
          <div>
            {rates.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <DollarSign className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                <p>{t('services.rates.empty')}</p>
              </div>
            ) : (
              <div className="space-y-3">
                {rates.map((rate) => (
                  <div key={rate.rate_id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium text-gray-900">{rate.tutor_name}</div>
                        <div className="text-sm text-gray-600 mt-1">
                          {t('services.rates.baseRate')}: ${rate.base_rate}/hr • 
                          {t('services.rates.clientRate')}: ${rate.client_rate}/hr • 
                          {t('services.rates.platformFee')}: ${rate.platform_fee}
                        </div>
                      </div>
                      {canManage && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditRate(rate)}
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                    
                    {editingRate?.rate_id === rate.rate_id && (
                      <div className="mt-4 p-4 bg-gray-50 rounded-lg space-y-3">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              {t('services.rates.baseRate')}
                            </label>
                            <Input
                              type="number"
                              value={rateForm.base_rate}
                              onChange={(e) => setRateForm(prev => ({ ...prev, base_rate: parseFloat(e.target.value) || 0 }))}
                              min="0"
                              step="0.01"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              {t('services.rates.clientRate')}
                            </label>
                            <Input
                              type="number"
                              value={rateForm.client_rate}
                              onChange={(e) => setRateForm(prev => ({ ...prev, client_rate: parseFloat(e.target.value) || 0 }))}
                              min="0"
                              step="0.01"
                            />
                          </div>
                        </div>
                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setEditingRate(null)}
                          >
                            {t('common.cancel')}
                          </Button>
                          <Button
                            variant="primary"
                            size="sm"
                            onClick={handleSaveRate}
                            loading={loading}
                          >
                            {t('common.save')}
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {activeTab === 'tutors' && (
          <div>
            {tutors.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Users className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                <p>{t('services.tutors.empty')}</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {tutors.map((tutor) => (
                  <div key={tutor.tutor_service_id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div>
                        <div className="font-medium text-gray-900">{tutor.tutor_name}</div>
                        <div className="text-sm text-gray-600 mt-1">
                          ${tutor.hourly_rate}/hr • {tutor.experience_level || t('services.tutors.allLevels')}
                        </div>
                        {tutor.specializations && tutor.specializations.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-2">
                            {tutor.specializations.slice(0, 3).map((spec, idx) => (
                              <Badge key={idx} variant="info" size="sm">{spec}</Badge>
                            ))}
                          </div>
                        )}
                      </div>
                      <Badge variant={tutor.is_available ? 'success' : 'secondary'} size="sm">
                        {tutor.is_available ? t('common.available') : t('common.unavailable')}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Actions */}
        <div className="mt-6 pt-6 border-t flex justify-between">
          <div>
            {canManage && activeTab === 'details' && (
              <Button
                variant="danger"
                onClick={handleDeleteService}
                loading={loading}
              >
                <Trash2 className="w-4 h-4 mr-2" />
                {t('services.delete')}
              </Button>
            )}
          </div>
          <div className="space-x-3">
            {canManage && activeTab === 'details' && (
              <Button variant="secondary">
                <Edit className="w-4 h-4 mr-2" />
                {t('services.edit')}
              </Button>
            )}
            <Button variant="secondary" onClick={onClose}>
              {t('common.close')}
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default ServiceDetailsModal;