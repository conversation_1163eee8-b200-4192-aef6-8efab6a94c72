"""
Client tutor request API endpoints for public form submissions.
Handles requests from the marketing website without authentication.
"""

from typing import Dict, Any, List
from fastapi import APIRouter, HTTPException, Depends, Request
from pydantic import BaseModel, Field, EmailStr, field_validator
from datetime import datetime
import re
import json
from app.config.database import get_db

# Helper function to get database connection
async def get_db_connection():
    """Get database connection."""
    async for db in get_db():
        return db
from app.services.geocoding_service import GeocodingService
from app.core.logging import logger

router = APIRouter()

class ClientTutorRequestCreate(BaseModel):
    """Schema for creating a new client tutor request from website form."""
    
    # Parent/Guardian Information
    parent_first_name: str = Field(..., min_length=1, max_length=100)
    parent_last_name: str = Field(..., min_length=1, max_length=100)
    parent_email: EmailStr
    parent_phone: str = Field(..., min_length=10, max_length=20)
    parent_address: str = Field(..., min_length=1, max_length=255)
    parent_apartment: str = Field(None, max_length=50)
    parent_city: str = Field(..., min_length=1, max_length=100)
    parent_postal_code: str = Field(..., min_length=6, max_length=10)
    parent_province: str = Field(..., min_length=1, max_length=50)
    
    # Student Information
    student_first_name: str = Field(..., min_length=1, max_length=100)
    student_last_name: str = Field(..., min_length=1, max_length=100)
    student_grade: str = Field(..., min_length=1, max_length=50)
    
    # Service Requirements
    subjects: List[str] = Field(..., min_items=1)
    frequency: str = Field(..., min_length=1, max_length=50)
    preferred_schedule: str = Field(None)
    location: str = Field(..., pattern="^(online|in_person|hybrid)$")
    additional_info: str = Field(None)
    
    # Source Tracking (optional)
    referrer_url: str = Field(None)
    utm_source: str = Field(None, max_length=100)
    utm_medium: str = Field(None, max_length=100)
    utm_campaign: str = Field(None, max_length=100)
    
    @field_validator('parent_phone')
    def validate_phone(cls, v):
        """Validate phone number format."""
        # Remove all non-digit characters for validation
        digits_only = re.sub(r'\D', '', v)
        if len(digits_only) < 10 or len(digits_only) > 15:
            raise ValueError('Phone number must contain 10-15 digits')
        return v
    
    @field_validator('parent_postal_code')
    def validate_postal_code(cls, v):
        """Validate Canadian postal code format."""
        # Canadian postal code pattern (with or without space)
        pattern = r'^[A-Za-z]\d[A-Za-z]\s?\d[A-Za-z]\d$'
        if not re.match(pattern, v):
            raise ValueError('Invalid Canadian postal code format')
        # Normalize to uppercase with space
        v = v.upper()
        if len(v) == 6:
            v = f"{v[:3]} {v[3:]}"
        return v
    
    @field_validator('subjects')
    def validate_subjects(cls, v):
        """Validate subjects list."""
        valid_subjects = [
            'mathematics', 'science', 'french', 'english', 
            'physics', 'chemistry', 'biology', 'history', 
            'geography', 'computer_science', 'economics', 
            'accounting', 'other'
        ]
        # Allow flexible matching but normalize to valid values
        normalized = []
        for subject in v:
            subject_lower = subject.lower().strip()
            # Map common variations
            if subject_lower in ['math', 'maths']:
                normalized.append('mathematics')
            elif subject_lower in ['comp sci', 'cs', 'programming']:
                normalized.append('computer_science')
            elif subject_lower in valid_subjects:
                normalized.append(subject_lower)
            else:
                normalized.append('other')
        return list(set(normalized))  # Remove duplicates

    class Config:
        json_schema_extra = {
            "example": {
                "parent_first_name": "John",
                "parent_last_name": "Doe",
                "parent_email": "<EMAIL>",
                "parent_phone": "************",
                "parent_address": "123 Main Street",
                "parent_apartment": "Apt 4B",
                "parent_city": "Montreal",
                "parent_postal_code": "H1A 2B3",
                "parent_province": "Quebec",
                "student_first_name": "Jane",
                "student_last_name": "Doe",
                "student_grade": "Grade 9",
                "subjects": ["mathematics", "science"],
                "frequency": "weekly",
                "preferred_schedule": "Weekday evenings after 5pm",
                "location": "online",
                "additional_info": "Student needs help preparing for final exams"
            }
        }

@router.post("/client-requests", response_model=Dict[str, Any])
async def create_client_request(
    request_data: ClientTutorRequestCreate,
    request: Request
) -> Dict[str, Any]:
    """
    Create a new client tutor request from the marketing website.
    
    This endpoint is public and doesn't require authentication.
    It captures form submissions and stores them for processing by managers.
    """
    conn = await get_db_connection()
    try:
        # Get client IP for tracking
        client_ip = request.client.host if request.client else None
        
        # Geocode the address to get coordinates and region
        geocoding_service = GeocodingService()
        full_address = f"{request_data.parent_address}, {request_data.parent_city}, {request_data.parent_province}, {request_data.parent_postal_code}"
        
        latitude = None
        longitude = None
        region = None
        
        try:
            geo_result = await geocoding_service.geocode_address(full_address)
            if geo_result and geo_result.get('success'):
                latitude = geo_result.get('latitude')
                longitude = geo_result.get('longitude')
                # Determine region based on city and coordinates
                region = _determine_region(request_data.parent_city, latitude, longitude)
        except Exception as e:
            logger.warning(f"Geocoding failed for address {full_address}: {e}")
            # Continue without coordinates - not critical for form submission
        
        # Insert the request into the database
        query = """
            INSERT INTO client_tutor_requests (
                parent_first_name, parent_last_name, parent_email, parent_phone,
                parent_address, parent_apartment, parent_city, parent_postal_code, parent_province,
                latitude, longitude, region,
                student_first_name, student_last_name, student_grade,
                subjects, frequency, preferred_schedule, location, additional_info,
                source, referrer_url, utm_source, utm_medium, utm_campaign,
                created_at, updated_at
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12,
                $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25,
                CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
            )
            RETURNING request_id, created_at
        """
        
        result = await conn.fetchone(
            query,
            request_data.parent_first_name,
            request_data.parent_last_name,
            request_data.parent_email,
            request_data.parent_phone,
            request_data.parent_address,
            request_data.parent_apartment,
            request_data.parent_city,
            request_data.parent_postal_code,
            request_data.parent_province,
            latitude,
            longitude,
            region,
            request_data.student_first_name,
            request_data.student_last_name,
            request_data.student_grade,
            request_data.subjects,  # PostgreSQL array
            request_data.frequency,
            request_data.preferred_schedule,
            request_data.location,
            request_data.additional_info,
            'website_form',  # source
            request_data.referrer_url,
            request_data.utm_source,
            request_data.utm_medium,
            request_data.utm_campaign
        )
        
        request_id = result['request_id']
        created_at = result['created_at']
        
        # Log the successful submission
        logger.info(
            f"New client tutor request created: {request_id}",
            extra={
                "request_id": request_id,
                "email": request_data.parent_email,
                "city": request_data.parent_city,
                "region": region,
                "subjects": request_data.subjects,
                "client_ip": client_ip
            }
        )
        
        # TODO: Send notification to managers about new request
        # TODO: Send confirmation email to parent
        
        return {
            "success": True,
            "message": "Your tutor request has been submitted successfully. Our team will contact you within 24-48 hours.",
            "request_id": str(request_id),
            "submitted_at": created_at.isoformat()
        }
        
    except Exception as e:
        logger.error(
            f"Error creating client tutor request: {str(e)}",
            extra={"error": str(e), "request_data": request_data.dict()}
        )
        raise HTTPException(
            status_code=500,
            detail="An error occurred while submitting your request. Please try again <NAME_EMAIL>"
        )
    finally:
        await conn.close()

def _determine_region(city: str, latitude: float = None, longitude: float = None) -> str:
    """
    Determine the region based on city and coordinates.
    This is a simplified implementation - should be enhanced with proper regional boundaries.
    """
    city_lower = city.lower()
    
    # Montreal regions
    if 'montreal' in city_lower or 'montréal' in city_lower:
        if latitude and longitude:
            # Simplified regional division based on coordinates
            if longitude < -73.7:
                return "Montreal-West"
            elif longitude > -73.5:
                return "Montreal-East"
            elif latitude > 45.55:
                return "Montreal-North"
            else:
                return "Montreal-South"
        return "Montreal"
    
    # Other major cities
    elif 'laval' in city_lower:
        return "Laval"
    elif 'longueuil' in city_lower:
        return "South-Shore"
    elif 'brossard' in city_lower:
        return "South-Shore"
    elif 'quebec' in city_lower or 'québec' in city_lower:
        return "Quebec-City"
    elif 'gatineau' in city_lower:
        return "Gatineau"
    elif 'sherbrooke' in city_lower:
        return "Sherbrooke"
    elif 'trois-rivi' in city_lower:
        return "Trois-Rivieres"
    
    # Default to city name if not in major regions
    return city

@router.get("/client-requests/health", response_model=Dict[str, Any])
async def health_check() -> Dict[str, Any]:
    """Health check endpoint for the client requests API."""
    return {
        "status": "healthy",
        "service": "client-requests-api",
        "timestamp": datetime.utcnow().isoformat()
    }