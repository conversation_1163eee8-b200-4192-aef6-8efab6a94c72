import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Bell, Mail, MessageSquare, Smartphone, Volume2,
  Save, Plus, Trash2, Edit2, TestTube, CheckCircle
} from 'lucide-react';

interface NotificationTemplate {
  id: string;
  type: 'appointment_reminder' | 'payment_due' | 'session_cancelled' | 'welcome' | 'custom';
  name: string;
  channels: ('email' | 'sms' | 'push' | 'in_app')[];
  timing?: string;
  enabled: boolean;
}

interface NotificationConfig {
  email: {
    enabled: boolean;
    provider: 'sendgrid' | 'mailgun' | 'smtp';
    fromAddress: string;
    fromName: string;
    replyTo: string;
    smtpHost?: string;
    smtpPort?: number;
    smtpUser?: string;
    smtpPassword?: string;
  };
  sms: {
    enabled: boolean;
    provider: 'twilio' | 'nexmo';
    accountSid: string;
    authToken: string;
    fromNumber: string;
    alphanumericSenderId: string;
  };
  push: {
    enabled: boolean;
    provider: 'onesignal' | 'firebase';
    appId: string;
    apiKey: string;
    safariWebId?: string;
  };
  preferences: {
    defaultChannels: string[];
    quietHoursEnabled: boolean;
    quietHoursStart: string;
    quietHoursEnd: string;
    batchNotifications: boolean;
    batchInterval: number;
  };
}

// Mock data
const mockTemplates: NotificationTemplate[] = [
  {
    id: '1',
    type: 'appointment_reminder',
    name: '24-Hour Appointment Reminder',
    channels: ['email', 'sms', 'push'],
    timing: '24 hours before',
    enabled: true,
  },
  {
    id: '2',
    type: 'payment_due',
    name: 'Payment Due Reminder',
    channels: ['email', 'sms'],
    timing: '3 days before due date',
    enabled: true,
  },
  {
    id: '3',
    type: 'session_cancelled',
    name: 'Session Cancellation Notice',
    channels: ['email', 'sms', 'push', 'in_app'],
    timing: 'Immediately',
    enabled: true,
  },
  {
    id: '4',
    type: 'welcome',
    name: 'Welcome Email',
    channels: ['email'],
    timing: 'On registration',
    enabled: true,
  },
];

const NotificationSettings: React.FC = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<'channels' | 'templates' | 'preferences'>('channels');
  const [templates, setTemplates] = useState<NotificationTemplate[]>(mockTemplates);
  const [hasChanges, setHasChanges] = useState(false);
  const [testingChannel, setTestingChannel] = useState<string | null>(null);
  const [testResults, setTestResults] = useState<Record<string, boolean>>({});

  const [config, setConfig] = useState<NotificationConfig>({
    email: {
      enabled: true,
      provider: 'sendgrid',
      fromAddress: '<EMAIL>',
      fromName: 'TutorAide',
      replyTo: '<EMAIL>',
    },
    sms: {
      enabled: true,
      provider: 'twilio',
      accountSid: 'AC1234567890abcdef',
      authToken: '••••••••••••••••',
      fromNumber: '+***********',
      alphanumericSenderId: 'TutorAide',
    },
    push: {
      enabled: true,
      provider: 'onesignal',
      appId: 'abc123-def456-ghi789',
      apiKey: '••••••••••••••••',
    },
    preferences: {
      defaultChannels: ['email', 'push'],
      quietHoursEnabled: true,
      quietHoursStart: '22:00',
      quietHoursEnd: '08:00',
      batchNotifications: true,
      batchInterval: 15,
    },
  });

  const updateConfig = (section: keyof NotificationConfig, field: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value,
      },
    }));
    setHasChanges(true);
  };

  const handleTestChannel = async (channel: string) => {
    setTestingChannel(channel);
    await new Promise(resolve => setTimeout(resolve, 2000));
    setTestResults({ ...testResults, [channel]: true });
    setTestingChannel(null);
    
    // Clear result after 5 seconds
    setTimeout(() => {
      setTestResults(prev => {
        const newResults = { ...prev };
        delete newResults[channel];
        return newResults;
      });
    }, 5000);
  };

  const handleSave = async () => {
    console.log('Saving notification settings:', config);
    await new Promise(resolve => setTimeout(resolve, 1500));
    setHasChanges(false);
  };

  const toggleTemplateChannel = (templateId: string, channel: string) => {
    setTemplates(templates.map(t => {
      if (t.id === templateId) {
        const channels = t.channels.includes(channel as any)
          ? t.channels.filter(c => c !== channel)
          : [...t.channels, channel as any];
        return { ...t, channels };
      }
      return t;
    }));
    setHasChanges(true);
  };

  return (
    <div className="p-6 max-w-6xl mx-auto">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-text-primary">Notification Settings</h1>
        <p className="text-text-secondary mt-1">Configure how notifications are sent to users</p>
      </div>

      {/* Tabs */}
      <div className="flex items-center gap-6 border-b border-primary-200 mb-6">
        <button
          onClick={() => setActiveTab('channels')}
          className={`pb-3 px-1 font-medium transition-colors ${
            activeTab === 'channels'
              ? 'text-accent-red border-b-2 border-accent-red'
              : 'text-text-secondary hover:text-text-primary'
          }`}
        >
          Notification Channels
        </button>
        <button
          onClick={() => setActiveTab('templates')}
          className={`pb-3 px-1 font-medium transition-colors ${
            activeTab === 'templates'
              ? 'text-accent-red border-b-2 border-accent-red'
              : 'text-text-secondary hover:text-text-primary'
          }`}
        >
          Templates
        </button>
        <button
          onClick={() => setActiveTab('preferences')}
          className={`pb-3 px-1 font-medium transition-colors ${
            activeTab === 'preferences'
              ? 'text-accent-red border-b-2 border-accent-red'
              : 'text-text-secondary hover:text-text-primary'
          }`}
        >
          Preferences
        </button>
      </div>

      {activeTab === 'channels' && (
        <>
          {/* Email Configuration */}
          <div className="bg-white rounded-large shadow-soft p-6 mb-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <Mail className="w-5 h-5 text-accent-red" />
                <h2 className="text-lg font-semibold text-text-primary">Email Configuration</h2>
              </div>
              <div className="flex items-center gap-2">
                {testResults.email && (
                  <span className="flex items-center gap-1 text-accent-green text-sm">
                    <CheckCircle className="w-4 h-4" />
                    Test Sent
                  </span>
                )}
                <button
                  onClick={() => handleTestChannel('email')}
                  disabled={testingChannel === 'email'}
                  className="flex items-center gap-2 px-4 py-2 border border-primary-300 rounded-soft hover:bg-background-secondary disabled:opacity-50"
                >
                  <TestTube className={`w-4 h-4 ${testingChannel === 'email' ? 'animate-pulse' : ''}`} />
                  Test
                </button>
              </div>
            </div>

            <div className="space-y-4">
              <label className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-text-primary">Enable Email Notifications</p>
                  <p className="text-sm text-text-secondary">Send notifications via email</p>
                </div>
                <input
                  type="checkbox"
                  checked={config.email.enabled}
                  onChange={(e) => updateConfig('email', 'enabled', e.target.checked)}
                  className="w-5 h-5 rounded accent-accent-red"
                />
              </label>

              {config.email.enabled && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Email Provider
                    </label>
                    <select
                      value={config.email.provider}
                      onChange={(e) => updateConfig('email', 'provider', e.target.value)}
                      className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                    >
                      <option value="sendgrid">SendGrid</option>
                      <option value="mailgun">Mailgun</option>
                      <option value="smtp">Custom SMTP</option>
                    </select>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-text-primary mb-2">
                        From Address
                      </label>
                      <input
                        type="email"
                        value={config.email.fromAddress}
                        onChange={(e) => updateConfig('email', 'fromAddress', e.target.value)}
                        className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-text-primary mb-2">
                        From Name
                      </label>
                      <input
                        type="text"
                        value={config.email.fromName}
                        onChange={(e) => updateConfig('email', 'fromName', e.target.value)}
                        className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-text-primary mb-2">
                        Reply-To Address
                      </label>
                      <input
                        type="email"
                        value={config.email.replyTo}
                        onChange={(e) => updateConfig('email', 'replyTo', e.target.value)}
                        className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                      />
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>

          {/* SMS Configuration */}
          <div className="bg-white rounded-large shadow-soft p-6 mb-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <MessageSquare className="w-5 h-5 text-accent-red" />
                <h2 className="text-lg font-semibold text-text-primary">SMS Configuration</h2>
              </div>
              <div className="flex items-center gap-2">
                {testResults.sms && (
                  <span className="flex items-center gap-1 text-accent-green text-sm">
                    <CheckCircle className="w-4 h-4" />
                    Test Sent
                  </span>
                )}
                <button
                  onClick={() => handleTestChannel('sms')}
                  disabled={testingChannel === 'sms'}
                  className="flex items-center gap-2 px-4 py-2 border border-primary-300 rounded-soft hover:bg-background-secondary disabled:opacity-50"
                >
                  <TestTube className={`w-4 h-4 ${testingChannel === 'sms' ? 'animate-pulse' : ''}`} />
                  Test
                </button>
              </div>
            </div>

            <div className="space-y-4">
              <label className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-text-primary">Enable SMS Notifications</p>
                  <p className="text-sm text-text-secondary">Send notifications via SMS</p>
                </div>
                <input
                  type="checkbox"
                  checked={config.sms.enabled}
                  onChange={(e) => updateConfig('sms', 'enabled', e.target.checked)}
                  className="w-5 h-5 rounded accent-accent-red"
                />
              </label>

              {config.sms.enabled && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      SMS Provider
                    </label>
                    <select
                      value={config.sms.provider}
                      onChange={(e) => updateConfig('sms', 'provider', e.target.value)}
                      className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                    >
                      <option value="twilio">Twilio</option>
                      <option value="nexmo">Nexmo/Vonage</option>
                    </select>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-text-primary mb-2">
                        Account SID
                      </label>
                      <input
                        type="text"
                        value={config.sms.accountSid}
                        onChange={(e) => updateConfig('sms', 'accountSid', e.target.value)}
                        className="w-full px-4 py-2 font-mono text-sm border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-text-primary mb-2">
                        Auth Token
                      </label>
                      <input
                        type="password"
                        value={config.sms.authToken}
                        onChange={(e) => updateConfig('sms', 'authToken', e.target.value)}
                        className="w-full px-4 py-2 font-mono text-sm border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-text-primary mb-2">
                        From Number
                      </label>
                      <input
                        type="tel"
                        value={config.sms.fromNumber}
                        onChange={(e) => updateConfig('sms', 'fromNumber', e.target.value)}
                        className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                        placeholder="+1234567890"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-text-primary mb-2">
                        Sender ID (optional)
                      </label>
                      <input
                        type="text"
                        value={config.sms.alphanumericSenderId}
                        onChange={(e) => updateConfig('sms', 'alphanumericSenderId', e.target.value)}
                        className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                        placeholder="TutorAide"
                        maxLength={11}
                      />
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>

          {/* Push Notification Configuration */}
          <div className="bg-white rounded-large shadow-soft p-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <Smartphone className="w-5 h-5 text-accent-red" />
                <h2 className="text-lg font-semibold text-text-primary">Push Notification Configuration</h2>
              </div>
              <div className="flex items-center gap-2">
                {testResults.push && (
                  <span className="flex items-center gap-1 text-accent-green text-sm">
                    <CheckCircle className="w-4 h-4" />
                    Test Sent
                  </span>
                )}
                <button
                  onClick={() => handleTestChannel('push')}
                  disabled={testingChannel === 'push'}
                  className="flex items-center gap-2 px-4 py-2 border border-primary-300 rounded-soft hover:bg-background-secondary disabled:opacity-50"
                >
                  <TestTube className={`w-4 h-4 ${testingChannel === 'push' ? 'animate-pulse' : ''}`} />
                  Test
                </button>
              </div>
            </div>

            <div className="space-y-4">
              <label className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-text-primary">Enable Push Notifications</p>
                  <p className="text-sm text-text-secondary">Send notifications to mobile devices</p>
                </div>
                <input
                  type="checkbox"
                  checked={config.push.enabled}
                  onChange={(e) => updateConfig('push', 'enabled', e.target.checked)}
                  className="w-5 h-5 rounded accent-accent-red"
                />
              </label>

              {config.push.enabled && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Push Provider
                    </label>
                    <select
                      value={config.push.provider}
                      onChange={(e) => updateConfig('push', 'provider', e.target.value)}
                      className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                    >
                      <option value="onesignal">OneSignal</option>
                      <option value="firebase">Firebase Cloud Messaging</option>
                    </select>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-text-primary mb-2">
                        App ID
                      </label>
                      <input
                        type="text"
                        value={config.push.appId}
                        onChange={(e) => updateConfig('push', 'appId', e.target.value)}
                        className="w-full px-4 py-2 font-mono text-sm border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-text-primary mb-2">
                        API Key
                      </label>
                      <input
                        type="password"
                        value={config.push.apiKey}
                        onChange={(e) => updateConfig('push', 'apiKey', e.target.value)}
                        className="w-full px-4 py-2 font-mono text-sm border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                      />
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        </>
      )}

      {activeTab === 'templates' && (
        <div className="bg-white rounded-large shadow-soft p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-semibold text-text-primary">Notification Templates</h2>
            <button className="flex items-center gap-2 px-4 py-2 bg-accent-red text-white rounded-soft hover:bg-accent-red-dark">
              <Plus className="w-4 h-4" />
              Add Template
            </button>
          </div>

          <div className="space-y-4">
            {templates.map(template => (
              <div
                key={template.id}
                className="p-4 border border-primary-200 rounded-medium hover:border-accent-red transition-colors"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="font-medium text-text-primary">{template.name}</h3>
                      <span className={`text-xs px-2 py-0.5 rounded-full ${
                        template.enabled 
                          ? 'bg-accent-green bg-opacity-10 text-accent-green' 
                          : 'bg-primary-200 text-text-secondary'
                      }`}>
                        {template.enabled ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                    {template.timing && (
                      <p className="text-sm text-text-secondary mb-2">
                        <Clock className="w-3 h-3 inline mr-1" />
                        {template.timing}
                      </p>
                    )}
                    <div className="flex items-center gap-4">
                      <label className="flex items-center gap-2 text-sm">
                        <input
                          type="checkbox"
                          checked={template.channels.includes('email')}
                          onChange={() => toggleTemplateChannel(template.id, 'email')}
                          className="w-4 h-4 rounded accent-accent-red"
                        />
                        <Mail className="w-4 h-4 text-text-secondary" />
                        Email
                      </label>
                      <label className="flex items-center gap-2 text-sm">
                        <input
                          type="checkbox"
                          checked={template.channels.includes('sms')}
                          onChange={() => toggleTemplateChannel(template.id, 'sms')}
                          className="w-4 h-4 rounded accent-accent-red"
                        />
                        <MessageSquare className="w-4 h-4 text-text-secondary" />
                        SMS
                      </label>
                      <label className="flex items-center gap-2 text-sm">
                        <input
                          type="checkbox"
                          checked={template.channels.includes('push')}
                          onChange={() => toggleTemplateChannel(template.id, 'push')}
                          className="w-4 h-4 rounded accent-accent-red"
                        />
                        <Smartphone className="w-4 h-4 text-text-secondary" />
                        Push
                      </label>
                      <label className="flex items-center gap-2 text-sm">
                        <input
                          type="checkbox"
                          checked={template.channels.includes('in_app')}
                          onChange={() => toggleTemplateChannel(template.id, 'in_app')}
                          className="w-4 h-4 rounded accent-accent-red"
                        />
                        <Bell className="w-4 h-4 text-text-secondary" />
                        In-App
                      </label>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 ml-4">
                    <button className="p-1.5 hover:bg-background-secondary rounded-medium transition-colors">
                      <Edit2 className="w-4 h-4 text-text-secondary" />
                    </button>
                    <button className="p-1.5 hover:bg-background-secondary rounded-medium transition-colors">
                      <Trash2 className="w-4 h-4 text-accent-red" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {activeTab === 'preferences' && (
        <div className="bg-white rounded-large shadow-soft p-6">
          <h2 className="text-lg font-semibold text-text-primary mb-6">Notification Preferences</h2>

          <div className="space-y-6">
            <div>
              <h3 className="font-medium text-text-primary mb-3">Default Notification Channels</h3>
              <p className="text-sm text-text-secondary mb-4">
                Select the default channels for new users. Users can customize their preferences later.
              </p>
              <div className="space-y-2">
                <label className="flex items-center gap-3">
                  <input
                    type="checkbox"
                    checked={config.preferences.defaultChannels.includes('email')}
                    onChange={(e) => {
                      const channels = e.target.checked
                        ? [...config.preferences.defaultChannels, 'email']
                        : config.preferences.defaultChannels.filter(c => c !== 'email');
                      updateConfig('preferences', 'defaultChannels', channels);
                    }}
                    className="w-4 h-4 rounded accent-accent-red"
                  />
                  <Mail className="w-4 h-4 text-text-secondary" />
                  <span className="text-sm">Email notifications</span>
                </label>
                <label className="flex items-center gap-3">
                  <input
                    type="checkbox"
                    checked={config.preferences.defaultChannels.includes('sms')}
                    onChange={(e) => {
                      const channels = e.target.checked
                        ? [...config.preferences.defaultChannels, 'sms']
                        : config.preferences.defaultChannels.filter(c => c !== 'sms');
                      updateConfig('preferences', 'defaultChannels', channels);
                    }}
                    className="w-4 h-4 rounded accent-accent-red"
                  />
                  <MessageSquare className="w-4 h-4 text-text-secondary" />
                  <span className="text-sm">SMS notifications</span>
                </label>
                <label className="flex items-center gap-3">
                  <input
                    type="checkbox"
                    checked={config.preferences.defaultChannels.includes('push')}
                    onChange={(e) => {
                      const channels = e.target.checked
                        ? [...config.preferences.defaultChannels, 'push']
                        : config.preferences.defaultChannels.filter(c => c !== 'push');
                      updateConfig('preferences', 'defaultChannels', channels);
                    }}
                    className="w-4 h-4 rounded accent-accent-red"
                  />
                  <Smartphone className="w-4 h-4 text-text-secondary" />
                  <span className="text-sm">Push notifications</span>
                </label>
              </div>
            </div>

            <div>
              <h3 className="font-medium text-text-primary mb-3">Quiet Hours</h3>
              <label className="flex items-center justify-between mb-4">
                <div>
                  <p className="font-medium text-text-primary">Enable Quiet Hours</p>
                  <p className="text-sm text-text-secondary">Pause non-urgent notifications during specified hours</p>
                </div>
                <input
                  type="checkbox"
                  checked={config.preferences.quietHoursEnabled}
                  onChange={(e) => updateConfig('preferences', 'quietHoursEnabled', e.target.checked)}
                  className="w-5 h-5 rounded accent-accent-red"
                />
              </label>

              {config.preferences.quietHoursEnabled && (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Start Time
                    </label>
                    <input
                      type="time"
                      value={config.preferences.quietHoursStart}
                      onChange={(e) => updateConfig('preferences', 'quietHoursStart', e.target.value)}
                      className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      End Time
                    </label>
                    <input
                      type="time"
                      value={config.preferences.quietHoursEnd}
                      onChange={(e) => updateConfig('preferences', 'quietHoursEnd', e.target.value)}
                      className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                    />
                  </div>
                </div>
              )}
            </div>

            <div>
              <h3 className="font-medium text-text-primary mb-3">Notification Batching</h3>
              <label className="flex items-center justify-between mb-4">
                <div>
                  <p className="font-medium text-text-primary">Batch Similar Notifications</p>
                  <p className="text-sm text-text-secondary">Group similar notifications to reduce noise</p>
                </div>
                <input
                  type="checkbox"
                  checked={config.preferences.batchNotifications}
                  onChange={(e) => updateConfig('preferences', 'batchNotifications', e.target.checked)}
                  className="w-5 h-5 rounded accent-accent-red"
                />
              </label>

              {config.preferences.batchNotifications && (
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Batch Interval (minutes)
                  </label>
                  <input
                    type="number"
                    value={config.preferences.batchInterval}
                    onChange={(e) => updateConfig('preferences', 'batchInterval', parseInt(e.target.value))}
                    className="w-full md:w-1/2 px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                    min={5}
                    max={60}
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Save Button */}
      <div className="mt-6 flex justify-end">
        <button
          onClick={handleSave}
          disabled={!hasChanges}
          className={`
            flex items-center gap-2 px-6 py-2 rounded-soft text-white
            ${hasChanges
              ? 'bg-accent-red hover:bg-accent-red-dark'
              : 'bg-primary-300 cursor-not-allowed'
            }
          `}
        >
          <Save className="w-4 h-4" />
          Save Changes
        </button>
      </div>
    </div>
  );
};

export default NotificationSettings;