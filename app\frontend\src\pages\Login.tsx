import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../contexts/AuthContext';
import { LoginCredentials } from '../types/auth';
import { Eye, EyeOff } from 'lucide-react';
import Button from '../components/common/Button';
import { Input } from '../components/common/Input';
import { Card } from '../components/common/Card';
import { getApiBaseUrl } from '../utils/api-config';
import toast from 'react-hot-toast';
import { useLocation, Link } from 'react-router-dom';

const Login: React.FC = () => {
  const { t } = useTranslation();
  const { login } = useAuth();
  const location = useLocation();
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [logoError, setLogoError] = useState(false);
  const [credentials, setCredentials] = useState<LoginCredentials>({
    email: '',
    password: '',
  });

  useEffect(() => {
    // Check if redirected from password reset with message
    if (location.state?.message) {
      toast.success(location.state.message);
      if (location.state.email) {
        setCredentials(prev => ({ ...prev, email: location.state.email }));
      }
    }
  }, [location]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!credentials.email || !credentials.password) {
      toast.error('Please fill in all fields');
      return;
    }

    setIsLoading(true);
    try {
      await login(credentials);
    } catch (error) {
      // Error is handled in AuthContext
    } finally {
      setIsLoading(false);
    }
  };


  return (
    <div className="min-h-screen bg-gradient-to-b from-background-primary to-background-secondary flex items-center justify-center px-4">
      <div className="max-w-md w-full">
        <Card className="p-8 shadow-elevated backdrop-blur-md bg-white/95">
          {/* Logo */}
          <div className="text-center mb-8">
            {!logoError ? (
              <img
                src="/logo_tutoraide.jpg"
                alt="TutorAide"
                className="h-20 w-auto mx-auto mb-6"
                onError={(e) => {
                  console.error('Logo failed to load');
                  setLogoError(true);
                }}
              />
            ) : (
              <div className="h-20 flex items-center justify-center mb-6">
                <h1 className="text-4xl font-bold text-accent-red">TutorAide</h1>
              </div>
            )}
            <h1 className="text-2xl font-semibold text-text-primary">
              {t('auth.loginTitle')}
            </h1>
            <p className="text-sm text-text-secondary mt-2">
              Welcome back! Please login to your account.
            </p>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium text-text-primary mb-2"
              >
                {t('auth.email')}
              </label>
              <Input
                id="email"
                type="email"
                value={credentials.email}
                onChange={(e) =>
                  setCredentials({ ...credentials, email: e.target.value })
                }
                placeholder="<EMAIL>"
                autoComplete="email"
                className="w-full"
              />
            </div>

            <div>
              <label
                htmlFor="password"
                className="block text-sm font-medium text-text-primary mb-2"
              >
                {t('auth.password')}
              </label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  value={credentials.password}
                  onChange={(e) =>
                    setCredentials({ ...credentials, password: e.target.value })
                  }
                  placeholder="••••••••"
                  autoComplete="current-password"
                  className="w-full pr-10"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-text-secondary hover:text-text-primary transition-colors"
                >
                  {showPassword ? (
                    <EyeOff className="w-5 h-5" />
                  ) : (
                    <Eye className="w-5 h-5" />
                  )}
                </button>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  className="w-4 h-4 text-accent-red border-border-primary rounded focus:ring-accent-red accent-accent-red"
                />
                <span className="ml-2 text-sm text-text-primary">
                  {t('auth.rememberMe')}
                </span>
              </label>
              <Link
                to="/auth/forgot-password"
                className="text-sm text-accent-red hover:text-accent-red-dark transition-colors"
              >
                {t('auth.forgotPassword.title')}
              </Link>
            </div>

            <Button
              type="submit"
              disabled={isLoading}
              loading={isLoading}
              className="w-full py-3"
              size="lg"
            >
              {t('auth.login')}
            </Button>
          </form>

          {/* Divider */}
          <div className="relative mt-6">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-border-primary"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-white text-text-muted">
                {t('auth.orContinueWith')}
              </span>
            </div>
          </div>

          {/* Google OAuth Button */}
          <button
            type="button"
            disabled={isLoading}
            onClick={() => {
              // Redirect to Google OAuth login endpoint
              const apiUrl = getApiBaseUrl();
              const googleAuthUrl = `${apiUrl}/auth/google/login`;
              console.log('Redirecting to Google OAuth:', googleAuthUrl);
              window.location.href = googleAuthUrl;
            }}
            className="mt-4 w-full flex items-center justify-center gap-3 px-4 py-3 bg-white border border-border-primary hover:bg-background-secondary rounded-xl transition-all"
          >
            <svg className="w-5 h-5" viewBox="0 0 24 24">
              <path
                fill="#4285F4"
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
              />
              <path
                fill="#34A853"
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
              />
              <path
                fill="#FBBC05"
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
              />
              <path
                fill="#EA4335"
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
              />
            </svg>
            <span className="text-text-primary font-medium">
              {t('auth.continueWithGoogle')}
            </span>
          </button>

          {/* Sign up link */}
          <p className="mt-6 text-center text-sm text-text-secondary">
            {t('auth.noAccount')}{' '}
            <a
              href="#"
              className="font-medium text-accent-red hover:text-accent-red-dark transition-colors"
            >
              {t('auth.signUp')}
            </a>
          </p>

        </Card>

      </div>
    </div>
  );
};

export default Login;