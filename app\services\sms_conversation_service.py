"""
SMS conversation management service for customer support.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict

from app.database.repositories.sms_repository import SMSRepository
from app.database.repositories.user_repository import UserRepository
from app.services.twilio_service import TwilioService
from app.models.notification_models import SMSStatus
from app.models.user_models import UserRole
from app.core.exceptions import ExternalServiceError, ValidationError

logger = logging.getLogger(__name__)


class SMSConversationService:
    """
    Service for managing SMS conversations.
    
    Features:
    - Conversation threading by phone number
    - Latest-first message ordering
    - Unread message tracking
    - Role-based access control
    - Conversation search and filtering
    - Bulk message operations
    - Conversation metrics
    """
    
    def __init__(self):
        self.sms_repo = SMSRepository()
        self.user_repo = UserRepository()
        self.twilio_service = TwilioService()
        
        # Conversation status definitions
        self.conversation_statuses = {
            'active': 'Active conversation',
            'resolved': 'Issue resolved',
            'waiting': 'Waiting for customer',
            'escalated': 'Escalated to manager',
            'closed': 'Conversation closed'
        }
    
    async def get_conversations(
        self,
        manager_id: int,
        status: Optional[str] = None,
        unread_only: bool = False,
        search_query: Optional[str] = None,
        limit: int = 50,
        offset: int = 0
    ) -> Dict[str, Any]:
        """
        Get SMS conversations for manager view.
        
        Args:
            manager_id: Manager user ID
            status: Filter by conversation status
            unread_only: Show only unread conversations
            search_query: Search in messages or user info
            limit: Number of conversations to return
            offset: Pagination offset
            
        Returns:
            Paginated list of conversations with metadata
        """
        try:
            # Verify manager role
            manager = await self.user_repo.get_user(manager_id)
            if not manager or UserRole.MANAGER not in manager.roles:
                raise ValidationError("Unauthorized: Manager access required")
            
            # Get conversations from repository
            conversations = await self.sms_repo.get_conversations(
                status=status,
                unread_only=unread_only,
                search_query=search_query,
                limit=limit,
                offset=offset
            )
            
            # Process each conversation
            processed_conversations = []
            for conv in conversations:
                # Get user info if available
                user_info = None
                if conv.user_id:
                    user = await self.user_repo.get_user(conv.user_id)
                    if user:
                        user_info = {
                            'user_id': user.user_id,
                            'name': f"{user.first_name} {user.last_name}",
                            'email': user.email,
                            'role': self._get_user_role_display(user.roles)
                        }
                
                # Get conversation metrics
                metrics = await self._get_conversation_metrics(
                    conv.phone_number,
                    conv.user_id
                )
                
                processed_conversations.append({
                    'conversation_id': conv.conversation_id,
                    'phone_number': conv.phone_number,
                    'user': user_info,
                    'last_message': {
                        'content': conv.last_message,
                        'direction': conv.last_direction,
                        'timestamp': conv.last_timestamp,
                        'is_unread': conv.has_unread
                    },
                    'status': conv.status,
                    'assigned_to': conv.assigned_to,
                    'metrics': metrics,
                    'tags': conv.tags or []
                })
            
            # Get total count for pagination
            total_count = await self.sms_repo.get_conversation_count(
                status=status,
                unread_only=unread_only,
                search_query=search_query
            )
            
            return {
                'conversations': processed_conversations,
                'pagination': {
                    'total': total_count,
                    'limit': limit,
                    'offset': offset,
                    'has_more': offset + limit < total_count
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting conversations: {e}")
            raise
    
    async def get_conversation_messages(
        self,
        phone_number: str,
        manager_id: int,
        limit: int = 100,
        before_timestamp: Optional[datetime] = None
    ) -> List[Dict[str, Any]]:
        """
        Get messages in a conversation (latest first).
        
        Args:
            phone_number: Phone number to get messages for
            manager_id: Manager requesting messages
            limit: Number of messages to return
            before_timestamp: Get messages before this timestamp
            
        Returns:
            List of messages in reverse chronological order
        """
        try:
            # Verify manager access
            manager = await self.user_repo.get_user(manager_id)
            if not manager or UserRole.MANAGER not in manager.roles:
                raise ValidationError("Unauthorized: Manager access required")
            
            # Get messages
            messages = await self.sms_repo.get_conversation_messages(
                phone_number=phone_number,
                limit=limit,
                before_timestamp=before_timestamp
            )
            
            # Mark messages as read
            unread_ids = [
                msg.sms_id for msg in messages 
                if msg.direction == 'inbound' and not msg.read_at
            ]
            if unread_ids:
                await self.sms_repo.mark_messages_read(
                    message_ids=unread_ids,
                    read_by=manager_id
                )
            
            # Format messages with role indicators
            formatted_messages = []
            for msg in messages:
                # Add role indicator
                role_indicator = ""
                if msg.user_id:
                    user = await self.user_repo.get_user(msg.user_id)
                    if user:
                        if UserRole.TUTOR in user.roles:
                            role_indicator = "👨‍🏫 TUTOR"
                        elif UserRole.CLIENT in user.roles:
                            role_indicator = "👤 CLIENT"
                
                formatted_messages.append({
                    'sms_id': msg.sms_id,
                    'message': msg.message,
                    'direction': msg.direction,
                    'timestamp': msg.created_at,
                    'status': msg.status,
                    'delivered_at': msg.delivered_at,
                    'error_message': msg.error_message,
                    'role_indicator': role_indicator,
                    'sent_by': msg.sent_by_name if msg.direction == 'outbound' else None,
                    'metadata': msg.metadata or {}
                })
            
            return formatted_messages
            
        except Exception as e:
            logger.error(f"Error getting conversation messages: {e}")
            raise
    
    async def send_message(
        self,
        phone_number: str,
        message: str,
        manager_id: int,
        template_id: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Send SMS message in conversation.
        
        Args:
            phone_number: Recipient phone number
            message: Message content
            manager_id: Manager sending the message
            template_id: Optional template ID used
            metadata: Additional metadata
            
        Returns:
            Sent message details
        """
        try:
            # Verify manager
            manager = await self.user_repo.get_user(manager_id)
            if not manager or UserRole.MANAGER not in manager.roles:
                raise ValidationError("Unauthorized: Manager access required")
            
            # Find associated user
            user = await self.sms_repo.get_user_by_phone(phone_number)
            user_id = user.user_id if user else None
            
            # Add manager info to metadata
            enhanced_metadata = metadata or {}
            enhanced_metadata.update({
                'sent_by': manager_id,
                'sent_by_name': f"{manager.first_name} {manager.last_name}",
                'template_id': template_id
            })
            
            # Send via Twilio
            result = await self.twilio_service.send_sms(
                to_number=phone_number,
                message=message,
                user_id=user_id,
                metadata=enhanced_metadata
            )
            
            # Update conversation status
            await self.sms_repo.update_conversation_status(
                phone_number=phone_number,
                status='active',
                assigned_to=manager_id
            )
            
            return {
                'success': True,
                'message_sid': result['message_sid'],
                'sms_id': result['sms_id'],
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            logger.error(f"Error sending message: {e}")
            raise
    
    async def update_conversation_status(
        self,
        phone_number: str,
        status: str,
        manager_id: int,
        notes: Optional[str] = None
    ) -> bool:
        """
        Update conversation status.
        
        Args:
            phone_number: Phone number identifying conversation
            status: New status
            manager_id: Manager updating status
            notes: Optional status change notes
            
        Returns:
            Success status
        """
        try:
            # Verify manager
            manager = await self.user_repo.get_user(manager_id)
            if not manager or UserRole.MANAGER not in manager.roles:
                raise ValidationError("Unauthorized: Manager access required")
            
            # Validate status
            if status not in self.conversation_statuses:
                raise ValidationError(f"Invalid status: {status}")
            
            # Update status
            await self.sms_repo.update_conversation_status(
                phone_number=phone_number,
                status=status,
                assigned_to=manager_id,
                notes=notes
            )
            
            # Log status change
            await self.sms_repo.log_conversation_event(
                phone_number=phone_number,
                event_type='status_change',
                event_data={
                    'old_status': await self._get_current_status(phone_number),
                    'new_status': status,
                    'changed_by': manager_id,
                    'notes': notes
                }
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Error updating conversation status: {e}")
            raise
    
    async def assign_conversation(
        self,
        phone_number: str,
        assignee_id: int,
        manager_id: int
    ) -> bool:
        """
        Assign conversation to a manager.
        
        Args:
            phone_number: Phone number identifying conversation
            assignee_id: Manager to assign to
            manager_id: Manager making the assignment
            
        Returns:
            Success status
        """
        try:
            # Verify both managers
            manager = await self.user_repo.get_user(manager_id)
            assignee = await self.user_repo.get_user(assignee_id)
            
            if not manager or UserRole.MANAGER not in manager.roles:
                raise ValidationError("Unauthorized: Manager access required")
            
            if not assignee or UserRole.MANAGER not in assignee.roles:
                raise ValidationError("Assignee must be a manager")
            
            # Update assignment
            await self.sms_repo.update_conversation_assignment(
                phone_number=phone_number,
                assigned_to=assignee_id
            )
            
            # Log assignment
            await self.sms_repo.log_conversation_event(
                phone_number=phone_number,
                event_type='assignment_change',
                event_data={
                    'assigned_to': assignee_id,
                    'assigned_by': manager_id
                }
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Error assigning conversation: {e}")
            raise
    
    async def add_conversation_tags(
        self,
        phone_number: str,
        tags: List[str],
        manager_id: int
    ) -> bool:
        """
        Add tags to conversation for categorization.
        
        Args:
            phone_number: Phone number identifying conversation
            tags: Tags to add
            manager_id: Manager adding tags
            
        Returns:
            Success status
        """
        try:
            # Verify manager
            manager = await self.user_repo.get_user(manager_id)
            if not manager or UserRole.MANAGER not in manager.roles:
                raise ValidationError("Unauthorized: Manager access required")
            
            # Add tags
            await self.sms_repo.add_conversation_tags(
                phone_number=phone_number,
                tags=tags
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Error adding conversation tags: {e}")
            raise
    
    async def get_conversation_statistics(
        self,
        manager_id: int,
        date_range: Optional[Tuple[datetime, datetime]] = None
    ) -> Dict[str, Any]:
        """
        Get conversation statistics for dashboard.
        
        Args:
            manager_id: Manager requesting stats
            date_range: Optional date range filter
            
        Returns:
            Conversation statistics
        """
        try:
            # Verify manager
            manager = await self.user_repo.get_user(manager_id)
            if not manager or UserRole.MANAGER not in manager.roles:
                raise ValidationError("Unauthorized: Manager access required")
            
            # Default to last 30 days
            if not date_range:
                end_date = datetime.now()
                start_date = end_date - timedelta(days=30)
                date_range = (start_date, end_date)
            
            # Get statistics
            stats = await self.sms_repo.get_conversation_statistics(date_range)
            
            # Calculate response metrics
            response_times = await self.sms_repo.get_response_time_metrics(date_range)
            
            return {
                'overview': {
                    'total_conversations': stats['total_conversations'],
                    'active_conversations': stats['active_conversations'],
                    'unread_messages': stats['unread_messages'],
                    'messages_sent': stats['messages_sent'],
                    'messages_received': stats['messages_received']
                },
                'status_breakdown': stats['status_breakdown'],
                'response_metrics': {
                    'average_response_time': response_times['average'],
                    'median_response_time': response_times['median'],
                    'response_rate': response_times['response_rate']
                },
                'top_tags': stats['top_tags'],
                'busiest_hours': stats['busiest_hours'],
                'manager_activity': stats['manager_activity']
            }
            
        except Exception as e:
            logger.error(f"Error getting conversation statistics: {e}")
            raise
    
    async def export_conversation(
        self,
        phone_number: str,
        manager_id: int,
        format: str = 'json'
    ) -> Dict[str, Any]:
        """
        Export conversation history.
        
        Args:
            phone_number: Phone number to export
            manager_id: Manager requesting export
            format: Export format (json, csv, pdf)
            
        Returns:
            Export data or file path
        """
        try:
            # Verify manager
            manager = await self.user_repo.get_user(manager_id)
            if not manager or UserRole.MANAGER not in manager.roles:
                raise ValidationError("Unauthorized: Manager access required")
            
            # Get all messages
            messages = await self.sms_repo.get_all_conversation_messages(phone_number)
            
            # Get user info
            user = await self.sms_repo.get_user_by_phone(phone_number)
            user_info = None
            if user:
                user_info = {
                    'user_id': user.user_id,
                    'name': f"{user.first_name} {user.last_name}",
                    'email': user.email
                }
            
            # Format export data
            export_data = {
                'conversation': {
                    'phone_number': phone_number,
                    'user': user_info,
                    'exported_at': datetime.now(),
                    'exported_by': f"{manager.first_name} {manager.last_name}",
                    'total_messages': len(messages)
                },
                'messages': [
                    {
                        'timestamp': msg.created_at,
                        'direction': msg.direction,
                        'content': msg.message,
                        'status': msg.status,
                        'sent_by': msg.sent_by_name if msg.direction == 'outbound' else phone_number
                    }
                    for msg in messages
                ]
            }
            
            # Log export
            await self.sms_repo.log_conversation_event(
                phone_number=phone_number,
                event_type='exported',
                event_data={
                    'exported_by': manager_id,
                    'format': format
                }
            )
            
            return export_data
            
        except Exception as e:
            logger.error(f"Error exporting conversation: {e}")
            raise
    
    def _get_user_role_display(self, roles: List[UserRole]) -> str:
        """Get display string for user roles."""
        if UserRole.TUTOR in roles:
            return "👨‍🏫 TUTOR"
        elif UserRole.CLIENT in roles:
            return "👤 CLIENT"
        elif UserRole.MANAGER in roles:
            return "👔 MANAGER"
        return "USER"
    
    async def _get_conversation_metrics(
        self,
        phone_number: str,
        user_id: Optional[int]
    ) -> Dict[str, Any]:
        """Get metrics for a conversation."""
        return await self.sms_repo.get_conversation_metrics(
            phone_number=phone_number,
            user_id=user_id
        )
    
    async def _get_current_status(self, phone_number: str) -> str:
        """Get current conversation status."""
        conversation = await self.sms_repo.get_conversation_by_phone(phone_number)
        return conversation.status if conversation else 'unknown'