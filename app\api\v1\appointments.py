"""
Appointment management API endpoints with comprehensive validation and authorization.
"""

import logging
from datetime import datetime, timedelta, date, time
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from pydantic import BaseModel, Field, field_validator
from app.core.dependencies import get_current_user, get_db_connection
from app.core.authorization import require_role
from app.core.validation import DataValidator, ValidationError
from app.core.exceptions import BusinessLogicError
from app.models.auth_models import UserRoleType
from app.models.appointment_models import (
    AppointmentStatus, AppointmentCreate,
    AppointmentUpdate, AppointmentResponse,
    AppointmentConfirmation, AppointmentCompletion,
    DurationAdjustmentRequest, DurationAdjustmentHistory,
    RecurringAppointmentCreate
)
from app.models.service_models import ServiceType, LocationType, PackageType
from app.database.repositories.appointment_repository import AppointmentRepository
from app.database.repositories.tutor_repository import TutorRepository
from app.database.repositories.client_repository import ClientRepository
from app.services.appointment_service import AppointmentSchedulingService as AppointmentService
from app.services.appointment_duration_service import AppointmentDurationService
from app.api.v1.websocket_calendar import notify_appointment_change

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/appointments", tags=["appointments"])


# Enhanced Request Models with Validation
class CreateAppointmentValidatedRequest(BaseModel):
    """Enhanced appointment creation request with comprehensive validation."""
    
    tutor_id: int = Field(..., gt=0, description="ID of the tutor")
    client_id: int = Field(..., gt=0, description="ID of the client")
    dependant_id: Optional[int] = Field(None, gt=0, description="ID of dependant (optional)")
    
    scheduled_date: date = Field(..., description="Date of the appointment")
    start_time: time = Field(..., description="Start time of the appointment")
    end_time: time = Field(..., description="End time of the appointment")
    
    subject_area: str = Field(..., min_length=1, max_length=100, description="Subject area")
    session_type: PackageType = Field(PackageType.INDIVIDUAL, description="Type of session")
    location_type: LocationType = Field(LocationType.ONLINE, description="Location type")
    location_details: Optional[str] = Field(None, max_length=200, description="Location details")


class UpdateRecurringRequest(BaseModel):
    """Request model for updating recurring appointment series."""
    
    day_of_week: Optional[int] = Field(None, ge=0, le=6, description="Day of week (0=Monday, 6=Sunday)")
    start_time: Optional[time] = Field(None, description="New start time")
    end_time: Optional[time] = Field(None, description="New end time")
    subject_area: Optional[str] = Field(None, min_length=1, max_length=100, description="Subject area")
    location_details: Optional[Dict[str, Any]] = Field(None, description="Location details")
    hourly_rate: Optional[float] = Field(None, ge=0, description="Hourly rate")
    end_date: Optional[date] = Field(None, description="New end date for the series")
    is_active: Optional[bool] = Field(None, description="Whether the series is active")
    update_mode: str = Field("future", description="Update mode: 'this_only', 'future', or 'all'")
    
    max_participants: Optional[int] = Field(None, gt=0, le=20, description="Maximum participants for group sessions")
    notes: Optional[str] = Field(None, max_length=1000, description="Additional notes")
    
    send_notifications: bool = Field(True, description="Whether to send notifications")
    require_confirmation: bool = Field(True, description="Whether tutor confirmation is required")
    
    @field_validator('end_date')
    @classmethod
    def validate_end_date(cls, v):
        if v and v < date.today():
            raise ValueError('End date cannot be in the past')
        if v and v > date.today() + timedelta(days=365 * 2):
            raise ValueError('End date cannot be more than 2 years in the future')
        return v
    
    @field_validator('end_time')
    @classmethod
    def validate_end_time(cls, v, info):
        if 'start_time' in info.data and info.data['start_time'] and v and v <= info.data['start_time']:
            raise ValueError('End time must be after start time')
        return v
    
    @field_validator('subject_area')
    @classmethod
    def validate_subject_area(cls, v):
        if v and v.strip():
            return v.strip()
        return v
    
    @field_validator('update_mode')
    @classmethod
    def validate_update_mode(cls, v):
        valid_modes = ['this_only', 'future', 'all']
        if v not in valid_modes:
            raise ValueError(f'Update mode must be one of: {", ".join(valid_modes)}')
        return v
    
    @field_validator('max_participants')
    @classmethod
    def validate_max_participants(cls, v, info):
        session_type = info.data.get('session_type')
        if session_type == PackageType.INDIVIDUAL and v and v > 1:
            raise ValueError('Individual sessions cannot have more than 1 participant')
        if session_type in [ServiceType.GROUP, ServiceType.TECFEE] and not v:
            raise ValueError('Group sessions must specify max participants')
        return v


class UpdateAppointmentValidatedRequest(BaseModel):
    """Enhanced appointment update request with validation."""
    
    scheduled_date: Optional[date] = Field(None, description="New date for appointment")
    start_time: Optional[time] = Field(None, description="New start time")
    end_time: Optional[time] = Field(None, description="New end time")
    
    status: Optional[AppointmentStatus] = Field(None, description="New appointment status")
    location_type: Optional[LocationType] = Field(None, description="New location type")
    location_details: Optional[str] = Field(None, max_length=200, description="New location details")
    
    notes: Optional[str] = Field(None, max_length=1000, description="Updated notes")
    cancellation_reason: Optional[str] = Field(None, max_length=200, description="Reason for cancellation")
    
    send_notifications: bool = Field(True, description="Whether to send update notifications")
    
    @field_validator('scheduled_date')
    def validate_scheduled_date(cls, v):
        if v and v < date.today():
            raise ValueError('Appointment date cannot be in the past')
        return v
    
    @field_validator('end_time')
    @classmethod
    def validate_end_time(cls, v, info):
        if v and 'start_time' in values and info.data['start_time'] and v <= info.data['start_time']:
            raise ValueError('End time must be after start time')
        return v
    
    @field_validator('cancellation_reason')
    @classmethod
    def validate_cancellation_reason(cls, v, info):
        if info.data.get('status') == AppointmentStatus.CANCELLED and not v:
            raise ValueError('Cancellation reason is required when cancelling appointment')
        return v.strip() if v else None


class ConfirmAppointmentRequest(BaseModel):
    """Request to confirm an appointment."""
    confirmed: bool = Field(..., description="Whether to confirm or unconfirm")
    confirmation_notes: Optional[str] = Field(None, max_length=500, description="Confirmation notes")


class BulkUpdateRequest(BaseModel):
    """Request for bulk appointment updates."""
    appointment_ids: List[int] = Field(..., min_items=1, max_items=50, description="List of appointment IDs")
    updates: UpdateAppointmentValidatedRequest = Field(..., description="Updates to apply")


# Response Models
class AppointmentDetailResponse(BaseModel):
    """Detailed appointment response."""
    appointment_id: int
    tutor_id: int
    tutor_name: str
    client_id: int
    client_name: str
    dependant_id: Optional[int]
    dependant_name: Optional[str]
    
    scheduled_date: date
    start_time: time
    end_time: time
    duration: int  # minutes
    
    subject_area: str
    session_type: PackageType
    status: AppointmentStatus
    
    location_type: LocationType
    location_details: Optional[str]
    
    max_participants: Optional[int]
    current_participants: int
    
    notes: Optional[str]
    confirmed_by_tutor: bool
    confirmation_date: Optional[datetime]
    
    created_at: datetime
    updated_at: datetime
    created_by: int
    
    class Config:
        from_attributes = True


# Dependencies
async def get_appointment_service(db = Depends(get_db_connection)) -> AppointmentService:
    """Dependency to get appointment service."""
    appointment_repo = AppointmentRepository(db)
    tutor_repo = TutorRepository(db)
    client_repo = ClientRepository(db)
    return AppointmentService(appointment_repo, tutor_repo, client_repo)


async def get_duration_service(db = Depends(get_db_connection)) -> AppointmentDurationService:
    """Dependency to get appointment duration service."""
    return AppointmentDurationService()


async def get_appointment_confirmation_service(db = Depends(get_db_connection)):
    """Dependency to get appointment confirmation service."""
    from app.services.appointment_confirmation_service import AppointmentConfirmationService
    return AppointmentConfirmationService()


async def validate_appointment_access(
    appointment_id: int,
    current_user,
    db = Depends(get_db_connection)
) -> Dict[str, Any]:
    """Validate user has access to specific appointment."""
    appointment_repo = AppointmentRepository(db)
    appointment = await appointment_repo.get_by_id(appointment_id)
    
    if not appointment:
        raise HTTPException(status_code=404, detail="Appointment not found")
    
    # Managers can access all appointments
    if UserRoleType.MANAGER in current_user.roles:
        return appointment
    
    # Tutors can access their own appointments
    if UserRoleType.TUTOR in current_user.roles and appointment.tutor_id == current_user.user_id:
        return appointment
    
    # Clients can access their own appointments
    if UserRoleType.CLIENT in current_user.roles and appointment.client_id == current_user.user_id:
        return appointment
    
    raise HTTPException(status_code=403, detail="Access denied to this appointment")


# API Endpoints
@router.post("/", response_model=AppointmentDetailResponse)
async def create_appointment(
    request: CreateAppointmentValidatedRequest,
    appointment_service: AppointmentService = Depends(get_appointment_service),
    current_user = Depends(get_current_user)
):
    """
    Create a new appointment with comprehensive validation.
    
    Requires: Manager or Client role
    - Managers can create appointments for any client
    - Clients can only create appointments for themselves
    """
    try:
        # Authorization check
        require_role(current_user, [UserRoleType.MANAGER, UserRoleType.CLIENT])
        
        # If client role, ensure they can only create for themselves
        if UserRoleType.CLIENT in current_user.roles and current_user.user_id != request.client_id:
            raise HTTPException(
                status_code=403,
                detail="Clients can only create appointments for themselves"
            )
        
        # Validate input fields
        request.subject_area = DataValidator.validate_text_field(request.subject_area, "subject_area", max_length=100)
        if request.location_details:
            request.location_details = DataValidator.validate_text_field(request.location_details, "location_details", max_length=200)
        if request.notes:
            request.notes = DataValidator.validate_text_field(request.notes, "notes", max_length=1000)
        
        # Additional business logic validation
        appointment_datetime = datetime.combine(request.scheduled_date, request.start_time)
        
        # Check if appointment is at least 2 hours in the future
        if appointment_datetime < datetime.now() + timedelta(hours=2):
            raise HTTPException(
                status_code=400,
                detail="Appointments must be scheduled at least 2 hours in advance"
            )
        
        # Validate business hours (example: 8 AM to 10 PM)
        if not (8 <= request.start_time.hour <= 21):
            raise HTTPException(
                status_code=400,
                detail="Appointments must be scheduled between 8 AM and 10 PM"
            )
        
        # Create appointment
        appointment = await appointment_service.create_appointment(
            tutor_id=request.tutor_id,
            client_id=request.client_id,
            dependant_id=request.dependant_id,
            scheduled_date=request.scheduled_date,
            start_time=request.start_time,
            end_time=request.end_time,
            subject_area=request.subject_area,
            session_type=request.session_type,
            location_type=request.location_type,
            location_details=request.location_details,
            max_participants=request.max_participants,
            notes=request.notes,
            created_by=current_user.user_id
        )
        
        # Send WebSocket notification
        await notify_appointment_change(
            action="created",
            appointment_data=appointment,
            user_id=current_user.user_id
        )
        
        logger.info(f"Appointment {appointment.appointment_id} created by user {current_user.user_id}")
        
        return appointment
        
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating appointment: {e}")
        raise HTTPException(status_code=500, detail="Failed to create appointment")


@router.get("/{appointment_id}", response_model=AppointmentDetailResponse)
async def get_appointment(
    appointment_id: int = Path(..., gt=0, description="ID of the appointment"),
    appointment = Depends(validate_appointment_access),
    current_user = Depends(get_current_user)
):
    """
    Get appointment details by ID.
    
    Access control:
    - Managers: Can view all appointments
    - Tutors: Can view their own appointments
    - Clients: Can view their own appointments
    """
    return appointment


@router.put("/{appointment_id}", response_model=AppointmentDetailResponse)
async def update_appointment(
    appointment_id: int = Path(..., gt=0, description="ID of the appointment"),
    request: UpdateAppointmentValidatedRequest = None,
    appointment_service: AppointmentService = Depends(get_appointment_service),
    appointment = Depends(validate_appointment_access),
    current_user = Depends(get_current_user)
):
    """
    Update an appointment with validation.
    
    Access control:
    - Managers: Can update all appointments
    - Tutors: Can update their own appointments (limited fields)
    - Clients: Can update their own appointments (limited fields)
    """
    try:
        # Role-based field restrictions
        allowed_fields = set()
        
        if UserRoleType.MANAGER in current_user.roles:
            # Managers can update all fields
            allowed_fields = {
                'scheduled_date', 'start_time', 'end_time', 'status',
                'location_type', 'location_details', 'notes', 'cancellation_reason'
            }
        elif UserRoleType.TUTOR in current_user.roles:
            # Tutors can update limited fields
            allowed_fields = {'status', 'notes', 'cancellation_reason'}
        elif UserRoleType.CLIENT in current_user.roles:
            # Clients can update very limited fields
            allowed_fields = {'notes', 'cancellation_reason'}
        
        # Filter request to only allowed fields
        update_data = {}
        for field, value in request.dict(exclude_unset=True).items():
            if field in allowed_fields:
                update_data[field] = value
            elif value is not None:
                raise HTTPException(
                    status_code=403,
                    detail=f"You don't have permission to update field: {field}"
                )
        
        # Validate input
        if 'notes' in update_data and update_data['notes']:
            update_data['notes'] = DataValidator.validate_text_field(update_data['notes'], "notes", max_length=1000)
        if 'cancellation_reason' in update_data and update_data['cancellation_reason']:
            update_data['cancellation_reason'] = DataValidator.validate_text_field(update_data['cancellation_reason'], "cancellation_reason", max_length=200)
        
        # Update appointment
        updated_appointment = await appointment_service.update_appointment(
            appointment_id=appointment_id,
            **update_data,
            updated_by=current_user.user_id
        )
        
        # Send WebSocket notification
        await notify_appointment_change(
            action="updated",
            appointment_data=updated_appointment,
            user_id=current_user.user_id,
            additional_data={'changes': update_data}
        )
        
        logger.info(f"Appointment {appointment_id} updated by user {current_user.user_id}")
        
        return updated_appointment
        
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating appointment {appointment_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to update appointment")


@router.delete("/{appointment_id}")
async def cancel_appointment(
    appointment_id: int = Path(..., gt=0, description="ID of the appointment"),
    cancellation_reason: str = Query(..., min_length=1, max_length=200, description="Reason for cancellation"),
    appointment_service: AppointmentService = Depends(get_appointment_service),
    appointment = Depends(validate_appointment_access),
    current_user = Depends(get_current_user)
):
    """
    Cancel an appointment.
    
    Access control:
    - Managers: Can cancel all appointments
    - Tutors: Can cancel their own appointments
    - Clients: Can cancel their own appointments
    """
    try:
        # Validate cancellation reason
        cancellation_reason = DataValidator.validate_text_field(cancellation_reason, "cancellation_reason", min_length=1, max_length=200)
        
        # Check if appointment can be cancelled
        if appointment.status == AppointmentStatus.CANCELLED:
            raise HTTPException(
                status_code=400,
                detail="Appointment is already cancelled"
            )
        
        if appointment.status == AppointmentStatus.COMPLETED:
            raise HTTPException(
                status_code=400,
                detail="Cannot cancel completed appointment"
            )
        
        # Cancel appointment
        cancelled_appointment = await appointment_service.cancel_appointment(
            appointment_id=appointment_id,
            cancellation_reason=cancellation_reason,
            cancelled_by=current_user.user_id
        )
        
        # Send WebSocket notification
        await notify_appointment_change(
            action="cancelled",
            appointment_data=cancelled_appointment,
            user_id=current_user.user_id,
            additional_data={'reason': cancellation_reason}
        )
        
        logger.info(f"Appointment {appointment_id} cancelled by user {current_user.user_id}")
        
        return {"message": "Appointment cancelled successfully", "appointment_id": appointment_id}
        
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cancelling appointment {appointment_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to cancel appointment")


@router.post("/{appointment_id}/confirm")
async def confirm_appointment(
    appointment_id: int,
    request: ConfirmAppointmentRequest,
    appointment_service: AppointmentService = Depends(get_appointment_service),
    appointment = Depends(validate_appointment_access),
    current_user = Depends(get_current_user)
):
    """
    Confirm or unconfirm an appointment.
    
    Only tutors can confirm their own appointments.
    Managers can confirm appointments on behalf of tutors.
    """
    try:
        # Only tutors and managers can confirm appointments
        require_role(current_user, [UserRoleType.TUTOR, UserRoleType.MANAGER])
        
        # If tutor role, can only confirm their own appointments
        if (UserRoleType.TUTOR in current_user.roles and 
            UserRoleType.MANAGER not in current_user.roles and 
            appointment.tutor_id != current_user.user_id):
            raise HTTPException(
                status_code=403,
                detail="Tutors can only confirm their own appointments"
            )
        
        # Validate confirmation notes
        if request.confirmation_notes:
            request.confirmation_notes = DataValidator.validate_text_field(request.confirmation_notes, "confirmation_notes", max_length=500)
        
        # Confirm appointment
        confirmed_appointment = await appointment_service.confirm_appointment(
            appointment_id=appointment_id,
            confirmed=request.confirmed,
            confirmation_notes=request.confirmation_notes,
            confirmed_by=current_user.user_id
        )
        
        # Send WebSocket notification
        await notify_appointment_change(
            action="confirmed" if request.confirmed else "unconfirmed",
            appointment_data=confirmed_appointment,
            user_id=current_user.user_id
        )
        
        action = "confirmed" if request.confirmed else "unconfirmed"
        logger.info(f"Appointment {appointment_id} {action} by user {current_user.user_id}")
        
        return {
            "message": f"Appointment {action} successfully",
            "appointment_id": appointment_id,
            "confirmed": request.confirmed
        }
        
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error confirming appointment {appointment_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to confirm appointment")


@router.get("/")
async def list_appointments(
    start_date: Optional[date] = Query(None, description="Filter by start date"),
    end_date: Optional[date] = Query(None, description="Filter by end date"),
    tutor_id: Optional[int] = Query(None, gt=0, description="Filter by tutor ID"),
    client_id: Optional[int] = Query(None, gt=0, description="Filter by client ID"),
    status: Optional[AppointmentStatus] = Query(None, description="Filter by status"),
    session_type: Optional[PackageType] = Query(None, description="Filter by session type"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Page size"),
    appointment_service: AppointmentService = Depends(get_appointment_service),
    current_user = Depends(get_current_user)
):
    """
    List appointments with filtering and pagination.
    
    Access control:
    - Managers: Can view all appointments
    - Tutors: Can only view their own appointments
    - Clients: Can only view their own appointments
    """
    try:
        # Role-based filtering
        if UserRoleType.MANAGER not in current_user.roles:
            if UserRoleType.TUTOR in current_user.roles:
                tutor_id = current_user.user_id  # Force filter to tutor's own appointments
            elif UserRoleType.CLIENT in current_user.roles:
                client_id = current_user.user_id  # Force filter to client's own appointments
            else:
                raise HTTPException(status_code=403, detail="Insufficient permissions")
        
        # Set default date range if not provided
        if not start_date:
            start_date = date.today()
        if not end_date:
            end_date = start_date + timedelta(days=30)
        
        # Validate date range
        if end_date < start_date:
            raise HTTPException(status_code=400, detail="End date must be after start date")
        
        if (end_date - start_date).days > 365:
            raise HTTPException(status_code=400, detail="Date range cannot exceed 365 days")
        
        # Get appointments
        appointments = await appointment_service.list_appointments(
            start_date=start_date,
            end_date=end_date,
            tutor_id=tutor_id,
            client_id=client_id,
            status=status,
            session_type=session_type,
            page=page,
            page_size=page_size
        )
        
        return appointments
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error listing appointments: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve appointments")


@router.post("/bulk-update")
async def bulk_update_appointments(
    request: BulkUpdateRequest,
    appointment_service: AppointmentService = Depends(get_appointment_service),
    current_user = Depends(get_current_user)
):
    """
    Bulk update multiple appointments.
    
    Only managers can perform bulk updates.
    """
    try:
        # Only managers can perform bulk operations
        require_role(current_user, [UserRoleType.MANAGER])
        
        # Validate appointment IDs
        if len(request.appointment_ids) > 50:
            raise HTTPException(
                status_code=400,
                detail="Cannot update more than 50 appointments at once"
            )
        
        # Perform bulk update
        results = await appointment_service.bulk_update_appointments(
            appointment_ids=request.appointment_ids,
            updates=request.updates.dict(exclude_unset=True),
            updated_by=current_user.user_id
        )
        
        logger.info(f"Bulk update performed by user {current_user.user_id} on {len(request.appointment_ids)} appointments")
        
        return {
            "message": "Bulk update completed",
            "updated_count": results['updated_count'],
            "failed_count": results['failed_count'],
            "errors": results.get('errors', [])
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error performing bulk update: {e}")
        raise HTTPException(status_code=500, detail="Failed to perform bulk update")


@router.get("/stats/summary")
async def get_appointment_stats(
    start_date: Optional[date] = Query(None, description="Stats start date"),
    end_date: Optional[date] = Query(None, description="Stats end date"),
    appointment_service: AppointmentService = Depends(get_appointment_service),
    current_user = Depends(get_current_user)
):
    """
    Get appointment statistics summary.
    
    Only managers can access global statistics.
    Tutors and clients get their own statistics.
    """
    try:
        # Set default date range
        if not end_date:
            end_date = date.today()
        if not start_date:
            start_date = end_date - timedelta(days=30)
        
        # Role-based stats
        if UserRoleType.MANAGER in current_user.roles:
            stats = await appointment_service.get_global_stats(start_date, end_date)
        elif UserRoleType.TUTOR in current_user.roles:
            stats = await appointment_service.get_tutor_stats(current_user.user_id, start_date, end_date)
        elif UserRoleType.CLIENT in current_user.roles:
            stats = await appointment_service.get_client_stats(current_user.user_id, start_date, end_date)
        else:
            raise HTTPException(status_code=403, detail="Insufficient permissions")
        
        return stats
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting appointment stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve statistics")


@router.patch("/{appointment_id}/complete")
async def complete_appointment(
    appointment_id: int = Path(..., gt=0, description="ID of the appointment"),
    actual_duration_minutes: Optional[int] = Query(None, ge=15, le=480, description="Actual duration in minutes"),
    notes: Optional[str] = Query(None, max_length=500, description="Completion notes"),
    tutor_no_show: bool = Query(False, description="Whether tutor was a no-show"),
    client_no_show: bool = Query(False, description="Whether client was a no-show"),
    send_sms_confirmation: bool = Query(True, description="Whether to send SMS confirmation to tutor"),
    appointment_service: AppointmentService = Depends(get_appointment_service),
    appointment = Depends(validate_appointment_access),
    current_user = Depends(get_current_user)
):
    """
    Complete an appointment and trigger billing.
    
    Access control:
    - Managers: Can complete all appointments
    - Tutors: Can complete their own appointments
    
    This endpoint handles the appointment completion workflow:
    1. Validates the appointment can be completed
    2. Updates status to completed or no-show
    3. Triggers billing process for invoicing
    4. Sends confirmation SMS to tutor for verification
    """
    try:
        # Only managers and tutors can complete appointments
        require_role(current_user, [UserRoleType.MANAGER, UserRoleType.TUTOR])
        
        # If tutor role, can only complete their own appointments
        if (UserRoleType.TUTOR in current_user.roles and 
            UserRoleType.MANAGER not in current_user.roles and 
            appointment.tutor_id != current_user.user_id):
            raise HTTPException(
                status_code=403,
                detail="Tutors can only complete their own appointments"
            )
        
        # Validate appointment can be completed
        if appointment.status not in [AppointmentStatus.SCHEDULED, AppointmentStatus.CONFIRMED, AppointmentStatus.IN_PROGRESS]:
            raise HTTPException(
                status_code=400,
                detail=f"Cannot complete appointment with status: {appointment.status}"
            )
        
        # Validate appointment time has passed
        appointment_end = datetime.combine(appointment.scheduled_date, appointment.end_time)
        if appointment_end > datetime.now():
            raise HTTPException(
                status_code=400,
                detail="Cannot complete appointment before scheduled end time"
            )
        
        # Validate completion notes if provided
        if notes:
            notes = DataValidator.validate_text_field(notes, "notes", max_length=500)
        
        # Determine final status
        if tutor_no_show or client_no_show:
            final_status = AppointmentStatus.NO_SHOW
        else:
            final_status = AppointmentStatus.COMPLETED
        
        # Complete appointment with enhanced service method
        result = await appointment_service.complete_appointment_with_confirmation(
            appointment_id=appointment_id,
            status=final_status,
            actual_duration_minutes=actual_duration_minutes,
            completion_notes=notes,
            tutor_no_show=tutor_no_show,
            client_no_show=client_no_show,
            completed_by=current_user.user_id,
            send_sms_confirmation=send_sms_confirmation
        )
        
        # Send WebSocket notification
        await notify_appointment_change(
            action="completed",
            appointment_data=result['appointment'],
            user_id=current_user.user_id,
            additional_data={
                'status': final_status.value,
                'tutor_no_show': tutor_no_show,
                'client_no_show': client_no_show,
                'sms_confirmation_sent': result.get('sms_sent', False),
                'billing_triggered': result.get('billing_triggered', False)
            }
        )
        
        logger.info(f"Appointment {appointment_id} completed as {final_status} by user {current_user.user_id}")
        
        return {
            "message": f"Appointment {final_status.value} successfully",
            "appointment_id": appointment_id,
            "status": final_status.value,
            "billing_triggered": result.get('billing_triggered', False),
            "sms_sent_to_tutor": result.get('sms_sent', False),
            "confirmation_token": result.get('confirmation_token'),
            "actual_duration_minutes": actual_duration_minutes
        }
        
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error completing appointment {appointment_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to complete appointment")


@router.post("/webhook/sms")
async def process_sms_webhook(
    From: str = Query(..., description="Sender's phone number"),
    Body: str = Query(..., description="SMS message body"),
    MessageSid: str = Query(..., description="Twilio message SID"),
    appointment_service: AppointmentService = Depends(get_appointment_service),
    confirmation_service = Depends(get_appointment_confirmation_service)
):
    """
    Process incoming SMS from Twilio webhook.
    
    This endpoint handles various SMS responses:
    - YES/NO - Appointment confirmation responses
    - DONE/COMPLETED - Appointment completion confirmations
    - NOSHOW - Client no-show notifications
    - General responses are forwarded to customer service
    
    Twilio webhook format includes:
    - From: Sender's phone number
    - Body: SMS message content
    - MessageSid: Unique message identifier
    """
    try:
        # Normalize response
        response_body = Body.strip().upper()
        phone_number = From
        
        # Log incoming SMS
        logger.info(f"Incoming SMS from {phone_number}: {response_body[:50]}...")
        
        # Extract any confirmation token if present
        import re
        token_match = re.search(r'[A-Z0-9]{6,8}', response_body)
        confirmation_token = token_match.group(0) if token_match else None
        
        result = None
        
        # Process different types of responses
        if response_body.startswith(('YES', 'Y', 'CONFIRM', 'OUI', 'O')):
            # Appointment confirmation
            if confirmation_token:
                result = await confirmation_service.process_tutor_response(
                    confirmation_token=confirmation_token,
                    response='YES',
                    metadata={'phone_number': phone_number, 'message_sid': MessageSid}
                )
        
        elif response_body.startswith(('NO', 'N', 'DECLINE', 'NON', 'CANCEL')):
            # Appointment decline
            if confirmation_token:
                result = await confirmation_service.process_tutor_response(
                    confirmation_token=confirmation_token,
                    response='NO',
                    metadata={'phone_number': phone_number, 'message_sid': MessageSid}
                )
        
        elif response_body.startswith(('DONE', 'COMPLETED', 'TERMINE', 'FINI')):
            # Appointment completion confirmation
            result = await appointment_service.process_completion_confirmation(
                phone_number=phone_number,
                response='COMPLETED',
                message_sid=MessageSid
            )
        
        elif response_body.startswith(('NOSHOW', 'NO SHOW', 'ABSENT')):
            # Client no-show report
            result = await appointment_service.process_completion_confirmation(
                phone_number=phone_number,
                response='NOSHOW',
                message_sid=MessageSid
            )
        
        else:
            # Forward to customer service for manual handling
            from app.services.sms_conversation_service import SMSConversationService
            sms_service = SMSConversationService()
            result = await sms_service.process_general_message(
                phone_number=phone_number,
                message=Body,
                message_sid=MessageSid
            )
        
        # Twilio expects a TwiML response
        # Empty response means no auto-reply
        from fastapi.responses import PlainTextResponse
        return PlainTextResponse(
            content='<?xml version="1.0" encoding="UTF-8"?><Response></Response>',
            media_type="application/xml"
        )
        
    except Exception as e:
        logger.error(f"Error processing SMS webhook: {e}")
        # Return empty TwiML response even on error
        from fastapi.responses import PlainTextResponse
        return PlainTextResponse(
            content='<?xml version="1.0" encoding="UTF-8"?><Response></Response>',
            media_type="application/xml"
        )


@router.post("/{appointment_id}/complete")
async def complete_appointment(
    appointment_id: int,
    request: AppointmentCompletion,
    current_user = Depends(get_current_user),
    appointment = Depends(validate_appointment_access),
    duration_service: AppointmentDurationService = Depends(get_duration_service)
):
    """
    Complete an appointment with optional duration adjustment.
    
    This endpoint allows managers and tutors to mark an appointment as completed
    and optionally adjust the actual duration if it differed from the scheduled time.
    
    Access control:
    - Managers: Can complete any appointment
    - Tutors: Can complete their own appointments
    """
    try:
        # Authorization check
        require_role(current_user, [UserRoleType.MANAGER, UserRoleType.TUTOR])
        
        # If tutor, ensure they can only complete their own appointments
        if (UserRoleType.TUTOR in current_user.roles and 
            UserRoleType.MANAGER not in current_user.roles and
            appointment.tutor_id != current_user.user_id):
            raise HTTPException(
                status_code=403,
                detail="Tutors can only complete their own appointments"
            )
        
        # Validate input
        if request.completion_notes:
            request.completion_notes = DataValidator.validate_text_field(
                request.completion_notes, "completion_notes", max_length=1000
            )
        
        # Complete appointment with duration service
        result = await duration_service.complete_appointment_with_duration(
            appointment_id=appointment_id,
            completion_request=request,
            completed_by=current_user.user_id
        )
        
        # Send WebSocket notification
        await notify_appointment_change(
            action="completed",
            appointment_data=result['appointment'],
            user_id=current_user.user_id,
            additional_data={
                'billing_result': result['billing_result'],
                'duration_adjusted': result['appointment'].duration_adjusted
            }
        )
        
        logger.info(
            f"Appointment {appointment_id} completed by user {current_user.user_id} "
            f"with duration adjustment: {result['appointment'].duration_adjusted}"
        )
        
        return {
            "message": "Appointment completed successfully",
            "appointment": result['appointment'],
            "billing_result": result['billing_result'],
            "duration_adjusted": result['appointment'].duration_adjusted
        }
        
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error completing appointment {appointment_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to complete appointment")


@router.post("/{appointment_id}/adjust-duration")
async def adjust_appointment_duration(
    appointment_id: int,
    request: DurationAdjustmentRequest,
    duration_service: AppointmentDurationService = Depends(get_duration_service),
    appointment = Depends(validate_appointment_access),
    current_user = Depends(get_current_user)
):
    """
    Adjust the duration of a completed appointment.
    
    This endpoint allows for post-completion duration adjustments when the actual
    session time differed from the scheduled time. It automatically recalculates
    billing based on the actual duration.
    
    Access control:
    - Managers: Can adjust any appointment
    - Tutors: Can adjust their own appointments (within 24 hours)
    """
    try:
        # Authorization check
        require_role(current_user, [UserRoleType.MANAGER, UserRoleType.TUTOR])
        
        # If tutor, additional restrictions apply
        if (UserRoleType.TUTOR in current_user.roles and 
            UserRoleType.MANAGER not in current_user.roles):
            # Can only adjust their own appointments
            if appointment.tutor_id != current_user.user_id:
                raise HTTPException(
                    status_code=403,
                    detail="Tutors can only adjust duration for their own appointments"
                )
            
            # Can only adjust within 24 hours of completion
            if appointment.completed_at:
                hours_since_completion = (
                    datetime.now() - appointment.completed_at
                ).total_seconds() / 3600
                if hours_since_completion > 24:
                    raise HTTPException(
                        status_code=403,
                        detail="Tutors can only adjust duration within 24 hours of completion"
                    )
        
        # Validate adjustment reason
        request.adjustment_reason = DataValidator.validate_text_field(
            request.adjustment_reason, "adjustment_reason", min_length=5, max_length=500
        )
        
        # Adjust duration
        result = await duration_service.adjust_appointment_duration(
            appointment_id=appointment_id,
            adjustment_request=request,
            adjusted_by=current_user.user_id
        )
        
        # Send WebSocket notification
        await notify_appointment_change(
            action="duration_adjusted",
            appointment_data=result['appointment'],
            user_id=current_user.user_id,
            additional_data={
                'billing_impact': result['billing_impact'],
                'duration_difference_minutes': result['duration_difference_minutes']
            }
        )
        
        logger.info(
            f"Duration adjusted for appointment {appointment_id} by user {current_user.user_id}: "
            f"{result['original_duration_minutes']} -> {result['actual_duration_minutes']} minutes"
        )
        
        return {
            "message": "Duration adjusted successfully",
            "appointment": result['appointment'],
            "billing_impact": result['billing_impact'],
            "original_duration_minutes": result['original_duration_minutes'],
            "actual_duration_minutes": result['actual_duration_minutes'],
            "duration_difference_minutes": result['duration_difference_minutes']
        }
        
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except BusinessLogicError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adjusting duration for appointment {appointment_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to adjust appointment duration")


@router.get("/{appointment_id}/duration-history")
async def get_duration_adjustment_history(
    appointment_id: int = Path(..., gt=0, description="ID of the appointment"),
    duration_service: AppointmentDurationService = Depends(get_duration_service),
    appointment = Depends(validate_appointment_access),
    current_user = Depends(get_current_user)
):
    """
    Get duration adjustment history for an appointment.
    
    Returns all duration adjustments made to the appointment, including who made
    them, when, and the billing impact.
    
    Access control: Same as appointment access
    """
    try:
        history = await duration_service.get_duration_adjustment_history(appointment_id)
        
        return {
            "appointment_id": appointment_id,
            "adjustments": history,
            "total_adjustments": len(history)
        }
        
    except Exception as e:
        logger.error(f"Error retrieving duration history for appointment {appointment_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve duration history")


@router.get("/{appointment_id}/audit-logs")
async def get_appointment_audit_logs(
    appointment_id: int = Path(..., gt=0, description="ID of the appointment"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of logs to return"),
    offset: int = Query(0, ge=0, description="Number of logs to skip"),
    appointment_service: AppointmentService = Depends(get_appointment_service),
    appointment = Depends(validate_appointment_access),
    current_user = Depends(get_current_user)
):
    """
    Get audit logs for an appointment.
    
    Returns a chronological list of all changes made to the appointment,
    including who made them, when, and what changed.
    
    Access control: Same as appointment access (managers, involved tutor/client)
    """
    try:
        logs = await appointment_service.get_appointment_audit_logs(
            appointment_id=appointment_id,
            limit=limit,
            offset=offset
        )
        
        return {
            "appointment_id": appointment_id,
            "audit_logs": logs,
            "total_logs": len(logs),
            "limit": limit,
            "offset": offset
        }
        
    except Exception as e:
        logger.error(f"Error retrieving audit logs for appointment {appointment_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve audit logs")


# ============================================
# Recurring Appointment Endpoints
# ============================================

@router.post("/recurring", response_model=Dict[str, Any])
async def create_recurring_series(
    request: RecurringAppointmentCreate,
    appointment_service: AppointmentService = Depends(get_appointment_service),
    current_user = Depends(get_current_user)
):
    """
    Create a recurring appointment series.
    
    This endpoint creates a template for recurring appointments and generates
    individual appointments based on the recurrence pattern.
    
    Access control:
    - Managers: Can create recurring series for any tutor/client
    - Clients: Can create recurring series for themselves
    """
    try:
        # Authorization check
        if UserRoleType.MANAGER not in current_user.roles:
            # Clients can only create for themselves
            if UserRoleType.CLIENT in current_user.roles:
                if request.client_id != current_user.user_id:
                    raise HTTPException(
                        status_code=403,
                        detail="Clients can only create recurring appointments for themselves"
                    )
            else:
                raise HTTPException(status_code=403, detail="Insufficient permissions")
        
        # Import the recurring service
        from app.services.appointment_service import RecurringAppointmentService
        recurring_service = RecurringAppointmentService()
        
        # Create recurring series
        result = await recurring_service.create_recurring_series({
            "tutor_id": request.tutor_id,
            "client_id": request.client_id,
            "dependant_id": request.dependant_id,
            "day_of_week": request.day_of_week,
            "start_time": request.start_time,
            "end_time": request.end_time,
            "subject_area": request.subject_area,
            "location_details": request.location_details.dict() if hasattr(request.location_details, 'dict') else request.location_details,
            "hourly_rate": request.hourly_rate,
            "currency": "CAD",
            "start_date": request.start_date,
            "end_date": request.end_date,
            "frequency_weeks": request.frequency_weeks or 1,
            "is_active": True,
            "created_by": current_user.user_id,
            "created_at": datetime.now()
        })
        
        logger.info(f"Created recurring series {result['recurring_id']} by user {current_user.user_id}")
        
        return result
        
    except BusinessLogicError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating recurring series: {e}")
        raise HTTPException(status_code=500, detail="Failed to create recurring series")


@router.get("/recurring")
async def list_recurring_series(
    tutor_id: Optional[int] = Query(None, gt=0, description="Filter by tutor ID"),
    client_id: Optional[int] = Query(None, gt=0, description="Filter by client ID"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Page size"),
    current_user = Depends(get_current_user),
    db = Depends(get_db_connection)
):
    """
    List recurring appointment series.
    
    Access control:
    - Managers: Can view all recurring series
    - Tutors: Can only view their own recurring series
    - Clients: Can only view their own recurring series
    """
    try:
        # Role-based filtering
        if UserRoleType.MANAGER not in current_user.roles:
            if UserRoleType.TUTOR in current_user.roles:
                tutor_id = current_user.user_id  # Force filter to tutor's own series
            elif UserRoleType.CLIENT in current_user.roles:
                client_id = current_user.user_id  # Force filter to client's own series
            else:
                raise HTTPException(status_code=403, detail="Insufficient permissions")
        
        # Build query
        query = """
            SELECT 
                r.*,
                t.first_name || ' ' || t.last_name as tutor_name,
                c.first_name || ' ' || c.last_name as client_name,
                d.first_name || ' ' || d.last_name as dependant_name,
                COUNT(DISTINCT a.appointment_id) as total_appointments,
                COUNT(DISTINCT a.appointment_id) FILTER (WHERE a.status = 'completed') as completed_appointments,
                COUNT(DISTINCT a.appointment_id) FILTER (WHERE a.status IN ('scheduled', 'confirmed') AND a.scheduled_date >= CURRENT_DATE) as upcoming_appointments,
                COUNT(DISTINCT a.appointment_id) FILTER (WHERE a.status = 'cancelled') as cancelled_appointments
            FROM recurring_appointments r
            JOIN tutor_profiles t ON r.tutor_id = t.tutor_id
            JOIN client_profiles c ON r.client_id = c.client_id
            LEFT JOIN client_dependants d ON r.dependant_id = d.dependant_id
            LEFT JOIN appointment_sessions a ON r.recurring_id = a.recurring_appointment_id
            WHERE r.deleted_at IS NULL
        """
        
        conditions = []
        params = []
        param_count = 0
        
        if tutor_id:
            param_count += 1
            conditions.append(f"r.tutor_id = ${param_count}")
            params.append(tutor_id)
        
        if client_id:
            param_count += 1
            conditions.append(f"r.client_id = ${param_count}")
            params.append(client_id)
        
        if is_active is not None:
            param_count += 1
            conditions.append(f"r.is_active = ${param_count}")
            params.append(is_active)
        
        if conditions:
            query += " AND " + " AND ".join(conditions)
        
        query += " GROUP BY r.recurring_id, t.first_name, t.last_name, c.first_name, c.last_name, d.first_name, d.last_name"
        
        # Count total
        count_query = f"SELECT COUNT(DISTINCT r.recurring_id) FROM recurring_appointments r WHERE r.deleted_at IS NULL"
        if conditions:
            count_query += " AND " + " AND ".join(conditions)
        
        total_count = await db.fetchval(count_query, *params)
        
        # Add pagination
        offset = (page - 1) * page_size
        param_count += 1
        params.append(page_size)
        param_count += 1
        params.append(offset)
        
        query += f" ORDER BY r.created_at DESC LIMIT ${param_count-1} OFFSET ${param_count}"
        
        rows = await db.fetch(query, *params)
        
        # Convert to response format
        items = []
        for row in rows:
            items.append({
                "recurring_id": row['recurring_id'],
                "tutor_id": row['tutor_id'],
                "tutor_name": row['tutor_name'],
                "client_id": row['client_id'],
                "client_name": row['client_name'],
                "dependant_id": row['dependant_id'],
                "dependant_name": row['dependant_name'],
                "day_of_week": row['day_of_week'],
                "start_time": str(row['start_time']),
                "end_time": str(row['end_time']),
                "subject_area": row['subject_area'],
                "location_details": row['location_details'],
                "hourly_rate": float(row['hourly_rate']),
                "currency": row['currency'],
                "start_date": row['start_date'].isoformat(),
                "end_date": row['end_date'].isoformat() if row['end_date'] else None,
                "is_active": row['is_active'],
                "frequency_weeks": row['frequency_weeks'],
                "total_appointments": row['total_appointments'],
                "completed_appointments": row['completed_appointments'],
                "upcoming_appointments": row['upcoming_appointments'],
                "cancelled_appointments": row['cancelled_appointments']
            })
        
        return {
            "items": items,
            "total": total_count,
            "page": page,
            "page_size": page_size,
            "total_pages": (total_count + page_size - 1) // page_size
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error listing recurring series: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve recurring series")


@router.get("/recurring/{recurring_id}")
async def get_recurring_series(
    recurring_id: int = Path(..., gt=0, description="Recurring series ID"),
    current_user = Depends(get_current_user),
    db = Depends(get_db_connection)
):
    """
    Get details of a specific recurring appointment series.
    
    Access control: Same as appointment access
    """
    try:
        # Get recurring series
        query = """
            SELECT 
                r.*,
                t.first_name || ' ' || t.last_name as tutor_name,
                c.first_name || ' ' || c.last_name as client_name,
                d.first_name || ' ' || d.last_name as dependant_name
            FROM recurring_appointments r
            JOIN tutor_profiles t ON r.tutor_id = t.tutor_id
            JOIN client_profiles c ON r.client_id = c.client_id
            LEFT JOIN client_dependants d ON r.dependant_id = d.dependant_id
            WHERE r.recurring_id = $1 AND r.deleted_at IS NULL
        """
        
        series = await db.fetchrow(query, recurring_id)
        
        if not series:
            raise HTTPException(status_code=404, detail="Recurring series not found")
        
        # Check access
        if UserRoleType.MANAGER not in current_user.roles:
            if UserRoleType.TUTOR in current_user.roles and series['tutor_id'] != current_user.user_id:
                raise HTTPException(status_code=403, detail="Access denied")
            elif UserRoleType.CLIENT in current_user.roles and series['client_id'] != current_user.user_id:
                raise HTTPException(status_code=403, detail="Access denied")
        
        return {
            "recurring_id": series['recurring_id'],
            "tutor_id": series['tutor_id'],
            "tutor_name": series['tutor_name'],
            "client_id": series['client_id'],
            "client_name": series['client_name'],
            "dependant_id": series['dependant_id'],
            "dependant_name": series['dependant_name'],
            "day_of_week": series['day_of_week'],
            "start_time": str(series['start_time']),
            "end_time": str(series['end_time']),
            "subject_area": series['subject_area'],
            "location_details": series['location_details'],
            "hourly_rate": float(series['hourly_rate']),
            "currency": series['currency'],
            "start_date": series['start_date'].isoformat(),
            "end_date": series['end_date'].isoformat() if series['end_date'] else None,
            "is_active": series['is_active'],
            "frequency_weeks": series['frequency_weeks']
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving recurring series {recurring_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve recurring series")


@router.put("/recurring/{recurring_id}")
async def update_recurring_series(
    recurring_id: int = Path(..., gt=0, description="Recurring series ID"),
    request: UpdateRecurringRequest = None,
    current_user = Depends(get_current_user),
    db = Depends(get_db_connection)
):
    """
    Update a recurring appointment series.
    
    The update_mode parameter determines how the update is applied:
    - 'this_only': Updates only affect future appointments from today
    - 'future': Updates affect all future appointments in the series
    - 'all': Updates affect all appointments in the series
    
    Access control: Same as appointment update
    """
    try:
        # Get recurring series
        series_query = "SELECT * FROM recurring_appointments WHERE recurring_id = $1 AND deleted_at IS NULL"
        series = await db.fetchrow(series_query, recurring_id)
        
        if not series:
            raise HTTPException(status_code=404, detail="Recurring series not found")
        
        # Check access
        if UserRoleType.MANAGER not in current_user.roles:
            if UserRoleType.TUTOR in current_user.roles and series['tutor_id'] != current_user.user_id:
                raise HTTPException(status_code=403, detail="Tutors can only update their own recurring series")
            elif UserRoleType.CLIENT in current_user.roles:
                raise HTTPException(status_code=403, detail="Clients cannot update recurring series")
        
        # Build update query
        update_fields = []
        params = []
        param_count = 0
        
        if request.day_of_week is not None:
            param_count += 1
            update_fields.append(f"day_of_week = ${param_count}")
            params.append(request.day_of_week)
        
        if request.start_time is not None:
            param_count += 1
            update_fields.append(f"start_time = ${param_count}")
            params.append(request.start_time)
        
        if request.end_time is not None:
            param_count += 1
            update_fields.append(f"end_time = ${param_count}")
            params.append(request.end_time)
        
        if request.subject_area is not None:
            param_count += 1
            update_fields.append(f"subject_area = ${param_count}")
            params.append(request.subject_area)
        
        if request.location_details is not None:
            param_count += 1
            update_fields.append(f"location_details = ${param_count}")
            params.append(request.location_details.dict() if hasattr(request.location_details, 'dict') else request.location_details)
        
        if request.hourly_rate is not None:
            param_count += 1
            update_fields.append(f"hourly_rate = ${param_count}")
            params.append(request.hourly_rate)
        
        if request.end_date is not None:
            param_count += 1
            update_fields.append(f"end_date = ${param_count}")
            params.append(request.end_date)
        
        if request.is_active is not None:
            param_count += 1
            update_fields.append(f"is_active = ${param_count}")
            params.append(request.is_active)
        
        # Add metadata
        param_count += 1
        update_fields.append(f"updated_at = ${param_count}")
        params.append(datetime.now())
        
        param_count += 1
        update_fields.append(f"updated_by = ${param_count}")
        params.append(current_user.user_id)
        
        # Add recurring_id
        param_count += 1
        params.append(recurring_id)
        
        # Update recurring series
        update_query = f"""
            UPDATE recurring_appointments 
            SET {', '.join(update_fields)}
            WHERE recurring_id = ${param_count}
            RETURNING *
        """
        
        await db.execute(update_query, *params)
        
        # Handle update mode for existing appointments
        if request.update_mode == 'all':
            # Update all appointments in the series
            appointment_update_query = """
                UPDATE appointment_sessions
                SET subject_area = COALESCE($1, subject_area),
                    location_details = COALESCE($2, location_details),
                    hourly_rate = COALESCE($3, hourly_rate),
                    updated_at = $4,
                    updated_by = $5
                WHERE recurring_appointment_id = $6
                AND status IN ('scheduled', 'confirmed')
            """
            await db.execute(
                appointment_update_query,
                request.subject_area,
                request.location_details.dict() if request.location_details and hasattr(request.location_details, 'dict') else request.location_details,
                request.hourly_rate,
                datetime.now(),
                current_user.user_id,
                recurring_id
            )
        elif request.update_mode == 'future':
            # Update only future appointments
            appointment_update_query = """
                UPDATE appointment_sessions
                SET subject_area = COALESCE($1, subject_area),
                    location_details = COALESCE($2, location_details),
                    hourly_rate = COALESCE($3, hourly_rate),
                    updated_at = $4,
                    updated_by = $5
                WHERE recurring_appointment_id = $6
                AND status IN ('scheduled', 'confirmed')
                AND scheduled_date >= CURRENT_DATE
            """
            await db.execute(
                appointment_update_query,
                request.subject_area,
                request.location_details.dict() if request.location_details and hasattr(request.location_details, 'dict') else request.location_details,
                request.hourly_rate,
                datetime.now(),
                current_user.user_id,
                recurring_id
            )
        
        logger.info(f"Updated recurring series {recurring_id} by user {current_user.user_id} with mode {request.update_mode}")
        
        return {"message": "Recurring series updated successfully", "recurring_id": recurring_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating recurring series {recurring_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to update recurring series")


@router.delete("/recurring/{recurring_id}")
async def cancel_recurring_series(
    recurring_id: int = Path(..., gt=0, description="Recurring series ID"),
    mode: str = Query(..., description="Cancellation mode: 'future', 'all', or 'this_only'"),
    reason: str = Query(..., min_length=5, max_length=200, description="Cancellation reason"),
    current_user = Depends(get_current_user),
    db = Depends(get_db_connection)
):
    """
    Cancel a recurring appointment series.
    
    Cancellation modes:
    - 'future': Cancel all future appointments from today
    - 'all': Cancel all appointments in the series
    - 'this_only': Deactivate the series but keep existing appointments
    
    Access control: Same as appointment cancellation
    """
    try:
        # Validate mode
        if mode not in ['future', 'all', 'this_only']:
            raise HTTPException(status_code=400, detail="Invalid cancellation mode")
        
        # Get recurring series
        series_query = "SELECT * FROM recurring_appointments WHERE recurring_id = $1 AND deleted_at IS NULL"
        series = await db.fetchrow(series_query, recurring_id)
        
        if not series:
            raise HTTPException(status_code=404, detail="Recurring series not found")
        
        # Check access
        if UserRoleType.MANAGER not in current_user.roles:
            if UserRoleType.TUTOR in current_user.roles and series['tutor_id'] != current_user.user_id:
                raise HTTPException(status_code=403, detail="Tutors can only cancel their own recurring series")
            elif UserRoleType.CLIENT in current_user.roles and series['client_id'] != current_user.user_id:
                raise HTTPException(status_code=403, detail="Clients can only cancel their own recurring series")
        
        # Validate cancellation reason
        reason = DataValidator.validate_text_field(reason, "reason", min_length=5, max_length=200)
        
        async with db.transaction():
            if mode == 'all':
                # Cancel all appointments in the series
                cancel_query = """
                    UPDATE appointment_sessions
                    SET status = 'cancelled',
                        cancellation_reason = $1,
                        cancelled_by = $2,
                        cancelled_at = $3,
                        updated_at = $3
                    WHERE recurring_appointment_id = $4
                    AND status IN ('scheduled', 'confirmed')
                """
                await db.execute(cancel_query, reason, current_user.user_id, datetime.now(), recurring_id)
                
                # Deactivate the series
                deactivate_query = """
                    UPDATE recurring_appointments
                    SET is_active = false,
                        updated_at = $1,
                        updated_by = $2
                    WHERE recurring_id = $3
                """
                await db.execute(deactivate_query, datetime.now(), current_user.user_id, recurring_id)
                
            elif mode == 'future':
                # Cancel only future appointments
                cancel_query = """
                    UPDATE appointment_sessions
                    SET status = 'cancelled',
                        cancellation_reason = $1,
                        cancelled_by = $2,
                        cancelled_at = $3,
                        updated_at = $3
                    WHERE recurring_appointment_id = $4
                    AND status IN ('scheduled', 'confirmed')
                    AND scheduled_date >= CURRENT_DATE
                """
                await db.execute(cancel_query, reason, current_user.user_id, datetime.now(), recurring_id)
                
                # Update series end date to today
                update_end_date_query = """
                    UPDATE recurring_appointments
                    SET end_date = CURRENT_DATE - INTERVAL '1 day',
                        is_active = false,
                        updated_at = $1,
                        updated_by = $2
                    WHERE recurring_id = $3
                """
                await db.execute(update_end_date_query, datetime.now(), current_user.user_id, recurring_id)
                
            else:  # this_only
                # Just deactivate the series, don't cancel appointments
                deactivate_query = """
                    UPDATE recurring_appointments
                    SET is_active = false,
                        updated_at = $1,
                        updated_by = $2
                    WHERE recurring_id = $3
                """
                await db.execute(deactivate_query, datetime.now(), current_user.user_id, recurring_id)
        
        logger.info(f"Cancelled recurring series {recurring_id} by user {current_user.user_id} with mode {mode}")
        
        return {"message": f"Recurring series cancelled successfully with mode: {mode}", "recurring_id": recurring_id}
        
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cancelling recurring series {recurring_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to cancel recurring series")


@router.patch("/recurring/{recurring_id}/toggle")
async def toggle_recurring_series(
    recurring_id: int = Path(..., gt=0, description="Recurring series ID"),
    is_active: bool = Query(..., description="New active status"),
    current_user = Depends(get_current_user),
    db = Depends(get_db_connection)
):
    """
    Pause or resume a recurring appointment series.
    
    This endpoint allows temporarily pausing a series without cancelling appointments.
    
    Access control: Same as appointment update
    """
    try:
        # Get recurring series
        series_query = "SELECT * FROM recurring_appointments WHERE recurring_id = $1 AND deleted_at IS NULL"
        series = await db.fetchrow(series_query, recurring_id)
        
        if not series:
            raise HTTPException(status_code=404, detail="Recurring series not found")
        
        # Check access
        if UserRoleType.MANAGER not in current_user.roles:
            if UserRoleType.TUTOR in current_user.roles and series['tutor_id'] != current_user.user_id:
                raise HTTPException(status_code=403, detail="Tutors can only toggle their own recurring series")
            elif UserRoleType.CLIENT in current_user.roles:
                raise HTTPException(status_code=403, detail="Clients cannot toggle recurring series")
        
        # Update active status
        update_query = """
            UPDATE recurring_appointments
            SET is_active = $1,
                updated_at = $2,
                updated_by = $3
            WHERE recurring_id = $4
            RETURNING is_active
        """
        
        result = await db.fetchrow(update_query, is_active, datetime.now(), current_user.user_id, recurring_id)
        
        logger.info(f"Toggled recurring series {recurring_id} to active={is_active} by user {current_user.user_id}")
        
        return {
            "message": f"Recurring series {'resumed' if is_active else 'paused'} successfully",
            "recurring_id": recurring_id,
            "is_active": result['is_active']
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error toggling recurring series {recurring_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to toggle recurring series")


@router.get("/recurring/{recurring_id}/appointments")
async def get_series_appointments(
    recurring_id: int = Path(..., gt=0, description="Recurring series ID"),
    status: Optional[AppointmentStatus] = Query(None, description="Filter by status"),
    start_date: Optional[date] = Query(None, description="Filter by start date"),
    end_date: Optional[date] = Query(None, description="Filter by end date"),
    current_user = Depends(get_current_user),
    db = Depends(get_db_connection)
):
    """
    Get all appointments in a recurring series.
    
    Access control: Same as appointment list
    """
    try:
        # Get recurring series
        series_query = "SELECT * FROM recurring_appointments WHERE recurring_id = $1 AND deleted_at IS NULL"
        series = await db.fetchrow(series_query, recurring_id)
        
        if not series:
            raise HTTPException(status_code=404, detail="Recurring series not found")
        
        # Check access
        if UserRoleType.MANAGER not in current_user.roles:
            if UserRoleType.TUTOR in current_user.roles and series['tutor_id'] != current_user.user_id:
                raise HTTPException(status_code=403, detail="Access denied")
            elif UserRoleType.CLIENT in current_user.roles and series['client_id'] != current_user.user_id:
                raise HTTPException(status_code=403, detail="Access denied")
        
        # Build query
        query = """
            SELECT 
                a.*,
                t.first_name || ' ' || t.last_name as tutor_name,
                c.first_name || ' ' || c.last_name as client_name,
                d.first_name || ' ' || d.last_name as dependant_name
            FROM appointment_sessions a
            JOIN tutor_profiles t ON a.tutor_id = t.tutor_id
            JOIN client_profiles c ON a.client_id = c.client_id
            LEFT JOIN client_dependants d ON a.dependant_id = d.dependant_id
            WHERE a.recurring_appointment_id = $1
            AND a.deleted_at IS NULL
        """
        
        params = [recurring_id]
        param_count = 1
        
        if status:
            param_count += 1
            query += f" AND a.status = ${param_count}"
            params.append(status.value)
        
        if start_date:
            param_count += 1
            query += f" AND a.scheduled_date >= ${param_count}"
            params.append(start_date)
        
        if end_date:
            param_count += 1
            query += f" AND a.scheduled_date <= ${param_count}"
            params.append(end_date)
        
        query += " ORDER BY a.scheduled_date, a.start_time"
        
        appointments = await db.fetch(query, *params)
        
        # Convert to response format
        results = []
        for apt in appointments:
            results.append({
                "appointment_id": apt['appointment_id'],
                "tutor_id": apt['tutor_id'],
                "tutor_name": apt['tutor_name'],
                "client_id": apt['client_id'],
                "client_name": apt['client_name'],
                "dependant_id": apt['dependant_id'],
                "dependant_name": apt['dependant_name'],
                "scheduled_date": apt['scheduled_date'].isoformat(),
                "start_time": str(apt['start_time']),
                "end_time": str(apt['end_time']),
                "status": apt['status'],
                "subject_area": apt['subject_area'],
                "session_type": apt.get('session_type', 'individual'),
                "location_type": apt['location_details'].get('type') if apt['location_details'] else 'online',
                "location_details": apt['location_details'],
                "hourly_rate": float(apt['hourly_rate']),
                "currency": apt['currency'],
                "confirmed_by_tutor": apt.get('confirmed_by_tutor', False),
                "notes": apt.get('notes'),
                "created_at": apt['created_at'].isoformat() if apt['created_at'] else None,
                "updated_at": apt['updated_at'].isoformat() if apt['updated_at'] else None
            })
        
        return results
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving appointments for series {recurring_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve series appointments")