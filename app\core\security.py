from datetime import datetime, timedelta, timezone
from typing import Any, Union, Optional, Dict
from passlib.context import Crypt<PERSON>ontext
from jose import JWTError, jwt
import asyncpg
import logging

from app.config.settings import settings
from app.models.user_models import User
from app.core.timezone import now_est

logger = logging.getLogger(__name__)

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def create_access_token(
    subject: Union[str, Any], 
    expires_delta: timedelta = None,
    user_roles: list = None
) -> str:
    """Create access token."""
    if expires_delta:
        expire = now_est() + expires_delta
    else:
        expire = now_est() + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )
    
    to_encode = {
        "exp": expire,
        "sub": str(subject),
        "roles": user_roles or [],
        "type": "access"
    }
    
    encoded_jwt = jwt.encode(
        to_encode, 
        settings.SECRET_KEY, 
        algorithm=settings.ALGORITHM
    )
    return encoded_jwt


def create_refresh_token(
    subject: Union[str, Any],
    user_roles: list = None,
    primary_role: str = None,
    expires_delta: timedelta = None
) -> str:
    """Create refresh token with longer expiration."""
    if expires_delta:
        expire = now_est() + expires_delta
    else:
        # Refresh tokens last 7 days by default
        expire = now_est() + timedelta(days=7)
    
    to_encode = {
        "exp": expire,
        "sub": str(subject),
        "roles": user_roles or [],
        "primary_role": primary_role,
        "type": "refresh"
    }
    
    encoded_jwt = jwt.encode(
        to_encode,
        settings.SECRET_KEY,
        algorithm=settings.ALGORITHM
    )
    return encoded_jwt


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password."""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """Hash a password."""
    return pwd_context.hash(password)


def verify_token(token: str) -> Dict[str, Any]:
    """Verify and decode JWT token."""
    try:
        payload = jwt.decode(
            token, 
            settings.SECRET_KEY, 
            algorithms=[settings.ALGORITHM]
        )
        
        # Check if token is expired
        exp = payload.get("exp")
        if exp:
            # Create timezone-aware datetime from timestamp
            exp_datetime = datetime.fromtimestamp(exp, tz=timezone.utc)
            if exp_datetime < datetime.now(timezone.utc):
                raise JWTError("Token has expired")
            
        return payload
    except JWTError as e:
        logger.warning(f"Token verification failed: {e}")
        raise


async def get_current_user_from_token(
    payload: Dict[str, Any], 
    db: asyncpg.Connection
) -> Optional[User]:
    """Get user from token payload."""
    try:
        user_id = payload.get("sub")
        if user_id is None:
            return None
        
        # Query user from database
        query = """
            SELECT 
                user_id, email, first_name, last_name, 
                phone, is_email_verified, google_id,
                created_at, updated_at, deleted_at
            FROM user_accounts 
            WHERE user_id = $1 AND deleted_at IS NULL
        """
        
        user_row = await db.fetchrow(query, user_id)
        if not user_row:
            return None
        
        # Get user roles
        roles_query = """
            SELECT role_type 
            FROM user_roles 
            WHERE user_id = $1 AND is_active = true
        """
        roles_rows = await db.fetch(roles_query, user_id)
        roles = [row['role_type'] for row in roles_rows]
        
        # Create User object
        user = User(
            user_id=user_row['user_id'],
            email=user_row['email'],
            first_name=user_row['first_name'],
            last_name=user_row['last_name'],
            phone=user_row['phone'],
            is_email_verified=user_row['is_email_verified'],
            google_id=user_row['google_id'],
            roles=roles,
            created_at=user_row['created_at'],
            updated_at=user_row['updated_at'],
            deleted_at=user_row['deleted_at']
        )
        
        return user
        
    except Exception as e:
        logger.error(f"Error getting user from token: {e}")
        return None


def create_password_reset_token(user_id: str) -> str:
    """Create password reset token."""
    delta = timedelta(hours=1)  # Reset tokens expire in 1 hour
    return create_access_token(subject=user_id, expires_delta=delta)


def create_email_verification_token(user_id: str) -> str:
    """Create email verification token."""
    delta = timedelta(days=7)  # Email verification tokens expire in 7 days
    return create_access_token(subject=user_id, expires_delta=delta)