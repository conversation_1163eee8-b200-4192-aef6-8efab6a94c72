import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../contexts/AuthContext';
import { Card } from '../../components/common/Card';
import Button from '../../components/common/Button';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import { 
  User, 
  Phone, 
  Mail, 
  MapPin, 
  Calendar, 
  Globe, 
  Bell, 
  Shield,
  AlertCircle,
  Check
} from 'lucide-react';
import api from '../../services/api';
import toast from 'react-hot-toast';
import { PersonalInfoForm } from '../../components/client/profile/PersonalInfoForm';
import { EmergencyContactForm } from '../../components/client/profile/EmergencyContactForm';
import { CommunicationPreferences } from '../../components/client/profile/CommunicationPreferences';
import { AddressForm } from '../../components/client/profile/AddressForm';

interface ClientProfile {
  user_id: number;
  first_name: string;
  last_name: string;
  date_of_birth: string | null;
  phone_number: string;
  email: string;
  address_line_1: string | null;
  address_line_2: string | null;
  city: string | null;
  province: string | null;
  postal_code: string | null;
  emergency_contact_name: string | null;
  emergency_contact_phone: string | null;
  emergency_contact_relation: string | null;
  preferred_language: 'en' | 'fr';
  sms_notifications_enabled: boolean;
  email_notifications_enabled: boolean;
  push_notifications_enabled: boolean;
}

export const Profile: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [profile, setProfile] = useState<ClientProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState<'personal' | 'emergency' | 'communication' | 'address'>('personal');

  useEffect(() => {
    fetchProfile();
  }, []);

  const fetchProfile = async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      const response = await api.get(`/api/v1/clients/${user.id}/profile`);
      setProfile(response.data);
    } catch (error) {
      console.error('Error fetching profile:', error);
      toast.error(t('client.profile.fetchError'));
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateProfile = async (section: string, data: any) => {
    if (!user || !profile) return;
    
    try {
      setSaving(true);
      
      const endpoint = section === 'preferences' 
        ? `/api/v1/clients/${user.id}/preferences`
        : `/api/v1/clients/${user.id}/profile`;
        
      const response = await api.put(endpoint, data);
      
      setProfile(response.data);
      toast.success(t('client.profile.updateSuccess'));
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error(t('client.profile.updateError'));
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="p-6">
          <div className="flex items-center gap-3 text-amber-600">
            <AlertCircle className="w-5 h-5" />
            <p>{t('client.profile.notFound')}</p>
          </div>
        </Card>
      </div>
    );
  }

  const tabs = [
    { id: 'personal', label: t('client.profile.tabs.personal'), icon: User },
    { id: 'emergency', label: t('client.profile.tabs.emergency'), icon: Shield },
    { id: 'communication', label: t('client.profile.tabs.communication'), icon: Bell },
    { id: 'address', label: t('client.profile.tabs.address'), icon: MapPin },
  ];

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-text-primary mb-2">
          {t('client.profile.title')}
        </h1>
        <p className="text-text-secondary">
          {t('client.profile.subtitle')}
        </p>
      </div>

      {/* Profile Card */}
      <Card className="shadow-elevated">
        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6" aria-label="Tabs">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`
                    py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2
                    ${activeTab === tab.id
                      ? 'border-accent-red text-accent-red'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }
                  `}
                >
                  <Icon className="w-4 h-4" />
                  {tab.label}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'personal' && (
            <PersonalInfoForm
              profile={profile}
              onUpdate={(data) => handleUpdateProfile('profile', data)}
              saving={saving}
            />
          )}

          {activeTab === 'emergency' && (
            <EmergencyContactForm
              profile={profile}
              onUpdate={(data) => handleUpdateProfile('profile', data)}
              saving={saving}
            />
          )}

          {activeTab === 'communication' && (
            <CommunicationPreferences
              profile={profile}
              onUpdate={(data) => handleUpdateProfile('preferences', data)}
              saving={saving}
            />
          )}

          {activeTab === 'address' && (
            <AddressForm
              profile={profile}
              onUpdate={(data) => handleUpdateProfile('profile', data)}
              saving={saving}
            />
          )}
        </div>
      </Card>

      {/* Account Actions */}
      <Card className="mt-6 p-6">
        <h3 className="text-lg font-semibold text-text-primary mb-4">
          {t('client.profile.accountActions')}
        </h3>
        <div className="space-y-3">
          <Button
            variant="secondary"
            className="w-full sm:w-auto"
            onClick={() => window.location.href = '/auth/forgot-password'}
          >
            {t('client.profile.changePassword')}
          </Button>
          <div className="flex items-center gap-2 text-sm text-text-secondary">
            <Check className="w-4 h-4 text-green-600" />
            <span>{t('client.profile.emailVerified')}</span>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default Profile;