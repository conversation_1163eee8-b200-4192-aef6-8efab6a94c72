import api from './api';
import { SubjectArea, ServiceType } from '../../../services/serviceService';

// Tutor Matching Types and Interfaces
export interface TutorMatchRequest {
  client_postal_code: string;
  subject_areas: SubjectArea[];
  service_types?: ServiceType[];
  max_distance_km?: number;
  max_results?: number;
  min_rating?: number;
  max_hourly_rate?: number;
  availability_days?: string[];
  availability_times?: string[];
  grade_level?: string;
  languages?: string[];
  experience_level?: 'any' | 'beginner' | 'intermediate' | 'expert';
  gender_preference?: 'any' | 'male' | 'female';
  age_preference?: {
    min?: number;
    max?: number;
  };
}

export interface TutorMatch {
  tutor_id: number;
  first_name: string;
  last_name: string;
  email: string;
  postal_code: string;
  distance_km: number;
  average_rating?: number;
  total_sessions: number;
  hourly_rate?: number;
  specialties: string[];
  service_types: ServiceType[];
  availability_status: 'available' | 'busy' | 'unavailable';
  profile_picture?: string;
  bio?: string;
  experience_years?: number;
  education?: string;
  languages: string[];
  latitude: number;
  longitude: number;
  relevance_score: number;
  match_reasons: string[];
  verified_status: {
    background_check: boolean;
    identity_verified: boolean;
    qualifications_verified: boolean;
  };
}

export interface TutorMatchResponse {
  matches: TutorMatch[];
  total_found: number;
  search_radius_km: number;
  filters_applied: Record<string, any>;
}

export interface TutorAvailability {
  tutor_id: number;
  weekly_schedule: Array<{
    day: string;
    time_slots: Array<{
      start_time: string;
      end_time: string;
      is_available: boolean;
    }>;
  }>;
  time_off: Array<{
    start_date: string;
    end_date: string;
    reason?: string;
  }>;
  next_available_slot?: {
    date: string;
    time: string;
  };
}

export interface TutorSearchFilters {
  subject_areas?: SubjectArea[];
  service_types?: ServiceType[];
  price_range?: {
    min: number;
    max: number;
  };
  rating_min?: number;
  distance_max_km?: number;
  languages?: string[];
  availability?: {
    days: string[];
    time_of_day: 'morning' | 'afternoon' | 'evening' | 'any';
  };
  experience_level?: string;
  verified_only?: boolean;
}

export interface SavedSearch {
  search_id: number;
  name: string;
  filters: TutorSearchFilters;
  created_at: string;
  last_used?: string;
  notification_enabled: boolean;
}

export interface MatchingStats {
  total_tutors: number;
  tutors_in_area: number;
  average_rating: number;
  average_hourly_rate: number;
  most_popular_subjects: Array<{
    subject: string;
    count: number;
  }>;
}

class TutorMatchingService {
  /**
   * Find nearest tutors based on criteria
   */
  async findNearestTutors(request: TutorMatchRequest): Promise<TutorMatchResponse> {
    try {
      const response = await api.post<TutorMatchResponse>('/tutor-matching/nearest', request);
      return response.data;
    } catch (error: any) {
      console.error('Error finding nearest tutors:', error);
      throw new Error(error.response?.data?.detail || 'Failed to find tutors');
    }
  }

  /**
   * Get tutor availability
   */
  async getTutorAvailability(tutorId: number): Promise<TutorAvailability> {
    try {
      const response = await api.get<TutorAvailability>(`/tutor-matching/availability/${tutorId}`);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching tutor availability:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch availability');
    }
  }

  /**
   * Get multiple tutors' availability
   */
  async getBatchAvailability(tutorIds: number[]): Promise<TutorAvailability[]> {
    try {
      const response = await api.post<TutorAvailability[]>('/tutor-matching/availability/batch', {
        tutor_ids: tutorIds
      });
      return response.data;
    } catch (error: any) {
      console.error('Error fetching batch availability:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch availability');
    }
  }

  /**
   * Save a search for future use
   */
  async saveSearch(name: string, filters: TutorSearchFilters): Promise<SavedSearch> {
    try {
      const response = await api.post<SavedSearch>('/tutor-matching/saved-searches', {
        name,
        filters,
        notification_enabled: true
      });
      return response.data;
    } catch (error: any) {
      console.error('Error saving search:', error);
      throw new Error(error.response?.data?.detail || 'Failed to save search');
    }
  }

  /**
   * Get saved searches
   */
  async getSavedSearches(): Promise<SavedSearch[]> {
    try {
      const response = await api.get<SavedSearch[]>('/tutor-matching/saved-searches');
      return response.data;
    } catch (error: any) {
      console.error('Error fetching saved searches:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch saved searches');
    }
  }

  /**
   * Delete saved search
   */
  async deleteSavedSearch(searchId: number): Promise<{ message: string }> {
    try {
      const response = await api.delete<{ message: string }>(`/tutor-matching/saved-searches/${searchId}`);
      return response.data;
    } catch (error: any) {
      console.error('Error deleting saved search:', error);
      throw new Error(error.response?.data?.detail || 'Failed to delete search');
    }
  }

  /**
   * Get matching statistics for an area
   */
  async getMatchingStats(postalCode: string, radiusKm: number = 15): Promise<MatchingStats> {
    try {
      const response = await api.get<MatchingStats>('/tutor-matching/stats', {
        params: { postal_code: postalCode, radius_km: radiusKm }
      });
      return response.data;
    } catch (error: any) {
      console.error('Error fetching matching stats:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch stats');
    }
  }

  /**
   * Request tutor contact information
   */
  async requestTutorContact(tutorId: number, message?: string): Promise<{ 
    contact_info: {
      phone?: string;
      email: string;
      preferred_contact: 'phone' | 'email' | 'in_app';
    };
    message: string;
  }> {
    try {
      const response = await api.post<{ 
        contact_info: {
          phone?: string;
          email: string;
          preferred_contact: 'phone' | 'email' | 'in_app';
        };
        message: string;
      }>(`/tutor-matching/contact/${tutorId}`, { message });
      return response.data;
    } catch (error: any) {
      console.error('Error requesting tutor contact:', error);
      throw new Error(error.response?.data?.detail || 'Failed to request contact');
    }
  }

  /**
   * Get tutor recommendations based on past bookings
   */
  async getRecommendations(clientId?: number): Promise<TutorMatch[]> {
    try {
      const params = clientId ? { client_id: clientId } : {};
      const response = await api.get<{ recommendations: TutorMatch[] }>('/tutor-matching/recommendations', { params });
      return response.data.recommendations;
    } catch (error: any) {
      console.error('Error fetching recommendations:', error);
      return [];
    }
  }

  /**
   * Calculate match score between client and tutor
   */
  calculateMatchScore(tutor: TutorMatch, preferences: TutorMatchRequest): number {
    let score = 100;

    // Distance factor (closer is better)
    if (tutor.distance_km > 0) {
      const distancePenalty = Math.min(tutor.distance_km * 2, 30);
      score -= distancePenalty;
    }

    // Rating factor
    if (tutor.average_rating) {
      score += (tutor.average_rating - 3) * 10; // +20 for 5 stars, -10 for 1 star
    }

    // Price factor
    if (preferences.max_hourly_rate && tutor.hourly_rate) {
      if (tutor.hourly_rate > preferences.max_hourly_rate) {
        score -= 20;
      } else {
        // Bonus for being under budget
        const savingsPercent = (preferences.max_hourly_rate - tutor.hourly_rate) / preferences.max_hourly_rate;
        score += savingsPercent * 10;
      }
    }

    // Experience factor
    if (tutor.total_sessions > 50) {
      score += 10;
    }

    // Subject match
    const subjectMatch = tutor.specialties.some(s => 
      preferences.subject_areas.some(ps => ps.toLowerCase() === s.toLowerCase())
    );
    if (!subjectMatch) {
      score -= 30;
    }

    // Service type match
    if (preferences.service_types && preferences.service_types.length > 0) {
      const serviceMatch = tutor.service_types.some(st => 
        preferences.service_types!.includes(st)
      );
      if (!serviceMatch) {
        score -= 20;
      }
    }

    // Language match
    if (preferences.languages && preferences.languages.length > 0) {
      const languageMatch = tutor.languages.some(l => 
        preferences.languages!.includes(l)
      );
      if (!languageMatch) {
        score -= 15;
      }
    }

    // Verification bonus
    if (tutor.verified_status.background_check) {
      score += 5;
    }
    if (tutor.verified_status.qualifications_verified) {
      score += 5;
    }

    return Math.max(0, Math.min(100, score));
  }

  /**
   * Format distance for display
   */
  formatDistance(distanceKm: number): string {
    if (distanceKm < 1) {
      return `${Math.round(distanceKm * 1000)}m away`;
    } else if (distanceKm < 10) {
      return `${distanceKm.toFixed(1)}km away`;
    } else {
      return `${Math.round(distanceKm)}km away`;
    }
  }

  /**
   * Get availability summary
   */
  getAvailabilitySummary(availability: TutorAvailability): string {
    const availableDays = availability.weekly_schedule
      .filter(day => day.time_slots.some(slot => slot.is_available))
      .map(day => day.day);

    if (availableDays.length === 0) {
      return 'Currently unavailable';
    } else if (availableDays.length === 7) {
      return 'Available every day';
    } else if (availableDays.length === 5 && 
               !availableDays.includes('Saturday') && 
               !availableDays.includes('Sunday')) {
      return 'Available weekdays';
    } else if (availableDays.length === 2 && 
               availableDays.includes('Saturday') && 
               availableDays.includes('Sunday')) {
      return 'Available weekends only';
    } else {
      return `Available ${availableDays.join(', ')}`;
    }
  }

  /**
   * Sort tutors by relevance
   */
  sortByRelevance(tutors: TutorMatch[], preferences: TutorMatchRequest): TutorMatch[] {
    return tutors.sort((a, b) => {
      const scoreA = this.calculateMatchScore(a, preferences);
      const scoreB = this.calculateMatchScore(b, preferences);
      return scoreB - scoreA;
    });
  }

  /**
   * Get match reason descriptions
   */
  getMatchReasons(tutor: TutorMatch): string[] {
    const reasons: string[] = [];

    if (tutor.distance_km < 5) {
      reasons.push('Very close to your location');
    } else if (tutor.distance_km < 10) {
      reasons.push('Nearby location');
    }

    if (tutor.average_rating && tutor.average_rating >= 4.5) {
      reasons.push('Highly rated tutor');
    }

    if (tutor.total_sessions > 100) {
      reasons.push('Very experienced');
    } else if (tutor.total_sessions > 50) {
      reasons.push('Experienced tutor');
    }

    if (tutor.verified_status.background_check) {
      reasons.push('Background verified');
    }

    if (tutor.availability_status === 'available') {
      reasons.push('Currently available');
    }

    return reasons;
  }
}

export const tutorMatchingService = new TutorMatchingService();