-- Debug script to test password reset functionality
-- Run these queries after attempting a password reset

-- 1. Check if any users exist with the email you're testing
-- Replace '<EMAIL>' with the actual email
SELECT 
    user_id,
    email,
    password_hash IS NOT NULL as has_password,
    google_id IS NOT NULL as has_google_id,
    email_verified,
    created_at,
    deleted_at
FROM user_accounts
WHERE LOWER(email) = LOWER('<EMAIL>');

-- 2. Check ALL tokens in auth_tokens table
SELECT 
    token_id,
    user_id,
    token_type,
    LENGTH(token_hash) as hash_length,
    expires_at,
    created_at,
    used_at,
    metadata
FROM auth_tokens
ORDER BY created_at DESC;

-- 3. Check if there are any recent token creation attempts
SELECT 
    at.*,
    ua.email
FROM auth_tokens at
LEFT JOIN user_accounts ua ON at.user_id = ua.user_id
WHERE at.created_at > CURRENT_TIMESTAMP - INTERVAL '1 hour'
ORDER BY at.created_at DESC;

-- 4. Create a test user if none exists
-- UNCOMMENT AND RUN THIS IF YOU NEED A TEST USER:
/*
INSERT INTO user_accounts (
    email, 
    password_hash, 
    email_verified,
    created_at,
    updated_at
) VALUES (
    '<EMAIL>',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewKyNiLXCfyjeUr2', -- password: Test123!
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
)
ON CONFLICT (email) DO UPDATE
SET 
    email_verified = true,
    deleted_at = NULL
RETURNING user_id, email;
*/

-- 5. Manually create a password reset token to test the flow
-- UNCOMMENT AND MODIFY WITH YOUR USER_ID TO TEST:
/*
INSERT INTO auth_tokens (
    user_id,
    token_type,
    token_hash,
    expires_at,
    metadata,
    created_at
) VALUES (
    1, -- Replace with your actual user_id
    'password_reset',
    'test_token_hash_' || gen_random_uuid()::text,
    CURRENT_TIMESTAMP + INTERVAL '1 hour',
    jsonb_build_object(
        'email', '<EMAIL>',
        'initiated_by', 'manual_test'
    ),
    CURRENT_TIMESTAMP
)
RETURNING token_id, token_hash;
*/