"""Search domain models."""

from .models import (
    # Enums
    EntityType,
    SearchResultType,
    SortOrder,
    SearchSort,
    
    # Filter models
    SearchFilter,
    SearchFilters,
    
    # Query models
    SearchQuery,
    SearchHighlight,
    
    # Result models
    BaseSearchResult,
    ClientSearchResult,
    TutorSearchResult,
    DependantSearchResult,
    SearchResult,
    SearchResponse,
    
    # Autocomplete models
    AutocompleteResult,
    AutocompleteResponse,
    
    # Analytics models
    PopularSearch,
    SearchHistory,
    SearchAnalytics,
)

__all__ = [
    # Enums
    "EntityType",
    "SearchResultType",
    "SortOrder",
    "SearchSort",
    
    # Filter models
    "SearchFilter",
    "SearchFilters",
    
    # Query models
    "SearchQuery",
    "SearchHighlight",
    
    # Result models
    "BaseSearchResult",
    "ClientSearchResult",
    "TutorSearchResult",
    "DependantSearchResult",
    "SearchResult",
    "SearchResponse",
    
    # Autocomplete models
    "AutocompleteResult",
    "AutocompleteResponse",
    
    # Analytics models
    "PopularSearch",
    "SearchHistory",
    "SearchAnalytics",
]