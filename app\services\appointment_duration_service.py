"""
Service for handling appointment duration adjustments and billing recalculations.
"""

import logging
from typing import Optional, Dict, Any, List
from datetime import datetime, time, timedelta
from decimal import Decimal

from app.core.exceptions import BusinessLogicError, ResourceNotFoundError, ValidationError
from app.core.timezone import now_est
from app.database.repositories.appointment_repository import AppointmentRepository
from app.models.appointment_models import (
    Appointment, AppointmentStatus, DurationAdjustmentRequest,
    DurationAdjustmentHistory, AppointmentCompletion
)
from app.services.billing_service import BillingService
from app.services.notification_orchestrator import NotificationOrchestrator
from app.services.subscription_service import SubscriptionService

logger = logging.getLogger(__name__)


class AppointmentDurationService:
    """Service for managing appointment duration adjustments."""
    
    def __init__(
        self,
        appointment_repo: Optional[AppointmentRepository] = None,
        billing_service: Optional[BillingService] = None,
        notification_service: Optional[NotificationOrchestrator] = None,
        subscription_service: Optional[SubscriptionService] = None
    ):
        self.appointment_repo = appointment_repo or AppointmentRepository()
        self.billing_service = billing_service or BillingService()
        self.notification_service = notification_service or NotificationOrchestrator()
        self.subscription_service = subscription_service or SubscriptionService()
    
    async def adjust_appointment_duration(
        self,
        appointment_id: int,
        adjustment_request: DurationAdjustmentRequest,
        adjusted_by: int
    ) -> Dict[str, Any]:
        """
        Adjust the duration of a completed appointment.
        
        Args:
            appointment_id: ID of the appointment to adjust
            adjustment_request: Duration adjustment details
            adjusted_by: User ID making the adjustment
        
        Returns:
            Dict containing updated appointment and billing impact
        """
        logger.info(f"Adjusting duration for appointment {appointment_id}")
        
        # Get the appointment
        appointment = await self.appointment_repo.get_by_id(appointment_id)
        if not appointment:
            raise ResourceNotFoundError(f"Appointment {appointment_id} not found")
        
        # Validate appointment can be adjusted
        if appointment.status not in [AppointmentStatus.COMPLETED, AppointmentStatus.IN_PROGRESS]:
            raise BusinessLogicError(
                "Can only adjust duration for completed or in-progress appointments"
            )
        
        # Calculate actual duration
        actual_duration_minutes = self._calculate_actual_duration(
            appointment, adjustment_request
        )
        
        # Calculate original duration
        original_duration_minutes = self._calculate_minutes_between(
            appointment.start_time, appointment.end_time
        )
        
        # Store adjustment history
        await self._create_adjustment_history(
            appointment, adjustment_request, actual_duration_minutes,
            original_duration_minutes, adjusted_by
        )
        
        # Update appointment with actual duration
        update_data = {
            'actual_duration_minutes': actual_duration_minutes,
            'duration_adjusted': True,
            'duration_adjusted_by': adjusted_by,
            'duration_adjusted_at': now_est(),
            'duration_adjustment_reason': adjustment_request.adjustment_reason
        }
        
        if adjustment_request.actual_start_time:
            update_data['actual_start_time'] = adjustment_request.actual_start_time
        if adjustment_request.actual_end_time:
            update_data['actual_end_time'] = adjustment_request.actual_end_time
        
        updated_appointment = await self.appointment_repo.update(
            appointment_id, update_data
        )
        
        # Calculate billing impact
        billing_impact = await self._calculate_billing_impact(
            appointment, actual_duration_minutes, original_duration_minutes
        )
        
        # Update billing if needed
        if billing_impact['requires_update']:
            await self._update_billing(appointment, billing_impact)
        
        # Send notifications if requested
        if adjustment_request.notify_participants:
            await self._send_adjustment_notifications(
                appointment, actual_duration_minutes, original_duration_minutes,
                adjustment_request.adjustment_reason
            )
        
        return {
            'appointment': updated_appointment,
            'billing_impact': billing_impact,
            'original_duration_minutes': original_duration_minutes,
            'actual_duration_minutes': actual_duration_minutes,
            'duration_difference_minutes': actual_duration_minutes - original_duration_minutes
        }
    
    async def complete_appointment_with_duration(
        self,
        appointment_id: int,
        completion_request: AppointmentCompletion,
        completed_by: int
    ) -> Dict[str, Any]:
        """
        Complete an appointment with optional duration adjustment.
        
        Args:
            appointment_id: ID of the appointment to complete
            completion_request: Completion details including duration
            completed_by: User ID completing the appointment
        
        Returns:
            Dict containing completed appointment and billing details
        """
        logger.info(f"Completing appointment {appointment_id} with duration adjustment")
        
        # Get the appointment
        appointment = await self.appointment_repo.get_by_id(appointment_id)
        if not appointment:
            raise ResourceNotFoundError(f"Appointment {appointment_id} not found")
        
        # Validate appointment can be completed
        if appointment.status in [AppointmentStatus.COMPLETED, AppointmentStatus.CANCELLED]:
            raise BusinessLogicError(
                f"Cannot complete appointment with status {appointment.status}"
            )
        
        # Update appointment status
        update_data = {
            'status': completion_request.status,
            'completed_at': now_est(),
            'completed_by': completed_by,
            'tutor_no_show': completion_request.tutor_no_show,
            'client_no_show': completion_request.client_no_show
        }
        
        if completion_request.completion_notes:
            update_data['notes'] = (
                f"{appointment.notes}\n\nCompletion notes: {completion_request.completion_notes}"
                if appointment.notes else completion_request.completion_notes
            )
        
        # Handle duration adjustment if provided
        billing_impact = None
        if completion_request.actual_duration_minutes is not None:
            original_duration_minutes = self._calculate_minutes_between(
                appointment.start_time, appointment.end_time
            )
            
            update_data.update({
                'actual_duration_minutes': completion_request.actual_duration_minutes,
                'duration_adjusted': True,
                'duration_adjusted_by': completed_by,
                'duration_adjusted_at': now_est(),
                'duration_adjustment_reason': 'Adjusted during completion'
            })
            
            # Calculate billing impact
            billing_impact = await self._calculate_billing_impact(
                appointment, completion_request.actual_duration_minutes,
                original_duration_minutes
            )
        
        # Apply duration adjustment if provided separately
        if completion_request.duration_adjustment:
            adjustment_result = await self.adjust_appointment_duration(
                appointment_id,
                completion_request.duration_adjustment,
                completed_by
            )
            billing_impact = adjustment_result['billing_impact']
        
        # Update the appointment
        updated_appointment = await self.appointment_repo.update(
            appointment_id, update_data
        )
        
        # Generate billing for completed appointment
        billing_result = await self._generate_appointment_billing(
            updated_appointment, billing_impact
        )
        
        return {
            'appointment': updated_appointment,
            'billing_result': billing_result,
            'billing_impact': billing_impact
        }
    
    async def get_duration_adjustment_history(
        self, appointment_id: int
    ) -> List[DurationAdjustmentHistory]:
        """Get duration adjustment history for an appointment."""
        return await self.appointment_repo.get_duration_adjustment_history(appointment_id)
    
    def _calculate_actual_duration(
        self,
        appointment: Appointment,
        adjustment_request: DurationAdjustmentRequest
    ) -> int:
        """Calculate actual duration based on adjustment request."""
        # If explicit duration is provided, use it
        if adjustment_request.actual_duration_minutes is not None:
            return adjustment_request.actual_duration_minutes
        
        # If both actual times are provided, calculate duration
        if adjustment_request.actual_start_time and adjustment_request.actual_end_time:
            return self._calculate_minutes_between(
                adjustment_request.actual_start_time,
                adjustment_request.actual_end_time
            )
        
        # If only end time is adjusted, use original start time
        if adjustment_request.actual_end_time and not adjustment_request.actual_start_time:
            return self._calculate_minutes_between(
                appointment.start_time,
                adjustment_request.actual_end_time
            )
        
        # If only start time is adjusted, use original end time
        if adjustment_request.actual_start_time and not adjustment_request.actual_end_time:
            return self._calculate_minutes_between(
                adjustment_request.actual_start_time,
                appointment.end_time
            )
        
        # Default to original duration
        return self._calculate_minutes_between(
            appointment.start_time, appointment.end_time
        )
    
    def _calculate_minutes_between(self, start_time: time, end_time: time) -> int:
        """Calculate minutes between two times."""
        # Convert times to timedelta for calculation
        start_delta = timedelta(hours=start_time.hour, minutes=start_time.minute)
        end_delta = timedelta(hours=end_time.hour, minutes=end_time.minute)
        
        # Handle case where end time is next day (e.g., 23:00 to 01:00)
        if end_delta < start_delta:
            end_delta += timedelta(days=1)
        
        duration = end_delta - start_delta
        return int(duration.total_seconds() / 60)
    
    async def _calculate_billing_impact(
        self,
        appointment: Appointment,
        actual_duration_minutes: int,
        original_duration_minutes: int
    ) -> Dict[str, Any]:
        """Calculate the billing impact of a duration adjustment."""
        # Calculate costs
        hourly_rate = appointment.hourly_rate
        original_cost = (Decimal(original_duration_minutes) / 60) * hourly_rate
        actual_cost = (Decimal(actual_duration_minutes) / 60) * hourly_rate
        cost_difference = actual_cost - original_cost
        
        # Check if client has a subscription
        client_subscription = await self.subscription_service.get_active_subscription(
            appointment.client_id
        )
        
        billing_impact = {
            'requires_update': actual_duration_minutes != original_duration_minutes,
            'original_duration_minutes': original_duration_minutes,
            'actual_duration_minutes': actual_duration_minutes,
            'duration_difference_minutes': actual_duration_minutes - original_duration_minutes,
            'hourly_rate': hourly_rate,
            'original_cost': original_cost,
            'actual_cost': actual_cost,
            'cost_difference': cost_difference,
            'billing_type': 'subscription' if client_subscription else 'invoice',
            'subscription_id': client_subscription.subscription_id if client_subscription else None
        }
        
        # Calculate subscription hour impact
        if client_subscription:
            original_hours = Decimal(original_duration_minutes) / 60
            actual_hours = Decimal(actual_duration_minutes) / 60
            hours_difference = actual_hours - original_hours
            
            billing_impact.update({
                'original_hours_deducted': original_hours,
                'actual_hours_to_deduct': actual_hours,
                'hours_difference': hours_difference,
                'subscription_hours_remaining': client_subscription.hours_remaining
            })
        
        return billing_impact
    
    async def _update_billing(
        self, appointment: Appointment, billing_impact: Dict[str, Any]
    ) -> None:
        """Update billing based on duration adjustment."""
        if billing_impact['billing_type'] == 'subscription':
            # Adjust subscription hours
            await self.subscription_service.adjust_hours_for_duration_change(
                billing_impact['subscription_id'],
                billing_impact['hours_difference']
            )
        else:
            # Update invoice amount
            await self.billing_service.adjust_appointment_invoice(
                appointment.appointment_id,
                billing_impact['actual_cost']
            )
    
    async def _generate_appointment_billing(
        self, appointment: Appointment, billing_impact: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Generate billing for a completed appointment."""
        # Use actual duration if available, otherwise use scheduled duration
        duration_minutes = (
            appointment.actual_duration_minutes or
            self._calculate_minutes_between(appointment.start_time, appointment.end_time)
        )
        
        duration_hours = Decimal(duration_minutes) / 60
        total_cost = duration_hours * appointment.hourly_rate
        
        # Check for subscription
        client_subscription = await self.subscription_service.get_active_subscription(
            appointment.client_id
        )
        
        if client_subscription:
            # Deduct from subscription
            result = await self.subscription_service.deduct_hours(
                client_subscription.subscription_id,
                duration_hours,
                f"Appointment {appointment.appointment_id} on {appointment.scheduled_date}"
            )
            return {
                'billing_type': 'subscription',
                'subscription_id': client_subscription.subscription_id,
                'hours_deducted': duration_hours,
                'hours_remaining': result['hours_remaining']
            }
        else:
            # Create invoice
            invoice = await self.billing_service.create_appointment_invoice(
                appointment_id=appointment.appointment_id,
                client_id=appointment.client_id,
                amount=total_cost,
                duration_hours=duration_hours
            )
            return {
                'billing_type': 'invoice',
                'invoice_id': invoice.invoice_id,
                'amount': total_cost
            }
    
    async def _create_adjustment_history(
        self,
        appointment: Appointment,
        adjustment_request: DurationAdjustmentRequest,
        actual_duration_minutes: int,
        original_duration_minutes: int,
        adjusted_by: int
    ) -> None:
        """Create adjustment history record."""
        billing_impact = await self._calculate_billing_impact(
            appointment, actual_duration_minutes, original_duration_minutes
        )
        
        history_data = {
            'appointment_id': appointment.appointment_id,
            'original_start_time': appointment.start_time,
            'original_end_time': appointment.end_time,
            'adjusted_start_time': adjustment_request.actual_start_time or appointment.start_time,
            'adjusted_end_time': adjustment_request.actual_end_time or appointment.end_time,
            'original_duration_minutes': original_duration_minutes,
            'adjusted_duration_minutes': actual_duration_minutes,
            'adjustment_reason': adjustment_request.adjustment_reason,
            'adjusted_by': adjusted_by,
            'billing_impact': billing_impact,
            'notification_sent': adjustment_request.notify_participants
        }
        
        await self.appointment_repo.create_duration_adjustment_history(history_data)
    
    async def _send_adjustment_notifications(
        self,
        appointment: Appointment,
        actual_duration_minutes: int,
        original_duration_minutes: int,
        reason: str
    ) -> None:
        """Send notifications about duration adjustment."""
        duration_diff = actual_duration_minutes - original_duration_minutes
        
        # Format duration for display
        def format_duration(minutes: int) -> str:
            hours = minutes // 60
            mins = minutes % 60
            if hours > 0 and mins > 0:
                return f"{hours}h {mins}min"
            elif hours > 0:
                return f"{hours}h"
            else:
                return f"{mins}min"
        
        original_duration_str = format_duration(original_duration_minutes)
        actual_duration_str = format_duration(actual_duration_minutes)
        
        # Prepare notification data
        notification_data = {
            'appointment_id': appointment.appointment_id,
            'scheduled_date': appointment.scheduled_date.isoformat(),
            'original_duration': original_duration_str,
            'actual_duration': actual_duration_str,
            'duration_difference': format_duration(abs(duration_diff)),
            'adjustment_type': 'overtime' if duration_diff > 0 else 'undertime',
            'reason': reason
        }
        
        # Notify client
        await self.notification_service.send_notification(
            user_id=appointment.client_id,
            notification_type='appointment_duration_adjusted',
            data=notification_data
        )
        
        # Notify tutor
        await self.notification_service.send_notification(
            user_id=appointment.tutor_id,
            notification_type='appointment_duration_adjusted',
            data=notification_data
        )
        
        logger.info(
            f"Sent duration adjustment notifications for appointment {appointment.appointment_id}"
        )