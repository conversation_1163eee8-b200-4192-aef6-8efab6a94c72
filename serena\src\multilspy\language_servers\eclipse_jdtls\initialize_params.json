{"_description": "The parameters sent by the client when initializing the language server with the \"initialize\" request. More details at https://microsoft.github.io/language-server-protocol/specifications/lsp/3.17/specification/#initialize", "processId": "os.getpid()", "clientInfo": {"name": "Visual Studio Code - Insiders", "version": "1.77.0-insider"}, "locale": "en", "rootPath": "repository_absolute_path", "rootUri": "pathlib.Path(repository_absolute_path).as_uri()", "capabilities": {"workspace": {"applyEdit": true, "workspaceEdit": {"documentChanges": true, "resourceOperations": ["create", "rename", "delete"], "failureHandling": "textOnlyTransactional", "normalizesLineEndings": true, "changeAnnotationSupport": {"groupsOnLabel": true}}, "didChangeConfiguration": {"dynamicRegistration": true}, "didChangeWatchedFiles": {"dynamicRegistration": true, "relativePatternSupport": true}, "symbol": {"dynamicRegistration": true, "symbolKind": {"valueSet": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26]}, "tagSupport": {"valueSet": [1]}, "resolveSupport": {"properties": ["location.range"]}}, "codeLens": {"refreshSupport": true}, "executeCommand": {"dynamicRegistration": true}, "configuration": true, "workspaceFolders": true, "semanticTokens": {"refreshSupport": true}, "fileOperations": {"dynamicRegistration": true, "didCreate": true, "didRename": true, "didDelete": true, "willCreate": true, "willRename": true, "willDelete": true}, "inlineValue": {"refreshSupport": true}, "inlayHint": {"refreshSupport": true}, "diagnostics": {"refreshSupport": true}}, "textDocument": {"publishDiagnostics": {"relatedInformation": true, "versionSupport": false, "tagSupport": {"valueSet": [1, 2]}, "codeDescriptionSupport": true, "dataSupport": true}, "synchronization": {"dynamicRegistration": true, "willSave": true, "willSaveWaitUntil": true, "didSave": true}, "completion": {"dynamicRegistration": true, "contextSupport": true, "completionItem": {"snippetSupport": false, "commitCharactersSupport": true, "documentationFormat": ["markdown", "plaintext"], "deprecatedSupport": true, "preselectSupport": true, "tagSupport": {"valueSet": [1]}, "insertReplaceSupport": false, "resolveSupport": {"properties": ["documentation", "detail", "additionalTextEdits"]}, "insertTextModeSupport": {"valueSet": [1, 2]}, "labelDetailsSupport": true}, "insertTextMode": 2, "completionItemKind": {"valueSet": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25]}, "completionList": {"itemDefaults": ["commitCharacters", "edit<PERSON>ange", "insertTextFormat", "insertTextMode"]}}, "hover": {"dynamicRegistration": true, "contentFormat": ["markdown", "plaintext"]}, "signatureHelp": {"dynamicRegistration": true, "signatureInformation": {"documentationFormat": ["markdown", "plaintext"], "parameterInformation": {"labelOffsetSupport": true}, "activeParameterSupport": true}, "contextSupport": true}, "definition": {"dynamicRegistration": true, "linkSupport": true}, "references": {"dynamicRegistration": true}, "documentHighlight": {"dynamicRegistration": true}, "documentSymbol": {"dynamicRegistration": true, "symbolKind": {"valueSet": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26]}, "hierarchicalDocumentSymbolSupport": true, "tagSupport": {"valueSet": [1]}, "labelSupport": true}, "codeAction": {"dynamicRegistration": true, "isPreferredSupport": true, "disabledSupport": true, "dataSupport": true, "resolveSupport": {"properties": ["edit"]}, "codeActionLiteralSupport": {"codeActionKind": {"valueSet": ["", "quickfix", "refactor", "refactor.extract", "refactor.inline", "refactor.rewrite", "source", "source.organizeImports"]}}, "honorsChangeAnnotations": false}, "codeLens": {"dynamicRegistration": true}, "formatting": {"dynamicRegistration": true}, "rangeFormatting": {"dynamicRegistration": true}, "onTypeFormatting": {"dynamicRegistration": true}, "rename": {"dynamicRegistration": true, "prepareSupport": true, "prepareSupportDefaultBehavior": 1, "honorsChangeAnnotations": true}, "documentLink": {"dynamicRegistration": true, "tooltipSupport": true}, "typeDefinition": {"dynamicRegistration": true, "linkSupport": true}, "implementation": {"dynamicRegistration": true, "linkSupport": true}, "colorProvider": {"dynamicRegistration": true}, "foldingRange": {"dynamicRegistration": true, "rangeLimit": 5000, "lineFoldingOnly": true, "foldingRangeKind": {"valueSet": ["comment", "imports", "region"]}, "foldingRange": {"collapsedText": false}}, "declaration": {"dynamicRegistration": true, "linkSupport": true}, "selectionRange": {"dynamicRegistration": true}, "callHierarchy": {"dynamicRegistration": true}, "semanticTokens": {"dynamicRegistration": true, "tokenTypes": ["namespace", "type", "class", "enum", "interface", "struct", "typeParameter", "parameter", "variable", "property", "enumMember", "event", "function", "method", "macro", "keyword", "modifier", "comment", "string", "number", "regexp", "operator", "decorator"], "tokenModifiers": ["declaration", "definition", "readonly", "static", "deprecated", "abstract", "async", "modification", "documentation", "defaultLibrary"], "formats": ["relative"], "requests": {"range": true, "full": {"delta": true}}, "multilineTokenSupport": false, "overlappingTokenSupport": false, "serverCancelSupport": true, "augmentsSyntaxTokens": true}, "linkedEditingRange": {"dynamicRegistration": true}, "typeHierarchy": {"dynamicRegistration": true}, "inlineValue": {"dynamicRegistration": true}, "inlayHint": {"dynamicRegistration": true, "resolveSupport": {"properties": ["tooltip", "textEdits", "label.tooltip", "label.location", "label.command"]}}, "diagnostic": {"dynamicRegistration": true, "relatedDocumentSupport": false}}, "window": {"showMessage": {"messageActionItem": {"additionalPropertiesSupport": true}}, "showDocument": {"support": true}, "workDoneProgress": true}, "general": {"staleRequestSupport": {"cancel": true, "retryOnContentModified": ["textDocument/semanticTokens/full", "textDocument/semanticTokens/range", "textDocument/semanticTokens/full/delta"]}, "regularExpressions": {"engine": "ECMAScript", "version": "ES2020"}, "markdown": {"parser": "marked", "version": "1.1.0"}, "positionEncodings": ["utf-16"]}, "notebookDocument": {"synchronization": {"dynamicRegistration": true, "executionSummarySupport": true}}}, "initializationOptions": {"bundles": ["intellicode-core.jar"], "workspaceFolders": "[pathlib.Path(repository_absolute_path).as_uri()]", "settings": {"java": {"home": null, "jdt": {"ls": {"java": {"home": null}, "vmargs": "-XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable", "lombokSupport": {"enabled": true}, "protobufSupport": {"enabled": true}, "androidSupport": {"enabled": true}}}, "errors": {"incompleteClasspath": {"severity": "error"}}, "configuration": {"checkProjectSettingsExclusions": false, "updateBuildConfiguration": "interactive", "maven": {"userSettings": null, "globalSettings": null, "notCoveredPluginExecutionSeverity": "warning", "defaultMojoExecutionAction": "ignore"}, "workspaceCacheLimit": 90, "runtimes": [{"name": "JavaSE-21", "path": "static/vscode-java/extension/jre/21.0.7-linux-x86_64", "default": true}]}, "trace": {"server": "verbose"}, "import": {"maven": {"enabled": true, "offline": {"enabled": false}, "disableTestClasspathFlag": false}, "gradle": {"enabled": true, "wrapper": {"enabled": true}, "version": null, "home": "abs(static/gradle-7.3.3)", "java": {"home": "abs(static/launch_jres/21.0.7-linux-x86_64)"}, "offline": {"enabled": false}, "arguments": null, "jvmArguments": null, "user": {"home": null}, "annotationProcessing": {"enabled": true}}, "exclusions": ["**/node_modules/**", "**/.metadata/**", "**/archetype-resources/**", "**/META-INF/maven/**"], "generatesMetadataFilesAtProjectRoot": false}, "maven": {"downloadSources": true, "updateSnapshots": true}, "eclipse": {"downloadSources": true}, "referencesCodeLens": {"enabled": true}, "signatureHelp": {"enabled": true, "description": {"enabled": true}}, "implementationsCodeLens": {"enabled": true}, "format": {"enabled": true, "settings": {"url": null, "profile": null}, "comments": {"enabled": true}, "onType": {"enabled": true}, "insertSpaces": true, "tabSize": 4}, "saveActions": {"organizeImports": false}, "project": {"referencedLibraries": ["lib/**/*.jar"], "importOnFirstTimeStartup": "automatic", "importHint": true, "resourceFilters": ["node_modules", "\\.git"], "encoding": "ignore", "exportJar": {"targetPath": "${workspaceFolder}/${workspaceFolderBasename}.jar"}}, "contentProvider": {"preferred": null}, "autobuild": {"enabled": true}, "maxConcurrentBuilds": 1, "recommendations": {"dependency": {"analytics": {"show": true}}}, "completion": {"maxResults": 0, "enabled": true, "guessMethodArguments": true, "favoriteStaticMembers": ["org.junit.Assert.*", "org.junit.Assume.*", "org.junit.jupiter.api.Assertions.*", "org.junit.jupiter.api.Assumptions.*", "org.junit.jupiter.api.DynamicContainer.*", "org.junit.jupiter.api.DynamicTest.*", "org.mockito.Mockito.*", "org.mockito.ArgumentMatchers.*", "org.mockito.Answers.*"], "filteredTypes": ["java.awt.*", "com.sun.*", "sun.*", "jdk.*", "org.graalvm.*", "io.micrometer.shaded.*"], "importOrder": ["#", "java", "javax", "org", "com", ""], "postfix": {"enabled": false}, "matchCase": "off"}, "foldingRange": {"enabled": true}, "progressReports": {"enabled": false}, "codeGeneration": {"hashCodeEquals": {"useJava7Objects": false, "useInstanceof": false}, "useBlocks": false, "generateComments": false, "toString": {"template": "${object.className} [${member.name()}=${member.value}, ${otherMembers}]", "codeStyle": "STRING_CONCATENATION", "skipNullValues": false, "listArrayContents": true, "limitElements": 0}, "insertionLocation": "afterCursor"}, "selectionRange": {"enabled": true}, "showBuildStatusOnStart": {"enabled": "notification"}, "server": {"launchMode": "Standard"}, "sources": {"organizeImports": {"starThreshold": 99, "staticStarThreshold": 99}}, "imports": {"gradle": {"wrapper": {"checksums": []}}}, "templates": {"fileHeader": [], "typeComment": []}, "references": {"includeAccessors": true, "includeDecompiledSources": true}, "typeHierarchy": {"lazyLoad": false}, "settings": {"url": null}, "symbols": {"includeSourceMethodDeclarations": false}, "quickfix": {"showAt": "line"}, "inlayHints": {"parameterNames": {"enabled": "literals", "exclusions": []}}, "codeAction": {"sortMembers": {"avoidVolatileChanges": true}}, "compile": {"nullAnalysis": {"nonnull": ["javax.annotation.Nonnull", "org.eclipse.jdt.annotation.NonNull", "org.springframework.lang.NonNull"], "nullable": ["javax.annotation.Nullable", "org.eclipse.jdt.annotation.Nullable", "org.springframework.lang.Nullable"], "mode": "automatic"}}, "cleanup": {"actionsOnSave": []}, "sharedIndexes": {"enabled": "auto", "location": ""}, "refactoring": {"extract": {"interface": {"replace": true}}}, "debug": {"logLevel": "verbose", "settings": {"showHex": false, "showStaticVariables": false, "showQualifiedNames": false, "showLogicalStructure": true, "showToString": true, "maxStringLength": 0, "numericPrecision": 0, "hotCodeReplace": "manual", "enableRunDebugCodeLens": true, "forceBuildBeforeLaunch": true, "onBuildFailureProceed": false, "console": "integratedTerminal", "exceptionBreakpoint": {"skipClasses": []}, "stepping": {"skipClasses": [], "skipSynthetics": false, "skipStaticInitializers": false, "skipConstructors": false}, "jdwp": {"limitOfVariablesPerJdwpRequest": 100, "requestTimeout": 3000, "async": "auto"}, "vmArgs": ""}}, "silentNotification": false, "dependency": {"showMembers": false, "syncWithFolderExplorer": true, "autoRefresh": true, "refreshDelay": 2000, "packagePresentation": "flat"}, "help": {"firstView": "auto", "showReleaseNotes": true, "collectErrorLog": false}, "test": {"defaultConfig": "", "config": {}}}}, "extendedClientCapabilities": {"progressReportProvider": false, "classFileContentsSupport": true, "overrideMethodsPromptSupport": true, "hashCodeEqualsPromptSupport": true, "advancedOrganizeImportsSupport": true, "generateToStringPromptSupport": true, "advancedGenerateAccessorsSupport": true, "generateConstructorsPromptSupport": true, "generateDelegateMethodsPromptSupport": true, "advancedExtractRefactoringSupport": true, "inferSelectionSupport": ["extractMethod", "extractVariable", "extractField"], "moveRefactoringSupport": true, "clientHoverProvider": true, "clientDocumentSymbolProvider": true, "gradleChecksumWrapperPromptSupport": true, "resolveAdditionalTextEditsSupport": true, "advancedIntroduceParameterRefactoringSupport": true, "actionableRuntimeNotificationSupport": true, "shouldLanguageServerExitOnShutdown": true, "onCompletionItemSelectedCommand": "editor.action.triggerParameterHints", "extractInterfaceSupport": true, "advancedUpgradeGradleSupport": true}, "triggerFiles": []}, "trace": "verbose", "workspaceFolders": "[\n            {\n                \"uri\": pathlib.Path(repository_absolute_path).as_uri(),\n                \"name\": os.path.basename(repository_absolute_path),\n            }\n        ]"}