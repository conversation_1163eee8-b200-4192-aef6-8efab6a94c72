"""
Tutor Matching Service for finding nearest tutors based on location and subject preferences.

This service implements the core algorithm for matching clients with tutors
based on geographic proximity and subject area expertise.
"""

from typing import List, Optional, Dict, Any
from dataclasses import dataclass
import logging
from decimal import Decimal
import asyncio

from app.database.repositories.tutor_repository import TutorRepository
from app.services.geocoding_service import GeocodingService
from app.core.exceptions import ValidationError, ResourceNotFoundError

logger = logging.getLogger(__name__)

@dataclass
class TutorMatch:
    """Represents a tutor match with distance and relevance information."""
    tutor_id: int
    first_name: str
    last_name: str
    email: str
    postal_code: str
    distance_km: float
    average_rating: Optional[float]
    total_sessions: int
    hourly_rate: Optional[Decimal]
    specialties: List[str]
    service_types: List[str]
    availability_status: str
    profile_picture: Optional[str]
    bio: Optional[str]
    experience_years: Optional[int]
    education: Optional[str]
    languages: List[str]
    latitude: float
    longitude: float
    relevance_score: float  # Combined score based on distance, rating, and subject match

@dataclass
class TutorSearchCriteria:
    """Search criteria for finding tutors."""
    client_postal_code: str
    subject_areas: List[str]
    service_types: Optional[List[str]] = None
    max_distance_km: float = 15.0
    max_results: int = 5
    min_rating: Optional[float] = None
    max_hourly_rate: Optional[Decimal] = None
    availability_filter: Optional[str] = None  # 'available', 'limited', 'all'

class TutorMatchingService:
    """Service for matching clients with the most suitable nearby tutors."""
    
    def __init__(self, tutor_repository: TutorRepository, geocoding_service: GeocodingService):
        self.tutor_repository = tutor_repository
        self.geocoding_service = geocoding_service
        
    async def find_nearest_tutors(self, criteria: TutorSearchCriteria) -> List[TutorMatch]:
        """
        Find the nearest tutors based on client location and preferences.
        
        Args:
            criteria: Search criteria including location and preferences
            
        Returns:
            List of tutor matches sorted by relevance score
            
        Raises:
            ValidationError: If postal code is invalid
            ResourceNotFoundError: If no tutors found
        """
        try:
            # Validate and geocode client postal code
            client_coords = await self.geocoding_service.geocode_postal_code(criteria.client_postal_code)
            if not client_coords:
                raise ValidationError(f"Invalid postal code: {criteria.client_postal_code}")
                
            client_lat = client_coords['latitude']
            client_lng = client_coords['longitude']
            
            logger.info(f"Finding tutors near {criteria.client_postal_code} ({client_lat}, {client_lng})")
            
            # Get all active tutors with their coordinates
            from app.config.database import get_db_connection
            async with get_db_connection() as conn:
                tutors = await self.tutor_repository.get_tutors_with_location(conn)
            
            if not tutors:
                raise ResourceNotFoundError("No tutors found in the system")
            
            # Filter and score tutors
            tutor_matches = []
            for tutor in tutors:
                if not tutor.get('latitude') or not tutor.get('longitude'):
                    continue
                    
                # Calculate distance
                distance_km = self.geocoding_service.calculate_distance(
                    client_lat, client_lng,
                    tutor['latitude'], tutor['longitude']
                )
                
                # Apply distance filter
                if distance_km > criteria.max_distance_km:
                    continue
                    
                # Apply other filters
                if not self._meets_criteria(tutor, criteria):
                    continue
                    
                # Calculate relevance score
                relevance_score = self._calculate_relevance_score(
                    tutor, distance_km, criteria
                )
                
                tutor_match = TutorMatch(
                    tutor_id=tutor['tutor_id'],
                    first_name=tutor['first_name'],
                    last_name=tutor['last_name'],
                    email=tutor['email'],
                    postal_code=tutor['postal_code'],
                    distance_km=distance_km,
                    average_rating=tutor.get('average_rating'),
                    total_sessions=tutor.get('total_sessions', 0),
                    hourly_rate=tutor.get('hourly_rate'),
                    specialties=tutor.get('specialties', []),
                    service_types=tutor.get('service_types', []),
                    availability_status=tutor.get('availability_status', 'busy'),
                    profile_picture=tutor.get('profile_picture'),
                    bio=tutor.get('bio'),
                    experience_years=tutor.get('experience_years'),
                    education=tutor.get('education'),
                    languages=tutor.get('languages', []),
                    latitude=tutor['latitude'],
                    longitude=tutor['longitude'],
                    relevance_score=relevance_score
                )
                
                tutor_matches.append(tutor_match)
            
            # Sort by relevance score (highest first) and return top results
            tutor_matches.sort(key=lambda x: x.relevance_score, reverse=True)
            result = tutor_matches[:criteria.max_results]
            
            logger.info(f"Found {len(result)} tutor matches out of {len(tutors)} total tutors")
            
            return result
            
        except Exception as e:
            logger.error(f"Error finding nearest tutors: {str(e)}")
            raise
    
    async def find_tutors_by_subject(self, postal_code: str, subject_area: str, max_results: int = 5) -> List[TutorMatch]:
        """
        Simplified method to find tutors by subject area near a postal code.
        
        Args:
            postal_code: Client's postal code
            subject_area: Subject area to search for
            max_results: Maximum number of results to return
            
        Returns:
            List of tutor matches
        """
        criteria = TutorSearchCriteria(
            client_postal_code=postal_code,
            subject_areas=[subject_area],
            max_results=max_results
        )
        return await self.find_nearest_tutors(criteria)
    
    async def get_tutor_availability_in_radius(self, postal_code: str, radius_km: float = 10.0) -> Dict[str, int]:
        """
        Get availability statistics for tutors within a radius.
        
        Args:
            postal_code: Center postal code
            radius_km: Search radius in kilometers
            
        Returns:
            Dictionary with availability counts
        """
        try:
            coords = await self.geocoding_service.geocode_postal_code(postal_code)
            if not coords:
                raise ValidationError(f"Invalid postal code: {postal_code}")
                
            from app.config.database import get_db_connection
            async with get_db_connection() as conn:
                tutors = await self.tutor_repository.get_tutors_with_location(conn)
            
            availability_counts = {
                'available': 0,
                'limited': 0,
                'busy': 0,
                'total': 0
            }
            
            for tutor in tutors:
                if not tutor.get('latitude') or not tutor.get('longitude'):
                    continue
                    
                distance_km = self.geocoding_service.calculate_distance(
                    coords['latitude'], coords['longitude'],
                    tutor['latitude'], tutor['longitude']
                )
                
                if distance_km <= radius_km:
                    status = tutor.get('availability_status', 'busy')
                    availability_counts[status] += 1
                    availability_counts['total'] += 1
            
            return availability_counts
            
        except Exception as e:
            logger.error(f"Error getting tutor availability: {str(e)}")
            raise
    
    def _meets_criteria(self, tutor: Dict[str, Any], criteria: TutorSearchCriteria) -> bool:
        """Check if tutor meets the search criteria."""
        
        # Rating filter
        if criteria.min_rating and tutor.get('average_rating', 0) < criteria.min_rating:
            return False
            
        # Hourly rate filter
        if criteria.max_hourly_rate and tutor.get('hourly_rate', 0) > criteria.max_hourly_rate:
            return False
            
        # Availability filter
        if criteria.availability_filter and criteria.availability_filter != 'all':
            if tutor.get('availability_status') != criteria.availability_filter:
                return False
                
        # Service type filter
        if criteria.service_types:
            tutor_service_types = tutor.get('service_types', [])
            if not any(st in tutor_service_types for st in criteria.service_types):
                return False
        
        return True
    
    def _calculate_relevance_score(self, tutor: Dict[str, Any], distance_km: float, criteria: TutorSearchCriteria) -> float:
        """
        Calculate a relevance score for a tutor based on multiple factors.
        
        Score components:
        - Distance (closer is better): max 40 points
        - Rating (higher is better): max 30 points  
        - Subject match (exact match bonus): max 20 points
        - Experience (more is better): max 10 points
        
        Returns:
            Relevance score (0-100)
        """
        score = 0.0
        
        # Distance score (40 points max) - inverse relationship
        max_distance = criteria.max_distance_km
        distance_score = max(0, (max_distance - distance_km) / max_distance * 40)
        score += distance_score
        
        # Rating score (30 points max)
        rating = tutor.get('average_rating', 0)
        if rating > 0:
            rating_score = (rating / 5.0) * 30
            score += rating_score
        
        # Subject match score (20 points max)
        tutor_specialties = tutor.get('specialties', [])
        subject_match_score = 0
        for subject in criteria.subject_areas:
            if subject.lower() in [s.lower() for s in tutor_specialties]:
                subject_match_score += 20 / len(criteria.subject_areas)
        score += subject_match_score
        
        # Experience score (10 points max)
        experience_years = tutor.get('experience_years', 0)
        if experience_years > 0:
            experience_score = min(10, experience_years * 2)  # 2 points per year, max 10
            score += experience_score
        
        # Availability bonus
        availability_status = tutor.get('availability_status', 'busy')
        if availability_status == 'available':
            score += 5
        elif availability_status == 'limited':
            score += 2
            
        return round(score, 2)

# Dependency injection helper
async def get_tutor_matching_service() -> TutorMatchingService:
    """Get tutor matching service instance with dependencies."""
    from app.database.repositories.tutor_repository import get_tutor_repository
    from app.services.geocoding_service import get_geocoding_service
    
    tutor_repo = await get_tutor_repository()
    geocoding_service = await get_geocoding_service()
    
    return TutorMatchingService(tutor_repo, geocoding_service)