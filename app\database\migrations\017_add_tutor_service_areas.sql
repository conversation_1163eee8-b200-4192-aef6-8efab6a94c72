-- Migration 017: Add tutor service areas enhancements
-- This migration enhances the existing tutor_service_areas table

-- Add new columns to existing table if they don't exist
DO $$ 
BEGIN
    -- Add center_postal_code column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'tutor_service_areas' 
                   AND column_name = 'center_postal_code') THEN
        ALTER TABLE tutor_service_areas ADD COLUMN center_postal_code VARCHAR(10);
        -- Copy from postal_code if it exists
        UPDATE tutor_service_areas SET center_postal_code = postal_code WHERE postal_code IS NOT NULL;
        -- Make it NOT NULL after copying data
        ALTER TABLE tutor_service_areas ALTER COLUMN center_postal_code SET NOT NULL;
    END IF;

    -- Add radius_km column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'tutor_service_areas' 
                   AND column_name = 'radius_km') THEN
        ALTER TABLE tutor_service_areas ADD COLUMN radius_km DECIMAL(5,2);
        -- Copy from max_distance_km if it exists
        UPDATE tutor_service_areas SET radius_km = max_distance_km WHERE max_distance_km IS NOT NULL;
        -- Set default and make NOT NULL
        UPDATE tutor_service_areas SET radius_km = 10 WHERE radius_km IS NULL;
        ALTER TABLE tutor_service_areas ALTER COLUMN radius_km SET NOT NULL;
        ALTER TABLE tutor_service_areas ADD CONSTRAINT check_radius_km CHECK (radius_km > 0 AND radius_km <= 100);
    END IF;

    -- Add covered_postal_codes column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'tutor_service_areas' 
                   AND column_name = 'covered_postal_codes') THEN
        ALTER TABLE tutor_service_areas ADD COLUMN covered_postal_codes TEXT[] NOT NULL DEFAULT '{}';
    END IF;

    -- Add service_types column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'tutor_service_areas' 
                   AND column_name = 'service_types') THEN
        ALTER TABLE tutor_service_areas ADD COLUMN service_types TEXT[] NOT NULL DEFAULT ARRAY['in_person']::TEXT[];
        ALTER TABLE tutor_service_areas ADD CONSTRAINT check_service_types CHECK (
            array_length(service_types, 1) > 0 AND 
            service_types <@ ARRAY['online', 'in_person', 'library', 'hybrid']
        );
    END IF;

    -- Add is_active column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'tutor_service_areas' 
                   AND column_name = 'is_active') THEN
        ALTER TABLE tutor_service_areas ADD COLUMN is_active BOOLEAN NOT NULL DEFAULT true;
    END IF;

    -- Add auto_expand column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'tutor_service_areas' 
                   AND column_name = 'auto_expand') THEN
        ALTER TABLE tutor_service_areas ADD COLUMN auto_expand BOOLEAN NOT NULL DEFAULT false;
    END IF;

    -- Add max_travel_time_minutes column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'tutor_service_areas' 
                   AND column_name = 'max_travel_time_minutes') THEN
        ALTER TABLE tutor_service_areas ADD COLUMN max_travel_time_minutes INTEGER;
        ALTER TABLE tutor_service_areas ADD CONSTRAINT check_travel_time CHECK (max_travel_time_minutes > 0);
    END IF;
END $$;

-- Add constraints if they don't exist
DO $$
BEGIN
    -- Add unique constraint if not exists
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                   WHERE constraint_name = 'unique_tutor_service_area' 
                   AND table_name = 'tutor_service_areas') THEN
        ALTER TABLE tutor_service_areas ADD CONSTRAINT unique_tutor_service_area UNIQUE (tutor_id);
    END IF;

    -- Add postal code format check if not exists
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                   WHERE constraint_name = 'valid_postal_code_format' 
                   AND table_name = 'tutor_service_areas') THEN
        ALTER TABLE tutor_service_areas ADD CONSTRAINT valid_postal_code_format 
            CHECK (center_postal_code ~ '^[A-Z]\d[A-Z][\s]?\d[A-Z]\d$');
    END IF;

    -- Add reasonable coverage check if not exists
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                   WHERE constraint_name = 'reasonable_coverage' 
                   AND table_name = 'tutor_service_areas') THEN
        ALTER TABLE tutor_service_areas ADD CONSTRAINT reasonable_coverage 
            CHECK (array_length(covered_postal_codes, 1) <= 1000);
    END IF;
END $$;

-- Create indexes if they don't exist
CREATE INDEX IF NOT EXISTS idx_tutor_service_areas_tutor_id ON tutor_service_areas(tutor_id);
CREATE INDEX IF NOT EXISTS idx_tutor_service_areas_center_postal ON tutor_service_areas(center_postal_code);
CREATE INDEX IF NOT EXISTS idx_tutor_service_areas_active ON tutor_service_areas(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_tutor_service_areas_covered_postal ON tutor_service_areas USING GIN (covered_postal_codes);
CREATE INDEX IF NOT EXISTS idx_tutor_service_areas_service_types ON tutor_service_areas USING GIN (service_types);

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_tutor_service_areas_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update the updated_at column
DROP TRIGGER IF EXISTS trigger_update_tutor_service_areas_updated_at ON tutor_service_areas;
CREATE TRIGGER trigger_update_tutor_service_areas_updated_at
    BEFORE UPDATE ON tutor_service_areas
    FOR EACH ROW
    EXECUTE FUNCTION update_tutor_service_areas_updated_at();

-- Add comments for documentation
COMMENT ON TABLE tutor_service_areas IS 'Manages tutor service coverage areas defined by postal codes and radius';
COMMENT ON COLUMN tutor_service_areas.center_postal_code IS 'Primary postal code for the service area center';
COMMENT ON COLUMN tutor_service_areas.radius_km IS 'Service radius in kilometers from center postal code';
COMMENT ON COLUMN tutor_service_areas.covered_postal_codes IS 'Array of all postal codes covered within the service area';
COMMENT ON COLUMN tutor_service_areas.service_types IS 'Types of tutoring services offered in this area';
COMMENT ON COLUMN tutor_service_areas.is_active IS 'Whether this service area is currently active';
COMMENT ON COLUMN tutor_service_areas.auto_expand IS 'Whether to automatically expand coverage based on demand';
COMMENT ON COLUMN tutor_service_areas.max_travel_time_minutes IS 'Maximum travel time tutor is willing to accept';