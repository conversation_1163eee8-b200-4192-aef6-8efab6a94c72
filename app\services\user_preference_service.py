"""
Unified User Preference Service for TutorAide Application.
Manages all user preferences including language, formatting, UI settings, 
notifications, and quick actions in a single consolidated service.
"""

import logging
from typing import Optional, Dict, Any, List, Tuple
from datetime import datetime, date
from fastapi import Request

from app.database.repositories.user_preference_repository import UserPreferenceRepository
from app.models.user_preference_models import (
    UserPreference, UserPreferenceUpdate, UserPreferenceResponse,
    LanguageDetectionResult, NotificationSettings, UISettings,
    UserPreferenceStats, BulkPreferenceUpdate, BulkPreferenceUpdateResult
)
from app.locales.constants import SUPPORTED_LANGUAGES, DEFAULT_LANGUAGE
from app.locales.language_detector import LanguageDetector
from app.core.exceptions import ValidationError, ResourceNotFoundError
from app.models.user_models import UserRoleType
from app.models.quick_action_models import QuickActionType, get_actions_for_role


logger = logging.getLogger(__name__)


class UserPreferenceService:
    """Unified service for managing all user preferences."""
    
    def __init__(self):
        self.repository = UserPreferenceRepository()
        self.language_detector = LanguageDetector()
        
        # Default notification preferences by role
        self.default_notification_settings = {
            UserRoleType.CLIENT: {
                'appointment_reminder': {'push': True, 'sms': True, 'email': True},
                'payment_due': {'push': True, 'sms': True, 'email': True},
                'session_confirmed': {'push': True, 'sms': False, 'email': True},
                'general': {'push': True, 'sms': False, 'email': True}
            },
            UserRoleType.TUTOR: {
                'appointment_reminder': {'push': True, 'sms': True, 'email': True},
                'payment_ready': {'push': True, 'sms': False, 'email': True},
                'session_confirmed': {'push': True, 'sms': True, 'email': True},
                'general': {'push': True, 'sms': False, 'email': True}
            },
            UserRoleType.MANAGER: {
                'appointment_reminder': {'push': True, 'sms': False, 'email': True},
                'payment_due': {'push': True, 'sms': False, 'email': True},
                'system_alert': {'push': True, 'sms': True, 'email': True},
                'general': {'push': True, 'sms': False, 'email': True}
            }
        }
    
    async def get_user_preferences(
        self,
        user_id: int,
        request: Optional[Request] = None
    ) -> UserPreferenceResponse:
        """Get complete user preferences with effective values."""
        
        try:
            # Get stored preferences
            preferences = await self.repository.get_user_preferences(user_id)
            
            # If no preferences exist, create defaults
            if not preferences:
                preferences = await self._create_default_preferences(user_id)
            
            # Detect language if request provided and auto-detect enabled
            effective_language = preferences.preferred_language
            detected_language = None
            
            if request and getattr(preferences, 'language_auto_detect', True):
                detected_language = self.language_detector.detect_language_from_request(request)
                if detected_language.confidence > 0.8:
                    effective_language = detected_language.detected_language
            
            return UserPreferenceResponse(
                user_id=user_id,
                preferences=preferences,
                effective_language=effective_language,
                detected_language=detected_language,
                success=True
            )
            
        except Exception as e:
            logger.error(f"Failed to get preferences for user {user_id}: {e}")
            raise ValidationError(f"Failed to get user preferences: {str(e)}")
    
    async def update_user_preferences(
        self,
        user_id: int,
        update_data: UserPreferenceUpdate,
        updated_by: Optional[int] = None
    ) -> UserPreferenceResponse:
        """Update user preferences with validation."""
        
        try:
            # Validate language if provided
            if update_data.preferred_language and update_data.preferred_language not in SUPPORTED_LANGUAGES:
                raise ValidationError(f"Unsupported language: {update_data.preferred_language}")
            
            # Validate date format
            valid_date_formats = ['short', 'medium', 'long', 'full']
            if update_data.date_format and update_data.date_format not in valid_date_formats:
                raise ValidationError(f"Invalid date format: {update_data.date_format}")
            
            # Validate time format
            valid_time_formats = ['auto', '12h', '24h']
            if update_data.time_format and update_data.time_format not in valid_time_formats:
                raise ValidationError(f"Invalid time format: {update_data.time_format}")
            
            # Update preferences
            updated_preferences = await self.repository.update_user_preferences(
                user_id=user_id,
                update_data=update_data,
                updated_by=updated_by
            )
            
            return UserPreferenceResponse(
                user_id=user_id,
                preferences=updated_preferences,
                effective_language=updated_preferences.preferred_language,
                success=True,
                message="Preferences updated successfully"
            )
            
        except ValidationError:
            raise
        except Exception as e:
            logger.error(f"Failed to update preferences for user {user_id}: {e}")
            raise ValidationError(f"Failed to update preferences: {str(e)}")
    
    async def update_notification_settings(
        self,
        user_id: int,
        notification_type: str,
        settings: Dict[str, bool]
    ) -> UserPreferenceResponse:
        """Update specific notification settings."""
        
        try:
            # Get current preferences
            current = await self.repository.get_user_preferences(user_id)
            if not current:
                current = await self._create_default_preferences(user_id)
            
            # Update notification settings
            notification_settings = current.notification_settings or {}
            notification_settings[notification_type] = settings
            
            # Save updated settings
            update_data = UserPreferenceUpdate(notification_settings=notification_settings)
            return await self.update_user_preferences(user_id, update_data)
            
        except Exception as e:
            logger.error(f"Failed to update notification settings for user {user_id}: {e}")
            raise ValidationError(f"Failed to update notification settings: {str(e)}")
    
    async def add_pinned_action(
        self,
        user_id: int,
        action_type: QuickActionType
    ) -> UserPreferenceResponse:
        """Add a quick action to user's pinned actions."""
        
        try:
            # Get current preferences
            current = await self.repository.get_user_preferences(user_id)
            if not current:
                current = await self._create_default_preferences(user_id)
            
            # Get user's role to validate action
            user = await self.repository.get_user(user_id)
            if not user:
                raise ResourceNotFoundError(f"User {user_id} not found")
            
            # Validate action is available for user's role
            available_actions = get_actions_for_role(user.primary_role)
            if action_type not in [a.action_type for a in available_actions]:
                raise ValidationError(f"Action {action_type} not available for role {user.primary_role}")
            
            # Add to pinned actions if not already there
            pinned_actions = current.pinned_actions or []
            if action_type.value not in pinned_actions:
                pinned_actions.append(action_type.value)
                
                # Limit to 6 pinned actions
                if len(pinned_actions) > 6:
                    pinned_actions = pinned_actions[-6:]
                
                update_data = UserPreferenceUpdate(pinned_actions=pinned_actions)
                return await self.update_user_preferences(user_id, update_data)
            
            return UserPreferenceResponse(
                user_id=user_id,
                preferences=current,
                effective_language=current.preferred_language,
                success=True,
                message="Action already pinned"
            )
            
        except ValidationError:
            raise
        except Exception as e:
            logger.error(f"Failed to add pinned action for user {user_id}: {e}")
            raise ValidationError(f"Failed to add pinned action: {str(e)}")
    
    async def remove_pinned_action(
        self,
        user_id: int,
        action_type: QuickActionType
    ) -> UserPreferenceResponse:
        """Remove a quick action from user's pinned actions."""
        
        try:
            # Get current preferences
            current = await self.repository.get_user_preferences(user_id)
            if not current:
                return UserPreferenceResponse(
                    user_id=user_id,
                    preferences=current,
                    effective_language=DEFAULT_LANGUAGE,
                    success=True,
                    message="No pinned actions to remove"
                )
            
            # Remove from pinned actions
            pinned_actions = current.pinned_actions or []
            if action_type.value in pinned_actions:
                pinned_actions.remove(action_type.value)
                update_data = UserPreferenceUpdate(pinned_actions=pinned_actions)
                return await self.update_user_preferences(user_id, update_data)
            
            return UserPreferenceResponse(
                user_id=user_id,
                preferences=current,
                effective_language=current.preferred_language,
                success=True,
                message="Action not in pinned list"
            )
            
        except Exception as e:
            logger.error(f"Failed to remove pinned action for user {user_id}: {e}")
            raise ValidationError(f"Failed to remove pinned action: {str(e)}")
    
    async def update_ui_settings(
        self,
        user_id: int,
        ui_settings: Dict[str, Any]
    ) -> UserPreferenceResponse:
        """Update UI settings like theme, layout preferences, etc."""
        
        try:
            # Get current preferences
            current = await self.repository.get_user_preferences(user_id)
            if not current:
                current = await self._create_default_preferences(user_id)
            
            # Merge with existing UI settings
            existing_ui_settings = current.ui_settings or {}
            existing_ui_settings.update(ui_settings)
            
            # Save updated settings
            update_data = UserPreferenceUpdate(ui_settings=existing_ui_settings)
            return await self.update_user_preferences(user_id, update_data)
            
        except Exception as e:
            logger.error(f"Failed to update UI settings for user {user_id}: {e}")
            raise ValidationError(f"Failed to update UI settings: {str(e)}")
    
    async def get_preference_stats(self) -> UserPreferenceStats:
        """Get comprehensive preference statistics."""
        return await self.repository.get_preference_stats()
    
    async def bulk_update_preferences(
        self,
        bulk_update: BulkPreferenceUpdate,
        updated_by: Optional[int] = None
    ) -> BulkPreferenceUpdateResult:
        """Bulk update preferences for multiple users."""
        
        try:
            return await self.repository.bulk_update_preferences(
                user_ids=bulk_update.user_ids,
                update_data=bulk_update.update_data,
                updated_by=updated_by
            )
            
        except Exception as e:
            logger.error(f"Bulk preference update failed: {e}")
            return BulkPreferenceUpdateResult(
                success=False,
                updated_count=0,
                failed_count=len(bulk_update.user_ids),
                failed_user_ids=bulk_update.user_ids,
                errors=[str(e)],
                message="Bulk update failed due to system error"
            )
    
    async def get_users_by_language(
        self,
        language: str,
        limit: int = 100,
        offset: int = 0
    ) -> Tuple[List[int], int]:
        """Get users filtered by language preference."""
        
        if language not in SUPPORTED_LANGUAGES:
            raise ValidationError(f"Unsupported language: {language}")
        
        return await self.repository.get_users_by_language(
            language=language,
            limit=limit,
            offset=offset
        )
    
    async def get_users_by_notification_preference(
        self,
        notification_type: str,
        channel: str,
        enabled: bool = True,
        limit: int = 100,
        offset: int = 0
    ) -> Tuple[List[int], int]:
        """Get users filtered by notification preference."""
        
        valid_channels = ['push', 'sms', 'email']
        if channel not in valid_channels:
            raise ValidationError(f"Invalid notification channel: {channel}")
        
        return await self.repository.get_users_by_notification_preference(
            notification_type=notification_type,
            channel=channel,
            enabled=enabled,
            limit=limit,
            offset=offset
        )
    
    async def _create_default_preferences(self, user_id: int) -> UserPreference:
        """Create default preferences for a new user."""
        
        try:
            # Get user to determine role
            user = await self.repository.get_user(user_id)
            if not user:
                raise ResourceNotFoundError(f"User {user_id} not found")
            
            # Get default notification settings for role
            notification_settings = self.default_notification_settings.get(
                user.primary_role,
                self.default_notification_settings[UserRoleType.CLIENT]
            )
            
            # Create default preferences
            default_prefs = UserPreference(
                user_id=user_id,
                preferred_language=DEFAULT_LANGUAGE,
                quebec_french_preference=True,
                date_format='medium',
                time_format='auto',
                currency_display='symbol',
                number_precision=2,
                ui_settings={
                    'theme': 'light',
                    'sidebar_collapsed': False,
                    'show_tooltips': True
                },
                notification_settings=notification_settings,
                pinned_actions=[],
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            # Save to database
            return await self.repository.create_user_preferences(default_prefs)
            
        except Exception as e:
            logger.error(f"Failed to create default preferences for user {user_id}: {e}")
            raise


# Global service instance
_user_preference_service = None


def get_user_preference_service() -> UserPreferenceService:
    """Get the global user preference service instance."""
    global _user_preference_service
    if _user_preference_service is None:
        _user_preference_service = UserPreferenceService()
    return _user_preference_service