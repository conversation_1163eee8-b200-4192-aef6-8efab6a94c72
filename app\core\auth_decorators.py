"""
Authorization decorators for API endpoint protection.
Provides easy-to-use decorators for securing endpoints with granular permissions.
"""

import functools
import inspect
from typing import Callable, Optional, Union, List, Any
from fastapi import HTTPException, status, Depends, Request

from app.core.dependencies import get_current_active_user, get_database
from app.core.permissions import (
    permission_engine, ResourceType, PermissionAction, 
    AuthorizationContext
)
from app.models.user_models import User, UserRoleType
from app.core.logging import Tutor<PERSON>ideLogger
import asyncpg


logger = TutorAideLogger.get_logger(__name__)


def require_auth(func: Callable) -> Callable:
    """
    Simple decorator that requires authentication.
    
    Example:
        @require_auth
        async def protected_endpoint(
            current_user: User = Depends(get_current_active_user)
        ):
            return {"user": current_user.email}
    """
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        # Check if current_user is in kwargs
        current_user = kwargs.get('current_user')
        if not current_user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication required"
            )
        return await func(*args, **kwargs)
    return wrapper


def require_roles(allowed_roles: Union[UserRoleType, List[UserRoleType]]) -> Callable:
    """
    Decorator that requires user to have one of the specified roles.
    
    Args:
        allowed_roles: Single role or list of roles allowed
    
    Example:
        @require_roles([UserRoleType.MANAGER, UserRoleType.TUTOR])
        async def staff_endpoint(
            current_user: User = Depends(get_current_active_user)
        ):
            return {"message": "Staff only content"}
    """
    if not isinstance(allowed_roles, list):
        allowed_roles = [allowed_roles]
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract current_user from kwargs
            current_user = kwargs.get('current_user')
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            # Check if user has any of the required roles
            user_roles = set(current_user.roles)
            allowed_roles_set = set(allowed_roles)
            
            if not user_roles.intersection(allowed_roles_set):
                logger.warning(
                    f"User {current_user.user_id} attempted to access "
                    f"endpoint requiring one of {[r.value for r in allowed_roles]} roles"
                )
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Insufficient permissions. Required roles: {[r.value for r in allowed_roles]}"
                )
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


def require_permission(
    resource_type: ResourceType,
    action: PermissionAction,
    resource_id_param: Optional[str] = None,
    allow_own_resource: bool = True
):
    """
    Decorator that requires specific permission for resource access.
    
    Args:
        resource_type: Type of resource being accessed
        action: Action being performed
        resource_id_param: Parameter name containing resource ID
        allow_own_resource: Whether to allow access to own resources
    
    Example:
        @require_permission(ResourceType.CLIENT_PROFILE, PermissionAction.VIEW, "client_id")
        async def get_client_profile(
            client_id: int,
            current_user: User = Depends(get_current_active_user),
            db: asyncpg.Connection = Depends(get_database)
        ):
            return {"client_id": client_id}
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract dependencies
            current_user = kwargs.get('current_user')
            db = kwargs.get('db')
            
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            if not db:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Database connection required"
                )
            
            # Get resource ID if specified
            resource_id = None
            if resource_id_param:
                resource_id = kwargs.get(resource_id_param)
                if resource_id is None:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Resource ID parameter '{resource_id_param}' is required"
                    )
            
            # Check permission
            has_permission = await permission_engine.check_permission(
                user=current_user,
                resource_type=resource_type,
                action=action,
                resource_id=resource_id,
                db=db
            )
            
            if not has_permission:
                logger.warning(
                    f"Permission denied: User {current_user.user_id} cannot {action.value} "
                    f"{resource_type.value} {resource_id or 'N/A'}"
                )
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="You do not have permission to perform this action"
                )
            
            # Log successful authorization
            logger.debug(
                f"Permission granted: User {current_user.user_id} can {action.value} "
                f"{resource_type.value} {resource_id or 'N/A'}"
            )
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


def require_role_or_ownership(
    allowed_roles: Union[UserRoleType, List[UserRoleType]],
    resource_type: ResourceType,
    resource_id_param: str,
    owner_check: Optional[Callable] = None
):
    """
    Decorator that allows access based on role OR resource ownership.
    
    Args:
        allowed_roles: Roles that have full access
        resource_type: Type of resource being accessed
        resource_id_param: Parameter name containing resource ID
        owner_check: Custom ownership check function
    
    Example:
        @require_role_or_ownership(
            UserRoleType.MANAGER, 
            ResourceType.CLIENT_PROFILE, 
            "client_id"
        )
        async def update_client_profile(
            client_id: int,
            current_user: User = Depends(get_current_active_user),
            db: asyncpg.Connection = Depends(get_database)
        ):
            return {"message": "Updated"}
    """
    if not isinstance(allowed_roles, list):
        allowed_roles = [allowed_roles]
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract dependencies
            current_user = kwargs.get('current_user')
            db = kwargs.get('db')
            resource_id = kwargs.get(resource_id_param)
            
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            if not db:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Database connection required"
                )
            
            if resource_id is None:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Resource ID parameter '{resource_id_param}' is required"
                )
            
            # Check if user has required role
            user_roles = set(current_user.roles)
            allowed_roles_set = set(allowed_roles)
            
            if user_roles.intersection(allowed_roles_set):
                logger.debug(f"Access granted via role for user {current_user.user_id}")
                return await func(*args, **kwargs)
            
            # Check ownership
            if owner_check:
                # Use custom ownership check
                has_access = await owner_check(current_user, resource_id, db)
            else:
                # Use permission engine
                has_access = await permission_engine.check_permission(
                    user=current_user,
                    resource_type=resource_type,
                    action=PermissionAction.VIEW,  # Default to VIEW for ownership
                    resource_id=resource_id,
                    db=db
                )
            
            if not has_access:
                logger.warning(
                    f"Access denied: User {current_user.user_id} cannot access "
                    f"{resource_type.value} {resource_id}"
                )
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="You do not have permission to access this resource"
                )
            
            logger.debug(f"Access granted via ownership for user {current_user.user_id}")
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


def require_relationship_access(
    relationship_type: str,
    resource_id_param: str,
    allowed_roles: Optional[List[UserRoleType]] = None
):
    """
    Decorator that checks relationship-based access.
    
    Args:
        relationship_type: Type of relationship (e.g., "parent-child", "tutor-client")
        resource_id_param: Parameter name containing resource ID
        allowed_roles: Roles that bypass relationship check
    
    Example:
        @require_relationship_access("parent-child", "dependant_id", [UserRoleType.MANAGER])
        async def get_dependant_assessment(
            dependant_id: int,
            current_user: User = Depends(get_current_active_user),
            db: asyncpg.Connection = Depends(get_database)
        ):
            return {"dependant_id": dependant_id}
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract dependencies
            current_user = kwargs.get('current_user')
            db = kwargs.get('db')
            resource_id = kwargs.get(resource_id_param)
            
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            if not db:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Database connection required"
                )
            
            if resource_id is None:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Resource ID parameter '{resource_id_param}' is required"
                )
            
            # Check bypass roles
            if allowed_roles:
                user_roles = set(current_user.roles)
                allowed_roles_set = set(allowed_roles)
                if user_roles.intersection(allowed_roles_set):
                    logger.debug(f"Relationship check bypassed via role for user {current_user.user_id}")
                    return await func(*args, **kwargs)
            
            # Check relationship
            has_access = await _check_relationship_access(
                relationship_type, current_user, resource_id, db
            )
            
            if not has_access:
                logger.warning(
                    f"Relationship access denied: User {current_user.user_id} "
                    f"lacks {relationship_type} relationship to resource {resource_id}"
                )
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="You do not have the required relationship to access this resource"
                )
            
            logger.debug(
                f"Relationship access granted: User {current_user.user_id} "
                f"has {relationship_type} relationship to resource {resource_id}"
            )
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


async def _check_relationship_access(
    relationship_type: str,
    user: User,
    resource_id: int,
    db: asyncpg.Connection
) -> bool:
    """Check specific relationship access."""
    
    if relationship_type == "parent-child":
        # Check if user is parent of dependant
        query = """
            SELECT COUNT(*)
            FROM dependants d
            JOIN client_profiles cp1 ON d.primary_client_id = cp1.client_id
            LEFT JOIN client_profiles cp2 ON d.secondary_client_id = cp2.client_id
            WHERE d.dependant_id = $1 
            AND (cp1.user_id = $2 OR cp2.user_id = $2)
            AND d.deleted_at IS NULL
        """
        
        try:
            count = await db.fetchval(query, resource_id, user.user_id)
            return count > 0
        except Exception as e:
            logger.error(f"Error checking parent-child relationship: {e}")
            return False
    
    elif relationship_type == "tutor-client":
        # Check if tutor is assigned to client's dependants
        query = """
            SELECT COUNT(*)
            FROM tutor_assignments ta
            JOIN tutor_profiles tp ON ta.tutor_id = tp.tutor_id
            JOIN dependants d ON ta.dependant_id = d.dependant_id
            WHERE tp.user_id = $1 
            AND (d.primary_client_id = $2 OR d.secondary_client_id = $2)
            AND ta.is_active = true
            AND ta.deleted_at IS NULL
        """
        
        try:
            count = await db.fetchval(query, user.user_id, resource_id)
            return count > 0
        except Exception as e:
            logger.error(f"Error checking tutor-client relationship: {e}")
            return False
    
    elif relationship_type == "client-tutor":
        # Check if client has tutor assigned to their dependants
        query = """
            SELECT COUNT(*)
            FROM tutor_assignments ta
            JOIN client_profiles cp ON ta.dependant_id IN (
                SELECT dependant_id FROM dependants 
                WHERE primary_client_id = cp.client_id OR secondary_client_id = cp.client_id
            )
            WHERE cp.user_id = $1 
            AND ta.tutor_id = $2
            AND ta.is_active = true
            AND ta.deleted_at IS NULL
        """
        
        try:
            count = await db.fetchval(query, user.user_id, resource_id)
            return count > 0
        except Exception as e:
            logger.error(f"Error checking client-tutor relationship: {e}")
            return False
    
    elif relationship_type == "appointment-participant":
        # Check if user is participant in appointment
        query = """
            SELECT COUNT(*)
            FROM appointment_sessions a
            LEFT JOIN client_profiles cp ON a.client_id = cp.client_id
            LEFT JOIN tutor_profiles tp ON a.tutor_id = tp.tutor_id
            WHERE a.appointment_id = $1 
            AND (cp.user_id = $2 OR tp.user_id = $2)
            AND a.deleted_at IS NULL
        """
        
        try:
            count = await db.fetchval(query, resource_id, user.user_id)
            return count > 0
        except Exception as e:
            logger.error(f"Error checking appointment participant relationship: {e}")
            return False
    
    return False


class PermissionDependency:
    """
    FastAPI dependency class for permission checking.
    
    Example:
        @router.get(
            "/clients/{client_id}",
            dependencies=[Depends(PermissionDependency(ResourceType.CLIENT_PROFILE, PermissionAction.VIEW, "client_id"))]
        )
        async def get_client(client_id: int):
            return {"client_id": client_id}
    """
    
    def __init__(
        self,
        resource_type: ResourceType,
        action: PermissionAction,
        resource_id_param: Optional[str] = None
    ):
        self.resource_type = resource_type
        self.action = action
        self.resource_id_param = resource_id_param
    
    async def __call__(
        self,
        request: Request,
        current_user: User = Depends(get_current_active_user),
        db: asyncpg.Connection = Depends(get_database)
    ) -> User:
        """Check permission and return user if authorized."""
        
        # Get resource ID from path parameters
        resource_id = None
        if self.resource_id_param:
            resource_id = request.path_params.get(self.resource_id_param)
            if resource_id is None:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Resource ID parameter '{self.resource_id_param}' is required"
                )
        
        # Check permission
        has_permission = await permission_engine.check_permission(
            user=current_user,
            resource_type=self.resource_type,
            action=self.action,
            resource_id=resource_id,
            db=db
        )
        
        if not has_permission:
            logger.warning(
                f"Permission denied: User {current_user.user_id} cannot {self.action.value} "
                f"{self.resource_type.value} {resource_id or 'N/A'}"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You do not have permission to perform this action"
            )
        
        return current_user


def audit_access(action_description: str):
    """
    Decorator to audit access attempts for security monitoring.
    
    Args:
        action_description: Description of the action being performed
    
    Example:
        @audit_access("viewed client profile")
        async def get_client_profile(client_id: int):
            return {"client_id": client_id}
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract user from kwargs
            current_user = kwargs.get('current_user')
            
            # Get function signature to extract parameters
            sig = inspect.signature(func)
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()
            
            # Extract relevant identifiers
            resource_ids = {}
            for param_name, param_value in bound_args.arguments.items():
                if param_name.endswith('_id') and isinstance(param_value, int):
                    resource_ids[param_name] = param_value
            
            # Log access attempt
            user_id = current_user.user_id if current_user else "anonymous"
            logger.info(
                f"AUDIT: User {user_id} {action_description} "
                f"with parameters {resource_ids}"
            )
            
            try:
                result = await func(*args, **kwargs)
                
                # Log successful access
                logger.info(
                    f"AUDIT SUCCESS: User {user_id} successfully {action_description}"
                )
                
                return result
                
            except HTTPException as e:
                # Log failed access with error code
                logger.warning(
                    f"AUDIT FAILURE: User {user_id} failed to {action_description} "
                    f"- HTTP {e.status_code}: {e.detail}"
                )
                raise
            except Exception as e:
                # Log unexpected errors
                logger.error(
                    f"AUDIT ERROR: User {user_id} encountered error while {action_description} "
                    f"- {type(e).__name__}: {str(e)}"
                )
                raise
        
        return wrapper
    return decorator