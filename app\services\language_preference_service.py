"""
Language Preference Service for TutorAide Application.
Manages user language preferences with Quebec French support and intelligent detection.
"""

import logging
from typing import Optional, Dict, Any, List, Tuple
from datetime import datetime, date
from fastapi import Request

from app.database.repositories.language_preference_repository import LanguagePreferenceRepository
from app.models.language_preference_models import (
    UserLanguagePreference, LanguagePreferenceUpdateRequest, LanguagePreferenceResponse,
    LanguageDetectionResult, UserLanguageContext, LanguageSource, ChangeSource,
    LanguagePreferenceStats, BulkLanguageUpdate, BulkLanguageUpdateResult
)
from app.locales.constants import SUPPORTED_LANGUAGES, DEFAULT_LANGUAGE
from app.locales.language_detector import LanguageDetector
from app.core.exceptions import ValidationError, ResourceNotFoundError


logger = logging.getLogger(__name__)


class LanguagePreferenceService:
    """Service for managing user language preferences with intelligent detection."""
    
    def __init__(self):
        self.repository = LanguagePreferenceRepository()
        self.language_detector = LanguageDetector()
    
    async def get_user_language_context(
        self,
        user_id: Optional[int],
        request: Request
    ) -> UserLanguageContext:
        """
        Get complete language context for a user including preferences and detection.
        """
        user_preference = None
        if user_id:
            user_preference = await self.repository.get_user_language_preference(user_id)
        
        # Detect language from request context
        detected_language = self.language_detector.detect_language_from_request(request)
        
        # Determine effective language based on preferences and detection
        effective_language = self._determine_effective_language(
            user_preference, detected_language, request
        )
        
        # Get Quebec French indicators
        quebec_indicators = self._get_quebec_french_indicators(request, detected_language)
        
        # Get session language if available
        session_language = getattr(request.state, 'language', None)
        
        return UserLanguageContext(
            user_preference=user_preference,
            detected_language=detected_language,
            effective_language=effective_language,
            auto_detect_enabled=user_preference.language_auto_detect if user_preference else True,
            quebec_french_indicators=quebec_indicators,
            session_language=session_language
        )
    
    def _determine_effective_language(
        self,
        user_preference: Optional[UserLanguagePreference],
        detected_language: LanguageDetectionResult,
        request: Request
    ) -> str:
        """Determine the effective language to use based on preferences and context."""
        
        # Priority order:
        # 1. User manual preference (if auto-detect is disabled)
        # 2. Session language override
        # 3. User preference with auto-detect
        # 4. Detected language
        # 5. Default language
        
        # Check session override
        session_language = getattr(request.state, 'language', None)
        if session_language and session_language in SUPPORTED_LANGUAGES:
            return session_language
        
        # User has manual preference and auto-detect disabled
        if (user_preference and 
            not user_preference.language_auto_detect and 
            user_preference.preferred_language in SUPPORTED_LANGUAGES):
            return user_preference.preferred_language
        
        # User has preference and auto-detect enabled - consider detection confidence
        if user_preference and user_preference.language_auto_detect:
            # High confidence detection overrides user preference
            if detected_language.confidence > 0.8:
                return detected_language.detected_language
            # Otherwise use user preference
            elif user_preference.preferred_language in SUPPORTED_LANGUAGES:
                return user_preference.preferred_language
        
        # Fall back to detection or default
        if detected_language.detected_language in SUPPORTED_LANGUAGES:
            return detected_language.detected_language
        
        return DEFAULT_LANGUAGE
    
    def _get_quebec_french_indicators(
        self,
        request: Request,
        detected_language: LanguageDetectionResult
    ) -> List[str]:
        """Get indicators that suggest Quebec French preference."""
        indicators = []
        
        # Check Quebec-specific indicators from detection
        if detected_language.quebec_indicators:
            indicators.extend(detected_language.quebec_indicators)
        
        # Check IP-based location (if available)
        client_ip = request.client.host if request.client else None
        if client_ip:
            # This would typically use a GeoIP service
            # For now, we'll use a simple heuristic
            pass
        
        # Check browser timezone (Quebec uses EST/EDT)
        user_agent = request.headers.get('user-agent', '')
        if 'Quebec' in user_agent or 'Montreal' in user_agent:
            indicators.append('user_agent_location')
        
        return indicators
    
    async def update_user_language_preference(
        self,
        user_id: int,
        update_request: LanguagePreferenceUpdateRequest,
        request: Request,
        updated_by: Optional[int] = None
    ) -> LanguagePreferenceResponse:
        """Update user's language preference with context tracking."""
        
        try:
            # Validate language
            if update_request.preferred_language not in SUPPORTED_LANGUAGES:
                raise ValidationError(f"Unsupported language: {update_request.preferred_language}")
            
            # Get current preference for comparison
            current_preference = await self.repository.get_user_language_preference(user_id)
            
            # Determine language source
            language_source = LanguageSource.MANUAL
            
            # Build change context
            change_context = {
                'new_language': update_request.preferred_language,
                'reason': 'Manual language preference update',
                'browser_language': request.headers.get('accept-language'),
                'ip_address': request.client.host if request.client else None,
                'user_agent': request.headers.get('user-agent'),
                'session_id': getattr(request.state, 'session_id', None),
                'updated_by': updated_by
            }
            
            # Update preference
            updated_preference = await self.repository.update_user_language_preference(
                user_id=user_id,
                preferred_language=update_request.preferred_language,
                language_auto_detect=update_request.language_auto_detect,
                language_source=language_source,
                quebec_french_preference=update_request.quebec_french_preference,
                change_context=change_context
            )
            
            # Get effective language after update
            language_context = await self.get_user_language_context(user_id, request)
            
            # Update request state if persisting
            if update_request.persist:
                request.state.language = updated_preference.preferred_language
                request.state.language_info = {
                    'language': updated_preference.preferred_language,
                    'source': 'user_preference',
                    'auto_detect': updated_preference.language_auto_detect,
                    'quebec_french': updated_preference.quebec_french_preference
                }
            
            return LanguagePreferenceResponse(
                success=True,
                preferred_language=updated_preference.preferred_language,
                language_auto_detect=updated_preference.language_auto_detect,
                language_source=updated_preference.language_source,
                quebec_french_preference=updated_preference.quebec_french_preference,
                effective_language=language_context.effective_language,
                updated_at=updated_preference.language_updated_at,
                message="Language preference updated successfully"
            )
            
        except ValidationError:
            raise
        except Exception as e:
            logger.error(f"Failed to update language preference for user {user_id}: {e}")
            raise ValidationError(f"Failed to update language preference: {str(e)}")
    
    async def detect_and_set_language_preference(
        self,
        user_id: int,
        request: Request,
        force_update: bool = False
    ) -> LanguagePreferenceResponse:
        """Detect language from context and update user preference if appropriate."""
        
        try:
            # Get current preference
            current_preference = await self.repository.get_user_language_preference(user_id)
            
            # Skip if user has manual preference and auto-detect is disabled
            if (current_preference and 
                not current_preference.language_auto_detect and 
                not force_update):
                
                return LanguagePreferenceResponse(
                    success=True,
                    preferred_language=current_preference.preferred_language,
                    language_auto_detect=current_preference.language_auto_detect,
                    language_source=current_preference.language_source,
                    quebec_french_preference=current_preference.quebec_french_preference,
                    effective_language=current_preference.preferred_language,
                    updated_at=current_preference.language_updated_at,
                    message="Auto-detection disabled, using manual preference"
                )
            
            # Detect language
            detected_language = self.language_detector.detect_language_from_request(request)
            
            # Only update if detection confidence is high enough
            min_confidence = 0.7
            if detected_language.confidence < min_confidence and not force_update:
                effective_language = (current_preference.preferred_language 
                                    if current_preference else DEFAULT_LANGUAGE)
                
                return LanguagePreferenceResponse(
                    success=True,
                    preferred_language=effective_language,
                    language_auto_detect=True,
                    language_source=LanguageSource.AUTO,
                    quebec_french_preference=True,
                    effective_language=effective_language,
                    updated_at=datetime.utcnow(),
                    message=f"Detection confidence too low ({detected_language.confidence:.2f})"
                )
            
            # Build change context
            change_context = {
                'new_language': detected_language.detected_language,
                'reason': f'Auto-detection (confidence: {detected_language.confidence:.2f})',
                'browser_language': request.headers.get('accept-language'),
                'ip_address': request.client.host if request.client else None,
                'user_agent': request.headers.get('user-agent'),
                'session_id': getattr(request.state, 'session_id', None),
                'detection_sources': detected_language.sources_tried
            }
            
            # Determine Quebec French preference
            quebec_french_pref = (detected_language.detected_language == 'fr' and 
                                len(detected_language.quebec_indicators or []) > 0)
            
            # Update preference
            updated_preference = await self.repository.update_user_language_preference(
                user_id=user_id,
                preferred_language=detected_language.detected_language,
                language_auto_detect=True,
                language_source=LanguageSource.AUTO,
                quebec_french_preference=quebec_french_pref,
                change_context=change_context
            )
            
            # Update request state
            request.state.language = updated_preference.preferred_language
            request.state.language_info = {
                'language': updated_preference.preferred_language,
                'source': 'auto_detection',
                'confidence': detected_language.confidence,
                'quebec_french': updated_preference.quebec_french_preference
            }
            
            return LanguagePreferenceResponse(
                success=True,
                preferred_language=updated_preference.preferred_language,
                language_auto_detect=updated_preference.language_auto_detect,
                language_source=updated_preference.language_source,
                quebec_french_preference=updated_preference.quebec_french_preference,
                effective_language=updated_preference.preferred_language,
                updated_at=updated_preference.language_updated_at,
                message=f"Language auto-detected and updated (confidence: {detected_language.confidence:.2f})"
            )
            
        except Exception as e:
            logger.error(f"Failed to detect and set language for user {user_id}: {e}")
            raise ValidationError(f"Failed to detect language: {str(e)}")
    
    async def get_user_preference_with_fallback(
        self,
        user_id: Optional[int],
        request: Request
    ) -> Tuple[str, Dict[str, Any]]:
        """
        Get user language preference with intelligent fallback.
        Returns tuple of (language, context_info).
        """
        
        context_info = {
            'source': 'default',
            'confidence': 1.0,
            'fallback_used': False,
            'auto_detect_enabled': True
        }
        
        if user_id:
            try:
                language_context = await self.get_user_language_context(user_id, request)
                
                context_info.update({
                    'source': language_context.detected_language.source,
                    'confidence': language_context.detected_language.confidence,
                    'fallback_used': language_context.detected_language.fallback_used,
                    'auto_detect_enabled': language_context.auto_detect_enabled,
                    'quebec_indicators': language_context.quebec_french_indicators
                })
                
                return language_context.effective_language, context_info
                
            except Exception as e:
                logger.warning(f"Failed to get language context for user {user_id}: {e}")
                context_info['fallback_used'] = True
                context_info['error'] = str(e)
        
        # Fallback to detection without user context
        try:
            detected = self.language_detector.detect_language_from_request(request)
            context_info.update({
                'source': detected.source,
                'confidence': detected.confidence,
                'fallback_used': detected.fallback_used or context_info['fallback_used']
            })
            return detected.detected_language, context_info
            
        except Exception as e:
            logger.warning(f"Language detection failed: {e}")
            context_info.update({
                'source': 'system_default',
                'fallback_used': True,
                'error': str(e)
            })
            return DEFAULT_LANGUAGE, context_info
    
    async def get_language_preference_stats(self) -> LanguagePreferenceStats:
        """Get comprehensive language preference statistics."""
        return await self.repository.get_language_preference_stats()
    
    async def bulk_update_language_preferences(
        self,
        bulk_update: BulkLanguageUpdate,
        updated_by: Optional[int] = None
    ) -> BulkLanguageUpdateResult:
        """Bulk update language preferences for multiple users."""
        
        try:
            return await self.repository.bulk_update_language_preferences(
                user_ids=bulk_update.user_ids,
                preferred_language=bulk_update.preferred_language,
                language_auto_detect=bulk_update.language_auto_detect,
                quebec_french_preference=bulk_update.quebec_french_preference,
                change_reason=bulk_update.change_reason,
                updated_by=updated_by
            )
            
        except Exception as e:
            logger.error(f"Bulk language update failed: {e}")
            return BulkLanguageUpdateResult(
                success=False,
                updated_count=0,
                failed_count=len(bulk_update.user_ids),
                failed_user_ids=bulk_update.user_ids,
                errors=[str(e)],
                message="Bulk update failed due to system error"
            )
    
    async def get_users_by_language(
        self,
        language: str,
        auto_detect_only: bool = False,
        quebec_french_only: bool = False,
        limit: int = 100,
        offset: int = 0
    ) -> Tuple[List[int], int]:
        """Get users filtered by language preferences."""
        
        if language not in SUPPORTED_LANGUAGES:
            raise ValidationError(f"Unsupported language: {language}")
        
        return await self.repository.get_users_by_language_preference(
            language=language,
            auto_detect_only=auto_detect_only,
            quebec_french_only=quebec_french_only,
            limit=limit,
            offset=offset
        )
    
    async def migrate_user_to_preferred_language(
        self,
        user_id: int,
        target_language: str,
        reason: str = "System migration",
        migrate_history: bool = True
    ) -> bool:
        """Migrate a user to a specific language preference (admin function)."""
        
        try:
            if target_language not in SUPPORTED_LANGUAGES:
                raise ValidationError(f"Unsupported language: {target_language}")
            
            # Get current preference
            current = await self.repository.get_user_language_preference(user_id)
            if not current:
                raise ResourceNotFoundError(f"User {user_id} not found")
            
            # Skip if already using target language
            if current.preferred_language == target_language:
                return True
            
            # Update preference
            await self.repository.update_user_language_preference(
                user_id=user_id,
                preferred_language=target_language,
                language_source=LanguageSource.SYSTEM,
                change_context={
                    'new_language': target_language,
                    'reason': reason,
                    'migration': True
                }
            )
            
            logger.info(f"Migrated user {user_id} to language {target_language}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to migrate user {user_id} to {target_language}: {e}")
            return False
    
    async def update_daily_analytics(self, analytics_date: date = None) -> None:
        """Update daily language usage analytics."""
        await self.repository.update_daily_analytics(analytics_date)
    
    async def cleanup_old_data(self, days_to_keep: int = 365) -> Dict[str, int]:
        """Clean up old language preference data."""
        
        try:
            history_deleted = await self.repository.cleanup_old_preference_history(days_to_keep)
            
            return {
                'history_records_deleted': history_deleted,
                'days_kept': days_to_keep
            }
            
        except Exception as e:
            logger.error(f"Failed to cleanup old language data: {e}")
            return {
                'history_records_deleted': 0,
                'days_kept': days_to_keep,
                'error': str(e)
            }


# Global service instance
_language_preference_service = None


def get_language_preference_service() -> LanguagePreferenceService:
    """Get the global language preference service instance."""
    global _language_preference_service
    if _language_preference_service is None:
        _language_preference_service = LanguagePreferenceService()
    return _language_preference_service