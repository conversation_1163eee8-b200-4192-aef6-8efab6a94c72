/**
 * React Hook for Locale-Specific Formatting
 * 
 * Provides formatting functions that automatically adapt to the user's
 * current language and Quebec preferences. Integrates with the existing
 * translation system and caches formatters for performance.
 */

import { useMemo, useCallback } from 'react';
import { useLanguage } from './useLanguage';
import { 
  LocaleFormatter, 
  Locale, 
  FormatOptions, 
  AddressFormat, 
  PhoneFormat 
} from '../services/formatters';

export interface FormattingPreferences {
  dateFormat?: 'short' | 'medium' | 'long';
  timeFormat?: '12h' | '24h';
  currencyDisplay?: 'symbol' | 'code';
  numberPrecision?: number;
}

export const useFormatting = (overrideOptions?: FormatOptions) => {
  const { currentLanguage, languageInfo } = useLanguage();

  // Create formatter instance with current locale settings
  const formatter = useMemo(() => {
    const options: FormatOptions = {
      locale: currentLanguage as Locale,
      quebecFrench: languageInfo?.quebec_french || false,
      ...overrideOptions
    };

    return new LocaleFormatter(options);
  }, [currentLanguage, languageInfo?.quebec_french, overrideOptions]);

  // Date formatting functions
  const formatDate = useCallback((
    date: Date | string | number,
    format: 'short' | 'medium' | 'long' | 'full' | string = 'medium'
  ): string => {
    return formatter.formatDate(date, format);
  }, [formatter]);

  const formatTime = useCallback((
    date: Date | string | number,
    format24h?: boolean
  ): string => {
    return formatter.formatTime(date, format24h);
  }, [formatter]);

  const formatDateTime = useCallback((
    date: Date | string | number,
    dateFormat: 'short' | 'medium' | 'long' = 'medium',
    format24h?: boolean
  ): string => {
    return formatter.formatDateTime(date, dateFormat, format24h);
  }, [formatter]);

  const formatRelativeTime = useCallback((
    date: Date | string | number
  ): string => {
    return formatter.formatRelativeTime(date);
  }, [formatter]);

  // Currency formatting functions
  const formatCurrency = useCallback((
    amount: number,
    options?: { showCode?: boolean; precision?: number }
  ): string => {
    return formatter.formatCurrency(amount, options);
  }, [formatter]);

  const formatTaxes = useCallback((
    amount: number,
    quebecTax?: number,
    federalTax?: number
  ): string => {
    return formatter.formatTaxes(amount, quebecTax, federalTax);
  }, [formatter]);

  // Number formatting functions
  const formatNumber = useCallback((
    num: number,
    options?: {
      precision?: number;
      useGrouping?: boolean;
      style?: 'decimal' | 'percent';
    }
  ): string => {
    return formatter.formatNumber(num, options);
  }, [formatter]);

  const formatLargeNumber = useCallback((num: number): string => {
    return formatter.formatLargeNumber(num);
  }, [formatter]);

  const formatPercentage = useCallback((
    value: number,
    precision?: number
  ): string => {
    return formatter.formatPercentage(value, precision);
  }, [formatter]);

  // Phone formatting functions
  const formatPhone = useCallback((
    phone: string | PhoneFormat,
    international?: boolean
  ): string => {
    return formatter.formatPhone(phone, international);
  }, [formatter]);

  const isQuebecAreaCode = useCallback((phone: string): boolean => {
    return formatter.isQuebecAreaCode(phone);
  }, [formatter]);

  // Address formatting functions
  const formatPostalCode = useCallback((postalCode: string): string => {
    return formatter.formatPostalCode(postalCode);
  }, [formatter]);

  const formatAddress = useCallback((
    address: AddressFormat,
    multiline?: boolean
  ): string => {
    return formatter.formatAddress(address, multiline);
  }, [formatter]);

  // Utility functions
  const getSeparators = useCallback(() => {
    return formatter.getSeparators();
  }, [formatter]);

  // Specialized business formatting for TutorAide
  const formatSessionDuration = useCallback((minutes: number): string => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;

    if (currentLanguage === 'fr') {
      if (hours > 0 && mins > 0) {
        return `${hours}h ${mins}min`;
      } else if (hours > 0) {
        return `${hours}h`;
      } else {
        return `${mins}min`;
      }
    } else {
      if (hours > 0 && mins > 0) {
        return `${hours}h ${mins}m`;
      } else if (hours > 0) {
        return `${hours}h`;
      } else {
        return `${mins}m`;
      }
    }
  }, [currentLanguage]);

  const formatAppointmentTime = useCallback((
    startTime: Date | string,
    endTime: Date | string,
    format24h?: boolean
  ): string => {
    const start = formatTime(startTime, format24h);
    const end = formatTime(endTime, format24h);
    
    if (currentLanguage === 'fr') {
      return `${start} à ${end}`;
    }
    return `${start} - ${end}`;
  }, [currentLanguage, formatTime]);

  const formatInvoiceNumber = useCallback((
    invoiceId: number,
    prefix: string = 'INV'
  ): string => {
    const paddedId = invoiceId.toString().padStart(6, '0');
    return `${prefix}-${paddedId}`;
  }, []);

  const formatGrade = useCallback((grade: string | number): string => {
    if (typeof grade === 'number') {
      if (currentLanguage === 'fr') {
        // Quebec education system
        if (grade <= 6) return `${grade}e année`;
        if (grade <= 11) return `${grade - 6}e secondaire`;
        return `Cégep ${grade - 11}`;
      } else {
        // English Canadian system
        if (grade <= 6) return `Grade ${grade}`;
        if (grade <= 12) return `Grade ${grade}`;
        return `Year ${grade - 12}`;
      }
    }
    return String(grade);
  }, [currentLanguage]);

  const formatTutorRate = useCallback((
    rate: number,
    per: 'hour' | 'session' = 'hour'
  ): string => {
    const formattedRate = formatCurrency(rate);
    
    if (currentLanguage === 'fr') {
      return per === 'hour' ? `${formattedRate}/heure` : `${formattedRate}/séance`;
    }
    return per === 'hour' ? `${formattedRate}/hour` : `${formattedRate}/session`;
  }, [currentLanguage, formatCurrency]);

  const formatDistance = useCallback((kilometers: number): string => {
    if (kilometers < 1) {
      const meters = Math.round(kilometers * 1000);
      return currentLanguage === 'fr' ? `${meters} m` : `${meters} m`;
    }
    
    const formattedKm = formatNumber(kilometers, { precision: 1 });
    return currentLanguage === 'fr' ? `${formattedKm} km` : `${formattedKm} km`;
  }, [currentLanguage, formatNumber]);

  const formatSubjects = useCallback((subjects: string[]): string => {
    if (subjects.length === 0) return '';
    if (subjects.length === 1) return subjects[0];
    
    const lastSubject = subjects[subjects.length - 1];
    const otherSubjects = subjects.slice(0, -1);
    
    if (currentLanguage === 'fr') {
      return `${otherSubjects.join(', ')} et ${lastSubject}`;
    }
    return `${otherSubjects.join(', ')} and ${lastSubject}`;
  }, [currentLanguage]);

  // Business-specific date formatting
  const formatAppointmentDate = useCallback((
    date: Date | string,
    includeDay: boolean = true
  ): string => {
    if (includeDay) {
      return formatDate(date, currentLanguage === 'fr' ? 'EEEE, d MMMM yyyy' : 'EEEE, MMMM d, yyyy');
    }
    return formatDate(date, currentLanguage === 'fr' ? 'd MMMM yyyy' : 'MMMM d, yyyy');
  }, [currentLanguage, formatDate]);

  const formatBillingPeriod = useCallback((
    startDate: Date | string,
    endDate: Date | string
  ): string => {
    const start = formatDate(startDate, 'short');
    const end = formatDate(endDate, 'short');
    
    if (currentLanguage === 'fr') {
      return `Période: ${start} au ${end}`;
    }
    return `Period: ${start} to ${end}`;
  }, [currentLanguage, formatDate]);

  // Input validation helpers
  const validatePostalCode = useCallback((postalCode: string): boolean => {
    const clean = postalCode.replace(/\s/g, '').toUpperCase();
    return /^[A-Z]\d[A-Z]\d[A-Z]\d$/.test(clean);
  }, []);

  const validatePhoneNumber = useCallback((phone: string): boolean => {
    const digits = phone.replace(/\D/g, '');
    return digits.length === 10;
  }, []);

  // Format for accessibility (screen readers)
  const formatForAccessibility = useCallback((
    text: string,
    type: 'currency' | 'date' | 'time' | 'phone' | 'number'
  ): string => {
    // Add ARIA-friendly formatting for screen readers
    switch (type) {
      case 'currency':
        return text.replace(/\$/g, currentLanguage === 'fr' ? 'dollars ' : 'dollars ');
      case 'phone':
        return text.replace(/[()-]/g, ' ').replace(/-/g, ' ');
      case 'date':
        return text; // Date formatting is already accessible
      case 'time':
        return text.replace(/:/g, currentLanguage === 'fr' ? ' heures ' : ' ');
      case 'number':
        return text.replace(/,/g, ' ');
      default:
        return text;
    }
  }, [currentLanguage]);

  return {
    // Core formatting functions
    formatDate,
    formatTime,
    formatDateTime,
    formatRelativeTime,
    formatCurrency,
    formatTaxes,
    formatNumber,
    formatLargeNumber,
    formatPercentage,
    formatPhone,
    formatPostalCode,
    formatAddress,
    
    // Business-specific formatters
    formatSessionDuration,
    formatAppointmentTime,
    formatAppointmentDate,
    formatInvoiceNumber,
    formatGrade,
    formatTutorRate,
    formatDistance,
    formatSubjects,
    formatBillingPeriod,
    
    // Validation functions
    isQuebecAreaCode,
    validatePostalCode,
    validatePhoneNumber,
    
    // Utility functions
    getSeparators,
    formatForAccessibility,
    
    // Formatter instance for advanced use
    formatter,
    
    // Current locale info
    currentLocale: currentLanguage as Locale,
    isQuebecFrench: languageInfo?.quebec_french || false,
    
    // Clear cache
    clearCache: formatter.clearCache.bind(formatter)
  };
};

export default useFormatting;