import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { X, Package, Edit, Trash2, Plus } from 'lucide-react';
import { Modal } from '../common/Modal';
import Button from '../common/Button';
import { Badge } from '../common/Badge';
import { serviceService, ServicePackage } from '../../services/serviceService';
import toast from 'react-hot-toast';
import AddServiceModal from './AddServiceModal';

interface ManagePackagesModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUpdate: () => void;
}

const ManagePackagesModal: React.FC<ManagePackagesModalProps> = ({
  isOpen,
  onClose,
  onUpdate
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [packages, setPackages] = useState<ServicePackage[]>([]);
  const [showAddModal, setShowAddModal] = useState(false);

  useEffect(() => {
    if (isOpen) {
      loadPackages();
    }
  }, [isOpen]);

  const loadPackages = async () => {
    try {
      const packagesData = await serviceService.getServicePackages();
      setPackages(packagesData);
    } catch (error) {
      console.error('Error loading packages:', error);
      toast.error(t('services.packages.loadError'));
    }
  };

  const handleDelete = async (pkg: ServicePackage) => {
    if (!confirm(t('services.packages.confirmDelete'))) return;

    setLoading(true);
    try {
      await serviceService.deletePackage(pkg.package_id);
      toast.success(t('services.packages.deleteSuccess'));
      loadPackages();
      onUpdate();
    } catch (error) {
      console.error('Error deleting package:', error);
      toast.error(t('services.packages.deleteError'));
    } finally {
      setLoading(false);
    }
  };

  const handleToggleActive = async (pkg: ServicePackage) => {
    setLoading(true);
    try {
      await serviceService.updatePackage(pkg.package_id, {
        is_active: !pkg.is_active
      });
      toast.success(t('services.packages.updateSuccess'));
      loadPackages();
      onUpdate();
    } catch (error) {
      console.error('Error updating package:', error);
      toast.error(t('services.packages.updateError'));
    } finally {
      setLoading(false);
    }
  };

  const handleAddComplete = () => {
    setShowAddModal(false);
    loadPackages();
    onUpdate();
  };

  return (
    <>
      <Modal isOpen={isOpen} onClose={onClose} size="xl">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900">
              {t('services.packages.manage')}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Add Package Button */}
          <div className="mb-6">
            <Button
              variant="primary"
              onClick={() => setShowAddModal(true)}
            >
              <Plus className="w-4 h-4 mr-2" />
              {t('services.packages.add')}
            </Button>
          </div>

          {/* Packages List */}
          <div className="space-y-4">
            {packages.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Package className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                <p>{t('services.packages.empty')}</p>
              </div>
            ) : (
              packages.map((pkg) => (
                <div
                  key={pkg.package_id}
                  className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">{pkg.name}</h3>
                        {pkg.is_tecfee && (
                          <Badge variant="warning" size="sm">TECFEE</Badge>
                        )}
                        <Badge variant={pkg.is_active ? 'success' : 'secondary'} size="sm">
                          {pkg.is_active ? t('common.active') : t('common.inactive')}
                        </Badge>
                      </div>
                      <div className="text-sm text-gray-600 mb-2">
                        {t('services.packages.service')}: {pkg.service_name}
                      </div>
                      {pkg.description && (
                        <p className="text-sm text-gray-600 mb-3">{pkg.description}</p>
                      )}
                      <div className="grid grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="text-gray-600">{t('services.packages.sessions')}:</span>
                          <span className="font-semibold ml-1">{pkg.session_count}</span>
                        </div>
                        <div>
                          <span className="text-gray-600">{t('services.packages.totalHours')}:</span>
                          <span className="font-semibold ml-1">{pkg.total_hours} hrs</span>
                        </div>
                        <div>
                          <span className="text-gray-600">{t('services.packages.price')}:</span>
                          <span className="font-semibold ml-1">${pkg.price}</span>
                        </div>
                        <div>
                          <span className="text-gray-600">{t('services.packages.validity')}:</span>
                          <span className="font-semibold ml-1">{pkg.valid_days} days</span>
                        </div>
                      </div>
                      {pkg.discount_percentage && pkg.discount_percentage > 0 && (
                        <div className="mt-2">
                          <Badge variant="success" size="sm">
                            {pkg.discount_percentage}% {t('common.discount')}
                          </Badge>
                        </div>
                      )}
                      {pkg.features && pkg.features.length > 0 && (
                        <div className="mt-3">
                          <div className="text-sm text-gray-600 mb-1">{t('services.packages.features')}:</div>
                          <div className="flex flex-wrap gap-2">
                            {pkg.features.map((feature, idx) => (
                              <Badge key={idx} variant="secondary" size="sm">{feature}</Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                    <div className="flex items-center space-x-2 ml-4">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleToggleActive(pkg)}
                        loading={loading}
                      >
                        {pkg.is_active ? t('common.deactivate') : t('common.activate')}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-red-600 hover:text-red-700"
                        onClick={() => handleDelete(pkg)}
                        loading={loading}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Footer */}
          <div className="mt-6 pt-6 border-t flex justify-end">
            <Button variant="secondary" onClick={onClose}>
              {t('common.close')}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Add Package Modal */}
      {showAddModal && (
        <AddServiceModal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
          onSuccess={handleAddComplete}
          mode="packages"
        />
      )}
    </>
  );
};

export default ManagePackagesModal;