# How to Check Railway Logs for Password Reset

The password reset request was successfully sent to the Railway API. Here's how to check if the email was actually sent:

## 1. Using Railway CLI

If you have Railway CLI installed:

```bash
# Login to Railway
railway login

# Link to your project
railway link

# View recent logs
railway logs

# Filter for email-related logs
railway logs | grep -E "(EmailService|password reset|danny.quan)"
```

## 2. Using Railway Dashboard

1. Go to https://railway.app/dashboard
2. Select your project: **tutoraide-production**
3. Click on the **web** service
4. Click on the **Logs** tab
5. Look for entries containing:
   - `=== PASSWORD RESET REQUEST DEBUG ===`
   - `EmailService`
   - `<EMAIL>`
   - `Password reset email sent successfully`

## 3. What to Look For

### Success Indicators:
- `Password reset token creation result: {'email': '<EMAIL>', ...}`
- `Sending password reset email to: <EMAIL>`
- `Password reset email sent <NAME_EMAIL>`

### Error Indicators:
- `Failed to send password reset email`
- `SMTP` errors
- `Failed to create auth token: relation "auth_tokens" does not exist`

## 4. Database Check

You can also check if the reset token was created in the database:

```sql
-- Connect to your Railway PostgreSQL
-- Run this query to see recent password reset tokens
SELECT 
    token_id, 
    user_id, 
    token_type, 
    expires_at, 
    created_at,
    metadata->>'email' as email
FROM auth_tokens
WHERE token_type = 'password_reset'
AND created_at > NOW() - INTERVAL '10 minutes'
ORDER BY created_at DESC;
```

## 5. Test Endpoints

If you need more debugging, you can use these test endpoints:

### Check Email Configuration:
```bash
curl https://tutoraide-production.up.railway.app/api/v1/auth/email/config-check \
  -H "Authorization: Bearer YOUR_MANAGER_TOKEN"
```

### Direct Email Test (requires manager token):
```bash
curl -X POST https://tutoraide-production.up.railway.app/api/v1/auth/email/test-send \
  -H "Authorization: Bearer YOUR_MANAGER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"to_email": "<EMAIL>", "subject": "Test Email"}'
```

## Summary

The password reset request was accepted by the API (HTTP 200). To confirm if the email was actually sent:

1. **Check Railway logs** - This is the most important step
2. **Check your email** at <EMAIL>
3. **Check the database** for the auth_tokens entry

The response `"If the email exists, a password reset link has been sent"` is intentionally vague for security reasons, but a 200 status indicates the request was processed successfully.