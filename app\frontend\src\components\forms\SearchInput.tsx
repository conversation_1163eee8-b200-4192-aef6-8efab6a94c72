import React from 'react';
import { clsx } from 'clsx';
import { Search, X } from 'lucide-react';

interface SearchInputProps {
  value: string;
  onChange: (value: string) => void;
  onSearch?: () => void;
  placeholder?: string;
  label?: string;
  hint?: string;
  showClearButton?: boolean;
  autoFocus?: boolean;
  disabled?: boolean;
  fullWidth?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const SearchInput: React.FC<SearchInputProps> = ({
  value,
  onChange,
  onSearch,
  placeholder = 'Search...',
  label,
  hint,
  showClearButton = true,
  autoFocus = false,
  disabled = false,
  fullWidth = false,
  size = 'md',
  className,
}) => {
  const id = React.useId();

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && onSearch) {
      onSearch();
    }
  };

  const handleClear = () => {
    onChange('');
    if (onSearch) {
      onSearch();
    }
  };

  const sizes = {
    sm: 'py-2 pl-9 pr-9 text-sm',
    md: 'py-3 pl-11 pr-11 text-base',
    lg: 'py-4 pl-12 pr-12 text-lg',
  };

  const iconSizes = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
  };

  const iconPositions = {
    sm: 'left-2.5',
    md: 'left-3',
    lg: 'left-3.5',
  };

  return (
    <div className={clsx(fullWidth ? 'w-full' : 'inline-block', className)}>
      {label && (
        <label
          htmlFor={id}
          className="block text-sm font-medium text-text-primary mb-2"
        >
          {label}
        </label>
      )}

      <div className="relative">
        <Search
          className={clsx(
            'absolute top-1/2 -translate-y-1/2 text-text-muted pointer-events-none',
            iconSizes[size],
            iconPositions[size]
          )}
        />
        
        <input
          id={id}
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          autoFocus={autoFocus}
          disabled={disabled}
          className={clsx(
            'w-full border rounded-full bg-background-secondary text-text-primary',
            'transition-all duration-200 focus:outline-none focus:border-accent-red focus:bg-white focus:shadow-focus',
            'placeholder:text-text-muted',
            sizes[size],
            'border-border-primary hover:border-border-secondary',
            disabled && 'opacity-60 cursor-not-allowed bg-background-tertiary'
          )}
        />
        
        {showClearButton && value && !disabled && (
          <button
            type="button"
            onClick={handleClear}
            className={clsx(
              'absolute top-1/2 -translate-y-1/2 right-3 text-text-muted hover:text-text-secondary transition-colors',
              iconSizes[size]
            )}
          >
            <X className="w-full h-full" />
          </button>
        )}
      </div>

      {hint && (
        <p className="mt-2 text-sm text-text-muted">
          {hint}
        </p>
      )}
    </div>
  );
};

interface SearchWithSuggestionsProps extends Omit<SearchInputProps, 'onSearch'> {
  suggestions: string[];
  onSuggestionSelect: (suggestion: string) => void;
  maxSuggestions?: number;
}

export const SearchWithSuggestions: React.FC<SearchWithSuggestionsProps> = ({
  value,
  onChange,
  suggestions,
  onSuggestionSelect,
  maxSuggestions = 5,
  ...props
}) => {
  const [showSuggestions, setShowSuggestions] = React.useState(false);
  const [selectedIndex, setSelectedIndex] = React.useState(-1);
  const containerRef = React.useRef<HTMLDivElement>(null);

  const filteredSuggestions = React.useMemo(() => {
    if (!value) return [];
    
    return suggestions
      .filter(suggestion => 
        suggestion.toLowerCase().includes(value.toLowerCase())
      )
      .slice(0, maxSuggestions);
  }, [value, suggestions, maxSuggestions]);

  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (!showSuggestions || filteredSuggestions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < filteredSuggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : filteredSuggestions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0) {
          onSuggestionSelect(filteredSuggestions[selectedIndex]);
          setShowSuggestions(false);
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        break;
    }
  };

  return (
    <div ref={containerRef} className="relative">
      <SearchInput
        {...props}
        value={value}
        onChange={(newValue) => {
          onChange(newValue);
          setShowSuggestions(true);
          setSelectedIndex(-1);
        }}
        onKeyDown={handleKeyDown}
      />
      
      {showSuggestions && filteredSuggestions.length > 0 && (
        <div className="absolute z-10 mt-1 w-full bg-white border border-border-primary rounded-lg shadow-soft overflow-hidden">
          {filteredSuggestions.map((suggestion, index) => (
            <button
              key={suggestion}
              type="button"
              onClick={() => {
                onSuggestionSelect(suggestion);
                setShowSuggestions(false);
              }}
              className={clsx(
                'w-full px-4 py-2 text-left text-sm transition-colors',
                index === selectedIndex
                  ? 'bg-red-50 text-accent-red'
                  : 'text-text-primary hover:bg-background-secondary'
              )}
            >
              {suggestion}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default SearchInput;