# Fix Railway Database URL - URGENT

## Problem
Your tables exist in the 'postgres' database, but Railway is connecting to the 'railway' database.

## Solution
Update the DATABASE_URL in Railway environment variables:

### Steps:
1. Go to Railway Dashboard: https://railway.app/dashboard
2. Select your project: **tutoraide-production**
3. Click on the **Variables** tab
4. Find the `DATABASE_URL` variable
5. Change the database name from 'railway' to 'postgres':

### Current (WRONG):
```
postgresql://postgres:<EMAIL>:5432/railway
```

### Change to (CORRECT):
```
postgresql://postgres:<EMAIL>:5432/postgres
```

6. Click **Save** or **Update**
7. Railway will automatically redeploy with the new configuration

## Verification
After deployment completes, test the password reset:
```bash
curl -X POST "https://tutoraide-production.up.railway.app/api/v1/auth/password/reset-request" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'
```

## Expected Result
- Should return success message
- Email should be sent
- No more "relation does not exist" errors