"""
Client profile models for TutorAide application.
"""

from datetime import datetime
from typing import Optional, Dict, Any
from enum import Enum

from pydantic import Field, field_validator, model_validator

from app.models.base import BaseModel, BaseEntity


class Province(str, Enum):
    """Canadian provinces and territories."""
    ONTARIO = "ON"
    QUEBEC = "QC"
    BRITISH_COLUMBIA = "BC"
    ALBERTA = "AB"
    MANITOBA = "MB"
    SASKATCHEWAN = "SK"
    NOVA_SCOTIA = "NS"
    NEW_BRUNSWICK = "NB"
    NEWFOUNDLAND = "NL"
    PRINCE_EDWARD_ISLAND = "PE"
    NORTHWEST_TERRITORIES = "NT"
    YUKON = "YT"
    NUNAVUT = "NU"


class CommunicationPreferences(BaseModel):
    """Communication channel preferences."""
    email: bool = True
    sms: bool = True
    push: bool = True


class ClientProfileBase(BaseModel):
    """Base client profile model."""
    # Emergency contact
    emergency_contact_name: Optional[str] = Field(None, max_length=255)
    emergency_contact_phone: Optional[str] = Field(None, max_length=20)
    emergency_contact_relationship: Optional[str] = Field(None, max_length=100)
    
    # Address
    address_line1: Optional[str] = Field(None, max_length=255)
    address_line2: Optional[str] = Field(None, max_length=255)
    city: Optional[str] = Field(None, max_length=100)
    province: Optional[Province] = None
    postal_code: Optional[str] = Field(None, max_length=10)
    
    # Contact
    phone: Optional[str] = Field(None, max_length=20)
    secondary_phone: Optional[str] = Field(None, max_length=20)
    
    # Preferences
    preferred_language: str = Field("en", pattern="^(en|fr)$")
    timezone: str = "America/Toronto"
    communication_preferences: CommunicationPreferences = Field(
        default_factory=CommunicationPreferences
    )
    
    # Notes (only visible to managers)
    internal_notes: Optional[str] = None

    @field_validator("postal_code")
    @classmethod
    def validate_postal_code(cls, v: Optional[str]) -> Optional[str]:
        """Validate Canadian postal code format."""
        if v is None:
            return v
        
        # Remove spaces and convert to uppercase
        v = v.replace(" ", "").upper()
        
        # Canadian postal code pattern: A1A 1A1
        import re
        pattern = r"^[A-Z]\d[A-Z]\d[A-Z]\d$"
        if not re.match(pattern, v):
            raise ValueError("Invalid Canadian postal code format")
        
        # Format with space
        return f"{v[:3]} {v[3:]}"
    
    @field_validator("phone", "secondary_phone", "emergency_contact_phone")
    @classmethod
    def validate_phone(cls, v: Optional[str]) -> Optional[str]:
        """Validate and format phone numbers."""
        if v is None:
            return v
        
        # Remove all non-numeric characters
        import re
        digits = re.sub(r"\D", "", v)
        
        # Validate length (10 digits for North American numbers)
        if len(digits) == 10:
            # Format as (XXX) XXX-XXXX
            return f"({digits[:3]}) {digits[3:6]}-{digits[6:]}"
        elif len(digits) == 11 and digits[0] == "1":
            # Remove country code
            return f"({digits[1:4]}) {digits[4:7]}-{digits[7:]}"
        else:
            raise ValueError("Invalid phone number format")

    @model_validator(mode='after')
    def validate_address_completeness(self) -> 'ClientProfileBase':
        """Ensure address is complete if any part is provided."""
        address_fields = ["address_line1", "city", "province", "postal_code"]
        provided_fields = [f for f in address_fields if getattr(self, f, None)]
        
        if provided_fields and len(provided_fields) < 4:
            missing = [f for f in address_fields if not getattr(self, f, None)]
            raise ValueError(
                f"Incomplete address. Missing required fields: {', '.join(missing)}"
            )
        
        return self


class ClientProfileCreate(ClientProfileBase):
    """Schema for creating a client profile."""
    user_id: int


class ClientProfileUpdate(BaseModel):
    """Schema for updating a client profile."""
    # All fields are optional for updates
    emergency_contact_name: Optional[str] = Field(None, max_length=255)
    emergency_contact_phone: Optional[str] = Field(None, max_length=20)
    emergency_contact_relationship: Optional[str] = Field(None, max_length=100)
    
    address_line1: Optional[str] = Field(None, max_length=255)
    address_line2: Optional[str] = Field(None, max_length=255)
    city: Optional[str] = Field(None, max_length=100)
    province: Optional[Province] = None
    postal_code: Optional[str] = Field(None, max_length=10)
    
    phone: Optional[str] = Field(None, max_length=20)
    secondary_phone: Optional[str] = Field(None, max_length=20)
    
    preferred_language: Optional[str] = Field(None, pattern="^(en|fr)$")
    timezone: Optional[str] = None
    communication_preferences: Optional[CommunicationPreferences] = None
    
    internal_notes: Optional[str] = None


class ClientProfile(ClientProfileBase, BaseEntity):
    """Full client profile model."""
    profile_id: int
    user_id: int
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    deleted_at: Optional[datetime] = None
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True


class ClientProfileWithUser(ClientProfile):
    """Client profile with user information."""
    user_email: str
    user_first_name: str
    user_last_name: str
    user_status: str


class ClientProfilePublic(BaseModel):
    """Public view of client profile (for tutors)."""
    profile_id: int
    first_name: str
    last_name: str
    city: Optional[str] = None
    preferred_language: str = "en"
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True