-- Migration: 025_add_billing_subscriptions.sql
-- Description: Add subscription system with pre-paid hours and package management
-- Author: System
-- Date: 2025-01-20

-- Create billing_subscriptions table for pre-paid hour packages
CREATE TABLE IF NOT EXISTS billing_subscriptions (
    subscription_id SERIAL PRIMARY KEY,
    client_id INTEGER NOT NULL REFERENCES client_profiles(client_id),
    subscription_name VARCHAR(255) NOT NULL,
    subscription_type VARCHAR(50) NOT NULL CHECK (subscription_type IN ('monthly', 'quarterly', 'annual', 'custom')),
    hours_purchased DECIMAL(10,2) NOT NULL CHECK (hours_purchased > 0),
    hours_remaining DECIMAL(10,2) NOT NULL CHECK (hours_remaining >= 0),
    price_paid DECIMAL(10,2) NOT NULL CHECK (price_paid >= 0),
    currency_code CHAR(3) DEFAULT 'CAD',
    
    -- Validity period
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    auto_renew BOOLEAN DEFAULT FALSE,
    
    -- Status tracking
    status VARCHAR(50) NOT NULL DEFAULT 'active' CHECK (status IN ('pending', 'active', 'paused', 'expired', 'cancelled')),
    
    -- Payment tracking
    payment_method VARCHAR(50),
    stripe_subscription_id VARCHAR(255),
    
    -- Metadata
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    cancelled_at TIMESTAMP WITH TIME ZONE,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    CONSTRAINT valid_dates CHECK (end_date > start_date),
    CONSTRAINT hours_not_negative CHECK (hours_remaining <= hours_purchased)
);

-- Create billing_packages table for TECFEE and other package deals
CREATE TABLE IF NOT EXISTS billing_packages (
    package_id SERIAL PRIMARY KEY,
    package_name VARCHAR(255) NOT NULL,
    package_type VARCHAR(50) NOT NULL CHECK (package_type IN ('tecfee', 'bundle', 'custom')),
    service_id INTEGER REFERENCES service_catalog(service_id),
    
    -- Package details
    session_count INTEGER NOT NULL CHECK (session_count > 0),
    session_duration_minutes INTEGER NOT NULL DEFAULT 60,
    total_price DECIMAL(10,2) NOT NULL CHECK (total_price > 0),
    currency_code CHAR(3) DEFAULT 'CAD',
    
    -- Group session settings for TECFEE
    is_group_package BOOLEAN DEFAULT FALSE,
    min_participants INTEGER,
    max_participants INTEGER,
    
    -- Validity
    is_active BOOLEAN DEFAULT TRUE,
    valid_from DATE DEFAULT CURRENT_DATE,
    valid_until DATE,
    
    -- Metadata
    description TEXT,
    terms_and_conditions TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    CONSTRAINT group_participant_check CHECK (
        (is_group_package = FALSE) OR 
        (is_group_package = TRUE AND min_participants IS NOT NULL AND max_participants IS NOT NULL AND max_participants >= min_participants)
    )
);

-- Create billing_package_purchases table to track who bought packages
CREATE TABLE IF NOT EXISTS billing_package_purchases (
    purchase_id SERIAL PRIMARY KEY,
    package_id INTEGER NOT NULL REFERENCES billing_packages(package_id),
    client_id INTEGER NOT NULL REFERENCES client_profiles(client_id),
    dependant_id INTEGER REFERENCES client_dependants(dependant_id),
    
    -- Purchase details
    purchase_date DATE NOT NULL DEFAULT CURRENT_DATE,
    sessions_remaining INTEGER NOT NULL,
    expire_date DATE,
    
    -- Payment tracking
    amount_paid DECIMAL(10,2) NOT NULL,
    payment_method VARCHAR(50),
    stripe_payment_id VARCHAR(255),
    invoice_id INTEGER REFERENCES billing_invoices(invoice_id),
    
    -- Status
    status VARCHAR(50) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'completed', 'expired', 'refunded')),
    
    -- Metadata
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    
    CONSTRAINT sessions_check CHECK (sessions_remaining >= 0)
);

-- Create billing_tutor_payments table for weekly payment tracking
CREATE TABLE IF NOT EXISTS billing_tutor_payments (
    tutor_payment_id SERIAL PRIMARY KEY,
    tutor_id INTEGER NOT NULL REFERENCES tutor_profiles(tutor_id),
    
    -- Payment period (Thursday to Wednesday)
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    
    -- Payment details
    total_sessions INTEGER NOT NULL DEFAULT 0,
    total_hours DECIMAL(10,2) NOT NULL DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    currency_code CHAR(3) DEFAULT 'CAD',
    
    -- Status tracking
    status VARCHAR(50) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'processing', 'paid', 'failed', 'cancelled')),
    approved_by INTEGER REFERENCES user_accounts(user_id),
    approved_at TIMESTAMP WITH TIME ZONE,
    
    -- Payment processing
    payment_method VARCHAR(50) DEFAULT 'direct_deposit',
    stripe_payout_id VARCHAR(255),
    bank_account_last4 VARCHAR(4),
    paid_at TIMESTAMP WITH TIME ZONE,
    
    -- Error tracking
    failure_reason TEXT,
    retry_count INTEGER DEFAULT 0,
    
    -- Metadata
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT valid_period CHECK (period_end > period_start)
);

-- Create billing_tutor_payment_details table for line items
CREATE TABLE IF NOT EXISTS billing_tutor_payment_details (
    detail_id SERIAL PRIMARY KEY,
    tutor_payment_id INTEGER NOT NULL REFERENCES billing_tutor_payments(tutor_payment_id) ON DELETE CASCADE,
    appointment_id INTEGER NOT NULL REFERENCES appointment_sessions(appointment_id),
    
    -- Session details
    session_date DATE NOT NULL,
    duration_minutes INTEGER NOT NULL,
    hourly_rate DECIMAL(10,2) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    
    -- Service info
    service_type VARCHAR(100),
    client_name VARCHAR(255),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create subscription usage tracking table
CREATE TABLE IF NOT EXISTS billing_subscription_usage (
    usage_id SERIAL PRIMARY KEY,
    subscription_id INTEGER NOT NULL REFERENCES billing_subscriptions(subscription_id),
    appointment_id INTEGER NOT NULL REFERENCES appointment_sessions(appointment_id),
    
    -- Usage details
    hours_used DECIMAL(10,2) NOT NULL CHECK (hours_used > 0),
    usage_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Balance tracking
    hours_before DECIMAL(10,2) NOT NULL,
    hours_after DECIMAL(10,2) NOT NULL CHECK (hours_after >= 0),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(subscription_id, appointment_id)
);

-- Create indexes for performance
CREATE INDEX idx_billing_subscriptions_client ON billing_subscriptions(client_id);
CREATE INDEX idx_billing_subscriptions_status ON billing_subscriptions(status);
CREATE INDEX idx_billing_subscriptions_dates ON billing_subscriptions(start_date, end_date);

CREATE INDEX idx_billing_packages_active ON billing_packages(is_active);
CREATE INDEX idx_billing_packages_type ON billing_packages(package_type);

CREATE INDEX idx_billing_package_purchases_client ON billing_package_purchases(client_id);
CREATE INDEX idx_billing_package_purchases_status ON billing_package_purchases(status);

CREATE INDEX idx_billing_tutor_payments_tutor ON billing_tutor_payments(tutor_id);
CREATE INDEX idx_billing_tutor_payments_period ON billing_tutor_payments(period_start, period_end);
CREATE INDEX idx_billing_tutor_payments_status ON billing_tutor_payments(status);

CREATE INDEX idx_billing_subscription_usage_subscription ON billing_subscription_usage(subscription_id);
CREATE INDEX idx_billing_subscription_usage_appointment ON billing_subscription_usage(appointment_id);

-- Add comment
COMMENT ON TABLE billing_subscriptions IS 'Tracks pre-paid hour subscriptions for clients';
COMMENT ON TABLE billing_packages IS 'Defines package deals including TECFEE programs';
COMMENT ON TABLE billing_tutor_payments IS 'Weekly payment tracking for tutors (Thursday to Wednesday cycle)';