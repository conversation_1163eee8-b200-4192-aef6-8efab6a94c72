import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  CreditCard, Shield, AlertCircle, Check,
  Lock, Building, User
} from 'lucide-react';
import { loadStripe } from '@stripe/stripe-js';
import {
  Elements,
  CardElement,
  useStripe,
  useElements,
  PaymentElement,
  usePaymentElement
} from '@stripe/react-stripe-js';
import api from '../../services/api';
import { billingService } from '../../services/billingService';
import Button from '../common/Button';
import { Input } from '../common/Input';
import { Select } from '../common/Select';
import { Card } from '../common/Card';
import { useAuth } from '../../contexts/AuthContext';
import toast from 'react-hot-toast';

// Initialize Stripe
const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLIC_KEY || '');

interface StripePaymentFormProps {
  amount: number;
  currency?: string;
  invoiceId?: number;
  onSuccess: (paymentIntent: any) => void;
  onCancel: () => void;
  saveCard?: boolean;
  useExistingCard?: boolean;
}

interface SavedPaymentMethod {
  id: string;
  type: string;
  card?: {
    brand: string;
    last4: string;
    exp_month: number;
    exp_year: number;
  };
  billing_details: {
    name: string;
    email: string;
  };
}

const PaymentMethodSelector: React.FC<{
  savedMethods: SavedPaymentMethod[];
  selectedMethodId: string | null;
  onSelect: (methodId: string | null) => void;
}> = ({ savedMethods, selectedMethodId, onSelect }) => {
  const { t } = useTranslation();

  return (
    <div className="space-y-3">
      {savedMethods.map((method) => (
        <div
          key={method.id}
          className={`
            relative rounded-lg border-2 p-4 cursor-pointer transition-all
            ${selectedMethodId === method.id 
              ? 'border-accent-red bg-red-50' 
              : 'border-gray-200 hover:border-gray-300'
            }
          `}
          onClick={() => onSelect(method.id)}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <CreditCard className="w-5 h-5 text-gray-600" />
              <div>
                <p className="font-medium text-gray-900">
                  {method.card?.brand} •••• {method.card?.last4}
                </p>
                <p className="text-sm text-gray-500">
                  {t('billing.expiresOn')} {method.card?.exp_month}/{method.card?.exp_year}
                </p>
              </div>
            </div>
            {selectedMethodId === method.id && (
              <Check className="w-5 h-5 text-accent-red" />
            )}
          </div>
        </div>
      ))}
      
      <div
        className={`
          relative rounded-lg border-2 p-4 cursor-pointer transition-all
          ${selectedMethodId === null 
            ? 'border-accent-red bg-red-50' 
            : 'border-gray-200 hover:border-gray-300'
          }
        `}
        onClick={() => onSelect(null)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <CreditCard className="w-5 h-5 text-gray-600" />
            <p className="font-medium text-gray-900">
              {t('billing.useNewCard')}
            </p>
          </div>
          {selectedMethodId === null && (
            <Check className="w-5 h-5 text-accent-red" />
          )}
        </div>
      </div>
    </div>
  );
};

const CheckoutForm: React.FC<StripePaymentFormProps> = ({
  amount,
  currency = 'CAD',
  invoiceId,
  onSuccess,
  onCancel,
  saveCard = true,
  useExistingCard = true
}) => {
  const { t } = useTranslation();
  const stripe = useStripe();
  const elements = useElements();
  const { user } = useAuth();
  const [processing, setProcessing] = useState(false);
  const [savedMethods, setSavedMethods] = useState<SavedPaymentMethod[]>([]);
  const [selectedMethodId, setSelectedMethodId] = useState<string | null>(null);
  const [billingDetails, setBillingDetails] = useState({
    name: user?.name || '',
    email: user?.email || '',
    address: {
      line1: '',
      city: '',
      state: '',
      postal_code: '',
      country: 'CA'
    }
  });
  const [saveNewCard, setSaveNewCard] = useState(saveCard);

  useEffect(() => {
    if (useExistingCard) {
      fetchSavedPaymentMethods();
    }
  }, []);

  const fetchSavedPaymentMethods = async () => {
    try {
      const response = await api.get<SavedPaymentMethod[]>(`/billing/payment-methods`);
      setSavedMethods(response.data);
      if (response.data.length > 0) {
        setSelectedMethodId(response.data[0].id);
      }
    } catch (error) {
      console.error('Error fetching payment methods:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!stripe || !elements) {
      return;
    }
    
    try {
      setProcessing(true);
      
      // Create payment intent using billing service
      const { client_secret } = await billingService.createPaymentIntent(invoiceId!);
      
      let result;
      
      if (selectedMethodId) {
        // Use existing payment method
        result = await stripe.confirmCardPayment(client_secret, {
          payment_method: selectedMethodId
        });
      } else {
        // Use new card
        const cardElement = elements.getElement(CardElement);
        if (!cardElement) return;
        
        result = await stripe.confirmCardPayment(client_secret, {
          payment_method: {
            card: cardElement,
            billing_details: billingDetails
          },
          setup_future_usage: saveNewCard ? 'off_session' : undefined
        });
      }
      
      if (result.error) {
        throw new Error(result.error.message);
      }
      
      if (result.paymentIntent?.status === 'succeeded') {
        toast.success(t('billing.paymentSuccess'));
        onSuccess(result.paymentIntent);
      }
      
    } catch (error: any) {
      console.error('Payment error:', error);
      toast.error(error.message || t('billing.errors.paymentFailed'));
    } finally {
      setProcessing(false);
    }
  };

  const formatAmount = () => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Amount Display */}
      <Card className="p-4 bg-gray-50">
        <div className="flex items-center justify-between">
          <span className="text-lg font-medium text-gray-900">
            {t('billing.totalAmount')}
          </span>
          <span className="text-2xl font-bold text-gray-900">
            {formatAmount()}
          </span>
        </div>
      </Card>

      {/* Payment Method Selection */}
      {useExistingCard && savedMethods.length > 0 && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            {t('billing.selectPaymentMethod')}
          </label>
          <PaymentMethodSelector
            savedMethods={savedMethods}
            selectedMethodId={selectedMethodId}
            onSelect={setSelectedMethodId}
          />
        </div>
      )}

      {/* New Card Form */}
      {(!useExistingCard || selectedMethodId === null) && (
        <>
          {/* Billing Details */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-gray-700">
              {t('billing.billingInformation')}
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('billing.cardholderName')}
                </label>
                <Input
                  type="text"
                  value={billingDetails.name}
                  onChange={(e) => setBillingDetails({
                    ...billingDetails,
                    name: e.target.value
                  })}
                  placeholder={t('billing.cardholderNamePlaceholder')}
                  leftIcon={<User className="w-4 h-4" />}
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('billing.email')}
                </label>
                <Input
                  type="email"
                  value={billingDetails.email}
                  onChange={(e) => setBillingDetails({
                    ...billingDetails,
                    email: e.target.value
                  })}
                  placeholder={t('billing.emailPlaceholder')}
                  required
                />
              </div>
            </div>
          </div>

          {/* Card Details */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('billing.cardDetails')}
            </label>
            <div className="p-4 border border-gray-300 rounded-lg">
              <CardElement
                options={{
                  style: {
                    base: {
                      fontSize: '16px',
                      color: '#374151',
                      '::placeholder': {
                        color: '#9CA3AF',
                      },
                    },
                  },
                  hidePostalCode: true
                }}
              />
            </div>
          </div>

          {/* Billing Address */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-gray-700">
              {t('billing.billingAddress')}
            </h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('billing.streetAddress')}
              </label>
              <Input
                type="text"
                value={billingDetails.address.line1}
                onChange={(e) => setBillingDetails({
                  ...billingDetails,
                  address: { ...billingDetails.address, line1: e.target.value }
                })}
                placeholder={t('billing.streetAddressPlaceholder')}
                required
              />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('billing.city')}
                </label>
                <Input
                  type="text"
                  value={billingDetails.address.city}
                  onChange={(e) => setBillingDetails({
                    ...billingDetails,
                    address: { ...billingDetails.address, city: e.target.value }
                  })}
                  placeholder={t('billing.cityPlaceholder')}
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('billing.province')}
                </label>
                <Select
                  value={billingDetails.address.state}
                  onChange={(e) => setBillingDetails({
                    ...billingDetails,
                    address: { ...billingDetails.address, state: e.target.value }
                  })}
                  required
                >
                  <option value="">{t('billing.selectProvince')}</option>
                  <option value="AB">Alberta</option>
                  <option value="BC">British Columbia</option>
                  <option value="MB">Manitoba</option>
                  <option value="NB">New Brunswick</option>
                  <option value="NL">Newfoundland and Labrador</option>
                  <option value="NS">Nova Scotia</option>
                  <option value="NT">Northwest Territories</option>
                  <option value="NU">Nunavut</option>
                  <option value="ON">Ontario</option>
                  <option value="PE">Prince Edward Island</option>
                  <option value="QC">Quebec</option>
                  <option value="SK">Saskatchewan</option>
                  <option value="YT">Yukon</option>
                </Select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('billing.postalCode')}
                </label>
                <Input
                  type="text"
                  value={billingDetails.address.postal_code}
                  onChange={(e) => setBillingDetails({
                    ...billingDetails,
                    address: { ...billingDetails.address, postal_code: e.target.value }
                  })}
                  placeholder="H1A 1A1"
                  pattern="[A-Za-z]\d[A-Za-z] ?\d[A-Za-z]\d"
                  required
                />
              </div>
            </div>
          </div>

          {/* Save Card Option */}
          {saveCard && (
            <div className="flex items-center">
              <input
                type="checkbox"
                id="save-card"
                checked={saveNewCard}
                onChange={(e) => setSaveNewCard(e.target.checked)}
                className="rounded border-gray-300 text-accent-red focus:ring-accent-red mr-2"
              />
              <label htmlFor="save-card" className="text-sm text-gray-700">
                {t('billing.saveCardForFuture')}
              </label>
            </div>
          )}
        </>
      )}

      {/* Security Notice */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <Shield className="w-5 h-5 text-gray-600 mt-0.5 flex-shrink-0" />
          <div className="space-y-2">
            <p className="text-sm font-medium text-gray-700">
              {t('billing.securePayment')}
            </p>
            <ul className="text-sm text-gray-600 space-y-1">
              <li className="flex items-center">
                <Lock className="w-4 h-4 mr-2" />
                {t('billing.encryptedConnection')}
              </li>
              <li className="flex items-center">
                <Check className="w-4 h-4 mr-2" />
                {t('billing.pciCompliant')}
              </li>
              <li className="flex items-center">
                <Building className="w-4 h-4 mr-2" />
                {t('billing.poweredByStripe')}
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-end space-x-3">
        <Button
          type="button"
          variant="secondary"
          onClick={onCancel}
          disabled={processing}
        >
          {t('common.cancel')}
        </Button>
        <Button
          type="submit"
          variant="primary"
          loading={processing}
          disabled={!stripe || processing}
          leftIcon={<CreditCard className="w-4 h-4" />}
        >
          {t('billing.payNow', { amount: formatAmount() })}
        </Button>
      </div>
    </form>
  );
};

interface StripePaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  amount: number;
  currency?: string;
  invoiceId?: number;
  onSuccess?: (paymentIntent: any) => void;
}

export const StripePaymentModal: React.FC<StripePaymentModalProps> = ({
  isOpen,
  onClose,
  amount,
  currency = 'CAD',
  invoiceId,
  onSuccess
}) => {
  const { t } = useTranslation();
  
  const handleSuccess = (paymentIntent: any) => {
    onSuccess?.(paymentIntent);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {t('billing.payment')}
          </h2>
        </div>
        
        <div className="p-6">
          <Elements stripe={stripePromise}>
            <CheckoutForm
              amount={amount}
              currency={currency}
              invoiceId={invoiceId}
              onSuccess={handleSuccess}
              onCancel={onClose}
            />
          </Elements>
        </div>
      </div>
    </div>
  );
};

export default StripePaymentModal;