-- Rollback Migration 019: Remove Customer Service System

-- Drop triggers first
DROP TRIGGER IF EXISTS update_conversation_activity ON cs_messages;
DROP TRIGGER IF EXISTS update_cs_knowledge_updated_at ON cs_knowledge_base;
DROP TRIGGER IF EXISTS update_cs_templates_updated_at ON cs_message_templates;
DROP TRIGGER IF EXISTS update_cs_conversations_updated_at ON cs_conversations;
DROP TRIGGER IF EXISTS update_cs_agents_updated_at ON customer_service_agents;

-- Drop trigger functions
DROP FUNCTION IF EXISTS update_conversation_last_activity();
DROP FUNCTION IF EXISTS update_updated_at_column();

-- Drop indexes
DROP INDEX IF EXISTS cs_conversations_unassigned_idx;
DROP INDEX IF EXISTS cs_conversations_agent_status_idx;
DROP INDEX IF EXISTS cs_messages_thread_sent_idx;
DROP INDEX IF EXISTS cs_conversations_status_priority_idx;

-- Drop tables in reverse dependency order
DROP TABLE IF EXISTS cs_broadcast_deliveries;
DROP TABLE IF EXISTS cs_broadcast_messages;
DROP TABLE IF EXISTS cs_agent_performance;
DROP TABLE IF EXISTS cs_knowledge_base;
DROP TABLE IF EXISTS cs_conversation_metrics;
DROP TABLE IF EXISTS cs_conversation_assignments;
DROP TABLE IF EXISTS cs_quick_responses;
DROP TABLE IF EXISTS cs_message_templates;
DROP TABLE IF EXISTS cs_messages;
DROP TABLE IF EXISTS cs_conversations;
DROP TABLE IF EXISTS customer_service_agents;

COMMIT;