"""
Authentication-related models for password reset and email verification.
"""

from datetime import datetime
from typing import Optional, Annotated
from pydantic import BaseModel, Field, field_validator, ConfigDict
from uuid import UUID

from app.models.base import BaseEntity, UserRoleType

# Enhanced validation imports
from app.core.validation import DataValidator, SecurityValidator


class PasswordResetTokenBase(BaseModel):
    """Base password reset token model."""
    model_config = ConfigDict(from_attributes=True)
    
    user_id: int = Field(..., description="User ID requesting password reset")
    email: str = Field(..., description="Email address for the reset")
    used: bool = Field(False, description="Whether token has been used")


class PasswordResetTokenCreate(PasswordResetTokenBase):
    """Model for creating a password reset token."""
    pass


class PasswordResetToken(PasswordResetTokenBase, BaseEntity):
    """Complete password reset token model."""
    reset_id: int = Field(..., description="Reset token ID")
    token: UUID = Field(..., description="Unique reset token")
    expires_at: datetime = Field(..., description="Token expiration time")
    used_at: Optional[datetime] = Field(None, description="When token was used")
    ip_address: Optional[str] = Field(None, description="IP address of requester")
    user_agent: Optional[str] = Field(None, description="User agent of requester")
    
    def is_expired(self) -> bool:
        """Check if token has expired."""
        from app.core.timezone import now_est
        return self.expires_at < now_est()
    
    def is_valid(self) -> bool:
        """Check if token is valid (not used and not expired)."""
        return not self.used and not self.is_expired()


class PasswordResetRequest(BaseModel):
    """Request model for password reset."""
    email: str = Field(..., description="Email address to reset password for")
    
    @field_validator('email')
    @classmethod
    def validate_email(cls, v: str) -> str:
        """Validate and normalize email with enhanced security."""
        return DataValidator.validate_email(v)


class PasswordResetConfirm(BaseModel):
    """Model for confirming password reset."""
    token: UUID = Field(..., description="Password reset token")
    new_password: str = Field(..., min_length=8, description="New password")
    
    @field_validator('new_password')
    @classmethod
    def validate_password(cls, v: str) -> str:
        """Validate password strength with enhanced security."""
        result = SecurityValidator.validate_password(v)
        if not result['is_valid']:
            raise ValueError(f"Password validation failed: {'; '.join(result['issues'])}")
        return v


class TokenData(BaseModel):
    """Token payload data."""
    user_id: str = Field(..., description="User ID")
    email: str = Field(..., description="User email")
    role: UserRoleType = Field(..., description="Current user role")
    exp: Optional[int] = Field(None, description="Token expiration timestamp")


# Alias for UserRole
UserRole = UserRoleType


class EmailVerificationToken(BaseEntity):
    """Email verification token model."""
    verification_id: int = Field(..., description="Verification token ID")
    user_id: int = Field(..., description="User ID to verify")
    email: str = Field(..., description="Email address to verify")
    token: UUID = Field(..., description="Unique verification token")
    expires_at: datetime = Field(..., description="Token expiration time")
    used: bool = Field(False, description="Whether token has been used")
    used_at: Optional[datetime] = Field(None, description="When token was used")
    
    def is_expired(self) -> bool:
        """Check if token has expired."""
        from app.core.timezone import now_est
        return self.expires_at < now_est()
    
    def is_valid(self) -> bool:
        """Check if token is valid (not used and not expired)."""
        return not self.used and not self.is_expired()


# Email Verification Models
class EmailVerificationTokenBase(BaseModel):
    """Base model for email verification tokens."""
    user_id: int = Field(..., description="User ID")
    email: Annotated[str, Field(pattern=r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')] = Field(
        ..., description="Email to be verified"
    )
    token: UUID = Field(..., description="Verification token")
    expires_at: datetime = Field(..., description="Token expiration time")
    ip_address: Optional[str] = Field(None, description="IP address when token was requested")
    user_agent: Optional[str] = Field(None, description="User agent when token was requested")
    
    model_config = ConfigDict(from_attributes=True)


class EmailVerificationToken(EmailVerificationTokenBase, BaseEntity):
    """Email verification token with entity metadata."""
    verification_id: int = Field(..., description="Verification token ID")
    verified_at: Optional[datetime] = Field(None, description="When email was verified")
    verified_from_ip: Optional[str] = Field(None, description="IP address where verification occurred")


class EmailVerificationRequest(BaseModel):
    """Request to send email verification."""
    email: Annotated[str, Field(pattern=r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')] = Field(
        ..., description="Email address to verify"
    )
    language: Optional[str] = Field('en', description="Language for verification email")
    
    @field_validator('language')
    @classmethod
    def validate_language(cls, v: str) -> str:
        if v not in ['en', 'fr']:
            raise ValueError('Language must be en or fr')
        return v


class EmailVerificationConfirm(BaseModel):
    """Confirm email verification with token."""
    token: UUID = Field(..., description="Verification token")


class EmailVerificationResponse(BaseModel):
    """Response for email verification operations."""
    message: str = Field(..., description="Status message")
    verified: bool = Field(..., description="Whether email is now verified")
    email: Optional[str] = Field(None, description="Verified email address")


class EmailVerificationStatus(BaseModel):
    """Email verification status."""
    email: str = Field(..., description="Email address")
    is_verified: bool = Field(..., description="Whether email is verified")
    verified_at: Optional[datetime] = Field(None, description="When email was verified")
    pending_verification: bool = Field(..., description="Whether verification is pending")
    can_resend: bool = Field(..., description="Whether verification can be resent")