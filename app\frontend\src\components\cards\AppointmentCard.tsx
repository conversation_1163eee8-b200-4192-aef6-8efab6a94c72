import React from 'react';
import { clsx } from 'clsx';
import { Badge } from '../common/Badge';
import { Avatar } from '../common/Avatar';
import { Clock, MapPin, Users, Calendar, Video } from 'lucide-react';

interface AppointmentCardProps {
  appointment: {
    id: string;
    title: string;
    date: string;
    startTime: string;
    endTime: string;
    duration: number; // in minutes
    type: 'individual' | 'group' | 'online';
    status: 'scheduled' | 'confirmed' | 'completed' | 'cancelled' | 'no_show';
    tutor: {
      name: string;
      avatar?: string;
    };
    client?: {
      name: string;
      avatar?: string;
    };
    participants?: number;
    location?: string;
    subject?: string;
  };
  variant?: 'compact' | 'detailed';
  onClick?: () => void;
  actions?: React.ReactNode;
  className?: string;
}

export const AppointmentCard: React.FC<AppointmentCardProps> = ({
  appointment,
  variant = 'compact',
  onClick,
  actions,
  className,
}) => {
  const statusStyles = {
    scheduled: { variant: 'info' as const, label: 'Scheduled' },
    confirmed: { variant: 'success' as const, label: 'Confirmed' },
    completed: { variant: 'secondary' as const, label: 'Completed' },
    cancelled: { variant: 'error' as const, label: 'Cancelled' },
    no_show: { variant: 'warning' as const, label: 'No Show' },
  };

  const typeIcons = {
    individual: <Users className="w-4 h-4" />,
    group: <Users className="w-4 h-4" />,
    online: <Video className="w-4 h-4" />,
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0 && mins > 0) {
      return `${hours}h ${mins}m`;
    } else if (hours > 0) {
      return `${hours}h`;
    } else {
      return `${mins}m`;
    }
  };

  if (variant === 'compact') {
    return (
      <div
        onClick={onClick}
        className={clsx(
          'bg-white border border-border-primary rounded-lg p-4',
          'transition-all duration-200',
          onClick && 'cursor-pointer hover:shadow-soft hover:border-accent-red',
          appointment.status === 'cancelled' && 'opacity-60',
          className
        )}
      >
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <h3 className="text-sm font-medium text-text-primary">
              {appointment.title}
            </h3>
            {appointment.subject && (
              <p className="text-sm text-text-secondary mt-1">
                {appointment.subject}
              </p>
            )}
          </div>
          <Badge {...statusStyles[appointment.status]} size="sm">
            {statusStyles[appointment.status].label}
          </Badge>
        </div>
        
        <div className="space-y-2">
          <div className="flex items-center gap-4 text-sm text-text-secondary">
            <div className="flex items-center gap-1">
              <Calendar className="w-4 h-4 text-text-muted" />
              {appointment.date}
            </div>
            <div className="flex items-center gap-1">
              <Clock className="w-4 h-4 text-text-muted" />
              {appointment.startTime} - {appointment.endTime}
            </div>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Avatar
                src={appointment.tutor.avatar}
                name={appointment.tutor.name}
                size="sm"
              />
              <span className="text-sm text-text-secondary">
                {appointment.tutor.name}
              </span>
            </div>
            
            {actions}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      onClick={onClick}
      className={clsx(
        'bg-white border border-border-primary rounded-lg shadow-subtle',
        'transition-all duration-200',
        onClick && 'cursor-pointer hover:shadow-soft hover:border-accent-red',
        appointment.status === 'cancelled' && 'opacity-60',
        className
      )}
    >
      <div className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-text-primary">
              {appointment.title}
            </h3>
            {appointment.subject && (
              <p className="text-text-secondary mt-1">
                {appointment.subject}
              </p>
            )}
          </div>
          <Badge {...statusStyles[appointment.status]}>
            {statusStyles[appointment.status].label}
          </Badge>
        </div>
        
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="space-y-3">
            <div className="flex items-center gap-2 text-sm">
              <Calendar className="w-4 h-4 text-text-muted" />
              <span className="text-text-secondary">{appointment.date}</span>
            </div>
            
            <div className="flex items-center gap-2 text-sm">
              <Clock className="w-4 h-4 text-text-muted" />
              <span className="text-text-secondary">
                {appointment.startTime} - {appointment.endTime} ({formatDuration(appointment.duration)})
              </span>
            </div>
            
            {appointment.location && (
              <div className="flex items-center gap-2 text-sm">
                <MapPin className="w-4 h-4 text-text-muted" />
                <span className="text-text-secondary">{appointment.location}</span>
              </div>
            )}
          </div>
          
          <div className="space-y-3">
            <div className="flex items-center gap-2 text-sm">
              {typeIcons[appointment.type]}
              <span className="text-text-secondary capitalize">
                {appointment.type}
                {appointment.type === 'group' && appointment.participants && (
                  <> ({appointment.participants} participants)</>
                )}
              </span>
            </div>
          </div>
        </div>
        
        <div className="border-t border-border-primary pt-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Avatar
                  src={appointment.tutor.avatar}
                  name={appointment.tutor.name}
                  size="sm"
                />
                <div>
                  <p className="text-xs text-text-muted">Tutor</p>
                  <p className="text-sm font-medium text-text-primary">
                    {appointment.tutor.name}
                  </p>
                </div>
              </div>
              
              {appointment.client && (
                <div className="flex items-center gap-2">
                  <Avatar
                    src={appointment.client.avatar}
                    name={appointment.client.name}
                    size="sm"
                  />
                  <div>
                    <p className="text-xs text-text-muted">Client</p>
                    <p className="text-sm font-medium text-text-primary">
                      {appointment.client.name}
                    </p>
                  </div>
                </div>
              )}
            </div>
            
            {actions}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AppointmentCard;