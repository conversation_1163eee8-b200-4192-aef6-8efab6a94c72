import React, { useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import toast from 'react-hot-toast';

const OAuthCallback: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { handleOAuthCallback } = useAuth();

  useEffect(() => {
    const processCallback = async () => {
      // Extract tokens from URL params
      const accessToken = searchParams.get('access_token');
      const refreshToken = searchParams.get('refresh_token');
      const tokenType = searchParams.get('token_type');
      const expiresIn = searchParams.get('expires_in');
      
      // Extract error params
      const error = searchParams.get('error');
      const message = searchParams.get('message');

      if (error) {
        // Handle error case
        toast.error(message || 'Authentication failed');
        navigate('/login');
        return;
      }

      if (accessToken && refreshToken) {
        try {
          console.log('Processing OAuth tokens...');
          console.log('Access token:', accessToken?.substring(0, 20) + '...');
          console.log('Refresh token:', refreshToken?.substring(0, 20) + '...');
          
          // Process the OAuth tokens
          const user = await handleOAuthCallback({
            access_token: accessToken,
            refresh_token: refreshToken,
            token_type: tokenType || 'bearer',
            expires_in: expiresIn ? parseInt(expiresIn) : 3600
          });
          
          console.log('OAuth callback completed, user:', user);
          toast.success('Successfully logged in with Google!');
          navigate('/dashboard');
        } catch (error) {
          console.error('OAuth callback error:', error);
          console.error('Error details:', error.response?.data || error.message);
          toast.error('Failed to complete authentication');
          navigate('/login');
        }
      } else {
        // Missing required tokens
        toast.error('Invalid authentication response');
        navigate('/login');
      }
    };

    processCallback();
  }, [searchParams, navigate, handleOAuthCallback]);

  return (
    <div className="min-h-screen bg-gradient-to-b from-background-primary to-background-secondary flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-red mx-auto"></div>
        <p className="mt-4 text-text-secondary">Completing authentication...</p>
      </div>
    </div>
  );
};

export default OAuthCallback;