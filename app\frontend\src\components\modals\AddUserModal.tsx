import React, { useState } from 'react';
import { Modal } from '../common/Modal';
import Button from '../common/Button';
import { Input } from '../common/Input';
import { Select } from '../common/Select';
import { X, Save, User, Mail, Phone, MapPin, GraduationCap, Briefcase, Globe, BookOpen } from 'lucide-react';
import toast from 'react-hot-toast';
import { useApi } from '../../hooks/useApi';

// Import new education and experience components
import { DegreeSelector } from '../forms/education/DegreeSelector';
import { UniversityAutocomplete } from '../forms/education/UniversityAutocomplete';
import { CertificationSelector } from '../forms/education/CertificationSelector';
import { LanguageProficiencyGrid } from '../forms/experience/LanguageProficiencyGrid';
import { SubjectExpertiseSelector } from '../forms/experience/SubjectExpertiseSelector';

interface AddUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  userType: 'client' | 'tutor';
  onSuccess?: (user: any) => void;
}

export const AddUserModal: React.FC<AddUserModalProps> = ({
  isOpen,
  onClose,
  userType,
  onSuccess
}) => {
  const { post } = useApi();
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    postal_code: '',
    password: 'TempPass123!', // Default password
    // Tutor-specific fields
    specialties: [] as string[],
    languages: [] as string[],
    experience_years: '',
    education: '',
    bio: '',
    // New education fields
    highest_degree_level: '',
    highest_degree_name: '',
    degree_major: '',
    university_name: '',
    graduation_year: '',
    teaching_certifications: [] as string[],
    // New experience fields
    years_of_experience: '',
    years_online_teaching: '',
    years_tutoring: '',
    current_occupation: '',
    teaching_languages: [] as string[],
    language_proficiency: {} as Record<string, string>,
    subject_expertise: {} as Record<string, string>,
    teaching_methodology: [] as string[],
    special_needs_experience: false,
    age_groups_experience: [] as string[],
    // Portfolio
    portfolio_url: '',
    achievement_highlights: '',
    // Client-specific fields
    emergency_contact_name: '',
    emergency_contact_phone: '',
    emergency_contact_relationship: '',
    preferred_language: 'en',
    timezone: 'America/Toronto',
    address_line1: '',
    address_line2: '',
    city: '',
    province: '',
    secondary_phone: '',
    communication_preferences: {
      email: true,
      sms: true,
      push: true
    }
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.first_name) newErrors.first_name = 'First name is required';
    if (!formData.last_name) newErrors.last_name = 'Last name is required';
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }
    if (!formData.phone) newErrors.phone = 'Phone is required';

    if (userType === 'tutor') {
      if (formData.specialties.length === 0) {
        newErrors.specialties = 'At least one specialty is required';
      }
      if (formData.languages.length === 0) {
        newErrors.languages = 'At least one language is required';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      // Prepare data based on user type
      let apiData: any = {
        email: formData.email,
        password: formData.password,
        first_name: formData.first_name,
        last_name: formData.last_name,
        phone: formData.phone,
        postal_code: formData.postal_code,
        preferred_language: formData.preferred_language
      };

      if (userType === 'client') {
        // Add client-specific fields
        apiData = {
          ...apiData,
          emergency_contact_name: formData.emergency_contact_name || null,
          emergency_contact_phone: formData.emergency_contact_phone || null,
          emergency_contact_relationship: formData.emergency_contact_relationship || null,
          timezone: formData.timezone,
          address_line1: formData.address_line1 || null,
          address_line2: formData.address_line2 || null,
          city: formData.city || null,
          province: formData.province || null,
          secondary_phone: formData.secondary_phone || null,
          communication_preferences: formData.communication_preferences
        };
      } else {
        // Add tutor-specific fields
        apiData = {
          ...apiData,
          bio: formData.bio || null,
          specialties: formData.specialties,
          languages: formData.languages,
          experience_years: parseInt(formData.experience_years) || 0,
          education: formData.education || null,
          highest_degree_level: formData.highest_degree_level || null,
          highest_degree_name: formData.highest_degree_name || null,
          degree_major: formData.degree_major || null,
          university_name: formData.university_name || null,
          graduation_year: formData.graduation_year ? parseInt(formData.graduation_year) : null,
          teaching_certifications: formData.teaching_certifications,
          years_of_experience: parseInt(formData.years_of_experience) || 0,
          years_online_teaching: parseInt(formData.years_online_teaching) || 0,
          years_tutoring: parseInt(formData.years_tutoring) || 0,
          current_occupation: formData.current_occupation || null,
          teaching_languages: formData.teaching_languages,
          language_proficiency: formData.language_proficiency,
          subject_expertise: formData.subject_expertise,
          teaching_methodology: formData.teaching_methodology,
          special_needs_experience: formData.special_needs_experience,
          age_groups_experience: formData.age_groups_experience,
          portfolio_url: formData.portfolio_url || null,
          achievement_highlights: formData.achievement_highlights || null
        };
      }

      // Call appropriate API endpoint
      const endpoint = userType === 'client' ? '/clients' : '/tutors';
      const newUser = await post(endpoint, apiData);

      toast.success(`${userType === 'tutor' ? 'Tutor' : 'Client'} added successfully!`);
      
      if (onSuccess) {
        onSuccess(newUser);
      }

      // Send welcome email notification
      toast.info('Welcome email sent to ' + formData.email);

      onClose();
      resetForm();
    } catch (error: any) {
      console.error('Error creating user:', error);
      if (error.response?.data?.detail) {
        toast.error(error.response.data.detail);
      } else {
        toast.error(`Failed to add ${userType}`);
      }
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      first_name: '',
      last_name: '',
      email: '',
      phone: '',
      postal_code: '',
      password: 'TempPass123!',
      specialties: [],
      languages: [],
      experience_years: '',
      education: '',
      bio: '',
      // New education fields
      highest_degree_level: '',
      highest_degree_name: '',
      degree_major: '',
      university_name: '',
      graduation_year: '',
      teaching_certifications: [],
      // New experience fields
      years_of_experience: '',
      years_online_teaching: '',
      years_tutoring: '',
      current_occupation: '',
      teaching_languages: [],
      language_proficiency: {},
      subject_expertise: {},
      teaching_methodology: [],
      special_needs_experience: false,
      age_groups_experience: [],
      // Portfolio
      portfolio_url: '',
      achievement_highlights: '',
      // Client-specific fields
      emergency_contact_name: '',
      emergency_contact_phone: '',
      emergency_contact_relationship: '',
      preferred_language: 'en',
      timezone: 'America/Toronto',
      address_line1: '',
      address_line2: '',
      city: '',
      province: '',
      secondary_phone: '',
      communication_preferences: {
        email: true,
        sms: true,
        push: true
      }
    });
    setErrors({});
  };

  const specialtyOptions = [
    { value: 'mathematics', label: 'Mathematics' },
    { value: 'physics', label: 'Physics' },
    { value: 'chemistry', label: 'Chemistry' },
    { value: 'biology', label: 'Biology' },
    { value: 'english', label: 'English' },
    { value: 'french', label: 'French' },
    { value: 'spanish', label: 'Spanish' },
    { value: 'history', label: 'History' },
    { value: 'geography', label: 'Geography' },
    { value: 'computer_science', label: 'Computer Science' }
  ];

  const languageOptions = [
    { value: 'en', label: 'English' },
    { value: 'fr', label: 'French' },
    { value: 'es', label: 'Spanish' },
    { value: 'zh', label: 'Chinese' },
    { value: 'ar', label: 'Arabic' }
  ];

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`Add New ${userType === 'tutor' ? 'Tutor' : 'Client'}`}
      size="lg"
    >
      <form onSubmit={(e) => { e.preventDefault(); handleSubmit(); }} className="space-y-6">
        {/* Basic Information */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                First Name <span className="text-red-500">*</span>
              </label>
              <Input
                value={formData.first_name}
                onChange={(e) => setFormData({ ...formData, first_name: e.target.value })}
                placeholder="John"
                error={errors.first_name}
                icon={User}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Last Name <span className="text-red-500">*</span>
              </label>
              <Input
                value={formData.last_name}
                onChange={(e) => setFormData({ ...formData, last_name: e.target.value })}
                placeholder="Doe"
                error={errors.last_name}
              />
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email <span className="text-red-500">*</span>
              </label>
              <Input
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                placeholder="<EMAIL>"
                error={errors.email}
                icon={Mail}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Phone <span className="text-red-500">*</span>
              </label>
              <Input
                value={formData.phone}
                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                placeholder="(*************"
                error={errors.phone}
                icon={Phone}
              />
            </div>
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Postal Code
              </label>
              <Input
                value={formData.postal_code}
                onChange={(e) => setFormData({ ...formData, postal_code: e.target.value })}
                placeholder="H2X 1Y9"
                icon={MapPin}
              />
            </div>
          </div>
        </div>

        {/* Tutor-specific fields */}
        {userType === 'tutor' && (
          <>
            {/* Education Section */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
                <GraduationCap className="w-5 h-5" />
                Education
              </h3>
              <div className="space-y-4">
                {/* Degree Selector */}
                <DegreeSelector
                  value={{
                    level: formData.highest_degree_level,
                    name: formData.highest_degree_name
                  }}
                  onChange={(value) => setFormData({
                    ...formData,
                    highest_degree_level: value.level || '',
                    highest_degree_name: value.name || ''
                  })}
                  error={{
                    level: errors.highest_degree_level,
                    name: errors.highest_degree_name
                  }}
                  required
                />

                {/* Major and University */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Major/Field of Study
                    </label>
                    <Input
                      value={formData.degree_major}
                      onChange={(e) => setFormData({ ...formData, degree_major: e.target.value })}
                      placeholder="e.g., Mathematics, Education"
                      error={errors.degree_major}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Graduation Year
                    </label>
                    <Input
                      type="number"
                      value={formData.graduation_year}
                      onChange={(e) => setFormData({ ...formData, graduation_year: e.target.value })}
                      placeholder={new Date().getFullYear().toString()}
                      min="1950"
                      max={new Date().getFullYear()}
                      error={errors.graduation_year}
                    />
                  </div>
                </div>

                {/* University Autocomplete */}
                <UniversityAutocomplete
                  value={formData.university_name}
                  onChange={(value) => setFormData({ ...formData, university_name: value })}
                  error={errors.university_name}
                />

                {/* Certifications */}
                <CertificationSelector
                  value={formData.teaching_certifications}
                  onChange={(value) => setFormData({ ...formData, teaching_certifications: value })}
                  error={errors.teaching_certifications}
                />
              </div>
            </div>

            {/* Experience Section */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
                <Briefcase className="w-5 h-5" />
                Teaching Experience
              </h3>
              <div className="space-y-4">
                {/* Experience Years */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Total Years of Experience
                    </label>
                    <Input
                      type="number"
                      value={formData.years_of_experience}
                      onChange={(e) => setFormData({ ...formData, years_of_experience: e.target.value })}
                      placeholder="0"
                      min="0"
                      max="50"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Years Teaching Online
                    </label>
                    <Input
                      type="number"
                      value={formData.years_online_teaching}
                      onChange={(e) => setFormData({ ...formData, years_online_teaching: e.target.value })}
                      placeholder="0"
                      min="0"
                      max="50"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Years Tutoring
                    </label>
                    <Input
                      type="number"
                      value={formData.years_tutoring}
                      onChange={(e) => setFormData({ ...formData, years_tutoring: e.target.value })}
                      placeholder="0"
                      min="0"
                      max="50"
                    />
                  </div>
                </div>

                {/* Current Occupation */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Current Occupation
                  </label>
                  <Input
                    value={formData.current_occupation}
                    onChange={(e) => setFormData({ ...formData, current_occupation: e.target.value })}
                    placeholder="e.g., Full-time Tutor, High School Teacher"
                  />
                </div>

                {/* Subject Expertise */}
                <SubjectExpertiseSelector
                  value={formData.subject_expertise}
                  onChange={(value) => setFormData({ ...formData, subject_expertise: value })}
                  error={errors.subject_expertise}
                />

                {/* Language Proficiency */}
                <LanguageProficiencyGrid
                  value={formData.language_proficiency}
                  onChange={(value) => setFormData({ ...formData, language_proficiency: value })}
                  error={errors.language_proficiency}
                />

                {/* Teaching Methodology */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Teaching Methodologies
                  </label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {[
                      { value: 'traditional', label: 'Traditional' },
                      { value: 'montessori', label: 'Montessori' },
                      { value: 'project_based', label: 'Project-Based' },
                      { value: 'flipped_classroom', label: 'Flipped Classroom' },
                      { value: 'socratic', label: 'Socratic Method' },
                      { value: 'experiential', label: 'Experiential Learning' }
                    ].map(method => (
                      <label key={method.value} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={formData.teaching_methodology.includes(method.value)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setFormData({
                                ...formData,
                                teaching_methodology: [...formData.teaching_methodology, method.value]
                              });
                            } else {
                              setFormData({
                                ...formData,
                                teaching_methodology: formData.teaching_methodology.filter(m => m !== method.value)
                              });
                            }
                          }}
                          className="rounded border-gray-300 text-accent-red focus:ring-accent-red"
                        />
                        <span className="text-sm">{method.label}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Special Needs Experience */}
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="special_needs"
                    checked={formData.special_needs_experience}
                    onChange={(e) => setFormData({ ...formData, special_needs_experience: e.target.checked })}
                    className="rounded border-gray-300 text-accent-red focus:ring-accent-red"
                  />
                  <label htmlFor="special_needs" className="text-sm font-medium text-gray-700">
                    Experience teaching students with special needs
                  </label>
                </div>

                {/* Age Groups */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Age Groups Experience
                  </label>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                    {[
                      { value: 'preschool', label: 'Preschool (3-5)' },
                      { value: 'elementary', label: 'Elementary (6-11)' },
                      { value: 'middle_school', label: 'Middle School (12-14)' },
                      { value: 'high_school', label: 'High School (15-18)' },
                      { value: 'college', label: 'College/CEGEP' },
                      { value: 'university', label: 'University' },
                      { value: 'adult', label: 'Adult Learners' },
                      { value: 'senior', label: 'Seniors' }
                    ].map(ageGroup => (
                      <label key={ageGroup.value} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={formData.age_groups_experience.includes(ageGroup.value)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setFormData({
                                ...formData,
                                age_groups_experience: [...formData.age_groups_experience, ageGroup.value]
                              });
                            } else {
                              setFormData({
                                ...formData,
                                age_groups_experience: formData.age_groups_experience.filter(a => a !== ageGroup.value)
                              });
                            }
                          }}
                          className="rounded border-gray-300 text-accent-red focus:ring-accent-red"
                        />
                        <span className="text-sm">{ageGroup.label}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Portfolio & Bio Section */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
                <BookOpen className="w-5 h-5" />
                Portfolio & Bio
              </h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Bio
                  </label>
                  <textarea
                    value={formData.bio}
                    onChange={(e) => setFormData({ ...formData, bio: e.target.value })}
                    placeholder="Tell us about your teaching philosophy, approach, and what makes you unique as a tutor..."
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-red"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Portfolio URL (Optional)
                  </label>
                  <Input
                    type="url"
                    value={formData.portfolio_url}
                    onChange={(e) => setFormData({ ...formData, portfolio_url: e.target.value })}
                    placeholder="https://your-portfolio.com"
                    icon={Globe}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Achievement Highlights (Optional)
                  </label>
                  <textarea
                    value={formData.achievement_highlights}
                    onChange={(e) => setFormData({ ...formData, achievement_highlights: e.target.value })}
                    placeholder="Notable achievements, awards, or success stories..."
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-red"
                  />
                </div>
              </div>
            </div>
          </>
        )}

        {/* Client-specific fields */}
        {userType === 'client' && (
          <>
            {/* Address Information */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Address Information</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Address Line 1
                  </label>
                  <Input
                    value={formData.address_line1}
                    onChange={(e) => setFormData({ ...formData, address_line1: e.target.value })}
                    placeholder="123 Main Street"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Address Line 2 (Apartment, Suite, etc.)
                  </label>
                  <Input
                    value={formData.address_line2}
                    onChange={(e) => setFormData({ ...formData, address_line2: e.target.value })}
                    placeholder="Apt 4B"
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      City
                    </label>
                    <Input
                      value={formData.city}
                      onChange={(e) => setFormData({ ...formData, city: e.target.value })}
                      placeholder="Montreal"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Province
                    </label>
                    <Select
                      value={formData.province}
                      onChange={(e) => setFormData({ ...formData, province: e.target.value })}
                      options={[
                        { value: '', label: 'Select Province' },
                        { value: 'ON', label: 'Ontario' },
                        { value: 'QC', label: 'Quebec' },
                        { value: 'BC', label: 'British Columbia' },
                        { value: 'AB', label: 'Alberta' },
                        { value: 'MB', label: 'Manitoba' },
                        { value: 'SK', label: 'Saskatchewan' },
                        { value: 'NS', label: 'Nova Scotia' },
                        { value: 'NB', label: 'New Brunswick' },
                        { value: 'NL', label: 'Newfoundland and Labrador' },
                        { value: 'PE', label: 'Prince Edward Island' },
                        { value: 'NT', label: 'Northwest Territories' },
                        { value: 'YT', label: 'Yukon' },
                        { value: 'NU', label: 'Nunavut' }
                      ]}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Emergency Contact */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Emergency Contact</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Contact Name
                  </label>
                  <Input
                    value={formData.emergency_contact_name}
                    onChange={(e) => setFormData({ ...formData, emergency_contact_name: e.target.value })}
                    placeholder="Jane Doe"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Contact Phone
                  </label>
                  <Input
                    value={formData.emergency_contact_phone}
                    onChange={(e) => setFormData({ ...formData, emergency_contact_phone: e.target.value })}
                    placeholder="(*************"
                  />
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Relationship
                  </label>
                  <Select
                    value={formData.emergency_contact_relationship}
                    onChange={(e) => setFormData({ ...formData, emergency_contact_relationship: e.target.value })}
                    options={[
                      { value: '', label: 'Select Relationship' },
                      { value: 'spouse', label: 'Spouse/Partner' },
                      { value: 'parent', label: 'Parent' },
                      { value: 'child', label: 'Child' },
                      { value: 'sibling', label: 'Sibling' },
                      { value: 'friend', label: 'Friend' },
                      { value: 'colleague', label: 'Colleague' },
                      { value: 'other', label: 'Other' }
                    ]}
                  />
                </div>
              </div>
            </div>

            {/* Additional Contact & Preferences */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Preferences</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Secondary Phone (Optional)
                  </label>
                  <Input
                    value={formData.secondary_phone}
                    onChange={(e) => setFormData({ ...formData, secondary_phone: e.target.value })}
                    placeholder="(*************"
                    icon={Phone}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Preferred Language
                  </label>
                  <Select
                    value={formData.preferred_language}
                    onChange={(e) => setFormData({ ...formData, preferred_language: e.target.value })}
                    options={[
                      { value: 'en', label: 'English' },
                      { value: 'fr', label: 'French' }
                    ]}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Timezone
                  </label>
                  <Select
                    value={formData.timezone}
                    onChange={(e) => setFormData({ ...formData, timezone: e.target.value })}
                    options={[
                      { value: 'America/Toronto', label: 'Eastern Time (Toronto)' },
                      { value: 'America/Montreal', label: 'Eastern Time (Montreal)' },
                      { value: 'America/Vancouver', label: 'Pacific Time (Vancouver)' },
                      { value: 'America/Calgary', label: 'Mountain Time (Calgary)' },
                      { value: 'America/Winnipeg', label: 'Central Time (Winnipeg)' }
                    ]}
                  />
                </div>
              </div>

              {/* Communication Preferences */}
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Communication Preferences
                </label>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.communication_preferences.email}
                      onChange={(e) => setFormData({
                        ...formData,
                        communication_preferences: {
                          ...formData.communication_preferences,
                          email: e.target.checked
                        }
                      })}
                      className="rounded border-gray-300 text-accent-red focus:ring-accent-red"
                    />
                    <span className="ml-2 text-sm text-gray-700">Email notifications</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.communication_preferences.sms}
                      onChange={(e) => setFormData({
                        ...formData,
                        communication_preferences: {
                          ...formData.communication_preferences,
                          sms: e.target.checked
                        }
                      })}
                      className="rounded border-gray-300 text-accent-red focus:ring-accent-red"
                    />
                    <span className="ml-2 text-sm text-gray-700">SMS notifications</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.communication_preferences.push}
                      onChange={(e) => setFormData({
                        ...formData,
                        communication_preferences: {
                          ...formData.communication_preferences,
                          push: e.target.checked
                        }
                      })}
                      className="rounded border-gray-300 text-accent-red focus:ring-accent-red"
                    />
                    <span className="ml-2 text-sm text-gray-700">Push notifications</span>
                  </label>
                </div>
              </div>
            </div>
          </>
        )}

        {/* Account Settings */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h4 className="font-medium text-yellow-800 mb-2">Account Creation</h4>
          <p className="text-sm text-yellow-700">
            A temporary password <strong>TempPass123!</strong> will be set for this account.
            The user will receive an email with instructions to change their password on first login.
          </p>
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-3 pt-4 border-t">
          <Button
            type="button"
            variant="ghost"
            onClick={() => {
              onClose();
              resetForm();
            }}
            disabled={loading}
          >
            <X className="w-4 h-4 mr-2" />
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={loading}
            className="bg-accent-red text-white hover:bg-accent-red-dark"
          >
            <Save className="w-4 h-4 mr-2" />
            {loading ? 'Adding...' : `Add ${userType === 'tutor' ? 'Tutor' : 'Client'}`}
          </Button>
        </div>
      </form>
    </Modal>
  );
};