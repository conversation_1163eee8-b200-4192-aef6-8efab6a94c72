"""
Payment domain models for tutor payment system.
Handles weekly tutor payments and payment batches.
"""

from datetime import datetime, date, timedelta
from decimal import Decimal
from typing import Optional, List, Dict, Any
from enum import Enum

from pydantic import BaseModel, Field, field_validator, computed_field


class PaymentStatus(str, Enum):
    """Payment status enumeration."""
    PENDING = "pending"
    PROCESSING = "processing"
    PAID = "paid"
    FAILED = "failed"
    REFUNDED = "refunded"
    CANCELLED = "cancelled"


class BatchStatus(str, Enum):
    """Payment batch status enumeration."""
    PENDING = "pending"
    APPROVED = "approved"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class TutorPayment(BaseModel):
    """Domain model for tutor payments."""
    payment_id: Optional[int] = None
    tutor_id: int
    appointment_id: int
    
    # Payment amounts
    service_amount: Decimal = Field(ge=0)
    transport_amount: Decimal = Field(default=Decimal("0.00"), ge=0)
    bonus_amount: Decimal = Field(default=Decimal("0.00"), ge=0)
    total_amount: Decimal = Field(ge=0)
    
    # Payment cycle (Thursday-Wednesday)
    payment_week_start: date
    payment_week_end: date
    
    # Payment status
    status: PaymentStatus = PaymentStatus.PENDING
    payment_date: Optional[datetime] = None
    payment_batch_id: Optional[int] = None
    
    # Stripe payout tracking
    stripe_payout_id: Optional[str] = None
    stripe_payout_status: Optional[str] = None
    
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    @field_validator("total_amount", mode='before')
    @classmethod
    def calculate_total(cls, v, info):
        """Calculate total amount if not provided."""
        if v is None or v == 0:
            service = info.data.get("service_amount", Decimal("0.00"))
            transport = info.data.get("transport_amount", Decimal("0.00"))
            bonus = info.data.get("bonus_amount", Decimal("0.00"))
            return service + transport + bonus
        return v
    
    @field_validator("payment_week_end", mode='before')
    @classmethod
    def set_week_end(cls, v, info):
        """Set week end date based on start date (Thursday to Wednesday)."""
        if v is None:
            start_date = info.data.get("payment_week_start")
            if start_date:
                return start_date + timedelta(days=6)
        return v
    
    @computed_field
    @property
    def is_paid(self) -> bool:
        """Check if payment is paid."""
        return self.status == PaymentStatus.PAID
    
    @computed_field
    @property
    def week_label(self) -> str:
        """Get week label for display."""
        return f"{self.payment_week_start.strftime('%b %d')} - {self.payment_week_end.strftime('%b %d, %Y')}"
    
    def mark_as_paid(self, stripe_payout_id: Optional[str] = None) -> None:
        """Mark payment as paid."""
        self.status = PaymentStatus.PAID
        self.payment_date = datetime.utcnow()
        if stripe_payout_id:
            self.stripe_payout_id = stripe_payout_id
            self.stripe_payout_status = "pending"  # Stripe payouts start as pending
    
    class Config:
        json_encoders = {
            Decimal: lambda v: float(v),
            datetime: lambda v: v.isoformat() if v else None,
            date: lambda v: v.isoformat() if v else None
        }


class PaymentBatch(BaseModel):
    """Domain model for payment batches."""
    batch_id: Optional[int] = None
    batch_number: Optional[str] = None
    week_start: date
    week_end: date
    
    # Batch totals
    total_payments: int = 0
    total_amount: Decimal = Field(default=Decimal("0.00"), ge=0)
    
    # Approval workflow
    status: BatchStatus = BatchStatus.PENDING
    approved_by: Optional[int] = None
    approved_at: Optional[datetime] = None
    
    # Processing details
    processed_at: Optional[datetime] = None
    processed_count: int = 0
    failed_count: int = 0
    
    notes: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    # Related payments
    payments: List[TutorPayment] = Field(default_factory=list)
    
    @computed_field
    @property
    def is_approved(self) -> bool:
        """Check if batch is approved."""
        return self.status in [BatchStatus.APPROVED, BatchStatus.PROCESSING, BatchStatus.COMPLETED]
    
    @computed_field
    @property
    def is_complete(self) -> bool:
        """Check if batch processing is complete."""
        return self.status == BatchStatus.COMPLETED
    
    @computed_field
    @property
    def success_rate(self) -> float:
        """Calculate success rate of processed payments."""
        if self.processed_count == 0:
            return 0.0
        return ((self.processed_count - self.failed_count) / self.processed_count) * 100
    
    def approve(self, manager_id: int, notes: Optional[str] = None) -> None:
        """Approve the payment batch."""
        self.status = BatchStatus.APPROVED
        self.approved_by = manager_id
        self.approved_at = datetime.utcnow()
        if notes:
            self.notes = notes
    
    def start_processing(self) -> None:
        """Mark batch as processing."""
        self.status = BatchStatus.PROCESSING
        self.processed_at = datetime.utcnow()
    
    def complete_processing(self, processed: int, failed: int) -> None:
        """Mark batch as completed with results."""
        self.status = BatchStatus.COMPLETED
        self.processed_count = processed
        self.failed_count = failed
    
    def add_payment(self, payment: TutorPayment) -> None:
        """Add a payment to the batch."""
        self.payments.append(payment)
        self.total_payments += 1
        self.total_amount += payment.total_amount
    
    class Config:
        json_encoders = {
            Decimal: lambda v: float(v),
            datetime: lambda v: v.isoformat() if v else None,
            date: lambda v: v.isoformat() if v else None
        }


class WeeklyPaymentReport(BaseModel):
    """Weekly payment report for managers."""
    week_start: date
    week_end: date
    batch: Optional[PaymentBatch] = None
    
    # Summary statistics
    total_tutors: int = 0
    total_appointments: int = 0
    total_service_amount: Decimal = Field(default=Decimal("0.00"))
    total_transport_amount: Decimal = Field(default=Decimal("0.00"))
    total_bonus_amount: Decimal = Field(default=Decimal("0.00"))
    grand_total: Decimal = Field(default=Decimal("0.00"))
    
    # Tutor breakdowns
    tutor_payments: Dict[int, List[TutorPayment]] = Field(default_factory=dict)
    tutor_summaries: List[Dict[str, Any]] = Field(default_factory=list)
    
    def add_payment(self, payment: TutorPayment) -> None:
        """Add a payment to the report."""
        if payment.tutor_id not in self.tutor_payments:
            self.tutor_payments[payment.tutor_id] = []
        
        self.tutor_payments[payment.tutor_id].append(payment)
        self.total_service_amount += payment.service_amount
        self.total_transport_amount += payment.transport_amount
        self.total_bonus_amount += payment.bonus_amount
        self.grand_total += payment.total_amount
    
    def calculate_summaries(self) -> None:
        """Calculate tutor summaries."""
        self.total_tutors = len(self.tutor_payments)
        self.total_appointments = sum(len(payments) for payments in self.tutor_payments.values())
        
        self.tutor_summaries = []
        for tutor_id, payments in self.tutor_payments.items():
            summary = {
                "tutor_id": tutor_id,
                "appointment_count": len(payments),
                "service_total": sum(p.service_amount for p in payments),
                "transport_total": sum(p.transport_amount for p in payments),
                "bonus_total": sum(p.bonus_amount for p in payments),
                "grand_total": sum(p.total_amount for p in payments),
                "payment_ids": [p.payment_id for p in payments if p.payment_id]
            }
            self.tutor_summaries.append(summary)
        
        # Sort by total amount descending
        self.tutor_summaries.sort(key=lambda x: x["grand_total"], reverse=True)
    
    class Config:
        json_encoders = {
            Decimal: lambda v: float(v),
            datetime: lambda v: v.isoformat() if v else None,
            date: lambda v: v.isoformat() if v else None
        }


class PaymentFilter(BaseModel):
    """Filter criteria for payment queries."""
    tutor_id: Optional[int] = None
    status: Optional[PaymentStatus] = None
    week_start: Optional[date] = None
    week_end: Optional[date] = None
    batch_id: Optional[int] = None
    min_amount: Optional[Decimal] = None
    max_amount: Optional[Decimal] = None
    limit: int = Field(default=100, ge=1, le=1000)
    offset: int = Field(default=0, ge=0)


class ScheduledTaskResult(BaseModel):
    """Result of scheduled task execution."""
    task_id: Optional[int] = None
    task_name: str
    task_type: str
    started_at: datetime = Field(default_factory=datetime.utcnow)
    completed_at: Optional[datetime] = None
    status: str = "running"
    
    # Task results
    records_processed: int = 0
    records_created: int = 0
    records_failed: int = 0
    
    # Error tracking
    error_message: Optional[str] = None
    error_details: Optional[Dict[str, Any]] = None
    
    # Task parameters
    parameters: Dict[str, Any] = Field(default_factory=dict)
    
    def complete(self, processed: int = 0, created: int = 0, failed: int = 0) -> None:
        """Mark task as completed."""
        self.status = "completed"
        self.completed_at = datetime.utcnow()
        self.records_processed = processed
        self.records_created = created
        self.records_failed = failed
    
    def fail(self, error_message: str, error_details: Optional[Dict[str, Any]] = None) -> None:
        """Mark task as failed."""
        self.status = "failed"
        self.completed_at = datetime.utcnow()
        self.error_message = error_message
        self.error_details = error_details
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }