/* animations.css - Smooth animations and transitions */

/* Page Transitions */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Button Animations */
@keyframes buttonPress {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes buttonHover {
  0% {
    transform: translateY(0) scale(1);
  }
  100% {
    transform: translateY(-2px) scale(1.02);
  }
}

/* Loading Animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Card Animations */
@keyframes cardFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

/* Utility Classes */
.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

.animate-fadeInUp {
  animation: fadeInUp 0.4s ease-out;
}

.animate-fadeInDown {
  animation: fadeInDown 0.4s ease-out;
}

.animate-slideInRight {
  animation: slideInRight 0.3s ease-out;
}

.animate-slideInLeft {
  animation: slideInLeft 0.3s ease-out;
}

.animate-scaleIn {
  animation: scaleIn 0.2s ease-out;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

.animate-bounce {
  animation: bounce 1s ease-in-out infinite;
}

/* Page Transition Wrapper */
.page-transition {
  animation: fadeInUp 0.4s ease-out;
}

/* Modal Animations */
.modal-enter {
  animation: scaleIn 0.2s ease-out;
}

.modal-backdrop-enter {
  animation: fadeIn 0.2s ease-out;
}

/* Dropdown Animations */
.dropdown-enter {
  animation: fadeInDown 0.2s ease-out;
  transform-origin: top center;
}

/* Tab Transitions */
.tab-panel-enter {
  animation: fadeInUp 0.3s ease-out;
}

/* Sidebar Animations */
.sidebar-enter {
  animation: slideInLeft 0.3s ease-out;
}

.sidebar-exit {
  animation: slideInLeft 0.3s ease-out reverse;
}

/* Button Group Animations */
.button-group-item {
  transition: all 0.2s ease;
}

.button-group-item:hover {
  animation: buttonHover 0.2s ease forwards;
}

.button-group-item:active {
  animation: buttonPress 0.1s ease;
}

/* Card Hover Effects */
.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  animation: cardFloat 2s ease-in-out infinite;
}

/* List Item Animations */
.list-item-enter {
  animation: fadeInUp 0.3s ease-out;
  animation-fill-mode: both;
}

.list-item-enter:nth-child(1) { animation-delay: 0.05s; }
.list-item-enter:nth-child(2) { animation-delay: 0.1s; }
.list-item-enter:nth-child(3) { animation-delay: 0.15s; }
.list-item-enter:nth-child(4) { animation-delay: 0.2s; }
.list-item-enter:nth-child(5) { animation-delay: 0.25s; }

/* Skeleton Loading Animation */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.skeleton {
  background: linear-gradient(
    90deg,
    var(--color-skeleton-base) 25%,
    var(--color-skeleton-shine) 50%,
    var(--color-skeleton-base) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* Form Field Animations */
.form-field-focus {
  transition: all 0.2s ease;
}

.form-field-focus:focus-within {
  transform: translateY(-2px);
}

/* Error State Animations */
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(2px);
  }
}

.error-shake {
  animation: shake 0.5s ease-in-out;
}

/* Success Animation */
@keyframes checkmark {
  0% {
    stroke-dashoffset: 100;
  }
  100% {
    stroke-dashoffset: 0;
  }
}

.checkmark-animation {
  stroke-dasharray: 100;
  animation: checkmark 0.5s ease-out forwards;
}

/* Notification Animations */
.notification-enter {
  animation: slideInRight 0.3s ease-out;
}

.notification-exit {
  animation: slideInRight 0.3s ease-out reverse;
}

/* Smooth Scrolling */
.smooth-scroll {
  scroll-behavior: smooth;
}

/* Micro-interactions */
.micro-interaction {
  transition: transform 0.1s ease;
}

.micro-interaction:active {
  transform: scale(0.98);
}

/* Focus Animations */
.focus-ring-animation {
  transition: box-shadow 0.2s ease;
}

.focus-ring-animation:focus {
  animation: pulse 1.5s ease-in-out infinite;
}