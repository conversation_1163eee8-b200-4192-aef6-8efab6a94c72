"""
Enhanced Messaging API endpoints with WebSocket integration
"""

from typing import Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Body, UploadFile, File
from pydantic import BaseModel
from datetime import datetime
import os
import uuid

from app.core.auth import require_auth, get_current_user
from app.core.database import get_db_connection
from app.services.messaging_service_websocket import WebSocketMessagingService
from app.services.twilio_service import TwilioService
from app.services.notification_service import NotificationService
from app.core.exceptions import ValidationError, ResourceNotFoundError
from app.config.settings import settings

router = APIRouter(prefix="/messaging", tags=["messaging"])

# Initialize enhanced messaging service
twilio_service = TwilioService()
notification_service = NotificationService()
messaging_service = WebSocketMessagingService(twilio_service, notification_service)


# Request/Response Models
class SendMessageRequest(BaseModel):
    content: str
    message_type: str = 'text'


class CreateConversationRequest(BaseModel):
    conversation_type: str
    client_id: Optional[int] = None
    tutor_id: Optional[int] = None
    phone_number: Optional[str] = None
    subject: Optional[str] = None


class MarkAsReadRequest(BaseModel):
    message_ids: list[int]


class SearchMessagesRequest(BaseModel):
    query: str
    conversation_id: Optional[int] = None
    sender_type: Optional[str] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None


# Conversation Endpoints
@router.get("/conversations")
async def get_conversations(
    conversation_type: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    limit: int = Query(50, le=100),
    offset: int = Query(0),
    user: dict = Depends(get_current_user),
):
    """Get conversations for the current user."""
    async with get_db_connection() as db:
        try:
            result = await messaging_service.get_conversations(
                db=db,
                user_id=user['user_id'],
                user_role=user['active_role'],
                conversation_type=conversation_type,
                status=status,
                limit=limit,
                offset=offset
            )
            return result
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))


@router.post("/conversations")
async def create_conversation(
    request: CreateConversationRequest,
    user: dict = Depends(get_current_user),
):
    """Create a new conversation."""
    async with get_db_connection() as db:
        try:
            # Set manager_id if user is a manager
            manager_id = user['user_id'] if user['active_role'] == 'manager' else None
            
            # Set client_id if user is a client
            if user['active_role'] == 'client' and not request.client_id:
                client_profile = await db.fetchrow(
                    "SELECT client_id FROM client_profiles WHERE user_id = $1",
                    user['user_id']
                )
                if client_profile:
                    request.client_id = client_profile['client_id']
            
            # Set tutor_id if user is a tutor
            if user['active_role'] == 'tutor' and not request.tutor_id:
                tutor_profile = await db.fetchrow(
                    "SELECT tutor_id FROM tutor_profiles WHERE user_id = $1",
                    user['user_id']
                )
                if tutor_profile:
                    request.tutor_id = tutor_profile['tutor_id']
            
            conversation_id = await messaging_service.create_conversation(
                db=db,
                conversation_type=request.conversation_type,
                client_id=request.client_id,
                tutor_id=request.tutor_id,
                manager_id=manager_id,
                phone_number=request.phone_number,
                subject=request.subject
            )
            
            return {"conversation_id": conversation_id}
            
        except ValidationError as e:
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))


@router.get("/conversations/{conversation_id}/messages")
async def get_conversation_messages(
    conversation_id: int,
    limit: int = Query(50, le=100),
    offset: int = Query(0),
    user: dict = Depends(get_current_user),
):
    """Get messages in a conversation."""
    async with get_db_connection() as db:
        try:
            messages = await messaging_service.get_conversation_messages(
                db=db,
                conversation_id=conversation_id,
                user_id=user['user_id'],
                user_role=user['active_role'],
                limit=limit,
                offset=offset
            )
            return {"messages": messages}
            
        except ResourceNotFoundError as e:
            raise HTTPException(status_code=404, detail=str(e))
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))


@router.post("/conversations/{conversation_id}/messages")
async def send_message(
    conversation_id: int,
    request: SendMessageRequest,
    user: dict = Depends(get_current_user),
):
    """Send a message in a conversation."""
    async with get_db_connection() as db:
        try:
            # Determine sender type based on user role
            sender_type = user['active_role']
            
            message = await messaging_service.send_message(
                db=db,
                conversation_id=conversation_id,
                sender_id=user['user_id'],
                sender_type=sender_type,
                content=request.content,
                message_type=request.message_type
            )
            
            return {"message": dict(message)}
            
        except ResourceNotFoundError as e:
            raise HTTPException(status_code=404, detail=str(e))
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))


@router.post("/conversations/{conversation_id}/messages/read")
async def mark_messages_as_read(
    conversation_id: int,
    request: MarkAsReadRequest,
    user: dict = Depends(get_current_user),
):
    """Mark messages as read."""
    async with get_db_connection() as db:
        try:
            for message_id in request.message_ids:
                await messaging_service.mark_message_as_read(
                    db=db,
                    message_id=message_id,
                    user_id=user['user_id']
                )
            
            return {"success": True, "marked_count": len(request.message_ids)}
            
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))


@router.post("/conversations/{conversation_id}/attachments")
async def upload_attachment(
    conversation_id: int,
    file: UploadFile = File(...),
    user: dict = Depends(get_current_user),
):
    """Upload a file attachment to a conversation."""
    async with get_db_connection() as db:
        try:
            # Validate file size (max 10MB)
            if file.size and file.size > 10 * 1024 * 1024:
                raise ValidationError("File size exceeds 10MB limit")
            
            # Validate file type
            allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 
                           'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
            if file.content_type not in allowed_types:
                raise ValidationError(f"File type {file.content_type} not allowed")
            
            # Generate unique filename
            file_extension = os.path.splitext(file.filename)[1]
            unique_filename = f"{uuid.uuid4()}{file_extension}"
            
            # Define upload path
            upload_dir = os.path.join(settings.UPLOAD_DIR, "messages", str(conversation_id))
            os.makedirs(upload_dir, exist_ok=True)
            file_path = os.path.join(upload_dir, unique_filename)
            
            # Save file
            contents = await file.read()
            with open(file_path, 'wb') as f:
                f.write(contents)
            
            # Create file URL
            file_url = f"/uploads/messages/{conversation_id}/{unique_filename}"
            
            # Send message with file attachment
            sender_type = user['active_role']
            message = await messaging_service.send_message(
                db=db,
                conversation_id=conversation_id,
                sender_id=user['user_id'],
                sender_type=sender_type,
                content=f"Sent a file: {file.filename}",
                message_type='file'
            )
            
            # Update message with file details
            await db.execute("""
                UPDATE messaging_messages
                SET file_url = $1,
                    file_name = $2,
                    file_size = $3,
                    file_type = $4
                WHERE message_id = $5
            """, file_url, file.filename, file.size, file.content_type, message['message_id'])
            
            return {
                "message_id": message['message_id'],
                "file_url": file_url,
                "file_name": file.filename,
                "file_size": file.size
            }
            
        except ValidationError as e:
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))


@router.post("/search")
async def search_messages(
    request: SearchMessagesRequest,
    limit: int = Query(50, le=100),
    offset: int = Query(0),
    user: dict = Depends(get_current_user),
):
    """Search messages across conversations."""
    async with get_db_connection() as db:
        try:
            # Build search query
            query_parts = []
            params = []
            param_count = 0
            
            # Base query
            base_query = """
                SELECT DISTINCT
                    m.message_id,
                    m.conversation_id,
                    m.sender_type,
                    m.sender_id,
                    m.content,
                    m.created_at,
                    c.conversation_type,
                    -- Sender name
                    CASE 
                        WHEN m.sender_type = 'client' THEN cp.first_name || ' ' || cp.last_name
                        WHEN m.sender_type = 'tutor' THEN tp.first_name || ' ' || tp.last_name
                        WHEN m.sender_type = 'manager' THEN u.email
                        ELSE 'System'
                    END as sender_name
                FROM messaging_messages m
                JOIN messaging_conversations c ON m.conversation_id = c.conversation_id
                LEFT JOIN user_accounts u ON m.sender_id = u.user_id
                LEFT JOIN client_profiles cp ON u.user_id = cp.user_id AND m.sender_type = 'client'
                LEFT JOIN tutor_profiles tp ON u.user_id = tp.user_id AND m.sender_type = 'tutor'
                WHERE m.deleted_at IS NULL
            """
            
            # Add user access control
            if user['active_role'] == 'client':
                param_count += 1
                query_parts.append(f"c.client_id = (SELECT client_id FROM client_profiles WHERE user_id = ${param_count})")
                params.append(user['user_id'])
            elif user['active_role'] == 'tutor':
                param_count += 1
                query_parts.append(f"c.tutor_id = (SELECT tutor_id FROM tutor_profiles WHERE user_id = ${param_count})")
                params.append(user['user_id'])
            
            # Add search filters
            if request.query:
                param_count += 1
                query_parts.append(f"m.content ILIKE ${param_count}")
                params.append(f"%{request.query}%")
            
            if request.conversation_id:
                param_count += 1
                query_parts.append(f"m.conversation_id = ${param_count}")
                params.append(request.conversation_id)
            
            if request.sender_type:
                param_count += 1
                query_parts.append(f"m.sender_type = ${param_count}")
                params.append(request.sender_type)
            
            if request.date_from:
                param_count += 1
                query_parts.append(f"m.created_at >= ${param_count}")
                params.append(request.date_from)
            
            if request.date_to:
                param_count += 1
                query_parts.append(f"m.created_at <= ${param_count}")
                params.append(request.date_to)
            
            # Combine query
            if query_parts:
                base_query += " AND " + " AND ".join(query_parts)
            
            # Add ordering and pagination
            base_query += f" ORDER BY m.created_at DESC LIMIT ${param_count + 1} OFFSET ${param_count + 2}"
            params.extend([limit, offset])
            
            # Execute search
            rows = await db.fetch(base_query, *params)
            results = [dict(row) for row in rows]
            
            # Get total count
            count_query = base_query.replace(
                "SELECT DISTINCT m.message_id,", 
                "SELECT COUNT(DISTINCT m.message_id) as count FROM ("
            ).replace(
                f" ORDER BY m.created_at DESC LIMIT ${param_count + 1} OFFSET ${param_count + 2}", 
                ") as subquery"
            )
            total = await db.fetchval(count_query, *params[:-2])
            
            return {
                "results": results,
                "total": total,
                "limit": limit,
                "offset": offset,
                "has_more": offset + limit < total
            }
            
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))


@router.put("/conversations/{conversation_id}/status")
async def update_conversation_status(
    conversation_id: int,
    status: str = Body(..., embed=True),
    user: dict = Depends(get_current_user),
):
    """Update conversation status (close, archive, etc.)."""
    async with get_db_connection() as db:
        try:
            # Validate status
            valid_statuses = ['active', 'closed', 'archived']
            if status not in valid_statuses:
                raise ValidationError(f"Invalid status. Must be one of: {', '.join(valid_statuses)}")
            
            await messaging_service.update_conversation_status(
                db=db,
                conversation_id=conversation_id,
                status=status,
                updated_by=user['user_id']
            )
            
            return {"success": True, "status": status}
            
        except ValidationError as e:
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))