"""
WebSocket endpoints for real-time calendar updates.
"""

import json
import logging
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, Query
from typing import Optional

from app.services.websocket_service import (
    connection_manager, calendar_service, websocket_authenticator, handle_websocket_message
)
from app.core.exceptions import AuthenticationError
from app.config.settings import get_settings

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/ws", tags=["websocket"])

settings = get_settings()


@router.websocket("/calendar")
async def websocket_calendar_endpoint(
    websocket: WebSocket,
    room: Optional[str] = Query(None, description="Specific room to join"),
    token: Optional[str] = Query(None, description="JWT authentication token")
):
    """
    WebSocket endpoint for real-time calendar updates.
    
    Authentication:
    - Provide JWT token via query parameter: ?token=your_jwt_token
    - Or via Authorization header: Authorization: Bearer your_jwt_token
    
    Room Types:
    - calendar_global: Global calendar updates (managers, tutors)
    - tutor_{id}: Tutor-specific updates
    - client_{id}: Client-specific updates
    - date_{YYYY-MM-DD}: Date-specific updates
    - managers: Manager-only updates
    """
    
    # Initialize authenticator with actual settings
    auth = websocket_authenticator
    auth.secret_key = settings.SECRET_KEY
    
    # Authenticate WebSocket connection
    user_info = await auth.authenticate_websocket(websocket)
    
    if not user_info:
        await websocket.close(code=4001, reason="Authentication failed")
        return
    
    # Determine appropriate room
    room_id = auth.get_room_for_user(user_info, room)
    
    try:
        # Connect to room
        await connection_manager.connect(
            websocket=websocket,
            room_id=room_id,
            user_id=user_info['user_id'],
            user_roles=user_info['roles']
        )
        
        # Send initial calendar data (if needed)
        await calendar_service.send_connection_status(websocket, room_id)
        
        # Listen for messages
        while True:
            try:
                # Receive message from client
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # Handle the message
                await handle_websocket_message(websocket, message, user_info)
                
            except json.JSONDecodeError:
                logger.warning(f"Invalid JSON received from user {user_info['user_id']}")
                await connection_manager.send_personal_message({
                    'type': 'error',
                    'message': 'Invalid JSON format'
                }, websocket)
            
            except Exception as e:
                logger.error(f"Error processing WebSocket message: {e}")
                break
    
    except WebSocketDisconnect:
        logger.info(f"User {user_info['user_id']} disconnected from room {room_id}")
    
    except Exception as e:
        logger.error(f"WebSocket connection error: {e}")
    
    finally:
        connection_manager.disconnect(websocket)


@router.websocket("/calendar/tutor/{tutor_id}")
async def websocket_tutor_calendar(
    websocket: WebSocket,
    tutor_id: int,
    token: Optional[str] = Query(None, description="JWT authentication token")
):
    """
    WebSocket endpoint for tutor-specific calendar updates.
    
    This endpoint is specifically for tutors to receive updates about their own appointments,
    availability changes, and time-off requests.
    """
    
    # Initialize authenticator
    auth = websocket_authenticator
    auth.secret_key = settings.SECRET_KEY
    
    # Authenticate WebSocket connection
    user_info = await auth.authenticate_websocket(websocket)
    
    if not user_info:
        await websocket.close(code=4001, reason="Authentication failed")
        return
    
    # Verify user can access this tutor's calendar
    if user_info['user_id'] != tutor_id and 'manager' not in user_info.get('roles', []):
        await websocket.close(code=4003, reason="Access denied")
        return
    
    room_id = f"tutor_{tutor_id}"
    
    try:
        await connection_manager.connect(
            websocket=websocket,
            room_id=room_id,
            user_id=user_info['user_id'],
            user_roles=user_info['roles']
        )
        
        # Send tutor-specific initialization data
        await calendar_service.send_connection_status(websocket, room_id)
        
        # Listen for messages
        while True:
            try:
                data = await websocket.receive_text()
                message = json.loads(data)
                await handle_websocket_message(websocket, message, user_info)
            
            except json.JSONDecodeError:
                await connection_manager.send_personal_message({
                    'type': 'error',
                    'message': 'Invalid JSON format'
                }, websocket)
            
            except Exception as e:
                logger.error(f"Error in tutor WebSocket: {e}")
                break
    
    except WebSocketDisconnect:
        logger.info(f"Tutor {tutor_id} disconnected")
    
    finally:
        connection_manager.disconnect(websocket)


@router.websocket("/calendar/managers")
async def websocket_managers_calendar(
    websocket: WebSocket,
    token: Optional[str] = Query(None, description="JWT authentication token")
):
    """
    WebSocket endpoint for manager-only calendar updates.
    
    Provides real-time updates for all calendar activities, conflicts,
    time-off requests, and system-wide notifications.
    """
    
    # Initialize authenticator
    auth = websocket_authenticator
    auth.secret_key = settings.SECRET_KEY
    
    # Authenticate WebSocket connection
    user_info = await auth.authenticate_websocket(websocket)
    
    if not user_info:
        await websocket.close(code=4001, reason="Authentication failed")
        return
    
    # Verify user is a manager
    if 'manager' not in user_info.get('roles', []):
        await websocket.close(code=4003, reason="Manager access required")
        return
    
    room_id = "managers"
    
    try:
        await connection_manager.connect(
            websocket=websocket,
            room_id=room_id,
            user_id=user_info['user_id'],
            user_roles=user_info['roles']
        )
        
        # Send manager-specific initialization data
        await calendar_service.send_connection_status(websocket, room_id)
        
        # Listen for messages
        while True:
            try:
                data = await websocket.receive_text()
                message = json.loads(data)
                await handle_websocket_message(websocket, message, user_info)
            
            except json.JSONDecodeError:
                await connection_manager.send_personal_message({
                    'type': 'error',
                    'message': 'Invalid JSON format'
                }, websocket)
            
            except Exception as e:
                logger.error(f"Error in manager WebSocket: {e}")
                break
    
    except WebSocketDisconnect:
        logger.info(f"Manager {user_info['user_id']} disconnected")
    
    finally:
        connection_manager.disconnect(websocket)


# HTTP endpoints for WebSocket management and monitoring

@router.get("/calendar/status")
async def get_websocket_status():
    """
    Get current WebSocket connection status.
    
    Returns information about active connections, rooms, and users.
    """
    
    total_connections = sum(
        len(connections) for connections in connection_manager.active_connections.values()
    )
    
    room_stats = {}
    for room_id, connections in connection_manager.active_connections.items():
        room_stats[room_id] = {
            'connection_count': len(connections),
            'users': connection_manager.get_room_users(room_id)
        }
    
    return {
        'total_connections': total_connections,
        'active_rooms': len(connection_manager.active_connections),
        'room_stats': room_stats,
        'timestamp': connection_manager.now_est().isoformat()
    }


@router.post("/calendar/broadcast")
async def broadcast_message(
    message: dict,
    room_id: Optional[str] = None,
    user_id: Optional[int] = None
):
    """
    Broadcast message to WebSocket connections.
    
    For testing and administrative purposes.
    Requires manager authentication in production.
    """
    
    if room_id:
        await connection_manager.broadcast_to_room(message, room_id)
        return {"status": "Message broadcasted to room", "room_id": room_id}
    
    elif user_id:
        await connection_manager.broadcast_to_user(message, user_id)
        return {"status": "Message sent to user", "user_id": user_id}
    
    else:
        # Broadcast to all rooms
        for room in connection_manager.active_connections.keys():
            await connection_manager.broadcast_to_room(message, room)
        return {"status": "Message broadcasted to all rooms"}


# Integration functions for appointment service

async def notify_appointment_change(
    action: str,
    appointment_data: dict,
    user_id: int,
    additional_data: Optional[dict] = None
):
    """
    Helper function to notify WebSocket clients about appointment changes.
    
    This should be called from the appointment service when changes occur.
    """
    
    if action == "created":
        await calendar_service.notify_appointment_created(appointment_data, user_id)
    
    elif action == "updated":
        changes = additional_data or {}
        await calendar_service.notify_appointment_updated(appointment_data, user_id, changes)
    
    elif action == "cancelled":
        reason = additional_data.get('reason') if additional_data else None
        await calendar_service.notify_appointment_cancelled(appointment_data, user_id, reason)
    
    elif action == "confirmed":
        await calendar_service.notify_appointment_confirmed(appointment_data, user_id)
    
    else:
        logger.warning(f"Unknown appointment action: {action}")


async def notify_conflict_detected(conflict_data: dict, affected_users: list):
    """Helper function to notify about scheduling conflicts."""
    await calendar_service.notify_conflict_detected(conflict_data, affected_users)


async def notify_availability_change(tutor_id: int, availability_data: dict, user_id: int):
    """Helper function to notify about availability changes."""
    await calendar_service.notify_availability_updated(tutor_id, availability_data, user_id)


async def notify_time_off_change(time_off_data: dict, action: str):
    """Helper function to notify about time-off changes."""
    await calendar_service.notify_time_off_request(time_off_data, action)