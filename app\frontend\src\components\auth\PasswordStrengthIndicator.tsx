import React from 'react';
import { useTranslation } from 'react-i18next';
import { Check, X } from 'lucide-react';

interface PasswordStrengthIndicatorProps {
  password: string;
}

export const PasswordStrengthIndicator: React.FC<PasswordStrengthIndicatorProps> = ({ password }) => {
  const { t } = useTranslation();
  
  const requirements = [
    {
      label: t('auth.passwordStrength.minLength'),
      met: password.length >= 8
    },
    {
      label: t('auth.passwordStrength.uppercase'),
      met: /[A-Z]/.test(password)
    },
    {
      label: t('auth.passwordStrength.lowercase'),
      met: /[a-z]/.test(password)
    },
    {
      label: t('auth.passwordStrength.number'),
      met: /\d/.test(password)
    }
  ];

  const strength = requirements.filter(req => req.met).length;
  
  const getStrengthColor = () => {
    if (strength === 0) return 'bg-gray-200';
    if (strength === 1) return 'bg-red-500';
    if (strength === 2) return 'bg-orange-500';
    if (strength === 3) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const getStrengthLabel = () => {
    if (strength === 0) return '';
    if (strength === 1) return t('auth.passwordStrength.strengthWeak');
    if (strength === 2) return t('auth.passwordStrength.strengthFair');
    if (strength === 3) return t('auth.passwordStrength.strengthGood');
    return t('auth.passwordStrength.strengthStrong');
  };

  return (
    <div className="mt-2 space-y-2">
      {/* Strength bar */}
      <div className="flex items-center gap-2">
        <div className="flex-1">
          <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
            <div 
              className={`h-full transition-all duration-300 ${getStrengthColor()}`}
              style={{ width: `${(strength / 4) * 100}%` }}
            />
          </div>
        </div>
        {getStrengthLabel() && (
          <span className={`text-xs font-medium ${
            strength === 4 ? 'text-green-600' : 
            strength === 3 ? 'text-yellow-600' : 
            strength === 2 ? 'text-orange-600' : 
            'text-red-600'
          }`}>
            {getStrengthLabel()}
          </span>
        )}
      </div>
      
      {/* Requirements list */}
      <div className="space-y-1">
        {requirements.map((req, index) => (
          <div key={index} className="flex items-center gap-2 text-xs">
            {req.met ? (
              <Check className="w-3 h-3 text-green-500" />
            ) : (
              <X className="w-3 h-3 text-gray-400" />
            )}
            <span className={req.met ? 'text-green-700' : 'text-gray-500'}>
              {req.label}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};