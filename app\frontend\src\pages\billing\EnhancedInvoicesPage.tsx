import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  FileText, Plus, Download, Send, Filter, Search, Calendar,
  DollarSign, CheckCircle, Clock, AlertCircle, XCircle, Mail,
  CreditCard, RefreshCw, Eye, Printer, MoreVertical
} from 'lucide-react';
import { Card } from '../../components/common/Card';
import Button from '../../components/common/Button';
import { Badge } from '../../components/common/Badge';
import { Input } from '../../components/common/Input';
import { Select } from '../../components/common/Select';
import { Modal } from '../../components/common/Modal';
import { Checkbox } from '../../components/common/Checkbox';
import { Dropdown } from '../../components/common/Dropdown';
import { format, subDays, startOfMonth, endOfMonth } from 'date-fns';
import toast from 'react-hot-toast';
import { useNavigate } from 'react-router-dom';
import { useApi } from '../../hooks/useApi';

interface Invoice {
  invoice_id: number;
  invoice_number: string;
  client_id: number;
  client_name: string;
  client_email: string;
  invoice_date: string;
  due_date: string;
  subtotal: number;
  tax_amount: number;
  total_amount: number;
  status: 'draft' | 'pending' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  payment_method?: string;
  paid_date?: string;
  items: InvoiceItem[];
  is_subscription_deduction: boolean;
  notes?: string;
}

interface InvoiceItem {
  item_id: number;
  description: string;
  quantity: number;
  unit_price: number;
  amount: number;
  appointment_id?: number;
  appointment_date?: string;
}

interface InvoiceSummary {
  totalPending: number;
  totalPaid: number;
  totalOverdue: number;
  draftCount: number;
  thisMonthRevenue: number;
  lastMonthRevenue: number;
}

const EnhancedInvoicesPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { get, post, put, patch, loading } = useApi();
  
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [selectedInvoices, setSelectedInvoices] = useState<number[]>([]);
  const [statusFilter, setStatusFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('this_month');
  const [searchTerm, setSearchTerm] = useState('');
  const [showInvoiceModal, setShowInvoiceModal] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);
  const [showBulkSendModal, setShowBulkSendModal] = useState(false);
  const [summary, setSummary] = useState<InvoiceSummary | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // Load invoices and summary data
  const loadInvoicesData = async () => {
    try {
      setRefreshing(true);
      
      // Build filters for API call
      const filters: Record<string, any> = {};
      if (statusFilter !== 'all') filters.status = statusFilter;
      if (dateFilter !== 'all') filters.date_filter = dateFilter;
      if (searchTerm) filters.search = searchTerm;
      
      // Load invoices and summary in parallel
      const [invoicesResponse, summaryResponse] = await Promise.all([
        get<Invoice[]>('/billing/invoices', { params: filters }),
        get<InvoiceSummary>('/billing/invoices/summary', { params: { period: dateFilter } })
      ]);
      
      setInvoices(invoicesResponse);
      setSummary(summaryResponse);
      
    } catch (error) {
      console.error('Error loading invoices data:', error);
      toast.error('Failed to load invoices data');
    } finally {
      setRefreshing(false);
    }
  };
  
  useEffect(() => {
    loadInvoicesData();
  }, [statusFilter, dateFilter, searchTerm]);

  const filteredInvoices = invoices.filter(invoice => {
    const matchesStatus = statusFilter === 'all' || invoice.status === statusFilter;
    const matchesSearch = searchTerm === '' || 
      invoice.client_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.invoice_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.client_email.toLowerCase().includes(searchTerm.toLowerCase());
    
    let matchesDate = true;
    if (dateFilter === 'this_month') {
      const invoiceDate = new Date(invoice.invoice_date);
      const monthStart = startOfMonth(new Date());
      const monthEnd = endOfMonth(new Date());
      matchesDate = invoiceDate >= monthStart && invoiceDate <= monthEnd;
    } else if (dateFilter === 'last_30_days') {
      const invoiceDate = new Date(invoice.invoice_date);
      const thirtyDaysAgo = subDays(new Date(), 30);
      matchesDate = invoiceDate >= thirtyDaysAgo;
    }
    
    return matchesStatus && matchesSearch && matchesDate;
  });

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const draftInvoiceIds = filteredInvoices
        .filter(inv => inv.status === 'draft')
        .map(inv => inv.invoice_id);
      setSelectedInvoices(draftInvoiceIds);
    } else {
      setSelectedInvoices([]);
    }
  };

  const handleSelectInvoice = (invoiceId: number, checked: boolean) => {
    if (checked) {
      setSelectedInvoices([...selectedInvoices, invoiceId]);
    } else {
      setSelectedInvoices(selectedInvoices.filter(id => id !== invoiceId));
    }
  };

  const handleBulkSend = async () => {
    try {
      await post('/billing/invoices/bulk-send', {
        invoice_ids: selectedInvoices
      });
      
      toast.success(`${selectedInvoices.length} invoices sent successfully`);
      setSelectedInvoices([]);
      setShowBulkSendModal(false);
      
      // Reload invoices to get updated statuses
      await loadInvoicesData();
      
    } catch (error) {
      console.error('Error sending invoices:', error);
      toast.error('Failed to send invoices');
    }
  };

  const handleGenerateInvoices = async () => {
    try {
      await post('/billing/invoices/generate-daily');
      
      toast.success('Daily invoice generation started. You will be notified when complete.');
      
      // Reload invoices to show new ones
      await loadInvoicesData();
      
    } catch (error) {
      console.error('Error generating invoices:', error);
      toast.error('Failed to generate invoices');
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig: Record<string, { color: string; icon: React.ReactNode }> = {
      draft: { color: 'bg-gray-100 text-gray-700', icon: <FileText className="w-4 h-4" /> },
      pending: { color: 'bg-yellow-100 text-yellow-700', icon: <Clock className="w-4 h-4" /> },
      sent: { color: 'bg-blue-100 text-blue-700', icon: <Send className="w-4 h-4" /> },
      paid: { color: 'bg-green-100 text-green-700', icon: <CheckCircle className="w-4 h-4" /> },
      overdue: { color: 'bg-red-100 text-red-700', icon: <AlertCircle className="w-4 h-4" /> },
      cancelled: { color: 'bg-gray-100 text-gray-700', icon: <XCircle className="w-4 h-4" /> }
    };

    const config = statusConfig[status] || statusConfig.draft;
    return (
      <Badge className={`inline-flex items-center gap-1 ${config.color}`}>
        {config.icon}
        <span className="capitalize">{status}</span>
      </Badge>
    );
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD',
    }).format(amount);
  };

  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Invoice Management</h1>
          <p className="text-gray-600 mt-1">
            Create, manage, and track client invoices
          </p>
        </div>
        <div className="flex gap-3">
          <Button
            variant="secondary"
            onClick={loadInvoicesData}
            disabled={loading || refreshing}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button
            variant="secondary"
            onClick={handleGenerateInvoices}
            disabled={loading}
          >
            <Calendar className="w-4 h-4 mr-2" />
            Generate Daily Invoices
          </Button>
          <Button
            variant="primary"
            onClick={() => navigate('/billing/invoices/new')}
          >
            <Plus className="w-4 h-4 mr-2" />
            Create Invoice
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      {loading && !summary ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="p-6 animate-pulse">
              <div className="flex items-center justify-between mb-4">
                <div className="w-8 h-8 bg-gray-200 rounded"></div>
                <div className="w-16 h-6 bg-gray-200 rounded"></div>
              </div>
              <div className="w-24 h-8 bg-gray-200 rounded mb-2"></div>
              <div className="w-20 h-4 bg-gray-200 rounded"></div>
            </Card>
          ))}
        </div>
      ) : summary ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <Clock className="w-8 h-8 text-yellow-600" />
              <Badge className="bg-yellow-100 text-yellow-700">Pending</Badge>
            </div>
            <p className="text-2xl font-bold">{formatCurrency(summary.totalPending)}</p>
            <p className="text-sm text-gray-600 mt-1">Awaiting payment</p>
          </Card>
          
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <CheckCircle className="w-8 h-8 text-green-600" />
              <Badge className="bg-green-100 text-green-700">Paid</Badge>
            </div>
            <p className="text-2xl font-bold">{formatCurrency(summary.totalPaid)}</p>
            <p className="text-sm text-gray-600 mt-1">This month</p>
          </Card>
          
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <AlertCircle className="w-8 h-8 text-red-600" />
              <Badge className="bg-red-100 text-red-700">Overdue</Badge>
            </div>
            <p className="text-2xl font-bold">{formatCurrency(summary.totalOverdue)}</p>
            <p className="text-sm text-gray-600 mt-1">Requires attention</p>
          </Card>
          
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <FileText className="w-8 h-8 text-gray-600" />
              <Badge className="bg-gray-100 text-gray-700">Draft</Badge>
            </div>
            <p className="text-2xl font-bold">{summary.draftCount}</p>
            <p className="text-sm text-gray-600 mt-1">Ready to send</p>
          </Card>
        </div>
      ) : null}

      {/* Revenue Comparison */}
      {summary && (
        <Card className="p-6">
          <h2 className="font-semibold text-gray-900 mb-4">Revenue Overview</h2>
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600">This Month</p>
              <p className="text-3xl font-bold text-gray-900">{formatCurrency(summary.thisMonthRevenue)}</p>
              <p className="text-sm text-green-600 mt-1">
                +{((summary.thisMonthRevenue - summary.lastMonthRevenue) / summary.lastMonthRevenue * 100).toFixed(1)}% from last month
              </p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600">Last Month</p>
              <p className="text-3xl font-bold text-gray-900">{formatCurrency(summary.lastMonthRevenue)}</p>
            </div>
          </div>
        </Card>
      )}

      {/* Filters */}
      <Card className="p-4">
        <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
          <div className="flex gap-3 w-full lg:w-auto">
            <div className="relative flex-1 lg:w-64">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                placeholder="Search invoices..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              options={[
                { value: 'all', label: 'All Status' },
                { value: 'draft', label: 'Draft' },
                { value: 'pending', label: 'Pending' },
                { value: 'sent', label: 'Sent' },
                { value: 'paid', label: 'Paid' },
                { value: 'overdue', label: 'Overdue' },
                { value: 'cancelled', label: 'Cancelled' }
              ]}
              className="w-40"
            />
            <Select
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
              options={[
                { value: 'all', label: 'All Time' },
                { value: 'this_month', label: 'This Month' },
                { value: 'last_30_days', label: 'Last 30 Days' },
                { value: 'last_90_days', label: 'Last 90 Days' }
              ]}
              className="w-40"
            />
          </div>
          
          <div className="flex items-center gap-2">
            <Checkbox
              checked={selectedInvoices.length === filteredInvoices.filter(inv => inv.status === 'draft').length}
              onChange={(e) => handleSelectAll(e.target.checked)}
              label="Select all drafts"
            />
          </div>
        </div>
      </Card>

      {/* Bulk Actions */}
      {selectedInvoices.length > 0 && (
        <Card className="p-4 bg-blue-50 border-blue-200">
          <div className="flex items-center justify-between">
            <p className="text-sm font-medium text-blue-900">
              {selectedInvoices.length} invoice{selectedInvoices.length > 1 ? 's' : ''} selected
            </p>
            <div className="flex gap-2">
              <Button
                variant="secondary"
                size="sm"
                onClick={() => setSelectedInvoices([])}
              >
                Clear Selection
              </Button>
              <Button
                variant="primary"
                size="sm"
                onClick={() => setShowBulkSendModal(true)}
              >
                <Send className="w-4 h-4 mr-2" />
                Send Selected
              </Button>
            </div>
          </div>
        </Card>
      )}

      {/* Invoices List */}
      <Card className="overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Invoice
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Client
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Due Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredInvoices.map((invoice) => (
                <tr key={invoice.invoice_id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {invoice.status === 'draft' && (
                        <Checkbox
                          checked={selectedInvoices.includes(invoice.invoice_id)}
                          onChange={(e) => handleSelectInvoice(invoice.invoice_id, e.target.checked)}
                          className="mr-3"
                        />
                      )}
                      <div>
                        <p className="text-sm font-medium text-gray-900">{invoice.invoice_number}</p>
                        {invoice.is_subscription_deduction && (
                          <p className="text-xs text-gray-500">Subscription</p>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <p className="text-sm font-medium text-gray-900">{invoice.client_name}</p>
                      <p className="text-sm text-gray-500">{invoice.client_email}</p>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {format(new Date(invoice.invoice_date), 'MMM d, yyyy')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {format(new Date(invoice.due_date), 'MMM d, yyyy')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <p className="text-sm font-medium text-gray-900">{formatCurrency(invoice.total_amount)}</p>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(invoice.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setSelectedInvoice(invoice);
                          setShowInvoiceModal(true);
                        }}
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Dropdown
                        trigger={
                          <Button variant="ghost" size="sm">
                            <MoreVertical className="w-4 h-4" />
                          </Button>
                        }
                        items={[
                          {
                            label: 'View Details',
                            onClick: () => {
                              setSelectedInvoice(invoice);
                              setShowInvoiceModal(true);
                            },
                            icon: <Eye className="w-4 h-4" />
                          },
                          {
                            label: 'Download PDF',
                            onClick: () => toast.info('PDF download coming soon'),
                            icon: <Download className="w-4 h-4" />
                          },
                          {
                            label: 'Send Email',
                            onClick: () => toast.info('Email sending coming soon'),
                            icon: <Mail className="w-4 h-4" />,
                            disabled: invoice.status === 'paid'
                          },
                          {
                            label: 'Mark as Paid',
                            onClick: async () => {
                              try {
                                await patch(`/billing/invoices/${invoice.invoice_id}/mark-paid`);
                                toast.success('Invoice marked as paid');
                                await loadInvoicesData();
                              } catch (error) {
                                console.error('Error marking invoice as paid:', error);
                                toast.error('Failed to mark invoice as paid');
                              }
                            },
                            icon: <CheckCircle className="w-4 h-4" />,
                            disabled: invoice.status === 'paid'
                          },
                          {
                            label: 'Cancel Invoice',
                            onClick: async () => {
                              try {
                                await patch(`/billing/invoices/${invoice.invoice_id}/cancel`);
                                toast.success('Invoice cancelled');
                                await loadInvoicesData();
                              } catch (error) {
                                console.error('Error cancelling invoice:', error);
                                toast.error('Failed to cancel invoice');
                              }
                            },
                            icon: <XCircle className="w-4 h-4" />,
                            variant: 'danger' as const,
                            disabled: invoice.status === 'paid' || invoice.status === 'cancelled'
                          }
                        ]}
                      />
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>

      {/* Bulk Send Modal */}
      <Modal
        isOpen={showBulkSendModal}
        onClose={() => setShowBulkSendModal(false)}
        title="Send Selected Invoices"
      >
        <div className="space-y-4">
          <p className="text-gray-600">
            You are about to send {selectedInvoices.length} invoice{selectedInvoices.length > 1 ? 's' : ''} totaling:
          </p>
          
          <div className="bg-blue-50 p-4 rounded-lg text-center">
            <p className="text-3xl font-bold text-blue-900">
              {formatCurrency(
                invoices
                  .filter(inv => selectedInvoices.includes(inv.invoice_id))
                  .reduce((sum, inv) => sum + inv.total_amount, 0)
              )}
            </p>
          </div>
          
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <div className="flex gap-2">
              <AlertCircle className="w-5 h-5 text-yellow-600 flex-shrink-0 mt-0.5" />
              <div className="text-sm text-yellow-800">
                <p className="font-medium">Important:</p>
                <p>Invoices will be sent via email to the respective clients.</p>
              </div>
            </div>
          </div>
          
          <div className="flex gap-3 justify-end">
            <Button
              variant="ghost"
              onClick={() => setShowBulkSendModal(false)}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleBulkSend}
              disabled={loading}
            >
              {loading ? 'Sending...' : 'Send Invoices'}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Invoice Details Modal */}
      <Modal
        isOpen={showInvoiceModal}
        onClose={() => setShowInvoiceModal(false)}
        title="Invoice Details"
        size="lg"
      >
        {selectedInvoice && (
          <div className="space-y-6">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-lg font-semibold">{selectedInvoice.invoice_number}</h3>
                <p className="text-sm text-gray-600">Invoice Date: {format(new Date(selectedInvoice.invoice_date), 'MMM d, yyyy')}</p>
                <p className="text-sm text-gray-600">Due Date: {format(new Date(selectedInvoice.due_date), 'MMM d, yyyy')}</p>
              </div>
              {getStatusBadge(selectedInvoice.status)}
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Bill To:</h4>
                <p className="font-medium">{selectedInvoice.client_name}</p>
                <p className="text-sm text-gray-600">{selectedInvoice.client_email}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Payment Details:</h4>
                {selectedInvoice.paid_date ? (
                  <>
                    <p className="text-sm">Paid on {format(new Date(selectedInvoice.paid_date), 'MMM d, yyyy')}</p>
                    <p className="text-sm text-gray-600">via {selectedInvoice.payment_method || 'N/A'}</p>
                  </>
                ) : (
                  <p className="text-sm text-gray-600">Unpaid</p>
                )}
              </div>
            </div>
            
            <div>
              <h4 className="font-medium mb-3">Invoice Items</h4>
              <div className="border rounded-lg overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Description</th>
                      <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">Qty</th>
                      <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">Rate</th>
                      <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">Amount</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {selectedInvoice.items.map((item) => (
                      <tr key={item.item_id}>
                        <td className="px-4 py-2">
                          <p className="text-sm">{item.description}</p>
                          {item.appointment_date && (
                            <p className="text-xs text-gray-500">{format(new Date(item.appointment_date), 'MMM d')}</p>
                          )}
                        </td>
                        <td className="px-4 py-2 text-right text-sm">{item.quantity}</td>
                        <td className="px-4 py-2 text-right text-sm">{formatCurrency(item.unit_price)}</td>
                        <td className="px-4 py-2 text-right text-sm font-medium">{formatCurrency(item.amount)}</td>
                      </tr>
                    ))}
                  </tbody>
                  <tfoot className="bg-gray-50">
                    <tr>
                      <td colSpan={3} className="px-4 py-2 text-right text-sm">Subtotal</td>
                      <td className="px-4 py-2 text-right text-sm font-medium">{formatCurrency(selectedInvoice.subtotal)}</td>
                    </tr>
                    <tr>
                      <td colSpan={3} className="px-4 py-2 text-right text-sm">Tax (15%)</td>
                      <td className="px-4 py-2 text-right text-sm font-medium">{formatCurrency(selectedInvoice.tax_amount)}</td>
                    </tr>
                    <tr>
                      <td colSpan={3} className="px-4 py-2 text-right text-sm font-semibold">Total</td>
                      <td className="px-4 py-2 text-right text-lg font-bold text-accent-red">{formatCurrency(selectedInvoice.total_amount)}</td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>
            
            {selectedInvoice.notes && (
              <div>
                <h4 className="font-medium mb-2">Notes</h4>
                <p className="text-sm text-gray-600">{selectedInvoice.notes}</p>
              </div>
            )}
            
            <div className="flex gap-3 justify-end">
              <Button
                variant="secondary"
                onClick={() => toast.info('PDF download coming soon')}
              >
                <Download className="w-4 h-4 mr-2" />
                Download PDF
              </Button>
              <Button
                variant="secondary"
                onClick={() => toast.info('Print feature coming soon')}
              >
                <Printer className="w-4 h-4 mr-2" />
                Print
              </Button>
              {selectedInvoice.status !== 'paid' && (
                <Button
                  variant="primary"
                  onClick={() => toast.info('Email sending coming soon')}
                >
                  <Mail className="w-4 h-4 mr-2" />
                  Send Email
                </Button>
              )}
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default EnhancedInvoicesPage;