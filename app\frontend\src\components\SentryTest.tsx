import React from 'react';
import * as Sentry from '@sentry/react';

export const SentryTest: React.FC = () => {
  const throwError = () => {
    throw new Error('Test error from React frontend!');
  };

  const captureMessage = () => {
    Sentry.captureMessage('Test message from TutorAide React app', 'info');
    alert('Message sent to Sentry!');
  };

  const captureException = () => {
    try {
      // This will throw an error
      const obj: any = null;
      obj.nonExistentMethod();
    } catch (error) {
      Sentry.captureException(error);
      alert('Exception captured and sent to Sentry!');
    }
  };

  const addBreadcrumb = () => {
    Sentry.addBreadcrumb({
      category: 'user',
      message: 'User clicked test button',
      level: 'info',
    });
    alert('Breadcrumb added! It will be included with the next error.');
  };

  const testPerformance = async () => {
    // Start a span for performance monitoring
    await Sentry.startSpan(
      {
        name: 'Test Performance Transaction',
        op: 'test',
      },
      async () => {
        // Simulate some work
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    );
    alert('Performance span sent to Sentry!');
  };

  // Only show in development
  if (!(import.meta as any).env.DEV) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white p-4 rounded-lg shadow-lg border border-gray-200 z-50">
      <h3 className="text-sm font-semibold mb-2">Sentry Test (Dev Only)</h3>
      <div className="space-y-2">
        <button
          onClick={captureMessage}
          className="w-full px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Send Test Message
        </button>
        <button
          onClick={captureException}
          className="w-full px-3 py-1 text-xs bg-yellow-500 text-white rounded hover:bg-yellow-600"
        >
          Capture Exception
        </button>
        <button
          onClick={addBreadcrumb}
          className="w-full px-3 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600"
        >
          Add Breadcrumb
        </button>
        <button
          onClick={testPerformance}
          className="w-full px-3 py-1 text-xs bg-purple-500 text-white rounded hover:bg-purple-600"
        >
          Test Performance
        </button>
        <button
          onClick={throwError}
          className="w-full px-3 py-1 text-xs bg-red-500 text-white rounded hover:bg-red-600"
        >
          Throw Error
        </button>
      </div>
    </div>
  );
};