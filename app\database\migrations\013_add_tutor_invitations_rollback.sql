-- Rollback Migration: Remove tutor invitations table
-- Description: Rollback script for 013_add_tutor_invitations.sql

-- Drop indexes
DROP INDEX IF EXISTS idx_tutor_invitations_email;
DROP INDEX IF EXISTS idx_tutor_invitations_token;
DROP INDEX IF EXISTS idx_tutor_invitations_status;
DROP INDEX IF EXISTS idx_tutor_invitations_expires;

-- Drop trigger
DROP TRIGGER IF EXISTS update_tutor_invitations_updated_at ON tutor_invitations;

-- Drop table
DROP TABLE IF EXISTS tutor_invitations;