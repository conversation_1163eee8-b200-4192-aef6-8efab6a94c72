import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { X, Plus, Trash2 } from 'lucide-react';
import { Modal } from '../common/Modal';
import Button from '../common/Button';
import { Input } from '../common/Input';
import { tutorService, TutorCreateRequest } from '../../services/tutorService';
import toast from 'react-hot-toast';

interface AddTutorModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const AddTutorModal: React.FC<AddTutorModalProps> = ({
  isOpen,
  onClose,
  onSuccess
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<TutorCreateRequest>({
    first_name: '',
    last_name: '',
    email: '',
    phone_number: '',
    bio: '',
    specializations: [],
    languages: ['en'],
    hourly_rate: 0,
    experience_years: 0,
    city: '',
    province: 'QC',
    postal_code: ''
  });

  const [newSpecialization, setNewSpecialization] = useState('');
  const [newLanguage, setNewLanguage] = useState('');

  const handleInputChange = (field: keyof TutorCreateRequest, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addSpecialization = () => {
    if (newSpecialization && !formData.specializations?.includes(newSpecialization)) {
      setFormData(prev => ({
        ...prev,
        specializations: [...(prev.specializations || []), newSpecialization]
      }));
      setNewSpecialization('');
    }
  };

  const removeSpecialization = (spec: string) => {
    setFormData(prev => ({
      ...prev,
      specializations: (prev.specializations || []).filter(s => s !== spec)
    }));
  };

  const addLanguage = () => {
    if (newLanguage && !formData.languages?.includes(newLanguage)) {
      setFormData(prev => ({
        ...prev,
        languages: [...(prev.languages || []), newLanguage]
      }));
      setNewLanguage('');
    }
  };

  const removeLanguage = (lang: string) => {
    setFormData(prev => ({
      ...prev,
      languages: (prev.languages || []).filter(l => l !== lang)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.first_name || !formData.last_name || !formData.email) {
      toast.error(t('validation.requiredFields'));
      return;
    }

    setLoading(true);
    try {
      await tutorService.createTutor(formData);
      toast.success(t('users.tutors.addSuccess'));
      onSuccess();
      onClose();
    } catch (error: any) {
      console.error('Error creating tutor:', error);
      toast.error(error.response?.data?.detail || t('users.tutors.addError'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="lg">
      <form onSubmit={handleSubmit} className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">
            {t('users.tutors.addTitle')}
          </h2>
          <button
            type="button"
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Form Fields */}
        <div className="space-y-6">
          {/* Basic Information */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {t('users.tutors.basicInfo')}
            </h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('users.tutors.firstName')} *
                </label>
                <Input
                  type="text"
                  value={formData.first_name}
                  onChange={(e) => handleInputChange('first_name', e.target.value)}
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('users.tutors.lastName')} *
                </label>
                <Input
                  type="text"
                  value={formData.last_name}
                  onChange={(e) => handleInputChange('last_name', e.target.value)}
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('users.tutors.email')} *
                </label>
                <Input
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('users.tutors.phone')}
                </label>
                <Input
                  type="tel"
                  value={formData.phone_number}
                  onChange={(e) => handleInputChange('phone_number', e.target.value)}
                />
              </div>
            </div>
          </div>

          {/* Professional Information */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {t('users.tutors.professionalInfo')}
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('users.tutors.bio')}
                </label>
                <textarea
                  value={formData.bio}
                  onChange={(e) => handleInputChange('bio', e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-red"
                  placeholder={t('users.tutors.bioPlaceholder')}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('users.tutors.hourlyRate')} (CAD)
                  </label>
                  <Input
                    type="number"
                    value={formData.hourly_rate}
                    onChange={(e) => handleInputChange('hourly_rate', parseFloat(e.target.value) || 0)}
                    min="0"
                    step="0.01"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('users.tutors.experienceYears')}
                  </label>
                  <Input
                    type="number"
                    value={formData.experience_years}
                    onChange={(e) => handleInputChange('experience_years', parseInt(e.target.value) || 0)}
                    min="0"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Specializations */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {t('users.tutors.specializations')}
            </h3>
            
            {/* Existing Specializations */}
            {formData.specializations && formData.specializations.length > 0 && (
              <div className="mb-3 flex flex-wrap gap-2">
                {formData.specializations.map((spec) => (
                  <div
                    key={spec}
                    className="inline-flex items-center bg-blue-100 text-blue-800 rounded-full px-3 py-1 text-sm"
                  >
                    <span>{spec}</span>
                    <button
                      type="button"
                      onClick={() => removeSpecialization(spec)}
                      className="ml-2 text-blue-600 hover:text-blue-800"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </div>
                ))}
              </div>
            )}

            {/* Add Specialization */}
            <div className="flex gap-2">
              <Input
                type="text"
                value={newSpecialization}
                onChange={(e) => setNewSpecialization(e.target.value)}
                placeholder={t('users.tutors.addSpecialization')}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    addSpecialization();
                  }
                }}
              />
              <Button
                type="button"
                variant="secondary"
                onClick={addSpecialization}
                disabled={!newSpecialization}
              >
                <Plus className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Languages */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {t('users.tutors.languages')}
            </h3>
            
            {/* Existing Languages */}
            {formData.languages && formData.languages.length > 0 && (
              <div className="mb-3 flex flex-wrap gap-2">
                {formData.languages.map((lang) => (
                  <div
                    key={lang}
                    className="inline-flex items-center bg-gray-100 text-gray-800 rounded-full px-3 py-1 text-sm"
                  >
                    <span>{lang === 'en' ? 'English' : lang === 'fr' ? 'Français' : lang}</span>
                    <button
                      type="button"
                      onClick={() => removeLanguage(lang)}
                      className="ml-2 text-gray-600 hover:text-gray-800"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </div>
                ))}
              </div>
            )}

            {/* Add Language */}
            <div className="flex gap-2">
              <select
                value={newLanguage}
                onChange={(e) => setNewLanguage(e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-red"
              >
                <option value="">{t('users.tutors.selectLanguage')}</option>
                <option value="en">English</option>
                <option value="fr">Français</option>
                <option value="es">Español</option>
                <option value="pt">Português</option>
                <option value="ar">العربية</option>
                <option value="zh">中文</option>
                <option value="other">{t('common.other')}</option>
              </select>
              <Button
                type="button"
                variant="secondary"
                onClick={addLanguage}
                disabled={!newLanguage}
              >
                <Plus className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Location */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {t('users.tutors.location')}
            </h3>
            <div className="grid grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('users.tutors.city')}
                </label>
                <Input
                  type="text"
                  value={formData.city}
                  onChange={(e) => handleInputChange('city', e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('users.tutors.province')}
                </label>
                <select
                  value={formData.province}
                  onChange={(e) => handleInputChange('province', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-red"
                >
                  <option value="AB">Alberta</option>
                  <option value="BC">British Columbia</option>
                  <option value="MB">Manitoba</option>
                  <option value="NB">New Brunswick</option>
                  <option value="NL">Newfoundland and Labrador</option>
                  <option value="NT">Northwest Territories</option>
                  <option value="NS">Nova Scotia</option>
                  <option value="NU">Nunavut</option>
                  <option value="ON">Ontario</option>
                  <option value="PE">Prince Edward Island</option>
                  <option value="QC">Quebec</option>
                  <option value="SK">Saskatchewan</option>
                  <option value="YT">Yukon</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('users.tutors.postalCode')}
                </label>
                <Input
                  type="text"
                  value={formData.postal_code}
                  onChange={(e) => handleInputChange('postal_code', e.target.value)}
                  placeholder="H0H 0H0"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="mt-8 flex justify-end space-x-3">
          <Button type="button" variant="secondary" onClick={onClose}>
            {t('common.cancel')}
          </Button>
          <Button type="submit" variant="primary" loading={loading}>
            {t('users.tutors.create')}
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default AddTutorModal;