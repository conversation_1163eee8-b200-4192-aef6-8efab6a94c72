/**
 * Enhanced Invoice Display Component
 * 
 * Demonstrates the new locale-specific formatting system with Quebec
 * standards for billing, currency, dates, and contact information.
 */

import React from 'react';
import { 
  Calendar, 
  DollarSign, 
  FileText, 
  User, 
  MapPin, 
  Phone,
  Mail,
  Clock,
  Calculator
} from 'lucide-react';
import { useTranslation } from '../../hooks/useTranslation';
import { 
  FormattedCurrency,
  FormattedDate,
  FormattedNumber,
  FormattedPhone,
  FormattedAddress,
  FormattedInvoiceNumber,
  FormattedTaxes,
  FormattedAppointmentTime,
  FormattedSessionDuration
} from '../common/FormattedDisplay';
import { AddressFormat } from '../../services/formatters';

interface SessionItem {
  session_id: number;
  date: string;
  start_time: string;
  end_time: string;
  duration_minutes: number;
  subject: string;
  tutor_name: string;
  rate_per_hour: number;
  total_amount: number;
  location: string;
  student_name?: string;
}

interface InvoiceData {
  invoice_id: number;
  invoice_number: string;
  client_name: string;
  client_email: string;
  client_phone: string;
  client_address: AddressFormat;
  issue_date: string;
  due_date: string;
  billing_period_start: string;
  billing_period_end: string;
  sessions: SessionItem[];
  subtotal: number;
  gst_amount: number;
  qst_amount: number;
  total_amount: number;
  payment_status: 'pending' | 'paid' | 'overdue' | 'cancelled';
  notes?: string;
}

interface InvoiceDisplayProps {
  invoice: InvoiceData;
  showPaymentButton?: boolean;
  onPayment?: () => void;
  className?: string;
}

export const InvoiceDisplay: React.FC<InvoiceDisplayProps> = ({
  invoice,
  showPaymentButton = false,
  onPayment,
  className = ''
}) => {
  const { t, currentLanguage } = useTranslation();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'overdue':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusText = (status: string) => {
    const statusMap = {
      'pending': currentLanguage === 'fr' ? 'En attente' : 'Pending',
      'paid': currentLanguage === 'fr' ? 'Payé' : 'Paid',
      'overdue': currentLanguage === 'fr' ? 'En retard' : 'Overdue',
      'cancelled': currentLanguage === 'fr' ? 'Annulé' : 'Cancelled'
    };
    return statusMap[status] || status;
  };

  return (
    <div className={`max-w-4xl mx-auto bg-white shadow-lg rounded-lg overflow-hidden ${className}`}>
      {/* Header */}
      <div className="bg-gradient-to-r from-red-600 to-red-700 text-white p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <FileText className="w-8 h-8" />
            <div>
              <h1 className="text-2xl font-bold">
                {currentLanguage === 'fr' ? 'Facture' : 'Invoice'}
              </h1>
              <FormattedInvoiceNumber 
                invoiceId={invoice.invoice_id}
                className="text-red-100 font-mono text-lg"
              />
            </div>
          </div>
          
          <div className="text-right">
            <div className="text-red-100 text-sm">
              {currentLanguage === 'fr' ? 'Date d\'émission' : 'Issue Date'}
            </div>
            <FormattedDate 
              date={invoice.issue_date}
              format="long"
              className="text-white font-medium"
            />
          </div>
        </div>
        
        {/* Status Badge */}
        <div className="mt-4">
          <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(invoice.payment_status)}`}>
            {getStatusText(invoice.payment_status)}
          </span>
        </div>
      </div>

      {/* Invoice Details */}
      <div className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Client Information */}
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
              <User className="w-5 h-5 text-red-600" />
              {currentLanguage === 'fr' ? 'Facturer à' : 'Bill To'}
            </h2>
            
            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
              <div className="font-medium text-gray-900">{invoice.client_name}</div>
              
              <div className="flex items-center gap-2 text-gray-600">
                <Mail className="w-4 h-4" />
                <span>{invoice.client_email}</span>
              </div>
              
              <div className="flex items-center gap-2 text-gray-600">
                <Phone className="w-4 h-4" />
                <FormattedPhone 
                  phone={invoice.client_phone}
                  showQuebecBadge={true}
                />
              </div>
              
              <div className="flex items-start gap-2 text-gray-600">
                <MapPin className="w-4 h-4 mt-0.5" />
                <FormattedAddress 
                  address={invoice.client_address}
                  multiline={true}
                />
              </div>
            </div>
          </div>

          {/* Invoice Information */}
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
              <Calendar className="w-5 h-5 text-red-600" />
              {currentLanguage === 'fr' ? 'Détails de la facture' : 'Invoice Details'}
            </h2>
            
            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">
                  {currentLanguage === 'fr' ? 'Période de facturation' : 'Billing Period'}:
                </span>
                <span className="font-medium">
                  <FormattedDate date={invoice.billing_period_start} format="short" />
                  {' - '}
                  <FormattedDate date={invoice.billing_period_end} format="short" />
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">
                  {currentLanguage === 'fr' ? 'Date d\'échéance' : 'Due Date'}:
                </span>
                <FormattedDate 
                  date={invoice.due_date}
                  format="long"
                  className="font-medium text-red-600"
                />
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">
                  {currentLanguage === 'fr' ? 'Nombre de sessions' : 'Sessions Count'}:
                </span>
                <span className="font-medium">{invoice.sessions.length}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Sessions Table */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <Clock className="w-5 h-5 text-red-600" />
            {currentLanguage === 'fr' ? 'Séances de tutorat' : 'Tutoring Sessions'}
          </h2>
          
          <div className="overflow-x-auto">
            <table className="w-full border border-gray-200 rounded-lg overflow-hidden">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {currentLanguage === 'fr' ? 'Date' : 'Date'}
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {currentLanguage === 'fr' ? 'Heure' : 'Time'}
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {currentLanguage === 'fr' ? 'Durée' : 'Duration'}
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {currentLanguage === 'fr' ? 'Matière' : 'Subject'}
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {currentLanguage === 'fr' ? 'Tuteur' : 'Tutor'}
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {currentLanguage === 'fr' ? 'Taux' : 'Rate'}
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {currentLanguage === 'fr' ? 'Montant' : 'Amount'}
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {invoice.sessions.map((session, index) => (
                  <tr key={session.session_id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <FormattedDate date={session.date} format="short" />
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <FormattedAppointmentTime 
                        startTime={session.start_time}
                        endTime={session.end_time}
                      />
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <FormattedSessionDuration minutes={session.duration_minutes} />
                    </td>
                    <td className="px-4 py-3">
                      <div className="font-medium text-gray-900">{session.subject}</div>
                      {session.student_name && (
                        <div className="text-sm text-gray-500">
                          {currentLanguage === 'fr' ? 'Étudiant' : 'Student'}: {session.student_name}
                        </div>
                      )}
                    </td>
                    <td className="px-4 py-3">
                      <div className="font-medium text-gray-900">{session.tutor_name}</div>
                      <div className="text-sm text-gray-500">{session.location}</div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <FormattedCurrency 
                        amount={session.rate_per_hour}
                        className="text-sm"
                      />
                      <div className="text-xs text-gray-500">
                        {currentLanguage === 'fr' ? '/heure' : '/hour'}
                      </div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-right">
                      <FormattedCurrency 
                        amount={session.total_amount}
                        className="font-medium"
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Totals Section */}
        <div className="border-t border-gray-200 pt-6">
          <div className="flex justify-end">
            <div className="w-full max-w-md space-y-3">
              {/* Subtotal */}
              <div className="flex justify-between items-center">
                <span className="text-gray-600">
                  {currentLanguage === 'fr' ? 'Sous-total' : 'Subtotal'}:
                </span>
                <FormattedCurrency 
                  amount={invoice.subtotal}
                  className="font-medium text-lg"
                />
              </div>

              {/* Taxes */}
              <div className="space-y-2 pt-2 border-t border-gray-100">
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-600">
                    {currentLanguage === 'fr' ? 'TPS (5%)' : 'GST (5%)'}:
                  </span>
                  <FormattedCurrency amount={invoice.gst_amount} />
                </div>
                
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-600">
                    {currentLanguage === 'fr' ? 'TVQ (9.975%)' : 'QST (9.975%)'}:
                  </span>
                  <FormattedCurrency amount={invoice.qst_amount} />
                </div>
              </div>

              {/* Total */}
              <div className="border-t border-gray-300 pt-3">
                <div className="flex justify-between items-center">
                  <span className="text-xl font-semibold text-gray-900">
                    {currentLanguage === 'fr' ? 'Total' : 'Total'}:
                  </span>
                  <FormattedCurrency 
                    amount={invoice.total_amount}
                    showCode={true}
                    className="text-2xl font-bold text-red-600"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Notes */}
        {invoice.notes && (
          <div className="mt-8 p-4 bg-red-50 rounded-lg border border-red-200">
            <h3 className="font-medium text-red-900 mb-2">
              {currentLanguage === 'fr' ? 'Notes' : 'Notes'}:
            </h3>
            <p className="text-red-800 text-sm">{invoice.notes}</p>
          </div>
        )}

        {/* Payment Information */}
        <div className="mt-8 p-6 bg-gray-50 rounded-lg">
          <h3 className="font-semibold text-gray-900 mb-3">
            {currentLanguage === 'fr' ? 'Informations de paiement' : 'Payment Information'}
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
            <div>
              <strong>
                {currentLanguage === 'fr' ? 'Méthodes de paiement acceptées' : 'Accepted Payment Methods'}:
              </strong>
              <ul className="mt-1 space-y-1">
                <li>• {currentLanguage === 'fr' ? 'Virement bancaire' : 'Bank Transfer'}</li>
                <li>• {currentLanguage === 'fr' ? 'Chèque' : 'Cheque'}</li>
                <li>• {currentLanguage === 'fr' ? 'Espèces' : 'Cash'}</li>
              </ul>
            </div>
            
            <div>
              <strong>
                {currentLanguage === 'fr' ? 'Coordonnées de paiement' : 'Payment Details'}:
              </strong>
              <div className="mt-1 space-y-1">
                <div>TutorAide Inc.</div>
                <div>Montreal, QC</div>
                <div><EMAIL></div>
              </div>
            </div>
          </div>
        </div>

        {/* Payment Button */}
        {showPaymentButton && invoice.payment_status === 'pending' && (
          <div className="mt-6 flex justify-center">
            <button
              onClick={onPayment}
              className="flex items-center gap-2 px-6 py-3 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors"
            >
              <DollarSign className="w-5 h-5" />
              {currentLanguage === 'fr' ? 'Effectuer le paiement' : 'Make Payment'}
            </button>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="bg-gray-100 px-6 py-4 text-center text-sm text-gray-600">
        {currentLanguage === 'fr' 
          ? 'Merci de votre confiance. Pour toute question, contactez-nous à <EMAIL>'
          : 'Thank you for your business. For any questions, contact <NAME_EMAIL>'
        }
      </div>
    </div>
  );
};
export default InvoiceDisplay;
