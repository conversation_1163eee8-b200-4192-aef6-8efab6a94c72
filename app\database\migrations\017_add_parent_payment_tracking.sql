-- Migration: 017_add_parent_payment_tracking.sql (Fixed for client_profiles)
-- Description: Add parent payment tracking tables for separated families
-- Author: System
-- Date: 2024-01-15

-- Create parents table if not exists
CREATE TABLE IF NOT EXISTS parents (
    parent_id SERIAL PRIMARY KEY,
    first_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    address_line_1 VARCHAR(200),
    address_line_2 VARCHAR(200),
    city VARCHAR(100),
    province VARCHAR(50),
    postal_code VARCHAR(10),
    country VARCHAR(50) DEFAULT 'Canada',
    is_primary BOOLEAN DEFAULT FALSE,
    relationship_to_child VARCHAR(50), -- 'mother', 'father', 'guardian', etc
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create client_parents junction table for many-to-many relationship
CREATE TABLE IF NOT EXISTS client_parents (
    client_parent_id SERIAL PRIMARY KEY,
    client_id INTEGER NOT NULL REFERENCES client_profiles(client_id),
    parent_id INTEGER NOT NULL REFERENCES parents(parent_id),
    is_billing_contact BOOLEAN DEFAULT FALSE,
    has_custody BOOLEAN DEFAULT TRUE,
    custody_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(client_id, parent_id)
);

-- Create invoice_parent_splits table
CREATE TABLE IF NOT EXISTS invoice_parent_splits (
    split_id SERIAL PRIMARY KEY,
    invoice_id INTEGER NOT NULL REFERENCES billing_invoices(invoice_id),
    parent_id INTEGER NOT NULL REFERENCES parents(parent_id),
    amount_paid DECIMAL(10,2) NOT NULL,
    payment_method VARCHAR(50),
    payment_date TIMESTAMP WITH TIME ZONE,
    payment_reference VARCHAR(100),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create parent_payment_methods table
CREATE TABLE IF NOT EXISTS parent_payment_methods (
    payment_method_id SERIAL PRIMARY KEY,
    parent_id INTEGER NOT NULL REFERENCES parents(parent_id),
    method_type VARCHAR(50) NOT NULL, -- 'credit_card', 'bank_transfer', 'etransfer', 'cheque'
    is_default BOOLEAN DEFAULT FALSE,
    account_last_four VARCHAR(4),
    account_holder_name VARCHAR(100),
    billing_address_line_1 VARCHAR(200),
    billing_address_line_2 VARCHAR(200),
    billing_city VARCHAR(100),
    billing_province VARCHAR(50),
    billing_postal_code VARCHAR(10),
    billing_country VARCHAR(50) DEFAULT 'Canada',
    stripe_payment_method_id VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_client_parents_client_id ON client_parents(client_id);
CREATE INDEX IF NOT EXISTS idx_client_parents_parent_id ON client_parents(parent_id);
CREATE INDEX IF NOT EXISTS idx_invoice_parent_splits_invoice_id ON invoice_parent_splits(invoice_id);
CREATE INDEX IF NOT EXISTS idx_invoice_parent_splits_parent_id ON invoice_parent_splits(parent_id);
CREATE INDEX IF NOT EXISTS idx_parent_payment_methods_parent_id ON parent_payment_methods(parent_id);

-- Add invoice_parent_splits reference to invoices if column doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'billing_invoices' 
                   AND column_name = 'split_payment') THEN
        ALTER TABLE billing_invoices ADD COLUMN split_payment BOOLEAN DEFAULT FALSE;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'billing_invoices' 
                   AND column_name = 'primary_parent_id') THEN
        ALTER TABLE billing_invoices ADD COLUMN primary_parent_id INTEGER REFERENCES parents(parent_id);
    END IF;
END $$;

-- Add billing preferences to client_profiles
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'client_profiles' 
                   AND column_name = 'default_billing_parent_id') THEN
        ALTER TABLE client_profiles ADD COLUMN default_billing_parent_id INTEGER REFERENCES parents(parent_id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'client_profiles' 
                   AND column_name = 'split_billing_enabled') THEN
        ALTER TABLE client_profiles ADD COLUMN split_billing_enabled BOOLEAN DEFAULT FALSE;
    END IF;
END $$;

-- Create view for parent payment history
CREATE OR REPLACE VIEW parent_payment_history AS
SELECT 
    p.parent_id,
    p.first_name || ' ' || p.last_name AS parent_name,
    p.email AS parent_email,
    i.invoice_number,
    i.created_at AS invoice_date,
    i.amount AS invoice_total,
    ips.amount_paid,
    ips.payment_method,
    ips.payment_date,
    ips.payment_reference,
    c.client_id,
    ua.email AS client_email
FROM invoice_parent_splits ips
JOIN parents p ON ips.parent_id = p.parent_id
JOIN billing_invoices i ON ips.invoice_id = i.invoice_id
JOIN client_profiles c ON i.client_id = c.client_id
JOIN user_accounts ua ON c.user_id = ua.user_id
ORDER BY ips.payment_date DESC;

-- Add comments
COMMENT ON TABLE parents IS 'Parent/guardian information for billing and contact purposes';
COMMENT ON TABLE client_parents IS 'Links clients to their parents/guardians for separated family scenarios';
COMMENT ON TABLE invoice_parent_splits IS 'Tracks which parent paid what portion of an invoice';
COMMENT ON TABLE parent_payment_methods IS 'Stores payment methods for each parent';
COMMENT ON VIEW parent_payment_history IS 'Consolidated view of parent payment history';