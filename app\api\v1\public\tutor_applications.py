"""
Public API endpoints for tutor applications.
No authentication required - tutors apply from marketing website.
"""

from typing import Dict, Optional
from datetime import datetime, timedelta
import secrets

from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel, Field, EmailStr, field_validator

from app.config.database import DatabaseManager
from app.core.dependencies import get_db_manager
from app.core.exceptions import DuplicateResourceError, ValidationError
from app.services.email_service import EmailService
from app.services.notification_service import NotificationService
from app.database.repositories.tutor_invitation_repository import TutorInvitationRepository
from app.core.logging import TutorAideLogger

logger = TutorAideLogger(__name__)

router = APIRouter(prefix="/public/tutor-applications", tags=["public-tutor-applications"])


class EducationInfo(BaseModel):
    """Education information from application."""
    current_level: str = Field(..., description="Current education level")
    institution: str = Field(..., description="Current institution")
    program: str = Field(..., description="Program of study")
    gpa: Optional[str] = Field(None, description="GPA if provided")
    highest_degree_level: Optional[str] = None
    highest_degree_name: Optional[str] = None
    degree_major: Optional[str] = None
    university: Optional[str] = None
    graduation_year: Optional[int] = None
    certifications: Optional[list[str]] = Field(default_factory=list)


class ExperienceInfo(BaseModel):
    """Experience information from application."""
    has_experience: str = Field(..., description="Yes/No")
    experience_details: Optional[str] = None
    strengths: str
    motivation: str
    years_teaching_total: Optional[int] = 0
    years_teaching_online: Optional[int] = 0
    years_tutoring: Optional[int] = 0
    current_occupation: Optional[str] = None
    languages_spoken: Optional[Dict[str, int]] = Field(default_factory=dict)
    teaching_methodologies: Optional[list[str]] = Field(default_factory=list)
    special_needs_experience: Optional[bool] = False
    age_groups_comfortable: Optional[list[str]] = Field(default_factory=list)


class TutoringPreferences(BaseModel):
    """Tutoring preferences from application."""
    subjects: list[str] = Field(..., min_items=1, description="Subjects to teach")
    levels: list[str] = Field(..., min_items=1, description="Teaching levels")
    preferred_location: str
    transport_method: str
    availability: Dict[str, Dict[str, bool]] = Field(..., description="Weekly availability")


class TutorApplicationSubmit(BaseModel):
    """Schema for tutor application submission from marketing website."""
    # Personal Information
    first_name: str = Field(..., min_length=1, max_length=100)
    last_name: str = Field(..., min_length=1, max_length=100)
    email: EmailStr
    phone: str = Field(..., min_length=10, max_length=20)
    date_of_birth: str
    address: str
    apartment: Optional[str] = None
    city: str
    postal_code: str = Field(..., min_length=6, max_length=7)
    province: str
    
    # Education
    education: EducationInfo
    
    # Experience
    experience: ExperienceInfo
    
    # Tutoring Preferences
    preferences: TutoringPreferences
    
    # References
    references: Optional[str] = None
    reference_contacts: Optional[list[Dict[str, str]]] = Field(default_factory=list)
    
    # Documents
    resume_url: Optional[str] = None
    
    @field_validator("phone")
    @classmethod
    def validate_phone(cls, v: str) -> str:
        """Validate and format phone number."""
        import re
        digits = re.sub(r"\D", "", v)
        
        if len(digits) == 10:
            return f"({digits[:3]}) {digits[3:6]}-{digits[6:]}"
        elif len(digits) == 11 and digits[0] == "1":
            return f"({digits[1:4]}) {digits[4:7]}-{digits[7:]}"
        else:
            raise ValueError("Invalid phone number format")
    
    @field_validator("postal_code")
    @classmethod
    def validate_postal_code(cls, v: str) -> str:
        """Validate Canadian postal code."""
        import re
        # Remove spaces and convert to uppercase
        v = v.replace(" ", "").upper()
        if not re.match(r"^[A-Z]\d[A-Z]\d[A-Z]\d$", v):
            raise ValueError("Invalid postal code format")
        # Format with space
        return f"{v[:3]} {v[3:]}"


class TutorApplicationResponse(BaseModel):
    """Response after submitting application."""
    success: bool
    message: str
    application_id: Optional[int] = None
    next_steps: Optional[str] = None


@router.post("", response_model=TutorApplicationResponse)
async def submit_tutor_application(
    application: TutorApplicationSubmit,
    db_manager: DatabaseManager = Depends(get_db_manager)
):
    """
    Submit a tutor application from the marketing website.
    This is a public endpoint - no authentication required.
    """
    try:
        async with db_manager.acquire() as conn:
            # Check if email already exists in system
            existing_check = await conn.fetchval(
                """
                SELECT COUNT(*) FROM users WHERE email = $1
                UNION ALL
                SELECT COUNT(*) FROM tutor_invitations 
                WHERE email = $1 AND status IN ('pending', 'applied', 'under_review')
                """,
                application.email.lower()
            )
            
            if existing_check > 0:
                logger.warning(f"Duplicate application attempt for email: {application.email}")
                raise DuplicateResourceError(
                    "An account or application already exists with this email address. "
                    "Please check your email for next steps <NAME_EMAIL>"
                )
            
            # Generate secure token for tracking
            token = secrets.token_urlsafe(32)
            
            # Prepare application data
            application_data = {
                "personal": {
                    "date_of_birth": application.date_of_birth,
                    "address": application.address,
                    "apartment": application.apartment,
                    "city": application.city,
                    "postal_code": application.postal_code,
                    "province": application.province
                },
                "education": application.education.model_dump(),
                "experience": application.experience.model_dump(),
                "preferences": application.preferences.model_dump(),
                "references": {
                    "text": application.references,
                    "contacts": application.reference_contacts or []
                },
                "documents": {
                    "resume_url": application.resume_url
                },
                "submitted_at": datetime.utcnow().isoformat()
            }
            
            # Create application record in tutor_invitations table
            application_id = await conn.fetchval(
                """
                INSERT INTO tutor_invitations (
                    email, first_name, last_name, phone,
                    token, status, application_status,
                    application_data, application_submitted_at,
                    invited_by, message, expires_at,
                    created_at, updated_at
                ) VALUES (
                    $1, $2, $3, $4,
                    $5, 'applied', 'under_review',
                    $6, $7,
                    1, 'Application from marketing website', $8,
                    NOW(), NOW()
                ) RETURNING invitation_id
                """,
                application.email.lower(),
                application.first_name,
                application.last_name,
                application.phone,
                token,
                application_data,
                datetime.utcnow(),
                datetime.utcnow() + timedelta(days=30)  # 30 day review period
            )
            
            # Send confirmation email to applicant
            email_service = EmailService()
            await email_service.send_email(
                to_email=application.email,
                subject="TutorAide Application Received",
                template="tutor_application_received",
                context={
                    "first_name": application.first_name,
                    "subjects": ", ".join(application.preferences.subjects[:3]),
                    "review_time": "2-3 business days"
                }
            )
            
            # Notify managers of new application
            notification_service = NotificationService(db_manager)
            manager_ids = await conn.fetch(
                """
                SELECT u.user_id 
                FROM users u
                JOIN user_roles ur ON u.user_id = ur.user_id
                WHERE ur.role = 'manager' AND u.is_active = true
                """,
            )
            
            for manager in manager_ids:
                await notification_service.send_in_app_notification(
                    user_id=manager['user_id'],
                    title="New Tutor Application",
                    message=f"{application.first_name} {application.last_name} has applied to be a tutor. Subjects: {', '.join(application.preferences.subjects[:3])}",
                    notification_type="application",
                    data={"application_id": application_id}
                )
            
            logger.info(f"Tutor application submitted: {application.email} (ID: {application_id})")
            
            return TutorApplicationResponse(
                success=True,
                message="Application submitted successfully! You will receive an email confirmation shortly.",
                application_id=application_id,
                next_steps="Our team will review your application within 2-3 business days. You will receive an email with the next steps."
            )
            
    except DuplicateResourceError:
        raise
    except Exception as e:
        logger.error(f"Error submitting tutor application: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while submitting your application. Please try again or contact support."
        )


@router.get("/check-email/{email}")
async def check_email_availability(
    email: EmailStr,
    db_manager: DatabaseManager = Depends(get_db_manager)
):
    """
    Check if an email is already in use.
    Public endpoint for form validation.
    """
    async with db_manager.acquire() as conn:
        exists = await conn.fetchval(
            """
            SELECT EXISTS(
                SELECT 1 FROM users WHERE email = $1
                UNION
                SELECT 1 FROM tutor_invitations 
                WHERE email = $1 AND status IN ('pending', 'applied', 'under_review', 'approved')
            )
            """,
            email.lower()
        )
        
        return {
            "available": not exists,
            "message": "Email is available" if not exists else "This email is already registered or has a pending application"
        }