"""
Service for managing client profiles.
"""

from datetime import datetime
from typing import Optional, List

from app.database.repositories.client_profile_repository import ClientProfileRepository
from app.database.repositories.user_repository import UserRepository
from app.models.client_profile_models import (
    ClientProfile,
    ClientProfileCreate,
    ClientProfileUpdate,
    ClientProfileWithUser,
    ClientProfilePublic
)
from app.models.auth_models import UserRole
from app.core.exceptions import (
    ResourceNotFoundError,
    ForbiddenError,
    ValidationError
)
from app.core.logging import TutorAideLogger
from app.config.database import DatabaseManager


logger = TutorAideLogger(__name__)


class ClientService:
    """Service for managing client profiles."""
    
    def __init__(self, db_manager: DatabaseManager):
        """Initialize the service."""
        self.db_manager = db_manager
        self.client_repo = ClientProfileRepository()
        self.user_repo = UserRepository()
    
    async def create_profile(
        self,
        user_id: int,
        profile_data: Client<PERSON><PERSON><PERSON>le<PERSON><PERSON>,
        current_user_id: int
    ) -> ClientProfile:
        """Create a new client profile."""
        # Verify user can create this profile
        if user_id != current_user_id and not await self._is_manager(current_user_id):
            raise ForbiddenError("You can only create your own profile")
        
        # Verify user exists and has client role
        async with self.db_manager.acquire() as conn:
            user = await self.user_repo.get(conn, user_id)
            if not user:
                raise ResourceNotFoundError("User not found")
            
            # Check if user has client role
            has_client_role = any(
                role.role == UserRole.CLIENT for role in user.roles
            )
            if not has_client_role:
                raise ValidationError("User does not have client role")
            
            # Create profile
            profile_data.user_id = user_id
            profile = await self.client_repo.create(conn, profile_data)
            
            logger.info(
                f"Client profile created for user {user_id} by {current_user_id}"
            )
            
            return profile
    
    async def get_profile(
        self,
        profile_id: int,
        current_user_id: int,
        current_user_role: UserRole
    ) -> ClientProfile:
        """Get a client profile."""
        async with self.db_manager.acquire() as conn:
            profile = await self.client_repo.get(conn, profile_id)
            if not profile:
                raise ResourceNotFoundError("Client profile not found")
            
            # Check permissions
            if not await self._can_view_profile(
                conn, profile, current_user_id, current_user_role
            ):
                raise ForbiddenError("You don't have permission to view this profile")
            
            return profile
    
    async def get_profile_by_user_id(
        self,
        user_id: int,
        current_user_id: int,
        current_user_role: UserRole
    ) -> Optional[ClientProfile]:
        """Get client profile by user ID."""
        async with self.db_manager.acquire() as conn:
            profile = await self.client_repo.get_by_user_id(conn, user_id)
            if not profile:
                return None
            
            # Check permissions
            if not await self._can_view_profile(
                conn, profile, current_user_id, current_user_role
            ):
                raise ForbiddenError("You don't have permission to view this profile")
            
            return profile
    
    async def get_profile_with_user(
        self,
        profile_id: int,
        current_user_id: int,
        current_user_role: UserRole
    ) -> ClientProfileWithUser:
        """Get client profile with user information."""
        async with self.db_manager.acquire() as conn:
            profile = await self.client_repo.get_with_user(conn, profile_id)
            if not profile:
                raise ResourceNotFoundError("Client profile not found")
            
            # Only managers can see full profile with user info
            if current_user_role != UserRole.MANAGER and profile.user_id != current_user_id:
                raise ForbiddenError("You don't have permission to view this profile")
            
            return profile
    
    async def get_public_profile(
        self,
        profile_id: int,
        current_user_role: UserRole
    ) -> ClientProfilePublic:
        """Get public view of client profile (for tutors)."""
        if current_user_role != UserRole.TUTOR:
            raise ForbiddenError("Only tutors can view public profiles")
        
        async with self.db_manager.acquire() as conn:
            profile = await self.client_repo.get_public_profile(conn, profile_id)
            if not profile:
                raise ResourceNotFoundError("Client profile not found")
            
            return profile
    
    async def update_profile(
        self,
        profile_id: int,
        update_data: ClientProfileUpdate,
        current_user_id: int,
        current_user_role: UserRole
    ) -> ClientProfile:
        """Update a client profile."""
        async with self.db_manager.acquire() as conn:
            # Get existing profile
            profile = await self.client_repo.get(conn, profile_id)
            if not profile:
                raise ResourceNotFoundError("Client profile not found")
            
            # Check permissions
            if not await self._can_edit_profile(
                conn, profile, current_user_id, current_user_role
            ):
                raise ForbiddenError("You don't have permission to edit this profile")
            
            # Managers can edit internal notes, others cannot
            if current_user_role != UserRole.MANAGER and update_data.internal_notes is not None:
                update_data.internal_notes = None
            
            # Update profile
            updated_profile = await self.client_repo.update(
                conn, profile_id, update_data
            )
            
            logger.info(
                f"Client profile {profile_id} updated by {current_user_id}"
            )
            
            return updated_profile
    
    async def delete_profile(
        self,
        profile_id: int,
        current_user_id: int,
        current_user_role: UserRole
    ) -> None:
        """Soft delete a client profile."""
        async with self.db_manager.acquire() as conn:
            profile = await self.client_repo.get(conn, profile_id)
            if not profile:
                raise ResourceNotFoundError("Client profile not found")
            
            # Only managers can delete profiles
            if current_user_role != UserRole.MANAGER:
                raise ForbiddenError("Only managers can delete profiles")
            
            await self.client_repo.delete(conn, profile_id)
            
            logger.info(
                f"Client profile {profile_id} deleted by {current_user_id}"
            )
    
    async def search_profiles(
        self,
        query: str,
        current_user_role: UserRole,
        limit: int = 10
    ) -> List[ClientProfileWithUser]:
        """Search client profiles."""
        # Only managers can search all profiles
        if current_user_role != UserRole.MANAGER:
            raise ForbiddenError("Only managers can search client profiles")
        
        async with self.db_manager.acquire() as conn:
            return await self.client_repo.search(conn, query, limit)
    
    async def _can_view_profile(
        self,
        conn,
        profile: ClientProfile,
        current_user_id: int,
        current_user_role: UserRole
    ) -> bool:
        """Check if user can view a profile."""
        # Managers can view all profiles
        if current_user_role == UserRole.MANAGER:
            return True
        
        # Users can view their own profile
        if profile.user_id == current_user_id:
            return True
        
        # Tutors can only see public profile view (handled separately)
        # Other clients cannot see each other's profiles
        return False
    
    async def _can_edit_profile(
        self,
        conn,
        profile: ClientProfile,
        current_user_id: int,
        current_user_role: UserRole
    ) -> bool:
        """Check if user can edit a profile."""
        # Managers can edit all profiles
        if current_user_role == UserRole.MANAGER:
            return True
        
        # Users can edit their own profile
        if profile.user_id == current_user_id:
            return True
        
        return False
    
    async def _is_manager(self, user_id: int) -> bool:
        """Check if user has manager role."""
        async with self.db_manager.acquire() as conn:
            user = await self.user_repo.get(conn, user_id)
            if not user:
                return False
            
            return any(
                role.role == UserRole.MANAGER and role.is_active
                for role in user.roles
            )
    
    async def _is_client_authorized(self, user_id: int, client_id: int) -> bool:
        """Check if user is authorized to access a client's data."""
        async with self.db_manager.acquire() as conn:
            # Get the client profile to check user_id
            profile = await self.client_repo.get(conn, client_id)
            if not profile:
                return False
            
            # User can access if they own the profile
            if profile.user_id == user_id:
                return True
            
            # Check if user is secondary client for any dependants
            secondary_access = await conn.fetchone(
                """
                SELECT 1 FROM client_dependants cd
                JOIN client_profiles cp ON cd.secondary_client_id = cp.client_id
                WHERE cd.primary_client_id = $1 AND cp.user_id = $2
                """,
                client_id, user_id
            )
            
            return secondary_access is not None
    
    # Dependant Management Methods
    
    async def create_dependant(
        self,
        client_id: int,
        dependant_data: dict,
        secondary_client_id: int = None,
        current_user_id: int = None
    ) -> dict:
        """Create a new dependant."""
        async with self.db_manager.acquire() as conn:
            dependant = await self.client_repo.create_dependant(
                conn, dependant_data, client_id, secondary_client_id
            )
            
            logger.info(
                f"Dependant created for client {client_id} by user {current_user_id}"
            )
            
            return dict(dependant)
    
    async def get_client_dependants(self, client_id: int) -> List[dict]:
        """Get all dependants for a client."""
        async with self.db_manager.acquire() as conn:
            dependants = await self.client_repo.get_client_dependants(conn, client_id)
            return [dict(d) for d in dependants]
    
    async def get_dependant(self, dependant_id: int, client_id: int) -> dict:
        """Get a specific dependant."""
        async with self.db_manager.acquire() as conn:
            dependant = await self.client_repo.get_dependant(conn, dependant_id)
            if not dependant:
                raise ResourceNotFoundError("Dependant not found")
            
            # Verify client has access to this dependant
            if (dependant["primary_client_id"] != client_id and 
                dependant.get("secondary_client_id") != client_id):
                raise ForbiddenError("You don't have access to this dependant")
            
            return dict(dependant)
    
    async def update_dependant(
        self,
        dependant_id: int,
        client_id: int,
        update_data: dict,
        current_user_id: int
    ) -> dict:
        """Update a dependant."""
        async with self.db_manager.acquire() as conn:
            # Verify access first
            await self.get_dependant(dependant_id, client_id)
            
            dependant = await self.client_repo.update_dependant(
                conn, dependant_id, update_data
            )
            
            logger.info(
                f"Dependant {dependant_id} updated by user {current_user_id}"
            )
            
            return dict(dependant)
    
    async def delete_dependant(
        self,
        dependant_id: int,
        client_id: int,
        current_user_id: int
    ) -> None:
        """Soft delete a dependant."""
        async with self.db_manager.acquire() as conn:
            # Verify access first
            await self.get_dependant(dependant_id, client_id)
            
            await self.client_repo.delete_dependant(conn, dependant_id)
            
            logger.info(
                f"Dependant {dependant_id} deleted by manager {current_user_id}"
            )
    
    async def search_dependants(self, query: str, limit: int = 10) -> List[dict]:
        """Search dependants (managers only)."""
        async with self.db_manager.acquire() as conn:
            dependants = await self.client_repo.search_dependants(conn, query, limit)
            return [dict(d) for d in dependants]
    
    # Learning Needs Methods
    
    async def create_learning_needs(
        self,
        client_id: int = None,
        dependant_id: int = None,
        needs_data: dict = None,
        current_user_id: int = None
    ) -> dict:
        """Create learning needs assessment."""
        async with self.db_manager.acquire() as conn:
            needs = await self.client_repo.create_learning_needs(
                conn, client_id, dependant_id, needs_data
            )
            
            logger.info(
                f"Learning needs created for client {client_id}, dependant {dependant_id} by user {current_user_id}"
            )
            
            return dict(needs)
    
    async def get_learning_needs(
        self,
        client_id: int = None,
        dependant_id: int = None
    ) -> List[dict]:
        """Get learning needs for client or dependant."""
        async with self.db_manager.acquire() as conn:
            needs = await self.client_repo.get_learning_needs(conn, client_id, dependant_id)
            return [dict(n) for n in needs]