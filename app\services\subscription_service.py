"""
Service layer for subscription management.
"""

from typing import List, Optional, Dict, Any
from datetime import date, datetime, timedelta
from decimal import Decimal
import asyncpg

from app.models.billing_models import PackageStatus
from app.core.exceptions import ValidationError, ResourceNotFoundError, BusinessLogicError
from app.core.logging import Tutor<PERSON>ideLogger

logger = TutorAideLogger.get_logger(__name__)


class SubscriptionService:
    
    async def get_client_subscriptions(
        self,
        db: asyncpg.Connection,
        client_id: int,
        include_expired: bool = True
    ) -> List[Dict[str, Any]]:
        """Get all subscriptions for a client."""
        query = """
            SELECT 
                s.subscription_id,
                s.client_id,
                s.subscription_name,
                s.subscription_type,
                s.hours_purchased,
                s.hours_remaining,
                s.start_date,
                s.end_date,
                s.status,
                s.auto_renew,
                s.price_paid,
                s.created_at,
                s.updated_at,
                pp.package_id,
                pp.package_name,
                pp.package_type
            FROM billing_subscriptions s
            LEFT JOIN billing_package_purchases pp ON s.package_purchase_id = pp.purchase_id
            WHERE s.client_id = $1
        """
        
        if not include_expired:
            query += " AND s.status = 'active'"
        
        query += " ORDER BY s.created_at DESC"
        
        rows = await db.fetch(query, client_id)
        
        subscriptions = []
        for row in rows:
            subscription = dict(row)
            # Calculate hours used
            subscription['hours_used'] = float(subscription['hours_purchased']) - float(subscription['hours_remaining'])
            subscription['billing_frequency'] = subscription['subscription_type']
            subscription['price_paid'] = float(subscription['price_paid'])
            subscriptions.append(subscription)
        
        return subscriptions
    
    async def get_subscription_statistics(
        self,
        db: asyncpg.Connection,
        client_id: int
    ) -> Dict[str, Any]:
        """Get subscription statistics for a client."""
        # Get all subscriptions
        subscriptions = await self.get_client_subscriptions(db, client_id)
        
        # Calculate statistics
        total_subscriptions = len(subscriptions)
        active_subscriptions = len([s for s in subscriptions if s['status'] == 'active'])
        total_hours_purchased = sum(float(s['hours_purchased']) for s in subscriptions)
        total_hours_used = sum(float(s['hours_used']) for s in subscriptions)
        total_spent = sum(float(s['price_paid']) for s in subscriptions)
        
        # Calculate average usage per month
        if subscriptions:
            oldest_date = min(s['created_at'] for s in subscriptions)
            months_active = max(1, (datetime.now() - oldest_date).days / 30)
            average_usage_per_month = total_hours_used / months_active
        else:
            average_usage_per_month = 0
        
        return {
            'total_subscriptions': total_subscriptions,
            'active_subscriptions': active_subscriptions,
            'total_hours_purchased': total_hours_purchased,
            'total_hours_used': total_hours_used,
            'total_spent': total_spent,
            'average_usage_per_month': round(average_usage_per_month, 1)
        }
    
    async def get_all_subscriptions(
        self,
        db: asyncpg.Connection,
        status: Optional[str] = None,
        expiring_days: Optional[int] = None,
        limit: int = 20,
        offset: int = 0
    ) -> Dict[str, Any]:
        """Get all subscriptions with filters (manager view)."""
        query = """
            SELECT 
                s.subscription_id,
                s.client_id,
                s.subscription_name,
                s.subscription_type,
                s.hours_purchased,
                s.hours_remaining,
                s.start_date,
                s.end_date,
                s.status,
                s.auto_renew,
                s.price_paid,
                s.created_at,
                s.updated_at,
                c.first_name || ' ' || c.last_name as client_name,
                pp.package_id,
                pp.package_name,
                pp.package_type
            FROM billing_subscriptions s
            JOIN client_profiles c ON s.client_id = c.client_id
            LEFT JOIN billing_package_purchases pp ON s.package_purchase_id = pp.purchase_id
            WHERE 1=1
        """
        
        params = []
        param_count = 0
        
        if status:
            param_count += 1
            query += f" AND s.status = ${param_count}"
            params.append(status)
        
        if expiring_days:
            param_count += 1
            query += f" AND s.status = 'active' AND s.end_date <= CURRENT_DATE + INTERVAL '${param_count} days'"
            params.append(expiring_days)
        
        # Get total count
        count_query = query.replace("SELECT", "SELECT COUNT(*) as total,", 1)
        count_row = await db.fetchrow(count_query, *params)
        total = count_row['total']
        
        # Add pagination
        query += " ORDER BY s.created_at DESC"
        param_count += 1
        query += f" LIMIT ${param_count}"
        params.append(limit)
        param_count += 1
        query += f" OFFSET ${param_count}"
        params.append(offset)
        
        rows = await db.fetch(query, *params)
        
        subscriptions = []
        for row in rows:
            subscription = dict(row)
            subscription['hours_used'] = float(subscription['hours_purchased']) - float(subscription['hours_remaining'])
            subscription['billing_frequency'] = subscription['subscription_type']
            subscription['price_paid'] = float(subscription['price_paid'])
            subscriptions.append(subscription)
        
        return {
            'subscriptions': subscriptions,
            'total': total,
            'limit': limit,
            'offset': offset,
            'has_more': (offset + limit) < total
        }
    
    async def get_subscription_usage(
        self,
        db: asyncpg.Connection,
        subscription_id: int
    ) -> List[Dict[str, Any]]:
        """Get usage history for a subscription."""
        query = """
            SELECT 
                st.transaction_id as usage_id,
                st.appointment_id,
                st.hours_used as hours_deducted,
                st.remaining_hours_after,
                st.transaction_date as usage_date,
                a.subject,
                t.first_name || ' ' || t.last_name as tutor_name
            FROM billing_subscription_transactions st
            JOIN appointments a ON st.appointment_id = a.appointment_id
            JOIN tutor_profiles t ON a.tutor_id = t.tutor_id
            WHERE st.subscription_id = $1
            ORDER BY st.transaction_date DESC
        """
        
        rows = await db.fetch(query, subscription_id)
        
        usage_history = []
        for row in rows:
            usage = dict(row)
            usage['hours_deducted'] = float(usage['hours_deducted'])
            usage['remaining_hours_after'] = float(usage['remaining_hours_after'])
            usage_history.append(usage)
        
        return usage_history
    
    async def renew_subscription(
        self,
        db: asyncpg.Connection,
        subscription_id: int,
        renewed_by_user_id: int
    ) -> None:
        """Renew a subscription for another period."""
        # Get current subscription
        subscription = await db.fetchrow(
            """
            SELECT * FROM billing_subscriptions 
            WHERE subscription_id = $1 AND status = 'active'
            """,
            subscription_id
        )
        
        if not subscription:
            raise ResourceNotFoundError("Active subscription not found")
        
        # Calculate new end date based on subscription type
        days_to_add = {
            'monthly': 30,
            'quarterly': 90,
            'annual': 365
        }.get(subscription['subscription_type'], 30)
        
        new_end_date = subscription['end_date'] + timedelta(days=days_to_add)
        
        # Update subscription
        await db.execute(
            """
            UPDATE billing_subscriptions
            SET 
                end_date = $2,
                hours_remaining = hours_remaining + hours_purchased,
                updated_at = NOW()
            WHERE subscription_id = $1
            """,
            subscription_id,
            new_end_date
        )
        
        logger.info(f"Renewed subscription {subscription_id} by user {renewed_by_user_id}")
    
    async def update_auto_renew(
        self,
        db: asyncpg.Connection,
        subscription_id: int,
        auto_renew: bool,
        client_id: int
    ) -> None:
        """Update auto-renewal setting for a subscription."""
        # Verify ownership
        subscription = await db.fetchrow(
            """
            SELECT subscription_id FROM billing_subscriptions 
            WHERE subscription_id = $1 AND client_id = $2
            """,
            subscription_id,
            client_id
        )
        
        if not subscription:
            raise ResourceNotFoundError("Subscription not found or access denied")
        
        # Update auto-renew
        await db.execute(
            """
            UPDATE billing_subscriptions
            SET auto_renew = $2, updated_at = NOW()
            WHERE subscription_id = $1
            """,
            subscription_id,
            auto_renew
        )
        
        logger.info(f"Updated auto-renew to {auto_renew} for subscription {subscription_id}")
    
    async def get_subscription_packages(
        self,
        db: asyncpg.Connection,
        is_active_only: bool = True
    ) -> List[Dict[str, Any]]:
        """Get available subscription packages."""
        query = """
            SELECT 
                package_id,
                package_name as name,
                description,
                session_count as hours_included,
                total_price as price,
                CASE 
                    WHEN package_type = 'tecfee' THEN 'quarterly'
                    WHEN package_type = 'bundle' THEN 'one_time'
                    ELSE 'monthly'
                END as billing_frequency,
                is_active,
                ARRAY[]::text[] as subject_restrictions
            FROM billing_packages
            WHERE deleted_at IS NULL
        """
        
        if is_active_only:
            query += " AND is_active = TRUE"
        
        query += " ORDER BY total_price ASC"
        
        rows = await db.fetch(query)
        
        packages = []
        for row in rows:
            package = dict(row)
            package['price'] = float(package['price'])
            packages.append(package)
        
        return packages
    
    async def check_subscription_hours(
        self,
        db: asyncpg.Connection,
        client_id: int,
        hours_needed: Decimal
    ) -> Optional[Dict[str, Any]]:
        """Check if client has enough subscription hours available."""
        query = """
            SELECT 
                subscription_id,
                subscription_name,
                hours_remaining,
                end_date
            FROM billing_subscriptions
            WHERE client_id = $1 
                AND status = 'active'
                AND hours_remaining >= $2
                AND end_date >= CURRENT_DATE
            ORDER BY end_date ASC
            LIMIT 1
        """
        
        row = await db.fetchrow(query, client_id, str(hours_needed))
        
        if row:
            return {
                'subscription_id': row['subscription_id'],
                'subscription_name': row['subscription_name'],
                'hours_remaining': float(row['hours_remaining']),
                'end_date': row['end_date']
            }
        
        return None
    
    async def deduct_subscription_hours(
        self,
        db: asyncpg.Connection,
        subscription_id: int,
        appointment_id: int,
        hours_to_deduct: Decimal,
        rate_per_hour: Decimal
    ) -> Dict[str, Any]:
        """Deduct hours from a subscription."""
        async with db.transaction():
            # Get current subscription
            subscription = await db.fetchrow(
                """
                SELECT hours_remaining 
                FROM billing_subscriptions 
                WHERE subscription_id = $1 AND status = 'active'
                FOR UPDATE
                """,
                subscription_id
            )
            
            if not subscription:
                raise ResourceNotFoundError("Active subscription not found")
            
            if subscription['hours_remaining'] < hours_to_deduct:
                raise BusinessLogicError("Insufficient subscription hours")
            
            # Update subscription hours
            remaining_after = subscription['hours_remaining'] - hours_to_deduct
            
            await db.execute(
                """
                UPDATE billing_subscriptions
                SET 
                    hours_remaining = $2,
                    last_used_at = NOW(),
                    updated_at = NOW()
                WHERE subscription_id = $1
                """,
                subscription_id,
                str(remaining_after)
            )
            
            # Record transaction
            await db.execute(
                """
                INSERT INTO billing_subscription_transactions (
                    subscription_id, appointment_id, hours_used,
                    rate_per_hour, amount_deducted, remaining_hours_after
                ) VALUES ($1, $2, $3, $4, $5, $6)
                """,
                subscription_id,
                appointment_id,
                str(hours_to_deduct),
                str(rate_per_hour),
                str(hours_to_deduct * rate_per_hour),
                str(remaining_after)
            )
            
            # Update subscription status if depleted
            if remaining_after == 0:
                await db.execute(
                    """
                    UPDATE billing_subscriptions
                    SET status = 'depleted'
                    WHERE subscription_id = $1
                    """,
                    subscription_id
                )
            
            return {
                'hours_deducted': float(hours_to_deduct),
                'remaining_hours': float(remaining_after),
                'subscription_id': subscription_id
            }


def get_subscription_service() -> SubscriptionService:
    """Dependency injection for SubscriptionService."""
    return SubscriptionService()