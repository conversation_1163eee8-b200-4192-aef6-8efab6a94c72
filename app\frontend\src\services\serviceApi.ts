import api from './api';

export interface ServicePreset {
  service_catalog_id: number;
  service_name: string;
  subject_area: string;
  service_type: 'online' | 'in_person' | 'library' | 'hybrid';
  service_level: 'elementary' | 'high_school' | 'college' | 'university' | 'adult';
  description?: string;
  prerequisites?: string;
  is_active: boolean;
  min_duration_minutes: number;
  max_duration_minutes: number;
  default_duration_minutes: number;
  created_at: string;
  updated_at: string;
  created_by: number;
  updated_by?: number;
}

export interface ServicePackage {
  service_package_id: number;
  package_name: string;
  package_type: 'individual' | 'tecfee' | 'bulk_hours' | 'monthly';
  description?: string;
  subject_areas: string[];
  service_types: string[];
  service_levels: string[];
  package_price: number;
  deposit_amount?: number;
  total_sessions: number;
  session_duration_minutes: number;
  validity_months: number;
  is_recurring: boolean;
  max_participants?: number;
  min_participants?: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  created_by: number;
  updated_by?: number;
  total_purchases?: number;
  active_subscriptions?: number;
}

export const serviceApi = {
  // Get all active service presets
  async getServicePresets(filters?: {
    service_type?: string;
    subject_area?: string;
    is_active_only?: boolean;
  }): Promise<ServicePreset[]> {
    try {
      // Temporarily use debug endpoint for testing
      const response = await api.get<any[]>('/debug/services/public');
      const services = response.data;

      // Apply filters
      let filteredServices = services;
      if (filters?.service_type && filters.service_type !== 'all') {
        filteredServices = filteredServices.filter(s => s.service_type === filters.service_type);
      }
      if (filters?.subject_area && filters.subject_area !== 'all') {
        filteredServices = filteredServices.filter(s => s.subject_area === filters.subject_area);
      }
      if (filters?.is_active_only !== false) {
        filteredServices = filteredServices.filter(s => s.is_active === true);
      }

      // Convert to ServicePreset format
      return filteredServices.map(service => ({
        service_id: service.service_id,
        name: service.service_name,
        description: service.description || '',
        subject_area: service.subject_area,
        service_type: service.service_type,
        grade_levels: [service.service_level],
        duration_options: [60], // Default duration
        frequency_options: ['weekly'],
        is_active: service.is_active,
        requires_location: service.service_type !== 'online',
        max_students: 1,
        min_students: 1,
        default_client_rate: 50, // Default rate
        default_tutor_rate: 40, // Default rate
        tags: [service.subject_area, service.service_level],
        created_at: service.created_at || new Date().toISOString(),
        updated_at: service.updated_at || new Date().toISOString()
      }));
    } catch (error) {
      console.error('Error fetching service presets:', error);
      // Fallback to original endpoint
      const params = new URLSearchParams();
      if (filters?.service_type) params.append('service_type', filters.service_type);
      if (filters?.subject_area) params.append('subject_area', filters.subject_area);
      if (filters?.is_active_only !== undefined) params.append('is_active_only', filters.is_active_only.toString());

      const response = await api.get<ServicePreset[]>(`/services/catalog?${params}`);
      return response.data;
    }
  },

  // Get service packages
  async getServicePackages(filters?: {
    package_type?: string;
    service_type?: string;
  }): Promise<ServicePackage[]> {
    const params = new URLSearchParams();
    if (filters?.package_type) params.append('package_type', filters.package_type);
    if (filters?.service_type) params.append('service_type', filters.service_type);
    
    const response = await api.get<ServicePackage[]>(`/services/packages?${params}`);
    return response.data;
  },

  // Create a new service package
  async createPackage(packageData: Omit<ServicePackage, 'service_package_id' | 'created_at' | 'updated_at' | 'created_by' | 'updated_by' | 'total_purchases' | 'active_subscriptions'>): Promise<ServicePackage> {
    const response = await api.post<ServicePackage>('/services/packages', packageData);
    return response.data;
  },

  // Update an existing service package
  async updatePackage(packageId: number, packageData: Partial<ServicePackage>): Promise<ServicePackage> {
    const response = await api.put<ServicePackage>(`/services/packages/${packageId}`, packageData);
    return response.data;
  },

  // Delete a service package
  async deletePackage(packageId: number): Promise<void> {
    await api.delete(`/services/packages/${packageId}`);
  },

  // Calculate pricing for a service
  async calculatePricing(params: {
    tutor_service_rate_id: number;
    duration_minutes: number;
    appointment_datetime: string;
    apply_surcharges?: boolean;
  }): Promise<{
    base_amount: number;
    surcharge_amount: number;
    client_total: number;
    tutor_earnings: number;
  }> {
    const response = await api.post('/services/pricing/calculate', params);
    return response.data;
  },

  // Get duration options formatted for select
  getDurationOptions(serviceType: string, presets: ServicePreset[]): Array<{ value: string; label: string }> {
    return presets
      .filter(preset => preset.service_type === serviceType && preset.is_active)
      .map(preset => ({
        value: preset.duration_minutes.toString(),
        label: `${preset.service_name} - ${this.formatDuration(preset.duration_minutes)}`
      }))
      .sort((a, b) => parseInt(a.value) - parseInt(b.value));
  },

  // Format duration for display
  formatDuration(minutes: number): string {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0 && mins > 0) {
      return `${hours}h ${mins}min`;
    } else if (hours > 0) {
      return `${hours}h`;
    } else {
      return `${mins}min`;
    }
  }
};