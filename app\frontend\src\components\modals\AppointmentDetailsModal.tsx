import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';
import { X, Clock, MapPin, User, Calendar, History, Shield, FileText, Edit } from 'lucide-react';
import api from '../../services/api';
import LoadingSpinner from '../ui/LoadingSpinner';
import { Badge } from '../common/Badge';
import toast from 'react-hot-toast';

interface AuditLog {
  audit_id: number;
  appointment_id: number;
  action: string;
  performed_by: number;
  performed_by_role: string;
  performed_by_name: string;
  field_name?: string;
  old_value?: string;
  new_value?: string;
  change_reason?: string;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
}

interface AppointmentDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  appointment: any;
  onEdit?: () => void;
}

const AppointmentDetailsModal: React.FC<AppointmentDetailsModalProps> = ({
  isOpen,
  onClose,
  appointment,
  onEdit
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<'details' | 'audit'>('details');
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [loadingAudit, setLoadingAudit] = useState(false);

  useEffect(() => {
    if (isOpen && appointment && activeTab === 'audit') {
      loadAuditLogs();
    }
  }, [isOpen, appointment, activeTab]);

  const loadAuditLogs = async () => {
    if (!appointment?.id) return;
    
    setLoadingAudit(true);
    try {
      const response = await api.get(`/api/v1/appointments/${appointment.id}/audit-logs`);
      setAuditLogs(response.data.audit_logs);
    } catch (error) {
      console.error('Error loading audit logs:', error);
      toast.error(t('appointments.errors.loadAuditLogs'));
    } finally {
      setLoadingAudit(false);
    }
  };

  if (!isOpen) return null;

  const getActionBadgeColor = (action: string) => {
    switch (action) {
      case 'created':
        return 'success';
      case 'updated':
      case 'status_changed':
        return 'info';
      case 'cancelled':
      case 'deleted':
        return 'error';
      case 'rescheduled':
        return 'warning';
      default:
        return 'secondary';
    }
  };

  const getActionLabel = (action: string) => {
    switch (action) {
      case 'created':
        return t('appointments.audit.actions.created');
      case 'updated':
        return t('appointments.audit.actions.updated');
      case 'status_changed':
        return t('appointments.audit.actions.statusChanged');
      case 'cancelled':
        return t('appointments.audit.actions.cancelled');
      case 'deleted':
        return t('appointments.audit.actions.deleted');
      case 'rescheduled':
        return t('appointments.audit.actions.rescheduled');
      default:
        return action;
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'manager':
        return 'primary';
      case 'tutor':
        return 'secondary';
      case 'client':
        return 'info';
      default:
        return 'secondary';
    }
  };

  const formatFieldName = (fieldName?: string) => {
    if (!fieldName) return '';
    
    const fieldLabels: Record<string, string> = {
      'status': t('appointments.fields.status'),
      'scheduled_date': t('appointments.fields.date'),
      'start_time': t('appointments.fields.startTime'),
      'end_time': t('appointments.fields.endTime'),
      'location_type': t('appointments.fields.locationType'),
      'notes': t('appointments.fields.notes')
    };
    
    return fieldLabels[fieldName] || fieldName;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-semibold text-gray-900">
              {t('appointments.details.title')}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
          
          {/* Tabs */}
          <div className="mt-4 flex space-x-4 border-b border-gray-200">
            <button
              onClick={() => setActiveTab('details')}
              className={`pb-2 px-1 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'details'
                  ? 'text-red-600 border-red-600'
                  : 'text-gray-500 border-transparent hover:text-gray-700'
              }`}
            >
              <div className="flex items-center space-x-2">
                <FileText className="w-4 h-4" />
                <span>{t('appointments.details.tabs.details')}</span>
              </div>
            </button>
            <button
              onClick={() => setActiveTab('audit')}
              className={`pb-2 px-1 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'audit'
                  ? 'text-red-600 border-red-600'
                  : 'text-gray-500 border-transparent hover:text-gray-700'
              }`}
            >
              <div className="flex items-center space-x-2">
                <History className="w-4 h-4" />
                <span>{t('appointments.details.tabs.audit')}</span>
              </div>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {activeTab === 'details' ? (
            <div className="space-y-6">
              {/* Basic Info */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  {t('appointments.details.basicInfo')}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      {t('appointments.fields.client')}
                    </label>
                    <p className="mt-1 text-sm text-gray-900">
                      {appointment.clientName}
                      {appointment.dependantName && ` (${appointment.dependantName})`}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      {t('appointments.fields.tutor')}
                    </label>
                    <p className="mt-1 text-sm text-gray-900">
                      {appointment.tutorName || t('appointments.details.notAssigned')}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      {t('appointments.fields.subject')}
                    </label>
                    <p className="mt-1 text-sm text-gray-900">{appointment.subject}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      {t('appointments.fields.status')}
                    </label>
                    <div className="mt-1">
                      <Badge variant={appointment.status === 'completed' ? 'success' : 'info'}>
                        {appointment.status}
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>

              {/* Schedule Info */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  {t('appointments.details.scheduleInfo')}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      <Calendar className="w-4 h-4 inline mr-1" />
                      {t('appointments.fields.date')}
                    </label>
                    <p className="mt-1 text-sm text-gray-900">
                      {format(new Date(appointment.startTime), 'PPP')}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      <Clock className="w-4 h-4 inline mr-1" />
                      {t('appointments.fields.time')}
                    </label>
                    <p className="mt-1 text-sm text-gray-900">
                      {format(new Date(appointment.startTime), 'p')} - {format(new Date(appointment.endTime), 'p')}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      <MapPin className="w-4 h-4 inline mr-1" />
                      {t('appointments.fields.location')}
                    </label>
                    <p className="mt-1 text-sm text-gray-900">
                      {appointment.location?.type || t('appointments.details.notSpecified')}
                      {appointment.location?.details && ` - ${appointment.location.details}`}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      {t('appointments.fields.duration')}
                    </label>
                    <p className="mt-1 text-sm text-gray-900">
                      {appointment.duration} {t('common.minutes')}
                    </p>
                  </div>
                </div>
              </div>

              {/* Notes */}
              {appointment.notes && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    {t('appointments.fields.notes')}
                  </h3>
                  <p className="text-sm text-gray-700 whitespace-pre-wrap">
                    {appointment.notes}
                  </p>
                </div>
              )}

              {/* Actions */}
              {onEdit && (
                <div className="flex justify-end space-x-4 pt-4 border-t">
                  <button
                    onClick={onEdit}
                    className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                  >
                    <Edit className="w-4 h-4 mr-2" />
                    {t('common.edit')}
                  </button>
                </div>
              )}
            </div>
          ) : (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {t('appointments.audit.title')}
              </h3>
              
              {loadingAudit ? (
                <div className="flex justify-center py-8">
                  <LoadingSpinner />
                </div>
              ) : auditLogs.length === 0 ? (
                <p className="text-center text-gray-500 py-8">
                  {t('appointments.audit.noLogs')}
                </p>
              ) : (
                <div className="space-y-4">
                  {auditLogs.map((log) => (
                    <div key={log.audit_id} className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <Badge variant={getActionBadgeColor(log.action)}>
                              {getActionLabel(log.action)}
                            </Badge>
                            <Badge variant={getRoleBadgeColor(log.performed_by_role)} size="sm">
                              {log.performed_by_role}
                            </Badge>
                            <span className="text-sm text-gray-600">
                              {t('common.by')} {log.performed_by_name}
                            </span>
                          </div>
                          
                          {log.field_name && (
                            <div className="text-sm text-gray-700 mb-1">
                              <span className="font-medium">{formatFieldName(log.field_name)}:</span>
                              {log.old_value && (
                                <span className="ml-2">
                                  <span className="line-through text-gray-500">{log.old_value}</span>
                                  {log.new_value && ' → '}
                                </span>
                              )}
                              {log.new_value && (
                                <span className="font-medium text-gray-900">{log.new_value}</span>
                              )}
                            </div>
                          )}
                          
                          {log.change_reason && (
                            <p className="text-sm text-gray-600 mt-1">
                              {t('appointments.audit.reason')}: {log.change_reason}
                            </p>
                          )}
                        </div>
                        
                        <div className="text-right ml-4">
                          <p className="text-xs text-gray-500">
                            {format(new Date(log.created_at), 'PPP')}
                          </p>
                          <p className="text-xs text-gray-500">
                            {format(new Date(log.created_at), 'p')}
                          </p>
                        </div>
                      </div>
                      
                      {log.ip_address && (
                        <div className="mt-2 pt-2 border-t border-gray-200">
                          <p className="text-xs text-gray-500">
                            <Shield className="w-3 h-3 inline mr-1" />
                            IP: {log.ip_address}
                          </p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AppointmentDetailsModal;