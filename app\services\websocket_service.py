"""
WebSocket service for real-time calendar updates with JWT authentication.
"""

import asyncio
import json
import logging
from typing import Dict, List, Set, Optional, Any, Union
from datetime import datetime, date
from fastapi import WebSocket, WebSocketDisconnect
from jose import JWTError, jwt
import weakref

from app.core.exceptions import ValidationError, AuthenticationError
from app.core.timezone import now_est
from app.models.auth_models import UserRoleType

logger = logging.getLogger(__name__)


class ConnectionManager:
    """Manages WebSocket connections with room-based broadcasting."""
    
    def __init__(self):
        # Use WeakSet to automatically clean up closed connections
        self.active_connections: Dict[str, Set[WebSocket]] = {}
        self.connection_users: weakref.WeakKeyDictionary = weakref.WeakKeyDictionary()
        self.user_connections: Dict[int, Set[WebSocket]] = {}
        
    async def connect(self, websocket: WebSocket, room_id: str, user_id: int, user_roles: List[str]):
        """Accept WebSocket connection and add to appropriate room."""
        await websocket.accept()
        
        # Add to room
        if room_id not in self.active_connections:
            self.active_connections[room_id] = set()
        self.active_connections[room_id].add(websocket)
        
        # Track user information
        self.connection_users[websocket] = {
            'user_id': user_id,
            'roles': user_roles,
            'room_id': room_id,
            'connected_at': now_est()
        }
        
        # Track user connections
        if user_id not in self.user_connections:
            self.user_connections[user_id] = set()
        self.user_connections[user_id].add(websocket)
        
        logger.info(f"User {user_id} connected to room {room_id}")
        
        # Send initial connection confirmation
        await self.send_personal_message({
            'type': 'connection_confirmed',
            'message': 'Connected to calendar updates',
            'room_id': room_id,
            'timestamp': now_est().isoformat()
        }, websocket)
    
    def disconnect(self, websocket: WebSocket):
        """Remove WebSocket connection from all rooms and tracking."""
        user_info = self.connection_users.get(websocket)
        if user_info:
            room_id = user_info['room_id']
            user_id = user_info['user_id']
            
            # Remove from room
            if room_id in self.active_connections:
                self.active_connections[room_id].discard(websocket)
                if not self.active_connections[room_id]:
                    del self.active_connections[room_id]
            
            # Remove from user connections
            if user_id in self.user_connections:
                self.user_connections[user_id].discard(websocket)
                if not self.user_connections[user_id]:
                    del self.user_connections[user_id]
            
            logger.info(f"User {user_id} disconnected from room {room_id}")
    
    async def send_personal_message(self, message: Dict[str, Any], websocket: WebSocket):
        """Send message to specific WebSocket connection."""
        try:
            await websocket.send_text(json.dumps(message, default=str))
        except Exception as e:
            logger.error(f"Error sending personal message: {e}")
            self.disconnect(websocket)
    
    async def broadcast_to_room(self, message: Dict[str, Any], room_id: str, exclude_user: Optional[int] = None):
        """Broadcast message to all connections in a room."""
        if room_id not in self.active_connections:
            return
        
        disconnected_connections = []
        
        for connection in self.active_connections[room_id].copy():
            user_info = self.connection_users.get(connection)
            if user_info and exclude_user and user_info['user_id'] == exclude_user:
                continue
            
            try:
                await connection.send_text(json.dumps(message, default=str))
            except Exception as e:
                logger.error(f"Error broadcasting to room {room_id}: {e}")
                disconnected_connections.append(connection)
        
        # Clean up disconnected connections
        for connection in disconnected_connections:
            self.disconnect(connection)
    
    async def broadcast_to_user(self, message: Dict[str, Any], user_id: int):
        """Send message to all connections for a specific user."""
        if user_id not in self.user_connections:
            return
        
        disconnected_connections = []
        
        for connection in self.user_connections[user_id].copy():
            try:
                await connection.send_text(json.dumps(message, default=str))
            except Exception as e:
                logger.error(f"Error broadcasting to user {user_id}: {e}")
                disconnected_connections.append(connection)
        
        # Clean up disconnected connections
        for connection in disconnected_connections:
            self.disconnect(connection)
    
    async def broadcast_to_multiple_rooms(self, message: Dict[str, Any], room_ids: List[str], exclude_user: Optional[int] = None):
        """Broadcast message to multiple rooms."""
        for room_id in room_ids:
            await self.broadcast_to_room(message, room_id, exclude_user)
    
    def get_room_connections_count(self, room_id: str) -> int:
        """Get number of active connections in a room."""
        return len(self.active_connections.get(room_id, set()))
    
    def get_user_connections_count(self, user_id: int) -> int:
        """Get number of active connections for a user."""
        return len(self.user_connections.get(user_id, set()))
    
    def get_room_users(self, room_id: str) -> List[Dict[str, Any]]:
        """Get list of users in a room."""
        if room_id not in self.active_connections:
            return []
        
        users = {}
        for connection in self.active_connections[room_id]:
            user_info = self.connection_users.get(connection)
            if user_info:
                user_id = user_info['user_id']
                if user_id not in users:
                    users[user_id] = {
                        'user_id': user_id,
                        'roles': user_info['roles'],
                        'connection_count': 0
                    }
                users[user_id]['connection_count'] += 1
        
        return list(users.values())


class CalendarWebSocketService:
    """Service for managing calendar-specific WebSocket communications."""
    
    def __init__(self, connection_manager: ConnectionManager):
        self.manager = connection_manager
    
    async def notify_appointment_created(self, appointment_data: Dict[str, Any], created_by: int):
        """Notify relevant users about new appointment creation."""
        message = {
            'type': 'appointment_created',
            'appointment': appointment_data,
            'created_by': created_by,
            'timestamp': now_est().isoformat()
        }
        
        # Determine which rooms to notify
        rooms_to_notify = self._get_rooms_for_appointment(appointment_data)
        
        await self.manager.broadcast_to_multiple_rooms(
            message, rooms_to_notify, exclude_user=created_by
        )
    
    async def notify_appointment_updated(self, appointment_data: Dict[str, Any], updated_by: int, changes: Dict[str, Any]):
        """Notify relevant users about appointment updates."""
        message = {
            'type': 'appointment_updated',
            'appointment': appointment_data,
            'changes': changes,
            'updated_by': updated_by,
            'timestamp': now_est().isoformat()
        }
        
        rooms_to_notify = self._get_rooms_for_appointment(appointment_data)
        
        await self.manager.broadcast_to_multiple_rooms(
            message, rooms_to_notify, exclude_user=updated_by
        )
    
    async def notify_appointment_cancelled(self, appointment_data: Dict[str, Any], cancelled_by: int, reason: Optional[str] = None):
        """Notify relevant users about appointment cancellation."""
        message = {
            'type': 'appointment_cancelled',
            'appointment': appointment_data,
            'cancelled_by': cancelled_by,
            'reason': reason,
            'timestamp': now_est().isoformat()
        }
        
        rooms_to_notify = self._get_rooms_for_appointment(appointment_data)
        
        await self.manager.broadcast_to_multiple_rooms(
            message, rooms_to_notify, exclude_user=cancelled_by
        )
    
    async def notify_appointment_confirmed(self, appointment_data: Dict[str, Any], confirmed_by: int):
        """Notify relevant users about appointment confirmation."""
        message = {
            'type': 'appointment_confirmed',
            'appointment': appointment_data,
            'confirmed_by': confirmed_by,
            'timestamp': now_est().isoformat()
        }
        
        rooms_to_notify = self._get_rooms_for_appointment(appointment_data)
        
        await self.manager.broadcast_to_multiple_rooms(
            message, rooms_to_notify
        )
    
    async def notify_conflict_detected(self, conflict_data: Dict[str, Any], affected_users: List[int]):
        """Notify users about scheduling conflicts."""
        message = {
            'type': 'conflict_detected',
            'conflict': conflict_data,
            'severity': conflict_data.get('severity', 'medium'),
            'timestamp': now_est().isoformat()
        }
        
        # Send to specific users who are affected
        for user_id in affected_users:
            await self.manager.broadcast_to_user(message, user_id)
    
    async def notify_availability_updated(self, tutor_id: int, availability_data: Dict[str, Any], updated_by: int):
        """Notify about tutor availability changes."""
        message = {
            'type': 'availability_updated',
            'tutor_id': tutor_id,
            'availability': availability_data,
            'updated_by': updated_by,
            'timestamp': now_est().isoformat()
        }
        
        # Notify calendar and tutor-specific rooms
        rooms_to_notify = [
            'calendar_global',
            f'tutor_{tutor_id}',
            'managers'
        ]
        
        await self.manager.broadcast_to_multiple_rooms(
            message, rooms_to_notify, exclude_user=updated_by
        )
    
    async def notify_time_off_request(self, time_off_data: Dict[str, Any], request_type: str):
        """Notify about time-off requests and approvals."""
        message = {
            'type': f'time_off_{request_type}',
            'time_off': time_off_data,
            'timestamp': now_est().isoformat()
        }
        
        # Notify managers and the requesting tutor
        await self.manager.broadcast_to_room(message, 'managers')
        await self.manager.broadcast_to_user(message, time_off_data['tutor_id'])
    
    async def send_connection_status(self, websocket: WebSocket, room_id: str):
        """Send current connection status to a specific connection."""
        connection_count = self.manager.get_room_connections_count(room_id)
        room_users = self.manager.get_room_users(room_id)
        
        message = {
            'type': 'connection_status',
            'room_id': room_id,
            'connection_count': connection_count,
            'users': room_users,
            'timestamp': now_est().isoformat()
        }
        
        await self.manager.send_personal_message(message, websocket)
    
    def _get_rooms_for_appointment(self, appointment_data: Dict[str, Any]) -> List[str]:
        """Determine which rooms should be notified for an appointment."""
        rooms = ['calendar_global']
        
        # Add tutor-specific room
        if 'tutor_id' in appointment_data:
            rooms.append(f"tutor_{appointment_data['tutor_id']}")
        
        # Add client-specific room
        if 'client_id' in appointment_data:
            rooms.append(f"client_{appointment_data['client_id']}")
        
        # Add dependant-specific room if applicable
        if appointment_data.get('dependant_id'):
            rooms.append(f"dependant_{appointment_data['dependant_id']}")
        
        # Add date-specific room for calendar views
        if 'scheduled_date' in appointment_data:
            date_str = appointment_data['scheduled_date']
            if isinstance(date_str, str):
                rooms.append(f"date_{date_str}")
            elif isinstance(date_str, date):
                rooms.append(f"date_{date_str.isoformat()}")
        
        # Always include managers room
        rooms.append('managers')
        
        return rooms


class WebSocketAuthenticator:
    """Handles WebSocket authentication using JWT tokens."""
    
    def __init__(self, secret_key: str, algorithm: str = "HS256"):
        self.secret_key = secret_key
        self.algorithm = algorithm
    
    async def authenticate_websocket(self, websocket: WebSocket) -> Optional[Dict[str, Any]]:
        """
        Authenticate WebSocket connection using JWT token.
        
        Returns user information if authentication successful, None otherwise.
        """
        try:
            # Try to get token from query parameters first
            token = websocket.query_params.get('token')
            
            if not token:
                # Try to get token from headers
                token = websocket.headers.get('Authorization')
                if token and token.startswith('Bearer '):
                    token = token[7:]
            
            if not token:
                logger.warning("No authentication token provided for WebSocket connection")
                return None
            
            # Decode and validate JWT token
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            user_id = payload.get('user_id')
            roles = payload.get('roles', [])
            exp = payload.get('exp')
            
            if not user_id:
                logger.warning("No user_id in JWT token")
                return None
            
            # Check token expiration
            if exp and datetime.utcnow().timestamp() > exp:
                logger.warning(f"Expired JWT token for user {user_id}")
                return None
            
            return {
                'user_id': user_id,
                'roles': roles,
                'token_payload': payload
            }
            
        except JWTError as e:
            logger.warning(f"JWT authentication failed: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error during WebSocket authentication: {e}")
            return None
    
    def get_room_for_user(self, user_info: Dict[str, Any], requested_room: Optional[str] = None) -> str:
        """
        Determine appropriate room for user based on roles and request.
        """
        user_id = user_info['user_id']
        roles = user_info.get('roles', [])
        
        # If specific room requested, validate access
        if requested_room:
            if self._can_access_room(user_info, requested_room):
                return requested_room
        
        # Default room assignment based on primary role
        if UserRoleType.MANAGER in roles:
            return 'calendar_global'
        elif UserRoleType.TUTOR in roles:
            return f'tutor_{user_id}'
        elif UserRoleType.CLIENT in roles:
            return f'client_{user_id}'
        else:
            return f'user_{user_id}'
    
    def _can_access_room(self, user_info: Dict[str, Any], room_id: str) -> bool:
        """Check if user can access specific room."""
        user_id = user_info['user_id']
        roles = user_info.get('roles', [])
        
        # Managers can access any room
        if UserRoleType.MANAGER in roles:
            return True
        
        # Users can access their own rooms
        if room_id in [f'user_{user_id}', f'tutor_{user_id}', f'client_{user_id}']:
            return True
        
        # Calendar global access for tutors and managers
        if room_id == 'calendar_global' and UserRoleType.TUTOR in roles:
            return True
        
        # Date-specific rooms for tutors and managers
        if room_id.startswith('date_') and UserRoleType.TUTOR in roles:
            return True
        
        # Dependant rooms (check if user is parent - would need additional validation)
        if room_id.startswith('dependant_'):
            # This would require checking parent-child relationships
            # For now, allow if user is a client
            return UserRoleType.CLIENT in roles
        
        return False


# Global instances
connection_manager = ConnectionManager()
calendar_service = CalendarWebSocketService(connection_manager)

# This would be injected with actual secret key from settings
websocket_authenticator = WebSocketAuthenticator("your-secret-key-here")


async def handle_websocket_message(websocket: WebSocket, message: Dict[str, Any], user_info: Dict[str, Any]):
    """Handle incoming WebSocket messages from clients."""
    message_type = message.get('type')
    
    try:
        if message_type == 'ping':
            await connection_manager.send_personal_message({
                'type': 'pong',
                'timestamp': now_est().isoformat()
            }, websocket)
        
        elif message_type == 'join_room':
            # Handle room switching (if needed)
            new_room = message.get('room_id')
            if new_room and websocket_authenticator._can_access_room(user_info, new_room):
                # This would require implementing room switching logic
                pass
        
        elif message_type == 'request_status':
            # Send current connection status
            room_id = connection_manager.connection_users.get(websocket, {}).get('room_id')
            if room_id:
                await calendar_service.send_connection_status(websocket, room_id)
        
        else:
            logger.warning(f"Unknown message type: {message_type}")
    
    except Exception as e:
        logger.error(f"Error handling WebSocket message: {e}")
        await connection_manager.send_personal_message({
            'type': 'error',
            'message': 'Error processing message',
            'timestamp': now_est().isoformat()
        }, websocket)