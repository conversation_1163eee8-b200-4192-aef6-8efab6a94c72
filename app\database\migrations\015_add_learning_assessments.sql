-- Migration 015: Add learning needs assessment system
-- This migration creates tables for comprehensive learning assessments

-- Create learning needs assessments table
CREATE TABLE learning_needs_assessments (
    assessment_id SERIAL PRIMARY KEY,
    client_id INTEGER NOT NULL,
    dependant_id INTEGER,
    tutor_id INTEGER,
    assessment_type VARCHAR(50) NOT NULL CHECK (assessment_type IN ('initial', 'progress', 'midterm', 'final', 'diagnostic', 'placement')),
    subject_area VARCHAR(50) NOT NULL CHECK (subject_area IN ('mathematics', 'science', 'english', 'french', 'history', 'geography', 'arts', 'music', 'physical_education', 'technology', 'other')),
    title VARCHAR(200) NOT NULL,
    description TEXT,
    difficulty_level VARCHAR(20) NOT NULL CHECK (difficulty_level IN ('beginner', 'elementary', 'intermediate', 'advanced', 'expert')),
    estimated_duration_minutes INTEGER NOT NULL CHECK (estimated_duration_minutes >= 5 AND estimated_duration_minutes <= 480),
    is_mandatory BOOLEAN NOT NULL DEFAULT FALSE,
    
    -- Assessment status and timing
    status VARCHAR(20) NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'scheduled', 'in_progress', 'completed', 'reviewed', 'archived')),
    scheduled_at TIMESTAMP WITH TIME ZONE,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    reviewed_by INTEGER,
    
    -- Scoring and results
    total_score NUMERIC(10,2),
    max_score NUMERIC(10,2),
    percentage_score NUMERIC(5,2) CHECK (percentage_score >= 0 AND percentage_score <= 100),
    
    -- Learning profile data (stored as JSON)
    identified_strengths JSON,
    identified_weaknesses JSON,
    learning_styles JSON,
    recommended_topics JSON,
    notes TEXT,
    
    -- Standard fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Foreign key constraints
    CONSTRAINT fk_learning_assessments_client_id FOREIGN KEY (client_id) REFERENCES client_profiles(client_id) ON DELETE CASCADE,
    CONSTRAINT fk_learning_assessments_dependant_id FOREIGN KEY (dependant_id) REFERENCES client_dependants(dependant_id) ON DELETE CASCADE,
    CONSTRAINT fk_learning_assessments_tutor_id FOREIGN KEY (tutor_id) REFERENCES tutor_profiles(tutor_id) ON DELETE SET NULL,
    CONSTRAINT fk_learning_assessments_reviewed_by FOREIGN KEY (reviewed_by) REFERENCES user_accounts(user_id) ON DELETE SET NULL
);

-- Create assessment questions table
CREATE TABLE assessment_questions (
    question_id SERIAL PRIMARY KEY,
    assessment_id INTEGER NOT NULL,
    question_order INTEGER NOT NULL CHECK (question_order >= 1),
    question_type VARCHAR(20) NOT NULL CHECK (question_type IN ('multiple_choice', 'true_false', 'short_answer', 'essay', 'practical', 'oral', 'portfolio')),
    question_text TEXT NOT NULL,
    points_possible NUMERIC(10,2) NOT NULL CHECK (points_possible >= 0 AND points_possible <= 100),
    is_required BOOLEAN NOT NULL DEFAULT TRUE,
    
    -- Question options and answers (stored as JSON)
    options JSON,
    correct_answers JSON,
    
    -- Grading and guidance
    grading_rubric TEXT,
    instructions TEXT,
    
    -- Standard fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Foreign key constraints
    CONSTRAINT fk_assessment_questions_assessment_id FOREIGN KEY (assessment_id) REFERENCES learning_needs_assessments(assessment_id) ON DELETE CASCADE,
    
    -- Unique constraint for question order within assessment
    CONSTRAINT uk_assessment_questions_order UNIQUE (assessment_id, question_order) DEFERRABLE INITIALLY DEFERRED
);

-- Create assessment responses table
CREATE TABLE assessment_responses (
    response_id SERIAL PRIMARY KEY,
    assessment_id INTEGER NOT NULL,
    question_id INTEGER NOT NULL,
    respondent_id INTEGER NOT NULL,
    response_text TEXT,
    selected_options JSON,
    time_spent_seconds INTEGER CHECK (time_spent_seconds >= 0),
    
    -- Grading
    score NUMERIC(10,2) CHECK (score >= 0),
    feedback TEXT,
    graded_at TIMESTAMP WITH TIME ZONE,
    graded_by INTEGER,
    
    -- Standard fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Foreign key constraints
    CONSTRAINT fk_assessment_responses_assessment_id FOREIGN KEY (assessment_id) REFERENCES learning_needs_assessments(assessment_id) ON DELETE CASCADE,
    CONSTRAINT fk_assessment_responses_question_id FOREIGN KEY (question_id) REFERENCES assessment_questions(question_id) ON DELETE CASCADE,
    CONSTRAINT fk_assessment_responses_respondent_id FOREIGN KEY (respondent_id) REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    CONSTRAINT fk_assessment_responses_graded_by FOREIGN KEY (graded_by) REFERENCES user_accounts(user_id) ON DELETE SET NULL,
    
    -- Unique constraint for one response per user per question
    CONSTRAINT uk_assessment_responses_user_question UNIQUE (assessment_id, question_id, respondent_id)
);

-- Create assessment templates table
CREATE TABLE assessment_templates (
    template_id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    subject_area VARCHAR(50) NOT NULL CHECK (subject_area IN ('mathematics', 'science', 'english', 'french', 'history', 'geography', 'arts', 'music', 'physical_education', 'technology', 'other')),
    difficulty_level VARCHAR(20) NOT NULL CHECK (difficulty_level IN ('beginner', 'elementary', 'intermediate', 'advanced', 'expert')),
    assessment_type VARCHAR(50) NOT NULL CHECK (assessment_type IN ('initial', 'progress', 'midterm', 'final', 'diagnostic', 'placement')),
    description TEXT,
    estimated_duration_minutes INTEGER NOT NULL CHECK (estimated_duration_minutes >= 5 AND estimated_duration_minutes <= 480),
    is_public BOOLEAN NOT NULL DEFAULT FALSE,
    created_by INTEGER NOT NULL,
    
    -- Usage statistics
    usage_count INTEGER NOT NULL DEFAULT 0,
    last_used_at TIMESTAMP WITH TIME ZONE,
    
    -- Standard fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Foreign key constraints
    CONSTRAINT fk_assessment_templates_created_by FOREIGN KEY (created_by) REFERENCES user_accounts(user_id) ON DELETE CASCADE
);

-- Create learning recommendations table
CREATE TABLE learning_recommendations (
    recommendation_id SERIAL PRIMARY KEY,
    assessment_id INTEGER NOT NULL,
    client_id INTEGER NOT NULL,
    dependant_id INTEGER,
    tutor_id INTEGER,
    subject_area VARCHAR(50) NOT NULL CHECK (subject_area IN ('mathematics', 'science', 'english', 'french', 'history', 'geography', 'arts', 'music', 'physical_education', 'technology', 'other')),
    recommendation_type VARCHAR(100) NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    priority VARCHAR(10) NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
    estimated_hours INTEGER CHECK (estimated_hours >= 1 AND estimated_hours <= 1000),
    
    -- Implementation tracking
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'implemented', 'declined')),
    implemented_at TIMESTAMP WITH TIME ZONE,
    notes TEXT,
    
    -- Standard fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Foreign key constraints
    CONSTRAINT fk_learning_recommendations_assessment_id FOREIGN KEY (assessment_id) REFERENCES learning_needs_assessments(assessment_id) ON DELETE CASCADE,
    CONSTRAINT fk_learning_recommendations_client_id FOREIGN KEY (client_id) REFERENCES client_profiles(client_id) ON DELETE CASCADE,
    CONSTRAINT fk_learning_recommendations_dependant_id FOREIGN KEY (dependant_id) REFERENCES client_dependants(dependant_id) ON DELETE CASCADE,
    CONSTRAINT fk_learning_recommendations_tutor_id FOREIGN KEY (tutor_id) REFERENCES tutor_profiles(tutor_id) ON DELETE SET NULL
);

-- Create indexes for performance
CREATE INDEX idx_learning_assessments_client_id ON learning_needs_assessments(client_id);
CREATE INDEX idx_learning_assessments_dependant_id ON learning_needs_assessments(dependant_id);
CREATE INDEX idx_learning_assessments_tutor_id ON learning_needs_assessments(tutor_id);
CREATE INDEX idx_learning_assessments_status ON learning_needs_assessments(status);
CREATE INDEX idx_learning_assessments_subject_area ON learning_needs_assessments(subject_area);
CREATE INDEX idx_learning_assessments_scheduled_at ON learning_needs_assessments(scheduled_at);
CREATE INDEX idx_learning_assessments_completed_at ON learning_needs_assessments(completed_at);
CREATE INDEX idx_learning_assessments_created_at ON learning_needs_assessments(created_at);

CREATE INDEX idx_assessment_questions_assessment_id ON assessment_questions(assessment_id);
CREATE INDEX idx_assessment_questions_order ON assessment_questions(assessment_id, question_order);

CREATE INDEX idx_assessment_responses_assessment_id ON assessment_responses(assessment_id);
CREATE INDEX idx_assessment_responses_question_id ON assessment_responses(question_id);
CREATE INDEX idx_assessment_responses_respondent_id ON assessment_responses(respondent_id);

CREATE INDEX idx_assessment_templates_subject_area ON assessment_templates(subject_area);
CREATE INDEX idx_assessment_templates_difficulty_level ON assessment_templates(difficulty_level);
CREATE INDEX idx_assessment_templates_is_public ON assessment_templates(is_public);
CREATE INDEX idx_assessment_templates_created_by ON assessment_templates(created_by);
CREATE INDEX idx_assessment_templates_usage_count ON assessment_templates(usage_count DESC);

CREATE INDEX idx_learning_recommendations_assessment_id ON learning_recommendations(assessment_id);
CREATE INDEX idx_learning_recommendations_client_id ON learning_recommendations(client_id);
CREATE INDEX idx_learning_recommendations_status ON learning_recommendations(status);
CREATE INDEX idx_learning_recommendations_priority ON learning_recommendations(priority);

-- Add comments
COMMENT ON TABLE learning_needs_assessments IS 'Comprehensive learning needs assessments for clients and dependants';
COMMENT ON COLUMN learning_needs_assessments.assessment_id IS 'Unique assessment identifier';
COMMENT ON COLUMN learning_needs_assessments.client_id IS 'Client who owns this assessment';
COMMENT ON COLUMN learning_needs_assessments.dependant_id IS 'Dependant being assessed (if applicable)';
COMMENT ON COLUMN learning_needs_assessments.tutor_id IS 'Tutor responsible for the assessment';
COMMENT ON COLUMN learning_needs_assessments.assessment_type IS 'Type of assessment (initial, progress, etc.)';
COMMENT ON COLUMN learning_needs_assessments.subject_area IS 'Subject area being assessed';
COMMENT ON COLUMN learning_needs_assessments.difficulty_level IS 'Current difficulty level of the learner';
COMMENT ON COLUMN learning_needs_assessments.percentage_score IS 'Final percentage score (0-100)';
COMMENT ON COLUMN learning_needs_assessments.identified_strengths IS 'JSON array of identified learning strengths';
COMMENT ON COLUMN learning_needs_assessments.identified_weaknesses IS 'JSON array of areas needing improvement';
COMMENT ON COLUMN learning_needs_assessments.learning_styles IS 'JSON array of preferred learning styles';
COMMENT ON COLUMN learning_needs_assessments.recommended_topics IS 'JSON array of recommended study topics';

COMMENT ON TABLE assessment_questions IS 'Individual questions within assessments';
COMMENT ON COLUMN assessment_questions.question_order IS 'Order of question within the assessment';
COMMENT ON COLUMN assessment_questions.question_type IS 'Type of question (multiple_choice, essay, etc.)';
COMMENT ON COLUMN assessment_questions.options IS 'JSON array of answer options for multiple choice questions';
COMMENT ON COLUMN assessment_questions.correct_answers IS 'JSON array of correct answers';

COMMENT ON TABLE assessment_responses IS 'Student responses to assessment questions';
COMMENT ON COLUMN assessment_responses.selected_options IS 'JSON array of selected options for multiple choice';
COMMENT ON COLUMN assessment_responses.time_spent_seconds IS 'Time spent answering this question';

COMMENT ON TABLE assessment_templates IS 'Reusable assessment templates';
COMMENT ON COLUMN assessment_templates.is_public IS 'Whether template is available to all users';
COMMENT ON COLUMN assessment_templates.usage_count IS 'Number of times template has been used';

COMMENT ON TABLE learning_recommendations IS 'Learning recommendations generated from assessments';
COMMENT ON COLUMN learning_recommendations.recommendation_type IS 'Type of recommendation (study plan, tutoring, etc.)';
COMMENT ON COLUMN learning_recommendations.priority IS 'Priority level (low, medium, high)';
COMMENT ON COLUMN learning_recommendations.estimated_hours IS 'Estimated hours needed to implement';

-- Grant permissions (skip if role doesn't exist)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'tutoraide_app') THEN
        
    END IF;
END $$;