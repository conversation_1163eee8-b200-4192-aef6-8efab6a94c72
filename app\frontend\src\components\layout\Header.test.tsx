import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { I18nextProvider } from 'react-i18next';
import Header from './Header';
import { AuthProvider } from '../../contexts/AuthContext';
import i18n from '../i18n';

const mockAuthContext = {
  user: {
    id: '1',
    email: '<EMAIL>',
    activeRole: 'manager',
    roles: ['manager', 'tutor'],
    firstName: 'John',
    lastName: 'Doe',
  },
  isAuthenticated: true,
  isLoading: false,
  login: vi.fn(),
  logout: vi.fn(),
  switchRole: vi.fn(),
};

const MockAuthProvider = ({ children }: { children: React.ReactNode }) => (
  <AuthProvider value={mockAuthContext}>
    {children}
  </AuthProvider>
);

const renderHeader = () => {
  return render(
    <BrowserRouter>
      <I18nextProvider i18n={i18n}>
        <MockAuthProvider>
          <Header />
        </MockAuthProvider>
      </I18nextProvider>
    </BrowserRouter>
  );
};

describe('Header', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders header with user information', () => {
    renderHeader();
    
    // Check user name is displayed
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    
    // Check current role is displayed
    expect(screen.getByText('Manager')).toBeInTheDocument();
  });

  it('displays notification bell with count', () => {
    renderHeader();
    
    const notificationBell = screen.getByLabelText(/notifications/i);
    expect(notificationBell).toBeInTheDocument();
    
    // Check notification count badge
    const notificationCount = screen.getByText('3');
    expect(notificationCount).toBeInTheDocument();
  });

  it('shows language switcher', () => {
    renderHeader();
    
    // Should show current language
    const languageButton = screen.getByRole('button', { name: /language/i });
    expect(languageButton).toBeInTheDocument();
  });

  it('toggles language when clicked', async () => {
    renderHeader();
    
    const languageButton = screen.getByRole('button', { name: /language/i });
    fireEvent.click(languageButton);
    
    await waitFor(() => {
      // Should show language options
      expect(screen.getByText('English')).toBeInTheDocument();
      expect(screen.getByText('Français')).toBeInTheDocument();
    });
  });

  it('opens user menu when profile clicked', async () => {
    renderHeader();
    
    const userProfile = screen.getByText('John Doe');
    fireEvent.click(userProfile);
    
    await waitFor(() => {
      expect(screen.getByText('Profile Settings')).toBeInTheDocument();
      expect(screen.getByText('Switch Role')).toBeInTheDocument();
      expect(screen.getByText('Logout')).toBeInTheDocument();
    });
  });

  it('shows role switching options for multi-role users', async () => {
    renderHeader();
    
    const userProfile = screen.getByText('John Doe');
    fireEvent.click(userProfile);
    
    await waitFor(() => {
      const switchRoleButton = screen.getByText('Switch Role');
      fireEvent.click(switchRoleButton);
    });
    
    await waitFor(() => {
      expect(screen.getByText('Manager')).toBeInTheDocument();
      expect(screen.getByText('Tutor')).toBeInTheDocument();
    });
  });

  it('calls switchRole when different role selected', async () => {
    renderHeader();
    
    const userProfile = screen.getByText('John Doe');
    fireEvent.click(userProfile);
    
    await waitFor(() => {
      const switchRoleButton = screen.getByText('Switch Role');
      fireEvent.click(switchRoleButton);
    });
    
    await waitFor(() => {
      const tutorRole = screen.getByText('Tutor');
      fireEvent.click(tutorRole);
    });
    
    expect(mockAuthContext.switchRole).toHaveBeenCalledWith('tutor');
  });

  it('calls logout when logout clicked', async () => {
    renderHeader();
    
    const userProfile = screen.getByText('John Doe');
    fireEvent.click(userProfile);
    
    await waitFor(() => {
      const logoutButton = screen.getByText('Logout');
      fireEvent.click(logoutButton);
    });
    
    expect(mockAuthContext.logout).toHaveBeenCalled();
  });

  it('shows different interface for different roles', () => {
    const tutorAuthContext = {
      ...mockAuthContext,
      user: {
        ...mockAuthContext.user,
        activeRole: 'tutor',
        roles: ['tutor'],
      },
    };

    render(
      <BrowserRouter>
        <I18nextProvider i18n={i18n}>
          <AuthProvider value={tutorAuthContext}>
            <Header />
          </AuthProvider>
        </I18nextProvider>
      </BrowserRouter>
    );

    expect(screen.getByText('Tutor')).toBeInTheDocument();
  });

  it('displays search functionality', () => {
    renderHeader();
    
    const searchInput = screen.getByPlaceholderText(/search/i);
    expect(searchInput).toBeInTheDocument();
  });

  it('handles search input changes', () => {
    renderHeader();
    
    const searchInput = screen.getByPlaceholderText(/search/i);
    fireEvent.change(searchInput, { target: { value: 'test search' } });
    
    expect(searchInput).toHaveValue('test search');
  });

  it('opens notifications dropdown', async () => {
    renderHeader();
    
    const notificationBell = screen.getByLabelText(/notifications/i);
    fireEvent.click(notificationBell);
    
    await waitFor(() => {
      expect(screen.getByText('Recent Notifications')).toBeInTheDocument();
    });
  });

  it('handles mobile responsive behavior', () => {
    // Mock mobile viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 768,
    });
    
    renderHeader();
    
    // Should show mobile menu toggle
    const mobileToggle = screen.getByLabelText(/menu/i);
    expect(mobileToggle).toBeInTheDocument();
  });

  it('shows user avatar with initials', () => {
    renderHeader();
    
    // Should show user initials
    expect(screen.getByText('JD')).toBeInTheDocument();
  });
});