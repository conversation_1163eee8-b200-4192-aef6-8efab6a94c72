"""
Consent management models for GDPR compliance and user agreement tracking.
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from enum import Enum
from pydantic import BaseModel, Field, field_validator, ConfigDict
from uuid import UUID

from app.models.base import BaseEntity


class ConsentLevel(str, Enum):
    """Consent levels for different types of agreements."""
    LEVEL_1_MANDATORY = "level_1_mandatory"  # Required for account creation
    LEVEL_2_OPTIONAL = "level_2_optional"   # Optional, user can opt-in/out


class ConsentStatus(str, Enum):
    """Status of user consent."""
    GRANTED = "granted"
    WITHDRAWN = "withdrawn"
    EXPIRED = "expired"


class ConsentCategory(str, Enum):
    """Categories of consent for organization."""
    LEGAL = "legal"           # Terms of Service, Privacy Policy
    MARKETING = "marketing"   # Email marketing, promotional content
    ANALYTICS = "analytics"   # Usage analytics, performance tracking
    FUNCTIONAL = "functional" # Non-essential features


# Consent Document Models
class ConsentDocumentBase(BaseModel):
    """Base consent document model."""
    consent_type: str = Field(..., description="Type of consent (tos, privacy, marketing, etc.)")
    title: str = Field(..., max_length=200, description="Human-readable title")
    content: str = Field(..., description="Full consent text/content")
    version: str = Field(..., max_length=20, description="Document version (e.g., 1.0, 2.1)")
    level: ConsentLevel = Field(..., description="Consent level")
    category: ConsentCategory = Field(..., description="Consent category")
    language: str = Field(default="en", max_length=5, description="Language code")
    is_active: bool = Field(default=True, description="Whether this version is active")
    effective_date: datetime = Field(..., description="When this version becomes effective")
    
    model_config = ConfigDict(from_attributes=True)

    @field_validator('consent_type')
    @classmethod
    def validate_consent_type(cls, v: str) -> str:
        """Validate consent type format."""
        if not v or len(v.strip()) == 0:
            raise ValueError("Consent type cannot be empty")
        return v.lower().strip()

    @field_validator('version')
    @classmethod
    def validate_version(cls, v: str) -> str:
        """Validate version format."""
        import re
        if not re.match(r'^\d+\.\d+(\.\d+)?$', v):
            raise ValueError("Version must be in format X.Y or X.Y.Z")
        return v

    @field_validator('language')
    @classmethod
    def validate_language(cls, v: str) -> str:
        """Validate language code."""
        if v not in ['en', 'fr']:
            raise ValueError("Language must be 'en' or 'fr'")
        return v


class ConsentDocument(ConsentDocumentBase, BaseEntity):
    """Complete consent document with metadata."""
    document_id: int = Field(..., description="Document ID")


class ConsentDocumentCreate(ConsentDocumentBase):
    """Model for creating consent documents."""
    pass


class ConsentDocumentUpdate(BaseModel):
    """Model for updating consent documents."""
    title: Optional[str] = Field(None, max_length=200)
    content: Optional[str] = None
    is_active: Optional[bool] = None
    effective_date: Optional[datetime] = None


# User Consent Models
class UserConsentBase(BaseModel):
    """Base user consent model."""
    user_id: int = Field(..., description="User ID")
    document_id: int = Field(..., description="Consent document ID")
    status: ConsentStatus = Field(..., description="Consent status")
    ip_address: Optional[str] = Field(None, description="IP address when consent was given/withdrawn")
    user_agent: Optional[str] = Field(None, description="User agent when consent was given/withdrawn")
    
    model_config = ConfigDict(from_attributes=True)


class UserConsent(UserConsentBase, BaseEntity):
    """Complete user consent with metadata."""
    consent_id: int = Field(..., description="Consent record ID")
    granted_at: Optional[datetime] = Field(None, description="When consent was granted")
    withdrawn_at: Optional[datetime] = Field(None, description="When consent was withdrawn")
    expires_at: Optional[datetime] = Field(None, description="When consent expires (if applicable)")


class UserConsentCreate(UserConsentBase):
    """Model for creating user consent records."""
    pass


class UserConsentUpdate(BaseModel):
    """Model for updating user consent."""
    status: ConsentStatus = Field(..., description="New consent status")
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None


# API Request/Response Models
class ConsentAcceptanceRequest(BaseModel):
    """Request to accept consent."""
    consent_type: str = Field(..., description="Type of consent to accept")
    document_version: Optional[str] = Field(None, description="Specific document version (latest if not provided)")
    language: str = Field(default="en", description="Language preference")
    
    @field_validator('language')
    @classmethod
    def validate_language(cls, v: str) -> str:
        if v not in ['en', 'fr']:
            raise ValueError("Language must be 'en' or 'fr'")
        return v


class ConsentWithdrawalRequest(BaseModel):
    """Request to withdraw consent."""
    consent_type: str = Field(..., description="Type of consent to withdraw")
    reason: Optional[str] = Field(None, max_length=500, description="Optional reason for withdrawal")


class ConsentStatusResponse(BaseModel):
    """Response with consent status information."""
    consent_type: str = Field(..., description="Type of consent")
    title: str = Field(..., description="Human-readable title")
    level: ConsentLevel = Field(..., description="Consent level")
    category: ConsentCategory = Field(..., description="Consent category")
    status: ConsentStatus = Field(..., description="Current status")
    granted_at: Optional[datetime] = Field(None, description="When consent was granted")
    withdrawn_at: Optional[datetime] = Field(None, description="When consent was withdrawn")
    expires_at: Optional[datetime] = Field(None, description="When consent expires")
    document_version: str = Field(..., description="Document version")
    can_withdraw: bool = Field(..., description="Whether consent can be withdrawn")


class ConsentHistoryEntry(BaseModel):
    """Single entry in consent history."""
    action: str = Field(..., description="Action taken (granted, withdrawn)")
    timestamp: datetime = Field(..., description="When action occurred")
    document_version: str = Field(..., description="Document version at time of action")
    ip_address: Optional[str] = Field(None, description="IP address")
    reason: Optional[str] = Field(None, description="Reason for action (if provided)")


class ConsentHistoryResponse(BaseModel):
    """Response with consent history."""
    consent_type: str = Field(..., description="Type of consent")
    current_status: ConsentStatus = Field(..., description="Current status")
    history: List[ConsentHistoryEntry] = Field(..., description="History of consent actions")


class ConsentSummaryResponse(BaseModel):
    """Summary of all user consents."""
    user_id: int = Field(..., description="User ID")
    mandatory_consents_complete: bool = Field(..., description="Whether all mandatory consents are granted")
    consents: List[ConsentStatusResponse] = Field(..., description="List of all consent statuses")
    missing_mandatory: List[str] = Field(..., description="List of missing mandatory consent types")


class ConsentValidationResult(BaseModel):
    """Result of consent validation."""
    is_valid: bool = Field(..., description="Whether consents are valid for operation")
    missing_mandatory: List[str] = Field(default_factory=list, description="Missing mandatory consents")
    expired_consents: List[str] = Field(default_factory=list, description="Expired consents")
    message: str = Field(..., description="Validation message")


# Bulk Operations
class BulkConsentRequest(BaseModel):
    """Request for bulk consent operations."""
    consents: List[ConsentAcceptanceRequest] = Field(..., description="List of consents to accept")


class BulkConsentResponse(BaseModel):
    """Response for bulk consent operations."""
    successful: List[str] = Field(..., description="Successfully processed consent types")
    failed: List[Dict[str, str]] = Field(..., description="Failed consent types with error messages")
    total_processed: int = Field(..., description="Total number of consents processed")


# Search and Filter Models
class ConsentSearchFilters(BaseModel):
    """Filters for searching consents."""
    consent_type: Optional[str] = None
    level: Optional[ConsentLevel] = None
    category: Optional[ConsentCategory] = None
    status: Optional[ConsentStatus] = None
    language: Optional[str] = None
    user_id: Optional[int] = None
    from_date: Optional[datetime] = None
    to_date: Optional[datetime] = None
    page: int = Field(default=1, ge=1)
    size: int = Field(default=20, ge=1, le=100)
    sort_by: str = Field(default="created_at")
    sort_order: str = Field(default="desc")

    @field_validator('sort_by')
    @classmethod
    def validate_sort_by(cls, v: str) -> str:
        allowed_fields = [
            'created_at', 'updated_at', 'consent_type', 'status', 
            'granted_at', 'withdrawn_at', 'level', 'category'
        ]
        if v not in allowed_fields:
            raise ValueError(f"Sort field must be one of: {', '.join(allowed_fields)}")
        return v

    @field_validator('sort_order')
    @classmethod
    def validate_sort_order(cls, v: str) -> str:
        if v.lower() not in ['asc', 'desc']:
            raise ValueError("Sort order must be 'asc' or 'desc'")
        return v.lower()