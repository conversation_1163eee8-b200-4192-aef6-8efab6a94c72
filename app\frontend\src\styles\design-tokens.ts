/**
 * Design System Tokens
 * Based on Apple-inspired design principles from CLAUDE.md
 */

export const designTokens = {
  // Color Palette
  colors: {
    // Primary Colors
    primary: {
      DEFAULT: '#1f2937', // Black/Dark Gray
      light: '#374151',
      dark: '#111827',
    },
    secondary: {
      DEFAULT: '#f9fafb', // Light Gray
      light: '#ffffff',
      dark: '#f3f4f6',
    },
    accent: {
      red: {
        DEFAULT: '#f87171', // Pastel Red
        light: '#fca5a5',
        dark: '#ef4444',
      },
      highlight: '#dc2626', // Red
      gold: '#fbbf24', // Gold accent
    },
    
    // Semantic Colors
    semantic: {
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#3b82f6',
    },
    
    // Text Colors
    text: {
      primary: '#1f2937',
      secondary: '#6b7280',
      muted: '#9ca3af',
      inverse: '#ffffff',
    },
    
    // Background Colors
    background: {
      primary: '#ffffff',
      secondary: '#f9fafb',
      tertiary: '#f3f4f6',
    },
    
    // Border Colors
    border: {
      primary: '#e5e7eb',
      secondary: '#d1d5db',
    },
  },
  
  // Border Radius
  radius: {
    none: '0px',
    sm: '8px',    // Small elements
    md: '12px',   // Medium elements (default)
    lg: '16px',   // Large elements
    xl: '24px',   // Extra large elements
    full: '9999px', // Pills/oval shapes
  },
  
  // Shadows (Apple-inspired)
  shadows: {
    none: 'none',
    subtle: '0 1px 3px rgba(0, 0, 0, 0.05)',
    soft: '0 4px 12px rgba(0, 0, 0, 0.08)',
    elevated: '0 8px 25px rgba(0, 0, 0, 0.12)',
    focus: '0 0 0 3px rgba(248, 113, 113, 0.1)',
    error: '0 0 0 3px rgba(239, 68, 68, 0.1)',
  },
  
  // Spacing
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px',
    '2xl': '48px',
  },
  
  // Typography
  typography: {
    fontSize: {
      xs: '0.75rem',    // 12px
      sm: '0.875rem',   // 14px
      base: '1rem',     // 16px
      lg: '1.125rem',   // 18px
      xl: '1.25rem',    // 20px
      '2xl': '1.5rem',  // 24px
      '3xl': '1.875rem', // 30px
      '4xl': '2.25rem', // 36px
    },
    fontWeight: {
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
    },
    lineHeight: {
      tight: '1.25',
      normal: '1.5',
      relaxed: '1.75',
    },
  },
  
  // Transitions
  transitions: {
    duration: {
      fast: '150ms',
      normal: '200ms',
      slow: '300ms',
    },
    easing: {
      default: 'ease',
      in: 'ease-in',
      out: 'ease-out',
      inOut: 'ease-in-out',
    },
  },
  
  // Z-index layers
  zIndex: {
    dropdown: 10,
    sticky: 20,
    fixed: 30,
    modalBackdrop: 40,
    modal: 50,
    popover: 60,
    tooltip: 70,
  },
  
  // Breakpoints
  breakpoints: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
  },
};

// Component-specific tokens
export const componentTokens = {
  button: {
    sizes: {
      sm: {
        padding: '8px 16px',
        fontSize: designTokens.typography.fontSize.sm,
        iconSize: '16px',
        gap: '6px',
      },
      md: {
        padding: '12px 24px',
        fontSize: designTokens.typography.fontSize.base,
        iconSize: '20px',
        gap: '8px',
      },
      lg: {
        padding: '16px 32px',
        fontSize: designTokens.typography.fontSize.lg,
        iconSize: '24px',
        gap: '10px',
      },
    },
    variants: {
      primary: {
        background: designTokens.colors.accent.highlight,
        color: designTokens.colors.text.inverse,
        hoverBackground: designTokens.colors.accent.red.light,
        shadow: designTokens.shadows.soft,
        hoverShadow: designTokens.shadows.elevated,
      },
      secondary: {
        background: designTokens.colors.background.tertiary,
        color: designTokens.colors.text.primary,
        hoverBackground: designTokens.colors.background.secondary,
      },
      ghost: {
        background: 'transparent',
        color: designTokens.colors.text.secondary,
        hoverColor: designTokens.colors.accent.red.DEFAULT,
        hoverBackground: 'rgba(248, 113, 113, 0.08)',
      },
      error: {
        background: designTokens.colors.semantic.error,
        color: designTokens.colors.text.inverse,
        hoverBackground: '#dc2626',
        shadow: designTokens.shadows.soft,
        hoverShadow: designTokens.shadows.elevated,
      },
    },
  },
  
  input: {
    padding: '12px 16px',
    borderRadius: designTokens.radius.md,
    borderColor: designTokens.colors.border.primary,
    hoverBorderColor: designTokens.colors.border.secondary,
    focusBorderColor: designTokens.colors.accent.red.DEFAULT,
    background: designTokens.colors.background.secondary,
    focusBackground: designTokens.colors.background.primary,
    focusShadow: designTokens.shadows.focus,
    errorBorderColor: designTokens.colors.semantic.error,
    errorFocusShadow: designTokens.shadows.error,
  },
  
  card: {
    borderRadius: designTokens.radius.lg,
    shadow: designTokens.shadows.subtle,
    hoverShadow: designTokens.shadows.soft,
    borderColor: designTokens.colors.border.primary,
    padding: {
      none: '0',
      sm: designTokens.spacing.md,
      md: designTokens.spacing.lg,
      lg: designTokens.spacing.xl,
    },
  },
  
  modal: {
    borderRadius: designTokens.radius.xl,
    shadow: designTokens.shadows.elevated,
    backdropColor: 'rgba(0, 0, 0, 0.5)',
    maxWidth: {
      sm: '28rem',
      md: '32rem',
      lg: '48rem',
      xl: '64rem',
      full: '90rem',
    },
  },
  
  badge: {
    borderRadius: designTokens.radius.full,
    padding: {
      sm: '2px 8px',
      md: '4px 10px',
      lg: '6px 12px',
    },
    fontSize: {
      sm: designTokens.typography.fontSize.xs,
      md: designTokens.typography.fontSize.sm,
      lg: designTokens.typography.fontSize.base,
    },
  },
  
  sidebar: {
    width: '288px',
    background: 'rgba(249, 250, 251, 0.95)',
    backdropBlur: '12px',
    borderColor: designTokens.colors.border.primary,
    itemBorderRadius: designTokens.radius.md,
    activeItemBackground: designTokens.colors.accent.highlight,
    activeItemColor: designTokens.colors.text.inverse,
    hoverItemBackground: 'rgba(248, 113, 113, 0.08)',
    hoverItemColor: designTokens.colors.accent.highlight,
  },
};

// Utility function to generate CSS variables from tokens
export const generateCSSVariables = () => {
  const cssVars: Record<string, string> = {};
  
  // Colors
  Object.entries(designTokens.colors).forEach(([category, values]) => {
    if (typeof values === 'object') {
      Object.entries(values).forEach(([key, value]) => {
        if (typeof value === 'string') {
          const varName = key === 'DEFAULT' 
            ? `--color-${category}` 
            : `--color-${category}-${key}`;
          cssVars[varName] = value;
        } else if (typeof value === 'object') {
          Object.entries(value).forEach(([subKey, subValue]) => {
            const varName = subKey === 'DEFAULT'
              ? `--color-${category}-${key}`
              : `--color-${category}-${key}-${subKey}`;
            cssVars[varName] = subValue as string;
          });
        }
      });
    }
  });
  
  // Shadows
  Object.entries(designTokens.shadows).forEach(([key, value]) => {
    cssVars[`--shadow-${key}`] = value;
  });
  
  // Radius
  Object.entries(designTokens.radius).forEach(([key, value]) => {
    cssVars[`--radius-${key}`] = value;
  });
  
  // Spacing
  Object.entries(designTokens.spacing).forEach(([key, value]) => {
    cssVars[`--space-${key}`] = value;
  });
  
  return cssVars;
};

// Type exports for TypeScript
export type ColorVariant = 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
export type ButtonVariant = 'primary' | 'secondary' | 'ghost' | 'error';
export type Size = 'sm' | 'md' | 'lg';
export type Radius = keyof typeof designTokens.radius;
export type Shadow = keyof typeof designTokens.shadows;