import api from './api';
import { Coordinates } from '../../../services/geocodingService';

// Distance Calculation Types and Interfaces
export interface DistanceRequest {
  origin: {
    postal_code?: string;
    coordinates?: Coordinates;
    address?: string;
  };
  destination: {
    postal_code?: string;
    coordinates?: Coordinates;
    address?: string;
  };
  unit?: 'km' | 'miles';
  mode?: 'straight_line' | 'driving' | 'transit' | 'walking' | 'cycling';
}

export interface DistanceResponse {
  distance: number;
  unit: 'km' | 'miles';
  duration_minutes?: number;
  mode: string;
  route_info?: {
    steps?: number;
    major_roads?: string[];
    traffic_level?: 'light' | 'moderate' | 'heavy';
    transit_options?: Array<{
      type: string;
      line: string;
      duration_minutes: number;
    }>;
  };
}

export interface BatchDistanceRequest {
  origin: {
    postal_code?: string;
    coordinates?: Coordinates;
  };
  destinations: Array<{
    id: string;
    postal_code?: string;
    coordinates?: Coordinates;
  }>;
  unit?: 'km' | 'miles';
  mode?: 'straight_line' | 'driving';
}

export interface BatchDistanceResponse {
  origin: string;
  distances: Array<{
    destination_id: string;
    distance: number;
    duration_minutes?: number;
    error?: string;
  }>;
  unit: 'km' | 'miles';
  mode: string;
}

export interface DistanceMatrix {
  locations: Array<{
    id: string;
    postal_code?: string;
    coordinates?: Coordinates;
  }>;
  matrix: number[][];
  unit: 'km' | 'miles';
}

export interface TravelTimeEstimate {
  origin_postal_code: string;
  destination_postal_code: string;
  estimates: {
    driving: {
      duration_minutes: number;
      distance_km: number;
      rush_hour_factor: number;
    };
    transit: {
      duration_minutes: number;
      transfers: number;
      walking_distance_km: number;
    };
    walking: {
      duration_minutes: number;
      distance_km: number;
    };
    cycling: {
      duration_minutes: number;
      distance_km: number;
      elevation_gain_m?: number;
    };
  };
  recommended_mode: string;
  factors: string[];
}

export interface ServiceRadius {
  center: {
    postal_code: string;
    coordinates: Coordinates;
  };
  radius_km: number;
  coverage_area_km2: number;
  postal_codes_in_radius: string[];
  population_estimate?: number;
  tutors_in_area?: number;
}

export interface RouteOptimization {
  waypoints: Array<{
    id: string;
    postal_code: string;
    time_required_minutes: number;
  }>;
  start_location: string;
  end_location?: string;
  time_windows?: Array<{
    waypoint_id: string;
    start_time: string;
    end_time: string;
  }>;
}

export interface OptimizedRoute {
  route: Array<{
    waypoint_id: string;
    postal_code: string;
    arrival_time: string;
    departure_time: string;
    travel_time_to_next: number;
  }>;
  total_distance_km: number;
  total_duration_minutes: number;
  efficiency_score: number;
}

class DistanceCalculationService {
  /**
   * Calculate distance between two points
   */
  async calculateDistance(request: DistanceRequest): Promise<DistanceResponse> {
    try {
      const response = await api.post<DistanceResponse>('/distance/calculate', request);
      return response.data;
    } catch (error: any) {
      console.error('Error calculating distance:', error);
      throw new Error(error.response?.data?.detail || 'Failed to calculate distance');
    }
  }

  /**
   * Calculate distances from one origin to multiple destinations
   */
  async calculateBatchDistances(request: BatchDistanceRequest): Promise<BatchDistanceResponse> {
    try {
      const response = await api.post<BatchDistanceResponse>('/distance/batch', request);
      return response.data;
    } catch (error: any) {
      console.error('Error calculating batch distances:', error);
      throw new Error(error.response?.data?.detail || 'Failed to calculate distances');
    }
  }

  /**
   * Generate distance matrix between multiple locations
   */
  async generateDistanceMatrix(locations: Array<{
    id: string;
    postal_code?: string;
    coordinates?: Coordinates;
  }>): Promise<DistanceMatrix> {
    try {
      const response = await api.post<DistanceMatrix>('/distance/matrix', { locations });
      return response.data;
    } catch (error: any) {
      console.error('Error generating distance matrix:', error);
      throw new Error(error.response?.data?.detail || 'Failed to generate matrix');
    }
  }

  /**
   * Get travel time estimates for different modes
   */
  async getTravelTimeEstimates(
    origin: string, 
    destination: string
  ): Promise<TravelTimeEstimate> {
    try {
      const response = await api.get<TravelTimeEstimate>('/distance/travel-time', {
        params: { origin, destination }
      });
      return response.data;
    } catch (error: any) {
      console.error('Error getting travel time estimates:', error);
      throw new Error(error.response?.data?.detail || 'Failed to get estimates');
    }
  }

  /**
   * Get service radius information
   */
  async getServiceRadius(
    postalCode: string, 
    radiusKm: number
  ): Promise<ServiceRadius> {
    try {
      const response = await api.get<ServiceRadius>('/distance/service-radius', {
        params: { postal_code: postalCode, radius_km: radiusKm }
      });
      return response.data;
    } catch (error: any) {
      console.error('Error getting service radius:', error);
      throw new Error(error.response?.data?.detail || 'Failed to get service radius');
    }
  }

  /**
   * Optimize route through multiple waypoints
   */
  async optimizeRoute(request: RouteOptimization): Promise<OptimizedRoute> {
    try {
      const response = await api.post<OptimizedRoute>('/distance/optimize-route', request);
      return response.data;
    } catch (error: any) {
      console.error('Error optimizing route:', error);
      throw new Error(error.response?.data?.detail || 'Failed to optimize route');
    }
  }

  /**
   * Calculate straight-line distance (Haversine formula)
   */
  calculateStraightLineDistance(coord1: Coordinates, coord2: Coordinates): number {
    const R = 6371; // Earth's radius in km
    const dLat = this.toRad(coord2.latitude - coord1.latitude);
    const dLon = this.toRad(coord2.longitude - coord1.longitude);
    
    const a = 
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRad(coord1.latitude)) * 
      Math.cos(this.toRad(coord2.latitude)) * 
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;
    
    return Math.round(distance * 10) / 10;
  }

  /**
   * Estimate driving distance from straight-line distance
   */
  estimateDrivingDistance(straightLineKm: number): number {
    // Driving distance is typically 1.2-1.5x straight-line distance
    // Use 1.3 as average multiplier
    return Math.round(straightLineKm * 1.3 * 10) / 10;
  }

  /**
   * Estimate travel cost
   */
  estimateTravelCost(distanceKm: number, options?: {
    mode?: 'driving' | 'transit';
    gasPrice?: number;
    fuelEfficiency?: number;
    transitFare?: number;
  }): number {
    const mode = options?.mode || 'driving';

    if (mode === 'driving') {
      const gasPrice = options?.gasPrice || 1.50; // $/liter
      const fuelEfficiency = options?.fuelEfficiency || 8; // liters/100km
      const fuelCost = (distanceKm / 100) * fuelEfficiency * gasPrice;
      const wearAndTear = distanceKm * 0.10; // $0.10/km for wear and tear
      return Math.round((fuelCost + wearAndTear) * 100) / 100;
    } else {
      // Transit
      const baseFare = options?.transitFare || 3.50;
      // Assume additional zones for longer distances
      const zones = Math.ceil(distanceKm / 15);
      return baseFare * zones;
    }
  }

  /**
   * Format distance for display
   */
  formatDistance(distance: number, unit: 'km' | 'miles' = 'km'): string {
    if (unit === 'km') {
      if (distance < 1) {
        return `${Math.round(distance * 1000)}m`;
      } else if (distance < 10) {
        return `${distance.toFixed(1)}km`;
      } else {
        return `${Math.round(distance)}km`;
      }
    } else {
      if (distance < 0.5) {
        return `${Math.round(distance * 5280)}ft`;
      } else if (distance < 10) {
        return `${distance.toFixed(1)}mi`;
      } else {
        return `${Math.round(distance)}mi`;
      }
    }
  }

  /**
   * Format duration for display
   */
  formatDuration(minutes: number): string {
    if (minutes < 60) {
      return `${Math.round(minutes)} min`;
    } else {
      const hours = Math.floor(minutes / 60);
      const mins = Math.round(minutes % 60);
      if (mins === 0) {
        return `${hours}h`;
      } else {
        return `${hours}h ${mins}min`;
      }
    }
  }

  /**
   * Convert between units
   */
  convertDistance(distance: number, from: 'km' | 'miles', to: 'km' | 'miles'): number {
    if (from === to) return distance;
    
    if (from === 'km' && to === 'miles') {
      return distance * 0.621371;
    } else {
      return distance * 1.60934;
    }
  }

  /**
   * Check if location is within service area
   */
  isWithinServiceArea(
    location: Coordinates, 
    center: Coordinates, 
    radiusKm: number
  ): boolean {
    const distance = this.calculateStraightLineDistance(location, center);
    return distance <= radiusKm;
  }

  /**
   * Get bearing between two points
   */
  calculateBearing(from: Coordinates, to: Coordinates): number {
    const dLon = this.toRad(to.longitude - from.longitude);
    const lat1 = this.toRad(from.latitude);
    const lat2 = this.toRad(to.latitude);

    const y = Math.sin(dLon) * Math.cos(lat2);
    const x = Math.cos(lat1) * Math.sin(lat2) -
              Math.sin(lat1) * Math.cos(lat2) * Math.cos(dLon);

    const bearing = Math.atan2(y, x);
    return (this.toDeg(bearing) + 360) % 360;
  }

  /**
   * Get compass direction from bearing
   */
  getCompassDirection(bearing: number): string {
    const directions = ['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW'];
    const index = Math.round(bearing / 45) % 8;
    return directions[index];
  }

  /**
   * Convert degrees to radians
   */
  private toRad(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * Convert radians to degrees
   */
  private toDeg(radians: number): number {
    return radians * (180 / Math.PI);
  }

  /**
   * Sort locations by distance from origin
   */
  sortByDistance<T extends { coordinates: Coordinates }>(
    locations: T[], 
    origin: Coordinates
  ): Array<T & { distance_km: number }> {
    return locations
      .map(location => ({
        ...location,
        distance_km: this.calculateStraightLineDistance(origin, location.coordinates)
      }))
      .sort((a, b) => a.distance_km - b.distance_km);
  }
}

export const distanceCalculationService = new DistanceCalculationService();