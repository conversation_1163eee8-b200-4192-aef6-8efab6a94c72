# Email Configuration Debug Guide for Railway

## Summary of Changes Made

### 1. Fixed React Error #306
- Added default export to `/app/frontend/src/pages/auth/ForgotPassword.tsx`
- This resolved the lazy loading issue with React Router

### 2. Fixed Toast Notification Error
- Changed `toast.info()` to `toast()` in `/app/frontend/src/pages/Login.tsx`
- react-hot-toast doesn't have an `.info()` method

### 3. Fixed 401 Unauthorized on Password Reset
- Password reset endpoints already added to PUBLIC_ENDPOINTS in `/app/core/authorization.py`

### 4. Fixed Request Hanging Issue
- Implemented BackgroundTasks in `/app/api/v1/auth_updated.py` for async email sending
- Fixed middleware consuming request body without restoring it in `/app/core/security_middleware.py`

### 5. Email Configuration Issues
- Updated `/app/config/settings.py` to skip .env file loading in production
- Added explicit Field declarations for SMTP configuration
- Implemented lazy initialization for EmailService in `/app/services/email_service.py`
- Added comprehensive logging throughout the email flow

### 6. Added Debug Endpoints
- `/api/v1/auth/email/config-check` - Check email configuration status
- `/api/v1/auth/email/test-send` - Test sending emails directly

## Current Issue: Emails Not Being Sent

Despite the 200 OK response, emails are not being sent because:
1. The application is using localhost:1025 instead of Railway environment variables
2. The lazy initialization logs aren't appearing in Railway logs

## Debugging Steps for Railway

### 1. Check Environment Variables
In Railway dashboard, verify these environment variables are set:
```
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_TLS=true
ENVIRONMENT=production
```

### 2. Use the Debug Script
Deploy and run this script in Railway console:
```bash
python debug_env.py
```

This will show:
- Raw environment variables
- How settings are loaded
- Email service initialization status

### 3. Check Application Startup Logs
Look for the "APPLICATION STARTUP" section in Railway logs which shows:
- Environment detection
- SMTP configuration from environment
- Settings loaded values

### 4. Test Email Configuration
After deployment, test the configuration endpoint:
```bash
curl https://your-app.railway.app/api/v1/auth/email/config-check
```

### 5. Test Direct Email Sending
For managers only, test email sending directly:
```bash
curl -X POST https://your-app.railway.app/api/v1/auth/email/test-send \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"to_email": "<EMAIL>", "subject": "Test Email"}'
```

### 6. Check Background Task Logs
When testing password reset, look for these log entries:
- "=== BACKGROUND TASK STARTED ==="
- "Raw environment variables in background task"
- "Settings reloaded in background task"
- "Email service obtained"
- "=== BACKGROUND TASK COMPLETED ==="

## Potential Solutions

### Option 1: Force Production Environment
Add to Railway environment variables:
```
RAILWAY_ENVIRONMENT=production
```

### Option 2: Direct Environment Variable Usage
If pydantic_settings is not loading Railway env vars, modify `/app/services/email_service.py` to read directly:
```python
smtp_host = os.environ.get('SMTP_HOST') or settings.SMTP_HOST
smtp_port = int(os.environ.get('SMTP_PORT', settings.SMTP_PORT))
# etc.
```

### Option 3: Use Railway Config File
Create a `railway.toml` in your repo:
```toml
[build]
builder = "NIXPACKS"

[deploy]
startCommand = "uvicorn app.main:app --host 0.0.0.0 --port $PORT"

[variables]
ENVIRONMENT = "production"
```

## Gmail SMTP Setup

For Gmail SMTP to work:
1. Enable 2-factor authentication on your Google account
2. Generate an app-specific password
3. Use these settings:
   - SMTP_HOST: smtp.gmail.com
   - SMTP_PORT: 587
   - SMTP_USERNAME: <EMAIL>
   - SMTP_PASSWORD: your-app-specific-password
   - SMTP_TLS: true

## Testing Locally

To test with production-like settings locally:
```bash
export ENVIRONMENT=production
export SMTP_HOST=smtp.gmail.com
export SMTP_PORT=587
export SMTP_USERNAME=<EMAIL>
export SMTP_PASSWORD=your-app-password
export SMTP_TLS=true

uvicorn app.main:app --reload
```

## Next Steps

1. Deploy the current changes to Railway
2. Check the startup logs for environment variable detection
3. Use the config-check endpoint to verify settings
4. Test with the direct email send endpoint
5. Monitor background task logs for password reset

The key issue appears to be that Railway environment variables aren't being loaded by pydantic_settings. The debug logging added should help identify exactly where the configuration is failing.