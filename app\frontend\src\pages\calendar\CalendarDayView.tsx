import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { format, addDays, subDays, startOfDay, endOfDay, eachHourOfInterval, isSameDay } from 'date-fns';
import { ChevronLeft, ChevronRight, Clock, MapPin, User, Users, Phone, Video, Home, Wifi, WifiOff, Filter, X, Calendar, Brain } from 'lucide-react';
import { useCalendarWebSocket } from '../../hooks/useCalendarWebSocket';
import { toast } from 'react-hot-toast';
import AISchedulingSuggestions from '../../components/calendar/AISchedulingSuggestions';
import AppointmentDetailsModal from '../../components/modals/AppointmentDetailsModal';
import { useAuth } from '../../contexts/AuthContext';
import { UserRoleType } from '../../types/auth';
import { hasPermission } from '../../utils/permissions';
import { appointmentService } from '../../services/appointmentService';
import { tutorService } from '../../services/tutorService';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import { useNavigate } from 'react-router-dom';

interface Tutor {
  id: number;
  name: string;
  avatar: string;
  specialties: string[];
  isActive: boolean;
  currentLoad: number;
  maxLoad: number;
}

interface Appointment {
  id: number;
  tutorId: number;
  clientId?: number;
  clientName: string;
  clientEmail?: string;
  dependantName?: string;
  subject: string;
  startTime: Date;
  endTime: Date;
  duration: number;
  status: 'scheduled' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled';
  type: 'individual' | 'group' | 'tecfee';
  location: {
    type: 'online' | 'in_person' | 'library';
    details?: string;
  };
  participants?: number;
  maxParticipants?: number;
  notes?: string;
  confirmed: boolean;
}

interface CalendarDayViewProps {
  selectedDate: Date;
}

const CalendarDayView: React.FC<CalendarDayViewProps> = ({ selectedDate: initialDate }) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [selectedDate, setSelectedDate] = useState(initialDate || new Date());
  const [tutors, setTutors] = useState<Tutor[]>([]);
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [loading, setLoading] = useState(true);
  const [draggedAppointment, setDraggedAppointment] = useState<Appointment | null>(null);
  
  const isClient = user?.activeRole === UserRoleType.CLIENT;
  const isTutor = user?.activeRole === UserRoleType.TUTOR;
  const canViewAllAppointments = hasPermission(user?.activeRole, 'viewAllAppointments');
  const canBookAppointment = hasPermission(user?.activeRole, 'bookAppointment');
  const canConfirmAppointment = hasPermission(user?.activeRole, 'confirmAppointment');
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  
  // Filtering state
  const [showFilters, setShowFilters] = useState(false);
  const [filteredTutorIds, setFilteredTutorIds] = useState<number[]>([]);
  const [dateRangeFilter, setDateRangeFilter] = useState<{
    startDate: string;
    endDate: string;
  }>({
    startDate: format(selectedDate, 'yyyy-MM-dd'),
    endDate: format(selectedDate, 'yyyy-MM-dd')
  });
  const [statusFilter, setStatusFilter] = useState<string[]>([]);
  const [subjectFilter, setSubjectFilter] = useState<string>('');
  
  // AI Scheduling state
  const [showAISuggestions, setShowAISuggestions] = useState(false);
  const [aiSuggestionsParams, setAISuggestionsParams] = useState<{
    clientId: number;
    subjectArea: string;
    duration: number;
    sessionType: string;
  } | null>(null);
  
  // Appointment details modal state
  const [showAppointmentDetails, setShowAppointmentDetails] = useState(false);
  const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(null);

  // WebSocket integration for real-time updates
  const websocket = useCalendarWebSocket({
    room: 'calendar_global',
    onAppointmentCreated: (appointment) => {
      // Add new appointment if it's for the current date
      if (isSameDay(new Date(appointment.scheduled_date), selectedDate)) {
        setAppointments(prev => [...prev, transformAppointmentFromAPI(appointment)]);
      }
    },
    onAppointmentUpdated: (appointment, changes) => {
      // Update appointment if it exists in current view
      setAppointments(prev => prev.map(apt => 
        apt.id === appointment.appointment_id 
          ? { ...apt, ...transformAppointmentFromAPI(appointment) }
          : apt
      ));
    },
    onAppointmentCancelled: (appointment) => {
      // Update appointment status or remove from view
      setAppointments(prev => prev.map(apt => 
        apt.id === appointment.appointment_id 
          ? { ...apt, status: 'cancelled' as const }
          : apt
      ));
    },
    onAppointmentConfirmed: (appointment) => {
      // Update appointment confirmation status
      setAppointments(prev => prev.map(apt => 
        apt.id === appointment.appointment_id 
          ? { ...apt, confirmed: true, status: 'confirmed' as const }
          : apt
      ));
    },
    onConflictDetected: (conflict) => {
      // Show conflict warning
      toast.error(
        `Scheduling conflict detected: ${conflict.description}`,
        { duration: 6000, position: 'top-right' }
      );
    },
    enableNotifications: true,
    enableToasts: true,
  });

  // Helper function to transform API appointment data to component format
  const transformAppointmentFromAPI = (apiAppointment: any): Appointment => {
    return {
      id: apiAppointment.appointment_id,
      tutorId: apiAppointment.tutor_id,
      clientId: apiAppointment.client_id,
      clientName: apiAppointment.client_name || 'Unknown Client',
      clientEmail: apiAppointment.client_email,
      dependantName: apiAppointment.dependant_name,
      subject: apiAppointment.subject_area,
      startTime: new Date(`${apiAppointment.scheduled_date}T${apiAppointment.start_time}`),
      endTime: new Date(`${apiAppointment.scheduled_date}T${apiAppointment.end_time}`),
      duration: apiAppointment.duration || 60,
      status: apiAppointment.status || 'scheduled',
      type: apiAppointment.session_type || 'individual',
      location: apiAppointment.location_details || { type: 'online' },
      participants: apiAppointment.current_participants || 1,
      maxParticipants: apiAppointment.max_participants,
      notes: apiAppointment.notes,
      confirmed: apiAppointment.confirmed_by_tutor || false,
    };
  };

  // Join date-specific room when date changes
  useEffect(() => {
    if (websocket?.joinDateRoom) {
      const dateStr = format(selectedDate, 'yyyy-MM-dd');
      websocket.joinDateRoom(dateStr);
    }
  }, [selectedDate, websocket]);

  // Filter functions
  const hasActiveFilters = useCallback(() => {
    return filteredTutorIds.length > 0 || 
           statusFilter.length > 0 || 
           subjectFilter.trim() !== '' ||
           dateRangeFilter.startDate !== format(selectedDate, 'yyyy-MM-dd') ||
           dateRangeFilter.endDate !== format(selectedDate, 'yyyy-MM-dd');
  }, [filteredTutorIds, statusFilter, subjectFilter, dateRangeFilter, selectedDate]);

  const getFilteredTutors = () => {
    // If user is a tutor, only show themselves
    if (isTutor) {
      const tutorId = user?.userId || 1; // Mock tutor ID
      return tutors.filter(tutor => tutor.id === tutorId);
    }
    
    if (filteredTutorIds.length === 0) return tutors;
    return tutors.filter(tutor => filteredTutorIds.includes(tutor.id));
  };

  const getFilteredAppointments = () => {
    let filtered = appointments;
    
    // If user is a client, only show their appointments
    if (isClient) {
      filtered = filtered.filter(apt => 
        apt.clientEmail === user?.email || 
        apt.clientId === user?.userId
      );
    }
    
    // If user is a tutor, only show their appointments
    if (isTutor) {
      // In a real app, tutor ID would come from user object
      const tutorId = user?.userId || 1; // Mock tutor ID
      filtered = filtered.filter(apt => apt.tutorId === tutorId);
    }
    
    // Filter by tutors (only for managers)
    if (filteredTutorIds.length > 0 && !isTutor) {
      filtered = filtered.filter(apt => filteredTutorIds.includes(apt.tutorId));
    }
    
    // Filter by status
    if (statusFilter.length > 0) {
      filtered = filtered.filter(apt => statusFilter.includes(apt.status));
    }
    
    // Filter by subject
    if (subjectFilter.trim()) {
      filtered = filtered.filter(apt => 
        apt.subject.toLowerCase().includes(subjectFilter.toLowerCase())
      );
    }
    
    return filtered;
  };

  const clearAllFilters = useCallback(() => {
    setFilteredTutorIds([]);
    setStatusFilter([]);
    setSubjectFilter('');
    setDateRangeFilter({
      startDate: format(selectedDate, 'yyyy-MM-dd'),
      endDate: format(selectedDate, 'yyyy-MM-dd')
    });
  }, [selectedDate]);

  const toggleTutorFilter = (tutorId: number) => {
    setFilteredTutorIds(prev => 
      prev.includes(tutorId) 
        ? prev.filter(id => id !== tutorId)
        : [...prev, tutorId]
    );
  };

  const toggleStatusFilter = (status: string) => {
    setStatusFilter(prev => 
      prev.includes(status) 
        ? prev.filter(s => s !== status)
        : [...prev, status]
    );
  };

  // Update date range filter when selected date changes
  useEffect(() => {
    // Only update if there are no active filters
    const hasFilters = filteredTutorIds.length > 0 || 
                      statusFilter.length > 0 || 
                      subjectFilter.trim() !== '';
    
    if (!hasFilters) {
      setDateRangeFilter({
        startDate: format(selectedDate, 'yyyy-MM-dd'),
        endDate: format(selectedDate, 'yyyy-MM-dd')
      });
    }
  }, [selectedDate, filteredTutorIds.length, statusFilter.length, subjectFilter]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      // Toggle filters with Ctrl/Cmd + F
      if ((event.ctrlKey || event.metaKey) && event.key === 'f') {
        event.preventDefault();
        setShowFilters(!showFilters);
      }
      // Clear filters with Escape
      if (event.key === 'Escape' && hasActiveFilters()) {
        clearAllFilters();
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [showFilters, hasActiveFilters, clearAllFilters]);

  // Load data from API
  useEffect(() => {
    loadData();
  }, [selectedDate, filteredTutorIds, statusFilter, subjectFilter]);

  const loadData = async () => {
    setLoading(true);
    try {
      // Load tutors
      const tutorResponse = await tutorService.searchTutors({
        is_active: true,
        limit: 50
      });
      
      const tutorList: Tutor[] = tutorResponse.items.map(t => ({
        id: t.tutor_id,
        name: `${t.first_name} ${t.last_name}`,
        avatar: t.profile_picture || '/avatars/default.jpg',
        specialties: t.specializations || [],
        isActive: t.is_active,
        currentLoad: 0, // TODO: Calculate from appointments
        maxLoad: 8 // TODO: Get from tutor settings
      }));
      
      setTutors(tutorList);

      // Build appointment query params
      const params: any = {
        scheduled_date: format(selectedDate, 'yyyy-MM-dd'),
        limit: 100
      };

      // Apply filters
      if (filteredTutorIds.length > 0 && !isTutor) {
        params.tutor_id = filteredTutorIds[0]; // API might support array later
      }
      
      if (statusFilter.length > 0) {
        params.status = statusFilter[0]; // API might support array later
      }
      
      if (subjectFilter.trim()) {
        params.subject_area = subjectFilter;
      }

      // Role-based filtering
      if (isClient) {
        params.client_id = user?.userId;
      } else if (isTutor) {
        params.tutor_id = user?.userId;
      }

      // Load appointments
      const appointmentResponse = await appointmentService.listAppointments(params);
      
      const appointmentList: Appointment[] = appointmentResponse.items.map(apt => transformAppointmentFromAPI(apt));
      
      setAppointments(appointmentList);
      
      // Update tutor load
      const updatedTutors = tutorList.map(tutor => {
        const tutorAppointments = appointmentList.filter(apt => 
          apt.tutorId === tutor.id && 
          ['scheduled', 'confirmed', 'in_progress'].includes(apt.status)
        );
        return {
          ...tutor,
          currentLoad: tutorAppointments.length
        };
      });
      
      setTutors(updatedTutors);
    } catch (error) {
      console.error('Error loading calendar data:', error);
      toast.error(t('calendar.loadError'));
    } finally {
      setLoading(false);
    }
  };

  // Scroll to current time on component mount
  useEffect(() => {
    if (scrollContainerRef.current && isSameDay(selectedDate, new Date())) {
      const currentHour = new Date().getHours();
      const hourHeight = 60; // Each hour is 60px
      const scrollTop = Math.max(0, (currentHour - 2) * hourHeight); // Scroll to 2 hours before current time
      scrollContainerRef.current.scrollTop = scrollTop;
    }
  }, [selectedDate]);

  const timeSlots = eachHourOfInterval({
    start: startOfDay(selectedDate),
    end: endOfDay(selectedDate)
  }).slice(6, 22); // 6 AM to 10 PM

  const navigateDate = (direction: 'prev' | 'next') => {
    setSelectedDate(direction === 'prev' ? subDays(selectedDate, 1) : addDays(selectedDate, 1));
  };

  const getAppointmentStyle = (appointment: Appointment) => {
    const startHour = appointment.startTime.getHours();
    const startMinute = appointment.startTime.getMinutes();
    const durationMinutes = appointment.duration;
    
    const top = ((startHour - 6) * 60) + startMinute; // 6 AM offset
    const height = durationMinutes;
    
    return {
      top: `${top}px`,
      height: `${height}px`
    };
  };

  const getAppointmentColor = (appointment: Appointment) => {
    const colors = {
      scheduled: 'bg-yellow-50 border-yellow-400 text-yellow-900',
      confirmed: 'bg-red-50 border-accent-red text-red-900',
      in_progress: 'bg-green-50 border-green-400 text-green-900',
      completed: 'bg-gray-50 border-gray-400 text-gray-700',
      cancelled: 'bg-red-100 border-red-400 text-red-700'
    };
    return colors[appointment.status];
  };

  const getLocationIcon = (type: string) => {
    switch (type) {
      case 'online': return <Video className="w-3 h-3" />;
      case 'in_person': return <Home className="w-3 h-3" />;
      case 'library': return <MapPin className="w-3 h-3" />;
      default: return <MapPin className="w-3 h-3" />;
    }
  };

  const navigate = useNavigate();
  
  const handleTimeSlotClick = (tutorId: number, time: Date) => {
    if (!canBookAppointment) {
      toast.error(t('calendar.noBookingPermission'));
      return;
    }
    
    // Navigate to booking flow with pre-selected tutor and time
    const params = new URLSearchParams({
      tutorId: tutorId.toString(),
      date: format(selectedDate, 'yyyy-MM-dd'),
      time: format(time, 'HH:mm')
    });
    navigate(`/appointments/book?${params.toString()}`);
  };

  const handleDragStart = (appointment: Appointment) => {
    if (!canBookAppointment) return;
    setDraggedAppointment(appointment);
  };

  const handleDragEnd = () => {
    setDraggedAppointment(null);
  };

  const handleDrop = async (tutorId: number, time: Date) => {
    if (!draggedAppointment || !canBookAppointment) return;
    
    try {
      const newDate = format(selectedDate, 'yyyy-MM-dd');
      const newStartTime = format(time, 'HH:mm');
      const duration = draggedAppointment.duration;
      const endHour = time.getHours() + Math.floor(duration / 60);
      const endMinute = time.getMinutes() + (duration % 60);
      const newEndTime = `${String(endHour).padStart(2, '0')}:${String(endMinute).padStart(2, '0')}`;
      
      await appointmentService.updateAppointment(draggedAppointment.id, {
        tutor_id: tutorId,
        scheduled_date: newDate,
        start_time: newStartTime,
        end_time: newEndTime
      });
      
      toast.success(t('calendar.appointmentRescheduled'));
      loadData(); // Reload appointments
    } catch (error) {
      console.error('Error rescheduling appointment:', error);
      toast.error(t('calendar.rescheduleError'));
    }
  };

  const handleConfirmAppointment = async (appointmentId: number) => {
    if (!canConfirmAppointment) {
      toast.error(t('calendar.noConfirmPermission'));
      return;
    }
    
    try {
      await appointmentService.confirmAppointment(appointmentId, {
        confirmation_notes: 'Confirmed via calendar',
        notify_client: true
      });
      toast.success(t('calendar.appointmentConfirmed'));
      loadData(); // Reload appointments
    } catch (error) {
      console.error('Error confirming appointment:', error);
      toast.error(t('calendar.confirmError'));
    }
  };

  const handleAISuggestionSelect = (suggestion: any) => {
    // Handle AI suggestion selection - create new appointment
    console.log('AI Suggestion selected:', suggestion);
    
    // Show success message
    toast.success('Scheduling appointment based on AI suggestion');
    
    // Close AI suggestions panel
    setShowAISuggestions(false);
    
    // Here you would typically call the appointment creation API
    // with the suggestion data
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Date Navigation */}
      <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigateDate('prev')}
            className="p-2 rounded-md hover:bg-gray-100 transition-colors"
          >
            <ChevronLeft className="w-5 h-5" />
          </button>
          
          <h2 className="text-lg font-semibold text-gray-900">
            {format(selectedDate, 'EEEE, MMMM d, yyyy')}
          </h2>
          
          <button
            onClick={() => navigateDate('next')}
            className="p-2 rounded-md hover:bg-gray-100 transition-colors"
          >
            <ChevronRight className="w-5 h-5" />
          </button>
          
          <button
            onClick={() => setSelectedDate(new Date())}
            className="px-4 py-2 text-sm font-medium text-accent-red bg-red-50 rounded-full hover:bg-red-100 transition-colors"
          >
            {t('calendar.today')}
          </button>
          
          {/* Filter Toggle Button */}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`relative px-4 py-2 text-sm font-medium rounded-full transition-colors flex items-center space-x-1 ${
              hasActiveFilters() 
                ? 'text-accent-red bg-red-100 hover:bg-red-200' 
                : 'text-gray-600 bg-gray-100 hover:bg-gray-200'
            }`}
          >
            <Filter className="w-4 h-4" />
            <span>{t('calendar.filters')}</span>
            {hasActiveFilters() && (
              <div className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></div>
            )}
          </button>
          
          {/* AI Suggestions Button */}
          {canBookAppointment && (
            <button
              onClick={() => {
                setAISuggestionsParams({
                  clientId: user?.userId || 1,
                  subjectArea: 'Mathematics',
                  duration: 60,
                  sessionType: 'individual'
                });
                setShowAISuggestions(true);
              }}
              className="px-4 py-2 text-sm font-medium text-yellow-800 bg-yellow-50 rounded-full hover:bg-yellow-100 transition-colors flex items-center space-x-1"
            >
              <Brain className="w-4 h-4" />
              <span>{t('calendar.aiSuggestions')}</span>
            </button>
          )}
        </div>
        
        <div className="flex items-center space-x-4">
          {/* WebSocket Status Indicator */}
          <div className="flex items-center space-x-2">
            {websocket.isConnected ? (
              <div className="flex items-center text-green-600">
                <Wifi className="w-4 h-4 mr-1" />
                <span className="text-xs font-medium">Live</span>
              </div>
            ) : websocket.isConnecting ? (
              <div className="flex items-center text-yellow-600">
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-yellow-600 mr-2"></div>
                <span className="text-xs font-medium">Connecting...</span>
              </div>
            ) : (
              <div className="flex items-center text-red-600">
                <WifiOff className="w-4 h-4 mr-1" />
                <span className="text-xs font-medium">Offline</span>
              </div>
            )}
          </div>
          
          <div className="text-sm text-gray-500">
            {getFilteredAppointments().length} {t('calendar.appointments')}
            {hasActiveFilters() && appointments.length > getFilteredAppointments().length && (
              <span className="text-xs text-gray-400 ml-1">
                (of {appointments.length} total)
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Filter Panel */}
      {showFilters && (
        <div className="border-b border-gray-200 bg-gray-50 p-4">
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-gray-700">{t('calendar.filters')}:</span>
            </div>
            
            {/* Tutor Filter */}
            <div className="flex items-center space-x-2">
              <label className="text-sm text-gray-600">{t('calendar.tutors')}:</label>
              <div className="flex flex-wrap gap-1">
                {tutors.map((tutor) => (
                  <button
                    key={tutor.id}
                    onClick={() => toggleTutorFilter(tutor.id)}
                    className={`px-2 py-1 text-xs rounded-md transition-colors ${
                      filteredTutorIds.includes(tutor.id)
                        ? 'bg-red-100 text-red-800 border border-red-300'
                        : 'bg-white text-gray-600 border border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {tutor.name}
                  </button>
                ))}
              </div>
            </div>
            
            {/* Status Filter */}
            <div className="flex items-center space-x-2">
              <label className="text-sm text-gray-600">{t('calendar.status')}:</label>
              <div className="flex flex-wrap gap-1">
                {['scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled'].map((status) => (
                  <button
                    key={status}
                    onClick={() => toggleStatusFilter(status)}
                    className={`px-2 py-1 text-xs rounded-md transition-colors capitalize ${
                      statusFilter.includes(status)
                        ? 'bg-red-100 text-red-800 border border-red-300'
                        : 'bg-white text-gray-600 border border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {status}
                  </button>
                ))}
              </div>
            </div>
            
            {/* Subject Filter */}
            <div className="flex items-center space-x-2">
              <label className="text-sm text-gray-600">{t('calendar.subject')}:</label>
              <input
                type="text"
                value={subjectFilter}
                onChange={(e) => setSubjectFilter(e.target.value)}
                placeholder={t('calendar.searchSubjects')}
                className="px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-red focus:border-transparent"
              />
            </div>
            
            {/* Date Range Filter */}
            <div className="flex items-center space-x-2">
              <label className="text-sm text-gray-600">{t('calendar.dateRange')}:</label>
              <input
                type="date"
                value={dateRangeFilter.startDate}
                onChange={(e) => setDateRangeFilter(prev => ({ ...prev, startDate: e.target.value }))}
                className="px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-red"
              />
              <span className="text-gray-400">to</span>
              <input
                type="date"
                value={dateRangeFilter.endDate}
                onChange={(e) => setDateRangeFilter(prev => ({ ...prev, endDate: e.target.value }))}
                className="px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-red"
              />
            </div>
            
            {/* Clear Filters */}
            {hasActiveFilters() && (
              <button
                onClick={clearAllFilters}
                className="px-3 py-1 text-sm text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md transition-colors flex items-center space-x-1"
              >
                <X className="w-4 h-4" />
                <span>{t('calendar.clearFilters')}</span>
              </button>
            )}
          </div>
          
          {/* Active Filter Summary */}
          {hasActiveFilters() && (
            <div className="mt-3 text-xs text-gray-600">
              <span className="font-medium">{t('calendar.activeFilters')}: </span>
              {filteredTutorIds.length > 0 && (
                <span className="mr-2">
                  {filteredTutorIds.length} {t('calendar.tutorsSelected')}
                </span>
              )}
              {statusFilter.length > 0 && (
                <span className="mr-2">
                  {statusFilter.length} {t('calendar.statusesSelected')}
                </span>
              )}
              {subjectFilter.trim() && (
                <span className="mr-2">
                  {t('calendar.subjectSearch')}: "{subjectFilter}"
                </span>
              )}
            </div>
          )}
        </div>
      )}

      {/* Calendar Grid */}
      <div className="flex-1 flex overflow-hidden">
        {/* Time Column */}
        <div className="w-20 flex-shrink-0 border-r border-gray-200">
          <div className="h-16"></div> {/* Header spacer */}
          {timeSlots.map((time) => (
            <div key={time.getTime()} className="h-15 flex items-start justify-end pr-2 text-xs text-gray-500 border-b border-gray-100">
              {format(time, 'HH:mm')}
            </div>
          ))}
        </div>

        {/* Tutors Columns */}
        <div className="flex-1 overflow-x-auto">
          <div className="flex min-w-full">
            {getFilteredTutors().map((tutor) => (
              <div key={tutor.id} className="flex-1 min-w-48 border-r border-gray-200">
                {/* Tutor Header */}
                <div className="h-16 p-3 border-b border-gray-200 bg-gray-50">
                  <div className="flex items-center space-x-3">
                    <div className="relative">
                      <img
                        src={tutor.avatar || '/default-avatar.png'}
                        alt={tutor.name}
                        className="w-8 h-8 rounded-full object-cover"
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = '/default-avatar.png';
                        }}
                      />
                      <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${
                        tutor.isActive ? 'bg-green-400' : 'bg-gray-400'
                      }`}></div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {tutor.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {tutor.currentLoad}/{tutor.maxLoad} {t('calendar.sessions')}
                      </p>
                    </div>
                  </div>
                  <div className="mt-1 flex flex-wrap gap-1">
                    {tutor.specialties.slice(0, 2).map((specialty) => (
                      <span
                        key={specialty}
                        className="inline-block px-1.5 py-0.5 text-xs font-medium bg-red-100 text-red-700 rounded"
                      >
                        {specialty}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Time Slots */}
                <div className="relative" ref={scrollContainerRef}>
                  {timeSlots.map((time) => (
                    <div
                      key={time.getTime()}
                      className="h-15 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors relative"
                      onClick={() => handleTimeSlotClick(tutor.id, time)}
                      onDrop={(e) => {
                        e.preventDefault();
                        handleDrop(tutor.id, time);
                      }}
                      onDragOver={(e) => e.preventDefault()}
                    >
                      {/* Hour marker */}
                      {time.getMinutes() === 0 && (
                        <div className="absolute top-0 left-0 right-0 border-t border-gray-300"></div>
                      )}
                    </div>
                  ))}

                  {/* Appointments */}
                  {getFilteredAppointments()
                    .filter((apt) => apt.tutorId === tutor.id)
                    .map((appointment) => (
                      <div
                        key={appointment.id}
                        className={`absolute left-1 right-1 rounded-md border-l-4 p-2 shadow-sm cursor-pointer transition-all hover:shadow-md ${getAppointmentColor(appointment)} ${
                          !appointment.confirmed ? 'opacity-75 border-dashed' : ''
                        }`}
                        style={getAppointmentStyle(appointment)}
                        draggable
                        onDragStart={() => handleDragStart(appointment)}
                        onDragEnd={handleDragEnd}
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedAppointment(appointment);
                          setShowAppointmentDetails(true);
                        }}
                        title={`${appointment.clientName} - ${appointment.subject}`}
                      >
                        <div className="text-xs font-medium truncate">
                          {appointment.clientName}
                          {appointment.dependantName && ` (${appointment.dependantName})`}
                        </div>
                        
                        <div className="text-xs text-gray-600 mt-0.5 truncate">
                          {appointment.subject}
                        </div>
                        
                        <div className="flex items-center justify-between mt-1">
                          <div className="flex items-center space-x-1 text-xs text-gray-500">
                            <Clock className="w-3 h-3" />
                            <span>{format(appointment.startTime, 'HH:mm')}</span>
                          </div>
                          
                          <div className="flex items-center space-x-1">
                            {getLocationIcon(appointment.location.type)}
                            
                            {appointment.type === 'group' || appointment.type === 'tecfee' ? (
                              <div className="flex items-center text-xs text-gray-500">
                                <Users className="w-3 h-3 mr-1" />
                                <span>{appointment.participants}/{appointment.maxParticipants}</span>
                              </div>
                            ) : (
                              <User className="w-3 h-3 text-gray-500" />
                            )}
                          </div>
                        </div>
                        
                        {!appointment.confirmed && (
                          <div className="flex items-center justify-between mt-1">
                            <div className="text-xs text-orange-600 font-medium">
                              {t('calendar.pendingConfirmation')}
                            </div>
                            {canConfirmAppointment && (
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleConfirmAppointment(appointment.id);
                                }}
                                className="px-2 py-0.5 text-xs text-white bg-green-600 hover:bg-green-700 rounded transition-colors"
                                title={t('calendar.confirmAppointment')}
                              >
                                {t('common.confirm')}
                              </button>
                            )}
                          </div>
                        )}
                      </div>
                    ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Current Time Indicator */}
      {isSameDay(selectedDate, new Date()) && (
        <div
          className="absolute left-20 right-0 h-0.5 bg-accent-red z-10 pointer-events-none"
          style={{
            top: `${16 + ((new Date().getHours() - 6) * 60) + new Date().getMinutes()}px`
          }}
        >
          <div className="absolute left-0 top-0 w-2 h-2 bg-accent-red rounded-full -translate-y-1"></div>
        </div>
      )}


      {/* AI Scheduling Suggestions Modal */}
      {showAISuggestions && aiSuggestionsParams && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <AISchedulingSuggestions
              clientId={aiSuggestionsParams.clientId}
              subjectArea={aiSuggestionsParams.subjectArea}
              duration={aiSuggestionsParams.duration}
              sessionType={aiSuggestionsParams.sessionType}
              onSuggestionSelect={handleAISuggestionSelect}
              onClose={() => setShowAISuggestions(false)}
              className="w-full"
            />
          </div>
        </div>
      )}

      {/* Appointment Details Modal */}
      <AppointmentDetailsModal
        isOpen={showAppointmentDetails}
        onClose={() => {
          setShowAppointmentDetails(false);
          setSelectedAppointment(null);
        }}
        appointment={selectedAppointment}
        onEdit={() => {
          // Handle edit action
          setShowAppointmentDetails(false);
          // Navigate to edit form or open edit modal
        }}
      />
    </div>
  );
};

export default CalendarDayView;