"""
Service dependencies for dependency injection.
"""

from fastapi import Depends
import asyncpg

from app.core.dependencies import get_database
from app.database.repositories.invoice_repository import InvoiceRepository
from app.database.repositories.subscription_repository import SubscriptionRepository
from app.database.repositories.payment_repository import PaymentRepository
from app.services.invoice_service import InvoiceService
from app.services.tutor_payment_service import TutorPaymentService
from app.services.notification_service import NotificationService
from app.services.email_service import EmailService
from app.services.stripe_service import StripeService
from app.services.messaging_service import MessagingService


# Repository dependencies
def get_invoice_repository() -> InvoiceRepository:
    """Get invoice repository instance."""
    return InvoiceRepository()


def get_subscription_repository() -> SubscriptionRepository:
    """Get subscription repository instance."""
    return SubscriptionRepository()


def get_payment_repository() -> PaymentRepository:
    """Get payment repository instance."""
    return PaymentRepository()


# Service dependencies
def get_notification_service() -> NotificationService:
    """Get notification service instance."""
    return NotificationService()


def get_email_service() -> EmailService:
    """Get email service instance."""
    return EmailService()


def get_stripe_service() -> StripeService:
    """Get stripe service instance."""
    return StripeService()


def get_messaging_service() -> MessagingService:
    """Get messaging service instance."""
    return MessagingService()


# Complex service dependencies
def get_invoice_service(
    invoice_repo: InvoiceRepository = Depends(get_invoice_repository),
    subscription_repo: SubscriptionRepository = Depends(get_subscription_repository),
    notification_service: NotificationService = Depends(get_notification_service),
    email_service: EmailService = Depends(get_email_service),
    stripe_service: StripeService = Depends(get_stripe_service)
) -> InvoiceService:
    """Get invoice service instance with dependencies."""
    return InvoiceService(
        invoice_repo=invoice_repo,
        subscription_repo=subscription_repo,
        notification_service=notification_service,
        email_service=email_service,
        stripe_service=stripe_service
    )


def get_tutor_payment_service(
    payment_repo: PaymentRepository = Depends(get_payment_repository),
    stripe_service: StripeService = Depends(get_stripe_service),
    notification_service: NotificationService = Depends(get_notification_service),
    email_service: EmailService = Depends(get_email_service)
) -> TutorPaymentService:
    """Get tutor payment service instance with dependencies."""
    return TutorPaymentService(
        payment_repo=payment_repo,
        stripe_service=stripe_service,
        notification_service=notification_service,
        email_service=email_service
    )