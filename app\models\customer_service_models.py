"""
Customer Service Models

Comprehensive data models for the customer service system including
conversations, messages, templates, agents, and analytics.
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from decimal import Decimal
from enum import Enum
from pydantic import BaseModel, Field, field_validator
from app.models.base import BaseEntity


class AgentStatus(str, Enum):
    """Agent availability status"""
    AVAILABLE = "available"
    BUSY = "busy"
    AWAY = "away"
    OFFLINE = "offline"


class ConversationStatus(str, Enum):
    """Conversation status options"""
    OPEN = "open"
    ASSIGNED = "assigned"
    RESOLVED = "resolved"
    CLOSED = "closed"
    ESCALATED = "escalated"


class ConversationType(str, Enum):
    """Type of conversation channel"""
    SMS = "sms"
    IN_APP = "in_app"
    EMAIL = "email"
    PHONE = "phone"


class Priority(str, Enum):
    """Priority levels"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class MessageDirection(str, Enum):
    """Message direction"""
    INBOUND = "inbound"
    OUTBOUND = "outbound"


class DeliveryStatus(str, Enum):
    """Message delivery status"""
    PENDING = "pending"
    SENT = "sent"
    DELIVERED = "delivered"
    READ = "read"
    FAILED = "failed"


class BroadcastStatus(str, Enum):
    """Broadcast message status"""
    DRAFT = "draft"
    SCHEDULED = "scheduled"
    SENDING = "sending"
    COMPLETED = "completed"
    FAILED = "failed"


class TemplateStatus(str, Enum):
    """Template approval status"""
    DRAFT = "draft"
    APPROVED = "approved"
    ARCHIVED = "archived"


# Database Models

class CustomerServiceAgent(BaseEntity):
    """Customer service agent model"""
    agent_id: int
    user_id: int
    agent_name: str
    department: str = "general"
    status: AgentStatus = AgentStatus.AVAILABLE
    max_concurrent_conversations: int = 10
    specialties: List[str] = []
    languages: List[str] = ["en"]
    created_at: datetime
    updated_at: datetime
    is_active: bool = True


class CSConversation(BaseEntity):
    """Customer service conversation model"""
    conversation_id: int
    thread_id: str
    participant_user_id: Optional[int] = None
    participant_phone: Optional[str] = None
    participant_email: Optional[str] = None
    participant_name: Optional[str] = None
    assigned_agent_id: Optional[int] = None
    conversation_type: ConversationType = ConversationType.SMS
    subject: Optional[str] = None
    status: ConversationStatus = ConversationStatus.OPEN
    priority: Priority = Priority.NORMAL
    category: Optional[str] = None
    source_channel: Optional[str] = None
    
    # Timing
    created_at: datetime
    first_response_at: Optional[datetime] = None
    last_activity_at: datetime
    resolved_at: Optional[datetime] = None
    closed_at: Optional[datetime] = None
    
    # SLA
    response_time_sla_minutes: int = 60
    resolution_time_sla_hours: int = 24
    customer_satisfaction_rating: Optional[int] = None
    customer_feedback: Optional[str] = None
    
    # Internal
    internal_notes: Optional[str] = None
    tags: List[str] = []
    escalation_reason: Optional[str] = None
    auto_assigned: bool = False
    
    # Localization
    language: str = "en"
    timezone: str = "America/Montreal"

    @field_validator('customer_satisfaction_rating')
    def validate_rating(cls, v):
        if v is not None and (v < 1 or v > 5):
            raise ValueError('Rating must be between 1 and 5')
        return v


class CSMessage(BaseEntity):
    """Customer service message model"""
    message_id: int
    conversation_id: int
    thread_id: str
    
    # Content
    message_content: str
    message_type: str = "text"
    message_direction: MessageDirection
    
    # Sender
    sender_user_id: Optional[int] = None
    sender_agent_id: Optional[int] = None
    sender_phone: Optional[str] = None
    sender_name: Optional[str] = None
    sender_role: Optional[str] = None
    
    # Delivery
    delivery_status: DeliveryStatus = DeliveryStatus.PENDING
    external_message_id: Optional[str] = None
    channel: str
    
    # Metadata
    template_id: Optional[int] = None
    automated: bool = False
    system_generated: bool = False
    internal_note: bool = False
    
    # Timing
    sent_at: datetime
    delivered_at: Optional[datetime] = None
    read_at: Optional[datetime] = None
    
    # Rich content
    attachments: Optional[Dict[str, Any]] = None
    rich_content: Optional[Dict[str, Any]] = None
    language: str = "en"


class CSMessageTemplate(BaseEntity):
    """Message template model"""
    template_id: int
    template_name: str
    template_key: str
    category: str
    
    # Content
    subject: Optional[str] = None
    message_content: str
    variables: Optional[Dict[str, Any]] = None
    
    # Metadata
    language: str = "en"
    channel: Optional[str] = None
    department: str = "general"
    
    # Usage
    usage_count: int = 0
    last_used_at: Optional[datetime] = None
    approved_by: Optional[int] = None
    approved_at: Optional[datetime] = None
    status: TemplateStatus = TemplateStatus.DRAFT
    
    # Management
    created_by: int
    created_at: datetime
    updated_at: datetime
    version: int = 1


class CSQuickResponse(BaseEntity):
    """Quick response model"""
    quick_response_id: int
    response_text: str
    trigger_keywords: List[str] = []
    category: Optional[str] = None
    language: str = "en"
    usage_count: int = 0
    created_by: int
    created_at: datetime
    is_active: bool = True


class CSConversationAssignment(BaseEntity):
    """Conversation assignment tracking"""
    assignment_id: int
    conversation_id: int
    agent_id: int
    assigned_by: Optional[int] = None
    assigned_at: datetime
    unassigned_at: Optional[datetime] = None
    assignment_reason: Optional[str] = None
    auto_assigned: bool = False


class CSConversationMetrics(BaseEntity):
    """Conversation performance metrics"""
    metric_id: int
    conversation_id: int
    
    # Response times (minutes)
    first_response_time: Optional[int] = None
    average_response_time: Optional[Decimal] = None
    total_response_time: Optional[int] = None
    
    # Resolution time (hours)
    resolution_time: Optional[Decimal] = None
    
    # Message counts
    total_messages: int = 0
    customer_messages: int = 0
    agent_messages: int = 0
    
    # Satisfaction
    satisfaction_rating: Optional[int] = None
    satisfaction_comment: Optional[str] = None
    
    # SLA compliance
    first_response_sla_met: Optional[bool] = None
    resolution_sla_met: Optional[bool] = None
    
    calculated_at: datetime

    @field_validator('satisfaction_rating')
    def validate_rating(cls, v):
        if v is not None and (v < 1 or v > 5):
            raise ValueError('Rating must be between 1 and 5')
        return v


class CSBroadcastMessage(BaseEntity):
    """Broadcast message model"""
    broadcast_id: int
    campaign_name: str
    message_content: str
    subject: Optional[str] = None
    
    # Targeting
    target_audience: Dict[str, Any]
    target_user_ids: List[int] = []
    exclude_user_ids: List[int] = []
    
    # Channel
    channels: List[str]
    
    # Scheduling
    scheduled_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    # Status
    status: BroadcastStatus = BroadcastStatus.DRAFT
    total_recipients: int = 0
    successful_deliveries: int = 0
    failed_deliveries: int = 0
    
    # Management
    created_by: int
    created_at: datetime
    approved_by: Optional[int] = None
    approved_at: Optional[datetime] = None
    
    # Localization
    language: str = "en"
    timezone: str = "America/Montreal"


class CSBroadcastDelivery(BaseEntity):
    """Broadcast delivery tracking"""
    delivery_id: int
    broadcast_id: int
    user_id: Optional[int] = None
    phone_number: Optional[str] = None
    email_address: Optional[str] = None
    
    channel: str
    delivery_status: DeliveryStatus = DeliveryStatus.PENDING
    external_message_id: Optional[str] = None
    
    sent_at: Optional[datetime] = None
    delivered_at: Optional[datetime] = None
    error_message: Optional[str] = None


class CSAgentPerformance(BaseEntity):
    """Daily agent performance metrics"""
    performance_id: int
    agent_id: int
    date: datetime
    
    # Volume
    conversations_handled: int = 0
    messages_sent: int = 0
    
    # Time
    total_active_time: int = 0  # minutes
    average_response_time: Optional[Decimal] = None
    
    # Quality
    satisfaction_average: Optional[Decimal] = None
    satisfaction_count: int = 0
    escalations_received: int = 0
    
    # SLA
    first_response_sla_met_count: int = 0
    resolution_sla_met_count: int = 0
    total_sla_conversations: int = 0
    
    # Efficiency
    conversations_resolved: int = 0
    
    calculated_at: datetime


class CSKnowledgeBase(BaseEntity):
    """Knowledge base article model"""
    article_id: int
    title: str
    content: str
    category: str
    tags: List[str] = []
    
    # Search
    keywords: List[str] = []
    faq_question: Optional[str] = None
    
    # Metadata
    language: str = "en"
    visibility: str = "internal"  # internal, public, client, tutor
    
    # Management
    created_by: int
    created_at: datetime
    updated_at: datetime
    reviewed_by: Optional[int] = None
    reviewed_at: Optional[datetime] = None
    
    # Usage
    view_count: int = 0
    helpful_count: int = 0
    not_helpful_count: int = 0
    
    is_active: bool = True


# API Request/Response Models

class CreateConversationRequest(BaseModel):
    """Request to create a new conversation"""
    participant_phone: Optional[str] = None
    participant_email: Optional[str] = None
    participant_name: Optional[str] = None
    conversation_type: ConversationType = ConversationType.SMS
    subject: Optional[str] = None
    priority: Priority = Priority.NORMAL
    category: Optional[str] = None
    initial_message: str
    language: str = "en"


class SendMessageRequest(BaseModel):
    """Request to send a message"""
    conversation_id: int
    message_content: str
    message_type: str = "text"
    template_id: Optional[int] = None
    internal_note: bool = False
    attachments: Optional[Dict[str, Any]] = None


class AssignConversationRequest(BaseModel):
    """Request to assign conversation to agent"""
    conversation_id: int
    agent_id: int
    assignment_reason: Optional[str] = None


class UpdateConversationRequest(BaseModel):
    """Request to update conversation"""
    status: Optional[ConversationStatus] = None
    priority: Optional[Priority] = None
    category: Optional[str] = None
    internal_notes: Optional[str] = None
    tags: Optional[List[str]] = None


class CreateTemplateRequest(BaseModel):
    """Request to create message template"""
    template_name: str
    template_key: str
    category: str
    message_content: str
    subject: Optional[str] = None
    variables: Optional[Dict[str, Any]] = None
    language: str = "en"
    channel: Optional[str] = None
    department: str = "general"


class CreateBroadcastRequest(BaseModel):
    """Request to create broadcast message"""
    campaign_name: str
    message_content: str
    subject: Optional[str] = None
    target_audience: Dict[str, Any]
    channels: List[str]
    scheduled_at: Optional[datetime] = None
    language: str = "en"


class ConversationSummary(BaseModel):
    """Conversation summary for dashboard"""
    conversation_id: int
    thread_id: str
    participant_name: Optional[str]
    participant_contact: Optional[str]  # phone or email
    agent_name: Optional[str]
    status: ConversationStatus
    priority: Priority
    category: Optional[str]
    last_message: str
    last_activity_at: datetime
    unread_count: int
    response_overdue: bool
    satisfaction_rating: Optional[int]


class AgentSummary(BaseModel):
    """Agent summary for dashboard"""
    agent_id: int
    agent_name: str
    status: AgentStatus
    current_conversations: int
    max_conversations: int
    average_response_time: Optional[Decimal]
    satisfaction_rating: Optional[Decimal]
    specialties: List[str]
    languages: List[str]


class ConversationStats(BaseModel):
    """Conversation statistics"""
    total_conversations: int
    open_conversations: int
    assigned_conversations: int
    resolved_today: int
    overdue_responses: int
    average_response_time: Optional[Decimal]
    satisfaction_average: Optional[Decimal]
    sla_compliance_rate: Optional[Decimal]


class MessageStats(BaseModel):
    """Message statistics"""
    total_messages_today: int
    inbound_messages: int
    outbound_messages: int
    automated_messages: int
    delivery_rate: Optional[Decimal]
    response_rate: Optional[Decimal]


# Response Models

class ConversationResponse(BaseModel):
    """Conversation response with related data"""
    conversation: CSConversation
    messages: List[CSMessage]
    agent: Optional[CustomerServiceAgent]
    metrics: Optional[CSConversationMetrics]
    participant_info: Optional[Dict[str, Any]]


class DashboardResponse(BaseModel):
    """Customer service dashboard response"""
    conversation_stats: ConversationStats
    message_stats: MessageStats
    agent_summaries: List[AgentSummary]
    recent_conversations: List[ConversationSummary]
    overdue_conversations: List[ConversationSummary]
    templates: List[CSMessageTemplate]
    quick_responses: List[CSQuickResponse]