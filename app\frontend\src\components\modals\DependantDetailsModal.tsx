import React from 'react';
import { useTranslation } from 'react-i18next';
import { X, User, Calendar, School, Heart, FileText, Users, Phone, Mail } from 'lucide-react';
import { Modal } from '../common/Modal';
import { Badge } from '../common/Badge';
import Button from '../common/Button';
import { Dependant } from '../../services/dependantService';
import { format, differenceInYears } from 'date-fns';

interface DependantDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  dependant: Dependant;
  onEdit?: (dependant: Dependant) => void;
  canEdit?: boolean;
}

const DependantDetailsModal: React.FC<DependantDetailsModalProps> = ({
  isOpen,
  onClose,
  dependant,
  onEdit,
  canEdit = false
}) => {
  const { t } = useTranslation();

  const age = differenceInYears(new Date(), new Date(dependant.date_of_birth));

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="lg">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              {dependant.first_name} {dependant.last_name}
            </h2>
            <div className="flex items-center space-x-3 mt-2">
              <Badge variant={dependant.is_active ? 'success' : 'secondary'} size="sm">
                {dependant.is_active ? t('common.active') : t('common.inactive')}
              </Badge>
              <span className="text-sm text-gray-600">
                {t('users.dependants.ageYears', { age })}
              </span>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="space-y-6">
          {/* Basic Information */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-3">
              {t('users.dependants.basicInfo')}
            </h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="flex items-center text-gray-600 mb-2">
                  <Calendar className="w-5 h-5 mr-2" />
                  <span className="text-sm">{t('users.dependants.dateOfBirth')}</span>
                </div>
                <div className="font-medium">{format(new Date(dependant.date_of_birth), 'PPP')}</div>
              </div>
              {dependant.grade_level && (
                <div>
                  <div className="flex items-center text-gray-600 mb-2">
                    <School className="w-5 h-5 mr-2" />
                    <span className="text-sm">{t('users.dependants.gradeLevel')}</span>
                  </div>
                  <div className="font-medium">{dependant.grade_level}</div>
                </div>
              )}
              {dependant.school_name && (
                <div>
                  <div className="flex items-center text-gray-600 mb-2">
                    <School className="w-5 h-5 mr-2" />
                    <span className="text-sm">{t('users.dependants.school')}</span>
                  </div>
                  <div className="font-medium">{dependant.school_name}</div>
                </div>
              )}
            </div>
          </div>

          {/* Parents Information */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-3">
              {t('users.dependants.parents')}
            </h3>
            <div className="space-y-3">
              {/* Parent 1 */}
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div>
                    <div className="font-medium text-gray-900">
                      {dependant.parent1_name || t('users.dependants.parent1')}
                      {dependant.primary_contact_parent === 1 && (
                        <Badge variant="info" size="sm" className="ml-2">
                          {t('users.dependants.primaryContact')}
                        </Badge>
                      )}
                    </div>
                    {dependant.parent1_relationship && (
                      <div className="text-sm text-gray-600 mt-1">
                        {dependant.parent1_relationship}
                      </div>
                    )}
                    <div className="text-sm text-gray-600 mt-2">
                      {t('users.dependants.clientId')}: {dependant.parent1_client_id}
                    </div>
                  </div>
                  <Users className="w-5 h-5 text-gray-400" />
                </div>
              </div>

              {/* Parent 2 */}
              {dependant.parent2_client_id && (
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div>
                      <div className="font-medium text-gray-900">
                        {dependant.parent2_name || t('users.dependants.parent2')}
                        {dependant.primary_contact_parent === 2 && (
                          <Badge variant="info" size="sm" className="ml-2">
                            {t('users.dependants.primaryContact')}
                          </Badge>
                        )}
                      </div>
                      {dependant.parent2_relationship && (
                        <div className="text-sm text-gray-600 mt-1">
                          {dependant.parent2_relationship}
                        </div>
                      )}
                      <div className="text-sm text-gray-600 mt-2">
                        {t('users.dependants.clientId')}: {dependant.parent2_client_id}
                      </div>
                    </div>
                    <Users className="w-5 h-5 text-gray-400" />
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Special Needs */}
          {dependant.special_needs && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                {t('users.dependants.specialNeeds')}
              </h3>
              <div className="bg-yellow-50 rounded-lg p-4">
                <div className="flex items-start">
                  <Heart className="w-5 h-5 text-yellow-600 mr-3 mt-0.5" />
                  <p className="text-gray-700">{dependant.special_needs}</p>
                </div>
              </div>
            </div>
          )}

          {/* Medical Information */}
          {dependant.medical_info && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                {t('users.dependants.medicalInfo')}
              </h3>
              <div className="bg-red-50 rounded-lg p-4">
                <div className="flex items-start">
                  <Heart className="w-5 h-5 text-red-600 mr-3 mt-0.5" />
                  <p className="text-gray-700">{dependant.medical_info}</p>
                </div>
              </div>
            </div>
          )}

          {/* Notes */}
          {dependant.notes && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                {t('users.dependants.notes')}
              </h3>
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-start">
                  <FileText className="w-5 h-5 text-gray-400 mr-3 mt-0.5" />
                  <p className="text-gray-700">{dependant.notes}</p>
                </div>
              </div>
            </div>
          )}

          {/* Account Information */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-3">
              {t('users.dependants.accountInfo')}
            </h3>
            <div className="space-y-2 text-sm">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">{t('users.dependants.dependantId')}</span>
                <span className="font-medium">{dependant.dependant_id}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">{t('users.dependants.createdAt')}</span>
                <span className="font-medium">{format(new Date(dependant.created_at), 'PPP')}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">{t('users.dependants.updatedAt')}</span>
                <span className="font-medium">{format(new Date(dependant.updated_at), 'PPP')}</span>
              </div>
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-3 gap-4 pt-4 border-t">
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Calendar className="w-8 h-8 text-gray-400" />
              </div>
              <div className="text-2xl font-bold text-gray-900">0</div>
              <div className="text-sm text-gray-600">{t('users.dependants.totalSessions')}</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Users className="w-8 h-8 text-gray-400" />
              </div>
              <div className="text-2xl font-bold text-gray-900">0</div>
              <div className="text-sm text-gray-600">{t('users.dependants.activeTutors')}</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <School className="w-8 h-8 text-gray-400" />
              </div>
              <div className="text-2xl font-bold text-gray-900">0</div>
              <div className="text-sm text-gray-600">{t('users.dependants.subjects')}</div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="mt-6 pt-6 border-t flex justify-end space-x-3">
          <Button variant="secondary" onClick={onClose}>
            {t('common.close')}
          </Button>
          {canEdit && onEdit && (
            <Button variant="primary" onClick={() => onEdit(dependant)}>
              {t('common.edit')}
            </Button>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default DependantDetailsModal;