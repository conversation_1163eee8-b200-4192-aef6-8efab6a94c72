import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';
import { 
  Send, Search, MoreVertical, Paperclip, Smile,
  Circle, CheckCircle, AlertCircle, Users, X, 
  Phone, Mail, MapPin, User, File, Image as ImageIcon,
  FileText, Download
} from 'lucide-react';
import { useWebSocketMessaging } from '../../hooks/useWebSocketMessaging';
import messagingService, { Conversation, Message } from '../../services/messagingService';
import { useAuth } from '../../contexts/AuthContext';
import { UserRoleType } from '../../types/auth';

interface ChatParticipant {
  id: number;
  name: string;
  role: UserRoleType;
  isOnline?: boolean;
  avatar?: string;
}

const InAppChatUpdated: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [messageText, setMessageText] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSending, setIsSending] = useState(false);
  const [typingUsers, setTypingUsers] = useState<Map<number, string>>(new Map());
  const [onlineUsers, setOnlineUsers] = useState<Set<number>>(new Set());
  const [uploadingFile, setUploadingFile] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [showSearch, setShowSearch] = useState(false);
  const [messageSearchQuery, setMessageSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Message[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // WebSocket connection
  const {
    isConnected,
    joinConversation,
    leaveConversation,
    sendTypingIndicator,
  } = useWebSocketMessaging({
    onMessage: handleWebSocketMessage,
    onConnectionChange: (connected) => {
      console.log('WebSocket connection status:', connected);
      if (connected && selectedConversation) {
        joinConversation(selectedConversation.conversation_id);
      }
    },
  });

  // Handle incoming WebSocket messages
  function handleWebSocketMessage(message: any) {
    switch (message.type) {
      case 'new_message':
        handleNewMessage(message.message);
        break;
      case 'typing_indicator':
        handleTypingIndicator(message);
        break;
      case 'presence_update':
        handlePresenceUpdate(message);
        break;
      case 'conversation_update':
        handleConversationUpdate(message.update);
        break;
      default:
        console.log('Unknown message type:', message.type);
    }
  }

  // Handle new message from WebSocket
  const handleNewMessage = useCallback((message: Message) => {
    // Update messages if it's for the current conversation
    if (selectedConversation && message.conversation_id === selectedConversation.conversation_id) {
      setMessages((prev) => [...prev, message]);
    }

    // Update conversation list
    setConversations((prev) =>
      prev.map((conv) =>
        conv.conversation_id === message.conversation_id
          ? {
              ...conv,
              last_message_at: message.created_at,
              last_message_content: message.content,
              last_message_sender: message.sender_name,
              unread_count: conv.conversation_id === selectedConversation?.conversation_id 
                ? 0 
                : conv.unread_count + 1,
            }
          : conv
      )
    );
  }, [selectedConversation]);

  // Handle typing indicator
  const handleTypingIndicator = useCallback((data: any) => {
    if (data.is_typing) {
      setTypingUsers((prev) => new Map(prev).set(data.user_id, data.user_name));
    } else {
      setTypingUsers((prev) => {
        const newMap = new Map(prev);
        newMap.delete(data.user_id);
        return newMap;
      });
    }
  }, []);

  // Handle presence update
  const handlePresenceUpdate = useCallback((data: any) => {
    if (data.status === 'online') {
      setOnlineUsers((prev) => new Set(prev).add(data.user_id));
    } else {
      setOnlineUsers((prev) => {
        const newSet = new Set(prev);
        newSet.delete(data.user_id);
        return newSet;
      });
    }
  }, []);

  // Handle conversation update
  const handleConversationUpdate = useCallback((update: any) => {
    // Handle read receipts
    if (update.type === 'message_read') {
      setMessages((prev) =>
        prev.map((msg) =>
          msg.message_id === update.message_id
            ? { ...msg, status: 'read' as any, read_at: update.read_at }
            : msg
        )
      );
    } else {
      // Handle other conversation updates
      setConversations((prev) =>
        prev.map((conv) =>
          conv.conversation_id === update.conversation_id
            ? { ...conv, ...update }
            : conv
        )
      );
    }
  }, []);

  // Load conversations
  const loadConversations = async () => {
    try {
      setIsLoading(true);
      const response = await messagingService.getConversations({
        status: 'active',
        limit: 50,
      });
      setConversations(response.conversations);
      
      // Select first conversation if available
      if (response.conversations.length > 0 && !selectedConversation) {
        setSelectedConversation(response.conversations[0]);
      }
    } catch (error) {
      console.error('Error loading conversations:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Load messages for selected conversation
  const loadMessages = async (conversationId: number) => {
    try {
      const response = await messagingService.getConversationMessages(conversationId, {
        limit: 100,
      });
      setMessages(response.messages);
      
      // Mark unread messages as read
      const unreadMessages = response.messages.filter(
        msg => msg.sender_id !== user?.userId && msg.status !== 'read'
      );
      
      if (unreadMessages.length > 0) {
        markMessagesAsRead(conversationId, unreadMessages.map(msg => msg.message_id));
      }
    } catch (error) {
      console.error('Error loading messages:', error);
    }
  };

  // Mark messages as read
  const markMessagesAsRead = async (conversationId: number, messageIds: number[]) => {
    try {
      await messagingService.markMessagesAsRead(conversationId, messageIds);
      
      // Update local message status
      setMessages(prev => 
        prev.map(msg => 
          messageIds.includes(msg.message_id) 
            ? { ...msg, status: 'read' as any }
            : msg
        )
      );
      
      // Update conversation unread count
      setConversations(prev =>
        prev.map(conv =>
          conv.conversation_id === conversationId
            ? { ...conv, unread_count: 0 }
            : conv
        )
      );
    } catch (error) {
      console.error('Error marking messages as read:', error);
    }
  };

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        alert('File size exceeds 10MB limit');
        return;
      }
      
      // Validate file type
      const allowedTypes = [
        'image/jpeg', 'image/png', 'image/gif',
        'application/pdf', 'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ];
      
      if (!allowedTypes.includes(file.type)) {
        alert('File type not allowed. Please upload images, PDFs, or Word documents.');
        return;
      }
      
      setSelectedFile(file);
      sendFile(file);
    }
  };

  // Send file
  const sendFile = async (file: File) => {
    if (!selectedConversation || uploadingFile) return;

    setUploadingFile(true);
    try {
      const response = await messagingService.uploadAttachment(
        selectedConversation.conversation_id,
        file
      );
      
      // File upload creates a message automatically
      // Just need to update our local state
      const fileMessage: Message = {
        message_id: response.message.message_id,
        conversation_id: selectedConversation.conversation_id,
        sender_id: user?.userId || 0,
        sender_type: user?.activeRole as any,
        sender_name: user?.name || 'You',
        content: `Sent a file: ${file.name}`,
        message_type: 'file',
        status: 'sent',
        created_at: new Date().toISOString(),
        file_url: response.message.file_url,
        file_name: response.message.file_name,
      };
      
      setMessages((prev) => [...prev, fileMessage]);
      
    } catch (error) {
      console.error('Error uploading file:', error);
      alert('Failed to upload file. Please try again.');
    } finally {
      setUploadingFile(false);
      setSelectedFile(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // Send message
  const sendMessage = async () => {
    if (!messageText.trim() || !selectedConversation || isSending) return;

    setIsSending(true);
    const tempMessage: Partial<Message> = {
      conversation_id: selectedConversation.conversation_id,
      sender_id: user?.userId || 0,
      sender_type: user?.activeRole as any,
      sender_name: user?.name || 'You',
      content: messageText,
      message_type: 'text',
      status: 'sent',
      created_at: new Date().toISOString(),
    };

    // Optimistically add message
    setMessages((prev) => [...prev, tempMessage as Message]);
    setMessageText('');

    try {
      const response = await messagingService.sendMessage(
        selectedConversation.conversation_id,
        {
          content: messageText,
          message_type: 'text',
        }
      );

      // Update with actual message
      setMessages((prev) =>
        prev.map((msg) =>
          msg === tempMessage ? response.message : msg
        )
      );
    } catch (error) {
      console.error('Error sending message:', error);
      // Remove optimistic message on error
      setMessages((prev) => prev.filter((msg) => msg !== tempMessage));
      setMessageText(tempMessage.content || '');
    } finally {
      setIsSending(false);
    }
  };

  // Handle typing
  const handleTyping = () => {
    if (!selectedConversation || !user) return;

    // Clear previous timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Send typing indicator
    sendTypingIndicator(
      selectedConversation.conversation_id,
      true,
      user.name || 'User'
    );

    // Set timeout to stop typing
    typingTimeoutRef.current = setTimeout(() => {
      sendTypingIndicator(
        selectedConversation.conversation_id,
        false,
        user.name || 'User'
      );
    }, 2000);
  };

  // Get participant info
  const getParticipantInfo = (conversation: Conversation): ChatParticipant => {
    if (conversation.conversation_type === 'in_app') {
      // Determine the other participant based on current user role
      if (user?.activeRole === UserRoleType.CLIENT && conversation.tutor_id) {
        return {
          id: conversation.tutor_id,
          name: `${conversation.tutor_first_name} ${conversation.tutor_last_name}`,
          role: UserRoleType.TUTOR,
          isOnline: onlineUsers.has(conversation.tutor_id),
        };
      } else if (user?.activeRole === UserRoleType.TUTOR && conversation.client_id) {
        return {
          id: conversation.client_id,
          name: `${conversation.client_first_name} ${conversation.client_last_name}`,
          role: UserRoleType.CLIENT,
          isOnline: onlineUsers.has(conversation.client_id),
        };
      }
    }

    // Default/group chat
    return {
      id: 0,
      name: conversation.subject || 'Conversation',
      role: UserRoleType.CLIENT,
    };
  };

  // Get role color
  const getRoleColor = (role: string) => {
    switch (role) {
      case 'manager': return 'text-purple-600';
      case 'tutor': return 'text-accent-red';
      case 'client': return 'text-accent-green';
      default: return 'text-text-primary';
    }
  };

  // Get file icon based on file type
  const getFileIcon = (fileName?: string) => {
    if (!fileName) return <File className="w-5 h-5" />;
    
    const extension = fileName.split('.').pop()?.toLowerCase();
    if (['jpg', 'jpeg', 'png', 'gif'].includes(extension || '')) {
      return <ImageIcon className="w-5 h-5" />;
    }
    if (['pdf'].includes(extension || '')) {
      return <FileText className="w-5 h-5" />;
    }
    return <File className="w-5 h-5" />;
  };

  // Search messages
  const searchMessages = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    try {
      const response = await messagingService.searchMessages({
        query,
        conversation_id: selectedConversation?.conversation_id,
        limit: 20,
      });
      setSearchResults(response.results);
    } catch (error) {
      console.error('Error searching messages:', error);
    } finally {
      setIsSearching(false);
    }
  };

  // Handle message search input
  const handleMessageSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setMessageSearchQuery(query);

    // Clear previous timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Debounce search
    searchTimeoutRef.current = setTimeout(() => {
      searchMessages(query);
    }, 300);
  };

  // Jump to message
  const jumpToMessage = (messageId: number) => {
    // Find message in current messages
    const messageElement = document.getElementById(`message-${messageId}`);
    if (messageElement) {
      messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      // Highlight message briefly
      messageElement.classList.add('bg-accent-red', 'bg-opacity-20');
      setTimeout(() => {
        messageElement.classList.remove('bg-accent-red', 'bg-opacity-20');
      }, 2000);
    }
    setShowSearch(false);
    setMessageSearchQuery('');
    setSearchResults([]);
  };

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Load conversations on mount
  useEffect(() => {
    loadConversations();
  }, []);

  // Load messages when conversation changes
  useEffect(() => {
    if (selectedConversation) {
      loadMessages(selectedConversation.conversation_id);
      
      // Join conversation room for WebSocket
      if (isConnected) {
        joinConversation(selectedConversation.conversation_id);
      }
    }

    // Leave previous conversation
    return () => {
      if (selectedConversation && isConnected) {
        leaveConversation(selectedConversation.conversation_id);
      }
    };
  }, [selectedConversation, isConnected, joinConversation, leaveConversation]);

  // Filtered conversations
  const filteredConversations = conversations.filter((conv) => {
    const participant = getParticipantInfo(conv);
    return participant.name.toLowerCase().includes(searchQuery.toLowerCase());
  });

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-red mx-auto mb-4"></div>
          <p className="text-text-secondary">Loading conversations...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex bg-background-secondary">
      {/* Chat List */}
      <div className="w-1/3 bg-white border-r border-primary-200 flex flex-col">
        {/* Search */}
        <div className="p-4 border-b border-primary-200">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-text-secondary" />
            <input
              type="text"
              placeholder="Search conversations..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-background-secondary rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
            />
          </div>
        </div>

        {/* Conversation List */}
        <div className="flex-1 overflow-y-auto">
          {filteredConversations.map((conversation) => {
            const participant = getParticipantInfo(conversation);
            return (
              <div
                key={conversation.conversation_id}
                onClick={() => setSelectedConversation(conversation)}
                className={`
                  p-4 border-b border-primary-100 cursor-pointer transition-colors
                  ${selectedConversation?.conversation_id === conversation.conversation_id 
                    ? 'bg-accent-red bg-opacity-5' 
                    : 'hover:bg-background-secondary'}
                `}
              >
                <div className="flex items-start gap-3">
                  <div className="relative">
                    <div className="w-10 h-10 bg-accent-red bg-opacity-10 rounded-full flex items-center justify-center">
                      <span className="text-lg font-medium text-accent-red">
                        {participant.name.charAt(0)}
                      </span>
                    </div>
                    {participant.isOnline && (
                      <Circle className="absolute bottom-0 right-0 w-3 h-3 text-accent-green fill-current" />
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <h3 className="font-medium text-text-primary truncate">
                        {participant.name}
                      </h3>
                      {conversation.last_message_at && (
                        <span className="text-xs text-text-secondary">
                          {format(new Date(conversation.last_message_at), 'HH:mm')}
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-text-secondary truncate">
                      {conversation.last_message_content || 
                        (participant.isOnline ? 'Online' : 'Offline')}
                    </p>
                    {conversation.unread_count > 0 && (
                      <span className="inline-block mt-1 bg-accent-red text-white text-xs rounded-full px-2 py-0.5">
                        {conversation.unread_count} new
                      </span>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Chat View */}
      {selectedConversation ? (
        <div className="flex-1 flex flex-col">
          {/* Header */}
          <div className="bg-white p-4 border-b border-primary-200 shadow-sm">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="relative">
                  <div className="w-10 h-10 bg-accent-red bg-opacity-10 rounded-full flex items-center justify-center">
                    <span className="text-lg font-medium text-accent-red">
                      {getParticipantInfo(selectedConversation).name.charAt(0)}
                    </span>
                  </div>
                  {getParticipantInfo(selectedConversation).isOnline && (
                    <Circle className="absolute bottom-0 right-0 w-3 h-3 text-accent-green fill-current" />
                  )}
                </div>
                <div>
                  <h2 className="font-semibold text-text-primary">
                    {getParticipantInfo(selectedConversation).name}
                  </h2>
                  <p className="text-sm text-text-secondary">
                    {getParticipantInfo(selectedConversation).isOnline ? 'Active now' : 'Offline'}
                  </p>
                </div>
              </div>
              <button 
                onClick={() => setShowSearch(!showSearch)}
                className="p-2 hover:bg-background-secondary rounded-medium transition-colors"
              >
                <Search className="w-5 h-5 text-text-secondary" />
              </button>
              <button className="p-2 hover:bg-background-secondary rounded-medium transition-colors">
                <MoreVertical className="w-5 h-5 text-text-secondary" />
              </button>
            </div>
          </div>

          {/* Search Panel */}
          {showSearch && (
            <div className="bg-white border-b border-primary-200 p-4">
              <div className="flex items-center gap-3">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-text-secondary" />
                  <input
                    type="text"
                    placeholder="Search messages..."
                    value={messageSearchQuery}
                    onChange={handleMessageSearchChange}
                    className="w-full pl-10 pr-4 py-2 bg-background-secondary rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                  />
                  {isSearching && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-accent-red" />
                    </div>
                  )}
                </div>
                <button
                  onClick={() => {
                    setShowSearch(false);
                    setMessageSearchQuery('');
                    setSearchResults([]);
                  }}
                  className="p-2 hover:bg-background-secondary rounded-medium transition-colors"
                >
                  <X className="w-5 h-5 text-text-secondary" />
                </button>
              </div>
              
              {/* Search Results */}
              {searchResults.length > 0 && (
                <div className="mt-3 space-y-2 max-h-48 overflow-y-auto">
                  {searchResults.map((result) => (
                    <div
                      key={result.message_id}
                      onClick={() => jumpToMessage(result.message_id)}
                      className="p-2 bg-background-secondary rounded-soft cursor-pointer hover:bg-primary-100 transition-colors"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <p className="text-sm font-medium text-text-primary">
                            {result.sender_name}
                          </p>
                          <p className="text-xs text-text-secondary line-clamp-2">
                            {result.content}
                          </p>
                        </div>
                        <span className="text-xs text-text-secondary">
                          {format(new Date(result.created_at), 'MMM d, HH:mm')}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
              
              {messageSearchQuery && searchResults.length === 0 && !isSearching && (
                <p className="mt-3 text-sm text-text-secondary text-center">
                  No messages found
                </p>
              )}
            </div>
          )}

          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.map((message) => (
              <div
                key={message.message_id}
                id={`message-${message.message_id}`}
                className={`flex ${
                  message.sender_id === user?.userId ? 'justify-end' : 'justify-start'
                }`}
              >
                <div className={`max-w-[70%] ${
                  message.sender_id === user?.userId ? 'items-end' : 'items-start'
                }`}>
                  {message.sender_id !== user?.userId && (
                    <p className={`text-xs font-medium mb-1 ${getRoleColor(message.sender_type)}`}>
                      {message.sender_name}
                    </p>
                  )}
                  <div
                    className={`
                      px-4 py-2 rounded-large inline-block
                      ${message.sender_id === user?.userId
                        ? 'bg-accent-red text-white' 
                        : 'bg-white text-text-primary shadow-sm'
                      }
                    `}
                  >
                    {message.message_type === 'file' ? (
                      <div className="flex items-center gap-2">
                        {getFileIcon(message.file_name)}
                        <div className="flex-1">
                          <p className="text-sm font-medium">{message.file_name || 'File'}</p>
                          <p className="text-xs opacity-75">{message.content}</p>
                        </div>
                        {message.file_url && (
                          <a
                            href={message.file_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="p-1 hover:bg-white hover:bg-opacity-20 rounded"
                          >
                            <Download className="w-4 h-4" />
                          </a>
                        )}
                      </div>
                    ) : (
                      <p className="text-sm">{message.content}</p>
                    )}
                  </div>
                  <div className={`flex items-center gap-1 mt-1 ${
                    message.sender_id === user?.userId ? 'justify-end' : ''
                  }`}>
                    <span className="text-xs text-text-secondary">
                      {format(new Date(message.created_at), 'HH:mm')}
                    </span>
                    {message.sender_id === user?.userId && (
                      <>
                        {message.status === 'sent' && <CheckCircle className="w-3 h-3 text-text-secondary" />}
                        {message.status === 'delivered' && <CheckCircle className="w-3 h-3 text-accent-green" />}
                        {message.status === 'read' && <CheckCircle className="w-3 h-3 text-accent-red" />}
                        {message.status === 'failed' && <AlertCircle className="w-3 h-3 text-accent-red" />}
                      </>
                    )}
                  </div>
                </div>
              </div>
            ))}
            
            {/* Typing indicators */}
            {typingUsers.size > 0 && (
              <div className="flex items-center gap-2 text-text-secondary">
                <div className="flex gap-1">
                  <div className="w-2 h-2 bg-text-secondary rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                  <div className="w-2 h-2 bg-text-secondary rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                  <div className="w-2 h-2 bg-text-secondary rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                </div>
                <span className="text-sm">
                  {Array.from(typingUsers.values()).join(', ')} {typingUsers.size === 1 ? 'is' : 'are'} typing...
                </span>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>

          {/* Message Input */}
          <div className="bg-white p-4 border-t border-primary-200">
            <div className="flex items-end gap-3">
              <input
                ref={fileInputRef}
                type="file"
                onChange={handleFileSelect}
                accept="image/*,.pdf,.doc,.docx"
                className="hidden"
              />
              <button 
                onClick={() => fileInputRef.current?.click()}
                disabled={uploadingFile}
                className={`p-2 rounded-medium transition-colors ${
                  uploadingFile 
                    ? 'bg-primary-200 text-text-secondary cursor-not-allowed' 
                    : 'hover:bg-background-secondary'
                }`}
              >
                {uploadingFile ? (
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-accent-red" />
                ) : (
                  <Paperclip className="w-5 h-5 text-text-secondary" />
                )}
              </button>
              <div className="flex-1 relative">
                <textarea
                  value={messageText}
                  onChange={(e) => {
                    setMessageText(e.target.value);
                    handleTyping();
                  }}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      sendMessage();
                    }
                  }}
                  placeholder="Type a message..."
                  className="w-full px-4 py-2 bg-background-secondary rounded-soft resize-none focus:outline-none focus:ring-2 focus:ring-accent-red"
                  rows={1}
                />
              </div>
              <button className="p-2 hover:bg-background-secondary rounded-medium transition-colors">
                <Smile className="w-5 h-5 text-text-secondary" />
              </button>
              <button
                onClick={sendMessage}
                disabled={!messageText.trim() || isSending}
                className={`
                  p-2 rounded-soft transition-colors
                  ${messageText.trim() && !isSending
                    ? 'bg-accent-red text-white hover:bg-accent-red-dark'
                    : 'bg-primary-200 text-text-secondary cursor-not-allowed'
                  }
                `}
              >
                <Send className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      ) : (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <Users className="w-12 h-12 text-text-secondary mx-auto mb-3" />
            <p className="text-text-secondary">Select a conversation to start messaging</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default InAppChatUpdated;