"""
Automated reminder service for appointments and other scheduled events.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from enum import Enum

from app.database.repositories.appointment_repository import AppointmentRepository
from app.database.repositories.reminder_repository import ReminderRepository
from app.services.notification_orchestrator import NotificationOrchestrator
from app.models.notification_models import NotificationType, NotificationPriority
from app.models.appointment_models import AppointmentStatus
from app.core.exceptions import BusinessLogicError
from celery import Celery
from app.config.settings import settings

logger = logging.getLogger(__name__)

# Initialize Celery with Redis URL
redis_url = f"redis://{settings.REDIS_HOST}:{settings.REDIS_PORT}/0"
if settings.REDIS_PASSWORD:
    redis_url = f"redis://:{settings.REDIS_PASSWORD}@{settings.REDIS_HOST}:{settings.REDIS_PORT}/0"
celery_app = Celery('reminders', broker=redis_url)


class ReminderType(Enum):
    """Types of automated reminders."""
    APPOINTMENT_24H = "appointment_24h"
    APPOINTMENT_2H = "appointment_2h"
    APPOINTMENT_30MIN = "appointment_30min"
    PAYMENT_DUE = "payment_due"
    SUBSCRIPTION_EXPIRY = "subscription_expiry"
    TUTOR_CONFIRMATION = "tutor_confirmation"
    SESSION_COMPLETION = "session_completion"


class AutomatedReminderService:
    """
    Service for managing automated reminders.
    
    Features:
    - 24-hour appointment reminders
    - 2-hour appointment reminders
    - 30-minute appointment reminders
    - Payment due reminders
    - Subscription expiry reminders
    - Tutor confirmation reminders
    - Session completion reminders
    - Batch processing for efficiency
    - Duplicate prevention
    """
    
    def __init__(self):
        self.appointment_repo = AppointmentRepository()
        self.reminder_repo = ReminderRepository()
        self.notification_orchestrator = NotificationOrchestrator()
        
        # Reminder configuration
        self.reminder_config = {
            ReminderType.APPOINTMENT_24H: {
                'advance_hours': 24,
                'notification_type': NotificationType.APPOINTMENT_REMINDER,
                'priority': NotificationPriority.NORMAL,
                'template': 'appointment_reminder_24h'
            },
            ReminderType.APPOINTMENT_2H: {
                'advance_hours': 2,
                'notification_type': NotificationType.APPOINTMENT_REMINDER,
                'priority': NotificationPriority.HIGH,
                'template': 'appointment_reminder_2h'
            },
            ReminderType.APPOINTMENT_30MIN: {
                'advance_hours': 0.5,
                'notification_type': NotificationType.APPOINTMENT_REMINDER,
                'priority': NotificationPriority.URGENT,
                'template': 'appointment_reminder_30min'
            },
            ReminderType.TUTOR_CONFIRMATION: {
                'advance_hours': 48,
                'notification_type': NotificationType.APPOINTMENT_CONFIRMATION,
                'priority': NotificationPriority.HIGH,
                'template': 'tutor_confirmation_request'
            },
            ReminderType.SESSION_COMPLETION: {
                'advance_hours': -0.5,  # 30 minutes after
                'notification_type': NotificationType.SESSION_COMPLETION,
                'priority': NotificationPriority.NORMAL,
                'template': 'session_completion_request'
            }
        }
    
    async def schedule_appointment_reminders(
        self,
        appointment_id: int,
        appointment_datetime: datetime,
        tutor_id: int,
        client_id: int,
        dependant_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Schedule all reminders for an appointment.
        
        Args:
            appointment_id: Appointment ID
            appointment_datetime: Appointment start time
            tutor_id: Tutor user ID
            client_id: Client user ID
            dependant_id: Optional dependant ID
            
        Returns:
            Scheduled reminder details
        """
        try:
            scheduled_reminders = []
            
            # Schedule 24-hour reminder
            reminder_24h = await self._schedule_reminder(
                appointment_id=appointment_id,
                reminder_type=ReminderType.APPOINTMENT_24H,
                appointment_datetime=appointment_datetime,
                tutor_id=tutor_id,
                client_id=client_id,
                dependant_id=dependant_id
            )
            if reminder_24h:
                scheduled_reminders.append(reminder_24h)
            
            # Schedule 2-hour reminder
            reminder_2h = await self._schedule_reminder(
                appointment_id=appointment_id,
                reminder_type=ReminderType.APPOINTMENT_2H,
                appointment_datetime=appointment_datetime,
                tutor_id=tutor_id,
                client_id=client_id,
                dependant_id=dependant_id
            )
            if reminder_2h:
                scheduled_reminders.append(reminder_2h)
            
            # Schedule 30-minute reminder
            reminder_30min = await self._schedule_reminder(
                appointment_id=appointment_id,
                reminder_type=ReminderType.APPOINTMENT_30MIN,
                appointment_datetime=appointment_datetime,
                tutor_id=tutor_id,
                client_id=client_id,
                dependant_id=dependant_id
            )
            if reminder_30min:
                scheduled_reminders.append(reminder_30min)
            
            # Schedule tutor confirmation reminder (48 hours before)
            confirmation_reminder = await self._schedule_reminder(
                appointment_id=appointment_id,
                reminder_type=ReminderType.TUTOR_CONFIRMATION,
                appointment_datetime=appointment_datetime,
                tutor_id=tutor_id,
                client_id=None,  # Only for tutor
                dependant_id=None
            )
            if confirmation_reminder:
                scheduled_reminders.append(confirmation_reminder)
            
            # Schedule session completion reminder (30 minutes after)
            completion_reminder = await self._schedule_reminder(
                appointment_id=appointment_id,
                reminder_type=ReminderType.SESSION_COMPLETION,
                appointment_datetime=appointment_datetime,
                tutor_id=tutor_id,
                client_id=client_id,
                dependant_id=dependant_id
            )
            if completion_reminder:
                scheduled_reminders.append(completion_reminder)
            
            logger.info(f"Scheduled {len(scheduled_reminders)} reminders for appointment {appointment_id}")
            
            return {
                'appointment_id': appointment_id,
                'scheduled_reminders': len(scheduled_reminders),
                'reminders': scheduled_reminders
            }
            
        except Exception as e:
            logger.error(f"Error scheduling appointment reminders: {e}")
            raise BusinessLogicError(f"Failed to schedule reminders: {str(e)}")
    
    async def cancel_appointment_reminders(
        self,
        appointment_id: int
    ) -> bool:
        """
        Cancel all reminders for a cancelled appointment.
        
        Args:
            appointment_id: Appointment ID
            
        Returns:
            Success status
        """
        try:
            # Get pending reminders
            reminders = await self.reminder_repo.get_pending_reminders_for_appointment(
                appointment_id
            )
            
            cancelled_count = 0
            for reminder in reminders:
                # Cancel Celery task if it exists
                if reminder.celery_task_id:
                    celery_app.control.revoke(reminder.celery_task_id, terminate=True)
                
                # Mark as cancelled
                await self.reminder_repo.cancel_reminder(reminder.reminder_id)
                cancelled_count += 1
            
            logger.info(f"Cancelled {cancelled_count} reminders for appointment {appointment_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error cancelling appointment reminders: {e}")
            return False
    
    async def process_due_reminders(
        self,
        check_time: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        Process reminders that are due to be sent.
        
        Args:
            check_time: Time to check for due reminders (defaults to now)
            
        Returns:
            Processing results
        """
        try:
            if not check_time:
                check_time = datetime.now()
            
            # Get due reminders
            due_reminders = await self.reminder_repo.get_due_reminders(check_time)
            
            results = {
                'processed': 0,
                'successful': 0,
                'failed': 0,
                'errors': []
            }
            
            for reminder in due_reminders:
                results['processed'] += 1
                
                try:
                    # Send reminder
                    await self._send_reminder(reminder)
                    
                    # Mark as sent
                    await self.reminder_repo.mark_reminder_sent(
                        reminder.reminder_id,
                        sent_at=check_time
                    )
                    
                    results['successful'] += 1
                    
                except Exception as e:
                    logger.error(f"Failed to send reminder {reminder.reminder_id}: {e}")
                    
                    # Mark as failed
                    await self.reminder_repo.mark_reminder_failed(
                        reminder.reminder_id,
                        error_message=str(e)
                    )
                    
                    results['failed'] += 1
                    results['errors'].append({
                        'reminder_id': reminder.reminder_id,
                        'error': str(e)
                    })
            
            if results['processed'] > 0:
                logger.info(f"Processed {results['processed']} due reminders: {results['successful']} successful, {results['failed']} failed")
            
            return results
            
        except Exception as e:
            logger.error(f"Error processing due reminders: {e}")
            raise BusinessLogicError(f"Failed to process reminders: {str(e)}")
    
    async def get_reminder_statistics(
        self,
        date_range: Optional[tuple] = None
    ) -> Dict[str, Any]:
        """
        Get reminder statistics.
        
        Args:
            date_range: Optional date range (start, end)
            
        Returns:
            Reminder statistics
        """
        try:
            if not date_range:
                end_date = datetime.now()
                start_date = end_date - timedelta(days=30)
                date_range = (start_date, end_date)
            
            stats = await self.reminder_repo.get_reminder_statistics(date_range)
            
            return {
                'period': {
                    'start': date_range[0],
                    'end': date_range[1]
                },
                'total_scheduled': stats.get('total_scheduled', 0),
                'sent': stats.get('sent', 0),
                'failed': stats.get('failed', 0),
                'cancelled': stats.get('cancelled', 0),
                'pending': stats.get('pending', 0),
                'by_type': stats.get('by_type', {}),
                'delivery_rate': stats.get('delivery_rate', 0.0),
                'response_rate': stats.get('response_rate', 0.0)
            }
            
        except Exception as e:
            logger.error(f"Error getting reminder statistics: {e}")
            raise
    
    async def reschedule_appointment_reminders(
        self,
        appointment_id: int,
        old_datetime: datetime,
        new_datetime: datetime,
        tutor_id: int,
        client_id: int,
        dependant_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Reschedule reminders when appointment time changes.
        
        Args:
            appointment_id: Appointment ID
            old_datetime: Previous appointment time
            new_datetime: New appointment time
            tutor_id: Tutor user ID
            client_id: Client user ID
            dependant_id: Optional dependant ID
            
        Returns:
            Rescheduling results
        """
        try:
            # Cancel existing reminders
            await self.cancel_appointment_reminders(appointment_id)
            
            # Schedule new reminders
            result = await self.schedule_appointment_reminders(
                appointment_id=appointment_id,
                appointment_datetime=new_datetime,
                tutor_id=tutor_id,
                client_id=client_id,
                dependant_id=dependant_id
            )
            
            logger.info(f"Rescheduled reminders for appointment {appointment_id}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error rescheduling reminders: {e}")
            raise BusinessLogicError(f"Failed to reschedule reminders: {str(e)}")
    
    async def _schedule_reminder(
        self,
        appointment_id: int,
        reminder_type: ReminderType,
        appointment_datetime: datetime,
        tutor_id: int,
        client_id: Optional[int],
        dependant_id: Optional[int]
    ) -> Optional[Dict[str, Any]]:
        """Schedule a specific reminder."""
        try:
            config = self.reminder_config[reminder_type]
            
            # Calculate reminder time
            reminder_time = appointment_datetime - timedelta(hours=config['advance_hours'])
            
            # Don't schedule if reminder time is in the past
            if reminder_time <= datetime.now():
                logger.warning(f"Skipping {reminder_type.value} reminder for appointment {appointment_id} - time in past")
                return None
            
            # Determine recipients
            recipients = []
            if tutor_id:
                recipients.append(tutor_id)
            if client_id:
                recipients.append(client_id)
            
            if not recipients:
                logger.warning(f"No recipients for {reminder_type.value} reminder")
                return None
            
            # Create reminder record
            reminder = await self.reminder_repo.create_reminder(
                appointment_id=appointment_id,
                reminder_type=reminder_type.value,
                scheduled_for=reminder_time,
                recipients=recipients,
                metadata={
                    'appointment_datetime': appointment_datetime.isoformat(),
                    'dependant_id': dependant_id,
                    'template': config['template']
                }
            )
            
            # Schedule Celery task
            task = send_reminder_task.apply_async(
                args=[reminder.reminder_id],
                eta=reminder_time
            )
            
            # Update reminder with task ID
            await self.reminder_repo.update_reminder_task_id(
                reminder.reminder_id,
                task.id
            )
            
            return {
                'reminder_id': reminder.reminder_id,
                'type': reminder_type.value,
                'scheduled_for': reminder_time,
                'recipients': recipients,
                'task_id': task.id
            }
            
        except Exception as e:
            logger.error(f"Error scheduling {reminder_type.value} reminder: {e}")
            return None
    
    async def _send_reminder(self, reminder) -> None:
        """Send a reminder notification."""
        try:
            # Get appointment details
            appointment = await self.appointment_repo.get_appointment(
                reminder.appointment_id
            )
            
            if not appointment:
                raise BusinessLogicError(f"Appointment {reminder.appointment_id} not found")
            
            # Skip if appointment is cancelled
            if appointment.status == AppointmentStatus.CANCELLED:
                logger.info(f"Skipping reminder for cancelled appointment {appointment.appointment_id}")
                return
            
            # Get reminder configuration
            reminder_type = ReminderType(reminder.reminder_type)
            config = self.reminder_config[reminder_type]
            
            # Build notification data
            template_variables = {
                'appointment_id': str(appointment.appointment_id),
                'appointment_date': appointment.start_time.strftime('%Y-%m-%d'),
                'appointment_time': appointment.start_time.strftime('%H:%M'),
                'tutor_name': appointment.tutor_name or 'Your tutor',
                'client_name': appointment.client_name or 'Client',
                'subject': appointment.subject or 'Tutoring session',
                'session_type': appointment.session_type.value if appointment.session_type else 'Session',
                'location': appointment.location or 'TBD'
            }
            
            # Add dependant name if applicable
            if reminder.metadata and reminder.metadata.get('dependant_id'):
                # Get dependant info from appointment or database
                template_variables['student_name'] = appointment.dependant_name or 'Student'
            
            # Generate appropriate title and message based on reminder type
            if reminder_type == ReminderType.APPOINTMENT_24H:
                title = f"Appointment Reminder - Tomorrow at {template_variables['appointment_time']}"
                message = f"You have a {template_variables['subject']} session tomorrow at {template_variables['appointment_time']} with {template_variables['tutor_name']}."
            
            elif reminder_type == ReminderType.APPOINTMENT_2H:
                title = f"Appointment Starting Soon - {template_variables['appointment_time']}"
                message = f"Your {template_variables['subject']} session starts in 2 hours at {template_variables['appointment_time']}."
            
            elif reminder_type == ReminderType.APPOINTMENT_30MIN:
                title = f"Appointment Starting Now - {template_variables['appointment_time']}"
                message = f"Your {template_variables['subject']} session starts in 30 minutes. Please prepare to join."
            
            elif reminder_type == ReminderType.TUTOR_CONFIRMATION:
                title = "Please Confirm Your Upcoming Session"
                message = f"Please confirm your {template_variables['subject']} session on {template_variables['appointment_date']} at {template_variables['appointment_time']}. Reply YES to confirm."
            
            elif reminder_type == ReminderType.SESSION_COMPLETION:
                title = "Session Completion Confirmation"
                message = f"Please confirm that your {template_variables['subject']} session has been completed. How did it go?"
            
            else:
                title = "TutorAide Reminder"
                message = "You have an upcoming appointment."
            
            # Send notification to each recipient
            for user_id in reminder.recipients:
                await self.notification_orchestrator.send_notification(
                    user_id=user_id,
                    notification_type=config['notification_type'],
                    title=title,
                    message=message,
                    data={
                        'appointment_id': reminder.appointment_id,
                        'reminder_type': reminder_type.value,
                        'reminder_id': reminder.reminder_id
                    },
                    priority=config['priority'],
                    template_name=config['template'],
                    template_variables=template_variables,
                    idempotency_key=f"reminder_{reminder.reminder_id}_{user_id}"
                )
            
            logger.info(f"Sent {reminder_type.value} reminder for appointment {reminder.appointment_id}")
            
        except Exception as e:
            logger.error(f"Error sending reminder {reminder.reminder_id}: {e}")
            raise


# Celery tasks
@celery_app.task(bind=True, max_retries=3)
def send_reminder_task(self, reminder_id: int):
    """Celery task to send a scheduled reminder."""
    reminder_service = AutomatedReminderService()
    
    try:
        # Run async function in sync context
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # Get reminder
        reminder = loop.run_until_complete(
            reminder_service.reminder_repo.get_reminder(reminder_id)
        )
        
        if not reminder:
            logger.error(f"Reminder {reminder_id} not found")
            return
        
        # Send reminder
        loop.run_until_complete(
            reminder_service._send_reminder(reminder)
        )
        
        # Mark as sent
        loop.run_until_complete(
            reminder_service.reminder_repo.mark_reminder_sent(
                reminder_id,
                sent_at=datetime.now()
            )
        )
        
        return f"Reminder {reminder_id} sent successfully"
        
    except Exception as e:
        logger.error(f"Reminder task failed: {e}")
        
        # Mark as failed
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(
            reminder_service.reminder_repo.mark_reminder_failed(
                reminder_id,
                error_message=str(e)
            )
        )
        
        # Retry with exponential backoff
        retry_in = 60 * (2 ** self.request.retries)
        raise self.retry(exc=e, countdown=retry_in)


@celery_app.task
def process_due_reminders_task():
    """Celery task to process all due reminders."""
    reminder_service = AutomatedReminderService()
    
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        result = loop.run_until_complete(
            reminder_service.process_due_reminders()
        )
        
        return f"Processed {result['processed']} reminders: {result['successful']} successful, {result['failed']} failed"
        
    except Exception as e:
        logger.error(f"Due reminders task failed: {e}")
        return f"Error: {str(e)}"


# Schedule periodic task to process due reminders
celery_app.conf.beat_schedule = {
    'process-due-reminders': {
        'task': 'app.services.automated_reminder_service.process_due_reminders_task',
        'schedule': 60.0,  # Run every minute
    },
}