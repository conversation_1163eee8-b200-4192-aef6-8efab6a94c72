"""
Comprehensive input validation and sanitization framework.
Provides centralized validation, sanitization, and security controls.
"""

import html
import re
import json
from typing import Any, Dict, List, Optional, Union, Callable
from functools import wraps
from decimal import Decimal, InvalidOperation
from datetime import datetime, date
from pydantic import ValidationError
from fastapi import HTT<PERSON>Exception, status
from app.core.logging import TutorAideLogger


class InputSanitizer:
    """Centralized input sanitization for security."""
    
    # Allowed HTML tags for rich text fields (very restrictive)
    ALLOWED_HTML_TAGS = ['p', 'br', 'strong', 'em', 'u', 'ol', 'ul', 'li']
    ALLOWED_HTML_ATTRIBUTES = {}
    
    # Common malicious patterns
    XSS_PATTERNS = [
        r'<script[^>]*>.*?</script>',
        r'javascript:',
        r'vbscript:',
        r'onload\s*=',
        r'onerror\s*=',
        r'onclick\s*=',
        r'onmouseover\s*=',
        r'<iframe[^>]*>.*?</iframe>',
        r'<object[^>]*>.*?</object>',
        r'<embed[^>]*>.*?</embed>',
    ]
    
    # SQL injection patterns - more specific to avoid false positives
    SQL_INJECTION_PATTERNS = [
        r"(--\s|/\*|\*/|;\s*(\bDROP\b|\bUNION\b|\bSELECT\b))",  # Require space after -- and ;
        r"(\b(union|select|insert|delete|update|drop|create|alter|exec|execute)\s+\w+\s+(from|into|table))",  # More specific SQL patterns
        r"(\bscript\s*>|\bjavascript\s*:|\bvbscript\s*:)",  # More specific script patterns
    ]
    
    @classmethod
    def sanitize_text(cls, text: str, allow_html: bool = False) -> str:
        """Sanitize text input for XSS prevention."""
        if not isinstance(text, str):
            return str(text)
        
        # Remove null bytes
        text = text.replace('\x00', '')
        
        if allow_html:
            # Basic HTML sanitization (bleach not available, use simple approach)
            # Remove script tags and dangerous attributes
            for pattern in cls.XSS_PATTERNS:
                text = re.sub(pattern, '', text, flags=re.IGNORECASE)
        else:
            # HTML encode for plain text
            text = html.escape(text)
        
        return text.strip()
    
    @classmethod
    def sanitize_json(cls, data: Dict[str, Any]) -> Dict[str, Any]:
        """Recursively sanitize JSON data."""
        if isinstance(data, dict):
            return {key: cls.sanitize_json(value) for key, value in data.items()}
        elif isinstance(data, list):
            return [cls.sanitize_json(item) for item in data]
        elif isinstance(data, str):
            return cls.sanitize_text(data)
        else:
            return data
    
    @classmethod
    def check_malicious_patterns(cls, text: str) -> List[str]:
        """Check for malicious patterns in input."""
        issues = []
        text_lower = text.lower()
        
        # Check XSS patterns
        for pattern in cls.XSS_PATTERNS:
            if re.search(pattern, text_lower, re.IGNORECASE):
                issues.append(f"Potential XSS detected: {pattern}")
        
        # Check SQL injection patterns
        for pattern in cls.SQL_INJECTION_PATTERNS:
            if re.search(pattern, text_lower, re.IGNORECASE):
                issues.append(f"Potential SQL injection detected: {pattern}")
        
        return issues
    
    @classmethod
    def normalize_phone(cls, phone: str) -> str:
        """Normalize phone number to Canadian format."""
        if not phone:
            return phone
        
        # Remove all non-digits
        digits = re.sub(r'\D', '', phone)
        
        # Validate and format Canadian phone number
        if len(digits) == 10:
            return f"+1{digits}"
        elif len(digits) == 11 and digits.startswith('1'):
            return f"+{digits}"
        else:
            raise ValueError(f"Invalid phone number format: {phone}")
    
    @classmethod
    def normalize_postal_code(cls, postal_code: str) -> str:
        """Normalize Canadian postal code."""
        if not postal_code:
            return postal_code
        
        # Remove spaces and convert to uppercase
        normalized = re.sub(r'\s+', '', postal_code.upper())
        
        # Validate Canadian postal code format
        if not re.match(r'^[A-Z]\d[A-Z]\d[A-Z]\d$', normalized):
            raise ValueError(f"Invalid Canadian postal code format: {postal_code}")
        
        # Format with space in middle
        return f"{normalized[:3]} {normalized[3:]}"
    
    @classmethod
    def validate_file_upload(cls, filename: str, content: bytes, 
                           allowed_types: List[str], max_size: int) -> Dict[str, Any]:
        """Validate file upload for security."""
        issues = []
        
        # Check file size
        if len(content) > max_size:
            issues.append(f"File size {len(content)} exceeds maximum {max_size} bytes")
        
        # Check filename for malicious patterns
        if '..' in filename or '/' in filename or '\\' in filename:
            issues.append("Filename contains path traversal characters")
        
        # Check file extension
        file_ext = filename.lower().split('.')[-1] if '.' in filename else ''
        if file_ext not in [t.lower() for t in allowed_types]:
            issues.append(f"File type '{file_ext}' not allowed. Allowed: {allowed_types}")
        
        # Check for executable extensions
        dangerous_exts = ['exe', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbs', 'js', 'jar']
        if file_ext in dangerous_exts:
            issues.append(f"Dangerous file extension: {file_ext}")
        
        # Basic content validation
        if file_ext in ['jpg', 'jpeg', 'png', 'gif'] and not content.startswith((b'\xff\xd8', b'\x89PNG', b'GIF8')):
            issues.append("File content doesn't match declared image type")
        
        return {
            'is_valid': len(issues) == 0,
            'issues': issues,
            'filename': filename,
            'size': len(content),
            'extension': file_ext
        }


class DataValidator:
    """Enhanced data validation with business rules."""
    
    logger = TutorAideLogger.get_logger(__name__)
    
    @classmethod
    def validate_name(cls, name: str, field_name: str = "name") -> str:
        """Validate and sanitize name fields."""
        if not name or not name.strip():
            raise ValueError(f"{field_name} cannot be empty")
        
        name = name.strip()
        
        # Check for malicious patterns before processing
        issues = InputSanitizer.check_malicious_patterns(name)
        if issues:
            cls.logger.warning(f"Malicious input detected in {field_name}: {issues}")
            raise ValueError(f"Invalid content detected in {field_name}")
        
        # Check length
        if len(name) < 1 or len(name) > 100:
            raise ValueError(f"{field_name} must be between 1 and 100 characters")
        
        # Check for valid characters (letters, spaces, hyphens, apostrophes)
        if not re.match(r"^[a-zA-Z\s\-']+$", name):
            raise ValueError(f"{field_name} contains invalid characters")
        
        return name
    
    @classmethod
    def validate_email(cls, email: str) -> str:
        """Validate email address."""
        if not email:
            raise ValueError("Email cannot be empty")
        
        email = email.strip().lower()
        
        # Basic email validation with regex
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            raise ValueError(f"Invalid email format: {email}")
        
        return email
    
    @classmethod
    def validate_text_field(cls, text: str, field_name: str, 
                           min_length: int = 0, max_length: int = 1000,
                           allow_html: bool = False) -> str:
        """Validate general text fields."""
        if text is None:
            text = ""
        
        text = InputSanitizer.sanitize_text(str(text), allow_html=allow_html)
        
        if len(text) < min_length:
            raise ValueError(f"{field_name} must be at least {min_length} characters")
        
        if len(text) > max_length:
            raise ValueError(f"{field_name} cannot exceed {max_length} characters")
        
        # Check for malicious patterns
        issues = InputSanitizer.check_malicious_patterns(text)
        if issues:
            cls.logger.warning(f"Malicious input detected in {field_name}: {issues}")
            raise ValueError(f"Invalid content detected in {field_name}")
        
        return text
    
    @classmethod
    def validate_numeric_field(cls, value: Union[int, float, str, Decimal], 
                              field_name: str, min_value: Optional[float] = None,
                              max_value: Optional[float] = None) -> Union[int, float, Decimal]:
        """Validate numeric fields."""
        if value is None:
            raise ValueError(f"{field_name} cannot be null")
        
        # Convert to appropriate numeric type
        try:
            if isinstance(value, str):
                # Sanitize string input
                value = InputSanitizer.sanitize_text(value)
                if '.' in value:
                    value = Decimal(value)
                else:
                    value = int(value)
            elif isinstance(value, float):
                value = Decimal(str(value))
        except (ValueError, InvalidOperation):
            raise ValueError(f"Invalid numeric value for {field_name}: {value}")
        
        # Validate range
        if min_value is not None and float(value) < min_value:
            raise ValueError(f"{field_name} must be at least {min_value}")
        
        if max_value is not None and float(value) > max_value:
            raise ValueError(f"{field_name} cannot exceed {max_value}")
        
        return value
    
    @classmethod
    def validate_date_field(cls, date_value: Union[str, date, datetime], 
                           field_name: str, allow_future: bool = True,
                           allow_past: bool = True) -> date:
        """Validate date fields."""
        if date_value is None:
            raise ValueError(f"{field_name} cannot be null")
        
        # Convert to date
        if isinstance(date_value, str):
            date_value = InputSanitizer.sanitize_text(date_value)
            try:
                date_value = datetime.fromisoformat(date_value).date()
            except ValueError:
                raise ValueError(f"Invalid date format for {field_name}: {date_value}")
        elif isinstance(date_value, datetime):
            date_value = date_value.date()
        
        today = date.today()
        
        # Validate date constraints
        if not allow_future and date_value > today:
            raise ValueError(f"{field_name} cannot be in the future")
        
        if not allow_past and date_value < today:
            raise ValueError(f"{field_name} cannot be in the past")
        
        return date_value
    
    @classmethod
    def validate_choice_field(cls, value: str, field_name: str, 
                             allowed_choices: List[str], case_sensitive: bool = False) -> str:
        """Validate choice/enum fields."""
        if not value:
            raise ValueError(f"{field_name} cannot be empty")
        
        value = InputSanitizer.sanitize_text(str(value))
        
        # Normalize case if needed
        if not case_sensitive:
            value = value.lower()
            allowed_choices = [choice.lower() for choice in allowed_choices]
        
        if value not in allowed_choices:
            raise ValueError(f"{field_name} must be one of: {', '.join(allowed_choices)}")
        
        return value


class ValidationDecorator:
    """Decorators for API endpoint validation."""
    
    logger = TutorAideLogger.get_logger(__name__)
    
    @staticmethod
    def validate_request_size(max_size: int = 1024 * 1024):  # 1MB default
        """Decorator to validate request payload size."""
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            async def wrapper(*args, **kwargs):
                # This would typically be handled by middleware
                # Implementation depends on FastAPI request handling
                return await func(*args, **kwargs)
            return wrapper
        return decorator
    
    @staticmethod
    def sanitize_query_params(func: Callable) -> Callable:
        """Decorator to sanitize query parameters."""
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Sanitize string query parameters
            for key, value in kwargs.items():
                if isinstance(value, str):
                    kwargs[key] = InputSanitizer.sanitize_text(value)
                    
                    # Check for malicious patterns
                    issues = InputSanitizer.check_malicious_patterns(value)
                    if issues:
                        ValidationDecorator.logger.warning(
                            f"Malicious query parameter detected: {key}={value}, issues: {issues}"
                        )
                        raise HTTPException(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            detail=f"Invalid query parameter: {key}"
                        )
            
            return await func(*args, **kwargs)
        return wrapper
    
    @staticmethod
    def validate_json_payload(func: Callable) -> Callable:
        """Decorator to validate and sanitize JSON payloads."""
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Find JSON payload in kwargs
            for key, value in kwargs.items():
                if isinstance(value, dict):
                    # Sanitize JSON data
                    kwargs[key] = InputSanitizer.sanitize_json(value)
                    
                    # Validate JSON size
                    json_str = json.dumps(value)
                    if len(json_str) > 100000:  # 100KB limit
                        raise HTTPException(
                            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                            detail="JSON payload too large"
                        )
            
            return await func(*args, **kwargs)
        return wrapper


class RateLimitValidator:
    """Rate limiting validation utilities."""
    
    logger = TutorAideLogger.get_logger(__name__)
    
    @classmethod
    def validate_rate_limit_key(cls, key: str) -> str:
        """Validate rate limit key format."""
        key = InputSanitizer.sanitize_text(key)
        if len(key) > 255:
            raise ValueError("Rate limit key too long")
        return key
    
    @classmethod
    def validate_rate_limit_window(cls, window_seconds: int) -> int:
        """Validate rate limit window."""
        return DataValidator.validate_numeric_field(
            window_seconds, "rate_limit_window", min_value=1, max_value=86400
        )
    
    @classmethod
    def validate_rate_limit_max_requests(cls, max_requests: int) -> int:
        """Validate maximum requests per window."""
        return DataValidator.validate_numeric_field(
            max_requests, "max_requests", min_value=1, max_value=10000
        )


class SecurityValidator:
    """Security-focused validation utilities."""
    
    logger = TutorAideLogger.get_logger(__name__)
    
    # Common passwords to reject
    COMMON_PASSWORDS = [
        'password', '123456', 'password123', 'admin', 'qwerty',
        'letmein', 'welcome', 'monkey', '1234567890', 'password1'
    ]
    
    @classmethod
    def validate_password(cls, password: str, min_length: int = 8) -> Dict[str, Any]:
        """Comprehensive password validation."""
        issues = []
        score = 0
        
        if not password:
            issues.append("Password cannot be empty")
            return {'is_valid': False, 'issues': issues, 'strength_score': 0}
        
        # Length check
        if len(password) < min_length:
            issues.append(f"Password must be at least {min_length} characters")
        else:
            score += min(len(password) - min_length + 1, 4)
        
        # Character variety checks
        if re.search(r'[a-z]', password):
            score += 1
        else:
            issues.append("Password must contain lowercase letters")
        
        if re.search(r'[A-Z]', password):
            score += 1
        else:
            issues.append("Password must contain uppercase letters")
        
        if re.search(r'\d', password):
            score += 1
        else:
            issues.append("Password must contain numbers")
        
        if re.search(r'[!@#$%^&*()_+\-=\[\]{};\'\"\\|,.<>\/?]', password):
            score += 2
        else:
            issues.append("Password should contain special characters")
        
        # Common password check
        if password.lower() in cls.COMMON_PASSWORDS:
            issues.append("Password is too common")
            score = max(0, score - 3)
        
        # Sequential characters check
        if re.search(r'(012|123|234|345|456|567|678|789|890|abc|bcd|cde)', password.lower()):
            issues.append("Password contains sequential characters")
            score = max(0, score - 1)
        
        return {
            'is_valid': len(issues) == 0,
            'issues': issues,
            'strength_score': min(score, 10),
            'strength_level': cls._get_strength_level(score)
        }
    
    @classmethod
    def _get_strength_level(cls, score: int) -> str:
        """Get password strength level from score."""
        if score < 3:
            return 'weak'
        elif score < 6:
            return 'fair'
        elif score < 8:
            return 'good'
        else:
            return 'strong'
    
    @classmethod
    def validate_file_content(cls, content: bytes, filename: str) -> Dict[str, Any]:
        """Validate file content for security issues."""
        issues = []
        
        # Check for executable headers
        executable_headers = [
            b'MZ',  # PE executable
            b'\x7fELF',  # ELF executable
            b'\xfe\xed\xfa',  # Mach-O executable
        ]
        
        for header in executable_headers:
            if content.startswith(header):
                issues.append("File appears to be an executable")
                break
        
        # Check for script patterns in content
        content_str = content.decode('utf-8', errors='ignore').lower()
        script_patterns = [
            '<script', 'javascript:', 'vbscript:', 'onload=', 'onerror='
        ]
        
        for pattern in script_patterns:
            if pattern in content_str:
                issues.append(f"File contains potentially malicious script: {pattern}")
        
        return {
            'is_valid': len(issues) == 0,
            'issues': issues
        }