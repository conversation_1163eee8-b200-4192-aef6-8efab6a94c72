import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Key, Plus, Trash2, Co<PERSON>, Eye, EyeOff, Clock,
  Shield, AlertCircle, CheckCircle, RefreshCw, Code
} from 'lucide-react';
import { format } from 'date-fns';

interface APIKey {
  id: string;
  name: string;
  key: string;
  prefix: string;
  permissions: string[];
  createdAt: Date;
  lastUsed?: Date;
  expiresAt?: Date;
  isActive: boolean;
}

interface Webhook {
  id: string;
  url: string;
  events: string[];
  secret: string;
  isActive: boolean;
  lastTriggered?: Date;
  failureCount: number;
}

// Mock data
const mockAPIKeys: APIKey[] = [
  {
    id: '1',
    name: 'Production API Key',
    key: 'sk_live_••••••••••••••••••••••••••••••••',
    prefix: 'sk_live',
    permissions: ['read', 'write', 'delete'],
    createdAt: new Date('2024-01-15'),
    lastUsed: new Date(Date.now() - 1000 * 60 * 30),
    isActive: true,
  },
  {
    id: '2',
    name: 'Test Integration Key',
    key: 'sk_test_••••••••••••••••••••••••••••••••',
    prefix: 'sk_test',
    permissions: ['read'],
    createdAt: new Date('2024-03-20'),
    lastUsed: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7),
    expiresAt: new Date(Date.now() + 1000 * 60 * 60 * 24 * 30),
    isActive: true,
  },
  {
    id: '3',
    name: 'Mobile App Key',
    key: 'sk_mobile_••••••••••••••••••••••••••••••••',
    prefix: 'sk_mobile',
    permissions: ['read', 'write'],
    createdAt: new Date('2024-02-10'),
    isActive: false,
  },
];

const mockWebhooks: Webhook[] = [
  {
    id: '1',
    url: 'https://api.example.com/webhooks/tutoraide',
    events: ['appointment.created', 'appointment.cancelled', 'payment.completed'],
    secret: 'whsec_••••••••••••••••',
    isActive: true,
    lastTriggered: new Date(Date.now() - 1000 * 60 * 45),
    failureCount: 0,
  },
  {
    id: '2',
    url: 'https://integration.partner.com/webhook',
    events: ['user.created', 'user.updated'],
    secret: 'whsec_••••••••••••••••',
    isActive: true,
    failureCount: 2,
  },
];

const availableEvents = [
  { category: 'Appointments', events: ['appointment.created', 'appointment.updated', 'appointment.cancelled', 'appointment.completed'] },
  { category: 'Users', events: ['user.created', 'user.updated', 'user.deleted', 'user.verified'] },
  { category: 'Payments', events: ['payment.created', 'payment.completed', 'payment.failed', 'invoice.created'] },
  { category: 'Sessions', events: ['session.started', 'session.completed', 'session.no_show'] },
];

const APISettings: React.FC = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<'keys' | 'webhooks' | 'docs'>('keys');
  const [apiKeys, setApiKeys] = useState<APIKey[]>(mockAPIKeys);
  const [webhooks, setWebhooks] = useState<Webhook[]>(mockWebhooks);
  const [showKey, setShowKey] = useState<string | null>(null);
  const [isCreatingKey, setIsCreatingKey] = useState(false);
  const [isCreatingWebhook, setIsCreatingWebhook] = useState(false);

  // Form state for new API key
  const [newKeyForm, setNewKeyForm] = useState({
    name: '',
    permissions: [] as string[],
    expiresIn: 'never' as 'never' | '30days' | '90days' | '1year',
  });

  // Form state for new webhook
  const [newWebhookForm, setNewWebhookForm] = useState({
    url: '',
    events: [] as string[],
  });

  const handleCreateAPIKey = () => {
    const newKey: APIKey = {
      id: Date.now().toString(),
      name: newKeyForm.name,
      key: `sk_live_${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`,
      prefix: 'sk_live',
      permissions: newKeyForm.permissions,
      createdAt: new Date(),
      isActive: true,
      expiresAt: newKeyForm.expiresIn === 'never' ? undefined : new Date(
        Date.now() + (
          newKeyForm.expiresIn === '30days' ? 30 * 24 * 60 * 60 * 1000 :
          newKeyForm.expiresIn === '90days' ? 90 * 24 * 60 * 60 * 1000 :
          365 * 24 * 60 * 60 * 1000
        )
      ),
    };

    setApiKeys([newKey, ...apiKeys]);
    setIsCreatingKey(false);
    setNewKeyForm({ name: '', permissions: [], expiresIn: 'never' });
    
    // Show the new key for copying
    setShowKey(newKey.id);
  };

  const handleCreateWebhook = () => {
    const newWebhook: Webhook = {
      id: Date.now().toString(),
      url: newWebhookForm.url,
      events: newWebhookForm.events,
      secret: `whsec_${Math.random().toString(36).substring(2, 20)}`,
      isActive: true,
      failureCount: 0,
    };

    setWebhooks([newWebhook, ...webhooks]);
    setIsCreatingWebhook(false);
    setNewWebhookForm({ url: '', events: [] });
  };

  const handleCopyKey = (key: string) => {
    navigator.clipboard.writeText(key);
  };

  const handleToggleKey = (keyId: string) => {
    setApiKeys(apiKeys.map(k => 
      k.id === keyId ? { ...k, isActive: !k.isActive } : k
    ));
  };

  const handleDeleteKey = (keyId: string) => {
    if (window.confirm('Are you sure you want to delete this API key? This action cannot be undone.')) {
      setApiKeys(apiKeys.filter(k => k.id !== keyId));
    }
  };

  const handleTestWebhook = async (webhookId: string) => {
    // Simulate webhook test
    console.log('Testing webhook:', webhookId);
  };

  return (
    <div className="p-6 max-w-6xl mx-auto">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-text-primary">API Settings</h1>
        <p className="text-text-secondary mt-1">Manage API keys and webhook configurations</p>
      </div>

      {/* Tabs */}
      <div className="flex items-center gap-6 border-b border-primary-200 mb-6">
        <button
          onClick={() => setActiveTab('keys')}
          className={`pb-3 px-1 font-medium transition-colors ${
            activeTab === 'keys'
              ? 'text-accent-red border-b-2 border-accent-red'
              : 'text-text-secondary hover:text-text-primary'
          }`}
        >
          API Keys
        </button>
        <button
          onClick={() => setActiveTab('webhooks')}
          className={`pb-3 px-1 font-medium transition-colors ${
            activeTab === 'webhooks'
              ? 'text-accent-red border-b-2 border-accent-red'
              : 'text-text-secondary hover:text-text-primary'
          }`}
        >
          Webhooks
        </button>
        <button
          onClick={() => setActiveTab('docs')}
          className={`pb-3 px-1 font-medium transition-colors ${
            activeTab === 'docs'
              ? 'text-accent-red border-b-2 border-accent-red'
              : 'text-text-secondary hover:text-text-primary'
          }`}
        >
          Documentation
        </button>
      </div>

      {activeTab === 'keys' && (
        <>
          {/* API Keys */}
          <div className="bg-white rounded-large shadow-soft p-6">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h2 className="text-lg font-semibold text-text-primary">API Keys</h2>
                <p className="text-sm text-text-secondary mt-1">
                  Create and manage API keys for accessing the TutorAide API
                </p>
              </div>
              <button
                onClick={() => setIsCreatingKey(true)}
                className="flex items-center gap-2 px-4 py-2 bg-accent-red text-white rounded-soft hover:bg-accent-red-dark"
              >
                <Plus className="w-4 h-4" />
                Create Key
              </button>
            </div>

            <div className="space-y-4">
              {apiKeys.map(apiKey => (
                <div
                  key={apiKey.id}
                  className={`p-4 border rounded-medium ${
                    apiKey.isActive ? 'border-primary-200' : 'border-accent-red border-opacity-30 bg-accent-red bg-opacity-5'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="font-medium text-text-primary">{apiKey.name}</h3>
                        <span className={`text-xs px-2 py-0.5 rounded-full ${
                          apiKey.isActive 
                            ? 'bg-accent-green bg-opacity-10 text-accent-green' 
                            : 'bg-accent-red bg-opacity-10 text-accent-red'
                        }`}>
                          {apiKey.isActive ? 'Active' : 'Inactive'}
                        </span>
                        {apiKey.expiresAt && (
                          <span className="text-xs text-text-secondary">
                            Expires {format(apiKey.expiresAt, 'MMM dd, yyyy')}
                          </span>
                        )}
                      </div>

                      <div className="flex items-center gap-4 mb-2">
                        <code className="font-mono text-sm bg-background-secondary px-2 py-1 rounded flex items-center gap-2">
                          {showKey === apiKey.id ? apiKey.key : `${apiKey.prefix}_••••••••••••••••••••••••••••••••`}
                          <button
                            onClick={() => setShowKey(showKey === apiKey.id ? null : apiKey.id)}
                            className="text-text-secondary hover:text-text-primary"
                          >
                            {showKey === apiKey.id ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                          </button>
                          <button
                            onClick={() => handleCopyKey(apiKey.key)}
                            className="text-text-secondary hover:text-text-primary"
                          >
                            <Copy className="w-4 h-4" />
                          </button>
                        </code>
                      </div>

                      <div className="flex items-center gap-4 text-sm text-text-secondary">
                        <span className="flex items-center gap-1">
                          <Shield className="w-3 h-3" />
                          Permissions: {apiKey.permissions.join(', ')}
                        </span>
                        <span className="flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          Created {format(apiKey.createdAt, 'MMM dd, yyyy')}
                        </span>
                        {apiKey.lastUsed && (
                          <span>
                            Last used {format(apiKey.lastUsed, 'MMM dd, yyyy HH:mm')}
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center gap-2 ml-4">
                      <button
                        onClick={() => handleToggleKey(apiKey.id)}
                        className="p-1.5 hover:bg-background-secondary rounded-medium transition-colors"
                      >
                        {apiKey.isActive ? (
                          <EyeOff className="w-4 h-4 text-text-secondary" />
                        ) : (
                          <Eye className="w-4 h-4 text-text-secondary" />
                        )}
                      </button>
                      <button
                        onClick={() => handleDeleteKey(apiKey.id)}
                        className="p-1.5 hover:bg-background-secondary rounded-medium transition-colors"
                      >
                        <Trash2 className="w-4 h-4 text-accent-red" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {apiKeys.length === 0 && (
              <div className="text-center py-8">
                <Key className="w-12 h-12 text-text-secondary mx-auto mb-3" />
                <p className="text-text-secondary">No API keys created yet</p>
              </div>
            )}
          </div>

          {/* Security Notice */}
          <div className="mt-6 bg-accent-orange bg-opacity-10 p-4 rounded-medium">
            <div className="flex items-start gap-3">
              <AlertCircle className="w-5 h-5 text-accent-orange mt-0.5" />
              <div>
                <p className="font-medium text-text-primary">Security Best Practices</p>
                <ul className="text-sm text-text-secondary mt-1 list-disc list-inside">
                  <li>Keep your API keys secure and never share them publicly</li>
                  <li>Use environment variables to store keys in your applications</li>
                  <li>Rotate keys regularly and delete unused keys</li>
                  <li>Use the minimum required permissions for each key</li>
                </ul>
              </div>
            </div>
          </div>
        </>
      )}

      {activeTab === 'webhooks' && (
        <div className="bg-white rounded-large shadow-soft p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-lg font-semibold text-text-primary">Webhooks</h2>
              <p className="text-sm text-text-secondary mt-1">
                Configure webhooks to receive real-time event notifications
              </p>
            </div>
            <button
              onClick={() => setIsCreatingWebhook(true)}
              className="flex items-center gap-2 px-4 py-2 bg-accent-red text-white rounded-soft hover:bg-accent-red-dark"
            >
              <Plus className="w-4 h-4" />
              Add Webhook
            </button>
          </div>

          <div className="space-y-4">
            {webhooks.map(webhook => (
              <div
                key={webhook.id}
                className={`p-4 border rounded-medium ${
                  webhook.isActive ? 'border-primary-200' : 'border-accent-red border-opacity-30'
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <code className="font-mono text-sm text-text-primary">{webhook.url}</code>
                      <span className={`text-xs px-2 py-0.5 rounded-full ${
                        webhook.isActive 
                          ? 'bg-accent-green bg-opacity-10 text-accent-green' 
                          : 'bg-accent-red bg-opacity-10 text-accent-red'
                      }`}>
                        {webhook.isActive ? 'Active' : 'Inactive'}
                      </span>
                      {webhook.failureCount > 0 && (
                        <span className="text-xs px-2 py-0.5 bg-accent-orange bg-opacity-10 text-accent-orange rounded-full">
                          {webhook.failureCount} failures
                        </span>
                      )}
                    </div>

                    <div className="mb-2">
                      <span className="text-sm text-text-secondary">Events: </span>
                      <span className="text-sm text-text-primary">{webhook.events.join(', ')}</span>
                    </div>

                    <div className="flex items-center gap-4 text-sm text-text-secondary">
                      <code className="font-mono bg-background-secondary px-2 py-1 rounded">
                        Secret: {webhook.secret}
                      </code>
                      {webhook.lastTriggered && (
                        <span>
                          Last triggered {format(webhook.lastTriggered, 'MMM dd, yyyy HH:mm')}
                        </span>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center gap-2 ml-4">
                    <button
                      onClick={() => handleTestWebhook(webhook.id)}
                      className="p-1.5 hover:bg-background-secondary rounded-medium transition-colors"
                    >
                      <RefreshCw className="w-4 h-4 text-text-secondary" />
                    </button>
                    <button className="p-1.5 hover:bg-background-secondary rounded-medium transition-colors">
                      <Trash2 className="w-4 h-4 text-accent-red" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {webhooks.length === 0 && (
            <div className="text-center py-8">
              <Code className="w-12 h-12 text-text-secondary mx-auto mb-3" />
              <p className="text-text-secondary">No webhooks configured yet</p>
            </div>
          )}
        </div>
      )}

      {activeTab === 'docs' && (
        <div className="bg-white rounded-large shadow-soft p-6">
          <h2 className="text-lg font-semibold text-text-primary mb-6">API Documentation</h2>

          <div className="space-y-6">
            <div>
              <h3 className="font-medium text-text-primary mb-3">Base URL</h3>
              <code className="block bg-background-secondary p-3 rounded-medium font-mono text-sm">
                https://api.tutoraide.ca/v1
              </code>
            </div>

            <div>
              <h3 className="font-medium text-text-primary mb-3">Authentication</h3>
              <p className="text-sm text-text-secondary mb-2">
                Include your API key in the Authorization header:
              </p>
              <code className="block bg-background-secondary p-3 rounded-medium font-mono text-sm">
                Authorization: Bearer YOUR_API_KEY
              </code>
            </div>

            <div>
              <h3 className="font-medium text-text-primary mb-3">Example Request</h3>
              <pre className="bg-background-secondary p-4 rounded-medium overflow-x-auto">
                <code className="text-sm">{`curl -X GET https://api.tutoraide.ca/v1/users \\
  -H "Authorization: Bearer sk_live_your_api_key" \\
  -H "Content-Type: application/json"`}</code>
              </pre>
            </div>

            <div>
              <h3 className="font-medium text-text-primary mb-3">Available Endpoints</h3>
              <div className="space-y-2">
                <div className="p-3 bg-background-secondary rounded-medium">
                  <code className="text-sm font-mono">GET /users</code>
                  <p className="text-sm text-text-secondary mt-1">List all users</p>
                </div>
                <div className="p-3 bg-background-secondary rounded-medium">
                  <code className="text-sm font-mono">POST /appointments</code>
                  <p className="text-sm text-text-secondary mt-1">Create a new appointment</p>
                </div>
                <div className="p-3 bg-background-secondary rounded-medium">
                  <code className="text-sm font-mono">GET /invoices</code>
                  <p className="text-sm text-text-secondary mt-1">List all invoices</p>
                </div>
              </div>
            </div>

            <div className="pt-4">
              <a
                href="https://docs.tutoraide.ca/api"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-2 text-accent-red hover:underline"
              >
                <Code className="w-4 h-4" />
                View Full API Documentation
              </a>
            </div>
          </div>
        </div>
      )}

      {/* Create API Key Modal */}
      {isCreatingKey && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-large shadow-soft max-w-md w-full p-6">
            <h2 className="text-xl font-semibold text-text-primary mb-6">Create New API Key</h2>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Key Name
                </label>
                <input
                  type="text"
                  value={newKeyForm.name}
                  onChange={(e) => setNewKeyForm({ ...newKeyForm, name: e.target.value })}
                  className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                  placeholder="e.g., Production Server"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Permissions
                </label>
                <div className="space-y-2">
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={newKeyForm.permissions.includes('read')}
                      onChange={(e) => {
                        const perms = e.target.checked
                          ? [...newKeyForm.permissions, 'read']
                          : newKeyForm.permissions.filter(p => p !== 'read');
                        setNewKeyForm({ ...newKeyForm, permissions: perms });
                      }}
                      className="w-4 h-4 rounded accent-accent-red"
                    />
                    <span className="text-sm">Read</span>
                  </label>
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={newKeyForm.permissions.includes('write')}
                      onChange={(e) => {
                        const perms = e.target.checked
                          ? [...newKeyForm.permissions, 'write']
                          : newKeyForm.permissions.filter(p => p !== 'write');
                        setNewKeyForm({ ...newKeyForm, permissions: perms });
                      }}
                      className="w-4 h-4 rounded accent-accent-red"
                    />
                    <span className="text-sm">Write</span>
                  </label>
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={newKeyForm.permissions.includes('delete')}
                      onChange={(e) => {
                        const perms = e.target.checked
                          ? [...newKeyForm.permissions, 'delete']
                          : newKeyForm.permissions.filter(p => p !== 'delete');
                        setNewKeyForm({ ...newKeyForm, permissions: perms });
                      }}
                      className="w-4 h-4 rounded accent-accent-red"
                    />
                    <span className="text-sm">Delete</span>
                  </label>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Expiration
                </label>
                <select
                  value={newKeyForm.expiresIn}
                  onChange={(e) => setNewKeyForm({ ...newKeyForm, expiresIn: e.target.value as any })}
                  className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                >
                  <option value="never">Never expires</option>
                  <option value="30days">30 days</option>
                  <option value="90days">90 days</option>
                  <option value="1year">1 year</option>
                </select>
              </div>

              <div className="flex items-center justify-end gap-3 pt-4">
                <button
                  onClick={() => {
                    setIsCreatingKey(false);
                    setNewKeyForm({ name: '', permissions: [], expiresIn: 'never' });
                  }}
                  className="px-4 py-2 border border-primary-300 rounded-soft hover:bg-background-secondary"
                >
                  Cancel
                </button>
                <button
                  onClick={handleCreateAPIKey}
                  disabled={!newKeyForm.name || newKeyForm.permissions.length === 0}
                  className={`
                    px-4 py-2 rounded-soft text-white
                    ${newKeyForm.name && newKeyForm.permissions.length > 0
                      ? 'bg-accent-red hover:bg-accent-red-dark'
                      : 'bg-primary-300 cursor-not-allowed'
                    }
                  `}
                >
                  Create Key
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Create Webhook Modal */}
      {isCreatingWebhook && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-large shadow-soft max-w-2xl w-full p-6 max-h-[80vh] overflow-y-auto">
            <h2 className="text-xl font-semibold text-text-primary mb-6">Add Webhook Endpoint</h2>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Endpoint URL
                </label>
                <input
                  type="url"
                  value={newWebhookForm.url}
                  onChange={(e) => setNewWebhookForm({ ...newWebhookForm, url: e.target.value })}
                  className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
                  placeholder="https://your-domain.com/webhooks/tutoraide"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Events to Subscribe
                </label>
                <div className="space-y-4">
                  {availableEvents.map(category => (
                    <div key={category.category}>
                      <p className="text-sm font-medium text-text-secondary mb-2">{category.category}</p>
                      <div className="grid grid-cols-2 gap-2">
                        {category.events.map(event => (
                          <label key={event} className="flex items-center gap-2">
                            <input
                              type="checkbox"
                              checked={newWebhookForm.events.includes(event)}
                              onChange={(e) => {
                                const events = e.target.checked
                                  ? [...newWebhookForm.events, event]
                                  : newWebhookForm.events.filter(ev => ev !== event);
                                setNewWebhookForm({ ...newWebhookForm, events });
                              }}
                              className="w-4 h-4 rounded accent-accent-red"
                            />
                            <span className="text-sm">{event}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex items-center justify-end gap-3 pt-4">
                <button
                  onClick={() => {
                    setIsCreatingWebhook(false);
                    setNewWebhookForm({ url: '', events: [] });
                  }}
                  className="px-4 py-2 border border-primary-300 rounded-soft hover:bg-background-secondary"
                >
                  Cancel
                </button>
                <button
                  onClick={handleCreateWebhook}
                  disabled={!newWebhookForm.url || newWebhookForm.events.length === 0}
                  className={`
                    px-4 py-2 rounded-soft text-white
                    ${newWebhookForm.url && newWebhookForm.events.length > 0
                      ? 'bg-accent-red hover:bg-accent-red-dark'
                      : 'bg-primary-300 cursor-not-allowed'
                    }
                  `}
                >
                  Add Webhook
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default APISettings;