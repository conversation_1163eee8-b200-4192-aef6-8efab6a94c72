import React from 'react';
import { clsx } from 'clsx';
import { MoreHorizontal } from 'lucide-react';
import { Dropdown, DropdownItem } from '../common/Dropdown';

interface TableRowProps {
  children: React.ReactNode;
  onClick?: () => void;
  selected?: boolean;
  hoverable?: boolean;
  className?: string;
}

export const TableRow: React.FC<TableRowProps> = ({
  children,
  onClick,
  selected = false,
  hoverable = true,
  className,
}) => {
  return (
    <tr
      onClick={onClick}
      className={clsx(
        'transition-all duration-200',
        hoverable && 'hover:bg-background-secondary',
        onClick && 'cursor-pointer',
        selected && 'bg-red-50 hover:bg-red-100',
        className
      )}
    >
      {children}
    </tr>
  );
};

interface TableCellProps {
  children: React.ReactNode;
  align?: 'left' | 'center' | 'right';
  className?: string;
}

export const TableCell: React.FC<TableCellProps> = ({
  children,
  align = 'left',
  className,
}) => {
  const alignClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right',
  };

  return (
    <td
      className={clsx(
        'px-6 py-4 text-sm text-text-primary',
        alignClasses[align],
        className
      )}
    >
      {children}
    </td>
  );
};

interface TableHeaderCellProps {
  children: React.ReactNode;
  align?: 'left' | 'center' | 'right';
  sortable?: boolean;
  sorted?: 'asc' | 'desc' | false;
  onSort?: () => void;
  className?: string;
}

export const TableHeaderCell: React.FC<TableHeaderCellProps> = ({
  children,
  align = 'left',
  sortable = false,
  sorted = false,
  onSort,
  className,
}) => {
  const alignClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right',
  };

  return (
    <th
      className={clsx(
        'px-6 py-3 text-xs font-medium text-text-secondary uppercase tracking-wider',
        alignClasses[align],
        className
      )}
    >
      {sortable && onSort ? (
        <button
          onClick={onSort}
          className={clsx(
            'inline-flex items-center gap-1 hover:text-text-primary transition-colors',
            sorted && 'text-accent-red'
          )}
        >
          {children}
          {sorted && (
            <svg
              className={clsx(
                'w-4 h-4 transition-transform',
                sorted === 'desc' && 'rotate-180'
              )}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 15l7-7 7 7"
              />
            </svg>
          )}
        </button>
      ) : (
        children
      )}
    </th>
  );
};

interface TableActionsProps {
  actions: {
    label: string;
    onClick: () => void;
    icon?: React.ReactNode;
    variant?: 'default' | 'danger';
    disabled?: boolean;
  }[];
  className?: string;
}

export const TableActions: React.FC<TableActionsProps> = ({
  actions,
  className,
}) => {
  return (
    <div className={clsx('flex items-center justify-end', className)}>
      <Dropdown
        trigger={
          <button className="p-2 hover:bg-background-secondary rounded-lg transition-colors">
            <MoreHorizontal className="w-4 h-4 text-text-secondary" />
          </button>
        }
        align="right"
      >
        {actions.map((action, index) => (
          <DropdownItem
            key={index}
            onClick={action.onClick}
            icon={action.icon}
            variant={action.variant}
            disabled={action.disabled}
          >
            {action.label}
          </DropdownItem>
        ))}
      </Dropdown>
    </div>
  );
};

export default TableRow;