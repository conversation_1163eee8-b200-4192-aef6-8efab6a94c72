"""
Tutor repository for tutor profiles, availability, and geolocation.
"""

import asyncpg
import logging
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, date, time

from app.database.repositories.base import BaseRepository
from app.core.exceptions import ResourceNotFoundError, BusinessLogicError
from app.core.timezone import now_est

logger = logging.getLogger(__name__)


class TutorRepository(BaseRepository):
    """Repository for tutor profiles and related data."""
    
    def __init__(self):
        super().__init__(table_name="tutor_profiles", id_column="tutor_id")
    
    async def find_by_user_id(
        self, 
        conn: asyncpg.Connection, 
        user_id: int
    ) -> Optional[asyncpg.Record]:
        """
        Find tutor profile by user ID.
        
        Args:
            conn: Database connection
            user_id: User ID to search for
        
        Returns:
            Tutor profile record if found, None otherwise
        """
        query = f"""
            SELECT * FROM {self.table_name}
            WHERE user_id = $1
        """
        
        try:
            result = await conn.fetchrow(query, user_id)
            return result
        except Exception as e:
            logger.error(f"Error finding tutor by user ID: {e}")
            raise
    
    async def find_by_postal_code(
        self, 
        conn: asyncpg.Connection, 
        postal_code_prefix: str,
        limit: int = 50
    ) -> List[asyncpg.Record]:
        """
        Find tutors by postal code prefix.
        
        Args:
            conn: Database connection
            postal_code_prefix: First few characters of postal code
            limit: Maximum number of results
        
        Returns:
            List of tutor records
        """
        query = f"""
            SELECT tp.*, ua.email
            FROM {self.table_name} tp
            JOIN user_accounts ua ON tp.user_id = ua.user_id
            WHERE tp.postal_code ILIKE $1
            AND ua.deleted_at IS NULL
            AND tp.verification_status IN ('basic_approved', 'fully_verified', 'premium')
            ORDER BY tp.verification_status DESC, tp.first_name
            LIMIT $2
        """
        
        search_pattern = f"{postal_code_prefix.strip()}%"
        
        try:
            results = await conn.fetch(query, search_pattern, limit)
            return results
        except Exception as e:
            logger.error(f"Error finding tutors by postal code: {e}")
            raise
    
    async def find_nearest_tutors(
        self,
        conn: asyncpg.Connection,
        latitude: float,
        longitude: float,
        subject_area: Optional[str] = None,
        limit: int = 5,
        max_distance_km: float = 50.0
    ) -> List[asyncpg.Record]:
        """
        Find nearest tutors by coordinates using distance calculation.
        
        Args:
            conn: Database connection
            latitude: Client latitude
            longitude: Client longitude
            subject_area: Optional subject area filter
            limit: Maximum number of tutors to return
            max_distance_km: Maximum distance in kilometers
        
        Returns:
            List of tutor records with distance information
        """
        # Using Haversine formula for distance calculation
        distance_query = """
            (6371 * acos(
                cos(radians($1)) * cos(radians(tp.latitude)) *
                cos(radians(tp.longitude) - radians($2)) +
                sin(radians($1)) * sin(radians(tp.latitude))
            )) as distance_km
        """
        
        query = f"""
            SELECT 
                tp.*,
                ua.email,
                {distance_query}
            FROM {self.table_name} tp
            JOIN user_accounts ua ON tp.user_id = ua.user_id
            WHERE tp.latitude IS NOT NULL 
            AND tp.longitude IS NOT NULL
            AND ua.deleted_at IS NULL
            AND tp.verification_status IN ('basic_approved', 'fully_verified', 'premium')
        """
        
        params = [latitude, longitude]
        param_count = 2
        
        # Add subject area filter if specified
        if subject_area:
            param_count += 1
            query += f" AND $3 = ANY(tp.specializations)"
            params.append(subject_area)
        
        # Add distance filter and ordering
        query += f"""
            HAVING {distance_query} <= ${param_count + 1}
            ORDER BY distance_km, tp.verification_status DESC
            LIMIT ${param_count + 2}
        """
        
        params.extend([max_distance_km, limit])
        
        try:
            results = await conn.fetch(query, *params)
            return results
        except Exception as e:
            logger.error(f"Error finding nearest tutors: {e}")
            raise
    
    async def get_tutors_with_location(
        self,
        conn: asyncpg.Connection,
        verification_filter: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """
        Get all tutors with their location data for matching algorithms.
        
        Args:
            conn: Database connection
            verification_filter: Optional list of verification statuses to filter by
        
        Returns:
            List of tutor dictionaries with location and profile data
        """
        # Default to verified tutors only
        if verification_filter is None:
            verification_filter = ['basic_approved', 'fully_verified', 'premium']
        
        query = """
            SELECT 
                tp.tutor_id,
                tp.user_id,
                tp.first_name,
                tp.last_name,
                tp.postal_code,
                tp.latitude,
                tp.longitude,
                tp.bio,
                tp.experience_years,
                tp.education,
                tp.hourly_rate,
                tp.specializations as specialties,
                tp.languages,
                tp.verification_status,
                tp.profile_picture,
                tp.created_at,
                tp.updated_at,
                ua.email,
                -- Calculate average rating and session count
                COALESCE(ratings.average_rating, 0) as average_rating,
                COALESCE(sessions.total_sessions, 0) as total_sessions,
                -- Determine availability status
                CASE 
                    WHEN availability.available_slots > 10 THEN 'available'
                    WHEN availability.available_slots > 5 THEN 'limited'
                    ELSE 'busy'
                END as availability_status,
                -- Get service types
                COALESCE(services.service_types, ARRAY[]::text[]) as service_types
            FROM {self.table_name} tp
            JOIN user_accounts ua ON tp.user_id = ua.user_id
            LEFT JOIN (
                -- Calculate average rating from appointments
                SELECT 
                    tutor_id,
                    AVG(client_rating) as average_rating
                FROM appointment_reviews ar
                JOIN appointments a ON ar.appointment_id = a.appointment_id
                WHERE client_rating IS NOT NULL
                GROUP BY tutor_id
            ) ratings ON tp.tutor_id = ratings.tutor_id
            LEFT JOIN (
                -- Count completed sessions
                SELECT 
                    tutor_id,
                    COUNT(*) as total_sessions
                FROM appointments
                WHERE status = 'completed'
                GROUP BY tutor_id
            ) sessions ON tp.tutor_id = sessions.tutor_id
            LEFT JOIN (
                -- Calculate available time slots (simplified)
                SELECT 
                    tutor_id,
                    COUNT(*) * 8 as available_slots  -- Assume 8 slots per day
                FROM tutor_availability
                WHERE is_active = true
                GROUP BY tutor_id
            ) availability ON tp.tutor_id = availability.tutor_id
            LEFT JOIN (
                -- Get service types from tutor services
                SELECT 
                    tutor_id,
                    array_agg(DISTINCT service_type) as service_types
                FROM tutor_services ts
                WHERE is_active = true
                GROUP BY tutor_id
            ) services ON tp.tutor_id = services.tutor_id
            WHERE ua.deleted_at IS NULL
            AND tp.verification_status = ANY($1)
            AND tp.latitude IS NOT NULL 
            AND tp.longitude IS NOT NULL
            ORDER BY tp.verification_status DESC, tp.first_name
        """
        
        try:
            results = await conn.fetch(query, verification_filter)
            # Convert to dictionaries for easier handling
            return [dict(record) for record in results]
        except Exception as e:
            logger.error(f"Error getting tutors with location: {e}")
            raise
    
    async def get_tutor_availability(
        self, 
        conn: asyncpg.Connection, 
        tutor_id: int,
        day_of_week: Optional[int] = None
    ) -> List[asyncpg.Record]:
        """
        Get tutor availability schedule.
        
        Args:
            conn: Database connection
            tutor_id: Tutor ID
            day_of_week: Optional specific day (0=Sunday, 6=Saturday)
        
        Returns:
            List of availability records
        """
        query = """
            SELECT 
                availability_id,
                day_of_week,
                start_time,
                end_time,
                is_active,
                CASE day_of_week
                    WHEN 0 THEN 'Sunday'
                    WHEN 1 THEN 'Monday'
                    WHEN 2 THEN 'Tuesday'
                    WHEN 3 THEN 'Wednesday'
                    WHEN 4 THEN 'Thursday'
                    WHEN 5 THEN 'Friday'
                    WHEN 6 THEN 'Saturday'
                END as day_name
            FROM tutor_availability
            WHERE tutor_id = $1 AND is_active = true
        """
        
        params = [tutor_id]
        
        if day_of_week is not None:
            query += " AND day_of_week = $2"
            params.append(day_of_week)
        
        query += " ORDER BY day_of_week, start_time"
        
        try:
            results = await conn.fetch(query, *params)
            return results
        except Exception as e:
            logger.error(f"Error getting tutor availability: {e}")
            raise
    
    async def set_tutor_availability(
        self,
        conn: asyncpg.Connection,
        tutor_id: int,
        day_of_week: int,
        start_time: time,
        end_time: time
    ) -> asyncpg.Record:
        """
        Set tutor availability for a specific day.
        
        Args:
            conn: Database connection
            tutor_id: Tutor ID
            day_of_week: Day of week (0=Sunday, 6=Saturday)
            start_time: Start time
            end_time: End time
        
        Returns:
            Availability record
        """
        if end_time <= start_time:
            raise BusinessLogicError(
                "End time must be after start time",
                {"start_time": str(start_time), "end_time": str(end_time)}
            )
        
        query = """
            INSERT INTO tutor_availability 
            (tutor_id, day_of_week, start_time, end_time, is_active, created_at, updated_at)
            VALUES ($1, $2, $3, $4, true, $5, $6)
            ON CONFLICT (tutor_id, day_of_week, start_time)
            DO UPDATE SET 
                end_time = $4,
                is_active = true,
                updated_at = $6
            RETURNING *
        """
        
        now = now_est()
        
        try:
            result = await conn.fetchrow(
                query, tutor_id, day_of_week, start_time, end_time, now, now
            )
            logger.info(f"Set availability for tutor {tutor_id} on day {day_of_week}")
            return result
        except Exception as e:
            logger.error(f"Error setting tutor availability: {e}")
            raise
    
    async def get_tutor_time_off(
        self,
        conn: asyncpg.Connection,
        tutor_id: int,
        status: Optional[str] = None,
        include_past: bool = False
    ) -> List[asyncpg.Record]:
        """
        Get tutor time off requests.
        
        Args:
            conn: Database connection
            tutor_id: Tutor ID
            status: Optional status filter
            include_past: Whether to include past time off
        
        Returns:
            List of time off records
        """
        query = """
            SELECT 
                tto.*,
                ua.first_name as approved_by_name
            FROM tutor_time_off tto
            LEFT JOIN user_accounts ua ON tto.approved_by = ua.user_id
            WHERE tto.tutor_id = $1
        """
        
        params = [tutor_id]
        param_count = 1
        
        if status:
            param_count += 1
            query += f" AND tto.status = ${param_count}"
            params.append(status)
        
        if not include_past:
            param_count += 1
            query += f" AND tto.end_date >= ${param_count}"
            params.append(date.today())
        
        query += " ORDER BY tto.start_date"
        
        try:
            results = await conn.fetch(query, *params)
            return results
        except Exception as e:
            logger.error(f"Error getting tutor time off: {e}")
            raise
    
    async def request_time_off(
        self,
        conn: asyncpg.Connection,
        tutor_id: int,
        start_date: date,
        end_date: date,
        reason: Optional[str] = None
    ) -> asyncpg.Record:
        """
        Request time off for a tutor.
        
        Args:
            conn: Database connection
            tutor_id: Tutor ID
            start_date: Start date of time off
            end_date: End date of time off
            reason: Optional reason for time off
        
        Returns:
            Time off record
        """
        if end_date < start_date:
            raise BusinessLogicError(
                "End date must be after or equal to start date",
                {"start_date": str(start_date), "end_date": str(end_date)}
            )
        
        query = """
            INSERT INTO tutor_time_off 
            (tutor_id, start_date, end_date, reason, status, created_at, updated_at)
            VALUES ($1, $2, $3, $4, 'requested', $5, $6)
            RETURNING *
        """
        
        now = now_est()
        
        try:
            result = await conn.fetchrow(
                query, tutor_id, start_date, end_date, reason, now, now
            )
            logger.info(f"Time off requested for tutor {tutor_id}: {start_date} to {end_date}")
            return result
        except Exception as e:
            logger.error(f"Error requesting time off: {e}")
            raise
    
    async def get_tutor_service_rates(
        self,
        conn: asyncpg.Connection,
        tutor_id: int,
        service_id: Optional[int] = None
    ) -> List[asyncpg.Record]:
        """
        Get tutor service rates.
        
        Args:
            conn: Database connection
            tutor_id: Tutor ID
            service_id: Optional specific service ID
        
        Returns:
            List of service rate records
        """
        query = """
            SELECT 
                tsr.*,
                sc.service_name,
                sc.subject_area
            FROM tutor_service_rates tsr
            JOIN service_catalog sc ON tsr.service_id = sc.service_id
            WHERE tsr.tutor_id = $1
        """
        
        params = [tutor_id]
        
        if service_id:
            query += " AND tsr.service_id = $2"
            params.append(service_id)
        
        query += " ORDER BY sc.subject_area, sc.service_name"
        
        try:
            results = await conn.fetch(query, *params)
            return results
        except Exception as e:
            logger.error(f"Error getting tutor service rates: {e}")
            raise
    
    async def set_tutor_service_rate(
        self,
        conn: asyncpg.Connection,
        tutor_id: int,
        service_id: int,
        hourly_rate: float
    ) -> asyncpg.Record:
        """
        Set tutor rate for a specific service.
        
        Args:
            conn: Database connection
            tutor_id: Tutor ID
            service_id: Service ID
            hourly_rate: Hourly rate in CAD
        
        Returns:
            Service rate record
        """
        if hourly_rate <= 0:
            raise BusinessLogicError(
                "Hourly rate must be positive",
                {"hourly_rate": hourly_rate}
            )
        
        query = """
            INSERT INTO tutor_service_rates 
            (tutor_id, service_id, hourly_rate, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5)
            ON CONFLICT (tutor_id, service_id)
            DO UPDATE SET 
                hourly_rate = $3,
                updated_at = $5
            RETURNING *
        """
        
        now = now_est()
        
        try:
            result = await conn.fetchrow(
                query, tutor_id, service_id, hourly_rate, now, now
            )
            logger.info(f"Set rate ${hourly_rate}/hr for tutor {tutor_id} service {service_id}")
            return result
        except Exception as e:
            logger.error(f"Error setting tutor service rate: {e}")
            raise
    
    async def find_tutors_by_specialization(
        self,
        conn: asyncpg.Connection,
        subject_area: str,
        service_type: Optional[str] = None,
        limit: int = 20
    ) -> List[asyncpg.Record]:
        """
        Find tutors by subject specialization.
        
        Args:
            conn: Database connection
            subject_area: Subject area to search for
            service_type: Optional service type filter
            limit: Maximum number of results
        
        Returns:
            List of tutor records
        """
        query = f"""
            SELECT 
                tp.*,
                ua.email,
                tsr.hourly_rate
            FROM {self.table_name} tp
            JOIN user_accounts ua ON tp.user_id = ua.user_id
            LEFT JOIN tutor_service_rates tsr ON tp.tutor_id = tsr.tutor_id
            LEFT JOIN service_catalog sc ON tsr.service_id = sc.service_id
            WHERE $1 = ANY(tp.specializations)
            AND ua.deleted_at IS NULL
            AND tp.verification_status IN ('basic_approved', 'fully_verified', 'premium')
        """
        
        params = [subject_area]
        
        if service_type:
            query += " AND sc.subject_area = $2"
            params.append(service_type)
        
        query += """
            ORDER BY tp.verification_status DESC, tsr.hourly_rate ASC
            LIMIT ${}
        """.format(len(params) + 1)
        
        params.append(limit)
        
        try:
            results = await conn.fetch(query, *params)
            return results
        except Exception as e:
            logger.error(f"Error finding tutors by specialization: {e}")
            raise
    
    async def update_tutor_location(
        self,
        conn: asyncpg.Connection,
        tutor_id: int,
        postal_code: str,
        latitude: Optional[float] = None,
        longitude: Optional[float] = None
    ) -> Optional[asyncpg.Record]:
        """
        Update tutor location information.
        
        Args:
            conn: Database connection
            tutor_id: Tutor ID
            postal_code: Postal code
            latitude: Optional latitude
            longitude: Optional longitude
        
        Returns:
            Updated tutor record
        """
        query = f"""
            UPDATE {self.table_name}
            SET postal_code = $1, latitude = $2, longitude = $3, updated_at = $4
            WHERE tutor_id = $5
            RETURNING *
        """
        
        try:
            result = await conn.fetchrow(
                query, postal_code, latitude, longitude, now_est(), tutor_id
            )
            if result:
                logger.info(f"Updated location for tutor {tutor_id}")
            return result
        except Exception as e:
            logger.error(f"Error updating tutor location: {e}")
            raise
    
    async def get_verified_tutors(
        self,
        conn: asyncpg.Connection,
        verification_level: str = "basic_approved"
    ) -> List[asyncpg.Record]:
        """
        Get tutors by verification status.
        
        Args:
            conn: Database connection
            verification_level: Minimum verification level
        
        Returns:
            List of verified tutor records
        """
        verification_order = {
            "pending": 0,
            "basic_approved": 1,
            "fully_verified": 2,
            "premium": 3
        }
        
        min_level = verification_order.get(verification_level, 1)
        
        query = f"""
            SELECT tp.*, ua.email
            FROM {self.table_name} tp
            JOIN user_accounts ua ON tp.user_id = ua.user_id
            WHERE ua.deleted_at IS NULL
            AND (
                (tp.verification_status = 'basic_approved' AND $1 <= 1) OR
                (tp.verification_status = 'fully_verified' AND $1 <= 2) OR
                (tp.verification_status = 'premium' AND $1 <= 3)
            )
            ORDER BY tp.verification_status DESC, tp.first_name
        """
        
        try:
            results = await conn.fetch(query, min_level)
            return results
        except Exception as e:
            logger.error(f"Error getting verified tutors: {e}")
            raise


# Dependency injection helpers
async def get_tutor_repository() -> TutorRepository:
    """Get tutor repository instance."""
    return TutorRepository()