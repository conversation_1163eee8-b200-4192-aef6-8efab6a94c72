# TutorAide API Environment Configuration
# Copy this file to .env and update with your values

# Project settings
PROJECT_NAME=\TutorAide API\
VERSION=\1.0.0\
API_V1_STR=\/api/v1\
DEBUG=False

# Security
SECRET_KEY=\your-secret-key-here-minimum-32-characters\
ACCESS_TOKEN_EXPIRE_MINUTES=30
MANAGER_TOKEN_EXPIRE_MINUTES=180
ALGORITHM=\HS256\

# Database
DATABASE_URL=\postgresql://user:password@localhost:5432/tutoraide\
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20
DATABASE_POOL_TIMEOUT=30

# Test Database (Railway PostgreSQL - Replace with your Railway credentials)
TEST_DATABASE_URL=\postgresql://postgres:YOUR_PASSWORD@YOUR_HOST.railway.app:PORT/railway\

# CORS
CORS_ORIGINS=\http://localhost:3000,http://localhost:8000\
ALLOWED_HOSTS=\localhost,127.0.0.1,*.tutoraide.ca\

# Email configuration
SMTP_HOST=\smtp.gmail.com\
SMTP_PORT=587
SMTP_USERNAME=\<EMAIL>\
SMTP_PASSWORD=\your-app-password\
SMTP_TLS=True

# Email addresses
BILLING_EMAIL=\<EMAIL>\
SUPPORT_EMAIL=\<EMAIL>\
GENERAL_EMAIL=\<EMAIL>\

# Google OAuth
GOOGLE_CLIENT_ID=\your-google-client-id\
GOOGLE_CLIENT_SECRET=\your-google-client-secret\
GOOGLE_REDIRECT_URI=\http://localhost:8000/api/v1/auth/google/callback\

# Stripe
STRIPE_PUBLIC_KEY=\pk_test_...\
STRIPE_SECRET_KEY=\sk_test_...\
STRIPE_WEBHOOK_SECRET=\whsec_...\

# Twilio
TWILIO_ACCOUNT_SID=\AC...\
TWILIO_AUTH_TOKEN=\your-auth-token\
TWILIO_PHONE_NUMBER=\+**********\

# OneSignal
ONESIGNAL_APP_ID=\your-app-id\
ONESIGNAL_API_KEY=\your-api-key\

# Geocoding
GEOCODING_API_KEY=\your-google-maps-api-key\

# Rate limiting
RATE_LIMIT_PER_MINUTE=100

# File upload
MAX_FILE_SIZE=********  # 10MB
UPLOAD_FOLDER=\uploads\
