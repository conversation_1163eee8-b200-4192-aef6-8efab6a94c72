"""
Database query optimization utilities for appointment system.

Provides optimized queries that leverage the new indexes for maximum performance.
"""

import logging
from datetime import datetime, date, timedelta
from typing import List, Dict, Any, Optional, Tuple
import asyncpg

from app.models.appointment_models import AppointmentStatus
from app.models.service_models import ServiceType, LocationType
from app.core.timezone import now_est

logger = logging.getLogger(__name__)


class OptimizedAppointmentQueries:
    """Optimized database queries for appointment operations."""
    
    def __init__(self, db_connection: asyncpg.Connection):
        self.db = db_connection
    
    async def get_tutor_schedule_optimized(
        self,
        tutor_id: int,
        start_date: date,
        end_date: date,
        include_cancelled: bool = False
    ) -> List[Dict[str, Any]]:
        """
        Optimized query for tutor schedule retrieval.
        
        Uses idx_appointments_tutor_date_status index for maximum performance.
        """
        try:
            status_filter = "status != 'cancelled'" if not include_cancelled else "TRUE"
            
            query = f"""
                SELECT 
                    appointment_id,
                    client_id,
                    dependant_id,
                    scheduled_date,
                    start_time,
                    end_time,
                    subject_area,
                    session_type,
                    status,
                    location_type,
                    location_details,
                    confirmed_by_tutor,
                    notes,
                    created_at
                FROM appointments 
                WHERE tutor_id = $1 
                  AND scheduled_date BETWEEN $2 AND $3
                  AND {status_filter}
                  AND deleted_at IS NULL
                ORDER BY scheduled_date, start_time
            """
            
            result = await self.db.fetch(
                query,
                tutor_id,
                start_date,
                end_date
            )
            
            return [dict(row) for row in result]
            
        except Exception as e:
            logger.error(f"Error in optimized tutor schedule query: {e}")
            raise


class QueryOptimizationService:
    """Service for managing query optimization and performance monitoring."""
    
    def __init__(self, db_connection: asyncpg.Connection):
        self.db = db_connection
        self.optimized_queries = OptimizedAppointmentQueries(db_connection)
    
    async def analyze_query_performance(self, query: str) -> Dict[str, Any]:
        """Analyze query performance using EXPLAIN ANALYZE."""
        try:
            explain_query = f"EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) {query}"
            result = await self.db.fetchrow(explain_query)
            
            if result:
                execution_plan = result[0][0]
                
                return {
                    'execution_time': execution_plan.get('Execution Time', 0),
                    'planning_time': execution_plan.get('Planning Time', 0),
                    'total_cost': execution_plan['Plan'].get('Total Cost', 0),
                    'rows_returned': execution_plan['Plan'].get('Actual Rows', 0),
                }
            else:
                return {}
            
        except Exception as e:
            logger.error(f"Error analyzing query performance: {e}")
            return {}