-- ================================================
-- Complete Combined TutorAide and SEO Platform Schema
-- ================================================
-- This script creates both the TutorAide core tables (from all migrations 001-032)
-- and SEO platform tables in separate schemas within the same PostgreSQL database
-- 
-- Version: 2.4 - Complete Schema with Comprehensive Audit Logging
-- Date: 2025-06-25
-- ================================================
-- Changes in 2.4:
-- - Added billing_audit_logs table for tracking all billing changes
-- - Added indexes for efficient billing audit log querying
-- ================================================
-- Changes in 2.3:
-- - Added appointment_audit_logs table for tracking all appointment changes
-- - Added indexes for efficient audit log querying
-- ================================================
-- Changes in 2.2:
-- - Removed 5 service lookup tables (service_types, subject_areas, location_preferences, frequency_options, duration_options)
-- - Added CHECK constraints to replace foreign key references
-- - Enhanced service_catalog and service_packages tables with proper constraints
-- - Improved tutor_service_rates table with comprehensive pricing options
-- ================================================

-- Enable required extensions (shared by both schemas)
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm"; -- For text search optimization
CREATE EXTENSION IF NOT EXISTS "gen_random_uuid"; -- For SEO schema UUID generation

-- ================================================
-- SCHEMA CREATION
-- ================================================

-- Create the SEO schema for SEO platform tables
CREATE SCHEMA IF NOT EXISTS seo;

-- Create a shared schema for cross-app views
CREATE SCHEMA IF NOT EXISTS shared;

-- The public schema will contain TutorAide core tables

-- ================================================
-- TUTORAIDE CORE SCHEMA (public schema)
-- ================================================

-- Set search path to public for TutorAide tables
SET search_path TO public;

-- ================================================
-- CUSTOM TYPES FOR TUTORAIDE
-- ================================================

-- Core user and auth types
CREATE TYPE user_role_type AS ENUM ('manager', 'tutor', 'client');
CREATE TYPE consent_level AS ENUM ('level_1', 'level_2');
CREATE TYPE consent_type AS ENUM ('terms_of_service', 'privacy_policy', 'marketing');
CREATE TYPE relationship_type AS ENUM ('parent', 'guardian', 'self');
CREATE TYPE participant_type AS ENUM ('client', 'dependant', 'tutor');
CREATE TYPE attendance_status AS ENUM ('scheduled', 'present', 'absent', 'cancelled');
CREATE TYPE appointment_status AS ENUM ('scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show');
CREATE TYPE payment_status AS ENUM ('pending', 'processing', 'paid', 'failed', 'refunded', 'cancelled');
CREATE TYPE time_off_status AS ENUM ('requested', 'approved', 'rejected', 'cancelled');
CREATE TYPE platform_type AS ENUM ('ios', 'android', 'web');
CREATE TYPE notification_type AS ENUM ('appointment_reminder', 'payment_due', 'session_confirmed', 'general');

-- Two-factor authentication types
CREATE TYPE two_factor_method AS ENUM ('sms', 'email', 'totp');

-- ================================================
-- UTILITY FUNCTIONS
-- ================================================

-- Function to get current timestamp in EST
CREATE OR REPLACE FUNCTION current_timestamp_est() 
RETURNS TIMESTAMP WITH TIME ZONE AS $$
BEGIN
    RETURN CURRENT_TIMESTAMP AT TIME ZONE 'America/New_York';
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- ============================================
-- Core User Tables
-- ============================================

-- User accounts table (base authentication)
CREATE TABLE user_accounts (
    user_id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255),
    google_id VARCHAR(255) UNIQUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    
    -- Email verification fields
    email_verified BOOLEAN NOT NULL DEFAULT false,
    email_verified_at TIMESTAMP WITH TIME ZONE NULL,
    
    -- Two-factor authentication fields
    two_factor_required BOOLEAN DEFAULT false,
    two_factor_grace_ends_at TIMESTAMP WITH TIME ZONE,
    
    -- Language preference fields
    preferred_language VARCHAR(5) DEFAULT 'en' CHECK (preferred_language IN ('en', 'fr')),
    language_auto_detect BOOLEAN DEFAULT true,
    language_source VARCHAR(20) DEFAULT 'auto' CHECK (language_source IN ('auto', 'manual', 'browser', 'system')),
    language_updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    quebec_french_preference BOOLEAN DEFAULT true,
    
    CONSTRAINT email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- Create users view for backward compatibility
CREATE OR REPLACE VIEW users AS
SELECT 
    user_id,
    email,
    password_hash,
    google_id,
    created_at,
    updated_at,
    deleted_at,
    preferred_language,
    language_auto_detect,
    language_source,
    language_updated_at,
    quebec_french_preference,
    email_verified,
    email_verified_at
FROM user_accounts;

-- User roles (many-to-many: users can have multiple roles)
CREATE TABLE user_roles (
    user_role_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    role_type user_role_type NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, role_type)
);

-- User consents tracking
CREATE TABLE user_consents (
    consent_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    consent_level consent_level NOT NULL,
    consent_type consent_type NOT NULL,
    accepted_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    ip_address INET,
    UNIQUE(user_id, consent_level, consent_type)
);

-- Password reset tokens
CREATE TABLE password_reset_tokens (
    reset_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    token UUID NOT NULL DEFAULT uuid_generate_v4() UNIQUE,
    expires_at TIMESTAMP NOT NULL,
    used_at TIMESTAMP,
    initiated_by VARCHAR(50) DEFAULT 'user', -- 'user' or 'manager'
    initiated_by_user_id INTEGER REFERENCES user_accounts(user_id),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Email verification tokens
CREATE TABLE email_verification_tokens (
    verification_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    token UUID UNIQUE NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    verified_at TIMESTAMP WITH TIME ZONE NULL,
    ip_address INET NULL,
    user_agent TEXT NULL,
    verified_from_ip INET NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT current_timestamp_est(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT current_timestamp_est(),
    deleted_at TIMESTAMP WITH TIME ZONE NULL
);

-- User sessions table
CREATE TABLE user_sessions (
    session_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL UNIQUE,
    role_type user_role_type NOT NULL,
    ip_address INET,
    user_agent TEXT,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    last_activity TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Two-factor authentication setups
CREATE TABLE two_factor_setups (
    setup_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    method two_factor_method NOT NULL,
    secret TEXT NOT NULL, -- Encrypted secret for TOTP or backup codes hash
    backup_codes TEXT, -- Encrypted JSON array of backup codes
    phone_number VARCHAR(20), -- For SMS method
    email VARCHAR(255), -- For email method
    is_primary BOOLEAN DEFAULT true,
    is_enabled BOOLEAN DEFAULT false,
    verified_at TIMESTAMP WITH TIME ZONE,
    last_used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Constraints
    CONSTRAINT unique_user_method UNIQUE (user_id, method),
    CONSTRAINT phone_required_for_sms CHECK (
        method != 'sms' OR phone_number IS NOT NULL
    ),
    CONSTRAINT email_required_for_email CHECK (
        method != 'email' OR email IS NOT NULL
    )
);

-- Two-factor authentication challenges
CREATE TABLE two_factor_challenges (
    challenge_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    setup_id INTEGER NOT NULL REFERENCES two_factor_setups(setup_id) ON DELETE CASCADE,
    method two_factor_method NOT NULL,
    code TEXT NOT NULL, -- Hashed challenge code
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    attempts INTEGER DEFAULT 0,
    verified_at TIMESTAMP WITH TIME ZONE,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CHECK (attempts >= 0)
);

-- Trusted devices for remember me functionality
CREATE TABLE trusted_devices (
    device_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    device_fingerprint TEXT NOT NULL, -- Hash of device info
    device_name VARCHAR(255),
    last_used_at TIMESTAMP WITH TIME ZONE NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    CONSTRAINT unique_user_device UNIQUE (user_id, device_fingerprint)
);

-- Tutor invitations (enhanced with application system - migration 032)
CREATE TABLE tutor_invitations (
    invitation_id SERIAL PRIMARY KEY,
    manager_id INTEGER NOT NULL REFERENCES user_accounts(user_id),
    email VARCHAR(255) NOT NULL,
    invitation_token UUID NOT NULL DEFAULT uuid_generate_v4() UNIQUE,
    expires_at TIMESTAMP NOT NULL,
    used_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Application system fields (migration 032)
    application_status VARCHAR(20) DEFAULT 'invitation_sent' 
        CHECK (application_status IN ('invitation_sent', 'under_review', 'approved', 'rejected', 'waitlisted')),
    application_data JSONB,
    application_submitted_at TIMESTAMP,
    reviewed_by INTEGER REFERENCES user_accounts(user_id),
    reviewed_at TIMESTAMP,
    review_notes TEXT,
    rejection_reason VARCHAR(50) 
        CHECK (rejection_reason IN ('insufficient_education', 'insufficient_experience', 
        'no_relevant_subjects', 'failed_reference_check', 'geographic_limitation', 'other')),
    can_reapply_after DATE,
    application_history JSONB DEFAULT '[]'::jsonb
);

-- Push notification tokens
CREATE TABLE push_notification_tokens (
    token_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    device_token VARCHAR(500) NOT NULL,
    platform platform_type NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, device_token)
);

-- Notification preferences
CREATE TABLE notification_preferences (
    preference_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    notification_type notification_type NOT NULL,
    push_enabled BOOLEAN DEFAULT true,
    sms_enabled BOOLEAN DEFAULT true,
    email_enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, notification_type)
);

-- Language preference history
CREATE TABLE user_language_preferences_history (
    preference_history_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    previous_language VARCHAR(5),
    new_language VARCHAR(5) NOT NULL,
    change_source VARCHAR(20) NOT NULL CHECK (change_source IN ('user_manual', 'auto_detect', 'browser_detect', 'admin_override', 'system_migration')),
    change_reason TEXT,
    browser_language VARCHAR(100),
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(100),
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES user_accounts(user_id)
);

-- Language usage analytics
CREATE TABLE language_usage_analytics (
    analytics_id SERIAL PRIMARY KEY,
    date_recorded DATE NOT NULL DEFAULT CURRENT_DATE,
    language_code VARCHAR(5) NOT NULL,
    total_users INTEGER DEFAULT 0,
    new_users INTEGER DEFAULT 0,
    active_sessions INTEGER DEFAULT 0,
    manual_switches INTEGER DEFAULT 0,
    auto_detections INTEGER DEFAULT 0,
    quebec_french_users INTEGER DEFAULT 0,
    browser_preferences JSONB,
    geographic_data JSONB,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(date_recorded, language_code)
);

-- User formatting preferences
CREATE TABLE user_formatting_preferences (
    formatting_preference_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    
    -- Date and time formatting
    date_format VARCHAR(20) DEFAULT 'medium' CHECK (date_format IN ('short', 'medium', 'long', 'full')),
    time_format VARCHAR(10) DEFAULT 'auto' CHECK (time_format IN ('auto', '12h', '24h')),
    
    -- Currency formatting
    currency_display VARCHAR(20) DEFAULT 'symbol' CHECK (currency_display IN ('symbol', 'code', 'name')),
    number_precision INTEGER DEFAULT 2 CHECK (number_precision >= 0 AND number_precision <= 6),
    
    -- Quebec-specific preferences
    show_quebec_indicators BOOLEAN DEFAULT true,
    use_relative_dates BOOLEAN DEFAULT true,
    
    -- Contact formatting
    phone_format VARCHAR(20) DEFAULT 'national' CHECK (phone_format IN ('national', 'international')),
    address_format VARCHAR(20) DEFAULT 'standard' CHECK (address_format IN ('standard', 'compact', 'multiline')),
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(user_id)
);

-- Formatting preferences history
CREATE TABLE user_formatting_preferences_history (
    history_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    formatting_preference_id INTEGER REFERENCES user_formatting_preferences(formatting_preference_id) ON DELETE SET NULL,
    
    -- Changed fields
    field_name VARCHAR(50) NOT NULL,
    old_value TEXT,
    new_value TEXT NOT NULL,
    
    -- Change context
    change_source VARCHAR(50) DEFAULT 'user_action' CHECK (change_source IN ('user_action', 'admin_override', 'system_update', 'language_change')),
    change_reason TEXT,
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by_user_id INTEGER REFERENCES user_accounts(user_id) ON DELETE SET NULL,
    
    -- Session tracking
    session_id VARCHAR(255),
    user_agent TEXT,
    ip_address INET
);

-- Quick action usage tracking
CREATE TABLE quick_action_usage (
    usage_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id),
    action_type VARCHAR(50) NOT NULL,
    executed_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    execution_time_ms INTEGER,
    success BOOLEAN NOT NULL DEFAULT true,
    error_message TEXT,
    context_data JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- User pinned actions
CREATE TABLE user_pinned_actions (
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id),
    action_type VARCHAR(50) NOT NULL,
    pinned_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, action_type)
);

-- ============================================
-- Client Tables
-- ============================================

-- Client profiles
CREATE TABLE client_profiles (
    client_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    address JSONB, -- Store as JSON: {street, city, province, postal_code, country}
    emergency_contact JSONB, -- Store as JSON: {name, phone, relationship}
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id)
);

-- Client dependants (children)
CREATE TABLE client_dependants (
    dependant_id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES user_accounts(user_id), -- Can be NULL for young children
    primary_client_id INTEGER NOT NULL REFERENCES client_profiles(client_id),
    secondary_client_id INTEGER REFERENCES client_profiles(client_id), -- For separated parents
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    birth_date DATE NOT NULL,
    relationship_type relationship_type NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT different_parents CHECK (primary_client_id != secondary_client_id),
    CONSTRAINT valid_birth_date CHECK (birth_date <= CURRENT_DATE)
);

-- Client relationships (tracks which clients can access which dependants)
CREATE TABLE client_relationships (
    relationship_id SERIAL PRIMARY KEY,
    client_id INTEGER NOT NULL REFERENCES client_profiles(client_id) ON DELETE CASCADE,
    dependant_id INTEGER NOT NULL REFERENCES client_dependants(dependant_id) ON DELETE CASCADE,
    relationship_type relationship_type NOT NULL,
    is_primary BOOLEAN DEFAULT false,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(client_id, dependant_id)
);

-- Client learning needs
CREATE TABLE client_needs (
    need_id SERIAL PRIMARY KEY,
    client_id INTEGER REFERENCES client_profiles(client_id),
    dependant_id INTEGER REFERENCES client_dependants(dependant_id),
    subject_area VARCHAR(50) NOT NULL CHECK (subject_area IN ('mathematics', 'science', 'french', 'english', 'physics', 'chemistry', 'biology', 'history', 'geography', 'computer_science', 'economics', 'accounting', 'other')),
    service_type VARCHAR(50) NOT NULL CHECK (service_type IN ('online', 'in_person', 'library', 'hybrid')),
    location_preference VARCHAR(50) CHECK (location_preference IN ('online', 'client_home', 'tutor_home', 'library', 'public_space')),
    frequency_preference VARCHAR(50) CHECK (frequency_preference IN ('once', 'weekly', 'biweekly', 'monthly', 'custom')),
    duration_preference INTEGER CHECK (duration_preference IN (30, 45, 60, 75, 90, 120, 150, 180, 240)), -- in minutes
    notes TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT client_or_dependant CHECK (
        (client_id IS NOT NULL AND dependant_id IS NULL) OR 
        (client_id IS NULL AND dependant_id IS NOT NULL)
    )
);

-- Client tutor requests (from marketing website form submissions)
CREATE TABLE client_tutor_requests (
    request_id SERIAL PRIMARY KEY,
    
    -- Parent/Guardian Information
    parent_first_name VARCHAR(100) NOT NULL,
    parent_last_name VARCHAR(100) NOT NULL,
    parent_email VARCHAR(255) NOT NULL,
    parent_phone VARCHAR(20) NOT NULL,
    
    -- Address Information (with regional tracking for future features)
    parent_address VARCHAR(255) NOT NULL,
    parent_apartment VARCHAR(50),
    parent_city VARCHAR(100) NOT NULL,
    parent_postal_code VARCHAR(10) NOT NULL,
    parent_province VARCHAR(50) NOT NULL,
    
    -- Geographic coordinates for regional tracking
    latitude DECIMAL(10, 7),
    longitude DECIMAL(10, 7),
    region VARCHAR(100), -- e.g., 'Montreal-North', 'Laval-West', etc.
    
    -- Student Information
    student_first_name VARCHAR(100) NOT NULL,
    student_last_name VARCHAR(100) NOT NULL,
    student_grade VARCHAR(50) NOT NULL,
    
    -- Service Requirements
    subjects TEXT[] NOT NULL, -- Array of subjects requested
    frequency VARCHAR(50) NOT NULL, -- 'once', 'weekly', 'biweekly', etc.
    preferred_schedule TEXT, -- Free text for schedule preferences
    location VARCHAR(50) NOT NULL, -- 'online', 'in_person', 'hybrid'
    additional_info TEXT,
    
    -- Processing Status
    status VARCHAR(50) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'matched', 'converted', 'cancelled', 'archived')),
    assigned_to INTEGER REFERENCES user_accounts(user_id), -- Manager handling the request
    processed_by INTEGER REFERENCES user_accounts(user_id), -- Who processed/converted it
    processed_at TIMESTAMP,
    
    -- Conversion Tracking
    converted_client_id INTEGER REFERENCES client_profiles(client_id), -- Link to created client
    converted_dependant_id INTEGER REFERENCES client_dependants(dependant_id), -- Link to created dependant
    conversion_notes TEXT,
    
    -- Matching Information (for future tutor matching)
    matched_tutor_ids INTEGER[], -- Array of potential tutor matches
    match_score DECIMAL(3,2), -- 0.00 to 1.00 matching score
    match_criteria JSONB, -- Criteria used for matching
    
    -- Source Tracking
    source VARCHAR(100) DEFAULT 'website_form', -- 'website_form', 'phone', 'email', etc.
    referrer_url TEXT,
    utm_source VARCHAR(100),
    utm_medium VARCHAR(100),
    utm_campaign VARCHAR(100),
    
    -- Timestamps
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    CONSTRAINT valid_email_format CHECK (parent_email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT valid_phone_format CHECK (parent_phone ~ '^\+?[0-9\s\-\(\)]+$')
);

-- Indexes for efficient querying
CREATE INDEX idx_client_tutor_requests_status ON client_tutor_requests(status);
CREATE INDEX idx_client_tutor_requests_region ON client_tutor_requests(region) WHERE region IS NOT NULL;
CREATE INDEX idx_client_tutor_requests_city ON client_tutor_requests(parent_city);
CREATE INDEX idx_client_tutor_requests_postal_code ON client_tutor_requests(parent_postal_code);
CREATE INDEX idx_client_tutor_requests_created_at ON client_tutor_requests(created_at);
CREATE INDEX idx_client_tutor_requests_subjects ON client_tutor_requests USING GIN(subjects);
CREATE INDEX idx_client_tutor_requests_email ON client_tutor_requests(parent_email);
CREATE INDEX idx_client_tutor_requests_geographic ON client_tutor_requests(latitude, longitude) WHERE latitude IS NOT NULL AND longitude IS NOT NULL;

-- Comments for documentation
COMMENT ON TABLE client_tutor_requests IS 'Stores tutor request form submissions from the marketing website for processing and conversion to actual client accounts';
COMMENT ON COLUMN client_tutor_requests.region IS 'Geographic region for future manager views of clients awaiting matches by area';
COMMENT ON COLUMN client_tutor_requests.matched_tutor_ids IS 'Array of potential tutor IDs that match the request criteria';
COMMENT ON COLUMN client_tutor_requests.match_score IS 'Calculated matching score between 0 and 1 based on criteria alignment';

-- ============================================
-- Tutor Tables
-- ============================================

-- Tutor profiles (enhanced with education & experience - migration 031)
CREATE TABLE tutor_profiles (
    tutor_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    bio TEXT,
    specializations TEXT[], -- Array of specializations
    postal_code VARCHAR(10),
    latitude DECIMAL(10, 7),
    longitude DECIMAL(10, 7),
    profile_photo_url VARCHAR(500),
    verification_status VARCHAR(20) DEFAULT 'pending', -- pending, basic_approved, fully_verified, premium
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Education fields (migration 031)
    highest_degree_level VARCHAR(20) 
        CHECK (highest_degree_level IN ('high_school', 'associate', 'bachelor', 'master', 'phd', 'professional', 'other')),
    highest_degree_name VARCHAR(100),
    degree_major VARCHAR(100),
    university_name VARCHAR(200),
    graduation_year INTEGER CHECK (graduation_year >= 1950 AND graduation_year <= EXTRACT(YEAR FROM CURRENT_DATE)),
    gpa DECIMAL(3,2) CHECK (gpa >= 0 AND gpa <= 4.0),
    additional_education JSONB DEFAULT '[]'::jsonb,
    teaching_certifications TEXT[] DEFAULT '{}',
    certification_details JSONB DEFAULT '{}'::jsonb,
    
    -- Experience fields (migration 031)
    years_of_experience INTEGER DEFAULT 0 CHECK (years_of_experience >= 0),
    years_online_teaching INTEGER DEFAULT 0 CHECK (years_online_teaching >= 0),
    years_tutoring INTEGER DEFAULT 0 CHECK (years_tutoring >= 0),
    students_taught_total INTEGER DEFAULT 0 CHECK (students_taught_total >= 0),
    current_occupation VARCHAR(200),
    previous_teaching_positions JSONB DEFAULT '[]'::jsonb,
    subject_expertise JSONB DEFAULT '{}'::jsonb,
    success_stories TEXT,
    
    -- Professional background fields (migration 031)
    teaching_languages TEXT[] DEFAULT '{}',
    language_proficiency JSONB DEFAULT '{}'::jsonb,
    teaching_methodology TEXT[] DEFAULT '{}',
    special_needs_experience BOOLEAN DEFAULT false,
    age_groups_experience TEXT[] DEFAULT '{}',
    last_training_date DATE,
    professional_memberships TEXT[] DEFAULT '{}',
    
    -- Portfolio and references fields (migration 031)
    portfolio_url VARCHAR(500),
    sample_lesson_urls TEXT[] DEFAULT '{}',
    achievement_highlights TEXT,
    has_references BOOLEAN DEFAULT false,
    reference_check_completed BOOLEAN DEFAULT false,
    reference_summary TEXT,
    
    -- Background verification fields (migration 031)
    background_check_date DATE,
    background_check_provider VARCHAR(100),
    background_check_status VARCHAR(50) 
        CHECK (background_check_status IN ('not_started', 'pending', 'passed', 'requires_review', 'failed')),
    working_with_children_clearance BOOLEAN DEFAULT false,
    police_check_expiry DATE,
    has_liability_insurance BOOLEAN DEFAULT false,
    insurance_provider VARCHAR(100),
    insurance_expiry DATE,
    work_permit_status VARCHAR(50) 
        CHECK (work_permit_status IN ('citizen', 'permanent_resident', 'work_permit', 'student_visa', 'other')),
    work_permit_expiry DATE,
    
    UNIQUE(user_id)
);

-- Tutor availability schedule
CREATE TABLE tutor_availability (
    availability_id SERIAL PRIMARY KEY,
    tutor_id INTEGER NOT NULL REFERENCES tutor_profiles(tutor_id) ON DELETE CASCADE,
    day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6), -- 0=Sunday, 6=Saturday
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT valid_time_range CHECK (end_time > start_time),
    UNIQUE(tutor_id, day_of_week, start_time)
);

-- Tutor time off requests
CREATE TABLE tutor_time_off (
    time_off_id SERIAL PRIMARY KEY,
    tutor_id INTEGER NOT NULL REFERENCES tutor_profiles(tutor_id) ON DELETE CASCADE,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    reason TEXT,
    status time_off_status NOT NULL DEFAULT 'requested',
    approved_by INTEGER REFERENCES user_accounts(user_id),
    approved_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT valid_date_range CHECK (end_date >= start_date)
);

-- Tutor service rates
CREATE TABLE tutor_service_rates (
    rate_id SERIAL PRIMARY KEY,
    tutor_id INTEGER NOT NULL REFERENCES tutor_profiles(tutor_id) ON DELETE CASCADE,
    service_catalog_id INTEGER NOT NULL REFERENCES service_catalog(service_id),
    service_type VARCHAR(50) NOT NULL CHECK (service_type IN ('online', 'in_person', 'library', 'hybrid')),
    pricing_tier VARCHAR(50) DEFAULT 'standard' CHECK (pricing_tier IN ('standard', 'premium', 'specialized')),
    
    -- Rates in CAD
    hourly_rate DECIMAL(10, 2) NOT NULL CHECK (hourly_rate >= 15 AND hourly_rate <= 500),
    platform_fee_percentage DECIMAL(5, 2) DEFAULT 20.0 CHECK (platform_fee_percentage >= 0 AND platform_fee_percentage <= 50),
    
    -- Availability settings
    is_available BOOLEAN DEFAULT true,
    max_distance_km INTEGER CHECK (max_distance_km >= 0 AND max_distance_km <= 100),
    preferred_locations TEXT[], -- Array of postal codes or areas
    
    -- Additional pricing options
    rush_hour_surcharge DECIMAL(10, 2) CHECK (rush_hour_surcharge >= 0),
    weekend_surcharge DECIMAL(10, 2) CHECK (weekend_surcharge >= 0),
    holiday_surcharge DECIMAL(10, 2) CHECK (holiday_surcharge >= 0),
    
    created_by INTEGER NOT NULL REFERENCES user_accounts(user_id),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tutor_id, service_catalog_id)
);

-- Tutor service areas
CREATE TABLE tutor_service_areas (
    area_id SERIAL PRIMARY KEY,
    tutor_id INTEGER NOT NULL REFERENCES tutor_profiles(tutor_id) ON DELETE CASCADE,
    postal_code VARCHAR(10) NOT NULL,
    max_distance_km DECIMAL(5, 2) DEFAULT 10.0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tutor_id, postal_code)
);

-- Tutor references (secure storage - migration 031)
CREATE TABLE tutor_references (
    reference_id SERIAL PRIMARY KEY,
    tutor_id INTEGER NOT NULL REFERENCES tutor_profiles(tutor_id) ON DELETE CASCADE,
    reference_name VARCHAR(100) NOT NULL,
    reference_title VARCHAR(100),
    reference_organization VARCHAR(200),
    reference_email VARCHAR(255),
    reference_phone VARCHAR(20),
    relationship VARCHAR(100) NOT NULL, -- 'supervisor', 'colleague', 'client', 'other'
    years_known INTEGER CHECK (years_known > 0),
    verified_date DATE,
    verified_by INTEGER REFERENCES user_accounts(user_id),
    verification_status VARCHAR(50) DEFAULT 'pending' 
        CHECK (verification_status IN ('pending', 'contacted', 'verified', 'unable_to_verify')),
    verification_notes TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Rejected applications (historical tracking - migration 032)
CREATE TABLE rejected_applications (
    rejection_id SERIAL PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    application_data JSONB NOT NULL,
    rejection_reason VARCHAR(50) NOT NULL,
    rejection_notes TEXT,
    rejected_by INTEGER NOT NULL REFERENCES user_accounts(user_id),
    rejected_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    can_reapply_after DATE,
    original_invitation_id INTEGER REFERENCES tutor_invitations(invitation_id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ============================================
-- Service Tables (Simplified)
-- ============================================

-- Service catalog (consolidated with CHECK constraints instead of lookup tables)
CREATE TABLE service_catalog (
    service_id SERIAL PRIMARY KEY,
    service_name VARCHAR(200) NOT NULL,
    subject_area VARCHAR(100) NOT NULL CHECK (subject_area IN ('mathematics', 'science', 'french', 'english', 'physics', 'chemistry', 'biology', 'history', 'geography', 'computer_science', 'economics', 'accounting', 'other')),
    service_type VARCHAR(50) NOT NULL CHECK (service_type IN ('online', 'in_person', 'library', 'hybrid')),
    service_level VARCHAR(50) NOT NULL CHECK (service_level IN ('elementary', 'high_school', 'college', 'university', 'adult')),
    description TEXT,
    prerequisites TEXT,
    is_active BOOLEAN DEFAULT true,
    is_group_session BOOLEAN DEFAULT false,
    min_participants INTEGER DEFAULT 1,
    max_participants INTEGER DEFAULT 1,
    min_duration_minutes INTEGER DEFAULT 30 CHECK (min_duration_minutes >= 15 AND min_duration_minutes <= 240),
    max_duration_minutes INTEGER DEFAULT 120 CHECK (max_duration_minutes >= 30 AND max_duration_minutes <= 480),
    default_duration_minutes INTEGER DEFAULT 60 CHECK (default_duration_minutes >= 15 AND default_duration_minutes <= 240),
    created_by INTEGER NOT NULL REFERENCES user_accounts(user_id),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT valid_participants CHECK (max_participants >= min_participants AND min_participants > 0),
    CONSTRAINT valid_duration_range CHECK (max_duration_minutes >= min_duration_minutes),
    CONSTRAINT valid_default_duration CHECK (default_duration_minutes >= min_duration_minutes AND default_duration_minutes <= max_duration_minutes)
);

-- Service packages (for TECFEE and bulk sessions)
CREATE TABLE service_packages (
    package_id SERIAL PRIMARY KEY,
    package_name VARCHAR(200) NOT NULL,
    package_type VARCHAR(50) NOT NULL CHECK (package_type IN ('individual', 'tecfee', 'bulk_hours', 'monthly')),
    description TEXT,
    
    -- Allowed services configuration
    subject_areas VARCHAR(100)[] NOT NULL, -- Array of allowed subject areas
    service_types VARCHAR(50)[] NOT NULL, -- Array of allowed service types
    service_levels VARCHAR(50)[] NOT NULL, -- Array of allowed service levels
    
    -- Pricing
    package_price DECIMAL(10, 2) NOT NULL CHECK (package_price > 0),
    deposit_amount DECIMAL(10, 2) CHECK (deposit_amount >= 0),
    
    -- Session configuration
    total_sessions INTEGER NOT NULL CHECK (total_sessions > 0 AND total_sessions <= 100),
    session_duration_minutes INTEGER DEFAULT 60 CHECK (session_duration_minutes >= 30 AND session_duration_minutes <= 240),
    validity_months INTEGER DEFAULT 12 CHECK (validity_months >= 1 AND validity_months <= 24),
    
    -- Special configurations
    is_recurring BOOLEAN DEFAULT false,
    max_participants INTEGER CHECK (max_participants > 0 AND max_participants <= 20),
    min_participants INTEGER CHECK (min_participants > 0 AND min_participants <= 20),
    
    is_active BOOLEAN DEFAULT true,
    created_by INTEGER NOT NULL REFERENCES user_accounts(user_id),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT valid_participant_range CHECK (
        (max_participants IS NULL AND min_participants IS NULL) OR 
        (max_participants >= min_participants)
    ),
    CONSTRAINT valid_deposit CHECK (deposit_amount IS NULL OR deposit_amount <= package_price)
);

-- ============================================
-- Appointment Tables
-- ============================================

-- Appointment sessions
CREATE TABLE appointment_sessions (
    appointment_id SERIAL PRIMARY KEY,
    tutor_id INTEGER NOT NULL REFERENCES tutor_profiles(tutor_id),
    service_catalog_id INTEGER NOT NULL REFERENCES service_catalog(service_id),
    package_id INTEGER REFERENCES service_packages(package_id),
    scheduled_date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    status appointment_status NOT NULL DEFAULT 'scheduled',
    location_type VARCHAR(50) NOT NULL CHECK (location_type IN ('online', 'client_home', 'tutor_home', 'library', 'public_space')),
    location_details JSONB, -- {address: {...}, meeting_link: '...', notes: '...'}
    notes TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT valid_appointment_time CHECK (end_time > start_time)
);

-- Appointment participants
CREATE TABLE appointment_participants (
    participant_id SERIAL PRIMARY KEY,
    appointment_id INTEGER NOT NULL REFERENCES appointment_sessions(appointment_id) ON DELETE CASCADE,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id),
    participant_type participant_type NOT NULL,
    attendance_status attendance_status NOT NULL DEFAULT 'scheduled',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(appointment_id, user_id)
);

-- Appointment confirmations (from tutors)
CREATE TABLE appointment_confirmations (
    confirmation_id SERIAL PRIMARY KEY,
    appointment_id INTEGER NOT NULL REFERENCES appointment_sessions(appointment_id) ON DELETE CASCADE,
    tutor_id INTEGER NOT NULL REFERENCES tutor_profiles(tutor_id),
    is_completed BOOLEAN NOT NULL,
    confirmed_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    UNIQUE(appointment_id)
);

-- Appointment audit logs (for tracking changes)
CREATE TABLE appointment_audit_logs (
    audit_id SERIAL PRIMARY KEY,
    appointment_id INTEGER NOT NULL REFERENCES appointment_sessions(appointment_id) ON DELETE CASCADE,
    action VARCHAR(50) NOT NULL CHECK (action IN ('created', 'updated', 'deleted', 'status_changed', 'rescheduled', 'cancelled')),
    
    -- User who performed the action
    performed_by INTEGER NOT NULL REFERENCES user_accounts(user_id),
    performed_by_role user_role_type NOT NULL,
    
    -- What changed
    field_name VARCHAR(100), -- e.g., 'status', 'scheduled_date', 'start_time', etc.
    old_value TEXT, -- JSON representation of old value
    new_value TEXT, -- JSON representation of new value
    
    -- Additional context
    change_reason TEXT,
    ip_address INET,
    user_agent TEXT,
    
    -- Timestamp
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Billing audit logs (for tracking all billing changes)
CREATE TABLE billing_audit_logs (
    audit_id SERIAL PRIMARY KEY,
    entity_type VARCHAR(50) NOT NULL CHECK (entity_type IN ('invoice', 'payment', 'subscription', 'tutor_payment')),
    entity_id INTEGER NOT NULL,
    action VARCHAR(50) NOT NULL CHECK (action IN (
        'created', 'updated', 'deleted', 'approved', 'rejected', 
        'paid', 'refunded', 'status_changed', 'hours_deducted',
        'subscription_purchased', 'subscription_renewed', 'subscription_cancelled',
        'payout_processed', 'payment_recorded', 'auto_renew_updated'
    )),
    
    -- User who performed the action
    performed_by INTEGER NOT NULL REFERENCES user_accounts(user_id),
    performed_by_role user_role_type NOT NULL,
    
    -- What changed
    field_name VARCHAR(100), -- e.g., 'status', 'amount', 'hours_remaining', etc.
    old_value TEXT, -- JSON representation of old value
    new_value TEXT, -- JSON representation of new value
    
    -- Additional context
    change_reason TEXT,
    ip_address INET,
    user_agent TEXT,
    
    -- Financial tracking
    amount_before DECIMAL(10, 2),
    amount_after DECIMAL(10, 2),
    stripe_transaction_id VARCHAR(255), -- For payment tracking
    
    -- Timestamp
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- ============================================
-- Messaging Tables  
-- ============================================

-- NOTE: Assessment tables have been removed from this schema
-- The assessment feature has been deprecated

-- Original section replaced by messaging tables below
    assessment_id SERIAL PRIMARY KEY,
    client_id INTEGER NOT NULL,
    dependant_id INTEGER,
    tutor_id INTEGER,
    assessment_type VARCHAR(50) NOT NULL CHECK (assessment_type IN ('initial', 'progress', 'midterm', 'final', 'diagnostic', 'placement')),
    subject_area VARCHAR(50) NOT NULL CHECK (subject_area IN ('mathematics', 'science', 'english', 'french', 'history', 'geography', 'arts', 'music', 'physical_education', 'technology', 'other')),
    title VARCHAR(200) NOT NULL,
    description TEXT,
    difficulty_level VARCHAR(20) NOT NULL CHECK (difficulty_level IN ('beginner', 'elementary', 'intermediate', 'advanced', 'expert')),
    estimated_duration_minutes INTEGER NOT NULL CHECK (estimated_duration_minutes >= 5 AND estimated_duration_minutes <= 480),
    is_mandatory BOOLEAN NOT NULL DEFAULT FALSE,
    
    -- Assessment status and timing
    status VARCHAR(20) NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'scheduled', 'in_progress', 'completed', 'reviewed', 'archived')),
    scheduled_at TIMESTAMP WITH TIME ZONE,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    reviewed_by INTEGER,
    
    -- Scoring and results
    total_score NUMERIC(10,2),
    max_score NUMERIC(10,2),
    percentage_score NUMERIC(5,2) CHECK (percentage_score >= 0 AND percentage_score <= 100),
    
    -- Learning profile data (stored as JSON)
    identified_strengths JSON,
    identified_weaknesses JSON,
    learning_styles JSON,
    recommended_topics JSON,
    notes TEXT,
    
    -- Standard fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Foreign key constraints
    CONSTRAINT fk_learning_assessments_client_id FOREIGN KEY (client_id) REFERENCES client_profiles(client_id) ON DELETE CASCADE,
    CONSTRAINT fk_learning_assessments_dependant_id FOREIGN KEY (dependant_id) REFERENCES client_dependants(dependant_id) ON DELETE CASCADE,
    CONSTRAINT fk_learning_assessments_tutor_id FOREIGN KEY (tutor_id) REFERENCES tutor_profiles(tutor_id) ON DELETE SET NULL,
    CONSTRAINT fk_learning_assessments_reviewed_by FOREIGN KEY (reviewed_by) REFERENCES user_accounts(user_id) ON DELETE SET NULL
);

-- Assessment questions
CREATE TABLE assessment_questions (
    question_id SERIAL PRIMARY KEY,
    assessment_id INTEGER NOT NULL,
    question_order INTEGER NOT NULL CHECK (question_order >= 1),
    question_type VARCHAR(20) NOT NULL CHECK (question_type IN ('multiple_choice', 'true_false', 'short_answer', 'essay', 'practical', 'oral', 'portfolio')),
    question_text TEXT NOT NULL,
    points_possible NUMERIC(10,2) NOT NULL CHECK (points_possible >= 0 AND points_possible <= 100),
    is_required BOOLEAN NOT NULL DEFAULT TRUE,
    
    -- Question options and answers (stored as JSON)
    options JSON,
    correct_answers JSON,
    
    -- Grading and guidance
    grading_rubric TEXT,
    instructions TEXT,
    
    -- Standard fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Foreign key constraints
    CONSTRAINT fk_assessment_questions_assessment_id FOREIGN KEY (assessment_id) REFERENCES learning_needs_assessments(assessment_id) ON DELETE CASCADE,
    
    -- Unique constraint for question order within assessment
    CONSTRAINT uk_assessment_questions_order UNIQUE (assessment_id, question_order) DEFERRABLE INITIALLY DEFERRED
);

-- Assessment responses
CREATE TABLE assessment_responses (
    response_id SERIAL PRIMARY KEY,
    assessment_id INTEGER NOT NULL,
    question_id INTEGER NOT NULL,
    respondent_id INTEGER NOT NULL,
    response_text TEXT,
    selected_options JSON,
    time_spent_seconds INTEGER CHECK (time_spent_seconds >= 0),
    
    -- Grading
    score NUMERIC(10,2) CHECK (score >= 0),
    feedback TEXT,
    graded_at TIMESTAMP WITH TIME ZONE,
    graded_by INTEGER,
    
    -- Standard fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Foreign key constraints
    CONSTRAINT fk_assessment_responses_assessment_id FOREIGN KEY (assessment_id) REFERENCES learning_needs_assessments(assessment_id) ON DELETE CASCADE,
    CONSTRAINT fk_assessment_responses_question_id FOREIGN KEY (question_id) REFERENCES assessment_questions(question_id) ON DELETE CASCADE,
    CONSTRAINT fk_assessment_responses_respondent_id FOREIGN KEY (respondent_id) REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    CONSTRAINT fk_assessment_responses_graded_by FOREIGN KEY (graded_by) REFERENCES user_accounts(user_id) ON DELETE SET NULL,
    
    -- Unique constraint for one response per user per question
    CONSTRAINT uk_assessment_responses_user_question UNIQUE (assessment_id, question_id, respondent_id)
);

-- Assessment templates
CREATE TABLE assessment_templates (
    template_id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    subject_area VARCHAR(50) NOT NULL CHECK (subject_area IN ('mathematics', 'science', 'english', 'french', 'history', 'geography', 'arts', 'music', 'physical_education', 'technology', 'other')),
    difficulty_level VARCHAR(20) NOT NULL CHECK (difficulty_level IN ('beginner', 'elementary', 'intermediate', 'advanced', 'expert')),
    assessment_type VARCHAR(50) NOT NULL CHECK (assessment_type IN ('initial', 'progress', 'midterm', 'final', 'diagnostic', 'placement')),
    description TEXT,
    estimated_duration_minutes INTEGER NOT NULL CHECK (estimated_duration_minutes >= 5 AND estimated_duration_minutes <= 480),
    is_public BOOLEAN NOT NULL DEFAULT FALSE,
    created_by INTEGER NOT NULL,
    
    -- Usage statistics
    usage_count INTEGER NOT NULL DEFAULT 0,
    last_used_at TIMESTAMP WITH TIME ZONE,
    
    -- Standard fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Foreign key constraints
    CONSTRAINT fk_assessment_templates_created_by FOREIGN KEY (created_by) REFERENCES user_accounts(user_id) ON DELETE CASCADE
);

-- Learning recommendations
CREATE TABLE learning_recommendations (
    recommendation_id SERIAL PRIMARY KEY,
    assessment_id INTEGER NOT NULL,
    client_id INTEGER NOT NULL,
    dependant_id INTEGER,
    tutor_id INTEGER,
    subject_area VARCHAR(50) NOT NULL CHECK (subject_area IN ('mathematics', 'science', 'english', 'french', 'history', 'geography', 'arts', 'music', 'physical_education', 'technology', 'other')),
    recommendation_type VARCHAR(100) NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    priority VARCHAR(10) NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
    estimated_hours INTEGER CHECK (estimated_hours >= 1 AND estimated_hours <= 1000),
    
    -- Implementation tracking
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'implemented', 'declined')),
    implemented_at TIMESTAMP WITH TIME ZONE,
    notes TEXT,
    
    -- Standard fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Foreign key constraints
    CONSTRAINT fk_learning_recommendations_assessment_id FOREIGN KEY (assessment_id) REFERENCES learning_needs_assessments(assessment_id) ON DELETE CASCADE,
    CONSTRAINT fk_learning_recommendations_client_id FOREIGN KEY (client_id) REFERENCES client_profiles(client_id) ON DELETE CASCADE,
    CONSTRAINT fk_learning_recommendations_dependant_id FOREIGN KEY (dependant_id) REFERENCES client_dependants(dependant_id) ON DELETE CASCADE,
    CONSTRAINT fk_learning_recommendations_tutor_id FOREIGN KEY (tutor_id) REFERENCES tutor_profiles(tutor_id) ON DELETE SET NULL
);

-- ============================================
-- Messaging Tables
-- ============================================

-- Messaging conversations
CREATE TABLE messaging_conversations (
    conversation_id SERIAL PRIMARY KEY,
    conversation_type VARCHAR(50) NOT NULL CHECK (conversation_type IN ('sms', 'in_app', 'email')),
    
    -- Participants (at least one required)
    client_id INTEGER REFERENCES client_profiles(client_id),
    tutor_id INTEGER REFERENCES tutor_profiles(tutor_id),
    manager_id INTEGER REFERENCES user_accounts(user_id),
    
    -- SMS specific fields
    phone_number VARCHAR(20), -- For SMS conversations
    
    -- Conversation metadata
    subject VARCHAR(255),
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'archived', 'blocked')),
    last_message_at TIMESTAMP WITH TIME ZONE,
    unread_count INTEGER DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    archived_at TIMESTAMP WITH TIME ZONE,
    
    -- Ensure at least one participant
    CONSTRAINT at_least_one_participant CHECK (
        client_id IS NOT NULL OR tutor_id IS NOT NULL OR manager_id IS NOT NULL
    )
);

-- Messaging messages
CREATE TABLE messaging_messages (
    message_id SERIAL PRIMARY KEY,
    conversation_id INTEGER NOT NULL REFERENCES messaging_conversations(conversation_id) ON DELETE CASCADE,
    
    -- Sender information
    sender_type VARCHAR(50) NOT NULL CHECK (sender_type IN ('client', 'tutor', 'manager', 'system')),
    sender_id INTEGER REFERENCES user_accounts(user_id),
    
    -- Message content
    message_type VARCHAR(50) DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'file', 'template', 'system')),
    content TEXT NOT NULL,
    
    -- Role indicators
    role_indicator VARCHAR(50), -- '👨‍🏫 TUTOR', '👤 CLIENT', etc.
    
    -- SMS specific
    twilio_message_sid VARCHAR(255),
    sms_status VARCHAR(50),
    sms_error_message TEXT,
    
    -- Read status
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP WITH TIME ZONE,
    
    -- Delivery status
    delivery_status VARCHAR(50) DEFAULT 'sent' CHECK (delivery_status IN ('pending', 'sent', 'delivered', 'failed', 'read')),
    delivered_at TIMESTAMP WITH TIME ZONE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    edited_at TIMESTAMP WITH TIME ZONE,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Messaging templates
CREATE TABLE messaging_templates (
    template_id SERIAL PRIMARY KEY,
    template_name VARCHAR(255) NOT NULL,
    template_type VARCHAR(50) NOT NULL CHECK (template_type IN ('sms', 'email', 'in_app', 'universal')),
    category VARCHAR(100) NOT NULL, -- 'appointment', 'billing', 'reminder', etc.
    
    -- Template content
    subject VARCHAR(255), -- For emails
    content_en TEXT NOT NULL,
    content_fr TEXT NOT NULL,
    
    -- Variables that can be replaced
    variables JSONB, -- {name: 'client_name', description: 'Client full name'}
    
    -- Usage tracking
    usage_count INTEGER DEFAULT 0,
    last_used_at TIMESTAMP WITH TIME ZONE,
    
    -- Metadata
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER REFERENCES user_accounts(user_id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(template_name, template_type)
);

-- Messaging broadcasts
CREATE TABLE messaging_broadcasts (
    broadcast_id SERIAL PRIMARY KEY,
    sender_id INTEGER NOT NULL REFERENCES user_accounts(user_id),
    broadcast_type VARCHAR(50) NOT NULL CHECK (broadcast_type IN ('sms', 'email', 'push', 'all')),
    
    -- Content
    subject VARCHAR(255),
    content TEXT NOT NULL,
    template_id INTEGER REFERENCES messaging_templates(template_id),
    
    -- Targeting
    target_audience VARCHAR(100) NOT NULL, -- 'all_clients', 'all_tutors', 'active_clients', etc.
    filter_criteria JSONB, -- Advanced filtering
    
    -- Status tracking
    status VARCHAR(50) DEFAULT 'draft' CHECK (status IN ('draft', 'scheduled', 'sending', 'sent', 'cancelled')),
    scheduled_at TIMESTAMP WITH TIME ZONE,
    sent_at TIMESTAMP WITH TIME ZONE,
    
    -- Statistics
    total_recipients INTEGER DEFAULT 0,
    successful_sends INTEGER DEFAULT 0,
    failed_sends INTEGER DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Messaging broadcast recipients
CREATE TABLE messaging_broadcast_recipients (
    recipient_id SERIAL PRIMARY KEY,
    broadcast_id INTEGER NOT NULL REFERENCES messaging_broadcasts(broadcast_id) ON DELETE CASCADE,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id),
    
    -- Delivery tracking
    delivery_status VARCHAR(50) DEFAULT 'pending' CHECK (delivery_status IN ('pending', 'sent', 'delivered', 'failed', 'bounced')),
    delivered_at TIMESTAMP WITH TIME ZONE,
    read_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    
    -- Contact method used
    contact_method VARCHAR(50), -- 'sms', 'email', 'push'
    contact_value VARCHAR(255), -- phone number, email, or device token
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(broadcast_id, user_id)
);

-- Messaging attachments
CREATE TABLE messaging_attachments (
    attachment_id SERIAL PRIMARY KEY,
    message_id INTEGER NOT NULL REFERENCES messaging_messages(message_id) ON DELETE CASCADE,
    
    -- File details
    file_name VARCHAR(255) NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    file_size INTEGER NOT NULL, -- in bytes
    file_url TEXT NOT NULL,
    
    -- Security
    upload_token VARCHAR(255),
    is_virus_scanned BOOLEAN DEFAULT FALSE,
    scanned_at TIMESTAMP WITH TIME ZONE,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by INTEGER REFERENCES user_accounts(user_id)
);

-- Push notifications
CREATE TABLE push_notifications (
    notification_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id),
    
    -- Notification content
    title VARCHAR(255) NOT NULL,
    body TEXT NOT NULL,
    data JSONB, -- Additional data payload
    
    -- Delivery
    platform platform_type NOT NULL,
    device_token VARCHAR(500),
    
    -- Status tracking
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'delivered', 'failed', 'expired')),
    sent_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    read_at TIMESTAMP WITH TIME ZONE,
    
    -- Provider tracking
    provider_message_id VARCHAR(255), -- OneSignal or other provider ID
    error_message TEXT,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE
);

-- ============================================
-- Billing Tables
-- ============================================

-- Billing invoices
CREATE TABLE billing_invoices (
    invoice_id SERIAL PRIMARY KEY,
    invoice_number VARCHAR(50) NOT NULL UNIQUE,
    client_id INTEGER NOT NULL REFERENCES client_profiles(client_id),
    appointment_id INTEGER REFERENCES appointment_sessions(appointment_id),
    amount DECIMAL(10, 2) NOT NULL CHECK (amount >= 0),
    currency VARCHAR(3) NOT NULL DEFAULT 'CAD',
    status payment_status NOT NULL DEFAULT 'pending',
    due_date DATE NOT NULL,
    paid_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Billing payments
CREATE TABLE billing_payments (
    payment_id SERIAL PRIMARY KEY,
    invoice_id INTEGER NOT NULL REFERENCES billing_invoices(invoice_id),
    paying_client_id INTEGER NOT NULL REFERENCES client_profiles(client_id),
    stripe_payment_id VARCHAR(255) UNIQUE,
    amount DECIMAL(10, 2) NOT NULL CHECK (amount > 0),
    payment_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    payment_method VARCHAR(50), -- card, bank_transfer, etc.
    status payment_status NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Billing subscriptions
CREATE TABLE billing_subscriptions (
    subscription_id SERIAL PRIMARY KEY,
    client_id INTEGER NOT NULL REFERENCES client_profiles(client_id),
    service_id INTEGER NOT NULL REFERENCES service_catalog(service_id),
    hours_purchased DECIMAL(5, 2) NOT NULL CHECK (hours_purchased > 0),
    hours_remaining DECIMAL(5, 2) NOT NULL CHECK (hours_remaining >= 0),
    expires_at DATE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT valid_hours CHECK (hours_remaining <= hours_purchased)
);

-- Tutor payment records
CREATE TABLE billing_tutor_payments (
    tutor_payment_id SERIAL PRIMARY KEY,
    tutor_id INTEGER NOT NULL REFERENCES tutor_profiles(tutor_id),
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    total_hours DECIMAL(5, 2) NOT NULL DEFAULT 0,
    total_amount DECIMAL(10, 2) NOT NULL DEFAULT 0,
    status payment_status NOT NULL DEFAULT 'pending',
    stripe_payout_id VARCHAR(255),
    paid_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT valid_period CHECK (period_end >= period_start)
);

-- ============================================
-- Indexes for Performance (TutorAide)
-- ============================================

-- User tables indexes
CREATE INDEX idx_user_accounts_email ON user_accounts(email) WHERE deleted_at IS NULL;
CREATE INDEX idx_user_accounts_google_id ON user_accounts(google_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_user_accounts_deleted_at ON user_accounts(deleted_at);
CREATE INDEX idx_user_accounts_preferred_language ON user_accounts(preferred_language);
CREATE INDEX idx_user_accounts_language_updated_at ON user_accounts(language_updated_at);
CREATE INDEX idx_user_accounts_language_auto_detect ON user_accounts(language_auto_detect);
CREATE INDEX idx_user_accounts_email_verified ON user_accounts(email_verified);

CREATE INDEX idx_user_roles_user_id ON user_roles(user_id) WHERE is_active = true;
CREATE INDEX idx_user_consents_user_id ON user_consents(user_id);
CREATE INDEX idx_password_reset_tokens_token ON password_reset_tokens(token) WHERE used_at IS NULL;
CREATE INDEX idx_password_reset_tokens_expires_at ON password_reset_tokens(expires_at) WHERE used_at IS NULL;

-- Email verification indexes
CREATE INDEX idx_email_verification_tokens_user_id ON email_verification_tokens(user_id);
CREATE INDEX idx_email_verification_tokens_token ON email_verification_tokens(token);
CREATE INDEX idx_email_verification_tokens_email ON email_verification_tokens(email);
CREATE INDEX idx_email_verification_tokens_expires_at ON email_verification_tokens(expires_at);

-- Session indexes
CREATE INDEX idx_user_sessions_token_hash ON user_sessions(token_hash) WHERE is_active = true;
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id) WHERE is_active = true;
CREATE INDEX idx_user_sessions_expires_at ON user_sessions(expires_at) WHERE is_active = true;
CREATE INDEX idx_user_sessions_last_activity ON user_sessions(last_activity) WHERE is_active = true;

-- Two-factor auth indexes
CREATE INDEX idx_2fa_setup_user ON two_factor_setups(user_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_2fa_setup_enabled ON two_factor_setups(user_id, is_enabled) WHERE deleted_at IS NULL;
CREATE INDEX idx_2fa_setup_primary ON two_factor_setups(user_id, is_primary) WHERE deleted_at IS NULL AND is_enabled = true;
CREATE INDEX idx_challenge_user_expires ON two_factor_challenges(user_id, expires_at);
CREATE INDEX idx_challenge_verified ON two_factor_challenges(verified_at);
CREATE INDEX idx_device_user_expires ON trusted_devices(user_id, expires_at);
CREATE INDEX idx_device_fingerprint ON trusted_devices(device_fingerprint);

-- Language preference indexes
CREATE INDEX idx_language_history_user_id ON user_language_preferences_history(user_id);
CREATE INDEX idx_language_history_created_at ON user_language_preferences_history(created_at);
CREATE INDEX idx_language_history_change_source ON user_language_preferences_history(change_source);
CREATE INDEX idx_language_analytics_date ON language_usage_analytics(date_recorded);
CREATE INDEX idx_language_analytics_language ON language_usage_analytics(language_code);

-- Formatting preference indexes
CREATE INDEX idx_formatting_preferences_user_id ON user_formatting_preferences(user_id);
CREATE INDEX idx_formatting_preferences_updated_at ON user_formatting_preferences(updated_at);
CREATE INDEX idx_formatting_preferences_history_user_id ON user_formatting_preferences_history(user_id);
CREATE INDEX idx_formatting_preferences_history_created_at ON user_formatting_preferences_history(created_at);
CREATE INDEX idx_formatting_preferences_history_field_name ON user_formatting_preferences_history(field_name);

-- Quick action indexes
CREATE INDEX idx_quick_action_usage_user_id ON quick_action_usage(user_id);
CREATE INDEX idx_quick_action_usage_action_type ON quick_action_usage(action_type);
CREATE INDEX idx_quick_action_usage_executed_at ON quick_action_usage(executed_at DESC);
CREATE INDEX idx_quick_action_usage_success ON quick_action_usage(success);
CREATE INDEX idx_quick_action_analytics ON quick_action_usage(action_type, executed_at DESC, success);

-- Client tables indexes
CREATE INDEX idx_client_profiles_user_id ON client_profiles(user_id);
CREATE INDEX idx_client_dependants_primary_client ON client_dependants(primary_client_id);
CREATE INDEX idx_client_dependants_secondary_client ON client_dependants(secondary_client_id);
CREATE INDEX idx_client_relationships_client_id ON client_relationships(client_id);
CREATE INDEX idx_client_relationships_dependant_id ON client_relationships(dependant_id);
CREATE INDEX idx_client_needs_active ON client_needs(client_id, dependant_id) WHERE is_active = true;

-- Tutor tables indexes
CREATE INDEX idx_tutor_profiles_user_id ON tutor_profiles(user_id);
CREATE INDEX idx_tutor_profiles_postal_code ON tutor_profiles(postal_code);
CREATE INDEX idx_tutor_profiles_location ON tutor_profiles(latitude, longitude);
CREATE INDEX idx_tutor_availability_tutor_id ON tutor_availability(tutor_id) WHERE is_active = true;
CREATE INDEX idx_tutor_time_off_tutor_dates ON tutor_time_off(tutor_id, start_date, end_date);
CREATE INDEX idx_tutor_service_rates_tutor_service ON tutor_service_rates(tutor_id, service_id);

-- Additional tutor profile indexes (from migration 031)
CREATE INDEX idx_tutor_degree_level ON tutor_profiles(highest_degree_level);
CREATE INDEX idx_tutor_years_experience ON tutor_profiles(years_of_experience);
CREATE INDEX idx_tutor_verification_status ON tutor_profiles(verification_status);
CREATE INDEX idx_tutor_background_check ON tutor_profiles(background_check_status);
CREATE INDEX idx_tutor_teaching_languages ON tutor_profiles USING GIN(teaching_languages);
CREATE INDEX idx_tutor_certifications ON tutor_profiles USING GIN(teaching_certifications);
CREATE INDEX idx_tutor_references_tutor ON tutor_references(tutor_id);

-- Tutor application system indexes (from migration 032)
CREATE INDEX idx_tutor_invitations_application_status ON tutor_invitations(application_status);
CREATE INDEX idx_tutor_invitations_email_status ON tutor_invitations(email, application_status);
CREATE INDEX idx_rejected_applications_email ON rejected_applications(email);
CREATE INDEX idx_rejected_applications_reapply ON rejected_applications(email, can_reapply_after);

-- Service tables indexes
CREATE INDEX idx_service_catalog_active ON service_catalog(service_id) WHERE is_active = true;
CREATE INDEX idx_service_catalog_subject ON service_catalog(subject_area) WHERE is_active = true;
CREATE INDEX idx_service_packages_service ON service_packages(service_id) WHERE is_active = true;

-- Appointment tables indexes
CREATE INDEX idx_appointment_sessions_tutor_date ON appointment_sessions(tutor_id, scheduled_date);
CREATE INDEX idx_appointment_sessions_status ON appointment_sessions(status) WHERE status != 'cancelled';
CREATE INDEX idx_appointment_sessions_date ON appointment_sessions(scheduled_date) WHERE status != 'cancelled';
CREATE INDEX idx_appointment_participants_appointment ON appointment_participants(appointment_id);
CREATE INDEX idx_appointment_participants_user ON appointment_participants(user_id);
CREATE INDEX idx_appointment_audit_logs_appointment ON appointment_audit_logs(appointment_id);
CREATE INDEX idx_appointment_audit_logs_performed_by ON appointment_audit_logs(performed_by);
CREATE INDEX idx_appointment_audit_logs_created_at ON appointment_audit_logs(created_at);
CREATE INDEX idx_appointment_audit_logs_action ON appointment_audit_logs(action);

-- Billing audit log indexes
CREATE INDEX idx_billing_audit_logs_entity ON billing_audit_logs(entity_type, entity_id);
CREATE INDEX idx_billing_audit_logs_performed_by ON billing_audit_logs(performed_by);
CREATE INDEX idx_billing_audit_logs_created_at ON billing_audit_logs(created_at);
CREATE INDEX idx_billing_audit_logs_action ON billing_audit_logs(action);
CREATE INDEX idx_billing_audit_logs_stripe_id ON billing_audit_logs(stripe_transaction_id) WHERE stripe_transaction_id IS NOT NULL;

-- Assessment indexes





-- Messaging indexes
CREATE INDEX idx_messaging_conversations_client ON messaging_conversations(client_id);
CREATE INDEX idx_messaging_conversations_tutor ON messaging_conversations(tutor_id);
CREATE INDEX idx_messaging_conversations_manager ON messaging_conversations(manager_id);
CREATE INDEX idx_messaging_conversations_status ON messaging_conversations(status);
CREATE INDEX idx_messaging_conversations_last_message ON messaging_conversations(last_message_at DESC);

CREATE INDEX idx_messaging_messages_conversation ON messaging_messages(conversation_id);
CREATE INDEX idx_messaging_messages_sender ON messaging_messages(sender_id);
CREATE INDEX idx_messaging_messages_created ON messaging_messages(created_at DESC);
CREATE INDEX idx_messaging_messages_read ON messaging_messages(is_read) WHERE is_read = false;

CREATE INDEX idx_messaging_templates_type ON messaging_templates(template_type);
CREATE INDEX idx_messaging_templates_category ON messaging_templates(category);
CREATE INDEX idx_messaging_templates_active ON messaging_templates(is_active);

CREATE INDEX idx_messaging_broadcasts_sender ON messaging_broadcasts(sender_id);
CREATE INDEX idx_messaging_broadcasts_status ON messaging_broadcasts(status);
CREATE INDEX idx_messaging_broadcasts_scheduled ON messaging_broadcasts(scheduled_at);

CREATE INDEX idx_broadcast_recipients_broadcast ON messaging_broadcast_recipients(broadcast_id);
CREATE INDEX idx_broadcast_recipients_user ON messaging_broadcast_recipients(user_id);
CREATE INDEX idx_broadcast_recipients_status ON messaging_broadcast_recipients(delivery_status);

CREATE INDEX idx_push_notifications_user ON push_notifications(user_id);
CREATE INDEX idx_push_notifications_status ON push_notifications(status);
CREATE INDEX idx_push_notifications_created ON push_notifications(created_at DESC);

-- Billing tables indexes
CREATE INDEX idx_billing_invoices_client ON billing_invoices(client_id);
CREATE INDEX idx_billing_invoices_status ON billing_invoices(status) WHERE status != 'paid';
CREATE INDEX idx_billing_invoices_due_date ON billing_invoices(due_date) WHERE status = 'pending';
CREATE INDEX idx_billing_payments_invoice ON billing_payments(invoice_id);
CREATE INDEX idx_billing_subscriptions_client ON billing_subscriptions(client_id) WHERE is_active = true;
CREATE INDEX idx_billing_tutor_payments_tutor ON billing_tutor_payments(tutor_id);
CREATE INDEX idx_billing_tutor_payments_period ON billing_tutor_payments(period_start, period_end);

-- ============================================
-- Triggers for Updated Timestamps (TutorAide)
-- ============================================

-- Create triggers for all tables with updated_at
CREATE TRIGGER update_user_accounts_updated_at BEFORE UPDATE ON user_accounts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_push_notification_tokens_updated_at BEFORE UPDATE ON push_notification_tokens
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notification_preferences_updated_at BEFORE UPDATE ON notification_preferences
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_client_profiles_updated_at BEFORE UPDATE ON client_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_client_dependants_updated_at BEFORE UPDATE ON client_dependants
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_client_needs_updated_at BEFORE UPDATE ON client_needs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_client_tutor_requests_updated_at BEFORE UPDATE ON client_tutor_requests
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tutor_profiles_updated_at BEFORE UPDATE ON tutor_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tutor_availability_updated_at BEFORE UPDATE ON tutor_availability
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tutor_time_off_updated_at BEFORE UPDATE ON tutor_time_off
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tutor_service_rates_updated_at BEFORE UPDATE ON tutor_service_rates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tutor_references_updated_at BEFORE UPDATE ON tutor_references
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_service_catalog_updated_at BEFORE UPDATE ON service_catalog
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_service_packages_updated_at BEFORE UPDATE ON service_packages
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_appointment_sessions_updated_at BEFORE UPDATE ON appointment_sessions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_appointment_participants_updated_at BEFORE UPDATE ON appointment_participants
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_billing_invoices_updated_at BEFORE UPDATE ON billing_invoices
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_billing_subscriptions_updated_at BEFORE UPDATE ON billing_subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_billing_tutor_payments_updated_at BEFORE UPDATE ON billing_tutor_payments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_sessions_updated_at BEFORE UPDATE ON user_sessions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_two_factor_setups_updated_at BEFORE UPDATE ON two_factor_setups
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_two_factor_challenges_updated_at BEFORE UPDATE ON two_factor_challenges
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_trusted_devices_updated_at BEFORE UPDATE ON trusted_devices
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_email_verification_tokens_updated_at BEFORE UPDATE ON email_verification_tokens
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_quick_action_usage_updated_at BEFORE UPDATE ON quick_action_usage
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_messaging_conversations_updated_at BEFORE UPDATE ON messaging_conversations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_messaging_templates_updated_at BEFORE UPDATE ON messaging_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_messaging_broadcasts_updated_at BEFORE UPDATE ON messaging_broadcasts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_formatting_preferences_updated_at BEFORE UPDATE ON user_formatting_preferences
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_learning_assessments_updated_at BEFORE UPDATE ON learning_needs_assessments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_assessment_questions_updated_at BEFORE UPDATE ON assessment_questions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_assessment_responses_updated_at BEFORE UPDATE ON assessment_responses
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_assessment_templates_updated_at BEFORE UPDATE ON assessment_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_learning_recommendations_updated_at BEFORE UPDATE ON learning_recommendations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================
-- Additional Functions and Triggers
-- ============================================

-- Function to automatically update language_updated_at timestamp
CREATE OR REPLACE FUNCTION update_language_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    IF OLD.preferred_language IS DISTINCT FROM NEW.preferred_language OR
       OLD.language_auto_detect IS DISTINCT FROM NEW.language_auto_detect OR
       OLD.quebec_french_preference IS DISTINCT FROM NEW.quebec_french_preference THEN
        NEW.language_updated_at = CURRENT_TIMESTAMP;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_language_timestamp
    BEFORE UPDATE ON user_accounts
    FOR EACH ROW
    EXECUTE FUNCTION update_language_timestamp();

-- Function to log language preference changes
CREATE OR REPLACE FUNCTION log_language_preference_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Only log if language actually changed
    IF OLD.preferred_language IS DISTINCT FROM NEW.preferred_language THEN
        INSERT INTO user_language_preferences_history (
            user_id,
            previous_language,
            new_language,
            change_source,
            change_reason
        ) VALUES (
            NEW.user_id,
            OLD.preferred_language,
            NEW.preferred_language,
            COALESCE(NEW.language_source, 'user_manual'),
            CASE 
                WHEN NEW.language_source = 'auto' THEN 'Automatic detection based on browser/context'
                WHEN NEW.language_source = 'manual' THEN 'Manual user selection'
                WHEN NEW.language_source = 'browser' THEN 'Browser language preference detection'
                ELSE 'Language preference updated'
            END
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_log_language_preference_change
    AFTER UPDATE ON user_accounts
    FOR EACH ROW
    EXECUTE FUNCTION log_language_preference_change();

-- Function to log formatting preference changes
CREATE OR REPLACE FUNCTION log_formatting_preference_change()
RETURNS TRIGGER AS $$
DECLARE
    field_name_var VARCHAR(50);
    old_value_var TEXT;
    new_value_var TEXT;
BEGIN
    -- Log each changed field
    IF TG_OP = 'UPDATE' THEN
        -- Date format change
        IF OLD.date_format != NEW.date_format THEN
            INSERT INTO user_formatting_preferences_history 
                (user_id, formatting_preference_id, field_name, old_value, new_value, created_by_user_id)
            VALUES 
                (NEW.user_id, NEW.formatting_preference_id, 'date_format', OLD.date_format, NEW.date_format, NEW.user_id);
        END IF;
        
        -- Time format change
        IF OLD.time_format != NEW.time_format THEN
            INSERT INTO user_formatting_preferences_history 
                (user_id, formatting_preference_id, field_name, old_value, new_value, created_by_user_id)
            VALUES 
                (NEW.user_id, NEW.formatting_preference_id, 'time_format', OLD.time_format, NEW.time_format, NEW.user_id);
        END IF;
        
        -- Currency display change
        IF OLD.currency_display != NEW.currency_display THEN
            INSERT INTO user_formatting_preferences_history 
                (user_id, formatting_preference_id, field_name, old_value, new_value, created_by_user_id)
            VALUES 
                (NEW.user_id, NEW.formatting_preference_id, 'currency_display', OLD.currency_display, NEW.currency_display, NEW.user_id);
        END IF;
        
        -- Number precision change
        IF OLD.number_precision != NEW.number_precision THEN
            INSERT INTO user_formatting_preferences_history 
                (user_id, formatting_preference_id, field_name, old_value, new_value, created_by_user_id)
            VALUES 
                (NEW.user_id, NEW.formatting_preference_id, 'number_precision', OLD.number_precision::TEXT, NEW.number_precision::TEXT, NEW.user_id);
        END IF;
        
        -- Quebec indicators change
        IF OLD.show_quebec_indicators != NEW.show_quebec_indicators THEN
            INSERT INTO user_formatting_preferences_history 
                (user_id, formatting_preference_id, field_name, old_value, new_value, created_by_user_id)
            VALUES 
                (NEW.user_id, NEW.formatting_preference_id, 'show_quebec_indicators', OLD.show_quebec_indicators::TEXT, NEW.show_quebec_indicators::TEXT, NEW.user_id);
        END IF;
        
        -- Relative dates change
        IF OLD.use_relative_dates != NEW.use_relative_dates THEN
            INSERT INTO user_formatting_preferences_history 
                (user_id, formatting_preference_id, field_name, old_value, new_value, created_by_user_id)
            VALUES 
                (NEW.user_id, NEW.formatting_preference_id, 'use_relative_dates', OLD.use_relative_dates::TEXT, NEW.use_relative_dates::TEXT, NEW.user_id);
        END IF;
        
        -- Phone format change
        IF OLD.phone_format != NEW.phone_format THEN
            INSERT INTO user_formatting_preferences_history 
                (user_id, formatting_preference_id, field_name, old_value, new_value, created_by_user_id)
            VALUES 
                (NEW.user_id, NEW.formatting_preference_id, 'phone_format', OLD.phone_format, NEW.phone_format, NEW.user_id);
        END IF;
        
        -- Address format change
        IF OLD.address_format != NEW.address_format THEN
            INSERT INTO user_formatting_preferences_history 
                (user_id, formatting_preference_id, field_name, old_value, new_value, created_by_user_id)
            VALUES 
                (NEW.user_id, NEW.formatting_preference_id, 'address_format', OLD.address_format, NEW.address_format, NEW.user_id);
        END IF;
        
    ELSIF TG_OP = 'INSERT' THEN
        -- Log initial preference creation
        INSERT INTO user_formatting_preferences_history 
            (user_id, formatting_preference_id, field_name, old_value, new_value, created_by_user_id, change_source)
        VALUES 
            (NEW.user_id, NEW.formatting_preference_id, 'preferences_created', NULL, 'initial_setup', NEW.user_id, 'system_update');
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_log_formatting_preference_change
    AFTER INSERT OR UPDATE ON user_formatting_preferences
    FOR EACH ROW
    EXECUTE FUNCTION log_formatting_preference_change();

-- Function to clean up expired 2FA challenges
CREATE OR REPLACE FUNCTION cleanup_expired_2fa_challenges() RETURNS void AS $$
BEGIN
    DELETE FROM two_factor_challenges 
    WHERE expires_at < CURRENT_TIMESTAMP 
    AND verified_at IS NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to check if user needs 2FA
CREATE OR REPLACE FUNCTION user_requires_2fa(p_user_id INTEGER) RETURNS BOOLEAN AS $$
DECLARE
    v_role VARCHAR(50);
    v_has_2fa BOOLEAN;
    v_grace_ends TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Get user role and grace period from user_roles
    SELECT r.role_type INTO v_role
    FROM user_roles r 
    WHERE r.user_id = p_user_id 
    AND r.is_active = true
    LIMIT 1;
    
    -- Get grace period from user_accounts
    SELECT two_factor_grace_ends_at 
    INTO v_grace_ends
    FROM user_accounts
    WHERE user_id = p_user_id;
    
    -- Check if user has active 2FA
    SELECT EXISTS(
        SELECT 1 FROM two_factor_setups 
        WHERE user_id = p_user_id 
        AND is_enabled = true 
        AND deleted_at IS NULL
    ) INTO v_has_2fa;
    
    -- Managers always require 2FA
    IF v_role = 'manager' AND NOT v_has_2fa THEN
        RETURN v_grace_ends IS NULL OR v_grace_ends < CURRENT_TIMESTAMP;
    END IF;
    
    -- Other roles check the requirement flag
    SELECT two_factor_required INTO v_has_2fa
    FROM user_accounts WHERE user_id = p_user_id;
    
    RETURN v_has_2fa;
END;
$$ LANGUAGE plpgsql;

-- ============================================
-- Views
-- ============================================

-- Create view for current user formatting preferences with language info
CREATE VIEW user_formatting_preferences_with_language AS
SELECT 
    ufp.*,
    u.email,
    u.preferred_language,
    u.quebec_french_preference,
    u.language_auto_detect
FROM user_formatting_preferences ufp
JOIN users u ON ufp.user_id = u.user_id
WHERE u.deleted_at IS NULL;

-- Create analytics view for formatting preference usage
CREATE VIEW formatting_preferences_analytics AS
SELECT 
    date_format,
    COUNT(*) as users_count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as percentage
FROM user_formatting_preferences_with_language
GROUP BY date_format
UNION ALL
SELECT 
    'time_format_' || time_format as preference_type,
    COUNT(*) as users_count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as percentage
FROM user_formatting_preferences_with_language
GROUP BY time_format
UNION ALL
SELECT 
    'currency_' || currency_display as preference_type,
    COUNT(*) as users_count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as percentage
FROM user_formatting_preferences_with_language
GROUP BY currency_display;

-- ============================================
-- Initial Data Population (TutorAide)
-- ============================================

-- No initial data needed for service tables as they now use CHECK constraints
-- The valid values are enforced at the database level through CHECK constraints

-- ================================================
-- SEO PLATFORM SCHEMA (seo schema)
-- ================================================

-- Switch to SEO schema
SET search_path TO seo;

-- Create Keyword table in seo schema
CREATE TABLE IF NOT EXISTS seo."Keyword" (
    "id" TEXT NOT NULL DEFAULT gen_random_uuid(),
    "term" TEXT NOT NULL,
    "language" CHAR(2) NOT NULL,
    "location" TEXT,
    "searchVolume" INTEGER,
    "competition" DOUBLE PRECISION,
    "cpc" DOUBLE PRECISION,
    "intent" TEXT,
    "priority" INTEGER NOT NULL DEFAULT 5,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Keyword_pkey" PRIMARY KEY ("id")
);

-- Create unique index on Keyword
CREATE UNIQUE INDEX IF NOT EXISTS seo."Keyword_term_language_location_key" 
ON seo."Keyword"("term", "language", "location");

-- Create Ranking table in seo schema
CREATE TABLE IF NOT EXISTS seo."Ranking" (
    "id" TEXT NOT NULL DEFAULT gen_random_uuid(),
    "keywordId" TEXT NOT NULL,
    "position" INTEGER NOT NULL,
    "url" TEXT,
    "title" TEXT,
    "metaDescription" TEXT,
    "trackedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "serpFeatures" JSONB,
    "competitorDomains" JSONB,

    CONSTRAINT "Ranking_pkey" PRIMARY KEY ("id")
);

-- Create index on Ranking for performance
CREATE INDEX IF NOT EXISTS seo."Ranking_keywordId_trackedAt_idx" 
ON seo."Ranking"("keywordId", "trackedAt" DESC);

-- Create Competitor table
CREATE TABLE IF NOT EXISTS seo."Competitor" (
    "id" TEXT NOT NULL DEFAULT gen_random_uuid(),
    "domain" TEXT NOT NULL,
    "businessName" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "services" JSONB,
    "locations" TEXT[],
    "trackingKeywords" TEXT[],
    "lastAnalyzed" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Competitor_pkey" PRIMARY KEY ("id")
);

-- Create unique index on Competitor domain
CREATE UNIQUE INDEX IF NOT EXISTS seo."Competitor_domain_key" ON seo."Competitor"("domain");

-- Create ContentPage table
CREATE TABLE IF NOT EXISTS seo."ContentPage" (
    "id" TEXT NOT NULL DEFAULT gen_random_uuid(),
    "slug" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "titleFr" TEXT,
    "titleEn" TEXT,
    "metaDescription" TEXT,
    "metaDescriptionFr" TEXT,
    "metaDescriptionEn" TEXT,
    "content" TEXT,
    "contentFr" TEXT,
    "contentEn" TEXT,
    "language" CHAR(2) NOT NULL,
    "pageType" TEXT,
    "targetKeywords" TEXT[],
    "aiGenerated" BOOLEAN NOT NULL DEFAULT false,
    "aiPromptUsed" TEXT,
    "performanceScore" DOUBLE PRECISION,
    "status" TEXT NOT NULL DEFAULT 'draft',
    "publishedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ContentPage_pkey" PRIMARY KEY ("id")
);

-- Create unique index on ContentPage slug
CREATE UNIQUE INDEX IF NOT EXISTS seo."ContentPage_slug_key" ON seo."ContentPage"("slug");

-- Create AutomationLog table
CREATE TABLE IF NOT EXISTS seo."AutomationLog" (
    "id" TEXT NOT NULL DEFAULT gen_random_uuid(),
    "actionType" TEXT NOT NULL,
    "entityType" TEXT NOT NULL,
    "entityId" TEXT,
    "status" TEXT NOT NULL,
    "aiConfidence" DOUBLE PRECISION,
    "humanReviewRequired" BOOLEAN NOT NULL DEFAULT false,
    "reviewedBy" TEXT,
    "reviewedAt" TIMESTAMP(3),
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "AutomationLog_pkey" PRIMARY KEY ("id")
);

-- Create AutomationSchedule table
CREATE TABLE IF NOT EXISTS seo."AutomationSchedule" (
    "id" TEXT NOT NULL DEFAULT gen_random_uuid(),
    "title" TEXT NOT NULL,
    "description" TEXT,
    "type" TEXT NOT NULL,
    "frequency" TEXT NOT NULL,
    "time" TEXT NOT NULL,
    "dayOfWeek" INTEGER,
    "dayOfMonth" INTEGER,
    "nextRun" TIMESTAMP(3) NOT NULL,
    "lastRun" TIMESTAMP(3),
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "config" JSONB,
    "createdBy" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "AutomationSchedule_pkey" PRIMARY KEY ("id")
);

-- Create index for efficient schedule queries
CREATE INDEX IF NOT EXISTS "AutomationSchedule_nextRun_isActive_idx" ON seo."AutomationSchedule"("nextRun", "isActive");

-- Add foreign key constraint for Ranking -> Keyword
ALTER TABLE seo."Ranking" 
ADD CONSTRAINT "Ranking_keywordId_fkey" 
FOREIGN KEY ("keywordId") REFERENCES seo."Keyword"("id") 
ON DELETE RESTRICT ON UPDATE CASCADE;

-- Add trigger for ContentPage updatedAt
DROP TRIGGER IF EXISTS update_content_page_updated_at ON seo."ContentPage";
CREATE TRIGGER update_content_page_updated_at 
BEFORE UPDATE ON seo."ContentPage" 
FOR EACH ROW 
EXECUTE FUNCTION public.update_updated_at_column();

-- Add trigger for AutomationSchedule updatedAt
DROP TRIGGER IF EXISTS update_automation_schedule_updated_at ON seo."AutomationSchedule";
CREATE TRIGGER update_automation_schedule_updated_at 
BEFORE UPDATE ON seo."AutomationSchedule" 
FOR EACH ROW 
EXECUTE FUNCTION public.update_updated_at_column();

-- ================================================
-- SHARED SCHEMA (Views for Cross-App Access)
-- ================================================

-- Switch to shared schema
SET search_path TO shared;

-- Create a read-only view of users for the SEO platform
CREATE OR REPLACE VIEW shared.users_readonly AS 
SELECT 
    u.user_id as id,
    u.email,
    CASE 
        WHEN cp.first_name IS NOT NULL THEN cp.first_name || ' ' || cp.last_name
        WHEN tp.first_name IS NOT NULL THEN tp.first_name || ' ' || tp.last_name
        ELSE 'Unknown User'
    END as name,
    CASE 
        WHEN ur.role_type IS NOT NULL THEN ur.role_type::text
        ELSE 'unknown'
    END as role,
    u.created_at as "createdAt",
    u.updated_at as "updatedAt"
FROM public.user_accounts u
LEFT JOIN public.user_roles ur ON u.user_id = ur.user_id AND ur.is_active = true
LEFT JOIN public.client_profiles cp ON u.user_id = cp.user_id
LEFT JOIN public.tutor_profiles tp ON u.user_id = tp.user_id
WHERE u.deleted_at IS NULL;

-- Create a view for SEO to see service offerings
CREATE OR REPLACE VIEW shared.services_readonly AS
SELECT 
    sc.service_id as id,
    sc.service_name as name,
    sc.subject_area as subject,
    sc.description,
    sc.is_active as "isActive",
    sc.created_at as "createdAt"
FROM public.service_catalog sc
WHERE sc.is_active = true;

-- ================================================
-- PERMISSIONS (Adjust usernames as needed)
-- ================================================

-- Grant permissions for TutorAide app user
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO tutoraide_app_user;
-- GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO tutoraide_app_user;
-- GRANT USAGE ON SCHEMA shared TO tutoraide_app_user;
-- GRANT SELECT ON ALL TABLES IN SCHEMA shared TO tutoraide_app_user;

-- Grant permissions for SEO app user
-- GRANT USAGE ON SCHEMA seo TO seo_app_user;
-- GRANT USAGE ON SCHEMA shared TO seo_app_user;
-- GRANT SELECT ON ALL TABLES IN SCHEMA shared TO seo_app_user;
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA seo TO seo_app_user;
-- GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA seo TO seo_app_user;

-- ================================================
-- COMMENTS FOR DOCUMENTATION
-- ================================================

-- TutorAide Comments
COMMENT ON SCHEMA public IS 'TutorAide core application tables';
COMMENT ON TABLE user_accounts IS 'Base authentication table for all users in the system';
COMMENT ON TABLE client_profiles IS 'Profile information for clients (parents/adult learners)';
COMMENT ON TABLE client_dependants IS 'Dependants (children) of clients who receive tutoring';
COMMENT ON TABLE tutor_profiles IS 'Profile information for tutors';
COMMENT ON TABLE appointment_sessions IS 'Scheduled tutoring sessions between tutors and clients/dependants';
COMMENT ON TABLE billing_invoices IS 'Invoices generated for completed sessions';
COMMENT ON TABLE billing_subscriptions IS 'Pre-paid hour packages for clients';
COMMENT ON COLUMN client_dependants.primary_client_id IS 'Main responsible parent for billing';
COMMENT ON COLUMN client_dependants.secondary_client_id IS 'Optional second parent (separated families)';
COMMENT ON COLUMN billing_payments.paying_client_id IS 'Tracks which parent actually paid (for separated families)';

-- Assessment Comments
COMMENT ON TABLE learning_needs_assessments IS 'Comprehensive learning needs assessments for clients and dependants';
COMMENT ON TABLE assessment_questions IS 'Individual questions within assessments';
COMMENT ON TABLE assessment_responses IS 'Student responses to assessment questions';
COMMENT ON TABLE assessment_templates IS 'Reusable assessment templates';
COMMENT ON TABLE learning_recommendations IS 'Learning recommendations generated from assessments';

-- Messaging Comments
COMMENT ON TABLE messaging_conversations IS 'Conversation threads for SMS, in-app chat, and email';
COMMENT ON TABLE messaging_messages IS 'Individual messages within conversations';
COMMENT ON TABLE messaging_templates IS 'Reusable message templates for common communications';
COMMENT ON TABLE messaging_broadcasts IS 'Mass messaging campaigns';
COMMENT ON TABLE messaging_broadcast_recipients IS 'Recipients and delivery tracking for broadcasts';
COMMENT ON TABLE messaging_attachments IS 'File attachments for messages';
COMMENT ON TABLE push_notifications IS 'Push notification delivery tracking';

-- Authentication Comments
COMMENT ON TABLE user_sessions IS 'Persistent session storage for authenticated users';
COMMENT ON TABLE two_factor_setups IS 'Stores 2FA configuration for users';
COMMENT ON TABLE two_factor_challenges IS 'Temporary 2FA challenge codes sent to users';
COMMENT ON TABLE trusted_devices IS 'Devices that can skip 2FA for a period';
COMMENT ON TABLE email_verification_tokens IS 'Email verification tokens for new accounts';

-- Preference Comments
COMMENT ON TABLE user_language_preferences_history IS 'Tracks all language preference changes for users';
COMMENT ON TABLE language_usage_analytics IS 'Daily analytics for language usage patterns';
COMMENT ON TABLE user_formatting_preferences IS 'User-specific formatting preferences for dates, currency, numbers, and Quebec-specific formatting';
COMMENT ON TABLE user_formatting_preferences_history IS 'Audit trail for formatting preference changes';
COMMENT ON TABLE quick_action_usage IS 'Tracks usage of quick actions for analytics and optimization';
COMMENT ON TABLE user_pinned_actions IS 'Stores user-pinned quick actions for easy access';

-- SEO Comments
COMMENT ON SCHEMA seo IS 'SEO platform tables for keyword tracking and content automation';
COMMENT ON TABLE seo."Keyword" IS 'Keywords to track for SEO performance';
COMMENT ON TABLE seo."Ranking" IS 'Historical ranking data for tracked keywords';
COMMENT ON TABLE seo."Competitor" IS 'Competitor domains to monitor';
COMMENT ON TABLE seo."ContentPage" IS 'SEO-optimized content pages';
COMMENT ON TABLE seo."AutomationLog" IS 'Log of automated SEO actions';
COMMENT ON TABLE seo."AutomationSchedule" IS 'Scheduled automation tasks';

-- Shared Comments
COMMENT ON SCHEMA shared IS 'Shared views for cross-application data access';
COMMENT ON VIEW shared.users_readonly IS 'Read-only view of users for SEO platform';
COMMENT ON VIEW shared.services_readonly IS 'Read-only view of services for SEO platform';

-- ================================================
-- TABLE COMMENTS AND DOCUMENTATION (Migrations 031-032)
-- ================================================

-- Comments for enhanced tutor profiles (migration 031)
COMMENT ON COLUMN tutor_profiles.highest_degree_level IS 'Standardized degree level for filtering';
COMMENT ON COLUMN tutor_profiles.highest_degree_name IS 'Full degree name as written on diploma';
COMMENT ON COLUMN tutor_profiles.additional_education IS 'Array of additional degrees/diplomas as JSONB';
COMMENT ON COLUMN tutor_profiles.certification_details IS 'Detailed certification info including expiry dates';
COMMENT ON COLUMN tutor_profiles.subject_expertise IS 'JSON mapping subjects to expertise levels';
COMMENT ON COLUMN tutor_profiles.language_proficiency IS 'JSON mapping languages to proficiency levels';
COMMENT ON COLUMN tutor_profiles.previous_teaching_positions IS 'Array of previous teaching positions as JSONB';
COMMENT ON TABLE tutor_references IS 'Secure storage for tutor reference contacts - manager access only';

-- Sample data structure comments for migration 031
COMMENT ON COLUMN tutor_profiles.additional_education IS 
'Example: [{"degree": "Bachelor of Education", "major": "Secondary Education", "university": "Concordia University", "year": 2018}]';

COMMENT ON COLUMN tutor_profiles.subject_expertise IS 
'Example: {"mathematics": "expert", "physics": "advanced", "chemistry": "intermediate"}';

COMMENT ON COLUMN tutor_profiles.language_proficiency IS 
'Example: {"english": "native", "french": "fluent", "spanish": "conversational"}';

COMMENT ON COLUMN tutor_profiles.certification_details IS 
'Example: {"qc_teaching_license": {"issuer": "MELS Quebec", "issued_date": "2019-06-15", "expiry_date": "2024-06-15", "number": "QC12345"}}';

-- Comments for tutor application system (migration 032)
COMMENT ON COLUMN tutor_invitations.application_status IS 'Status of the application review process';
COMMENT ON COLUMN tutor_invitations.application_data IS 'Full application data including education, experience, preferences';
COMMENT ON COLUMN tutor_invitations.rejection_reason IS 'Standardized reason for rejection';
COMMENT ON COLUMN tutor_invitations.can_reapply_after IS 'Date after which the applicant can reapply';
COMMENT ON COLUMN tutor_invitations.application_history IS 'Array of previous application attempts';

COMMENT ON TABLE rejected_applications IS 'Historical record of rejected tutor applications for compliance and analytics';

-- ============================================
-- Consent Management System (Migration 011 & 033)
-- ============================================

-- Drop old consent system if exists
DROP TABLE IF EXISTS user_consents CASCADE;
DROP TYPE IF EXISTS consent_level CASCADE;
DROP TYPE IF EXISTS consent_type CASCADE;

-- Create consent document types
CREATE TYPE consent_level_type AS ENUM ('level_1_mandatory', 'level_2_optional');
CREATE TYPE consent_status_type AS ENUM ('granted', 'withdrawn', 'expired');
CREATE TYPE consent_category_type AS ENUM ('legal', 'marketing', 'analytics', 'functional');

-- Create consent_documents table for storing consent document versions
CREATE TABLE IF NOT EXISTS consent_documents (
    document_id SERIAL PRIMARY KEY,
    consent_type VARCHAR(100) NOT NULL,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    version VARCHAR(20) NOT NULL,
    level consent_level_type NOT NULL,
    category consent_category_type NOT NULL,
    language VARCHAR(5) NOT NULL DEFAULT 'en',
    is_active BOOLEAN NOT NULL DEFAULT true,
    effective_date TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT current_timestamp_est(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT current_timestamp_est(),
    deleted_at TIMESTAMP WITH TIME ZONE NULL,
    
    -- Ensure unique active version per consent type and language
    CONSTRAINT unique_active_consent_version UNIQUE (consent_type, language, version) DEFERRABLE INITIALLY DEFERRED
);

-- Create user_consents table for tracking user consent status
CREATE TABLE IF NOT EXISTS user_consents (
    consent_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    document_id INTEGER NOT NULL REFERENCES consent_documents(document_id) ON DELETE CASCADE,
    status consent_status_type NOT NULL DEFAULT 'granted',
    granted_at TIMESTAMP WITH TIME ZONE NULL,
    withdrawn_at TIMESTAMP WITH TIME ZONE NULL,
    expires_at TIMESTAMP WITH TIME ZONE NULL,
    ip_address INET NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT current_timestamp_est(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT current_timestamp_est(),
    deleted_at TIMESTAMP WITH TIME ZONE NULL
);

-- Create consent_history table for audit trail
CREATE TABLE IF NOT EXISTS consent_history (
    history_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    consent_type VARCHAR(100) NOT NULL,
    document_id INTEGER NOT NULL REFERENCES consent_documents(document_id) ON DELETE CASCADE,
    action VARCHAR(50) NOT NULL, -- 'granted', 'withdrawn', 'expired'
    previous_status consent_status_type NULL,
    new_status consent_status_type NOT NULL,
    document_version VARCHAR(20) NOT NULL,
    ip_address INET NULL,
    user_agent TEXT NULL,
    reason TEXT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT current_timestamp_est()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_consent_documents_type_lang 
    ON consent_documents(consent_type, language);
CREATE INDEX IF NOT EXISTS idx_consent_documents_active 
    ON consent_documents(is_active, effective_date);
CREATE INDEX IF NOT EXISTS idx_consent_documents_level 
    ON consent_documents(level);
CREATE INDEX IF NOT EXISTS idx_consent_documents_category 
    ON consent_documents(category);

CREATE INDEX IF NOT EXISTS idx_user_consents_user_id 
    ON user_consents(user_id);
CREATE INDEX IF NOT EXISTS idx_user_consents_document_id 
    ON user_consents(document_id);
CREATE INDEX IF NOT EXISTS idx_user_consents_status 
    ON user_consents(status);
CREATE INDEX IF NOT EXISTS idx_user_consents_granted_at 
    ON user_consents(granted_at);
CREATE INDEX IF NOT EXISTS idx_user_consents_expires_at 
    ON user_consents(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_consents_user_document 
    ON user_consents(user_id, document_id);

CREATE INDEX IF NOT EXISTS idx_consent_history_user_id 
    ON consent_history(user_id);
CREATE INDEX IF NOT EXISTS idx_consent_history_consent_type 
    ON consent_history(consent_type);
CREATE INDEX IF NOT EXISTS idx_consent_history_created_at 
    ON consent_history(created_at);
CREATE INDEX IF NOT EXISTS idx_consent_history_user_type 
    ON consent_history(user_id, consent_type);

-- Create trigger for updated_at on consent_documents
CREATE OR REPLACE FUNCTION update_consent_documents_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = current_timestamp_est();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_consent_documents_updated_at
    BEFORE UPDATE ON consent_documents
    FOR EACH ROW
    EXECUTE FUNCTION update_consent_documents_updated_at();

-- Create trigger for updated_at on user_consents
CREATE OR REPLACE FUNCTION update_user_consents_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = current_timestamp_est();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_user_consents_updated_at
    BEFORE UPDATE ON user_consents
    FOR EACH ROW
    EXECUTE FUNCTION update_user_consents_updated_at();

-- Create trigger to automatically populate granted_at/withdrawn_at timestamps
CREATE OR REPLACE FUNCTION update_consent_timestamps()
RETURNS TRIGGER AS $$
BEGIN
    -- Set granted_at when status changes to granted
    IF NEW.status = 'granted' AND (OLD.status IS NULL OR OLD.status != 'granted') THEN
        NEW.granted_at = current_timestamp_est();
        NEW.withdrawn_at = NULL;
    END IF;
    
    -- Set withdrawn_at when status changes to withdrawn
    IF NEW.status = 'withdrawn' AND (OLD.status IS NULL OR OLD.status != 'withdrawn') THEN
        NEW.withdrawn_at = current_timestamp_est();
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_user_consents_timestamps
    BEFORE INSERT OR UPDATE ON user_consents
    FOR EACH ROW
    EXECUTE FUNCTION update_consent_timestamps();

-- Create trigger to log consent changes to history table
CREATE OR REPLACE FUNCTION log_consent_history()
RETURNS TRIGGER AS $$
DECLARE
    doc_record RECORD;
BEGIN
    -- Get document information
    SELECT consent_type, version INTO doc_record
    FROM consent_documents 
    WHERE document_id = NEW.document_id;
    
    -- Log the consent change
    INSERT INTO consent_history (
        user_id, consent_type, document_id, action, 
        previous_status, new_status, document_version,
        ip_address, user_agent
    ) VALUES (
        NEW.user_id,
        doc_record.consent_type,
        NEW.document_id,
        NEW.status::text,
        CASE WHEN TG_OP = 'UPDATE' THEN OLD.status ELSE NULL END,
        NEW.status,
        doc_record.version,
        NEW.ip_address,
        NEW.user_agent
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_user_consents_history
    AFTER INSERT OR UPDATE ON user_consents
    FOR EACH ROW
    EXECUTE FUNCTION log_consent_history();

-- Insert default consent documents for TutorAide
INSERT INTO consent_documents (
    consent_type, title, content, version, level, category, language, effective_date
) VALUES 
-- Terms of Service (Level 1 Mandatory)
(
    'terms_of_service',
    'Terms of Service',
    'By using TutorAide services, you agree to these terms and conditions. You must comply with all applicable laws and regulations when using our platform. We reserve the right to terminate accounts that violate these terms.',
    '1.0',
    'level_1_mandatory',
    'legal',
    'en',
    current_timestamp_est()
),
(
    'terms_of_service',
    'Conditions d''utilisation',
    'En utilisant les services TutorAide, vous acceptez ces termes et conditions. Vous devez respecter toutes les lois et réglementations applicables lors de l''utilisation de notre plateforme. Nous nous réservons le droit de résilier les comptes qui violent ces conditions.',
    '1.0',
    'level_1_mandatory',
    'legal',
    'fr',
    current_timestamp_est()
),

-- Privacy Policy (Level 1 Mandatory)
(
    'privacy_policy',
    'Privacy Policy',
    'We collect and process your personal data in accordance with applicable privacy laws. Your data is used to provide tutoring services, process payments, and improve our platform. We do not sell your personal information to third parties.',
    '1.0',
    'level_1_mandatory',
    'legal',
    'en',
    current_timestamp_est()
),
(
    'privacy_policy',
    'Politique de confidentialité',
    'Nous collectons et traitons vos données personnelles conformément aux lois applicables sur la confidentialité. Vos données sont utilisées pour fournir des services de tutorat, traiter les paiements et améliorer notre plateforme. Nous ne vendons pas vos informations personnelles à des tiers.',
    '1.0',
    'level_1_mandatory',
    'legal',
    'fr',
    current_timestamp_est()
),

-- Marketing Emails (Level 2 Optional)
(
    'marketing_emails',
    'Marketing Communications',
    'Receive email updates about new tutors, special offers, educational tips, and platform improvements. You can unsubscribe at any time by clicking the unsubscribe link in our emails or updating your preferences.',
    '1.0',
    'level_2_optional',
    'marketing',
    'en',
    current_timestamp_est()
),
(
    'marketing_emails',
    'Communications marketing',
    'Recevez des mises à jour par courriel sur les nouveaux tuteurs, les offres spéciales, les conseils éducatifs et les améliorations de la plateforme. Vous pouvez vous désabonner à tout moment en cliquant sur le lien de désabonnement dans nos courriels ou en mettant à jour vos préférences.',
    '1.0',
    'level_2_optional',
    'marketing',
    'fr',
    current_timestamp_est()
),

-- Usage Analytics (Level 2 Optional)
(
    'usage_analytics',
    'Usage Analytics',
    'Allow us to collect anonymous usage data to improve our platform. This includes page views, feature usage, and performance metrics. No personally identifiable information is collected for analytics purposes.',
    '1.0',
    'level_2_optional',
    'analytics',
    'en',
    current_timestamp_est()
),
(
    'usage_analytics',
    'Analytiques d''utilisation',
    'Permettez-nous de collecter des données d''utilisation anonymes pour améliorer notre plateforme. Cela inclut les pages vues, l''utilisation des fonctionnalités et les métriques de performance. Aucune information personnellement identifiable n''est collectée à des fins analytiques.',
    '1.0',
    'level_2_optional',
    'analytics',
    'fr',
    current_timestamp_est()
),

-- Tutor Employment Agreement (Level 1 Mandatory for Tutors)
(
    'tutor_employment_agreement',
    'Tutor Service Agreement',
    E'TUTORAID INC. INDEPENDENT CONTRACTOR AGREEMENT\n\n' ||
    E'This Service Agreement ("Agreement") is entered into between TutorAide Inc. ("Platform") and you ("Tutor") as an independent contractor.\n\n' ||
    E'1. INDEPENDENT CONTRACTOR STATUS\n' ||
    E'You acknowledge and agree that:\n' ||
    E'• You are an independent contractor, not an employee of TutorAide\n' ||
    E'• You are responsible for your own taxes, insurance, and business expenses\n' ||
    E'• You have the right to accept or decline tutoring requests\n' ||
    E'• You may provide tutoring services through other platforms\n\n' ||
    E'2. SERVICE STANDARDS\n' ||
    E'As a tutor on our platform, you agree to:\n' ||
    E'• Maintain professional conduct in all interactions with clients\n' ||
    E'• Arrive prepared and on time for all scheduled sessions\n' ||
    E'• Provide high-quality educational services\n' ||
    E'• Communicate respectfully with students, parents, and platform staff\n' ||
    E'• Maintain appropriate boundaries with students\n' ||
    E'• Report any concerns about student welfare to appropriate authorities\n\n' ||
    E'3. COMPENSATION AND PAYMENT\n' ||
    E'• Platform fees: TutorAide retains a service fee from client payments\n' ||
    E'• Payment schedule: Weekly payments for completed sessions (Thursday to Wednesday cycle)\n' ||
    E'• Payment method: Direct deposit to your designated Canadian bank account\n' ||
    E'• You are responsible for tracking your income for tax purposes\n' ||
    E'• Payments are processed within 3-5 business days after the payment period\n\n' ||
    E'4. SCHEDULING AND AVAILABILITY\n' ||
    E'• You must maintain accurate availability in your profile\n' ||
    E'• Confirm appointment requests within 24 hours\n' ||
    E'• Provide minimum 24-hour notice for cancellations except in emergencies\n' ||
    E'• Excessive cancellations may result in account review\n\n' ||
    E'5. BACKGROUND VERIFICATION\n' ||
    E'• You consent to background checks as required by law\n' ||
    E'• You must maintain valid documentation to work in Canada\n' ||
    E'• You must disclose any criminal convictions that may affect your ability to work with minors\n' ||
    E'• You agree to maintain required clearances (e.g., vulnerable sector check)\n\n' ||
    E'6. CONFIDENTIALITY AND DATA PROTECTION\n' ||
    E'• You will maintain strict confidentiality of all student information\n' ||
    E'• You will not share student contact information or personal details\n' ||
    E'• You will not contact students or families outside the platform without consent\n' ||
    E'• You will comply with applicable privacy laws including PIPEDA\n\n' ||
    E'7. PROHIBITED CONDUCT\n' ||
    E'You agree NOT to:\n' ||
    E'• Solicit clients to work outside the TutorAide platform\n' ||
    E'• Share inappropriate content with students\n' ||
    E'• Engage in any form of discrimination or harassment\n' ||
    E'• Use student information for personal gain\n' ||
    E'• Provide false information about your qualifications\n' ||
    E'• Accept payments directly from clients for platform-arranged sessions\n\n' ||
    E'8. INTELLECTUAL PROPERTY\n' ||
    E'• You retain rights to your original teaching materials\n' ||
    E'• You grant TutorAide a license to use your profile information for marketing\n' ||
    E'• You will respect copyright laws and not share copyrighted materials without permission\n\n' ||
    E'9. INSURANCE AND LIABILITY\n' ||
    E'• You are encouraged to maintain professional liability insurance\n' ||
    E'• You are responsible for any damages arising from your services\n' ||
    E'• TutorAide''s liability is limited to the platform services provided\n\n' ||
    E'10. TERMINATION\n' ||
    E'• Either party may terminate this agreement with 7 days notice\n' ||
    E'• Immediate termination may occur for violation of terms\n' ||
    E'• Upon termination, you must complete scheduled sessions or provide proper notice\n' ||
    E'• Post-termination obligations regarding confidentiality remain in effect\n\n' ||
    E'11. DISPUTE RESOLUTION\n' ||
    E'• Disputes will first be addressed through platform mediation\n' ||
    E'• Unresolved disputes will be subject to binding arbitration\n' ||
    E'• This agreement is governed by the laws of Quebec, Canada\n\n' ||
    E'12. SAFETY AND MANDATORY REPORTING\n' ||
    E'• You must comply with mandatory reporting laws for suspected child abuse or neglect\n' ||
    E'• You must maintain appropriate professional boundaries\n' ||
    E'• You must report any safety concerns to TutorAide immediately\n\n' ||
    E'By accepting this agreement, you confirm that you have read, understood, and agree to be bound by these terms.',
    '1.0',
    'level_1_mandatory',
    'legal',
    'en',
    CURRENT_TIMESTAMP
),
(
    'tutor_employment_agreement',
    'Entente de service du tuteur',
    E'ENTENTE DE CONTRACTANT INDÉPENDANT TUTORAID INC.\n\n' ||
    E'Cette entente de service ("Entente") est conclue entre TutorAide Inc. ("Plateforme") et vous ("Tuteur") en tant que contractant indépendant.\n\n' ||
    E'1. STATUT DE CONTRACTANT INDÉPENDANT\n' ||
    E'Vous reconnaissez et acceptez que :\n' ||
    E'• Vous êtes un contractant indépendant, non un employé de TutorAide\n' ||
    E'• Vous êtes responsable de vos impôts, assurances et dépenses d''entreprise\n' ||
    E'• Vous avez le droit d''accepter ou de refuser les demandes de tutorat\n' ||
    E'• Vous pouvez offrir des services de tutorat via d''autres plateformes\n\n' ||
    E'2. NORMES DE SERVICE\n' ||
    E'En tant que tuteur sur notre plateforme, vous acceptez de :\n' ||
    E'• Maintenir une conduite professionnelle dans toutes les interactions avec les clients\n' ||
    E'• Arriver préparé et à l''heure pour toutes les sessions planifiées\n' ||
    E'• Fournir des services éducatifs de haute qualité\n' ||
    E'• Communiquer respectueusement avec les étudiants, parents et le personnel\n' ||
    E'• Maintenir des limites appropriées avec les étudiants\n' ||
    E'• Signaler toute préoccupation concernant le bien-être des étudiants\n\n' ||
    E'3. COMPENSATION ET PAIEMENT\n' ||
    E'• Frais de plateforme : TutorAide retient des frais de service sur les paiements\n' ||
    E'• Calendrier de paiement : Paiements hebdomadaires (cycle jeudi à mercredi)\n' ||
    E'• Méthode de paiement : Dépôt direct dans votre compte bancaire canadien\n' ||
    E'• Vous êtes responsable de suivre vos revenus pour fins fiscales\n' ||
    E'• Les paiements sont traités dans les 3-5 jours ouvrables\n\n' ||
    E'4. HORAIRE ET DISPONIBILITÉ\n' ||
    E'• Vous devez maintenir votre disponibilité à jour dans votre profil\n' ||
    E'• Confirmer les demandes de rendez-vous dans les 24 heures\n' ||
    E'• Fournir un préavis minimum de 24 heures pour les annulations\n' ||
    E'• Les annulations excessives peuvent entraîner un examen du compte\n\n' ||
    E'5. VÉRIFICATION DES ANTÉCÉDENTS\n' ||
    E'• Vous consentez aux vérifications d''antécédents requises par la loi\n' ||
    E'• Vous devez maintenir une documentation valide pour travailler au Canada\n' ||
    E'• Vous devez divulguer toute condamnation criminelle\n' ||
    E'• Vous acceptez de maintenir les autorisations requises\n\n' ||
    E'6. CONFIDENTIALITÉ ET PROTECTION DES DONNÉES\n' ||
    E'• Vous maintiendrez la confidentialité de toutes les informations des étudiants\n' ||
    E'• Vous ne partagerez pas les coordonnées ou détails personnels\n' ||
    E'• Vous ne contacterez pas les étudiants en dehors de la plateforme\n' ||
    E'• Vous vous conformerez aux lois sur la confidentialité incluant la LPRPDE\n\n' ||
    E'7. CONDUITE INTERDITE\n' ||
    E'Vous acceptez de NE PAS :\n' ||
    E'• Solliciter les clients pour travailler en dehors de TutorAide\n' ||
    E'• Partager du contenu inapproprié avec les étudiants\n' ||
    E'• Vous engager dans la discrimination ou le harcèlement\n' ||
    E'• Utiliser les informations des étudiants pour un gain personnel\n' ||
    E'• Fournir de fausses informations sur vos qualifications\n' ||
    E'• Accepter des paiements directs des clients\n\n' ||
    E'8. PROPRIÉTÉ INTELLECTUELLE\n' ||
    E'• Vous conservez les droits sur vos matériels pédagogiques originaux\n' ||
    E'• Vous accordez à TutorAide une licence d''utilisation de votre profil\n' ||
    E'• Vous respecterez les lois sur le droit d''auteur\n\n' ||
    E'9. ASSURANCE ET RESPONSABILITÉ\n' ||
    E'• Vous êtes encouragé à maintenir une assurance responsabilité professionnelle\n' ||
    E'• Vous êtes responsable des dommages découlant de vos services\n' ||
    E'• La responsabilité de TutorAide est limitée aux services de plateforme\n\n' ||
    E'10. RÉSILIATION\n' ||
    E'• Chaque partie peut résilier cette entente avec un préavis de 7 jours\n' ||
    E'• Une résiliation immédiate peut survenir pour violation des termes\n' ||
    E'• Lors de la résiliation, vous devez compléter les sessions planifiées\n' ||
    E'• Les obligations de confidentialité restent en vigueur\n\n' ||
    E'11. RÉSOLUTION DES DIFFÉRENDS\n' ||
    E'• Les différends seront d''abord traités par médiation\n' ||
    E'• Les différends non résolus seront soumis à l''arbitrage\n' ||
    E'• Cette entente est régie par les lois du Québec, Canada\n\n' ||
    E'12. SÉCURITÉ ET SIGNALEMENT OBLIGATOIRE\n' ||
    E'• Vous devez respecter les lois sur le signalement obligatoire\n' ||
    E'• Vous devez maintenir des limites professionnelles appropriées\n' ||
    E'• Vous devez signaler immédiatement toute préoccupation de sécurité\n\n' ||
    E'En acceptant cette entente, vous confirmez avoir lu, compris et accepté ces termes.',
    '1.0',
    'level_1_mandatory',
    'legal',
    'fr',
    CURRENT_TIMESTAMP
);

-- Add index for tutor agreement
CREATE INDEX IF NOT EXISTS idx_consent_documents_tutor_agreement 
    ON consent_documents(consent_type) 
    WHERE consent_type = 'tutor_employment_agreement';

-- ================================================
-- VERIFICATION QUERIES
-- ================================================

-- Verify schemas were created
SELECT schema_name 
FROM information_schema.schemata 
WHERE schema_name IN ('public', 'seo', 'shared')
ORDER BY schema_name;

-- List all tables in each schema
SELECT 
    schemaname as schema,
    tablename as table_name
FROM pg_tables
WHERE schemaname IN ('public', 'seo', 'shared')
ORDER BY schemaname, tablename;

-- Show views in shared schema
SELECT 
    table_schema as schema,
    table_name as view_name
FROM information_schema.views
WHERE table_schema = 'shared'
ORDER BY table_name;

-- Count tables in public schema (should be 70+ tables)
SELECT COUNT(*) as tutoraide_table_count
FROM pg_tables
WHERE schemaname = 'public';

-- Count tables in seo schema (should be 6 tables)
SELECT COUNT(*) as seo_table_count
FROM pg_tables
WHERE schemaname = 'seo';