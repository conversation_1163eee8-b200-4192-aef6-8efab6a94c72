/**
 * Client service for managing client profiles and data
 */

import api from './api';

export interface ClientProfile {
  client_id: number;
  user_id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone_number?: string;
  profile_picture?: string;
  address?: {
    street?: string;
    city?: string;
    province?: string;
    postal_code?: string;
    country?: string;
  };
  emergency_contacts?: EmergencyContact[];
  communication_preferences?: {
    email_notifications: boolean;
    sms_notifications: boolean;
    push_notifications: boolean;
    language: 'en' | 'fr';
  };
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface EmergencyContact {
  name: string;
  relationship: string;
  phone_number: string;
  email?: string;
  is_primary: boolean;
}

export interface ClientSearchParams {
  search?: string;
  is_active?: boolean;
  city?: string;
  province?: string;
  page?: number;
  limit?: number;
}

export interface ClientListResponse {
  items: ClientProfile[];
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
}

export interface ClientCreateRequest {
  user_id?: number;
  first_name: string;
  last_name: string;
  email: string;
  phone_number?: string;
  address?: {
    street?: string;
    city?: string;
    province?: string;
    postal_code?: string;
    country?: string;
  };
  emergency_contacts?: EmergencyContact[];
  communication_preferences?: {
    email_notifications: boolean;
    sms_notifications: boolean;
    push_notifications: boolean;
    language: 'en' | 'fr';
  };
}

export interface ClientUpdateRequest {
  first_name?: string;
  last_name?: string;
  phone_number?: string;
  profile_picture?: string;
  address?: {
    street?: string;
    city?: string;
    province?: string;
    postal_code?: string;
    country?: string;
  };
  emergency_contacts?: EmergencyContact[];
  communication_preferences?: {
    email_notifications: boolean;
    sms_notifications: boolean;
    push_notifications: boolean;
    language: 'en' | 'fr';
  };
}

export const clientService = {
  // Search and list clients
  async searchClients(params?: ClientSearchParams): Promise<ClientListResponse> {
    const response = await api.get<ClientListResponse>('/clients', { params });
    return response.data;
  },

  // Get client by ID
  async getClient(clientId: number): Promise<ClientProfile> {
    const response = await api.get<ClientProfile>(`/clients/${clientId}`);
    return response.data;
  },

  // Get client by user ID
  async getClientByUserId(userId: number): Promise<ClientProfile> {
    const response = await api.get<ClientProfile>(`/clients/user/${userId}`);
    return response.data;
  },

  // Create new client profile
  async createClient(data: ClientCreateRequest): Promise<ClientProfile> {
    const response = await api.post<ClientProfile>('/clients', data);
    return response.data;
  },

  // Update client profile
  async updateClient(clientId: number, data: ClientUpdateRequest): Promise<ClientProfile> {
    const response = await api.put<ClientProfile>(`/clients/${clientId}`, data);
    return response.data;
  },

  // Delete client profile (soft delete)
  async deleteClient(clientId: number): Promise<void> {
    await api.delete(`/clients/${clientId}`);
  },

  // Update emergency contacts
  async updateEmergencyContacts(clientId: number, contacts: EmergencyContact[]): Promise<ClientProfile> {
    const response = await api.put<ClientProfile>(`/clients/${clientId}/emergency-contacts`, { contacts });
    return response.data;
  },

  // Update communication preferences
  async updateCommunicationPreferences(
    clientId: number, 
    preferences: ClientProfile['communication_preferences']
  ): Promise<ClientProfile> {
    const response = await api.put<ClientProfile>(`/clients/${clientId}/communication-preferences`, preferences);
    return response.data;
  },

  // Upload profile picture
  async uploadProfilePicture(clientId: number, file: File): Promise<{ url: string }> {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await api.post<{ url: string }>(
      `/clients/${clientId}/profile-picture`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return response.data;
  },

  // Get client's subscription status
  async getClientSubscriptions(clientId: number): Promise<any> {
    const response = await api.get(`/clients/${clientId}/subscriptions`);
    return response.data;
  },

  // Get client's appointment history
  async getClientAppointments(clientId: number, params?: { 
    status?: string;
    from_date?: string;
    to_date?: string;
    limit?: number;
  }): Promise<any> {
    const response = await api.get(`/clients/${clientId}/appointments`, { params });
    return response.data;
  },

  // Get client's dependants
  async getClientDependants(clientId: number): Promise<any[]> {
    const response = await api.get(`/clients/${clientId}/dependants`);
    return response.data;
  },

  // Get client's invoices
  async getClientInvoices(clientId: number, params?: {
    status?: string;
    from_date?: string;
    to_date?: string;
    limit?: number;
  }): Promise<any> {
    const response = await api.get(`/clients/${clientId}/invoices`, { params });
    return response.data;
  }
};