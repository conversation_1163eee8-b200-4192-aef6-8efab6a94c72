import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';
import { 
  DollarSign, Calendar, Clock, Download, 
  FileText, TrendingUp, AlertCircle, Check
} from 'lucide-react';
import api from '../../services/api';
import { Card } from '../common/Card';
import Button from '../common/Button';
import { Badge } from '../common/Badge';
import { Select } from '../common/Select';
import LoadingSpinner from '../ui/LoadingSpinner';
import { EmptyState } from '../common/EmptyState';
import { useAuth } from '../../contexts/AuthContext';
import toast from 'react-hot-toast';

interface TutorPayment {
  payment_id: number;
  payment_period_start: string;
  payment_period_end: string;
  total_hours: number;
  base_amount: number;
  bonus_amount: number;
  total_amount: number;
  status: 'pending' | 'approved' | 'processing' | 'paid' | 'rejected';
  paid_date?: string;
  payment_method?: string;
  payment_reference?: string;
  session_count: number;
}

interface PaymentStatistics {
  total_earned: number;
  total_hours: number;
  average_hourly_rate: number;
  pending_amount: number;
  last_payment_date?: string;
  payment_count: number;
}

const TutorPaymentHistory: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [payments, setPayments] = useState<TutorPayment[]>([]);
  const [statistics, setStatistics] = useState<PaymentStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [yearFilter, setYearFilter] = useState(new Date().getFullYear());
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const pageSize = 20;

  useEffect(() => {
    fetchPaymentHistory();
  }, [yearFilter, statusFilter, currentPage]);

  const fetchPaymentHistory = async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      
      // Fetch payment history
      const params = new URLSearchParams({
        tutor_id: user.userId?.toString() || '1',
        year: yearFilter.toString(),
        limit: pageSize.toString(),
        offset: ((currentPage - 1) * pageSize).toString()
      });
      
      if (statusFilter) {
        params.append('status', statusFilter);
      }
      
      const [paymentsResponse, statsResponse] = await Promise.all([
        api.get<{
          payments: TutorPayment[];
          total: number;
          limit: number;
          offset: number;
        }>(`/billing/tutors/${user.userId}/payment-history?${params}`),
        api.get<PaymentStatistics>(`/billing/tutors/${user.userId}/payment-statistics`)
      ]);
      
      setPayments(paymentsResponse.data.payments);
      setTotalPages(Math.ceil(paymentsResponse.data.total / pageSize));
      setStatistics(statsResponse.data);
      
    } catch (error) {
      console.error('Error fetching payment history:', error);
      // Use mock data in development
      const mockPayments: TutorPayment[] = [
        {
          payment_id: 1,
          payment_period_start: '2024-01-11',
          payment_period_end: '2024-01-17',
          total_hours: 24.5,
          base_amount: 1102.50,
          bonus_amount: 50.00,
          total_amount: 1152.50,
          status: 'paid',
          paid_date: '2024-01-19',
          payment_method: 'Direct Deposit',
          payment_reference: 'DD-2024-01-19-001',
          session_count: 12
        },
        {
          payment_id: 2,
          payment_period_start: '2024-01-04',
          payment_period_end: '2024-01-10',
          total_hours: 20.0,
          base_amount: 900.00,
          bonus_amount: 0,
          total_amount: 900.00,
          status: 'paid',
          paid_date: '2024-01-12',
          payment_method: 'Direct Deposit',
          payment_reference: 'DD-2024-01-12-001',
          session_count: 10
        },
        {
          payment_id: 3,
          payment_period_start: '2023-12-28',
          payment_period_end: '2024-01-03',
          total_hours: 16.0,
          base_amount: 720.00,
          bonus_amount: 0,
          total_amount: 720.00,
          status: 'paid',
          paid_date: '2024-01-05',
          payment_method: 'Direct Deposit',
          payment_reference: 'DD-2024-01-05-001',
          session_count: 8
        },
        {
          payment_id: 4,
          payment_period_start: '2024-01-18',
          payment_period_end: '2024-01-24',
          total_hours: 22.0,
          base_amount: 990.00,
          bonus_amount: 0,
          total_amount: 990.00,
          status: 'processing',
          session_count: 11
        }
      ];
      
      const mockStats: PaymentStatistics = {
        total_earned: 3762.50,
        total_hours: 82.5,
        average_hourly_rate: 45.61,
        pending_amount: 990.00,
        last_payment_date: '2024-01-19',
        payment_count: 4
      };
      
      // Filter by status if needed
      let filteredPayments = mockPayments;
      if (statusFilter) {
        filteredPayments = mockPayments.filter(p => p.status === statusFilter);
      }
      
      setPayments(filteredPayments);
      setTotalPages(1);
      setStatistics(mockStats);
    } finally {
      setLoading(false);
    }
  };

  const downloadPaymentStatement = async (paymentId: number) => {
    try {
      const response = await api.get(`/billing/tutor-payments/${paymentId}/statement`, {
        responseType: 'blob'
      });
      
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `payment-statement-${paymentId}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      
      toast.success(t('billing.tutorPayments.statementDownloaded'));
    } catch (error) {
      console.error('Error downloading statement:', error);
      toast.error(t('billing.errors.downloadStatement'));
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD'
    }).format(amount);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="warning">{t('billing.status.pending')}</Badge>;
      case 'approved':
        return <Badge variant="info">{t('billing.status.approved')}</Badge>;
      case 'processing':
        return <Badge variant="info">{t('billing.status.processing')}</Badge>;
      case 'paid':
        return <Badge variant="success">{t('billing.status.paid')}</Badge>;
      case 'rejected':
        return <Badge variant="danger">{t('billing.status.rejected')}</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  if (loading && payments.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      {statistics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">{t('billing.tutorPayments.totalEarned')}</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {formatCurrency(statistics.total_earned)}
                </p>
              </div>
              <DollarSign className="w-8 h-8 text-green-600" />
            </div>
          </Card>
          
          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">{t('billing.tutorPayments.totalHours')}</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {statistics.total_hours.toFixed(1)}h
                </p>
              </div>
              <Clock className="w-8 h-8 text-red-600" />
            </div>
          </Card>
          
          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">{t('billing.tutorPayments.averageRate')}</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {formatCurrency(statistics.average_hourly_rate)}/hr
                </p>
              </div>
              <TrendingUp className="w-8 h-8 text-accent-red" />
            </div>
          </Card>
          
          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">{t('billing.tutorPayments.pendingAmount')}</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {formatCurrency(statistics.pending_amount)}
                </p>
              </div>
              <AlertCircle className="w-8 h-8 text-yellow-600" />
            </div>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card className="p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('billing.tutorPayments.year')}
            </label>
            <Select
              value={yearFilter.toString()}
              onChange={(e) => {
                setYearFilter(parseInt(e.target.value));
                setCurrentPage(1);
              }}
            >
              {[...Array(5)].map((_, i) => {
                const year = new Date().getFullYear() - i;
                return (
                  <option key={year} value={year}>
                    {year}
                  </option>
                );
              })}
            </Select>
          </div>
          
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('billing.status.label')}
            </label>
            <Select
              value={statusFilter}
              onChange={(e) => {
                setStatusFilter(e.target.value);
                setCurrentPage(1);
              }}
            >
              <option value="">{t('common.all')}</option>
              <option value="pending">{t('billing.status.pending')}</option>
              <option value="approved">{t('billing.status.approved')}</option>
              <option value="processing">{t('billing.status.processing')}</option>
              <option value="paid">{t('billing.status.paid')}</option>
              <option value="rejected">{t('billing.status.rejected')}</option>
            </Select>
          </div>
          
          <div className="flex items-end">
            <Button
              variant="secondary"
              leftIcon={<Download className="w-4 h-4" />}
              onClick={() => {
                window.open(`/api/v1/billing/tutors/${user?.userId}/annual-statement/${yearFilter}`, '_blank');
              }}
            >
              {t('billing.tutorPayments.downloadAnnualStatement')}
            </Button>
          </div>
        </div>
      </Card>

      {/* Payment History */}
      {payments.length === 0 ? (
        <Card>
          <EmptyState
            icon={<DollarSign className="w-12 h-12 text-gray-400" />}
            title={t('billing.tutorPayments.noPayments')}
            description={t('billing.tutorPayments.noPaymentsDescription')}
          />
        </Card>
      ) : (
        <Card className="overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('billing.tutorPayments.period')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('billing.tutorPayments.sessions')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('billing.tutorPayments.hours')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('billing.tutorPayments.amount')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('billing.status.label')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('billing.tutorPayments.paidDate')}
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('common.actions')}
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {payments.map((payment) => (
                  <tr key={payment.payment_id} className="hover:bg-gray-50 transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <Calendar className="w-4 h-4 text-gray-400 mr-2" />
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {format(new Date(payment.payment_period_start), 'MMM d')} - {' '}
                            {format(new Date(payment.payment_period_end), 'MMM d, yyyy')}
                          </p>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {payment.session_count}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {payment.total_hours.toFixed(1)}h
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {formatCurrency(payment.total_amount)}
                        </p>
                        {payment.bonus_amount > 0 && (
                          <p className="text-xs text-gray-500">
                            {t('billing.tutorPayments.includesBonus', { 
                              amount: formatCurrency(payment.bonus_amount) 
                            })}
                          </p>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(payment.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {payment.paid_date ? (
                        <div className="flex items-center">
                          <Check className="w-4 h-4 text-green-600 mr-1" />
                          {format(new Date(payment.paid_date), 'MMM d, yyyy')}
                        </div>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <Button
                        variant="ghost"
                        size="sm"
                        leftIcon={<FileText className="w-4 h-4" />}
                        onClick={() => downloadPaymentStatement(payment.payment_id)}
                      >
                        {t('billing.tutorPayments.viewStatement')}
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="bg-gray-50 px-6 py-3 flex items-center justify-between border-t border-gray-200">
              <div className="flex-1 flex justify-between sm:hidden">
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                >
                  {t('common.previous')}
                </Button>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                >
                  {t('common.next')}
                </Button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    {t('common.showing')} <span className="font-medium">{((currentPage - 1) * pageSize) + 1}</span> {t('common.to')}{' '}
                    <span className="font-medium">{Math.min(currentPage * pageSize, payments.length)}</span> {t('common.of')}{' '}
                    <span className="font-medium">{payments.length}</span> {t('common.results')}
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <button
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {t('common.previous')}
                    </button>
                    <button
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                      className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {t('common.next')}
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          )}
        </Card>
      )}
    </div>
  );
};

export default TutorPaymentHistory;
