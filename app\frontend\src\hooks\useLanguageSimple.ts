import { useState, useEffect, useCallback } from 'react';

export interface LanguageInfo {
  language: string;
  source: string;
  quebec_french: boolean;
  fallback_used: boolean;
  browser_preferences?: any;
}

export const useLanguageSimple = () => {
  const [currentLanguage, setCurrentLanguage] = useState<string>(() => {
    // Get saved language from localStorage or default to 'en'
    return localStorage.getItem('language') || 'en';
  });
  
  const [languageInfo, setLanguageInfo] = useState<LanguageInfo>({
    language: currentLanguage,
    source: 'localStorage',
    quebec_french: false,
    fallback_used: false
  });

  const switchLanguage = useCallback((language: string) => {
    setCurrentLanguage(language);
    localStorage.setItem('language', language);
    setLanguageInfo(prev => ({
      ...prev,
      language,
      source: 'manual_switch'
    }));
    // Trigger a custom event for other components
    window.dispatchEvent(new CustomEvent('languageChanged', { detail: language }));
  }, []);

  // Listen for language changes
  useEffect(() => {
    const handleLanguageChange = (e: CustomEvent) => {
      const newLanguage = e.detail;
      setCurrentLanguage(newLanguage);
      setLanguageInfo(prev => ({
        ...prev,
        language: newLanguage,
        source: 'event'
      }));
    };

    window.addEventListener('languageChanged' as any, handleLanguageChange);
    return () => {
      window.removeEventListener('languageChanged' as any, handleLanguageChange);
    };
  }, []);

  return {
    currentLanguage,
    languageInfo,
    supportedLanguages: ['en', 'fr'],
    availableTranslations: { en: 'English', fr: 'Français' },
    isLoading: false,
    error: null,
    switchLanguage
  };
};