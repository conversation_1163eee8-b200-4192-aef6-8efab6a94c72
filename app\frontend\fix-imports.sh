#!/bin/bash

# Fix imports in services folder
find src/services -name "*.ts" -o -name "*.tsx" | while read file; do
  sed -i "s|from '../../../services/api'|from './api'|g" "$file"
  sed -i "s|from '../../../types/auth'|from '../types/auth'|g" "$file"
done

# Fix imports in components folders
find src/components -name "*.ts" -o -name "*.tsx" | while read file; do
  depth=$(echo "$file" | tr '/' '\n' | grep -c .)
  if [ $depth -eq 3 ]; then
    # components/category/file.tsx
    sed -i "s|from '../../../services/api'|from '../../services/api'|g" "$file"
    sed -i "s|from '../../../types/auth'|from '../../types/auth'|g" "$file"
    sed -i "s|from '../../../contexts/AuthContext'|from '../../contexts/AuthContext'|g" "$file"
    sed -i "s|from '../../../utils/permissions'|from '../../utils/permissions'|g" "$file"
    sed -i "s|from '../../../components/|from '../|g" "$file"
  elif [ $depth -eq 4 ]; then
    # components/category/subcategory/file.tsx
    sed -i "s|from '../../../services/api'|from '../../../services/api'|g" "$file"
    sed -i "s|from '../../../types/auth'|from '../../../types/auth'|g" "$file"
    sed -i "s|from '../../../contexts/AuthContext'|from '../../../contexts/AuthContext'|g" "$file"
    sed -i "s|from '../../../utils/permissions'|from '../../../utils/permissions'|g" "$file"
    sed -i "s|from '../../../components/|from '../../|g" "$file"
  fi
done

# Fix imports in pages folders
find src/pages -name "*.ts" -o -name "*.tsx" | while read file; do
  depth=$(echo "$file" | tr '/' '\n' | grep -c .)
  if [ $depth -eq 3 ]; then
    # pages/category/file.tsx
    sed -i "s|from '../../../services/api'|from '../../services/api'|g" "$file"
    sed -i "s|from '../../../types/auth'|from '../../types/auth'|g" "$file"
    sed -i "s|from '../../../contexts/AuthContext'|from '../../contexts/AuthContext'|g" "$file"
    sed -i "s|from '../../../utils/permissions'|from '../../utils/permissions'|g" "$file"
    sed -i "s|from '../../../components/|from '../../components/|g" "$file"
  fi
done

echo "Import paths fixed!"