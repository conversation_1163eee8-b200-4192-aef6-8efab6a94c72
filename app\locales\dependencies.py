"""
Comprehensive FastAPI dependencies for localization in TutorAide.
"""

from typing import Annotated, Callable, Dict, Any, List
from fastapi import Depends, Request

from .constants import DEFAULT_LANGUAGE, SUPPORTED_LANGUAGES
from .translation_service import TranslationService, get_translation_service
from .middleware import get_request_language, get_request_language_info
from .language_detector import get_language_detector
from .validation import get_translation_validator


# FastAPI dependency for getting current language
def get_current_language(request: Request) -> str:
    """Get the current request's language."""
    return get_request_language(request)


# FastAPI dependency for getting translation service
def get_translations() -> TranslationService:
    """Get the translation service instance."""
    return get_translation_service()


# Annotated types for dependency injection
CurrentLanguage = Annotated[str, Depends(get_current_language)]
TranslationService = Annotated[TranslationService, Depends(get_translations)]


def get_language_info(request: Request) -> Dict[str, Any]:
    """Get comprehensive language information for the current request."""
    return get_request_language_info(request)


def get_enhanced_translation_functions(request: Request) -> Dict[str, Callable]:
    """Get comprehensive translation functions for the current request's language."""
    language = get_request_language(request)
    translation_service = get_translation_service()
    
    return {
        "t": lambda key, **kwargs: translation_service.get_translation(key, language, **kwargs),
        "t_fallback": lambda key, fallback_langs=None, **kwargs: translation_service.get_translation_with_fallback(
            key, language, fallback_langs, **kwargs
        ),
        "t_plural": lambda key, count, **kwargs: translation_service.pluralize(key, count, language, **kwargs),
        "render_template": lambda template, variables: translation_service.render_template(
            template, variables, language
        )
    }


def get_formatting_functions(request: Request) -> Dict[str, Callable]:
    """Get comprehensive formatting functions for the current request's language."""
    language = get_request_language(request)
    translation_service = get_translation_service()
    
    return {
        "format_currency": lambda amount: translation_service.format_currency(amount, language),
        "format_number": lambda number: translation_service.format_number(number, language),
        "format_date": lambda date_obj, fmt="medium": translation_service.format_date(date_obj, fmt, language),
        "format_time": lambda time_obj, fmt="short": translation_service.format_time(time_obj, fmt, language),
        "format_quebec_phone": translation_service.format_quebec_phone,
        "format_quebec_postal_code": translation_service.format_quebec_postal_code,
    }


def get_language_context(request: Request) -> Dict[str, Any]:
    """Get complete language context for templates and responses."""
    language = get_request_language(request)
    language_info = get_request_language_info(request)
    translation_service = get_translation_service()
    
    return {
        # Language metadata
        "language": language,
        "language_info": language_info,
        "supported_languages": SUPPORTED_LANGUAGES,
        "is_quebec_french": language_info.get('quebec_french', False),
        "is_french": language == 'fr',
        "is_english": language == 'en',
        
        # Translation functions
        **get_enhanced_translation_functions(request),
        
        # Formatting functions
        **get_formatting_functions(request),
        
        # Utilities
        "get_language_variants": lambda: get_language_detector().get_language_variants(language),
        "translation_stats": lambda: translation_service.get_translation_statistics()
    }


class EnhancedLocalizedMessages:
    """Enhanced helper class for working with localized messages in endpoints."""
    
    def __init__(self, translation_service: TranslationService, language: str, language_info: Dict[str, Any]):
        self.translation_service = translation_service
        self.language = language
        self.language_info = language_info
    
    def get(self, key: str, **kwargs) -> str:
        """Get a translation for the given key."""
        return self.translation_service.get_translation(key, self.language, **kwargs)
    
    def get_fallback(self, key: str, fallback_languages: List[str] = None, **kwargs) -> str:
        """Get a translation with fallback languages."""
        return self.translation_service.get_translation_with_fallback(
            key, self.language, fallback_languages, **kwargs
        )
    
    def plural(self, key: str, count: int, **kwargs) -> str:
        """Get a pluralized translation."""
        return self.translation_service.pluralize(key, count, self.language, **kwargs)
    
    def success(self, key: str, **kwargs) -> str:
        """Get a success message."""
        return self.get(f"success.{key}", **kwargs)
    
    def error(self, key: str, **kwargs) -> str:
        """Get an error message."""
        return self.get(f"errors.{key}", **kwargs)
    
    def validation_error(self, field: str, error_type: str, **kwargs) -> str:
        """Get a validation error message."""
        return self.get(f"errors.{error_type}", field=field, **kwargs)
    
    def notification(self, notification_type: str, **kwargs) -> str:
        """Get a notification message."""
        return self.get(f"notifications.{notification_type}", **kwargs)
    
    def email_subject(self, email_type: str, **kwargs) -> str:
        """Get an email subject."""
        return self.get(f"email.{email_type}.subject", **kwargs)
    
    def format_currency(self, amount: float) -> str:
        """Format currency amount."""
        return self.translation_service.format_currency(amount, self.language)
    
    def format_number(self, number: float) -> str:
        """Format number."""
        return self.translation_service.format_number(number, self.language)
    
    def format_date(self, date_obj, format_type: str = "medium") -> str:
        """Format date."""
        return self.translation_service.format_date(date_obj, format_type, self.language)
    
    def format_time(self, time_obj, format_type: str = "short") -> str:
        """Format time."""
        return self.translation_service.format_time(time_obj, format_type, self.language)
    
    def format_quebec_phone(self, phone: str) -> str:
        """Format Quebec phone number."""
        return self.translation_service.format_quebec_phone(phone)
    
    def format_quebec_postal_code(self, postal_code: str) -> str:
        """Format Quebec postal code."""
        return self.translation_service.format_quebec_postal_code(postal_code)
    
    def render_template(self, template: str, variables: Dict[str, Any]) -> str:
        """Render template with variables."""
        return self.translation_service.render_template(template, variables, self.language)
    
    @property
    def is_french(self) -> bool:
        """Check if current language is French."""
        return self.language == 'fr'
    
    @property
    def is_english(self) -> bool:
        """Check if current language is English."""
        return self.language == 'en'
    
    @property
    def is_quebec_french(self) -> bool:
        """Check if Quebec French is preferred."""
        return self.language_info.get('quebec_french', False)


class LocalizedMessages:
    """Legacy helper class for backward compatibility."""
    
    def __init__(self, translation_service: TranslationService, language: str):
        self.translation_service = translation_service
        self.language = language
    
    def get(self, key: str, **kwargs) -> str:
        """Get a translation for the given key."""
        return self.translation_service.get_translation(key, self.language, **kwargs)
    
    def success(self, key: str, **kwargs) -> str:
        """Get a success message."""
        return self.get(f"success.{key}", **kwargs)
    
    def error(self, key: str, **kwargs) -> str:
        """Get an error message."""
        return self.get(f"errors.{key}", **kwargs)
    
    def validation_error(self, field: str, error_type: str, **kwargs) -> str:
        """Get a validation error message."""
        return self.get(f"errors.{error_type}", field=field, **kwargs)
    
    def format_currency(self, amount: float) -> str:
        """Format currency amount."""
        return self.translation_service.format_currency(amount, self.language)
    
    def format_date(self, date_obj, format_type: str = "medium") -> str:
        """Format date."""
        return self.translation_service.format_date(date_obj, format_type, self.language)
    
    def format_time(self, time_obj, format_type: str = "short") -> str:
        """Format time."""
        return self.translation_service.format_time(time_obj, format_type, self.language)


def get_enhanced_localized_messages(
    translation_service: TranslationService,
    language: CurrentLanguage,
    request: Request
) -> EnhancedLocalizedMessages:
    """Get enhanced localized messages helper for current request."""
    language_info = get_request_language_info(request)
    return EnhancedLocalizedMessages(translation_service, language, language_info)


def get_localized_messages(
    translation_service: TranslationService,
    language: CurrentLanguage
) -> LocalizedMessages:
    """Get localized messages helper for current request (legacy)."""
    return LocalizedMessages(translation_service, language)


def get_validation_functions() -> Dict[str, Callable]:
    """Get translation validation functions."""
    validator = get_translation_validator()
    
    return {
        "validate_translations": validator.validate_all_translations,
        "validate_key": validator.validate_single_key,
        "suggest_missing": validator.suggest_missing_translations,
        "get_stats": lambda: get_translation_service().get_translation_statistics()
    }


def get_admin_functions(request: Request) -> Dict[str, Any]:
    """Get admin functions for translation management (manager-only)."""
    # This would include authorization check for manager role
    return {
        **get_validation_functions(),
        **get_language_context(request),
        "all_languages": SUPPORTED_LANGUAGES,
        "reload_translations": lambda: get_translation_service().reload_translations()
    }


# Enhanced Annotated types for dependency injection
CurrentLanguage = Annotated[str, Depends(get_current_language)]
LanguageInfo = Annotated[Dict[str, Any], Depends(get_language_info)]
TranslationService = Annotated[TranslationService, Depends(get_translations)]
EnhancedTranslationFunctions = Annotated[Dict[str, Callable], Depends(get_enhanced_translation_functions)]
FormattingFunctions = Annotated[Dict[str, Callable], Depends(get_formatting_functions)]
LanguageContext = Annotated[Dict[str, Any], Depends(get_language_context)]
ValidationFunctions = Annotated[Dict[str, Callable], Depends(get_validation_functions)]
AdminFunctions = Annotated[Dict[str, Any], Depends(get_admin_functions)]

# Message helpers
EnhancedLocalizedMessages = Annotated[EnhancedLocalizedMessages, Depends(get_enhanced_localized_messages)]
LocalizedMessages = Annotated[LocalizedMessages, Depends(get_localized_messages)]

# Legacy compatibility
Messages = LocalizedMessages