import React, { useState, useEffect } from 'react';
import { Modal } from '../common/Modal';
import Button from '../common/Button';
import { Input } from '../common/Input';
import { Select } from '../common/Select';
import { X, Save, User, Mail, Phone, MapPin, AlertTriangle, Heart } from 'lucide-react';
import toast from 'react-hot-toast';
import { useApi } from '../../hooks/useApi';

interface Client {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  postalCode?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  emergencyContactRelationship?: string;
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  province?: string;
  secondaryPhone?: string;
  preferredLanguage?: string;
  timezone?: string;
  communicationPreferences?: {
    email: boolean;
    sms: boolean;
    push: boolean;
  };
  internalNotes?: string;
}

interface ClientEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  client: Client;
  onSuccess?: (updatedClient: Client) => void;
}

export const ClientEditModal: React.FC<ClientEditModalProps> = ({
  isOpen,
  onClose,
  client,
  onSuccess
}) => {
  const { put, loading } = useApi();
  
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    postalCode: '',
    emergencyContactName: '',
    emergencyContactPhone: '',
    emergencyContactRelationship: '',
    addressLine1: '',
    addressLine2: '',
    city: '',
    province: '',
    secondaryPhone: '',
    preferredLanguage: 'en',
    timezone: 'America/Toronto',
    communicationPreferences: {
      email: true,
      sms: true,
      push: true
    },
    internalNotes: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Initialize form data when client changes
  useEffect(() => {
    if (client) {
      setFormData({
        firstName: client.firstName || '',
        lastName: client.lastName || '',
        email: client.email || '',
        phone: client.phone || '',
        postalCode: client.postalCode || '',
        emergencyContactName: client.emergencyContactName || '',
        emergencyContactPhone: client.emergencyContactPhone || '',
        emergencyContactRelationship: client.emergencyContactRelationship || '',
        addressLine1: client.addressLine1 || '',
        addressLine2: client.addressLine2 || '',
        city: client.city || '',
        province: client.province || '',
        secondaryPhone: client.secondaryPhone || '',
        preferredLanguage: client.preferredLanguage || 'en',
        timezone: client.timezone || 'America/Toronto',
        communicationPreferences: client.communicationPreferences || {
          email: true,
          sms: true,
          push: true
        },
        internalNotes: client.internalNotes || ''
      });
    }
  }, [client]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    }
    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    }
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }

    // Validate postal code format if provided
    if (formData.postalCode && !/^[A-Za-z]\d[A-Za-z]\s?\d[A-Za-z]\d$/.test(formData.postalCode)) {
      newErrors.postalCode = 'Invalid Canadian postal code format (A1A 1A1)';
    }

    // Validate phone numbers if provided
    const phonePattern = /^\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}$/;
    if (formData.phone && !phonePattern.test(formData.phone)) {
      newErrors.phone = 'Invalid phone number format';
    }
    if (formData.secondaryPhone && !phonePattern.test(formData.secondaryPhone)) {
      newErrors.secondaryPhone = 'Invalid phone number format';
    }
    if (formData.emergencyContactPhone && !phonePattern.test(formData.emergencyContactPhone)) {
      newErrors.emergencyContactPhone = 'Invalid phone number format';
    }

    // Address completeness validation
    const addressFields = ['addressLine1', 'city', 'province', 'postalCode'];
    const providedAddressFields = addressFields.filter(field => formData[field as keyof typeof formData]);
    if (providedAddressFields.length > 0 && providedAddressFields.length < 4) {
      const missingFields = addressFields.filter(field => !formData[field as keyof typeof formData]);
      newErrors.address = `Complete address required. Missing: ${missingFields.join(', ')}`;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      // Format postal code
      let formattedPostalCode = formData.postalCode;
      if (formattedPostalCode) {
        formattedPostalCode = formattedPostalCode.replace(/\s/g, '').toUpperCase();
        formattedPostalCode = `${formattedPostalCode.slice(0, 3)} ${formattedPostalCode.slice(3)}`;
      }

      const updateData = {
        first_name: formData.firstName,
        last_name: formData.lastName,
        email: formData.email,
        phone: formData.phone || null,
        postal_code: formattedPostalCode || null,
        emergency_contact_name: formData.emergencyContactName || null,
        emergency_contact_phone: formData.emergencyContactPhone || null,
        emergency_contact_relationship: formData.emergencyContactRelationship || null,
        address_line1: formData.addressLine1 || null,
        address_line2: formData.addressLine2 || null,
        city: formData.city || null,
        province: formData.province || null,
        secondary_phone: formData.secondaryPhone || null,
        preferred_language: formData.preferredLanguage,
        timezone: formData.timezone,
        communication_preferences: formData.communicationPreferences,
        internal_notes: formData.internalNotes || null
      };

      const updatedClient = await put<Client>(`/clients/${client.id}`, updateData);
      
      toast.success('Client profile updated successfully');
      
      if (onSuccess) {
        onSuccess(updatedClient);
      }

      onClose();
    } catch (error: any) {
      console.error('Error updating client:', error);
      if (error.response?.data?.detail) {
        toast.error(error.response.data.detail);
      } else {
        toast.error('Failed to update client profile');
      }
    }
  };

  const provinceOptions = [
    { value: '', label: 'Select Province' },
    { value: 'ON', label: 'Ontario' },
    { value: 'QC', label: 'Quebec' },
    { value: 'BC', label: 'British Columbia' },
    { value: 'AB', label: 'Alberta' },
    { value: 'MB', label: 'Manitoba' },
    { value: 'SK', label: 'Saskatchewan' },
    { value: 'NS', label: 'Nova Scotia' },
    { value: 'NB', label: 'New Brunswick' },
    { value: 'NL', label: 'Newfoundland and Labrador' },
    { value: 'PE', label: 'Prince Edward Island' },
    { value: 'NT', label: 'Northwest Territories' },
    { value: 'YT', label: 'Yukon' },
    { value: 'NU', label: 'Nunavut' }
  ];

  const relationshipOptions = [
    { value: '', label: 'Select Relationship' },
    { value: 'spouse', label: 'Spouse/Partner' },
    { value: 'parent', label: 'Parent' },
    { value: 'child', label: 'Child' },
    { value: 'sibling', label: 'Sibling' },
    { value: 'friend', label: 'Friend' },
    { value: 'colleague', label: 'Colleague' },
    { value: 'other', label: 'Other' }
  ];

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`Edit Client - ${client?.firstName} ${client?.lastName}`}
      size="lg"
    >
      <form onSubmit={(e) => { e.preventDefault(); handleSubmit(); }} className="space-y-6">
        {/* Basic Information */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <User className="w-5 h-5 mr-2" />
            Basic Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                First Name <span className="text-red-500">*</span>
              </label>
              <Input
                value={formData.firstName}
                onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}
                placeholder="John"
                error={errors.firstName}
                icon={User}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Last Name <span className="text-red-500">*</span>
              </label>
              <Input
                value={formData.lastName}
                onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}
                placeholder="Doe"
                error={errors.lastName}
              />
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <Mail className="w-5 h-5 mr-2" />
            Contact Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email <span className="text-red-500">*</span>
              </label>
              <Input
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                placeholder="<EMAIL>"
                error={errors.email}
                icon={Mail}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Primary Phone
              </label>
              <Input
                value={formData.phone}
                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                placeholder="(*************"
                error={errors.phone}
                icon={Phone}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Secondary Phone
              </label>
              <Input
                value={formData.secondaryPhone}
                onChange={(e) => setFormData({ ...formData, secondaryPhone: e.target.value })}
                placeholder="(*************"
                error={errors.secondaryPhone}
                icon={Phone}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Postal Code
              </label>
              <Input
                value={formData.postalCode}
                onChange={(e) => setFormData({ ...formData, postalCode: e.target.value })}
                placeholder="H2X 1Y9"
                error={errors.postalCode}
                icon={MapPin}
              />
            </div>
          </div>
        </div>

        {/* Address Information */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <MapPin className="w-5 h-5 mr-2" />
            Address Information
          </h3>
          {errors.address && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-600">{errors.address}</p>
            </div>
          )}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Address Line 1
              </label>
              <Input
                value={formData.addressLine1}
                onChange={(e) => setFormData({ ...formData, addressLine1: e.target.value })}
                placeholder="123 Main Street"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Address Line 2 (Apartment, Suite, etc.)
              </label>
              <Input
                value={formData.addressLine2}
                onChange={(e) => setFormData({ ...formData, addressLine2: e.target.value })}
                placeholder="Apt 4B"
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  City
                </label>
                <Input
                  value={formData.city}
                  onChange={(e) => setFormData({ ...formData, city: e.target.value })}
                  placeholder="Montreal"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Province
                </label>
                <Select
                  value={formData.province}
                  onChange={(e) => setFormData({ ...formData, province: e.target.value })}
                  options={provinceOptions}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Emergency Contact */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <AlertTriangle className="w-5 h-5 mr-2" />
            Emergency Contact
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Contact Name
              </label>
              <Input
                value={formData.emergencyContactName}
                onChange={(e) => setFormData({ ...formData, emergencyContactName: e.target.value })}
                placeholder="Jane Doe"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Contact Phone
              </label>
              <Input
                value={formData.emergencyContactPhone}
                onChange={(e) => setFormData({ ...formData, emergencyContactPhone: e.target.value })}
                placeholder="(*************"
                error={errors.emergencyContactPhone}
                icon={Phone}
              />
            </div>
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Relationship
              </label>
              <Select
                value={formData.emergencyContactRelationship}
                onChange={(e) => setFormData({ ...formData, emergencyContactRelationship: e.target.value })}
                options={relationshipOptions}
              />
            </div>
          </div>
        </div>

        {/* Preferences */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <Heart className="w-5 h-5 mr-2" />
            Preferences
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Preferred Language
              </label>
              <Select
                value={formData.preferredLanguage}
                onChange={(e) => setFormData({ ...formData, preferredLanguage: e.target.value })}
                options={[
                  { value: 'en', label: 'English' },
                  { value: 'fr', label: 'French' }
                ]}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Timezone
              </label>
              <Select
                value={formData.timezone}
                onChange={(e) => setFormData({ ...formData, timezone: e.target.value })}
                options={[
                  { value: 'America/Toronto', label: 'Eastern Time (Toronto)' },
                  { value: 'America/Montreal', label: 'Eastern Time (Montreal)' },
                  { value: 'America/Vancouver', label: 'Pacific Time (Vancouver)' },
                  { value: 'America/Calgary', label: 'Mountain Time (Calgary)' },
                  { value: 'America/Winnipeg', label: 'Central Time (Winnipeg)' }
                ]}
              />
            </div>
          </div>

          {/* Communication Preferences */}
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Communication Preferences
            </label>
            <div className="space-y-2">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.communicationPreferences.email}
                  onChange={(e) => setFormData({
                    ...formData,
                    communicationPreferences: {
                      ...formData.communicationPreferences,
                      email: e.target.checked
                    }
                  })}
                  className="rounded border-gray-300 text-accent-red focus:ring-accent-red"
                />
                <span className="ml-2 text-sm text-gray-700">Email notifications</span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.communicationPreferences.sms}
                  onChange={(e) => setFormData({
                    ...formData,
                    communicationPreferences: {
                      ...formData.communicationPreferences,
                      sms: e.target.checked
                    }
                  })}
                  className="rounded border-gray-300 text-accent-red focus:ring-accent-red"
                />
                <span className="ml-2 text-sm text-gray-700">SMS notifications</span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.communicationPreferences.push}
                  onChange={(e) => setFormData({
                    ...formData,
                    communicationPreferences: {
                      ...formData.communicationPreferences,
                      push: e.target.checked
                    }
                  })}
                  className="rounded border-gray-300 text-accent-red focus:ring-accent-red"
                />
                <span className="ml-2 text-sm text-gray-700">Push notifications</span>
              </label>
            </div>
          </div>
        </div>

        {/* Internal Notes */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Internal Notes (Manager Only)
          </label>
          <textarea
            value={formData.internalNotes}
            onChange={(e) => setFormData({ ...formData, internalNotes: e.target.value })}
            placeholder="Add any internal notes about this client..."
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-red"
          />
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-3 pt-4 border-t">
          <Button
            type="button"
            variant="ghost"
            onClick={onClose}
            disabled={loading}
          >
            <X className="w-4 h-4 mr-2" />
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={loading}
            className="bg-accent-red text-white hover:bg-accent-red-dark"
          >
            <Save className="w-4 h-4 mr-2" />
            {loading ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </form>
    </Modal>
  );
};