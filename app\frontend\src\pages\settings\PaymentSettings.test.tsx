import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { I18nextProvider } from 'react-i18next';
import PaymentSettings from './PaymentSettings';
import i18n from '../../i18n';

const renderPaymentSettings = () => {
  return render(
    <I18nextProvider i18n={i18n}>
      <PaymentSettings />
    </I18nextProvider>
  );
};

describe('PaymentSettings', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders payment settings page', () => {
    renderPaymentSettings();
    
    expect(screen.getByText('Payment Settings')).toBeInTheDocument();
    expect(screen.getByText('Configure payment processing and billing options')).toBeInTheDocument();
  });

  it('displays Stripe configuration section', () => {
    renderPaymentSettings();
    
    expect(screen.getByText('Stripe Configuration')).toBeInTheDocument();
    expect(screen.getByText('Publishable Key')).toBeInTheDocument();
    expect(screen.getByText('Secret Key')).toBeInTheDocument();
    expect(screen.getByText('Webhook Secret')).toBeInTheDocument();
  });

  it('shows Stripe keys with masking', () => {
    renderPaymentSettings();
    
    const secretKeyInput = screen.getByDisplayValue(/sk_live_\*\*\*\*/);
    expect(secretKeyInput).toBeInTheDocument();
    expect(secretKeyInput).toHaveAttribute('type', 'password');
  });

  it('toggles test mode', async () => {
    renderPaymentSettings();
    
    const testModeToggle = screen.getByRole('checkbox', { name: /test mode/i });
    expect(testModeToggle).not.toBeChecked();
    
    fireEvent.click(testModeToggle);
    expect(testModeToggle).toBeChecked();
    
    // Should show test mode warning
    expect(screen.getByText(/Test mode is enabled/)).toBeInTheDocument();
  });

  it('displays payment methods section', () => {
    renderPaymentSettings();
    
    expect(screen.getByText('Accepted Payment Methods')).toBeInTheDocument();
    expect(screen.getByText('Credit Cards')).toBeInTheDocument();
    expect(screen.getByText('Direct Bank Transfer')).toBeInTheDocument();
    expect(screen.getByText('Interac e-Transfer')).toBeInTheDocument();
  });

  it('configures payment method settings', async () => {
    renderPaymentSettings();
    
    const creditCardToggle = screen.getByRole('checkbox', { name: /credit cards/i });
    const bankTransferToggle = screen.getByRole('checkbox', { name: /direct bank transfer/i });
    
    expect(creditCardToggle).toBeChecked();
    expect(bankTransferToggle).toBeChecked();
    
    fireEvent.click(bankTransferToggle);
    expect(bankTransferToggle).not.toBeChecked();
  });

  it('shows currency settings', () => {
    renderPaymentSettings();
    
    expect(screen.getByText('Currency Settings')).toBeInTheDocument();
    expect(screen.getByDisplayValue('CAD')).toBeInTheDocument();
    expect(screen.getByText('Canadian Dollar (CAD)')).toBeInTheDocument();
  });

  it('displays invoice settings', () => {
    renderPaymentSettings();
    
    expect(screen.getByText('Invoice Settings')).toBeInTheDocument();
    expect(screen.getByDisplayValue('INV-')).toBeInTheDocument(); // Invoice prefix
    expect(screen.getByDisplayValue('7')).toBeInTheDocument(); // Payment terms (days)
  });

  it('configures automatic billing', () => {
    renderPaymentSettings();
    
    expect(screen.getByText('Automatic Billing')).toBeInTheDocument();
    expect(screen.getByText('Auto-charge after sessions')).toBeInTheDocument();
    expect(screen.getByText('Auto-retry failed payments')).toBeInTheDocument();
  });

  it('shows tutor payment settings', () => {
    renderPaymentSettings();
    
    expect(screen.getByText('Tutor Payments')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Thursday')).toBeInTheDocument(); // Payment day
    expect(screen.getByDisplayValue('2')).toBeInTheDocument(); // Processing delay days
  });

  it('displays refund policy settings', () => {
    renderPaymentSettings();
    
    expect(screen.getByText('Refund Policy')).toBeInTheDocument();
    expect(screen.getByDisplayValue('24')).toBeInTheDocument(); // Cancellation hours
    expect(screen.getByText('Allow partial refunds')).toBeInTheDocument();
  });

  it('configures late payment fees', async () => {
    const user = userEvent.setup();
    renderPaymentSettings();
    
    const lateFeeToggle = screen.getByRole('checkbox', { name: /enable late fees/i });
    fireEvent.click(lateFeeToggle);
    
    // Should show late fee configuration
    await waitFor(() => {
      expect(screen.getByText('Late Fee Amount')).toBeInTheDocument();
      expect(screen.getByDisplayValue('25.00')).toBeInTheDocument();
    });
  });

  it('shows payment notifications settings', () => {
    renderPaymentSettings();
    
    expect(screen.getByText('Payment Notifications')).toBeInTheDocument();
    expect(screen.getByText('Send payment confirmations')).toBeInTheDocument();
    expect(screen.getByText('Send payment reminders')).toBeInTheDocument();
    expect(screen.getByText('Send failed payment alerts')).toBeInTheDocument();
  });

  it('validates webhook URL format', async () => {
    const user = userEvent.setup();
    renderPaymentSettings();
    
    const webhookInput = screen.getByPlaceholderText('https://your-domain.com/webhooks/stripe');
    await user.clear(webhookInput);
    await user.type(webhookInput, 'invalid-url');
    
    // Should show validation error
    expect(webhookInput).toHaveAttribute('type', 'url');
  });

  it('tests Stripe connection', async () => {
    renderPaymentSettings();
    
    const testConnectionButton = screen.getByRole('button', { name: /test connection/i });
    fireEvent.click(testConnectionButton);
    
    await waitFor(() => {
      expect(screen.getByText('Testing Stripe connection...')).toBeInTheDocument();
    });
  });

  it('displays subscription settings', () => {
    renderPaymentSettings();
    
    expect(screen.getByText('Subscription Settings')).toBeInTheDocument();
    expect(screen.getByText('Enable subscriptions')).toBeInTheDocument();
    expect(screen.getByText('Proration on plan changes')).toBeInTheDocument();
  });

  it('configures tax settings', () => {
    renderPaymentSettings();
    
    expect(screen.getByText('Tax Settings')).toBeInTheDocument();
    expect(screen.getByDisplayValue('13.00')).toBeInTheDocument(); // HST rate
    expect(screen.getByText('Tax-inclusive pricing')).toBeInTheDocument();
  });

  it('shows payment security settings', () => {
    renderPaymentSettings();
    
    expect(screen.getByText('Security Settings')).toBeInTheDocument();
    expect(screen.getByText('Require CVV verification')).toBeInTheDocument();
    expect(screen.getByText('Enable 3D Secure')).toBeInTheDocument();
    expect(screen.getByText('Fraud protection')).toBeInTheDocument();
  });

  it('handles minimum payment amounts', async () => {
    const user = userEvent.setup();
    renderPaymentSettings();
    
    const minAmountInput = screen.getByDisplayValue('10.00');
    await user.clear(minAmountInput);
    await user.type(minAmountInput, '5.00');
    
    expect(minAmountInput).toHaveValue('5.00');
  });

  it('configures payment retries', () => {
    renderPaymentSettings();
    
    expect(screen.getByText('Payment Retries')).toBeInTheDocument();
    expect(screen.getByDisplayValue('3')).toBeInTheDocument(); // Max retry attempts
    expect(screen.getByDisplayValue('72')).toBeInTheDocument(); // Retry interval hours
  });

  it('shows bank account information', () => {
    renderPaymentSettings();
    
    expect(screen.getByText('Bank Account Information')).toBeInTheDocument();
    expect(screen.getByText('Account Name')).toBeInTheDocument();
    expect(screen.getByText('Institution Number')).toBeInTheDocument();
    expect(screen.getByText('Transit Number')).toBeInTheDocument();
  });

  it('validates Canadian bank account format', async () => {
    const user = userEvent.setup();
    renderPaymentSettings();
    
    const transitInput = screen.getByPlaceholderText('00000');
    await user.type(transitInput, '12345');
    
    expect(transitInput).toHaveValue('12345');
    expect(transitInput).toHaveAttribute('maxLength', '5');
  });

  it('saves payment settings', async () => {
    renderPaymentSettings();
    
    const testModeToggle = screen.getByRole('checkbox', { name: /test mode/i });
    fireEvent.click(testModeToggle);
    
    const saveButton = screen.getByRole('button', { name: /save changes/i });
    fireEvent.click(saveButton);
    
    await waitFor(() => {
      expect(screen.getByText('Payment settings saved successfully')).toBeInTheDocument();
    });
  });

  it('shows payment statistics', () => {
    renderPaymentSettings();
    
    expect(screen.getByText('Payment Statistics')).toBeInTheDocument();
    expect(screen.getByText('Total Processed: $45,231')).toBeInTheDocument();
    expect(screen.getByText('Success Rate: 98.5%')).toBeInTheDocument();
    expect(screen.getByText('Failed Payments: 12')).toBeInTheDocument();
  });

  it('handles payment method logos', () => {
    renderPaymentSettings();
    
    // Should show payment method icons
    expect(screen.getByAltText('Visa')).toBeInTheDocument();
    expect(screen.getByAltText('Mastercard')).toBeInTheDocument();
    expect(screen.getByAltText('Interac')).toBeInTheDocument();
  });

  it('configures dispute settings', () => {
    renderPaymentSettings();
    
    expect(screen.getByText('Dispute Management')).toBeInTheDocument();
    expect(screen.getByText('Auto-respond to disputes')).toBeInTheDocument();
    expect(screen.getByText('Dispute notification email')).toBeInTheDocument();
  });

  it('shows compliance information', () => {
    renderPaymentSettings();
    
    expect(screen.getByText('Compliance')).toBeInTheDocument();
    expect(screen.getByText('PCI DSS Compliant')).toBeInTheDocument();
    expect(screen.getByText('PIPEDA Compliant')).toBeInTheDocument();
  });

  it('validates payment settings before saving', async () => {
    const user = userEvent.setup();
    renderPaymentSettings();
    
    // Clear required field
    const secretKeyInput = screen.getByDisplayValue(/sk_live_\*\*\*\*/);
    await user.clear(secretKeyInput);
    
    const saveButton = screen.getByRole('button', { name: /save changes/i });
    fireEvent.click(saveButton);
    
    await waitFor(() => {
      expect(screen.getByText('Secret key is required')).toBeInTheDocument();
    });
  });

  it('shows reset to defaults option', () => {
    renderPaymentSettings();
    
    const resetButton = screen.getByRole('button', { name: /reset to defaults/i });
    expect(resetButton).toBeInTheDocument();
    
    fireEvent.click(resetButton);
    
    // Should show confirmation dialog
    expect(screen.getByText('Reset to Default Settings')).toBeInTheDocument();
  });
});