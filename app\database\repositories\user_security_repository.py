"""
User Security Repository for database operations.
"""

import json
from typing import Optional, List, Dict, Any
from datetime import datetime
from uuid import UUID
import asyncpg

from app.models.user_security_models import (
    UserSecurity, UserSecurityUpdate, SecurityEvent, SecurityEventType
)
from app.core.logging import <PERSON><PERSON><PERSON><PERSON><PERSON>ogger
from app.core.exceptions import DatabaseError, ResourceNotFoundError
from app.core.timezone import now_est

logger = TutorAideLogger.get_logger(__name__)


class UserSecurityRepository:
    """Repository for user security database operations."""
    
    async def get_user_security(
        self,
        conn: asyncpg.Connection,
        user_id: int
    ) -> Optional[UserSecurity]:
        """Get user security settings."""
        
        query = """
            SELECT 
                user_id,
                two_factor_enabled,
                two_factor_method,
                two_factor_secret,
                two_factor_backup_codes,
                two_factor_verified_at,
                trusted_devices,
                require_password_change,
                last_password_change,
                failed_login_attempts,
                locked_until,
                created_at,
                updated_at
            FROM user_security
            WHERE user_id = $1
        """
        
        try:
            row = await conn.fetchrow(query, user_id)
            
            if not row:
                return None
            
            return UserSecurity(
                user_id=row['user_id'],
                two_factor_enabled=row['two_factor_enabled'],
                two_factor_method=row['two_factor_method'],
                two_factor_secret=row['two_factor_secret'],
                two_factor_backup_codes=row['two_factor_backup_codes'] or [],
                two_factor_verified_at=row['two_factor_verified_at'],
                trusted_devices=row['trusted_devices'] or [],
                require_password_change=row['require_password_change'],
                last_password_change=row['last_password_change'],
                failed_login_attempts=row['failed_login_attempts'],
                locked_until=row['locked_until'],
                created_at=row['created_at'],
                updated_at=row['updated_at']
            )
            
        except Exception as e:
            logger.error(f"Failed to get user security: {e}")
            raise DatabaseError(f"Failed to get user security: {str(e)}")
    
    async def create_user_security(
        self,
        conn: asyncpg.Connection,
        security: UserSecurity
    ) -> UserSecurity:
        """Create user security settings."""
        
        query = """
            INSERT INTO user_security (
                user_id,
                two_factor_enabled,
                two_factor_method,
                two_factor_secret,
                two_factor_backup_codes,
                two_factor_verified_at,
                trusted_devices,
                require_password_change,
                last_password_change,
                failed_login_attempts,
                locked_until
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
            RETURNING *
        """
        
        try:
            row = await conn.fetchrow(
                query,
                security.user_id,
                security.two_factor_enabled,
                security.two_factor_method,
                security.two_factor_secret,
                security.two_factor_backup_codes,
                security.two_factor_verified_at,
                json.dumps(security.trusted_devices),
                security.require_password_change,
                security.last_password_change,
                security.failed_login_attempts,
                security.locked_until
            )
            
            return UserSecurity(
                user_id=row['user_id'],
                two_factor_enabled=row['two_factor_enabled'],
                two_factor_method=row['two_factor_method'],
                two_factor_secret=row['two_factor_secret'],
                two_factor_backup_codes=row['two_factor_backup_codes'] or [],
                two_factor_verified_at=row['two_factor_verified_at'],
                trusted_devices=row['trusted_devices'] or [],
                require_password_change=row['require_password_change'],
                last_password_change=row['last_password_change'],
                failed_login_attempts=row['failed_login_attempts'],
                locked_until=row['locked_until'],
                created_at=row['created_at'],
                updated_at=row['updated_at']
            )
            
        except Exception as e:
            logger.error(f"Failed to create user security: {e}")
            raise DatabaseError(f"Failed to create user security: {str(e)}")
    
    async def update_user_security(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        update_data: UserSecurityUpdate,
        metadata: Optional[Dict[str, Any]] = None
    ) -> UserSecurity:
        """Update user security settings."""
        
        # Build dynamic update query
        updates = []
        params = [user_id]
        param_count = 2
        
        update_dict = update_data.dict(exclude_unset=True)
        
        for field, value in update_dict.items():
            if field == 'trusted_devices':
                updates.append(f"{field} = ${param_count}::jsonb")
                params.append(json.dumps(value))
            else:
                updates.append(f"{field} = ${param_count}")
                params.append(value)
            param_count += 1
        
        if not updates:
            # No updates provided, just return current settings
            current = await self.get_user_security(conn, user_id)
            if not current:
                raise ResourceNotFoundError(f"User security not found for user {user_id}")
            return current
        
        query = f"""
            UPDATE user_security
            SET {', '.join(updates)}, updated_at = CURRENT_TIMESTAMP
            WHERE user_id = $1
            RETURNING *
        """
        
        try:
            row = await conn.fetchrow(query, *params)
            
            if not row:
                raise ResourceNotFoundError(f"User security not found for user {user_id}")
            
            # Store metadata if provided (in a separate metadata table in production)
            if metadata:
                await self._store_metadata(conn, user_id, metadata)
            
            return UserSecurity(
                user_id=row['user_id'],
                two_factor_enabled=row['two_factor_enabled'],
                two_factor_method=row['two_factor_method'],
                two_factor_secret=row['two_factor_secret'],
                two_factor_backup_codes=row['two_factor_backup_codes'] or [],
                two_factor_verified_at=row['two_factor_verified_at'],
                trusted_devices=row['trusted_devices'] or [],
                require_password_change=row['require_password_change'],
                last_password_change=row['last_password_change'],
                failed_login_attempts=row['failed_login_attempts'],
                locked_until=row['locked_until'],
                created_at=row['created_at'],
                updated_at=row['updated_at'],
                metadata=metadata
            )
            
        except ResourceNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Failed to update user security: {e}")
            raise DatabaseError(f"Failed to update user security: {str(e)}")
    
    async def add_trusted_device(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        device_info: Dict[str, Any]
    ) -> bool:
        """Add a trusted device."""
        
        query = """
            UPDATE user_security
            SET trusted_devices = trusted_devices || $2::jsonb,
                updated_at = CURRENT_TIMESTAMP
            WHERE user_id = $1
        """
        
        try:
            result = await conn.execute(query, user_id, json.dumps([device_info]))
            return result.split()[-1] == '1'
            
        except Exception as e:
            logger.error(f"Failed to add trusted device: {e}")
            return False
    
    async def remove_trusted_device(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        device_fingerprint: str
    ) -> bool:
        """Remove a trusted device."""
        
        query = """
            UPDATE user_security
            SET trusted_devices = (
                SELECT jsonb_agg(device)
                FROM jsonb_array_elements(trusted_devices) AS device
                WHERE device->>'device_fingerprint' != $2
            ),
            updated_at = CURRENT_TIMESTAMP
            WHERE user_id = $1
        """
        
        try:
            result = await conn.execute(query, user_id, device_fingerprint)
            return result.split()[-1] == '1'
            
        except Exception as e:
            logger.error(f"Failed to remove trusted device: {e}")
            return False
    
    async def update_trusted_device_usage(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        device_fingerprint: str
    ) -> bool:
        """Update last used time for a trusted device."""
        
        query = """
            UPDATE user_security
            SET trusted_devices = (
                SELECT jsonb_agg(
                    CASE 
                        WHEN device->>'device_fingerprint' = $2 
                        THEN jsonb_set(device, '{last_used_at}', to_jsonb($3::text))
                        ELSE device
                    END
                )
                FROM jsonb_array_elements(trusted_devices) AS device
            ),
            updated_at = CURRENT_TIMESTAMP
            WHERE user_id = $1
        """
        
        try:
            result = await conn.execute(
                query, user_id, device_fingerprint, now_est().isoformat()
            )
            return result.split()[-1] == '1'
            
        except Exception as e:
            logger.error(f"Failed to update trusted device usage: {e}")
            return False
    
    async def clear_trusted_devices(
        self,
        conn: asyncpg.Connection,
        user_id: int
    ) -> bool:
        """Clear all trusted devices for a user."""
        
        query = """
            UPDATE user_security
            SET trusted_devices = '[]'::jsonb,
                updated_at = CURRENT_TIMESTAMP
            WHERE user_id = $1
        """
        
        try:
            result = await conn.execute(query, user_id)
            return result.split()[-1] == '1'
            
        except Exception as e:
            logger.error(f"Failed to clear trusted devices: {e}")
            return False
    
    async def log_security_event(
        self,
        conn: asyncpg.Connection,
        event: SecurityEvent
    ) -> None:
        """Log a security event."""
        
        # In production, this would write to a security_events table
        # For now, we'll just log it
        logger.info(
            f"Security Event: {event.event_type.value} for user {event.user_id} "
            f"at {event.timestamp} - {event.event_data}"
        )
        
        # You could implement a security_events table:
        """
        query = '''
            INSERT INTO security_events (
                user_id, event_type, event_data, timestamp, ip_address, user_agent
            ) VALUES ($1, $2, $3, $4, $5, $6)
        '''
        
        try:
            await conn.execute(
                query,
                event.user_id,
                event.event_type.value,
                json.dumps(event.event_data),
                event.timestamp,
                event.ip_address,
                event.user_agent
            )
        except Exception as e:
            logger.error(f"Failed to log security event: {e}")
        """
    
    async def get_security_events(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        event_type: Optional[SecurityEventType] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 100
    ) -> List[SecurityEvent]:
        """Get security events for a user."""
        
        # This would query the security_events table
        # For now, return empty list
        return []
    
    async def get_security_statistics(
        self,
        conn: asyncpg.Connection
    ) -> Dict[str, Any]:
        """Get security statistics."""
        
        query = """
            SELECT 
                COUNT(*) as total_users,
                COUNT(CASE WHEN two_factor_enabled THEN 1 END) as users_with_2fa,
                COUNT(CASE WHEN two_factor_method = 'totp' THEN 1 END) as totp_users,
                COUNT(CASE WHEN two_factor_method = 'sms' THEN 1 END) as sms_users,
                COUNT(CASE WHEN two_factor_method = 'email' THEN 1 END) as email_users,
                COUNT(CASE WHEN require_password_change THEN 1 END) as password_change_required,
                COUNT(CASE WHEN locked_until > CURRENT_TIMESTAMP THEN 1 END) as locked_accounts,
                AVG(failed_login_attempts) as avg_failed_attempts,
                COUNT(CASE WHEN jsonb_array_length(trusted_devices) > 0 THEN 1 END) as users_with_trusted_devices
            FROM user_security
        """
        
        try:
            row = await conn.fetchrow(query)
            
            return {
                'total_users': row['total_users'],
                'users_with_2fa': row['users_with_2fa'],
                '2fa_methods': {
                    'totp': row['totp_users'],
                    'sms': row['sms_users'],
                    'email': row['email_users']
                },
                'password_change_required': row['password_change_required'],
                'locked_accounts': row['locked_accounts'],
                'avg_failed_attempts': float(row['avg_failed_attempts'] or 0),
                'users_with_trusted_devices': row['users_with_trusted_devices']
            }
            
        except Exception as e:
            logger.error(f"Failed to get security statistics: {e}")
            return {}
    
    async def _store_metadata(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        metadata: Dict[str, Any]
    ) -> None:
        """Store metadata (temporary implementation)."""
        
        # In production, this would be stored in a separate metadata table
        # or as part of the user_security table
        logger.debug(f"Storing metadata for user {user_id}: {metadata}")