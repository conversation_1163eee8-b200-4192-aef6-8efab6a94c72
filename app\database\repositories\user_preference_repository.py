"""
User Preference Repository for database operations.
"""

import json
from typing import Optional, List, Dict, Any, <PERSON><PERSON>
from datetime import datetime
import asyncpg

from app.models.user_preference_models import (
    UserPreference, UserPreferenceUpdate, UserPreferenceStats,
    BulkPreferenceUpdate, BulkPreferenceUpdateResult
)
from app.models.user_models import User
from app.core.logging import Tutor<PERSON><PERSON><PERSON>ogger
from app.core.exceptions import DatabaseError, ResourceNotFoundError

logger = TutorAideLogger.get_logger(__name__)


class UserPreferenceRepository:
    """Repository for user preference database operations."""
    
    async def get_user_preferences(
        self,
        user_id: int,
        conn: Optional[asyncpg.Connection] = None
    ) -> Optional[UserPreference]:
        """Get user preferences by user ID."""
        
        query = """
            SELECT 
                user_id,
                preferred_language,
                quebec_french_preference,
                date_format,
                time_format,
                currency_display,
                number_precision,
                ui_settings,
                notification_settings,
                pinned_actions,
                created_at,
                updated_at
            FROM user_preferences
            WHERE user_id = $1
        """
        
        try:
            if conn:
                row = await conn.fetchrow(query, user_id)
            else:
                from app.database.connection import get_db_connection
                async with get_db_connection() as conn:
                    row = await conn.fetchrow(query, user_id)
            
            if not row:
                return None
            
            return UserPreference(
                user_id=row['user_id'],
                preferred_language=row['preferred_language'],
                quebec_french_preference=row['quebec_french_preference'],
                date_format=row['date_format'],
                time_format=row['time_format'],
                currency_display=row['currency_display'],
                number_precision=row['number_precision'],
                ui_settings=row['ui_settings'] or {},
                notification_settings=row['notification_settings'] or {},
                pinned_actions=row['pinned_actions'] or [],
                created_at=row['created_at'],
                updated_at=row['updated_at']
            )
            
        except Exception as e:
            logger.error(f"Failed to get user preferences: {e}")
            raise DatabaseError(f"Failed to get user preferences: {str(e)}")
    
    async def create_user_preferences(
        self,
        preferences: UserPreference,
        conn: Optional[asyncpg.Connection] = None
    ) -> UserPreference:
        """Create new user preferences."""
        
        query = """
            INSERT INTO user_preferences (
                user_id,
                preferred_language,
                quebec_french_preference,
                date_format,
                time_format,
                currency_display,
                number_precision,
                ui_settings,
                notification_settings,
                pinned_actions
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            RETURNING *
        """
        
        try:
            if conn:
                row = await conn.fetchrow(
                    query,
                    preferences.user_id,
                    preferences.preferred_language,
                    preferences.quebec_french_preference,
                    preferences.date_format,
                    preferences.time_format,
                    preferences.currency_display,
                    preferences.number_precision,
                    json.dumps(preferences.ui_settings),
                    json.dumps(preferences.notification_settings),
                    preferences.pinned_actions
                )
            else:
                from app.database.connection import get_db_connection
                async with get_db_connection() as conn:
                    row = await conn.fetchrow(
                        query,
                        preferences.user_id,
                        preferences.preferred_language,
                        preferences.quebec_french_preference,
                        preferences.date_format,
                        preferences.time_format,
                        preferences.currency_display,
                        preferences.number_precision,
                        json.dumps(preferences.ui_settings),
                        json.dumps(preferences.notification_settings),
                        preferences.pinned_actions
                    )
            
            return UserPreference(
                user_id=row['user_id'],
                preferred_language=row['preferred_language'],
                quebec_french_preference=row['quebec_french_preference'],
                date_format=row['date_format'],
                time_format=row['time_format'],
                currency_display=row['currency_display'],
                number_precision=row['number_precision'],
                ui_settings=row['ui_settings'] or {},
                notification_settings=row['notification_settings'] or {},
                pinned_actions=row['pinned_actions'] or [],
                created_at=row['created_at'],
                updated_at=row['updated_at']
            )
            
        except Exception as e:
            logger.error(f"Failed to create user preferences: {e}")
            raise DatabaseError(f"Failed to create user preferences: {str(e)}")
    
    async def update_user_preferences(
        self,
        user_id: int,
        update_data: UserPreferenceUpdate,
        updated_by: Optional[int] = None,
        conn: Optional[asyncpg.Connection] = None
    ) -> UserPreference:
        """Update user preferences."""
        
        # Build dynamic update query
        updates = []
        params = [user_id]
        param_count = 2
        
        update_dict = update_data.dict(exclude_unset=True)
        
        for field, value in update_dict.items():
            if field in ['ui_settings', 'notification_settings']:
                updates.append(f"{field} = ${param_count}::jsonb")
                params.append(json.dumps(value))
            else:
                updates.append(f"{field} = ${param_count}")
                params.append(value)
            param_count += 1
        
        if not updates:
            # No updates provided, just return current preferences
            current = await self.get_user_preferences(user_id, conn)
            if not current:
                raise ResourceNotFoundError(f"User preferences not found for user {user_id}")
            return current
        
        query = f"""
            UPDATE user_preferences
            SET {', '.join(updates)}, updated_at = CURRENT_TIMESTAMP
            WHERE user_id = $1
            RETURNING *
        """
        
        try:
            if conn:
                row = await conn.fetchrow(query, *params)
            else:
                from app.database.connection import get_db_connection
                async with get_db_connection() as conn:
                    row = await conn.fetchrow(query, *params)
            
            if not row:
                raise ResourceNotFoundError(f"User preferences not found for user {user_id}")
            
            return UserPreference(
                user_id=row['user_id'],
                preferred_language=row['preferred_language'],
                quebec_french_preference=row['quebec_french_preference'],
                date_format=row['date_format'],
                time_format=row['time_format'],
                currency_display=row['currency_display'],
                number_precision=row['number_precision'],
                ui_settings=row['ui_settings'] or {},
                notification_settings=row['notification_settings'] or {},
                pinned_actions=row['pinned_actions'] or [],
                created_at=row['created_at'],
                updated_at=row['updated_at']
            )
            
        except ResourceNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Failed to update user preferences: {e}")
            raise DatabaseError(f"Failed to update user preferences: {str(e)}")
    
    async def get_user(
        self,
        user_id: int,
        conn: Optional[asyncpg.Connection] = None
    ) -> Optional[User]:
        """Get user by ID (needed for role information)."""
        
        query = """
            SELECT 
                u.user_id,
                u.email,
                u.password_hash,
                u.google_id,
                u.created_at,
                u.updated_at,
                u.deleted_at,
                u.email_verified,
                u.email_verified_at,
                u.two_factor_required,
                u.two_factor_grace_ends_at,
                COALESCE(
                    ARRAY_AGG(DISTINCT ur.role_type) FILTER (WHERE ur.role_type IS NOT NULL),
                    '{}'::user_role_type[]
                ) as roles
            FROM user_accounts u
            LEFT JOIN user_roles ur ON u.user_id = ur.user_id AND ur.is_active = true
            WHERE u.user_id = $1 AND u.deleted_at IS NULL
            GROUP BY u.user_id
        """
        
        try:
            if conn:
                row = await conn.fetchrow(query, user_id)
            else:
                from app.database.connection import get_db_connection
                async with get_db_connection() as conn:
                    row = await conn.fetchrow(query, user_id)
            
            if not row:
                return None
            
            # Determine primary role
            roles = row['roles'] or []
            primary_role = roles[0] if roles else None
            
            return User(
                user_id=row['user_id'],
                email=row['email'],
                password_hash=row['password_hash'],
                google_id=row['google_id'],
                created_at=row['created_at'],
                updated_at=row['updated_at'],
                deleted_at=row['deleted_at'],
                email_verified=row['email_verified'],
                email_verified_at=row['email_verified_at'],
                two_factor_required=row['two_factor_required'],
                two_factor_grace_ends_at=row['two_factor_grace_ends_at'],
                roles=roles,
                primary_role=primary_role
            )
            
        except Exception as e:
            logger.error(f"Failed to get user: {e}")
            raise DatabaseError(f"Failed to get user: {str(e)}")
    
    async def get_preference_stats(
        self,
        conn: Optional[asyncpg.Connection] = None
    ) -> UserPreferenceStats:
        """Get comprehensive preference statistics."""
        
        query = """
            WITH stats AS (
                SELECT 
                    COUNT(*) as total_users,
                    COUNT(CASE WHEN preferred_language = 'en' THEN 1 END) as english_users,
                    COUNT(CASE WHEN preferred_language = 'fr' THEN 1 END) as french_users,
                    COUNT(CASE WHEN quebec_french_preference = true THEN 1 END) as quebec_french_users,
                    COUNT(CASE WHEN date_format = 'short' THEN 1 END) as short_date_users,
                    COUNT(CASE WHEN date_format = 'medium' THEN 1 END) as medium_date_users,
                    COUNT(CASE WHEN date_format = 'long' THEN 1 END) as long_date_users,
                    COUNT(CASE WHEN date_format = 'full' THEN 1 END) as full_date_users,
                    COUNT(CASE WHEN time_format = '12h' THEN 1 END) as format_12h_users,
                    COUNT(CASE WHEN time_format = '24h' THEN 1 END) as format_24h_users,
                    COUNT(CASE WHEN time_format = 'auto' THEN 1 END) as format_auto_users,
                    COUNT(CASE WHEN array_length(pinned_actions, 1) > 0 THEN 1 END) as users_with_pinned_actions
                FROM user_preferences
            )
            SELECT * FROM stats
        """
        
        try:
            if conn:
                row = await conn.fetchrow(query)
            else:
                from app.database.connection import get_db_connection
                async with get_db_connection() as conn:
                    row = await conn.fetchrow(query)
            
            return UserPreferenceStats(
                total_users=row['total_users'],
                language_distribution={
                    'en': row['english_users'],
                    'fr': row['french_users']
                },
                quebec_french_users=row['quebec_french_users'],
                date_format_distribution={
                    'short': row['short_date_users'],
                    'medium': row['medium_date_users'],
                    'long': row['long_date_users'],
                    'full': row['full_date_users']
                },
                time_format_distribution={
                    '12h': row['format_12h_users'],
                    '24h': row['format_24h_users'],
                    'auto': row['format_auto_users']
                },
                users_with_pinned_actions=row['users_with_pinned_actions']
            )
            
        except Exception as e:
            logger.error(f"Failed to get preference stats: {e}")
            raise DatabaseError(f"Failed to get preference stats: {str(e)}")
    
    async def bulk_update_preferences(
        self,
        user_ids: List[int],
        update_data: UserPreferenceUpdate,
        updated_by: Optional[int] = None,
        conn: Optional[asyncpg.Connection] = None
    ) -> BulkPreferenceUpdateResult:
        """Bulk update preferences for multiple users."""
        
        # Build update statement
        updates = []
        update_dict = update_data.dict(exclude_unset=True)
        
        for field, value in update_dict.items():
            if field in ['ui_settings', 'notification_settings']:
                updates.append(f"{field} = '{json.dumps(value)}'::jsonb")
            elif isinstance(value, str):
                updates.append(f"{field} = '{value}'")
            elif isinstance(value, bool):
                updates.append(f"{field} = {str(value).lower()}")
            elif isinstance(value, list):
                updates.append(f"{field} = ARRAY{value}::text[]")
            else:
                updates.append(f"{field} = {value}")
        
        if not updates:
            return BulkPreferenceUpdateResult(
                success=True,
                updated_count=0,
                failed_count=0,
                failed_user_ids=[],
                errors=[],
                message="No updates provided"
            )
        
        update_clause = ', '.join(updates)
        query = f"""
            UPDATE user_preferences
            SET {update_clause}, updated_at = CURRENT_TIMESTAMP
            WHERE user_id = ANY($1::int[])
            RETURNING user_id
        """
        
        try:
            if conn:
                rows = await conn.fetch(query, user_ids)
            else:
                from app.database.connection import get_db_connection
                async with get_db_connection() as conn:
                    rows = await conn.fetch(query, user_ids)
            
            updated_ids = [row['user_id'] for row in rows]
            failed_ids = [uid for uid in user_ids if uid not in updated_ids]
            
            return BulkPreferenceUpdateResult(
                success=True,
                updated_count=len(updated_ids),
                failed_count=len(failed_ids),
                failed_user_ids=failed_ids,
                errors=[],
                message=f"Successfully updated {len(updated_ids)} users"
            )
            
        except Exception as e:
            logger.error(f"Failed to bulk update preferences: {e}")
            return BulkPreferenceUpdateResult(
                success=False,
                updated_count=0,
                failed_count=len(user_ids),
                failed_user_ids=user_ids,
                errors=[str(e)],
                message="Bulk update failed"
            )
    
    async def get_users_by_language(
        self,
        language: str,
        limit: int = 100,
        offset: int = 0,
        conn: Optional[asyncpg.Connection] = None
    ) -> Tuple[List[int], int]:
        """Get users filtered by language preference."""
        
        count_query = """
            SELECT COUNT(*) FROM user_preferences
            WHERE preferred_language = $1
        """
        
        data_query = """
            SELECT user_id FROM user_preferences
            WHERE preferred_language = $1
            ORDER BY user_id
            LIMIT $2 OFFSET $3
        """
        
        try:
            if conn:
                total = await conn.fetchval(count_query, language)
                rows = await conn.fetch(data_query, language, limit, offset)
            else:
                from app.database.connection import get_db_connection
                async with get_db_connection() as conn:
                    total = await conn.fetchval(count_query, language)
                    rows = await conn.fetch(data_query, language, limit, offset)
            
            user_ids = [row['user_id'] for row in rows]
            return user_ids, total
            
        except Exception as e:
            logger.error(f"Failed to get users by language: {e}")
            raise DatabaseError(f"Failed to get users by language: {str(e)}")
    
    async def get_users_by_notification_preference(
        self,
        notification_type: str,
        channel: str,
        enabled: bool = True,
        limit: int = 100,
        offset: int = 0,
        conn: Optional[asyncpg.Connection] = None
    ) -> Tuple[List[int], int]:
        """Get users filtered by notification preference."""
        
        count_query = """
            SELECT COUNT(*) FROM user_preferences
            WHERE notification_settings->$1->$2 = $3
        """
        
        data_query = """
            SELECT user_id FROM user_preferences
            WHERE notification_settings->$1->$2 = $3
            ORDER BY user_id
            LIMIT $4 OFFSET $5
        """
        
        try:
            if conn:
                total = await conn.fetchval(
                    count_query, notification_type, channel, json.dumps(enabled)
                )
                rows = await conn.fetch(
                    data_query, notification_type, channel, json.dumps(enabled), limit, offset
                )
            else:
                from app.database.connection import get_db_connection
                async with get_db_connection() as conn:
                    total = await conn.fetchval(
                        count_query, notification_type, channel, json.dumps(enabled)
                    )
                    rows = await conn.fetch(
                        data_query, notification_type, channel, json.dumps(enabled), limit, offset
                    )
            
            user_ids = [row['user_id'] for row in rows]
            return user_ids, total
            
        except Exception as e:
            logger.error(f"Failed to get users by notification preference: {e}")
            raise DatabaseError(f"Failed to get users by notification preference: {str(e)}")