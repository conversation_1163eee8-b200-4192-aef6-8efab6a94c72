import React from 'react';
import ReactDOM from 'react-dom/client';
import * as Sentry from '@sentry/react';
import App from './App';
import ErrorBoundary from './components/ErrorBoundary';
import './index.css';

// Initialize Sentry
Sentry.init({
  dsn: import.meta.env.VITE_SENTRY_DSN || "https://<EMAIL>/4509579550851072",
  integrations: [
    // Browser tracing integration
    Sentry.browserTracingIntegration(),
    // React Router integration (will be configured in App.tsx)
    Sentry.replayIntegration({
      maskAllText: false,
      blockAllMedia: false,
    }),
  ],
  // Performance Monitoring
  tracesSampleRate: import.meta.env.PROD 
    ? parseFloat(import.meta.env.VITE_SENTRY_TRACES_SAMPLE_RATE || '0.1')
    : 1.0,
  // Session Replay
  replaysSessionSampleRate: parseFloat(import.meta.env.VITE_SENTRY_REPLAYS_SESSION_SAMPLE_RATE || '0.1'),
  replaysOnErrorSampleRate: parseFloat(import.meta.env.VITE_SENTRY_REPLAYS_ON_ERROR_SAMPLE_RATE || '1.0'),
  // Set environment
  environment: import.meta.env.VITE_SENTRY_ENVIRONMENT || import.meta.env.MODE,
  // Enable debug mode in development
  debug: import.meta.env.DEV,
  // Send default PII data
  sendDefaultPii: true,
  // Set trace propagation targets for distributed tracing
  tracePropagationTargets: [
    "localhost",
    /^https:\/\/tutoraide-production\.up\.railway\.app\/api/,
    /^https:\/\/.*\.tutoraide\.ca\/api/,
  ],
  // Before send hook to filter out certain errors
  beforeSend(event, hint) {
    // Filter out network errors in development
    if (import.meta.env.DEV && hint.originalException?.message?.includes('Network Error')) {
      return null;
    }
    // Filter out cancelled requests
    if (hint.originalException?.message?.includes('cancelled')) {
      return null;
    }
    return event;
  },
});

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <Sentry.ErrorBoundary fallback={ErrorBoundary} showDialog>
      <App />
    </Sentry.ErrorBoundary>
  </React.StrictMode>
);