"""
API endpoints for appointment confirmations and notifications.
"""

from fastapi import APIRouter, HTTPException, Depends, Query, Body
from fastapi.responses import JSONResponse
from typing import Dict, List, Optional, Any
from datetime import date, datetime
from pydantic import BaseModel, Field

from app.core.auth_decorators import require_auth, require_roles
from app.core.exceptions import ResourceNotFoundError, BusinessLogicError, ValidationError
from app.services.appointment_confirmation_service import (
    AppointmentConfirmationService, ConfirmationStatus, NotificationType
)
from app.models.auth_models import UserRoleType
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/appointment-confirmations", tags=["appointment-confirmations"])

# Pydantic models for request/response
class ConfirmationRequest(BaseModel):
    """Request for appointment confirmation."""
    appointment_id: int = Field(..., description="Appointment ID")
    send_immediately: bool = Field(True, description="Send SMS immediately")


class ConfirmationResponse(BaseModel):
    """Response to confirmation request."""
    confirmation_token: str = Field(..., description="Confirmation token")
    response: str = Field(..., description="Response text")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class ReminderBatchRequest(BaseModel):
    """Request for sending reminder batch."""
    hours_before: int = Field(24, description="Hours before appointment")
    batch_size: int = Field(50, description="Batch size")


class CompletionRequest(BaseModel):
    """Request for completion confirmation."""
    appointment_id: int = Field(..., description="Appointment ID")
    send_immediately: bool = Field(True, description="Send SMS immediately")


class StatisticsRequest(BaseModel):
    """Request for confirmation statistics."""
    start_date: date = Field(..., description="Start date")
    end_date: date = Field(..., description="End date")


# Initialize service
confirmation_service = AppointmentConfirmationService()


@router.post("/request")
@require_auth
@require_roles([UserRoleType.MANAGER, UserRoleType.TUTOR])
async def request_confirmation(
    request: ConfirmationRequest,
    current_user: Dict = Depends(require_auth)
) -> JSONResponse:
    """
    Request tutor confirmation for an appointment.
    
    - **appointment_id**: ID of the appointment to confirm
    - **send_immediately**: Whether to send SMS confirmation immediately
    """
    try:
        result = await confirmation_service.request_tutor_confirmation(
            appointment_id=request.appointment_id,
            send_immediately=request.send_immediately
        )
        
        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": "Confirmation request processed successfully",
                "data": result
            }
        )
        
    except ResourceNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except BusinessLogicError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error requesting confirmation: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/respond")
async def process_confirmation_response(
    response: ConfirmationResponse
) -> JSONResponse:
    """
    Process tutor's confirmation response (usually from SMS webhook).
    
    - **confirmation_token**: Unique confirmation token
    - **response**: Response text ('YES', 'NO', etc.)
    - **metadata**: Additional response metadata
    """
    try:
        result = await confirmation_service.process_tutor_response(
            confirmation_token=response.confirmation_token,
            response=response.response,
            metadata=response.metadata
        )
        
        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": result['message'],
                "data": result
            }
        )
        
    except ResourceNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except BusinessLogicError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error processing confirmation response: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/reminders/send")
@require_auth
@require_roles([UserRoleType.MANAGER])
async def send_reminder_batch(
    request: ReminderBatchRequest,
    current_user: Dict = Depends(require_auth)
) -> JSONResponse:
    """
    Send appointment reminders in batch.
    
    - **hours_before**: How many hours before appointment to send reminder (24 or 2)
    - **batch_size**: Number of reminders to send in this batch
    """
    try:
        result = await confirmation_service.send_appointment_reminders(
            hours_before=request.hours_before,
            batch_size=request.batch_size
        )
        
        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": f"Sent {result['sent_count']} reminders successfully",
                "data": result
            }
        )
        
    except Exception as e:
        logger.error(f"Error sending reminder batch: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/completion/request")
@require_auth
@require_roles([UserRoleType.MANAGER, UserRoleType.TUTOR])
async def request_completion_confirmation(
    request: CompletionRequest,
    current_user: Dict = Depends(require_auth)
) -> JSONResponse:
    """
    Request tutor to confirm appointment completion.
    
    - **appointment_id**: ID of the completed appointment
    - **send_immediately**: Whether to send SMS completion request immediately
    """
    try:
        result = await confirmation_service.request_completion_confirmation(
            appointment_id=request.appointment_id,
            send_immediately=request.send_immediately
        )
        
        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": "Completion confirmation request sent successfully",
                "data": result
            }
        )
        
    except ResourceNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except BusinessLogicError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error requesting completion confirmation: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/statistics")
@require_auth
@require_roles([UserRoleType.MANAGER])
async def get_confirmation_statistics(
    start_date: date = Query(..., description="Start date for statistics"),
    end_date: date = Query(..., description="End date for statistics"),
    current_user: Dict = Depends(require_auth)
) -> JSONResponse:
    """
    Get confirmation statistics for a date range.
    
    - **start_date**: Start date for statistics period
    - **end_date**: End date for statistics period
    """
    try:
        stats = await confirmation_service.get_confirmation_statistics(
            start_date=start_date,
            end_date=end_date
        )
        
        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": "Confirmation statistics retrieved successfully",
                "data": stats
            }
        )
        
    except Exception as e:
        logger.error(f"Error getting confirmation statistics: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/appointment/{appointment_id}")
@require_auth
async def get_appointment_confirmations(
    appointment_id: int,
    current_user: Dict = Depends(require_auth)
) -> JSONResponse:
    """
    Get confirmation history for a specific appointment.
    
    - **appointment_id**: ID of the appointment
    """
    try:
        # This would typically fetch confirmation history from the repository
        # For now, return a placeholder response
        
        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": "Confirmation history retrieved successfully",
                "data": {
                    "appointment_id": appointment_id,
                    "confirmations": [],
                    "notifications": []
                }
            }
        )
        
    except Exception as e:
        logger.error(f"Error getting appointment confirmations: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/webhook/sms")
async def handle_sms_webhook(
    body: Dict[str, Any] = Body(...)
) -> JSONResponse:
    """
    Handle SMS webhook from Twilio for confirmation responses.
    
    This endpoint processes incoming SMS messages that contain confirmation responses.
    """
    try:
        # Extract relevant information from Twilio webhook
        # This is a simplified example - actual implementation would parse Twilio's webhook format
        
        from_number = body.get('From', '')
        message_body = body.get('Body', '').strip()
        
        # Extract confirmation token from message or lookup by phone number
        # For this example, assume token is in the message or we can derive it
        
        logger.info(f"Received SMS from {from_number}: {message_body}")
        
        # TODO: Implement actual token extraction and response processing
        # This would typically:
        # 1. Extract confirmation token from message or lookup by phone number
        # 2. Call confirmation_service.process_tutor_response()
        # 3. Send appropriate response back to tutor
        
        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": "SMS webhook processed successfully"
            }
        )
        
    except Exception as e:
        logger.error(f"Error processing SMS webhook: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/pending")
@require_auth
@require_roles([UserRoleType.MANAGER])
async def get_pending_confirmations(
    limit: int = Query(50, description="Maximum number of results"),
    current_user: Dict = Depends(require_auth)
) -> JSONResponse:
    """
    Get list of pending confirmations that need attention.
    
    - **limit**: Maximum number of results to return
    """
    try:
        # This would fetch pending confirmations from the repository
        # For now, return a placeholder response
        
        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": "Pending confirmations retrieved successfully",
                "data": {
                    "pending_confirmations": [],
                    "total_count": 0
                }
            }
        )
        
    except Exception as e:
        logger.error(f"Error getting pending confirmations: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/bulk-remind")
@require_auth
@require_roles([UserRoleType.MANAGER])
async def send_bulk_reminders(
    appointment_ids: List[int] = Body(..., description="List of appointment IDs"),
    message_type: str = Body("reminder", description="Type of message to send"),
    current_user: Dict = Depends(require_auth)
) -> JSONResponse:
    """
    Send bulk reminders for specific appointments.
    
    - **appointment_ids**: List of appointment IDs to send reminders for
    - **message_type**: Type of reminder ('reminder', 'confirmation', 'completion')
    """
    try:
        sent_count = 0
        failed_count = 0
        results = []
        
        for appointment_id in appointment_ids:
            try:
                if message_type == "confirmation":
                    result = await confirmation_service.request_tutor_confirmation(
                        appointment_id=appointment_id,
                        send_immediately=True
                    )
                elif message_type == "completion":
                    result = await confirmation_service.request_completion_confirmation(
                        appointment_id=appointment_id,
                        send_immediately=True
                    )
                else:
                    # Default to reminder
                    result = await confirmation_service.send_appointment_reminders(
                        hours_before=24,
                        batch_size=1
                    )
                
                results.append({
                    "appointment_id": appointment_id,
                    "status": "sent",
                    "result": result
                })
                sent_count += 1
                
            except Exception as e:
                results.append({
                    "appointment_id": appointment_id,
                    "status": "failed",
                    "error": str(e)
                })
                failed_count += 1
        
        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": f"Bulk reminders processed: {sent_count} sent, {failed_count} failed",
                "data": {
                    "sent_count": sent_count,
                    "failed_count": failed_count,
                    "results": results
                }
            }
        )
        
    except Exception as e:
        logger.error(f"Error sending bulk reminders: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/confirmation/{confirmation_id}")
@require_auth
@require_roles([UserRoleType.MANAGER])
async def cancel_confirmation(
    confirmation_id: int,
    reason: Optional[str] = Body(None, description="Cancellation reason"),
    current_user: Dict = Depends(require_auth)
) -> JSONResponse:
    """
    Cancel a pending confirmation request.
    
    - **confirmation_id**: ID of the confirmation to cancel
    - **reason**: Optional cancellation reason
    """
    try:
        # This would cancel the confirmation in the repository
        # For now, return a placeholder response
        
        logger.info(f"Cancelled confirmation {confirmation_id}: {reason}")
        
        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": "Confirmation cancelled successfully",
                "data": {
                    "confirmation_id": confirmation_id,
                    "status": "cancelled",
                    "reason": reason
                }
            }
        )
        
    except Exception as e:
        logger.error(f"Error cancelling confirmation: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")