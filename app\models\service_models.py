"""
Service catalog and rate management models.

Defines the structure for services, subject areas, and pricing.
"""

from datetime import datetime
from decimal import Decimal
from pydantic import condecimal, field_validator
from typing import List, Optional, Dict, Any
from enum import Enum
from pydantic import BaseModel, Field, validator

from app.models.base import BaseEntity, BaseModel


# Enums for service catalog
class SubjectArea(str, Enum):
    """Available subject areas for tutoring services."""
    MATHEMATICS = "mathematics"
    SCIENCE = "science"
    FRENCH = "french"
    ENGLISH = "english"
    PHYSICS = "physics"
    CHEMISTRY = "chemistry"
    BIOLOGY = "biology"
    HISTORY = "history"
    GEOGRAPHY = "geography"
    COMPUTER_SCIENCE = "computer_science"
    ECONOMICS = "economics"
    ACCOUNTING = "accounting"
    OTHER = "other"


class ServiceType(str, Enum):
    """Types of tutoring services offered."""
    ONLINE = "online"
    IN_PERSON = "in_person"
    LIBRARY = "library"
    HYBRID = "hybrid"


class ServiceLevel(str, Enum):
    """Academic levels for services."""
    ELEMENTARY = "elementary"
    HIGH_SCHOOL = "high_school"
    COLLEGE = "college"
    UNIVERSITY = "university"
    ADULT = "adult"


class LocationType(str, Enum):
    """Types of locations for services."""
    ONLINE = "online"
    CLIENT_HOME = "client_home"
    TUTOR_HOME = "tutor_home"
    LIBRARY = "library"
    PUBLIC_SPACE = "public_space"


class PackageType(str, Enum):
    """Types of service packages."""
    INDIVIDUAL = "individual"
    TECFEE = "tecfee"
    BULK_HOURS = "bulk_hours"
    MONTHLY = "monthly"


class PricingTier(str, Enum):
    """Pricing tiers for different service configurations."""
    STANDARD = "standard"
    PREMIUM = "premium"
    SPECIALIZED = "specialized"


# Base Models
class ServiceCatalogBase(BaseModel):
    """Base model for service catalog entries."""
    service_name: str = Field(..., min_length=1, max_length=100)
    subject_area: SubjectArea
    service_type: ServiceType
    service_level: ServiceLevel
    description: Optional[str] = Field(None, max_length=500)
    prerequisites: Optional[str] = Field(None, max_length=500)
    is_active: bool = True
    min_duration_minutes: int = Field(default=30, ge=15, le=240)
    max_duration_minutes: int = Field(default=120, ge=30, le=480)
    default_duration_minutes: int = Field(default=60, ge=15, le=240)
    
    @field_validator('max_duration_minutes')
    @classmethod
    def validate_duration_range(cls, v, info):
        if 'min_duration_minutes' in info.data and v < info.data['min_duration_minutes']:
            raise ValueError('max_duration_minutes must be >= min_duration_minutes')
        return v
    
    @field_validator('default_duration_minutes')
    @classmethod
    def validate_default_duration(cls, v, info):
        if 'min_duration_minutes' in info.data and v < info.data['min_duration_minutes']:
            raise ValueError('default_duration_minutes must be >= min_duration_minutes')
        if 'max_duration_minutes' in info.data and v > info.data['max_duration_minutes']:
            raise ValueError('default_duration_minutes must be <= max_duration_minutes')
        return v


class TutorServiceRateBase(BaseModel):
    """Base model for tutor-specific service rates."""
    tutor_id: int = Field(..., gt=0)
    service_catalog_id: int = Field(..., gt=0)
    service_type: ServiceType
    pricing_tier: PricingTier = PricingTier.STANDARD
    
    # Rates in CAD
    hourly_rate: Decimal = Field(..., ge=0, le=500)
    platform_fee_percentage: Decimal = Field(default=20.0, ge=0, le=50)
    
    # Availability settings
    is_available: bool = True
    max_distance_km: Optional[int] = Field(None, ge=0, le=100)  # For in-person services
    preferred_locations: Optional[List[str]] = None  # Postal codes or areas
    
    # Additional pricing options
    rush_hour_surcharge: Optional[Decimal] = Field(None, ge=0)
    weekend_surcharge: Optional[Decimal] = Field(None, ge=0)
    holiday_surcharge: Optional[Decimal] = Field(None, ge=0)
    
    @field_validator('hourly_rate')
    def validate_hourly_rate(cls, v):
        if v < 15:
            raise ValueError('Hourly rate must be at least $15 CAD')
        return v
    
    @field_validator('max_distance_km')
    @classmethod
    def validate_distance_for_service_type(cls, v, info):
        if v is not None and info.data.get('service_type') in [ServiceType.ONLINE, ServiceType.HYBRID]:
            return None  # Distance not applicable for online services
        return v


class ServicePackageBase(BaseModel):
    """Base model for service packages."""
    package_name: str = Field(..., min_length=1, max_length=100)
    package_type: PackageType
    description: Optional[str] = Field(None, max_length=500)
    
    # Package configuration
    subject_areas: List[SubjectArea]
    service_types: List[ServiceType]
    service_levels: List[ServiceLevel]
    
    # Pricing in CAD
    package_price: Decimal = Field(..., ge=0)
    deposit_amount: Optional[Decimal] = Field(None, ge=0)
    
    # Session configuration
    total_sessions: int = Field(..., gt=0, le=100)
    session_duration_minutes: int = Field(default=60, ge=30, le=240)
    validity_months: int = Field(default=12, ge=1, le=24)
    
    # Special configurations
    is_recurring: bool = False
    max_participants: Optional[int] = Field(None, gt=0, le=20)  # For group packages
    min_participants: Optional[int] = Field(None, gt=0, le=20)  # For group packages
    
    is_active: bool = True
    
    @field_validator('deposit_amount')
    @classmethod
    def validate_deposit(cls, v, info):
        if v is not None and 'package_price' in values:
            if v > info.data['package_price']:
                raise ValueError('Deposit cannot exceed package price')
        return v
    
    @field_validator('max_participants')
    @classmethod
    def validate_participants(cls, v, info):
        if v is not None and 'min_participants' in values:
            if info.data['min_participants'] is not None and v < info.data['min_participants']:
                raise ValueError('max_participants must be >= min_participants')
        return v


# Request/Response Models
class CreateServiceCatalogRequest(ServiceCatalogBase):
    """Request model for creating a service catalog entry."""
    pass


class UpdateServiceCatalogRequest(BaseModel):
    """Request model for updating a service catalog entry."""
    service_name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    prerequisites: Optional[str] = Field(None, max_length=500)
    is_active: Optional[bool] = None
    min_duration_minutes: Optional[int] = Field(None, ge=15, le=240)
    max_duration_minutes: Optional[int] = Field(None, ge=30, le=480)
    default_duration_minutes: Optional[int] = Field(None, ge=15, le=240)


class ServiceCatalogResponse(ServiceCatalogBase, BaseEntity):
    """Response model for service catalog entries."""
    service_catalog_id: int
    created_by: int
    updated_by: Optional[int] = None
    
    class Config:
        from_attributes = True


class CreateTutorServiceRateRequest(TutorServiceRateBase):
    """Request model for creating a tutor service rate."""
    pass


class UpdateTutorServiceRateRequest(BaseModel):
    """Request model for updating a tutor service rate."""
    hourly_rate: Optional[Decimal] = Field(None, ge=15, le=500)
    platform_fee_percentage: Optional[Decimal] = Field(None, ge=0, le=50)
    is_available: Optional[bool] = None
    max_distance_km: Optional[int] = Field(None, ge=0, le=100)
    preferred_locations: Optional[List[str]] = None
    rush_hour_surcharge: Optional[Decimal] = Field(None, ge=0)
    weekend_surcharge: Optional[Decimal] = Field(None, ge=0)
    holiday_surcharge: Optional[Decimal] = Field(None, ge=0)
    pricing_tier: Optional[PricingTier] = None


class TutorServiceRateResponse(TutorServiceRateBase, BaseEntity):
    """Response model for tutor service rates."""
    tutor_service_rate_id: int
    service_catalog: Optional[ServiceCatalogResponse] = None  # Populated via join
    created_by: int
    updated_by: Optional[int] = None
    
    # Calculated fields
    effective_hourly_rate: Optional[Decimal] = None  # After platform fee
    total_rating: Optional[float] = None  # From reviews
    total_sessions: Optional[int] = None  # Historical count
    
    class Config:
        from_attributes = True


class CreateServicePackageRequest(ServicePackageBase):
    """Request model for creating a service package."""
    pass


class UpdateServicePackageRequest(BaseModel):
    """Request model for updating a service package."""
    package_name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    package_price: Optional[Decimal] = Field(None, ge=0)
    deposit_amount: Optional[Decimal] = Field(None, ge=0)
    is_active: Optional[bool] = None
    validity_months: Optional[int] = Field(None, ge=1, le=24)


class ServicePackageResponse(ServicePackageBase, BaseEntity):
    """Response model for service packages."""
    service_package_id: int
    created_by: int
    updated_by: Optional[int] = None
    
    # Usage statistics
    total_purchases: Optional[int] = None
    active_subscriptions: Optional[int] = None
    
    class Config:
        from_attributes = True


# Search and Filter Models
class ServiceSearchRequest(BaseModel):
    """Request model for searching services."""
    subject_area: Optional[SubjectArea] = None
    service_type: Optional[ServiceType] = None
    service_level: Optional[ServiceLevel] = None
    tutor_id: Optional[int] = Field(None, gt=0)
    max_hourly_rate: Optional[Decimal] = Field(None, gt=0)
    max_distance_km: Optional[int] = Field(None, gt=0)
    client_postal_code: Optional[str] = Field(None, pattern=r'^[A-Z]\d[A-Z]\s?\d[A-Z]\d$')
    is_active_only: bool = True
    include_packages: bool = False
    page: int = Field(default=1, ge=1)
    page_size: int = Field(default=20, ge=1, le=100)


class ServiceRecommendationRequest(BaseModel):
    """Request model for service recommendations."""
    client_id: int = Field(..., gt=0)
    dependant_id: Optional[int] = Field(None, gt=0)
    preferred_subject_areas: Optional[List[SubjectArea]] = None
    preferred_service_types: Optional[List[ServiceType]] = None
    max_recommendations: int = Field(default=5, ge=1, le=20)
    include_past_tutors: bool = True
    location_postal_code: Optional[str] = Field(None, pattern=r'^[A-Z]\d[A-Z]\s?\d[A-Z]\d$')


# Pricing Calculation Models
class PricingCalculationRequest(BaseModel):
    """Request model for calculating service pricing."""
    tutor_service_rate_id: int = Field(..., gt=0)
    duration_minutes: int = Field(..., ge=15, le=480)
    appointment_datetime: datetime
    is_rush_hour: bool = False
    apply_surcharges: bool = True
    package_id: Optional[int] = Field(None, gt=0)


class PricingCalculationResponse(BaseModel):
    """Response model for pricing calculations."""
    base_amount: Decimal = Field(...)
    surcharge_amount: Decimal = Field(default=0)
    platform_fee: Decimal = Field(...)
    tutor_earnings: Decimal = Field(...)
    client_total: Decimal = Field(...)
    
    # Breakdown
    surcharge_breakdown: Dict[str, Decimal] = {}
    is_package_session: bool = False
    package_sessions_remaining: Optional[int] = None
    
    # Applied rates
    hourly_rate: Decimal
    platform_fee_percentage: Decimal
    
    class Config:
        json_encoders = {
            Decimal: lambda v: float(v)
        }


# Location Models
class LocationPreference(BaseModel):
    """Model for location preferences and distance calculations."""
    postal_code: str = Field(..., pattern=r'^[A-Z]\d[A-Z]\s?\d[A-Z]\d$')
    city: Optional[str] = Field(None, max_length=100)
    province: str = Field(default="QC", pattern=r'^[A-Z]{2}$')
    latitude: Optional[float] = Field(None, ge=-90, le=90)
    longitude: Optional[float] = Field(None, ge=-180, le=180)
    
    # Preferences
    max_travel_distance_km: int = Field(default=10, ge=0, le=50)
    preferred_areas: Optional[List[str]] = None  # Neighborhood names


class DistanceCalculationRequest(BaseModel):
    """Request model for distance calculations."""
    origin_postal_code: str = Field(..., pattern=r'^[A-Z]\d[A-Z]\s?\d[A-Z]\d$')
    destination_postal_code: str = Field(..., pattern=r'^[A-Z]\d[A-Z]\s?\d[A-Z]\d$')
    travel_mode: str = Field(default="driving", pattern=r'^(driving|transit|walking)$')


class DistanceCalculationResponse(BaseModel):
    """Response model for distance calculations."""
    distance_km: float = Field(..., ge=0)
    duration_minutes: int = Field(..., ge=0)
    travel_mode: str
    is_within_service_area: bool
    
    # Additional info
    route_polyline: Optional[str] = None
    alternate_routes: Optional[List[Dict[str, Any]]] = None


# Aggregate Models
class TutorServiceSummary(BaseModel):
    """Summary of all services offered by a tutor."""
    tutor_id: int
    total_services: int
    active_services: int
    
    # Rate ranges
    min_hourly_rate: Decimal
    max_hourly_rate: Decimal
    average_hourly_rate: Decimal
    
    # Service breakdown
    services_by_type: Dict[ServiceType, int]
    services_by_subject: Dict[SubjectArea, int]
    
    # Performance metrics
    average_rating: Optional[float] = None
    total_sessions_completed: int = 0
    completion_rate: float = 0.0
    
    # Availability
    max_service_distance_km: Optional[int] = None
    available_service_types: List[ServiceType]
    
    class Config:
        json_encoders = {
            Decimal: lambda v: float(v)
        }


class ServiceCatalogSummary(BaseModel):
    """Summary statistics for service catalog."""
    total_services: int
    active_services: int
    services_by_subject: Dict[SubjectArea, int]
    services_by_type: Dict[ServiceType, int]
    services_by_level: Dict[ServiceLevel, int]
    
    # Pricing summary
    average_hourly_rate: Decimal
    min_hourly_rate: Decimal
    max_hourly_rate: Decimal
    
    # Popular services
    most_booked_services: List[Dict[str, Any]]
    highest_rated_services: List[Dict[str, Any]]
    
    class Config:
        json_encoders = {
            Decimal: lambda v: float(v)
        }