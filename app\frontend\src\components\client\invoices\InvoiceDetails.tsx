import React from 'react';
import { useTranslation } from 'react-i18next';
import Button from '../../common/Button';
import { 
  FileText, 
  Download, 
  Calendar,
  DollarSign,
  User,
  CheckCircle,
  Clock,
  AlertTriangle,
  XCircle,
  CreditCard,
  Hash,
  Mail,
  Phone,
  MapPin,
  Building
} from 'lucide-react';

interface InvoiceDetailsProps {
  invoice: {
    invoice_id: number;
    invoice_number: string;
    client_id: number;
    amount: number;
    status: 'pending' | 'paid' | 'overdue' | 'cancelled';
    issue_date: string;
    due_date: string;
    paid_date: string | null;
    payment_method: string | null;
    paid_by_parent: 1 | 2 | null;
    created_at: string;
    updated_at: string;
    items?: InvoiceItem[];
    client?: {
      first_name: string;
      last_name: string;
      email: string;
      phone_number: string;
      address?: {
        street_address: string;
        city: string;
        province: string;
        postal_code: string;
      };
    };
  };
  onDownload: (invoice: any) => void;
  onClose: () => void;
}

interface InvoiceItem {
  description: string;
  quantity: number;
  rate: number;
  amount: number;
  tutor_name?: string;
  appointment_date?: string;
  service_type?: string;
}

export const InvoiceDetails: React.FC<InvoiceDetailsProps> = ({
  invoice,
  onDownload,
  onClose
}) => {
  const { t } = useTranslation();

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-CA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return <CheckCircle className="w-5 h-5" />;
      case 'pending':
        return <Clock className="w-5 h-5" />;
      case 'overdue':
        return <AlertTriangle className="w-5 h-5" />;
      case 'cancelled':
        return <XCircle className="w-5 h-5" />;
      default:
        return null;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'text-green-600 bg-green-50';
      case 'pending':
        return 'text-blue-600 bg-blue-50';
      case 'overdue':
        return 'text-red-600 bg-red-50';
      case 'cancelled':
        return 'text-gray-600 bg-gray-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  // Calculate subtotal and tax
  const subtotal = invoice.items?.reduce((sum, item) => sum + item.amount, 0) || invoice.amount;
  const taxRate = 0.13; // 13% HST for Ontario (configurable)
  const taxAmount = subtotal * taxRate;
  const total = subtotal + taxAmount;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start pb-6 border-b border-gray-200">
        <div>
          <div className="flex items-center gap-3 mb-2">
            <FileText className="w-8 h-8 text-accent-red" />
            <h2 className="text-2xl font-bold text-text-primary">
              {t('client.invoices.invoice')} #{invoice.invoice_number}
            </h2>
          </div>
          <div className={`inline-flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-medium ${getStatusColor(invoice.status)}`}>
            {getStatusIcon(invoice.status)}
            {t(`client.invoices.status.${invoice.status}`)}
          </div>
        </div>
        
        <Button
          onClick={() => onDownload(invoice)}
          leftIcon={<Download className="w-4 h-4" />}
        >
          {t('client.invoices.download')}
        </Button>
      </div>

      {/* Invoice Info Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Left Column - Billing Info */}
        <div>
          <h3 className="text-sm font-medium text-text-secondary mb-4 uppercase tracking-wider">
            {t('client.invoices.billingInfo')}
          </h3>
          <div className="space-y-3">
            {invoice.client && (
              <>
                <div className="flex items-start gap-3">
                  <User className="w-4 h-4 text-text-secondary mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-text-primary">
                      {invoice.client.first_name} {invoice.client.last_name}
                    </p>
                    <p className="text-sm text-text-secondary">{invoice.client.email}</p>
                    <p className="text-sm text-text-secondary">{invoice.client.phone_number}</p>
                  </div>
                </div>
                
                {invoice.client.address && (
                  <div className="flex items-start gap-3">
                    <MapPin className="w-4 h-4 text-text-secondary mt-0.5" />
                    <div className="text-sm text-text-secondary">
                      <p>{invoice.client.address.street_address}</p>
                      <p>{invoice.client.address.city}, {invoice.client.address.province}</p>
                      <p>{invoice.client.address.postal_code}</p>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        </div>

        {/* Right Column - Invoice Details */}
        <div>
          <h3 className="text-sm font-medium text-text-secondary mb-4 uppercase tracking-wider">
            {t('client.invoices.invoiceDetails')}
          </h3>
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <Hash className="w-4 h-4 text-text-secondary" />
              <div>
                <p className="text-sm text-text-secondary">{t('client.invoices.invoiceNumber')}</p>
                <p className="text-sm font-medium text-text-primary">{invoice.invoice_number}</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <Calendar className="w-4 h-4 text-text-secondary" />
              <div>
                <p className="text-sm text-text-secondary">{t('client.invoices.issueDate')}</p>
                <p className="text-sm font-medium text-text-primary">{formatDate(invoice.issue_date)}</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <Calendar className="w-4 h-4 text-text-secondary" />
              <div>
                <p className="text-sm text-text-secondary">{t('client.invoices.dueDate')}</p>
                <p className="text-sm font-medium text-text-primary">{formatDate(invoice.due_date)}</p>
              </div>
            </div>

            {invoice.paid_date && (
              <div className="flex items-center gap-3">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <div>
                  <p className="text-sm text-text-secondary">{t('client.invoices.paidDate')}</p>
                  <p className="text-sm font-medium text-text-primary">{formatDate(invoice.paid_date)}</p>
                </div>
              </div>
            )}

            {invoice.payment_method && (
              <div className="flex items-center gap-3">
                <CreditCard className="w-4 h-4 text-text-secondary" />
                <div>
                  <p className="text-sm text-text-secondary">{t('client.invoices.paymentMethod')}</p>
                  <p className="text-sm font-medium text-text-primary">{invoice.payment_method}</p>
                </div>
              </div>
            )}

            {invoice.paid_by_parent && (
              <div className="flex items-center gap-3">
                <User className="w-4 h-4 text-text-secondary" />
                <div>
                  <p className="text-sm text-text-secondary">{t('client.invoices.paidBy')}</p>
                  <p className="text-sm font-medium text-text-primary">
                    {t(`client.invoices.parent${invoice.paid_by_parent}`)}
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Line Items */}
      <div>
        <h3 className="text-sm font-medium text-text-secondary mb-4 uppercase tracking-wider">
          {t('client.invoices.lineItems')}
        </h3>
        <div className="bg-gray-50 rounded-lg overflow-hidden">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('client.invoices.description')}
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('client.invoices.quantity')}
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('client.invoices.rate')}
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('client.invoices.amount')}
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {invoice.items && invoice.items.length > 0 ? (
                invoice.items.map((item, index) => (
                  <tr key={index}>
                    <td className="px-4 py-3 text-sm text-text-primary">
                      <div>
                        <p className="font-medium">{item.description}</p>
                        {item.tutor_name && (
                          <p className="text-xs text-text-secondary mt-1">
                            {t('client.invoices.tutor')}: {item.tutor_name}
                          </p>
                        )}
                        {item.appointment_date && (
                          <p className="text-xs text-text-secondary">
                            {formatDate(item.appointment_date)}
                          </p>
                        )}
                      </div>
                    </td>
                    <td className="px-4 py-3 text-sm text-text-primary text-right">
                      {item.quantity}
                    </td>
                    <td className="px-4 py-3 text-sm text-text-primary text-right">
                      {formatCurrency(item.rate)}
                    </td>
                    <td className="px-4 py-3 text-sm font-medium text-text-primary text-right">
                      {formatCurrency(item.amount)}
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={4} className="px-4 py-3 text-sm text-text-secondary text-center">
                    {t('client.invoices.noLineItems')}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Totals */}
      <div className="flex justify-end">
        <div className="w-full md:w-1/2 space-y-2">
          <div className="flex justify-between items-center py-2">
            <span className="text-sm text-text-secondary">{t('client.invoices.subtotal')}</span>
            <span className="text-sm font-medium text-text-primary">{formatCurrency(subtotal)}</span>
          </div>
          <div className="flex justify-between items-center py-2">
            <span className="text-sm text-text-secondary">{t('client.invoices.tax')} (HST {(taxRate * 100).toFixed(0)}%)</span>
            <span className="text-sm font-medium text-text-primary">{formatCurrency(taxAmount)}</span>
          </div>
          <div className="flex justify-between items-center py-3 border-t border-gray-200">
            <span className="text-base font-medium text-text-primary">{t('client.invoices.total')}</span>
            <span className="text-xl font-bold text-text-primary">{formatCurrency(total)}</span>
          </div>
        </div>
      </div>

      {/* Footer Actions */}
      <div className="flex justify-between items-center pt-6 border-t border-gray-200">
        <p className="text-sm text-text-secondary">
          {t('client.invoices.thankYou')}
        </p>
        <div className="flex gap-3">
          <Button
            variant="secondary"
            onClick={onClose}
          >
            {t('common.close')}
          </Button>
          {invoice.status === 'pending' && (
            <Button
              leftIcon={<CreditCard className="w-4 h-4" />}
            >
              {t('client.invoices.payNow')}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};