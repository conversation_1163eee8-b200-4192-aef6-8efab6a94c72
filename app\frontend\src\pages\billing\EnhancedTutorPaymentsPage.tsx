import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  DollarSign, Users, Calendar, TrendingUp, CheckCircle, XCircle,
  AlertCircle, Clock, Send, Download, Filter, Search, ChevronLeft,
  ChevronRight, RefreshCw, FileText, CreditCard, Activity
} from 'lucide-react';
import { Card } from '../../components/common/Card';
import Button from '../../components/common/Button';
import { Badge } from '../../components/common/Badge';
import { Input } from '../../components/common/Input';
import { Select } from '../../components/common/Select';
import { Modal } from '../../components/common/Modal';
import { Checkbox } from '../../components/common/Checkbox';
import { format, startOfWeek, endOfWeek, addDays, subDays } from 'date-fns';
import toast from 'react-hot-toast';

interface TutorPayment {
  payment_id: number;
  tutor_id: number;
  tutor_name: string;
  tutor_email: string;
  appointment_id: number;
  appointment_date: string;
  service_type: string;
  hours: number;
  base_rate: number;
  transport_amount: number;
  bonus_amount: number;
  total_amount: number;
  status: 'pending' | 'approved' | 'processing' | 'paid' | 'rejected';
  week_start: string;
  week_end: string;
  notes?: string;
}

interface PaymentSummary {
  totalPending: number;
  totalApproved: number;
  totalPaid: number;
  tutorsAffected: number;
  currentWeekTotal: number;
  previousWeekTotal: number;
}

const EnhancedTutorPaymentsPage: React.FC = () => {
  const { t } = useTranslation();
  const [selectedWeek, setSelectedWeek] = useState(new Date());
  const [payments, setPayments] = useState<TutorPayment[]>([]);
  const [selectedPayments, setSelectedPayments] = useState<number[]>([]);
  const [summary, setSummary] = useState<PaymentSummary>({
    totalPending: 45280,
    totalApproved: 12450,
    totalPaid: 132560,
    tutorsAffected: 45,
    currentWeekTotal: 24890,
    previousWeekTotal: 22340
  });
  const [loading, setLoading] = useState(false);
  const [statusFilter, setStatusFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [showBulkApproveModal, setShowBulkApproveModal] = useState(false);
  const [showPaymentDetailsModal, setShowPaymentDetailsModal] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState<TutorPayment | null>(null);

  // Calculate week start and end (Thursday to Wednesday)
  const getWeekBounds = (date: Date) => {
    const dayOfWeek = date.getDay();
    const daysFromThursday = (dayOfWeek + 3) % 7;
    const weekStart = subDays(date, daysFromThursday);
    const weekEnd = addDays(weekStart, 6);
    return { weekStart, weekEnd };
  };

  const { weekStart, weekEnd } = getWeekBounds(selectedWeek);

  // Mock payment data
  useEffect(() => {
    const mockPayments: TutorPayment[] = [
      {
        payment_id: 1,
        tutor_id: 101,
        tutor_name: 'Sarah Johnson',
        tutor_email: '<EMAIL>',
        appointment_id: 1001,
        appointment_date: '2024-11-21',
        service_type: 'Mathematics - Online',
        hours: 2.5,
        base_rate: 45,
        transport_amount: 0,
        bonus_amount: 10,
        total_amount: 122.50,
        status: 'pending',
        week_start: format(weekStart, 'yyyy-MM-dd'),
        week_end: format(weekEnd, 'yyyy-MM-dd')
      },
      {
        payment_id: 2,
        tutor_id: 101,
        tutor_name: 'Sarah Johnson',
        tutor_email: '<EMAIL>',
        appointment_id: 1002,
        appointment_date: '2024-11-19',
        service_type: 'Mathematics - In Person',
        hours: 1.5,
        base_rate: 55,
        transport_amount: 15,
        bonus_amount: 0,
        total_amount: 97.50,
        status: 'pending',
        week_start: format(weekStart, 'yyyy-MM-dd'),
        week_end: format(weekEnd, 'yyyy-MM-dd')
      },
      {
        payment_id: 3,
        tutor_id: 102,
        tutor_name: 'Michael Chen',
        tutor_email: '<EMAIL>',
        appointment_id: 1003,
        appointment_date: '2024-11-20',
        service_type: 'French - Online',
        hours: 3,
        base_rate: 40,
        transport_amount: 0,
        bonus_amount: 0,
        total_amount: 120,
        status: 'approved',
        week_start: format(weekStart, 'yyyy-MM-dd'),
        week_end: format(weekEnd, 'yyyy-MM-dd')
      },
      {
        payment_id: 4,
        tutor_id: 103,
        tutor_name: 'Emma Davis',
        tutor_email: '<EMAIL>',
        appointment_id: 1004,
        appointment_date: '2024-11-18',
        service_type: 'Science - Library',
        hours: 2,
        base_rate: 50,
        transport_amount: 10,
        bonus_amount: 5,
        total_amount: 115,
        status: 'paid',
        week_start: format(weekStart, 'yyyy-MM-dd'),
        week_end: format(weekEnd, 'yyyy-MM-dd')
      }
    ];
    
    setPayments(mockPayments);
  }, [selectedWeek]);

  const filteredPayments = payments.filter(payment => {
    const matchesStatus = statusFilter === 'all' || payment.status === statusFilter;
    const matchesSearch = searchTerm === '' || 
      payment.tutor_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payment.tutor_email.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesStatus && matchesSearch;
  });

  // Group payments by tutor
  const groupedPayments = filteredPayments.reduce((acc, payment) => {
    if (!acc[payment.tutor_id]) {
      acc[payment.tutor_id] = {
        tutor_name: payment.tutor_name,
        tutor_email: payment.tutor_email,
        payments: [],
        total_amount: 0,
        total_hours: 0
      };
    }
    acc[payment.tutor_id].payments.push(payment);
    acc[payment.tutor_id].total_amount += payment.total_amount;
    acc[payment.tutor_id].total_hours += payment.hours;
    return acc;
  }, {} as Record<number, any>);

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const pendingPaymentIds = filteredPayments
        .filter(p => p.status === 'pending')
        .map(p => p.payment_id);
      setSelectedPayments(pendingPaymentIds);
    } else {
      setSelectedPayments([]);
    }
  };

  const handleSelectPayment = (paymentId: number, checked: boolean) => {
    if (checked) {
      setSelectedPayments([...selectedPayments, paymentId]);
    } else {
      setSelectedPayments(selectedPayments.filter(id => id !== paymentId));
    }
  };

  const handleBulkApprove = async () => {
    setLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    setPayments(payments.map(payment => 
      selectedPayments.includes(payment.payment_id) 
        ? { ...payment, status: 'approved' as const }
        : payment
    ));
    
    toast.success(`${selectedPayments.length} payments approved successfully`);
    setSelectedPayments([]);
    setShowBulkApproveModal(false);
    setLoading(false);
  };

  const handleProcessPayments = async () => {
    const approvedPayments = payments.filter(p => p.status === 'approved');
    if (approvedPayments.length === 0) {
      toast.error('No approved payments to process');
      return;
    }
    
    setLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    toast.success(`Processing ${approvedPayments.length} payments via Stripe`);
    setLoading(false);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig: Record<string, { color: string; icon: React.ReactNode }> = {
      pending: { color: 'bg-yellow-100 text-yellow-700', icon: <Clock className="w-4 h-4" /> },
      approved: { color: 'bg-blue-100 text-blue-700', icon: <CheckCircle className="w-4 h-4" /> },
      processing: { color: 'bg-purple-100 text-purple-700', icon: <RefreshCw className="w-4 h-4" /> },
      paid: { color: 'bg-green-100 text-green-700', icon: <CheckCircle className="w-4 h-4" /> },
      rejected: { color: 'bg-red-100 text-red-700', icon: <XCircle className="w-4 h-4" /> }
    };

    const config = statusConfig[status] || statusConfig.pending;
    return (
      <Badge className={`inline-flex items-center gap-1 ${config.color}`}>
        {config.icon}
        <span className="capitalize">{status}</span>
      </Badge>
    );
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD',
    }).format(amount);
  };

  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Tutor Payments Management</h1>
          <p className="text-gray-600 mt-1">
            Review and process weekly tutor payments (Thursday to Wednesday cycle)
          </p>
        </div>
        <div className="flex gap-3">
          <Button
            variant="secondary"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          <Button
            variant="secondary"
            onClick={() => toast.info('Export coming soon')}
          >
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button
            variant="primary"
            onClick={handleProcessPayments}
            disabled={loading || payments.filter(p => p.status === 'approved').length === 0}
          >
            <Send className="w-4 h-4 mr-2" />
            Process Payments
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <Clock className="w-8 h-8 text-yellow-600" />
            <Badge className="bg-yellow-100 text-yellow-700">Pending</Badge>
          </div>
          <p className="text-2xl font-bold">{formatCurrency(summary.totalPending)}</p>
          <p className="text-sm text-gray-600 mt-1">Awaiting approval</p>
        </Card>
        
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <CheckCircle className="w-8 h-8 text-blue-600" />
            <Badge className="bg-blue-100 text-blue-700">Approved</Badge>
          </div>
          <p className="text-2xl font-bold">{formatCurrency(summary.totalApproved)}</p>
          <p className="text-sm text-gray-600 mt-1">Ready to process</p>
        </Card>
        
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <DollarSign className="w-8 h-8 text-green-600" />
            <Badge className="bg-green-100 text-green-700">Paid</Badge>
          </div>
          <p className="text-2xl font-bold">{formatCurrency(summary.totalPaid)}</p>
          <p className="text-sm text-gray-600 mt-1">This month</p>
        </Card>
        
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <Users className="w-8 h-8 text-purple-600" />
            <Badge className="bg-purple-100 text-purple-700">Active</Badge>
          </div>
          <p className="text-2xl font-bold">{summary.tutorsAffected}</p>
          <p className="text-sm text-gray-600 mt-1">Tutors this week</p>
        </Card>
      </div>

      {/* Week Selector and Filters */}
      <Card className="p-4">
        <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSelectedWeek(subDays(selectedWeek, 7))}
            >
              <ChevronLeft className="w-4 h-4" />
            </Button>
            <div className="text-center">
              <p className="text-sm text-gray-600">Payment Period</p>
              <p className="font-semibold">
                {format(weekStart, 'MMM d')} - {format(weekEnd, 'MMM d, yyyy')}
              </p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSelectedWeek(addDays(selectedWeek, 7))}
              disabled={weekStart > new Date()}
            >
              <ChevronRight className="w-4 h-4" />
            </Button>
          </div>
          
          <div className="flex gap-3 w-full lg:w-auto">
            <div className="relative flex-1 lg:w-64">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                placeholder="Search tutors..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              options={[
                { value: 'all', label: 'All Status' },
                { value: 'pending', label: 'Pending' },
                { value: 'approved', label: 'Approved' },
                { value: 'processing', label: 'Processing' },
                { value: 'paid', label: 'Paid' },
                { value: 'rejected', label: 'Rejected' }
              ]}
              className="w-40"
            />
          </div>
        </div>
      </Card>

      {/* Bulk Actions */}
      {selectedPayments.length > 0 && (
        <Card className="p-4 bg-blue-50 border-blue-200">
          <div className="flex items-center justify-between">
            <p className="text-sm font-medium text-blue-900">
              {selectedPayments.length} payment{selectedPayments.length > 1 ? 's' : ''} selected
            </p>
            <div className="flex gap-2">
              <Button
                variant="secondary"
                size="sm"
                onClick={() => setSelectedPayments([])}
              >
                Clear Selection
              </Button>
              <Button
                variant="primary"
                size="sm"
                onClick={() => setShowBulkApproveModal(true)}
              >
                <CheckCircle className="w-4 h-4 mr-2" />
                Approve Selected
              </Button>
            </div>
          </div>
        </Card>
      )}

      {/* Payments List */}
      <Card className="overflow-hidden">
        <div className="p-4 border-b bg-gray-50">
          <div className="flex items-center justify-between">
            <h2 className="font-semibold text-gray-900">Payment Details</h2>
            <div className="flex items-center gap-2">
              <Checkbox
                checked={selectedPayments.length === filteredPayments.filter(p => p.status === 'pending').length}
                onChange={(e) => handleSelectAll(e.target.checked)}
                label="Select all pending"
              />
            </div>
          </div>
        </div>
        
        <div className="divide-y">
          {Object.entries(groupedPayments).map(([tutorId, tutorData]) => (
            <div key={tutorId} className="p-4 hover:bg-gray-50">
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h3 className="font-semibold text-gray-900">{tutorData.tutor_name}</h3>
                  <p className="text-sm text-gray-600">{tutorData.tutor_email}</p>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-lg">{formatCurrency(tutorData.total_amount)}</p>
                  <p className="text-sm text-gray-600">{tutorData.total_hours} hours total</p>
                </div>
              </div>
              
              <div className="space-y-2">
                {tutorData.payments.map((payment: TutorPayment) => (
                  <div 
                    key={payment.payment_id} 
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    <div className="flex items-center gap-3">
                      {payment.status === 'pending' && (
                        <Checkbox
                          checked={selectedPayments.includes(payment.payment_id)}
                          onChange={(e) => handleSelectPayment(payment.payment_id, e.target.checked)}
                        />
                      )}
                      <div>
                        <p className="font-medium text-sm">
                          {payment.service_type} - {format(new Date(payment.appointment_date), 'MMM d')}
                        </p>
                        <p className="text-sm text-gray-600">
                          {payment.hours} hours @ {formatCurrency(payment.base_rate)}/hr
                          {payment.transport_amount > 0 && ` + ${formatCurrency(payment.transport_amount)} transport`}
                          {payment.bonus_amount > 0 && ` + ${formatCurrency(payment.bonus_amount)} bonus`}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-3">
                      <span className="font-medium">{formatCurrency(payment.total_amount)}</span>
                      {getStatusBadge(payment.status)}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setSelectedPayment(payment);
                          setShowPaymentDetailsModal(true);
                        }}
                      >
                        <FileText className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Bulk Approve Modal */}
      <Modal
        isOpen={showBulkApproveModal}
        onClose={() => setShowBulkApproveModal(false)}
        title="Approve Selected Payments"
      >
        <div className="space-y-4">
          <p className="text-gray-600">
            You are about to approve {selectedPayments.length} payment{selectedPayments.length > 1 ? 's' : ''} totaling:
          </p>
          
          <div className="bg-blue-50 p-4 rounded-lg text-center">
            <p className="text-3xl font-bold text-blue-900">
              {formatCurrency(
                payments
                  .filter(p => selectedPayments.includes(p.payment_id))
                  .reduce((sum, p) => sum + p.total_amount, 0)
              )}
            </p>
          </div>
          
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <div className="flex gap-2">
              <AlertCircle className="w-5 h-5 text-yellow-600 flex-shrink-0 mt-0.5" />
              <div className="text-sm text-yellow-800">
                <p className="font-medium">Important:</p>
                <p>Once approved, these payments will be ready for processing via Stripe.</p>
              </div>
            </div>
          </div>
          
          <div className="flex gap-3 justify-end">
            <Button
              variant="ghost"
              onClick={() => setShowBulkApproveModal(false)}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleBulkApprove}
              disabled={loading}
            >
              {loading ? 'Approving...' : 'Approve Payments'}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Payment Details Modal */}
      <Modal
        isOpen={showPaymentDetailsModal}
        onClose={() => setShowPaymentDetailsModal(false)}
        title="Payment Details"
      >
        {selectedPayment && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-600">Tutor</p>
                <p className="font-medium">{selectedPayment.tutor_name}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Status</p>
                {getStatusBadge(selectedPayment.status)}
              </div>
              <div>
                <p className="text-sm text-gray-600">Appointment Date</p>
                <p className="font-medium">{format(new Date(selectedPayment.appointment_date), 'MMM d, yyyy')}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Service Type</p>
                <p className="font-medium">{selectedPayment.service_type}</p>
              </div>
            </div>
            
            <div className="border-t pt-4">
              <h4 className="font-medium mb-2">Payment Breakdown</h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">Base Rate ({selectedPayment.hours} hrs @ {formatCurrency(selectedPayment.base_rate)}/hr)</span>
                  <span>{formatCurrency(selectedPayment.hours * selectedPayment.base_rate)}</span>
                </div>
                {selectedPayment.transport_amount > 0 && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Transport</span>
                    <span>{formatCurrency(selectedPayment.transport_amount)}</span>
                  </div>
                )}
                {selectedPayment.bonus_amount > 0 && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Bonus</span>
                    <span>{formatCurrency(selectedPayment.bonus_amount)}</span>
                  </div>
                )}
                <div className="flex justify-between font-semibold pt-2 border-t">
                  <span>Total</span>
                  <span>{formatCurrency(selectedPayment.total_amount)}</span>
                </div>
              </div>
            </div>
            
            <div className="flex gap-3 justify-end">
              <Button
                variant="ghost"
                onClick={() => setShowPaymentDetailsModal(false)}
              >
                Close
              </Button>
              {selectedPayment.status === 'pending' && (
                <Button
                  variant="primary"
                  onClick={() => {
                    setSelectedPayments([selectedPayment.payment_id]);
                    setShowPaymentDetailsModal(false);
                    setShowBulkApproveModal(true);
                  }}
                >
                  Approve Payment
                </Button>
              )}
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default EnhancedTutorPaymentsPage;