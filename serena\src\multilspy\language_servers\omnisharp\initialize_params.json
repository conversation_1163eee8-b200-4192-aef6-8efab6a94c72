{"_description": "The parameters sent by the client when initializing the language server with the \"initialize\" request. More details at https://microsoft.github.io/language-server-protocol/specifications/lsp/3.17/specification/#initialize", "processId": "os.getpid()", "clientInfo": {"name": "Visual Studio Code - Insiders", "version": "1.82.0-insider"}, "locale": "en", "rootPath": "$rootPath", "rootUri": "$rootUri", "capabilities": {"workspace": {"applyEdit": true, "workspaceEdit": {"documentChanges": true, "resourceOperations": ["create", "rename", "delete"], "failureHandling": "textOnlyTransactional", "normalizesLineEndings": true, "changeAnnotationSupport": {"groupsOnLabel": true}}, "configuration": false, "didChangeWatchedFiles": {"dynamicRegistration": true, "relativePatternSupport": true}, "symbol": {"dynamicRegistration": true, "symbolKind": {"valueSet": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26]}, "tagSupport": {"valueSet": [1]}, "resolveSupport": {"properties": ["location.range"]}}, "codeLens": {"refreshSupport": true}, "executeCommand": {"dynamicRegistration": true}, "didChangeConfiguration": {"dynamicRegistration": true}, "workspaceFolders": true, "semanticTokens": {"refreshSupport": true}, "fileOperations": {"dynamicRegistration": true, "didCreate": true, "didRename": true, "didDelete": true, "willCreate": true, "willRename": true, "willDelete": true}, "inlineValue": {"refreshSupport": true}, "inlayHint": {"refreshSupport": true}, "diagnostics": {"refreshSupport": true}}, "textDocument": {"publishDiagnostics": {"relatedInformation": true, "versionSupport": false, "tagSupport": {"valueSet": [1, 2]}, "codeDescriptionSupport": true, "dataSupport": true}, "synchronization": {"dynamicRegistration": true, "willSave": true, "willSaveWaitUntil": true, "didSave": true}, "completion": {"dynamicRegistration": true, "contextSupport": true, "completionItem": {"snippetSupport": true, "commitCharactersSupport": true, "documentationFormat": ["markdown", "plaintext"], "deprecatedSupport": true, "preselectSupport": true, "tagSupport": {"valueSet": [1]}, "insertReplaceSupport": true, "resolveSupport": {"properties": ["documentation", "detail", "additionalTextEdits"]}, "insertTextModeSupport": {"valueSet": [1, 2]}, "labelDetailsSupport": true}, "insertTextMode": 2, "completionItemKind": {"valueSet": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25]}, "completionList": {"itemDefaults": ["commitCharacters", "edit<PERSON>ange", "insertTextFormat", "insertTextMode"]}}, "hover": {"dynamicRegistration": true, "contentFormat": ["markdown", "plaintext"]}, "signatureHelp": {"dynamicRegistration": true, "signatureInformation": {"documentationFormat": ["markdown", "plaintext"], "parameterInformation": {"labelOffsetSupport": true}, "activeParameterSupport": true}, "contextSupport": true}, "definition": {"dynamicRegistration": true, "linkSupport": true}, "references": {"dynamicRegistration": true}, "documentHighlight": {"dynamicRegistration": true}, "documentSymbol": {"dynamicRegistration": true, "symbolKind": {"valueSet": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26]}, "hierarchicalDocumentSymbolSupport": true, "tagSupport": {"valueSet": [1]}, "labelSupport": true}, "codeAction": {"dynamicRegistration": true, "isPreferredSupport": true, "disabledSupport": true, "dataSupport": true, "resolveSupport": {"properties": ["edit"]}, "codeActionLiteralSupport": {"codeActionKind": {"valueSet": ["", "quickfix", "refactor", "refactor.extract", "refactor.inline", "refactor.rewrite", "source", "source.organizeImports"]}}, "honorsChangeAnnotations": false}, "codeLens": {"dynamicRegistration": true}, "formatting": {"dynamicRegistration": true}, "rangeFormatting": {"dynamicRegistration": true}, "onTypeFormatting": {"dynamicRegistration": true}, "rename": {"dynamicRegistration": true, "prepareSupport": true, "prepareSupportDefaultBehavior": 1, "honorsChangeAnnotations": true}, "documentLink": {"dynamicRegistration": true, "tooltipSupport": true}, "typeDefinition": {"dynamicRegistration": true, "linkSupport": true}, "implementation": {"dynamicRegistration": true, "linkSupport": true}, "colorProvider": {"dynamicRegistration": true}, "foldingRange": {"dynamicRegistration": true, "rangeLimit": 5000, "lineFoldingOnly": true, "foldingRangeKind": {"valueSet": ["comment", "imports", "region"]}, "foldingRange": {"collapsedText": false}}, "declaration": {"dynamicRegistration": true, "linkSupport": true}, "selectionRange": {"dynamicRegistration": true}, "callHierarchy": {"dynamicRegistration": true}, "semanticTokens": {"dynamicRegistration": true, "tokenTypes": ["namespace", "type", "class", "enum", "interface", "struct", "typeParameter", "parameter", "variable", "property", "enumMember", "event", "function", "method", "macro", "keyword", "modifier", "comment", "string", "number", "regexp", "operator", "decorator"], "tokenModifiers": ["declaration", "definition", "readonly", "static", "deprecated", "abstract", "async", "modification", "documentation", "defaultLibrary"], "formats": ["relative"], "requests": {"range": true, "full": {"delta": true}}, "multilineTokenSupport": false, "overlappingTokenSupport": false, "serverCancelSupport": true, "augmentsSyntaxTokens": false}, "linkedEditingRange": {"dynamicRegistration": true}, "typeHierarchy": {"dynamicRegistration": true}, "inlineValue": {"dynamicRegistration": true}, "inlayHint": {"dynamicRegistration": true, "resolveSupport": {"properties": ["tooltip", "textEdits", "label.tooltip", "label.location", "label.command"]}}, "diagnostic": {"dynamicRegistration": true, "relatedDocumentSupport": false}}, "window": {"showMessage": {"messageActionItem": {"additionalPropertiesSupport": true}}, "showDocument": {"support": true}, "workDoneProgress": true}, "general": {"staleRequestSupport": {"cancel": true, "retryOnContentModified": ["textDocument/semanticTokens/full", "textDocument/semanticTokens/range", "textDocument/semanticTokens/full/delta"]}, "regularExpressions": {"engine": "ECMAScript", "version": "ES2020"}, "markdown": {"parser": "marked", "version": "1.1.0", "allowedTags": ["ul", "li", "p", "code", "blockquote", "ol", "h1", "h2", "h3", "h4", "h5", "h6", "hr", "em", "pre", "table", "thead", "tbody", "tr", "th", "td", "div", "del", "a", "strong", "br", "img", "span"]}, "positionEncodings": ["utf-16"]}, "notebookDocument": {"synchronization": {"dynamicRegistration": true, "executionSummarySupport": true}}, "experimental": {"snippetTextEdit": true, "codeActionGroup": true, "hoverActions": true, "serverStatusNotification": true, "colorDiagnosticOutput": true, "openServerLogs": true, "commands": {"commands": ["editor.action.triggerParameterHints"]}}}, "initializationOptions": {"RoslynExtensionsOptions": {"EnableDecompilationSupport": false, "EnableAnalyzersSupport": true, "EnableImportCompletion": true, "EnableAsyncCompletion": false, "DocumentAnalysisTimeoutMs": 30000, "DiagnosticWorkersThreadCount": 18, "AnalyzeOpenDocumentsOnly": true, "InlayHintsOptions": {"EnableForParameters": false, "ForLiteralParameters": false, "ForIndexerParameters": false, "ForObjectCreationParameters": false, "ForOtherParameters": false, "SuppressForParametersThatDifferOnlyBySuffix": false, "SuppressForParametersThatMatchMethodIntent": false, "SuppressForParametersThatMatchArgumentName": false, "EnableForTypes": false, "ForImplicitVariableTypes": false, "ForLambdaParameterTypes": false, "ForImplicitObjectCreation": false}, "LocationPaths": null}, "FormattingOptions": {"OrganizeImports": false, "EnableEditorConfigSupport": true, "NewLine": "\n", "UseTabs": false, "TabSize": 4, "IndentationSize": 4, "SpacingAfterMethodDeclarationName": false, "SeparateImportDirectiveGroups": false, "SpaceWithinMethodDeclarationParenthesis": false, "SpaceBetweenEmptyMethodDeclarationParentheses": false, "SpaceAfterMethodCallName": false, "SpaceWithinMethodCallParentheses": false, "SpaceBetweenEmptyMethodCallParentheses": false, "SpaceAfterControlFlowStatementKeyword": true, "SpaceWithinExpressionParentheses": false, "SpaceWithinCastParentheses": false, "SpaceWithinOtherParentheses": false, "SpaceAfterCast": false, "SpaceBeforeOpenSquareBracket": false, "SpaceBetweenEmptySquareBrackets": false, "SpaceWithinSquareBrackets": false, "SpaceAfterColonInBaseTypeDeclaration": true, "SpaceAfterComma": true, "SpaceAfterDot": false, "SpaceAfterSemicolonsInForStatement": true, "SpaceBeforeColonInBaseTypeDeclaration": true, "SpaceBeforeComma": false, "SpaceBeforeDot": false, "SpaceBeforeSemicolonsInForStatement": false, "SpacingAroundBinaryOperator": "single", "IndentBraces": false, "IndentBlock": true, "IndentSwitchSection": true, "IndentSwitchCaseSection": true, "IndentSwitchCaseSectionWhenBlock": true, "LabelPositioning": "oneLess", "WrappingPreserveSingleLine": true, "WrappingKeepStatementsOnSingleLine": true, "NewLinesForBracesInTypes": true, "NewLinesForBracesInMethods": true, "NewLinesForBracesInProperties": true, "NewLinesForBracesInAccessors": true, "NewLinesForBracesInAnonymousMethods": true, "NewLinesForBracesInControlBlocks": true, "NewLinesForBracesInAnonymousTypes": true, "NewLinesForBracesInObjectCollectionArrayInitializers": true, "NewLinesForBracesInLambdaExpressionBody": true, "NewLineForElse": true, "NewLineForCatch": true, "NewLineForFinally": true, "NewLineForMembersInObjectInit": true, "NewLineForMembersInAnonymousTypes": true, "NewLineForClausesInQuery": true}, "FileOptions": {"SystemExcludeSearchPatterns": ["**/node_modules/**/*", "**/bin/**/*", "**/obj/**/*", "**/.git/**/*", "**/.git", "**/.svn", "**/.hg", "**/CVS", "**/.DS_Store", "**/Thumbs.db"], "ExcludeSearchPatterns": []}, "RenameOptions": {"RenameOverloads": false, "RenameInStrings": false, "RenameInComments": false}, "ImplementTypeOptions": {"InsertionBehavior": 0, "PropertyGenerationBehavior": 0}, "DotNetCliOptions": {"LocationPaths": null}, "Plugins": {"LocationPaths": null}}, "trace": "verbose", "workspaceFolders": [{"uri": "$uri", "name": "$name"}]}