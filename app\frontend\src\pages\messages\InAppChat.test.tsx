import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { I18nextProvider } from 'react-i18next';
import InAppChat from './InAppChat';
import i18n from '../../i18n';

// Mock date-fns
vi.mock('date-fns', () => ({
  format: vi.fn((date, formatString) => {
    if (formatString === 'HH:mm') return '15:45';
    if (formatString === 'MMM dd, yyyy HH:mm') return 'Jan 15, 2024 15:45';
    return '2024-01-15';
  }),
}));

const renderInAppChat = () => {
  return render(
    <I18nextProvider i18n={i18n}>
      <InAppChat />
    </I18nextProvider>
  );
};

describe('InAppChat', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders in-app chat interface', () => {
    renderInAppChat();
    
    expect(screen.getByPlaceholderText('Search users...')).toBeInTheDocument();
    expect(screen.getByText('Online Users')).toBeInTheDocument();
  });

  it('displays online users list', () => {
    renderInAppChat();
    
    expect(screen.getByText('Marie Dubois')).toBeInTheDocument();
    expect(screen.getByText('John Smith')).toBeInTheDocument();
    expect(screen.getByText('Sophie Martin')).toBeInTheDocument();
  });

  it('shows user online status indicators', () => {
    renderInAppChat();
    
    // Should show green dots for online users
    const onlineIndicators = screen.getAllByTestId('online-indicator');
    expect(onlineIndicators.length).toBeGreaterThan(0);
  });

  it('filters users by search query', async () => {
    const user = userEvent.setup();
    renderInAppChat();
    
    const searchInput = screen.getByPlaceholderText('Search users...');
    await user.type(searchInput, 'Marie');
    
    await waitFor(() => {
      expect(screen.getByText('Marie Dubois')).toBeInTheDocument();
      expect(screen.queryByText('John Smith')).not.toBeInTheDocument();
    });
  });

  it('opens chat when user is selected', async () => {
    renderInAppChat();
    
    const userItem = screen.getByText('Marie Dubois').closest('div');
    fireEvent.click(userItem!);
    
    await waitFor(() => {
      expect(screen.getByText('Marie Dubois')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Type a message...')).toBeInTheDocument();
    });
  });

  it('displays message bubbles correctly', () => {
    renderInAppChat();
    
    // Select a user to view messages
    const userItem = screen.getByText('Marie Dubois').closest('div');
    fireEvent.click(userItem!);
    
    // Should show message bubbles
    expect(screen.getByText('Hello! How are you today?')).toBeInTheDocument();
    expect(screen.getByText('I have a question about my upcoming session.')).toBeInTheDocument();
  });

  it('shows typing indicator when user is typing', () => {
    renderInAppChat();
    
    // Select a user
    const userItem = screen.getByText('Marie Dubois').closest('div');
    fireEvent.click(userItem!);
    
    // Should show typing indicator for Marie
    expect(screen.getByText('Marie is typing...')).toBeInTheDocument();
  });

  it('sends message on button click', async () => {
    const user = userEvent.setup();
    renderInAppChat();
    
    // Select a user
    const userItem = screen.getByText('Marie Dubois').closest('div');
    fireEvent.click(userItem!);
    
    const messageInput = screen.getByPlaceholderText('Type a message...');
    const sendButton = screen.getByRole('button', { name: /send/i });
    
    await user.type(messageInput, 'Test message');
    fireEvent.click(sendButton);
    
    await waitFor(() => {
      expect(messageInput).toHaveValue('');
    });
  });

  it('sends message on Enter key press', async () => {
    const user = userEvent.setup();
    renderInAppChat();
    
    // Select a user
    const userItem = screen.getByText('Marie Dubois').closest('div');
    fireEvent.click(userItem!);
    
    const messageInput = screen.getByPlaceholderText('Type a message...');
    await user.type(messageInput, 'Test message{enter}');
    
    await waitFor(() => {
      expect(messageInput).toHaveValue('');
    });
  });

  it('prevents sending empty messages', () => {
    renderInAppChat();
    
    // Select a user
    const userItem = screen.getByText('Marie Dubois').closest('div');
    fireEvent.click(userItem!);
    
    const sendButton = screen.getByRole('button', { name: /send/i });
    expect(sendButton).toBeDisabled();
  });

  it('displays message timestamps', () => {
    renderInAppChat();
    
    // Select a user to view messages
    const userItem = screen.getByText('Marie Dubois').closest('div');
    fireEvent.click(userItem!);
    
    expect(screen.getByText('15:45')).toBeInTheDocument();
  });

  it('shows read status for messages', () => {
    renderInAppChat();
    
    // Select a user
    const userItem = screen.getByText('Marie Dubois').closest('div');
    fireEvent.click(userItem!);
    
    // Should show read/unread indicators
    const readIndicators = screen.getAllByTestId('check-circle');
    expect(readIndicators.length).toBeGreaterThan(0);
  });

  it('displays unread message count for users', () => {
    renderInAppChat();
    
    // Marie should have unread messages
    expect(screen.getByText('3')).toBeInTheDocument();
  });

  it('handles different message types', () => {
    renderInAppChat();
    
    // Select a user
    const userItem = screen.getByText('Marie Dubois').closest('div');
    fireEvent.click(userItem!);
    
    // Should distinguish between sent and received messages
    const sentMessage = screen.getByText('Great! See you then.');
    const receivedMessage = screen.getByText('Hello! How are you today?');
    
    expect(sentMessage).toBeInTheDocument();
    expect(receivedMessage).toBeInTheDocument();
  });

  it('shows role indicators in user list', () => {
    renderInAppChat();
    
    const clientIndicators = screen.getAllByText('👤');
    const tutorIndicators = screen.getAllByText('👨‍🏫');
    
    expect(clientIndicators.length).toBeGreaterThan(0);
    expect(tutorIndicators.length).toBeGreaterThan(0);
  });

  it('displays last message preview in user list', () => {
    renderInAppChat();
    
    expect(screen.getByText('Great! See you then.')).toBeInTheDocument();
    expect(screen.getByText('Perfect! Thank you.')).toBeInTheDocument();
  });

  it('handles user search with no results', async () => {
    const user = userEvent.setup();
    renderInAppChat();
    
    const searchInput = screen.getByPlaceholderText('Search users...');
    await user.type(searchInput, 'NonexistentUser');
    
    await waitFor(() => {
      expect(screen.getByText('No users found')).toBeInTheDocument();
    });
  });

  it('shows empty state when no chat selected', () => {
    renderInAppChat();
    
    expect(screen.getByText('Select a user to start chatting')).toBeInTheDocument();
  });

  it('handles message loading state', async () => {
    renderInAppChat();
    
    const userItem = screen.getByText('Marie Dubois').closest('div');
    fireEvent.click(userItem!);
    
    // Should handle loading gracefully
    await waitFor(() => {
      expect(screen.getByPlaceholderText('Type a message...')).toBeInTheDocument();
    });
  });

  it('displays proper message alignment', () => {
    renderInAppChat();
    
    // Select a user
    const userItem = screen.getByText('Marie Dubois').closest('div');
    fireEvent.click(userItem!);
    
    // Sent messages should be right-aligned, received left-aligned
    const messageContainer = screen.getByTestId('messages-container');
    expect(messageContainer).toBeInTheDocument();
  });

  it('handles real-time message updates', async () => {
    renderInAppChat();
    
    // Select a user
    const userItem = screen.getByText('Marie Dubois').closest('div');
    fireEvent.click(userItem!);
    
    // Should handle real-time updates (WebSocket simulation)
    await waitFor(() => {
      expect(screen.getByText('Hello! How are you today?')).toBeInTheDocument();
    });
  });

  it('shows connection status indicator', () => {
    renderInAppChat();
    
    // Should show connection status
    const connectionStatus = screen.getByTestId('connection-status');
    expect(connectionStatus).toBeInTheDocument();
  });
});