import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { I18nextProvider } from 'react-i18next';
import ClientsPage from './ClientsPage';
import i18n from '../../i18n';

// Mock the auth context
const mockAuthContext = {
  user: {
    id: '1',
    email: '<EMAIL>',
    activeRole: 'manager',
    roles: ['manager'],
    firstName: 'Test',
    lastName: 'Manager',
  },
  isAuthenticated: true,
  isLoading: false,
  login: vi.fn(),
  logout: vi.fn(),
  switchRole: vi.fn(),
};

// Mock API service
vi.mock('../../services/auth.service', () => ({
  authService: {
    getClients: vi.fn().mockResolvedValue([
      {
        id: '1',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+****************',
        status: 'active',
        createdAt: '2024-01-15',
      },
      {
        id: '2',
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        phone: '+****************',
        status: 'inactive',
        createdAt: '2024-01-10',
      },
    ]),
    getClientById: vi.fn().mockResolvedValue({
      id: '1',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '+****************',
      status: 'active',
      address: '123 Main St, Montreal, QC',
      emergencyContact: {
        name: 'Emergency Contact',
        phone: '+****************',
      },
      notes: 'Test notes',
    }),
  },
}));

const renderClientsPage = () => {
  return render(
    <BrowserRouter>
      <I18nextProvider i18n={i18n}>
        <ClientsPage />
      </I18nextProvider>
    </BrowserRouter>
  );
};

describe('ClientsPage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders clients page with search and filters', async () => {
    renderClientsPage();
    
    expect(screen.getByPlaceholderText(/search clients/i)).toBeInTheDocument();
    expect(screen.getByText('Add Client')).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    });
  });

  it('displays client list with proper information', async () => {
    renderClientsPage();
    
    await waitFor(() => {
      // Check client information
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByText('+****************')).toBeInTheDocument();
    });
  });

  it('shows status badges for clients', async () => {
    renderClientsPage();
    
    await waitFor(() => {
      const activeStatus = screen.getByText('Active');
      const inactiveStatus = screen.getByText('Inactive');
      
      expect(activeStatus).toBeInTheDocument();
      expect(inactiveStatus).toBeInTheDocument();
      
      expect(activeStatus).toHaveClass('text-accent-green');
      expect(inactiveStatus).toHaveClass('text-text-secondary');
    });
  });

  it('filters clients by search query', async () => {
    const user = userEvent.setup();
    renderClientsPage();
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });
    
    const searchInput = screen.getByPlaceholderText(/search clients/i);
    await user.type(searchInput, 'John');
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
    });
  });

  it('filters clients by status', async () => {
    renderClientsPage();
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });
    
    const statusFilter = screen.getByDisplayValue('All Status');
    fireEvent.change(statusFilter, { target: { value: 'active' } });
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
    });
  });

  it('opens client detail when clicked', async () => {
    renderClientsPage();
    
    await waitFor(() => {
      const clientRow = screen.getByText('John Doe').closest('tr');
      fireEvent.click(clientRow!);
    });
    
    await waitFor(() => {
      expect(screen.getByText('Client Details')).toBeInTheDocument();
      expect(screen.getByText('123 Main St, Montreal, QC')).toBeInTheDocument();
    });
  });

  it('displays client profile information in detail view', async () => {
    renderClientsPage();
    
    await waitFor(() => {
      const clientRow = screen.getByText('John Doe').closest('tr');
      fireEvent.click(clientRow!);
    });
    
    await waitFor(() => {
      expect(screen.getByText('Emergency Contact')).toBeInTheDocument();
      expect(screen.getByText('+****************')).toBeInTheDocument();
      expect(screen.getByText('Test notes')).toBeInTheDocument();
    });
  });

  it('shows add client button for managers', () => {
    renderClientsPage();
    
    const addButton = screen.getByRole('button', { name: /add client/i });
    expect(addButton).toBeInTheDocument();
  });

  it('handles client creation flow', async () => {
    renderClientsPage();
    
    const addButton = screen.getByRole('button', { name: /add client/i });
    fireEvent.click(addButton);
    
    // Should open client creation modal/form
    await waitFor(() => {
      expect(screen.getByText('Create New Client')).toBeInTheDocument();
    });
  });

  it('displays proper table headers', async () => {
    renderClientsPage();
    
    expect(screen.getByText('Name')).toBeInTheDocument();
    expect(screen.getByText('Email')).toBeInTheDocument();
    expect(screen.getByText('Phone')).toBeInTheDocument();
    expect(screen.getByText('Status')).toBeInTheDocument();
    expect(screen.getByText('Created')).toBeInTheDocument();
  });

  it('shows loading state initially', () => {
    renderClientsPage();
    
    // Should show loading indicator
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });

  it('handles empty search results', async () => {
    const user = userEvent.setup();
    renderClientsPage();
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });
    
    const searchInput = screen.getByPlaceholderText(/search clients/i);
    await user.type(searchInput, 'NonexistentName');
    
    await waitFor(() => {
      expect(screen.getByText('No clients found')).toBeInTheDocument();
    });
  });

  it('sorts clients by different columns', async () => {
    renderClientsPage();
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });
    
    const nameHeader = screen.getByText('Name');
    fireEvent.click(nameHeader);
    
    // Should trigger sorting (implementation specific)
    expect(nameHeader).toBeInTheDocument();
  });

  it('shows pagination when there are many clients', async () => {
    renderClientsPage();
    
    await waitFor(() => {
      // If there are many clients, pagination should appear
      const pagination = screen.queryByText('Next');
      if (pagination) {
        expect(pagination).toBeInTheDocument();
      }
    });
  });

  it('handles client status updates', async () => {
    renderClientsPage();
    
    await waitFor(() => {
      const clientRow = screen.getByText('John Doe').closest('tr');
      fireEvent.click(clientRow!);
    });
    
    await waitFor(() => {
      const editButton = screen.getByRole('button', { name: /edit/i });
      fireEvent.click(editButton);
    });
    
    // Should allow status editing
    expect(screen.getByDisplayValue('active')).toBeInTheDocument();
  });

  it('displays creation dates properly formatted', async () => {
    renderClientsPage();
    
    await waitFor(() => {
      // Should show formatted dates
      expect(screen.getByText('Jan 15, 2024')).toBeInTheDocument();
      expect(screen.getByText('Jan 10, 2024')).toBeInTheDocument();
    });
  });
});