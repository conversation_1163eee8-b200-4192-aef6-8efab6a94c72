"""
User domain models for authentication, roles, and user management.
"""

from datetime import datetime
from typing import List, Optional, Dict
from uuid import UUID
from pydantic import BaseModel, Field, field_validator, ConfigDict
from typing import Annotated
from pydantic.types import constr
import re

# Use a simple string pattern instead of EmailStr to avoid email-validator dependency
EmailStr = Annotated[str, constr(pattern=r'^[\w\.-]+@[\w\.-]+\.\w+$')]

from app.models.base import (
    BaseEntity, 
    IdentifiedEntity, 
    UserRoleType, 
    ConsentLevel, 
    ConsentType,
    ContactModel
)


class PasswordStrength(BaseModel):
    """Password strength assessment result."""
    
    score: int = Field(..., ge=0, le=5, description="Password strength score (0-5)")
    feedback: List[str] = Field(default_factory=list, description="Feedback messages")
    
    @classmethod
    def check_password_strength(cls, password: str) -> 'PasswordStrength':
        """
        Check password strength and return assessment.
        
        Args:
            password: Password to check
            
        Returns:
            PasswordStrength object with score and feedback
        """
        score = 0
        feedback = []
        
        # Length check
        if len(password) < 8:
            feedback.append("Password must be at least 8 characters long")
        elif len(password) >= 12:
            score += 1
        elif len(password) >= 8:
            score += 0.5
        
        # Uppercase check
        if not any(c.isupper() for c in password):
            feedback.append("Password must contain at least one uppercase letter")
        else:
            score += 1
        
        # Lowercase check
        if not any(c.islower() for c in password):
            feedback.append("Password must contain at least one lowercase letter")
        else:
            score += 1
        
        # Digit check
        if not any(c.isdigit() for c in password):
            feedback.append("Password must contain at least one digit")
        else:
            score += 1
        
        # Special character check
        if re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            score += 1
        else:
            feedback.append("Consider adding special characters for better security")
        
        # Common patterns check
        common_patterns = ['123', '321', 'abc', 'qwerty', 'password']
        if any(pattern in password.lower() for pattern in common_patterns):
            score -= 0.5
            feedback.append("Avoid common patterns in your password")
        
        # Ensure score is within bounds
        score = max(0, min(5, int(score)))
        
        return cls(score=score, feedback=feedback)


class User(IdentifiedEntity):
    """User account model."""
    
    user_id: int = Field(..., description="Unique user identifier")
    email: EmailStr = Field(..., description="User email address")
    password_hash: Optional[str] = Field(None, description="Hashed password")
    google_id: Optional[str] = Field(None, description="Google OAuth ID")
    is_email_verified: bool = Field(False, description="Whether email is verified")
    roles: List[UserRoleType] = Field(default_factory=list, description="User roles")
    
    def has_local_auth(self) -> bool:
        """Check if user has local password authentication."""
        return self.password_hash is not None
    
    def has_google_auth(self) -> bool:
        """Check if user has Google OAuth authentication."""
        return self.google_id is not None


class UserRole(IdentifiedEntity):
    """User role assignment model."""
    
    user_role_id: int = Field(..., description="Unique role assignment identifier")
    user_id: int = Field(..., description="User identifier")
    role_type: UserRoleType = Field(..., description="Role type")
    is_active: bool = Field(True, description="Whether role is active")


class UserConsent(IdentifiedEntity):
    """User consent tracking model."""
    
    consent_id: int = Field(..., description="Unique consent identifier")
    user_id: int = Field(..., description="User identifier")
    consent_level: ConsentLevel = Field(..., description="Consent level")
    consent_type: ConsentType = Field(..., description="Type of consent")
    accepted_at: datetime = Field(..., description="When consent was accepted")
    ip_address: Optional[str] = Field(None, description="IP address when consent was given")


class PasswordResetToken(IdentifiedEntity):
    """Password reset token model."""
    
    reset_id: int = Field(..., description="Unique reset token identifier")
    user_id: int = Field(..., description="User identifier")
    token: UUID = Field(..., description="Reset token")
    expires_at: datetime = Field(..., description="Token expiration time")
    used_at: Optional[datetime] = Field(None, description="When token was used")
    
    def is_expired(self) -> bool:
        """Check if token is expired."""
        return datetime.utcnow() > self.expires_at
    
    def is_used(self) -> bool:
        """Check if token has been used."""
        return self.used_at is not None
    
    def is_valid(self) -> bool:
        """Check if token is valid (not expired and not used)."""
        return not self.is_expired() and not self.is_used()


class TutorInvitation(IdentifiedEntity):
    """Tutor invitation model."""
    
    invitation_id: int = Field(..., description="Unique invitation identifier")
    manager_id: int = Field(..., description="Manager who sent invitation")
    email: EmailStr = Field(..., description="Invited email address")
    invitation_token: UUID = Field(..., description="Invitation token")
    expires_at: datetime = Field(..., description="Invitation expiration time")
    used_at: Optional[datetime] = Field(None, description="When invitation was used")
    
    def is_expired(self) -> bool:
        """Check if invitation is expired."""
        return datetime.utcnow() > self.expires_at
    
    def is_used(self) -> bool:
        """Check if invitation has been used."""
        return self.used_at is not None
    
    def is_valid(self) -> bool:
        """Check if invitation is valid (not expired and not used)."""
        return not self.is_expired() and not self.is_used()


class PushNotificationToken(IdentifiedEntity):
    """Push notification token model."""
    
    token_id: int = Field(..., description="Unique token identifier")
    user_id: int = Field(..., description="User identifier")
    device_token: str = Field(..., description="Device push token")
    platform: str = Field(..., description="Platform (ios/android/web)")
    is_active: bool = Field(True, description="Whether token is active")
    
    @field_validator('platform')
    @classmethod
    def validate_platform(cls, v: str) -> str:
        """Validate platform value."""
        valid_platforms = {'ios', 'android', 'web'}
        if v.lower() not in valid_platforms:
            raise ValueError(f'Platform must be one of: {", ".join(valid_platforms)}')
        return v.lower()


class NotificationPreference(IdentifiedEntity):
    """User notification preferences model."""
    
    preference_id: int = Field(..., description="Unique preference identifier")
    user_id: int = Field(..., description="User identifier")
    notification_type: str = Field(..., description="Type of notification")
    push_enabled: bool = Field(True, description="Push notifications enabled")
    sms_enabled: bool = Field(True, description="SMS notifications enabled")
    email_enabled: bool = Field(True, description="Email notifications enabled")


class UserProfile(BaseModel):
    """Combined user profile with roles and basic info."""
    
    model_config = ConfigDict(from_attributes=True)
    
    user: User
    roles: List[UserRole]
    consents: List[UserConsent]
    notification_preferences: List[NotificationPreference]
    
    def get_active_roles(self) -> List[UserRoleType]:
        """Get list of active role types."""
        return [role.role_type for role in self.roles if role.is_active]
    
    def has_role(self, role_type: UserRoleType) -> bool:
        """Check if user has specific active role."""
        return role_type in self.get_active_roles()
    
    def can_switch_to_role(self, role_type: UserRoleType) -> bool:
        """Check if user can switch to specific role."""
        return self.has_role(role_type)
    
    def has_mandatory_consents(self) -> bool:
        """Check if user has accepted all mandatory consents."""
        mandatory_consents = [
            consent for consent in self.consents 
            if consent.consent_level == ConsentLevel.LEVEL_1
        ]
        required_types = {ConsentType.TERMS_OF_SERVICE, ConsentType.PRIVACY_POLICY}
        given_types = {consent.consent_type for consent in mandatory_consents}
        return required_types.issubset(given_types)


# ============================================
# Create/Update Schemas
# ============================================

class UserCreate(BaseModel):
    """Schema for creating a new user."""
    
    model_config = ConfigDict(from_attributes=True)
    
    email: EmailStr = Field(..., description="User email address")
    password: Optional[str] = Field(None, min_length=8, description="User password")
    google_id: Optional[str] = Field(None, description="Google OAuth ID")
    roles: List[UserRoleType] = Field(default_factory=list, description="Initial user roles")
    
    @field_validator('password')
    @classmethod
    def validate_password(cls, v: Optional[str]) -> Optional[str]:
        """Validate password strength."""
        if v is None:
            return v
        
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        
        # Check for basic complexity
        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)
        
        if not (has_upper and has_lower and has_digit):
            raise ValueError('Password must contain uppercase, lowercase, and digit')
        
        return v


class UserUpdate(BaseModel):
    """Schema for updating user information."""
    
    model_config = ConfigDict(from_attributes=True)
    
    # Note: email changes require verification, handled separately
    # password changes handled by PasswordChange schema


class PasswordChange(BaseModel):
    """Schema for changing password."""
    
    model_config = ConfigDict(from_attributes=True)
    
    current_password: str = Field(..., description="Current password")
    new_password: str = Field(..., min_length=8, description="New password")
    
    @field_validator('new_password')
    @classmethod
    def validate_new_password(cls, v: str) -> str:
        """Validate new password strength."""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        
        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)
        
        if not (has_upper and has_lower and has_digit):
            raise ValueError('Password must contain uppercase, lowercase, and digit')
        
        return v


class PasswordReset(BaseModel):
    """Schema for password reset request."""
    
    model_config = ConfigDict(from_attributes=True)
    
    email: EmailStr = Field(..., description="Email address")


class PasswordResetConfirm(BaseModel):
    """Schema for confirming password reset."""
    
    model_config = ConfigDict(from_attributes=True)
    
    token: str = Field(..., description="Reset token")
    new_password: str = Field(..., min_length=8, description="New password")
    
    @field_validator('new_password')
    @classmethod
    def validate_new_password(cls, v: str) -> str:
        """Validate new password strength."""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        
        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)
        
        if not (has_upper and has_lower and has_digit):
            raise ValueError('Password must contain uppercase, lowercase, and digit')
        
        return v


class ConsentAcceptance(BaseModel):
    """Schema for accepting user consent."""
    
    model_config = ConfigDict(from_attributes=True)
    
    consent_level: ConsentLevel = Field(..., description="Level of consent")
    consent_type: ConsentType = Field(..., description="Type of consent")
    ip_address: Optional[str] = Field(None, description="User's IP address")


class TutorInvitationCreate(BaseModel):
    """Schema for creating tutor invitation."""
    
    model_config = ConfigDict(from_attributes=True)
    
    email: EmailStr = Field(..., description="Email to invite")
    expires_hours: int = Field(72, ge=1, le=168, description="Hours until expiration (max 1 week)")


class NotificationPreferenceUpdate(BaseModel):
    """Schema for updating notification preferences."""
    
    model_config = ConfigDict(from_attributes=True)
    
    notification_type: str = Field(..., description="Type of notification")
    push_enabled: bool = Field(..., description="Enable push notifications")
    sms_enabled: bool = Field(..., description="Enable SMS notifications")
    email_enabled: bool = Field(..., description="Enable email notifications")


class PushTokenRegistration(BaseModel):
    """Schema for registering push notification token."""
    
    model_config = ConfigDict(from_attributes=True)
    
    device_token: str = Field(..., description="Device push token")
    platform: str = Field(..., description="Platform (ios/android/web)")
    
    @field_validator('platform')
    @classmethod
    def validate_platform(cls, v: str) -> str:
        """Validate platform value."""
        valid_platforms = {'ios', 'android', 'web'}
        if v.lower() not in valid_platforms:
            raise ValueError(f'Platform must be one of: {", ".join(valid_platforms)}')
        return v.lower()


# ============================================
# Response Schemas
# ============================================

class UserResponse(BaseModel):
    """User response schema."""
    
    model_config = ConfigDict(from_attributes=True)
    
    user_id: int
    email: EmailStr
    is_email_verified: bool
    has_local_auth: bool
    has_google_auth: bool
    roles: List[UserRoleType]
    active_role: Optional[UserRoleType] = Field(None, description="Currently active role")
    created_at: datetime
    updated_at: datetime


class AuthResponse(BaseModel):
    """Authentication response schema."""
    
    model_config = ConfigDict(from_attributes=True)
    
    access_token: str = Field(..., description="JWT access token")
    refresh_token: Optional[str] = Field(None, description="JWT refresh token")
    token_type: str = Field("bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration in seconds")
    user: UserResponse = Field(..., description="User information")
    requires_consents: List[ConsentType] = Field(default_factory=list, description="Required consents")


class TokenResponse(BaseModel):
    """Token response schema."""
    
    model_config = ConfigDict(from_attributes=True)
    
    access_token: str = Field(..., description="JWT access token")
    token_type: str = Field("bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration in seconds")


class LoginRequest(BaseModel):
    """Login request schema."""
    
    model_config = ConfigDict(from_attributes=True)
    
    email: EmailStr = Field(..., description="User email address")
    password: str = Field(..., description="User password")
    requested_role: Optional[UserRoleType] = Field(None, description="Specific role to login as")


class RefreshTokenRequest(BaseModel):
    """Refresh token request schema."""
    
    model_config = ConfigDict(from_attributes=True)
    
    refresh_token: str = Field(..., description="JWT refresh token")