export enum UserRoleType {
  MANAGER = 'MANAGER',
  TUTOR = 'TUTOR',
  CLIENT = 'CLIENT',
}

export interface User {
  userId: number;
  email: string;
  firstName?: string;
  lastName?: string;
  roles: UserRoleType[];
  activeRole?: UserRoleType;
  isActive?: boolean;
  emailVerified: boolean;
  isEmailVerified?: boolean; // Backend returns this instead of emailVerified
  hasLocalAuth?: boolean;
  hasGoogleAuth?: boolean;
  postal_code?: string;
  address?: string;
  city?: string;
  province?: string;
  phone?: string;
  createdAt: string;
  updatedAt: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface AuthResponse {
  accessToken: string;
  refreshToken: string;
  tokenType: string;
  expiresIn: number;
  user: User;
}

export interface AuthContextType {
  user: User | null;
  accessToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  switchRole: (role: UserRoleType) => Promise<void>;
  refreshAccessToken: () => Promise<void>;
  handleOAuthCallback: (tokenData: {
    access_token: string;
    refresh_token: string;
    token_type: string;
    expires_in: number;
  }) => Promise<User>;
}