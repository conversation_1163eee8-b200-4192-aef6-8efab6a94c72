name: Lin<PERSON>, Types and Docs Check

on:
  workflow_dispatch:  # Manual trigger only - workflow disabled from auto-running

jobs:
  check:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      pages: write
      actions: write
      contents: read
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: 3.11
      
      
      # use uv package manager
      - name: Install uv
        run: pip install uv
      - uses: actions/cache@v3
        name: Cache dependencies
        with:
          path: ~/.cache/uv
          key: uv-${{ hashFiles('pyproject.toml') }}
      - name: Install dependencies
        run: |
          uv venv
          uv pip install -e ".[dev]"
      - name: Lint
        run: uv run poe lint
      - name: Types
        run: uv run poe type-check
      - name: Docs
        run: uv run poe doc-build
      - name: Upload artifact
        uses: actions/upload-pages-artifact@v2
        with:
          path: "docs/_build"
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v2