"""
Tutor invitation API endpoints.
"""

from typing import List

from fastapi import APIRouter, Depends, HTTPException, Query, status

from app.models.tutor_invitation_models import (
    TutorInvitation,
    TutorInvitationCreate,
    TutorInvitationWithInviter,
    TutorInvitationAccept
)
from app.models.auth_models import TokenData
from app.services.tutor_invitation_service import TutorInvitationService
from app.core.dependencies import (
    get_db_manager,
    get_current_user,
    check_rate_limit
)
from app.core.exceptions import (
    ResourceNotFoundError,
    ForbiddenError,
    ValidationError,
    BusinessLogicError,
    DuplicateResourceError
)
from app.config.database import DatabaseManager


router = APIRouter(prefix="/tutors", tags=["tutor-invitations"])


@router.post(
    "/invite",
    response_model=TutorInvitation,
    status_code=status.HTTP_201_CREATED,
    summary="Send tutor invitation",
    description="Send invitation to join as a tutor (managers only)"
)
async def send_tutor_invitation(
    invitation_data: TutorInvitationCreate,
    current_user: TokenData = Depends(get_current_user),
    db_manager: DatabaseManager = Depends(get_db_manager),
    _: None = Depends(check_rate_limit)
):
    """Send a tutor invitation."""
    service = TutorInvitationService(db_manager)
    
    try:
        invitation = await service.create_invitation(
            invitation_data=invitation_data,
            current_user_id=int(current_user.user_id),
            current_user_role=current_user.role
        )
        return invitation
    except (BusinessLogicError, DuplicateResourceError) as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )
    except ForbiddenError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )


@router.get(
    "/invitations",
    response_model=List[TutorInvitation],
    summary="List tutor invitations",
    description="List pending tutor invitations (managers only)"
)
async def list_tutor_invitations(
    only_mine: bool = Query(False, description="Only show invitations I sent"),
    current_user: TokenData = Depends(get_current_user),
    db_manager: DatabaseManager = Depends(get_db_manager)
):
    """List tutor invitations."""
    service = TutorInvitationService(db_manager)
    
    try:
        invitations = await service.list_invitations(
            current_user_id=int(current_user.user_id),
            current_user_role=current_user.role,
            only_mine=only_mine
        )
        return invitations
    except ForbiddenError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )


@router.get(
    "/invitations/search",
    response_model=List[TutorInvitationWithInviter],
    summary="Search tutor invitations",
    description="Search tutor invitations (managers only)"
)
async def search_tutor_invitations(
    q: str = Query(..., min_length=2, description="Search query"),
    limit: int = Query(20, ge=1, le=50, description="Maximum results"),
    current_user: TokenData = Depends(get_current_user),
    db_manager: DatabaseManager = Depends(get_db_manager)
):
    """Search tutor invitations."""
    service = TutorInvitationService(db_manager)
    
    try:
        invitations = await service.search_invitations(
            query=q,
            current_user_role=current_user.role,
            limit=limit
        )
        return invitations
    except ForbiddenError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )


@router.get(
    "/invitation/{invitation_id}",
    response_model=TutorInvitationWithInviter,
    summary="Get invitation details",
    description="Get tutor invitation details (managers only)"
)
async def get_tutor_invitation(
    invitation_id: int,
    current_user: TokenData = Depends(get_current_user),
    db_manager: DatabaseManager = Depends(get_db_manager)
):
    """Get tutor invitation details."""
    service = TutorInvitationService(db_manager)
    
    try:
        invitation = await service.get_invitation(
            invitation_id=invitation_id,
            current_user_id=int(current_user.user_id),
            current_user_role=current_user.role
        )
        return invitation
    except ResourceNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except ForbiddenError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )


@router.get(
    "/invitation/verify/{token}",
    response_model=TutorInvitation,
    summary="Verify invitation token",
    description="Verify tutor invitation token (public endpoint)"
)
async def verify_invitation_token(
    token: str,
    db_manager: DatabaseManager = Depends(get_db_manager)
):
    """Verify invitation token."""
    service = TutorInvitationService(db_manager)
    
    try:
        invitation = await service.get_invitation_by_token(token)
        return invitation
    except (ResourceNotFoundError, BusinessLogicError) as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )


@router.post(
    "/invitation/accept",
    response_model=dict,
    summary="Accept tutor invitation",
    description="Accept invitation and create tutor account (public endpoint)"
)
async def accept_tutor_invitation(
    accept_data: TutorInvitationAccept,
    db_manager: DatabaseManager = Depends(get_db_manager),
    _: None = Depends(check_rate_limit)
):
    """Accept tutor invitation."""
    service = TutorInvitationService(db_manager)
    
    try:
        result = await service.accept_invitation(accept_data)
        return result
    except (ResourceNotFoundError, BusinessLogicError) as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post(
    "/invitation/{invitation_id}/resend",
    response_model=TutorInvitation,
    summary="Resend invitation",
    description="Resend tutor invitation email (managers only)"
)
async def resend_tutor_invitation(
    invitation_id: int,
    current_user: TokenData = Depends(get_current_user),
    db_manager: DatabaseManager = Depends(get_db_manager)
):
    """Resend tutor invitation."""
    service = TutorInvitationService(db_manager)
    
    try:
        invitation = await service.resend_invitation(
            invitation_id=invitation_id,
            current_user_id=int(current_user.user_id),
            current_user_role=current_user.role
        )
        return invitation
    except ResourceNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except (ForbiddenError, BusinessLogicError) as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )


@router.delete(
    "/invitation/{invitation_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Cancel invitation",
    description="Cancel pending tutor invitation (managers only)"
)
async def cancel_tutor_invitation(
    invitation_id: int,
    current_user: TokenData = Depends(get_current_user),
    db_manager: DatabaseManager = Depends(get_db_manager)
):
    """Cancel tutor invitation."""
    service = TutorInvitationService(db_manager)
    
    try:
        await service.cancel_invitation(
            invitation_id=invitation_id,
            current_user_id=int(current_user.user_id),
            current_user_role=current_user.role
        )
    except ResourceNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except (ForbiddenError, BusinessLogicError) as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )