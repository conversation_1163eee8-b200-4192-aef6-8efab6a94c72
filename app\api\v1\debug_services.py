"""
Debug endpoint for services - temporary solution to test CRUD functionality
"""

from fastapi import APIRouter, Depends
import asyncpg
from typing import List, Dict, Any

from app.core.dependencies import get_database

router = APIRouter(prefix="/debug", tags=["debug"])

@router.get("/services/raw")
async def get_raw_services(db: asyncpg.Connection = Depends(get_database)):
    """Get raw services data directly from database for debugging."""
    try:
        # Get all services directly from database
        services = await db.fetch("""
            SELECT 
                service_id, service_name, subject_area, service_type, service_level,
                description, is_active, created_by, created_at, updated_at
            FROM service_catalog
            ORDER BY service_id
        """)
        
        # Convert to list of dicts
        result = []
        for service in services:
            result.append({
                "service_id": service["service_id"],
                "service_name": service["service_name"],
                "subject_area": service["subject_area"],
                "service_type": service["service_type"],
                "service_level": service["service_level"],
                "description": service["description"],
                "is_active": service["is_active"],
                "created_by": service["created_by"],
                "created_at": service["created_at"].isoformat() if service["created_at"] else None,
                "updated_at": service["updated_at"].isoformat() if service["updated_at"] else None,
            })
        
        return {
            "total": len(result),
            "services": result
        }
        
    except Exception as e:
        return {
            "error": str(e),
            "total": 0,
            "services": []
        }

@router.get("/users/raw")
async def get_raw_users(db: asyncpg.Connection = Depends(get_database)):
    """Get raw users data directly from database for debugging."""
    try:
        # Get all users with their roles
        users = await db.fetch("""
            SELECT 
                u.user_id, u.email, u.email_verified, u.created_at,
                array_agg(ur.role_type) as roles
            FROM user_accounts u
            LEFT JOIN user_roles ur ON u.user_id = ur.user_id
            GROUP BY u.user_id, u.email, u.email_verified, u.created_at
            ORDER BY u.user_id
        """)
        
        # Convert to list of dicts
        result = []
        for user in users:
            result.append({
                "user_id": user["user_id"],
                "email": user["email"],
                "email_verified": user["email_verified"],
                "roles": user["roles"] if user["roles"] and user["roles"][0] else [],
                "created_at": user["created_at"].isoformat() if user["created_at"] else None,
            })
        
        return {
            "total": len(result),
            "users": result
        }
        
    except Exception as e:
        return {
            "error": str(e),
            "total": 0,
            "users": []
        }

@router.get("/profiles/raw")
async def get_raw_profiles(db: asyncpg.Connection = Depends(get_database)):
    """Get raw profile data directly from database for debugging."""
    try:
        # Get client profiles
        client_profiles = await db.fetch("""
            SELECT 
                cp.client_id, cp.user_id, cp.first_name, cp.last_name, cp.phone,
                u.email
            FROM client_profiles cp
            JOIN user_accounts u ON cp.user_id = u.user_id
            ORDER BY cp.client_id
        """)
        
        # Get tutor profiles
        tutor_profiles = await db.fetch("""
            SELECT 
                tp.tutor_id, tp.user_id, tp.first_name, tp.last_name, tp.phone,
                tp.bio, tp.highest_degree_level, tp.years_of_experience,
                u.email
            FROM tutor_profiles tp
            JOIN user_accounts u ON tp.user_id = u.user_id
            ORDER BY tp.tutor_id
        """)
        
        return {
            "client_profiles": [dict(cp) for cp in client_profiles],
            "tutor_profiles": [dict(tp) for tp in tutor_profiles],
            "total_clients": len(client_profiles),
            "total_tutors": len(tutor_profiles)
        }
        
    except Exception as e:
        return {
            "error": str(e),
            "client_profiles": [],
            "tutor_profiles": [],
            "total_clients": 0,
            "total_tutors": 0
        }
