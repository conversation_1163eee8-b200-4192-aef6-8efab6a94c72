"""Global pytest configuration and fixtures."""

import os
import pytest
from typing import AsyncGenerator
from httpx import AsyncClient
from fastapi.testclient import TestClient

# Set test environment variables before importing the app
os.environ["ALLOWED_HOSTS"] = '["localhost", "127.0.0.1", "*.tutoraide.ca", "*.up.railway.app", "tutoraide-production.up.railway.app", "testserver"]'
os.environ["DISABLE_STRICT_SECURITY"] = "true"
os.environ["ENVIRONMENT"] = "test"

# Import after setting env vars
from app.main import app
from app.config.database import get_database_pool, close_database_pool
from app.config.settings import settings, get_settings

# Clear the settings cache to ensure env vars are loaded
get_settings.cache_clear()


@pytest.fixture(scope="session")
def anyio_backend():
    """Use asyncio backend for async tests."""
    return "asyncio"


@pytest.fixture(scope="session")
async def setup_database():
    """Set up test database for the entire test session."""
    pool = await get_database_pool()
    yield pool
    await close_database_pool()


@pytest.fixture
def test_client() -> TestClient:
    """Create a test client with proper headers."""
    client = TestClient(app)
    client.headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "Accept": "application/json",
    }
    return client


@pytest.fixture
async def async_client(test_client: TestClient) -> AsyncGenerator[AsyncClient, None]:
    """Create an async test client."""
    async with AsyncClient(app=app, base_url="http://testserver") as client:
        client.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json",
        }
        yield client