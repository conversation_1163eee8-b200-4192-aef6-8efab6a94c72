import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { X, Save, Package, DollarSign, Clock, Calendar, Users, Book, Layers, RefreshCw } from 'lucide-react';
import { Modal } from '../common/Modal';
import { Input } from '../common/Input';
import { Select } from '../common/Select';
import { Textarea } from '../common/Textarea';
import Button from '../common/Button';
import { Badge } from '../common/Badge';
import { useApi } from '../../hooks/useApi';
import toast from 'react-hot-toast';
import { ServicePackage } from '../../services/serviceApi';

interface PackageModalProps {
  isOpen: boolean;
  onClose: () => void;
  package?: ServicePackage | null;
  onSuccess: () => void;
}

export const PackageModal: React.FC<PackageModalProps> = ({
  isOpen,
  onClose,
  package: existingPackage,
  onSuccess
}) => {
  const { t } = useTranslation();
  const { post, put, loading } = useApi();
  const isEdit = !!existingPackage;

  const [formData, setFormData] = useState({
    package_name: '',
    package_type: 'individual',
    description: '',
    subject_areas: [] as string[],
    service_types: [] as string[],
    service_levels: [] as string[],
    package_price: '',
    deposit_amount: '',
    total_sessions: '',
    session_duration_minutes: '60',
    validity_months: '12',
    is_recurring: false,
    max_participants: '',
    min_participants: '',
    is_active: true
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Available options for multi-selects
  const subjectAreaOptions = [
    'mathematics', 'science', 'french', 'english', 'physics', 
    'chemistry', 'biology', 'history', 'geography', 'computer_science',
    'economics', 'accounting', 'other'
  ];

  const serviceTypeOptions = ['online', 'in_person', 'library', 'hybrid'];
  const serviceLevelOptions = ['elementary', 'high_school', 'college', 'university', 'adult'];

  useEffect(() => {
    if (existingPackage) {
      setFormData({
        package_name: existingPackage.package_name,
        package_type: existingPackage.package_type,
        description: existingPackage.description || '',
        subject_areas: existingPackage.subject_areas || [],
        service_types: existingPackage.service_types || [],
        service_levels: existingPackage.service_levels || [],
        package_price: existingPackage.package_price.toString(),
        deposit_amount: existingPackage.deposit_amount?.toString() || '',
        total_sessions: existingPackage.total_sessions.toString(),
        session_duration_minutes: existingPackage.session_duration_minutes.toString(),
        validity_months: existingPackage.validity_months.toString(),
        is_recurring: existingPackage.is_recurring,
        max_participants: existingPackage.max_participants?.toString() || '',
        min_participants: existingPackage.min_participants?.toString() || '',
        is_active: existingPackage.is_active
      });
    } else {
      // Reset form for new package
      setFormData({
        package_name: '',
        package_type: 'individual',
        description: '',
        subject_areas: [],
        service_types: [],
        service_levels: [],
        package_price: '',
        deposit_amount: '',
        total_sessions: '',
        session_duration_minutes: '60',
        validity_months: '12',
        is_recurring: false,
        max_participants: '',
        min_participants: '',
        is_active: true
      });
    }
    setErrors({});
  }, [existingPackage, isOpen]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Required fields
    if (!formData.package_name.trim()) {
      newErrors.package_name = 'Package name is required';
    }
    if (!formData.package_price || parseFloat(formData.package_price) <= 0) {
      newErrors.package_price = 'Package price must be greater than 0';
    }
    if (!formData.total_sessions || parseInt(formData.total_sessions) <= 0) {
      newErrors.total_sessions = 'Total sessions must be greater than 0';
    } else if (parseInt(formData.total_sessions) > 100) {
      newErrors.total_sessions = 'Total sessions cannot exceed 100';
    }

    // Array validations
    if (formData.subject_areas.length === 0) {
      newErrors.subject_areas = 'At least one subject area is required';
    }
    if (formData.service_types.length === 0) {
      newErrors.service_types = 'At least one service type is required';
    }
    if (formData.service_levels.length === 0) {
      newErrors.service_levels = 'At least one service level is required';
    }

    // Deposit validation
    if (formData.deposit_amount) {
      const deposit = parseFloat(formData.deposit_amount);
      const price = parseFloat(formData.package_price);
      if (deposit < 0) {
        newErrors.deposit_amount = 'Deposit amount cannot be negative';
      } else if (deposit > price) {
        newErrors.deposit_amount = 'Deposit cannot exceed package price';
      }
    }

    // Duration validation
    const duration = parseInt(formData.session_duration_minutes);
    if (duration < 30 || duration > 240) {
      newErrors.session_duration_minutes = 'Duration must be between 30 and 240 minutes';
    }

    // Validity validation
    const validity = parseInt(formData.validity_months);
    if (validity < 1 || validity > 24) {
      newErrors.validity_months = 'Validity must be between 1 and 24 months';
    }

    // Participants validation
    if (formData.min_participants || formData.max_participants) {
      const min = parseInt(formData.min_participants) || 0;
      const max = parseInt(formData.max_participants) || 0;
      
      if (min < 1 || min > 20) {
        newErrors.min_participants = 'Minimum participants must be between 1 and 20';
      }
      if (max < 1 || max > 20) {
        newErrors.max_participants = 'Maximum participants must be between 1 and 20';
      }
      if (min && max && min > max) {
        newErrors.max_participants = 'Maximum must be greater than or equal to minimum';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      const packageData = {
        package_name: formData.package_name.trim(),
        package_type: formData.package_type,
        description: formData.description.trim() || null,
        subject_areas: formData.subject_areas,
        service_types: formData.service_types,
        service_levels: formData.service_levels,
        package_price: parseFloat(formData.package_price),
        deposit_amount: formData.deposit_amount ? parseFloat(formData.deposit_amount) : null,
        total_sessions: parseInt(formData.total_sessions),
        session_duration_minutes: parseInt(formData.session_duration_minutes),
        validity_months: parseInt(formData.validity_months),
        is_recurring: formData.is_recurring,
        max_participants: formData.max_participants ? parseInt(formData.max_participants) : null,
        min_participants: formData.min_participants ? parseInt(formData.min_participants) : null,
        is_active: formData.is_active
      };

      if (isEdit && existingPackage) {
        await put(`/services/packages/${existingPackage.service_package_id}`, packageData);
        toast.success('Package updated successfully');
      } else {
        await post('/services/packages', packageData);
        toast.success('Package created successfully');
      }

      onSuccess();
      onClose();
    } catch (error: any) {
      console.error('Error saving package:', error);
      if (error.response?.data?.detail) {
        toast.error(error.response.data.detail);
      } else {
        toast.error(`Failed to ${isEdit ? 'update' : 'create'} package`);
      }
    }
  };

  const toggleArrayItem = (array: string[], item: string, field: keyof typeof formData) => {
    const currentArray = formData[field] as string[];
    const newArray = currentArray.includes(item)
      ? currentArray.filter(i => i !== item)
      : [...currentArray, item];
    
    setFormData({ ...formData, [field]: newArray });
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={isEdit ? 'Edit Package' : 'Create Service Package'}
      size="xl"
    >
      <form onSubmit={(e) => { e.preventDefault(); handleSubmit(); }} className="space-y-6">
        {/* Basic Information */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
            <Package className="w-5 h-5" />
            Basic Information
          </h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Package Name <span className="text-red-500">*</span>
              </label>
              <Input
                value={formData.package_name}
                onChange={(e) => setFormData({ ...formData, package_name: e.target.value })}
                placeholder="e.g., TECFEE Math Excellence Program"
                error={errors.package_name}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Package Type <span className="text-red-500">*</span>
              </label>
              <Select
                value={formData.package_type}
                onChange={(e) => setFormData({ ...formData, package_type: e.target.value })}
                options={[
                  { value: 'individual', label: 'Individual Sessions' },
                  { value: 'tecfee', label: 'TECFEE Program' },
                  { value: 'bulk_hours', label: 'Bulk Hours' },
                  { value: 'monthly', label: 'Monthly Subscription' }
                ]}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <Textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Describe what this package includes and who it's for..."
                rows={3}
              />
            </div>
          </div>
        </div>

        {/* Service Configuration */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
            <Book className="w-5 h-5" />
            Service Configuration
          </h3>
          
          {/* Subject Areas */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Subject Areas <span className="text-red-500">*</span>
            </label>
            <div className="grid grid-cols-3 gap-2">
              {subjectAreaOptions.map(subject => (
                <label key={subject} className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={formData.subject_areas.includes(subject)}
                    onChange={() => toggleArrayItem(formData.subject_areas, subject, 'subject_areas')}
                    className="rounded border-gray-300 text-accent-red focus:ring-accent-red"
                  />
                  <span className="text-sm capitalize">{subject.replace(/_/g, ' ')}</span>
                </label>
              ))}
            </div>
            {errors.subject_areas && (
              <p className="text-red-500 text-sm mt-1">{errors.subject_areas}</p>
            )}
          </div>

          {/* Service Types */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Service Types <span className="text-red-500">*</span>
            </label>
            <div className="grid grid-cols-2 gap-2">
              {serviceTypeOptions.map(type => (
                <label key={type} className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={formData.service_types.includes(type)}
                    onChange={() => toggleArrayItem(formData.service_types, type, 'service_types')}
                    className="rounded border-gray-300 text-accent-red focus:ring-accent-red"
                  />
                  <span className="text-sm capitalize">{type.replace(/_/g, ' ')}</span>
                </label>
              ))}
            </div>
            {errors.service_types && (
              <p className="text-red-500 text-sm mt-1">{errors.service_types}</p>
            )}
          </div>

          {/* Service Levels */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Service Levels <span className="text-red-500">*</span>
            </label>
            <div className="grid grid-cols-2 gap-2">
              {serviceLevelOptions.map(level => (
                <label key={level} className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={formData.service_levels.includes(level)}
                    onChange={() => toggleArrayItem(formData.service_levels, level, 'service_levels')}
                    className="rounded border-gray-300 text-accent-red focus:ring-accent-red"
                  />
                  <span className="text-sm capitalize">{level.replace(/_/g, ' ')}</span>
                </label>
              ))}
            </div>
            {errors.service_levels && (
              <p className="text-red-500 text-sm mt-1">{errors.service_levels}</p>
            )}
          </div>
        </div>

        {/* Pricing & Sessions */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
            <DollarSign className="w-5 h-5" />
            Pricing & Sessions
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Package Price (CAD) <span className="text-red-500">*</span>
              </label>
              <Input
                type="number"
                value={formData.package_price}
                onChange={(e) => setFormData({ ...formData, package_price: e.target.value })}
                placeholder="0.00"
                min="0"
                step="0.01"
                error={errors.package_price}
                icon={DollarSign}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Deposit Amount (CAD)
              </label>
              <Input
                type="number"
                value={formData.deposit_amount}
                onChange={(e) => setFormData({ ...formData, deposit_amount: e.target.value })}
                placeholder="0.00"
                min="0"
                step="0.01"
                error={errors.deposit_amount}
                icon={DollarSign}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Total Sessions <span className="text-red-500">*</span>
              </label>
              <Input
                type="number"
                value={formData.total_sessions}
                onChange={(e) => setFormData({ ...formData, total_sessions: e.target.value })}
                placeholder="12"
                min="1"
                max="100"
                error={errors.total_sessions}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Session Duration (minutes) <span className="text-red-500">*</span>
              </label>
              <Select
                value={formData.session_duration_minutes}
                onChange={(e) => setFormData({ ...formData, session_duration_minutes: e.target.value })}
                options={[
                  { value: '30', label: '30 minutes' },
                  { value: '45', label: '45 minutes' },
                  { value: '60', label: '60 minutes' },
                  { value: '90', label: '90 minutes' },
                  { value: '120', label: '2 hours' },
                  { value: '180', label: '3 hours' },
                  { value: '240', label: '4 hours' }
                ]}
                error={errors.session_duration_minutes}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Validity Period (months) <span className="text-red-500">*</span>
              </label>
              <Select
                value={formData.validity_months}
                onChange={(e) => setFormData({ ...formData, validity_months: e.target.value })}
                options={[
                  { value: '1', label: '1 month' },
                  { value: '3', label: '3 months' },
                  { value: '6', label: '6 months' },
                  { value: '12', label: '12 months' },
                  { value: '18', label: '18 months' },
                  { value: '24', label: '24 months' }
                ]}
                error={errors.validity_months}
              />
            </div>
          </div>

          {/* Price Summary */}
          {formData.package_price && formData.total_sessions && (
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600">
                Price per session: <span className="font-semibold text-gray-900">
                  ${(parseFloat(formData.package_price) / parseInt(formData.total_sessions)).toFixed(2)} CAD
                </span>
              </p>
            </div>
          )}
        </div>

        {/* Group Settings */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
            <Users className="w-5 h-5" />
            Group Settings
          </h3>
          
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <input
                type="checkbox"
                id="is_recurring"
                checked={formData.is_recurring}
                onChange={(e) => setFormData({ ...formData, is_recurring: e.target.checked })}
                className="rounded border-gray-300 text-accent-red focus:ring-accent-red"
              />
              <label htmlFor="is_recurring" className="text-sm font-medium text-gray-700">
                <RefreshCw className="w-4 h-4 inline mr-1" />
                This is a recurring package
              </label>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Minimum Participants
                </label>
                <Input
                  type="number"
                  value={formData.min_participants}
                  onChange={(e) => setFormData({ ...formData, min_participants: e.target.value })}
                  placeholder="1"
                  min="1"
                  max="20"
                  error={errors.min_participants}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Maximum Participants
                </label>
                <Input
                  type="number"
                  value={formData.max_participants}
                  onChange={(e) => setFormData({ ...formData, max_participants: e.target.value })}
                  placeholder="1"
                  min="1"
                  max="20"
                  error={errors.max_participants}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Active Status */}
        <div className="flex items-center space-x-3 p-4 bg-gray-50 rounded-lg">
          <input
            type="checkbox"
            id="is_active"
            checked={formData.is_active}
            onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
            className="rounded border-gray-300 text-accent-red focus:ring-accent-red"
          />
          <label htmlFor="is_active" className="text-sm font-medium text-gray-700">
            Package is active and available for purchase
          </label>
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-3 pt-4 border-t">
          <Button
            type="button"
            variant="ghost"
            onClick={onClose}
            disabled={loading}
          >
            <X className="w-4 h-4 mr-2" />
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={loading}
            className="bg-accent-red text-white hover:bg-accent-red-dark"
          >
            <Save className="w-4 h-4 mr-2" />
            {loading ? 'Saving...' : isEdit ? 'Update Package' : 'Create Package'}
          </Button>
        </div>
      </form>
    </Modal>
  );
};