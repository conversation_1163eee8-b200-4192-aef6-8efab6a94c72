-- Create auth_tokens table for <PERSON><PERSON><PERSON><PERSON>
-- This script creates the auth_tokens table which is missing from the production database
-- Run this directly on your Railway PostgreSQL database

-- Create the auth_tokens table based on migration 030_simplify_user_tables.sql
CREATE TABLE IF NOT EXISTS auth_tokens (
    token_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    token_type VARCHAR(20) NOT NULL CHECK (token_type IN ('password_reset', 'email_verify', 'session', 'api_key')),
    token_hash VARCHAR(255) NOT NULL UNIQUE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    
    -- Metadata for different token types (JSONB for flexibility)
    metadata JSONB DEFAULT '{}',
    
    -- Usage tracking
    used_at TIMESTAMP WITH TIME ZONE,
    ip_address INET,
    user_agent TEXT,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraint to ensure expiry is after creation
    CHECK (expires_at > created_at)
);

-- <PERSON>reate indexes for performance
CREATE INDEX IF NOT EXISTS idx_auth_tokens_user_id ON auth_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_auth_tokens_type ON auth_tokens(token_type);
CREATE INDEX IF NOT EXISTS idx_auth_tokens_hash ON auth_tokens(token_hash) WHERE used_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_auth_tokens_expires ON auth_tokens(expires_at) WHERE used_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_auth_tokens_composite ON auth_tokens(token_type, user_id, expires_at) WHERE used_at IS NULL;

-- Add table comment for documentation
COMMENT ON TABLE auth_tokens IS 'Unified authentication tokens for sessions, password resets, email verification, etc.';
COMMENT ON COLUMN auth_tokens.metadata IS 'JSON object with token-specific data (session info, reset initiator, etc.)';

-- Verify the table was created successfully
SELECT 
    'auth_tokens table created successfully' as status,
    COUNT(*) as column_count
FROM information_schema.columns 
WHERE table_name = 'auth_tokens';