import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';
import { 
  Download, Printer, Mail, Calendar, TrendingUp, 
  Users, DollarSign, Star, BookOpen 
} from 'lucide-react';

// Mock data for monthly summary
const monthlyData = {
  period: 'June 2024',
  summary: {
    revenue: 67000,
    revenueGrowth: 12.5,
    expenses: 41000,
    newClients: 35,
    newTutors: 6,
    totalSessions: 1027,
    avgRating: 4.8,
    completionRate: 97,
  },
  highlights: [
    'Record month for new client acquisitions',
    'Tutor satisfaction rate reached all-time high',
    'Successfully launched TECFEE summer program',
    'Mobile app usage increased by 25%',
  ],
  challenges: [
    'Tutor recruitment lagging behind demand',
    'Need to improve conversion rate for trial users',
    'Some technical issues with video sessions',
  ],
  topPerformers: [
    { name: '<PERSON>', role: 'Tutor', metric: '100% completion rate' },
    { name: '<PERSON>', role: 'Tutor', metric: 'Highest student satisfaction' },
    { name: '<PERSON>', role: 'Admin', metric: 'Best customer service' },
  ],
  upcomingGoals: [
    'Recruit 10 new qualified tutors',
    'Launch referral program',
    'Improve onboarding completion by 20%',
    'Expand to two new service areas',
  ],
};

const MonthlyReport: React.FC = () => {
  const { t } = useTranslation();
  const [selectedMonth, setSelectedMonth] = useState('2024-06');

  const handleExport = (format: 'pdf' | 'excel') => {
    // Mock export functionality
    console.log(`Exporting monthly report as ${format}`);
  };

  const handlePrint = () => {
    window.print();
  };

  const handleEmail = () => {
    console.log('Emailing monthly report to stakeholders');
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-text-primary">
              {t('sidebar.monthly')} Report
            </h1>
            <p className="text-text-secondary mt-1">
              Comprehensive monthly summary and insights
            </p>
          </div>
          <div className="flex items-center gap-3">
            <input
              type="month"
              value={selectedMonth}
              onChange={(e) => setSelectedMonth(e.target.value)}
              className="px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
            />
            <button
              onClick={() => handleExport('pdf')}
              className="flex items-center gap-2 px-4 py-2 border border-primary-300 rounded-soft hover:bg-background-secondary transition-colors"
            >
              <Download className="w-4 h-4" />
              PDF
            </button>
            <button
              onClick={handlePrint}
              className="flex items-center gap-2 px-4 py-2 border border-primary-300 rounded-soft hover:bg-background-secondary transition-colors"
            >
              <Printer className="w-4 h-4" />
              Print
            </button>
            <button
              onClick={handleEmail}
              className="flex items-center gap-2 px-4 py-2 bg-accent-red text-white rounded-soft hover:bg-accent-red-dark transition-colors"
            >
              <Mail className="w-4 h-4" />
              Email
            </button>
          </div>
        </div>
      </div>

      {/* Executive Summary */}
      <div className="bg-white rounded-large shadow-soft p-8 mb-8">
        <div className="flex items-center gap-3 mb-6">
          <Calendar className="w-6 h-6 text-accent-red" />
          <h2 className="text-xl font-semibold text-text-primary">
            Executive Summary - {monthlyData.period}
          </h2>
        </div>

        {/* Key Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <DollarSign className="w-8 h-8 text-accent-green" />
            </div>
            <h3 className="text-3xl font-bold text-text-primary">
              ${monthlyData.summary.revenue.toLocaleString()}
            </h3>
            <p className="text-sm text-text-secondary">Total Revenue</p>
            <span className="text-xs text-accent-green">
              +{monthlyData.summary.revenueGrowth}% vs last month
            </span>
          </div>

          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Users className="w-8 h-8 text-accent-red" />
            </div>
            <h3 className="text-3xl font-bold text-text-primary">
              {monthlyData.summary.newClients}
            </h3>
            <p className="text-sm text-text-secondary">New Clients</p>
            <span className="text-xs text-accent-green">
              {monthlyData.summary.newTutors} new tutors
            </span>
          </div>

          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <BookOpen className="w-8 h-8 text-accent-orange" />
            </div>
            <h3 className="text-3xl font-bold text-text-primary">
              {monthlyData.summary.totalSessions}
            </h3>
            <p className="text-sm text-text-secondary">Total Sessions</p>
            <span className="text-xs text-accent-green">
              {monthlyData.summary.completionRate}% completion
            </span>
          </div>

          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Star className="w-8 h-8 text-accent-orange" />
            </div>
            <h3 className="text-3xl font-bold text-text-primary">
              {monthlyData.summary.avgRating}
            </h3>
            <p className="text-sm text-text-secondary">Average Rating</p>
            <span className="text-xs text-accent-green">
              Industry leading
            </span>
          </div>
        </div>

        {/* Profit/Loss Summary */}
        <div className="bg-background-secondary rounded-medium p-4 mb-6">
          <div className="flex justify-between items-center">
            <span className="text-sm text-text-secondary">Revenue</span>
            <span className="text-sm font-medium text-accent-green">
              +${monthlyData.summary.revenue.toLocaleString()}
            </span>
          </div>
          <div className="flex justify-between items-center mt-2">
            <span className="text-sm text-text-secondary">Expenses</span>
            <span className="text-sm font-medium text-accent-red">
              -${monthlyData.summary.expenses.toLocaleString()}
            </span>
          </div>
          <div className="border-t border-primary-300 mt-2 pt-2">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-text-primary">Net Profit</span>
              <span className="text-sm font-bold text-accent-green">
                ${(monthlyData.summary.revenue - monthlyData.summary.expenses).toLocaleString()}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Highlights and Challenges */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Highlights */}
        <div className="bg-white rounded-large shadow-soft p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-4 flex items-center gap-2">
            <TrendingUp className="w-5 h-5 text-accent-green" />
            Key Highlights
          </h3>
          <ul className="space-y-3">
            {monthlyData.highlights.map((highlight, index) => (
              <li key={index} className="flex items-start gap-3">
                <span className="w-2 h-2 bg-accent-green rounded-full mt-1.5 flex-shrink-0" />
                <span className="text-sm text-text-secondary">{highlight}</span>
              </li>
            ))}
          </ul>
        </div>

        {/* Challenges */}
        <div className="bg-white rounded-large shadow-soft p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-4">
            Challenges & Areas for Improvement
          </h3>
          <ul className="space-y-3">
            {monthlyData.challenges.map((challenge, index) => (
              <li key={index} className="flex items-start gap-3">
                <span className="w-2 h-2 bg-accent-orange rounded-full mt-1.5 flex-shrink-0" />
                <span className="text-sm text-text-secondary">{challenge}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>

      {/* Top Performers */}
      <div className="bg-white rounded-large shadow-soft p-6 mb-8">
        <h3 className="text-lg font-semibold text-text-primary mb-4">
          Top Performers of the Month
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {monthlyData.topPerformers.map((performer, index) => (
            <div key={index} className="text-center p-4 bg-background-secondary rounded-medium">
              <div className="w-16 h-16 bg-accent-red rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-white text-xl font-bold">
                  {performer.name.split(' ').map(n => n[0]).join('')}
                </span>
              </div>
              <h4 className="font-medium text-text-primary">{performer.name}</h4>
              <p className="text-sm text-text-secondary">{performer.role}</p>
              <p className="text-xs text-accent-red mt-2">{performer.metric}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Goals for Next Month */}
      <div className="bg-white rounded-large shadow-soft p-6">
        <h3 className="text-lg font-semibold text-text-primary mb-4">
          Goals for Next Month
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {monthlyData.upcomingGoals.map((goal, index) => (
            <div key={index} className="flex items-center gap-3 p-3 bg-background-secondary rounded-medium">
              <input type="checkbox" className="w-4 h-4" />
              <span className="text-sm text-text-primary">{goal}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Footer */}
      <div className="mt-8 text-center text-sm text-text-secondary">
        <p>
          Report generated on {format(new Date(), 'MMMM dd, yyyy')} at{' '}
          {format(new Date(), 'HH:mm')}
        </p>
        <p className="mt-1">
          For questions or feedback, contact{' '}
          <a href="mailto:<EMAIL>" className="text-accent-red hover:underline">
            <EMAIL>
          </a>
        </p>
      </div>
    </div>
  );
};

export default MonthlyReport;