from functools import lru_cache
from typing import List, Optional, Annotated
from pydantic import field_validator, ConfigDict, Field
from pydantic_settings import BaseSettings
import secrets
import os
from pathlib import Path


def find_env_file():
    """Find .env file in current or parent directory"""
    # In production (Railway), don't use .env file
    if os.environ.get('RAILWAY_ENVIRONMENT') or os.environ.get('ENVIRONMENT') == 'production':
        return None
    
    # Check if ENV_FILE_PATH is set
    if env_path := os.environ.get('ENV_FILE_PATH'):
        return env_path
    
    # Check current directory
    current_dir = Path.cwd()
    if (current_dir / ".env").exists():
        return ".env"
    
    # Check parent directory
    parent_dir = current_dir.parent
    if (parent_dir / ".env").exists():
        return "../.env"
    
    # Default to parent directory for local dev
    return "../.env"


class Settings(BaseSettings):
    # Dynamic configuration based on environment
    model_config = ConfigDict(
        env_file=find_env_file(),
        case_sensitive=True,
        env_file_encoding="utf-8",
        env_ignore_empty=True,  # Ignore empty env vars
        json_schema_extra={
            "env_parse_none_str": "None",
            "env_nested_delimiter": "__"
        }
    )
    
    # Project info
    PROJECT_NAME: str = "TutorAide API"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"
    DEBUG: bool = False
    
    # Security
    SECRET_KEY: str = secrets.token_urlsafe(32)
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30  # Clients/Tutors
    MANAGER_TOKEN_EXPIRE_MINUTES: int = 180  # Managers
    ALGORITHM: str = "HS256"
    
    # Database
    DATABASE_URL: str
    DATABASE_USER: Optional[str] = "postgres"
    DATABASE_PASSWORD: Optional[str] = None
    DATABASE_HOST: Optional[str] = "localhost"
    DATABASE_PORT: Optional[int] = 5432
    DATABASE_NAME: Optional[str] = "tutoraide"
    DATABASE_POOL_SIZE: int = 10
    DATABASE_MAX_OVERFLOW: int = 20
    DATABASE_POOL_TIMEOUT: int = 30
    
    # Test Database
    TEST_DATABASE_URL: Optional[str] = None
    
    # CORS
    CORS_ORIGINS: Annotated[List[str], Field(json_schema_extra={"env_parse_none_str": None})] = ["http://localhost:3000", "http://localhost:5173", "http://localhost:8000", "https://*.up.railway.app", "https://tutoraide-production.up.railway.app"]
    ALLOWED_HOSTS: Annotated[List[str], Field(json_schema_extra={"env_parse_none_str": None})] = ["localhost", "127.0.0.1", "*.tutoraide.ca", "*.up.railway.app", "tutoraide-production.up.railway.app", "testserver"]
    
    # Email configuration
    SMTP_HOST: Optional[str] = Field(default=None, env="SMTP_HOST")
    SMTP_PORT: int = Field(default=587, env="SMTP_PORT")
    SMTP_USERNAME: Optional[str] = Field(default=None, env="SMTP_USERNAME")
    SMTP_PASSWORD: Optional[str] = Field(default=None, env="SMTP_PASSWORD")
    SMTP_TLS: bool = Field(default=True, env="SMTP_TLS")
    
    # Email addresses
    BILLING_EMAIL: str = "<EMAIL>"
    SUPPORT_EMAIL: str = "<EMAIL>"
    GENERAL_EMAIL: str = "<EMAIL>"
    
    # Google OAuth
    GOOGLE_CLIENT_ID: Optional[str] = None
    GOOGLE_CLIENT_SECRET: Optional[str] = None
    GOOGLE_REDIRECT_URI: Optional[str] = None
    
    # Stripe
    STRIPE_PUBLIC_KEY: Optional[str] = None
    STRIPE_SECRET_KEY: Optional[str] = None
    STRIPE_WEBHOOK_SECRET: Optional[str] = None
    STRIPE_CONNECT_CLIENT_ID: Optional[str] = None
    
    # Frontend URL for redirects
    FRONTEND_URL: str = "http://localhost:3000"
    
    # Environment
    ENVIRONMENT: str = "development"
    
    # Twilio
    TWILIO_ACCOUNT_SID: Optional[str] = None
    TWILIO_AUTH_TOKEN: Optional[str] = None
    TWILIO_PHONE_NUMBER: Optional[str] = None
    
    # OneSignal
    ONESIGNAL_APP_ID: Optional[str] = None
    ONESIGNAL_API_KEY: Optional[str] = None
    
    # Geocoding
    GEOCODING_API_KEY: Optional[str] = None
    
    # Redis
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_PASSWORD: Optional[str] = None
    
    # Rate limiting
    RATE_LIMIT_PER_MINUTE: int = 100
    
    # File upload
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    UPLOAD_FOLDER: str = "uploads"
    
    # Sentry
    SENTRY_DSN: Optional[str] = "https://<EMAIL>/****************"
    SENTRY_TRACES_SAMPLE_RATE: float = Field(default=1.0 if os.environ.get("ENVIRONMENT") != "production" else 0.1)
    SENTRY_PROFILES_SAMPLE_RATE: float = Field(default=1.0 if os.environ.get("ENVIRONMENT") != "production" else 0.01)
    
    @field_validator("CORS_ORIGINS", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v):
        if isinstance(v, str) and v:
            # Handle comma-separated string
            return [origin.strip() for origin in v.split(",")]
        elif isinstance(v, list):
            return v
        return ["http://localhost:3000", "http://localhost:8000", "https://*.up.railway.app", "https://tutoraide-production.up.railway.app"]
    
    @field_validator("ALLOWED_HOSTS", mode="before")
    @classmethod
    def assemble_allowed_hosts(cls, v):
        if isinstance(v, str) and v:
            # Handle comma-separated string
            return [host.strip() for host in v.split(",")]
        elif isinstance(v, list):
            return v
        return ["localhost", "127.0.0.1", "*.tutoraide.ca", "*.up.railway.app", "tutoraide-production.up.railway.app", "testserver"]


@lru_cache()
def get_settings() -> Settings:
    # Create settings instance
    settings_instance = Settings()
    
    # Log environment info in production
    if os.environ.get('RAILWAY_ENVIRONMENT') or os.environ.get('ENVIRONMENT') == 'production':
        import logging
        logger = logging.getLogger(__name__)
        logger.info("=== Production Environment Configuration ===")
        logger.info(f"RAILWAY_ENVIRONMENT: {os.environ.get('RAILWAY_ENVIRONMENT', 'Not set')}")
        logger.info(f"ENVIRONMENT: {os.environ.get('ENVIRONMENT', 'Not set')}")
        logger.info(f"SMTP_HOST from env: {os.environ.get('SMTP_HOST', 'Not set')}")
        logger.info(f"SMTP_PORT from env: {os.environ.get('SMTP_PORT', 'Not set')}")
        logger.info(f"SMTP_USERNAME from env: {os.environ.get('SMTP_USERNAME', 'Not set')}")
        logger.info(f"Loaded SMTP_HOST: {settings_instance.SMTP_HOST}")
        logger.info(f"Loaded SMTP_PORT: {settings_instance.SMTP_PORT}")
        logger.info(f"Loaded SMTP_USERNAME: {settings_instance.SMTP_USERNAME}")
        logger.info("==========================================")
    
    return settings_instance


def reload_settings() -> Settings:
    """Force reload settings, clearing the cache."""
    get_settings.cache_clear()
    return get_settings()


settings = get_settings()