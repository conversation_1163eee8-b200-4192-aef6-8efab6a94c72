"""
Sentry middleware for enhanced error tracking and user context.
"""

from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
import sentry_sdk
from sentry_sdk import set_user, set_tag, set_context


class SentryMiddleware(BaseHTTPMiddleware):
    """
    Middleware to enhance Sentry error tracking with request context.
    """
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Add request context to Sentry for better error tracking."""
        
        # Set request-specific tags
        set_tag("request.method", request.method)
        set_tag("request.path", request.url.path)
        set_tag("request.host", request.headers.get("host", "unknown"))
        
        # Set request context
        set_context("request", {
            "url": str(request.url),
            "method": request.method,
            "headers": dict(request.headers),
            "query_params": dict(request.query_params),
            "client_host": request.client.host if request.client else None,
        })
        
        # Set user context if user is authenticated
        if hasattr(request.state, "user") and request.state.user:
            user = request.state.user
            set_user({
                "id": str(user.get("user_id", "")),
                "email": user.get("email", ""),
                "username": user.get("username", ""),
                "role": user.get("role", ""),
            })
        
        # Continue processing the request
        try:
            response = await call_next(request)
            
            # Add response status to tags
            set_tag("response.status_code", response.status_code)
            
            return response
            
        except Exception as e:
            # Sentry will automatically capture this exception
            # with all the context we've set
            raise e