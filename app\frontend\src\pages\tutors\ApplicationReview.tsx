import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, User, GraduationCap, Briefcase, MapPin, Calendar, Star, Check, X, Clock } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import api from '../../services/api';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import Button from '../../components/common/Button';

interface TutorApplication {
  invitation_id: number;
  email: string;
  first_name: string;
  last_name: string;
  phone: string;
  application_submitted_at: string;
  review_status: string;
  parsed_education: {
    current_level: string;
    institution: string;
    program: string;
    gpa?: string;
    highest_degree_level?: string;
    highest_degree_name?: string;
    degree_major?: string;
    university?: string;
    graduation_year?: number;
    certifications?: string[];
  };
  parsed_experience: {
    has_experience: string;
    experience_details?: string;
    strengths: string;
    motivation: string;
    years_teaching_total?: number;
    years_teaching_online?: number;
    years_tutoring?: number;
    current_occupation?: string;
    languages_spoken?: Record<string, number>;
    teaching_methodologies?: string[];
    special_needs_experience?: boolean;
    age_groups_comfortable?: string[];
  };
  parsed_preferences: {
    subjects: string[];
    levels: string[];
    preferred_location: string;
    transport_method: string;
    availability: Record<string, Record<string, boolean>>;
  };
  parsed_references?: {
    text?: string;
    contacts?: Array<{
      name: string;
      relationship: string;
      email: string;
      phone: string;
    }>;
  };
}

const ApplicationReview: React.FC = () => {
  const { t } = useTranslation();
  const { applicationId } = useParams<{ applicationId: string }>();
  const navigate = useNavigate();
  
  const [application, setApplication] = useState<TutorApplication | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [rejectReason, setRejectReason] = useState('');
  const [rejectNotes, setRejectNotes] = useState('');
  const [canReapplyDate, setCanReapplyDate] = useState('');

  useEffect(() => {
    fetchApplication();
  }, [applicationId]);

  const fetchApplication = async () => {
    try {
      const response = await api.get(`/tutor-applications/${applicationId}`);
      setApplication(response.data);
    } catch (error) {
      console.error('Error fetching application:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async () => {
    setActionLoading(true);
    try {
      await api.post(`/tutor-applications/${applicationId}/review`, {
        action: 'approve',
        notes: 'Application meets all requirements'
      });
      alert(t('tutors.application.approved'));
      navigate('/tutors/applications');
    } catch (error) {
      console.error('Error approving application:', error);
      alert(t('common.error'));
    } finally {
      setActionLoading(false);
    }
  };

  const handleReject = async () => {
    if (!rejectReason) {
      alert(t('tutors.application.selectRejectionReason'));
      return;
    }
    
    setActionLoading(true);
    try {
      await api.post(`/tutor-applications/${applicationId}/review`, {
        action: 'reject',
        rejection_reason: rejectReason,
        notes: rejectNotes,
        can_reapply_after: canReapplyDate || undefined
      });
      alert(t('tutors.application.rejected'));
      navigate('/tutors/applications');
    } catch (error) {
      console.error('Error rejecting application:', error);
      alert(t('common.error'));
    } finally {
      setActionLoading(false);
    }
  };

  const handleWaitlist = async () => {
    setActionLoading(true);
    try {
      await api.post(`/tutor-applications/${applicationId}/review`, {
        action: 'waitlist',
        notes: 'Qualified candidate - waiting for suitable position'
      });
      alert(t('tutors.application.waitlisted'));
      navigate('/tutors/applications');
    } catch (error) {
      console.error('Error waitlisting application:', error);
      alert(t('common.error'));
    } finally {
      setActionLoading(false);
    }
  };

  if (loading) return <LoadingSpinner />;
  if (!application) return <div>Application not found</div>;

  const { parsed_education, parsed_experience, parsed_preferences, parsed_references } = application;

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <button
            onClick={() => navigate('/tutors/applications')}
            className="p-2 hover:bg-gray-100 rounded-lg"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>
          <div>
            <h1 className="text-2xl font-bold">
              {application.first_name} {application.last_name}
            </h1>
            <p className="text-gray-600">{application.email} • {application.phone}</p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <span className="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm">
            {application.review_status}
          </span>
        </div>
      </div>

      {/* Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Education Section */}
          <div className="bg-white rounded-xl shadow-sm p-6">
            <div className="flex items-center gap-3 mb-4">
              <GraduationCap className="w-5 h-5 text-primary" />
              <h2 className="text-lg font-semibold">{t('tutors.education')}</h2>
            </div>
            <div className="space-y-3">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-600">{t('tutors.currentLevel')}</p>
                  <p className="font-medium">{parsed_education.current_level}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">{t('tutors.institution')}</p>
                  <p className="font-medium">{parsed_education.institution}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">{t('tutors.program')}</p>
                  <p className="font-medium">{parsed_education.program}</p>
                </div>
                {parsed_education.gpa && (
                  <div>
                    <p className="text-sm text-gray-600">{t('tutors.gpa')}</p>
                    <p className="font-medium">{parsed_education.gpa}</p>
                  </div>
                )}
              </div>
              
              {parsed_education.certifications && parsed_education.certifications.length > 0 && (
                <div>
                  <p className="text-sm text-gray-600 mb-2">{t('tutors.certifications')}</p>
                  <div className="flex flex-wrap gap-2">
                    {parsed_education.certifications.map((cert, index) => (
                      <span key={index} className="px-3 py-1 bg-primary-light/30 text-primary rounded-full text-sm">
                        {cert}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Experience Section */}
          <div className="bg-white rounded-xl shadow-sm p-6">
            <div className="flex items-center gap-3 mb-4">
              <Briefcase className="w-5 h-5 text-primary" />
              <h2 className="text-lg font-semibold">{t('tutors.experience')}</h2>
            </div>
            <div className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <p className="text-2xl font-bold text-primary">{parsed_experience.years_teaching_total || 0}</p>
                  <p className="text-sm text-gray-600">{t('tutors.yearsTeaching')}</p>
                </div>
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <p className="text-2xl font-bold text-primary">{parsed_experience.years_teaching_online || 0}</p>
                  <p className="text-sm text-gray-600">{t('tutors.yearsOnline')}</p>
                </div>
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <p className="text-2xl font-bold text-primary">{parsed_experience.years_tutoring || 0}</p>
                  <p className="text-sm text-gray-600">{t('tutors.yearsTutoring')}</p>
                </div>
              </div>

              {parsed_experience.current_occupation && (
                <div>
                  <p className="text-sm text-gray-600">{t('tutors.currentOccupation')}</p>
                  <p className="font-medium">{parsed_experience.current_occupation}</p>
                </div>
              )}

              {parsed_experience.experience_details && (
                <div>
                  <p className="text-sm text-gray-600 mb-1">{t('tutors.experienceDetails')}</p>
                  <p className="text-gray-800">{parsed_experience.experience_details}</p>
                </div>
              )}

              <div>
                <p className="text-sm text-gray-600 mb-1">{t('tutors.strengths')}</p>
                <p className="text-gray-800">{parsed_experience.strengths}</p>
              </div>

              <div>
                <p className="text-sm text-gray-600 mb-1">{t('tutors.motivation')}</p>
                <p className="text-gray-800">{parsed_experience.motivation}</p>
              </div>

              {parsed_experience.languages_spoken && (
                <div>
                  <p className="text-sm text-gray-600 mb-2">{t('tutors.languages')}</p>
                  <div className="flex gap-4">
                    {Object.entries(parsed_experience.languages_spoken).map(([lang, level]) => (
                      <div key={lang} className="flex items-center gap-1">
                        <span className="font-medium capitalize">{lang}:</span>
                        <div className="flex">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`w-4 h-4 ${i < level ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
                            />
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* References Section */}
          {parsed_references && (
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h2 className="text-lg font-semibold mb-4">{t('tutors.references')}</h2>
              {parsed_references.text && (
                <p className="text-gray-800 mb-4">{parsed_references.text}</p>
              )}
              {parsed_references.contacts && parsed_references.contacts.length > 0 && (
                <div className="space-y-3">
                  {parsed_references.contacts.map((ref, index) => (
                    <div key={index} className="p-3 bg-gray-50 rounded-lg">
                      <p className="font-medium">{ref.name}</p>
                      <p className="text-sm text-gray-600">{ref.relationship}</p>
                      <p className="text-sm text-gray-600">{ref.email} • {ref.phone}</p>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Teaching Preferences */}
          <div className="bg-white rounded-xl shadow-sm p-6">
            <h3 className="font-semibold mb-4">{t('tutors.teachingPreferences')}</h3>
            
            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-600 mb-2">{t('tutors.subjects')}</p>
                <div className="flex flex-wrap gap-2">
                  {parsed_preferences.subjects.map((subject, index) => (
                    <span key={index} className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                      {subject}
                    </span>
                  ))}
                </div>
              </div>

              <div>
                <p className="text-sm text-gray-600 mb-2">{t('tutors.levels')}</p>
                <div className="flex flex-wrap gap-2">
                  {parsed_preferences.levels.map((level, index) => (
                    <span key={index} className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                      {level}
                    </span>
                  ))}
                </div>
              </div>

              <div className="flex items-center gap-2">
                <MapPin className="w-4 h-4 text-gray-600" />
                <div>
                  <p className="text-sm text-gray-600">{t('tutors.preferredArea')}</p>
                  <p className="font-medium">{parsed_preferences.preferred_location}</p>
                </div>
              </div>

              <div>
                <p className="text-sm text-gray-600">{t('tutors.transport')}</p>
                <p className="font-medium capitalize">{parsed_preferences.transport_method}</p>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="bg-white rounded-xl shadow-sm p-6">
            <h3 className="font-semibold mb-4">{t('tutors.reviewActions')}</h3>
            <div className="space-y-3">
              <Button
                onClick={handleApprove}
                className="w-full"
                disabled={actionLoading}
              >
                <Check className="w-4 h-4 mr-2" />
                {t('tutors.approve')}
              </Button>
              
              <Button
                variant="secondary"
                onClick={handleWaitlist}
                className="w-full"
                disabled={actionLoading}
              >
                <Clock className="w-4 h-4 mr-2" />
                {t('tutors.waitlist')}
              </Button>
              
              <Button
                variant="danger"
                onClick={() => setShowRejectModal(true)}
                className="w-full"
                disabled={actionLoading}
              >
                <X className="w-4 h-4 mr-2" />
                {t('tutors.reject')}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Reject Modal */}
      {showRejectModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md w-full">
            <h3 className="text-lg font-semibold mb-4">{t('tutors.rejectApplication')}</h3>
            
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-700">{t('tutors.rejectionReason')}</label>
                <select
                  value={rejectReason}
                  onChange={(e) => setRejectReason(e.target.value)}
                  className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary mt-1"
                >
                  <option value="">{t('common.select')}</option>
                  <option value="insufficient_education">{t('tutors.rejectionReasons.education')}</option>
                  <option value="insufficient_experience">{t('tutors.rejectionReasons.experience')}</option>
                  <option value="no_relevant_subjects">{t('tutors.rejectionReasons.subjects')}</option>
                  <option value="failed_reference_check">{t('tutors.rejectionReasons.references')}</option>
                  <option value="geographic_limitation">{t('tutors.rejectionReasons.location')}</option>
                  <option value="other">{t('tutors.rejectionReasons.other')}</option>
                </select>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700">{t('tutors.additionalNotes')}</label>
                <textarea
                  value={rejectNotes}
                  onChange={(e) => setRejectNotes(e.target.value)}
                  className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary mt-1"
                  rows={3}
                  placeholder={t('tutors.notesPlaceholder')}
                />
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700">{t('tutors.canReapplyAfter')}</label>
                <input
                  type="date"
                  value={canReapplyDate}
                  onChange={(e) => setCanReapplyDate(e.target.value)}
                  className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary mt-1"
                  min={new Date().toISOString().split('T')[0]}
                />
              </div>
            </div>

            <div className="flex gap-3 mt-6">
              <Button
                variant="danger"
                onClick={handleReject}
                disabled={actionLoading || !rejectReason}
                className="flex-1"
              >
                {t('tutors.confirmReject')}
              </Button>
              <Button
                variant="secondary"
                onClick={() => setShowRejectModal(false)}
                disabled={actionLoading}
                className="flex-1"
              >
                {t('common.cancel')}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ApplicationReview;