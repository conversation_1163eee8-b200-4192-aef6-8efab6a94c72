import React from 'react';
import { clsx } from 'clsx';
import { Upload, File, X, Image, FileText } from 'lucide-react';

interface FileUploadProps {
  value?: File | File[];
  onChange: (files: File | File[] | null) => void;
  accept?: string;
  multiple?: boolean;
  maxSize?: number; // in bytes
  maxFiles?: number;
  label?: string;
  hint?: string;
  error?: string;
  disabled?: boolean;
  required?: boolean;
  className?: string;
}

export const FileUpload: React.FC<FileUploadProps> = ({
  value,
  onChange,
  accept,
  multiple = false,
  maxSize = 10 * 1024 * 1024, // 10MB default
  maxFiles = 10,
  label,
  hint,
  error,
  disabled = false,
  required = false,
  className,
}) => {
  const [isDragging, setIsDragging] = React.useState(false);
  const fileInputRef = React.useRef<HTMLInputElement>(null);
  const id = React.useId();

  const files = React.useMemo(() => {
    if (!value) return [];
    return Array.isArray(value) ? value : [value];
  }, [value]);

  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    if (disabled) return;

    const droppedFiles = Array.from(e.dataTransfer.files);
    handleFiles(droppedFiles);
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const selectedFiles = Array.from(e.target.files);
      handleFiles(selectedFiles);
    }
  };

  const handleFiles = (newFiles: File[]) => {
    let validFiles = newFiles;

    // Check file size
    validFiles = validFiles.filter(file => {
      if (file.size > maxSize) {
        console.error(`File ${file.name} exceeds maximum size of ${formatFileSize(maxSize)}`);
        return false;
      }
      return true;
    });

    if (multiple) {
      const currentCount = files.length;
      const availableSlots = maxFiles - currentCount;
      validFiles = validFiles.slice(0, availableSlots);
      
      if (validFiles.length > 0) {
        onChange([...files, ...validFiles]);
      }
    } else {
      if (validFiles.length > 0) {
        onChange(validFiles[0]);
      }
    }
  };

  const removeFile = (index: number) => {
    if (multiple) {
      const newFiles = files.filter((_, i) => i !== index);
      onChange(newFiles.length > 0 ? newFiles : null);
    } else {
      onChange(null);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) {
      return <Image className="w-5 h-5" />;
    }
    if (file.type.includes('pdf') || file.type.includes('document')) {
      return <FileText className="w-5 h-5" />;
    }
    return <File className="w-5 h-5" />;
  };

  return (
    <div className={className}>
      {label && (
        <label
          htmlFor={id}
          className="block text-sm font-medium text-text-primary mb-2"
        >
          {label}
          {required && <span className="ml-1 text-accent-red">*</span>}
        </label>
      )}

      <div
        className={clsx(
          'relative border-2 border-dashed rounded-lg transition-all',
          isDragging
            ? 'border-accent-red bg-red-50'
            : error
            ? 'border-semantic-error bg-red-50'
            : 'border-border-secondary bg-background-secondary hover:border-accent-red hover:bg-red-50',
          disabled && 'opacity-60 cursor-not-allowed'
        )}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          id={id}
          type="file"
          accept={accept}
          multiple={multiple}
          onChange={handleFileSelect}
          disabled={disabled}
          required={required && files.length === 0}
          className="hidden"
        />

        <button
          type="button"
          onClick={() => fileInputRef.current?.click()}
          disabled={disabled}
          className="w-full p-8 text-center"
        >
          <Upload className="w-10 h-10 mx-auto mb-4 text-text-muted" />
          <p className="text-sm font-medium text-text-primary mb-1">
            Click to upload or drag and drop
          </p>
          <p className="text-xs text-text-muted">
            {accept ? `Accepted formats: ${accept}` : 'Any file type'} • Max size: {formatFileSize(maxSize)}
          </p>
        </button>
      </div>

      {files.length > 0 && (
        <div className="mt-4 space-y-2">
          {files.map((file, index) => (
            <div
              key={`${file.name}-${index}`}
              className="flex items-center gap-3 p-3 bg-white border border-border-primary rounded-lg"
            >
              <span className="text-text-muted">{getFileIcon(file)}</span>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-text-primary truncate">
                  {file.name}
                </p>
                <p className="text-xs text-text-muted">
                  {formatFileSize(file.size)}
                </p>
              </div>
              {!disabled && (
                <button
                  type="button"
                  onClick={() => removeFile(index)}
                  className="p-1 text-text-muted hover:text-semantic-error transition-colors"
                >
                  <X className="w-4 h-4" />
                </button>
              )}
            </div>
          ))}
        </div>
      )}

      {error && (
        <p className="mt-2 text-sm text-semantic-error">
          {error}
        </p>
      )}

      {hint && !error && (
        <p className="mt-2 text-sm text-text-muted">
          {hint}
        </p>
      )}
    </div>
  );
};

export default FileUpload;