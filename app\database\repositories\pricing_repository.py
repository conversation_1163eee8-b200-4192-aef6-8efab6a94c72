"""
Repository for pricing and commission management.
"""

import logging
from datetime import date, datetime
from decimal import Decimal
from typing import List, Optional, Dict, Any

from app.database.repositories.base import BaseRepository
from app.models.pricing_models import (
    PricingRule, ServicePricing, CommissionStructure,
    CommissionTier, PricingSearchFilters
)

logger = logging.getLogger(__name__)


class PricingRepository(BaseRepository):
    """Repository for pricing operations."""
    
    async def create_pricing_rule(
        self,
        rule_name: str,
        rule_code: str,
        service_type: str,
        base_client_rate: Decimal,
        base_tutor_rate: Decimal,
        commission_percentage: Decimal,
        commission_amount: Decimal,
        created_by: int,
        description: Optional[str] = None,
        subject_area: Optional[str] = None,
        location_type: Optional[str] = None,
        priority: int = 100,
        effective_date: Optional[date] = None,
        expiry_date: Optional[date] = None
    ) -> PricingRule:
        """Create a new pricing rule."""
        try:
            query = """
                INSERT INTO pricing_rules (
                    rule_name, rule_code, description, service_type,
                    subject_area, location_type, base_client_rate, base_tutor_rate,
                    commission_percentage, commission_amount, priority, is_active,
                    effective_date, expiry_date, created_by
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                ) RETURNING *
            """
            
            params = (
                rule_name, rule_code, description, service_type,
                subject_area, location_type, base_client_rate, base_tutor_rate,
                commission_percentage, commission_amount, priority, True,
                effective_date or date.today(), expiry_date, created_by
            )
            
            result = await self._fetchone(query, params)
            return PricingRule(**result)
            
        except Exception as e:
            logger.error(f"Error creating pricing rule: {e}")
            raise
    
    async def get_pricing_rule_by_id(self, rule_id: int) -> Optional[PricingRule]:
        """Get pricing rule by ID."""
        try:
            query = """
                SELECT * FROM pricing_rules
                WHERE rule_id = %s AND deleted_at IS NULL
            """
            
            result = await self._fetchone(query, (rule_id,))
            return PricingRule(**result) if result else None
            
        except Exception as e:
            logger.error(f"Error getting pricing rule: {e}")
            raise
    
    async def get_pricing_rules(
        self,
        filters: Optional[PricingSearchFilters] = None
    ) -> List[PricingRule]:
        """Get pricing rules with optional filters."""
        try:
            query = """
                SELECT * FROM pricing_rules
                WHERE deleted_at IS NULL
            """
            params = []
            
            if filters:
                if filters.service_type:
                    query += " AND service_type = %s"
                    params.append(filters.service_type)
                
                if filters.subject_area:
                    query += " AND subject_area = %s"
                    params.append(filters.subject_area)
                
                if filters.location_type:
                    query += " AND location_type = %s"
                    params.append(filters.location_type)
                
                if filters.is_active is not None:
                    query += " AND is_active = %s"
                    params.append(filters.is_active)
                
                if filters.effective_date:
                    query += " AND effective_date <= %s"
                    params.append(filters.effective_date)
                    query += " AND (expiry_date IS NULL OR expiry_date >= %s)"
                    params.append(filters.effective_date)
            
            query += " ORDER BY priority DESC, created_at DESC"
            
            if filters and filters.limit:
                query += f" LIMIT {filters.limit}"
                if filters.offset:
                    query += f" OFFSET {filters.offset}"
            
            results = await self._fetchall(query, params)
            return [PricingRule(**row) for row in results]
            
        except Exception as e:
            logger.error(f"Error getting pricing rules: {e}")
            raise
    
    async def get_applicable_pricing_rule(
        self,
        service_type: str,
        subject_area: Optional[str] = None,
        location_type: Optional[str] = None,
        as_of_date: Optional[date] = None
    ) -> Optional[PricingRule]:
        """Get the most applicable pricing rule based on criteria."""
        try:
            effective_date = as_of_date or date.today()
            
            query = """
                SELECT * FROM pricing_rules
                WHERE deleted_at IS NULL
                AND is_active = true
                AND service_type = %s
                AND effective_date <= %s
                AND (expiry_date IS NULL OR expiry_date >= %s)
            """
            params = [service_type, effective_date, effective_date]
            
            # Subject-specific rules have higher priority
            if subject_area:
                query += " AND (subject_area = %s OR subject_area IS NULL)"
                params.append(subject_area)
            else:
                query += " AND subject_area IS NULL"
            
            # Location-specific rules for in-person services
            if service_type == 'in-person' and location_type:
                query += " AND (location_type = %s OR location_type IS NULL)"
                params.append(location_type)
            
            # Order by specificity and priority
            query += """
                ORDER BY 
                    CASE WHEN subject_area IS NOT NULL THEN 1 ELSE 2 END,
                    CASE WHEN location_type IS NOT NULL THEN 1 ELSE 2 END,
                    priority DESC,
                    effective_date DESC
                LIMIT 1
            """
            
            result = await self._fetchone(query, params)
            return PricingRule(**result) if result else None
            
        except Exception as e:
            logger.error(f"Error getting applicable pricing rule: {e}")
            raise
    
    async def get_tutor_custom_rate(
        self,
        tutor_id: int,
        service_type: str,
        subject_area: Optional[str] = None,
        as_of_date: Optional[date] = None
    ) -> Optional[ServicePricing]:
        """Get custom rate for a specific tutor."""
        try:
            effective_date = as_of_date or date.today()
            
            query = """
                SELECT 
                    rate_id AS pricing_id,
                    tutor_id,
                    service_type,
                    subject_area,
                    custom_rate AS custom_tutor_rate,
                    rate_type,
                    commission_override_percentage,
                    is_active,
                    effective_date,
                    expiry_date,
                    approved_by,
                    notes,
                    created_at,
                    updated_at
                FROM tutor_service_rates
                WHERE tutor_id = %s
                AND service_type = %s
                AND is_active = true
                AND effective_date <= %s
                AND (expiry_date IS NULL OR expiry_date >= %s)
            """
            params = [tutor_id, service_type, effective_date, effective_date]
            
            if subject_area:
                query += " AND (subject_area = %s OR subject_area IS NULL)"
                params.append(subject_area)
            else:
                query += " AND subject_area IS NULL"
            
            query += " ORDER BY subject_area NULLS LAST, effective_date DESC LIMIT 1"
            
            result = await self._fetchone(query, params)
            return ServicePricing(**result) if result else None
            
        except Exception as e:
            logger.error(f"Error getting tutor custom rate: {e}")
            raise
    
    async def get_client_negotiated_rate(
        self,
        client_id: int,
        service_type: str,
        subject_area: Optional[str] = None,
        as_of_date: Optional[date] = None
    ) -> Optional[ServicePricing]:
        """Get negotiated rate for a specific client."""
        try:
            effective_date = as_of_date or date.today()
            
            query = """
                SELECT 
                    rate_id AS pricing_id,
                    client_id,
                    service_type,
                    subject_area,
                    negotiated_rate AS custom_client_rate,
                    'client_rate' AS rate_type,
                    NULL AS commission_override_percentage,
                    is_active,
                    effective_date,
                    expiry_date,
                    approved_by,
                    notes,
                    created_at,
                    updated_at
                FROM client_service_rates
                WHERE client_id = %s
                AND service_type = %s
                AND is_active = true
                AND effective_date <= %s
                AND (expiry_date IS NULL OR expiry_date >= %s)
            """
            params = [client_id, service_type, effective_date, effective_date]
            
            if subject_area:
                query += " AND (subject_area = %s OR subject_area IS NULL)"
                params.append(subject_area)
            else:
                query += " AND subject_area IS NULL"
            
            query += " ORDER BY subject_area NULLS LAST, effective_date DESC LIMIT 1"
            
            result = await self._fetchone(query, params)
            return ServicePricing(**result) if result else None
            
        except Exception as e:
            logger.error(f"Error getting client negotiated rate: {e}")
            raise
    
    async def get_surge_multiplier(
        self,
        service_type: Optional[str] = None,
        session_date: Optional[date] = None,
        session_time: Optional[str] = None
    ) -> Decimal:
        """Get surge pricing multiplier for a specific time."""
        try:
            if not session_date:
                return Decimal("1.00")
            
            # Get day of week (0=Monday in Python, 0=Sunday in DB)
            day_of_week = (session_date.weekday() + 1) % 7
            
            query = """
                SELECT multiplier FROM surge_pricing_rules
                WHERE is_active = true
                AND (
                    -- Specific date rules
                    (date_specific = %s)
                    OR
                    -- Day of week rules
                    (date_specific IS NULL AND day_of_week = %s)
                )
            """
            params = [session_date, day_of_week]
            
            if service_type:
                query += " AND (service_type = %s OR service_type IS NULL)"
                params.append(service_type)
            
            if session_time:
                query += """
                    AND (
                        (time_start IS NULL AND time_end IS NULL)
                        OR
                        (time_start <= %s::time AND time_end >= %s::time)
                    )
                """
                params.extend([session_time, session_time])
            
            query += " ORDER BY date_specific NULLS LAST, multiplier DESC LIMIT 1"
            
            result = await self._fetchone(query, params)
            return Decimal(str(result['multiplier'])) if result else Decimal("1.00")
            
        except Exception as e:
            logger.error(f"Error getting surge multiplier: {e}")
            raise
    
    async def create_revenue_tracking(
        self,
        appointment_id: int,
        client_amount: Decimal,
        tutor_amount: Decimal,
        platform_commission: Decimal,
        commission_percentage: Decimal,
        pricing_rule_id: Optional[int] = None,
        surge_applied: bool = False,
        surge_multiplier: Decimal = Decimal("1.00")
    ) -> Dict[str, Any]:
        """Create revenue tracking entry."""
        try:
            query = """
                INSERT INTO platform_revenue_tracking (
                    appointment_id, client_amount, tutor_amount,
                    platform_commission, commission_percentage,
                    pricing_rule_id, surge_applied, surge_multiplier,
                    tracked_date
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s
                ) RETURNING *
            """
            
            params = (
                appointment_id, client_amount, tutor_amount,
                platform_commission, commission_percentage,
                pricing_rule_id, surge_applied, surge_multiplier,
                date.today()
            )
            
            result = await self._fetchone(query, params)
            return dict(result)
            
        except Exception as e:
            logger.error(f"Error creating revenue tracking: {e}")
            raise
    
    async def get_platform_revenue(
        self,
        period_start: date,
        period_end: date
    ) -> Dict[str, Any]:
        """Get platform revenue summary for a period."""
        try:
            # Main revenue summary
            summary_query = """
                SELECT 
                    SUM(client_amount) AS gross_revenue,
                    SUM(tutor_amount) AS tutor_payments,
                    SUM(platform_commission) AS platform_commission,
                    AVG(commission_percentage) AS avg_commission_rate,
                    COUNT(DISTINCT appointment_id) AS session_count,
                    SUM(client_amount - tutor_amount) AS net_revenue
                FROM platform_revenue_tracking
                WHERE tracked_date BETWEEN %s AND %s
            """
            
            summary = await self._fetchone(summary_query, (period_start, period_end))
            
            # Revenue by service type
            service_query = """
                SELECT 
                    a.service_type,
                    SUM(prt.client_amount) AS revenue,
                    SUM(prt.platform_commission) AS commission,
                    COUNT(DISTINCT prt.appointment_id) AS sessions
                FROM platform_revenue_tracking prt
                JOIN appointments a ON a.appointment_id = prt.appointment_id
                WHERE prt.tracked_date BETWEEN %s AND %s
                GROUP BY a.service_type
            """
            
            service_results = await self._fetchall(service_query, (period_start, period_end))
            revenue_by_service = {
                row['service_type']: {
                    'revenue': row['revenue'],
                    'commission': row['commission'],
                    'sessions': row['sessions']
                }
                for row in service_results
            }
            
            # Revenue by subject
            subject_query = """
                SELECT 
                    a.subject_area,
                    SUM(prt.client_amount) AS revenue,
                    SUM(prt.platform_commission) AS commission,
                    COUNT(DISTINCT prt.appointment_id) AS sessions
                FROM platform_revenue_tracking prt
                JOIN appointments a ON a.appointment_id = prt.appointment_id
                WHERE prt.tracked_date BETWEEN %s AND %s
                GROUP BY a.subject_area
            """
            
            subject_results = await self._fetchall(subject_query, (period_start, period_end))
            revenue_by_subject = {
                row['subject_area']: {
                    'revenue': row['revenue'],
                    'commission': row['commission'],
                    'sessions': row['sessions']
                }
                for row in subject_results
            }
            
            # Top performing tutors
            tutor_query = """
                SELECT 
                    a.tutor_id,
                    u.first_name || ' ' || u.last_name AS tutor_name,
                    SUM(prt.client_amount) AS revenue_generated,
                    SUM(prt.platform_commission) AS commission_earned,
                    COUNT(DISTINCT prt.appointment_id) AS sessions
                FROM platform_revenue_tracking prt
                JOIN appointments a ON a.appointment_id = prt.appointment_id
                JOIN tutors t ON t.tutor_id = a.tutor_id
                JOIN users u ON u.user_id = t.user_id
                WHERE prt.tracked_date BETWEEN %s AND %s
                GROUP BY a.tutor_id, u.first_name, u.last_name
                ORDER BY commission_earned DESC
                LIMIT 10
            """
            
            tutor_results = await self._fetchall(tutor_query, (period_start, period_end))
            top_tutors = [
                {
                    'tutor_id': row['tutor_id'],
                    'tutor_name': row['tutor_name'],
                    'revenue_generated': row['revenue_generated'],
                    'commission_earned': row['commission_earned'],
                    'sessions': row['sessions']
                }
                for row in tutor_results
            ]
            
            # Client and tutor counts
            counts_query = """
                SELECT 
                    COUNT(DISTINCT a.client_id) AS unique_clients,
                    COUNT(DISTINCT a.tutor_id) AS active_tutors,
                    SUM(a.duration_hours) AS total_hours
                FROM platform_revenue_tracking prt
                JOIN appointments a ON a.appointment_id = prt.appointment_id
                WHERE prt.tracked_date BETWEEN %s AND %s
            """
            
            counts = await self._fetchone(counts_query, (period_start, period_end))
            
            return {
                'gross_revenue': summary['gross_revenue'] or Decimal("0.00"),
                'tutor_payments': summary['tutor_payments'] or Decimal("0.00"),
                'platform_commission': summary['platform_commission'] or Decimal("0.00"),
                'session_count': summary['session_count'] or 0,
                'unique_clients': counts['unique_clients'] or 0,
                'active_tutors': counts['active_tutors'] or 0,
                'total_hours': counts['total_hours'] or Decimal("0.00"),
                'revenue_by_service': revenue_by_service,
                'revenue_by_subject': revenue_by_subject,
                'top_tutors': top_tutors
            }
            
        except Exception as e:
            logger.error(f"Error getting platform revenue: {e}")
            raise
    
    async def create_commission_structure(
        self,
        structure_name: str,
        base_commission_rate: Decimal,
        created_by: int,
        description: Optional[str] = None,
        tiers: Optional[List[Dict[str, Any]]] = None
    ) -> CommissionStructure:
        """Create commission structure with tiers."""
        try:
            # Create structure
            structure_query = """
                INSERT INTO commission_structures (
                    structure_name, description, base_commission_rate,
                    is_active, created_by
                ) VALUES (
                    %s, %s, %s, %s, %s
                ) RETURNING *
            """
            
            structure_params = (
                structure_name, description, base_commission_rate,
                True, created_by
            )
            
            structure_result = await self._fetchone(structure_query, structure_params)
            structure_id = structure_result['structure_id']
            
            # Create tiers if provided
            tier_objects = []
            if tiers:
                for tier in tiers:
                    tier_query = """
                        INSERT INTO commission_tiers (
                            structure_id, tier_name, min_monthly_revenue,
                            max_monthly_revenue, commission_rate, bonus_percentage
                        ) VALUES (
                            %s, %s, %s, %s, %s, %s
                        ) RETURNING *
                    """
                    
                    tier_params = (
                        structure_id,
                        tier['tier_name'],
                        tier['min_monthly_revenue'],
                        tier.get('max_monthly_revenue'),
                        tier['commission_rate'],
                        tier.get('bonus_percentage', 0)
                    )
                    
                    tier_result = await self._fetchone(tier_query, tier_params)
                    tier_objects.append(CommissionTier(**tier_result))
            
            structure = CommissionStructure(**structure_result)
            structure.tiers = tier_objects
            return structure
            
        except Exception as e:
            logger.error(f"Error creating commission structure: {e}")
            raise
    
    async def get_commission_structure(
        self,
        structure_id: int
    ) -> Optional[CommissionStructure]:
        """Get commission structure with tiers."""
        try:
            # Get structure
            structure_query = """
                SELECT * FROM commission_structures
                WHERE structure_id = %s AND deleted_at IS NULL
            """
            
            structure_result = await self._fetchone(structure_query, (structure_id,))
            if not structure_result:
                return None
            
            # Get tiers
            tiers_query = """
                SELECT * FROM commission_tiers
                WHERE structure_id = %s
                ORDER BY min_monthly_revenue
            """
            
            tiers_results = await self._fetchall(tiers_query, (structure_id,))
            
            structure = CommissionStructure(**structure_result)
            structure.tiers = [CommissionTier(**tier) for tier in tiers_results]
            
            return structure
            
        except Exception as e:
            logger.error(f"Error getting commission structure: {e}")
            raise