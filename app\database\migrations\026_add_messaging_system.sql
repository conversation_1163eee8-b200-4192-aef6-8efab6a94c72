-- Migration: 026_add_messaging_system.sql
-- Description: Add complete messaging system for SMS and in-app chat
-- Author: System
-- Date: 2025-01-20

-- Create messaging_conversations table for conversation threads
CREATE TABLE IF NOT EXISTS messaging_conversations (
    conversation_id SERIAL PRIMARY KEY,
    conversation_type VARCHAR(50) NOT NULL CHECK (conversation_type IN ('sms', 'in_app', 'email')),
    
    -- Participants (at least one required)
    client_id INTEGER REFERENCES client_profiles(client_id),
    tutor_id INTEGER REFERENCES tutor_profiles(tutor_id),
    manager_id INTEGER REFERENCES user_accounts(user_id),
    
    -- SMS specific fields
    phone_number VARCHAR(20), -- For SMS conversations
    
    -- Conversation metadata
    subject VARCHAR(255),
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'archived', 'blocked')),
    last_message_at TIMESTAMP WITH TIME ZONE,
    unread_count INTEGER DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    archived_at TIMESTAMP WITH TIME ZONE,
    
    -- Ensure at least one participant
    CONSTRAINT at_least_one_participant CHECK (
        client_id IS NOT NULL OR tutor_id IS NOT NULL OR manager_id IS NOT NULL
    )
);

-- Create messaging_messages table for individual messages
CREATE TABLE IF NOT EXISTS messaging_messages (
    message_id SERIAL PRIMARY KEY,
    conversation_id INTEGER NOT NULL REFERENCES messaging_conversations(conversation_id) ON DELETE CASCADE,
    
    -- Sender information
    sender_type VARCHAR(50) NOT NULL CHECK (sender_type IN ('client', 'tutor', 'manager', 'system')),
    sender_id INTEGER REFERENCES user_accounts(user_id),
    
    -- Message content
    message_type VARCHAR(50) DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'file', 'template', 'system')),
    content TEXT NOT NULL,
    
    -- Role indicators
    role_indicator VARCHAR(50), -- '👨‍🏫 TUTOR', '👤 CLIENT', etc.
    
    -- SMS specific
    twilio_message_sid VARCHAR(255),
    sms_status VARCHAR(50),
    sms_error_message TEXT,
    
    -- Read status
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP WITH TIME ZONE,
    
    -- Delivery status
    delivery_status VARCHAR(50) DEFAULT 'sent' CHECK (delivery_status IN ('pending', 'sent', 'delivered', 'failed', 'read')),
    delivered_at TIMESTAMP WITH TIME ZONE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    edited_at TIMESTAMP WITH TIME ZONE,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create messaging_templates table for reusable message templates
CREATE TABLE IF NOT EXISTS messaging_templates (
    template_id SERIAL PRIMARY KEY,
    template_name VARCHAR(255) NOT NULL,
    template_type VARCHAR(50) NOT NULL CHECK (template_type IN ('sms', 'email', 'in_app', 'universal')),
    category VARCHAR(100) NOT NULL, -- 'appointment', 'billing', 'reminder', etc.
    
    -- Template content
    subject VARCHAR(255), -- For emails
    content_en TEXT NOT NULL,
    content_fr TEXT NOT NULL,
    
    -- Variables that can be replaced
    variables JSONB, -- {name: 'client_name', description: 'Client full name'}
    
    -- Usage tracking
    usage_count INTEGER DEFAULT 0,
    last_used_at TIMESTAMP WITH TIME ZONE,
    
    -- Metadata
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER REFERENCES user_accounts(user_id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(template_name, template_type)
);

-- Create messaging_broadcasts table for mass messaging
CREATE TABLE IF NOT EXISTS messaging_broadcasts (
    broadcast_id SERIAL PRIMARY KEY,
    
    -- Broadcast details
    title VARCHAR(255) NOT NULL,
    message_type VARCHAR(50) NOT NULL CHECK (message_type IN ('sms', 'email', 'push', 'in_app')),
    content_en TEXT NOT NULL,
    content_fr TEXT NOT NULL,
    
    -- Target audience
    recipient_type VARCHAR(50) NOT NULL CHECK (recipient_type IN ('all', 'clients', 'tutors', 'managers', 'custom')),
    recipient_filters JSONB, -- {role: 'client', active: true, service: 'math'}
    recipient_count INTEGER DEFAULT 0,
    
    -- Scheduling
    status VARCHAR(50) DEFAULT 'draft' CHECK (status IN ('draft', 'scheduled', 'sending', 'sent', 'cancelled')),
    scheduled_at TIMESTAMP WITH TIME ZONE,
    sent_at TIMESTAMP WITH TIME ZONE,
    
    -- Tracking
    sent_count INTEGER DEFAULT 0,
    failed_count INTEGER DEFAULT 0,
    read_count INTEGER DEFAULT 0,
    
    -- Metadata
    created_by INTEGER NOT NULL REFERENCES user_accounts(user_id),
    approved_by INTEGER REFERENCES user_accounts(user_id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    cancelled_at TIMESTAMP WITH TIME ZONE
);

-- Create messaging_broadcast_recipients table to track individual sends
CREATE TABLE IF NOT EXISTS messaging_broadcast_recipients (
    recipient_id SERIAL PRIMARY KEY,
    broadcast_id INTEGER NOT NULL REFERENCES messaging_broadcasts(broadcast_id) ON DELETE CASCADE,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id),
    
    -- Delivery tracking
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'delivered', 'failed', 'read')),
    sent_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    read_at TIMESTAMP WITH TIME ZONE,
    failed_at TIMESTAMP WITH TIME ZONE,
    failure_reason TEXT,
    
    -- Message details
    message_sid VARCHAR(255), -- For SMS via Twilio
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create messaging_attachments table for file uploads
CREATE TABLE IF NOT EXISTS messaging_attachments (
    attachment_id SERIAL PRIMARY KEY,
    message_id INTEGER NOT NULL REFERENCES messaging_messages(message_id) ON DELETE CASCADE,
    
    -- File details
    file_name VARCHAR(255) NOT NULL,
    file_type VARCHAR(100),
    file_size INTEGER,
    file_path TEXT NOT NULL,
    
    -- Security
    is_encrypted BOOLEAN DEFAULT FALSE,
    encryption_key VARCHAR(255),
    
    -- Metadata
    uploaded_by INTEGER REFERENCES user_accounts(user_id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_messaging_conversations_client ON messaging_conversations(client_id);
CREATE INDEX idx_messaging_conversations_tutor ON messaging_conversations(tutor_id);
CREATE INDEX idx_messaging_conversations_phone ON messaging_conversations(phone_number) WHERE phone_number IS NOT NULL;
CREATE INDEX idx_messaging_conversations_last_message ON messaging_conversations(last_message_at DESC);

CREATE INDEX idx_messaging_messages_conversation ON messaging_messages(conversation_id);
CREATE INDEX idx_messaging_messages_created ON messaging_messages(created_at DESC);
CREATE INDEX idx_messaging_messages_unread ON messaging_messages(conversation_id, is_read) WHERE is_read = FALSE;

CREATE INDEX idx_messaging_templates_active ON messaging_templates(is_active, template_type);
CREATE INDEX idx_messaging_templates_category ON messaging_templates(category);

CREATE INDEX idx_messaging_broadcasts_status ON messaging_broadcasts(status);
CREATE INDEX idx_messaging_broadcasts_scheduled ON messaging_broadcasts(scheduled_at) WHERE status = 'scheduled';

CREATE INDEX idx_messaging_broadcast_recipients_broadcast ON messaging_broadcast_recipients(broadcast_id);
CREATE INDEX idx_messaging_broadcast_recipients_user ON messaging_broadcast_recipients(user_id);

-- Add comments
COMMENT ON TABLE messaging_conversations IS 'Tracks all conversation threads across SMS, in-app chat, and email';
COMMENT ON TABLE messaging_messages IS 'Individual messages within conversations';
COMMENT ON TABLE messaging_templates IS 'Reusable message templates in English and French';
COMMENT ON TABLE messaging_broadcasts IS 'Mass messaging campaigns to multiple recipients';