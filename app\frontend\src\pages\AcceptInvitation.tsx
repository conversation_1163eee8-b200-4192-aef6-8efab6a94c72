import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { CheckCircle, XCircle, Eye, EyeOff, GraduationCap } from 'lucide-react';
import api from '../services/api';
import { Card } from '../components/common/Card';
import Button from '../components/common/Button';
import { Input } from '../components/common/Input';
import LoadingSpinner from '../components/ui/LoadingSpinner';
import { ConsentFlow } from '../components/consent/ConsentFlow';
import toast from 'react-hot-toast';

interface InvitationData {
  invitation_id: number;
  email: string;
  first_name?: string;
  last_name?: string;
  phone?: string;
  message?: string;
  expires_at: string;
}

const AcceptInvitation: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const token = searchParams.get('token');

  const [invitationData, setInvitationData] = useState<InvitationData | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showConsentFlow, setShowConsentFlow] = useState(false);
  const [accountCreated, setAccountCreated] = useState(false);

  useEffect(() => {
    if (token) {
      verifyToken();
    } else {
      setError(t('invitation.invalid_token'));
      setLoading(false);
    }
  }, [token]);

  const verifyToken = async () => {
    try {
      setLoading(true);
      const response = await api.get(`/api/v1/tutors/invitation/verify/${token}`);
      setInvitationData(response.data);
    } catch (error: any) {
      console.error('Error verifying token:', error);
      if (error.response?.status === 404) {
        setError(t('invitation.token_expired_or_invalid'));
      } else {
        setError(t('invitation.verification_error'));
      }
    } finally {
      setLoading(false);
    }
  };

  const validatePassword = (password: string): boolean => {
    if (password.length < 8) {
      setError(t('invitation.password_min_length'));
      return false;
    }

    const hasUpper = /[A-Z]/.test(password);
    const hasLower = /[a-z]/.test(password);
    const hasDigit = /\d/.test(password);

    if (!hasUpper || !hasLower || !hasDigit) {
      setError(t('invitation.password_requirements'));
      return false;
    }

    return true;
  };

  const handleAcceptInvitation = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!password || !confirmPassword) {
      setError(t('invitation.password_required'));
      return;
    }

    if (password !== confirmPassword) {
      setError(t('invitation.password_mismatch'));
      return;
    }

    if (!validatePassword(password)) {
      return;
    }

    try {
      setSubmitting(true);

      const response = await api.post('/api/v1/tutors/invitation/accept', {
        token,
        password
      });

      toast.success(t('invitation.account_created_successfully'));
      
      // The response should include an access token
      if (response.data.access_token) {
        // Store the token and user data
        localStorage.setItem('token', response.data.access_token);
        localStorage.setItem('user', JSON.stringify(response.data.user));
        
        // Mark account as created and show consent flow
        setAccountCreated(true);
        setShowConsentFlow(true);
      } else {
        // Redirect to login page
        navigate('/login', { 
          state: { 
            message: t('invitation.account_created_login_prompt'),
            email: invitationData?.email 
          }
        });
      }
    } catch (error: any) {
      console.error('Error accepting invitation:', error);
      if (error.response?.status === 404) {
        setError(t('invitation.token_expired_or_invalid'));
      } else if (error.response?.status === 400) {
        setError(t('invitation.validation_error'));
      } else {
        setError(t('invitation.acceptance_error'));
      }
    } finally {
      setSubmitting(false);
    }
  };

  const handleConsentComplete = () => {
    // After consents are accepted, redirect to dashboard
    navigate('/dashboard');
  };

  // Show consent flow after account creation
  if (accountCreated && showConsentFlow) {
    return (
      <ConsentFlow 
        onComplete={handleConsentComplete} 
        blockAccess={true}
      />
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <LoadingSpinner />
            <p className="mt-4 text-gray-600">{t('invitation.verifying_token')}</p>
          </div>
        </div>
      </div>
    );
  }

  if (error && !invitationData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <Card>
            <div className="text-center p-8">
              <XCircle className="mx-auto h-12 w-12 text-red-500 mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                {t('invitation.invalid_invitation')}
              </h2>
              <p className="text-gray-600 mb-6">{error}</p>
              <Button
                onClick={() => navigate('/login')}
                variant="outline"
              >
                {t('invitation.go_to_login')}
              </Button>
            </div>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <GraduationCap className="mx-auto h-12 w-12 text-blue-600" />
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            {t('invitation.welcome_to_tutoraide')}
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            {t('invitation.create_account_subtitle')}
          </p>
        </div>

        <Card>
          <div className="p-8">
            <div className="mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {t('invitation.invitation_details')}
              </h3>
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="text-sm text-gray-600">
                  <strong>{t('invitation.invited_as')}:</strong> {invitationData?.email}
                </p>
                {invitationData?.first_name && invitationData?.last_name && (
                  <p className="text-sm text-gray-600 mt-1">
                    <strong>{t('invitation.name')}:</strong> {invitationData.first_name} {invitationData.last_name}
                  </p>
                )}
                {invitationData?.message && (
                  <div className="mt-3 pt-3 border-t border-gray-200">
                    <p className="text-sm text-gray-600">
                      <strong>{t('invitation.personal_message')}:</strong>
                    </p>
                    <p className="text-sm text-gray-700 mt-1 italic">
                      "{invitationData.message}"
                    </p>
                  </div>
                )}
              </div>
            </div>

            <form onSubmit={handleAcceptInvitation} className="space-y-6">
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('invitation.create_password')}
                </label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder={t('invitation.password_placeholder')}
                    required
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-gray-400" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-400" />
                    )}
                  </button>
                </div>
                <p className="mt-1 text-xs text-gray-500">
                  {t('invitation.password_help')}
                </p>
              </div>

              <div>
                <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('invitation.confirm_password')}
                </label>
                <div className="relative">
                  <Input
                    id="confirmPassword"
                    type={showConfirmPassword ? 'text' : 'password'}
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    placeholder={t('invitation.confirm_password_placeholder')}
                    required
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-4 w-4 text-gray-400" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>

              {error && (
                <div className="bg-red-50 border border-red-200 rounded-md p-3">
                  <div className="flex">
                    <XCircle className="h-5 w-5 text-red-400" />
                    <div className="ml-3">
                      <p className="text-sm text-red-800">{error}</p>
                    </div>
                  </div>
                </div>
              )}

              <Button
                type="submit"
                disabled={submitting || !password || !confirmPassword}
                className="w-full"
                icon={submitting ? <LoadingSpinner size="sm" /> : <CheckCircle />}
              >
                {submitting ? t('invitation.creating_account') : t('invitation.accept_and_create_account')}
              </Button>
            </form>

            <div className="mt-6 text-center">
              <p className="text-xs text-gray-500">
                {t('invitation.terms_notice')}
              </p>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default AcceptInvitation;