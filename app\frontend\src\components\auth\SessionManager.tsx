import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { UserRoleType } from '../../types/auth';
import { SessionTimeoutModal } from './SessionTimeoutModal';

const SESSION_TIMEOUT_MS = {
  [UserRoleType.MANAGER]: 3 * 60 * 60 * 1000, // 3 hours
  [UserRoleType.TUTOR]: 30 * 60 * 1000, // 30 minutes
  [UserRoleType.CLIENT]: 30 * 60 * 1000, // 30 minutes
};

const WARNING_BEFORE_TIMEOUT_MS = 2 * 60 * 1000; // Show warning 2 minutes before timeout

interface SessionManagerProps {
  children: React.ReactNode;
}

export const SessionManager: React.FC<SessionManagerProps> = ({ children }) => {
  const { user, isAuthenticated, logout, refreshAccessToken } = useAuth();
  const [showTimeoutModal, setShowTimeoutModal] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState(0);
  const lastActivityRef = useRef(Date.now());
  const timeoutTimerRef = useRef<NodeJS.Timeout>();
  const warningTimerRef = useRef<NodeJS.Timeout>();

  const resetTimers = useCallback(() => {
    // Clear existing timers
    if (timeoutTimerRef.current) {
      clearTimeout(timeoutTimerRef.current);
    }
    if (warningTimerRef.current) {
      clearTimeout(warningTimerRef.current);
    }

    if (!isAuthenticated || !user) return;

    const sessionTimeout = SESSION_TIMEOUT_MS[user.activeRole];
    const warningTime = sessionTimeout - WARNING_BEFORE_TIMEOUT_MS;

    // Set warning timer
    warningTimerRef.current = setTimeout(() => {
      setTimeRemaining(Math.floor(WARNING_BEFORE_TIMEOUT_MS / 1000));
      setShowTimeoutModal(true);
    }, warningTime);

    // Set logout timer
    timeoutTimerRef.current = setTimeout(() => {
      setShowTimeoutModal(false);
      logout();
    }, sessionTimeout);
  }, [isAuthenticated, user, logout]);

  const handleActivity = useCallback(() => {
    const now = Date.now();
    const timeSinceLastActivity = now - lastActivityRef.current;

    // Only reset if more than 1 minute has passed since last activity
    if (timeSinceLastActivity > 60000) {
      lastActivityRef.current = now;
      resetTimers();
    }
  }, [resetTimers]);

  const handleExtendSession = useCallback(async () => {
    setShowTimeoutModal(false);
    lastActivityRef.current = Date.now();
    resetTimers();
    
    // Optionally refresh the access token
    try {
      await refreshAccessToken();
    } catch (error) {
      console.error('Failed to refresh token:', error);
    }
  }, [resetTimers, refreshAccessToken]);

  const handleLogout = useCallback(() => {
    setShowTimeoutModal(false);
    logout();
  }, [logout]);

  // Set up activity listeners
  useEffect(() => {
    if (!isAuthenticated) return;

    const events = ['mousedown', 'keydown', 'scroll', 'touchstart'];
    
    events.forEach(event => {
      window.addEventListener(event, handleActivity);
    });

    // Initial timer setup
    resetTimers();

    return () => {
      events.forEach(event => {
        window.removeEventListener(event, handleActivity);
      });
      
      if (timeoutTimerRef.current) {
        clearTimeout(timeoutTimerRef.current);
      }
      if (warningTimerRef.current) {
        clearTimeout(warningTimerRef.current);
      }
    };
  }, [isAuthenticated, handleActivity, resetTimers]);

  // Update time remaining
  useEffect(() => {
    if (showTimeoutModal && timeRemaining > 0) {
      const timer = setTimeout(() => {
        setTimeRemaining(prev => Math.max(0, prev - 1));
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [showTimeoutModal, timeRemaining]);

  return (
    <>
      {children}
      <SessionTimeoutModal
        isOpen={showTimeoutModal}
        timeRemaining={timeRemaining}
        onExtend={handleExtendSession}
        onLogout={handleLogout}
      />
    </>
  );
};