/**
 * Unit Tests for TranslationEditor Component
 * 
 * Tests admin translation editing functionality,
 * validation, and multi-language management.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { MemoryRouter } from 'react-router-dom';

import TranslationEditor from './TranslationEditor';
import api from '../../services/api';

// Mock API
vi.mock('../../services/api', () => ({
  api: {
    get: vi.fn(),
    put: vi.fn(),
    post: vi.fn(),
    delete: vi.fn()
  }
}));

// Mock toast notifications
vi.mock('react-hot-toast', () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
    loading: vi.fn()
  }
}));

describe('TranslationEditor', () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  });

  const mockTranslationKey = {
    key: 'common.save',
    category: 'common',
    translations: {
      en: 'Save',
      fr: 'Enregistrer'
    },
    description: 'Save button text',
    variables: [],
    lastModified: '2024-01-15T10:00:00Z',
    modifiedBy: '<EMAIL>'
  };

  beforeEach(() => {
    vi.clearAllMocks();
    (api.get as any).mockResolvedValue({ 
      data: { data: mockTranslationKey } 
    });
  });

  const renderComponent = (props = {}) => {
    return render(
      <QueryClientProvider client={queryClient}>
        <MemoryRouter>
          <TranslationEditor translationKey="common.save" {...props} />
        </MemoryRouter>
      </QueryClientProvider>
    );
  };

  it('renders translation editor with all languages', async () => {
    renderComponent();
    
    await waitFor(() => {
      expect(screen.getByText('common.save')).toBeInTheDocument();
      expect(screen.getByLabelText('English')).toHaveValue('Save');
      expect(screen.getByLabelText('Français')).toHaveValue('Enregistrer');
    });
  });

  it('shows loading state while fetching', () => {
    (api.get as any).mockImplementation(() => new Promise(() => {}));
    renderComponent();
    
    expect(screen.getByTestId('loading-skeleton')).toBeInTheDocument();
  });

  it('handles translation update', async () => {
    const user = userEvent.setup();
    renderComponent();
    
    await waitFor(() => {
      expect(screen.getByLabelText('English')).toBeInTheDocument();
    });
    
    // Update English translation
    const englishInput = screen.getByLabelText('English');
    await user.clear(englishInput);
    await user.type(englishInput, 'Save Changes');
    
    // Save changes
    const saveButton = screen.getByRole('button', { name: /save/i });
    await user.click(saveButton);
    
    expect(api.put).toHaveBeenCalledWith(
      '/admin/translations/keys/common.save',
      {
        en: 'Save Changes',
        fr: 'Enregistrer'
      }
    );
  });

  it('validates translation completeness', async () => {
    const user = userEvent.setup();
    renderComponent();
    
    await waitFor(() => {
      expect(screen.getByLabelText('Français')).toBeInTheDocument();
    });
    
    // Clear French translation
    const frenchInput = screen.getByLabelText('Français');
    await user.clear(frenchInput);
    
    // Try to save
    const saveButton = screen.getByRole('button', { name: /save/i });
    await user.click(saveButton);
    
    expect(screen.getByText(/translation required/i)).toBeInTheDocument();
    expect(api.put).not.toHaveBeenCalled();
  });

  it('shows variable placeholders', async () => {
    const keyWithVariables = {
      ...mockTranslationKey,
      translations: {
        en: 'Hello {name}',
        fr: 'Bonjour {name}'
      },
      variables: ['name']
    };
    
    (api.get as any).mockResolvedValue({ 
      data: { data: keyWithVariables } 
    });
    
    renderComponent();
    
    await waitFor(() => {
      expect(screen.getByText('Variables: {name}')).toBeInTheDocument();
    });
  });

  it('validates variable consistency', async () => {
    const user = userEvent.setup();
    const keyWithVariables = {
      ...mockTranslationKey,
      translations: {
        en: 'Hello {name}',
        fr: 'Bonjour {name}'
      },
      variables: ['name']
    };
    
    (api.get as any).mockResolvedValue({ 
      data: { data: keyWithVariables } 
    });
    
    renderComponent();
    
    await waitFor(() => {
      expect(screen.getByLabelText('Français')).toBeInTheDocument();
    });
    
    // Remove variable from French translation
    const frenchInput = screen.getByLabelText('Français');
    await user.clear(frenchInput);
    await user.type(frenchInput, 'Bonjour');
    
    // Try to save
    const saveButton = screen.getByRole('button', { name: /save/i });
    await user.click(saveButton);
    
    expect(screen.getByText(/missing variable: {name}/i)).toBeInTheDocument();
  });

  it('handles pluralization forms', async () => {
    const pluralKey = {
      ...mockTranslationKey,
      key: 'common.items',
      translations: {
        en: '{count, plural, =0{no items} one{# item} other{# items}}',
        fr: '{count, plural, =0{aucun article} one{# article} other{# articles}}'
      },
      variables: ['count'],
      pluralized: true
    };
    
    (api.get as any).mockResolvedValue({ 
      data: { data: pluralKey } 
    });
    
    renderComponent();
    
    await waitFor(() => {
      expect(screen.getByText('Pluralization')).toBeInTheDocument();
      expect(screen.getByLabelText('Zero (English)')).toHaveValue('no items');
      expect(screen.getByLabelText('One (English)')).toHaveValue('# item');
      expect(screen.getByLabelText('Many (English)')).toHaveValue('# items');
    });
  });

  it('shows translation preview', async () => {
    renderComponent();
    
    await waitFor(() => {
      expect(screen.getByText('Preview')).toBeInTheDocument();
    });
    
    const previewTab = screen.getByRole('tab', { name: /preview/i });
    await userEvent.click(previewTab);
    
    expect(screen.getByText('English: Save')).toBeInTheDocument();
    expect(screen.getByText('Français: Enregistrer')).toBeInTheDocument();
  });

  it('supports creating new translation key', async () => {
    const user = userEvent.setup();
    renderComponent({ translationKey: null, mode: 'create' });
    
    // Fill in new key details
    await user.type(screen.getByLabelText('Key'), 'common.new_key');
    await user.type(screen.getByLabelText('English'), 'New Key');
    await user.type(screen.getByLabelText('Français'), 'Nouvelle clé');
    await user.type(screen.getByLabelText('Description'), 'A new translation key');
    
    // Save
    const saveButton = screen.getByRole('button', { name: /create/i });
    await user.click(saveButton);
    
    expect(api.post).toHaveBeenCalledWith('/admin/translations/keys', {
      key: 'common.new_key',
      category: 'common',
      en: 'New Key',
      fr: 'Nouvelle clé',
      description: 'A new translation key'
    });
  });

  it('handles key deletion', async () => {
    const user = userEvent.setup();
    renderComponent();
    
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /delete/i })).toBeInTheDocument();
    });
    
    // Click delete
    const deleteButton = screen.getByRole('button', { name: /delete/i });
    await user.click(deleteButton);
    
    // Confirm deletion
    const confirmButton = screen.getByRole('button', { name: /confirm/i });
    await user.click(confirmButton);
    
    expect(api.delete).toHaveBeenCalledWith('/admin/translations/keys/common.save');
  });

  it('shows translation history', async () => {
    const keyWithHistory = {
      ...mockTranslationKey,
      history: [
        {
          timestamp: '2024-01-14T10:00:00Z',
          user: '<EMAIL>',
          changes: {
            en: { old: 'Save', new: 'Save Changes' }
          }
        }
      ]
    };
    
    (api.get as any).mockResolvedValue({ 
      data: { data: keyWithHistory } 
    });
    
    renderComponent();
    
    await waitFor(() => {
      expect(screen.getByRole('tab', { name: /history/i })).toBeInTheDocument();
    });
    
    const historyTab = screen.getByRole('tab', { name: /history/i });
    await userEvent.click(historyTab);
    
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText(/Save → Save Changes/)).toBeInTheDocument();
  });

  it('supports context help for translations', async () => {
    const user = userEvent.setup();
    renderComponent();
    
    await waitFor(() => {
      expect(screen.getByTestId('context-help')).toBeInTheDocument();
    });
    
    const helpButton = screen.getByTestId('context-help');
    await user.hover(helpButton);
    
    await waitFor(() => {
      expect(screen.getByText(/used in save buttons/i)).toBeInTheDocument();
    });
  });

  it('handles concurrent editing warning', async () => {
    const keyWithLock = {
      ...mockTranslationKey,
      locked: true,
      lockedBy: '<EMAIL>',
      lockedAt: new Date().toISOString()
    };
    
    (api.get as any).mockResolvedValue({ 
      data: { data: keyWithLock } 
    });
    
    renderComponent();
    
    await waitFor(() => {
      expect(screen.getByText(/currently being <NAME_EMAIL>/i)).toBeInTheDocument();
    });
  });

  it('auto-saves drafts', async () => {
    const user = userEvent.setup();
    renderComponent();
    
    await waitFor(() => {
      expect(screen.getByLabelText('English')).toBeInTheDocument();
    });
    
    // Type in input
    const englishInput = screen.getByLabelText('English');
    await user.clear(englishInput);
    await user.type(englishInput, 'Save Draft');
    
    // Wait for auto-save
    await waitFor(() => {
      expect(screen.getByText(/draft saved/i)).toBeInTheDocument();
    }, { timeout: 3000 });
  });

  it('validates translation key format', async () => {
    const user = userEvent.setup();
    renderComponent({ translationKey: null, mode: 'create' });
    
    // Try invalid key format
    const keyInput = screen.getByLabelText('Key');
    await user.type(keyInput, 'invalid key with spaces');
    
    // Try to save
    const saveButton = screen.getByRole('button', { name: /create/i });
    await user.click(saveButton);
    
    expect(screen.getByText(/invalid key format/i)).toBeInTheDocument();
  });

  it('supports bulk operations', async () => {
    const user = userEvent.setup();
    renderComponent({ mode: 'bulk' });
    
    await waitFor(() => {
      expect(screen.getByText(/bulk edit mode/i)).toBeInTheDocument();
    });
    
    // Select multiple keys
    const checkbox1 = screen.getByRole('checkbox', { name: /common.save/i });
    const checkbox2 = screen.getByRole('checkbox', { name: /common.cancel/i });
    
    await user.click(checkbox1);
    await user.click(checkbox2);
    
    // Apply bulk action
    const bulkButton = screen.getByRole('button', { name: /apply to selected/i });
    await user.click(bulkButton);
    
    expect(screen.getByText(/2 keys selected/i)).toBeInTheDocument();
  });

  it('exports translations', async () => {
    const user = userEvent.setup();
    renderComponent();
    
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /export/i })).toBeInTheDocument();
    });
    
    const exportButton = screen.getByRole('button', { name: /export/i });
    await user.click(exportButton);
    
    expect(api.post).toHaveBeenCalledWith('/admin/translations/export', {
      keys: ['common.save'],
      format: 'json',
      languages: ['en', 'fr']
    });
  });

  it('imports translations from file', async () => {
    const user = userEvent.setup();
    renderComponent();
    
    await waitFor(() => {
      expect(screen.getByLabelText(/import/i)).toBeInTheDocument();
    });
    
    const file = new File(['{"common.save": {"en": "Save", "fr": "Enregistrer"}}'], 'translations.json', {
      type: 'application/json'
    });
    
    const input = screen.getByLabelText(/import/i);
    await user.upload(input, file);
    
    expect(screen.getByText(/1 translation imported/i)).toBeInTheDocument();
  });

  it('shows Quebec French specific options', async () => {
    renderComponent();
    
    await waitFor(() => {
      expect(screen.getByRole('checkbox', { name: /quebec french variant/i })).toBeInTheDocument();
    });
    
    const quebecCheckbox = screen.getByRole('checkbox', { name: /quebec french variant/i });
    await userEvent.click(quebecCheckbox);
    
    // Should show Quebec-specific field
    expect(screen.getByLabelText('Français (Québec)')).toBeInTheDocument();
  });

  it('validates against reserved keywords', async () => {
    const user = userEvent.setup();
    renderComponent({ translationKey: null, mode: 'create' });
    
    // Try to use reserved keyword
    const keyInput = screen.getByLabelText('Key');
    await user.type(keyInput, 'system.internal');
    
    const saveButton = screen.getByRole('button', { name: /create/i });
    await user.click(saveButton);
    
    expect(screen.getByText(/reserved keyword/i)).toBeInTheDocument();
  });

  it('shows character count for translations', async () => {
    renderComponent();
    
    await waitFor(() => {
      expect(screen.getByText(/4 characters/i)).toBeInTheDocument(); // "Save"
      expect(screen.getByText(/11 characters/i)).toBeInTheDocument(); // "Enregistrer"
    });
  });

  it('supports translation search', async () => {
    const user = userEvent.setup();
    renderComponent({ showSearch: true });
    
    await waitFor(() => {
      expect(screen.getByPlaceholderText(/search translations/i)).toBeInTheDocument();
    });
    
    const searchInput = screen.getByPlaceholderText(/search translations/i);
    await user.type(searchInput, 'save');
    
    // Should filter results
    expect(screen.getByText('common.save')).toBeInTheDocument();
    expect(screen.queryByText('common.cancel')).not.toBeInTheDocument();
  });
});

describe('TranslationEditor Accessibility', () => {
  const queryClient = new QueryClient();
  
  const renderComponent = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <MemoryRouter>
          <TranslationEditor translationKey="common.save" />
        </MemoryRouter>
      </QueryClientProvider>
    );
  };

  it('has proper form labels', async () => {
    renderComponent();
    
    await waitFor(() => {
      expect(screen.getByLabelText('English')).toBeInTheDocument();
      expect(screen.getByLabelText('Français')).toBeInTheDocument();
    });
  });

  it('shows validation errors with proper ARIA', async () => {
    const user = userEvent.setup();
    renderComponent();
    
    await waitFor(() => {
      expect(screen.getByLabelText('English')).toBeInTheDocument();
    });
    
    const englishInput = screen.getByLabelText('English');
    await user.clear(englishInput);
    
    const saveButton = screen.getByRole('button', { name: /save/i });
    await user.click(saveButton);
    
    const errorMessage = screen.getByText(/translation required/i);
    expect(errorMessage).toHaveAttribute('role', 'alert');
    expect(englishInput).toHaveAttribute('aria-invalid', 'true');
  });

  it('supports keyboard navigation', async () => {
    const user = userEvent.setup();
    renderComponent();
    
    await waitFor(() => {
      expect(screen.getByLabelText('English')).toBeInTheDocument();
    });
    
    // Tab through fields
    await user.tab();
    expect(screen.getByLabelText('English')).toHaveFocus();
    
    await user.tab();
    expect(screen.getByLabelText('Français')).toHaveFocus();
    
    await user.tab();
    expect(screen.getByRole('button', { name: /save/i })).toHaveFocus();
  });
});