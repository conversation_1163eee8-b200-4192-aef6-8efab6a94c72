"""
Consent management API endpoints for GDPR compliance.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Request
from fastapi.responses import JSONResponse

from app.core.dependencies import get_current_active_user, get_database
from app.models.user_models import User
from app.models.consent_models import (
    ConsentAcceptanceRequest,
    ConsentWithdrawalRequest,
    ConsentStatusResponse,
    ConsentHistoryResponse,
    ConsentSummaryResponse,
    ConsentValidationResult,
    BulkConsentRequest,
    BulkConsentResponse,
    ConsentDocument,
    ConsentLevel
)
from app.services.consent_service import ConsentService
from app.core.logging import TutorAideLogger

router = APIRouter()
logger = TutorAideLogger.get_logger(__name__)
consent_service = ConsentService()


@router.get("/documents", response_model=List[ConsentDocument])
async def get_consent_documents(
    language: str = Query("en", description="Language code (en/fr)"),
    consent_type: Optional[str] = Query(None, description="Filter by consent type"),
    level: Optional[ConsentLevel] = Query(None, description="Filter by consent level"),
    current_user: User = Depends(get_current_active_user),
    db=Depends(get_database)
):
    """
    Get available consent documents.
    
    Returns all active consent documents, optionally filtered by type or level.
    """
    try:
        async with db.acquire() as conn:
            if consent_type:
                # Get specific consent document
                documents = await consent_service.document_repo.find_active_by_type_and_language(
                    conn, consent_type, language
                )
                return [documents] if documents else []
            elif level:
                # Get documents by level
                return await consent_service.get_available_consent_documents(
                    conn, language, level
                )
            else:
                # Get all documents
                return await consent_service.get_available_consent_documents(
                    conn, language
                )
    except Exception as e:
        logger.error(f"Error fetching consent documents: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch consent documents")


@router.get("/summary", response_model=ConsentSummaryResponse)
async def get_consent_summary(
    language: str = Query("en", description="Language code for consent titles"),
    current_user: User = Depends(get_current_active_user),
    db=Depends(get_database)
):
    """
    Get summary of user's consent status.
    
    Returns the current status of all consents for the authenticated user.
    """
    try:
        async with db.acquire() as conn:
            return await consent_service.get_user_consent_summary(
                conn, current_user.user_id, language
            )
    except Exception as e:
        logger.error(f"Error fetching consent summary: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch consent summary")


@router.post("/accept", response_model=ConsentStatusResponse)
async def accept_consent(
    request: ConsentAcceptanceRequest,
    http_request: Request,
    current_user: User = Depends(get_current_active_user),
    db=Depends(get_database)
):
    """
    Accept a specific consent.
    
    Records user's acceptance of a consent document.
    """
    try:
        # Get IP address and user agent from request
        ip_address = http_request.client.host if http_request.client else None
        user_agent = http_request.headers.get("user-agent")
        
        async with db.acquire() as conn:
            return await consent_service.accept_consent(
                conn, 
                current_user.user_id, 
                request,
                ip_address,
                user_agent
            )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error accepting consent: {e}")
        raise HTTPException(status_code=500, detail="Failed to accept consent")


@router.post("/bulk-accept", response_model=BulkConsentResponse)
async def bulk_accept_consents(
    request: BulkConsentRequest,
    http_request: Request,
    current_user: User = Depends(get_current_active_user),
    db=Depends(get_database)
):
    """
    Accept multiple consents in bulk.
    
    Useful for initial consent collection during onboarding.
    """
    try:
        # Get IP address and user agent from request
        ip_address = http_request.client.host if http_request.client else None
        user_agent = http_request.headers.get("user-agent")
        
        async with db.acquire() as conn:
            return await consent_service.bulk_accept_consents(
                conn, 
                current_user.user_id, 
                request,
                ip_address,
                user_agent
            )
    except Exception as e:
        logger.error(f"Error bulk accepting consents: {e}")
        raise HTTPException(status_code=500, detail="Failed to accept consents")


@router.post("/withdraw", response_model=ConsentStatusResponse)
async def withdraw_consent(
    request: ConsentWithdrawalRequest,
    http_request: Request,
    current_user: User = Depends(get_current_active_user),
    db=Depends(get_database)
):
    """
    Withdraw a previously granted consent.
    
    Note: Mandatory consents (Level 1) cannot be withdrawn.
    """
    try:
        # Get IP address and user agent from request
        ip_address = http_request.client.host if http_request.client else None
        user_agent = http_request.headers.get("user-agent")
        
        async with db.acquire() as conn:
            return await consent_service.withdraw_consent(
                conn, 
                current_user.user_id, 
                request,
                ip_address,
                user_agent
            )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error withdrawing consent: {e}")
        raise HTTPException(status_code=500, detail="Failed to withdraw consent")


@router.get("/history", response_model=ConsentHistoryResponse)
async def get_consent_history(
    consent_type: Optional[str] = Query(None, description="Filter by consent type"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of entries"),
    current_user: User = Depends(get_current_active_user),
    db=Depends(get_database)
):
    """
    Get consent history for the user.
    
    Returns a chronological list of consent actions (granted, withdrawn, etc.).
    """
    try:
        async with db.acquire() as conn:
            return await consent_service.get_consent_history(
                conn, 
                current_user.user_id, 
                consent_type,
                limit
            )
    except Exception as e:
        logger.error(f"Error fetching consent history: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch consent history")


@router.get("/validate", response_model=ConsentValidationResult)
async def validate_consents(
    required_consents: Optional[List[str]] = Query(None, description="Specific consents to validate"),
    language: str = Query("en", description="Language for validation messages"),
    current_user: User = Depends(get_current_active_user),
    db=Depends(get_database)
):
    """
    Validate user's consent status.
    
    Checks if the user has all required consents and if any have expired.
    """
    try:
        async with db.acquire() as conn:
            return await consent_service.validate_user_consents(
                conn,
                current_user.user_id,
                required_consents,
                language
            )
    except Exception as e:
        logger.error(f"Error validating consents: {e}")
        raise HTTPException(status_code=500, detail="Failed to validate consents")


@router.post("/cleanup-expired")
async def cleanup_expired_consents(
    current_user: User = Depends(get_current_active_user),
    db=Depends(get_database)
):
    """
    Clean up expired consents (admin only).
    
    Marks expired consents as expired in the database.
    """
    # Check if user is admin/manager
    if current_user.active_role != "manager":
        raise HTTPException(status_code=403, detail="Only managers can clean up expired consents")
    
    try:
        async with db.acquire() as conn:
            count = await consent_service.cleanup_expired_consents(conn)
            return {
                "status": "success",
                "message": f"Cleaned up {count} expired consents"
            }
    except Exception as e:
        logger.error(f"Error cleaning up expired consents: {e}")
        raise HTTPException(status_code=500, detail="Failed to clean up expired consents")