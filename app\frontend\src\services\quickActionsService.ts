import api from './api';
import { UserRoleType } from '../types/auth';

// Quick Action Types and Interfaces
export enum QuickActionType {
  // Manager Actions
  ADD_CLIENT = 'add_client',
  ADD_TUTOR = 'add_tutor',
  BOOK_APPOINTMENT = 'book_appointment',
  CREATE_INVOICE = 'create_invoice',
  APPROVE_PAYMENTS = 'approve_payments',
  VIEW_REPORTS = 'view_reports',
  SEND_BROADCAST = 'send_broadcast',
  
  // Tutor Actions
  VIEW_SCHEDULE = 'view_schedule',
  SET_AVAILABILITY = 'set_availability',
  REQUEST_TIME_OFF = 'request_time_off',
  UPDATE_PROFILE = 'update_profile',
  VIEW_EARNINGS = 'view_earnings',
  
  // Client Actions
  BOOK_SESSION = 'book_session',
  VIEW_INVOICES = 'view_invoices',
  UPDATE_PAYMENT = 'update_payment',
  MESSAGE_TUTOR = 'message_tutor',
  VIEW_PROGRESS = 'view_progress',
  
  // Common Actions
  VIEW_CALENDAR = 'view_calendar',
  SEARCH_USERS = 'search_users',
  VIEW_MESSAGES = 'view_messages',
  SETTINGS = 'settings'
}

export enum QuickActionCategory {
  USERS = 'users',
  APPOINTMENTS = 'appointments',
  BILLING = 'billing',
  COMMUNICATION = 'communication',
  REPORTS = 'reports',
  SETTINGS = 'settings'
}

export interface QuickAction {
  type: QuickActionType;
  title: string;
  description: string;
  icon: string;
  category: QuickActionCategory;
  route?: string;
  modal?: string;
  params?: Record<string, any>;
  shortcut?: string;
  color?: string;
  requires_selection?: boolean;
  allowed_roles: UserRoleType[];
}

export interface QuickActionContext {
  user_id?: number;
  entity_type?: string;
  entity_id?: number;
  page?: string;
  search_query?: string;
  filters?: Record<string, any>;
}

export interface QuickActionResult {
  success: boolean;
  action_type: QuickActionType;
  message?: string;
  redirect_to?: string;
  data?: any;
}

export interface UserQuickActions {
  user_id: number;
  role: UserRoleType;
  available_actions: QuickAction[];
  recent_actions: Array<{
    action: QuickAction;
    used_at: string;
    context?: QuickActionContext;
  }>;
  pinned_actions: QuickAction[];
  suggested_actions: QuickAction[];
}

export interface ExecuteActionRequest {
  action_type: QuickActionType;
  context?: QuickActionContext;
  params?: Record<string, any>;
}

export interface PinActionRequest {
  action_type: QuickActionType;
  is_pinned: boolean;
}

class QuickActionsService {
  /**
   * Get available quick actions for current user
   */
  async getAvailableQuickActions(): Promise<UserQuickActions> {
    try {
      const response = await api.get<UserQuickActions>('/quick-actions/available');
      return response.data;
    } catch (error: any) {
      console.error('Error fetching quick actions:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch quick actions');
    }
  }

  /**
   * Execute a quick action
   */
  async executeAction(request: ExecuteActionRequest): Promise<QuickActionResult> {
    try {
      const response = await api.post<QuickActionResult>('/quick-actions/execute', request);
      return response.data;
    } catch (error: any) {
      console.error('Error executing quick action:', error);
      throw new Error(error.response?.data?.detail || 'Failed to execute action');
    }
  }

  /**
   * Pin/unpin a quick action
   */
  async pinAction(actionType: QuickActionType, isPinned: boolean): Promise<{ message: string }> {
    try {
      const response = await api.post<{ message: string }>('/quick-actions/pin', {
        action_type: actionType,
        is_pinned: isPinned
      });
      return response.data;
    } catch (error: any) {
      console.error('Error pinning action:', error);
      throw new Error(error.response?.data?.detail || 'Failed to update pinned action');
    }
  }

  /**
   * Get recent actions
   */
  async getRecentActions(limit: number = 5): Promise<Array<{
    action: QuickAction;
    used_at: string;
    context?: QuickActionContext;
  }>> {
    try {
      const response = await api.get<UserQuickActions>('/quick-actions/available');
      return response.data.recent_actions.slice(0, limit);
    } catch (error: any) {
      console.error('Error fetching recent actions:', error);
      return [];
    }
  }

  /**
   * Get actions for specific context
   */
  async getContextualActions(context: QuickActionContext): Promise<QuickAction[]> {
    try {
      const response = await api.post<{ actions: QuickAction[] }>('/quick-actions/contextual', context);
      return response.data.actions;
    } catch (error: any) {
      console.error('Error fetching contextual actions:', error);
      return [];
    }
  }

  /**
   * Get action by type
   */
  getActionByType(actions: QuickAction[], type: QuickActionType): QuickAction | undefined {
    return actions.find(action => action.type === type);
  }

  /**
   * Get actions by category
   */
  getActionsByCategory(actions: QuickAction[], category: QuickActionCategory): QuickAction[] {
    return actions.filter(action => action.category === category);
  }

  /**
   * Get action icon class
   */
  getActionIconClass(icon: string): string {
    // Map icon names to Lucide React icon components
    const iconMap: Record<string, string> = {
      'user-plus': 'UserPlus',
      'calendar-plus': 'CalendarPlus',
      'file-text': 'FileText',
      'dollar-sign': 'DollarSign',
      'message-square': 'MessageSquare',
      'bar-chart': 'BarChart',
      'settings': 'Settings',
      'calendar': 'Calendar',
      'search': 'Search',
      'clock': 'Clock',
      'user': 'User',
      'credit-card': 'CreditCard',
      'send': 'Send'
    };

    return iconMap[icon] || 'Circle';
  }

  /**
   * Get action color
   */
  getActionColor(action: QuickAction): string {
    if (action.color) return action.color;

    // Default colors by category
    const categoryColors: Record<QuickActionCategory, string> = {
      [QuickActionCategory.USERS]: 'blue',
      [QuickActionCategory.APPOINTMENTS]: 'green',
      [QuickActionCategory.BILLING]: 'purple',
      [QuickActionCategory.COMMUNICATION]: 'orange',
      [QuickActionCategory.REPORTS]: 'indigo',
      [QuickActionCategory.SETTINGS]: 'gray'
    };

    return categoryColors[action.category] || 'gray';
  }

  /**
   * Build action route
   */
  buildActionRoute(action: QuickAction, context?: QuickActionContext): string {
    if (!action.route) return '#';

    let route = action.route;

    // Replace route parameters
    if (action.params && context) {
      Object.entries(action.params).forEach(([key, value]) => {
        if (context[key as keyof QuickActionContext]) {
          route = route.replace(`:${key}`, String(context[key as keyof QuickActionContext]));
        }
      });
    }

    return route;
  }

  /**
   * Check if action is available for role
   */
  isActionAvailableForRole(action: QuickAction, role: UserRoleType): boolean {
    return action.allowed_roles.includes(role);
  }

  /**
   * Get keyboard shortcut display
   */
  formatShortcut(shortcut?: string): string {
    if (!shortcut) return '';

    // Format shortcuts for display (e.g., "cmd+k" -> "⌘K")
    return shortcut
      .replace('cmd', '⌘')
      .replace('ctrl', '⌃')
      .replace('alt', '⌥')
      .replace('shift', '⇧')
      .replace('+', '')
      .toUpperCase();
  }

  /**
   * Register keyboard shortcuts
   */
  registerKeyboardShortcuts(actions: QuickAction[], onAction: (action: QuickAction) => void) {
    const handleKeyPress = (event: KeyboardEvent) => {
      actions.forEach(action => {
        if (!action.shortcut) return;

        const shortcut = action.shortcut.toLowerCase();
        const keys = shortcut.split('+');
        
        const isCmd = keys.includes('cmd') && (event.metaKey || event.ctrlKey);
        const isCtrl = keys.includes('ctrl') && event.ctrlKey;
        const isAlt = keys.includes('alt') && event.altKey;
        const isShift = keys.includes('shift') && event.shiftKey;
        const key = keys[keys.length - 1];

        if (event.key.toLowerCase() === key) {
          if ((isCmd || isCtrl || !keys.some(k => ['cmd', 'ctrl'].includes(k))) &&
              (isAlt || !keys.includes('alt')) &&
              (isShift || !keys.includes('shift'))) {
            event.preventDefault();
            onAction(action);
          }
        }
      });
    };

    document.addEventListener('keydown', handleKeyPress);

    // Return cleanup function
    return () => {
      document.removeEventListener('keydown', handleKeyPress);
    };
  }
}

export const quickActionsService = new QuickActionsService();