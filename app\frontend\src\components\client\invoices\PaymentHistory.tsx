import React from 'react';
import { useTranslation } from 'react-i18next';
import { 
  CreditCard,
  Calendar,
  DollarSign,
  FileText,
  User,
  CheckCircle,
  Download,
  Eye
} from 'lucide-react';
import Button from '../../common/Button';

interface Payment {
  payment_id: number;
  invoice_id: number;
  invoice_number?: string;
  amount: number;
  payment_date: string;
  payment_method: string;
  reference_number: string | null;
  paid_by_parent: 1 | 2 | null;
  description?: string;
}

interface PaymentHistoryProps {
  payments: Payment[];
  onViewInvoice?: (invoiceId: number) => void;
}

export const PaymentHistory: React.FC<PaymentHistoryProps> = ({
  payments,
  onViewInvoice
}) => {
  const { t } = useTranslation();

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-CA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-CA', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getPaymentMethodIcon = (method: string) => {
    switch (method.toLowerCase()) {
      case 'credit card':
      case 'visa':
      case 'mastercard':
      case 'amex':
        return <CreditCard className="w-4 h-4" />;
      case 'direct deposit':
      case 'bank transfer':
      case 'e-transfer':
        return <DollarSign className="w-4 h-4" />;
      default:
        return <CheckCircle className="w-4 h-4" />;
    }
  };

  const getPaymentMethodColor = (method: string) => {
    switch (method.toLowerCase()) {
      case 'credit card':
      case 'visa':
      case 'mastercard':
      case 'amex':
        return 'text-blue-600 bg-blue-50';
      case 'direct deposit':
      case 'bank transfer':
      case 'e-transfer':
        return 'text-green-600 bg-green-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  // Calculate summary statistics
  const totalPaid = payments.reduce((sum, payment) => sum + payment.amount, 0);
  const paymentsByParent1 = payments.filter(p => p.paid_by_parent === 1);
  const paymentsByParent2 = payments.filter(p => p.paid_by_parent === 2);
  const totalParent1 = paymentsByParent1.reduce((sum, p) => sum + p.amount, 0);
  const totalParent2 = paymentsByParent2.reduce((sum, p) => sum + p.amount, 0);

  if (payments.length === 0) {
    return (
      <div className="text-center py-12">
        <CreditCard className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-text-primary mb-2">
          {t('client.payments.noPayments')}
        </h3>
        <p className="text-text-secondary">
          {t('client.payments.noPaymentsDescription')}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-text-secondary">{t('client.payments.totalPaid')}</p>
              <p className="text-2xl font-bold text-text-primary">{formatCurrency(totalPaid)}</p>
            </div>
            <div className="p-3 bg-green-100 rounded-lg">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>
        
        {paymentsByParent1.length > 0 && (
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary">{t('client.payments.parent1Total')}</p>
                <p className="text-2xl font-bold text-text-primary">{formatCurrency(totalParent1)}</p>
                <p className="text-xs text-text-secondary mt-1">
                  {paymentsByParent1.length} {t('client.payments.payments')}
                </p>
              </div>
              <div className="p-3 bg-blue-100 rounded-lg">
                <User className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </div>
        )}
        
        {paymentsByParent2.length > 0 && (
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary">{t('client.payments.parent2Total')}</p>
                <p className="text-2xl font-bold text-text-primary">{formatCurrency(totalParent2)}</p>
                <p className="text-xs text-text-secondary mt-1">
                  {paymentsByParent2.length} {t('client.payments.payments')}
                </p>
              </div>
              <div className="p-3 bg-purple-100 rounded-lg">
                <User className="w-6 h-6 text-purple-600" />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Payment List */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-200">
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {t('client.payments.table.date')}
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {t('client.payments.table.invoice')}
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {t('client.payments.table.method')}
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {t('client.payments.table.reference')}
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {t('client.payments.table.paidBy')}
              </th>
              <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                {t('client.payments.table.amount')}
              </th>
              <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                {t('client.payments.table.actions')}
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {payments.map((payment) => (
              <tr key={payment.payment_id} className="hover:bg-gray-50">
                <td className="px-4 py-4 whitespace-nowrap">
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-text-primary">
                        {formatDate(payment.payment_date)}
                      </p>
                      <p className="text-xs text-text-secondary">
                        {formatTime(payment.payment_date)}
                      </p>
                    </div>
                  </div>
                </td>
                <td className="px-4 py-4 whitespace-nowrap">
                  <div className="flex items-center gap-2">
                    <FileText className="w-4 h-4 text-gray-400" />
                    <span className="text-sm text-text-primary">
                      {payment.invoice_number || `#${payment.invoice_id}`}
                    </span>
                  </div>
                </td>
                <td className="px-4 py-4 whitespace-nowrap">
                  <span className={`inline-flex items-center gap-1 px-2.5 py-1 rounded-full text-xs font-medium ${getPaymentMethodColor(payment.payment_method)}`}>
                    {getPaymentMethodIcon(payment.payment_method)}
                    {payment.payment_method}
                  </span>
                </td>
                <td className="px-4 py-4 whitespace-nowrap">
                  <span className="text-sm text-text-secondary">
                    {payment.reference_number || '-'}
                  </span>
                </td>
                <td className="px-4 py-4 whitespace-nowrap">
                  {payment.paid_by_parent && (
                    <div className="flex items-center gap-1 text-sm text-text-secondary">
                      <User className="w-4 h-4" />
                      {t(`client.payments.parent${payment.paid_by_parent}`)}
                    </div>
                  )}
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-right">
                  <span className="text-sm font-medium text-text-primary">
                    {formatCurrency(payment.amount)}
                  </span>
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-right">
                  {onViewInvoice && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onViewInvoice(payment.invoice_id)}
                      className="p-2"
                    >
                      <Eye className="w-4 h-4" />
                    </Button>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Export Option */}
      <div className="flex justify-end pt-4 border-t border-gray-200">
        <Button
          variant="secondary"
          leftIcon={<Download className="w-4 h-4" />}
        >
          {t('client.payments.exportPayments')}
        </Button>
      </div>
    </div>
  );
};