"""
Comprehensive localization middleware for TutorAide Quebec market.
Handles language detection, preference management, and request context.
"""

from typing import Optional, Callable, Dict, Any
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import RedirectResponse, JSONResponse
import contextvars
import logging

from .constants import SUPPORTED_LANGUAGES, DEFAULT_LANGUAGE
from .translation_service import get_translation_service
from .language_detector import get_language_detector


# Context variable for current request language
current_language: contextvars.ContextVar[str] = contextvars.ContextVar('current_language', default=DEFAULT_LANGUAGE)

# Context variable for user preferences
user_language_preference: contextvars.ContextVar[Optional[str]] = contextvars.ContextVar('user_language_preference', default=None)


class EnhancedLocalizationMiddleware(BaseHTTPMiddleware):
    """Enhanced middleware for comprehensive language detection and management."""
    
    def __init__(
        self, 
        app, 
        cookie_name: str = "tutoraide_language",
        header_name: str = "Accept-Language",
        enable_analytics: bool = True,
        enable_caching: bool = True
    ):
        super().__init__(app)
        self.cookie_name = cookie_name
        self.header_name = header_name
        self.enable_analytics = enable_analytics
        self.enable_caching = enable_caching
        self.translation_service = get_translation_service()
        self.language_detector = get_language_detector()
        self.logger = logging.getLogger(__name__)
    
    async def dispatch(self, request: Request, call_next):
        """Process request with comprehensive language detection."""
        try:
            # Detect user's preferred language with enhanced logic
            language_info = await self._detect_language_comprehensive(request)
            detected_language = language_info['language']
            
            # Set language in request state and context vars
            request.state.language = detected_language
            request.state.language_info = language_info
            current_language.set(detected_language)
            
            # Add language metadata to request
            request.state.language_metadata = {
                'detected_from': language_info['source'],
                'quebec_french': language_info.get('quebec_french', False),
                'fallback_used': language_info.get('fallback_used', False)
            }
            
            # Process the request
            response = await call_next(request)
            
            # Handle language persistence
            await self._handle_language_persistence(request, response, language_info)
            
            # Add language headers to response
            self._add_language_headers(response, detected_language)
            
            # Log language analytics if enabled
            if self.enable_analytics:
                self._log_language_analytics(request, language_info)
            
            return response
            
        except Exception as e:
            self.logger.error(f"Error in localization middleware: {e}")
            # Fallback to default language on error
            request.state.language = DEFAULT_LANGUAGE
            current_language.set(DEFAULT_LANGUAGE)
            return await call_next(request)
    
    async def _detect_language_comprehensive(self, request: Request) -> Dict[str, Any]:
        """Comprehensive language detection with metadata."""
        language_info = {
            'language': DEFAULT_LANGUAGE,
            'source': 'default',
            'quebec_french': False,
            'fallback_used': False,
            'user_authenticated': False
        }
        
        # 1. Check URL parameter (highest priority)
        lang_param = request.query_params.get("lang") or request.query_params.get("language")
        if lang_param and lang_param in SUPPORTED_LANGUAGES:
            language_info.update({
                'language': lang_param,
                'source': 'url_parameter'
            })
            return language_info
        
        # 2. Check authenticated user preference
        # This would be implemented with user context from auth system
        user_lang = await self._get_user_language_preference(request)
        if user_lang and user_lang in SUPPORTED_LANGUAGES:
            language_info.update({
                'language': user_lang,
                'source': 'user_preference',
                'user_authenticated': True
            })
            return language_info
        
        # 3. Check cookie
        cookie_lang = request.cookies.get(self.cookie_name)
        if cookie_lang and cookie_lang in SUPPORTED_LANGUAGES:
            language_info.update({
                'language': cookie_lang,
                'source': 'cookie'
            })
            return language_info
        
        # 4. Enhanced Accept-Language header detection
        accept_language = request.headers.get(self.header_name, "")
        if accept_language:
            detected_lang = self.language_detector.detect_from_header(accept_language)
            quebec_french = self.language_detector.is_quebec_french_preferred(accept_language)
            
            language_info.update({
                'language': detected_lang,
                'source': 'accept_language_header',
                'quebec_french': quebec_french,
                'browser_preferences': self.language_detector.get_browser_language_preferences(accept_language)
            })
            return language_info
        
        # 5. User-Agent fallback
        user_agent = request.headers.get('user-agent', '')
        if user_agent:
            ua_lang = self.language_detector.detect_from_user_agent(user_agent)
            if ua_lang != DEFAULT_LANGUAGE:
                language_info.update({
                    'language': ua_lang,
                    'source': 'user_agent',
                    'fallback_used': True
                })
                return language_info
        
        # 6. Default language with fallback flag
        language_info['fallback_used'] = True
        return language_info
    
    async def _get_user_language_preference(self, request: Request) -> Optional[str]:
        """Get authenticated user's language preference from database."""
        # This would integrate with the auth system to get user preferences
        # For now, return None - will be implemented in task 11.5
        return None
    
    async def _handle_language_persistence(
        self, 
        request: Request, 
        response: Response, 
        language_info: Dict[str, Any]
    ) -> None:
        """Handle language persistence through cookies and database."""
        detected_language = language_info['language']
        
        # Set language cookie if needed
        current_cookie = request.cookies.get(self.cookie_name)
        if current_cookie != detected_language:
            response.set_cookie(
                key=self.cookie_name,
                value=detected_language,
                max_age=365 * 24 * 60 * 60,  # 1 year
                httponly=True,
                secure=request.url.scheme == "https",
                samesite="lax",
                domain=None  # Will be set to request domain
            )
        
        # Update user preference if authenticated (implement in task 11.5)
        if language_info.get('user_authenticated') and language_info['source'] == 'url_parameter':
            # This would update the user's language preference in the database
            pass
    
    def _add_language_headers(self, response: Response, language: str) -> None:
        """Add language-related headers to response."""
        response.headers["Content-Language"] = language
        response.headers["Vary"] = "Accept-Language"
        
        # Add Quebec-specific headers if French
        if language == 'fr':
            response.headers["X-Language-Region"] = "Quebec"
    
    def _log_language_analytics(self, request: Request, language_info: Dict[str, Any]) -> None:
        """Log language detection analytics for monitoring."""
        analytics_data = {
            'detected_language': language_info['language'],
            'detection_source': language_info['source'],
            'quebec_french': language_info.get('quebec_french', False),
            'fallback_used': language_info.get('fallback_used', False),
            'path': request.url.path,
            'user_agent': request.headers.get('user-agent', ''),
            'accept_language': request.headers.get('accept-language', '')
        }
        
        # Log at debug level to avoid spam, but available for analytics
        self.logger.debug(f"Language detection: {analytics_data}")


# Legacy middleware for backward compatibility
class LocalizationMiddleware(EnhancedLocalizationMiddleware):
    """Legacy middleware class for backward compatibility."""
    pass


# Enhanced utility functions and dependencies

def get_current_language() -> str:
    """Get current request language from context variable."""
    return current_language.get(DEFAULT_LANGUAGE)


def get_request_language(request: Request) -> str:
    """Get the current request's language from middleware."""
    return getattr(request.state, "language", DEFAULT_LANGUAGE)


def get_request_language_info(request: Request) -> Dict[str, Any]:
    """Get comprehensive language information from request."""
    return getattr(request.state, "language_info", {
        'language': DEFAULT_LANGUAGE,
        'source': 'default',
        'quebec_french': False,
        'fallback_used': True
    })


def create_localized_response(
    request: Request,
    template_name: str,
    context: dict,
    template_engine
) -> Response:
    """Create a response with comprehensive localized template context."""
    language = get_request_language(request)
    language_info = get_request_language_info(request)
    
    # Add comprehensive translation functions to context
    translation_service = get_translation_service()
    
    context.update({
        # Basic translation functions
        "t": lambda key, **kwargs: translation_service.get_translation(key, language, **kwargs),
        "t_fallback": lambda key, fallback_langs=None, **kwargs: translation_service.get_translation_with_fallback(
            key, language, fallback_langs, **kwargs
        ),
        "t_plural": lambda key, count, **kwargs: translation_service.pluralize(key, count, language, **kwargs),
        
        # Formatting functions
        "format_currency": lambda amount: translation_service.format_currency(amount, language),
        "format_number": lambda number: translation_service.format_number(number, language),
        "format_date": lambda date_obj, fmt="medium": translation_service.format_date(date_obj, fmt, language),
        "format_time": lambda time_obj, fmt="short": translation_service.format_time(time_obj, fmt, language),
        
        # Quebec-specific formatting
        "format_quebec_phone": translation_service.format_quebec_phone,
        "format_quebec_postal_code": translation_service.format_quebec_postal_code,
        
        # Template rendering
        "render_template": lambda template, variables: translation_service.render_template(template, variables, language),
        
        # Language metadata
        "language": language,
        "language_info": language_info,
        "supported_languages": SUPPORTED_LANGUAGES,
        "is_quebec_french": language_info.get('quebec_french', False),
        "language_direction": "ltr",  # Both English and French are LTR
        
        # Helper functions
        "get_language_variants": lambda: get_language_detector().get_language_variants(language),
        "is_french": lambda: language == 'fr',
        "is_english": lambda: language == 'en'
    })
    
    return template_engine.TemplateResponse(template_name, {"request": request, **context})


def create_localized_json_response(
    request: Request,
    data: dict,
    status_code: int = 200
) -> JSONResponse:
    """Create a JSON response with localized content."""
    language = get_request_language(request)
    translation_service = get_translation_service()
    
    # Add language metadata to response
    response_data = {
        **data,
        "_language": language,
        "_language_info": get_request_language_info(request)
    }
    
    response = JSONResponse(content=response_data, status_code=status_code)
    response.headers["Content-Language"] = language
    
    return response


# FastAPI Dependencies

def get_language_dependency():
    """FastAPI dependency to get current request language."""
    def get_language(request: Request) -> str:
        return get_request_language(request)
    return get_language


def get_language_info_dependency():
    """FastAPI dependency to get comprehensive language information."""
    def get_language_info(request: Request) -> Dict[str, Any]:
        return get_request_language_info(request)
    return get_language_info


def get_translation_dependency():
    """FastAPI dependency to get translation function for current language."""
    def get_translation_func(request: Request) -> Callable[[str], str]:
        language = get_request_language(request)
        translation_service = get_translation_service()
        return lambda key, **kwargs: translation_service.get_translation(key, language, **kwargs)
    return get_translation_func


def get_formatting_dependency():
    """FastAPI dependency to get formatting functions for current language."""
    def get_formatting_funcs(request: Request) -> Dict[str, Callable]:
        language = get_request_language(request)
        translation_service = get_translation_service()
        
        return {
            'currency': lambda amount: translation_service.format_currency(amount, language),
            'number': lambda number: translation_service.format_number(number, language),
            'date': lambda date_obj, fmt="medium": translation_service.format_date(date_obj, fmt, language),
            'time': lambda time_obj, fmt="short": translation_service.format_time(time_obj, fmt, language),
            'quebec_phone': translation_service.format_quebec_phone,
            'quebec_postal_code': translation_service.format_quebec_postal_code
        }
    return get_formatting_funcs


# Context managers for testing and development

class LanguageContext:
    """Context manager for temporarily setting language in tests."""
    
    def __init__(self, language: str):
        self.language = language
        self.previous_language = None
    
    def __enter__(self):
        self.previous_language = current_language.get(DEFAULT_LANGUAGE)
        current_language.set(self.language)
        return self.language
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.previous_language:
            current_language.set(self.previous_language)
        else:
            current_language.set(DEFAULT_LANGUAGE)


def set_test_language(language: str) -> LanguageContext:
    """Set language for testing purposes."""
    return LanguageContext(language)