import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import * as Sentry from '@sentry/react';
import { authService } from '../services/auth.service';
import { User, UserRoleType, LoginCredentials, AuthContextType } from '../types/auth';
import { setupAxiosInterceptors } from '../utils/axios-config';
import toast from 'react-hot-toast';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const navigate = useNavigate();
  const [user, setUser] = useState<User | null>(null);
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  // Logout function defined early so it can be used in interceptor setup
  const logout = useCallback(() => {
    setUser(null);
    setAccessToken(null);
    
    localStorage.removeItem('accessToken');
    localStorage.removeItem('user');
    
    authService.clearAuthToken();
    
    // Clear Sentry user context
    Sentry.setUser(null);
    
    // Don't show toast if already on login page
    if (window.location.pathname !== '/login') {
      toast.success('Logged out successfully');
    }
    navigate('/login');
  }, [navigate]);

  // Initialize auth state from localStorage
  useEffect(() => {
    const initAuth = async () => {
      try {
        const storedToken = localStorage.getItem('accessToken');
        const storedUser = localStorage.getItem('user');

        if (storedToken && storedUser) {
          const parsedUser = JSON.parse(storedUser);
          setAccessToken(storedToken);
          setUser(parsedUser);
          authService.setAuthToken(storedToken);
          
          // Set Sentry user context
          Sentry.setUser({
            id: parsedUser.userId,
            email: parsedUser.email,
            username: `${parsedUser.firstName} ${parsedUser.lastName}`,
            role: parsedUser.activeRole || parsedUser.roles?.[0],
          });
        }
      } catch (error) {
        console.error('Failed to initialize auth:', error);
        localStorage.removeItem('accessToken');
        localStorage.removeItem('user');
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
    
    // Setup axios interceptors with logout function
    setupAxiosInterceptors(logout);
  }, [logout]);

  const login = useCallback(async (credentials: LoginCredentials) => {
    try {
      const response = await authService.login(credentials);
      
      setUser(response.user);
      setAccessToken(response.accessToken);
      
      localStorage.setItem('accessToken', response.accessToken);
      localStorage.setItem('user', JSON.stringify(response.user));
      
      authService.setAuthToken(response.accessToken);
      
      // Set Sentry user context
      Sentry.setUser({
        id: response.user.userId,
        email: response.user.email,
        username: `${response.user.firstName} ${response.user.lastName}`,
        role: response.user.activeRole || response.user.roles?.[0],
      });
      
      toast.success('Login successful');
      navigate('/dashboard');
    } catch (error: any) {
      toast.error(error.response?.data?.detail || 'Login failed');
      throw error;
    }
  }, [navigate]);


  const switchRole = useCallback(async (role: UserRoleType) => {
    if (!user || !user.roles.includes(role)) {
      toast.error('Invalid role');
      return;
    }

    try {
      const response = await authService.switchRole(role);
      
      const updatedUser = { ...user, activeRole: role };
      setUser(updatedUser);
      setAccessToken(response.accessToken);
      
      localStorage.setItem('accessToken', response.accessToken);
      localStorage.setItem('user', JSON.stringify(updatedUser));
      
      authService.setAuthToken(response.accessToken);
      
      toast.success(`Switched to ${role} role`);
    } catch (error: any) {
      toast.error(error.response?.data?.detail || 'Failed to switch role');
      throw error;
    }
  }, [user]);

  const refreshAccessToken = useCallback(async () => {
    try {
      const response = await authService.refreshToken();
      
      setAccessToken(response.accessToken);
      localStorage.setItem('accessToken', response.accessToken);
      authService.setAuthToken(response.accessToken);
    } catch (error) {
      console.error('Failed to refresh token:', error);
      logout();
    }
  }, [logout]);

  const handleOAuthCallback = useCallback(async (tokenData: {
    access_token: string;
    refresh_token: string;
    token_type: string;
    expires_in: number;
  }) => {
    try {
      console.log('AuthContext: Processing OAuth callback...');
      
      // Store tokens
      localStorage.setItem('accessToken', tokenData.access_token);
      localStorage.setItem('refreshToken', tokenData.refresh_token);
      
      // Set auth token in service
      authService.setAuthToken(tokenData.access_token, tokenData.refresh_token);
      setAccessToken(tokenData.access_token);
      
      console.log('AuthContext: Getting user profile...');
      // Get user profile
      const userProfile = await authService.getCurrentUser();
      console.log('AuthContext: User profile received:', userProfile);
      console.log('AuthContext: User roles from profile:', userProfile.roles);
      
      setUser(userProfile);
      localStorage.setItem('user', JSON.stringify(userProfile));
      
      // Set Sentry user context
      Sentry.setUser({
        id: userProfile.userId,
        email: userProfile.email,
        username: `${userProfile.firstName} ${userProfile.lastName}`,
        role: userProfile.activeRole || userProfile.roles?.[0],
      });
      
      return userProfile;
    } catch (error) {
      console.error('AuthContext: OAuth callback error:', error);
      console.error('AuthContext: Error details:', error.response?.data || error.message);
      // Clear any partial state
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('user');
      setAccessToken(null);
      setUser(null);
      throw error;
    }
  }, []);

  const value: AuthContextType = {
    user,
    accessToken,
    isAuthenticated: !!user && !!accessToken,
    isLoading,
    login,
    logout,
    switchRole,
    refreshAccessToken,
    handleOAuthCallback,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};