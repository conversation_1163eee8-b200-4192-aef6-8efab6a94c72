#!/bin/bash
# Build script for TutorAide frontend

echo "Building TutorAide frontend..."

# Navigate to frontend directory
cd app/frontend

# Install dependencies
echo "Installing frontend dependencies..."
npm install

# Build the production bundle
echo "Building production bundle..."
npm run build

# Check if build was successful
if [ -d "dist" ]; then
    echo "Frontend build completed successfully!"
    echo "Build output is in app/frontend/dist"
else
    echo "Frontend build failed!"
    exit 1
fi