/**
 * Utility functions for API configuration and URL handling
 */

/**
 * Get the appropriate API base URL based on environment
 * In production: use the current domain's API endpoint
 * In development: use localhost or VITE_API_URL
 */
export function getApiBaseUrl(): string {
  // Check if we're in production (not localhost)
  const isProduction = typeof window !== 'undefined' && window.location.hostname !== 'localhost';
  
  if (isProduction) {
    // In production, construct API URL from current domain
    return `${window.location.protocol}//${window.location.host}/api/v1`;
  } else {
    // In development, use environment variable or default to localhost
    return (import.meta as any).env.VITE_API_URL || 'http://localhost:8000/api/v1';
  }
}

/**
 * Get the frontend base URL for redirects
 */
export function getFrontendBaseUrl(): string {
  if (typeof window !== 'undefined') {
    return `${window.location.protocol}//${window.location.host}`;
  }
  return 'http://localhost:3000'; // fallback for SSR
}

/**
 * Check if we're running in production environment
 */
export function isProduction(): boolean {
  return typeof window !== 'undefined' && window.location.hostname !== 'localhost';
}

/**
 * Check if we're running in development environment
 */
export function isDevelopment(): boolean {
  return !isProduction();
}