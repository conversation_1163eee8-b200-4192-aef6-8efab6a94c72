import React from 'react';
import { clsx } from 'clsx';

interface AvatarProps {
  src?: string;
  alt?: string;
  name?: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  shape?: 'circle' | 'square';
  status?: 'online' | 'offline' | 'busy' | 'away';
  className?: string;
  onClick?: () => void;
}

export const Avatar: React.FC<AvatarProps> = ({
  src,
  alt,
  name,
  size = 'md',
  shape = 'circle',
  status,
  className,
  onClick,
}) => {
  const initials = React.useMemo(() => {
    if (!name) return '?';
    const parts = name.trim().split(' ');
    if (parts.length >= 2) {
      return `${parts[0][0]}${parts[parts.length - 1][0]}`.toUpperCase();
    }
    return name.slice(0, 2).toUpperCase();
  }, [name]);

  const baseStyles = 'relative inline-flex items-center justify-center overflow-hidden bg-background-tertiary text-text-secondary font-medium transition-transform';
  
  const sizes = {
    xs: 'w-6 h-6 text-xs',
    sm: 'w-8 h-8 text-sm',
    md: 'w-10 h-10 text-base',
    lg: 'w-14 h-14 text-lg',
    xl: 'w-20 h-20 text-2xl',
  };

  const statusSizes = {
    xs: 'w-1.5 h-1.5 ring-1',
    sm: 'w-2 h-2 ring-1',
    md: 'w-2.5 h-2.5 ring-2',
    lg: 'w-3 h-3 ring-2',
    xl: 'w-4 h-4 ring-2',
  };

  const statusColors = {
    online: 'bg-semantic-success',
    offline: 'bg-text-muted',
    busy: 'bg-semantic-error',
    away: 'bg-semantic-warning',
  };

  const statusPositions = {
    xs: 'bottom-0 right-0',
    sm: 'bottom-0 right-0',
    md: 'bottom-0 right-0',
    lg: 'bottom-0.5 right-0.5',
    xl: 'bottom-1 right-1',
  };

  return (
    <div
      className={clsx(
        baseStyles,
        sizes[size],
        shape === 'circle' ? 'rounded-full' : 'rounded-lg',
        onClick && 'cursor-pointer hover:scale-105',
        className
      )}
      onClick={onClick}
    >
      {src ? (
        <img
          src={src}
          alt={alt || name || 'Avatar'}
          className="w-full h-full object-cover"
        />
      ) : (
        <span>{initials}</span>
      )}
      
      {status && (
        <span
          className={clsx(
            'absolute rounded-full ring-white',
            statusSizes[size],
            statusColors[status],
            statusPositions[size]
          )}
        />
      )}
    </div>
  );
};

interface AvatarGroupProps {
  children: React.ReactNode;
  max?: number;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

export const AvatarGroup: React.FC<AvatarGroupProps> = ({
  children,
  max = 3,
  size = 'md',
  className,
}) => {
  const childrenArray = React.Children.toArray(children);
  const visibleChildren = childrenArray.slice(0, max);
  const remainingCount = childrenArray.length - max;

  const overlapStyles = {
    xs: '-ml-2',
    sm: '-ml-2.5',
    md: '-ml-3',
    lg: '-ml-4',
    xl: '-ml-6',
  };

  return (
    <div className={clsx('flex items-center', className)}>
      {visibleChildren.map((child, index) => (
        <div
          key={index}
          className={clsx(
            'relative ring-2 ring-white rounded-full',
            index > 0 && overlapStyles[size]
          )}
          style={{ zIndex: visibleChildren.length - index }}
        >
          {React.isValidElement(child) && React.cloneElement(child as React.ReactElement<AvatarProps>, { size })}
        </div>
      ))}
      
      {remainingCount > 0 && (
        <div
          className={clsx(
            'relative ring-2 ring-white rounded-full',
            overlapStyles[size]
          )}
          style={{ zIndex: 0 }}
        >
          <Avatar
            name={`+${remainingCount}`}
            size={size}
            className="bg-background-tertiary"
          />
        </div>
      )}
    </div>
  );
};

export default Avatar;