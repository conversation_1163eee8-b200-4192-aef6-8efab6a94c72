import React from 'react';
import { clsx } from 'clsx';
import { designTokens, Size } from '../styles/design-tokens';

interface LoadingSpinnerProps {
  size?: Size | 'xs' | 'xl';
  message?: string;
  className?: string;
  color?: 'primary' | 'white' | 'accent';
  label?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'md', 
  message,
  className,
  color = 'accent',
  label = 'Loading...',
}) => {
  // Map legacy size names
  const mappedSize = size === 'small' ? 'sm' : size === 'medium' ? 'md' : size === 'large' ? 'lg' : size;
  
  const sizeClasses = {
    xs: 'w-3 h-3 border-2',
    sm: 'w-4 h-4 border-2',
    md: 'w-8 h-8 border-3',
    lg: 'w-12 h-12 border-4',
    xl: 'w-16 h-16 border-4',
  };

  const colorClasses = {
    primary: 'border-gray-200 border-t-text-primary',
    white: 'border-gray-100 border-t-white',
    accent: 'border-gray-200 border-t-accent-red',
  };

  return (
    <div className={clsx('flex flex-col items-center justify-center', className)} role="status">
      <div 
        className={clsx(
          'animate-spin rounded-full',
          sizeClasses[mappedSize],
          colorClasses[color]
        )}
        aria-hidden="true"
      />
      {message && (
        <p className="mt-3 text-sm text-text-muted">{message}</p>
      )}
      <span className="sr-only">{label || message}</span>
    </div>
  );
};

interface LoadingOverlayProps {
  isLoading: boolean;
  label?: string;
  size?: Size | 'xs' | 'xl';
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isLoading,
  label = 'Loading...',
  size = 'lg',
}) => {
  if (!isLoading) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="bg-white rounded-xl shadow-elevated p-6 flex flex-col items-center gap-4">
        <LoadingSpinner size={size} color="accent" />
        <p className="text-text-primary font-medium">{label}</p>
      </div>
    </div>
  );
};

interface LoadingSkeletonProps {
  className?: string;
  variant?: 'text' | 'rect' | 'circle';
  width?: string | number;
  height?: string | number;
  animate?: boolean;
}

export const LoadingSkeleton: React.FC<LoadingSkeletonProps> = ({
  className,
  variant = 'rect',
  width,
  height,
  animate = true,
}) => {
  const variantClasses = {
    text: 'h-4 w-full',
    rect: 'h-16 w-full',
    circle: 'h-12 w-12 rounded-full',
  };

  return (
    <div
      className={clsx(
        'bg-gradient-to-r from-background-secondary via-background-tertiary to-background-secondary bg-[length:200%_100%]',
        animate && 'animate-pulse',
        variantClasses[variant],
        variant !== 'circle' && 'rounded-lg',
        className
      )}
      style={{
        width: width,
        height: height,
      }}
      aria-hidden="true"
    />
  );
};

export default LoadingSpinner;