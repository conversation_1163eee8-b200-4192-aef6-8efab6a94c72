-- Migration 016: Add onboarding system for progressive user setup
-- This migration creates tables for managing user onboarding flows

-- Create onboarding flows table
CREATE TABLE onboarding_flows (
    flow_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('client', 'tutor', 'manager')),
    flow_name VARCHAR(200) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'paused', 'completed', 'skipped')),
    current_step_order INTEGER,
    completion_percentage NUMERIC(5,2) NOT NULL DEFAULT 0.00 CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
    
    -- Timing information
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    paused_at TIMESTAMP WITH TIME ZONE,
    
    -- Additional metadata
    metadata JSON,
    
    -- Standard fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Foreign key constraints
    CONSTRAINT fk_onboarding_flows_user_id FOREIGN KEY (user_id) REFERENCES user_accounts(user_id) ON DELETE CASCADE
);

-- Create onboarding steps table
CREATE TABLE onboarding_steps (
    step_id SERIAL PRIMARY KEY,
    flow_id INTEGER NOT NULL,
    step_order INTEGER NOT NULL CHECK (step_order >= 1),
    step_type VARCHAR(30) NOT NULL CHECK (step_type IN (
        'profile_setup', 'verification', 'preferences', 'learning_assessment',
        'document_upload', 'contact_info', 'emergency_contacts', 'availability',
        'subjects', 'rates', 'location', 'consent', 'welcome', 'tutorial'
    )),
    title VARCHAR(200) NOT NULL,
    description TEXT,
    is_required BOOLEAN NOT NULL DEFAULT TRUE,
    is_skippable BOOLEAN NOT NULL DEFAULT FALSE,
    estimated_minutes INTEGER CHECK (estimated_minutes >= 1),
    instructions TEXT,
    
    -- Configuration and validation
    validation_rules JSON,
    metadata JSON,
    
    -- Progress tracking
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'skipped', 'blocked')),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    skipped_at TIMESTAMP WITH TIME ZONE,
    skip_reason TEXT,
    
    -- Completion data
    completion_data JSON,
    validation_errors JSON,
    
    -- Standard fields
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Foreign key constraints
    CONSTRAINT fk_onboarding_steps_flow_id FOREIGN KEY (flow_id) REFERENCES onboarding_flows(flow_id) ON DELETE CASCADE,
    
    -- Unique constraint for step order within flow
    CONSTRAINT uk_onboarding_steps_order UNIQUE (flow_id, step_order) DEFERRABLE INITIALLY DEFERRED
);

-- Create indexes for performance
CREATE INDEX idx_onboarding_flows_user_id ON onboarding_flows(user_id);
CREATE INDEX idx_onboarding_flows_role ON onboarding_flows(role);
CREATE INDEX idx_onboarding_flows_status ON onboarding_flows(status);
CREATE INDEX idx_onboarding_flows_created_at ON onboarding_flows(created_at);
CREATE INDEX idx_onboarding_flows_user_role ON onboarding_flows(user_id, role);

CREATE INDEX idx_onboarding_steps_flow_id ON onboarding_steps(flow_id);
CREATE INDEX idx_onboarding_steps_order ON onboarding_steps(flow_id, step_order);
CREATE INDEX idx_onboarding_steps_status ON onboarding_steps(status);
CREATE INDEX idx_onboarding_steps_step_type ON onboarding_steps(step_type);

-- Add comments
COMMENT ON TABLE onboarding_flows IS 'Progressive onboarding flows for different user roles';
COMMENT ON COLUMN onboarding_flows.flow_id IS 'Unique onboarding flow identifier';
COMMENT ON COLUMN onboarding_flows.user_id IS 'User undergoing onboarding';
COMMENT ON COLUMN onboarding_flows.role IS 'User role for this onboarding (client, tutor, manager)';
COMMENT ON COLUMN onboarding_flows.flow_name IS 'Name of the onboarding flow';
COMMENT ON COLUMN onboarding_flows.status IS 'Current status of the onboarding flow';
COMMENT ON COLUMN onboarding_flows.current_step_order IS 'Current step order number';
COMMENT ON COLUMN onboarding_flows.completion_percentage IS 'Percentage of onboarding completed (0-100)';
COMMENT ON COLUMN onboarding_flows.metadata IS 'Additional flow configuration and data';

COMMENT ON TABLE onboarding_steps IS 'Individual steps within onboarding flows';
COMMENT ON COLUMN onboarding_steps.step_id IS 'Unique step identifier';
COMMENT ON COLUMN onboarding_steps.flow_id IS 'Parent onboarding flow';
COMMENT ON COLUMN onboarding_steps.step_order IS 'Order of step within the flow (1-based)';
COMMENT ON COLUMN onboarding_steps.step_type IS 'Type of onboarding step';
COMMENT ON COLUMN onboarding_steps.title IS 'Display title for the step';
COMMENT ON COLUMN onboarding_steps.is_required IS 'Whether this step must be completed';
COMMENT ON COLUMN onboarding_steps.is_skippable IS 'Whether this step can be skipped';
COMMENT ON COLUMN onboarding_steps.estimated_minutes IS 'Estimated time to complete step';
COMMENT ON COLUMN onboarding_steps.instructions IS 'Detailed instructions for the user';
COMMENT ON COLUMN onboarding_steps.validation_rules IS 'JSON validation rules for step completion';
COMMENT ON COLUMN onboarding_steps.completion_data IS 'Data collected when step was completed';
COMMENT ON COLUMN onboarding_steps.validation_errors IS 'Current validation errors (if any)';
COMMENT ON COLUMN onboarding_steps.skip_reason IS 'Reason provided when step was skipped';

-- Grant permissions (skip if role doesn't exist)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'tutoraide_app') THEN
        
    END IF;
END $$;