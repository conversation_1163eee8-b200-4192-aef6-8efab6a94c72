-- ================================================
-- Migration: Simplify User Tables Structure (FIXED)
-- ================================================
-- This migration consolidates user-related tables into simplified tables
-- Version: 030
-- Date: 2025-06-24
-- ================================================

-- ============================================
-- Step 1: Create New Simplified Tables
-- ============================================

-- Unified user preferences table
CREATE TABLE IF NOT EXISTS user_preferences (
    user_id INTEGER PRIMARY KEY REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    
    -- Language preferences
    preferred_language VARCHAR(5) DEFAULT 'en' CHECK (preferred_language IN ('en', 'fr')),
    quebec_french_preference BOOLEAN DEFAULT true,
    
    -- Date and time formatting
    date_format VARCHAR(20) DEFAULT 'medium' CHECK (date_format IN ('short', 'medium', 'long', 'full')),
    time_format VARCHAR(10) DEFAULT 'auto' CHECK (time_format IN ('auto', '12h', '24h')),
    
    -- Currency and numbers
    currency_display VARCHAR(20) DEFAULT 'symbol' CHECK (currency_display IN ('symbol', 'code', 'name')),
    number_precision INTEGER DEFAULT 2 CHECK (number_precision >= 0 AND number_precision <= 6),
    
    -- UI preferences (JSONB for flexibility)
    ui_settings JSONB DEFAULT '{}'::jsonb,
    
    -- Notification preferences (JSONB for all notification types)
    notification_settings JSONB DEFAULT '{
        "appointment_reminder": {"push": true, "sms": true, "email": true},
        "payment_due": {"push": true, "sms": true, "email": true},
        "session_confirmed": {"push": true, "sms": false, "email": true},
        "general": {"push": true, "sms": false, "email": true}
    }'::jsonb,
    
    -- Quick actions and pinned items
    pinned_actions TEXT[] DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Unified authentication tokens table
CREATE TABLE IF NOT EXISTS auth_tokens (
    token_id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    
    -- Token type and value
    token_type VARCHAR(20) NOT NULL CHECK (token_type IN ('session', 'password_reset', 'email_verify', 'api_key')),
    token_hash VARCHAR(255) NOT NULL UNIQUE,
    
    -- Token lifecycle
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    used_at TIMESTAMP WITH TIME ZONE,
    revoked_at TIMESTAMP WITH TIME ZONE,
    
    -- Additional metadata (JSONB for flexibility)
    metadata JSONB DEFAULT '{}'::jsonb,
    
    -- Request information
    ip_address INET,
    user_agent TEXT,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Unified user security settings table
CREATE TABLE IF NOT EXISTS user_security (
    user_id INTEGER PRIMARY KEY REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    
    -- Two-factor authentication
    two_factor_enabled BOOLEAN DEFAULT false,
    two_factor_method VARCHAR(20) CHECK (two_factor_method IN ('totp', 'sms', 'email')),
    two_factor_secret TEXT,
    two_factor_backup_codes TEXT[] DEFAULT '{}',
    two_factor_verified_at TIMESTAMP WITH TIME ZONE,
    
    -- Trusted devices (JSONB array)
    trusted_devices JSONB DEFAULT '[]'::jsonb,
    
    -- Password security
    require_password_change BOOLEAN DEFAULT false,
    last_password_change TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    password_history JSONB DEFAULT '[]'::jsonb,
    
    -- Account security
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP WITH TIME ZONE,
    last_login_at TIMESTAMP WITH TIME ZONE,
    last_login_ip INET,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ============================================
-- Step 2: Create Indexes
-- ============================================

CREATE INDEX IF NOT EXISTS idx_user_preferences_language ON user_preferences(preferred_language);
CREATE INDEX IF NOT EXISTS idx_user_security_2fa ON user_security(two_factor_enabled) WHERE two_factor_enabled = true;
CREATE INDEX IF NOT EXISTS idx_user_security_locked ON user_security(locked_until) WHERE locked_until IS NOT NULL;

-- Indexes for auth_tokens
CREATE INDEX IF NOT EXISTS idx_auth_tokens_user_id ON auth_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_auth_tokens_type ON auth_tokens(token_type);
CREATE INDEX IF NOT EXISTS idx_auth_tokens_expires ON auth_tokens(expires_at);
CREATE INDEX IF NOT EXISTS idx_auth_tokens_hash ON auth_tokens(token_hash);

-- ============================================
-- Step 3: Create Update Triggers
-- ============================================

-- Update timestamp trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers
CREATE TRIGGER update_user_preferences_updated_at BEFORE UPDATE ON user_preferences
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_security_updated_at BEFORE UPDATE ON user_security
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================
-- Step 4: Migrate Data (only from existing columns)
-- ============================================

-- Migrate user preferences from user_accounts
INSERT INTO user_preferences (user_id, preferred_language, quebec_french_preference)
SELECT 
    user_id,
    COALESCE(preferred_language, 'en'),
    COALESCE(quebec_french_preference, true)
FROM user_accounts
WHERE deleted_at IS NULL
ON CONFLICT (user_id) DO NOTHING;

-- Migrate password reset tokens (only columns that exist)
INSERT INTO auth_tokens (user_id, token_type, token_hash, expires_at, metadata, ip_address, user_agent, created_at)
SELECT 
    user_id,
    'password_reset',
    token::text,
    expires_at,
    '{}'::jsonb,
    ip_address,
    user_agent,
    created_at
FROM password_reset_tokens
WHERE used_at IS NULL AND expires_at > CURRENT_TIMESTAMP;

-- Migrate email verification tokens
INSERT INTO auth_tokens (user_id, token_type, token_hash, expires_at, metadata, ip_address, user_agent, created_at)
SELECT 
    user_id,
    'email_verify',
    token::text,
    expires_at,
    jsonb_build_object('email', email),
    ip_address,
    user_agent,
    created_at
FROM email_verification_tokens
WHERE verified_at IS NULL AND expires_at > CURRENT_TIMESTAMP;

-- Migrate active sessions
INSERT INTO auth_tokens (user_id, token_type, token_hash, expires_at, metadata, ip_address, user_agent, created_at)
SELECT 
    user_id,
    'session',
    token_hash,
    expires_at,
    jsonb_build_object(
        'session_id', session_id::text,
        'role_type', role_type
    ),
    ip_address,
    user_agent,
    created_at
FROM user_sessions
WHERE is_active = true AND expires_at > CURRENT_TIMESTAMP;

-- Create default security settings for all users
INSERT INTO user_security (user_id)
SELECT user_id
FROM user_accounts
WHERE deleted_at IS NULL
ON CONFLICT (user_id) DO NOTHING;

-- Migrate notification preferences if they exist
UPDATE user_preferences p
SET notification_settings = (
    SELECT jsonb_object_agg(
        np.notification_type,
        jsonb_build_object(
            'push', np.push_enabled,
            'sms', np.sms_enabled,
            'email', np.email_enabled
        )
    )
    FROM notification_preferences np
    WHERE np.user_id = p.user_id
)
WHERE EXISTS (
    SELECT 1 FROM notification_preferences np WHERE np.user_id = p.user_id
);

-- ============================================
-- Step 5: Add Comments
-- ============================================

COMMENT ON TABLE user_preferences IS 'Unified table for all user preferences including language, formatting, notifications, and UI settings';
COMMENT ON TABLE auth_tokens IS 'Unified table for all authentication tokens including sessions, password resets, email verification, and API keys';
COMMENT ON TABLE user_security IS 'Unified table for user security settings including 2FA, trusted devices, and account security';

-- ============================================
-- Migration Complete
-- ============================================