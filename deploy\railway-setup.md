# Railway Deployment Guide for TutorAide

## 🚀 Quick Deploy on Railway

### Prerequisites
- Railway account (https://railway.app)
- GitHub repository connected
- Environment variables ready

### Step 1: Create Railway Project
```bash
# Install Railway CLI
npm install -g @railway/cli

# Login to Railway
railway login

# Create new project
railway init
```

### Step 2: Add PostgreSQL Database
1. In Railway dashboard, click "New Service"
2. Select "Database" → "PostgreSQL"
3. Note the connection string in variables

### Step 3: Deploy Backend Service
1. Click "New Service" → "GitHub Repo"
2. Configure:
   ```yaml
   Root Directory: /app
   Build Command: pip install -r requirements.txt
   Start Command: uvicorn main:app --host 0.0.0.0 --port $PORT
   ```

3. Add environment variables:
   ```env
   DATABASE_URL=${{Postgres.DATABASE_URL}}
   SECRET_KEY=your-secret-key-here
   ENVIRONMENT=production
   CORS_ORIGINS=https://tutoraide.ca,https://www.tutoraide.ca
   
   # Email Config
   SMTP_HOST=smtp.gmail.com
   SMTP_PORT=587
   SMTP_USER=<EMAIL>
   SMTP_PASSWORD=your-app-password
   
   # Stripe
   STRIPE_SECRET_KEY=sk_live_...
   STRIPE_WEBHOOK_SECRET=whsec_...
   
   # Twilio
   TWILIO_ACCOUNT_SID=AC...
   TWILIO_AUTH_TOKEN=...
   TWILIO_PHONE_NUMBER=+1...
   
   # OneSignal
   ONESIGNAL_APP_ID=...
   ONESIGNAL_REST_API_KEY=...
   ```

### Step 4: Deploy Frontend Service
1. Click "New Service" → "GitHub Repo" (same repo)
2. Configure:
   ```yaml
   Root Directory: /app/frontend
   Build Command: npm install && npm run build
   Start Command: npm install -g serve && serve -s dist -l $PORT
   ```

3. Add environment variables:
   ```env
   VITE_API_URL=${{Backend.RAILWAY_PUBLIC_URL}}
   NODE_ENV=production
   ```

### Step 5: Configure Custom Domains
1. Backend: `api.tutoraide.ca`
2. Frontend: `app.tutoraide.ca` or `tutoraide.ca`

### Step 6: Setup SSL & Health Checks
Railway provides automatic SSL. Add health endpoints:

Backend (`/app/api/health.py`):
```python
@router.get("/health")
async def health_check():
    return {"status": "healthy"}
```

## 🔧 Environment-Specific Configs

### Production Frontend (.env.production)
```env
VITE_API_URL=https://api.tutoraide.ca
VITE_GOOGLE_CLIENT_ID=your-google-client-id
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_...
VITE_ONESIGNAL_APP_ID=...
```

### Production Backend (.env.production)
```env
DATABASE_URL=postgresql://...
REDIS_URL=redis://...
ENVIRONMENT=production
DEBUG=false
```

## 📊 Monitoring & Logs

### Railway Dashboard
- Real-time logs
- Metrics (CPU, Memory, Network)
- Deployment history
- Environment variables

### Custom Monitoring
```python
# Add to backend
import sentry_sdk
sentry_sdk.init(
    dsn="your-sentry-dsn",
    environment="production"
)
```

## 🚨 Rollback Strategy

```bash
# Via CLI
railway rollback

# Via Dashboard
# Click deployment → "Rollback to this deployment"
```

## 💰 Cost Optimization

### Railway Pricing (as of 2024)
- $5/month subscription includes:
  - $5 of usage
  - Unlimited projects
  - Custom domains

### Estimated Monthly Costs
- Backend: ~$5-10
- Frontend: ~$5
- PostgreSQL: ~$5-10
- Total: ~$15-25/month

### Cost Saving Tips
1. Use sleep mode for staging
2. Optimize database queries
3. Cache static assets
4. Use CDN for images

## 🔄 CI/CD Pipeline

The GitHub Actions workflow automatically:
1. Runs tests on push to main
2. Deploys to Railway on success
3. Sends notifications on failure

### Manual Deploy
```bash
# Deploy all services
railway up

# Deploy specific service
railway up -s backend
railway up -s frontend
```

## 🛠️ Troubleshooting

### Common Issues

1. **Build Failures**
   - Check logs: `railway logs`
   - Verify package versions
   - Check environment variables

2. **Database Connection**
   - Ensure DATABASE_URL is set
   - Check connection pooling
   - Verify SSL mode

3. **CORS Issues**
   - Update CORS_ORIGINS
   - Check API URL in frontend
   - Verify headers

### Debug Commands
```bash
# View logs
railway logs -s backend
railway logs -s frontend

# SSH into service
railway run bash

# Check environment
railway env
```

## 🎯 Next Steps

1. Set up staging environment
2. Configure automated backups
3. Implement monitoring alerts
4. Set up CDN for assets
5. Configure rate limiting

## 📚 Resources

- [Railway Docs](https://docs.railway.app)
- [Railway Templates](https://railway.app/templates)
- [Railway Discord](https://discord.gg/railway)