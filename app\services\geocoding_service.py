"""
Geocoding service for postal code to coordinate conversion and distance calculations.
"""

import logging
import math
import os
from typing import Op<PERSON>, Di<PERSON>, Tu<PERSON>, List
import httpx
from datetime import timedelta
# from cachetools import TTLCache  # TODO: Install cachetools
# from sqlalchemy.ext.asyncio import AsyncSession
# from sqlalchemy import text

from app.core.exceptions import ExternalServiceError, ValidationError

logger = logging.getLogger(__name__)


class GeocodingService:
    """Service for geocoding postal codes and calculating distances."""
    
    def __init__(self, db_connection=None):
        self.db = db_connection
        
        # External API configuration
        self.google_maps_api_key = os.getenv('GOOGLE_MAPS_API_KEY')
        self.mapbox_api_key = os.getenv('MAPBOX_API_KEY')
        self.enable_external_geocoding = os.getenv('ENABLE_EXTERNAL_GEOCODING', 'false').lower() == 'true'
        
        # Cache geocoding results for 24 hours to reduce API calls
        # TODO: Replace with TTLCache when cachetools is installed
        self._geocode_cache = {}  # TTLCache(maxsize=1000, ttl=timedelta(hours=24).total_seconds())
        # Cache distance calculations for 1 hour
        self._distance_cache = {}  # TTLCache(maxsize=5000, ttl=timedelta(hours=1).total_seconds())
        
        # HTTP client for external API calls
        self._http_client = httpx.AsyncClient(timeout=30.0)
        
        # Quebec postal code prefixes with approximate coordinates
        self._quebec_postal_prefixes = {
            # Montreal area
            'H1A': {'lat': 45.6575, 'lng': -73.5077, 'area': 'Pointe-aux-Trembles'},
            'H1B': {'lat': 45.6411, 'lng': -73.5160, 'area': 'Rivière-des-Prairies'},
            'H1C': {'lat': 45.6226, 'lng': -73.5076, 'area': 'Rivière-des-Prairies'},
            'H1E': {'lat': 45.6273, 'lng': -73.5877, 'area': 'Anjou'},
            'H1G': {'lat': 45.6027, 'lng': -73.5648, 'area': 'Anjou'},
            'H1H': {'lat': 45.6469, 'lng': -73.5544, 'area': 'Montreal-Nord'},
            'H1J': {'lat': 45.6089, 'lng': -73.5827, 'area': 'Saint-Léonard'},
            'H1K': {'lat': 45.6566, 'lng': -73.5821, 'area': 'Ahuntsic'},
            'H1L': {'lat': 45.5826, 'lng': -73.5365, 'area': 'Mercier'},
            'H1M': {'lat': 45.5992, 'lng': -73.5426, 'area': 'Mercier'},
            'H1N': {'lat': 45.5688, 'lng': -73.5980, 'area': 'Hochelaga'},
            'H1P': {'lat': 45.6154, 'lng': -73.5672, 'area': 'Saint-Léonard'},
            'H1R': {'lat': 45.5930, 'lng': -73.5850, 'area': 'Saint-Léonard'},
            'H1S': {'lat': 45.5888, 'lng': -73.5650, 'area': 'Rosemont'},
            'H1T': {'lat': 45.5710, 'lng': -73.5766, 'area': 'Rosemont'},
            'H1V': {'lat': 45.5436, 'lng': -73.6059, 'area': 'Ville-Marie'},
            'H1W': {'lat': 45.5485, 'lng': -73.5856, 'area': 'Hochelaga'},
            'H1X': {'lat': 45.5543, 'lng': -73.5658, 'area': 'Rosemont'},
            'H1Y': {'lat': 45.5618, 'lng': -73.5507, 'area': 'Rosemont'},
            'H1Z': {'lat': 45.5742, 'lng': -73.5347, 'area': 'Mercier'},
            
            # Downtown Montreal
            'H2A': {'lat': 45.5149, 'lng': -73.5619, 'area': 'Old Montreal'},
            'H2B': {'lat': 45.5237, 'lng': -73.5672, 'area': 'Downtown'},
            'H2C': {'lat': 45.5073, 'lng': -73.5604, 'area': 'Old Montreal'},
            'H2E': {'lat': 45.5267, 'lng': -73.5519, 'area': 'Centre-Sud'},
            'H2G': {'lat': 45.5341, 'lng': -73.5638, 'area': 'Plateau'},
            'H2H': {'lat': 45.5328, 'lng': -73.5756, 'area': 'Plateau'},
            'H2J': {'lat': 45.5250, 'lng': -73.5817, 'area': 'Plateau'},
            'H2K': {'lat': 45.5316, 'lng': -73.5472, 'area': 'Centre-Sud'},
            'H2L': {'lat': 45.5238, 'lng': -73.5568, 'area': 'Centre-Sud'},
            'H2M': {'lat': 45.5355, 'lng': -73.5969, 'area': 'Outremont'},
            'H2N': {'lat': 45.5466, 'lng': -73.6222, 'area': 'Parc Extension'},
            'H2P': {'lat': 45.5580, 'lng': -73.6286, 'area': 'Villeray'},
            'H2R': {'lat': 45.5498, 'lng': -73.6118, 'area': 'Villeray'},
            'H2S': {'lat': 45.5299, 'lng': -73.6056, 'area': 'Mile End'},
            'H2T': {'lat': 45.5269, 'lng': -73.5953, 'area': 'Mile End'},
            'H2V': {'lat': 45.5151, 'lng': -73.6108, 'area': 'Outremont'},
            'H2W': {'lat': 45.5172, 'lng': -73.5839, 'area': 'Mile End'},
            'H2X': {'lat': 45.5088, 'lng': -73.5542, 'area': 'Downtown'},
            'H2Y': {'lat': 45.5033, 'lng': -73.5592, 'area': 'Old Montreal'},
            'H2Z': {'lat': 45.4975, 'lng': -73.5562, 'area': 'Old Montreal'},
            
            # McGill/Westmount area
            'H3A': {'lat': 45.5048, 'lng': -73.5772, 'area': 'McGill'},
            'H3B': {'lat': 45.4976, 'lng': -73.5789, 'area': 'Downtown'},
            'H3C': {'lat': 45.4897, 'lng': -73.5874, 'area': 'Griffintown'},
            'H3E': {'lat': 45.4756, 'lng': -73.5981, 'area': 'Verdun'},
            'H3G': {'lat': 45.5005, 'lng': -73.5886, 'area': 'Downtown West'},
            'H3H': {'lat': 45.4938, 'lng': -73.5869, 'area': 'Westmount'},
            'H3J': {'lat': 45.4803, 'lng': -73.5883, 'area': 'Saint-Henri'},
            'H3K': {'lat': 45.4713, 'lng': -73.5665, 'area': 'Pointe-Saint-Charles'},
            'H3L': {'lat': 45.5165, 'lng': -73.6356, 'area': 'Ahuntsic'},
            'H3M': {'lat': 45.5087, 'lng': -73.6388, 'area': 'Cartierville'},
            'H3N': {'lat': 45.5270, 'lng': -73.6249, 'area': 'Parc Extension'},
            'H3P': {'lat': 45.5021, 'lng': -73.6533, 'area': 'Mont-Royal'},
            'H3R': {'lat': 45.5161, 'lng': -73.6610, 'area': 'Côte-des-Neiges'},
            'H3S': {'lat': 45.4864, 'lng': -73.6321, 'area': 'Côte-des-Neiges'},
            'H3T': {'lat': 45.4955, 'lng': -73.6189, 'area': 'Côte-des-Neiges'},
            'H3V': {'lat': 45.4755, 'lng': -73.6198, 'area': 'Côte-des-Neiges'},
            'H3W': {'lat': 45.4850, 'lng': -73.6394, 'area': 'Côte-des-Neiges'},
            'H3X': {'lat': 45.4940, 'lng': -73.6505, 'area': 'Hampstead'},
            'H3Y': {'lat': 45.4821, 'lng': -73.6047, 'area': 'Westmount'},
            'H3Z': {'lat': 45.4689, 'lng': -73.5958, 'area': 'Westmount'},
            
            # NDG/West area
            'H4A': {'lat': 45.4730, 'lng': -73.6051, 'area': 'Notre-Dame-de-Grâce'},
            'H4B': {'lat': 45.4563, 'lng': -73.6178, 'area': 'Notre-Dame-de-Grâce'},
            'H4C': {'lat': 45.4583, 'lng': -73.5841, 'area': 'Saint-Henri'},
            'H4E': {'lat': 45.4391, 'lng': -73.5866, 'area': 'Verdun'},
            'H4G': {'lat': 45.4555, 'lng': -73.5725, 'area': 'Verdun'},
            'H4H': {'lat': 45.4409, 'lng': -73.5684, 'area': 'Verdun'},
            'H4J': {'lat': 45.4672, 'lng': -73.6614, 'area': 'Montreal-Ouest'},
            'H4K': {'lat': 45.4235, 'lng': -73.6125, 'area': 'LaSalle'},
            'H4L': {'lat': 45.4951, 'lng': -73.6679, 'area': 'Saint-Laurent'},
            'H4M': {'lat': 45.5095, 'lng': -73.6765, 'area': 'Saint-Laurent'},
            'H4N': {'lat': 45.5239, 'lng': -73.6851, 'area': 'Saint-Laurent'},
            'H4P': {'lat': 45.4911, 'lng': -73.6767, 'area': 'Mont-Royal'},
            'H4R': {'lat': 45.5378, 'lng': -73.7016, 'area': 'Saint-Laurent'},
            'H4S': {'lat': 45.5521, 'lng': -73.7183, 'area': 'Saint-Laurent'},
            'H4T': {'lat': 45.5233, 'lng': -73.7125, 'area': 'Saint-Laurent'},
            'H4V': {'lat': 45.4802, 'lng': -73.6498, 'area': 'Côte-Saint-Luc'},
            'H4W': {'lat': 45.4655, 'lng': -73.6387, 'area': 'Côte-Saint-Luc'},
            'H4X': {'lat': 45.4543, 'lng': -73.6528, 'area': 'Hampstead'},
            
            # Laval
            'H7A': {'lat': 45.5969, 'lng': -73.7556, 'area': 'Duvernay'},
            'H7B': {'lat': 45.5825, 'lng': -73.7412, 'area': 'Pont-Viau'},
            'H7C': {'lat': 45.5682, 'lng': -73.7269, 'area': 'Chomedey'},
            'H7E': {'lat': 45.5754, 'lng': -73.7125, 'area': 'Laval-des-Rapides'},
            'H7G': {'lat': 45.5611, 'lng': -73.6982, 'area': 'Pont-Viau'},
            'H7H': {'lat': 45.5468, 'lng': -73.6839, 'area': 'Chomedey'},
            'H7J': {'lat': 45.5325, 'lng': -73.6696, 'area': 'Chomedey'},
            'H7K': {'lat': 45.5902, 'lng': -73.6553, 'area': 'Auteuil'},
            'H7L': {'lat': 45.6045, 'lng': -73.6410, 'area': 'Vimont'},
            'H7M': {'lat': 45.6188, 'lng': -73.6267, 'area': 'Vimont'},
            'H7N': {'lat': 45.5650, 'lng': -73.7633, 'area': 'Chomedey'},
            'H7P': {'lat': 45.5793, 'lng': -73.7776, 'area': 'Fabreville'},
            'H7R': {'lat': 45.5936, 'lng': -73.7919, 'area': 'Fabreville'},
            'H7S': {'lat': 45.5507, 'lng': -73.7490, 'area': 'Chomedey'},
            'H7T': {'lat': 45.5364, 'lng': -73.7347, 'area': 'Chomedey'},
            'H7V': {'lat': 45.5221, 'lng': -73.7204, 'area': 'Chomedey'},
            'H7W': {'lat': 45.5636, 'lng': -73.8062, 'area': 'Sainte-Rose'},
            'H7X': {'lat': 45.5779, 'lng': -73.8205, 'area': 'Sainte-Rose'},
            'H7Y': {'lat': 45.5922, 'lng': -73.8348, 'area': 'Fabreville'},
            
            # South Shore
            'J3Y': {'lat': 45.4924, 'lng': -73.4850, 'area': 'Saint-Hubert'},
            'J4B': {'lat': 45.5170, 'lng': -73.4606, 'area': 'Boucherville'},
            'J4G': {'lat': 45.5414, 'lng': -73.4362, 'area': 'Longueuil'},
            'J4H': {'lat': 45.5307, 'lng': -73.5183, 'area': 'Longueuil'},
            'J4J': {'lat': 45.5201, 'lng': -73.5004, 'area': 'Longueuil'},
            'J4K': {'lat': 45.5095, 'lng': -73.4825, 'area': 'Longueuil'},
            'J4L': {'lat': 45.4543, 'lng': -73.4646, 'area': 'Saint-Lambert'},
            'J4M': {'lat': 45.4989, 'lng': -73.4467, 'area': 'Longueuil'},
            'J4P': {'lat': 45.4437, 'lng': -73.4824, 'area': 'Saint-Lambert'},
            'J4R': {'lat': 45.4331, 'lng': -73.5003, 'area': 'Greenfield Park'},
            'J4S': {'lat': 45.4683, 'lng': -73.4288, 'area': 'Saint-Lambert'},
            'J4T': {'lat': 45.4578, 'lng': -73.4467, 'area': 'Saint-Hubert'},
            'J4V': {'lat': 45.4225, 'lng': -73.5181, 'area': 'LeMoyne'},
            'J4W': {'lat': 45.5037, 'lng': -73.4109, 'area': 'Saint-Bruno'},
            'J4X': {'lat': 45.5290, 'lng': -73.3866, 'area': 'Boucherville'},
            'J4Y': {'lat': 45.4472, 'lng': -73.4109, 'area': 'Saint-Hubert'},
            'J4Z': {'lat': 45.4119, 'lng': -73.4288, 'area': 'Greenfield Park'},
        }
    
    async def geocode_postal_code(self, postal_code: str) -> Optional[Dict[str, float]]:
        """
        Convert a postal code to latitude/longitude coordinates.
        
        Args:
            postal_code: Canadian postal code (e.g., "H2X 1Y9")
            
        Returns:
            Dict with 'lat' and 'lng' keys, or None if not found
        """
        try:
            # Normalize postal code
            postal_code = postal_code.upper().replace(' ', '')
            if len(postal_code) < 3:
                raise ValidationError("Invalid postal code format")
            
            # Check cache first
            if postal_code in self._geocode_cache:
                return self._geocode_cache[postal_code]
            
            # Check database cache if available
            if self.db:
                db_coords = await self._get_postal_code_from_db(postal_code)
                if db_coords:
                    self._geocode_cache[postal_code] = db_coords
                    return db_coords
            
            # Get prefix (first 3 characters)
            prefix = postal_code[:3]
            
            # Look up in Quebec postal codes (fallback)
            if prefix in self._quebec_postal_prefixes:
                coords = {
                    'lat': self._quebec_postal_prefixes[prefix]['lat'],
                    'lng': self._quebec_postal_prefixes[prefix]['lng'],
                    'area': self._quebec_postal_prefixes[prefix]['area']
                }
                
                # Add small random offset for specific postal codes within the prefix
                # This simulates more precise geocoding
                if len(postal_code) >= 6:
                    import hashlib
                    hash_val = int(hashlib.md5(postal_code.encode()).hexdigest()[:8], 16)
                    lat_offset = (hash_val % 1000 - 500) / 100000  # ±0.005 degrees
                    lng_offset = ((hash_val // 1000) % 1000 - 500) / 100000
                    coords['lat'] += lat_offset
                    coords['lng'] += lng_offset
                
                # Cache and optionally save to database
                self._geocode_cache[postal_code] = coords
                if self.db:
                    await self._save_postal_code_to_db(postal_code, coords)
                
                return coords
            
            # Try external geocoding API if enabled
            if self.enable_external_geocoding:
                external_coords = await self._geocode_with_external_api(postal_code)
                if external_coords:
                    self._geocode_cache[postal_code] = external_coords
                    if self.db:
                        await self._save_postal_code_to_db(postal_code, external_coords)
                    return external_coords
            
            # Not found
            logger.warning(f"Postal code {postal_code} not found in any geocoding source")
            return None
            
        except Exception as e:
            logger.error(f"Error geocoding postal code {postal_code}: {e}")
            return None
    
    async def _get_postal_code_from_db(self, postal_code: str) -> Optional[Dict[str, float]]:
        """Get postal code coordinates from database."""
        try:
            query = text("""
                SELECT latitude, longitude, city, province, area
                FROM postal_code_coordinates
                WHERE postal_code = :postal_code
            """)
            
            result = await self.db.execute(query, {"postal_code": postal_code})
            row = result.fetchone()
            
            if row:
                return {
                    'lat': float(row.latitude),
                    'lng': float(row.longitude),
                    'city': row.city,
                    'province': row.province,
                    'area': row.area
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting postal code from database: {e}")
            return None
    
    async def _save_postal_code_to_db(self, postal_code: str, coords: Dict[str, float]):
        """Save postal code coordinates to database."""
        try:
            query = text("""
                INSERT INTO postal_code_coordinates (
                    postal_code, latitude, longitude, city, province, area, created_at
                ) VALUES (
                    :postal_code, :latitude, :longitude, :city, :province, :area, CURRENT_TIMESTAMP
                )
                ON CONFLICT (postal_code) DO UPDATE SET
                    latitude = EXCLUDED.latitude,
                    longitude = EXCLUDED.longitude,
                    city = EXCLUDED.city,
                    province = EXCLUDED.province,
                    area = EXCLUDED.area,
                    updated_at = CURRENT_TIMESTAMP
            """)
            
            await self.db.execute(query, {
                "postal_code": postal_code,
                "latitude": coords['lat'],
                "longitude": coords['lng'],
                "city": coords.get('city', ''),
                "province": coords.get('province', 'QC'),
                "area": coords.get('area', '')
            })
            
            await self.db.commit()
            
        except Exception as e:
            logger.error(f"Error saving postal code to database: {e}")
            await self.db.rollback()
    
    async def _geocode_with_external_api(self, postal_code: str) -> Optional[Dict[str, float]]:
        """Geocode using external API (Google Maps or Mapbox)."""
        try:
            # Try Google Maps API first
            if self.google_maps_api_key:
                return await self._geocode_with_google_maps(postal_code)
            
            # Fall back to Mapbox
            if self.mapbox_api_key:
                return await self._geocode_with_mapbox(postal_code)
            
            return None
            
        except Exception as e:
            logger.error(f"Error with external geocoding API: {e}")
            return None
    
    async def _geocode_with_google_maps(self, postal_code: str) -> Optional[Dict[str, float]]:
        """Geocode using Google Maps Geocoding API."""
        try:
            formatted_postal = f"{postal_code[:3]} {postal_code[3:]}" if len(postal_code) >= 6 else postal_code
            
            url = "https://maps.googleapis.com/maps/api/geocode/json"
            params = {
                'address': f"{formatted_postal}, Canada",
                'key': self.google_maps_api_key,
                'region': 'ca',
                'components': 'country:CA'
            }
            
            response = await self._http_client.get(url, params=params)
            data = response.json()
            
            if data['status'] == 'OK' and data['results']:
                location = data['results'][0]['geometry']['location']
                
                # Extract additional info
                components = data['results'][0].get('address_components', [])
                city = province = area = ''
                
                for component in components:
                    types = component['types']
                    if 'locality' in types:
                        city = component['long_name']
                    elif 'administrative_area_level_1' in types:
                        province = component['short_name']
                    elif 'sublocality' in types:
                        area = component['long_name']
                
                return {
                    'lat': location['lat'],
                    'lng': location['lng'],
                    'city': city,
                    'province': province,
                    'area': area,
                    'source': 'google_maps'
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error with Google Maps geocoding: {e}")
            return None
    
    async def _geocode_with_mapbox(self, postal_code: str) -> Optional[Dict[str, float]]:
        """Geocode using Mapbox Geocoding API."""
        try:
            formatted_postal = f"{postal_code[:3]} {postal_code[3:]}" if len(postal_code) >= 6 else postal_code
            
            url = f"https://api.mapbox.com/geocoding/v5/mapbox.places/{formatted_postal}.json"
            params = {
                'access_token': self.mapbox_api_key,
                'country': 'ca',
                'types': 'postcode',
                'limit': 1
            }
            
            response = await self._http_client.get(url, params=params)
            data = response.json()
            
            if data['features']:
                feature = data['features'][0]
                coords = feature['geometry']['coordinates']  # [lng, lat]
                
                # Extract place name
                place_name = feature.get('place_name', '')
                parts = place_name.split(', ')
                
                return {
                    'lat': coords[1],
                    'lng': coords[0],
                    'city': parts[1] if len(parts) > 1 else '',
                    'province': parts[2] if len(parts) > 2 else 'QC',
                    'area': parts[0] if parts else '',
                    'source': 'mapbox'
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error with Mapbox geocoding: {e}")
            return None
    
    def calculate_distance(
        self,
        coord1: Dict[str, float],
        coord2: Dict[str, float]
    ) -> float:
        """
        Calculate distance between two coordinates using Haversine formula.
        
        Args:
            coord1: Dict with 'lat' and 'lng' keys
            coord2: Dict with 'lat' and 'lng' keys
            
        Returns:
            Distance in kilometers
        """
        # Earth's radius in kilometers
        R = 6371.0
        
        # Convert to radians
        lat1 = math.radians(coord1['lat'])
        lon1 = math.radians(coord1['lng'])
        lat2 = math.radians(coord2['lat'])
        lon2 = math.radians(coord2['lng'])
        
        # Haversine formula
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))
        
        distance = R * c
        return round(distance, 2)
    
    async def calculate_distance_between_postal_codes(
        self,
        postal_code1: str,
        postal_code2: str
    ) -> Optional[float]:
        """
        Calculate distance between two postal codes.
        
        Args:
            postal_code1: First postal code
            postal_code2: Second postal code
            
        Returns:
            Distance in kilometers, or None if geocoding fails
        """
        # Check cache first
        cache_key = f"{postal_code1}:{postal_code2}"
        if cache_key in self._distance_cache:
            return self._distance_cache[cache_key]
        
        # Also check reverse key
        reverse_key = f"{postal_code2}:{postal_code1}"
        if reverse_key in self._distance_cache:
            return self._distance_cache[reverse_key]
        
        # Geocode both postal codes
        coord1 = await self.geocode_postal_code(postal_code1)
        coord2 = await self.geocode_postal_code(postal_code2)
        
        if not coord1 or not coord2:
            return None
        
        # Calculate distance
        distance = self.calculate_distance(coord1, coord2)
        
        # Cache the result
        self._distance_cache[cache_key] = distance
        
        return distance
    
    async def find_postal_codes_within_radius(
        self,
        center_postal_code: str,
        radius_km: float
    ) -> List[str]:
        """
        Find all postal codes within a given radius of a center postal code.
        
        Args:
            center_postal_code: Center postal code
            radius_km: Search radius in kilometers
            
        Returns:
            List of postal code prefixes within radius
        """
        center_coords = await self.geocode_postal_code(center_postal_code)
        if not center_coords:
            return []
        
        nearby_prefixes = []
        
        for prefix, coords in self._quebec_postal_prefixes.items():
            distance = self.calculate_distance(center_coords, coords)
            if distance <= radius_km:
                nearby_prefixes.append(prefix)
        
        return sorted(nearby_prefixes)
    
    def estimate_travel_time(
        self,
        distance_km: float,
        travel_mode: str = 'driving'
    ) -> int:
        """
        Estimate travel time based on distance and mode of transport.
        
        Args:
            distance_km: Distance in kilometers
            travel_mode: 'driving', 'transit', or 'walking'
            
        Returns:
            Estimated time in minutes
        """
        # Average speeds in km/h for Montreal area
        speeds = {
            'driving': 25,    # City driving with traffic
            'transit': 20,    # Including wait times
            'walking': 5      # Average walking speed
        }
        
        speed = speeds.get(travel_mode, 25)
        
        # Calculate base time
        time_hours = distance_km / speed
        time_minutes = int(time_hours * 60)
        
        # Add buffer time based on mode
        if travel_mode == 'transit':
            time_minutes += 10  # Wait time
        elif travel_mode == 'driving':
            time_minutes += 5   # Parking time
        
        return max(time_minutes, 5)  # Minimum 5 minutes
    
    def get_area_name(self, postal_code: str) -> Optional[str]:
        """Get the area/neighborhood name for a postal code."""
        prefix = postal_code.upper().replace(' ', '')[:3]
        if prefix in self._quebec_postal_prefixes:
            return self._quebec_postal_prefixes[prefix].get('area')
        return None
    
    async def batch_geocode_postal_codes(self, postal_codes: List[str]) -> Dict[str, Optional[Dict[str, float]]]:
        """
        Geocode multiple postal codes efficiently.
        
        Args:
            postal_codes: List of postal codes to geocode
            
        Returns:
            Dictionary mapping postal code to coordinates
        """
        results = {}
        
        # Group by what needs external API vs local lookup
        local_codes = []
        external_codes = []
        
        for postal_code in postal_codes:
            normalized = postal_code.upper().replace(' ', '')
            
            # Check cache first
            if normalized in self._geocode_cache:
                results[postal_code] = self._geocode_cache[normalized]
                continue
            
            # Check if in local database
            prefix = normalized[:3]
            if prefix in self._quebec_postal_prefixes:
                local_codes.append(postal_code)
            else:
                external_codes.append(postal_code)
        
        # Process local codes
        for postal_code in local_codes:
            coords = await self.geocode_postal_code(postal_code)
            results[postal_code] = coords
        
        # Process external codes if enabled
        if self.enable_external_geocoding and external_codes:
            # Batch process external codes to reduce API calls
            for postal_code in external_codes:
                coords = await self.geocode_postal_code(postal_code)
                results[postal_code] = coords
        
        return results
    
    async def find_nearest_locations(
        self,
        center_coords: Dict[str, float],
        candidate_locations: List[Dict[str, any]],
        max_distance_km: Optional[float] = None,
        limit: int = 5
    ) -> List[Dict[str, any]]:
        """
        Find nearest locations to a center point.
        
        Args:
            center_coords: Center point coordinates {'lat': x, 'lng': y}
            candidate_locations: List of locations with coordinates
            max_distance_km: Maximum distance filter
            limit: Maximum number of results
            
        Returns:
            List of locations sorted by distance with distance added
        """
        locations_with_distance = []
        
        for location in candidate_locations:
            if 'lat' not in location or 'lng' not in location:
                continue
            
            distance = self.calculate_distance(
                center_coords,
                {'lat': location['lat'], 'lng': location['lng']}
            )
            
            # Apply distance filter
            if max_distance_km and distance > max_distance_km:
                continue
            
            location_copy = location.copy()
            location_copy['distance_km'] = distance
            locations_with_distance.append(location_copy)
        
        # Sort by distance and limit results
        locations_with_distance.sort(key=lambda x: x['distance_km'])
        
        return locations_with_distance[:limit]
    
    async def get_postal_codes_in_radius(
        self,
        center_postal_code: str,
        radius_km: float,
        province: str = 'QC'
    ) -> List[Dict[str, any]]:
        """
        Get all postal codes within radius of center postal code.
        
        Args:
            center_postal_code: Center postal code
            radius_km: Search radius in kilometers
            province: Province to search (default QC)
            
        Returns:
            List of postal codes with distances
        """
        center_coords = await self.geocode_postal_code(center_postal_code)
        if not center_coords:
            return []
        
        results = []
        
        # Search in hardcoded Quebec postal codes
        for prefix, coords in self._quebec_postal_prefixes.items():
            distance = self.calculate_distance(center_coords, coords)
            if distance <= radius_km:
                results.append({
                    'postal_code': prefix,
                    'lat': coords['lat'],
                    'lng': coords['lng'],
                    'area': coords['area'],
                    'distance_km': distance
                })
        
        # Also search database if available
        if self.db:
            db_results = await self._get_postal_codes_in_radius_from_db(
                center_coords, radius_km, province
            )
            results.extend(db_results)
        
        # Remove duplicates and sort by distance
        unique_results = {}
        for result in results:
            postal_code = result['postal_code']
            if postal_code not in unique_results or result['distance_km'] < unique_results[postal_code]['distance_km']:
                unique_results[postal_code] = result
        
        sorted_results = sorted(unique_results.values(), key=lambda x: x['distance_km'])
        return sorted_results
    
    async def _get_postal_codes_in_radius_from_db(
        self,
        center_coords: Dict[str, float],
        radius_km: float,
        province: str
    ) -> List[Dict[str, any]]:
        """Get postal codes within radius from database using spatial query."""
        try:
            # Use Haversine formula in SQL for efficiency
            query = text("""
                SELECT postal_code, latitude, longitude, city, area,
                       (6371 * acos(
                           cos(radians(:center_lat)) * 
                           cos(radians(latitude)) * 
                           cos(radians(longitude) - radians(:center_lng)) + 
                           sin(radians(:center_lat)) * 
                           sin(radians(latitude))
                       )) AS distance_km
                FROM postal_code_coordinates
                WHERE province = :province
                  AND (6371 * acos(
                          cos(radians(:center_lat)) * 
                          cos(radians(latitude)) * 
                          cos(radians(longitude) - radians(:center_lng)) + 
                          sin(radians(:center_lat)) * 
                          sin(radians(latitude))
                      )) <= :radius_km
                ORDER BY distance_km
            """)
            
            result = await self.db.execute(query, {
                "center_lat": center_coords['lat'],
                "center_lng": center_coords['lng'],
                "radius_km": radius_km,
                "province": province
            })
            
            results = []
            for row in result.fetchall():
                results.append({
                    'postal_code': row.postal_code,
                    'lat': float(row.latitude),
                    'lng': float(row.longitude),
                    'city': row.city,
                    'area': row.area,
                    'distance_km': float(row.distance_km)
                })
            
            return results
            
        except Exception as e:
            logger.error(f"Error getting postal codes from database: {e}")
            return []
    
    async def validate_postal_code_format(self, postal_code: str, country: str = 'CA') -> bool:
        """
        Validate postal code format.
        
        Args:
            postal_code: Postal code to validate
            country: Country code (CA for Canada)
            
        Returns:
            True if format is valid
        """
        import re
        
        if country == 'CA':
            # Canadian postal code format: A1A 1A1 or A1A1A1
            pattern = r'^[A-Za-z]\d[A-Za-z]\s?\d[A-Za-z]\d$'
            return bool(re.match(pattern, postal_code.strip()))
        
        # Add other country formats as needed
        return False
    
    async def get_route_distance_and_duration(
        self,
        origin_postal_code: str,
        destination_postal_code: str,
        mode: str = 'driving'
    ) -> Optional[Dict[str, any]]:
        """
        Get route distance and duration between two postal codes.
        
        Args:
            origin_postal_code: Starting postal code
            destination_postal_code: Ending postal code
            mode: Travel mode (driving, walking, transit)
            
        Returns:
            Dict with distance_km, duration_minutes, and route info
        """
        try:
            # Get coordinates for both postal codes
            origin_coords = await self.geocode_postal_code(origin_postal_code)
            dest_coords = await self.geocode_postal_code(destination_postal_code)
            
            if not origin_coords or not dest_coords:
                return None
            
            # Calculate straight-line distance
            straight_distance = self.calculate_distance(origin_coords, dest_coords)
            
            # If external routing API is available, use it
            if self.enable_external_geocoding and self.google_maps_api_key:
                route_info = await self._get_route_from_google_maps(
                    origin_coords, dest_coords, mode
                )
                if route_info:
                    return route_info
            
            # Fall back to estimation based on straight-line distance
            estimated_duration = self.estimate_travel_time(straight_distance, mode)
            
            # Road distance is typically 1.2-1.5x straight-line distance in urban areas
            road_multiplier = 1.3 if mode == 'driving' else 1.2
            estimated_distance = straight_distance * road_multiplier
            
            return {
                'distance_km': round(estimated_distance, 2),
                'duration_minutes': estimated_duration,
                'straight_line_distance_km': straight_distance,
                'mode': mode,
                'estimated': True
            }
            
        except Exception as e:
            logger.error(f"Error getting route information: {e}")
            return None
    
    async def _get_route_from_google_maps(
        self,
        origin_coords: Dict[str, float],
        dest_coords: Dict[str, float],
        mode: str
    ) -> Optional[Dict[str, any]]:
        """Get route information from Google Maps Directions API."""
        try:
            url = "https://maps.googleapis.com/maps/api/directions/json"
            params = {
                'origin': f"{origin_coords['lat']},{origin_coords['lng']}",
                'destination': f"{dest_coords['lat']},{dest_coords['lng']}",
                'mode': mode,
                'key': self.google_maps_api_key,
                'region': 'ca',
                'units': 'metric'
            }
            
            response = await self._http_client.get(url, params=params)
            data = response.json()
            
            if data['status'] == 'OK' and data['routes']:
                route = data['routes'][0]
                leg = route['legs'][0]
                
                distance_km = leg['distance']['value'] / 1000  # Convert meters to km
                duration_minutes = leg['duration']['value'] / 60  # Convert seconds to minutes
                
                return {
                    'distance_km': round(distance_km, 2),
                    'duration_minutes': int(duration_minutes),
                    'mode': mode,
                    'estimated': False,
                    'route_summary': route.get('summary', ''),
                    'traffic_info': leg.get('duration_in_traffic', {})
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting route from Google Maps: {e}")
            return None
    
    async def close(self):
        """Close HTTP client connections."""
        if hasattr(self, '_http_client'):
            await self._http_client.aclose()