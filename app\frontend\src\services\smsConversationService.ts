import api from './api';
import { UserRoleType } from '../types/auth';

// SMS Conversation Types and Interfaces
export enum MessageStatus {
  PENDING = 'pending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  FAILED = 'failed',
  READ = 'read'
}

export enum MessageDirection {
  INBOUND = 'inbound',
  OUTBOUND = 'outbound'
}

export enum ConversationStatus {
  ACTIVE = 'active',
  RESOLVED = 'resolved',
  ARCHIVED = 'archived',
  SPAM = 'spam'
}

export interface SMSMessage {
  message_id: number;
  conversation_id: number;
  direction: MessageDirection;
  sender_id?: number;
  sender_name: string;
  sender_role?: UserRoleType;
  recipient_id?: number;
  recipient_name: string;
  recipient_phone: string;
  message_text: string;
  status: MessageStatus;
  sent_at: string;
  delivered_at?: string;
  read_at?: string;
  twilio_sid?: string;
  error_message?: string;
  attachments?: Array<{
    type: string;
    url: string;
    name: string;
  }>;
}

export interface SMSConversation {
  conversation_id: number;
  participant_1_id: number;
  participant_1_name: string;
  participant_1_phone: string;
  participant_1_role: UserRoleType;
  participant_2_id?: number;
  participant_2_name: string;
  participant_2_phone: string;
  participant_2_role?: UserRoleType;
  status: ConversationStatus;
  last_message?: SMSMessage;
  last_message_at?: string;
  unread_count: number;
  total_messages: number;
  created_at: string;
  updated_at: string;
  tags?: string[];
  assigned_to?: {
    user_id: number;
    name: string;
  };
}

export interface SendSMSRequest {
  recipient_phone: string;
  recipient_user_id?: number;
  message_text: string;
  conversation_id?: number;
  schedule_for?: string;
  template_id?: number;
  template_variables?: Record<string, any>;
}

export interface BulkSMSRequest {
  recipients: Array<{
    phone: string;
    user_id?: number;
    custom_variables?: Record<string, any>;
  }>;
  message_text?: string;
  template_id?: number;
  schedule_for?: string;
  tag?: string;
}

export interface ConversationListParams {
  status?: ConversationStatus;
  participant_id?: number;
  assigned_to?: number;
  has_unread?: boolean;
  tags?: string[];
  search?: string;
  date_from?: string;
  date_to?: string;
  limit?: number;
  offset?: number;
  sort_by?: 'last_message' | 'created' | 'unread';
  sort_order?: 'asc' | 'desc';
}

export interface MessageListParams {
  conversation_id?: number;
  direction?: MessageDirection;
  status?: MessageStatus;
  date_from?: string;
  date_to?: string;
  limit?: number;
  offset?: number;
}

export interface ConversationStats {
  total_conversations: number;
  active_conversations: number;
  unread_messages: number;
  average_response_time_minutes: number;
  messages_by_status: Record<MessageStatus, number>;
  conversations_by_role: Record<UserRoleType, number>;
  peak_hours: Array<{
    hour: number;
    message_count: number;
  }>;
}

export interface SMSTemplate {
  template_id: number;
  name: string;
  message_text: string;
  variables: string[];
  category: string;
  language: 'en' | 'fr';
  is_active: boolean;
  usage_count: number;
  last_used?: string;
}

export interface ConversationTag {
  tag_id: number;
  name: string;
  color: string;
  description?: string;
  conversation_count: number;
}

class SMSConversationService {
  /**
   * Get conversations list
   */
  async getConversations(params?: ConversationListParams): Promise<{
    conversations: SMSConversation[];
    total: number;
    limit: number;
    offset: number;
  }> {
    try {
      const response = await api.get<{
        conversations: SMSConversation[];
        total: number;
        limit: number;
        offset: number;
      }>('/sms-conversations', { params });
      return response.data;
    } catch (error: any) {
      console.error('Error fetching conversations:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch conversations');
    }
  }

  /**
   * Get specific conversation
   */
  async getConversation(conversationId: number): Promise<SMSConversation> {
    try {
      const response = await api.get<SMSConversation>(`/sms-conversations/${conversationId}`);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching conversation:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch conversation');
    }
  }

  /**
   * Get messages in a conversation
   */
  async getMessages(conversationId: number, params?: MessageListParams): Promise<{
    messages: SMSMessage[];
    total: number;
    limit: number;
    offset: number;
  }> {
    try {
      const response = await api.get<{
        messages: SMSMessage[];
        total: number;
        limit: number;
        offset: number;
      }>(`/sms-conversations/${conversationId}/messages`, { params });
      return response.data;
    } catch (error: any) {
      console.error('Error fetching messages:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch messages');
    }
  }

  /**
   * Send SMS message
   */
  async sendSMS(request: SendSMSRequest): Promise<SMSMessage> {
    try {
      const response = await api.post<SMSMessage>('/sms-conversations/send', request);
      return response.data;
    } catch (error: any) {
      console.error('Error sending SMS:', error);
      throw new Error(error.response?.data?.detail || 'Failed to send SMS');
    }
  }

  /**
   * Send bulk SMS
   */
  async sendBulkSMS(request: BulkSMSRequest): Promise<{
    sent: number;
    failed: number;
    messages: SMSMessage[];
    errors?: Array<{ phone: string; error: string }>;
  }> {
    try {
      const response = await api.post<{
        sent: number;
        failed: number;
        messages: SMSMessage[];
        errors?: Array<{ phone: string; error: string }>;
      }>('/sms-conversations/send-bulk', request);
      return response.data;
    } catch (error: any) {
      console.error('Error sending bulk SMS:', error);
      throw new Error(error.response?.data?.detail || 'Failed to send bulk SMS');
    }
  }

  /**
   * Mark messages as read
   */
  async markAsRead(conversationId: number, messageIds?: number[]): Promise<{
    updated_count: number;
  }> {
    try {
      const response = await api.post<{ updated_count: number }>(
        `/sms-conversations/${conversationId}/mark-read`,
        { message_ids: messageIds }
      );
      return response.data;
    } catch (error: any) {
      console.error('Error marking messages as read:', error);
      throw new Error(error.response?.data?.detail || 'Failed to mark as read');
    }
  }

  /**
   * Update conversation status
   */
  async updateConversationStatus(
    conversationId: number, 
    status: ConversationStatus
  ): Promise<SMSConversation> {
    try {
      const response = await api.patch<SMSConversation>(
        `/sms-conversations/${conversationId}/status`,
        { status }
      );
      return response.data;
    } catch (error: any) {
      console.error('Error updating conversation status:', error);
      throw new Error(error.response?.data?.detail || 'Failed to update status');
    }
  }

  /**
   * Assign conversation to user
   */
  async assignConversation(
    conversationId: number, 
    userId: number
  ): Promise<SMSConversation> {
    try {
      const response = await api.post<SMSConversation>(
        `/sms-conversations/${conversationId}/assign`,
        { user_id: userId }
      );
      return response.data;
    } catch (error: any) {
      console.error('Error assigning conversation:', error);
      throw new Error(error.response?.data?.detail || 'Failed to assign conversation');
    }
  }

  /**
   * Add tags to conversation
   */
  async addTags(conversationId: number, tags: string[]): Promise<SMSConversation> {
    try {
      const response = await api.post<SMSConversation>(
        `/sms-conversations/${conversationId}/tags`,
        { tags }
      );
      return response.data;
    } catch (error: any) {
      console.error('Error adding tags:', error);
      throw new Error(error.response?.data?.detail || 'Failed to add tags');
    }
  }

  /**
   * Remove tags from conversation
   */
  async removeTags(conversationId: number, tags: string[]): Promise<SMSConversation> {
    try {
      const response = await api.delete<SMSConversation>(
        `/sms-conversations/${conversationId}/tags`,
        { data: { tags } }
      );
      return response.data;
    } catch (error: any) {
      console.error('Error removing tags:', error);
      throw new Error(error.response?.data?.detail || 'Failed to remove tags');
    }
  }

  /**
   * Get conversation statistics
   */
  async getStats(dateFrom?: string, dateTo?: string): Promise<ConversationStats> {
    try {
      const params = { date_from: dateFrom, date_to: dateTo };
      const response = await api.get<ConversationStats>('/sms-conversations/stats', { params });
      return response.data;
    } catch (error: any) {
      console.error('Error fetching conversation stats:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch stats');
    }
  }

  /**
   * Get SMS templates
   */
  async getTemplates(category?: string, language?: 'en' | 'fr'): Promise<SMSTemplate[]> {
    try {
      const params = { category, language };
      const response = await api.get<SMSTemplate[]>('/sms-conversations/templates', { params });
      return response.data;
    } catch (error: any) {
      console.error('Error fetching templates:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch templates');
    }
  }

  /**
   * Create SMS template
   */
  async createTemplate(template: Omit<SMSTemplate, 'template_id' | 'usage_count' | 'last_used'>): Promise<SMSTemplate> {
    try {
      const response = await api.post<SMSTemplate>('/sms-conversations/templates', template);
      return response.data;
    } catch (error: any) {
      console.error('Error creating template:', error);
      throw new Error(error.response?.data?.detail || 'Failed to create template');
    }
  }

  /**
   * Get available tags
   */
  async getTags(): Promise<ConversationTag[]> {
    try {
      const response = await api.get<ConversationTag[]>('/sms-conversations/tags');
      return response.data;
    } catch (error: any) {
      console.error('Error fetching tags:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch tags');
    }
  }

  /**
   * Create new tag
   */
  async createTag(tag: Omit<ConversationTag, 'tag_id' | 'conversation_count'>): Promise<ConversationTag> {
    try {
      const response = await api.post<ConversationTag>('/sms-conversations/tags', tag);
      return response.data;
    } catch (error: any) {
      console.error('Error creating tag:', error);
      throw new Error(error.response?.data?.detail || 'Failed to create tag');
    }
  }

  /**
   * Search messages
   */
  async searchMessages(query: string, params?: {
    conversation_id?: number;
    date_from?: string;
    date_to?: string;
    limit?: number;
  }): Promise<SMSMessage[]> {
    try {
      const response = await api.get<SMSMessage[]>('/sms-conversations/search', {
        params: { query, ...params }
      });
      return response.data;
    } catch (error: any) {
      console.error('Error searching messages:', error);
      throw new Error(error.response?.data?.detail || 'Failed to search messages');
    }
  }

  /**
   * Format phone number for display
   */
  formatPhoneNumber(phone: string): string {
    // Remove all non-digits
    const cleaned = phone.replace(/\D/g, '');
    
    // Format as: (XXX) XXX-XXXX
    if (cleaned.length === 10) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    } else if (cleaned.length === 11 && cleaned[0] === '1') {
      return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
    }
    
    return phone;
  }

  /**
   * Get participant display info
   */
  getParticipantInfo(conversation: SMSConversation, currentUserId: number): {
    name: string;
    phone: string;
    role?: UserRoleType;
    isCurrentUser: boolean;
  } {
    if (conversation.participant_1_id === currentUserId) {
      return {
        name: conversation.participant_2_name,
        phone: conversation.participant_2_phone,
        role: conversation.participant_2_role,
        isCurrentUser: false
      };
    } else {
      return {
        name: conversation.participant_1_name,
        phone: conversation.participant_1_phone,
        role: conversation.participant_1_role,
        isCurrentUser: false
      };
    }
  }

  /**
   * Format message time
   */
  formatMessageTime(timestamp: string): string {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) {
      return 'Just now';
    } else if (diffMins < 60) {
      return `${diffMins}m ago`;
    } else if (diffHours < 24) {
      return `${diffHours}h ago`;
    } else if (diffDays < 7) {
      return `${diffDays}d ago`;
    } else {
      return date.toLocaleDateString();
    }
  }

  /**
   * Get role emoji
   */
  getRoleEmoji(role?: UserRoleType): string {
    if (!role) return '👤';
    
    const emojis: Record<UserRoleType, string> = {
      [UserRoleType.MANAGER]: '👔',
      [UserRoleType.TUTOR]: '👨‍🏫',
      [UserRoleType.CLIENT]: '👤'
    };

    return emojis[role] || '👤';
  }

  /**
   * Check if message is automated
   */
  isAutomatedMessage(message: SMSMessage): boolean {
    const automatedPhrases = [
      'appointment reminder',
      'payment confirmation',
      'scheduled message',
      'automated response'
    ];

    const lowerMessage = message.message_text.toLowerCase();
    return automatedPhrases.some(phrase => lowerMessage.includes(phrase));
  }
}

export const smsConversationService = new SMSConversationService();