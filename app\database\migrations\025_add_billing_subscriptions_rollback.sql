-- Rollback: 025_add_billing_subscriptions.sql
-- Description: Remove subscription system tables
-- Author: System
-- Date: 2025-01-20

-- Drop indexes first
DROP INDEX IF EXISTS idx_billing_subscription_usage_appointment;
DROP INDEX IF EXISTS idx_billing_subscription_usage_subscription;
DROP INDEX IF EXISTS idx_billing_tutor_payments_status;
DROP INDEX IF EXISTS idx_billing_tutor_payments_period;
DROP INDEX IF EXISTS idx_billing_tutor_payments_tutor;
DROP INDEX IF EXISTS idx_billing_package_purchases_status;
DROP INDEX IF EXISTS idx_billing_package_purchases_client;
DROP INDEX IF EXISTS idx_billing_packages_type;
DROP INDEX IF EXISTS idx_billing_packages_active;
DROP INDEX IF EXISTS idx_billing_subscriptions_dates;
DROP INDEX IF EXISTS idx_billing_subscriptions_status;
DROP INDEX IF EXISTS idx_billing_subscriptions_client;

-- Drop tables in reverse order of dependencies
DROP TABLE IF EXISTS billing_subscription_usage CASCADE;
DROP TABLE IF EXISTS billing_tutor_payment_details CASCADE;
DROP TABLE IF EXISTS billing_tutor_payments CASCADE;
DROP TABLE IF EXISTS billing_package_purchases CASCADE;
DROP TABLE IF EXISTS billing_packages CASCADE;
DROP TABLE IF EXISTS billing_subscriptions CASCADE;