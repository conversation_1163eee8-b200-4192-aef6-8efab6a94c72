import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { format, startOfWeek, addDays, parse, isValid } from 'date-fns';
import { Clock, Plus, Edit, Trash2, Copy, Calendar, DollarSign, Coffee, Save, X } from 'lucide-react';

interface TimeSlot {
  startTime: string;
  endTime: string;
  isAvailable: boolean;
  hourlyRate?: number;
  breakStart?: string;
  breakEnd?: string;
  notes?: string;
}

interface DayAvailability {
  dayOfWeek: number;
  dayName: string;
  timeSlots: TimeSlot[];
  isActive: boolean;
}

interface TutorSchedule {
  tutorId: number;
  tutorName: string;
  effectiveDate: Date;
  expiryDate?: Date;
  weeklySchedule: DayAvailability[];
  defaultHourlyRate: number;
  isActive: boolean;
}

interface TutorAvailabilityProps {
  tutorId?: number;
  isModal?: boolean;
  onClose?: () => void;
  onSave?: (schedule: TutorSchedule) => void;
}

const TutorAvailability: React.FC<TutorAvailabilityProps> = ({
  tutorId,
  isModal = false,
  onClose,
  onSave
}) => {
  const { t } = useTranslation();
  const [selectedTutorId, setSelectedTutorId] = useState(tutorId || 0);
  const [schedule, setSchedule] = useState<TutorSchedule | null>(null);
  const [editingDay, setEditingDay] = useState<number | null>(null);
  const [editingSlot, setEditingSlot] = useState<{ dayIndex: number; slotIndex: number } | null>(null);
  const [tutors, setTutors] = useState<Array<{ id: number; name: string; }>>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  const daysOfWeek = [
    { id: 1, name: 'Monday', short: 'Mon' },
    { id: 2, name: 'Tuesday', short: 'Tue' },
    { id: 3, name: 'Wednesday', short: 'Wed' },
    { id: 4, name: 'Thursday', short: 'Thu' },
    { id: 5, name: 'Friday', short: 'Fri' },
    { id: 6, name: 'Saturday', short: 'Sat' },
    { id: 7, name: 'Sunday', short: 'Sun' }
  ];

  // Mock data - replace with actual API calls
  useEffect(() => {
    const mockTutors = [
      { id: 1, name: 'Marie Dubois' },
      { id: 2, name: 'John Smith' },
      { id: 3, name: 'Sophie Martin' },
      { id: 4, name: 'David Wilson' }
    ];
    setTutors(mockTutors);
  }, []);

  // Load tutor schedule
  useEffect(() => {
    if (selectedTutorId) {
      loadTutorSchedule(selectedTutorId);
    }
  }, [selectedTutorId]);

  const loadTutorSchedule = async (tutorId: number) => {
    setLoading(true);
    try {
      // Mock API call - replace with actual implementation
      const mockSchedule: TutorSchedule = {
        tutorId,
        tutorName: tutors.find(t => t.id === tutorId)?.name || 'Unknown',
        effectiveDate: new Date(),
        weeklySchedule: daysOfWeek.map(day => ({
          dayOfWeek: day.id,
          dayName: day.name,
          isActive: day.id <= 5, // Weekdays active by default
          timeSlots: day.id <= 5 ? [
            {
              startTime: '09:00',
              endTime: '17:00',
              isAvailable: true,
              hourlyRate: 55,
              breakStart: '12:00',
              breakEnd: '13:00',
              notes: 'Standard availability'
            }
          ] : []
        })),
        defaultHourlyRate: 50,
        isActive: true
      };
      
      setSchedule(mockSchedule);
    } catch (error) {
      console.error('Error loading tutor schedule:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDayToggle = (dayIndex: number) => {
    if (!schedule) return;
    
    const updatedSchedule = { ...schedule };
    updatedSchedule.weeklySchedule[dayIndex].isActive = !updatedSchedule.weeklySchedule[dayIndex].isActive;
    
    if (!updatedSchedule.weeklySchedule[dayIndex].isActive) {
      updatedSchedule.weeklySchedule[dayIndex].timeSlots = [];
    } else if (updatedSchedule.weeklySchedule[dayIndex].timeSlots.length === 0) {
      // Add default time slot when activating a day
      updatedSchedule.weeklySchedule[dayIndex].timeSlots = [{
        startTime: '09:00',
        endTime: '17:00',
        isAvailable: true,
        hourlyRate: schedule.defaultHourlyRate
      }];
    }
    
    setSchedule(updatedSchedule);
    setHasChanges(true);
  };

  const handleAddTimeSlot = (dayIndex: number) => {
    if (!schedule) return;
    
    const updatedSchedule = { ...schedule };
    const lastSlot = updatedSchedule.weeklySchedule[dayIndex].timeSlots.slice(-1)[0];
    const startTime = lastSlot ? lastSlot.endTime : '09:00';
    
    updatedSchedule.weeklySchedule[dayIndex].timeSlots.push({
      startTime,
      endTime: '18:00',
      isAvailable: true,
      hourlyRate: schedule.defaultHourlyRate
    });
    
    setSchedule(updatedSchedule);
    setHasChanges(true);
  };

  const handleRemoveTimeSlot = (dayIndex: number, slotIndex: number) => {
    if (!schedule) return;
    
    const updatedSchedule = { ...schedule };
    updatedSchedule.weeklySchedule[dayIndex].timeSlots.splice(slotIndex, 1);
    
    setSchedule(updatedSchedule);
    setHasChanges(true);
  };

  const handleTimeSlotChange = (
    dayIndex: number, 
    slotIndex: number, 
    field: keyof TimeSlot, 
    value: any
  ) => {
    if (!schedule) return;
    
    const updatedSchedule = { ...schedule };
    (updatedSchedule.weeklySchedule[dayIndex].timeSlots[slotIndex] as any)[field] = value;
    
    setSchedule(updatedSchedule);
    setHasChanges(true);
  };

  const handleCopyDay = (fromDayIndex: number, toDayIndex: number) => {
    if (!schedule) return;
    
    const updatedSchedule = { ...schedule };
    const sourceDay = updatedSchedule.weeklySchedule[fromDayIndex];
    
    updatedSchedule.weeklySchedule[toDayIndex] = {
      ...updatedSchedule.weeklySchedule[toDayIndex],
      isActive: sourceDay.isActive,
      timeSlots: sourceDay.timeSlots.map(slot => ({ ...slot }))
    };
    
    setSchedule(updatedSchedule);
    setHasChanges(true);
  };

  const handleBulkUpdate = (operation: 'setRate' | 'addBreak' | 'setHours', value?: any) => {
    if (!schedule) return;
    
    const updatedSchedule = { ...schedule };
    
    updatedSchedule.weeklySchedule.forEach(day => {
      if (!day.isActive) return;
      
      day.timeSlots.forEach(slot => {
        switch (operation) {
          case 'setRate':
            slot.hourlyRate = value;
            break;
          case 'addBreak':
            slot.breakStart = '12:00';
            slot.breakEnd = '13:00';
            break;
          case 'setHours':
            slot.startTime = '09:00';
            slot.endTime = '17:00';
            break;
        }
      });
    });
    
    setSchedule(updatedSchedule);
    setHasChanges(true);
  };

  const validateTimeSlot = (slot: TimeSlot): string[] => {
    const errors: string[] = [];
    
    const start = parse(slot.startTime, 'HH:mm', new Date());
    const end = parse(slot.endTime, 'HH:mm', new Date());
    
    if (!isValid(start) || !isValid(end)) {
      errors.push(t('availability.validation.invalidTime'));
    } else if (start >= end) {
      errors.push(t('availability.validation.endBeforeStart'));
    }
    
    if (slot.breakStart && slot.breakEnd) {
      const breakStart = parse(slot.breakStart, 'HH:mm', new Date());
      const breakEnd = parse(slot.breakEnd, 'HH:mm', new Date());
      
      if (breakStart < start || breakEnd > end) {
        errors.push(t('availability.validation.breakOutsideSlot'));
      } else if (breakStart >= breakEnd) {
        errors.push(t('availability.validation.breakEndBeforeStart'));
      }
    }
    
    if (slot.hourlyRate && slot.hourlyRate <= 0) {
      errors.push(t('availability.validation.invalidRate'));
    }
    
    return errors;
  };

  const handleSave = async () => {
    if (!schedule) return;
    
    // Validate all time slots
    let hasErrors = false;
    schedule.weeklySchedule.forEach(day => {
      day.timeSlots.forEach(slot => {
        const errors = validateTimeSlot(slot);
        if (errors.length > 0) {
          hasErrors = true;
          console.error('Validation errors:', errors);
        }
      });
    });
    
    if (hasErrors) {
      alert(t('availability.validation.fixErrors'));
      return;
    }
    
    setSaving(true);
    try {
      // API call would go here
      console.log('Saving schedule:', schedule);
      
      if (onSave) {
        onSave(schedule);
      }
      
      setHasChanges(false);
      alert(t('availability.saveSuccess'));
    } catch (error) {
      console.error('Error saving schedule:', error);
      alert(t('availability.saveError'));
    } finally {
      setSaving(false);
    }
  };

  const renderTimeSlot = (slot: TimeSlot, dayIndex: number, slotIndex: number) => {
    const isEditing = editingSlot?.dayIndex === dayIndex && editingSlot?.slotIndex === slotIndex;
    const errors = validateTimeSlot(slot);
    
    return (
      <div
        key={slotIndex}
        className={`border rounded-lg p-3 space-y-2 ${
          errors.length > 0 ? 'border-red-300 bg-red-50' : 'border-gray-200'
        } ${isEditing ? 'ring-2 ring-red-500' : ''}`}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Clock className="w-4 h-4 text-gray-400" />
            <input
              type="time"
              value={slot.startTime}
              onChange={(e) => handleTimeSlotChange(dayIndex, slotIndex, 'startTime', e.target.value)}
              className="text-sm border border-gray-300 rounded px-2 py-1"
            />
            <span className="text-gray-500">-</span>
            <input
              type="time"
              value={slot.endTime}
              onChange={(e) => handleTimeSlotChange(dayIndex, slotIndex, 'endTime', e.target.value)}
              className="text-sm border border-gray-300 rounded px-2 py-1"
            />
          </div>
          
          <div className="flex items-center space-x-1">
            <button
              onClick={() => setEditingSlot(isEditing ? null : { dayIndex, slotIndex })}
              className="p-1 text-gray-400 hover:text-gray-600"
              title={t('common.edit')}
            >
              <Edit className="w-4 h-4" />
            </button>
            <button
              onClick={() => handleRemoveTimeSlot(dayIndex, slotIndex)}
              className="p-1 text-red-400 hover:text-red-600"
              title={t('common.delete')}
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>
        </div>
        
        {isEditing && (
          <div className="space-y-2 bg-gray-50 p-2 rounded">
            <div className="grid grid-cols-2 gap-2">
              <div>
                <label className="text-xs text-gray-600">{t('availability.hourlyRate')}</label>
                <div className="flex items-center">
                  <DollarSign className="w-4 h-4 text-gray-400" />
                  <input
                    type="number"
                    value={slot.hourlyRate || ''}
                    onChange={(e) => handleTimeSlotChange(
                      dayIndex, slotIndex, 'hourlyRate', 
                      e.target.value ? parseFloat(e.target.value) : undefined
                    )}
                    className="w-full text-sm border border-gray-300 rounded px-2 py-1 ml-1"
                    placeholder="50.00"
                    step="0.01"
                  />
                </div>
              </div>
              
              <div>
                <label className="text-xs text-gray-600 flex items-center">
                  <Coffee className="w-3 h-3 mr-1" />
                  {t('availability.breakTime')}
                </label>
                <div className="flex items-center space-x-1">
                  <input
                    type="time"
                    value={slot.breakStart || ''}
                    onChange={(e) => handleTimeSlotChange(dayIndex, slotIndex, 'breakStart', e.target.value)}
                    className="text-xs border border-gray-300 rounded px-1 py-1"
                  />
                  <span className="text-xs">-</span>
                  <input
                    type="time"
                    value={slot.breakEnd || ''}
                    onChange={(e) => handleTimeSlotChange(dayIndex, slotIndex, 'breakEnd', e.target.value)}
                    className="text-xs border border-gray-300 rounded px-1 py-1"
                  />
                </div>
              </div>
            </div>
            
            <div>
              <label className="text-xs text-gray-600">{t('availability.notes')}</label>
              <textarea
                value={slot.notes || ''}
                onChange={(e) => handleTimeSlotChange(dayIndex, slotIndex, 'notes', e.target.value)}
                className="w-full text-sm border border-gray-300 rounded px-2 py-1"
                rows={2}
                placeholder={t('availability.notesPlaceholder')}
              />
            </div>
          </div>
        )}
        
        {errors.length > 0 && (
          <div className="text-xs text-red-600">
            {errors.map((error, i) => (
              <div key={i}>• {error}</div>
            ))}
          </div>
        )}
      </div>
    );
  };

  if (!schedule && loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
      </div>
    );
  }

  const content = (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">
            {t('availability.title')}
          </h2>
          <p className="text-sm text-gray-500">
            {t('availability.description')}
          </p>
        </div>
        
        {hasChanges && (
          <div className="flex items-center space-x-2">
            <span className="text-sm text-orange-600">{t('availability.unsavedChanges')}</span>
            <button
              onClick={handleSave}
              disabled={saving}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
            >
              <Save className="w-4 h-4 mr-1" />
              {saving ? t('common.saving') : t('common.save')}
            </button>
          </div>
        )}
      </div>

      {/* Tutor Selection */}
      {!tutorId && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t('availability.selectTutor')}
          </label>
          <select
            value={selectedTutorId}
            onChange={(e) => setSelectedTutorId(parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
          >
            <option value="">{t('availability.chooseTutor')}</option>
            {tutors.map((tutor) => (
              <option key={tutor.id} value={tutor.id}>
                {tutor.name}
              </option>
            ))}
          </select>
        </div>
      )}

      {schedule && (
        <>
          {/* Schedule Settings */}
          <div className="bg-gray-50 rounded-lg p-4 space-y-4">
            <h3 className="text-sm font-medium text-gray-900">{t('availability.scheduleSettings')}</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('availability.defaultRate')}
                </label>
                <div className="flex items-center">
                  <DollarSign className="w-4 h-4 text-gray-400" />
                  <input
                    type="number"
                    value={schedule.defaultHourlyRate}
                    onChange={(e) => {
                      const rate = parseFloat(e.target.value);
                      setSchedule(prev => prev ? { ...prev, defaultHourlyRate: rate } : null);
                      setHasChanges(true);
                    }}
                    className="w-full border border-gray-300 rounded px-2 py-1 ml-1"
                    step="0.01"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('availability.effectiveDate')}
                </label>
                <input
                  type="date"
                  value={format(schedule.effectiveDate, 'yyyy-MM-dd')}
                  onChange={(e) => {
                    setSchedule(prev => prev ? { 
                      ...prev, 
                      effectiveDate: new Date(e.target.value) 
                    } : null);
                    setHasChanges(true);
                  }}
                  className="w-full border border-gray-300 rounded px-2 py-1"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('availability.expiryDate')} ({t('common.optional')})
                </label>
                <input
                  type="date"
                  value={schedule.expiryDate ? format(schedule.expiryDate, 'yyyy-MM-dd') : ''}
                  onChange={(e) => {
                    setSchedule(prev => prev ? { 
                      ...prev, 
                      expiryDate: e.target.value ? new Date(e.target.value) : undefined
                    } : null);
                    setHasChanges(true);
                  }}
                  className="w-full border border-gray-300 rounded px-2 py-1"
                />
              </div>
            </div>
            
            {/* Bulk Actions */}
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => handleBulkUpdate('setRate', schedule.defaultHourlyRate)}
                className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded hover:bg-red-200"
              >
                {t('availability.bulkSetRate')}
              </button>
              <button
                onClick={() => handleBulkUpdate('addBreak')}
                className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded hover:bg-green-200"
              >
                {t('availability.bulkAddBreak')}
              </button>
              <button
                onClick={() => handleBulkUpdate('setHours')}
                className="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded hover:bg-purple-200"
              >
                {t('availability.bulkSetHours')}
              </button>
            </div>
          </div>

          {/* Weekly Schedule */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-gray-900">{t('availability.weeklySchedule')}</h3>
            
            {schedule.weeklySchedule.map((day, dayIndex) => (
              <div
                key={day.dayOfWeek}
                className={`border rounded-lg p-4 ${
                  day.isActive ? 'border-red-200 bg-red-50' : 'border-gray-200 bg-gray-50'
                }`}
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      checked={day.isActive}
                      onChange={() => handleDayToggle(dayIndex)}
                      className="rounded border-gray-300 text-red-600 focus:ring-red-500"
                    />
                    <h4 className="font-medium text-gray-900">{day.dayName}</h4>
                    <span className="text-sm text-gray-500">
                      ({day.timeSlots.length} {t('availability.timeSlots')})
                    </span>
                  </div>
                  
                  {day.isActive && (
                    <div className="flex items-center space-x-2">
                      <select
                        onChange={(e) => {
                          const fromDay = parseInt(e.target.value);
                          if (fromDay !== dayIndex) {
                            handleCopyDay(fromDay, dayIndex);
                          }
                        }}
                        className="text-xs border border-gray-300 rounded px-2 py-1"
                        value=""
                      >
                        <option value="">{t('availability.copyFrom')}</option>
                        {schedule.weeklySchedule.map((d, i) => (
                          i !== dayIndex && d.isActive && d.timeSlots.length > 0 && (
                            <option key={i} value={i}>{d.dayName}</option>
                          )
                        ))}
                      </select>
                      
                      <button
                        onClick={() => handleAddTimeSlot(dayIndex)}
                        className="inline-flex items-center text-xs bg-red-600 text-white px-2 py-1 rounded hover:bg-red-700"
                      >
                        <Plus className="w-3 h-3 mr-1" />
                        {t('availability.addSlot')}
                      </button>
                    </div>
                  )}
                </div>
                
                {day.isActive && (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                    {day.timeSlots.map((slot, slotIndex) => 
                      renderTimeSlot(slot, dayIndex, slotIndex)
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        </>
      )}
    </div>
  );

  if (isModal) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
          <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">
              {t('availability.title')}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
          <div className="p-6">
            {content}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      {content}
    </div>
  );
};

export default TutorAvailability;
