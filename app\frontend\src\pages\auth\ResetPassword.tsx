import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate, Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Lock, AlertCircle, CheckCircle, Eye, EyeOff, ArrowLeft } from 'lucide-react';
import { Card } from '../../components/common/Card';
import Button from '../../components/common/Button';
import { Input } from '../../components/common/Input';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import { PasswordStrengthIndicator } from '../../components/auth/PasswordStrengthIndicator';
import api from '../../services/api';
import toast from 'react-hot-toast';

export const ResetPassword: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const token = searchParams.get('token');
  
  const [loading, setLoading] = useState(false);
  const [verifying, setVerifying] = useState(true);
  const [isValidToken, setIsValidToken] = useState(false);
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [userEmail, setUserEmail] = useState<string>('');

  useEffect(() => {
    console.log('ResetPassword component mounted');
    console.log('Token from URL:', token);
    console.log('Token length:', token?.length);
    
    if (token) {
      verifyResetToken();
    } else {
      setError(t('auth.resetPassword.invalidToken'));
      setVerifying(false);
    }
  }, [token]);

  const verifyResetToken = async () => {
    console.log('verifyResetToken called');
    console.log('Making API call to:', `/api/v1/auth/password/reset-validate/${token}`);
    
    try {
      const response = await api.get(`/api/v1/auth/password/reset-validate/${token}`);
      console.log('API response:', response);
      console.log('Response data:', response.data);
      
      if (response.data.valid) {
        console.log('Token is valid, email:', response.data.email);
        setIsValidToken(true);
        setUserEmail(response.data.email);
      } else {
        console.log('Token marked as invalid by API');
        setError(t('auth.resetPassword.tokenExpired'));
      }
    } catch (error: any) {
      console.error('Error verifying token:', error);
      console.error('Error response:', error.response);
      console.error('Error status:', error.response?.status);
      console.error('Error data:', error.response?.data);
      setError(t('auth.resetPassword.tokenInvalid'));
    } finally {
      setVerifying(false);
    }
  };

  const validatePassword = (password: string): boolean => {
    if (password.length < 8) {
      setError(t('auth.resetPassword.passwordTooShort'));
      return false;
    }

    const hasUpper = /[A-Z]/.test(password);
    const hasLower = /[a-z]/.test(password);
    const hasDigit = /\d/.test(password);

    if (!hasUpper || !hasLower || !hasDigit) {
      setError(t('auth.resetPassword.passwordRequirements'));
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!password || !confirmPassword) {
      setError(t('auth.resetPassword.allFieldsRequired'));
      return;
    }

    if (password !== confirmPassword) {
      setError(t('auth.resetPassword.passwordMismatch'));
      return;
    }

    if (!validatePassword(password)) {
      return;
    }

    try {
      setLoading(true);
      
      console.log('Submitting password reset');
      console.log('Token:', token);
      console.log('Password length:', password.length);
      
      const response = await api.post('/api/v1/auth/password/reset-confirm', {
        token,
        new_password: password,
        confirm_password: confirmPassword
      });
      
      console.log('Password reset response:', response);

      setSuccess(true);
      toast.success(t('auth.resetPassword.success'));
      
      // Redirect to login after 3 seconds
      setTimeout(() => {
        navigate('/login', { 
          state: { 
            message: t('auth.resetPassword.successMessage'),
            email: userEmail 
          }
        });
      }, 3000);
      
    } catch (error: any) {
      console.error('Error resetting password:', error);
      console.error('Error response:', error.response);
      console.error('Error status:', error.response?.status);
      console.error('Error data:', error.response?.data);
      console.error('Error detail:', error.response?.data?.detail);
      
      if (error.response?.status === 400) {
        // Use the specific error message from the backend if available
        const errorDetail = error.response?.data?.detail;
        if (errorDetail && typeof errorDetail === 'string') {
          setError(errorDetail);
        } else {
          setError(t('auth.resetPassword.tokenExpired'));
        }
      } else {
        setError(t('auth.resetPassword.resetError'));
      }
    } finally {
      setLoading(false);
    }
  };

  if (verifying) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4">
        <Card className="max-w-md w-full p-8">
          <div className="text-center">
            <LoadingSpinner size="lg" />
            <p className="mt-4 text-gray-600">{t('auth.resetPassword.verifyingToken')}</p>
          </div>
        </Card>
      </div>
    );
  }

  if (!isValidToken) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4">
        <Card className="max-w-md w-full p-8">
          <div className="text-center">
            <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
              <AlertCircle className="w-8 h-8 text-red-600" />
            </div>
            <h2 className="text-2xl font-semibold text-gray-900 mb-2">
              {t('auth.resetPassword.invalidTokenTitle')}
            </h2>
            <p className="text-gray-600 mb-6">{error}</p>
            <Link to="/login">
              <Button variant="primary">
                {t('auth.resetPassword.backToLogin')}
              </Button>
            </Link>
          </div>
        </Card>
      </div>
    );
  }

  if (success) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4">
        <Card className="max-w-md w-full p-8">
          <div className="text-center">
            <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <h2 className="text-2xl font-semibold text-gray-900 mb-2">
              {t('auth.resetPassword.successTitle')}
            </h2>
            <p className="text-gray-600 mb-2">
              {t('auth.resetPassword.successMessage')}
            </p>
            <p className="text-sm text-gray-500">
              {t('auth.resetPassword.redirecting')}
            </p>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4">
      <div className="max-w-md w-full">
        <Link to="/login" className="inline-flex items-center text-sm text-gray-600 hover:text-gray-900 mb-6">
          <ArrowLeft className="w-4 h-4 mr-1" />
          {t('auth.resetPassword.backToLogin')}
        </Link>
        
        <Card className="p-8">
          <div className="text-center mb-8">
            <div className="mx-auto w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mb-4">
              <Lock className="w-8 h-8 text-primary-600" />
            </div>
            <h1 className="text-2xl font-semibold text-gray-900">
              {t('auth.resetPassword.title')}
            </h1>
            <p className="mt-2 text-sm text-gray-600">
              {t('auth.resetPassword.subtitle')}
            </p>
            {userEmail && (
              <p className="mt-1 text-sm font-medium text-gray-700">
                {userEmail}
              </p>
            )}
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                {t('auth.resetPassword.newPassword')}
              </label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder={t('auth.resetPassword.passwordPlaceholder')}
                  required
                  autoComplete="new-password"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 text-gray-400" />
                  ) : (
                    <Eye className="h-4 w-4 text-gray-400" />
                  )}
                </button>
              </div>
              {password && <PasswordStrengthIndicator password={password} />}
            </div>

            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-2">
                {t('auth.resetPassword.confirmPassword')}
              </label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  type={showConfirmPassword ? 'text' : 'password'}
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  placeholder={t('auth.resetPassword.confirmPasswordPlaceholder')}
                  required
                  autoComplete="new-password"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4 text-gray-400" />
                  ) : (
                    <Eye className="h-4 w-4 text-gray-400" />
                  )}
                </button>
              </div>
            </div>

            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3">
                <div className="flex">
                  <AlertCircle className="h-5 w-5 text-red-400" />
                  <div className="ml-3">
                    <p className="text-sm text-red-800">{error}</p>
                  </div>
                </div>
              </div>
            )}

            <div className="space-y-3">
              <Button
                type="submit"
                variant="primary"
                className="w-full"
                disabled={loading || !password || !confirmPassword}
              >
                {loading ? (
                  <>
                    <LoadingSpinner size="sm" className="mr-2" />
                    {t('auth.resetPassword.resetting')}
                  </>
                ) : (
                  t('auth.resetPassword.resetPassword')
                )}
              </Button>

              <p className="text-xs text-center text-gray-500">
                {t('auth.resetPassword.securityNote')}
              </p>
            </div>
          </form>
        </Card>
      </div>
    </div>
  );
};

export default ResetPassword;