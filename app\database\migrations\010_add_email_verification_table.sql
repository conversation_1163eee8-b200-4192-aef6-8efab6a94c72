-- Migration: Add email verification tokens table
-- Description: Create table for email verification tokens and update user_accounts

-- Create email_verification_tokens table
CREATE TABLE IF NOT EXISTS email_verification_tokens (
    verification_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    token UUID UNIQUE NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    verified_at TIMESTAMP WITH TIME ZONE NULL,
    ip_address INET NULL,
    user_agent TEXT NULL,
    verified_from_ip INET NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT current_timestamp_est(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT current_timestamp_est(),
    deleted_at TIMESTAMP WITH TIME ZONE NULL
);

-- Add email verification columns to user_accounts
ALTER TABLE user_accounts 
ADD COLUMN IF NOT EXISTS email_verified BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN IF NOT EXISTS email_verified_at TIMESTAMP WITH TIME ZONE NULL;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_email_verification_tokens_user_id 
    ON email_verification_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_email_verification_tokens_token 
    ON email_verification_tokens(token);
CREATE INDEX IF NOT EXISTS idx_email_verification_tokens_email 
    ON email_verification_tokens(email);
CREATE INDEX IF NOT EXISTS idx_email_verification_tokens_expires_at 
    ON email_verification_tokens(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_accounts_email_verified 
    ON user_accounts(email_verified);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_email_verification_tokens_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = current_timestamp_est();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_email_verification_tokens_updated_at
    BEFORE UPDATE ON email_verification_tokens
    FOR EACH ROW
    EXECUTE FUNCTION update_email_verification_tokens_updated_at();

-- Update existing Google OAuth users to verified (they're pre-verified)
UPDATE user_accounts 
SET email_verified = true, email_verified_at = created_at 
WHERE google_id IS NOT NULL AND email_verified = false;