"""
Tests for analytics models.
"""

import pytest
from datetime import date, datetime, timedelta
from decimal import Decimal

from app.models.analytics_models import (
    TutorPerformanceMetrics, StudentLearningGoal, StudentProgressMetrics,
    PlatformAnalytics, SessionFeedback, AnalyticsSnapshot,
    PeriodType, GoalStatus, FeedbackRole, MetricType,
    TutorLeaderboardEntry, StudentProgressSummary,
    SessionFeedbackCreate, StudentLearningGoalCreate,
    TutorDashboardData, StudentDashboardData, PlatformDashboardData
)


class TestTutorPerformanceMetrics:
    """Test TutorPerformanceMetrics model."""
    
    def test_completion_rate_calculation(self):
        """Test session completion rate calculation."""
        metrics = TutorPerformanceMetrics(
            metric_id=1,
            tutor_id=1,
            period_start=date.today(),
            period_end=date.today(),
            period_type=PeriodType.MONTHLY,
            total_sessions=10,
            completed_sessions=8,
            cancelled_sessions=1,
            no_show_sessions=1,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        assert metrics.completion_rate == 80.0
    
    def test_completion_rate_zero_sessions(self):
        """Test completion rate when no sessions."""
        metrics = TutorPerformanceMetrics(
            metric_id=1,
            tutor_id=1,
            period_start=date.today(),
            period_end=date.today(),
            period_type=PeriodType.MONTHLY,
            total_sessions=0,
            completed_sessions=0,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        assert metrics.completion_rate is None
    
    def test_cancellation_rate_calculation(self):
        """Test session cancellation rate calculation."""
        metrics = TutorPerformanceMetrics(
            metric_id=1,
            tutor_id=1,
            period_start=date.today(),
            period_end=date.today(),
            period_type=PeriodType.MONTHLY,
            total_sessions=10,
            completed_sessions=8,
            cancelled_sessions=2,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        assert metrics.cancellation_rate == 20.0


class TestStudentProgressMetrics:
    """Test StudentProgressMetrics model."""
    
    def test_attendance_rate_calculation(self):
        """Test attendance rate calculation."""
        metrics = StudentProgressMetrics(
            metric_id=1,
            student_id=1,
            subject_area="Math",
            period_start=date.today() - timedelta(days=30),
            period_end=date.today(),
            total_sessions=10,
            attended_sessions=9,
            session_hours=Decimal("15.0")
        )
        
        assert metrics.attendance_rate == 90.0
    
    def test_milestone_completion_rate(self):
        """Test milestone completion rate calculation."""
        metrics = StudentProgressMetrics(
            metric_id=1,
            student_id=1,
            subject_area="Math",
            period_start=date.today() - timedelta(days=30),
            period_end=date.today(),
            total_sessions=10,
            attended_sessions=10,
            session_hours=Decimal("15.0"),
            milestones_achieved=3,
            milestones_total=4
        )
        
        assert metrics.milestone_completion_rate == 75.0
    
    def test_milestone_completion_rate_zero_total(self):
        """Test milestone completion rate when no milestones."""
        metrics = StudentProgressMetrics(
            metric_id=1,
            student_id=1,
            subject_area="Math",
            period_start=date.today() - timedelta(days=30),
            period_end=date.today(),
            total_sessions=10,
            attended_sessions=10,
            session_hours=Decimal("15.0"),
            milestones_achieved=0,
            milestones_total=0
        )
        
        assert metrics.milestone_completion_rate is None


class TestSessionFeedback:
    """Test SessionFeedback model."""
    
    def test_average_rating_calculation(self):
        """Test average rating calculation across all ratings."""
        feedback = SessionFeedback(
            feedback_id=1,
            appointment_id=1,
            given_by=1,
            given_to=2,
            role_type=FeedbackRole.STUDENT_TO_TUTOR,
            overall_rating=5,
            punctuality_rating=4,
            preparation_rating=5,
            communication_rating=4,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        assert feedback.average_rating == 4.5
    
    def test_average_rating_partial_ratings(self):
        """Test average rating with some ratings missing."""
        feedback = SessionFeedback(
            feedback_id=1,
            appointment_id=1,
            given_by=1,
            given_to=2,
            role_type=FeedbackRole.STUDENT_TO_TUTOR,
            overall_rating=5,
            punctuality_rating=None,
            preparation_rating=4,
            communication_rating=None,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        assert feedback.average_rating == 4.5
    
    def test_average_rating_no_ratings(self):
        """Test average rating when all ratings are None."""
        feedback = SessionFeedback(
            feedback_id=1,
            appointment_id=1,
            given_by=1,
            given_to=2,
            role_type=FeedbackRole.STUDENT_TO_TUTOR,
            overall_rating=None,
            punctuality_rating=None,
            preparation_rating=None,
            communication_rating=None,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        assert feedback.average_rating is None


class TestModelCreation:
    """Test model creation and validation."""
    
    def test_session_feedback_create_validation(self):
        """Test SessionFeedbackCreate validation."""
        # Valid feedback
        feedback = SessionFeedbackCreate(
            appointment_id=1,
            overall_rating=5,
            punctuality_rating=4,
            feedback_text="Great session!",
            improvement_areas=["More practice problems"],
            positive_highlights=["Clear explanations"]
        )
        
        assert feedback.overall_rating == 5
        assert feedback.requires_followup is False
        
        # Test rating bounds
        with pytest.raises(ValueError):
            SessionFeedbackCreate(
                appointment_id=1,
                overall_rating=6  # Invalid - max is 5
            )
        
        with pytest.raises(ValueError):
            SessionFeedbackCreate(
                appointment_id=1,
                overall_rating=0  # Invalid - min is 1
            )
    
    def test_learning_goal_create_validation(self):
        """Test StudentLearningGoalCreate validation."""
        # Valid goal
        goal = StudentLearningGoalCreate(
            subject_area="Mathematics",
            goal_title="Master Algebra Basics",
            goal_description="Focus on linear equations and graphing",
            target_date=date.today() + timedelta(days=30)
        )
        
        assert goal.subject_area == "Mathematics"
        assert goal.assigned_tutor_id is None
        
        # Test field validation
        with pytest.raises(ValueError):
            StudentLearningGoalCreate(
                subject_area="",  # Invalid - min length 1
                goal_title="Test"
            )
        
        with pytest.raises(ValueError):
            StudentLearningGoalCreate(
                subject_area="Math",
                goal_title=""  # Invalid - min length 1
            )


class TestEnums:
    """Test enum values."""
    
    def test_period_type_values(self):
        """Test PeriodType enum values."""
        assert PeriodType.DAILY.value == "daily"
        assert PeriodType.WEEKLY.value == "weekly"
        assert PeriodType.MONTHLY.value == "monthly"
        assert PeriodType.QUARTERLY.value == "quarterly"
        assert PeriodType.YEARLY.value == "yearly"
    
    def test_goal_status_values(self):
        """Test GoalStatus enum values."""
        assert GoalStatus.ACTIVE.value == "active"
        assert GoalStatus.COMPLETED.value == "completed"
        assert GoalStatus.PAUSED.value == "paused"
        assert GoalStatus.CANCELLED.value == "cancelled"
    
    def test_feedback_role_values(self):
        """Test FeedbackRole enum values."""
        assert FeedbackRole.TUTOR_TO_STUDENT.value == "tutor_to_student"
        assert FeedbackRole.STUDENT_TO_TUTOR.value == "student_to_tutor"
        assert FeedbackRole.PARENT_TO_TUTOR.value == "parent_to_tutor"
    
    def test_metric_type_values(self):
        """Test MetricType enum values."""
        assert MetricType.USER_ACTIVITY.value == "user_activity"
        assert MetricType.SESSION_METRICS.value == "session_metrics"
        assert MetricType.FINANCIAL_METRICS.value == "financial_metrics"
        assert MetricType.GEOGRAPHIC_METRICS.value == "geographic_metrics"


class TestDashboardModels:
    """Test dashboard response models."""
    
    def test_tutor_dashboard_data(self):
        """Test TutorDashboardData model."""
        metrics = TutorPerformanceMetrics(
            metric_id=1,
            tutor_id=1,
            period_start=date.today(),
            period_end=date.today(),
            period_type=PeriodType.MONTHLY,
            total_sessions=10,
            completed_sessions=8,
            total_revenue=Decimal("800.00"),
            average_rating=Decimal("4.5"),
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        dashboard = TutorDashboardData(
            performance_metrics=metrics,
            recent_feedback=[],
            subject_performance={},
            student_progress=[],
            earnings_trend=[],
            upcoming_sessions=3,
            pending_feedback=2
        )
        
        assert dashboard.performance_metrics.tutor_id == 1
        assert dashboard.upcoming_sessions == 3
        assert dashboard.pending_feedback == 2
    
    def test_platform_analytics_distributions(self):
        """Test PlatformAnalytics with distributions."""
        analytics = PlatformAnalytics(
            analytics_id=1,
            metric_date=date.today(),
            metric_type=MetricType.USER_ACTIVITY,
            active_clients=50,
            active_tutors=20,
            active_students=45,
            new_registrations=5,
            total_bookings=30,
            completed_sessions=25,
            daily_revenue=Decimal("2500.00"),
            subject_distribution={"Math": 10, "Science": 8, "English": 7},
            peak_hours={"14": 5, "15": 8, "16": 6},
            time_slot_distribution={"morning": 8, "afternoon": 15, "evening": 7},
            geographic_distribution={"H3H": 10, "H2X": 8, "H4A": 7}
        )
        
        assert analytics.active_clients == 50
        assert analytics.subject_distribution["Math"] == 10
        assert analytics.peak_hours["15"] == 8
        assert len(analytics.geographic_distribution) == 3