import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { X, Clock, Calendar, DollarSign, TrendingUp, User, AlertCircle, Plus } from 'lucide-react';
import { Modal } from '../common/Modal';
import Button from '../common/Button';
import { Badge } from '../common/Badge';
import { Input } from '../common/Input';
import { subscriptionService, Subscription, SubscriptionUsage, SubscriptionStatus } from '../../services/subscriptionService';
import { format } from 'date-fns';
import toast from 'react-hot-toast';

interface SubscriptionDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  subscription: Subscription;
  onUpdate: () => void;
  canManage: boolean;
}

const SubscriptionDetailsModal: React.FC<SubscriptionDetailsModalProps> = ({
  isOpen,
  onClose,
  subscription,
  onUpdate,
  canManage
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [usage, setUsage] = useState<SubscriptionUsage[]>([]);
  const [totalHoursUsed, setTotalHoursUsed] = useState(0);
  const [showAddHours, setShowAddHours] = useState(false);
  const [addHoursData, setAddHoursData] = useState({
    hours: 0,
    reason: ''
  });

  useEffect(() => {
    if (isOpen) {
      loadUsage();
    }
  }, [isOpen, subscription.subscription_id]);

  const loadUsage = async () => {
    try {
      const response = await subscriptionService.getSubscriptionUsage(subscription.subscription_id);
      setUsage(response.items);
      setTotalHoursUsed(response.total_hours);
    } catch (error) {
      console.error('Error loading usage:', error);
    }
  };

  const getStatusBadge = () => {
    switch (subscription.status) {
      case SubscriptionStatus.ACTIVE:
        return <Badge variant="success">{t('billing.subscriptions.status.active')}</Badge>;
      case SubscriptionStatus.PAUSED:
        return <Badge variant="warning">{t('billing.subscriptions.status.paused')}</Badge>;
      case SubscriptionStatus.CANCELLED:
        return <Badge variant="danger">{t('billing.subscriptions.status.cancelled')}</Badge>;
      case SubscriptionStatus.EXPIRED:
        return <Badge variant="secondary">{t('billing.subscriptions.status.expired')}</Badge>;
      default:
        return null;
    }
  };

  const handleAddHours = async () => {
    if (!addHoursData.hours || !addHoursData.reason) {
      toast.error(t('validation.requiredFields'));
      return;
    }

    setLoading(true);
    try {
      await subscriptionService.addHours(subscription.subscription_id, addHoursData);
      toast.success(t('billing.subscriptions.addHoursSuccess'));
      setShowAddHours(false);
      setAddHoursData({ hours: 0, reason: '' });
      onUpdate();
      onClose();
    } catch (error) {
      console.error('Error adding hours:', error);
      toast.error(t('billing.subscriptions.addHoursError'));
    } finally {
      setLoading(false);
    }
  };

  const handleToggleAutoRenew = async () => {
    setLoading(true);
    try {
      await subscriptionService.updateSubscription(subscription.subscription_id, {
        auto_renew: !subscription.auto_renew
      });
      toast.success(t('billing.subscriptions.updateSuccess'));
      onUpdate();
    } catch (error) {
      console.error('Error updating subscription:', error);
      toast.error(t('billing.subscriptions.updateError'));
    } finally {
      setLoading(false);
    }
  };

  const usagePercentage = (subscription.hours_used / subscription.hours_purchased) * 100;

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              {t('billing.subscriptions.details')}
            </h2>
            <div className="flex items-center space-x-3 mt-2">
              <span className="text-lg text-gray-600">{subscription.plan_name}</span>
              {getStatusBadge()}
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left Column - Subscription Info */}
          <div className="space-y-6">
            {/* Client Info */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                {t('billing.subscriptions.clientInfo')}
              </h3>
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center">
                  <User className="w-5 h-5 text-gray-400 mr-3" />
                  <div>
                    <div className="font-medium text-gray-900">{subscription.client_name}</div>
                    <div className="text-sm text-gray-600">
                      {t('billing.subscriptions.clientId')}: {subscription.client_id}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Hours Usage */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                {t('billing.subscriptions.hoursUsage')}
              </h3>
              <div className="bg-gray-50 rounded-lg p-4 space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">{t('billing.subscriptions.hoursUsed')}</span>
                  <span className="font-semibold">{subscription.hours_used} hrs</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">{t('billing.subscriptions.hoursRemaining')}</span>
                  <span className="font-semibold text-green-600">{subscription.hours_remaining} hrs</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">{t('billing.subscriptions.totalHours')}</span>
                  <span className="font-semibold">{subscription.hours_purchased} hrs</span>
                </div>
                
                {/* Progress Bar */}
                <div>
                  <div className="flex items-center justify-between text-sm mb-1">
                    <span className="text-gray-600">{t('billing.subscriptions.usageProgress')}</span>
                    <span className="font-medium">{usagePercentage.toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div
                      className={`h-3 rounded-full transition-all ${
                        usagePercentage > 80 ? 'bg-red-500' : usagePercentage > 60 ? 'bg-yellow-500' : 'bg-green-500'
                      }`}
                      style={{ width: `${Math.min(usagePercentage, 100)}%` }}
                    />
                  </div>
                </div>

                {canManage && !showAddHours && (
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={() => setShowAddHours(true)}
                    className="w-full"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    {t('billing.subscriptions.addHours')}
                  </Button>
                )}

                {showAddHours && (
                  <div className="space-y-3 pt-3 border-t">
                    <Input
                      type="number"
                      placeholder={t('billing.subscriptions.numberOfHours')}
                      value={addHoursData.hours}
                      onChange={(e) => setAddHoursData(prev => ({ ...prev, hours: parseFloat(e.target.value) || 0 }))}
                      min="0"
                      step="0.5"
                    />
                    <Input
                      type="text"
                      placeholder={t('billing.subscriptions.reason')}
                      value={addHoursData.reason}
                      onChange={(e) => setAddHoursData(prev => ({ ...prev, reason: e.target.value }))}
                    />
                    <div className="flex space-x-2">
                      <Button
                        variant="primary"
                        size="sm"
                        onClick={handleAddHours}
                        loading={loading}
                        className="flex-1"
                      >
                        {t('common.add')}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setShowAddHours(false);
                          setAddHoursData({ hours: 0, reason: '' });
                        }}
                        className="flex-1"
                      >
                        {t('common.cancel')}
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Billing Info */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                {t('billing.subscriptions.billingInfo')}
              </h3>
              <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">{t('billing.subscriptions.amount')}</span>
                  <span className="font-semibold text-lg">
                    ${subscription.amount} {subscription.currency}
                  </span>
                </div>
                {subscription.payment_method && (
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">{t('billing.subscriptions.paymentMethod')}</span>
                    <span className="font-medium">{subscription.payment_method}</span>
                  </div>
                )}
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">{t('billing.subscriptions.autoRenew')}</span>
                  <div className="flex items-center space-x-2">
                    <Badge variant={subscription.auto_renew ? 'success' : 'secondary'} size="sm">
                      {subscription.auto_renew ? t('common.enabled') : t('common.disabled')}
                    </Badge>
                    {canManage && subscription.status === SubscriptionStatus.ACTIVE && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleToggleAutoRenew}
                        loading={loading}
                      >
                        {t('common.toggle')}
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Dates */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                {t('billing.subscriptions.dates')}
              </h3>
              <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">{t('billing.subscriptions.startDate')}</span>
                  <span className="font-medium">{format(new Date(subscription.start_date), 'PPP')}</span>
                </div>
                {subscription.end_date && (
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">{t('billing.subscriptions.endDate')}</span>
                    <span className="font-medium">{format(new Date(subscription.end_date), 'PPP')}</span>
                  </div>
                )}
                {subscription.next_billing_date && (
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">{t('billing.subscriptions.nextBilling')}</span>
                    <span className="font-medium">{format(new Date(subscription.next_billing_date), 'PPP')}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Right Column - Usage History */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-3">
              {t('billing.subscriptions.usageHistory')}
            </h3>
            <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
              {usage.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Clock className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                  <p>{t('billing.subscriptions.noUsageYet')}</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {usage.map((item) => (
                    <div key={item.usage_id} className="bg-white rounded-lg p-3 border border-gray-200">
                      <div className="flex items-start justify-between mb-2">
                        <div>
                          <div className="font-medium text-gray-900">{item.subject}</div>
                          <div className="text-sm text-gray-600">{item.tutor_name}</div>
                        </div>
                        <div className="text-right">
                          <div className="font-semibold text-gray-900">{item.hours_used} hrs</div>
                          <div className="text-xs text-gray-500">
                            {format(new Date(item.session_date), 'PP')}
                          </div>
                        </div>
                      </div>
                      {item.notes && (
                        <div className="text-sm text-gray-600 mt-2">{item.notes}</div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="mt-6 pt-6 border-t flex justify-end">
          <Button variant="secondary" onClick={onClose}>
            {t('common.close')}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default SubscriptionDetailsModal;