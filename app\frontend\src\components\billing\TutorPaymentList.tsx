import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { format, startOfWeek, endOfWeek, addDays } from 'date-fns';
import { 
  Search, Filter, Download, Eye, DollarSign, Calendar, 
  ChevronLeft, ChevronRight, User, Clock, CheckCircle, 
  XCircle, AlertCircle, RefreshCw, Send
} from 'lucide-react';
import api from '../../services/api';
import { Card } from '../common/Card';
import Button from '../common/Button';
import { Input } from '../common/Input';
import { Select } from '../common/Select';
import { Badge } from '../common/Badge';
import { Modal } from '../common/Modal';
import LoadingSpinner from '../ui/LoadingSpinner';
import { EmptyState } from '../common/EmptyState';
import toast from 'react-hot-toast';

interface TutorPayment {
  payment_id: number;
  tutor_id: number;
  tutor_name: string;
  payment_period_start: string;
  payment_period_end: string;
  total_hours: number;
  base_amount: number;
  bonus_amount: number;
  total_amount: number;
  currency: string;
  status: 'pending' | 'approved' | 'processing' | 'paid' | 'rejected';
  created_at: string;
  approved_at?: string;
  paid_at?: string;
  session_count: number;
}

interface PaymentPeriod {
  week_number: number;
  period_start: string;
  period_end: string;
  year: number;
}

interface TutorPaymentListResponse {
  payments: TutorPayment[];
  total: number;
  limit: number;
  offset: number;
  has_more: boolean;
}

const TutorPaymentList: React.FC = () => {
  const { t } = useTranslation();
  const [payments, setPayments] = useState<TutorPayment[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20);
  
  // Filters
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [selectedPeriod, setSelectedPeriod] = useState<PaymentPeriod | null>(null);
  const [paymentPeriods, setPaymentPeriods] = useState<PaymentPeriod[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  
  // Selection for bulk actions
  const [selectedPayments, setSelectedPayments] = useState<Set<number>>(new Set());
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [processingAction, setProcessingAction] = useState(false);
  
  // Modal states
  const [selectedPayment, setSelectedPayment] = useState<number | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  // Fetch payment periods for the current year
  useEffect(() => {
    const fetchPaymentPeriods = async () => {
      try {
        const currentYear = new Date().getFullYear();
        const response = await api.get<PaymentPeriod[]>(`/billing/payment-periods/${currentYear}`);
        setPaymentPeriods(response.data);
        
        // Set current period as default
        const currentDate = new Date();
        const currentPeriod = response.data.find(period => {
          const start = new Date(period.period_start);
          const end = new Date(period.period_end);
          return currentDate >= start && currentDate <= end;
        });
        
        if (currentPeriod) {
          setSelectedPeriod(currentPeriod);
        }
      } catch (error) {
        console.error('Error fetching payment periods:', error);
      }
    };
    
    fetchPaymentPeriods();
  }, []);

  const fetchPayments = useCallback(async () => {
    try {
      setLoading(true);
      
      const params = new URLSearchParams();
      if (statusFilter) params.append('status', statusFilter);
      if (selectedPeriod) {
        params.append('period_start', selectedPeriod.period_start);
        params.append('period_end', selectedPeriod.period_end);
      }
      params.append('limit', pageSize.toString());
      params.append('offset', ((currentPage - 1) * pageSize).toString());

      const response = await api.get<TutorPaymentListResponse>(`/billing/tutor-payments?${params}`);
      
      // Filter by search term on client side if needed
      let filteredPayments = response.data.payments;
      if (searchTerm) {
        const term = searchTerm.toLowerCase();
        filteredPayments = filteredPayments.filter(payment =>
          payment.tutor_name.toLowerCase().includes(term)
        );
      }
      
      setPayments(filteredPayments);
      setTotalCount(response.data.total);
    } catch (error) {
      console.error('Error fetching tutor payments:', error);
      toast.error(t('billing.errors.fetchPayments'));
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize, statusFilter, selectedPeriod, searchTerm, t]);

  useEffect(() => {
    if (selectedPeriod) {
      fetchPayments();
    }
  }, [fetchPayments, selectedPeriod]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return <Badge variant="success">{t('billing.tutorPayments.status.paid')}</Badge>;
      case 'approved':
        return <Badge variant="info">{t('billing.tutorPayments.status.approved')}</Badge>;
      case 'processing':
        return <Badge variant="warning">{t('billing.tutorPayments.status.processing')}</Badge>;
      case 'pending':
        return <Badge variant="secondary">{t('billing.tutorPayments.status.pending')}</Badge>;
      case 'rejected':
        return <Badge variant="danger">{t('billing.tutorPayments.status.rejected')}</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD'
    }).format(amount);
  };

  const handleSelectPayment = (paymentId: number) => {
    const newSelection = new Set(selectedPayments);
    if (newSelection.has(paymentId)) {
      newSelection.delete(paymentId);
    } else {
      newSelection.add(paymentId);
    }
    setSelectedPayments(newSelection);
  };

  const handleSelectAll = () => {
    if (selectedPayments.size === payments.filter(p => p.status === 'pending').length) {
      setSelectedPayments(new Set());
    } else {
      const pendingIds = payments
        .filter(p => p.status === 'pending')
        .map(p => p.payment_id);
      setSelectedPayments(new Set(pendingIds));
    }
  };

  const handleBulkApprove = async (approved: boolean) => {
    try {
      setProcessingAction(true);
      
      const response = await api.post('/billing/tutor-payments/approve', {
        payment_ids: Array.from(selectedPayments),
        approved
      });
      
      toast.success(
        approved 
          ? t('billing.tutorPayments.paymentsApproved', { count: selectedPayments.size })
          : t('billing.tutorPayments.paymentsRejected', { count: selectedPayments.size })
      );
      
      setSelectedPayments(new Set());
      setShowApprovalModal(false);
      fetchPayments();
    } catch (error) {
      console.error('Error approving payments:', error);
      toast.error(t('billing.errors.approvePayments'));
    } finally {
      setProcessingAction(false);
    }
  };

  const handleGeneratePayments = async () => {
    if (!selectedPeriod) return;
    
    try {
      setProcessingAction(true);
      
      const response = await api.post('/billing/tutor-payments/generate-weekly', {
        period_start: selectedPeriod.period_start,
        period_end: selectedPeriod.period_end
      });
      
      toast.success(t('billing.tutorPayments.paymentsGenerated', { count: response.data.count }));
      fetchPayments();
    } catch (error) {
      console.error('Error generating payments:', error);
      toast.error(t('billing.errors.generatePayments'));
    } finally {
      setProcessingAction(false);
    }
  };

  const handleProcessPayments = async () => {
    if (!selectedPeriod) return;
    
    try {
      setProcessingAction(true);
      
      const response = await api.post('/billing/tutor-payments/process-approved', {
        period_start: selectedPeriod.period_start,
        period_end: selectedPeriod.period_end
      });
      
      toast.success(t('billing.tutorPayments.paymentsProcessed', { count: response.data.count }));
      fetchPayments();
    } catch (error) {
      console.error('Error processing payments:', error);
      toast.error(t('billing.errors.processPayments'));
    } finally {
      setProcessingAction(false);
    }
  };

  const clearFilters = () => {
    setSearchTerm('');
    setStatusFilter('');
    setCurrentPage(1);
  };

  const totalPages = Math.ceil(totalCount / pageSize);
  const hasSelectedPayments = selectedPayments.size > 0;
  const pendingCount = payments.filter(p => p.status === 'pending').length;
  const approvedCount = payments.filter(p => p.status === 'approved').length;

  if (loading && payments.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Period Selection and Actions */}
      <div className="bg-white rounded-xl shadow-soft p-6">
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Period Selection */}
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('billing.tutorPayments.paymentPeriod')}
            </label>
            <Select
              value={selectedPeriod ? `${selectedPeriod.period_start}_${selectedPeriod.period_end}` : ''}
              onChange={(e) => {
                const [start, end] = e.target.value.split('_');
                const period = paymentPeriods.find(
                  p => p.period_start === start && p.period_end === end
                );
                setSelectedPeriod(period || null);
                setCurrentPage(1);
              }}
              className="w-full"
            >
              <option value="">{t('common.selectPeriod')}</option>
              {paymentPeriods.map((period) => (
                <option 
                  key={`${period.period_start}_${period.period_end}`}
                  value={`${period.period_start}_${period.period_end}`}
                >
                  Week {period.week_number} - {format(new Date(period.period_start), 'MMM d')} to {format(new Date(period.period_end), 'MMM d, yyyy')}
                </option>
              ))}
            </Select>
          </div>

          {/* Actions */}
          <div className="flex items-end gap-3">
            <Button
              variant="secondary"
              leftIcon={<RefreshCw className="w-4 h-4" />}
              onClick={handleGeneratePayments}
              disabled={!selectedPeriod || processingAction}
            >
              {t('billing.tutorPayments.generatePayments')}
            </Button>
            
            {approvedCount > 0 && (
              <Button
                variant="primary"
                leftIcon={<Send className="w-4 h-4" />}
                onClick={handleProcessPayments}
                disabled={processingAction}
              >
                {t('billing.tutorPayments.processPayments')} ({approvedCount})
              </Button>
            )}
          </div>
        </div>

        {/* Search and Filters */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <Input
                  type="text"
                  placeholder={t('billing.tutorPayments.searchTutors')}
                  value={searchTerm}
                  onChange={(e) => {
                    setSearchTerm(e.target.value);
                    setCurrentPage(1);
                  }}
                  className="pl-10"
                />
              </div>
            </div>
            
            {/* Filter Toggle */}
            <Button
              variant="secondary"
              leftIcon={<Filter className="w-4 h-4" />}
              onClick={() => setShowFilters(!showFilters)}
              className="relative"
            >
              {t('common.filters')}
              {statusFilter && (
                <span className="absolute -top-1 -right-1 w-2 h-2 bg-accent-red rounded-full"></span>
              )}
            </Button>
          </div>

          {/* Expanded Filters */}
          {showFilters && (
            <div className="mt-4">
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                {/* Status Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('billing.status.label')}
                  </label>
                  <Select
                    value={statusFilter}
                    onChange={(e) => {
                      setStatusFilter(e.target.value);
                      setCurrentPage(1);
                    }}
                  >
                    <option value="">{t('common.all')}</option>
                    <option value="pending">{t('billing.tutorPayments.status.pending')}</option>
                    <option value="approved">{t('billing.tutorPayments.status.approved')}</option>
                    <option value="processing">{t('billing.tutorPayments.status.processing')}</option>
                    <option value="paid">{t('billing.tutorPayments.status.paid')}</option>
                    <option value="rejected">{t('billing.tutorPayments.status.rejected')}</option>
                  </Select>
                </div>
              </div>

              {/* Clear Filters */}
              <div className="mt-4 flex justify-end">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearFilters}
                >
                  {t('common.clearFilters')}
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Bulk Actions Bar */}
      {hasSelectedPayments && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 flex items-center justify-between">
          <span className="text-sm text-yellow-800">
            {t('billing.tutorPayments.selectedPayments', { count: selectedPayments.size })}
          </span>
          <div className="flex gap-3">
            <Button
              variant="secondary"
              size="sm"
              onClick={() => setSelectedPayments(new Set())}
            >
              {t('common.clearSelection')}
            </Button>
            <Button
              variant="success"
              size="sm"
              leftIcon={<CheckCircle className="w-4 h-4" />}
              onClick={() => setShowApprovalModal(true)}
            >
              {t('billing.tutorPayments.approveSelected')}
            </Button>
            <Button
              variant="danger"
              size="sm"
              leftIcon={<XCircle className="w-4 h-4" />}
              onClick={() => handleBulkApprove(false)}
            >
              {t('billing.tutorPayments.rejectSelected')}
            </Button>
          </div>
        </div>
      )}

      {/* Payments List */}
      {payments.length === 0 ? (
        <Card>
          <EmptyState
            icon={<DollarSign className="w-12 h-12 text-gray-400" />}
            title={t('billing.tutorPayments.noPayments')}
            description={t('billing.tutorPayments.noPaymentsDescription')}
            action={
              selectedPeriod && (
                <Button
                  variant="primary"
                  leftIcon={<RefreshCw className="w-4 h-4" />}
                  onClick={handleGeneratePayments}
                >
                  {t('billing.tutorPayments.generatePayments')}
                </Button>
              )
            }
          />
        </Card>
      ) : (
        <div className="bg-white rounded-xl shadow-soft overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {pendingCount > 0 && (
                    <th className="px-6 py-3 text-left">
                      <input
                        type="checkbox"
                        className="rounded border-gray-300 text-accent-red focus:ring-accent-red"
                        checked={selectedPayments.size === pendingCount}
                        onChange={handleSelectAll}
                      />
                    </th>
                  )}
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('billing.tutorPayments.tutor')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('billing.tutorPayments.period')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('billing.tutorPayments.hours')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('billing.tutorPayments.baseAmount')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('billing.tutorPayments.bonus')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('billing.tutorPayments.total')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('billing.status.label')}
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('common.actions')}
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {payments.map((payment) => (
                  <tr key={payment.payment_id} className="hover:bg-gray-50 transition-colors">
                    {pendingCount > 0 && (
                      <td className="px-6 py-4 whitespace-nowrap">
                        {payment.status === 'pending' && (
                          <input
                            type="checkbox"
                            className="rounded border-gray-300 text-accent-red focus:ring-accent-red"
                            checked={selectedPayments.has(payment.payment_id)}
                            onChange={() => handleSelectPayment(payment.payment_id)}
                          />
                        )}
                      </td>
                    )}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <User className="w-4 h-4 text-gray-400 mr-2" />
                        <span className="text-sm font-medium text-gray-900">
                          {payment.tutor_name}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">
                        {format(new Date(payment.payment_period_start), 'MMM d')} - {format(new Date(payment.payment_period_end), 'MMM d')}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-900">
                        <Clock className="w-4 h-4 text-gray-400 mr-1" />
                        {payment.total_hours.toFixed(1)}h
                      </div>
                      <div className="text-xs text-gray-500">
                        {payment.session_count} {t('billing.tutorPayments.sessions')}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatCurrency(payment.base_amount)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {payment.bonus_amount > 0 ? formatCurrency(payment.bonus_amount) : '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {formatCurrency(payment.total_amount)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(payment.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <Button
                        variant="ghost"
                        size="sm"
                        leftIcon={<Eye className="w-4 h-4" />}
                        onClick={() => {
                          setSelectedPayment(payment.payment_id);
                          setShowDetailsModal(true);
                        }}
                      >
                        {t('common.view')}
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="bg-gray-50 px-6 py-3 flex items-center justify-between border-t border-gray-200">
              <div className="flex-1 flex justify-between sm:hidden">
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                >
                  {t('common.previous')}
                </Button>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                >
                  {t('common.next')}
                </Button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    {t('common.showing')} <span className="font-medium">{((currentPage - 1) * pageSize) + 1}</span> {t('common.to')}{' '}
                    <span className="font-medium">{Math.min(currentPage * pageSize, totalCount)}</span> {t('common.of')}{' '}
                    <span className="font-medium">{totalCount}</span> {t('common.results')}
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <button
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <ChevronLeft className="h-5 w-5" />
                    </button>
                    {[...Array(Math.min(5, totalPages))].map((_, idx) => {
                      const pageNumber = currentPage - 2 + idx;
                      if (pageNumber < 1 || pageNumber > totalPages) return null;
                      return (
                        <button
                          key={pageNumber}
                          onClick={() => setCurrentPage(pageNumber)}
                          className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                            pageNumber === currentPage
                              ? 'z-10 bg-accent-red border-accent-red text-white'
                              : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                          }`}
                        >
                          {pageNumber}
                        </button>
                      );
                    })}
                    <button
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                      className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <ChevronRight className="h-5 w-5" />
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Approval Confirmation Modal */}
      <Modal
        isOpen={showApprovalModal}
        onClose={() => setShowApprovalModal(false)}
        title={t('billing.tutorPayments.confirmApproval')}
      >
        <div className="p-6">
          <p className="text-gray-600 mb-6">
            {t('billing.tutorPayments.confirmApprovalMessage', { count: selectedPayments.size })}
          </p>
          <div className="flex justify-end gap-3">
            <Button
              variant="secondary"
              onClick={() => setShowApprovalModal(false)}
            >
              {t('common.cancel')}
            </Button>
            <Button
              variant="success"
              leftIcon={<CheckCircle className="w-4 h-4" />}
              onClick={() => handleBulkApprove(true)}
              disabled={processingAction}
            >
              {t('billing.tutorPayments.approvePayments')}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Payment Details Modal - To be implemented */}
      {showDetailsModal && selectedPayment && (
        <Modal
          isOpen={showDetailsModal}
          onClose={() => {
            setShowDetailsModal(false);
            setSelectedPayment(null);
          }}
          title={t('billing.tutorPayments.paymentDetails')}
          size="xl"
        >
          <div className="p-6">
            {/* Payment details component would go here */}
            <p className="text-gray-600">Payment details view to be implemented...</p>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default TutorPaymentList;