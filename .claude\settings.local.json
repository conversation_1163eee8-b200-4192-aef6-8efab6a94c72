{"permissions": {"allow": ["Bash(ls:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(python -m pytest tests/unit/test_main.py -v)", "Bash(python3 -m pytest tests/unit/test_main.py -v)", "Bash(find:*)", "<PERSON><PERSON>(chmod:*)", "Bash(python -m pytest tests/unit/test_models.py -v)", "Bash(python3 -m pytest tests/unit/test_models.py -v)", "Bash(pip install:*)", "Bash(pip3 install:*)", "Bash(python3 -m pip install:*)", "<PERSON><PERSON>(apt:*)", "Bash(apt install:*)", "<PERSON><PERSON>(sudo apt:*)", "Bash(sudo apt install:*)", "<PERSON><PERSON>(python3:*)", "Bash(python -m pytest tests/unit/test_database_security.py -v)", "Bash(rm:*)", "Bash(python test_logging_standalone.py:*)", "Bash(python -m pytest tests/unit/ -v)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(uv:*)", "<PERSON><PERSON>(source:*)", "Bash(grep:*)", "<PERSON><PERSON>(mv:*)", "Bash(pytest:*)", "<PERSON><PERSON>(sed:*)", "Bash(python -m pytest tests/unit/test_middleware.py -v)", "Bash(python -m pytest tests/unit/test_exceptions.py tests/unit/test_middleware.py tests/unit/test_database.py tests/unit/test_models.py tests/unit/test_config.py tests/unit/test_main.py tests/unit/test_pytest_config.py tests/unit/test_localization.py tests/unit/test_branding.py tests/unit/test_branding_config.py tests/unit/test_database_security.py tests/unit/test_logging_framework.py tests/unit/test_repository_user.py tests/unit/test_repository_client.py tests/unit/test_repository_tutor.py tests/unit/test_repository_appointment.py tests/unit/test_repository_billing.py -v)", "<PERSON><PERSON>(python:*)", "Bash(.venv/bin/python:*)", "<PERSON><PERSON>(touch:*)", "Bash(PGPASSWORD=\"$POSTGRES_PASSWORD\" psql -h \"$POSTGRES_HOST\" -U \"$POSTGRES_USER\" -d \"$POSTGRES_DB\" -c \"SELECT table_name FROM information_schema.tables WHERE table_name = 'client_profiles';\")", "Bash(npm test)", "Bash(npm install)", "Bash(npm install:*)", "Bash(for file in /mnt/c/Users/<USER>/OneDrive/Documents/tutorAide_app/app/api/v1/{sms_conversations.py,tutor_matching.py,geocoding.py,services.py})", "Bash(do if [ -f \"$file\" ])", "Bash(then sed -i 's/regex=/pattern=/g' \"$file\")", "Bash(fi)", "Bash(done)", "Bash(.venv/Scripts/python.exe:*)", "Bash(../.venv/Scripts/python.exe:*)", "Bash(.venv/Scripts/uvicorn.exe app.main:app --reload --host 0.0.0.0 --port 8000)", "Bash(UV_PYTHON=\".venv/Scripts/python.exe\" uv pip install cachetools==5.3.2)", "Bash(rg:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(curl:*)", "Bash(# Fix all remaining migrations\nfor file in app/database/migrations/018_add_pricing_management.sql app/database/migrations/019_add_payment_security.sql app/database/migrations/020_add_encryption_audit.sql; do\n    if [ -f \"$file\" ]; then\n        sed -i 's/REFERENCES users/REFERENCES user_accounts/g' \"$file\"\n        echo \"Fixed: $file\"\n    fi\ndone)", "Bash(for:*)", "Bash(do)", "Bash(if grep -q \"GRANT.*tutoraide_app\" \"$file\")", "<PERSON><PERSON>(then)", "Bash(echo \"Fixed GRANT in: $file\")", "<PERSON><PERSON>(true)", "Bash(npm run dev:*)", "Bash(./cleanup_project.sh:*)", "<PERSON><PERSON>(git clone:*)", "Bash(timeout 5 uv run uvicorn app.main:app --host 0.0.0.0 --port 8000)", "mcp__serena__activate_project", "mcp__serena__initial_instructions", "mcp__serena__list_dir", "mcp__serena__search_for_pattern", "mcp__serena__read_file", "mcp__serena__think_about_whether_you_are_done", "mcp__serena__summarize_changes", "Bash(git add:*)", "Bash(git commit:*)", "Bash(cp:*)", "Bash(git revert:*)", "Bash(git checkout:*)", "Bash(if ! grep -q \"async def $method\\|def $method\" /mnt/c/Users/<USER>/OneDrive/Documents/TutorAide_app/app/services/analytics_service.py)", "<PERSON><PERSON>(echo:*)", "Bash(git push:*)", "Bash(npm run build:*)", "Bash(./fix-imports.sh)", "Bash(./fix-all-imports.sh)", "Bash(timeout 90 npm run build 2>&1)", "Bash(NODE_ENV=production npx vite build --minify false)", "mcp__filesystem__read_file", "mcp__sequential-thinking__sequentialthinking", "mcp__browser-tools__getNetworkLogs", "mcp__sequential-thinking__sequentialthinking", "Bash(./test_password_reset_final.sh:*)", "Bash(git reset:*)", "mcp__puppeteer__puppeteer_navigate", "mcp__puppeteer__puppeteer_screenshot", "mcp__puppeteer__puppeteer_evaluate", "mcp__browser-tools__getConsoleLogs", "mcp__sentry__whoami", "mcp__sentry__find_organizations", "mcp__sentry__find_issues", "mcp__sentry__find_projects", "mcp__filesystem__list_directory", "mcp__filesystem__edit_file", "mcp__filesystem__write_file", "mcp__sentry__get_issue_details", "mcp__sentry__begin_seer_issue_fix", "mcp__sentry__find_errors", "Bash(npm run type-check:*)"], "deny": []}}