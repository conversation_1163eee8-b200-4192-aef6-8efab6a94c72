"""
Database security utilities and connection encryption for TutorAide.
"""

import ssl
import logging
from typing import Dict, Any, List, Optional
from urllib.parse import quote_plus
from pathlib import Path
from datetime import datetime

from app.config.settings import settings


logger = logging.getLogger(__name__)


class DatabaseSecurity:
    """Database security configuration and utilities."""
    
    @staticmethod
    def get_secure_connection_string() -> str:
        """Generate secure database connection string with encryption."""
        # Use DATABASE_URL directly if it's a complete URL
        if settings.DATABASE_URL and settings.DATABASE_URL.startswith('postgresql://'):
            base_url = settings.DATABASE_URL
        else:
            # Fall back to building from components
            encoded_password = quote_plus(settings.DATABASE_PASSWORD) if settings.DATABASE_PASSWORD else ''
            base_url = f"postgresql://{settings.DATABASE_USER}:{encoded_password}@{settings.DATABASE_HOST}:{settings.DATABASE_PORT}/{settings.DATABASE_NAME}"
        
        # SSL/TLS parameters for encryption
        ssl_params = []
        
        # SSL configuration for Railway and other cloud providers
        # Railway provides SSL automatically, we just need to require it
        if not settings.DEBUG:
            ssl_params.append("sslmode=require")
        else:
            # In development, prefer SSL but don't require it
            ssl_params.append("sslmode=prefer")
        
        # Connection pool and timeout settings for security
        # Note: asyncpg doesn't support these as connection string parameters
        # They should be passed to create_pool() instead
        security_params = []
        
        # Combine all parameters
        all_params = ssl_params + security_params
        
        if all_params:
            connection_string = f"{base_url}?{'&'.join(all_params)}"
        else:
            connection_string = base_url
            
        return connection_string
    
    @staticmethod
    def validate_ssl_certificates() -> Dict[str, bool]:
        """Validate SSL certificate files exist and are readable."""
        cert_files = {
            "client_cert": "/certs/client-cert.pem",
            "client_key": "/certs/client-key.pem",
            "ca_cert": "/certs/ca-cert.pem"
        }
        
        validation_results = {}
        
        for cert_name, cert_path in cert_files.items():
            cert_file = Path(cert_path)
            validation_results[cert_name] = cert_file.exists() and cert_file.is_file()
            
            if not validation_results[cert_name]:
                logger.warning(f"SSL certificate not found: {cert_path}")
        
        return validation_results
    
    @staticmethod
    def get_ssl_context() -> Optional[ssl.SSLContext]:
        """Create SSL context for database connections."""
        if settings.DEBUG:
            # In development, don't require SSL
            return None
            
        try:
            context = ssl.create_default_context(ssl.Purpose.SERVER_AUTH)
            context.check_hostname = False  # PostgreSQL uses IP addresses
            context.verify_mode = ssl.CERT_REQUIRED
            
            # Load client certificates
            cert_validation = DatabaseSecurity.validate_ssl_certificates()
            
            if all(cert_validation.values()):
                context.load_cert_chain(
                    "/certs/client-cert.pem",
                    "/certs/client-key.pem"
                )
                context.load_verify_locations("/certs/ca-cert.pem")
                logger.info("SSL context configured with client certificates")
            else:
                logger.warning("SSL certificates not available, using default context")
            
            return context
            
        except Exception as e:
            logger.error(f"Failed to create SSL context: {e}")
            return None
    
    @staticmethod
    def sanitize_sql_identifier(identifier: str) -> str:
        """Sanitize SQL identifiers (table names, column names) to prevent injection."""
        # Remove any characters that aren't alphanumeric or underscore
        sanitized = ''.join(c for c in identifier if c.isalnum() or c == '_')
        
        # Ensure it doesn't start with a number
        if sanitized and sanitized[0].isdigit():
            sanitized = f"_{sanitized}"
        
        # Limit length to prevent buffer overflow attacks
        sanitized = sanitized[:63]  # PostgreSQL identifier limit
        
        if not sanitized:
            raise ValueError("Invalid SQL identifier")
            
        return sanitized
    
    @staticmethod
    def validate_query_parameters(params: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and sanitize query parameters to prevent injection."""
        validated_params = {}
        
        for key, value in params.items():
            # Sanitize parameter names
            clean_key = DatabaseSecurity.sanitize_sql_identifier(key)
            
            # Basic validation for parameter values
            if value is None:
                validated_params[clean_key] = None
            elif isinstance(value, (str, int, float, bool)):
                validated_params[clean_key] = value
            elif isinstance(value, datetime):
                # Support datetime objects for timestamps
                validated_params[clean_key] = value
            elif isinstance(value, (list, tuple)):
                # For IN clauses, validate each item
                validated_list = []
                for item in value:
                    if isinstance(item, (str, int, float)):
                        validated_list.append(item)
                    else:
                        raise ValueError(f"Invalid list parameter type: {type(item)}")
                validated_params[clean_key] = validated_list
            else:
                raise ValueError(f"Unsupported parameter type: {type(value)}")
        
        return validated_params
    
    @staticmethod
    def log_security_event(event_type: str, details: Dict[str, Any], user_id: Optional[str] = None):
        """Log security-related database events for auditing."""
        log_entry = {
            "event_type": event_type,
            "user_id": user_id,
            "details": details,
            "timestamp": "now()"
        }
        
        # In production, this would write to a secure audit log
        logger.info(f"Security event: {event_type}", extra=log_entry)
    
    @staticmethod
    def get_connection_limits() -> Dict[str, int]:
        """Get secure connection pool limits."""
        return {
            "min_size": 5,
            "max_size": 20,
            "max_queries": 50000,  # Queries per connection before rotation
            "max_inactive_connection_lifetime": 300,  # 5 minutes
            "timeout": 10,  # Connection timeout
            "command_timeout": 60  # Query timeout
        }
    
    @staticmethod
    def create_security_indexes() -> List[str]:
        """Generate SQL commands for security-focused database indexes."""
        security_indexes = [
            # User authentication indexes (updated for user_accounts table)
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_accounts_email_hash ON user_accounts USING HASH (email) WHERE deleted_at IS NULL;",
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_accounts_google_id ON user_accounts (google_id) WHERE deleted_at IS NULL AND google_id IS NOT NULL;",
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_accounts_user_id ON user_accounts (user_id) WHERE deleted_at IS NULL;",
            
            # Auth tokens indexes (replaces user_sessions)
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_auth_tokens_hash ON auth_tokens USING HASH (token_hash) WHERE used_at IS NULL;",
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_auth_tokens_user_type ON auth_tokens (user_id, token_type) WHERE used_at IS NULL;",
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_auth_tokens_expiry ON auth_tokens (expires_at) WHERE used_at IS NULL;",
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_auth_tokens_type ON auth_tokens (token_type) WHERE used_at IS NULL;",
            
            # User roles indexes
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_roles_user_id ON user_roles (user_id) WHERE is_active = true;",
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_roles_type ON user_roles (role_type) WHERE is_active = true;",
            
            # Note: Skipping audit_logs indexes as table doesn't exist yet
            # "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_login_failures ON audit_logs (event_type, created_at) WHERE event_type = 'login_failure';",
            # "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_security_events ON audit_logs (event_type, user_id, created_at) WHERE event_type IN ('login_failure', 'password_reset', 'role_change');",
            
            # Note: These indexes will be created when the corresponding tables exist
            # "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_appointments_tutor_date ON appointments (tutor_id, scheduled_at) WHERE deleted_at IS NULL;",
            # "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_appointments_client_date ON appointments (client_id, scheduled_at) WHERE deleted_at IS NULL;",
            # "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_billing_invoices_client_date ON billing_invoices (client_id, created_at) WHERE deleted_at IS NULL;",
            
            # Soft delete optimization indexes for existing tables
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_accounts_active ON user_accounts (user_id) WHERE deleted_at IS NULL;",
            # "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_clients_active ON clients (client_id) WHERE deleted_at IS NULL;",
            # "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tutors_active ON tutors (tutor_id) WHERE deleted_at IS NULL;",
            
            # Email search optimization
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_accounts_email_search ON user_accounts (LOWER(email)) WHERE deleted_at IS NULL;",
        ]
        
        return security_indexes


class QueryValidator:
    """SQL query validation and security checking."""
    
    # Dangerous SQL keywords that should not appear in user input
    DANGEROUS_KEYWORDS = {
        'drop', 'delete', 'truncate', 'create', 'alter', 'grant', 'revoke',
        'insert', 'update', 'exec', 'execute', 'sp_', 'xp_', 'script',
        'declare', 'cursor', 'fetch', 'union', 'information_schema',
        'pg_', 'mysql', 'sqlite_master'
    }
    
    @staticmethod
    def validate_search_query(query: str) -> str:
        """Validate and sanitize search queries."""
        if not query or not isinstance(query, str):
            raise ValueError("Search query must be a non-empty string")
        
        # Limit query length
        if len(query) > 200:
            raise ValueError("Search query too long")
        
        # Convert to lowercase for checking
        query_lower = query.lower()
        
        # Check for dangerous keywords
        for keyword in QueryValidator.DANGEROUS_KEYWORDS:
            if keyword in query_lower:
                raise ValueError(f"Invalid search query: contains restricted keyword '{keyword}'")
        
        # Remove potentially dangerous characters
        dangerous_chars = [';', '--', '/*', '*/', 'xp_', 'sp_']
        for char in dangerous_chars:
            if char in query_lower:
                raise ValueError(f"Invalid search query: contains restricted sequence '{char}'")
        
        # Basic sanitization - keep only alphanumeric, spaces, and safe punctuation
        sanitized = ''.join(c for c in query if c.isalnum() or c in ' .-_@')
        
        # Trim whitespace
        sanitized = sanitized.strip()
        
        if not sanitized:
            raise ValueError("Search query contains no valid characters")
        
        return sanitized
    
    @staticmethod
    def validate_order_by(column: str, direction: str = 'ASC') -> tuple[str, str]:
        """Validate ORDER BY clauses to prevent injection."""
        # Sanitize column name
        clean_column = DatabaseSecurity.sanitize_sql_identifier(column)
        
        # Validate direction
        if direction.upper() not in ('ASC', 'DESC'):
            raise ValueError("Invalid sort direction")
        
        return clean_column, direction.upper()
    
    @staticmethod
    def validate_limit_offset(limit: Optional[int], offset: Optional[int] = None) -> tuple[int, int]:
        """Validate LIMIT and OFFSET values."""
        # Default and maximum limits
        default_limit = 50
        max_limit = 1000
        
        if limit is None:
            limit = default_limit
        elif not isinstance(limit, int) or limit < 1:
            raise ValueError("Limit must be a positive integer")
        elif limit > max_limit:
            limit = max_limit
        
        if offset is None:
            offset = 0
        elif not isinstance(offset, int) or offset < 0:
            raise ValueError("Offset must be a non-negative integer")
        
        return limit, offset


class SecurityMonitor:
    """Monitor database operations for security threats."""
    
    @staticmethod
    def log_suspicious_activity(activity_type: str, details: Dict[str, Any], user_id: Optional[str] = None):
        """Log suspicious database activity."""
        logger.warning(
            f"Suspicious database activity detected: {activity_type}",
            extra={
                "activity_type": activity_type,
                "user_id": user_id,
                "details": details,
                "timestamp": "now()"
            }
        )
    
    @staticmethod
    def check_query_patterns(query: str, params: Dict[str, Any]) -> bool:
        """Check for suspicious query patterns."""
        suspicious_patterns = [
            "UNION ALL SELECT",
            "'; DROP TABLE",
            "1=1",
            "OR 1=1",
            "'; INSERT INTO",
            "EXEC(",
            "EXECUTE(",
            "--"
        ]
        
        query_upper = query.upper()
        
        for pattern in suspicious_patterns:
            if pattern in query_upper:
                SecurityMonitor.log_suspicious_activity(
                    "sql_injection_attempt",
                    {"query": query[:200], "pattern": pattern, "params": list(params.keys())}
                )
                return False
        
        return True
    
    @staticmethod
    def monitor_connection_attempts(connection_info: Dict[str, Any]):
        """Monitor database connection attempts for anomalies."""
        # Log connection attempts for security analysis
        logger.info(
            "Database connection attempt",
            extra={
                "host": connection_info.get("host"),
                "database": connection_info.get("database"),
                "ssl_enabled": connection_info.get("ssl_enabled", False),
                "timestamp": "now()"
            }
        )