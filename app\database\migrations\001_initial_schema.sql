-- TutorAide Initial Database Schema
-- Version: 001
-- Description: Creates the initial database schema with persona-based table naming
-- Author: TutorAide Development Team
-- Date: 2025-06-10

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm"; -- For text search optimization

-- <PERSON><PERSON> custom types
CREATE TYPE user_role_type AS ENUM ('manager', 'tutor', 'client');
CREATE TYPE consent_level AS ENUM ('level_1', 'level_2');
CREATE TYPE consent_type AS ENUM ('terms_of_service', 'privacy_policy', 'marketing');
CREATE TYPE relationship_type AS ENUM ('parent', 'guardian', 'self');
CREATE TYPE participant_type AS ENUM ('client', 'dependant', 'tutor');
CREATE TYPE attendance_status AS ENUM ('scheduled', 'present', 'absent', 'cancelled');
CREATE TYPE appointment_status AS ENUM ('scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show');
CREATE TYPE payment_status AS ENUM ('pending', 'processing', 'paid', 'failed', 'refunded', 'cancelled');
CREATE TYPE time_off_status AS ENUM ('requested', 'approved', 'rejected', 'cancelled');
CREATE TYPE platform_type AS ENUM ('ios', 'android', 'web');
CREATE TYPE notification_type AS ENUM ('appointment_reminder', 'payment_due', 'session_confirmed', 'general');

-- ============================================
-- Core User Tables
-- ============================================

-- User accounts table (base authentication)
CREATE TABLE user_accounts (
    user_id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255),
    google_id VARCHAR(255) UNIQUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    CONSTRAINT email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- User roles (many-to-many: users can have multiple roles)
CREATE TABLE user_roles (
    user_role_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    role_type user_role_type NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, role_type)
);

-- User consents tracking
CREATE TABLE user_consents (
    consent_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    consent_level consent_level NOT NULL,
    consent_type consent_type NOT NULL,
    accepted_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    ip_address INET,
    UNIQUE(user_id, consent_level, consent_type)
);

-- Password reset tokens
CREATE TABLE password_reset_tokens (
    reset_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    token UUID NOT NULL DEFAULT uuid_generate_v4() UNIQUE,
    expires_at TIMESTAMP NOT NULL,
    used_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Tutor invitations
CREATE TABLE tutor_invitations (
    invitation_id SERIAL PRIMARY KEY,
    manager_id INTEGER NOT NULL REFERENCES user_accounts(user_id),
    email VARCHAR(255) NOT NULL,
    invitation_token UUID NOT NULL DEFAULT uuid_generate_v4() UNIQUE,
    expires_at TIMESTAMP NOT NULL,
    used_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Push notification tokens
CREATE TABLE push_notification_tokens (
    token_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    device_token VARCHAR(500) NOT NULL,
    platform platform_type NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, device_token)
);

-- Notification preferences
CREATE TABLE notification_preferences (
    preference_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    notification_type notification_type NOT NULL,
    push_enabled BOOLEAN DEFAULT true,
    sms_enabled BOOLEAN DEFAULT true,
    email_enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, notification_type)
);

-- ============================================
-- Client Tables
-- ============================================

-- Client profiles
CREATE TABLE client_profiles (
    client_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    address JSONB, -- Store as JSON: {street, city, province, postal_code, country}
    emergency_contact JSONB, -- Store as JSON: {name, phone, relationship}
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id)
);

-- Client dependants (children)
CREATE TABLE client_dependants (
    dependant_id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES user_accounts(user_id), -- Can be NULL for young children
    primary_client_id INTEGER NOT NULL REFERENCES client_profiles(client_id),
    secondary_client_id INTEGER REFERENCES client_profiles(client_id), -- For separated parents
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    birth_date DATE NOT NULL,
    relationship_type relationship_type NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT different_parents CHECK (primary_client_id != secondary_client_id),
    CONSTRAINT valid_birth_date CHECK (birth_date <= CURRENT_DATE)
);

-- Client relationships (tracks which clients can access which dependants)
CREATE TABLE client_relationships (
    relationship_id SERIAL PRIMARY KEY,
    client_id INTEGER NOT NULL REFERENCES client_profiles(client_id) ON DELETE CASCADE,
    dependant_id INTEGER NOT NULL REFERENCES client_dependants(dependant_id) ON DELETE CASCADE,
    relationship_type relationship_type NOT NULL,
    is_primary BOOLEAN DEFAULT false,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(client_id, dependant_id)
);

-- Client learning needs
CREATE TABLE client_needs (
    need_id SERIAL PRIMARY KEY,
    client_id INTEGER REFERENCES client_profiles(client_id),
    dependant_id INTEGER REFERENCES client_dependants(dependant_id),
    subject_area VARCHAR(50) NOT NULL,
    service_type VARCHAR(50) NOT NULL,
    location_preference VARCHAR(50),
    frequency_preference VARCHAR(50),
    duration_preference INTEGER, -- in minutes
    notes TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT client_or_dependant CHECK (
        (client_id IS NOT NULL AND dependant_id IS NULL) OR 
        (client_id IS NULL AND dependant_id IS NOT NULL)
    )
);

-- ============================================
-- Tutor Tables
-- ============================================

-- Tutor profiles
CREATE TABLE tutor_profiles (
    tutor_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    bio TEXT,
    specializations TEXT[], -- Array of specializations
    postal_code VARCHAR(10),
    latitude DECIMAL(10, 7),
    longitude DECIMAL(10, 7),
    profile_photo_url VARCHAR(500),
    verification_status VARCHAR(20) DEFAULT 'pending', -- pending, basic_approved, fully_verified, premium
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id)
);

-- Tutor availability schedule
CREATE TABLE tutor_availability (
    availability_id SERIAL PRIMARY KEY,
    tutor_id INTEGER NOT NULL REFERENCES tutor_profiles(tutor_id) ON DELETE CASCADE,
    day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6), -- 0=Sunday, 6=Saturday
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT valid_time_range CHECK (end_time > start_time),
    UNIQUE(tutor_id, day_of_week, start_time)
);

-- Tutor time off requests
CREATE TABLE tutor_time_off (
    time_off_id SERIAL PRIMARY KEY,
    tutor_id INTEGER NOT NULL REFERENCES tutor_profiles(tutor_id) ON DELETE CASCADE,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    reason TEXT,
    status time_off_status NOT NULL DEFAULT 'requested',
    approved_by INTEGER REFERENCES user_accounts(user_id),
    approved_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT valid_date_range CHECK (end_date >= start_date)
);

-- Tutor service rates
CREATE TABLE tutor_service_rates (
    rate_id SERIAL PRIMARY KEY,
    tutor_id INTEGER NOT NULL REFERENCES tutor_profiles(tutor_id) ON DELETE CASCADE,
    service_id INTEGER NOT NULL,
    hourly_rate DECIMAL(10, 2) NOT NULL CHECK (hourly_rate > 0),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tutor_id, service_id)
);

-- Tutor service areas
CREATE TABLE tutor_service_areas (
    area_id SERIAL PRIMARY KEY,
    tutor_id INTEGER NOT NULL REFERENCES tutor_profiles(tutor_id) ON DELETE CASCADE,
    postal_code VARCHAR(10) NOT NULL,
    max_distance_km DECIMAL(5, 2) DEFAULT 10.0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tutor_id, postal_code)
);

-- ============================================
-- Service Tables
-- ============================================

-- Service types (online, in-person, library, hybrid)
CREATE TABLE service_types (
    type_id SERIAL PRIMARY KEY,
    type_name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT
);

-- Subject areas (mathematics, science, french, english, others)
CREATE TABLE subject_areas (
    subject_id SERIAL PRIMARY KEY,
    subject_name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT
);

-- Location preferences
CREATE TABLE location_preferences (
    location_id SERIAL PRIMARY KEY,
    location_name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT
);

-- Frequency options
CREATE TABLE frequency_options (
    frequency_id SERIAL PRIMARY KEY,
    frequency_name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT
);

-- Duration options
CREATE TABLE duration_options (
    duration_id SERIAL PRIMARY KEY,
    duration_minutes INTEGER NOT NULL UNIQUE,
    description TEXT
);

-- Service catalog
CREATE TABLE service_catalog (
    service_id SERIAL PRIMARY KEY,
    service_name VARCHAR(200) NOT NULL,
    subject_area VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    is_group_session BOOLEAN DEFAULT false,
    min_participants INTEGER DEFAULT 1,
    max_participants INTEGER DEFAULT 1,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT valid_participants CHECK (max_participants >= min_participants AND min_participants > 0)
);

-- Service packages (for TECFEE and bulk sessions)
CREATE TABLE service_packages (
    package_id SERIAL PRIMARY KEY,
    service_id INTEGER NOT NULL REFERENCES service_catalog(service_id),
    package_name VARCHAR(200) NOT NULL,
    session_count INTEGER NOT NULL CHECK (session_count > 0),
    total_price DECIMAL(10, 2) NOT NULL CHECK (total_price > 0),
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- ============================================
-- Appointment Tables
-- ============================================

-- Appointment sessions
CREATE TABLE appointment_sessions (
    appointment_id SERIAL PRIMARY KEY,
    tutor_id INTEGER NOT NULL REFERENCES tutor_profiles(tutor_id),
    service_id INTEGER NOT NULL REFERENCES service_catalog(service_id),
    package_id INTEGER REFERENCES service_packages(package_id),
    scheduled_date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    status appointment_status NOT NULL DEFAULT 'scheduled',
    location_details JSONB, -- {type: 'online'|'in_person', address: {...}, meeting_link: '...'}
    notes TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT valid_appointment_time CHECK (end_time > start_time)
);

-- Appointment participants
CREATE TABLE appointment_participants (
    participant_id SERIAL PRIMARY KEY,
    appointment_id INTEGER NOT NULL REFERENCES appointment_sessions(appointment_id) ON DELETE CASCADE,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id),
    participant_type participant_type NOT NULL,
    attendance_status attendance_status NOT NULL DEFAULT 'scheduled',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(appointment_id, user_id)
);

-- Appointment confirmations (from tutors)
CREATE TABLE appointment_confirmations (
    confirmation_id SERIAL PRIMARY KEY,
    appointment_id INTEGER NOT NULL REFERENCES appointment_sessions(appointment_id) ON DELETE CASCADE,
    tutor_id INTEGER NOT NULL REFERENCES tutor_profiles(tutor_id),
    is_completed BOOLEAN NOT NULL,
    confirmed_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    UNIQUE(appointment_id)
);

-- ============================================
-- Billing Tables
-- ============================================

-- Billing invoices
CREATE TABLE billing_invoices (
    invoice_id SERIAL PRIMARY KEY,
    invoice_number VARCHAR(50) NOT NULL UNIQUE,
    client_id INTEGER NOT NULL REFERENCES client_profiles(client_id),
    appointment_id INTEGER REFERENCES appointment_sessions(appointment_id),
    amount DECIMAL(10, 2) NOT NULL CHECK (amount >= 0),
    currency VARCHAR(3) NOT NULL DEFAULT 'CAD',
    status payment_status NOT NULL DEFAULT 'pending',
    due_date DATE NOT NULL,
    paid_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Billing payments
CREATE TABLE billing_payments (
    payment_id SERIAL PRIMARY KEY,
    invoice_id INTEGER NOT NULL REFERENCES billing_invoices(invoice_id),
    paying_client_id INTEGER NOT NULL REFERENCES client_profiles(client_id),
    stripe_payment_id VARCHAR(255) UNIQUE,
    amount DECIMAL(10, 2) NOT NULL CHECK (amount > 0),
    payment_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    payment_method VARCHAR(50), -- card, bank_transfer, etc.
    status payment_status NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Billing subscriptions
CREATE TABLE billing_subscriptions (
    subscription_id SERIAL PRIMARY KEY,
    client_id INTEGER NOT NULL REFERENCES client_profiles(client_id),
    service_id INTEGER NOT NULL REFERENCES service_catalog(service_id),
    hours_purchased DECIMAL(5, 2) NOT NULL CHECK (hours_purchased > 0),
    hours_remaining DECIMAL(5, 2) NOT NULL CHECK (hours_remaining >= 0),
    expires_at DATE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT valid_hours CHECK (hours_remaining <= hours_purchased)
);

-- Tutor payment records
CREATE TABLE billing_tutor_payments (
    tutor_payment_id SERIAL PRIMARY KEY,
    tutor_id INTEGER NOT NULL REFERENCES tutor_profiles(tutor_id),
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    total_hours DECIMAL(5, 2) NOT NULL DEFAULT 0,
    total_amount DECIMAL(10, 2) NOT NULL DEFAULT 0,
    status payment_status NOT NULL DEFAULT 'pending',
    stripe_payout_id VARCHAR(255),
    paid_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT valid_period CHECK (period_end >= period_start)
);

-- ============================================
-- Indexes for Performance
-- ============================================

-- User tables indexes
CREATE INDEX idx_user_accounts_email ON user_accounts(email) WHERE deleted_at IS NULL;
CREATE INDEX idx_user_accounts_google_id ON user_accounts(google_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_user_accounts_deleted_at ON user_accounts(deleted_at);
CREATE INDEX idx_user_roles_user_id ON user_roles(user_id) WHERE is_active = true;
CREATE INDEX idx_user_consents_user_id ON user_consents(user_id);
CREATE INDEX idx_password_reset_tokens_token ON password_reset_tokens(token) WHERE used_at IS NULL;
CREATE INDEX idx_password_reset_tokens_expires_at ON password_reset_tokens(expires_at) WHERE used_at IS NULL;

-- Client tables indexes
CREATE INDEX idx_client_profiles_user_id ON client_profiles(user_id);
CREATE INDEX idx_client_dependants_primary_client ON client_dependants(primary_client_id);
CREATE INDEX idx_client_dependants_secondary_client ON client_dependants(secondary_client_id);
CREATE INDEX idx_client_relationships_client_id ON client_relationships(client_id);
CREATE INDEX idx_client_relationships_dependant_id ON client_relationships(dependant_id);
CREATE INDEX idx_client_needs_active ON client_needs(client_id, dependant_id) WHERE is_active = true;

-- Tutor tables indexes
CREATE INDEX idx_tutor_profiles_user_id ON tutor_profiles(user_id);
CREATE INDEX idx_tutor_profiles_postal_code ON tutor_profiles(postal_code);
CREATE INDEX idx_tutor_profiles_location ON tutor_profiles(latitude, longitude);
CREATE INDEX idx_tutor_availability_tutor_id ON tutor_availability(tutor_id) WHERE is_active = true;
CREATE INDEX idx_tutor_time_off_tutor_dates ON tutor_time_off(tutor_id, start_date, end_date);
CREATE INDEX idx_tutor_service_rates_tutor_service ON tutor_service_rates(tutor_id, service_id);

-- Service tables indexes
CREATE INDEX idx_service_catalog_active ON service_catalog(service_id) WHERE is_active = true;
CREATE INDEX idx_service_catalog_subject ON service_catalog(subject_area) WHERE is_active = true;
CREATE INDEX idx_service_packages_service ON service_packages(service_id) WHERE is_active = true;

-- Appointment tables indexes
CREATE INDEX idx_appointment_sessions_tutor_date ON appointment_sessions(tutor_id, scheduled_date);
CREATE INDEX idx_appointment_sessions_status ON appointment_sessions(status) WHERE status != 'cancelled';
CREATE INDEX idx_appointment_sessions_date ON appointment_sessions(scheduled_date) WHERE status != 'cancelled';
CREATE INDEX idx_appointment_participants_appointment ON appointment_participants(appointment_id);
CREATE INDEX idx_appointment_participants_user ON appointment_participants(user_id);

-- Billing tables indexes
CREATE INDEX idx_billing_invoices_client ON billing_invoices(client_id);
CREATE INDEX idx_billing_invoices_status ON billing_invoices(status) WHERE status != 'paid';
CREATE INDEX idx_billing_invoices_due_date ON billing_invoices(due_date) WHERE status = 'pending';
CREATE INDEX idx_billing_payments_invoice ON billing_payments(invoice_id);
CREATE INDEX idx_billing_subscriptions_client ON billing_subscriptions(client_id) WHERE is_active = true;
CREATE INDEX idx_billing_tutor_payments_tutor ON billing_tutor_payments(tutor_id);
CREATE INDEX idx_billing_tutor_payments_period ON billing_tutor_payments(period_start, period_end);

-- ============================================
-- Triggers for Updated Timestamps
-- ============================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for all tables with updated_at
CREATE TRIGGER update_user_accounts_updated_at BEFORE UPDATE ON user_accounts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_push_notification_tokens_updated_at BEFORE UPDATE ON push_notification_tokens
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notification_preferences_updated_at BEFORE UPDATE ON notification_preferences
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_client_profiles_updated_at BEFORE UPDATE ON client_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_client_dependants_updated_at BEFORE UPDATE ON client_dependants
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_client_needs_updated_at BEFORE UPDATE ON client_needs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tutor_profiles_updated_at BEFORE UPDATE ON tutor_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tutor_availability_updated_at BEFORE UPDATE ON tutor_availability
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tutor_time_off_updated_at BEFORE UPDATE ON tutor_time_off
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tutor_service_rates_updated_at BEFORE UPDATE ON tutor_service_rates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_service_catalog_updated_at BEFORE UPDATE ON service_catalog
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_service_packages_updated_at BEFORE UPDATE ON service_packages
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_appointment_sessions_updated_at BEFORE UPDATE ON appointment_sessions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_appointment_participants_updated_at BEFORE UPDATE ON appointment_participants
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_billing_invoices_updated_at BEFORE UPDATE ON billing_invoices
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_billing_subscriptions_updated_at BEFORE UPDATE ON billing_subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_billing_tutor_payments_updated_at BEFORE UPDATE ON billing_tutor_payments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================
-- Initial Data Population
-- ============================================

-- Insert service types
INSERT INTO service_types (type_name, description) VALUES
    ('online', 'Online tutoring via video conference'),
    ('in_person', 'In-person tutoring at specified location'),
    ('library', 'Tutoring at public library'),
    ('hybrid', 'Combination of online and in-person');

-- Insert subject areas
INSERT INTO subject_areas (subject_name, description) VALUES
    ('mathematics', 'All levels of mathematics including algebra, calculus, statistics'),
    ('science', 'Physics, chemistry, biology, and general science'),
    ('french', 'French language, grammar, conversation, and literature'),
    ('english', 'English language, writing, literature, and ESL'),
    ('others', 'Other subjects including history, geography, music, etc.');

-- Insert location preferences
INSERT INTO location_preferences (location_name, description) VALUES
    ('home', 'At the client''s home'),
    ('tutor_place', 'At the tutor''s location'),
    ('library', 'At a public library'),
    ('online', 'Online via video conference');

-- Insert frequency options
INSERT INTO frequency_options (frequency_name, description) VALUES
    ('weekly', 'Once per week'),
    ('bi_weekly', 'Every two weeks'),
    ('monthly', 'Once per month'),
    ('as_needed', 'On demand as needed');

-- Insert duration options
INSERT INTO duration_options (duration_minutes, description) VALUES
    (30, '30 minute session'),
    (60, '1 hour session'),
    (90, '1.5 hour session'),
    (120, '2 hour session');

-- ============================================
-- Comments for Documentation
-- ============================================

COMMENT ON TABLE user_accounts IS 'Base authentication table for all users in the system';
COMMENT ON TABLE client_profiles IS 'Profile information for clients (parents/adult learners)';
COMMENT ON TABLE client_dependants IS 'Dependants (children) of clients who receive tutoring';
COMMENT ON TABLE tutor_profiles IS 'Profile information for tutors';
COMMENT ON TABLE appointment_sessions IS 'Scheduled tutoring sessions between tutors and clients/dependants';
COMMENT ON TABLE billing_invoices IS 'Invoices generated for completed sessions';
COMMENT ON TABLE billing_subscriptions IS 'Pre-paid hour packages for clients';
COMMENT ON COLUMN client_dependants.primary_client_id IS 'Main responsible parent for billing';
COMMENT ON COLUMN client_dependants.secondary_client_id IS 'Optional second parent (separated families)';
COMMENT ON COLUMN billing_payments.paying_client_id IS 'Tracks which parent actually paid (for separated families)';