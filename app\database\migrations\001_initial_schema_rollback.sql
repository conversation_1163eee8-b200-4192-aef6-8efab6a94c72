-- TutorAide Initial Database Schema Rollback
-- Version: 001
-- Description: Rollback script for the initial database schema
-- Author: TutorAide Development Team
-- Date: 2025-06-10

-- ============================================
-- Drop Tables in Reverse Order (due to foreign keys)
-- ============================================

-- Drop billing tables
DROP TABLE IF EXISTS billing_tutor_payments CASCADE;
DROP TABLE IF EXISTS billing_subscriptions CASCADE;
DROP TABLE IF EXISTS billing_payments CASCADE;
DROP TABLE IF EXISTS billing_invoices CASCADE;

-- Drop appointment tables
DROP TABLE IF EXISTS appointment_confirmations CASCADE;
DROP TABLE IF EXISTS appointment_participants CASCADE;
DROP TABLE IF EXISTS appointment_sessions CASCADE;

-- Drop service tables
DROP TABLE IF EXISTS service_packages CASCADE;
DROP TABLE IF EXISTS service_catalog CASCADE;
DROP TABLE IF EXISTS duration_options CASCADE;
DROP TABLE IF EXISTS frequency_options CASCADE;
DROP TABLE IF EXISTS location_preferences CASCADE;
DROP TABLE IF EXISTS subject_areas CASCADE;
DROP TABLE IF EXISTS service_types CASCADE;

-- Drop tutor tables
DROP TABLE IF EXISTS tutor_service_areas CASCADE;
DROP TABLE IF EXISTS tutor_service_rates CASCADE;
DROP TABLE IF EXISTS tutor_time_off CASCADE;
DROP TABLE IF EXISTS tutor_availability CASCADE;
DROP TABLE IF EXISTS tutor_profiles CASCADE;

-- Drop client tables
DROP TABLE IF EXISTS client_needs CASCADE;
DROP TABLE IF EXISTS client_relationships CASCADE;
DROP TABLE IF EXISTS client_dependants CASCADE;
DROP TABLE IF EXISTS client_profiles CASCADE;

-- Drop user tables
DROP TABLE IF EXISTS notification_preferences CASCADE;
DROP TABLE IF EXISTS push_notification_tokens CASCADE;
DROP TABLE IF EXISTS tutor_invitations CASCADE;
DROP TABLE IF EXISTS password_reset_tokens CASCADE;
DROP TABLE IF EXISTS user_consents CASCADE;
DROP TABLE IF EXISTS user_roles CASCADE;
DROP TABLE IF EXISTS user_accounts CASCADE;

-- ============================================
-- Drop Functions
-- ============================================

DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;

-- ============================================
-- Drop Custom Types
-- ============================================

DROP TYPE IF EXISTS notification_type CASCADE;
DROP TYPE IF EXISTS platform_type CASCADE;
DROP TYPE IF EXISTS time_off_status CASCADE;
DROP TYPE IF EXISTS payment_status CASCADE;
DROP TYPE IF EXISTS appointment_status CASCADE;
DROP TYPE IF EXISTS attendance_status CASCADE;
DROP TYPE IF EXISTS participant_type CASCADE;
DROP TYPE IF EXISTS relationship_type CASCADE;
DROP TYPE IF EXISTS consent_type CASCADE;
DROP TYPE IF EXISTS consent_level CASCADE;
DROP TYPE IF EXISTS user_role_type CASCADE;

-- ============================================
-- Drop Extensions (optional - might be used by other schemas)
-- ============================================

-- Commented out by default to avoid dropping extensions that might be used elsewhere
-- DROP EXTENSION IF EXISTS "pg_trgm";
-- DROP EXTENSION IF EXISTS "uuid-ossp";