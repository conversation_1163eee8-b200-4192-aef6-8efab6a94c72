import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';
import { Search, Filter, Download, Eye, DollarSign, Calendar, ChevronLeft, ChevronRight, FileText, User, AlertCircle, CheckCircle } from 'lucide-react';
import api from '../../services/api';
import { billingService, Invoice as BillingInvoice } from '../../services/billingService';
import { Card } from '../common/Card';
import Button from '../common/Button';
import { Input } from '../common/Input';
import { Select } from '../common/Select';
import { Badge } from '../common/Badge';
import { Modal } from '../common/Modal';
import LoadingSpinner from '../ui/LoadingSpinner';
import { EmptyState } from '../common/EmptyState';
import InvoiceDisplay from './InvoiceDisplay';
import { StripePaymentModal } from './StripePaymentForm';
import toast from 'react-hot-toast';
import { useAuth } from '../../contexts/AuthContext';
import { UserRoleType } from '../../types/auth';
import { hasPermission } from '../../utils/permissions';

interface Invoice {
  invoice_id: number;
  client_id: number;
  client_name: string;
  client_email?: string;
  invoice_number: string;
  amount: number;
  currency: string;
  tax_amount: number;
  total_amount: number;
  status: 'pending' | 'paid' | 'overdue' | 'cancelled';
  issue_date: string;
  due_date: string;
  paid_date?: string;
  payment_method?: string;
  payment_reference?: string;
  days_overdue?: number;
  line_item_count: number;
}

interface InvoiceListResponse {
  invoices: Invoice[];
  total: number;
  limit: number;
  offset: number;
  has_more: boolean;
}

const InvoiceList: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20);
  
  // Filters
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  
  // Modal states
  const [selectedInvoice, setSelectedInvoice] = useState<number | null>(null);
  const [showInvoiceModal, setShowInvoiceModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentInvoice, setPaymentInvoice] = useState<Invoice | null>(null);
  
  const isClient = user?.activeRole === UserRoleType.CLIENT;
  const canViewAllInvoices = hasPermission(user?.activeRole, 'viewAllInvoices');

  const fetchInvoices = useCallback(async () => {
    try {
      setLoading(true);
      
      const response = await billingService.getInvoices({
        status: statusFilter || undefined,
        client_id: isClient ? user?.userId : undefined,
        start_date: dateFrom || undefined,
        end_date: dateTo || undefined,
        page: currentPage,
        limit: pageSize
      });
      
      // Filter by search term on client side if needed
      let filteredInvoices = response.invoices;
      if (searchTerm) {
        const term = searchTerm.toLowerCase();
        filteredInvoices = filteredInvoices.filter(invoice =>
          invoice.invoice_number.toLowerCase().includes(term) ||
          invoice.client_name.toLowerCase().includes(term)
        );
      }
      
      // Convert billing service invoice format to component format
      const mappedInvoices = filteredInvoices.map(inv => ({
        invoice_id: inv.invoice_id,
        client_id: inv.client_id,
        client_name: inv.client_name,
        client_email: inv.client_email,
        invoice_number: inv.invoice_number,
        amount: inv.amount,
        currency: inv.currency || 'CAD',
        tax_amount: inv.tax_amount || 0,
        total_amount: inv.amount,
        status: inv.status as any,
        issue_date: inv.created_at,
        due_date: inv.due_date,
        paid_date: inv.paid_at,
        payment_method: inv.payment_method,
        payment_reference: inv.stripe_payment_intent_id,
        days_overdue: inv.days_overdue,
        line_item_count: inv.items?.length || 0
      }));
      
      setInvoices(mappedInvoices);
      setTotalCount(response.total);
    } catch (error) {
      console.error('Error fetching invoices:', error);
      toast.error(t('billing.errors.fetchInvoices'));
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize, statusFilter, dateFrom, dateTo, searchTerm, t, isClient, user]);

  useEffect(() => {
    fetchInvoices();
  }, [fetchInvoices]);

  const getStatusBadge = (status: string, daysOverdue?: number) => {
    switch (status) {
      case 'paid':
        return <Badge variant="success" icon={<CheckCircle className="w-3 h-3" />}>{t('billing.status.paid')}</Badge>;
      case 'pending':
        if (daysOverdue && daysOverdue > 0) {
          return (
            <Badge variant="error" icon={<AlertCircle className="w-3 h-3" />}>
              {t('billing.status.overdue')} ({daysOverdue} {t('common.days')})
            </Badge>
          );
        }
        return <Badge variant="warning">{t('billing.status.pending')}</Badge>;
      case 'cancelled':
        return <Badge variant="secondary">{t('billing.status.cancelled')}</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD'
    }).format(amount);
  };

  const handleViewInvoice = (invoiceId: number) => {
    setSelectedInvoice(invoiceId);
    setShowInvoiceModal(true);
  };

  const handlePayInvoice = (invoice: Invoice) => {
    setPaymentInvoice(invoice);
    setShowPaymentModal(true);
  };

  const handlePaymentSuccess = async (paymentIntent: any) => {
    try {
      // Update invoice status to paid
      await api.patch(`/billing/invoices/${paymentInvoice?.invoice_id}/status`, {
        status: 'paid',
        payment_intent_id: paymentIntent.id
      });
      
      toast.success(t('billing.paymentSuccess'));
      fetchInvoices(); // Refresh the list
      setShowPaymentModal(false);
      setPaymentInvoice(null);
    } catch (error) {
      console.error('Error updating invoice status:', error);
      toast.error(t('billing.errors.updateInvoiceStatus'));
    }
  };

  const handleDownloadInvoice = async (invoiceId: number, invoiceNumber: string) => {
    try {
      const blob = await billingService.downloadInvoice(invoiceId);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `invoice-${invoiceNumber}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      
      toast.success(t('billing.invoiceDownloaded'));
    } catch (error) {
      console.error('Error downloading invoice:', error);
      toast.error(t('billing.errors.downloadInvoice'));
    }
  };

  const clearFilters = () => {
    setSearchTerm('');
    setStatusFilter('');
    setDateFrom('');
    setDateTo('');
    setCurrentPage(1);
  };

  const totalPages = Math.ceil(totalCount / pageSize);

  if (loading && invoices.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Search and Filters */}
      <Card className="p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <Input
                type="text"
                placeholder={t('billing.searchInvoices')}
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                  setCurrentPage(1);
                }}
                className="pl-10"
              />
            </div>
          </div>
          
          {/* Filter Toggle */}
          <Button
            variant="secondary"
            leftIcon={<Filter className="w-4 h-4" />}
            onClick={() => setShowFilters(!showFilters)}
            className="relative"
          >
            {t('common.filters')}
            {(statusFilter || dateFrom || dateTo) && (
              <span className="absolute -top-1 -right-1 w-2 h-2 bg-accent-red rounded-full"></span>
            )}
          </Button>
        </div>

        {/* Expanded Filters */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              {/* Status Filter */}
              <div>
                <label className="block text-sm font-medium text-text-secondary mb-1">
                  {t('billing.status.label')}
                </label>
                <Select
                  value={statusFilter}
                  onChange={(e) => {
                    setStatusFilter(e.target.value);
                    setCurrentPage(1);
                  }}
                >
                  <option value="">{t('common.all')}</option>
                  <option value="pending">{t('billing.status.pending')}</option>
                  <option value="paid">{t('billing.status.paid')}</option>
                  <option value="overdue">{t('billing.status.overdue')}</option>
                  <option value="cancelled">{t('billing.status.cancelled')}</option>
                </Select>
              </div>

              {/* Date From */}
              <div>
                <label className="block text-sm font-medium text-text-secondary mb-1">
                  {t('billing.dateFrom')}
                </label>
                <Input
                  type="date"
                  value={dateFrom}
                  onChange={(e) => {
                    setDateFrom(e.target.value);
                    setCurrentPage(1);
                  }}
                />
              </div>

              {/* Date To */}
              <div>
                <label className="block text-sm font-medium text-text-secondary mb-1">
                  {t('billing.dateTo')}
                </label>
                <Input
                  type="date"
                  value={dateTo}
                  onChange={(e) => {
                    setDateTo(e.target.value);
                    setCurrentPage(1);
                  }}
                />
              </div>
            </div>

            {/* Clear Filters */}
            <div className="mt-4 flex justify-end">
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
              >
                {t('common.clearFilters')}
              </Button>
            </div>
          </div>
        )}
      </Card>

      {/* Invoices List */}
      {invoices.length === 0 ? (
        <Card>
          <EmptyState
            icon={<FileText className="w-12 h-12 text-gray-400" />}
            title={t('billing.noInvoices')}
            description={t('billing.noInvoicesDescription')}
          />
        </Card>
      ) : (
        <Card className="overflow-hidden p-0">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('billing.invoiceNumber')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('billing.client')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('billing.issueDate')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('billing.dueDate')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('billing.amount')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('billing.status.label')}
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('common.actions')}
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {invoices.map((invoice) => (
                  <tr key={invoice.invoice_id} className="hover:bg-gray-50 transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <FileText className="w-4 h-4 text-gray-400 mr-2" />
                        <span className="text-sm font-medium text-gray-900">
                          {invoice.invoice_number}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <User className="w-4 h-4 text-gray-400 mr-2" />
                        <span className="text-sm text-gray-900">{invoice.client_name}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-500">
                        <Calendar className="w-4 h-4 mr-1" />
                        {format(new Date(invoice.issue_date), 'MMM d, yyyy')}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-500">
                        <Calendar className="w-4 h-4 mr-1" />
                        {format(new Date(invoice.due_date), 'MMM d, yyyy')}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {formatCurrency(invoice.total_amount)}
                      </div>
                      <div className="text-xs text-gray-500">
                        {invoice.line_item_count} {t('billing.items')}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(invoice.status, invoice.days_overdue)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          leftIcon={<Eye className="w-4 h-4" />}
                          onClick={() => handleViewInvoice(invoice.invoice_id)}
                        >
                          {t('common.view')}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          leftIcon={<Download className="w-4 h-4" />}
                          onClick={() => handleDownloadInvoice(invoice.invoice_id, invoice.invoice_number)}
                        >
                          {t('common.download')}
                        </Button>
                        {invoice.status === 'pending' && isClient && (
                          <Button
                            variant="primary"
                            size="sm"
                            leftIcon={<DollarSign className="w-4 h-4" />}
                            onClick={() => handlePayInvoice(invoice)}
                          >
                            {t('billing.payNow')}
                          </Button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="bg-gray-50 px-6 py-3 flex items-center justify-between border-t border-gray-200">
              <div className="flex-1 flex justify-between sm:hidden">
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                >
                  {t('common.previous')}
                </Button>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                >
                  {t('common.next')}
                </Button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    {t('common.showing')} <span className="font-medium">{((currentPage - 1) * pageSize) + 1}</span> {t('common.to')}{' '}
                    <span className="font-medium">{Math.min(currentPage * pageSize, totalCount)}</span> {t('common.of')}{' '}
                    <span className="font-medium">{totalCount}</span> {t('common.results')}
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <button
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <ChevronLeft className="h-5 w-5" />
                    </button>
                    {[...Array(Math.min(5, totalPages))].map((_, idx) => {
                      const pageNumber = currentPage - 2 + idx;
                      if (pageNumber < 1 || pageNumber > totalPages) return null;
                      return (
                        <button
                          key={pageNumber}
                          onClick={() => setCurrentPage(pageNumber)}
                          className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                            pageNumber === currentPage
                              ? 'z-10 bg-accent-highlight border-accent-highlight text-white'
                              : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                          }`}
                        >
                          {pageNumber}
                        </button>
                      );
                    })}
                    <button
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                      className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <ChevronRight className="h-5 w-5" />
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          )}
        </Card>
      )}

      {/* Invoice Display Modal */}
      {showInvoiceModal && selectedInvoice && (
        <Modal
          isOpen={showInvoiceModal}
          onClose={() => {
            setShowInvoiceModal(false);
            setSelectedInvoice(null);
          }}
          title={t('billing.viewInvoice')}
          size="xl"
        >
          <div className="p-6">
            <InvoiceDisplay invoiceId={selectedInvoice} />
          </div>
        </Modal>
      )}

      {/* Stripe Payment Modal */}
      <StripePaymentModal
        isOpen={showPaymentModal}
        onClose={() => {
          setShowPaymentModal(false);
          setPaymentInvoice(null);
        }}
        amount={paymentInvoice?.total_amount || 0}
        invoiceId={paymentInvoice?.invoice_id}
        onSuccess={handlePaymentSuccess}
      />
    </div>
  );
};

export default InvoiceList;