import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Search, Filter, Plus, Edit2, Trash2, DollarSign, 
  CheckCircle, XCircle, AlertCircle, User, Mail, Phone,
  MapPin, Calendar, Star, Save, X, GraduationCap, Globe
} from 'lucide-react';
import { useApi } from '../../hooks/useApi';
import { Card } from '../../components/common/Card';
import Button from '../../components/common/Button';
import { Input } from '../../components/common/Input';
import { Select } from '../../components/common/Select';
import { Badge } from '../../components/common/Badge';
import { Modal } from '../../components/common/Modal';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import { EmptyState } from '../../components/common/EmptyState';
import { SearchBar } from '../../components/forms/SearchBar';
import { AddUserModal } from '../../components/modals/AddUserModal';
import toast from 'react-hot-toast';
import { useAuth } from '../../contexts/AuthContext';
import { UserRoleType } from '../../types/auth';
import { hasPermission } from '../../utils/permissions';
import { PasswordResetModal } from '../../components/modals/PasswordResetModal';
import { Key } from 'lucide-react';
import { TutorProfileModal } from '../../components/manager/TutorProfileModal';
import { TutorEditModal } from '../../components/modals/TutorEditModal';

interface TutorRate {
  tutor_id: number;
  base_hourly_rate: number;
  online_rate?: number;
  in_person_rate?: number;
  library_rate?: number;
  notes?: string;
  effective_date: string;
  created_by: string;
  updated_at: string;
}

interface Tutor {
  tutor_id: number;
  user_id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  profile_picture?: string;
  bio?: string;
  experience_years?: number;
  education?: string;
  specialties: string[];
  languages: string[];
  availability_status: 'available' | 'limited' | 'busy' | 'inactive';
  verification_status: 'pending' | 'verified' | 'rejected';
  average_rating?: number;
  total_sessions?: number;
  postal_code?: string;
  rates?: TutorRate;
  created_at: string;
  is_active: boolean;
  
  // New education fields
  highest_degree_level?: string;
  highest_degree_name?: string;
  degree_major?: string;
  university_name?: string;
  graduation_year?: number;
  teaching_certifications?: string[];
  
  // New experience fields
  years_of_experience?: number;
  years_online_teaching?: number;
  years_tutoring?: number;
  current_occupation?: string;
  subject_expertise?: Record<string, string>;
  language_proficiency?: Record<string, string>;
  teaching_methodology?: string[];
  special_needs_experience?: boolean;
  age_groups_experience?: string[];
  
  // Portfolio and verification
  portfolio_url?: string;
  achievement_highlights?: string;
  has_references?: boolean;
  reference_check_completed?: boolean;
  background_check_status?: string;
  working_with_children_clearance?: boolean;
  has_liability_insurance?: boolean;
}

const TutorsPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { get, put, del, loading } = useApi();
  
  const [tutors, setTutors] = useState<Tutor[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [showRateModal, setShowRateModal] = useState(false);
  const [selectedTutor, setSelectedTutor] = useState<Tutor | null>(null);
  const [editingRate, setEditingRate] = useState<Partial<TutorRate>>({});
  const [showAddModal, setShowAddModal] = useState(false);
  const [showPasswordResetModal, setShowPasswordResetModal] = useState(false);
  const [passwordResetUser, setPasswordResetUser] = useState<any>(null);
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [selectedTutorForProfile, setSelectedTutorForProfile] = useState<Tutor | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [tutorToEdit, setTutorToEdit] = useState<Tutor | null>(null);
  
  const isClient = user?.activeRole === UserRoleType.CLIENT;
  const canEditRates = hasPermission(user?.activeRole, 'editTutorRates');
  const canAddTutors = hasPermission(user?.activeRole, 'addUsers');
  const canViewDetails = hasPermission(user?.activeRole, 'viewTutorDetails');
  const canResetPassword = hasPermission(user?.activeRole, 'resetUserPassword');
  const canEditTutors = hasPermission(user?.activeRole, 'editUsers');
  const canDeleteTutors = hasPermission(user?.activeRole, 'deleteUsers');


  useEffect(() => {
    fetchTutors();
  }, [statusFilter]);
  
  // Separate effect for search to avoid too many API calls
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchTerm !== '') {
        fetchTutors();
      }
    }, 500); // Debounce search
    
    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  const fetchTutors = async () => {
    try {
      const tutorsData = await get<Tutor[]>('/tutors', {
        params: {
          status: statusFilter !== 'all' ? statusFilter : undefined,
          search: searchTerm || undefined
        }
      });
      setTutors(tutorsData);
    } catch (error) {
      console.error('Error fetching tutors:', error);
      toast.error('Failed to load tutors data');
    }
  };

  const handleSearch = (term: string) => {
    setSearchTerm(term);
  };

  // Tutors are already filtered from the API based on search and status
  const filteredTutors = tutors;

  const handleEditRate = (tutor: Tutor) => {
    setSelectedTutor(tutor);
    setEditingRate({
      tutor_id: tutor.tutor_id,
      base_hourly_rate: tutor.rates?.base_hourly_rate || 0,
      online_rate: tutor.rates?.online_rate || tutor.rates?.base_hourly_rate || 0,
      in_person_rate: tutor.rates?.in_person_rate || tutor.rates?.base_hourly_rate || 0,
      library_rate: tutor.rates?.library_rate || tutor.rates?.base_hourly_rate || 0,
      notes: tutor.rates?.notes || ''
    });
    setShowRateModal(true);
  };

  const handleSaveRate = async () => {
    if (!selectedTutor) return;

    try {
      const updatedRates = await put<TutorRate>(`/tutors/${selectedTutor.tutor_id}/rates`, editingRate);
      
      // Update local state
      setTutors(prev => prev.map(tutor => 
        tutor.tutor_id === selectedTutor.tutor_id 
          ? { ...tutor, rates: updatedRates }
          : tutor
      ));
      
      toast.success('Tutor rate updated successfully');
      setShowRateModal(false);
      setSelectedTutor(null);
      setEditingRate({});
    } catch (error) {
      console.error('Error updating rate:', error);
      toast.error('Failed to update tutor rates');
    }
  };

  const handleAddTutor = async (newTutor: any) => {
    // Reload tutors to get the newly added one
    await fetchTutors();
  };

  const handleEditTutor = (tutor: Tutor) => {
    setTutorToEdit(tutor);
    setShowEditModal(true);
  };

  const handleEditSuccess = async (updatedTutor: Tutor) => {
    // Reload tutors to get the updated data
    await fetchTutors();
    
    toast.success('Tutor updated successfully');
  };

  const handleDeleteTutor = async (tutorId: number) => {
    try {
      await del(`/tutors/${tutorId}`);
      toast.success('Tutor deleted successfully');
      await fetchTutors();
    } catch (error) {
      console.error('Error deleting tutor:', error);
      toast.error('Failed to delete tutor');
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig: Record<string, { color: string; icon: React.ReactNode }> = {
      available: { color: 'bg-green-100 text-green-700', icon: <CheckCircle className="w-4 h-4" /> },
      limited: { color: 'bg-yellow-100 text-yellow-700', icon: <AlertCircle className="w-4 h-4" /> },
      busy: { color: 'bg-red-100 text-red-700', icon: <XCircle className="w-4 h-4" /> },
      inactive: { color: 'bg-gray-100 text-gray-700', icon: <XCircle className="w-4 h-4" /> }
    };

    const config = statusConfig[status] || statusConfig.inactive;
    
    return (
      <Badge className={`inline-flex items-center gap-1 ${config.color}`}>
        {config.icon}
        <span className="capitalize">{status}</span>
      </Badge>
    );
  };

  const getVerificationBadge = (status: string) => {
    const verificationConfig: Record<string, { color: string; label: string }> = {
      verified: { color: 'bg-green-100 text-green-700', label: 'Verified' },
      pending: { color: 'bg-yellow-100 text-yellow-700', label: 'Pending' },
      rejected: { color: 'bg-red-100 text-red-700', label: 'Rejected' }
    };

    const config = verificationConfig[status] || verificationConfig.pending;
    
    return (
      <Badge className={config.color}>
        {config.label}
      </Badge>
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-96">
        <LoadingSpinner size="large" message="Loading tutors..." />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-text-primary">{t('tutors.title', 'Tutors')}</h1>
          <p className="text-text-secondary mt-1">
            {isClient ? 'View available tutors' : 'Manage tutor profiles, rates, and verification status'}
          </p>
        </div>
        {canAddTutors && (
          <Button
            onClick={() => setShowAddModal(true)}
            className="bg-accent-red text-white hover:bg-accent-red-dark"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Tutor
          </Button>
        )}
      </div>

      {/* Filters */}
      <Card className="p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <SearchBar
              placeholder="Search tutors by name or email..."
              onSearch={handleSearch}
              className="w-full"
              value={searchTerm}
            />
          </div>
          <Select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            options={[
              { value: 'all', label: 'All Status' },
              { value: 'available', label: 'Available' },
              { value: 'limited', label: 'Limited' },
              { value: 'busy', label: 'Busy' },
              { value: 'inactive', label: 'Inactive' }
            ]}
            className="w-48"
          />
        </div>
      </Card>

      {/* Tutors List */}
      {loading && tutors.length === 0 ? (
        <div className="grid gap-4">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="p-6 animate-pulse">
              <div className="flex items-start space-x-4">
                <div className="w-16 h-16 bg-gray-200 rounded-full"></div>
                <div className="flex-1">
                  <div className="w-32 h-6 bg-gray-200 rounded mb-2"></div>
                  <div className="w-48 h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="w-64 h-4 bg-gray-200 rounded"></div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      ) : filteredTutors.length === 0 ? (
        <EmptyState
          icon={User}
          title="No tutors found"
          description={searchTerm ? "No tutors match your search criteria" : "No tutors available"}
        />
      ) : (
        <div className="grid gap-4">
          {filteredTutors.map((tutor) => (
            <Card key={tutor.tutor_id} className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4">
                  {/* Profile Picture */}
                  <div className="flex-shrink-0">
                    {tutor.profile_picture ? (
                      <img
                        src={tutor.profile_picture}
                        alt={`${tutor.first_name} ${tutor.last_name}`}
                        className="w-16 h-16 rounded-full object-cover border-2 border-gray-200"
                      />
                    ) : (
                      <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center border-2 border-red-200">
                        <User className="w-8 h-8 text-red-600" />
                      </div>
                    )}
                  </div>

                  {/* Tutor Info */}
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">
                        {tutor.first_name} {tutor.last_name}
                      </h3>
                      {!isClient && getVerificationBadge(tutor.verification_status)}
                      {getStatusBadge(tutor.availability_status)}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-600">
                      {!isClient && (
                        <div className="flex items-center gap-2">
                          <Mail className="w-4 h-4" />
                          {tutor.email}
                        </div>
                      )}
                      {!isClient && tutor.phone && (
                        <div className="flex items-center gap-2">
                          <Phone className="w-4 h-4" />
                          {tutor.phone}
                        </div>
                      )}
                      {tutor.postal_code && (
                        <div className="flex items-center gap-2">
                          <MapPin className="w-4 h-4" />
                          {tutor.postal_code}
                        </div>
                      )}
                      {!isClient && (
                        <div className="flex items-center gap-2">
                          <Calendar className="w-4 h-4" />
                          Member since {new Date(tutor.created_at).toLocaleDateString()}
                        </div>
                      )}
                    </div>

                    {/* Stats */}
                    <div className="flex items-center gap-4 mt-3">
                      {tutor.average_rating && (
                        <div className="flex items-center gap-1">
                          <Star className="w-4 h-4 text-yellow-500 fill-yellow-500" />
                          <span className="font-medium">{tutor.average_rating.toFixed(1)}</span>
                          <span className="text-gray-500">rating</span>
                        </div>
                      )}
                      {tutor.total_sessions && (
                        <div className="text-gray-600">
                          <span className="font-medium">{tutor.total_sessions}</span> sessions
                        </div>
                      )}
                      {tutor.experience_years && (
                        <div className="text-gray-600">
                          <span className="font-medium">{tutor.experience_years}</span> years exp.
                        </div>
                      )}
                    </div>

                    {/* Specialties */}
                    {tutor.specialties.length > 0 && (
                      <div className="flex flex-wrap gap-2 mt-3">
                        {tutor.specialties.map((specialty) => (
                          <Badge key={specialty} variant="secondary">
                            {specialty}
                          </Badge>
                        ))}
                      </div>
                    )}

                    {/* Education & Certifications */}
                    {(tutor.highest_degree_name || tutor.teaching_certifications?.length) && (
                      <div className="mt-3 flex flex-wrap items-center gap-3">
                        {tutor.highest_degree_name && (
                          <div className="flex items-center gap-1 text-sm text-gray-600">
                            <GraduationCap className="w-4 h-4" />
                            <span>{tutor.highest_degree_name}</span>
                            {tutor.university_name && (
                              <span className="text-gray-500">• {tutor.university_name}</span>
                            )}
                          </div>
                        )}
                        {tutor.teaching_certifications && tutor.teaching_certifications.length > 0 && (
                          <div className="flex items-center gap-1">
                            <Badge className="bg-green-100 text-green-700">
                              {tutor.teaching_certifications.length} Certification{tutor.teaching_certifications.length > 1 ? 's' : ''}
                            </Badge>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Languages & Special Skills */}
                    {(tutor.language_proficiency || tutor.special_needs_experience) && (
                      <div className="mt-3 flex flex-wrap items-center gap-3 text-sm">
                        {tutor.language_proficiency && Object.keys(tutor.language_proficiency).length > 0 && (
                          <div className="flex items-center gap-1 text-gray-600">
                            <Globe className="w-4 h-4" />
                            <span>
                              {Object.entries(tutor.language_proficiency)
                                .filter(([_, level]) => level === 'native' || level === 'fluent')
                                .map(([lang]) => lang.toUpperCase())
                                .join(', ')}
                            </span>
                          </div>
                        )}
                        {tutor.special_needs_experience && (
                          <Badge className="bg-purple-100 text-purple-700">
                            Special Needs Experience
                          </Badge>
                        )}
                      </div>
                    )}

                    {/* Verification Badges */}
                    {!isClient && (tutor.background_check_status === 'passed' || tutor.has_references || tutor.has_liability_insurance) && (
                      <div className="mt-3 flex flex-wrap gap-2">
                        {tutor.background_check_status === 'passed' && (
                          <Badge className="bg-blue-100 text-blue-700">
                            <CheckCircle className="w-3 h-3 mr-1" />
                            Background Verified
                          </Badge>
                        )}
                        {tutor.has_references && tutor.reference_check_completed && (
                          <Badge className="bg-blue-100 text-blue-700">
                            <CheckCircle className="w-3 h-3 mr-1" />
                            References Verified
                          </Badge>
                        )}
                        {tutor.has_liability_insurance && (
                          <Badge className="bg-blue-100 text-blue-700">
                            <CheckCircle className="w-3 h-3 mr-1" />
                            Insured
                          </Badge>
                        )}
                      </div>
                    )}

                    {/* Rate Info - Only show to managers */}
                    {!isClient && (
                      <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <DollarSign className="w-4 h-4 text-gray-600" />
                            <span className="text-sm font-medium text-gray-700">Hourly Rates:</span>
                          </div>
                          {canEditRates && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEditRate(tutor)}
                              className="text-accent-red border-accent-red hover:bg-red-50"
                            >
                              <Edit2 className="w-3 h-3 mr-1" />
                              Edit Rates
                            </Button>
                          )}
                        </div>
                      {tutor.rates ? (
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mt-2">
                          <div>
                            <span className="text-xs text-gray-500">Base Rate</span>
                            <p className="font-semibold">${tutor.rates.base_hourly_rate}/hr</p>
                          </div>
                          <div>
                            <span className="text-xs text-gray-500">Online</span>
                            <p className="font-semibold">${tutor.rates.online_rate || tutor.rates.base_hourly_rate}/hr</p>
                          </div>
                          <div>
                            <span className="text-xs text-gray-500">In-Person</span>
                            <p className="font-semibold">${tutor.rates.in_person_rate || tutor.rates.base_hourly_rate}/hr</p>
                          </div>
                          <div>
                            <span className="text-xs text-gray-500">Library</span>
                            <p className="font-semibold">${tutor.rates.library_rate || tutor.rates.base_hourly_rate}/hr</p>
                          </div>
                        </div>
                      ) : (
                        <p className="text-sm text-gray-500 mt-2">No rates set</p>
                      )}
                        {tutor.rates?.notes && (
                          <p className="text-xs text-gray-600 mt-2 italic">{tutor.rates.notes}</p>
                        )}
                      </div>
                    )}
                  </div>
                </div>

                {/* Actions */}
                <div className="flex flex-col gap-2">
                  {canViewDetails && (
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => {
                        setSelectedTutorForProfile(tutor);
                        setShowProfileModal(true);
                      }}
                    >
                      View Profile
                    </Button>
                  )}
                  {canEditTutors && (
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleEditTutor(tutor)}
                    >
                      <Edit2 className="w-3 h-3 mr-1" />
                      Edit Info
                    </Button>
                  )}
                  {canDeleteTutors && (
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => {
                        if (window.confirm(`Are you sure you want to delete ${tutor.first_name} ${tutor.last_name}? This action cannot be undone.`)) {
                          handleDeleteTutor(tutor.tutor_id);
                        }
                      }}
                      className="text-red-600 hover:bg-red-50"
                    >
                      <Trash2 className="w-3 h-3 mr-1" />
                      Delete
                    </Button>
                  )}
                  {canResetPassword && (
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => {
                        setPasswordResetUser({
                          id: tutor.tutor_id,
                          email: tutor.email,
                          firstName: tutor.first_name,
                          lastName: tutor.last_name,
                          userType: 'tutor'
                        });
                        setShowPasswordResetModal(true);
                      }}
                    >
                      <Key className="w-3 h-3 mr-1" />
                      Reset Password
                    </Button>
                  )}
                  {isClient && (
                    <Button
                      size="sm"
                      variant="primary"
                      onClick={() => toast.info('Book session coming soon')}
                    >
                      Book Session
                    </Button>
                  )}
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* Rate Edit Modal */}
      <Modal
        isOpen={showRateModal}
        onClose={() => {
          setShowRateModal(false);
          setSelectedTutor(null);
          setEditingRate({});
        }}
        title={`Edit Rates - ${selectedTutor?.first_name} ${selectedTutor?.last_name}`}
      >
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Base Hourly Rate
              </label>
              <Input
                type="number"
                value={editingRate.base_hourly_rate || ''}
                onChange={(e) => setEditingRate(prev => ({
                  ...prev,
                  base_hourly_rate: parseFloat(e.target.value) || 0
                }))}
                placeholder="0.00"
                min="0"
                step="0.01"
                className="w-full"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Online Rate
              </label>
              <Input
                type="number"
                value={editingRate.online_rate || ''}
                onChange={(e) => setEditingRate(prev => ({
                  ...prev,
                  online_rate: parseFloat(e.target.value) || 0
                }))}
                placeholder="0.00"
                min="0"
                step="0.01"
                className="w-full"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                In-Person Rate
              </label>
              <Input
                type="number"
                value={editingRate.in_person_rate || ''}
                onChange={(e) => setEditingRate(prev => ({
                  ...prev,
                  in_person_rate: parseFloat(e.target.value) || 0
                }))}
                placeholder="0.00"
                min="0"
                step="0.01"
                className="w-full"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Library Rate
              </label>
              <Input
                type="number"
                value={editingRate.library_rate || ''}
                onChange={(e) => setEditingRate(prev => ({
                  ...prev,
                  library_rate: parseFloat(e.target.value) || 0
                }))}
                placeholder="0.00"
                min="0"
                step="0.01"
                className="w-full"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Notes (optional)
            </label>
            <textarea
              value={editingRate.notes || ''}
              onChange={(e) => setEditingRate(prev => ({
                ...prev,
                notes: e.target.value
              }))}
              placeholder="Add any notes about this tutor's rates..."
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-red"
            />
          </div>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <p className="text-sm text-yellow-800">
              <strong>Note:</strong> These are the rates that the platform pays to the tutor. 
              Client rates are managed separately in Service Settings.
            </p>
          </div>

          <div className="flex justify-end gap-3">
            <Button
              variant="ghost"
              onClick={() => {
                setShowRateModal(false);
                setSelectedTutor(null);
                setEditingRate({});
              }}
            >
              <X className="w-4 h-4 mr-2" />
              Cancel
            </Button>
            <Button
              onClick={handleSaveRate}
              className="bg-accent-red text-white hover:bg-accent-red-dark"
            >
              <Save className="w-4 h-4 mr-2" />
              Save Rates
            </Button>
          </div>
        </div>
      </Modal>

      {/* Add Tutor Modal */}
      <AddUserModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        userType="tutor"
        onSuccess={handleAddTutor}
      />
      
      {/* Password Reset Modal */}
      {passwordResetUser && (
        <PasswordResetModal
          isOpen={showPasswordResetModal}
          onClose={() => {
            setShowPasswordResetModal(false);
            setPasswordResetUser(null);
          }}
          user={passwordResetUser}
        />
      )}
      
      {/* Tutor Profile Modal */}
      {selectedTutorForProfile && (
        <TutorProfileModal
          isOpen={showProfileModal}
          onClose={() => {
            setShowProfileModal(false);
            setSelectedTutorForProfile(null);
          }}
          tutor={selectedTutorForProfile}
        />
      )}
      
      {/* Tutor Edit Modal */}
      {tutorToEdit && (
        <TutorEditModal
          isOpen={showEditModal}
          onClose={() => {
            setShowEditModal(false);
            setTutorToEdit(null);
          }}
          tutor={tutorToEdit}
          onSuccess={handleEditSuccess}
        />
      )}
    </div>
  );
};

export default TutorsPage;