import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card } from '../../common/Card';
import Button from '../../common/Button';
import { 
  User, 
  Calendar, 
  School, 
  Target, 
  Edit2, 
  Trash2,
  Heart,
  Pill,
  AlertTriangle
} from 'lucide-react';

interface DependantCardProps {
  dependant: {
    dependant_id: number;
    first_name: string;
    last_name: string;
    date_of_birth: string;
    grade_level: string | null;
    school_name: string | null;
    special_needs: string | null;
    allergies: string | null;
    medications: string | null;
    learning_goals: string | null;
  };
  onEdit: () => void;
  onDelete: () => void;
}

export const DependantCard: React.FC<DependantCardProps> = ({
  dependant,
  onEdit,
  onDelete
}) => {
  const { t } = useTranslation();

  const calculateAge = (dateOfBirth: string) => {
    const birthDate = new Date(dateOfBirth);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  };

  const age = calculateAge(dependant.date_of_birth);
  
  const hasHealthInfo = dependant.special_needs || dependant.allergies || dependant.medications;

  return (
    <Card className="hover:shadow-elevated transition-shadow duration-200">
      <div className="p-6">
        {/* Header */}
        <div className="flex justify-between items-start mb-4">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-accent-red/10 rounded-full flex items-center justify-center">
              <User className="w-6 h-6 text-accent-red" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-text-primary">
                {dependant.first_name} {dependant.last_name}
              </h3>
              <p className="text-sm text-text-secondary">
                {t('client.dependants.age', { age })}
              </p>
            </div>
          </div>
          
          <div className="flex gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={onEdit}
              className="p-2"
            >
              <Edit2 className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onDelete}
              className="p-2 text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <Trash2 className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Info Grid */}
        <div className="space-y-3">
          {/* School Info */}
          {(dependant.grade_level || dependant.school_name) && (
            <div className="flex items-start gap-3">
              <School className="w-4 h-4 text-text-secondary mt-0.5" />
              <div className="flex-1">
                {dependant.grade_level && (
                  <p className="text-sm text-text-primary">
                    {t('client.dependants.grade')} {dependant.grade_level}
                  </p>
                )}
                {dependant.school_name && (
                  <p className="text-sm text-text-secondary">
                    {dependant.school_name}
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Learning Goals */}
          {dependant.learning_goals && (
            <div className="flex items-start gap-3">
              <Target className="w-4 h-4 text-text-secondary mt-0.5" />
              <div className="flex-1">
                <p className="text-sm text-text-secondary line-clamp-2">
                  {dependant.learning_goals}
                </p>
              </div>
            </div>
          )}

          {/* Health Indicators */}
          {hasHealthInfo && (
            <div className="flex items-center gap-3 pt-2 border-t border-gray-100">
              {dependant.special_needs && (
                <div className="flex items-center gap-1 text-amber-600">
                  <Heart className="w-4 h-4" />
                  <span className="text-xs">{t('client.dependants.specialNeeds')}</span>
                </div>
              )}
              {dependant.allergies && (
                <div className="flex items-center gap-1 text-orange-600">
                  <AlertTriangle className="w-4 h-4" />
                  <span className="text-xs">{t('client.dependants.allergies')}</span>
                </div>
              )}
              {dependant.medications && (
                <div className="flex items-center gap-1 text-blue-600">
                  <Pill className="w-4 h-4" />
                  <span className="text-xs">{t('client.dependants.medications')}</span>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="mt-4 pt-4 border-t border-gray-100">
          <Button
            variant="secondary"
            size="sm"
            className="w-full"
            onClick={onEdit}
          >
            {t('client.dependants.viewDetails')}
          </Button>
        </div>
      </div>
    </Card>
  );
};