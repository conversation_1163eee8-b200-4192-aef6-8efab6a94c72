<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TutorAide - Building Frontend</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f9fafb;
            color: #1f2937;
            line-height: 1.6;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 2rem;
            text-align: center;
        }
        
        .card {
            background: white;
            padding: 3rem;
            border-radius: 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }
        
        h1 {
            color: #dc2626;
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        
        .subtitle {
            color: #6b7280;
            font-size: 1.25rem;
            margin-bottom: 2rem;
        }
        
        .status-message {
            background: #fef3c7;
            color: #92400e;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            font-weight: 500;
        }
        
        .info {
            background: #f3f4f6;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: left;
        }
        
        .info h3 {
            color: #1f2937;
            margin-bottom: 0.5rem;
        }
        
        .info p {
            color: #6b7280;
            margin-bottom: 0.5rem;
        }
        
        .spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            margin: 2rem auto;
        }
        
        .spinner:after {
            content: " ";
            display: block;
            width: 32px;
            height: 32px;
            margin: 4px;
            border-radius: 50%;
            border: 3px solid #dc2626;
            border-color: #dc2626 transparent #dc2626 transparent;
            animation: spinner 1.2s linear infinite;
        }
        
        @keyframes spinner {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }
        
        .api-link {
            color: #dc2626;
            text-decoration: none;
            font-weight: 500;
        }
        
        .api-link:hover {
            text-decoration: underline;
        }
        
        code {
            background: #f3f4f6;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
        }
    </style>
    <script>
        // Auto-refresh page every 30 seconds to check if app is ready
        setTimeout(() => {
            window.location.reload();
        }, 30000);
    </script>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1>TutorAide</h1>
            <p class="subtitle">Comprehensive Tutoring Management Platform</p>
            
            <div class="spinner"></div>
            
            <div class="status-message">
                🚧 Frontend application is being built...
            </div>
            
            <div class="info">
                <h3>What's happening?</h3>
                <p>The TutorAide frontend application is currently being compiled and deployed.</p>
                <p>This process usually takes 2-5 minutes during the first deployment.</p>
                <p>This page will automatically refresh every 30 seconds.</p>
            </div>
            
            <div class="info">
                <h3>API Status</h3>
                <p>✅ The API is operational and ready to use</p>
                <p>📍 API Endpoint: <code>/api/v1</code></p>
                <p>📍 Health Check: <a href="/health" class="api-link">/health</a></p>
            </div>
            
            <p style="color: #6b7280; margin-top: 2rem;">
                If this page doesn't update after 5 minutes, please check the deployment logs on Railway.
            </p>
        </div>
    </div>
</body>
</html>