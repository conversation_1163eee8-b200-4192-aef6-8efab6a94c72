-- Rollback Migration 017: Remove Language Preferences System

-- Drop triggers first
DROP TRIGGER IF EXISTS trigger_update_language_timestamp ON users;
DROP TRIGGER IF EXISTS trigger_log_language_preference_change ON users;

-- Drop functions
DROP FUNCTION IF EXISTS update_language_timestamp();
DROP FUNCTION IF EXISTS log_language_preference_change();

-- Drop tables (in reverse dependency order)
DROP TABLE IF EXISTS language_usage_analytics;
DROP TABLE IF EXISTS user_language_preferences_history;

-- Remove columns from users table
ALTER TABLE users 
DROP COLUMN IF EXISTS preferred_language,
DROP COLUMN IF EXISTS language_auto_detect,
DROP COLUMN IF EXISTS language_source,
DROP COLUMN IF EXISTS language_updated_at,
DROP COLUMN IF EXISTS quebec_french_preference;