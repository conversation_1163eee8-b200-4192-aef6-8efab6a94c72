-- Add appointment duration adjustment fields
-- Version: 020
-- Description: Adds fields for tracking actual appointment duration vs scheduled duration
-- Author: TutorAide Development Team
-- Date: 2025-01-15

-- Add actual duration fields to appointment_sessions
ALTER TABLE appointment_sessions
    ADD COLUMN IF NOT EXISTS actual_start_time TIME,
    ADD COLUMN IF NOT EXISTS actual_end_time TIME,
    ADD COLUMN IF NOT EXISTS actual_duration_minutes INTEGER,
    ADD COLUMN IF NOT EXISTS duration_adjusted BOOLEAN DEFAULT FALSE,
    ADD COLUMN IF NOT EXISTS duration_adjusted_by INTEGER REFERENCES user_accounts(user_id),
    ADD COLUMN IF NOT EXISTS duration_adjusted_at TIMESTAMP WITH TIME ZONE,
    ADD COLUMN IF NOT EXISTS duration_adjustment_reason TEXT,
    ADD COLUMN IF NOT EXISTS original_duration_minutes INTEGER GENERATED ALWAYS AS (
        EXTRACT(EPOCH FROM (end_time - start_time)) / 60
    ) STORED,
    ADD COLUMN IF NOT EXISTS duration_difference_minutes INTEGER GENERATED ALWAYS AS (
        CASE 
            WHEN actual_duration_minutes IS NOT NULL 
            THEN actual_duration_minutes - (EXTRACT(EPOCH FROM (end_time - start_time)) / 60)::INTEGER
            ELSE NULL
        END
    ) STORED;

-- Create table for duration adjustment audit trail
CREATE TABLE IF NOT EXISTS appointment_duration_adjustments (
    adjustment_id SERIAL PRIMARY KEY,
    appointment_id INTEGER NOT NULL REFERENCES appointment_sessions(appointment_id) ON DELETE CASCADE,
    original_start_time TIME NOT NULL,
    original_end_time TIME NOT NULL,
    adjusted_start_time TIME NOT NULL,
    adjusted_end_time TIME NOT NULL,
    original_duration_minutes INTEGER NOT NULL,
    adjusted_duration_minutes INTEGER NOT NULL,
    adjustment_reason TEXT NOT NULL,
    adjusted_by INTEGER NOT NULL REFERENCES user_accounts(user_id),
    adjusted_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    billing_impact JSONB,
    notification_sent BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_appointments_duration_adjusted 
    ON appointment_sessions(duration_adjusted) 
    WHERE duration_adjusted = true;

CREATE INDEX IF NOT EXISTS idx_appointments_actual_duration 
    ON appointment_sessions(actual_duration_minutes) 
    WHERE actual_duration_minutes IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_duration_adjustments_appointment 
    ON appointment_duration_adjustments(appointment_id);

CREATE INDEX IF NOT EXISTS idx_duration_adjustments_adjusted_by 
    ON appointment_duration_adjustments(adjusted_by);

-- Add check constraint to ensure actual times are valid
ALTER TABLE appointment_sessions 
    ADD CONSTRAINT check_actual_times 
    CHECK (
        (actual_start_time IS NULL AND actual_end_time IS NULL) OR
        (actual_start_time IS NOT NULL AND actual_end_time IS NOT NULL AND actual_end_time > actual_start_time)
    );

-- Add check constraint to ensure actual duration matches actual times when both are present
ALTER TABLE appointment_sessions 
    ADD CONSTRAINT check_actual_duration_consistency 
    CHECK (
        actual_duration_minutes IS NULL OR
        (actual_start_time IS NULL AND actual_end_time IS NULL) OR
        actual_duration_minutes = EXTRACT(EPOCH FROM (actual_end_time - actual_start_time)) / 60
    );

-- Add comments for documentation
COMMENT ON COLUMN appointment_sessions.actual_start_time IS 'Actual start time of the appointment (if different from scheduled)';
COMMENT ON COLUMN appointment_sessions.actual_end_time IS 'Actual end time of the appointment (if different from scheduled)';
COMMENT ON COLUMN appointment_sessions.actual_duration_minutes IS 'Actual duration in minutes (can be manually set or calculated from actual times)';
COMMENT ON COLUMN appointment_sessions.duration_adjusted IS 'Whether the duration has been adjusted from the original';
COMMENT ON COLUMN appointment_sessions.duration_adjusted_by IS 'User who adjusted the duration';
COMMENT ON COLUMN appointment_sessions.duration_adjusted_at IS 'When the duration was adjusted';
COMMENT ON COLUMN appointment_sessions.duration_adjustment_reason IS 'Reason for the duration adjustment';
COMMENT ON COLUMN appointment_sessions.original_duration_minutes IS 'Original scheduled duration in minutes (computed)';
COMMENT ON COLUMN appointment_sessions.duration_difference_minutes IS 'Difference between actual and scheduled duration (computed)';

COMMENT ON TABLE appointment_duration_adjustments IS 'Audit trail for appointment duration adjustments';
COMMENT ON COLUMN appointment_duration_adjustments.billing_impact IS 'JSON data about how the adjustment affects billing';