"""
Invoice service for managing client invoices and daily generation.
"""

import asyncpg
from typing import List, Optional, Dict, Any
from datetime import date, datetime, timedelta
from decimal import Decimal
import logging

from app.models.invoice_models import (
    Invoice, InvoiceItem, InvoiceFilter, 
    InvoiceGenerationResult, PaymentStatus, ItemType
)
from app.models.subscription_models import SubscriptionUsage
from app.database.repositories.invoice_repository import InvoiceRepository
from app.database.repositories.subscription_repository import SubscriptionRepository
from app.services.notification_service import NotificationService
from app.services.email_service import EmailService
from app.services.stripe_service import StripeService
from app.core.exceptions import BusinessLogicError, ValidationError


logger = logging.getLogger(__name__)


class InvoiceService:
    """Service for managing invoices."""
    
    def __init__(
        self,
        invoice_repo: InvoiceRepository,
        subscription_repo: SubscriptionRepository,
        notification_service: NotificationService,
        email_service: EmailService,
        stripe_service: StripeService
    ):
        self.invoice_repo = invoice_repo
        self.subscription_repo = subscription_repo
        self.notification_service = notification_service
        self.email_service = email_service
        self.stripe_service = stripe_service
    
    async def generate_daily_invoices(
        self, 
        db: asyncpg.Connection,
        invoice_date: Optional[date] = None
    ) -> InvoiceGenerationResult:
        """
        Generate invoices for all completed appointments from the previous day.
        This is the main method called by the daily cron job.
        """
        result = InvoiceGenerationResult()
        
        # Default to yesterday if no date provided
        if not invoice_date:
            invoice_date = date.today() - timedelta(days=1)
        
        logger.info(f"Starting daily invoice generation for {invoice_date}")
        
        # Create task log
        task_id = await self.invoice_repo.create_scheduled_task_log(
            db,
            task_name="daily_invoice_generation",
            task_type="invoice",
            parameters={"invoice_date": invoice_date.isoformat()}
        )
        
        try:
            # Get all unbilled appointments for the date
            unbilled = await self.invoice_repo.get_unbilled_appointments(
                db,
                start_date=invoice_date,
                end_date=invoice_date
            )
            
            result.appointments_processed = len(unbilled)
            logger.info(f"Found {len(unbilled)} unbilled appointments")
            
            # Group appointments by client
            client_appointments: Dict[int, List[Dict[str, Any]]] = {}
            for appointment in unbilled:
                client_id = appointment["client_id"]
                if client_id not in client_appointments:
                    client_appointments[client_id] = []
                client_appointments[client_id].append(appointment)
            
            # Generate invoice for each client
            for client_id, appointments in client_appointments.items():
                try:
                    # Start transaction for each client
                    async with db.transaction():
                        invoice = await self._create_invoice_for_client(
                            db, client_id, appointments, invoice_date
                        )
                        
                        if invoice:
                            result.add_invoice(invoice)
                            
                            # Send notifications
                            await self._send_invoice_notifications(invoice)
                            
                            logger.info(
                                f"Created invoice {invoice.invoice_number} "
                                f"for client {client_id} with {len(appointments)} items"
                            )
                        
                except Exception as e:
                    error_msg = f"Failed to create invoice for client {client_id}: {str(e)}"
                    logger.error(error_msg)
                    result.add_error(error_msg)
            
            # Update task log with success
            await self.invoice_repo.update_scheduled_task_log(
                db,
                task_id=task_id,
                status="completed",
                records_processed=result.appointments_processed,
                records_created=result.invoices_created,
                records_failed=len(result.errors)
            )
            
            logger.info(
                f"Daily invoice generation completed: "
                f"{result.invoices_created} invoices created, "
                f"{result.appointments_processed} appointments processed, "
                f"{len(result.errors)} errors"
            )
            
        except Exception as e:
            # Update task log with failure
            await self.invoice_repo.update_scheduled_task_log(
                db,
                task_id=task_id,
                status="failed",
                error_message=str(e),
                error_details={"type": type(e).__name__}
            )
            raise BusinessLogicError(f"Daily invoice generation failed: {str(e)}")
        
        return result
    
    async def _create_invoice_for_client(
        self,
        db: asyncpg.Connection,
        client_id: int,
        appointments: List[Dict[str, Any]],
        invoice_date: date
    ) -> Optional[Invoice]:
        """Create invoice for a client with multiple appointments."""
        # Check if client has active subscription
        active_subscriptions = await self.subscription_repo.get_client_active_subscriptions(
            db, client_id
        )
        
        # Separate subscription-based and regular appointments
        subscription_appointments = []
        regular_appointments = []
        
        for appointment in appointments:
            if appointment.get("is_subscription_based") and active_subscriptions:
                subscription_appointments.append(appointment)
            else:
                regular_appointments.append(appointment)
        
        # Handle subscription appointments (deduct hours, no invoice)
        if subscription_appointments:
            await self._process_subscription_appointments(
                db, client_id, subscription_appointments, active_subscriptions[0]
            )
        
        # Create invoice for regular appointments
        if regular_appointments:
            invoice = Invoice(
                client_id=client_id,
                invoice_date=invoice_date,
                due_date=invoice_date + timedelta(days=30),
                status=PaymentStatus.PENDING
            )
            
            # Add invoice items
            for appointment in regular_appointments:
                # Calculate service amount
                duration_hours = Decimal(appointment["duration_minutes"]) / 60
                base_rate = appointment.get("individual_rate") or appointment["service_rate"]
                surge_multiplier = Decimal(str(appointment.get("surge_multiplier", 1.0)))
                
                service_amount = base_rate * duration_hours * surge_multiplier
                surge_amount = service_amount - (base_rate * duration_hours)
                
                # Add service item
                service_item = InvoiceItem(
                    appointment_id=appointment["appointment_id"],
                    item_type=ItemType.SERVICE,
                    description=(
                        f"{appointment['service_name']} with {appointment['tutor_name']} "
                        f"on {appointment['start_time'].strftime('%Y-%m-%d %H:%M')} "
                        f"({duration_hours:.2f} hours)"
                    ),
                    quantity=duration_hours,
                    unit_price=base_rate,
                    amount=service_amount,
                    surge_multiplier=surge_multiplier,
                    surge_amount=surge_amount
                )
                invoice.add_item(service_item)
                
                # Add transport fee if applicable
                transport_fee = appointment.get("transport_fee")
                if transport_fee and transport_fee > 0:
                    transport_item = InvoiceItem(
                        appointment_id=appointment["appointment_id"],
                        item_type=ItemType.TRANSPORT,
                        description=f"Transport fee for {appointment['start_time'].strftime('%Y-%m-%d')} session",
                        quantity=Decimal("1.00"),
                        unit_price=transport_fee,
                        amount=transport_fee
                    )
                    invoice.add_item(transport_item)
            
            # Create invoice in database
            invoice = await self.invoice_repo.create_invoice(db, invoice)
            return invoice
        
        return None
    
    async def _process_subscription_appointments(
        self,
        db: asyncpg.Connection,
        client_id: int,
        appointments: List[Dict[str, Any]],
        subscription: Any
    ) -> None:
        """Process subscription-based appointments by deducting hours."""
        total_hours = sum(
            Decimal(app["duration_minutes"]) / 60 
            for app in appointments
        )
        
        # Record subscription usage
        for appointment in appointments:
            usage = SubscriptionUsage(
                subscription_id=subscription.subscription_id,
                appointment_id=appointment["appointment_id"],
                hours_used=Decimal(appointment["duration_minutes"]) / 60,
                usage_date=appointment["start_time"].date()
            )
            
            await self.subscription_repo.record_subscription_usage(db, usage)
        
        logger.info(
            f"Deducted {total_hours} hours from subscription {subscription.subscription_id} "
            f"for {len(appointments)} appointments"
        )
    
    async def _send_invoice_notifications(self, invoice: Invoice) -> None:
        """Send invoice notifications via email and SMS."""
        try:
            # Send email notification
            await self.email_service.send_invoice_email(
                invoice_id=invoice.invoice_id,
                recipient_email=None,  # Will be fetched from client
                invoice_data=invoice.to_email_dict()
            )
            
            # Send SMS notification
            await self.notification_service.send_invoice_sms(
                client_id=invoice.client_id,
                invoice_number=invoice.invoice_number,
                amount=invoice.total_amount,
                due_date=invoice.due_date
            )
            
        except Exception as e:
            logger.error(f"Failed to send notifications for invoice {invoice.invoice_number}: {str(e)}")
    
    async def create_invoice(
        self,
        db: asyncpg.Connection,
        invoice: Invoice
    ) -> Invoice:
        """Create a new invoice."""
        if not invoice.items:
            raise ValidationError("Invoice must have at least one item")
        
        # Calculate totals
        invoice.recalculate_totals()
        
        # Create invoice
        created_invoice = await self.invoice_repo.create_invoice(db, invoice)
        
        # Send notifications
        await self._send_invoice_notifications(created_invoice)
        
        return created_invoice
    
    async def get_invoice(
        self,
        db: asyncpg.Connection,
        invoice_id: int
    ) -> Optional[Invoice]:
        """Get invoice by ID."""
        return await self.invoice_repo.get_invoice_by_id(db, invoice_id)
    
    async def find_invoices(
        self,
        db: asyncpg.Connection,
        filter_params: InvoiceFilter
    ) -> List[Invoice]:
        """Find invoices with filtering."""
        return await self.invoice_repo.find_invoices(db, filter_params)
    
    async def mark_invoice_paid(
        self,
        db: asyncpg.Connection,
        invoice_id: int,
        paid_by_client_id: Optional[int] = None,
        payment_method: str = "stripe",
        stripe_payment_intent_id: Optional[str] = None
    ) -> bool:
        """Mark an invoice as paid."""
        success = await self.invoice_repo.update_invoice_status(
            db,
            invoice_id=invoice_id,
            status=PaymentStatus.PAID,
            paid_by_client_id=paid_by_client_id,
            payment_method=payment_method,
            stripe_payment_intent_id=stripe_payment_intent_id
        )
        
        if success:
            # Send payment confirmation
            invoice = await self.get_invoice(db, invoice_id)
            if invoice:
                await self.notification_service.send_payment_confirmation(
                    client_id=invoice.client_id,
                    invoice_number=invoice.invoice_number,
                    amount=invoice.total_amount
                )
        
        return success
    
    async def create_stripe_payment_intent(
        self,
        db: asyncpg.Connection,
        invoice_id: int,
        paid_by_client_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """Create Stripe payment intent for an invoice."""
        invoice = await self.get_invoice(db, invoice_id)
        if not invoice:
            raise ValidationError(f"Invoice {invoice_id} not found")
        
        if invoice.is_paid:
            raise ValidationError(f"Invoice {invoice_id} is already paid")
        
        # Create payment intent
        metadata = {
            "invoice_id": str(invoice_id),
            "invoice_number": invoice.invoice_number,
            "client_id": str(invoice.client_id)
        }
        
        if paid_by_client_id:
            metadata["paid_by_client_id"] = str(paid_by_client_id)
        
        payment_intent = await self.stripe_service.create_payment_intent(
            amount=invoice.total_amount,
            currency="cad",
            description=f"Invoice {invoice.invoice_number}",
            metadata=metadata
        )
        
        # Update invoice with payment intent ID
        await self.invoice_repo.update_invoice_status(
            db,
            invoice_id=invoice_id,
            status=PaymentStatus.PROCESSING,
            stripe_payment_intent_id=payment_intent["id"]
        )
        
        return payment_intent
    
    async def handle_stripe_webhook(
        self,
        db: asyncpg.Connection,
        event_type: str,
        event_data: Dict[str, Any]
    ) -> None:
        """Handle Stripe webhook events for invoices."""
        if event_type == "payment_intent.succeeded":
            payment_intent = event_data["object"]
            metadata = payment_intent.get("metadata", {})
            
            invoice_id = metadata.get("invoice_id")
            if invoice_id:
                paid_by_client_id = metadata.get("paid_by_client_id")
                
                await self.mark_invoice_paid(
                    db,
                    invoice_id=int(invoice_id),
                    paid_by_client_id=int(paid_by_client_id) if paid_by_client_id else None,
                    payment_method="stripe",
                    stripe_payment_intent_id=payment_intent["id"]
                )
                
                logger.info(f"Invoice {invoice_id} marked as paid via Stripe webhook")
    
    async def get_overdue_invoices(
        self,
        db: asyncpg.Connection
    ) -> List[Dict[str, Any]]:
        """Get all overdue invoices."""
        return await self.invoice_repo.get_overdue_invoices(db)
    
    async def send_overdue_reminders(
        self,
        db: asyncpg.Connection
    ) -> int:
        """Send reminders for overdue invoices."""
        overdue_invoices = await self.get_overdue_invoices(db)
        sent_count = 0
        
        for invoice_data in overdue_invoices:
            try:
                # Send email reminder
                await self.email_service.send_overdue_invoice_reminder(
                    invoice_id=invoice_data["invoice_id"],
                    recipient_email=invoice_data["client_email"],
                    days_overdue=invoice_data["days_overdue"],
                    invoice_data={
                        "invoice_number": invoice_data["invoice_number"],
                        "total_amount": f"${invoice_data['total_amount']:.2f}",
                        "due_date": invoice_data["due_date"].strftime("%B %d, %Y"),
                        "days_overdue": invoice_data["days_overdue"]
                    }
                )
                
                # Send SMS reminder if phone available
                if invoice_data.get("client_phone"):
                    await self.notification_service.send_overdue_invoice_sms(
                        phone_number=invoice_data["client_phone"],
                        invoice_number=invoice_data["invoice_number"],
                        amount=invoice_data["total_amount"],
                        days_overdue=invoice_data["days_overdue"]
                    )
                
                sent_count += 1
                
            except Exception as e:
                logger.error(
                    f"Failed to send overdue reminder for invoice {invoice_data['invoice_id']}: {str(e)}"
                )
        
        logger.info(f"Sent {sent_count} overdue invoice reminders")
        return sent_count

