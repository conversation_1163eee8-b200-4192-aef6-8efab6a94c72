"""
Search domain models for the TutorAide application.

This module contains all Pydantic models related to search functionality,
including queries, results, filters, and autocomplete.
"""

from datetime import datetime
from typing import Optional, List, Dict, Any, Union, Literal
from enum import Enum

from pydantic import BaseModel, Field, ConfigDict, field_validator, computed_field


class EntityType(str, Enum):
    """Searchable entity types."""
    CLIENT = "client"
    TUTOR = "tutor"
    DEPENDANT = "dependant"
    ALL = "all"


class SearchResultType(str, Enum):
    """Search result types for UI display."""
    USER = "user"
    PROFILE = "profile"
    DOCUMENT = "document"
    APPOINTMENT = "appointment"
    SERVICE = "service"


class SortOrder(str, Enum):
    """Sort order options."""
    ASC = "asc"
    DESC = "desc"


class SearchSort(str, Enum):
    """Search sort options."""
    RELEVANCE = "relevance"
    NAME = "name"
    DATE_CREATED = "date_created"
    DATE_UPDATED = "date_updated"
    COMPLETENESS = "completeness"


class SearchFilter(BaseModel):
    """Individual search filter."""
    
    model_config = ConfigDict(from_attributes=True)
    
    field: str = Field(..., description="Field to filter on")
    operator: str = Field(..., description="Filter operator (eq, ne, gt, lt, gte, lte, in, contains)")
    value: Any = Field(..., description="Filter value")
    
    @field_validator('operator')
    @classmethod
    def validate_operator(cls, v: str) -> str:
        """Validate filter operator."""
        valid_operators = ['eq', 'ne', 'gt', 'lt', 'gte', 'lte', 'in', 'contains', 'starts_with', 'ends_with']
        if v not in valid_operators:
            raise ValueError(f"Invalid operator. Must be one of: {', '.join(valid_operators)}")
        return v


class SearchFilters(BaseModel):
    """Collection of search filters."""
    
    model_config = ConfigDict(from_attributes=True)
    
    # Entity type filter
    entity_type: Optional[EntityType] = Field(None, description="Filter by entity type")
    
    # Status filters
    is_active: Optional[bool] = Field(None, description="Filter by active status")
    is_verified: Optional[bool] = Field(None, description="Filter by verification status")
    
    # Date range filters
    created_after: Optional[datetime] = Field(None, description="Created after date")
    created_before: Optional[datetime] = Field(None, description="Created before date")
    updated_after: Optional[datetime] = Field(None, description="Updated after date")
    updated_before: Optional[datetime] = Field(None, description="Updated before date")
    
    # Profile completeness
    min_completeness: Optional[int] = Field(None, ge=0, le=100, description="Minimum profile completeness")
    max_completeness: Optional[int] = Field(None, ge=0, le=100, description="Maximum profile completeness")
    
    # Location filters
    city: Optional[str] = Field(None, description="Filter by city")
    province: Optional[str] = Field(None, description="Filter by province")
    postal_code_prefix: Optional[str] = Field(None, description="Filter by postal code prefix")
    
    # Tutor-specific filters
    subjects: Optional[List[str]] = Field(None, description="Filter by subjects taught")
    teaching_levels: Optional[List[str]] = Field(None, description="Filter by teaching levels")
    languages: Optional[List[str]] = Field(None, description="Filter by languages spoken")
    min_experience_years: Optional[int] = Field(None, ge=0, description="Minimum years of experience")
    accepts_special_needs: Optional[bool] = Field(None, description="Accepts special needs students")
    
    # Client-specific filters
    has_dependants: Optional[bool] = Field(None, description="Has dependants")
    preferred_language: Optional[str] = Field(None, description="Preferred language")
    
    # Custom filters
    custom_filters: List[SearchFilter] = Field(default_factory=list, description="Custom field filters")
    
    @field_validator('created_before')
    @classmethod
    def validate_created_dates(cls, v: Optional[datetime], info) -> Optional[datetime]:
        """Validate created date range."""
        if v and 'created_after' in info.data and info.data['created_after']:
            if v <= info.data['created_after']:
                raise ValueError("created_before must be after created_after")
        return v
    
    @field_validator('updated_before')
    @classmethod
    def validate_updated_dates(cls, v: Optional[datetime], info) -> Optional[datetime]:
        """Validate updated date range."""
        if v and 'updated_after' in info.data and info.data['updated_after']:
            if v <= info.data['updated_after']:
                raise ValueError("updated_before must be after updated_after")
        return v
    
    @field_validator('max_completeness')
    @classmethod
    def validate_completeness_range(cls, v: Optional[int], info) -> Optional[int]:
        """Validate completeness range."""
        if v is not None and 'min_completeness' in info.data and info.data['min_completeness'] is not None:
            if v < info.data['min_completeness']:
                raise ValueError("max_completeness must be >= min_completeness")
        return v


class SearchQuery(BaseModel):
    """Search query model."""
    
    model_config = ConfigDict(from_attributes=True)
    
    query: str = Field(..., min_length=1, max_length=500, description="Search query string")
    entity_types: List[EntityType] = Field(
        default=[EntityType.ALL],
        description="Entity types to search"
    )
    filters: Optional[SearchFilters] = Field(None, description="Search filters")
    sort_by: SearchSort = Field(default=SearchSort.RELEVANCE, description="Sort field")
    sort_order: SortOrder = Field(default=SortOrder.DESC, description="Sort order")
    page: int = Field(default=1, ge=1, description="Page number")
    limit: int = Field(default=20, ge=1, le=100, description="Results per page")
    include_inactive: bool = Field(default=False, description="Include inactive records")
    fuzzy_threshold: float = Field(default=0.3, ge=0, le=1, description="Fuzzy search threshold")
    
    @computed_field
    @property
    def offset(self) -> int:
        """Calculate offset for pagination."""
        return (self.page - 1) * self.limit
    
    @computed_field
    @property
    def search_all_types(self) -> bool:
        """Check if searching all entity types."""
        return EntityType.ALL in self.entity_types or not self.entity_types


class SearchHighlight(BaseModel):
    """Search result highlight."""
    
    model_config = ConfigDict(from_attributes=True)
    
    field: str = Field(..., description="Field name")
    snippet: str = Field(..., description="Text snippet with highlight")
    match_positions: List[tuple[int, int]] = Field(
        default_factory=list,
        description="Start and end positions of matches"
    )


class BaseSearchResult(BaseModel):
    """Base search result model."""
    
    model_config = ConfigDict(from_attributes=True)
    
    entity_type: EntityType = Field(..., description="Entity type")
    entity_id: int = Field(..., description="Entity ID")
    score: float = Field(..., ge=0, le=1, description="Relevance score")
    highlights: List[SearchHighlight] = Field(default_factory=list, description="Search highlights")
    
    @computed_field
    @property
    def result_id(self) -> str:
        """Generate unique result ID."""
        return f"{self.entity_type.value}_{self.entity_id}"


class ClientSearchResult(BaseSearchResult):
    """Client search result."""
    
    first_name: str = Field(..., description="First name")
    last_name: str = Field(..., description="Last name")
    email: str = Field(..., description="Email address")
    phone: Optional[str] = Field(None, description="Phone number")
    profile_photo_url: Optional[str] = Field(None, description="Profile photo URL")
    profile_completeness: int = Field(..., description="Profile completeness")
    city: Optional[str] = Field(None, description="City")
    created_at: datetime = Field(..., description="Created date")
    last_activity_at: Optional[datetime] = Field(None, description="Last activity")
    
    @computed_field
    @property
    def full_name(self) -> str:
        """Get full name."""
        return f"{self.first_name} {self.last_name}"
    
    @computed_field
    @property
    def display_subtitle(self) -> str:
        """Get display subtitle."""
        return self.email


class TutorSearchResult(BaseSearchResult):
    """Tutor search result."""
    
    first_name: str = Field(..., description="First name")
    last_name: str = Field(..., description="Last name")
    email: str = Field(..., description="Email address")
    phone: Optional[str] = Field(None, description="Phone number")
    profile_photo_url: Optional[str] = Field(None, description="Profile photo URL")
    headline: Optional[str] = Field(None, description="Professional headline")
    bio: Optional[str] = Field(None, description="Bio snippet")
    subjects: List[str] = Field(default_factory=list, description="Subjects taught")
    teaching_levels: List[str] = Field(default_factory=list, description="Teaching levels")
    years_experience: int = Field(default=0, description="Years of experience")
    rating: Optional[float] = Field(None, ge=0, le=5, description="Average rating")
    total_reviews: int = Field(default=0, description="Total reviews")
    profile_completeness: int = Field(..., description="Profile completeness")
    is_verified: bool = Field(..., description="Verification status")
    is_featured: bool = Field(default=False, description="Featured status")
    languages_spoken: List[str] = Field(default_factory=list, description="Languages spoken")
    
    @computed_field
    @property
    def full_name(self) -> str:
        """Get full name."""
        return f"{self.first_name} {self.last_name}"
    
    @computed_field
    @property
    def display_subtitle(self) -> str:
        """Get display subtitle."""
        if self.headline:
            return self.headline
        elif self.subjects:
            return f"{', '.join(self.subjects[:3])} tutor"
        else:
            return "Tutor"
    
    @computed_field
    @property
    def rating_display(self) -> str:
        """Get rating display string."""
        if self.rating and self.total_reviews > 0:
            return f"{self.rating:.1f} ⭐ ({self.total_reviews} reviews)"
        return "No reviews yet"


class DependantSearchResult(BaseSearchResult):
    """Dependant search result."""
    
    first_name: str = Field(..., description="First name")
    last_name: str = Field(..., description="Last name")
    preferred_name: Optional[str] = Field(None, description="Preferred name")
    date_of_birth: datetime = Field(..., description="Date of birth")
    age: int = Field(..., description="Current age")
    gender: Optional[str] = Field(None, description="Gender")
    profile_photo_url: Optional[str] = Field(None, description="Profile photo URL")
    grade_level: Optional[str] = Field(None, description="Grade level")
    school_name: Optional[str] = Field(None, description="School name")
    parent_names: List[str] = Field(default_factory=list, description="Parent names")
    parent_ids: List[int] = Field(default_factory=list, description="Parent IDs")
    
    @computed_field
    @property
    def full_name(self) -> str:
        """Get full name."""
        return f"{self.first_name} {self.last_name}"
    
    @computed_field
    @property
    def display_name(self) -> str:
        """Get display name."""
        return self.preferred_name or self.first_name
    
    @computed_field
    @property
    def display_subtitle(self) -> str:
        """Get display subtitle."""
        parts = []
        if self.age:
            parts.append(f"Age {self.age}")
        if self.grade_level:
            parts.append(f"Grade {self.grade_level}")
        if self.parent_names:
            parts.append(f"Parent: {self.parent_names[0]}")
        return " • ".join(parts) if parts else "Dependant"


# Union type for all search results
SearchResult = Union[ClientSearchResult, TutorSearchResult, DependantSearchResult]


class SearchResponse(BaseModel):
    """Search response with results and metadata."""
    
    model_config = ConfigDict(from_attributes=True)
    
    query: str = Field(..., description="Original search query")
    results: List[SearchResult] = Field(..., description="Search results")
    total_results: int = Field(..., description="Total number of results")
    page: int = Field(..., description="Current page")
    limit: int = Field(..., description="Results per page")
    total_pages: int = Field(..., description="Total pages")
    search_time_ms: int = Field(..., description="Search execution time in milliseconds")
    filters_applied: Dict[str, Any] = Field(default_factory=dict, description="Applied filters")
    suggestions: List[str] = Field(default_factory=list, description="Search suggestions")
    
    @computed_field
    @property
    def has_more(self) -> bool:
        """Check if there are more results."""
        return self.page < self.total_pages
    
    @computed_field
    @property
    def result_range(self) -> tuple[int, int]:
        """Get result range for current page."""
        start = (self.page - 1) * self.limit + 1
        end = min(self.page * self.limit, self.total_results)
        return (start, end)


class AutocompleteResult(BaseModel):
    """Autocomplete suggestion result."""
    
    model_config = ConfigDict(from_attributes=True)
    
    entity_type: EntityType = Field(..., description="Entity type")
    entity_id: int = Field(..., description="Entity ID")
    text: str = Field(..., description="Display text")
    subtitle: Optional[str] = Field(None, description="Subtitle text")
    icon: Optional[str] = Field(None, description="Icon identifier")
    photo_url: Optional[str] = Field(None, description="Photo URL")
    tags: List[str] = Field(default_factory=list, description="Tags for filtering")
    
    @computed_field
    @property
    def value(self) -> str:
        """Get autocomplete value."""
        return f"{self.entity_type.value}:{self.entity_id}"


class AutocompleteResponse(BaseModel):
    """Autocomplete response."""
    
    model_config = ConfigDict(from_attributes=True)
    
    query: str = Field(..., description="Original query")
    suggestions: List[AutocompleteResult] = Field(..., description="Autocomplete suggestions")
    total_suggestions: int = Field(..., description="Total suggestions available")
    
    @computed_field
    @property
    def has_results(self) -> bool:
        """Check if there are any suggestions."""
        return len(self.suggestions) > 0


class PopularSearch(BaseModel):
    """Popular search term."""
    
    model_config = ConfigDict(from_attributes=True)
    
    search_query: str = Field(..., description="Search query")
    search_count: int = Field(..., ge=1, description="Number of times searched")
    last_searched: datetime = Field(..., description="Last search timestamp")
    created_at: datetime = Field(..., description="First search timestamp")
    
    @computed_field
    @property
    def popularity_score(self) -> float:
        """Calculate popularity score with time decay."""
        from datetime import datetime as dt, timezone
        
        # Time decay factor (searches decay over 30 days)
        now = dt.now(timezone.utc)
        days_old = (now - self.last_searched).days
        time_factor = max(0, 1 - (days_old / 30))
        
        # Combine count and recency
        return self.search_count * time_factor


class SearchHistory(BaseModel):
    """User search history entry."""
    
    model_config = ConfigDict(from_attributes=True)
    
    search_id: int = Field(..., description="Search history ID")
    user_id: int = Field(..., description="User who performed search")
    search_query: str = Field(..., description="Search query")
    search_type: Optional[str] = Field(None, description="Search type")
    search_filters: Dict[str, Any] = Field(default_factory=dict, description="Applied filters")
    results_count: int = Field(default=0, description="Number of results")
    selected_result_id: Optional[int] = Field(None, description="Selected result ID")
    selected_result_type: Optional[str] = Field(None, description="Selected result type")
    search_timestamp: datetime = Field(..., description="Search timestamp")
    session_id: Optional[str] = Field(None, description="Session ID")
    ip_address: Optional[str] = Field(None, description="IP address")
    user_agent: Optional[str] = Field(None, description="User agent string")
    
    @computed_field
    @property
    def was_successful(self) -> bool:
        """Check if search led to a selection."""
        return self.selected_result_id is not None


class SearchAnalytics(BaseModel):
    """Search analytics summary."""
    
    model_config = ConfigDict(from_attributes=True)
    
    period_start: datetime = Field(..., description="Analytics period start")
    period_end: datetime = Field(..., description="Analytics period end")
    total_searches: int = Field(..., description="Total searches performed")
    unique_users: int = Field(..., description="Unique users searching")
    avg_results_per_search: float = Field(..., description="Average results per search")
    click_through_rate: float = Field(..., description="Click-through rate percentage")
    
    # Top searches
    top_queries: List[PopularSearch] = Field(..., description="Top search queries")
    top_no_result_queries: List[str] = Field(..., description="Top queries with no results")
    
    # Search breakdown
    searches_by_type: Dict[EntityType, int] = Field(..., description="Searches by entity type")
    searches_by_hour: Dict[int, int] = Field(..., description="Searches by hour of day")
    
    # Performance metrics
    avg_search_time_ms: float = Field(..., description="Average search time in ms")
    p95_search_time_ms: float = Field(..., description="95th percentile search time")
    cache_hit_rate: float = Field(..., description="Search cache hit rate percentage")