# Client Tutor Request Integration Documentation

## Overview

This document describes the implementation of the client tutor request form integration between the TutorAide marketing website and the main application. The integration allows prospective clients to submit tutor requests through the marketing website, which are then processed by managers in the main application.

## Architecture

### Database Schema

A new table `client_tutor_requests` has been added to store form submissions:

```sql
CREATE TABLE client_tutor_requests (
    request_id SERIAL PRIMARY KEY,
    -- Parent information
    parent_first_name, parent_last_name, parent_email, parent_phone,
    parent_address, parent_apartment, parent_city, parent_postal_code, parent_province,
    -- Geographic tracking for regional management
    latitude, longitude, region,
    -- Student information
    student_first_name, student_last_name, student_grade,
    -- Service requirements
    subjects (TEXT[]), frequency, preferred_schedule, location, additional_info,
    -- Processing status and tracking
    status, assigned_to, processed_by, processed_at,
    -- Conversion tracking
    converted_client_id, converted_dependant_id, conversion_notes,
    -- Matching information
    matched_tutor_ids (INTEGER[]), match_score, match_criteria (JSONB),
    -- Source tracking
    source, referrer_url, utm_source, utm_medium, utm_campaign,
    -- Timestamps
    created_at, updated_at
);
```

Key features:
- **Regional Tracking**: Includes latitude, longitude, and region fields for future geographic-based manager views
- **Status Management**: Tracks request lifecycle (pending → processing → matched/converted/cancelled)
- **Conversion Tracking**: Links to created client and dependant records
- **Marketing Attribution**: Captures UTM parameters and referrer information

### API Endpoints

#### Public Endpoints

1. **POST /api/v1/client-requests**
   - Public endpoint for form submissions
   - No authentication required
   - Basic validation and sanitization
   - Returns success message with request ID

2. **POST /api/v1/client-requests/secure**
   - Enhanced version with security features:
     - Rate limiting (5 requests per hour per IP)
     - CORS origin verification
     - Duplicate submission prevention
     - Honeypot bot detection
     - Enhanced input validation

#### Admin Endpoints (Manager Access Only)

1. **GET /api/v1/admin/requests/client-requests**
   - View pending requests with filtering by region/city
   - Returns regional distribution statistics

2. **GET /api/v1/admin/requests/client-requests/{request_id}**
   - Get detailed information about a specific request

3. **POST /api/v1/admin/requests/client-requests/assign**
   - Assign a request to a manager for processing

4. **POST /api/v1/admin/requests/client-requests/convert**
   - Convert request to client account
   - Creates user account, client profile, dependant, and learning needs
   - Generates temporary password
   - Triggers welcome email

5. **POST /api/v1/admin/requests/client-requests/cancel**
   - Cancel a request with reason

6. **GET /api/v1/admin/requests/client-requests/stats/regional**
   - Get statistics by region for manager overview

### Services

#### ClientRequestProcessor Service

Core service handling request processing:
- `get_pending_requests()`: Retrieve requests with filtering
- `assign_request()`: Assign to manager
- `convert_to_client()`: Convert to client account
- `cancel_request()`: Cancel with reason
- `get_requests_by_region()`: Regional statistics

Features:
- Automatic geocoding of addresses
- Regional determination based on city/coordinates
- Temporary password generation
- Initial tutor matching suggestions

### Security Measures

1. **Rate Limiting**
   - In-memory rate limiter with configurable limits
   - Prevents abuse and spam submissions
   - Client identification by IP + User-Agent hash

2. **Input Validation**
   - Email format validation with disposable domain blocking
   - Canadian postal code format validation
   - Phone number format validation
   - Content length limits
   - Subject normalization and validation

3. **CORS Protection**
   - Origin verification against allowed domains
   - Proper CORS headers for cross-origin requests

4. **Duplicate Prevention**
   - Checks for recent submissions (24-hour window)
   - Prevents multiple submissions with same email/phone

5. **Bot Protection**
   - Honeypot field implementation
   - Request pattern analysis

## Field Mapping

### Marketing Website Form → Database

| Form Field | Database Column | Validation/Transformation |
|------------|----------------|--------------------------|
| parentFirstName | parent_first_name | Trimmed, max 100 chars |
| parentLastName | parent_last_name | Trimmed, max 100 chars |
| parentEmail | parent_email | Lowercase, email format |
| parentPhone | parent_phone | Phone format validation |
| parentAddress | parent_address | Trimmed, max 255 chars |
| parentApartment | parent_apartment | Optional, max 50 chars |
| parentCity | parent_city | Trimmed, max 100 chars |
| parentPostalCode | parent_postal_code | Canadian format, uppercase |
| parentProvince | parent_province | Max 50 chars |
| studentFirstName | student_first_name | Trimmed, max 100 chars |
| studentLastName | student_last_name | Trimmed, max 100 chars |
| studentGrade | student_grade | Max 50 chars |
| subjects | subjects | Array, normalized values |
| frequency | frequency | Max 50 chars |
| preferredSchedule | preferred_schedule | Optional, free text |
| location | location | Enum: online/in_person/hybrid |
| additionalInfo | additional_info | Optional, max 1000 chars |

### Request → Client Account Conversion

1. **User Account** (if create_account = true)
   - Email from parent_email
   - Generated temporary password
   - Role: 'client'

2. **Client Profile**
   - Names from parent fields
   - Phone from parent_phone
   - Address as JSONB from parent address fields

3. **Dependant**
   - Names from student fields
   - Birth date: Current date (placeholder)
   - Relationship: 'parent'

4. **Learning Needs** (per subject)
   - Subject from subjects array
   - Service type from location field
   - Frequency from frequency field
   - Notes from additional_info

## Regional Tracking

The system is designed to support future regional management features:

1. **Automatic Geocoding**: Addresses are geocoded to latitude/longitude
2. **Regional Assignment**: Cities mapped to regions (e.g., "Montreal-North", "Laval")
3. **Manager Views**: APIs support filtering by region
4. **Statistics**: Regional distribution of pending requests

Current regions:
- Montreal (subdivided into North/South/East/West based on coordinates)
- Laval
- South-Shore (Longueuil, Brossard)
- Quebec-City
- Gatineau
- Sherbrooke
- Trois-Rivieres
- Others (city name as region)

## Usage Flow

1. **Client Submission**
   - Client fills form on marketing website
   - Form submitted to `/api/v1/client-requests`
   - Request stored with 'pending' status
   - Confirmation shown to client

2. **Manager Processing**
   - Manager views pending requests in admin panel
   - Can filter by region/city
   - Assigns request to themselves
   - Reviews and converts to client account

3. **Account Creation**
   - System creates user account with temporary password
   - Creates client profile and dependant
   - Sets up learning needs
   - Sends welcome email with credentials

4. **Follow-up**
   - Manager can view converted clients
   - Initial tutor matches suggested
   - Standard client onboarding flow continues

## Testing

Integration tests cover:
- Form submission validation
- Rate limiting behavior
- Duplicate prevention
- Manager request viewing
- Request assignment
- Client conversion
- Regional statistics

Run tests:
```bash
uv run pytest tests/integration/test_client_requests_flow.py -v
```

## Future Enhancements

1. **Automated Matching**: Enhance tutor matching algorithm
2. **Email Templates**: Implement welcome email templates
3. **SMS Notifications**: Notify managers of new requests
4. **Analytics Dashboard**: Visual regional distribution
5. **Bulk Processing**: Convert multiple requests at once
6. **API Integration**: Direct API for partner websites