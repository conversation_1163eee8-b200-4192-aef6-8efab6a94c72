"""
Session management models for TutorAide application.
"""

from datetime import datetime
from typing import Optional, List
from uuid import UUID
from pydantic import BaseModel, Field, ConfigDict

from app.models.user_models import UserRoleType


class SessionBase(BaseModel):
    """Base session model with common fields."""
    model_config = ConfigDict(from_attributes=True)
    
    user_id: int = Field(..., description="User ID who owns this session")
    role_type: UserRoleType = Field(..., description="Role the user is acting as")
    ip_address: Optional[str] = Field(None, description="IP address of the session")
    user_agent: Optional[str] = Field(None, description="User agent string")


class SessionCreate(SessionBase):
    """Model for creating a new session."""
    token: str = Field(..., description="Session token to be hashed")


class SessionUpdate(BaseModel):
    """Model for updating session information."""
    last_activity: Optional[datetime] = Field(None, description="Last activity timestamp")
    ip_address: Optional[str] = Field(None, description="Updated IP address")
    is_active: Optional[bool] = Field(None, description="Session active status")


class Session(SessionBase):
    """Complete session model."""
    session_id: UUID = Field(..., description="Unique session identifier")
    token_hash: str = Field(..., description="Hashed session token")
    expires_at: datetime = Field(..., description="Session expiration time")
    last_activity: datetime = Field(..., description="Last activity timestamp")
    is_active: bool = Field(True, description="Whether session is active")
    created_at: datetime = Field(..., description="Session creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")


class SessionInfo(BaseModel):
    """Session information for API responses."""
    session_id: UUID = Field(..., description="Session identifier")
    user_id: int = Field(..., description="User ID")
    role_type: UserRoleType = Field(..., description="Current role")
    ip_address: Optional[str] = Field(None, description="Session IP address")
    user_agent: Optional[str] = Field(None, description="User agent")
    last_activity: datetime = Field(..., description="Last activity")
    created_at: datetime = Field(..., description="Creation timestamp")


class SessionListResponse(BaseModel):
    """Response model for listing user sessions."""
    sessions: List[SessionInfo] = Field(..., description="List of active sessions")
    total: int = Field(..., description="Total number of sessions")