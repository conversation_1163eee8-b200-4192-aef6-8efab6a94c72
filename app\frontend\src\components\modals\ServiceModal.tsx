import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { X } from 'lucide-react';
import { Modal } from '../common/Modal';
import { Input } from '../common/Input';
import { Select } from '../common/Select';
import { Textarea } from '../common/Textarea';
import Button from '../common/Button';
import { useApi } from '../../hooks/useApi';
import toast from 'react-hot-toast';
import { ServicePreset } from '../../services/serviceApi';

interface ServiceModalProps {
  isOpen: boolean;
  onClose: () => void;
  service?: ServicePreset | null;
  onSuccess: () => void;
}

export const ServiceModal: React.FC<ServiceModalProps> = ({
  isOpen,
  onClose,
  service,
  onSuccess
}) => {
  const { t } = useTranslation();
  const { post, put, loading } = useApi();
  const isEdit = !!service;

  const [formData, setFormData] = useState({
    service_name: '',
    subject_area: 'mathematics',
    service_type: 'online',
    service_level: 'high_school',
    description: '',
    prerequisites: '',
    min_duration_minutes: 30,
    max_duration_minutes: 120,
    default_duration_minutes: 60,
    is_active: true
  });

  useEffect(() => {
    if (service) {
      setFormData({
        service_name: service.service_name,
        subject_area: service.subject_area,
        service_type: service.service_type,
        service_level: service.service_level,
        description: service.description || '',
        prerequisites: service.prerequisites || '',
        min_duration_minutes: service.min_duration_minutes,
        max_duration_minutes: service.max_duration_minutes,
        default_duration_minutes: service.default_duration_minutes,
        is_active: service.is_active
      });
    } else {
      // Reset form for new service
      setFormData({
        service_name: '',
        subject_area: 'mathematics',
        service_type: 'online',
        service_level: 'high_school',
        description: '',
        prerequisites: '',
        min_duration_minutes: 30,
        max_duration_minutes: 120,
        default_duration_minutes: 60,
        is_active: true
      });
    }
  }, [service]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'number') {
      setFormData(prev => ({ ...prev, [name]: parseInt(value) || 0 }));
    } else if (type === 'checkbox') {
      const target = e.target as HTMLInputElement;
      setFormData(prev => ({ ...prev, [name]: target.checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      if (isEdit) {
        await put(`/services/catalog/${service.service_catalog_id}`, formData);
        toast.success(t('services.updateSuccess', 'Service updated successfully'));
      } else {
        await post('/services/catalog', formData);
        toast.success(t('services.createSuccess', 'Service created successfully'));
      }
      onSuccess();
      onClose();
    } catch (error) {
      console.error('Error saving service:', error);
      toast.error(isEdit ? t('services.updateError', 'Failed to update service') : t('services.createError', 'Failed to create service'));
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={isEdit ? t('services.editService', 'Edit Service') : t('services.createService', 'Create Service')}>
      <form onSubmit={handleSubmit} className="space-y-4">
        <Input
          label={t('services.serviceName', 'Service Name')}
          name="service_name"
          value={formData.service_name}
          onChange={handleChange}
          required
          placeholder={t('services.serviceNamePlaceholder', 'e.g., Math Tutoring')}
        />

        <Select
          label={t('services.subjectArea', 'Subject Area')}
          name="subject_area"
          value={formData.subject_area}
          onChange={handleChange}
          required
          options={[
            { value: 'mathematics', label: 'Mathematics' },
            { value: 'science', label: 'Science' },
            { value: 'french', label: 'French' },
            { value: 'english', label: 'English' },
            { value: 'physics', label: 'Physics' },
            { value: 'chemistry', label: 'Chemistry' },
            { value: 'biology', label: 'Biology' },
            { value: 'history', label: 'History' },
            { value: 'geography', label: 'Geography' },
            { value: 'computer_science', label: 'Computer Science' },
            { value: 'economics', label: 'Economics' },
            { value: 'accounting', label: 'Accounting' },
            { value: 'other', label: 'Other' }
          ]}
        />

        <Select
          label={t('services.serviceType', 'Service Type')}
          name="service_type"
          value={formData.service_type}
          onChange={handleChange}
          required
          options={[
            { value: 'online', label: 'Online' },
            { value: 'in_person', label: 'In-Person' },
            { value: 'library', label: 'Library' },
            { value: 'hybrid', label: 'Hybrid' }
          ]}
        />

        <Select
          label={t('services.serviceLevel', 'Academic Level')}
          name="service_level"
          value={formData.service_level}
          onChange={handleChange}
          required
          options={[
            { value: 'elementary', label: 'Elementary' },
            { value: 'high_school', label: 'High School' },
            { value: 'college', label: 'College' },
            { value: 'university', label: 'University' },
            { value: 'adult', label: 'Adult' }
          ]}
        />

        <Textarea
          label={t('services.description', 'Description')}
          name="description"
          value={formData.description}
          onChange={handleChange}
          rows={3}
          placeholder={t('services.descriptionPlaceholder', 'Describe the service...')}
        />

        <Textarea
          label={t('services.prerequisites', 'Prerequisites')}
          name="prerequisites"
          value={formData.prerequisites}
          onChange={handleChange}
          rows={2}
          placeholder={t('services.prerequisitesPlaceholder', 'Any prerequisites or requirements...')}
        />

        <div className="grid grid-cols-3 gap-4">
          <Input
            label={t('services.minDuration', 'Min Duration (min)')}
            name="min_duration_minutes"
            type="number"
            value={formData.min_duration_minutes}
            onChange={handleChange}
            min={15}
            max={240}
            required
          />

          <Input
            label={t('services.defaultDuration', 'Default Duration')}
            name="default_duration_minutes"
            type="number"
            value={formData.default_duration_minutes}
            onChange={handleChange}
            min={15}
            max={240}
            required
          />

          <Input
            label={t('services.maxDuration', 'Max Duration (min)')}
            name="max_duration_minutes"
            type="number"
            value={formData.max_duration_minutes}
            onChange={handleChange}
            min={30}
            max={480}
            required
          />
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="is_active"
            name="is_active"
            checked={formData.is_active}
            onChange={handleChange}
            className="h-4 w-4 text-accent-red focus:ring-accent-red border-gray-300 rounded"
          />
          <label htmlFor="is_active" className="ml-2 block text-sm text-gray-900">
            {t('services.activeService', 'Active Service')}
          </label>
        </div>

        <div className="flex justify-end gap-3 pt-4">
          <Button
            type="button"
            variant="secondary"
            onClick={onClose}
            disabled={loading}
          >
            {t('common.cancel', 'Cancel')}
          </Button>
          <Button
            type="submit"
            variant="primary"
            loading={loading}
          >
            {isEdit ? t('common.update', 'Update') : t('common.create', 'Create')}
          </Button>
        </div>
      </form>
    </Modal>
  );
};