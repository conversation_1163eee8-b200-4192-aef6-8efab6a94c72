# TutorAide Timezone Configuration

## Overview
As of June 11, 2025, TutorAide has been configured to use **Eastern Standard Time (EST)** for all timestamps throughout the application. This ensures consistency for our primarily Eastern North American user base.

## Implementation Details

### 1. Core Timezone Module
- **Location**: `/app/core/timezone.py`
- **Functions**:
  - `now_est()`: Returns current datetime in EST
  - `utc_to_est(dt)`: Converts UTC datetime to EST
  - `est_to_utc(dt)`: Converts EST datetime to UTC
  - `format_est_datetime(dt)`: Formats datetime with EST indicator

### 2. Application Changes
All instances of `datetime.utcnow()` have been replaced with `now_est()` in:
- Domain models
- Exception handling
- Logging framework
- Security/JWT tokens
- All repository classes
- Database operations

### 3. Database Configuration
- Migration `002_set_timezone_est.sql` sets the PostgreSQL database timezone to 'America/New_York'
- This handles EST/EDT transitions automatically
- Database functions available:
  - `current_timestamp_est()`: Get current time in EST
  - `utc_to_est(timestamp)`: Convert UTC to EST
  - `est_to_utc(timestamp)`: Convert EST to UTC

### 4. Logging Configuration
- Log timestamps now display in EST with "EST" suffix
- System timezone set to 'US/Eastern' for consistent logging

### 5. Task Completion Log
- All timestamps in TASK_COMPLETION_LOG.md now use EST instead of UTC

## Important Notes

### For Developers
1. **Always use `now_est()`** instead of `datetime.utcnow()` or `datetime.now()`
2. Import from `app.core.timezone` for timezone functions
3. When displaying timestamps to users, use `format_est_datetime()` for consistency

### For Testing
- Mock `now_est()` instead of `datetime.utcnow()` in tests
- Be aware that all timestamps are EST when asserting test results

### For Production
- Ensure the production database has the EST timezone migration applied
- Monitor logs to confirm timestamps are displaying correctly in EST

## Migration Guide

If you need to convert existing UTC data to EST:

```python
from app.core.timezone import utc_to_est

# Convert a UTC datetime to EST
utc_time = datetime(2025, 6, 11, 12, 0, 0)  # 12:00 PM UTC
est_time = utc_to_est(utc_time)  # 8:00 AM EST
```

## Timezone Considerations

- EST is UTC-5 (UTC-4 during daylight saving time)
- The system automatically handles EST/EDT transitions
- All database timestamps are timezone-aware
- API responses include timezone information