import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../contexts/AuthContext';
import { Card } from '../../components/common/Card';
import Button from '../../components/common/Button';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import { Modal } from '../../components/common/Modal';
import { 
  Users, 
  Plus, 
  Edit2, 
  Trash2, 
  User,
  Calendar,
  School,
  Target,
  AlertCircle,
  ChevronRight
} from 'lucide-react';
import api from '../../services/api';
import toast from 'react-hot-toast';
import { DependantCard } from '../../components/client/dependants/DependantCard';
import { DependantForm } from '../../components/client/dependants/DependantForm';

interface Dependant {
  dependant_id: number;
  client_id: number;
  first_name: string;
  last_name: string;
  date_of_birth: string;
  grade_level: string | null;
  school_name: string | null;
  special_needs: string | null;
  allergies: string | null;
  medications: string | null;
  learning_goals: string | null;
  created_at: string;
  updated_at: string;
}

export const DependantDashboard: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [dependants, setDependants] = useState<Dependant[]>([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [editingDependant, setEditingDependant] = useState<Dependant | null>(null);
  const [deletingDependant, setDeletingDependant] = useState<Dependant | null>(null);

  useEffect(() => {
    fetchDependants();
  }, []);

  const fetchDependants = async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      const response = await api.get(`/api/v1/clients/${user.id}/dependants`);
      setDependants(response.data);
    } catch (error) {
      console.error('Error fetching dependants:', error);
      toast.error(t('client.dependants.fetchError'));
    } finally {
      setLoading(false);
    }
  };

  const handleSaveDependant = async (data: any) => {
    if (!user) return;
    
    try {
      if (editingDependant) {
        // Update existing dependant
        await api.put(`/api/v1/dependants/${editingDependant.dependant_id}`, data);
        toast.success(t('client.dependants.updateSuccess'));
      } else {
        // Create new dependant
        await api.post(`/api/v1/clients/${user.id}/dependants`, data);
        toast.success(t('client.dependants.createSuccess'));
      }
      
      setShowModal(false);
      setEditingDependant(null);
      fetchDependants();
    } catch (error) {
      console.error('Error saving dependant:', error);
      toast.error(
        editingDependant 
          ? t('client.dependants.updateError')
          : t('client.dependants.createError')
      );
    }
  };

  const handleDeleteDependant = async () => {
    if (!deletingDependant) return;
    
    try {
      await api.delete(`/api/v1/dependants/${deletingDependant.dependant_id}`);
      toast.success(t('client.dependants.deleteSuccess'));
      setDeletingDependant(null);
      fetchDependants();
    } catch (error) {
      console.error('Error deleting dependant:', error);
      toast.error(t('client.dependants.deleteError'));
    }
  };

  const openEditModal = (dependant: Dependant) => {
    setEditingDependant(dependant);
    setShowModal(true);
  };

  const openCreateModal = () => {
    setEditingDependant(null);
    setShowModal(true);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Header */}
      <div className="flex justify-between items-start mb-8">
        <div>
          <h1 className="text-3xl font-bold text-text-primary mb-2">
            {t('client.dependants.title')}
          </h1>
          <p className="text-text-secondary">
            {t('client.dependants.subtitle')}
          </p>
        </div>
        
        <Button
          onClick={openCreateModal}
          leftIcon={<Plus className="w-4 h-4" />}
        >
          {t('client.dependants.addDependant')}
        </Button>
      </div>

      {/* Dependants Grid */}
      {dependants.length === 0 ? (
        <Card className="p-8 text-center">
          <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-text-primary mb-2">
            {t('client.dependants.noDependants')}
          </h3>
          <p className="text-text-secondary mb-6">
            {t('client.dependants.noDependantsDescription')}
          </p>
          <Button
            onClick={openCreateModal}
            leftIcon={<Plus className="w-4 h-4" />}
          >
            {t('client.dependants.addFirstDependant')}
          </Button>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {dependants.map((dependant) => (
            <DependantCard
              key={dependant.dependant_id}
              dependant={dependant}
              onEdit={() => openEditModal(dependant)}
              onDelete={() => setDeletingDependant(dependant)}
            />
          ))}
        </div>
      )}

      {/* Add/Edit Modal */}
      <Modal
        isOpen={showModal}
        onClose={() => {
          setShowModal(false);
          setEditingDependant(null);
        }}
        title={editingDependant 
          ? t('client.dependants.editDependant') 
          : t('client.dependants.addDependant')
        }
        size="lg"
      >
        <DependantForm
          dependant={editingDependant}
          onSave={handleSaveDependant}
          onCancel={() => {
            setShowModal(false);
            setEditingDependant(null);
          }}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={!!deletingDependant}
        onClose={() => setDeletingDependant(null)}
        title={t('client.dependants.confirmDelete')}
        size="sm"
      >
        <div className="space-y-4">
          <div className="flex items-start gap-3">
            <AlertCircle className="w-5 h-5 text-amber-600 flex-shrink-0 mt-0.5" />
            <div>
              <p className="text-text-primary">
                {t('client.dependants.deleteWarning', {
                  name: deletingDependant ? 
                    `${deletingDependant.first_name} ${deletingDependant.last_name}` : ''
                })}
              </p>
              <p className="text-sm text-text-secondary mt-2">
                {t('client.dependants.deleteInfo')}
              </p>
            </div>
          </div>
          
          <div className="flex justify-end gap-3 pt-4">
            <Button
              variant="ghost"
              onClick={() => setDeletingDependant(null)}
            >
              {t('common.cancel')}
            </Button>
            <Button
              variant="primary"
              className="bg-red-600 hover:bg-red-700"
              onClick={handleDeleteDependant}
            >
              {t('common.delete')}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Info Section */}
      <Card className="mt-8 p-6 bg-blue-50 border-blue-200">
        <h3 className="text-lg font-semibold text-blue-900 mb-3">
          {t('client.dependants.info.title')}
        </h3>
        <div className="space-y-2 text-sm text-blue-800">
          <p>{t('client.dependants.info.booking')}</p>
          <p>{t('client.dependants.info.progress')}</p>
          <p>{t('client.dependants.info.communication')}</p>
          <p>{t('client.dependants.info.separated')}</p>
        </div>
      </Card>
    </div>
  );
};

export default DependantDashboard;