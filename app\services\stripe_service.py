"""
Stripe payment integration service for Canadian banking and direct deposits.
"""

import stripe
import asyncio
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, date
from decimal import Decimal
import json

from app.config.settings import get_settings
from app.database.repositories.billing_repository import BillingRepository
from app.core.exceptions import BusinessLogicError, ValidationError
from app.core.timezone import now_est
from app.config.database import get_db_connection
from app.models.billing_models import StripePayment, StripePaymentResponse

logger = logging.getLogger(__name__)

settings = get_settings()


class StripeService:
    """Stripe payment processing service with Canadian banking support."""
    
    def __init__(self):
        # Initialize Stripe with secret key
        stripe.api_key = settings.STRIPE_SECRET_KEY
        self.billing_repo = BillingRepository()
        self.webhook_secret = settings.STRIPE_WEBHOOK_SECRET
        
        # Canadian banking configuration
        self.supported_currencies = ['CAD']
        self.supported_payment_methods = [
            'card',
            'acss_debit',  # Canadian Pre-Authorized Debit
            'interac',     # Interac e-Transfer
        ]
    
    async def create_payment_intent(
        self,
        amount: Decimal,
        client_id: int,
        currency: str = 'CAD',
        invoice_id: Optional[int] = None,
        payment_method_types: Optional[List[str]] = None,
        metadata: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """
        Create Stripe PaymentIntent for invoice payment.
        
        Args:
            amount: Payment amount in CAD
            currency: Currency code (CAD)
            client_id: Client making payment
            invoice_id: Optional invoice ID
            payment_method_types: Allowed payment methods
            metadata: Additional metadata
            
        Returns:
            PaymentIntent data with client_secret
        """
        try:
            # Validate currency
            if currency not in self.supported_currencies:
                raise ValidationError(f"Currency {currency} not supported")
            
            # Convert Decimal to cents for Stripe
            amount_cents = int(amount * 100)
            
            if amount_cents < 50:  # Stripe minimum for CAD
                raise ValidationError("Amount must be at least $0.50 CAD")
            
            # Default payment method types for Canada
            if not payment_method_types:
                payment_method_types = ['card', 'acss_debit']
            
            # Validate payment methods
            for pm_type in payment_method_types:
                if pm_type not in self.supported_payment_methods:
                    raise ValidationError(f"Payment method {pm_type} not supported in Canada")
            
            # Prepare metadata
            payment_metadata = {
                'client_id': str(client_id),
                'platform': 'tutoraide',
                'environment': settings.ENVIRONMENT
            }
            if invoice_id:
                payment_metadata['invoice_id'] = str(invoice_id)
            if metadata:
                payment_metadata.update(metadata)
            
            # Create PaymentIntent
            payment_intent = stripe.PaymentIntent.create(
                amount=amount_cents,
                currency=currency.lower(),
                payment_method_types=payment_method_types,
                metadata=payment_metadata,
                automatic_payment_methods={
                    'enabled': True,
                    'allow_redirects': 'never'  # For better UX
                },
                statement_descriptor='TUTORAIDE',
                statement_descriptor_suffix='TUTORING',
                # Canadian specific configurations
                setup_future_usage='off_session' if invoice_id else None,  # Save for future if invoice
            )
            
            # Store payment in database
            async with get_db_connection() as conn:
                await self.billing_repo.create_stripe_payment(conn, {
                    'invoice_id': invoice_id,
                    'client_id': client_id,
                    'stripe_payment_intent_id': payment_intent.id,
                    'amount': amount,
                    'currency': currency,
                    'status': payment_intent.status,
                    'payment_method_type': ','.join(payment_method_types),
                    'metadata': payment_metadata
                })
            
            logger.info(f"Created PaymentIntent {payment_intent.id} for client {client_id}, amount ${amount}")
            
            return {
                'client_secret': payment_intent.client_secret,
                'payment_intent_id': payment_intent.id,
                'amount': amount,
                'currency': currency,
                'status': payment_intent.status
            }
            
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error creating PaymentIntent: {str(e)}")
            raise BusinessLogicError(f"Payment processing error: {str(e)}")
        except Exception as e:
            logger.error(f"Error creating PaymentIntent: {str(e)}")
            raise BusinessLogicError(f"Failed to create payment: {str(e)}")
    
    async def confirm_payment(
        self,
        payment_intent_id: str,
        payment_method_id: str
    ) -> Dict[str, Any]:
        """
        Confirm payment with provided payment method.
        
        Args:
            payment_intent_id: Stripe PaymentIntent ID
            payment_method_id: Stripe PaymentMethod ID
            
        Returns:
            Confirmed PaymentIntent data
        """
        try:
            # Confirm the payment
            payment_intent = stripe.PaymentIntent.confirm(
                payment_intent_id,
                payment_method=payment_method_id,
                return_url=f"{settings.FRONTEND_URL}/billing/payment-complete"
            )
            
            # Update database record
            async with get_db_connection() as conn:
                await self.billing_repo.update_stripe_payment(
                    conn,
                    payment_intent_id,
                    {
                        'status': payment_intent.status,
                        'payment_method_type': payment_intent.charges.data[0].payment_method_details.type if payment_intent.charges.data else 'unknown'
                    }
                )
            
            logger.info(f"Confirmed PaymentIntent {payment_intent_id}, status: {payment_intent.status}")
            
            return {
                'payment_intent_id': payment_intent.id,
                'status': payment_intent.status,
                'requires_action': payment_intent.status == 'requires_action',
                'next_action': payment_intent.next_action
            }
            
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error confirming payment: {str(e)}")
            raise BusinessLogicError(f"Payment confirmation error: {str(e)}")
    
    async def create_payout_to_tutor(
        self,
        tutor_id: int,
        amount: Decimal,
        currency: str = 'CAD',
        description: str = "Weekly tutor payment",
        payment_period: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Create payout to tutor using Stripe Connect.
        
        Args:
            tutor_id: Tutor ID
            amount: Payout amount
            currency: Currency (CAD)
            description: Payout description
            payment_period: Payment period (e.g. "2024-01-15 to 2024-01-21")
            
        Returns:
            Payout data
        """
        try:
            # Convert to cents
            amount_cents = int(amount * 100)
            
            if amount_cents < 100:  # $1.00 minimum for payouts
                raise ValidationError("Payout amount must be at least $1.00 CAD")
            
            async with get_db_connection() as conn:
                # Get tutor's Stripe Connect account
                tutor_query = """
                    SELECT stripe_connect_account_id, bank_account_verified,
                           first_name, last_name, email
                    FROM tutors
                    WHERE tutor_id = $1 AND deleted_at IS NULL
                """
                tutor = await conn.fetchrow(tutor_query, tutor_id)
                
                if not tutor:
                    raise BusinessLogicError(f"Tutor {tutor_id} not found")
                
                if not tutor['stripe_connect_account_id']:
                    # Create Stripe Connect account if doesn't exist
                    connect_account = await self._create_tutor_connect_account(
                        tutor_id, tutor['email'], 
                        f"{tutor['first_name']} {tutor['last_name']}"
                    )
                    stripe_account_id = connect_account['id']
                else:
                    stripe_account_id = tutor['stripe_connect_account_id']
                
                # Create transfer to connected account
                transfer = stripe.Transfer.create(
                    amount=amount_cents,
                    currency=currency.lower(),
                    destination=stripe_account_id,
                    description=description,
                    metadata={
                        'tutor_id': str(tutor_id),
                        'platform': 'tutoraide',
                        'payout_type': 'weekly_payment',
                        'payment_period': payment_period or ''
                    },
                    transfer_group=f"tutor_{tutor_id}_week_{payment_period}"
                )
                
                # Create payout in connected account
                payout = stripe.Payout.create(
                    amount=amount_cents,
                    currency=currency.lower(),
                    description=f"TutorAide: {description}",
                    metadata={
                        'tutor_id': str(tutor_id),
                        'payment_period': payment_period or ''
                    },
                    statement_descriptor='TUTORAIDE',
                    stripe_account=stripe_account_id  # Payout from connected account
                )
                
                # Store payout record
                await conn.execute("""
                    INSERT INTO tutor_stripe_payouts (
                        tutor_id, stripe_transfer_id, stripe_payout_id,
                        amount, currency, status, payment_period,
                        created_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                """, tutor_id, transfer.id, payout.id, amount, currency,
                    payout.status, payment_period, now_est())
                
                logger.info(f"Created payout {payout.id} for tutor {tutor_id}, amount ${amount}")
                
                return {
                    'payout_id': payout.id,
                    'transfer_id': transfer.id,
                    'amount': amount,
                    'currency': currency,
                    'status': payout.status,
                    'arrival_date': payout.arrival_date,
                    'description': description
                }
            
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error creating payout: {str(e)}")
            raise BusinessLogicError(f"Payout creation error: {str(e)}")
    
    async def _create_tutor_connect_account(
        self, tutor_id: int, email: str, name: str
    ) -> Dict[str, Any]:
        """Create Stripe Connect account for tutor."""
        try:
            # Create Express account for Canadian tutor
            account = stripe.Account.create(
                type='express',
                country='CA',
                email=email,
                capabilities={
                    'transfers': {'requested': True},
                },
                business_type='individual',
                individual={
                    'email': email,
                    'first_name': name.split()[0] if name else '',
                    'last_name': name.split()[-1] if name and len(name.split()) > 1 else ''
                },
                metadata={
                    'tutor_id': str(tutor_id),
                    'platform': 'tutoraide'
                },
                settings={
                    'payouts': {
                        'schedule': {
                            'interval': 'manual'  # We control payout timing
                        }
                    }
                }
            )
            
            # Store account ID
            async with get_db_connection() as conn:
                await conn.execute("""
                    UPDATE tutors 
                    SET stripe_connect_account_id = $1,
                        stripe_account_created_at = $2
                    WHERE tutor_id = $3
                """, account.id, now_est(), tutor_id)
            
            logger.info(f"Created Stripe Connect account {account.id} for tutor {tutor_id}")
            
            return {
                'id': account.id,
                'type': account.type,
                'charges_enabled': account.charges_enabled,
                'payouts_enabled': account.payouts_enabled
            }
            
        except stripe.error.StripeError as e:
            logger.error(f"Error creating Connect account: {str(e)}")
            raise BusinessLogicError(f"Failed to create payment account: {str(e)}")
    
    async def create_tutor_onboarding_link(
        self, tutor_id: int, return_url: str, refresh_url: str
    ) -> Dict[str, Any]:
        """
        Create Stripe Connect onboarding link for tutor.
        
        Args:
            tutor_id: Tutor ID
            return_url: URL to redirect after onboarding
            refresh_url: URL to refresh expired link
            
        Returns:
            Onboarding link data
        """
        try:
            async with get_db_connection() as conn:
                # Get tutor's Stripe account
                tutor = await conn.fetchrow("""
                    SELECT stripe_connect_account_id, email, first_name, last_name
                    FROM tutors
                    WHERE tutor_id = $1
                """, tutor_id)
                
                if not tutor:
                    raise BusinessLogicError(f"Tutor {tutor_id} not found")
                
                # Create account if doesn't exist
                if not tutor['stripe_connect_account_id']:
                    account = await self._create_tutor_connect_account(
                        tutor_id, tutor['email'],
                        f"{tutor['first_name']} {tutor['last_name']}"
                    )
                    stripe_account_id = account['id']
                else:
                    stripe_account_id = tutor['stripe_connect_account_id']
                
                # Create account link
                account_link = stripe.AccountLink.create(
                    account=stripe_account_id,
                    return_url=return_url,
                    refresh_url=refresh_url,
                    type='account_onboarding'
                )
                
                logger.info(f"Created onboarding link for tutor {tutor_id}")
                
                return {
                    'url': account_link.url,
                    'expires_at': account_link.expires_at,
                    'stripe_account_id': stripe_account_id
                }
                
        except stripe.error.StripeError as e:
            logger.error(f"Error creating onboarding link: {str(e)}")
            raise BusinessLogicError(f"Failed to create onboarding link: {str(e)}")
    
    async def setup_canadian_bank_account(
        self,
        tutor_id: int,
        account_holder_name: str,
        institution_number: str,
        transit_number: str,
        account_number: str,
        account_holder_type: str = 'individual'
    ) -> Dict[str, Any]:
        """
        Set up Canadian bank account for tutor payouts.
        
        Args:
            tutor_id: Tutor ID
            account_holder_name: Account holder name
            institution_number: Canadian institution number (3 digits)
            transit_number: Transit number (5 digits)
            account_number: Account number
            account_holder_type: individual or company
            
        Returns:
            Bank account setup data
        """
        try:
            # Validate Canadian banking format
            if len(institution_number) != 3 or not institution_number.isdigit():
                raise ValidationError("Institution number must be 3 digits")
            
            if len(transit_number) != 5 or not transit_number.isdigit():
                raise ValidationError("Transit number must be 5 digits")
            
            if len(account_number) < 7 or len(account_number) > 12:
                raise ValidationError("Account number must be 7-12 digits")
            
            # Create bank account token
            bank_account = stripe.BankAccount.create(
                account_number=account_number,
                routing_number=f"{institution_number}{transit_number}",  # Combine for Stripe
                account_holder_name=account_holder_name,
                account_holder_type=account_holder_type,
                country='CA',
                currency='cad'
            )
            
            logger.info(f"Created Canadian bank account for tutor {tutor_id}")
            
            return {
                'bank_account_id': bank_account.id,
                'account_holder_name': account_holder_name,
                'institution_number': institution_number,
                'transit_number': transit_number,
                'last4': bank_account.last4,
                'status': bank_account.status
            }
            
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error setting up bank account: {str(e)}")
            raise BusinessLogicError(f"Bank account setup error: {str(e)}")
    
    async def handle_webhook(self, payload: bytes, signature: str) -> Dict[str, Any]:
        """
        Handle Stripe webhook events.
        
        Args:
            payload: Webhook payload
            signature: Stripe signature header
            
        Returns:
            Processing result
        """
        try:
            # Verify webhook signature
            event = stripe.Webhook.construct_event(
                payload, signature, self.webhook_secret
            )
            
            event_type = event['type']
            event_data = event['data']['object']
            
            logger.info(f"Processing Stripe webhook: {event_type}")
            
            # Handle different event types
            if event_type == 'payment_intent.succeeded':
                await self._handle_payment_succeeded(event_data)
            elif event_type == 'payment_intent.payment_failed':
                await self._handle_payment_failed(event_data)
            elif event_type == 'payout.paid':
                await self._handle_payout_paid(event_data)
            elif event_type == 'payout.failed':
                await self._handle_payout_failed(event_data)
            elif event_type == 'charge.dispute.created':
                await self._handle_chargeback(event_data)
            else:
                logger.info(f"Unhandled webhook event type: {event_type}")
            
            return {'status': 'processed', 'event_type': event_type}
            
        except stripe.error.SignatureVerificationError as e:
            logger.error(f"Invalid webhook signature: {str(e)}")
            raise ValidationError("Invalid webhook signature")
        except Exception as e:
            logger.error(f"Error processing webhook: {str(e)}")
            raise BusinessLogicError(f"Webhook processing error: {str(e)}")
    
    async def _handle_payment_succeeded(self, payment_intent: Dict[str, Any]) -> None:
        """Handle successful payment webhook."""
        payment_intent_id = payment_intent['id']
        
        try:
            async with get_db_connection() as conn:
                # Update payment record
                await self.billing_repo.update_stripe_payment(
                    conn,
                    payment_intent_id,
                    {
                        'status': 'succeeded',
                        'stripe_charge_id': payment_intent.get('charges', {}).get('data', [{}])[0].get('id'),
                        'receipt_url': payment_intent.get('charges', {}).get('data', [{}])[0].get('receipt_url'),
                        'webhook_events': ['payment_intent.succeeded']
                    }
                )
                
                # Get the payment record to find associated invoice
                payment_record = await self.billing_repo.find_stripe_payment_by_intent(conn, payment_intent_id)
                
                if payment_record and payment_record['invoice_id']:
                    # Mark invoice as paid
                    from app.models.billing_models import PaymentRecord
                    
                    payment_data = PaymentRecord(
                        invoice_id=payment_record['invoice_id'],
                        payment_method='stripe',
                        payment_reference=payment_intent_id,
                        payment_date=date.today()
                    )
                    
                    # Import locally to avoid circular dependency
                    from app.services.billing_service import BillingService
                    billing_service = BillingService()
                    
                    await billing_service.record_payment(
                        payment_data=payment_data,
                        recorded_by=0  # System user
                    )
                    
                    logger.info(f"Marked invoice {payment_record['invoice_id']} as paid via Stripe")
            
        except Exception as e:
            logger.error(f"Error handling payment success webhook: {str(e)}")
    
    async def _handle_payment_failed(self, payment_intent: Dict[str, Any]) -> None:
        """Handle failed payment webhook."""
        payment_intent_id = payment_intent['id']
        
        try:
            async with get_db_connection() as conn:
                await self.billing_repo.update_stripe_payment(
                    conn,
                    payment_intent_id,
                    {
                        'status': 'failed',
                        'failure_reason': payment_intent.get('last_payment_error', {}).get('message', 'Unknown error'),
                        'webhook_events': ['payment_intent.payment_failed']
                    }
                )
            
            logger.warning(f"Payment failed for PaymentIntent {payment_intent_id}")
            
        except Exception as e:
            logger.error(f"Error handling payment failed webhook: {str(e)}")
    
    async def _handle_payout_paid(self, payout: Dict[str, Any]) -> None:
        """Handle successful payout webhook."""
        payout_id = payout['id']
        tutor_id = payout.get('metadata', {}).get('tutor_id')
        
        try:
            if tutor_id:
                # Update tutor payment record
                async with get_db_connection() as conn:
                    await conn.execute("""
                        UPDATE tutor_payments 
                        SET 
                            status = 'paid',
                            paid_date = CURRENT_DATE,
                            payment_reference = $1,
                            updated_at = $2
                        WHERE tutor_id = $3 
                        AND status = 'approved'
                        AND payment_reference IS NULL
                    """, payout_id, now_est(), int(tutor_id))
                
                logger.info(f"Marked tutor payment as paid: payout {payout_id}, tutor {tutor_id}")
            
        except Exception as e:
            logger.error(f"Error handling payout paid webhook: {str(e)}")
    
    async def _handle_payout_failed(self, payout: Dict[str, Any]) -> None:
        """Handle failed payout webhook."""
        payout_id = payout['id']
        failure_reason = payout.get('failure_message', 'Unknown error')
        
        logger.error(f"Payout failed: {payout_id}, reason: {failure_reason}")
        
        # TODO: Implement notification to administrators
    
    async def _handle_chargeback(self, dispute: Dict[str, Any]) -> None:
        """Handle chargeback/dispute webhook."""
        charge_id = dispute['charge']
        amount = Decimal(dispute['amount']) / 100  # Convert from cents
        reason = dispute['reason']
        
        logger.warning(f"Chargeback created for charge {charge_id}: ${amount}, reason: {reason}")
        
        # TODO: Implement chargeback handling workflow
    
    async def get_payment_methods_for_client(self, client_id: int) -> List[Dict[str, Any]]:
        """Get saved payment methods for a client."""
        try:
            # Get customer ID from database or create customer
            customer_id = await self._get_or_create_stripe_customer(client_id)
            
            # List payment methods
            payment_methods = stripe.PaymentMethod.list(
                customer=customer_id,
                type='card'
            )
            
            return [
                {
                    'id': pm.id,
                    'type': pm.type,
                    'card': {
                        'brand': pm.card.brand,
                        'last4': pm.card.last4,
                        'exp_month': pm.card.exp_month,
                        'exp_year': pm.card.exp_year
                    } if pm.card else None
                }
                for pm in payment_methods.data
            ]
            
        except stripe.error.StripeError as e:
            logger.error(f"Error getting payment methods: {str(e)}")
            return []
    
    async def _get_or_create_stripe_customer(self, client_id: int) -> str:
        """Get or create Stripe customer for client."""
        try:
            async with get_db_connection() as conn:
                # Check if customer exists
                customer_record = await conn.fetchrow("""
                    SELECT stripe_customer_id 
                    FROM client_stripe_customers 
                    WHERE client_id = $1
                """, client_id)
                
                if customer_record:
                    return customer_record['stripe_customer_id']
                
                # Get client details
                client = await conn.fetchrow("""
                    SELECT first_name, last_name, email 
                    FROM clients 
                    WHERE client_id = $1
                """, client_id)
                
                if not client:
                    raise BusinessLogicError(f"Client {client_id} not found")
                
                # Create Stripe customer
                customer = stripe.Customer.create(
                    name=f"{client['first_name']} {client['last_name']}",
                    email=client['email'],
                    metadata={'client_id': str(client_id)}
                )
                
                # Store customer ID
                await conn.execute("""
                    INSERT INTO client_stripe_customers (client_id, stripe_customer_id, created_at)
                    VALUES ($1, $2, $3)
                """, client_id, customer.id, now_est())
                
                return customer.id
                
        except stripe.error.StripeError as e:
            logger.error(f"Error creating Stripe customer: {str(e)}")
            raise BusinessLogicError(f"Customer creation error: {str(e)}")
    
    async def refund_payment(
        self,
        payment_intent_id: str,
        amount: Optional[Decimal] = None,
        reason: str = 'requested_by_customer'
    ) -> Dict[str, Any]:
        """
        Refund a payment.
        
        Args:
            payment_intent_id: Stripe PaymentIntent ID
            amount: Optional partial refund amount
            reason: Refund reason
            
        Returns:
            Refund data
        """
        try:
            # Get the charge ID from PaymentIntent
            payment_intent = stripe.PaymentIntent.retrieve(payment_intent_id)
            
            if not payment_intent.charges.data:
                raise BusinessLogicError("No charge found for this payment")
            
            charge_id = payment_intent.charges.data[0].id
            
            refund_params = {
                'charge': charge_id,
                'reason': reason,
                'metadata': {
                    'payment_intent_id': payment_intent_id,
                    'refund_type': 'partial' if amount else 'full'
                }
            }
            
            if amount:
                refund_params['amount'] = int(amount * 100)  # Convert to cents
            
            # Create refund
            refund = stripe.Refund.create(**refund_params)
            
            logger.info(f"Created refund {refund.id} for PaymentIntent {payment_intent_id}")
            
            return {
                'refund_id': refund.id,
                'amount': Decimal(refund.amount) / 100,
                'currency': refund.currency.upper(),
                'status': refund.status,
                'reason': refund.reason
            }
            
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error creating refund: {str(e)}")
            raise BusinessLogicError(f"Refund error: {str(e)}")
    
    def validate_canadian_banking_info(
        self,
        institution_number: str,
        transit_number: str,
        account_number: str
    ) -> Dict[str, Any]:
        """
        Validate Canadian banking information.
        
        Args:
            institution_number: 3-digit institution number
            transit_number: 5-digit transit number  
            account_number: 7-12 digit account number
            
        Returns:
            Validation results
        """
        errors = []
        
        # Validate institution number
        if not institution_number.isdigit() or len(institution_number) != 3:
            errors.append("Institution number must be exactly 3 digits")
        
        # Validate transit number
        if not transit_number.isdigit() or len(transit_number) != 5:
            errors.append("Transit number must be exactly 5 digits")
        
        # Validate account number
        if not account_number.isdigit() or not (7 <= len(account_number) <= 12):
            errors.append("Account number must be 7-12 digits")
        
        # Check mod-10 validation for account number (simplified)
        if account_number.isdigit() and len(account_number) >= 7:
            checksum = sum(int(digit) for digit in account_number[-1::-2]) + \
                      sum(sum(divmod(int(digit) * 2, 10)) for digit in account_number[-2::-2])
            if checksum % 10 != 0:
                errors.append("Account number appears to be invalid (checksum failed)")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'formatted_routing': f"{institution_number}{transit_number}" if len(errors) == 0 else None
        }