# TutorAide Key Features and Business Logic

## Core Features

### 1. **Authentication & Authorization**
- Multi-role support (<PERSON>, Tu<PERSON>, Client)
- JWT-based authentication with refresh tokens
- Google OAuth integration
- Role switching capabilities
- Email verification system
- Password reset functionality
- Session management
- Consent management (Level 1 mandatory, Level 2 optional)

### 2. **User Management**
- User profiles with role-based access
- Tutor invitation system
- Client profile management
- Dependant management (supporting separated families)
- Language preferences (EN/FR)
- Push notification tokens

### 3. **Appointment System**
- Appointment scheduling with conflict detection
- Tutor availability management
- SMS confirmation system (via Twilio)
- Calendar integration with WebSocket updates
- Duration adjustment tracking
- Appointment stats and analytics
- Automated reminders (24-hour advance)
- AI-powered scheduling suggestions

### 4. **Service Management**
- Service catalog with subjects (math, science, french, english, others)
- Service types (online, in-person, library, hybrid)
- TECFEE program management (12-session packages)
- Service area configuration
- Pricing and rate management

### 5. **Billing & Payments**
- Invoice generation for completed sessions
- Stripe payment integration
- Subscription system with pre-paid hours
- Weekly tutor payment cycles (Thursday-Wednesday)
- Parent payment tracking (separated families)
- Financial encryption for sensitive data
- Package management

### 6. **Communication**
- Dual notification system:
  - OneSignal for push notifications
  - Twilio for SMS notifications
- WebSocket real-time messaging
- SMS conversation management
- Email service integration
- Template management (EN/FR)
- Role indicators in messages

### 7. **Geographic Features**
- Geocoding service for address to coordinates
- Distance calculation between clients and tutors
- Tutor service area management
- 5 nearest tutors algorithm

### 8. **Search & Discovery**
- Global search functionality
- Quick actions based on user role
- Tutor matching algorithm
- Recommendation engine

### 9. **Customer Service**
- SMS conversation threads
- Messaging interface for managers
- Customer service statistics

### 10. **Security & Compliance**
- Input validation middleware
- Rate limiting service
- Financial data encryption
- Secure payment processing
- Authorization middleware
- Security headers

## Key Services
- **AppointmentService**: Core scheduling logic
- **AuthService**: Authentication and authorization
- **BillingService**: Invoice and payment processing
- **NotificationOrchestrator**: Unified notification handling
- **TutorMatchingService**: Smart tutor recommendations
- **StripeService**: Payment processing
- **TwilioService**: SMS messaging
- **OneSignalService**: Push notifications
- **GeocodingService**: Location services
- **TemplateService**: Multi-language templates
```