-- Migration: Add quick actions tables
-- Description: Tables for tracking quick action usage and user preferences

-- Create table for tracking quick action usage
CREATE TABLE IF NOT EXISTS quick_action_usage (
    usage_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id),
    action_type VARCHAR(50) NOT NULL,
    executed_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    execution_time_ms INTEGER,
    success BOOLEAN NOT NULL DEFAULT true,
    error_message TEXT,
    context_data JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create table for user pinned actions
CREATE TABLE IF NOT EXISTS user_pinned_actions (
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id),
    action_type VARCHAR(50) NOT NULL,
    pinned_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, action_type)
);

-- <PERSON>reate indexes for performance
CREATE INDEX idx_quick_action_usage_user_id ON quick_action_usage(user_id);
CREATE INDEX idx_quick_action_usage_action_type ON quick_action_usage(action_type);
CREATE INDEX idx_quick_action_usage_executed_at ON quick_action_usage(executed_at DESC);
CREATE INDEX idx_quick_action_usage_success ON quick_action_usage(success);

-- Create composite index for analytics queries
CREATE INDEX idx_quick_action_analytics ON quick_action_usage(
    action_type, 
    executed_at DESC, 
    success
);

-- Add comments
COMMENT ON TABLE quick_action_usage IS 'Tracks usage of quick actions for analytics and optimization';
COMMENT ON TABLE user_pinned_actions IS 'Stores user-pinned quick actions for easy access';

COMMENT ON COLUMN quick_action_usage.execution_time_ms IS 'Time taken to execute the action in milliseconds';
COMMENT ON COLUMN quick_action_usage.context_data IS 'Additional context data about the action execution';
COMMENT ON COLUMN user_pinned_actions.action_type IS 'Type of quick action from QuickActionType enum';