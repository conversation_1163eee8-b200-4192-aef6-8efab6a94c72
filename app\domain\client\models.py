"""
Client domain models for the TutorAide application.

This module contains all Pydantic models related to client profiles,
including addresses, emergency contacts, preferences, and learning needs.
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from decimal import Decimal
from enum import Enum

from pydantic import BaseModel, Field, ConfigDict, field_validator, computed_field, EmailStr
from app.models.base import BaseEntity, IdentifiedEntity, AddressModel


class AddressType(str, Enum):
    """Address type enumeration."""
    HOME = "home"
    WORK = "work"
    BILLING = "billing"
    OTHER = "other"


class ContactMethod(str, Enum):
    """Preferred contact method enumeration."""
    EMAIL = "email"
    SMS = "sms"
    PHONE = "phone"
    IN_APP = "in_app"


class ParentInvolvementLevel(str, Enum):
    """Parent involvement level enumeration."""
    MINIMAL = "minimal"
    MODERATE = "moderate"
    HIGH = "high"


class Language(str, Enum):
    """Supported languages."""
    EN = "en"
    FR = "fr"


class ClientAddress(BaseEntity):
    """Client address model."""
    
    address_id: int = Field(..., description="Unique address identifier")
    client_id: int = Field(..., description="Associated client ID")
    address_type: AddressType = Field(..., description="Type of address")
    street: str = Field(..., min_length=1, max_length=255, description="Street address")
    city: str = Field(..., min_length=1, max_length=100, description="City")
    province: str = Field(..., min_length=1, max_length=50, description="Province/State")
    postal_code: str = Field(..., min_length=6, max_length=10, description="Postal code")
    country: str = Field(default="Canada", max_length=50, description="Country")
    is_primary: bool = Field(default=False, description="Whether this is the primary address")
    notes: Optional[str] = Field(None, description="Additional notes about the address")
    
    @field_validator('postal_code')
    @classmethod
    def validate_postal_code(cls, v: str) -> str:
        """Validate Canadian postal code format."""
        import re
        # Canadian postal code pattern: A1A 1A1
        postal_pattern = r'^[A-Z]\d[A-Z]\s?\d[A-Z]\d$'
        cleaned = v.upper().strip()
        if not re.match(postal_pattern, cleaned):
            raise ValueError('Invalid Canadian postal code format')
        # Ensure space in middle
        if len(cleaned) == 6:
            cleaned = f"{cleaned[:3]} {cleaned[3:]}"
        return cleaned


class ClientEmergencyContact(BaseEntity):
    """Client emergency contact model."""
    
    contact_id: int = Field(..., description="Unique contact identifier")
    client_id: int = Field(..., description="Associated client ID")
    contact_name: str = Field(..., min_length=1, max_length=100, description="Contact person's name")
    relationship: str = Field(..., min_length=1, max_length=50, description="Relationship to client")
    phone: str = Field(..., min_length=10, max_length=20, description="Contact phone number")
    email: Optional[EmailStr] = Field(None, description="Contact email address")
    is_primary: bool = Field(default=False, description="Whether this is the primary emergency contact")
    can_pickup_dependant: bool = Field(default=False, description="Authorized to pick up dependants")
    notes: Optional[str] = Field(None, description="Additional notes")
    
    @field_validator('phone')
    @classmethod
    def validate_phone(cls, v: str) -> str:
        """Validate phone number format."""
        import re
        # Remove all non-digit characters
        digits = re.sub(r'\D', '', v)
        
        # Check if it's a valid North American number
        if len(digits) == 10:
            return f"+1{digits}"
        elif len(digits) == 11 and digits.startswith('1'):
            return f"+{digits}"
        else:
            raise ValueError('Invalid phone number format')


class ClientPreferences(BaseModel):
    """Client preferences model."""
    
    model_config = ConfigDict(from_attributes=True)
    
    preference_id: int = Field(..., description="Unique preference identifier")
    client_id: int = Field(..., description="Associated client ID")
    preferred_language: Language = Field(default=Language.EN, description="Preferred language")
    timezone: str = Field(default="America/Toronto", max_length=50, description="Preferred timezone")
    sms_enabled: bool = Field(default=True, description="SMS notifications enabled")
    email_enabled: bool = Field(default=True, description="Email notifications enabled")
    push_enabled: bool = Field(default=True, description="Push notifications enabled")
    sms_reminder_hours: int = Field(default=24, ge=0, le=72, description="Hours before appointment for SMS reminder")
    email_reminder_hours: int = Field(default=24, ge=0, le=72, description="Hours before appointment for email reminder")
    preferred_contact_method: ContactMethod = Field(default=ContactMethod.EMAIL, description="Preferred contact method")
    marketing_opt_in: bool = Field(default=False, description="Marketing communications opt-in")
    newsletter_opt_in: bool = Field(default=False, description="Newsletter subscription opt-in")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    
    @field_validator('timezone')
    @classmethod
    def validate_timezone(cls, v: str) -> str:
        """Validate timezone string."""
        import pytz
        try:
            pytz.timezone(v)
        except pytz.exceptions.UnknownTimeZoneError:
            raise ValueError(f"Invalid timezone: {v}")
        return v


class ClientLearningNeeds(BaseModel):
    """Client learning needs model."""
    
    model_config = ConfigDict(from_attributes=True)
    
    learning_need_id: int = Field(..., description="Unique learning need identifier")
    client_id: int = Field(..., description="Associated client ID")
    subject_areas: List[str] = Field(default_factory=list, description="Subject areas of interest")
    learning_goals: Optional[str] = Field(None, description="Specific learning goals")
    special_accommodations: Optional[str] = Field(None, description="Special accommodations required")
    preferred_session_length: int = Field(default=60, ge=30, le=180, description="Preferred session length in minutes")
    preferred_session_frequency: str = Field(default="weekly", max_length=20, description="Preferred session frequency")
    preferred_tutoring_style: Optional[str] = Field(None, max_length=50, description="Preferred tutoring approach")
    strengths: Optional[str] = Field(None, description="Academic strengths")
    challenges: Optional[str] = Field(None, description="Academic challenges")
    parent_involvement_level: ParentInvolvementLevel = Field(
        default=ParentInvolvementLevel.MODERATE,
        description="Expected parent involvement level"
    )
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    
    @field_validator('subject_areas')
    @classmethod
    def validate_subject_areas(cls, v: List[str]) -> List[str]:
        """Validate and normalize subject areas."""
        # Remove duplicates and empty strings
        cleaned = list(set(s.strip().lower() for s in v if s.strip()))
        return sorted(cleaned)


class ClientProfile(IdentifiedEntity):
    """Enhanced client profile model with all relationships."""
    
    client_id: int = Field(..., description="Unique client identifier")
    user_id: int = Field(..., description="Associated user ID")
    first_name: str = Field(..., min_length=1, max_length=50, description="Client's first name")
    last_name: str = Field(..., min_length=1, max_length=50, description="Client's last name")
    phone: Optional[str] = Field(None, max_length=20, description="Primary phone number")
    email: EmailStr = Field(..., description="Primary email address")
    profile_photo_url: Optional[str] = Field(None, max_length=500, description="Profile photo URL")
    profile_completeness: int = Field(default=0, ge=0, le=100, description="Profile completeness percentage")
    last_activity_at: Optional[datetime] = Field(None, description="Last activity timestamp")
    internal_notes: Optional[str] = Field(None, description="Internal notes (not visible to client)")
    referral_source: Optional[str] = Field(None, max_length=100, description="How client found us")
    
    # Related entities
    addresses: List[ClientAddress] = Field(default_factory=list, description="Client addresses")
    emergency_contacts: List[ClientEmergencyContact] = Field(default_factory=list, description="Emergency contacts")
    preferences: Optional[ClientPreferences] = Field(None, description="Client preferences")
    learning_needs: Optional[ClientLearningNeeds] = Field(None, description="Learning needs")
    
    @computed_field
    @property
    def full_name(self) -> str:
        """Get client's full name."""
        return f"{self.first_name} {self.last_name}"
    
    @computed_field
    @property
    def primary_address(self) -> Optional[ClientAddress]:
        """Get primary address."""
        for address in self.addresses:
            if address.is_primary and address.is_active():
                return address
        # Return first active address if no primary
        active_addresses = [a for a in self.addresses if a.is_active()]
        return active_addresses[0] if active_addresses else None
    
    @computed_field
    @property
    def primary_emergency_contact(self) -> Optional[ClientEmergencyContact]:
        """Get primary emergency contact."""
        for contact in self.emergency_contacts:
            if contact.is_primary and contact.is_active():
                return contact
        # Return first active contact if no primary
        active_contacts = [c for c in self.emergency_contacts if c.is_active()]
        return active_contacts[0] if active_contacts else None
    
    @computed_field
    @property
    def has_complete_profile(self) -> bool:
        """Check if profile is complete."""
        return self.profile_completeness >= 80
    
    @computed_field
    @property
    def preferred_language_display(self) -> str:
        """Get display name for preferred language."""
        if self.preferences:
            return "English" if self.preferences.preferred_language == Language.EN else "Français"
        return "English"
    
    def calculate_completeness(self) -> int:
        """Calculate profile completeness percentage."""
        completeness = 0
        
        # Basic info (25%)
        if self.phone:
            completeness += 15
        if self.profile_photo_url:
            completeness += 10
            
        # Address (25%)
        if any(a.is_active() for a in self.addresses):
            completeness += 25
            
        # Emergency contact (25%)
        if any(c.is_active() for c in self.emergency_contacts):
            completeness += 25
            
        # Preferences (15%)
        if self.preferences:
            completeness += 15
            
        # Learning needs (10%)
        if self.learning_needs:
            completeness += 10
            
        return min(completeness, 100)


class ClientProfileCreate(BaseModel):
    """Model for creating a client profile."""
    
    model_config = ConfigDict(from_attributes=True)
    
    user_id: int = Field(..., description="Associated user ID")
    first_name: str = Field(..., min_length=1, max_length=50, description="Client's first name")
    last_name: str = Field(..., min_length=1, max_length=50, description="Client's last name")
    phone: Optional[str] = Field(None, max_length=20, description="Primary phone number")
    email: EmailStr = Field(..., description="Primary email address")
    referral_source: Optional[str] = Field(None, max_length=100, description="How client found us")


class ClientProfileUpdate(BaseModel):
    """Model for updating a client profile."""
    
    model_config = ConfigDict(from_attributes=True)
    
    first_name: Optional[str] = Field(None, min_length=1, max_length=50, description="Client's first name")
    last_name: Optional[str] = Field(None, min_length=1, max_length=50, description="Client's last name")
    phone: Optional[str] = Field(None, max_length=20, description="Primary phone number")
    email: Optional[EmailStr] = Field(None, description="Primary email address")
    profile_photo_url: Optional[str] = Field(None, max_length=500, description="Profile photo URL")
    internal_notes: Optional[str] = Field(None, description="Internal notes")
    referral_source: Optional[str] = Field(None, max_length=100, description="How client found us")


class ClientAddressCreate(BaseModel):
    """Model for creating a client address."""
    
    model_config = ConfigDict(from_attributes=True)
    
    address_type: AddressType = Field(..., description="Type of address")
    street: str = Field(..., min_length=1, max_length=255, description="Street address")
    city: str = Field(..., min_length=1, max_length=100, description="City")
    province: str = Field(..., min_length=1, max_length=50, description="Province/State")
    postal_code: str = Field(..., min_length=6, max_length=10, description="Postal code")
    country: str = Field(default="Canada", max_length=50, description="Country")
    is_primary: bool = Field(default=False, description="Whether this is the primary address")
    notes: Optional[str] = Field(None, description="Additional notes")


class ClientEmergencyContactCreate(BaseModel):
    """Model for creating an emergency contact."""
    
    model_config = ConfigDict(from_attributes=True)
    
    contact_name: str = Field(..., min_length=1, max_length=100, description="Contact person's name")
    relationship: str = Field(..., min_length=1, max_length=50, description="Relationship to client")
    phone: str = Field(..., min_length=10, max_length=20, description="Contact phone number")
    email: Optional[EmailStr] = Field(None, description="Contact email address")
    is_primary: bool = Field(default=False, description="Whether this is the primary emergency contact")
    can_pickup_dependant: bool = Field(default=False, description="Authorized to pick up dependants")
    notes: Optional[str] = Field(None, description="Additional notes")


class ClientPreferencesCreate(BaseModel):
    """Model for creating client preferences."""
    
    model_config = ConfigDict(from_attributes=True)
    
    preferred_language: Language = Field(default=Language.EN, description="Preferred language")
    timezone: str = Field(default="America/Toronto", max_length=50, description="Preferred timezone")
    sms_enabled: bool = Field(default=True, description="SMS notifications enabled")
    email_enabled: bool = Field(default=True, description="Email notifications enabled")
    push_enabled: bool = Field(default=True, description="Push notifications enabled")
    sms_reminder_hours: int = Field(default=24, ge=0, le=72, description="Hours before appointment for SMS reminder")
    email_reminder_hours: int = Field(default=24, ge=0, le=72, description="Hours before appointment for email reminder")
    preferred_contact_method: ContactMethod = Field(default=ContactMethod.EMAIL, description="Preferred contact method")
    marketing_opt_in: bool = Field(default=False, description="Marketing communications opt-in")
    newsletter_opt_in: bool = Field(default=False, description="Newsletter subscription opt-in")


class ClientLearningNeedsCreate(BaseModel):
    """Model for creating learning needs."""
    
    model_config = ConfigDict(from_attributes=True)
    
    subject_areas: List[str] = Field(default_factory=list, description="Subject areas of interest")
    learning_goals: Optional[str] = Field(None, description="Specific learning goals")
    special_accommodations: Optional[str] = Field(None, description="Special accommodations required")
    preferred_session_length: int = Field(default=60, ge=30, le=180, description="Preferred session length in minutes")
    preferred_session_frequency: str = Field(default="weekly", max_length=20, description="Preferred session frequency")
    preferred_tutoring_style: Optional[str] = Field(None, max_length=50, description="Preferred tutoring approach")
    strengths: Optional[str] = Field(None, description="Academic strengths")
    challenges: Optional[str] = Field(None, description="Academic challenges")
    parent_involvement_level: ParentInvolvementLevel = Field(
        default=ParentInvolvementLevel.MODERATE,
        description="Expected parent involvement level"
    )