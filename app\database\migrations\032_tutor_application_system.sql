-- Migration: Add tutor application system fields
-- Description: Enhance tutor_invitations table to support full application workflow

-- Add application-related columns to tutor_invitations
ALTER TABLE tutor_invitations
ADD COLUMN IF NOT EXISTS application_status VARCHAR(20) DEFAULT 'invitation_sent' 
    CHECK (application_status IN ('invitation_sent', 'under_review', 'approved', 'rejected', 'waitlisted')),
ADD COLUMN IF NOT EXISTS application_data JSONB,
ADD COLUMN IF NOT EXISTS application_submitted_at TIMESTAMP,
ADD COLUMN IF NOT EXISTS reviewed_by INTEGER REFERENCES users(user_id),
ADD COLUMN IF NOT EXISTS reviewed_at TIMESTAMP,
ADD COLUMN IF NOT EXISTS review_notes TEXT,
ADD COLUMN IF NOT EXISTS rejection_reason VARCHAR(50) 
    CHECK (rejection_reason IN ('insufficient_education', 'insufficient_experience', 
    'no_relevant_subjects', 'failed_reference_check', 'geographic_limitation', 'other')),
ADD COLUMN IF NOT EXISTS can_reapply_after DATE,
ADD COLUMN IF NOT EXISTS application_history JSONB DEFAULT '[]'::jsonb;

-- Create index for faster application queries
CREATE INDEX IF NOT EXISTS idx_tutor_invitations_application_status 
ON tutor_invitations(application_status) 
WHERE status = 'applied';

CREATE INDEX IF NOT EXISTS idx_tutor_invitations_email_status 
ON tutor_invitations(email, application_status);

-- Create rejected applications table for historical tracking
CREATE TABLE IF NOT EXISTS rejected_applications (
    rejection_id SERIAL PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    application_data JSONB NOT NULL,
    rejection_reason VARCHAR(50) NOT NULL,
    rejection_notes TEXT,
    rejected_by INTEGER NOT NULL REFERENCES users(user_id),
    rejected_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    can_reapply_after DATE,
    original_invitation_id INTEGER REFERENCES tutor_invitations(invitation_id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for rejected applications
CREATE INDEX IF NOT EXISTS idx_rejected_applications_email 
ON rejected_applications(email);

CREATE INDEX IF NOT EXISTS idx_rejected_applications_reapply 
ON rejected_applications(email, can_reapply_after);

-- Add comments for documentation
COMMENT ON COLUMN tutor_invitations.application_status IS 'Status of the application review process';
COMMENT ON COLUMN tutor_invitations.application_data IS 'Full application data including education, experience, preferences';
COMMENT ON COLUMN tutor_invitations.rejection_reason IS 'Standardized reason for rejection';
COMMENT ON COLUMN tutor_invitations.can_reapply_after IS 'Date after which the applicant can reapply';
COMMENT ON COLUMN tutor_invitations.application_history IS 'Array of previous application attempts';

COMMENT ON TABLE rejected_applications IS 'Historical record of rejected tutor applications for compliance and analytics';