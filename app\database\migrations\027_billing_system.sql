-- TutorAide Billing System Schema
-- Version: 027
-- Description: Creates tables for daily invoice generation and weekly tutor payments
-- Author: TutorAide Development Team
-- Date: 2025-01-21

-- ============================================
-- Invoice Management Tables
-- ============================================

-- Client invoices table
CREATE TABLE billing_invoices (
    invoice_id SERIAL PRIMARY KEY,
    client_id INTEGER NOT NULL REFERENCES client_profiles(client_id),
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    invoice_date DATE NOT NULL,
    due_date DATE NOT NULL,
    subtotal DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    tax_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    status payment_status NOT NULL DEFAULT 'pending',
    
    -- Payment tracking
    paid_by_client_id INTEGER REFERENCES client_profiles(client_id),
    paid_date TIMESTAMP,
    payment_method VARCHAR(50), -- 'stripe', 'cash', 'e-transfer', etc.
    stripe_payment_intent_id VARCHAR(255),
    
    -- Subscription related
    is_subscription_deduction BOOLEAN DEFAULT false,
    subscription_id INTEGER REFERENCES subscription_subscriptions(subscription_id),
    
    -- Metadata
    notes TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    
    -- Indexes for performance
    INDEX idx_billing_invoices_client (client_id),
    INDEX idx_billing_invoices_status (status),
    INDEX idx_billing_invoices_date (invoice_date),
    INDEX idx_billing_invoices_due (due_date)
);

-- Invoice line items
CREATE TABLE billing_invoice_items (
    item_id SERIAL PRIMARY KEY,
    invoice_id INTEGER NOT NULL REFERENCES billing_invoices(invoice_id) ON DELETE CASCADE,
    appointment_id INTEGER REFERENCES appointment_appointments(appointment_id),
    
    -- Item details
    item_type VARCHAR(50) NOT NULL, -- 'service', 'transport', 'material', 'other'
    description TEXT NOT NULL,
    quantity DECIMAL(10,2) NOT NULL DEFAULT 1.00,
    unit_price DECIMAL(10,2) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    
    -- For surge pricing tracking
    surge_multiplier DECIMAL(4,2) DEFAULT 1.00,
    surge_amount DECIMAL(10,2) DEFAULT 0.00,
    
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Ensure appointments aren't double-invoiced
    UNIQUE(appointment_id),
    INDEX idx_invoice_items_invoice (invoice_id)
);

-- ============================================
-- Tutor Payment Tables
-- ============================================

-- Weekly tutor payments
CREATE TABLE billing_tutor_payments (
    payment_id SERIAL PRIMARY KEY,
    tutor_id INTEGER NOT NULL REFERENCES tutor_profiles(tutor_id),
    appointment_id INTEGER NOT NULL REFERENCES appointment_appointments(appointment_id),
    
    -- Payment amounts
    service_amount DECIMAL(10,2) NOT NULL,
    transport_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    bonus_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_amount DECIMAL(10,2) NOT NULL,
    
    -- Payment cycle (Thursday-Wednesday)
    payment_week_start DATE NOT NULL,
    payment_week_end DATE NOT NULL,
    
    -- Payment status
    status payment_status NOT NULL DEFAULT 'pending',
    payment_date TIMESTAMP,
    payment_batch_id INTEGER REFERENCES billing_payment_batches(batch_id),
    
    -- Stripe payout tracking
    stripe_payout_id VARCHAR(255),
    stripe_payout_status VARCHAR(50),
    
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Prevent duplicate payments for same appointment
    UNIQUE(appointment_id),
    INDEX idx_tutor_payments_tutor (tutor_id),
    INDEX idx_tutor_payments_week (payment_week_start),
    INDEX idx_tutor_payments_status (status)
);

-- Payment batches for bulk approval
CREATE TABLE billing_payment_batches (
    batch_id SERIAL PRIMARY KEY,
    batch_number VARCHAR(50) UNIQUE NOT NULL,
    week_start DATE NOT NULL,
    week_end DATE NOT NULL,
    
    -- Batch totals
    total_payments INTEGER NOT NULL DEFAULT 0,
    total_amount DECIMAL(12,2) NOT NULL DEFAULT 0.00,
    
    -- Approval workflow
    status VARCHAR(50) NOT NULL DEFAULT 'pending', -- 'pending', 'approved', 'processing', 'completed', 'failed'
    approved_by INTEGER REFERENCES user_accounts(user_id),
    approved_at TIMESTAMP,
    
    -- Processing details
    processed_at TIMESTAMP,
    processed_count INTEGER DEFAULT 0,
    failed_count INTEGER DEFAULT 0,
    
    notes TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_payment_batches_week (week_start),
    INDEX idx_payment_batches_status (status)
);

-- ============================================
-- Scheduled Task Tracking
-- ============================================

-- Track batch job executions
CREATE TABLE billing_scheduled_tasks (
    task_id SERIAL PRIMARY KEY,
    task_name VARCHAR(100) NOT NULL, -- 'daily_invoice_generation', 'weekly_payment_calculation'
    task_type VARCHAR(50) NOT NULL, -- 'invoice', 'payment'
    
    -- Execution details
    started_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    status VARCHAR(50) NOT NULL DEFAULT 'running', -- 'running', 'completed', 'failed'
    
    -- Task results
    records_processed INTEGER DEFAULT 0,
    records_created INTEGER DEFAULT 0,
    records_failed INTEGER DEFAULT 0,
    
    -- Error tracking
    error_message TEXT,
    error_details JSONB,
    
    -- Task parameters
    parameters JSONB, -- Store date ranges, filters, etc.
    
    INDEX idx_scheduled_tasks_name (task_name),
    INDEX idx_scheduled_tasks_started (started_at)
);

-- ============================================
-- Functions and Triggers
-- ============================================

-- Function to generate invoice numbers
CREATE OR REPLACE FUNCTION generate_invoice_number()
RETURNS TEXT AS $$
DECLARE
    new_number TEXT;
    year_month TEXT;
    sequence_num INTEGER;
BEGIN
    year_month := TO_CHAR(CURRENT_DATE, 'YYYYMM');
    
    -- Get the next sequence number for this month
    SELECT COALESCE(MAX(CAST(SUBSTRING(invoice_number FROM 8) AS INTEGER)), 0) + 1
    INTO sequence_num
    FROM billing_invoices
    WHERE invoice_number LIKE 'INV' || year_month || '%';
    
    new_number := 'INV' || year_month || LPAD(sequence_num::TEXT, 4, '0');
    
    RETURN new_number;
END;
$$ LANGUAGE plpgsql;

-- Function to generate payment batch numbers
CREATE OR REPLACE FUNCTION generate_batch_number()
RETURNS TEXT AS $$
DECLARE
    new_number TEXT;
    year_week TEXT;
    sequence_num INTEGER;
BEGIN
    year_week := TO_CHAR(CURRENT_DATE, 'YYYY-IW');
    
    -- Get the next sequence number for this week
    SELECT COALESCE(MAX(CAST(SUBSTRING(batch_number FROM 9) AS INTEGER)), 0) + 1
    INTO sequence_num
    FROM billing_payment_batches
    WHERE batch_number LIKE 'PAY' || year_week || '%';
    
    new_number := 'PAY' || year_week || '-' || LPAD(sequence_num::TEXT, 3, '0');
    
    RETURN new_number;
END;
$$ LANGUAGE plpgsql;

-- Trigger to set invoice number
CREATE OR REPLACE FUNCTION set_invoice_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.invoice_number IS NULL THEN
        NEW.invoice_number := generate_invoice_number();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER tr_set_invoice_number
    BEFORE INSERT ON billing_invoices
    FOR EACH ROW
    EXECUTE FUNCTION set_invoice_number();

-- Trigger to set batch number
CREATE OR REPLACE FUNCTION set_batch_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.batch_number IS NULL THEN
        NEW.batch_number := generate_batch_number();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER tr_set_batch_number
    BEFORE INSERT ON billing_payment_batches
    FOR EACH ROW
    EXECUTE FUNCTION set_batch_number();

-- Trigger to update invoice totals
CREATE OR REPLACE FUNCTION update_invoice_totals()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE billing_invoices
    SET subtotal = (
            SELECT COALESCE(SUM(amount), 0)
            FROM billing_invoice_items
            WHERE invoice_id = NEW.invoice_id
        ),
        total_amount = subtotal + tax_amount,
        updated_at = CURRENT_TIMESTAMP
    WHERE invoice_id = NEW.invoice_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER tr_update_invoice_totals
    AFTER INSERT OR UPDATE OR DELETE ON billing_invoice_items
    FOR EACH ROW
    EXECUTE FUNCTION update_invoice_totals();

-- Trigger to update payment batch totals
CREATE OR REPLACE FUNCTION update_batch_totals()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE billing_payment_batches
    SET total_payments = (
            SELECT COUNT(*)
            FROM billing_tutor_payments
            WHERE payment_batch_id = NEW.payment_batch_id
        ),
        total_amount = (
            SELECT COALESCE(SUM(total_amount), 0)
            FROM billing_tutor_payments
            WHERE payment_batch_id = NEW.payment_batch_id
        ),
        updated_at = CURRENT_TIMESTAMP
    WHERE batch_id = NEW.payment_batch_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER tr_update_batch_totals
    AFTER INSERT OR UPDATE ON billing_tutor_payments
    FOR EACH ROW
    WHEN (NEW.payment_batch_id IS NOT NULL)
    EXECUTE FUNCTION update_batch_totals();

-- ============================================
-- Indexes for Performance
-- ============================================

-- Composite indexes for common queries
CREATE INDEX idx_invoices_unpaid_by_client 
    ON billing_invoices(client_id, status) 
    WHERE status IN ('pending', 'processing');

CREATE INDEX idx_payments_pending_by_week 
    ON billing_tutor_payments(payment_week_start, status) 
    WHERE status = 'pending';

CREATE INDEX idx_appointments_unbilled 
    ON appointment_appointments(appointment_id, status, completed_at)
    WHERE status = 'completed' 
    AND appointment_id NOT IN (
        SELECT DISTINCT appointment_id 
        FROM billing_invoice_items 
        WHERE appointment_id IS NOT NULL
    );

-- ============================================
-- Initial Data
-- ============================================

-- Insert common task types
INSERT INTO billing_scheduled_tasks (task_name, task_type, status, completed_at, records_processed)
VALUES 
    ('daily_invoice_generation', 'invoice', 'completed', CURRENT_TIMESTAMP, 0),
    ('weekly_payment_calculation', 'payment', 'completed', CURRENT_TIMESTAMP, 0);

-- ============================================
-- Comments for Documentation
-- ============================================

COMMENT ON TABLE billing_invoices IS 'Client invoices generated daily for completed appointments';
COMMENT ON TABLE billing_invoice_items IS 'Line items for each invoice, linked to appointments';
COMMENT ON TABLE billing_tutor_payments IS 'Weekly tutor payments calculated Thursday-Wednesday';
COMMENT ON TABLE billing_payment_batches IS 'Batch processing for manager approval of weekly payments';
COMMENT ON TABLE billing_scheduled_tasks IS 'Audit trail for automated billing processes';

COMMENT ON COLUMN billing_invoices.paid_by_client_id IS 'Tracks which parent paid in separated family scenarios';
COMMENT ON COLUMN billing_tutor_payments.payment_week_start IS 'Thursday of the payment week';
COMMENT ON COLUMN billing_tutor_payments.payment_week_end IS 'Wednesday of the payment week';