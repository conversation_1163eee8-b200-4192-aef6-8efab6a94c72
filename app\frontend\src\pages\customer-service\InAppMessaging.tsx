/**
 * In-App Messaging System
 * 
 * Real-time messaging interface for customers and agents to communicate
 * directly through the web application with WebSocket support.
 */

import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Typography,
  TextField,
  Button,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Badge,
  Divider,
  Paper,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  Grid,
  Fab,
  CircularProgress,
  Alert,
  useTheme,
  useMediaQuery,
  InputAdornment,
  Drawer
} from '@mui/material';
import {
  Send as SendIcon,
  AttachFile as AttachIcon,
  EmojiEmotions as EmojiIcon,
  MoreVert as MoreVertIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Refresh as RefreshIcon,
  Close as CloseIcon,
  Add as AddIcon,
  VideoCall as VideoCallIcon,
  Phone as PhoneIcon,
  Info as InfoIcon,
  Star as StarIcon,
  Archive as ArchiveIcon,
  Reply as ReplyIcon,
  Forward as ForwardIcon,
  Block as BlockIcon,
  CheckCircle as DeliveredIcon,
  Schedule as PendingIcon,
  Error as FailedIcon
} from '@mui/icons-material';
import { formatDistanceToNow, format } from 'date-fns';
import { fr } from 'date-fns/locale';

import api from '../../services/api';
import { useTranslation } from '../../hooks/useTranslation';
import { useFormatting } from '../../hooks/useFormatting';
import { useWebSocket } from '../../hooks/useWebSocket';
import { useNotifications } from '../../hooks/useNotifications';
import { useAuth } from '../../hooks/useAuth';

// Types
interface Conversation {
  conversation_id: number;
  thread_id: string;
  participant_user_id?: number;
  participant_name?: string;
  participant_email?: string;
  participant_phone?: string;
  assigned_agent_id?: number;
  conversation_type: 'in_app' | 'sms' | 'email';
  subject?: string;
  status: 'open' | 'assigned' | 'resolved' | 'closed' | 'escalated';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  category?: string;
  last_activity_at: string;
  created_at: string;
  unread_count?: number;
  last_message?: string;
  agent_name?: string;
}

interface Message {
  message_id: number;
  conversation_id: number;
  message_content: string;
  message_direction: 'inbound' | 'outbound';
  sender_user_id?: number;
  sender_agent_id?: number;
  sender_name?: string;
  sender_role?: string;
  message_type: 'text' | 'image' | 'file' | 'system';
  delivery_status: 'pending' | 'sent' | 'delivered' | 'read' | 'failed';
  sent_at: string;
  read_at?: string;
  attachments?: any[];
  rich_content?: any;
  internal_note: boolean;
  system_generated: boolean;
}

interface ConversationParticipant {
  user_id: number;
  name: string;
  email?: string;
  phone?: string;
  avatar_url?: string;
  is_online: boolean;
  last_seen?: string;
  role: 'customer' | 'agent' | 'manager';
}

// Main Component
const InAppMessaging: React.FC = () => {
  const { t } = useTranslation();
  const { formatDate, formatTime } = useFormatting();
  const { showNotification } = useNotifications();
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // State
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [newMessage, setNewMessage] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('active');
  const [isTyping, setIsTyping] = useState(false);
  const [typingUsers, setTypingUsers] = useState<string[]>([]);
  const [showConversationInfo, setShowConversationInfo] = useState(false);
  const [newConversationDialog, setNewConversationDialog] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [mobileDrawerOpen, setMobileDrawerOpen] = useState(false);

  // WebSocket for real-time updates
  const { isConnected, sendMessage: sendWebSocketMessage } = useWebSocket({
    onMessage: (data) => {
      if (data.type === 'new_message') {
        queryClient.invalidateQueries(['conversations']);
        if (selectedConversation && data.conversation_id === selectedConversation.conversation_id) {
          queryClient.invalidateQueries(['conversation', selectedConversation.conversation_id]);
        }
        
        // Show notification for new messages
        if (data.message_direction === 'inbound' && data.conversation_id !== selectedConversation?.conversation_id) {
          showNotification(`New message from ${data.sender_name}`, 'info');
        }
      } else if (data.type === 'typing_indicator') {
        if (data.conversation_id === selectedConversation?.conversation_id) {
          if (data.is_typing) {
            setTypingUsers(prev => [...prev.filter(u => u !== data.user_name), data.user_name]);
          } else {
            setTypingUsers(prev => prev.filter(u => u !== data.user_name));
          }
        }
      } else if (data.type === 'conversation_update') {
        queryClient.invalidateQueries(['conversations']);
      }
    }
  });

  // Queries
  const { data: conversations = [], isLoading: conversationsLoading, refetch: refetchConversations } = useQuery({
    queryKey: ['conversations', statusFilter, searchTerm],
    queryFn: async () => {
      const params = new URLSearchParams();
      if (statusFilter === 'active') {
        params.append('status', 'open');
        params.append('status', 'assigned');
      } else if (statusFilter !== 'all') {
        params.append('status', statusFilter);
      }
      
      // Only show in-app conversations for this interface
      params.append('type', 'in_app');
      
      if (searchTerm) {
        return api.get(`/customer-service/search?q=${encodeURIComponent(searchTerm)}&${params}`);
      }
      return api.get(`/customer-service/conversations?${params}`);
    },
    refetchInterval: 30000 // Refresh every 30 seconds
  });

  const { data: messages = [], isLoading: messagesLoading } = useQuery({
    queryKey: ['conversation', selectedConversation?.conversation_id],
    queryFn: () => api.get(`/customer-service/conversations/${selectedConversation?.conversation_id}/messages?include_internal=false`),
    enabled: !!selectedConversation?.conversation_id,
    refetchInterval: 5000 // Refresh messages every 5 seconds
  });

  const { data: participants = [] } = useQuery({
    queryKey: ['conversation-participants', selectedConversation?.conversation_id],
    queryFn: () => api.get(`/customer-service/conversations/${selectedConversation?.conversation_id}/participants`),
    enabled: !!selectedConversation?.conversation_id
  });

  // Mutations
  const sendMessageMutation = useMutation({
    mutationFn: (data: { conversation_id: number; message_content: string; attachments?: File[] }) =>
      api.post(`/customer-service/conversations/${data.conversation_id}/messages`, {
        message_content: data.message_content,
        message_type: data.attachments?.length ? 'file' : 'text',
        attachments: data.attachments
      }),
    onSuccess: () => {
      setNewMessage('');
      setSelectedFiles([]);
      queryClient.invalidateQueries(['conversation', selectedConversation?.conversation_id]);
      queryClient.invalidateQueries(['conversations']);
      scrollToBottom();
    },
    onError: (error) => {
      showNotification('Failed to send message', 'error');
    }
  });

  const createConversationMutation = useMutation({
    mutationFn: (data: { participant_email?: string; subject: string; initial_message: string }) =>
      api.post('/customer-service/conversations', {
        ...data,
        conversation_type: 'in_app',
        priority: 'normal'
      }),
    onSuccess: (conversation) => {
      setNewConversationDialog(false);
      setSelectedConversation(conversation);
      queryClient.invalidateQueries(['conversations']);
    }
  });

  const markAsReadMutation = useMutation({
    mutationFn: (conversation_id: number) =>
      api.post(`/customer-service/conversations/${conversation_id}/mark-read`),
    onSuccess: () => {
      queryClient.invalidateQueries(['conversations']);
    }
  });

  // Handlers
  const handleSendMessage = useCallback(() => {
    if (!selectedConversation || (!newMessage.trim() && selectedFiles.length === 0)) return;

    sendMessageMutation.mutate({
      conversation_id: selectedConversation.conversation_id,
      message_content: newMessage.trim(),
      attachments: selectedFiles
    });
  }, [selectedConversation, newMessage, selectedFiles, sendMessageMutation]);

  const handleConversationSelect = useCallback((conversation: Conversation) => {
    setSelectedConversation(conversation);
    if (isMobile) {
      setMobileDrawerOpen(false);
    }
    
    // Mark as read if there are unread messages
    if (conversation.unread_count && conversation.unread_count > 0) {
      markAsReadMutation.mutate(conversation.conversation_id);
    }
  }, [isMobile, markAsReadMutation]);

  const handleTyping = useCallback((typing: boolean) => {
    if (!selectedConversation) return;
    
    setIsTyping(typing);
    sendWebSocketMessage({
      type: 'typing_indicator',
      conversation_id: selectedConversation.conversation_id,
      user_name: user?.name || 'User',
      is_typing: typing
    });
  }, [selectedConversation, sendWebSocketMessage, user]);

  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setSelectedFiles(prev => [...prev, ...files]);
  }, []);

  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  // Effects
  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  useEffect(() => {
    let typingTimer: NodeJS.Timeout;
    if (newMessage) {
      handleTyping(true);
      typingTimer = setTimeout(() => handleTyping(false), 1000);
    } else {
      handleTyping(false);
    }
    
    return () => clearTimeout(typingTimer);
  }, [newMessage, handleTyping]);

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'delivered': return 'success';
      case 'read': return 'success';
      case 'failed': return 'error';
      default: return 'default';
    }
  };

  // Get delivery icon
  const getDeliveryIcon = (status: string) => {
    switch (status) {
      case 'delivered': return <DeliveredIcon fontSize="small" />;
      case 'read': return <DeliveredIcon fontSize="small" />;
      case 'failed': return <FailedIcon fontSize="small" />;
      default: return <PendingIcon fontSize="small" />;
    }
  };

  // Render conversation list
  const renderConversationList = () => (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <CardHeader
        title={t('messaging.conversations')}
        action={
          <Box sx={{ display: 'flex', gap: 1 }}>
            <IconButton onClick={() => refetchConversations()} size="small">
              <RefreshIcon />
            </IconButton>
            <IconButton onClick={() => setNewConversationDialog(true)} size="small">
              <AddIcon />
            </IconButton>
          </Box>
        }
      />
      
      <CardContent sx={{ pb: 1 }}>
        {/* Search */}
        <TextField
          fullWidth
          size="small"
          placeholder={t('messaging.search_conversations')}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            )
          }}
          sx={{ mb: 2 }}
        />
        
        {/* Status Filter */}
        <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
          {['active', 'resolved', 'closed', 'all'].map((status) => (
            <Chip
              key={status}
              label={t(`messaging.status.${status}`)}
              color={statusFilter === status ? 'primary' : 'default'}
              onClick={() => setStatusFilter(status)}
              size="small"
            />
          ))}
        </Box>
      </CardContent>

      <Divider />

      {/* Conversation List */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        {conversationsLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : conversations.length === 0 ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography color="text.secondary">
              {t('messaging.no_conversations')}
            </Typography>
          </Box>
        ) : (
          <List sx={{ p: 0 }}>
            {conversations.map((conversation: Conversation, index: number) => (
              <React.Fragment key={conversation.conversation_id}>
                <ListItem
                  button
                  selected={selectedConversation?.conversation_id === conversation.conversation_id}
                  onClick={() => handleConversationSelect(conversation)}
                  sx={{ py: 2 }}
                >
                  <ListItemAvatar>
                    <Badge
                      badgeContent={conversation.unread_count}
                      color="error"
                      invisible={!conversation.unread_count}
                    >
                      <Avatar sx={{ bgcolor: 'primary.main' }}>
                        {(conversation.participant_name || conversation.participant_email || 'U')[0].toUpperCase()}
                      </Avatar>
                    </Badge>
                  </ListItemAvatar>

                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="subtitle2" sx={{ flex: 1 }} noWrap>
                          {conversation.participant_name || 
                           conversation.participant_email ||
                           t('messaging.unknown_participant')}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {formatDistanceToNow(new Date(conversation.last_activity_at), { addSuffix: true })}
                        </Typography>
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary" noWrap sx={{ mb: 0.5 }}>
                          {conversation.subject || conversation.last_message}
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Chip
                            size="small"
                            label={t(`messaging.status.${conversation.status}`)}
                            color={conversation.status === 'resolved' ? 'success' : 'default'}
                            variant="outlined"
                          />
                          {conversation.priority !== 'normal' && (
                            <Chip
                              size="small"
                              label={t(`messaging.priority.${conversation.priority}`)}
                              color={conversation.priority === 'urgent' ? 'error' : 'warning'}
                              variant="outlined"
                            />
                          )}
                        </Box>
                      </Box>
                    }
                  />
                </ListItem>
                {index < conversations.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        )}
      </Box>
    </Card>
  );

  // Render conversation detail
  const renderConversationDetail = () => {
    if (!selectedConversation) {
      return (
        <Card sx={{ height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <Typography color="text.secondary">
            {t('messaging.select_conversation')}
          </Typography>
        </Card>
      );
    }

    return (
      <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Conversation Header */}
        <CardHeader
          avatar={
            <Avatar sx={{ bgcolor: 'primary.main' }}>
              {(selectedConversation.participant_name || selectedConversation.participant_email || 'U')[0].toUpperCase()}
            </Avatar>
          }
          title={
            <Typography variant="h6">
              {selectedConversation.participant_name || 
               selectedConversation.participant_email ||
               t('messaging.unknown_participant')}
            </Typography>
          }
          subheader={
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
              <Chip
                size="small"
                label={t(`messaging.status.${selectedConversation.status}`)}
                color={selectedConversation.status === 'resolved' ? 'success' : 'primary'}
              />
              {selectedConversation.agent_name && (
                <Chip
                  size="small"
                  label={`${t('messaging.agent')}: ${selectedConversation.agent_name}`}
                  variant="outlined"
                />
              )}
            </Box>
          }
          action={
            <Box sx={{ display: 'flex', gap: 1 }}>
              <IconButton onClick={() => setShowConversationInfo(true)} size="small">
                <InfoIcon />
              </IconButton>
              <IconButton size="small">
                <MoreVertIcon />
              </IconButton>
            </Box>
          }
        />

        <Divider />

        {/* Messages */}
        <Box sx={{ flex: 1, overflow: 'auto', p: 1 }}>
          {messagesLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <List sx={{ p: 0 }}>
              {messages.map((message: Message) => (
                <ListItem
                  key={message.message_id}
                  sx={{
                    flexDirection: 'column',
                    alignItems: message.message_direction === 'outbound' ? 'flex-end' : 'flex-start',
                    py: 1
                  }}
                >
                  <Paper
                    sx={{
                      p: 2,
                      maxWidth: '70%',
                      bgcolor: message.message_direction === 'outbound' 
                        ? 'primary.main' 
                        : 'grey.100',
                      color: message.message_direction === 'outbound' ? 'primary.contrastText' : 'text.primary',
                      borderRadius: 2
                    }}
                  >
                    <Typography variant="body2">{message.message_content}</Typography>
                    
                    {message.attachments && message.attachments.length > 0 && (
                      <Box sx={{ mt: 1 }}>
                        {message.attachments.map((attachment, index) => (
                          <Chip
                            key={index}
                            label={attachment.filename}
                            size="small"
                            icon={<AttachIcon />}
                            sx={{ mr: 1 }}
                          />
                        ))}
                      </Box>
                    )}
                  </Paper>
                  
                  <Box sx={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    gap: 0.5, 
                    mt: 0.5,
                    alignSelf: message.message_direction === 'outbound' ? 'flex-end' : 'flex-start'
                  }}>
                    <Typography variant="caption" color="text.secondary">
                      {message.sender_name && `${message.sender_name} • `}
                      {formatTime(new Date(message.sent_at))}
                    </Typography>
                    {message.message_direction === 'outbound' && (
                      <Tooltip title={t(`messaging.delivery.${message.delivery_status}`)}>
                        <Box sx={{ color: getStatusColor(message.delivery_status) }}>
                          {getDeliveryIcon(message.delivery_status)}
                        </Box>
                      </Tooltip>
                    )}
                  </Box>
                </ListItem>
              ))}
              
              {/* Typing Indicator */}
              {typingUsers.length > 0 && (
                <ListItem sx={{ justifyContent: 'flex-start' }}>
                  <Paper sx={{ p: 1, bgcolor: 'grey.100', borderRadius: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      {typingUsers.join(', ')} {typingUsers.length === 1 ? 'is' : 'are'} typing...
                    </Typography>
                  </Paper>
                </ListItem>
              )}
              
              <div ref={messagesEndRef} />
            </List>
          )}
        </Box>

        <Divider />

        {/* Message Input */}
        <CardContent sx={{ pt: 1 }}>
          {selectedFiles.length > 0 && (
            <Box sx={{ mb: 1 }}>
              {selectedFiles.map((file, index) => (
                <Chip
                  key={index}
                  label={file.name}
                  onDelete={() => setSelectedFiles(prev => prev.filter((_, i) => i !== index))}
                  size="small"
                  sx={{ mr: 1, mb: 1 }}
                />
              ))}
            </Box>
          )}
          
          <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>
            <TextField
              fullWidth
              multiline
              maxRows={4}
              placeholder={t('messaging.type_message')}
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSendMessage();
                }
              }}
              disabled={selectedConversation.status === 'closed'}
            />
            
            <input
              accept="*/*"
              style={{ display: 'none' }}
              id="file-upload"
              multiple
              type="file"
              onChange={handleFileSelect}
            />
            <label htmlFor="file-upload">
              <IconButton component="span" disabled={selectedConversation.status === 'closed'}>
                <AttachIcon />
              </IconButton>
            </label>
            
            <IconButton disabled={selectedConversation.status === 'closed'}>
              <EmojiIcon />
            </IconButton>
            
            <Button
              variant="contained"
              startIcon={<SendIcon />}
              onClick={handleSendMessage}
              disabled={(!newMessage.trim() && selectedFiles.length === 0) || selectedConversation.status === 'closed'}
            >
              {t('common.send')}
            </Button>
          </Box>
        </CardContent>
      </Card>
    );
  };

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column', p: 3 }}>
      <Typography variant="h4" gutterBottom>
        {t('messaging.in_app_messaging')}
      </Typography>

      {isMobile ? (
        // Mobile Layout
        <>
          <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
            {!selectedConversation ? (
              <Button
                variant="outlined"
                onClick={() => setMobileDrawerOpen(true)}
                sx={{ mb: 2 }}
              >
                {t('messaging.select_conversation')}
              </Button>
            ) : (
              <Button
                variant="outlined"
                onClick={() => setMobileDrawerOpen(true)}
                sx={{ mb: 2 }}
              >
                {selectedConversation.participant_name || t('messaging.unknown_participant')}
              </Button>
            )}
            {renderConversationDetail()}
          </Box>
          
          <Drawer
            anchor="left"
            open={mobileDrawerOpen}
            onClose={() => setMobileDrawerOpen(false)}
            PaperProps={{ sx: { width: '80%' } }}
          >
            {renderConversationList()}
          </Drawer>
        </>
      ) : (
        // Desktop Layout
        <Grid container spacing={3} sx={{ flex: 1 }}>
          <Grid item xs={12} md={4}>
            {renderConversationList()}
          </Grid>
          <Grid item xs={12} md={8}>
            {renderConversationDetail()}
          </Grid>
        </Grid>
      )}

      {/* New Conversation Dialog */}
      <Dialog open={newConversationDialog} onClose={() => setNewConversationDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>{t('messaging.new_conversation')}</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label={t('messaging.participant_email')}
            type="email"
            sx={{ mt: 2, mb: 2 }}
          />
          <TextField
            fullWidth
            label={t('messaging.subject')}
            sx={{ mb: 2 }}
          />
          <TextField
            fullWidth
            multiline
            rows={4}
            label={t('messaging.initial_message')}
            placeholder={t('messaging.type_your_message')}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setNewConversationDialog(false)}>{t('common.cancel')}</Button>
          <Button variant="contained">{t('messaging.start_conversation')}</Button>
        </DialogActions>
      </Dialog>

      {/* Connection Status */}
      {!isConnected && (
        <Alert severity="warning" sx={{ position: 'fixed', bottom: 16, right: 16, zIndex: 1000 }}>
          {t('messaging.connection_lost')}
        </Alert>
      )}
    </Box>
  );
};

export default InAppMessaging;