"""
Role-based authorization decorators and middleware for TutorAide.
"""

import functools
import logging
from typing import List, Optional, Union, Callable, Any, Dict
from fastapi import HTTPException, status, Request, Depends
from fastapi.responses import J<PERSON>NResponse
from starlette.middleware.base import BaseHTTPMiddleware
import asyncpg

from app.core.dependencies import get_current_active_user, get_database
from app.core.security import verify_token
from app.models.user_models import User
from app.models.base import UserRoleType
from app.core.exceptions import AuthorizationError, AuthenticationError

logger = logging.getLogger(__name__)


# Public endpoints that don't require authentication
PUBLIC_ENDPOINTS = {
    ("POST", "/api/v1/auth/register"),
    ("POST", "/api/v1/auth/login"),
    ("POST", "/api/v1/auth/login/json"),
    ("POST", "/api/v1/auth/refresh"),
    ("POST", "/api/v1/auth/password/reset-request"),
    ("POST", "/api/v1/auth/password/reset-confirm"),
    ("POST", "/api/v1/auth/password/reset-request-sync"),  # Temporary for debugging
    ("POST", "/api/v1/auth/test-email-direct"),  # Temporary for debugging
    ("GET", "/api/v1/auth/debug-logs"),  # Temporary for debugging
    ("GET", "/api/v1/auth/google/login"),
    ("GET", "/api/v1/auth/google/callback"),
    ("GET", "/api/v1/auth/test-background-task"),  # Temporary for debugging
    ("GET", "/api/v1/auth/"),
    ("GET", "/api/v1/"),
    ("GET", "/"),
    ("GET", "/docs"),
    ("GET", "/redoc"),
    ("GET", "/openapi.json"),
    ("GET", "/api/v1/health"),
    # Services endpoints (public for browsing)
    ("GET", "/api/v1/services/catalog"),
    ("GET", "/api/v1/services/packages"),
    ("GET", "/api/v1/services/"),
    # Debug endpoints (temporary for testing)
    ("GET", "/api/v1/debug/services/public"),
    ("GET", "/api/v1/debug/services/raw"),
    ("GET", "/api/v1/debug/users/raw"),
    ("GET", "/api/v1/debug/profiles/raw"),
}


def require_role(required_role: UserRoleType) -> Callable:
    """
    Decorator that requires user to have a specific role.
    
    Args:
        required_role: The role required to access the endpoint
    
    Example:
        @require_role(UserRoleType.MANAGER)
        async def admin_endpoint(current_user: User = Depends(get_current_active_user)):
            return {"message": "Admin only content"}
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract current_user from kwargs
            current_user = kwargs.get('current_user')
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            # Check if user has required role
            if required_role not in current_user.roles:
                logger.warning(
                    f"User {current_user.user_id} attempted to access "
                    f"endpoint requiring {required_role.value} role"
                )
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Insufficient permissions. {required_role.value} role required."
                )
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


def require_any_role(required_roles: List[UserRoleType]) -> Callable:
    """
    Decorator that requires user to have at least one of the specified roles.
    
    Args:
        required_roles: List of roles, user must have at least one
    
    Example:
        @require_any_role([UserRoleType.MANAGER, UserRoleType.TUTOR])
        async def staff_endpoint(current_user: User = Depends(get_current_active_user)):
            return {"message": "Staff content"}
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract current_user from kwargs
            current_user = kwargs.get('current_user')
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            # Check if user has any of the required roles
            user_roles = set(current_user.roles)
            required_roles_set = set(required_roles)
            
            if not user_roles.intersection(required_roles_set):
                logger.warning(
                    f"User {current_user.user_id} attempted to access "
                    f"endpoint requiring one of {[r.value for r in required_roles]} roles"
                )
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Insufficient permissions. One of {[r.value for r in required_roles]} roles required."
                )
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


def require_all_roles(required_roles: List[UserRoleType]) -> Callable:
    """
    Decorator that requires user to have all specified roles.
    
    Args:
        required_roles: List of roles, user must have all
    
    Example:
        @require_all_roles([UserRoleType.MANAGER, UserRoleType.TUTOR])
        async def special_endpoint(current_user: User = Depends(get_current_active_user)):
            return {"message": "Special access content"}
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract current_user from kwargs
            current_user = kwargs.get('current_user')
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            # Check if user has all required roles
            user_roles = set(current_user.roles)
            required_roles_set = set(required_roles)
            
            if not required_roles_set.issubset(user_roles):
                missing_roles = required_roles_set - user_roles
                logger.warning(
                    f"User {current_user.user_id} attempted to access "
                    f"endpoint requiring all of {[r.value for r in required_roles]} roles"
                )
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Insufficient permissions. Missing roles: {[r.value for r in missing_roles]}"
                )
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


def check_resource_ownership(
    table_name: str,
    resource_id_param: str,
    owner_field: str = "user_id",
    allow_roles: Optional[List[UserRoleType]] = None
) -> Callable:
    """
    Decorator that checks if the current user owns the requested resource.
    
    Args:
        table_name: Database table containing the resource
        resource_id_param: Parameter name containing the resource ID
        owner_field: Field in table containing owner's user_id
        allow_roles: Roles that can bypass ownership check (e.g., MANAGER)
    
    Example:
        @check_resource_ownership("appointment_sessions", "appointment_id", allow_roles=[UserRoleType.MANAGER])
        async def update_appointment(
            appointment_id: int,
            current_user: User = Depends(get_current_active_user),
            db: asyncpg.Connection = Depends(get_database)
        ):
            return {"message": "Appointment updated"}
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract required parameters
            current_user = kwargs.get('current_user')
            db = kwargs.get('db')
            resource_id = kwargs.get(resource_id_param)
            
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            if not db:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Database connection required"
                )
            
            if resource_id is None:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Resource ID '{resource_id_param}' is required"
                )
            
            # Check if user has a role that bypasses ownership
            if allow_roles:
                user_roles = set(current_user.roles)
                allowed_roles_set = set(allow_roles)
                if user_roles.intersection(allowed_roles_set):
                    # User has bypass role, allow access
                    return await func(*args, **kwargs)
            
            # Check resource ownership
            query = f"""
                SELECT {owner_field} 
                FROM {table_name} 
                WHERE {resource_id_param} = $1 
                AND deleted_at IS NULL
            """
            
            try:
                owner_id = await db.fetchval(query, resource_id)
                
                if owner_id is None:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=f"Resource not found"
                    )
                
                if owner_id != current_user.user_id:
                    logger.warning(
                        f"User {current_user.user_id} attempted to access "
                        f"{table_name}.{resource_id_param}={resource_id} owned by user {owner_id}"
                    )
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="You do not have permission to access this resource"
                    )
                
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Error checking resource ownership: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Error checking resource permissions"
                )
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


class RoleChecker:
    """
    Dependency class for role-based authorization in FastAPI.
    
    Example:
        @router.get("/admin", dependencies=[Depends(RoleChecker(UserRoleType.MANAGER))])
        async def admin_endpoint():
            return {"message": "Admin content"}
    """
    
    def __init__(
        self, 
        allowed_roles: Union[UserRoleType, List[UserRoleType]],
        require_all: bool = False
    ):
        """
        Initialize role checker.
        
        Args:
            allowed_roles: Single role or list of allowed roles
            require_all: If True, user must have all roles. If False, any role is sufficient.
        """
        self.allowed_roles = allowed_roles if isinstance(allowed_roles, list) else [allowed_roles]
        self.require_all = require_all
    
    def __call__(self, current_user: User = Depends(get_current_active_user)) -> User:
        """
        Check if current user has required roles.
        
        Args:
            current_user: The authenticated user
            
        Returns:
            The user if authorized
            
        Raises:
            HTTPException: If user lacks required roles
        """
        user_roles = set(current_user.roles)
        required_roles = set(self.allowed_roles)
        
        if self.require_all:
            # User must have all specified roles
            if not required_roles.issubset(user_roles):
                missing_roles = required_roles - user_roles
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Missing required roles: {[r.value for r in missing_roles]}"
                )
        else:
            # User must have at least one of the specified roles
            if not user_roles.intersection(required_roles):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Requires one of: {[r.value for r in self.allowed_roles]}"
                )
        
        return current_user


class ResourceOwnershipChecker:
    """
    Dependency class for checking resource ownership in FastAPI.
    
    Example:
        @router.put(
            "/appointments/{appointment_id}",
            dependencies=[Depends(ResourceOwnershipChecker("appointment_sessions", "appointment_id"))]
        )
        async def update_appointment(appointment_id: int):
            return {"message": "Updated"}
    """
    
    def __init__(
        self,
        table_name: str,
        resource_id_param: str,
        owner_field: str = "user_id",
        allow_roles: Optional[List[UserRoleType]] = None
    ):
        """
        Initialize resource ownership checker.
        
        Args:
            table_name: Database table containing the resource
            resource_id_param: Parameter name containing the resource ID
            owner_field: Field in table containing owner's user_id
            allow_roles: Roles that can bypass ownership check
        """
        self.table_name = table_name
        self.resource_id_param = resource_id_param
        self.owner_field = owner_field
        self.allow_roles = allow_roles or []
    
    async def __call__(
        self,
        request: Request,
        current_user: User = Depends(get_current_active_user),
        db: asyncpg.Connection = Depends(get_database)
    ) -> User:
        """
        Check if current user owns the requested resource.
        
        Args:
            request: FastAPI request object
            current_user: The authenticated user
            db: Database connection
            
        Returns:
            The user if authorized
            
        Raises:
            HTTPException: If user doesn't own the resource
        """
        # Get resource ID from path parameters
        resource_id = request.path_params.get(self.resource_id_param)
        
        if resource_id is None:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Resource ID '{self.resource_id_param}' is required"
            )
        
        # Check bypass roles
        if self.allow_roles:
            user_roles = set(current_user.roles)
            allowed_roles = set(self.allow_roles)
            if user_roles.intersection(allowed_roles):
                return current_user
        
        # Check ownership
        query = f"""
            SELECT {self.owner_field}
            FROM {self.table_name}
            WHERE {self.resource_id_param} = $1
            AND deleted_at IS NULL
        """
        
        try:
            owner_id = await db.fetchval(query, resource_id)
            
            if owner_id is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Resource not found"
                )
            
            if owner_id != current_user.user_id:
                logger.warning(
                    f"User {current_user.user_id} attempted to access "
                    f"{self.table_name}.{self.resource_id_param}={resource_id} owned by {owner_id}"
                )
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="You do not have permission to access this resource"
                )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error checking resource ownership: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error checking resource permissions"
            )
        
        return current_user


class AuthorizationMiddleware(BaseHTTPMiddleware):
    """
    Middleware for handling authorization across all endpoints.
    
    This middleware:
    1. Checks if endpoint requires authentication
    2. Validates JWT token if present
    3. Allows public endpoints through
    4. Returns 401 for protected endpoints without valid token
    """
    
    async def dispatch(self, request: Request, call_next: Callable) -> Any:
        """
        Process the request and check authorization.

        Args:
            request: The incoming request
            call_next: The next middleware or endpoint

        Returns:
            Response from the endpoint or error response
        """
        # Always allow OPTIONS requests (CORS preflight)
        if request.method == "OPTIONS":
            return await call_next(request)

        # Check if this is a public endpoint
        endpoint = (request.method, request.url.path)

        # Check exact match and prefix match for public endpoints
        is_public = endpoint in PUBLIC_ENDPOINTS or any(
            request.url.path.startswith(path)
            for method, path in PUBLIC_ENDPOINTS
            if method == request.method and path.endswith("/")
        )

        if is_public:
            # Public endpoint, pass through
            return await call_next(request)
        
        # Protected endpoint, check authorization
        auth_header = request.headers.get("authorization", "")
        
        if not auth_header.startswith("Bearer "):
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"detail": "Missing or invalid authorization header"}
            )
        
        token = auth_header.split(" ")[1]
        
        try:
            # Verify token
            payload = verify_token(token)
            if not payload:
                return JSONResponse(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    content={"detail": "Invalid or expired token"}
                )
            
            # Token is valid, continue
            response = await call_next(request)
            return response
            
        except Exception as e:
            logger.error(f"Authorization error: {e}")
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"detail": "Authentication failed"}
            )