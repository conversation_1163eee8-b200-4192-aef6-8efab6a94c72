"""
Localization constants and configuration for TutorAide Quebec market.
"""

from typing import Dict, List, Set
from enum import Enum

# Supported languages (Quebec-focused)
SUPPORTED_LANGUAGES: List[str] = ["en", "fr"]
DEFAULT_LANGUAGE: str = "en"
FALLBACK_LANGUAGE: str = "en"

# Quebec market language configuration
class LanguageCode(Enum):
    ENGLISH = "en"
    FRENCH_QUEBEC = "fr"

# Language names for display (Quebec-specific French)
LANGUAGE_NAMES: Dict[str, Dict[str, str]] = {
    "en": {
        "en": "English",
        "fr": "French (Quebec)"
    },
    "fr": {
        "en": "Anglais", 
        "fr": "Français (Québec)"
    }
}

# Language metadata
LANGUAGE_METADATA: Dict[str, Dict[str, str]] = {
    "en": {
        "name": "English",
        "native_name": "English",
        "locale": "en_CA",
        "region": "Canada"
    },
    "fr": {
        "name": "French",
        "native_name": "Français",
        "locale": "fr_CA",
        "region": "Quebec"
    }
}

# Language direction (for future RTL support if needed)
LANGUAGE_DIRECTION: Dict[str, str] = {
    "en": "ltr",
    "fr": "ltr"
}

# Date format patterns by language
DATE_FORMATS: Dict[str, Dict[str, str]] = {
    "en": {
        "short": "%m/%d/%Y",
        "medium": "%b %d, %Y",
        "long": "%B %d, %Y",
        "full": "%A, %B %d, %Y"
    },
    "fr": {
        "short": "%d/%m/%Y",
        "medium": "%d %b %Y",
        "long": "%d %B %Y",
        "full": "%A %d %B %Y"
    }
}

# Time format patterns by language
TIME_FORMATS: Dict[str, Dict[str, str]] = {
    "en": {
        "short": "%I:%M %p",
        "medium": "%I:%M:%S %p",
        "long": "%I:%M:%S %p %Z"
    },
    "fr": {
        "short": "%H:%M",
        "medium": "%H:%M:%S",
        "long": "%H:%M:%S %Z"
    }
}

# Currency formatting (CAD - Quebec market)
CURRENCY_FORMATS: Dict[str, Dict[str, str]] = {
    "en": {
        "symbol": "$",
        "code": "CAD",
        "format": "${amount}",
        "decimal_separator": ".",
        "thousands_separator": ",",
        "precision": 2
    },
    "fr": {
        "symbol": "$",
        "code": "CAD", 
        "format": "{amount} $",
        "decimal_separator": ",",
        "thousands_separator": " ",
        "precision": 2
    }
}

# Number formatting (Quebec standards)
NUMBER_FORMATS: Dict[str, Dict[str, str]] = {
    "en": {
        "decimal_separator": ".",
        "thousands_separator": ",",
        "grouping": [3, 3]
    },
    "fr": {
        "decimal_separator": ",",
        "thousands_separator": " ",
        "grouping": [3, 3]
    }
}

# Pluralization rules (basic for English/French)
PLURALIZATION_RULES: Dict[str, Dict[str, str]] = {
    "en": {
        "zero": "other",
        "one": "one", 
        "other": "other"
    },
    "fr": {
        "zero": "one",
        "one": "one",
        "other": "other"
    }
}

# Translation validation rules
TRANSLATION_VALIDATION: Dict[str, any] = {
    "required_placeholders": True,
    "max_length_variance": 0.5,  # 50% variance allowed
    "forbidden_html": ["<script>", "<iframe>", "<object>"],
    "required_keys": [
        "common.yes", "common.no", "common.save", "common.cancel",
        "auth.login.title", "errors.required_field", "success.saved"
    ]
}

# Template variable patterns
TEMPLATE_PATTERNS: Dict[str, str] = {
    "variable": r"\{([a-zA-Z_][a-zA-Z0-9_]*)\}",
    "function": r"\{([a-zA-Z_][a-zA-Z0-9_]*)\((.*?)\)\}",
    "conditional": r"\{%\s*(if|elif|else|endif)\s*(.*?)\s*%\}",
    "plural": r"\{%\s*plural\s+(\w+)\s*%\}(.*?)\{%\s*endplural\s*%\}"
}

# Content categories for organization
CONTENT_CATEGORIES: Set[str] = {
    "common", "navigation", "auth", "users", "clients", "tutors", 
    "appointments", "billing", "messages", "reports", "settings",
    "tecfee", "notifications", "email", "errors", "success"
}

# Quebec-specific formatting preferences
QUEBEC_FORMATTING: Dict[str, any] = {
    "phone_format": "(XXX) XXX-XXXX",
    "postal_code_format": "XXX XXX",
    "address_format": "{street}\n{city}, {province} {postal_code}",
    "tax_display": "separate_lines",  # Show GST and QST separately
    "date_input_format": "YYYY-MM-DD"
}