"""
Timezone utilities for TutorAide application.
All timestamps are in Eastern Standard Time (EST).
"""

from datetime import datetime, timezone, timedelta
from typing import Optional

# EST timezone (UTC-5)
EST = timezone(timedelta(hours=-5))

# EDT timezone (UTC-4) for daylight saving time
EDT = timezone(timedelta(hours=-4))


def get_est_timezone() -> timezone:
    """
    Get the current EST/EDT timezone.
    For simplicity, we'll use EST year-round.
    In production, you might want to handle daylight saving time.
    """
    return EST


def now_est() -> datetime:
    """Get current datetime in EST."""
    return datetime.now(EST)


def utc_to_est(dt: datetime) -> datetime:
    """Convert UTC datetime to EST."""
    if dt.tzinfo is None:
        # Assume naive datetime is UTC
        dt = dt.replace(tzinfo=timezone.utc)
    return dt.astimezone(EST)


def est_to_utc(dt: datetime) -> datetime:
    """Convert EST datetime to UTC."""
    if dt.tzinfo is None:
        # Assume naive datetime is EST
        dt = dt.replace(tzinfo=EST)
    return dt.astimezone(timezone.utc)


def format_est_datetime(dt: Optional[datetime] = None) -> str:
    """
    Format datetime in EST with timezone indicator.
    If no datetime provided, uses current EST time.
    """
    if dt is None:
        dt = now_est()
    elif dt.tzinfo is None:
        # Assume naive datetime is EST
        dt = dt.replace(tzinfo=EST)
    elif dt.tzinfo != EST:
        # Convert to EST if in different timezone
        dt = dt.astimezone(EST)
    
    return dt.strftime("%Y-%m-%d %H:%M:%S EST")