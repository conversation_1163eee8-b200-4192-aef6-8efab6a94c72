#!/bin/bash

echo "Fixing all import paths..."

# Fix all components imports
find src/components -name "*.tsx" -o -name "*.ts" | while read file; do
  # Fix Button imports
  sed -i "s|import { Button } from '\(.*\)/Button'|import Button from '\1/Button'|g" "$file"
  
  # Fix LoadingSpinner imports
  sed -i "s|import { LoadingSpinner } from '\(.*\)/LoadingSpinner'|import LoadingSpinner from '../ui/LoadingSpinner'|g" "$file"
  sed -i "s|import LoadingSpinner from '\(.*\)/LoadingSpinner'|import LoadingSpinner from '../ui/LoadingSpinner'|g" "$file"
  
  # Fix all "../../../" to be appropriate depth
  depth=$(echo "$file" | tr '/' '\n' | grep -c .)
  if [ $depth -eq 3 ]; then
    # src/components/file.tsx - use ../
    sed -i "s|from '../../../|from '../|g" "$file"
  elif [ $depth -eq 4 ]; then
    # src/components/category/file.tsx - use ../../
    sed -i "s|from '../../../|from '../../|g" "$file"
  elif [ $depth -eq 5 ]; then
    # src/components/category/subcategory/file.tsx - use ../../../
    : # Keep as is
  fi
done

# Fix pages imports
find src/pages -name "*.tsx" -o -name "*.ts" | while read file; do
  depth=$(echo "$file" | tr '/' '\n' | grep -c .)
  if [ $depth -eq 3 ]; then
    # src/pages/file.tsx - use ../
    sed -i "s|from '../../../|from '../|g" "$file"
  elif [ $depth -eq 4 ]; then
    # src/pages/category/file.tsx - use ../../
    sed -i "s|from '../../../|from '../../|g" "$file"
  fi
done

# Fix hooks imports
find src/hooks -name "*.tsx" -o -name "*.ts" | while read file; do
  sed -i "s|from '../../../|from '../|g" "$file"
done

# Fix utils imports
find src/utils -name "*.tsx" -o -name "*.ts" | while read file; do
  sed -i "s|from '../../../|from '../|g" "$file"
done

echo "Import paths fixed!"