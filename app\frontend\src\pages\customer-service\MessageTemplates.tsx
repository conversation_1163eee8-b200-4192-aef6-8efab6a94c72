/**
 * Message Templates and Quick Responses Management
 * 
 * Comprehensive system for managing reusable message templates and quick response
 * options for customer service agents with categorization and variable support.
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Typography,
  TextField,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  ListItemIcon,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Paper,
  IconButton,
  Tooltip,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Switch,
  FormControlLabel,
  Checkbox,
  FormGroup,
  Tabs,
  Tab,
  Badge,
  Avatar,
  useTheme,
  useMediaQuery,
  Autocomplete,
  Snackbar
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Copy as CopyIcon,
  Visibility as ViewIcon,
  ExpandMore as ExpandMoreIcon,
  Search as SearchIcon,
  Filter as FilterIcon,
  Category as CategoryIcon,
  Language as LanguageIcon,
  Star as StarIcon,
  Speed as SpeedIcon,
  Message as MessageIcon,
  Template as TemplateIcon,
  Bookmark as BookmarkIcon,
  Preview as PreviewIcon,
  Save as SaveIcon,
  Close as CloseIcon,
  Refresh as RefreshIcon,
  ImportExport as ImportExportIcon
} from '@mui/icons-material';
import { formatDistanceToNow } from 'date-fns';

import api from '../../services/api';
import { useTranslation } from '../../hooks/useTranslation';
import { useNotifications } from '../../hooks/useNotifications';

// Types
interface MessageTemplate {
  template_id: number;
  template_name: string;
  template_key: string;
  category: string;
  subject?: string;
  message_content: string;
  variables: Record<string, any>;
  language: string;
  channel?: string;
  department: string;
  usage_count: number;
  last_used_at?: string;
  approved_by?: number;
  approved_at?: string;
  status: 'draft' | 'pending' | 'approved' | 'rejected';
  created_by: number;
  created_at: string;
  updated_at: string;
  version: number;
}

interface QuickResponse {
  quick_response_id: number;
  response_text: string;
  trigger_keywords: string[];
  category: string;
  language: string;
  usage_count: number;
  created_by: number;
  created_at: string;
  is_active: boolean;
}

interface TemplateCategory {
  category: string;
  count: number;
  description?: string;
}

interface TemplateVariable {
  name: string;
  description: string;
  example: string;
  required: boolean;
}

// Main Component
const MessageTemplates: React.FC = () => {
  const { t } = useTranslation();
  const { showNotification } = useNotifications();
  const queryClient = useQueryClient();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // State
  const [currentTab, setCurrentTab] = useState(0);
  const [templateDialogOpen, setTemplateDialogOpen] = useState(false);
  const [quickResponseDialogOpen, setQuickResponseDialogOpen] = useState(false);
  const [previewDialogOpen, setPreviewDialogOpen] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<MessageTemplate | null>(null);
  const [editingQuickResponse, setEditingQuickResponse] = useState<QuickResponse | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedLanguage, setSelectedLanguage] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [previewTemplate, setPreviewTemplate] = useState<MessageTemplate | null>(null);
  const [previewVariables, setPreviewVariables] = useState<Record<string, string>>({});

  // Template form state
  const [templateForm, setTemplateForm] = useState({
    template_name: '',
    template_key: '',
    category: 'general',
    subject: '',
    message_content: '',
    variables: {} as Record<string, any>,
    language: 'en',
    channel: '',
    department: 'general'
  });

  // Quick response form state
  const [quickResponseForm, setQuickResponseForm] = useState({
    response_text: '',
    trigger_keywords: [] as string[],
    category: 'general',
    language: 'en'
  });

  const [keywordInput, setKeywordInput] = useState('');

  // Queries
  const { data: templates = [], isLoading: templatesLoading, refetch: refetchTemplates } = useQuery({
    queryKey: ['message-templates', selectedCategory, selectedLanguage, searchTerm],
    queryFn: async () => {
      const params = new URLSearchParams();
      if (selectedCategory !== 'all') params.append('category', selectedCategory);
      if (selectedLanguage !== 'all') params.append('language', selectedLanguage);
      if (searchTerm) params.append('search', searchTerm);
      return api.get(`/customer-service/templates?${params}`);
    }
  });

  const { data: quickResponses = [], isLoading: quickResponsesLoading, refetch: refetchQuickResponses } = useQuery({
    queryKey: ['quick-responses', selectedCategory, selectedLanguage],
    queryFn: async () => {
      const params = new URLSearchParams();
      if (selectedCategory !== 'all') params.append('category', selectedCategory);
      if (selectedLanguage !== 'all') params.append('language', selectedLanguage);
      return api.get(`/customer-service/quick-responses?${params}`);
    }
  });

  const { data: categories = [] } = useQuery({
    queryKey: ['template-categories'],
    queryFn: () => api.get('/customer-service/template-categories')
  });

  const { data: templateVariables = [] } = useQuery({
    queryKey: ['template-variables'],
    queryFn: () => api.get('/customer-service/template-variables')
  });

  // Mutations
  const createTemplateMutation = useMutation({
    mutationFn: (data: any) => api.post('/customer-service/templates', data),
    onSuccess: () => {
      setTemplateDialogOpen(false);
      resetTemplateForm();
      queryClient.invalidateQueries(['message-templates']);
      showNotification('Template created successfully', 'success');
    }
  });

  const updateTemplateMutation = useMutation({
    mutationFn: ({ id, ...data }: any) => api.put(`/customer-service/templates/${id}`, data),
    onSuccess: () => {
      setEditingTemplate(null);
      setTemplateDialogOpen(false);
      queryClient.invalidateQueries(['message-templates']);
      showNotification('Template updated successfully', 'success');
    }
  });

  const deleteTemplateMutation = useMutation({
    mutationFn: (id: number) => api.delete(`/customer-service/templates/${id}`),
    onSuccess: () => {
      queryClient.invalidateQueries(['message-templates']);
      showNotification('Template deleted successfully', 'success');
    }
  });

  const createQuickResponseMutation = useMutation({
    mutationFn: (data: any) => api.post('/customer-service/quick-responses', data),
    onSuccess: () => {
      setQuickResponseDialogOpen(false);
      resetQuickResponseForm();
      queryClient.invalidateQueries(['quick-responses']);
      showNotification('Quick response created successfully', 'success');
    }
  });

  const updateQuickResponseMutation = useMutation({
    mutationFn: ({ id, ...data }: any) => api.put(`/customer-service/quick-responses/${id}`, data),
    onSuccess: () => {
      setEditingQuickResponse(null);
      setQuickResponseDialogOpen(false);
      queryClient.invalidateQueries(['quick-responses']);
      showNotification('Quick response updated successfully', 'success');
    }
  });

  const deleteQuickResponseMutation = useMutation({
    mutationFn: (id: number) => api.delete(`/customer-service/quick-responses/${id}`),
    onSuccess: () => {
      queryClient.invalidateQueries(['quick-responses']);
      showNotification('Quick response deleted successfully', 'success');
    }
  });

  // Handlers
  const resetTemplateForm = useCallback(() => {
    setTemplateForm({
      template_name: '',
      template_key: '',
      category: 'general',
      subject: '',
      message_content: '',
      variables: {},
      language: 'en',
      channel: '',
      department: 'general'
    });
  }, []);

  const resetQuickResponseForm = useCallback(() => {
    setQuickResponseForm({
      response_text: '',
      trigger_keywords: [],
      category: 'general',
      language: 'en'
    });
    setKeywordInput('');
  }, []);

  const handleCreateTemplate = useCallback(() => {
    if (!templateForm.template_name.trim() || !templateForm.message_content.trim()) {
      showNotification('Please fill in all required fields', 'error');
      return;
    }

    const data = {
      ...templateForm,
      template_key: templateForm.template_key || generateTemplateKey(templateForm.template_name)
    };

    if (editingTemplate) {
      updateTemplateMutation.mutate({ id: editingTemplate.template_id, ...data });
    } else {
      createTemplateMutation.mutate(data);
    }
  }, [templateForm, editingTemplate, createTemplateMutation, updateTemplateMutation, showNotification]);

  const handleCreateQuickResponse = useCallback(() => {
    if (!quickResponseForm.response_text.trim()) {
      showNotification('Please fill in the response text', 'error');
      return;
    }

    if (editingQuickResponse) {
      updateQuickResponseMutation.mutate({ id: editingQuickResponse.quick_response_id, ...quickResponseForm });
    } else {
      createQuickResponseMutation.mutate(quickResponseForm);
    }
  }, [quickResponseForm, editingQuickResponse, createQuickResponseMutation, updateQuickResponseMutation, showNotification]);

  const handleEditTemplate = useCallback((template: MessageTemplate) => {
    setEditingTemplate(template);
    setTemplateForm({
      template_name: template.template_name,
      template_key: template.template_key,
      category: template.category,
      subject: template.subject || '',
      message_content: template.message_content,
      variables: template.variables,
      language: template.language,
      channel: template.channel || '',
      department: template.department
    });
    setTemplateDialogOpen(true);
  }, []);

  const handleEditQuickResponse = useCallback((quickResponse: QuickResponse) => {
    setEditingQuickResponse(quickResponse);
    setQuickResponseForm({
      response_text: quickResponse.response_text,
      trigger_keywords: quickResponse.trigger_keywords,
      category: quickResponse.category,
      language: quickResponse.language
    });
    setQuickResponseDialogOpen(true);
  }, []);

  const handlePreviewTemplate = useCallback((template: MessageTemplate) => {
    setPreviewTemplate(template);
    setPreviewVariables({});
    setPreviewDialogOpen(true);
  }, []);

  const handleCopyTemplate = useCallback((template: MessageTemplate) => {
    setTemplateForm({
      template_name: `${template.template_name} (Copy)`,
      template_key: '',
      category: template.category,
      subject: template.subject || '',
      message_content: template.message_content,
      variables: template.variables,
      language: template.language,
      channel: template.channel || '',
      department: template.department
    });
    setEditingTemplate(null);
    setTemplateDialogOpen(true);
  }, []);

  const handleAddKeyword = useCallback(() => {
    if (keywordInput.trim() && !quickResponseForm.trigger_keywords.includes(keywordInput.trim())) {
      setQuickResponseForm(prev => ({
        ...prev,
        trigger_keywords: [...prev.trigger_keywords, keywordInput.trim()]
      }));
      setKeywordInput('');
    }
  }, [keywordInput, quickResponseForm.trigger_keywords]);

  const handleRemoveKeyword = useCallback((keyword: string) => {
    setQuickResponseForm(prev => ({
      ...prev,
      trigger_keywords: prev.trigger_keywords.filter(k => k !== keyword)
    }));
  }, []);

  const generateTemplateKey = (name: string) => {
    return name.toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '_')
      .substring(0, 50);
  };

  const detectVariablesInContent = (content: string) => {
    const variableRegex = /\{([^}]+)\}/g;
    const matches = [];
    let match;
    while ((match = variableRegex.exec(content)) !== null) {
      matches.push(match[1]);
    }
    return [...new Set(matches)];
  };

  const renderPreviewContent = () => {
    if (!previewTemplate) return '';
    
    let content = previewTemplate.message_content;
    Object.keys(previewVariables).forEach(variable => {
      const value = previewVariables[variable] || `{${variable}}`;
      content = content.replace(new RegExp(`\\{${variable}\\}`, 'g'), value);
    });
    return content;
  };

  // Render template dialog
  const renderTemplateDialog = () => (
    <Dialog
      open={templateDialogOpen}
      onClose={() => setTemplateDialogOpen(false)}
      maxWidth="lg"
      fullWidth
      fullScreen={isMobile}
    >
      <DialogTitle>
        {editingTemplate ? 'Edit Template' : 'Create New Template'}
      </DialogTitle>
      
      <DialogContent>
        <Grid container spacing={3} sx={{ mt: 1 }}>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={t('templates.template_name')}
              value={templateForm.template_name}
              onChange={(e) => setTemplateForm(prev => ({ ...prev, template_name: e.target.value }))}
              required
              sx={{ mb: 2 }}
            />
          </Grid>
          
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label={t('templates.template_key')}
              value={templateForm.template_key}
              onChange={(e) => setTemplateForm(prev => ({ ...prev, template_key: e.target.value }))}
              helperText={t('templates.key_helper')}
              sx={{ mb: 2 }}
            />
          </Grid>
          
          <Grid item xs={12} md={4}>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>{t('templates.category')}</InputLabel>
              <Select
                value={templateForm.category}
                onChange={(e) => setTemplateForm(prev => ({ ...prev, category: e.target.value }))}
                label={t('templates.category')}
              >
                <MenuItem value="general">General</MenuItem>
                <MenuItem value="greeting">Greeting</MenuItem>
                <MenuItem value="closing">Closing</MenuItem>
                <MenuItem value="escalation">Escalation</MenuItem>
                <MenuItem value="billing">Billing</MenuItem>
                <MenuItem value="technical">Technical</MenuItem>
                <MenuItem value="appointment">Appointment</MenuItem>
                <MenuItem value="broadcast">Broadcast</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>{t('templates.language')}</InputLabel>
              <Select
                value={templateForm.language}
                onChange={(e) => setTemplateForm(prev => ({ ...prev, language: e.target.value }))}
                label={t('templates.language')}
              >
                <MenuItem value="en">English</MenuItem>
                <MenuItem value="fr">Français</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>{t('templates.department')}</InputLabel>
              <Select
                value={templateForm.department}
                onChange={(e) => setTemplateForm(prev => ({ ...prev, department: e.target.value }))}
                label={t('templates.department')}
              >
                <MenuItem value="general">General</MenuItem>
                <MenuItem value="support">Support</MenuItem>
                <MenuItem value="billing">Billing</MenuItem>
                <MenuItem value="sales">Sales</MenuItem>
                <MenuItem value="technical">Technical</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12}>
            <TextField
              fullWidth
              label={t('templates.subject')}
              value={templateForm.subject}
              onChange={(e) => setTemplateForm(prev => ({ ...prev, subject: e.target.value }))}
              helperText={t('templates.subject_helper')}
              sx={{ mb: 2 }}
            />
          </Grid>
          
          <Grid item xs={12}>
            <TextField
              fullWidth
              multiline
              rows={8}
              label={t('templates.message_content')}
              value={templateForm.message_content}
              onChange={(e) => setTemplateForm(prev => ({ ...prev, message_content: e.target.value }))}
              helperText={t('templates.content_helper')}
              required
              sx={{ mb: 2 }}
            />
          </Grid>
          
          {/* Variable detection and management */}
          <Grid item xs={12}>
            <Paper sx={{ p: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                {t('templates.detected_variables')}
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {detectVariablesInContent(templateForm.message_content).map((variable) => (
                  <Chip
                    key={variable}
                    label={variable}
                    size="small"
                    color="primary"
                    variant="outlined"
                  />
                ))}
              </Box>
            </Paper>
          </Grid>
          
          {/* Available variables reference */}
          <Grid item xs={12}>
            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="subtitle2">{t('templates.available_variables')}</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <List dense>
                  {templateVariables.map((variable: TemplateVariable, index: number) => (
                    <ListItem key={index}>
                      <ListItemText
                        primary={`{${variable.name}}`}
                        secondary={
                          <Box>
                            <Typography variant="caption" display="block">
                              {variable.description}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              Example: {variable.example}
                            </Typography>
                          </Box>
                        }
                      />
                      <IconButton
                        size="small"
                        onClick={() => {
                          const cursorPos = document.getElementById('message-content')?.selectionStart || 0;
                          const newContent = templateForm.message_content.slice(0, cursorPos) + 
                                           `{${variable.name}}` + 
                                           templateForm.message_content.slice(cursorPos);
                          setTemplateForm(prev => ({ ...prev, message_content: newContent }));
                        }}
                      >
                        <AddIcon />
                      </IconButton>
                    </ListItem>
                  ))}
                </List>
              </AccordionDetails>
            </Accordion>
          </Grid>
        </Grid>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={() => setTemplateDialogOpen(false)}>
          {t('common.cancel')}
        </Button>
        <Button
          variant="contained"
          onClick={handleCreateTemplate}
          disabled={createTemplateMutation.isLoading || updateTemplateMutation.isLoading}
        >
          {editingTemplate ? t('common.update') : t('common.create')}
        </Button>
      </DialogActions>
    </Dialog>
  );

  // Render quick response dialog
  const renderQuickResponseDialog = () => (
    <Dialog
      open={quickResponseDialogOpen}
      onClose={() => setQuickResponseDialogOpen(false)}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>
        {editingQuickResponse ? 'Edit Quick Response' : 'Create New Quick Response'}
      </DialogTitle>
      
      <DialogContent>
        <Grid container spacing={3} sx={{ mt: 1 }}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              multiline
              rows={4}
              label={t('quick_responses.response_text')}
              value={quickResponseForm.response_text}
              onChange={(e) => setQuickResponseForm(prev => ({ ...prev, response_text: e.target.value }))}
              required
              sx={{ mb: 2 }}
            />
          </Grid>
          
          <Grid item xs={12} md={6}>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>{t('quick_responses.category')}</InputLabel>
              <Select
                value={quickResponseForm.category}
                onChange={(e) => setQuickResponseForm(prev => ({ ...prev, category: e.target.value }))}
                label={t('quick_responses.category')}
              >
                <MenuItem value="general">General</MenuItem>
                <MenuItem value="greeting">Greeting</MenuItem>
                <MenuItem value="closing">Closing</MenuItem>
                <MenuItem value="confirmation">Confirmation</MenuItem>
                <MenuItem value="apology">Apology</MenuItem>
                <MenuItem value="escalation">Escalation</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>{t('quick_responses.language')}</InputLabel>
              <Select
                value={quickResponseForm.language}
                onChange={(e) => setQuickResponseForm(prev => ({ ...prev, language: e.target.value }))}
                label={t('quick_responses.language')}
              >
                <MenuItem value="en">English</MenuItem>
                <MenuItem value="fr">Français</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12}>
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                {t('quick_responses.trigger_keywords')}
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
                <TextField
                  size="small"
                  placeholder={t('quick_responses.add_keyword')}
                  value={keywordInput}
                  onChange={(e) => setKeywordInput(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleAddKeyword()}
                />
                <Button
                  size="small"
                  variant="outlined"
                  onClick={handleAddKeyword}
                  disabled={!keywordInput.trim()}
                >
                  {t('common.add')}
                </Button>
              </Box>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {quickResponseForm.trigger_keywords.map((keyword) => (
                  <Chip
                    key={keyword}
                    label={keyword}
                    size="small"
                    onDelete={() => handleRemoveKeyword(keyword)}
                    color="primary"
                    variant="outlined"
                  />
                ))}
              </Box>
            </Box>
          </Grid>
        </Grid>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={() => setQuickResponseDialogOpen(false)}>
          {t('common.cancel')}
        </Button>
        <Button
          variant="contained"
          onClick={handleCreateQuickResponse}
          disabled={createQuickResponseMutation.isLoading || updateQuickResponseMutation.isLoading}
        >
          {editingQuickResponse ? t('common.update') : t('common.create')}
        </Button>
      </DialogActions>
    </Dialog>
  );

  // Render preview dialog
  const renderPreviewDialog = () => (
    <Dialog
      open={previewDialogOpen}
      onClose={() => setPreviewDialogOpen(false)}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>
        {t('templates.preview_template')} - {previewTemplate?.template_name}
      </DialogTitle>
      
      <DialogContent>
        {previewTemplate && (
          <Grid container spacing={3}>
            {/* Variable inputs */}
            {detectVariablesInContent(previewTemplate.message_content).map((variable) => (
              <Grid item xs={12} md={6} key={variable}>
                <TextField
                  fullWidth
                  label={variable}
                  value={previewVariables[variable] || ''}
                  onChange={(e) => setPreviewVariables(prev => ({
                    ...prev,
                    [variable]: e.target.value
                  }))}
                  size="small"
                />
              </Grid>
            ))}
            
            {/* Preview content */}
            <Grid item xs={12}>
              <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                <Typography variant="subtitle2" gutterBottom>
                  {t('templates.preview')}
                </Typography>
                {previewTemplate.subject && (
                  <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'bold' }}>
                    Subject: {previewTemplate.subject}
                  </Typography>
                )}
                <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                  {renderPreviewContent()}
                </Typography>
              </Paper>
            </Grid>
          </Grid>
        )}
      </DialogContent>
      
      <DialogActions>
        <Button onClick={() => setPreviewDialogOpen(false)}>
          {t('common.close')}
        </Button>
      </DialogActions>
    </Dialog>
  );

  // Render templates tab
  const renderTemplatesTab = () => (
    <Box>
      {/* Header and controls */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" component="h2">
          {t('templates.message_templates')}
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 2 }}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>{t('templates.category')}</InputLabel>
            <Select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              label={t('templates.category')}
            >
              <MenuItem value="all">{t('common.all')}</MenuItem>
              {categories.map((cat: TemplateCategory) => (
                <MenuItem key={cat.category} value={cat.category}>
                  {cat.category} ({cat.count})
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>{t('templates.language')}</InputLabel>
            <Select
              value={selectedLanguage}
              onChange={(e) => setSelectedLanguage(e.target.value)}
              label={t('templates.language')}
            >
              <MenuItem value="all">{t('common.all')}</MenuItem>
              <MenuItem value="en">English</MenuItem>
              <MenuItem value="fr">Français</MenuItem>
            </Select>
          </FormControl>
          
          <TextField
            size="small"
            placeholder={t('templates.search_templates')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: <SearchIcon />
            }}
          />
          
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => {
              resetTemplateForm();
              setEditingTemplate(null);
              setTemplateDialogOpen(true);
            }}
          >
            {t('templates.create_template')}
          </Button>
        </Box>
      </Box>

      {/* Templates list */}
      {templatesLoading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : templates.length === 0 ? (
        <Alert severity="info">
          {t('templates.no_templates')}
        </Alert>
      ) : (
        <Grid container spacing={3}>
          {templates.map((template: MessageTemplate) => (
            <Grid item xs={12} md={6} lg={4} key={template.template_id}>
              <Card>
                <CardHeader
                  title={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="h6" component="div" sx={{ flex: 1 }}>
                        {template.template_name}
                      </Typography>
                      <Chip
                        size="small"
                        label={template.status}
                        color={template.status === 'approved' ? 'success' : 'default'}
                        variant="outlined"
                      />
                    </Box>
                  }
                  subheader={
                    <Box>
                      <Typography variant="caption" display="block">
                        {template.category} • {template.language}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Used {template.usage_count} times
                      </Typography>
                    </Box>
                  }
                  action={
                    <Box>
                      <IconButton size="small" onClick={() => handlePreviewTemplate(template)}>
                        <PreviewIcon />
                      </IconButton>
                      <IconButton size="small" onClick={() => handleEditTemplate(template)}>
                        <EditIcon />
                      </IconButton>
                      <IconButton size="small" onClick={() => handleCopyTemplate(template)}>
                        <CopyIcon />
                      </IconButton>
                      <IconButton 
                        size="small" 
                        onClick={() => deleteTemplateMutation.mutate(template.template_id)}
                        color="error"
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Box>
                  }
                />
                <CardContent>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    {template.message_content.substring(0, 150)}
                    {template.message_content.length > 150 && '...'}
                  </Typography>
                  
                  {template.subject && (
                    <Typography variant="caption" display="block" sx={{ mb: 1 }}>
                      Subject: {template.subject}
                    </Typography>
                  )}
                  
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 2 }}>
                    <Typography variant="caption" color="text.secondary">
                      {formatDistanceToNow(new Date(template.updated_at), { addSuffix: true })}
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 0.5 }}>
                      {Object.keys(template.variables || {}).map((variable) => (
                        <Chip key={variable} size="small" label={variable} variant="outlined" />
                      ))}
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}
    </Box>
  );

  // Render quick responses tab
  const renderQuickResponsesTab = () => (
    <Box>
      {/* Header and controls */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" component="h2">
          {t('quick_responses.quick_responses')}
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 2 }}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>{t('quick_responses.category')}</InputLabel>
            <Select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              label={t('quick_responses.category')}
            >
              <MenuItem value="all">{t('common.all')}</MenuItem>
              <MenuItem value="general">General</MenuItem>
              <MenuItem value="greeting">Greeting</MenuItem>
              <MenuItem value="closing">Closing</MenuItem>
              <MenuItem value="confirmation">Confirmation</MenuItem>
              <MenuItem value="apology">Apology</MenuItem>
              <MenuItem value="escalation">Escalation</MenuItem>
            </Select>
          </FormControl>
          
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => {
              resetQuickResponseForm();
              setEditingQuickResponse(null);
              setQuickResponseDialogOpen(true);
            }}
          >
            {t('quick_responses.create_response')}
          </Button>
        </Box>
      </Box>

      {/* Quick responses list */}
      {quickResponsesLoading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : quickResponses.length === 0 ? (
        <Alert severity="info">
          {t('quick_responses.no_responses')}
        </Alert>
      ) : (
        <List>
          {quickResponses.map((response: QuickResponse, index: number) => (
            <React.Fragment key={response.quick_response_id}>
              <ListItem>
                <ListItemIcon>
                  <SpeedIcon color={response.is_active ? 'primary' : 'disabled'} />
                </ListItemIcon>
                
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="subtitle1">
                        {response.response_text.substring(0, 100)}
                        {response.response_text.length > 100 && '...'}
                      </Typography>
                      <Chip
                        size="small"
                        label={response.category}
                        variant="outlined"
                      />
                      <Chip
                        size="small"
                        label={response.language}
                        variant="outlined"
                      />
                    </Box>
                  }
                  secondary={
                    <Box sx={{ mt: 1 }}>
                      <Box sx={{ display: 'flex', gap: 0.5, mb: 1 }}>
                        {response.trigger_keywords.map((keyword) => (
                          <Chip
                            key={keyword}
                            size="small"
                            label={keyword}
                            color="primary"
                            variant="outlined"
                          />
                        ))}
                      </Box>
                      <Typography variant="caption" color="text.secondary">
                        Used {response.usage_count} times • {formatDistanceToNow(new Date(response.created_at), { addSuffix: true })}
                      </Typography>
                    </Box>
                  }
                />
                
                <ListItemSecondaryAction>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <IconButton
                      size="small"
                      onClick={() => handleEditQuickResponse(response)}
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => deleteQuickResponseMutation.mutate(response.quick_response_id)}
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                </ListItemSecondaryAction>
              </ListItem>
              {index < quickResponses.length - 1 && <Divider />}
            </React.Fragment>
          ))}
        </List>
      )}
    </Box>
  );

  const tabLabels = ['templates.message_templates', 'quick_responses.quick_responses'];

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        {t('templates.templates_and_responses')}
      </Typography>

      <Tabs value={currentTab} onChange={(_, newValue) => setCurrentTab(newValue)} sx={{ mb: 3 }}>
        {tabLabels.map((label, index) => (
          <Tab key={index} label={t(label)} />
        ))}
      </Tabs>

      <Box sx={{ mt: 3 }}>
        {currentTab === 0 && renderTemplatesTab()}
        {currentTab === 1 && renderQuickResponsesTab()}
      </Box>

      {renderTemplateDialog()}
      {renderQuickResponseDialog()}
      {renderPreviewDialog()}
    </Box>
  );
};

export default MessageTemplates;