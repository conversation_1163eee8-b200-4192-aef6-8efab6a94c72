import api from './api';
import i18n from '../../../i18n';

// Language Types and Interfaces
export enum SupportedLanguage {
  ENGLISH = 'en',
  FRENCH = 'fr'
}

export interface LanguagePreference {
  user_id: number;
  preferred_language: SupportedLanguage;
  fallback_language: SupportedLanguage;
  auto_detect: boolean;
  updated_at: string;
}

export interface TranslationKey {
  key: string;
  namespace: string;
  en: string;
  fr: string;
  description?: string;
  context?: string;
}

export interface TranslationNamespace {
  namespace: string;
  description: string;
  keys: TranslationKey[];
  last_updated: string;
}

export interface LanguageStats {
  total_keys: number;
  translated_keys: {
    en: number;
    fr: number;
  };
  coverage_percentage: {
    en: number;
    fr: number;
  };
  missing_translations: {
    en: string[];
    fr: string[];
  };
}

export interface UpdateLanguageRequest {
  language: SupportedLanguage;
  auto_detect?: boolean;
}

export interface DetectedLanguage {
  language: SupportedLanguage;
  confidence: number;
  source: 'browser' | 'location' | 'user_history';
}

class LanguageService {
  /**
   * Get current language preference
   */
  async getLanguagePreference(): Promise<LanguagePreference> {
    try {
      const response = await api.get<LanguagePreference>('/language/preference');
      return response.data;
    } catch (error: any) {
      console.error('Error fetching language preference:', error);
      // Return default if error
      return {
        user_id: 0,
        preferred_language: SupportedLanguage.ENGLISH,
        fallback_language: SupportedLanguage.ENGLISH,
        auto_detect: true,
        updated_at: new Date().toISOString()
      };
    }
  }

  /**
   * Update language preference
   */
  async updateLanguagePreference(request: UpdateLanguageRequest): Promise<LanguagePreference> {
    try {
      const response = await api.put<LanguagePreference>('/language/preference', request);
      
      // Update i18n instance
      await this.applyLanguage(request.language);
      
      return response.data;
    } catch (error: any) {
      console.error('Error updating language preference:', error);
      throw new Error(error.response?.data?.detail || 'Failed to update language preference');
    }
  }

  /**
   * Get available languages
   */
  async getAvailableLanguages(): Promise<Array<{
    code: SupportedLanguage;
    name: string;
    native_name: string;
    flag: string;
    is_rtl: boolean;
  }>> {
    try {
      const response = await api.get<Array<{
        code: SupportedLanguage;
        name: string;
        native_name: string;
        flag: string;
        is_rtl: boolean;
      }>>('/language/available');
      return response.data;
    } catch (error: any) {
      // Return default languages if API fails
      return [
        {
          code: SupportedLanguage.ENGLISH,
          name: 'English',
          native_name: 'English',
          flag: '🇬🇧',
          is_rtl: false
        },
        {
          code: SupportedLanguage.FRENCH,
          name: 'French',
          native_name: 'Français',
          flag: '🇫🇷',
          is_rtl: false
        }
      ];
    }
  }

  /**
   * Detect user's preferred language
   */
  async detectLanguage(): Promise<DetectedLanguage> {
    try {
      const response = await api.get<DetectedLanguage>('/language/detect');
      return response.data;
    } catch (error: any) {
      // Fallback to browser detection
      return this.detectBrowserLanguage();
    }
  }

  /**
   * Get translation keys for a namespace
   */
  async getTranslations(namespace: string): Promise<TranslationNamespace> {
    try {
      const response = await api.get<TranslationNamespace>(`/language/translations/${namespace}`);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching translations:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch translations');
    }
  }

  /**
   * Get translation statistics
   */
  async getTranslationStats(): Promise<LanguageStats> {
    try {
      const response = await api.get<LanguageStats>('/language/stats');
      return response.data;
    } catch (error: any) {
      console.error('Error fetching translation stats:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch translation stats');
    }
  }

  /**
   * Apply language to the application
   */
  async applyLanguage(language: SupportedLanguage): Promise<void> {
    try {
      // Change i18n language
      await i18n.changeLanguage(language);
      
      // Update HTML lang attribute
      document.documentElement.lang = language;
      
      // Store in localStorage
      localStorage.setItem('preferredLanguage', language);
      
      // Update API default headers
      api.defaults.headers.common['Accept-Language'] = language;
      
      // Notify other components of language change
      window.dispatchEvent(new CustomEvent('languageChanged', { detail: { language } }));
    } catch (error) {
      console.error('Error applying language:', error);
      throw error;
    }
  }

  /**
   * Initialize language on app load
   */
  async initializeLanguage(): Promise<SupportedLanguage> {
    try {
      // Check localStorage first
      const storedLanguage = localStorage.getItem('preferredLanguage') as SupportedLanguage;
      if (storedLanguage && Object.values(SupportedLanguage).includes(storedLanguage)) {
        await this.applyLanguage(storedLanguage);
        return storedLanguage;
      }

      // Get user preference from API
      const preference = await this.getLanguagePreference();
      await this.applyLanguage(preference.preferred_language);
      return preference.preferred_language;
    } catch (error) {
      // Fallback to browser language
      const detected = this.detectBrowserLanguage();
      await this.applyLanguage(detected.language);
      return detected.language;
    }
  }

  /**
   * Detect browser language
   */
  private detectBrowserLanguage(): DetectedLanguage {
    const browserLang = navigator.language.toLowerCase();
    let detectedLang = SupportedLanguage.ENGLISH;
    let confidence = 0.5;

    if (browserLang.startsWith('fr')) {
      detectedLang = SupportedLanguage.FRENCH;
      confidence = 0.9;
    } else if (browserLang.startsWith('en')) {
      detectedLang = SupportedLanguage.ENGLISH;
      confidence = 0.9;
    }

    return {
      language: detectedLang,
      confidence,
      source: 'browser'
    };
  }

  /**
   * Get language name in its native form
   */
  getLanguageNativeName(language: SupportedLanguage): string {
    const names: Record<SupportedLanguage, string> = {
      [SupportedLanguage.ENGLISH]: 'English',
      [SupportedLanguage.FRENCH]: 'Français'
    };

    return names[language] || language;
  }

  /**
   * Get language flag emoji
   */
  getLanguageFlag(language: SupportedLanguage): string {
    const flags: Record<SupportedLanguage, string> = {
      [SupportedLanguage.ENGLISH]: '🇬🇧',
      [SupportedLanguage.FRENCH]: '🇫🇷'
    };

    return flags[language] || '🌐';
  }

  /**
   * Format date according to language
   */
  formatDate(date: Date | string, language: SupportedLanguage, format: 'short' | 'long' = 'short'): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const locale = language === SupportedLanguage.FRENCH ? 'fr-CA' : 'en-CA';

    const options: Intl.DateTimeFormatOptions = format === 'long' 
      ? { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' }
      : { year: 'numeric', month: 'short', day: 'numeric' };

    return dateObj.toLocaleDateString(locale, options);
  }

  /**
   * Format time according to language
   */
  formatTime(date: Date | string, language: SupportedLanguage, format: '12h' | '24h' = '12h'): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const locale = language === SupportedLanguage.FRENCH ? 'fr-CA' : 'en-CA';

    const options: Intl.DateTimeFormatOptions = {
      hour: 'numeric',
      minute: '2-digit',
      hour12: format === '12h'
    };

    return dateObj.toLocaleTimeString(locale, options);
  }

  /**
   * Format currency according to language
   */
  formatCurrency(amount: number, language: SupportedLanguage): string {
    const locale = language === SupportedLanguage.FRENCH ? 'fr-CA' : 'en-CA';
    
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: 'CAD'
    }).format(amount);
  }

  /**
   * Get pluralization rules for language
   */
  pluralize(count: number, key: string, language: SupportedLanguage): string {
    // This would integrate with i18n pluralization
    // For now, return with count
    return `${count} ${i18n.t(key, { count })}`;
  }

  /**
   * Subscribe to language changes
   */
  onLanguageChange(callback: (language: SupportedLanguage) => void): () => void {
    const handler = (event: Event) => {
      const customEvent = event as CustomEvent<{ language: SupportedLanguage }>;
      callback(customEvent.detail.language);
    };

    window.addEventListener('languageChanged', handler);

    // Return unsubscribe function
    return () => {
      window.removeEventListener('languageChanged', handler);
    };
  }
}

export const languageService = new LanguageService();