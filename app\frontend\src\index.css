/* Temporarily removed Google Fonts import due to CSP issues on production */
/* @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap'); */
/* Import the global styles if it exists */
@import './styles/globals.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    /* Using system fonts as fallback until CSP is fixed on production */
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    @apply bg-background-primary text-text-primary;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-background-secondary;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-border-secondary rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-text-muted;
  }

  /* Focus styles */
  *:focus {
    @apply outline-none;
  }

  *:focus-visible {
    @apply ring-2 ring-accent-red ring-offset-2 ring-offset-white;
  }

  /* Smooth transitions */
  * {
    @apply transition-colors duration-200 ease-smooth;
  }
}

@layer components {
  /* Button styles */
  .btn-primary {
    @apply px-6 py-3 bg-accent-red text-white rounded-full hover:bg-accent-red-dark transition-all font-medium shadow-soft hover:shadow-elevated;
  }

  .btn-secondary {
    @apply px-6 py-3 border border-border-primary rounded-full hover:bg-background-secondary transition-all font-medium;
  }

  .btn-danger {
    @apply px-6 py-3 bg-semantic-error text-white rounded-full hover:bg-red-700 transition-all font-medium shadow-soft hover:shadow-elevated;
  }

  /* Input styles */
  .input {
    @apply w-full px-4 py-3 border border-border-primary rounded-xl focus:outline-none focus:ring-2 focus:ring-accent-red focus:border-transparent transition-all;
  }

  /* Card styles */
  .card {
    @apply bg-white rounded-lg shadow-soft p-6;
  }

  .card-hover {
    @apply card hover:shadow-soft transition-shadow cursor-pointer;
  }

  /* Badge styles */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-success {
    @apply badge bg-semantic-success bg-opacity-10 text-semantic-success;
  }

  .badge-warning {
    @apply badge bg-semantic-warning bg-opacity-10 text-semantic-warning;
  }

  .badge-danger {
    @apply badge bg-accent-red bg-opacity-10 text-accent-red;
  }

  .badge-info {
    @apply badge bg-accent-red bg-opacity-10 text-accent-red;
  }

  /* Table styles */
  .table {
    @apply w-full divide-y divide-border-primary;
  }

  .table-header {
    @apply bg-background-secondary;
  }

  .table-row {
    @apply hover:bg-background-secondary transition-colors;
  }

  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm;
  }
}

@layer utilities {
  /* Frosted glass effect */
  .frosted-glass {
    @apply backdrop-blur-xl backdrop-saturate-150 bg-white bg-opacity-80;
  }

  /* Text truncation */
  .truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Animation delays */
  .animation-delay-100 {
    animation-delay: 100ms;
  }

  .animation-delay-200 {
    animation-delay: 200ms;
  }

  .animation-delay-300 {
    animation-delay: 300ms;
  }
}