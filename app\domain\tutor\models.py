"""
Tutor domain models for the TutorAide application.

This module contains all Pydantic models related to tutor profiles,
including documents, education, experience, specializations, and performance.
"""

from datetime import datetime, date
from typing import Optional, List, Dict, Any
from decimal import Decimal
from enum import Enum

from pydantic import BaseModel, Field, ConfigDict, field_validator, computed_field, EmailStr, HttpUrl
from app.models.base import BaseEntity, IdentifiedEntity


class DocumentType(str, Enum):
    """Document type enumeration."""
    RESUME = "resume"
    CERTIFICATION = "certification"
    DEGREE = "degree"
    TRANSCRIPT = "transcript"
    BACKGROUND_CHECK = "background_check"
    REFERENCE = "reference"
    ID_VERIFICATION = "id_verification"
    OTHER = "other"


class VerificationStatus(str, Enum):
    """Document verification status."""
    PENDING = "pending"
    VERIFIED = "verified"
    REJECTED = "rejected"


class EmploymentType(str, Enum):
    """Employment type enumeration."""
    FULL_TIME = "full_time"
    PART_TIME = "part_time"
    CONTRACT = "contract"
    INTERNSHIP = "internship"
    VOLUNTEER = "volunteer"


class TeachingLevel(str, Enum):
    """Teaching level enumeration."""
    ELEMENTARY = "elementary"
    MIDDLE_SCHOOL = "middle_school"
    HIGH_SCHOOL = "high_school"
    COLLEGE = "college"
    UNIVERSITY = "university"
    ADULT = "adult"
    ALL = "all"


class ProficiencyLevel(str, Enum):
    """Subject proficiency level."""
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"


class BackgroundCheckStatus(str, Enum):
    """Background check status."""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    EXPIRED = "expired"
    FAILED = "failed"


class TutorDocument(BaseEntity):
    """Tutor document model."""
    
    document_id: int = Field(..., description="Unique document identifier")
    tutor_id: int = Field(..., description="Associated tutor ID")
    document_type: DocumentType = Field(..., description="Type of document")
    document_name: str = Field(..., min_length=1, max_length=255, description="Document name")
    file_url: str = Field(..., max_length=500, description="Document file URL")
    file_size: Optional[int] = Field(None, ge=0, description="File size in bytes")
    mime_type: Optional[str] = Field(None, max_length=100, description="MIME type")
    uploaded_at: datetime = Field(..., description="Upload timestamp")
    uploaded_by: Optional[int] = Field(None, description="User who uploaded")
    verified_at: Optional[datetime] = Field(None, description="Verification timestamp")
    verified_by: Optional[int] = Field(None, description="User who verified")
    verification_status: VerificationStatus = Field(default=VerificationStatus.PENDING, description="Verification status")
    verification_notes: Optional[str] = Field(None, description="Verification notes")
    expiry_date: Optional[date] = Field(None, description="Document expiry date")
    is_public: bool = Field(default=False, description="Whether document is publicly visible")
    
    @computed_field
    @property
    def is_verified(self) -> bool:
        """Check if document is verified."""
        return self.verification_status == VerificationStatus.VERIFIED
    
    @computed_field
    @property
    def is_expired(self) -> bool:
        """Check if document is expired."""
        if self.expiry_date:
            return date.today() > self.expiry_date
        return False
    
    @computed_field
    @property
    def needs_renewal(self) -> bool:
        """Check if document needs renewal (within 30 days of expiry)."""
        if self.expiry_date:
            days_until_expiry = (self.expiry_date - date.today()).days
            return 0 <= days_until_expiry <= 30
        return False


class TutorEducation(BaseEntity):
    """Tutor education history model."""
    
    education_id: int = Field(..., description="Unique education identifier")
    tutor_id: int = Field(..., description="Associated tutor ID")
    institution: str = Field(..., min_length=1, max_length=200, description="Institution name")
    degree: str = Field(..., min_length=1, max_length=100, description="Degree/Certificate name")
    field_of_study: str = Field(..., min_length=1, max_length=200, description="Field of study")
    start_date: Optional[date] = Field(None, description="Start date")
    graduation_date: Optional[date] = Field(None, description="Graduation date")
    is_current: bool = Field(default=False, description="Currently enrolled")
    gpa: Optional[Decimal] = Field(None, ge=Decimal("0"), le=Decimal("4.0"), description="GPA")
    honors_awards: Optional[str] = Field(None, description="Honors and awards")
    relevant_coursework: List[str] = Field(default_factory=list, description="Relevant coursework")
    thesis_title: Optional[str] = Field(None, max_length=500, description="Thesis title")
    advisor_name: Optional[str] = Field(None, max_length=100, description="Advisor name")
    
    @field_validator('graduation_date')
    @classmethod
    def validate_dates(cls, v: Optional[date], info) -> Optional[date]:
        """Validate graduation date is after start date."""
        if v and 'start_date' in info.data and info.data['start_date']:
            if v < info.data['start_date']:
                raise ValueError("Graduation date must be after start date")
        return v
    
    @computed_field
    @property
    def duration_years(self) -> Optional[float]:
        """Calculate education duration in years."""
        if self.start_date and (self.graduation_date or self.is_current):
            end_date = self.graduation_date or date.today()
            delta = end_date - self.start_date
            return round(delta.days / 365.25, 1)
        return None


class TutorExperience(BaseEntity):
    """Tutor work experience model."""
    
    experience_id: int = Field(..., description="Unique experience identifier")
    tutor_id: int = Field(..., description="Associated tutor ID")
    organization: str = Field(..., min_length=1, max_length=200, description="Organization name")
    position: str = Field(..., min_length=1, max_length=100, description="Position title")
    employment_type: Optional[EmploymentType] = Field(None, description="Employment type")
    start_date: date = Field(..., description="Start date")
    end_date: Optional[date] = Field(None, description="End date")
    is_current: bool = Field(default=False, description="Currently employed")
    location: Optional[str] = Field(None, max_length=200, description="Work location")
    description: Optional[str] = Field(None, description="Role description")
    achievements: List[str] = Field(default_factory=list, description="Key achievements")
    skills_used: List[str] = Field(default_factory=list, description="Skills used")
    students_taught: Optional[int] = Field(None, ge=0, description="Number of students taught")
    age_groups_taught: List[str] = Field(default_factory=list, description="Age groups taught")
    subjects_taught: List[str] = Field(default_factory=list, description="Subjects taught")
    
    @field_validator('end_date')
    @classmethod
    def validate_dates(cls, v: Optional[date], info) -> Optional[date]:
        """Validate end date is after start date."""
        if v and 'start_date' in info.data:
            if v < info.data['start_date']:
                raise ValueError("End date must be after start date")
        return v
    
    @computed_field
    @property
    def duration_years(self) -> float:
        """Calculate experience duration in years."""
        end_date = self.end_date or date.today()
        delta = end_date - self.start_date
        return round(delta.days / 365.25, 1)
    
    @computed_field
    @property
    def is_teaching_experience(self) -> bool:
        """Check if this is teaching experience."""
        return bool(self.students_taught or self.subjects_taught or self.age_groups_taught)


class TutorSpecialization(BaseModel):
    """Tutor subject specialization model."""
    
    model_config = ConfigDict(from_attributes=True)
    
    specialization_id: int = Field(..., description="Unique specialization identifier")
    tutor_id: int = Field(..., description="Associated tutor ID")
    subject_area: str = Field(..., min_length=1, max_length=100, description="Subject area")
    level: TeachingLevel = Field(..., description="Teaching level")
    years_experience: int = Field(default=0, ge=0, description="Years of experience")
    certification: Optional[str] = Field(None, max_length=200, description="Relevant certification")
    certification_date: Optional[date] = Field(None, description="Certification date")
    certification_expiry: Optional[date] = Field(None, description="Certification expiry")
    proficiency_level: ProficiencyLevel = Field(default=ProficiencyLevel.INTERMEDIATE, description="Proficiency level")
    max_students_per_session: int = Field(default=1, ge=1, le=20, description="Max students per session")
    preferred_age_groups: List[str] = Field(default_factory=list, description="Preferred age groups")
    teaching_methods: List[str] = Field(default_factory=list, description="Teaching methods used")
    success_stories: Optional[str] = Field(None, description="Success stories")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    
    @computed_field
    @property
    def is_certified(self) -> bool:
        """Check if certification is valid."""
        if self.certification and self.certification_expiry:
            return date.today() <= self.certification_expiry
        return bool(self.certification)
    
    @computed_field
    @property
    def supports_group_sessions(self) -> bool:
        """Check if tutor supports group sessions."""
        return self.max_students_per_session > 1


class TutorVerificationLog(BaseModel):
    """Tutor verification log entry."""
    
    model_config = ConfigDict(from_attributes=True)
    
    log_id: int = Field(..., description="Unique log identifier")
    tutor_id: int = Field(..., description="Associated tutor ID")
    verification_step: str = Field(..., max_length=50, description="Verification step")
    previous_status: Optional[str] = Field(None, max_length=50, description="Previous status")
    new_status: str = Field(..., max_length=50, description="New status")
    verified_by: Optional[int] = Field(None, description="User who performed verification")
    verification_method: Optional[str] = Field(None, max_length=50, description="Verification method")
    notes: Optional[str] = Field(None, description="Verification notes")
    documents_reviewed: List[str] = Field(default_factory=list, description="Documents reviewed")
    issues_found: List[str] = Field(default_factory=list, description="Issues found")
    timestamp: datetime = Field(..., description="Log timestamp")


class TutorPerformanceMetrics(BaseModel):
    """Tutor performance metrics for a period."""
    
    model_config = ConfigDict(from_attributes=True)
    
    metric_id: int = Field(..., description="Unique metric identifier")
    tutor_id: int = Field(..., description="Associated tutor ID")
    period_start: date = Field(..., description="Period start date")
    period_end: date = Field(..., description="Period end date")
    
    # Session metrics
    total_sessions: int = Field(default=0, ge=0, description="Total sessions scheduled")
    completed_sessions: int = Field(default=0, ge=0, description="Sessions completed")
    cancelled_sessions: int = Field(default=0, ge=0, description="Sessions cancelled")
    no_show_sessions: int = Field(default=0, ge=0, description="No-show sessions")
    
    # Student metrics
    total_students: int = Field(default=0, ge=0, description="Total unique students")
    new_students: int = Field(default=0, ge=0, description="New students this period")
    returning_students: int = Field(default=0, ge=0, description="Returning students")
    
    # Time metrics
    total_hours: Decimal = Field(default=Decimal("0"), ge=Decimal("0"), description="Total teaching hours")
    avg_session_duration: Optional[Decimal] = Field(None, ge=Decimal("0"), description="Average session duration")
    
    # Rating metrics
    avg_rating: Optional[Decimal] = Field(None, ge=Decimal("0"), le=Decimal("5"), description="Average rating")
    total_ratings: int = Field(default=0, ge=0, description="Total ratings received")
    five_star_ratings: int = Field(default=0, ge=0, description="5-star ratings")
    four_star_ratings: int = Field(default=0, ge=0, description="4-star ratings")
    three_star_ratings: int = Field(default=0, ge=0, description="3-star ratings")
    two_star_ratings: int = Field(default=0, ge=0, description="2-star ratings")
    one_star_ratings: int = Field(default=0, ge=0, description="1-star ratings")
    
    # Performance metrics
    on_time_rate: Optional[Decimal] = Field(None, ge=Decimal("0"), le=Decimal("100"), description="On-time rate %")
    response_rate: Optional[Decimal] = Field(None, ge=Decimal("0"), le=Decimal("100"), description="Response rate %")
    response_time_hours: Optional[Decimal] = Field(None, ge=Decimal("0"), description="Avg response time")
    
    # Financial metrics
    revenue_generated: Decimal = Field(default=Decimal("0"), ge=Decimal("0"), description="Revenue generated")
    bonuses_earned: Decimal = Field(default=Decimal("0"), ge=Decimal("0"), description="Bonuses earned")
    
    # Feedback metrics
    complaints_received: int = Field(default=0, ge=0, description="Complaints received")
    compliments_received: int = Field(default=0, ge=0, description="Compliments received")
    
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    
    @field_validator('period_end')
    @classmethod
    def validate_period(cls, v: date, info) -> date:
        """Validate period end is after period start."""
        if 'period_start' in info.data:
            if v < info.data['period_start']:
                raise ValueError("Period end must be after period start")
        return v
    
    @computed_field
    @property
    def completion_rate(self) -> Decimal:
        """Calculate session completion rate."""
        if self.total_sessions > 0:
            rate = (self.completed_sessions / self.total_sessions) * 100
            return Decimal(str(round(rate, 2)))
        return Decimal("0")
    
    @computed_field
    @property
    def cancellation_rate(self) -> Decimal:
        """Calculate cancellation rate."""
        if self.total_sessions > 0:
            rate = (self.cancelled_sessions / self.total_sessions) * 100
            return Decimal(str(round(rate, 2)))
        return Decimal("0")
    
    @computed_field
    @property
    def rating_distribution(self) -> Dict[int, int]:
        """Get rating distribution."""
        return {
            5: self.five_star_ratings,
            4: self.four_star_ratings,
            3: self.three_star_ratings,
            2: self.two_star_ratings,
            1: self.one_star_ratings
        }


class TutorProfile(IdentifiedEntity):
    """Enhanced tutor profile with all relationships."""
    
    tutor_id: int = Field(..., description="Unique tutor identifier")
    user_id: int = Field(..., description="Associated user ID")
    bio: Optional[str] = Field(None, description="Tutor biography")
    phone: Optional[str] = Field(None, max_length=20, description="Contact phone")
    profile_photo_url: Optional[str] = Field(None, max_length=500, description="Profile photo URL")
    
    # Professional info
    headline: Optional[str] = Field(None, max_length=200, description="Professional headline")
    professional_summary: Optional[str] = Field(None, description="Professional summary")
    teaching_philosophy: Optional[str] = Field(None, description="Teaching philosophy")
    linkedin_url: Optional[HttpUrl] = Field(None, description="LinkedIn profile URL")
    website_url: Optional[HttpUrl] = Field(None, description="Personal website URL")
    
    # Experience summary
    years_of_experience: int = Field(default=0, ge=0, description="Years of teaching experience")
    total_students_taught: int = Field(default=0, ge=0, description="Total students taught")
    preferred_student_age_min: Optional[int] = Field(None, ge=3, le=100, description="Min student age")
    preferred_student_age_max: Optional[int] = Field(None, ge=3, le=100, description="Max student age")
    
    # Special needs
    accepts_special_needs: bool = Field(default=False, description="Accepts special needs students")
    special_needs_experience: List[str] = Field(default_factory=list, description="Special needs experience")
    
    # Languages and availability
    languages_spoken: List[str] = Field(default_factory=list, description="Languages spoken")
    availability_notes: Optional[str] = Field(None, description="Availability notes")
    
    # Verification
    verification_status: str = Field(..., max_length=50, description="Overall verification status")
    background_check_status: BackgroundCheckStatus = Field(
        default=BackgroundCheckStatus.NOT_STARTED,
        description="Background check status"
    )
    background_check_date: Optional[date] = Field(None, description="Background check date")
    background_check_expiry: Optional[date] = Field(None, description="Background check expiry")
    
    # Profile metrics
    profile_completeness: int = Field(default=0, ge=0, le=100, description="Profile completeness %")
    profile_views: int = Field(default=0, ge=0, description="Profile view count")
    last_active_date: Optional[date] = Field(None, description="Last active date")
    
    # Featured status
    is_featured: bool = Field(default=False, description="Featured tutor")
    featured_until: Optional[date] = Field(None, description="Featured until date")
    
    # Related entities
    documents: List[TutorDocument] = Field(default_factory=list, description="Tutor documents")
    education: List[TutorEducation] = Field(default_factory=list, description="Education history")
    experience: List[TutorExperience] = Field(default_factory=list, description="Work experience")
    specializations: List[TutorSpecialization] = Field(default_factory=list, description="Subject specializations")
    performance_metrics: Optional[TutorPerformanceMetrics] = Field(None, description="Current performance metrics")
    
    @computed_field
    @property
    def is_verified(self) -> bool:
        """Check if tutor is fully verified."""
        return (
            self.verification_status == "verified" and
            self.background_check_status == BackgroundCheckStatus.COMPLETED and
            (not self.background_check_expiry or self.background_check_expiry >= date.today())
        )
    
    @computed_field
    @property
    def has_complete_profile(self) -> bool:
        """Check if profile is complete."""
        return self.profile_completeness >= 80
    
    @computed_field
    @property
    def verified_documents_count(self) -> int:
        """Count verified documents."""
        return sum(1 for doc in self.documents if doc.is_verified and doc.is_active())
    
    @computed_field
    @property
    def teaching_subjects(self) -> List[str]:
        """Get all unique teaching subjects."""
        subjects = set()
        for spec in self.specializations:
            subjects.add(spec.subject_area)
        for exp in self.experience:
            subjects.update(exp.subjects_taught)
        return sorted(list(subjects))
    
    @computed_field
    @property
    def teaching_levels(self) -> List[TeachingLevel]:
        """Get all teaching levels."""
        levels = set()
        for spec in self.specializations:
            levels.add(spec.level)
        return sorted(list(levels))
    
    @computed_field
    @property
    def total_teaching_years(self) -> float:
        """Calculate total years of teaching experience."""
        total_years = 0.0
        for exp in self.experience:
            if exp.is_teaching_experience:
                total_years += exp.duration_years
        return round(total_years, 1)
    
    def calculate_completeness(self) -> int:
        """Calculate profile completeness percentage."""
        completeness = 0
        
        # Basic info (20%)
        if self.profile_photo_url:
            completeness += 10
        if self.bio and len(self.bio) > 50:
            completeness += 10
            
        # Education (15%)
        if any(e.is_active() for e in self.education):
            completeness += 15
            
        # Experience (15%)
        if any(e.is_active() for e in self.experience):
            completeness += 15
            
        # Specializations (15%)
        if self.specializations:
            completeness += 15
            
        # Documents (10%)
        if self.verified_documents_count > 0:
            completeness += 10
            
        # Availability (10%) - would check tutor_availability table
        # Rates (10%) - would check tutor_service_rates table
        # Background check (5%)
        if self.background_check_status == BackgroundCheckStatus.COMPLETED:
            completeness += 5
            
        return min(completeness, 100)


class TutorProfileCreate(BaseModel):
    """Model for creating a tutor profile."""
    
    model_config = ConfigDict(from_attributes=True)
    
    user_id: int = Field(..., description="Associated user ID")
    bio: Optional[str] = Field(None, description="Tutor biography")
    phone: Optional[str] = Field(None, max_length=20, description="Contact phone")
    headline: Optional[str] = Field(None, max_length=200, description="Professional headline")
    professional_summary: Optional[str] = Field(None, description="Professional summary")
    teaching_philosophy: Optional[str] = Field(None, description="Teaching philosophy")
    years_of_experience: int = Field(default=0, ge=0, description="Years of teaching experience")
    languages_spoken: List[str] = Field(default_factory=list, description="Languages spoken")
    accepts_special_needs: bool = Field(default=False, description="Accepts special needs students")


class TutorProfileUpdate(BaseModel):
    """Model for updating a tutor profile."""
    
    model_config = ConfigDict(from_attributes=True)
    
    bio: Optional[str] = Field(None, description="Tutor biography")
    phone: Optional[str] = Field(None, max_length=20, description="Contact phone")
    profile_photo_url: Optional[str] = Field(None, max_length=500, description="Profile photo URL")
    headline: Optional[str] = Field(None, max_length=200, description="Professional headline")
    professional_summary: Optional[str] = Field(None, description="Professional summary")
    teaching_philosophy: Optional[str] = Field(None, description="Teaching philosophy")
    linkedin_url: Optional[HttpUrl] = Field(None, description="LinkedIn profile URL")
    website_url: Optional[HttpUrl] = Field(None, description="Personal website URL")
    years_of_experience: Optional[int] = Field(None, ge=0, description="Years of teaching experience")
    total_students_taught: Optional[int] = Field(None, ge=0, description="Total students taught")
    preferred_student_age_min: Optional[int] = Field(None, ge=3, le=100, description="Min student age")
    preferred_student_age_max: Optional[int] = Field(None, ge=3, le=100, description="Max student age")
    accepts_special_needs: Optional[bool] = Field(None, description="Accepts special needs students")
    special_needs_experience: Optional[List[str]] = Field(None, description="Special needs experience")
    languages_spoken: Optional[List[str]] = Field(None, description="Languages spoken")
    availability_notes: Optional[str] = Field(None, description="Availability notes")


class TutorVerificationStatus(BaseModel):
    """Tutor verification status summary."""
    
    model_config = ConfigDict(from_attributes=True)
    
    tutor_id: int
    user_id: int
    tutor_name: str
    verification_status: str
    background_check_status: BackgroundCheckStatus
    background_check_date: Optional[date]
    profile_completeness: int
    verified_documents: int
    pending_documents: int
    education_entries: int
    experience_entries: int
    specializations: int
    last_verification_action: Optional[datetime]