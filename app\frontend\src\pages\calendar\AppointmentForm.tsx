import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';
import { Calendar, Clock, User, Users, MapPin, Video, Home, Phone } from 'lucide-react';

interface AppointmentFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (appointment: AppointmentData) => void;
  tutorId?: number;
  selectedDate?: Date;
  selectedTime?: string;
  editingAppointment?: Appointment | null;
}

interface AppointmentData {
  tutorId: number;
  clientId: number;
  dependantId?: number;
  scheduledDate: Date;
  startTime: string;
  endTime: string;
  subject: string;
  sessionType: 'individual' | 'group' | 'tecfee';
  locationType: 'online' | 'in_person' | 'library';
  locationDetails?: string;
  notes?: string;
  hourlyRate: number;
  maxParticipants?: number;
}

interface Appointment {
  id: number;
  tutorId: number;
  clientName: string;
  subject: string;
  startTime: Date;
  endTime: Date;
  status: string;
  type: string;
  location: {
    type: string;
    details?: string;
  };
  notes?: string;
}

interface Client {
  id: number;
  name: string;
  email: string;
  dependants?: Array<{
    id: number;
    name: string;
  }>;
}

interface Tutor {
  id: number;
  name: string;
  specialties: string[];
  hourlyRates: {
    individual: number;
    group: number;
    tecfee: number;
  };
}

const AppointmentForm: React.FC<AppointmentFormProps> = ({
  isOpen,
  onClose,
  onSubmit,
  tutorId,
  selectedDate,
  selectedTime,
  editingAppointment
}) => {
  const { t } = useTranslation();
  
  const [formData, setFormData] = useState<AppointmentData>({
    tutorId: tutorId || 0,
    clientId: 0,
    scheduledDate: selectedDate || new Date(),
    startTime: selectedTime || '09:00',
    endTime: '10:00',
    subject: '',
    sessionType: 'individual',
    locationType: 'online',
    hourlyRate: 50,
    notes: ''
  });

  const [clients, setClients] = useState<Client[]>([]);
  const [tutors, setTutors] = useState<Tutor[]>([]);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [conflicts, setConflicts] = useState<string[]>([]);

  // Mock data - replace with actual API calls
  useEffect(() => {
    const mockClients: Client[] = [
      {
        id: 1,
        name: 'Emma Johnson',
        email: '<EMAIL>',
        dependants: [
          { id: 1, name: 'Alice Johnson' },
          { id: 2, name: 'Bob Johnson' }
        ]
      },
      {
        id: 2,
        name: 'Lucas Martin',
        email: '<EMAIL>',
        dependants: [
          { id: 3, name: 'Sophie Martin' }
        ]
      },
      {
        id: 3,
        name: 'David Wilson',
        email: '<EMAIL>',
        dependants: []
      }
    ];

    const mockTutors: Tutor[] = [
      {
        id: 1,
        name: 'Marie Dubois',
        specialties: ['Mathematics', 'Physics', 'Chemistry'],
        hourlyRates: { individual: 55, group: 45, tecfee: 60 }
      },
      {
        id: 2,
        name: 'John Smith',
        specialties: ['English', 'French', 'Literature'],
        hourlyRates: { individual: 50, group: 40, tecfee: 55 }
      },
      {
        id: 3,
        name: 'Sophie Martin',
        specialties: ['Science', 'Biology', 'Chemistry'],
        hourlyRates: { individual: 52, group: 42, tecfee: 58 }
      }
    ];

    setClients(mockClients);
    setTutors(mockTutors);
  }, []);

  // Initialize form data when editing
  useEffect(() => {
    if (editingAppointment) {
      setFormData({
        tutorId: editingAppointment.tutorId,
        clientId: 1, // Would get from appointment data
        scheduledDate: new Date(editingAppointment.startTime),
        startTime: format(editingAppointment.startTime, 'HH:mm'),
        endTime: format(editingAppointment.endTime, 'HH:mm'),
        subject: editingAppointment.subject,
        sessionType: editingAppointment.type as any,
        locationType: editingAppointment.location.type as any,
        locationDetails: editingAppointment.location.details,
        notes: editingAppointment.notes,
        hourlyRate: 50 // Would get from appointment data
      });
    }
  }, [editingAppointment]);

  // Update hourly rate when session type or tutor changes
  useEffect(() => {
    const selectedTutor = tutors.find(t => t.id === formData.tutorId);
    if (selectedTutor) {
      setFormData(prev => ({
        ...prev,
        hourlyRate: selectedTutor.hourlyRates[formData.sessionType]
      }));
    }
  }, [formData.tutorId, formData.sessionType, tutors]);

  // Check for conflicts when time or tutor changes
  useEffect(() => {
    if (formData.tutorId && formData.scheduledDate && formData.startTime && formData.endTime) {
      checkConflicts();
    }
  }, [formData.tutorId, formData.scheduledDate, formData.startTime, formData.endTime]);

  const checkConflicts = async () => {
    // Mock conflict checking - replace with actual API call
    const mockConflicts = [];
    
    // Example conflict scenarios
    if (formData.startTime === '14:00' && formData.tutorId === 1) {
      mockConflicts.push(t('calendar.conflictExistingAppointment'));
    }
    
    if (formData.scheduledDate.getDay() === 0) { // Sunday
      mockConflicts.push(t('calendar.conflictWeekendNotAllowed'));
    }

    setConflicts(mockConflicts);
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.tutorId) {
      newErrors.tutorId = t('calendar.validation.tutorRequired');
    }

    if (!formData.clientId) {
      newErrors.clientId = t('calendar.validation.clientRequired');
    }

    if (!formData.subject.trim()) {
      newErrors.subject = t('calendar.validation.subjectRequired');
    }

    if (!formData.startTime) {
      newErrors.startTime = t('calendar.validation.startTimeRequired');
    }

    if (!formData.endTime) {
      newErrors.endTime = t('calendar.validation.endTimeRequired');
    }

    if (formData.startTime && formData.endTime && formData.startTime >= formData.endTime) {
      newErrors.endTime = t('calendar.validation.endTimeAfterStart');
    }

    if (formData.locationType === 'in_person' && !formData.locationDetails?.trim()) {
      newErrors.locationDetails = t('calendar.validation.locationDetailsRequired');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    if (conflicts.length > 0) {
      // Show confirmation dialog for conflicts
      const confirmOverride = window.confirm(
        `${t('calendar.conflictsDetected')}:\n${conflicts.join('\n')}\n\n${t('calendar.continueAnyway')}`
      );
      if (!confirmOverride) {
        return;
      }
    }

    setLoading(true);

    try {
      await onSubmit(formData);
      onClose();
    } catch (error) {
      console.error('Error creating appointment:', error);
      // Handle error
    } finally {
      setLoading(false);
    }
  };

  const selectedClient = clients.find(c => c.id === formData.clientId);
  const selectedTutor = tutors.find(t => t.id === formData.tutorId);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">
            {editingAppointment ? t('calendar.editAppointment') : t('calendar.newAppointment')}
          </h2>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Date and Time */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Calendar className="w-4 h-4 inline mr-1" />
                {t('calendar.date')}
              </label>
              <input
                type="date"
                value={format(formData.scheduledDate, 'yyyy-MM-dd')}
                onChange={(e) => setFormData(prev => ({ 
                  ...prev, 
                  scheduledDate: new Date(e.target.value) 
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Clock className="w-4 h-4 inline mr-1" />
                {t('calendar.startTime')}
              </label>
              <input
                type="time"
                value={formData.startTime}
                onChange={(e) => setFormData(prev => ({ ...prev, startTime: e.target.value }))}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 ${
                  errors.startTime ? 'border-red-300' : 'border-gray-300'
                }`}
                required
              />
              {errors.startTime && (
                <p className="mt-1 text-sm text-red-600">{errors.startTime}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('calendar.endTime')}
              </label>
              <input
                type="time"
                value={formData.endTime}
                onChange={(e) => setFormData(prev => ({ ...prev, endTime: e.target.value }))}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 ${
                  errors.endTime ? 'border-red-300' : 'border-gray-300'
                }`}
                required
              />
              {errors.endTime && (
                <p className="mt-1 text-sm text-red-600">{errors.endTime}</p>
              )}
            </div>
          </div>

          {/* Tutor Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <User className="w-4 h-4 inline mr-1" />
              {t('calendar.tutor')}
            </label>
            <select
              value={formData.tutorId}
              onChange={(e) => setFormData(prev => ({ ...prev, tutorId: parseInt(e.target.value) }))}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 ${
                errors.tutorId ? 'border-red-300' : 'border-gray-300'
              }`}
              required
            >
              <option value="">{t('calendar.selectTutor')}</option>
              {tutors.map((tutor) => (
                <option key={tutor.id} value={tutor.id}>
                  {tutor.name} - {tutor.specialties.join(', ')}
                </option>
              ))}
            </select>
            {errors.tutorId && (
              <p className="mt-1 text-sm text-red-600">{errors.tutorId}</p>
            )}
          </div>

          {/* Client Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('calendar.client')}
            </label>
            <select
              value={formData.clientId}
              onChange={(e) => setFormData(prev => ({ 
                ...prev, 
                clientId: parseInt(e.target.value),
                dependantId: undefined 
              }))}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 ${
                errors.clientId ? 'border-red-300' : 'border-gray-300'
              }`}
              required
            >
              <option value="">{t('calendar.selectClient')}</option>
              {clients.map((client) => (
                <option key={client.id} value={client.id}>
                  {client.name}
                </option>
              ))}
            </select>
            {errors.clientId && (
              <p className="mt-1 text-sm text-red-600">{errors.clientId}</p>
            )}
          </div>

          {/* Dependant Selection */}
          {selectedClient && selectedClient.dependants && selectedClient.dependants.length > 0 && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('calendar.dependant')} ({t('common.optional')})
              </label>
              <select
                value={formData.dependantId || ''}
                onChange={(e) => setFormData(prev => ({ 
                  ...prev, 
                  dependantId: e.target.value ? parseInt(e.target.value) : undefined 
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              >
                <option value="">{t('calendar.selectDependant')}</option>
                {selectedClient.dependants.map((dependant) => (
                  <option key={dependant.id} value={dependant.id}>
                    {dependant.name}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* Subject and Session Type */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('calendar.subject')}
              </label>
              <input
                type="text"
                value={formData.subject}
                onChange={(e) => setFormData(prev => ({ ...prev, subject: e.target.value }))}
                placeholder={t('calendar.subjectPlaceholder')}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 ${
                  errors.subject ? 'border-red-300' : 'border-gray-300'
                }`}
                required
              />
              {errors.subject && (
                <p className="mt-1 text-sm text-red-600">{errors.subject}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Users className="w-4 h-4 inline mr-1" />
                {t('calendar.sessionType')}
              </label>
              <select
                value={formData.sessionType}
                onChange={(e) => setFormData(prev => ({ 
                  ...prev, 
                  sessionType: e.target.value as any 
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              >
                <option value="individual">{t('calendar.individual')}</option>
                <option value="group">{t('calendar.group')}</option>
                <option value="tecfee">{t('calendar.tecfee')}</option>
              </select>
            </div>
          </div>

          {/* Location */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <MapPin className="w-4 h-4 inline mr-1" />
              {t('calendar.location')}
            </label>
            <div className="space-y-3">
              <div className="flex space-x-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="locationType"
                    value="online"
                    checked={formData.locationType === 'online'}
                    onChange={(e) => setFormData(prev => ({ 
                      ...prev, 
                      locationType: e.target.value as any 
                    }))}
                    className="mr-2"
                  />
                  <Video className="w-4 h-4 mr-1" />
                  {t('calendar.online')}
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="locationType"
                    value="in_person"
                    checked={formData.locationType === 'in_person'}
                    onChange={(e) => setFormData(prev => ({ 
                      ...prev, 
                      locationType: e.target.value as any 
                    }))}
                    className="mr-2"
                  />
                  <Home className="w-4 h-4 mr-1" />
                  {t('calendar.inPerson')}
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="locationType"
                    value="library"
                    checked={formData.locationType === 'library'}
                    onChange={(e) => setFormData(prev => ({ 
                      ...prev, 
                      locationType: e.target.value as any 
                    }))}
                    className="mr-2"
                  />
                  <MapPin className="w-4 h-4 mr-1" />
                  {t('calendar.library')}
                </label>
              </div>

              {(formData.locationType === 'in_person' || formData.locationType === 'library') && (
                <input
                  type="text"
                  value={formData.locationDetails || ''}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    locationDetails: e.target.value 
                  }))}
                  placeholder={t('calendar.locationDetailsPlaceholder')}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 ${
                    errors.locationDetails ? 'border-red-300' : 'border-gray-300'
                  }`}
                />
              )}
              {errors.locationDetails && (
                <p className="mt-1 text-sm text-red-600">{errors.locationDetails}</p>
              )}
            </div>
          </div>

          {/* Hourly Rate */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('calendar.hourlyRate')}
            </label>
            <div className="relative">
              <span className="absolute left-3 top-2 text-gray-500">$</span>
              <input
                type="number"
                value={formData.hourlyRate}
                onChange={(e) => setFormData(prev => ({ 
                  ...prev, 
                  hourlyRate: parseFloat(e.target.value) 
                }))}
                className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                min="0"
                step="0.01"
                required
              />
              <span className="absolute right-3 top-2 text-gray-500">CAD/hour</span>
            </div>
            {selectedTutor && (
              <p className="mt-1 text-sm text-gray-500">
                {t('calendar.suggestedRate')}: ${selectedTutor.hourlyRates[formData.sessionType]} CAD/hour
              </p>
            )}
          </div>

          {/* Group Session Settings */}
          {(formData.sessionType === 'group' || formData.sessionType === 'tecfee') && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('calendar.maxParticipants')}
              </label>
              <input
                type="number"
                value={formData.maxParticipants || ''}
                onChange={(e) => setFormData(prev => ({ 
                  ...prev, 
                  maxParticipants: e.target.value ? parseInt(e.target.value) : undefined 
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                min="2"
                max="20"
                placeholder="8"
              />
            </div>
          )}

          {/* Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('calendar.notes')} ({t('common.optional')})
            </label>
            <textarea
              value={formData.notes || ''}
              onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              placeholder={t('calendar.notesPlaceholder')}
            />
          </div>

          {/* Conflicts */}
          {conflicts.length > 0 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
              <h4 className="text-sm font-medium text-yellow-800 mb-2">
                {t('calendar.conflictsDetected')}:
              </h4>
              <ul className="text-sm text-yellow-700 space-y-1">
                {conflicts.map((conflict, index) => (
                  <li key={index}>• {conflict}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              {t('common.cancel')}
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? t('common.saving') : (editingAppointment ? t('common.save') : t('calendar.createAppointment'))}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AppointmentForm;