"""
Advanced permission system for granular access control in TutorAide.
Implements resource-based and relationship-based authorization.
"""

from enum import Enum
from typing import Dict, List, Optional, Set, Union
from dataclasses import dataclass
from abc import ABC, abstractmethod
import asyncpg

from app.models.user_models import User, UserRoleType
from app.core.logging import TutorAideLogger


class PermissionAction(Enum):
    """Available permission actions."""
    VIEW = "view"
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"
    MANAGE = "manage"  # Full control


class ResourceType(Enum):
    """Available resource types."""
    CLIENT_PROFILE = "client_profile"
    DEPENDANT = "dependant" 
    TUTOR_PROFILE = "tutor_profile"
    APPOINTMENT = "appointment"
    ASSESSMENT = "assessment"
    BILLING = "billing"
    DOCUMENT = "document"
    MESSAGE = "message"
    USER_ACCOUNT = "user_account"


@dataclass
class Permission:
    """Represents a single permission."""
    resource_type: ResourceType
    action: PermissionAction
    resource_id: Optional[int] = None
    conditions: Optional[Dict] = None


@dataclass
class AuthorizationContext:
    """Context for authorization checks."""
    user: User
    resource_type: ResourceType
    action: PermissionAction
    resource_id: Optional[int] = None
    resource_data: Optional[Dict] = None
    request_data: Optional[Dict] = None


class PermissionChecker(ABC):
    """Abstract base class for permission checkers."""
    
    @abstractmethod
    async def has_permission(self, context: AuthorizationContext, db: asyncpg.Connection) -> bool:
        """Check if user has permission for the given context."""
        pass


class RoleBasedPermissionChecker(PermissionChecker):
    """Checks permissions based on user roles."""
    
    def __init__(self):
        self.logger = TutorAideLogger.get_logger(__name__)
        
        # Define role-based permissions
        self.role_permissions = {
            UserRoleType.MANAGER: {
                # Managers have full access to everything
                ResourceType.CLIENT_PROFILE: [PermissionAction.VIEW, PermissionAction.CREATE, PermissionAction.UPDATE, PermissionAction.DELETE, PermissionAction.MANAGE],
                ResourceType.DEPENDANT: [PermissionAction.VIEW, PermissionAction.CREATE, PermissionAction.UPDATE, PermissionAction.DELETE, PermissionAction.MANAGE],
                ResourceType.TUTOR_PROFILE: [PermissionAction.VIEW, PermissionAction.CREATE, PermissionAction.UPDATE, PermissionAction.DELETE, PermissionAction.MANAGE],
                ResourceType.APPOINTMENT: [PermissionAction.VIEW, PermissionAction.CREATE, PermissionAction.UPDATE, PermissionAction.DELETE, PermissionAction.MANAGE],
                ResourceType.ASSESSMENT: [PermissionAction.VIEW, PermissionAction.CREATE, PermissionAction.UPDATE, PermissionAction.DELETE, PermissionAction.MANAGE],
                ResourceType.BILLING: [PermissionAction.VIEW, PermissionAction.CREATE, PermissionAction.UPDATE, PermissionAction.DELETE, PermissionAction.MANAGE],
                ResourceType.DOCUMENT: [PermissionAction.VIEW, PermissionAction.CREATE, PermissionAction.UPDATE, PermissionAction.DELETE, PermissionAction.MANAGE],
                ResourceType.MESSAGE: [PermissionAction.VIEW, PermissionAction.CREATE, PermissionAction.UPDATE, PermissionAction.DELETE, PermissionAction.MANAGE],
                ResourceType.USER_ACCOUNT: [PermissionAction.VIEW, PermissionAction.CREATE, PermissionAction.UPDATE, PermissionAction.DELETE, PermissionAction.MANAGE],
            },
            UserRoleType.TUTOR: {
                # Tutors can manage their own profile and view assigned clients
                ResourceType.TUTOR_PROFILE: [PermissionAction.VIEW, PermissionAction.UPDATE],
                ResourceType.CLIENT_PROFILE: [PermissionAction.VIEW],  # Only assigned clients
                ResourceType.DEPENDANT: [PermissionAction.VIEW],  # Only assigned dependants
                ResourceType.APPOINTMENT: [PermissionAction.VIEW, PermissionAction.UPDATE],  # Only their appointments
                ResourceType.ASSESSMENT: [PermissionAction.VIEW, PermissionAction.UPDATE],  # Only for assigned students
                ResourceType.DOCUMENT: [PermissionAction.VIEW, PermissionAction.CREATE, PermissionAction.UPDATE],  # Own documents
                ResourceType.MESSAGE: [PermissionAction.VIEW, PermissionAction.CREATE],  # Communication with clients
                ResourceType.USER_ACCOUNT: [PermissionAction.VIEW, PermissionAction.UPDATE],  # Own account only
            },
            UserRoleType.CLIENT: {
                # Clients can manage their own data and dependants
                ResourceType.CLIENT_PROFILE: [PermissionAction.VIEW, PermissionAction.UPDATE],  # Own profile only
                ResourceType.DEPENDANT: [PermissionAction.VIEW, PermissionAction.CREATE, PermissionAction.UPDATE],  # Own dependants
                ResourceType.TUTOR_PROFILE: [PermissionAction.VIEW],  # View assigned tutors
                ResourceType.APPOINTMENT: [PermissionAction.VIEW, PermissionAction.CREATE],  # Own appointments
                ResourceType.ASSESSMENT: [PermissionAction.VIEW, PermissionAction.CREATE, PermissionAction.UPDATE],  # Own dependants' assessments
                ResourceType.BILLING: [PermissionAction.VIEW],  # Own billing only
                ResourceType.MESSAGE: [PermissionAction.VIEW, PermissionAction.CREATE],  # Communication
                ResourceType.USER_ACCOUNT: [PermissionAction.VIEW, PermissionAction.UPDATE],  # Own account only
            }
        }
    
    async def has_permission(self, context: AuthorizationContext, db: asyncpg.Connection) -> bool:
        """Check if user has role-based permission."""
        # Check if any of the user's roles have the required permission
        for role in context.user.roles:
            # Handle both enum and string role values
            role_key = role if isinstance(role, UserRoleType) else UserRoleType(role)
            role_perms = self.role_permissions.get(role_key, {})
            resource_perms = role_perms.get(context.resource_type, [])
            if context.action in resource_perms:
                self.logger.debug(f"User {context.user.user_id} granted {context.action.value} on {context.resource_type.value} via role {role}")
                return True
        
        return False


class ResourceOwnershipChecker(PermissionChecker):
    """Checks permissions based on resource ownership."""
    
    def __init__(self):
        self.logger = TutorAideLogger.get_logger(__name__)
        
        # Define ownership mappings for different resource types
        self.ownership_queries = {
            ResourceType.CLIENT_PROFILE: {
                "table": "client_profiles",
                "id_field": "client_id",
                "owner_field": "user_id"
            },
            ResourceType.DEPENDANT: {
                "table": "dependants",
                "id_field": "dependant_id",
                "owner_check": self._check_dependant_access
            },
            ResourceType.TUTOR_PROFILE: {
                "table": "tutor_profiles", 
                "id_field": "tutor_id",
                "owner_field": "user_id"
            },
            ResourceType.APPOINTMENT: {
                "table": "appointment_sessions",
                "id_field": "appointment_id",
                "owner_check": self._check_appointment_access
            },
            ResourceType.ASSESSMENT: {
                "table": "learning_needs_assessments",
                "id_field": "assessment_id", 
                "owner_check": self._check_assessment_access
            },
            ResourceType.BILLING: {
                "table": "billing_invoices",
                "id_field": "invoice_id",
                "owner_check": self._check_billing_access
            },
            ResourceType.USER_ACCOUNT: {
                "table": "user_accounts",
                "id_field": "user_id",
                "owner_field": "user_id"
            }
        }
    
    async def has_permission(self, context: AuthorizationContext, db: asyncpg.Connection) -> bool:
        """Check if user owns or has access to the resource."""
        if not context.resource_id:
            return False
        
        ownership_config = self.ownership_queries.get(context.resource_type)
        if not ownership_config:
            return False
        
        # Use custom ownership check if available
        if "owner_check" in ownership_config:
            return await ownership_config["owner_check"](context, db)
        
        # Use simple ownership check
        table = ownership_config["table"]
        id_field = ownership_config["id_field"]
        owner_field = ownership_config["owner_field"]
        
        query = f"""
            SELECT {owner_field}
            FROM {table}
            WHERE {id_field} = $1 AND deleted_at IS NULL
        """
        
        try:
            owner_id = await db.fetchval(query, context.resource_id)
            
            if owner_id is None:
                return False
            
            is_owner = owner_id == context.user.user_id
            if is_owner:
                self.logger.debug(f"User {context.user.user_id} granted access to {context.resource_type.value} {context.resource_id} via ownership")
            
            return is_owner
            
        except Exception as e:
            self.logger.error(f"Error checking resource ownership: {e}")
            return False
    
    async def _check_dependant_access(self, context: AuthorizationContext, db: asyncpg.Connection) -> bool:
        """Check if user can access dependant (parent relationship)."""
        query = """
            SELECT d.primary_client_id, d.secondary_client_id,
                   cp1.user_id as primary_user_id, cp2.user_id as secondary_user_id
            FROM dependants d
            LEFT JOIN client_profiles cp1 ON d.primary_client_id = cp1.client_id
            LEFT JOIN client_profiles cp2 ON d.secondary_client_id = cp2.client_id
            WHERE d.dependant_id = $1 AND d.deleted_at IS NULL
        """
        
        try:
            row = await db.fetchrow(query, context.resource_id)
            if not row:
                return False
            
            # User can access if they are primary or secondary parent
            return (context.user.user_id == row['primary_user_id'] or 
                   context.user.user_id == row['secondary_user_id'])
            
        except Exception as e:
            self.logger.error(f"Error checking dependant access: {e}")
            return False
    
    async def _check_appointment_access(self, context: AuthorizationContext, db: asyncpg.Connection) -> bool:
        """Check if user can access appointment (participant)."""
        query = """
            SELECT a.client_id, a.tutor_id,
                   cp.user_id as client_user_id, tp.user_id as tutor_user_id
            FROM appointment_sessions a
            LEFT JOIN client_profiles cp ON a.client_id = cp.client_id
            LEFT JOIN tutor_profiles tp ON a.tutor_id = tp.tutor_id
            WHERE a.appointment_id = $1 AND a.deleted_at IS NULL
        """
        
        try:
            row = await db.fetchrow(query, context.resource_id)
            if not row:
                return False
            
            # User can access if they are client or tutor
            return (context.user.user_id == row['client_user_id'] or 
                   context.user.user_id == row['tutor_user_id'])
            
        except Exception as e:
            self.logger.error(f"Error checking appointment access: {e}")
            return False
    
    async def _check_assessment_access(self, context: AuthorizationContext, db: asyncpg.Connection) -> bool:
        """Check if user can access assessment (parent or assigned tutor)."""
        query = """
            SELECT a.dependant_id, d.primary_client_id, d.secondary_client_id,
                   cp1.user_id as primary_user_id, cp2.user_id as secondary_user_id,
                   ta.tutor_id, tp.user_id as tutor_user_id
            FROM learning_needs_assessments a
            JOIN dependants d ON a.dependant_id = d.dependant_id
            LEFT JOIN client_profiles cp1 ON d.primary_client_id = cp1.client_id
            LEFT JOIN client_profiles cp2 ON d.secondary_client_id = cp2.client_id
            LEFT JOIN tutor_assignments ta ON d.dependant_id = ta.dependant_id AND ta.is_active = true
            LEFT JOIN tutor_profiles tp ON ta.tutor_id = tp.tutor_id
            WHERE a.assessment_id = $1 AND a.deleted_at IS NULL
        """
        
        try:
            row = await db.fetchrow(query, context.resource_id)
            if not row:
                return False
            
            # User can access if they are parent or assigned tutor
            return (context.user.user_id == row['primary_user_id'] or 
                   context.user.user_id == row['secondary_user_id'] or
                   context.user.user_id == row['tutor_user_id'])
            
        except Exception as e:
            self.logger.error(f"Error checking assessment access: {e}")
            return False
    
    async def _check_billing_access(self, context: AuthorizationContext, db: asyncpg.Connection) -> bool:
        """Check if user can access billing (client only)."""
        query = """
            SELECT b.client_id, cp.user_id as client_user_id
            FROM billing_invoices b
            JOIN client_profiles cp ON b.client_id = cp.client_id
            WHERE b.invoice_id = $1 AND b.deleted_at IS NULL
        """
        
        try:
            row = await db.fetchrow(query, context.resource_id)
            if not row:
                return False
            
            # Only the client can access their billing
            return context.user.user_id == row['client_user_id']
            
        except Exception as e:
            self.logger.error(f"Error checking billing access: {e}")
            return False


class RelationshipPermissionChecker(PermissionChecker):
    """Checks permissions based on relationships between users."""
    
    def __init__(self):
        self.logger = TutorAideLogger.get_logger(__name__)
    
    async def has_permission(self, context: AuthorizationContext, db: asyncpg.Connection) -> bool:
        """Check if user has access based on relationships."""
        if not context.resource_id:
            return False
        
        # Check tutor-client relationships
        if context.resource_type == ResourceType.CLIENT_PROFILE:
            return await self._check_tutor_client_relationship(context, db)
        elif context.resource_type == ResourceType.TUTOR_PROFILE:
            return await self._check_client_tutor_relationship(context, db)
        
        return False
    
    async def _check_tutor_client_relationship(self, context: AuthorizationContext, db: asyncpg.Connection) -> bool:
        """Check if tutor has access to client via active assignments."""
        if UserRoleType.TUTOR not in context.user.roles:
            return False
        
        query = """
            SELECT COUNT(*)
            FROM tutor_assignments ta
            JOIN tutor_profiles tp ON ta.tutor_id = tp.tutor_id
            JOIN dependants d ON ta.dependant_id = d.dependant_id
            WHERE tp.user_id = $1 
            AND (d.primary_client_id = $2 OR d.secondary_client_id = $2)
            AND ta.is_active = true
            AND ta.deleted_at IS NULL
        """
        
        try:
            count = await db.fetchval(query, context.user.user_id, context.resource_id)
            return count > 0
            
        except Exception as e:
            self.logger.error(f"Error checking tutor-client relationship: {e}")
            return False
    
    async def _check_client_tutor_relationship(self, context: AuthorizationContext, db: asyncpg.Connection) -> bool:
        """Check if client has access to tutor via active assignments."""
        if UserRoleType.CLIENT not in context.user.roles:
            return False
        
        query = """
            SELECT COUNT(*)
            FROM tutor_assignments ta
            JOIN client_profiles cp ON (ta.dependant_id IN (
                SELECT dependant_id FROM dependants 
                WHERE primary_client_id = cp.client_id OR secondary_client_id = cp.client_id
            ))
            WHERE cp.user_id = $1 
            AND ta.tutor_id = $2
            AND ta.is_active = true
            AND ta.deleted_at IS NULL
        """
        
        try:
            count = await db.fetchval(query, context.user.user_id, context.resource_id)
            return count > 0
            
        except Exception as e:
            self.logger.error(f"Error checking client-tutor relationship: {e}")
            return False


class PermissionEngine:
    """Main permission engine that coordinates all permission checkers."""
    
    def __init__(self):
        self.logger = TutorAideLogger.get_logger(__name__)
        
        # Initialize permission checkers in priority order
        self.checkers = [
            RoleBasedPermissionChecker(),
            ResourceOwnershipChecker(),
            RelationshipPermissionChecker()
        ]
    
    async def check_permission(
        self, 
        user: User,
        resource_type: ResourceType,
        action: PermissionAction,
        resource_id: Optional[int] = None,
        resource_data: Optional[Dict] = None,
        db: asyncpg.Connection = None
    ) -> bool:
        """
        Check if user has permission for the specified action on resource.
        
        Args:
            user: The user requesting access
            resource_type: Type of resource being accessed
            action: Action being performed
            resource_id: ID of specific resource (if applicable)
            resource_data: Additional resource data for context
            db: Database connection
            
        Returns:
            True if user has permission, False otherwise
        """
        if not db:
            self.logger.error("Database connection required for permission check")
            return False
        
        # Managers have full access to everything
        if UserRoleType.MANAGER in user.roles:
            self.logger.debug(f"User {user.user_id} granted {action.value} on {resource_type.value} via MANAGER role")
            return True
        
        # Create authorization context
        context = AuthorizationContext(
            user=user,
            resource_type=resource_type,
            action=action,
            resource_id=resource_id,
            resource_data=resource_data
        )
        
        # Check each permission checker
        for checker in self.checkers:
            try:
                if await checker.has_permission(context, db):
                    self.logger.debug(f"Permission granted by {checker.__class__.__name__}")
                    return True
            except Exception as e:
                self.logger.error(f"Error in {checker.__class__.__name__}: {e}")
                continue
        
        # Log permission denial
        self.logger.warning(
            f"Permission denied: User {user.user_id} cannot {action.value} "
            f"{resource_type.value} {resource_id or 'N/A'}"
        )
        return False
    
    async def filter_accessible_resources(
        self,
        user: User,
        resource_type: ResourceType,
        action: PermissionAction,
        db: asyncpg.Connection,
        additional_filters: Optional[Dict] = None
    ) -> str:
        """
        Generate SQL WHERE clause to filter resources user can access.
        
        Args:
            user: The user requesting access
            resource_type: Type of resource being filtered
            action: Action being performed
            db: Database connection
            additional_filters: Additional filters to apply
            
        Returns:
            SQL WHERE clause string
        """
        # Managers can see everything
        if UserRoleType.MANAGER in user.roles:
            return "1=1"  # Always true condition
        
        conditions = []
        
        # Add resource-specific filtering based on user roles and relationships
        if resource_type == ResourceType.CLIENT_PROFILE:
            conditions.extend(await self._get_client_profile_filters(user, action, db))
        elif resource_type == ResourceType.DEPENDANT:
            conditions.extend(await self._get_dependant_filters(user, action, db))
        elif resource_type == ResourceType.TUTOR_PROFILE:
            conditions.extend(await self._get_tutor_profile_filters(user, action, db))
        elif resource_type == ResourceType.APPOINTMENT:
            conditions.extend(await self._get_appointment_filters(user, action, db))
        elif resource_type == ResourceType.ASSESSMENT:
            conditions.extend(await self._get_assessment_filters(user, action, db))
        elif resource_type == ResourceType.BILLING:
            conditions.extend(await self._get_billing_filters(user, action, db))
        
        if not conditions:
            return "1=0"  # No access - always false condition
        
        return f"({' OR '.join(conditions)})"
    
    async def _get_client_profile_filters(self, user: User, action: PermissionAction, db: asyncpg.Connection) -> List[str]:
        """Get filtering conditions for client profiles."""
        conditions = []
        
        # Users can access their own profile
        if UserRoleType.CLIENT in user.roles:
            conditions.append(f"user_id = {user.user_id}")
        
        # Tutors can access profiles of assigned clients
        if UserRoleType.TUTOR in user.roles:
            conditions.append(f"""
                client_id IN (
                    SELECT DISTINCT d.primary_client_id
                    FROM tutor_assignments ta
                    JOIN tutor_profiles tp ON ta.tutor_id = tp.tutor_id
                    JOIN dependants d ON ta.dependant_id = d.dependant_id
                    WHERE tp.user_id = {user.user_id} AND ta.is_active = true
                    UNION
                    SELECT DISTINCT d.secondary_client_id
                    FROM tutor_assignments ta
                    JOIN tutor_profiles tp ON ta.tutor_id = tp.tutor_id
                    JOIN dependants d ON ta.dependant_id = d.dependant_id
                    WHERE tp.user_id = {user.user_id} AND ta.is_active = true
                )
            """)
        
        return conditions
    
    async def _get_dependant_filters(self, user: User, action: PermissionAction, db: asyncpg.Connection) -> List[str]:
        """Get filtering conditions for dependants."""
        conditions = []
        
        # Clients can access their own dependants
        if UserRoleType.CLIENT in user.roles:
            conditions.append(f"""
                (primary_client_id IN (SELECT client_id FROM client_profiles WHERE user_id = {user.user_id})
                 OR secondary_client_id IN (SELECT client_id FROM client_profiles WHERE user_id = {user.user_id}))
            """)
        
        # Tutors can access assigned dependants
        if UserRoleType.TUTOR in user.roles:
            conditions.append(f"""
                dependant_id IN (
                    SELECT ta.dependant_id
                    FROM tutor_assignments ta
                    JOIN tutor_profiles tp ON ta.tutor_id = tp.tutor_id
                    WHERE tp.user_id = {user.user_id} AND ta.is_active = true
                )
            """)
        
        return conditions
    
    async def _get_tutor_profile_filters(self, user: User, action: PermissionAction, db: asyncpg.Connection) -> List[str]:
        """Get filtering conditions for tutor profiles."""
        conditions = []
        
        # Users can access their own profile
        if UserRoleType.TUTOR in user.roles:
            conditions.append(f"user_id = {user.user_id}")
        
        # Clients can access profiles of assigned tutors
        if UserRoleType.CLIENT in user.roles:
            conditions.append(f"""
                tutor_id IN (
                    SELECT DISTINCT ta.tutor_id
                    FROM tutor_assignments ta
                    JOIN dependants d ON ta.dependant_id = d.dependant_id
                    JOIN client_profiles cp ON (d.primary_client_id = cp.client_id OR d.secondary_client_id = cp.client_id)
                    WHERE cp.user_id = {user.user_id} AND ta.is_active = true
                )
            """)
        
        return conditions
    
    async def _get_appointment_filters(self, user: User, action: PermissionAction, db: asyncpg.Connection) -> List[str]:
        """Get filtering conditions for appointments."""
        conditions = []
        
        # Clients can access their own appointments
        if UserRoleType.CLIENT in user.roles:
            conditions.append(f"""
                client_id IN (SELECT client_id FROM client_profiles WHERE user_id = {user.user_id})
            """)
        
        # Tutors can access their own appointments
        if UserRoleType.TUTOR in user.roles:
            conditions.append(f"""
                tutor_id IN (SELECT tutor_id FROM tutor_profiles WHERE user_id = {user.user_id})
            """)
        
        return conditions
    
    async def _get_assessment_filters(self, user: User, action: PermissionAction, db: asyncpg.Connection) -> List[str]:
        """Get filtering conditions for assessments."""
        conditions = []
        
        # Clients can access assessments of their dependants
        if UserRoleType.CLIENT in user.roles:
            conditions.append(f"""
                dependant_id IN (
                    SELECT dependant_id FROM dependants 
                    WHERE primary_client_id IN (SELECT client_id FROM client_profiles WHERE user_id = {user.user_id})
                    OR secondary_client_id IN (SELECT client_id FROM client_profiles WHERE user_id = {user.user_id})
                )
            """)
        
        # Tutors can access assessments of assigned dependants
        if UserRoleType.TUTOR in user.roles:
            conditions.append(f"""
                dependant_id IN (
                    SELECT ta.dependant_id
                    FROM tutor_assignments ta
                    JOIN tutor_profiles tp ON ta.tutor_id = tp.tutor_id
                    WHERE tp.user_id = {user.user_id} AND ta.is_active = true
                )
            """)
        
        return conditions
    
    async def _get_billing_filters(self, user: User, action: PermissionAction, db: asyncpg.Connection) -> List[str]:
        """Get filtering conditions for billing."""
        conditions = []
        
        # Only clients can access their own billing (tutors cannot see client billing)
        if UserRoleType.CLIENT in user.roles:
            conditions.append(f"""
                client_id IN (SELECT client_id FROM client_profiles WHERE user_id = {user.user_id})
            """)
        
        return conditions


# Global permission engine instance
permission_engine = PermissionEngine()