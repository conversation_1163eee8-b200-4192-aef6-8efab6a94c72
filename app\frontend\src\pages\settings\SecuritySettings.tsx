import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Shield, Smartphone, Mail, Key, AlertCircle, CheckCircle, Monitor } from 'lucide-react';
import { Card } from '../../components/common/Card';
import Button from '../../components/common/Button';
import { Toggle } from '../../components/common/Toggle';
import { Badge } from '../../components/common/Badge';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import { TwoFactorSetup } from '../../components/security/TwoFactorSetup';
import { TrustedDevices } from '../../components/security/TrustedDevices';
import api from '../../services/api';
import toast from 'react-hot-toast';

interface TwoFactorMethod {
  method: 'sms' | 'email' | 'totp';
  isEnabled: boolean;
  isPrimary: boolean;
  lastUsed?: string;
  phoneNumber?: string;
  email?: string;
}

interface SecuritySettings {
  twoFactorRequired: boolean;
  twoFactorGraceEndsAt?: string;
  methods: TwoFactorMethod[];
  trustedDevicesCount: number;
}

export const SecuritySettings: React.FC = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [settings, setSettings] = useState<SecuritySettings | null>(null);
  const [showSetupModal, setShowSetupModal] = useState(false);
  const [setupMethod, setSetupMethod] = useState<'sms' | 'email' | 'totp'>('totp');
  const [savingSettings, setSavingSettings] = useState(false);

  useEffect(() => {
    fetchSecuritySettings();
  }, []);

  const fetchSecuritySettings = async () => {
    try {
      setLoading(true);
      const response = await api.get<SecuritySettings>('/api/v1/users/security');
      setSettings(response.data);
    } catch (error) {
      console.error('Error fetching security settings:', error);
      toast.error(t('settings.security.errors.fetchFailed'));
    } finally {
      setLoading(false);
    }
  };

  const handleTwoFactorToggle = async (enabled: boolean) => {
    if (!settings) return;
    
    // Check if at least one method is set up
    const hasEnabledMethod = settings.methods.some(m => m.isEnabled);
    if (enabled && !hasEnabledMethod) {
      toast.error(t('settings.security.errors.noMethodEnabled'));
      return;
    }

    try {
      setSavingSettings(true);
      await api.put('/api/v1/users/security/2fa', {
        twoFactorRequired: enabled
      });
      
      setSettings({
        ...settings,
        twoFactorRequired: enabled
      });
      
      toast.success(
        enabled 
          ? t('settings.security.twoFactor.enabled')
          : t('settings.security.twoFactor.disabled')
      );
    } catch (error) {
      console.error('Error updating 2FA setting:', error);
      toast.error(t('settings.security.errors.updateFailed'));
    } finally {
      setSavingSettings(false);
    }
  };

  const handleMethodSetup = (method: 'sms' | 'email' | 'totp') => {
    setSetupMethod(method);
    setShowSetupModal(true);
  };

  const handleSetupComplete = () => {
    setShowSetupModal(false);
    fetchSecuritySettings();
  };

  const getMethodIcon = (method: string) => {
    switch (method) {
      case 'sms':
        return <Smartphone className="w-5 h-5" />;
      case 'email':
        return <Mail className="w-5 h-5" />;
      case 'totp':
        return <Key className="w-5 h-5" />;
      default:
        return <Shield className="w-5 h-5" />;
    }
  };

  const getMethodLabel = (method: string) => {
    switch (method) {
      case 'sms':
        return t('settings.security.methods.sms');
      case 'email':
        return t('settings.security.methods.email');
      case 'totp':
        return t('settings.security.methods.totp');
      default:
        return method;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!settings) {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* Two-Factor Authentication */}
      <Card>
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary-100 rounded-lg">
                <Shield className="w-6 h-6 text-primary-600" />
              </div>
              <div>
                <h2 className="text-lg font-semibold text-gray-900">
                  {t('settings.security.twoFactor.title')}
                </h2>
                <p className="text-sm text-gray-600">
                  {t('settings.security.twoFactor.description')}
                </p>
              </div>
            </div>
            <Toggle
              enabled={settings.twoFactorRequired}
              onChange={handleTwoFactorToggle}
              disabled={savingSettings}
            />
          </div>

          {settings.twoFactorGraceEndsAt && (
            <div className="mb-6 p-4 bg-yellow-50 rounded-lg">
              <div className="flex items-start gap-3">
                <AlertCircle className="w-5 h-5 text-yellow-600 mt-0.5" />
                <div>
                  <p className="text-sm text-yellow-800">
                    {t('settings.security.twoFactor.gracePeriod', {
                      date: new Date(settings.twoFactorGraceEndsAt).toLocaleDateString()
                    })}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Authentication Methods */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-gray-700">
              {t('settings.security.methods.title')}
            </h3>
            
            <div className="grid gap-4 md:grid-cols-3">
              {['totp', 'sms', 'email'].map((methodType) => {
                const method = settings.methods.find(m => m.method === methodType);
                const isEnabled = method?.isEnabled || false;
                const isPrimary = method?.isPrimary || false;
                
                return (
                  <Card key={methodType} className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center gap-3">
                        {getMethodIcon(methodType)}
                        <div>
                          <h4 className="font-medium text-gray-900">
                            {getMethodLabel(methodType)}
                          </h4>
                          {method?.phoneNumber && (
                            <p className="text-sm text-gray-600">{method.phoneNumber}</p>
                          )}
                          {method?.email && (
                            <p className="text-sm text-gray-600">{method.email}</p>
                          )}
                        </div>
                      </div>
                      {isEnabled && (
                        <Badge 
                          variant={isPrimary ? 'primary' : 'secondary'}
                          size="sm"
                        >
                          {isPrimary ? t('common.primary') : t('common.active')}
                        </Badge>
                      )}
                    </div>
                    
                    {method?.lastUsed && (
                      <p className="text-xs text-gray-500 mb-3">
                        {t('settings.security.methods.lastUsed')}: {
                          new Date(method.lastUsed).toLocaleDateString()
                        }
                      </p>
                    )}
                    
                    <Button
                      variant={isEnabled ? 'secondary' : 'primary'}
                      size="sm"
                      className="w-full"
                      onClick={() => handleMethodSetup(methodType as any)}
                    >
                      {isEnabled 
                        ? t('settings.security.methods.manage')
                        : t('settings.security.methods.setup')
                      }
                    </Button>
                  </Card>
                );
              })}
            </div>
          </div>
        </div>
      </Card>

      {/* Trusted Devices */}
      <Card>
        <div className="p-6">
          <div className="flex items-center gap-3 mb-6">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Monitor className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900">
                {t('settings.security.trustedDevices.title')}
              </h2>
              <p className="text-sm text-gray-600">
                {t('settings.security.trustedDevices.description', {
                  count: settings.trustedDevicesCount
                })}
              </p>
            </div>
          </div>
          
          <TrustedDevices />
        </div>
      </Card>

      {/* Security Tips */}
      <Card>
        <div className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            {t('settings.security.tips.title')}
          </h3>
          <div className="space-y-3">
            {[
              'useStrongPassword',
              'enableTwoFactor',
              'reviewDevices',
              'bewarePhishing'
            ].map((tip) => (
              <div key={tip} className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                <p className="text-sm text-gray-600">
                  {t(`settings.security.tips.${tip}`)}
                </p>
              </div>
            ))}
          </div>
        </div>
      </Card>

      {/* Two-Factor Setup Modal */}
      {showSetupModal && (
        <TwoFactorSetup
          method={setupMethod}
          onClose={() => setShowSetupModal(false)}
          onComplete={handleSetupComplete}
        />
      )}
    </div>
  );
};

export default SecuritySettings;