"""
Search service for user and entity search functionality.
"""

import time
from typing import List, Optional, Dict, Any
from datetime import datetime

from app.database.repositories.search_repository import SearchRepository
from app.models.search_models import (
    UserSearchRequest, UserSearchResponse, UserSearchResult,
    AutocompleteRequest, AutocompleteResponse, AutocompleteSuggestion,
    SearchEntityType, SearchSortBy, SearchSortOrder
)
from app.models.user_models import User, UserRoleType
from app.config.database import DatabaseManager
from app.core.exceptions import ValidationError, AuthorizationError, DatabaseOperationError
from app.core.logging import TutorAideLogger
from app.services.rate_limiting_service import RateLimitingService


class SearchService:
    """Service for user and entity search operations."""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.search_repo = SearchRepository()
        self.rate_limiter = RateLimitingService()
        self.logger = TutorAideLogger("search_service")
    
    async def search_users(
        self,
        request: UserSearchRequest,
        current_user: User
    ) -> UserSearchResponse:
        """
        Search users with filtering and pagination.
        
        Args:
            request: Search request parameters
            current_user: User performing the search
            
        Returns:
            UserSearchResponse with results and metadata
        """
        try:
            # Rate limiting
            await self._check_search_rate_limit(current_user.user_id)
            
            # Validate search request
            self._validate_search_request(request, current_user)
            
            start_time = time.time()
            
            async with self.db_manager.acquire() as conn:
                # Perform search
                results, total_count = await self.search_repo.search_users(
                    conn, request, current_user.user_id, current_user.roles
                )
                
                # Filter results based on user permissions
                filtered_results = self._filter_results_by_permissions(results, current_user)
                
                # Calculate pagination metadata
                total_pages = (total_count + request.page_size - 1) // request.page_size
                has_next = request.page < total_pages
                has_previous = request.page > 1
                
                duration_ms = (time.time() - start_time) * 1000
                
                return UserSearchResponse(
                    results=filtered_results,
                    total_count=total_count,
                    page=request.page,
                    page_size=request.page_size,
                    total_pages=total_pages,
                    has_next=has_next,
                    has_previous=has_previous,
                    query=request.query,
                    entity_type=request.entity_type,
                    sort_by=request.sort_by,
                    sort_order=request.sort_order,
                    search_duration_ms=duration_ms
                )
                
        except Exception as e:
            self.logger.error(f"Error in search_users: {str(e)}")
            if isinstance(e, (ValidationError, AuthorizationError, DatabaseOperationError)):
                raise
            raise DatabaseOperationError(f"Search operation failed: {str(e)}", operation="search_users")
    
    async def get_autocomplete_suggestions(
        self,
        request: AutocompleteRequest,
        current_user: User
    ) -> AutocompleteResponse:
        """
        Get autocomplete suggestions for search.
        
        Args:
            request: Autocomplete request parameters
            current_user: User requesting suggestions
            
        Returns:
            AutocompleteResponse with suggestions
        """
        try:
            # Rate limiting (more lenient for autocomplete)
            await self._check_autocomplete_rate_limit(current_user.user_id)
            
            # Validate request
            self._validate_autocomplete_request(request, current_user)
            
            start_time = time.time()
            
            async with self.db_manager.acquire() as conn:
                suggestions = await self.search_repo.get_autocomplete_suggestions(
                    conn, request, current_user.roles
                )
                
                # Filter suggestions based on permissions
                filtered_suggestions = self._filter_suggestions_by_permissions(suggestions, current_user)
                
                duration_ms = (time.time() - start_time) * 1000
                
                return AutocompleteResponse(
                    suggestions=filtered_suggestions,
                    query=request.query,
                    total_found=len(filtered_suggestions),
                    search_duration_ms=duration_ms
                )
                
        except Exception as e:
            self.logger.error(f"Error in get_autocomplete_suggestions: {str(e)}")
            if isinstance(e, (ValidationError, AuthorizationError, DatabaseOperationError)):
                raise
            raise DatabaseOperationError(
                f"Autocomplete operation failed: {str(e)}", 
                operation="get_autocomplete_suggestions"
            )
    
    async def get_quick_search_results(
        self,
        query: str,
        current_user: User,
        limit: int = 5
    ) -> List[UserSearchResult]:
        """
        Get quick search results for global search bar.
        
        Args:
            query: Search query
            current_user: User performing search
            limit: Maximum results to return
            
        Returns:
            List of quick search results
        """
        try:
            # Create simplified search request for quick results
            request = UserSearchRequest(
                query=query,
                entity_type=SearchEntityType.ALL,
                page=1,
                page_size=limit,
                sort_by=SearchSortBy.RELEVANCE,
                active_only=True
            )
            
            response = await self.search_users(request, current_user)
            return response.results
            
        except Exception as e:
            self.logger.error(f"Error in get_quick_search_results: {str(e)}")
            # Return empty results for quick search failures
            return []
    
    async def search_by_role(
        self,
        role: UserRoleType,
        current_user: User,
        query: Optional[str] = None,
        limit: int = 20
    ) -> List[UserSearchResult]:
        """
        Search users by specific role.
        
        Args:
            role: User role to search for
            current_user: User performing search
            query: Optional search query
            limit: Maximum results to return
            
        Returns:
            List of users with the specified role
        """
        try:
            # Determine entity type based on role
            entity_type = SearchEntityType.ALL
            if role == UserRoleType.CLIENT:
                entity_type = SearchEntityType.CLIENT
            elif role == UserRoleType.TUTOR:
                entity_type = SearchEntityType.TUTOR
            
            request = UserSearchRequest(
                query=query,
                entity_type=entity_type,
                roles=[role],
                page=1,
                page_size=limit,
                sort_by=SearchSortBy.NAME,
                active_only=True
            )
            
            response = await self.search_users(request, current_user)
            return response.results
            
        except Exception as e:
            self.logger.error(f"Error in search_by_role: {str(e)}")
            if isinstance(e, (ValidationError, AuthorizationError, DatabaseOperationError)):
                raise
            raise DatabaseOperationError(f"Role search failed: {str(e)}", operation="search_by_role")
    
    async def search_nearby_tutors(
        self,
        postal_code: str,
        radius_km: int,
        current_user: User,
        subject: Optional[str] = None,
        limit: int = 10
    ) -> List[UserSearchResult]:
        """
        Search for tutors near a specific location.
        
        Args:
            postal_code: Canadian postal code
            radius_km: Search radius in kilometers
            current_user: User performing search
            subject: Optional subject filter
            limit: Maximum results to return
            
        Returns:
            List of nearby tutors
        """
        try:
            request = UserSearchRequest(
                query=subject,
                entity_type=SearchEntityType.TUTOR,
                roles=[UserRoleType.TUTOR],
                postal_code=postal_code,
                radius_km=radius_km,
                page=1,
                page_size=limit,
                sort_by=SearchSortBy.NAME,
                verified_only=True,  # Only show verified tutors
                active_only=True
            )
            
            response = await self.search_users(request, current_user)
            return response.results
            
        except Exception as e:
            self.logger.error(f"Error in search_nearby_tutors: {str(e)}")
            if isinstance(e, (ValidationError, AuthorizationError, DatabaseOperationError)):
                raise
            raise DatabaseOperationError(
                f"Location search failed: {str(e)}", 
                operation="search_nearby_tutors"
            )
    
    def _validate_search_request(self, request: UserSearchRequest, current_user: User):
        """Validate search request parameters."""
        # Check query length
        if request.query and len(request.query.strip()) < 2:
            raise ValidationError("Search query must be at least 2 characters long")
        
        # Check pagination limits
        if request.page_size > 100:
            raise ValidationError("Page size cannot exceed 100 results")
        
        # Check role-based access permissions
        if request.entity_type == SearchEntityType.CLIENT:
            if UserRoleType.MANAGER not in current_user.roles and UserRoleType.CLIENT not in current_user.roles:
                raise AuthorizationError("Insufficient permissions to search clients")
        
        # Restrict detailed tutor searches for non-managers
        if (request.entity_type == SearchEntityType.TUTOR and 
            UserRoleType.MANAGER not in current_user.roles and
            request.verified_only is False):
            raise AuthorizationError("Only managers can search unverified tutors")
    
    def _validate_autocomplete_request(self, request: AutocompleteRequest, current_user: User):
        """Validate autocomplete request parameters."""
        # Check query length
        if len(request.query.strip()) < 1:
            raise ValidationError("Autocomplete query must be at least 1 character long")
        
        # Check suggestion limit
        if request.limit > 50:
            raise ValidationError("Autocomplete limit cannot exceed 50 suggestions")
    
    def _filter_results_by_permissions(
        self, 
        results: List[UserSearchResult], 
        current_user: User
    ) -> List[UserSearchResult]:
        """Filter search results based on user permissions."""
        # Managers can see all results
        if UserRoleType.MANAGER in current_user.roles:
            return results
        
        filtered_results = []
        
        for result in results:
            # Apply privacy filters based on user role and result type
            if self._can_view_result(result, current_user):
                # Sanitize result data based on permissions
                sanitized_result = self._sanitize_result_data(result, current_user)
                filtered_results.append(sanitized_result)
        
        return filtered_results
    
    def _can_view_result(self, result: UserSearchResult, current_user: User) -> bool:
        """Check if current user can view a specific search result."""
        # Users can always see their own data
        if result.user_id == current_user.user_id:
            return True
        
        # Managers can see everything
        if UserRoleType.MANAGER in current_user.roles:
            return True
        
        # Tutors can see:
        # - Other verified tutors (public profiles)
        # - Dependants (for potential pairing)
        if UserRoleType.TUTOR in current_user.roles:
            if result.entity_type == SearchEntityType.TUTOR:
                return result.verification_status in ['basic_approved', 'fully_verified', 'premium']
            if result.entity_type == SearchEntityType.DEPENDANT:
                return True
            # Cannot see client details
            return False
        
        # Clients can see:
        # - Other clients in their family (dependant relationships)
        # - Verified tutors (public profiles)
        if UserRoleType.CLIENT in current_user.roles:
            if result.entity_type == SearchEntityType.TUTOR:
                return result.verification_status in ['basic_approved', 'fully_verified', 'premium']
            if result.entity_type in [SearchEntityType.CLIENT, SearchEntityType.DEPENDANT]:
                # Additional logic needed to check family relationships
                return True
        
        return False
    
    def _sanitize_result_data(self, result: UserSearchResult, current_user: User) -> UserSearchResult:
        """Sanitize result data based on user permissions."""
        # Managers get full data
        if UserRoleType.MANAGER in current_user.roles:
            return result
        
        # For non-managers, hide sensitive information
        sanitized = result.model_copy()
        
        # Hide phone numbers from non-managers (except own data)
        if result.user_id != current_user.user_id:
            sanitized.phone_number = None
        
        # Hide postal codes for privacy (except for tutors showing service areas)
        if (result.entity_type != SearchEntityType.TUTOR and 
            result.user_id != current_user.user_id):
            sanitized.postal_code = None
        
        return sanitized
    
    def _filter_suggestions_by_permissions(
        self, 
        suggestions: List[AutocompleteSuggestion], 
        current_user: User
    ) -> List[AutocompleteSuggestion]:
        """Filter autocomplete suggestions based on permissions."""
        # Managers can see all suggestions
        if UserRoleType.MANAGER in current_user.roles:
            return suggestions
        
        # For other users, apply basic filtering
        filtered = []
        for suggestion in suggestions:
            # Basic permission check (could be enhanced with more detailed logic)
            if self._can_view_suggestion(suggestion, current_user):
                filtered.append(suggestion)
        
        return filtered
    
    def _can_view_suggestion(self, suggestion: AutocompleteSuggestion, current_user: User) -> bool:
        """Check if user can view a specific suggestion."""
        # Managers can see all
        if UserRoleType.MANAGER in current_user.roles:
            return True
        
        # Apply entity-type specific rules
        if suggestion.entity_type == SearchEntityType.TUTOR:
            # Only show verified tutors in suggestions
            return True  # Repo already filters by verification status
        
        if suggestion.entity_type == SearchEntityType.CLIENT:
            # Clients can see other clients, tutors cannot
            return UserRoleType.CLIENT in current_user.roles
        
        # Default to allowing
        return True
    
    async def _check_search_rate_limit(self, user_id: int):
        """Check search rate limiting."""
        try:
            await self.rate_limiter.check_limit(
                key=f"search:{user_id}",
                limit=100,  # 100 searches per hour
                window_seconds=3600
            )
        except Exception as e:
            raise ValidationError("Search rate limit exceeded. Please wait before searching again.")
    
    async def _check_autocomplete_rate_limit(self, user_id: int):
        """Check autocomplete rate limiting."""
        try:
            await self.rate_limiter.check_limit(
                key=f"autocomplete:{user_id}",
                limit=300,  # 300 autocomplete requests per hour
                window_seconds=3600
            )
        except Exception as e:
            raise ValidationError("Autocomplete rate limit exceeded. Please wait before searching again.")