"""
Two-Factor Authentication API endpoints - Updated to use unified security service.
"""

from fastapi import APIRouter, Depends, HTT<PERSON>Exception, Request, Response, status
from typing import Optional, Dict, Any, List
import logging
import asyncpg

from app.models.user_security_models import (
    TwoFactorSetupRequest, TwoFactorVerifyRequest,
    TwoFactorMethod, SecurityEventType
)
from app.models.user_models import User
from app.models.base import UserRoleType
from app.core.dependencies import get_current_user, get_database
from app.core.auth_decorators import require_auth
from app.services.user_security_service import get_user_security_service
from app.core.exceptions import (
    ValidationError, AuthenticationError, ResourceNotFoundError,
    ConflictError, PermissionDeniedError, BusinessLogicError
)
from app.core.logging import TutorAideLogger

logger = TutorAideLogger.get_logger(__name__)
router = APIRouter(prefix="/2fa", tags=["Two-Factor Authentication"])


@router.get("/status", response_model=Dict[str, Any])
async def get_2fa_status(
    current_user: User = Depends(get_current_user),
    db: asyncpg.Connection = Depends(get_database)
):
    """
    Get current 2FA status for the user.
    
    Returns whether 2FA is enabled and which method is being used.
    """
    try:
        security_service = get_user_security_service()
        
        # Get security settings
        security_settings = await security_service.get_user_security_settings(
            conn=db,
            user_id=current_user.user_id
        )
        
        return {
            "enabled": security_settings.two_factor_enabled,
            "method": security_settings.two_factor_method,
            "verified_at": security_settings.two_factor_verified_at.isoformat() if security_settings.two_factor_verified_at else None,
            "has_backup_codes": len(security_settings.two_factor_backup_codes) > 0,
            "backup_codes_count": len(security_settings.two_factor_backup_codes)
        }
        
    except Exception as e:
        logger.error(f"Error getting 2FA status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get 2FA status"
        )


@router.post("/setup", response_model=Dict[str, Any], status_code=status.HTTP_201_CREATED)
async def setup_2fa(
    setup_data: TwoFactorSetupRequest,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: asyncpg.Connection = Depends(get_database)
):
    """
    Set up two-factor authentication for the current user.
    
    Returns:
    - setup_data: QR code for TOTP or confirmation that SMS/Email setup initiated
    - backup_codes: Initial backup codes (only shown once)
    - message: Instructions for completing setup
    """
    try:
        security_service = get_user_security_service()
        
        # Set up 2FA
        setup_result, backup_codes = await security_service.setup_2fa(
            conn=db,
            user_id=current_user.user_id,
            method=setup_data.method,
            phone_number=setup_data.phone_number,
            email=setup_data.email
        )
        
        response = {
            "method": setup_data.method,
            "backup_codes": backup_codes,
            "message": "2FA setup initiated. Please verify with the code to enable."
        }
        
        # Add method-specific data
        if setup_data.method == TwoFactorMethod.TOTP:
            response["qr_code"] = setup_result  # Base64 encoded QR code
            response["message"] = "Scan the QR code with your authenticator app and verify with the code."
        elif setup_data.method == TwoFactorMethod.SMS:
            response["phone_number"] = setup_data.phone_number
            response["message"] = f"A verification code has been sent to {setup_data.phone_number}"
        elif setup_data.method == TwoFactorMethod.EMAIL:
            response["email"] = setup_data.email or current_user.email
            response["message"] = f"A verification code has been sent to {setup_data.email or current_user.email}"
        
        logger.info(
            f"2FA setup initiated for user {current_user.user_id} "
            f"using {setup_data.method}"
        )
        
        return response
        
    except ConflictError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"2FA setup error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to set up 2FA"
        )


@router.post("/verify-setup", response_model=Dict[str, Any])
async def verify_2fa_setup(
    verify_data: TwoFactorVerifyRequest,
    current_user: User = Depends(get_current_user),
    db: asyncpg.Connection = Depends(get_database)
):
    """
    Verify 2FA setup with the provided code to enable it.
    
    This completes the 2FA setup process.
    """
    try:
        security_service = get_user_security_service()
        
        # Verify setup
        success = await security_service.verify_2fa_setup(
            conn=db,
            user_id=current_user.user_id,
            code=verify_data.code
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid verification code"
            )
        
        return {
            "success": True,
            "message": "Two-factor authentication has been enabled successfully"
        }
        
    except HTTPException:
        raise
    except (ValidationError, AuthenticationError) as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"2FA verification error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to verify 2FA setup"
        )


@router.post("/challenge", response_model=Dict[str, Any])
async def create_2fa_challenge(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: asyncpg.Connection = Depends(get_database),
    purpose: str = "login"
):
    """
    Create a 2FA challenge for authentication.
    
    For SMS/Email: Sends a code to the registered phone/email
    For TOTP: Returns a placeholder (user uses their authenticator app)
    """
    try:
        security_service = get_user_security_service()
        
        # Create challenge
        challenge_id = await security_service.create_2fa_challenge(
            conn=db,
            user_id=current_user.user_id,
            purpose=purpose
        )
        
        # Get user's 2FA method
        security_settings = await security_service.get_user_security_settings(
            conn=db,
            user_id=current_user.user_id
        )
        
        response = {
            "challenge_id": challenge_id,
            "method": security_settings.two_factor_method,
            "message": "2FA challenge created"
        }
        
        if security_settings.two_factor_method == TwoFactorMethod.TOTP:
            response["message"] = "Please enter the code from your authenticator app"
        elif security_settings.two_factor_method == TwoFactorMethod.SMS:
            response["message"] = "A verification code has been sent to your phone"
        elif security_settings.two_factor_method == TwoFactorMethod.EMAIL:
            response["message"] = "A verification code has been sent to your email"
        
        return response
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"2FA challenge error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create 2FA challenge"
        )


@router.post("/verify", response_model=Dict[str, Any])
async def verify_2fa_challenge(
    verify_data: TwoFactorVerifyRequest,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: asyncpg.Connection = Depends(get_database)
):
    """
    Verify a 2FA challenge with the provided code.
    
    Can also handle backup codes.
    """
    try:
        security_service = get_user_security_service()
        
        # Verify challenge
        is_valid, trust_token = await security_service.verify_2fa_challenge(
            conn=db,
            user_id=current_user.user_id,
            code=verify_data.code,
            device_fingerprint=verify_data.device_fingerprint,
            trust_device=verify_data.trust_device
        )
        
        if not is_valid:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid verification code"
            )
        
        response = {
            "success": True,
            "message": "2FA verification successful"
        }
        
        if trust_token:
            response["trust_token"] = trust_token
            response["message"] = "2FA verification successful. Device has been trusted."
        
        return response
        
    except HTTPException:
        raise
    except AuthenticationError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"2FA verification error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to verify 2FA"
        )


@router.post("/disable", response_model=Dict[str, Any])
async def disable_2fa(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: asyncpg.Connection = Depends(get_database),
    password: str = None
):
    """
    Disable two-factor authentication.
    
    Requires password confirmation for security.
    """
    if not password:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Password required to disable 2FA"
        )
    
    try:
        security_service = get_user_security_service()
        
        # Disable 2FA
        success = await security_service.disable_2fa(
            conn=db,
            user_id=current_user.user_id,
            password=password
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to disable 2FA"
            )
        
        logger.info(f"2FA disabled for user {current_user.user_id}")
        
        return {
            "success": True,
            "message": "Two-factor authentication has been disabled"
        }
        
    except AuthenticationError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"2FA disable error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to disable 2FA"
        )


@router.post("/backup-codes/regenerate", response_model=Dict[str, Any])
async def regenerate_backup_codes(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: asyncpg.Connection = Depends(get_database),
    password: str = None
):
    """
    Regenerate backup codes.
    
    Requires password confirmation. Old codes are invalidated.
    """
    if not password:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Password required to regenerate backup codes"
        )
    
    try:
        security_service = get_user_security_service()
        
        # Regenerate codes
        new_codes = await security_service.regenerate_backup_codes(
            conn=db,
            user_id=current_user.user_id,
            password=password
        )
        
        return {
            "success": True,
            "backup_codes": new_codes,
            "message": "Backup codes regenerated successfully. Save these codes in a safe place."
        }
        
    except AuthenticationError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Backup codes regeneration error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to regenerate backup codes"
        )


@router.get("/trusted-devices", response_model=Dict[str, Any])
async def get_trusted_devices(
    current_user: User = Depends(get_current_user),
    db: asyncpg.Connection = Depends(get_database)
):
    """
    Get list of trusted devices for the current user.
    """
    try:
        security_service = get_user_security_service()
        
        # Get trusted devices
        devices = await security_service.get_trusted_devices(
            conn=db,
            user_id=current_user.user_id
        )
        
        # Format for response
        formatted_devices = []
        for device in devices:
            formatted_devices.append({
                "device_fingerprint": device.device_fingerprint,
                "device_name": device.device_name,
                "last_used_at": device.last_used_at.isoformat(),
                "expires_at": device.expires_at.isoformat(),
                "ip_address": device.ip_address,
                "user_agent": device.user_agent
            })
        
        return {
            "devices": formatted_devices,
            "count": len(formatted_devices)
        }
        
    except Exception as e:
        logger.error(f"Error getting trusted devices: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get trusted devices"
        )


@router.delete("/trusted-devices/{device_fingerprint}", response_model=Dict[str, Any])
async def remove_trusted_device(
    device_fingerprint: str,
    current_user: User = Depends(get_current_user),
    db: asyncpg.Connection = Depends(get_database)
):
    """
    Remove a trusted device.
    """
    try:
        security_service = get_user_security_service()
        
        # Remove device
        success = await security_service.remove_trusted_device(
            conn=db,
            user_id=current_user.user_id,
            device_fingerprint=device_fingerprint
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Device not found"
            )
        
        return {
            "success": True,
            "message": "Trusted device removed successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error removing trusted device: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to remove trusted device"
        )