-- Rollback Migration: Remove Performance Analytics System
-- Description: Removes all analytics tables, views, and related objects

-- Drop views
DROP VIEW IF EXISTS student_progress_summary;
DROP VIEW IF EXISTS tutor_leaderboard;

-- Drop triggers
DROP TRIGGER IF EXISTS update_session_feedback_timestamp ON session_feedback;
DROP TRIGGER IF EXISTS update_student_metrics_timestamp ON student_progress_metrics;
DROP TRIGGER IF EXISTS update_student_goals_timestamp ON student_learning_goals;
DROP TRIGGER IF EXISTS update_tutor_metrics_timestamp ON tutor_performance_metrics;

-- Drop function
DROP FUNCTION IF EXISTS update_analytics_timestamp();

-- Drop tables in reverse order of dependencies
DROP TABLE IF EXISTS analytics_snapshots;
DROP TABLE IF EXISTS session_feedback;
DROP TABLE IF EXISTS platform_analytics;
DROP TABLE IF EXISTS student_progress_metrics;
DROP TABLE IF EXISTS student_learning_goals;
DROP TABLE IF EXISTS tutor_performance_metrics;

-- Revoke permissions (if roles exist)
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'tutoraide_read') THEN
        REVOKE SELECT ON ALL TABLES IN SCHEMA public FROM tutoraide_read;
    END IF;
    
    IF EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'tutoraide_write') THEN
        REVOKE SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA public FROM tutoraide_write;
        REVOKE USAGE ON ALL SEQUENCES IN SCHEMA public FROM tutoraide_write;
    END IF;
END $$;