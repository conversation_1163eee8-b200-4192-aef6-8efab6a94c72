import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  DollarSign, TrendingUp, TrendingDown, Calendar, Download,
  FileText, BarChart, LineChart, <PERSON><PERSON>hart, ArrowUp, ArrowDown,
  Filter, RefreshCw, Building, Users, Package, Clock
} from 'lucide-react';
import { Card } from '../../components/common/Card';
import Button from '../../components/common/Button';
import { Badge } from '../../components/common/Badge';
import { Select } from '../../components/common/Select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/common/TabsAdapter';
import { 
  Line<PERSON>hart as RechartsLineChart, Line, BarChart as RechartsBarChart, Bar,
  PieChart as RechartsPieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, 
  Tooltip, Legend, ResponsiveContainer, Area, AreaChart
} from 'recharts';
import { format, subMonths, startOfMonth, endOfMonth } from 'date-fns';
import toast from 'react-hot-toast';

interface FinancialSummary {
  totalRevenue: number;
  totalExpenses: number;
  netProfit: number;
  profitMargin: number;
  revenueGrowth: number;
  expenseGrowth: number;
  avgTransactionValue: number;
  outstandingInvoices: number;
}

interface RevenueBreakdown {
  source: string;
  amount: number;
  percentage: number;
  transactions: number;
}

interface ExpenseBreakdown {
  category: string;
  amount: number;
  percentage: number;
  items: number;
}

const EnhancedFinancialReport: React.FC = () => {
  const { t } = useTranslation();
  const [dateRange, setDateRange] = useState('this_month');
  const [compareWith, setCompareWith] = useState('last_month');
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  
  const [summary, setSummary] = useState<FinancialSummary>({
    totalRevenue: 156780,
    totalExpenses: 89450,
    netProfit: 67330,
    profitMargin: 42.9,
    revenueGrowth: 12.5,
    expenseGrowth: 8.3,
    avgTransactionValue: 115.50,
    outstandingInvoices: 24567
  });

  const [monthlyRevenue] = useState([
    { month: 'Jun', revenue: 125000, expenses: 75000, profit: 50000 },
    { month: 'Jul', revenue: 132000, expenses: 78000, profit: 54000 },
    { month: 'Aug', revenue: 128000, expenses: 80000, profit: 48000 },
    { month: 'Sep', revenue: 145000, expenses: 82000, profit: 63000 },
    { month: 'Oct', revenue: 152000, expenses: 85000, profit: 67000 },
    { month: 'Nov', revenue: 156780, expenses: 89450, profit: 67330 }
  ]);

  const [revenueBreakdown] = useState<RevenueBreakdown[]>([
    { source: 'Tutoring Sessions', amount: 125424, percentage: 80, transactions: 1087 },
    { source: 'Subscription Packages', amount: 18936, percentage: 12.1, transactions: 42 },
    { source: 'TECFEE Programs', amount: 9407, percentage: 6, transactions: 28 },
    { source: 'Late Fees', amount: 3013, percentage: 1.9, transactions: 67 }
  ]);

  const [expenseBreakdown] = useState<ExpenseBreakdown[]>([
    { category: 'Tutor Payments', amount: 68125, percentage: 76.1, items: 324 },
    { category: 'Payment Processing', amount: 4678, percentage: 5.2, items: 1224 },
    { category: 'SMS/Email Services', amount: 2890, percentage: 3.2, items: 4580 },
    { category: 'Platform Operations', amount: 8900, percentage: 10, items: 45 },
    { category: 'Marketing', amount: 3500, percentage: 3.9, items: 12 },
    { category: 'Other', amount: 1357, percentage: 1.6, items: 23 }
  ]);

  const [subjectRevenue] = useState([
    { subject: 'Mathematics', revenue: 45680, students: 187 },
    { subject: 'Science', revenue: 32450, students: 134 },
    { subject: 'French', revenue: 28900, students: 156 },
    { subject: 'English', revenue: 24350, students: 98 },
    { subject: 'Other', revenue: 25400, students: 89 }
  ]);

  const [serviceTypeRevenue] = useState([
    { type: 'Online', revenue: 67890, percentage: 43.3, sessions: 587 },
    { type: 'In Person', revenue: 58430, percentage: 37.2, sessions: 412 },
    { type: 'Library', revenue: 22340, percentage: 14.3, sessions: 156 },
    { type: 'Hybrid', revenue: 8120, percentage: 5.2, sessions: 45 }
  ]);

  const COLORS = ['#dc2626', '#3b82f6', '#10b981', '#f59e0b', '#8b5cf6', '#6b7280'];

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD',
    }).format(amount);
  };

  const handleExportReport = async () => {
    setLoading(true);
    // Simulate export
    await new Promise(resolve => setTimeout(resolve, 2000));
    toast.success('Financial report exported successfully');
    setLoading(false);
  };

  const getGrowthIcon = (growth: number) => {
    if (growth > 0) {
      return <ArrowUp className="w-4 h-4 text-green-600" />;
    } else if (growth < 0) {
      return <ArrowDown className="w-4 h-4 text-red-600" />;
    }
    return null;
  };

  const getGrowthColor = (growth: number) => {
    if (growth > 0) return 'text-green-600';
    if (growth < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Financial Report</h1>
          <p className="text-gray-600 mt-1">
            Comprehensive financial overview and analytics
          </p>
        </div>
        <div className="flex gap-3">
          <Button
            variant="secondary"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          <Button
            variant="primary"
            onClick={handleExportReport}
            disabled={loading}
          >
            <Download className="w-4 h-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Date Range Filters */}
      <Card className="p-4">
        <div className="flex flex-col lg:flex-row gap-4 items-center">
          <div className="flex gap-3 flex-1">
            <Select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              options={[
                { value: 'today', label: 'Today' },
                { value: 'this_week', label: 'This Week' },
                { value: 'this_month', label: 'This Month' },
                { value: 'this_quarter', label: 'This Quarter' },
                { value: 'this_year', label: 'This Year' },
                { value: 'last_30_days', label: 'Last 30 Days' },
                { value: 'last_90_days', label: 'Last 90 Days' },
                { value: 'custom', label: 'Custom Range' }
              ]}
              className="w-48"
            />
            <Select
              value={compareWith}
              onChange={(e) => setCompareWith(e.target.value)}
              options={[
                { value: 'none', label: 'No Comparison' },
                { value: 'last_period', label: 'Last Period' },
                { value: 'last_month', label: 'Last Month' },
                { value: 'last_year', label: 'Last Year' }
              ]}
              className="w-48"
            />
          </div>
          
          <div className="text-sm text-gray-600">
            Showing data for: <span className="font-medium">November 2024</span>
          </div>
        </div>
      </Card>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 rounded-full bg-green-100">
              <TrendingUp className="w-6 h-6 text-green-600" />
            </div>
            <div className={`flex items-center gap-1 text-sm ${getGrowthColor(summary.revenueGrowth)}`}>
              {getGrowthIcon(summary.revenueGrowth)}
              <span>{Math.abs(summary.revenueGrowth)}%</span>
            </div>
          </div>
          <h3 className="text-2xl font-bold text-gray-900">{formatCurrency(summary.totalRevenue)}</h3>
          <p className="text-sm text-gray-600 mt-1">Total Revenue</p>
        </Card>
        
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 rounded-full bg-red-100">
              <TrendingDown className="w-6 h-6 text-red-600" />
            </div>
            <div className={`flex items-center gap-1 text-sm ${getGrowthColor(-summary.expenseGrowth)}`}>
              {getGrowthIcon(summary.expenseGrowth)}
              <span>{Math.abs(summary.expenseGrowth)}%</span>
            </div>
          </div>
          <h3 className="text-2xl font-bold text-gray-900">{formatCurrency(summary.totalExpenses)}</h3>
          <p className="text-sm text-gray-600 mt-1">Total Expenses</p>
        </Card>
        
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 rounded-full bg-blue-100">
              <DollarSign className="w-6 h-6 text-blue-600" />
            </div>
            <Badge className="bg-green-100 text-green-700">
              {summary.profitMargin}%
            </Badge>
          </div>
          <h3 className="text-2xl font-bold text-gray-900">{formatCurrency(summary.netProfit)}</h3>
          <p className="text-sm text-gray-600 mt-1">Net Profit</p>
        </Card>
        
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 rounded-full bg-yellow-100">
              <Clock className="w-6 h-6 text-yellow-600" />
            </div>
            <Badge className="bg-yellow-100 text-yellow-700">Pending</Badge>
          </div>
          <h3 className="text-2xl font-bold text-gray-900">{formatCurrency(summary.outstandingInvoices)}</h3>
          <p className="text-sm text-gray-600 mt-1">Outstanding</p>
        </Card>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="revenue">Revenue</TabsTrigger>
          <TabsTrigger value="expenses">Expenses</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="forecasts">Forecasts</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6 mt-6">
          {/* Revenue vs Expenses Chart */}
          <Card className="p-6">
            <h2 className="font-semibold text-gray-900 mb-4">Revenue vs Expenses</h2>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={monthlyRevenue}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis tickFormatter={(value) => `$${value / 1000}k`} />
                <Tooltip formatter={(value: number) => formatCurrency(value)} />
                <Legend />
                <Area 
                  type="monotone" 
                  dataKey="revenue" 
                  stackId="1"
                  stroke="#10b981" 
                  fill="#10b981" 
                  fillOpacity={0.8}
                  name="Revenue"
                />
                <Area 
                  type="monotone" 
                  dataKey="expenses" 
                  stackId="2"
                  stroke="#ef4444" 
                  fill="#ef4444" 
                  fillOpacity={0.8}
                  name="Expenses"
                />
              </AreaChart>
            </ResponsiveContainer>
          </Card>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card className="p-6">
              <h3 className="font-semibold text-gray-900 mb-4">Key Metrics</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Avg Transaction Value</span>
                  <span className="font-medium">{formatCurrency(summary.avgTransactionValue)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Transactions</span>
                  <span className="font-medium">1,357</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Active Clients</span>
                  <span className="font-medium">320</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Active Tutors</span>
                  <span className="font-medium">85</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Collection Rate</span>
                  <span className="font-medium">94.2%</span>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <h3 className="font-semibold text-gray-900 mb-4">Revenue by Service Type</h3>
              <ResponsiveContainer width="100%" height={200}>
                <RechartsPieChart>
                  <Pie
                    data={serviceTypeRevenue}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={80}
                    fill="#8884d8"
                    paddingAngle={5}
                    dataKey="revenue"
                  >
                    {serviceTypeRevenue.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value: number) => formatCurrency(value)} />
                </RechartsPieChart>
              </ResponsiveContainer>
              <div className="mt-4 space-y-2">
                {serviceTypeRevenue.map((item, index) => (
                  <div key={item.type} className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full" style={{ backgroundColor: COLORS[index] }}></div>
                      <span>{item.type}</span>
                    </div>
                    <span className="font-medium">{item.percentage}%</span>
                  </div>
                ))}
              </div>
            </Card>

            <Card className="p-6">
              <h3 className="font-semibold text-gray-900 mb-4">Top Revenue Sources</h3>
              <div className="space-y-3">
                {revenueBreakdown.map((source) => (
                  <div key={source.source} className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">{source.source}</span>
                      <span className="font-medium">{formatCurrency(source.amount)}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-accent-red h-2 rounded-full"
                        style={{ width: `${source.percentage}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="revenue" className="space-y-6 mt-6">
          {/* Revenue Breakdown */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="p-6">
              <h3 className="font-semibold text-gray-900 mb-4">Revenue by Subject</h3>
              <ResponsiveContainer width="100%" height={300}>
                <RechartsBarChart data={subjectRevenue}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="subject" />
                  <YAxis tickFormatter={(value) => `$${value / 1000}k`} />
                  <Tooltip formatter={(value: number) => formatCurrency(value)} />
                  <Bar dataKey="revenue" fill="#dc2626" radius={[8, 8, 0, 0]} />
                </RechartsBarChart>
              </ResponsiveContainer>
            </Card>

            <Card className="p-6">
              <h3 className="font-semibold text-gray-900 mb-4">Revenue Streams</h3>
              <div className="space-y-4">
                {revenueBreakdown.map((stream) => (
                  <div key={stream.source} className="border-b pb-3 last:border-0">
                    <div className="flex justify-between mb-1">
                      <span className="font-medium">{stream.source}</span>
                      <span className="font-semibold">{formatCurrency(stream.amount)}</span>
                    </div>
                    <div className="flex justify-between text-sm text-gray-600">
                      <span>{stream.transactions} transactions</span>
                      <span>{stream.percentage}% of total</span>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </div>

          {/* Monthly Recurring Revenue */}
          <Card className="p-6">
            <h3 className="font-semibold text-gray-900 mb-4">Monthly Recurring Revenue (MRR)</h3>
            <div className="grid grid-cols-4 gap-4 mb-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <p className="text-sm text-gray-600">New MRR</p>
                <p className="text-xl font-bold text-blue-600">{formatCurrency(8500)}</p>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <p className="text-sm text-gray-600">Expansion MRR</p>
                <p className="text-xl font-bold text-green-600">{formatCurrency(3200)}</p>
              </div>
              <div className="text-center p-4 bg-red-50 rounded-lg">
                <p className="text-sm text-gray-600">Churned MRR</p>
                <p className="text-xl font-bold text-red-600">{formatCurrency(1800)}</p>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <p className="text-sm text-gray-600">Net MRR</p>
                <p className="text-xl font-bold text-purple-600">{formatCurrency(9900)}</p>
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="expenses" className="space-y-6 mt-6">
          {/* Expense Breakdown */}
          <Card className="p-6">
            <h3 className="font-semibold text-gray-900 mb-4">Expense Categories</h3>
            <ResponsiveContainer width="100%" height={300}>
              <RechartsPieChart>
                <Pie
                  data={expenseBreakdown}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ category, percentage }) => `${category}: ${percentage}%`}
                  outerRadius={100}
                  fill="#8884d8"
                  dataKey="amount"
                >
                  {expenseBreakdown.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value: number) => formatCurrency(value)} />
              </RechartsPieChart>
            </ResponsiveContainer>
          </Card>

          {/* Expense Details */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="p-6">
              <h3 className="font-semibold text-gray-900 mb-4">Expense Details</h3>
              <div className="space-y-3">
                {expenseBreakdown.map((expense) => (
                  <div key={expense.category} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium">{expense.category}</p>
                      <p className="text-sm text-gray-600">{expense.items} items</p>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold">{formatCurrency(expense.amount)}</p>
                      <p className="text-sm text-gray-600">{expense.percentage}%</p>
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            <Card className="p-6">
              <h3 className="font-semibold text-gray-900 mb-4">Cost Optimization</h3>
              <div className="space-y-4">
                <div className="p-4 bg-green-50 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <TrendingDown className="w-5 h-5 text-green-600" />
                    <span className="font-medium text-green-900">Payment Processing</span>
                  </div>
                  <p className="text-sm text-green-800">Saved {formatCurrency(450)} by negotiating lower rates</p>
                </div>
                <div className="p-4 bg-yellow-50 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Clock className="w-5 h-5 text-yellow-600" />
                    <span className="font-medium text-yellow-900">SMS Optimization</span>
                  </div>
                  <p className="text-sm text-yellow-800">Consider batching messages to save {formatCurrency(200)}/month</p>
                </div>
                <div className="p-4 bg-blue-50 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Users className="w-5 h-5 text-blue-600" />
                    <span className="font-medium text-blue-900">Tutor Efficiency</span>
                  </div>
                  <p className="text-sm text-blue-800">Group sessions increased revenue by 15%</p>
                </div>
              </div>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="trends" className="space-y-6 mt-6">
          {/* Profit Trend */}
          <Card className="p-6">
            <h3 className="font-semibold text-gray-900 mb-4">Profit Trend</h3>
            <ResponsiveContainer width="100%" height={300}>
              <RechartsLineChart data={monthlyRevenue}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis tickFormatter={(value) => `$${value / 1000}k`} />
                <Tooltip formatter={(value: number) => formatCurrency(value)} />
                <Legend />
                <Line 
                  type="monotone" 
                  dataKey="profit" 
                  stroke="#3b82f6" 
                  strokeWidth={3}
                  dot={{ fill: '#3b82f6', r: 6 }}
                  name="Net Profit"
                />
              </RechartsLineChart>
            </ResponsiveContainer>
          </Card>

          {/* Year over Year Comparison */}
          <Card className="p-6">
            <h3 className="font-semibold text-gray-900 mb-4">Year over Year Comparison</h3>
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center p-4 border rounded-lg">
                <p className="text-sm text-gray-600">2024 YTD</p>
                <p className="text-2xl font-bold">{formatCurrency(1456780)}</p>
                <p className="text-sm text-green-600 mt-1">↑ 23.5%</p>
              </div>
              <div className="text-center p-4 border rounded-lg">
                <p className="text-sm text-gray-600">2023 YTD</p>
                <p className="text-2xl font-bold">{formatCurrency(1179450)}</p>
                <p className="text-sm text-gray-600 mt-1">Previous Year</p>
              </div>
              <div className="text-center p-4 border rounded-lg">
                <p className="text-sm text-gray-600">Growth</p>
                <p className="text-2xl font-bold text-green-600">{formatCurrency(277330)}</p>
                <p className="text-sm text-gray-600 mt-1">Increase</p>
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="forecasts" className="space-y-6 mt-6">
          <Card className="p-6">
            <h3 className="font-semibold text-gray-900 mb-4">Revenue Forecast</h3>
            <div className="mb-4 p-4 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-800">
                Based on current trends, projected revenue for next month: <span className="font-bold">{formatCurrency(168000)}</span>
              </p>
            </div>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={[
                ...monthlyRevenue,
                { month: 'Dec', revenue: 168000, expenses: 92000, profit: 76000, forecast: true }
              ]}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis tickFormatter={(value) => `$${value / 1000}k`} />
                <Tooltip formatter={(value: number) => formatCurrency(value)} />
                <Area 
                  type="monotone" 
                  dataKey="revenue" 
                  stroke="#10b981" 
                  fill="#10b981" 
                  fillOpacity={0.6}
                  strokeDasharray={(data: any) => data.forecast ? "5 5" : "0"}
                />
              </AreaChart>
            </ResponsiveContainer>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default EnhancedFinancialReport;