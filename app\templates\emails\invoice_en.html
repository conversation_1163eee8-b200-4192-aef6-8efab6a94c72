<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice #{{ invoice_number }} - {{ company_name }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 700px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #059669 0%, #**********%);
            padding: 40px 30px;
            color: white;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .company-info h1 {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .invoice-info {
            text-align: right;
        }
        
        .invoice-number {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .invoice-date {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .billing-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }
        
        .billing-section h3 {
            color: #059669;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
            border-bottom: 2px solid #d1fae5;
            padding-bottom: 8px;
        }
        
        .billing-section p {
            margin-bottom: 8px;
            color: #374151;
        }
        
        .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin: 32px 0;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .invoice-table th {
            background-color: #f0fdf4;
            color: #047857;
            font-weight: 600;
            padding: 16px;
            text-align: left;
            font-size: 14px;
        }
        
        .invoice-table td {
            padding: 16px;
            border-bottom: 1px solid #e5e7eb;
            color: #374151;
        }
        
        .invoice-table tr:last-child td {
            border-bottom: none;
        }
        
        .amount-column {
            text-align: right;
            font-weight: 600;
        }
        
        .totals-section {
            background-color: #f9fafb;
            border-radius: 8px;
            padding: 24px;
            margin: 32px 0;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
            padding: 8px 0;
        }
        
        .total-row.subtotal {
            color: #6b7280;
        }
        
        .total-row.tax {
            color: #6b7280;
            font-size: 14px;
        }
        
        .total-row.final {
            border-top: 2px solid #d1d5db;
            padding-top: 16px;
            font-size: 18px;
            font-weight: 600;
            color: #047857;
        }
        
        .payment-info {
            background-color: #ecfdf5;
            border-left: 4px solid #10b981;
            padding: 20px;
            margin: 32px 0;
            border-radius: 4px;
        }
        
        .payment-info h3 {
            color: #065f46;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 12px;
        }
        
        .payment-info p {
            color: #047857;
            margin-bottom: 8px;
        }
        
        .action-buttons {
            text-align: center;
            margin: 32px 0;
            display: flex;
            gap: 16px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            display: inline-block;
            text-decoration: none;
            padding: 14px 24px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            transition: transform 0.2s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #059669 0%, #**********%);
            color: white;
        }
        
        .btn-secondary {
            background-color: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .footer {
            background-color: #f8f9fa;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e2e8f0;
        }
        
        .footer p {
            color: #6b7280;
            font-size: 14px;
            margin-bottom: 8px;
        }
        
        .footer a {
            color: #059669;
            text-decoration: none;
        }
        
        .footer a:hover {
            text-decoration: underline;
        }
        
        @media only screen and (max-width: 600px) {
            .container {
                margin: 0;
                border-radius: 0;
            }
            
            .header, .content, .footer {
                padding: 24px 20px;
            }
            
            .header-content {
                flex-direction: column;
                text-align: center;
            }
            
            .invoice-info {
                text-align: center;
            }
            
            .billing-details {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .invoice-table {
                font-size: 14px;
            }
            
            .invoice-table th,
            .invoice-table td {
                padding: 12px 8px;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-content">
                <div class="company-info">
                    <h1>{{ company_name }}</h1>
                    <p>Professional Tutoring Services</p>
                </div>
                <div class="invoice-info">
                    <div class="invoice-number">Invoice #{{ invoice_number }}</div>
                    <div class="invoice-date">{{ invoice_date }}</div>
                </div>
            </div>
        </div>
        
        <div class="content">
            <div class="billing-details">
                <div class="billing-section">
                    <h3>Bill To:</h3>
                    <p><strong>{{ client_name }}</strong></p>
                    <p>{{ client_email }}</p>
                    {% if client_phone %}<p>{{ client_phone }}</p>{% endif %}
                    {% if client_address %}
                    <p>{{ client_address.street }}</p>
                    <p>{{ client_address.city }}, {{ client_address.province }} {{ client_address.postal_code }}</p>
                    {% endif %}
                </div>
                
                <div class="billing-section">
                    <h3>Invoice Details:</h3>
                    <p><strong>Due Date:</strong> {{ due_date }}</p>
                    <p><strong>Payment Terms:</strong> {{ payment_terms | default("Net 30") }}</p>
                    {% if payment_method %}<p><strong>Payment Method:</strong> {{ payment_method }}</p>{% endif %}
                </div>
            </div>
            
            <table class="invoice-table">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Description</th>
                        <th>Tutor</th>
                        <th>Duration</th>
                        <th>Rate</th>
                        <th class="amount-column">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in invoice_items %}
                    <tr>
                        <td>{{ item.date }}</td>
                        <td>
                            <strong>{{ item.subject }}</strong>
                            {% if item.description %}<br><small>{{ item.description }}</small>{% endif %}
                        </td>
                        <td>{{ item.tutor_name }}</td>
                        <td>{{ item.duration }} min</td>
                        <td>${{ "%.2f"|format(item.rate) }}</td>
                        <td class="amount-column">${{ "%.2f"|format(item.amount) }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            
            <div class="totals-section">
                <div class="total-row subtotal">
                    <span>Subtotal:</span>
                    <span>${{ "%.2f"|format(subtotal) }}</span>
                </div>
                {% if gst_amount %}
                <div class="total-row tax">
                    <span>GST ({{ gst_rate }}%):</span>
                    <span>${{ "%.2f"|format(gst_amount) }}</span>
                </div>
                {% endif %}
                {% if qst_amount %}
                <div class="total-row tax">
                    <span>QST ({{ qst_rate }}%):</span>
                    <span>${{ "%.2f"|format(qst_amount) }}</span>
                </div>
                {% endif %}
                <div class="total-row final">
                    <span>Total Amount:</span>
                    <span>${{ "%.2f"|format(total_amount) }} CAD</span>
                </div>
            </div>
            
            {% if payment_status == "pending" %}
            <div class="payment-info">
                <h3>💳 Payment Information</h3>
                <p><strong>Amount Due:</strong> ${{ "%.2f"|format(total_amount) }} CAD</p>
                <p><strong>Due Date:</strong> {{ due_date }}</p>
                <p>Please pay by the due date to avoid late fees.</p>
            </div>
            
            <div class="action-buttons">
                <a href="{{ payment_link }}" class="btn btn-primary">Pay Invoice Online</a>
                <a href="{{ download_link }}" class="btn btn-secondary">Download PDF</a>
            </div>
            {% else %}
            <div class="payment-info">
                <h3>✅ Payment Complete</h3>
                <p><strong>Payment Date:</strong> {{ payment_date }}</p>
                <p><strong>Payment Method:</strong> {{ payment_method }}</p>
                <p>Thank you for your payment!</p>
            </div>
            
            <div class="action-buttons">
                <a href="{{ receipt_link }}" class="btn btn-primary">View Receipt</a>
                <a href="{{ download_link }}" class="btn btn-secondary">Download PDF</a>
            </div>
            {% endif %}
            
            <div style="margin: 32px 0; padding: 20px; background-color: #f9fafb; border-radius: 8px;">
                <h4 style="color: #374151; margin-bottom: 12px;">Questions about this invoice?</h4>
                <p style="color: #6b7280; font-size: 14px; line-height: 1.5;">
                    Contact our billing department at 
                    <a href="mailto:<EMAIL>" style="color: #059669;"><EMAIL></a> 
                    or call {{ support_phone }}.
                </p>
            </div>
        </div>
        
        <div class="footer">
            <p>This invoice was sent to {{ client_email }}</p>
            <p>For billing inquiries, contact <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <p>&copy; {{ year }} {{ company_name }}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>