/** @type {import('tailwindcss').Config} */
import { designTokens } from './src/styles/design-tokens';

export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // Import colors from design tokens
        primary: designTokens.colors.primary,
        secondary: designTokens.colors.secondary,
        accent: {
          red: designTokens.colors.accent.red.DEFAULT,
          'red-light': designTokens.colors.accent.red.light,
          'red-dark': designTokens.colors.accent.red.dark,
          highlight: designTokens.colors.accent.highlight,
          gold: designTokens.colors.accent.gold,
        },
        semantic: designTokens.colors.semantic,
        text: designTokens.colors.text,
        background: designTokens.colors.background,
        border: designTokens.colors.border,
      },
      fontFamily: {
        sans: ['system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif'],
      },
      borderRadius: {
        'none': designTokens.radius.none,
        'sm': designTokens.radius.sm,
        'md': designTokens.radius.md,
        'lg': designTokens.radius.lg,
        'xl': designTokens.radius.xl,
        'full': designTokens.radius.full,
        // Legacy names for backward compatibility
        'soft': designTokens.radius.sm,
        'medium': designTokens.radius.md,
        'large': designTokens.radius.lg,
      },
      boxShadow: {
        'none': designTokens.shadows.none,
        'subtle': designTokens.shadows.subtle,
        'soft': designTokens.shadows.soft,
        'elevated': designTokens.shadows.elevated,
        'focus': designTokens.shadows.focus,
        'error': designTokens.shadows.error,
        // Legacy names for backward compatibility
        'medium': designTokens.shadows.soft,
        'large': designTokens.shadows.elevated,
      },
      spacing: designTokens.spacing,
      fontSize: designTokens.typography.fontSize,
      fontWeight: designTokens.typography.fontWeight,
      lineHeight: designTokens.typography.lineHeight,
      transitionDuration: designTokens.transitions.duration,
      transitionTimingFunction: {
        ...designTokens.transitions.easing,
        'smooth': 'cubic-bezier(0.4, 0, 0.2, 1)',
      },
      zIndex: designTokens.zIndex,
      screens: designTokens.breakpoints,
      animation: {
        'slide-in': 'slideIn 200ms ease-out',
        'slide-out': 'slideOut 200ms ease-in',
        'fade-in': 'fadeIn 200ms ease-out',
        'fade-out': 'fadeOut 200ms ease-in',
        'scale-in': 'scaleIn 200ms ease-out',
        'spin': 'spin 1s linear infinite',
      },
      keyframes: {
        slideIn: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(0)' },
        },
        slideOut: {
          '0%': { transform: 'translateX(0)' },
          '100%': { transform: 'translateX(-100%)' },
        },
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        fadeOut: {
          '0%': { opacity: '1' },
          '100%': { opacity: '0' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
        spin: {
          'to': { transform: 'rotate(360deg)' },
        },
      },
    },
  },
  plugins: [],
}