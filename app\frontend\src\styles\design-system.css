/* TutorAide Design System - Apple-Inspired */

:root {
  /* Primary Colors */
  --color-primary: #1f2937;        /* Black/Dark Gray */
  --color-secondary: #f9fafb;      /* Light Gray */
  --color-accent: #f87171;         /* Pastel Red */
  --color-highlight: #dc2626;      /* Red */
  --color-gold: #fbbf24;           /* Gold accent */
  
  /* Semantic Colors */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;
  
  /* Text Colors */
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-muted: #9ca3af;
  --text-inverse: #ffffff;
  
  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;
  --bg-inverse: #1f2937;
  
  /* Border Colors */
  --border-primary: #e5e7eb;
  --border-secondary: #d1d5db;
  --border-focus: var(--color-accent);
  
  /* Rounded & Spacing */
  --radius-sm: 8px;               /* Small elements */
  --radius-md: 12px;              /* Medium elements */
  --radius-lg: 16px;              /* Large elements */
  --radius-xl: 24px;              /* Extra large elements */
  --radius-full: 9999px;          /* Pills/oval shapes */
  
  /* Shadows (Apple-inspired) */
  --shadow-subtle: 0 1px 3px rgba(0, 0, 0, 0.05);
  --shadow-soft: 0 4px 12px rgba(0, 0, 0, 0.08);
  --shadow-elevated: 0 8px 25px rgba(0, 0, 0, 0.12);
  --shadow-focus: 0 0 0 3px rgba(248, 113, 113, 0.1);
  
  /* Spacing (consistent rhythm) */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 16px;
  --space-lg: 24px;
  --space-xl: 32px;
  --space-2xl: 48px;
  --space-3xl: 64px;
  
  /* Typography */
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  
  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  
  /* Font Weights */
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  
  /* Transitions */
  --transition-fast: 150ms ease;
  --transition-base: 200ms ease;
  --transition-slow: 300ms ease;
  
  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* Dark mode variables (future-proofing) */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #111827;
    --bg-secondary: #1f2937;
    --bg-tertiary: #374151;
    --text-primary: #f9fafb;
    --text-secondary: #e5e7eb;
    --text-muted: #9ca3af;
    --border-primary: #374151;
    --border-secondary: #4b5563;
  }
}

/* Base Resets */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Smooth Scrolling */
html {
  scroll-behavior: smooth;
}

/* Base Typography */
body {
  font-family: var(--font-sans);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Focus Styles */
*:focus {
  outline: none;
}

*:focus-visible {
  outline: 2px solid var(--color-accent);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* Selection */
::selection {
  background-color: var(--color-accent);
  color: white;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
  background: var(--border-primary);
  border-radius: var(--radius-full);
  border: 3px solid var(--bg-secondary);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}