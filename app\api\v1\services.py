"""
API endpoints for service catalog and rate management.
"""

import logging
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
import asyncpg

from app.core.dependencies import get_database, get_current_active_user
from app.core.auth_decorators import require_roles as require_role
from app.models.user_models import UserRoleType
from app.models.service_models import (
    SubjectArea, ServiceType, ServiceLevel, PackageType,
    CreateServiceCatalogRequest, UpdateServiceCatalogRequest, ServiceCatalogResponse,
    CreateTutorServiceRateRequest, UpdateTutorServiceRateRequest, TutorServiceRateResponse,
    CreateServicePackageRequest, UpdateServicePackageRequest, ServicePackageResponse,
    ServiceSearchRequest, ServiceRecommendationRequest,
    PricingCalculationRequest, PricingCalculationResponse,
    LocationPreference, DistanceCalculationRequest, DistanceCalculationResponse,
    TutorServiceSummary, ServiceCatalogSummary
)
from app.services.service_catalog_service import (
    ServiceCatalogService, TutorServiceRateService,
    ServicePackageService, ServicePricingService,
    LocationService, ServiceRecommendationService
)
from app.core.exceptions import ValidationError, ResourceNotFoundError, BusinessLogicError

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/services", tags=["services"])


# Service Catalog Endpoints
@router.post("/catalog", response_model=ServiceCatalogResponse, status_code=status.HTTP_201_CREATED)
@require_role([UserRoleType.MANAGER])
async def create_service(
    request: CreateServiceCatalogRequest,
    current_user=Depends(get_current_active_user),
    db: asyncpg.Connection = Depends(get_database)
):
    """Create a new service in the catalog. Manager only."""
    try:
        service = ServiceCatalogService(db)
        return await service.create_service(request, current_user.user_id)
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating service: {e}")
        raise HTTPException(status_code=500, detail="Failed to create service")


@router.get("/catalog")
async def search_services(
    subject_area: Optional[str] = Query(None),
    service_type: Optional[str] = Query(None),
    service_level: Optional[str] = Query(None),
    is_active_only: bool = Query(True),
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    db: asyncpg.Connection = Depends(get_database)
):
    """Search services in the catalog. Public endpoint."""
    try:
        # Simple direct database query instead of complex service layer
        where_conditions = []
        params = []
        param_count = 0

        if is_active_only:
            param_count += 1
            where_conditions.append(f"is_active = ${param_count}")
            params.append(True)

        if subject_area:
            param_count += 1
            where_conditions.append(f"subject_area = ${param_count}")
            params.append(subject_area)

        if service_type:
            param_count += 1
            where_conditions.append(f"service_type = ${param_count}")
            params.append(service_type)

        if service_level:
            param_count += 1
            where_conditions.append(f"service_level = ${param_count}")
            params.append(service_level)

        where_clause = "WHERE " + " AND ".join(where_conditions) if where_conditions else ""

        # Calculate offset for pagination
        offset = (page - 1) * page_size
        param_count += 1
        limit_param = f"${param_count}"
        params.append(page_size)
        param_count += 1
        offset_param = f"${param_count}"
        params.append(offset)

        query = f"""
            SELECT
                service_id, service_name, subject_area, service_type, service_level,
                description, is_active, created_at, updated_at
            FROM service_catalog
            {where_clause}
            ORDER BY service_id
            LIMIT {limit_param} OFFSET {offset_param}
        """

        services = await db.fetch(query, *params)

        # Convert to response format
        result = []
        for service in services:
            result.append({
                "service_id": service["service_id"],
                "name": service["service_name"],
                "service_name": service["service_name"],
                "description": service["description"],
                "subject_area": service["subject_area"],
                "service_type": service["service_type"],
                "service_level": service["service_level"],
                "grade_levels": [service["service_level"]],
                "duration_options": [60],
                "frequency_options": ["weekly"],
                "is_active": service["is_active"],
                "requires_location": service["service_type"] != "online",
                "max_students": 1,
                "min_students": 1,
                "default_client_rate": 50.0,
                "default_tutor_rate": 40.0,
                "tags": [service["subject_area"], service["service_level"]],
                "created_at": service["created_at"].isoformat() if service["created_at"] else None,
                "updated_at": service["updated_at"].isoformat() if service["updated_at"] else None
            })

        return result

    except Exception as e:
        logger.error(f"Error searching services: {e}")
        raise HTTPException(status_code=500, detail="Failed to search services")


@router.get("/")
async def get_services(
    subject_area: Optional[str] = Query(None),
    service_type: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    is_active: bool = Query(True),
    page: int = Query(1, ge=1),
    limit: int = Query(12, ge=1, le=100),
    db: asyncpg.Connection = Depends(get_database)
):
    """Get services with filters. Public endpoint for frontend."""
    try:
        # Build query conditions
        where_conditions = []
        params = []
        param_count = 0

        if is_active:
            param_count += 1
            where_conditions.append(f"is_active = ${param_count}")
            params.append(True)

        if subject_area:
            param_count += 1
            where_conditions.append(f"subject_area = ${param_count}")
            params.append(subject_area)

        if service_type:
            param_count += 1
            where_conditions.append(f"service_type = ${param_count}")
            params.append(service_type)

        if search:
            param_count += 1
            where_conditions.append(f"(service_name ILIKE ${param_count} OR description ILIKE ${param_count})")
            params.append(f"%{search}%")

        where_clause = "WHERE " + " AND ".join(where_conditions) if where_conditions else ""

        # Get total count
        count_query = f"SELECT COUNT(*) FROM service_catalog {where_clause}"
        total = await db.fetchval(count_query, *params)

        # Get paginated results
        offset = (page - 1) * limit
        param_count += 1
        limit_param = f"${param_count}"
        params.append(limit)
        param_count += 1
        offset_param = f"${param_count}"
        params.append(offset)

        query = f"""
            SELECT
                service_id, service_name, subject_area, service_type, service_level,
                description, is_active, created_at, updated_at
            FROM service_catalog
            {where_clause}
            ORDER BY service_id
            LIMIT {limit_param} OFFSET {offset_param}
        """

        services = await db.fetch(query, *params)

        # Convert to frontend format
        items = []
        for service in services:
            items.append({
                "service_id": service["service_id"],
                "name": service["service_name"],
                "description": service["description"],
                "subject_area": service["subject_area"],
                "service_type": service["service_type"],
                "grade_levels": [service["service_level"]],
                "duration_options": [60],
                "frequency_options": ["weekly"],
                "is_active": service["is_active"],
                "requires_location": service["service_type"] != "online",
                "max_students": 1,
                "min_students": 1,
                "default_client_rate": 50.0,
                "default_tutor_rate": 40.0,
                "tags": [service["subject_area"], service["service_level"]],
                "created_at": service["created_at"].isoformat() if service["created_at"] else None,
                "updated_at": service["updated_at"].isoformat() if service["updated_at"] else None
            })

        return {
            "items": items,
            "total": total,
            "page": page,
            "page_size": limit,
            "total_pages": (total + limit - 1) // limit
        }

    except Exception as e:
        logger.error(f"Error getting services: {e}")
        raise HTTPException(status_code=500, detail="Failed to get services")


@router.get("/catalog/summary", response_model=ServiceCatalogSummary)
async def get_catalog_summary(
    current_user=Depends(get_current_active_user),
    db: asyncpg.Connection = Depends(get_database)
):
    """Get service catalog summary statistics. Requires authentication."""
    try:
        service = ServiceCatalogService(db)
        return await service.get_catalog_summary()
    except Exception as e:
        logger.error(f"Error getting catalog summary: {e}")
        raise HTTPException(status_code=500, detail="Failed to get catalog summary")


@router.get("/catalog/{service_catalog_id}", response_model=ServiceCatalogResponse)
async def get_service(
    service_catalog_id: int,
    db: asyncpg.Connection = Depends(get_database)
):
    """Get a specific service by ID. Public endpoint."""
    try:
        service = ServiceCatalogService(db)
        catalog_repo = service.catalog_repo
        
        result = await catalog_repo.get_by_id(service_catalog_id)
        if not result:
            raise HTTPException(status_code=404, detail="Service not found")
        
        return result
        
    except ResourceNotFoundError:
        raise HTTPException(status_code=404, detail="Service not found")
    except Exception as e:
        logger.error(f"Error getting service: {e}")
        raise HTTPException(status_code=500, detail="Failed to get service")


@router.put("/catalog/{service_catalog_id}", response_model=ServiceCatalogResponse)
@require_role([UserRoleType.MANAGER])
async def update_service(
    service_catalog_id: int,
    request: UpdateServiceCatalogRequest,
    current_user=Depends(get_current_active_user),
    db: asyncpg.Connection = Depends(get_database)
):
    """Update a service in the catalog. Manager only."""
    try:
        service = ServiceCatalogService(db)
        return await service.update_service(service_catalog_id, request, current_user.user_id)
    except ResourceNotFoundError:
        raise HTTPException(status_code=404, detail="Service not found")
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error updating service: {e}")
        raise HTTPException(status_code=500, detail="Failed to update service")


# Tutor Service Rate Endpoints
@router.post("/rates", response_model=TutorServiceRateResponse, status_code=status.HTTP_201_CREATED)
@require_role([UserRoleType.MANAGER, UserRoleType.TUTOR])
async def create_tutor_rate(
    request: CreateTutorServiceRateRequest,
    current_user=Depends(get_current_active_user),
    db: asyncpg.Connection = Depends(get_database)
):
    """Create a tutor service rate. Tutors can only create their own rates."""
    try:
        # Tutors can only create their own rates
        if current_user.role == UserRoleType.TUTOR:
            # Get tutor_id from user
            # This would need to be implemented based on your user-tutor relationship
            # For now, we'll assume tutor_id is stored in user context
            if request.tutor_id != current_user.tutor_id:
                raise HTTPException(status_code=403, detail="Cannot create rates for other tutors")
        
        service = TutorServiceRateService(db)
        return await service.create_tutor_rate(request, current_user.user_id)
        
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except ResourceNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating tutor rate: {e}")
        raise HTTPException(status_code=500, detail="Failed to create tutor rate")


@router.get("/rates/tutor/{tutor_id}", response_model=List[TutorServiceRateResponse])
async def get_tutor_rates(
    tutor_id: int,
    service_type: Optional[ServiceType] = Query(None),
    is_available_only: bool = Query(True),
    db: asyncpg.Connection = Depends(get_database)
):
    """Get all service rates for a tutor. Public endpoint."""
    try:
        service = TutorServiceRateService(db)
        return await service.get_tutor_services(tutor_id, service_type, is_available_only)
    except Exception as e:
        logger.error(f"Error getting tutor rates: {e}")
        raise HTTPException(status_code=500, detail="Failed to get tutor rates")


@router.get("/rates/tutor/{tutor_id}/summary", response_model=TutorServiceSummary)
async def get_tutor_service_summary(
    tutor_id: int,
    db: asyncpg.Connection = Depends(get_database)
):
    """Get summary of all services offered by a tutor. Public endpoint."""
    try:
        service = TutorServiceRateService(db)
        return await service.get_tutor_service_summary(tutor_id)
    except Exception as e:
        logger.error(f"Error getting tutor service summary: {e}")
        raise HTTPException(status_code=500, detail="Failed to get tutor service summary")


@router.put("/rates/{tutor_service_rate_id}", response_model=TutorServiceRateResponse)
@require_role([UserRoleType.MANAGER, UserRoleType.TUTOR])
async def update_tutor_rate(
    tutor_service_rate_id: int,
    request: UpdateTutorServiceRateRequest,
    current_user=Depends(get_current_active_user),
    db: asyncpg.Connection = Depends(get_database)
):
    """Update a tutor service rate. Tutors can only update their own rates."""
    try:
        service = TutorServiceRateService(db)
        
        # Get existing rate to check ownership
        existing_rate = await service.rate_repo.get_tutor_rate_with_service(tutor_service_rate_id)
        if not existing_rate:
            raise HTTPException(status_code=404, detail="Tutor rate not found")
        
        # Check permissions
        if current_user.role == UserRoleType.TUTOR and existing_rate.tutor_id != current_user.tutor_id:
            raise HTTPException(status_code=403, detail="Cannot update rates for other tutors")
        
        return await service.update_tutor_rate(tutor_service_rate_id, request, current_user.user_id)
        
    except ResourceNotFoundError:
        raise HTTPException(status_code=404, detail="Tutor rate not found")
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error updating tutor rate: {e}")
        raise HTTPException(status_code=500, detail="Failed to update tutor rate")


@router.get("/rates/find-tutors", response_model=List[dict])
async def find_available_tutors(
    service_catalog_id: int = Query(..., gt=0),
    service_type: ServiceType = Query(...),
    max_hourly_rate: Optional[float] = Query(None, gt=0),
    location_postal_code: Optional[str] = Query(None, pattern=r'^[A-Z]\d[A-Z]\s?\d[A-Z]\d$'),
    max_distance_km: Optional[int] = Query(None, gt=0, le=50),
    db: asyncpg.Connection = Depends(get_database)
):
    """Find available tutors for a specific service. Public endpoint."""
    try:
        service = TutorServiceRateService(db)
        return await service.find_available_tutors(
            service_catalog_id,
            service_type,
            max_hourly_rate,
            location_postal_code,
            max_distance_km
        )
    except Exception as e:
        logger.error(f"Error finding tutors: {e}")
        raise HTTPException(status_code=500, detail="Failed to find tutors")


# Service Package Endpoints
@router.post("/packages", response_model=ServicePackageResponse, status_code=status.HTTP_201_CREATED)
@require_role([UserRoleType.MANAGER])
async def create_package(
    request: CreateServicePackageRequest,
    current_user=Depends(get_current_active_user),
    db: asyncpg.Connection = Depends(get_database)
):
    """Create a new service package. Manager only."""
    try:
        service = ServicePackageService(db)
        return await service.create_package(request, current_user.user_id)
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating package: {e}")
        raise HTTPException(status_code=500, detail="Failed to create package")


@router.get("/packages", response_model=List[ServicePackageResponse])
async def get_packages(
    package_type: Optional[PackageType] = Query(None),
    subject_area: Optional[SubjectArea] = Query(None),
    service_type: Optional[ServiceType] = Query(None),
    db: asyncpg.Connection = Depends(get_database)
):
    """Get active service packages. Public endpoint."""
    try:
        service = ServicePackageService(db)
        return await service.get_active_packages(package_type, subject_area, service_type)
    except Exception as e:
        logger.error(f"Error getting packages: {e}")
        raise HTTPException(status_code=500, detail="Failed to get packages")


@router.put("/packages/{service_package_id}", response_model=ServicePackageResponse)
@require_role([UserRoleType.MANAGER])
async def update_package(
    service_package_id: int,
    request: UpdateServicePackageRequest,
    current_user=Depends(get_current_active_user),
    db: asyncpg.Connection = Depends(get_database)
):
    """Update a service package. Manager only."""
    try:
        service = ServicePackageService(db)
        return await service.update_package(service_package_id, request, current_user.user_id)
    except ResourceNotFoundError:
        raise HTTPException(status_code=404, detail="Package not found")
    except BusinessLogicError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error updating package: {e}")
        raise HTTPException(status_code=500, detail="Failed to update package")


@router.post("/packages/{service_package_id}/purchase", response_model=dict)
@require_role([UserRoleType.MANAGER, UserRoleType.CLIENT])
async def purchase_package(
    service_package_id: int,
    client_id: int = Query(..., gt=0),
    dependant_id: Optional[int] = Query(None, gt=0),
    current_user=Depends(get_current_active_user),
    db: asyncpg.Connection = Depends(get_database)
):
    """Purchase a package for a client. Clients can only purchase for themselves."""
    try:
        # Clients can only purchase for themselves
        if current_user.role == UserRoleType.CLIENT and client_id != current_user.client_id:
            raise HTTPException(status_code=403, detail="Cannot purchase packages for other clients")
        
        service = ServicePackageService(db)
        return await service.purchase_package(
            service_package_id,
            client_id,
            current_user.user_id,
            dependant_id
        )
        
    except ResourceNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error purchasing package: {e}")
        raise HTTPException(status_code=500, detail="Failed to purchase package")


# Pricing Endpoints
@router.post("/pricing/calculate", response_model=PricingCalculationResponse)
async def calculate_pricing(
    request: PricingCalculationRequest,
    current_user=Depends(get_current_active_user),
    db: asyncpg.Connection = Depends(get_database)
):
    """Calculate pricing for a service. Requires authentication."""
    try:
        service = ServicePricingService(db)
        return await service.calculate_pricing(request)
    except ResourceNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error calculating pricing: {e}")
        raise HTTPException(status_code=500, detail="Failed to calculate pricing")


# Location Endpoints
@router.post("/locations/preferences", response_model=dict)
async def save_location_preference(
    entity_type: str = Query(..., pattern=r'^(user|tutor|client)$'),
    entity_id: int = Query(..., gt=0),
    location: LocationPreference = ...,
    current_user=Depends(get_current_active_user),
    db: asyncpg.Connection = Depends(get_database)
):
    """Save location preferences. Users can only save their own preferences."""
    try:
        # Check permissions
        if entity_type == 'user' and entity_id != current_user.user_id:
            raise HTTPException(status_code=403, detail="Cannot set preferences for other users")
        elif entity_type == 'tutor' and current_user.role == UserRoleType.TUTOR and entity_id != current_user.tutor_id:
            raise HTTPException(status_code=403, detail="Cannot set preferences for other tutors")
        elif entity_type == 'client' and current_user.role == UserRoleType.CLIENT and entity_id != current_user.client_id:
            raise HTTPException(status_code=403, detail="Cannot set preferences for other clients")
        
        service = LocationService(db)
        return await service.save_location_preference(entity_type, entity_id, location)
        
    except Exception as e:
        logger.error(f"Error saving location preference: {e}")
        raise HTTPException(status_code=500, detail="Failed to save location preference")


@router.post("/locations/distance", response_model=DistanceCalculationResponse)
async def calculate_distance(
    request: DistanceCalculationRequest,
    db: asyncpg.Connection = Depends(get_database)
):
    """Calculate distance between two postal codes. Public endpoint."""
    try:
        service = LocationService(db)
        return await service.calculate_distance(request)
    except BusinessLogicError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error calculating distance: {e}")
        raise HTTPException(status_code=500, detail="Failed to calculate distance")


# Recommendation Endpoints
@router.post("/recommendations", response_model=List[dict])
async def get_service_recommendations(
    request: ServiceRecommendationRequest,
    current_user=Depends(get_current_active_user),
    db: asyncpg.Connection = Depends(get_database)
):
    """Get personalized service recommendations. Requires authentication."""
    try:
        # Clients can only get recommendations for themselves or their dependants
        if current_user.role == UserRoleType.CLIENT:
            if request.client_id != current_user.client_id:
                raise HTTPException(status_code=403, detail="Cannot get recommendations for other clients")
        
        service = ServiceRecommendationService(db)
        return await service.get_recommendations(request)
        
    except Exception as e:
        logger.error(f"Error getting recommendations: {e}")
        raise HTTPException(status_code=500, detail="Failed to get recommendations")