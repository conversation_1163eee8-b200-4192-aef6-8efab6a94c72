"""
Invoice management API endpoints.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from typing import List, Optional, Dict, Any
from datetime import date, datetime
from decimal import Decimal

from app.services.invoice_service import InvoiceService
from app.core.service_dependencies import get_invoice_service
from app.models.invoice_models import (
    Invoice, InvoiceFilter, PaymentStatus
)
from app.core.auth_decorators import require_auth, require_roles
from app.core.dependencies import get_current_user, get_database
from app.core.exceptions import ValidationError, BusinessLogicError
from app.models.user_models import User

router = APIRouter(prefix="/invoices", tags=["invoices"])


@router.post("/generate-daily", response_model=Dict[str, Any])
@require_roles(["manager"])
async def generate_daily_invoices(
    invoice_date: Optional[date] = Query(None, description="Date to generate invoices for (defaults to yesterday)"),
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    invoice_service: InvoiceService = Depends(get_invoice_service)
):
    """
    Generate daily invoices for completed appointments.
    This is typically called by the daily cron job.
    """
    try:
        result = await invoice_service.generate_daily_invoices(db, invoice_date)
        
        return {
            "success": True,
            "invoice_date": result.invoice_date,
            "appointments_processed": result.appointments_processed,
            "invoices_created": result.invoices_created,
            "subscription_deductions": result.subscription_deductions,
            "total_amount": float(result.total_amount),
            "errors": result.errors,
            "created_invoices": [
                {
                    "invoice_id": inv.invoice_id,
                    "invoice_number": inv.invoice_number,
                    "client_id": inv.client_id,
                    "total_amount": float(inv.total_amount),
                    "items_count": len(inv.items)
                }
                for inv in result.invoices
            ]
        }
        
    except ServiceError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate daily invoices: {str(e)}"
        )


@router.get("/{invoice_id}", response_model=Dict[str, Any])
@require_auth
async def get_invoice(
    invoice_id: int = Path(..., description="Invoice ID"),
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    invoice_service: InvoiceService = Depends(get_invoice_service)
):
    """Get invoice by ID with authorization checks."""
    try:
        invoice = await invoice_service.get_invoice(db, invoice_id)
        
        if not invoice:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Invoice not found"
            )
        
        # Authorization check - clients can only see their own invoices
        if current_user.role == "client" and invoice.client_id != current_user.user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        return {
            "invoice_id": invoice.invoice_id,
            "invoice_number": invoice.invoice_number,
            "client_id": invoice.client_id,
            "invoice_date": invoice.invoice_date,
            "due_date": invoice.due_date,
            "subtotal": float(invoice.subtotal),
            "tax_amount": float(invoice.tax_amount),
            "total_amount": float(invoice.total_amount),
            "status": invoice.status.value,
            "is_paid": invoice.is_paid,
            "is_overdue": invoice.is_overdue,
            "paid_by_client_id": invoice.paid_by_client_id,
            "paid_date": invoice.paid_date,
            "payment_method": invoice.payment_method,
            "stripe_payment_intent_id": invoice.stripe_payment_intent_id,
            "is_subscription_deduction": invoice.is_subscription_deduction,
            "subscription_id": invoice.subscription_id,
            "notes": invoice.notes,
            "created_at": invoice.created_at,
            "updated_at": invoice.updated_at,
            "items": [
                {
                    "item_id": item.item_id,
                    "appointment_id": item.appointment_id,
                    "item_type": item.item_type.value,
                    "description": item.description,
                    "quantity": float(item.quantity),
                    "unit_price": float(item.unit_price),
                    "amount": float(item.amount),
                    "surge_multiplier": float(item.surge_multiplier) if item.surge_multiplier else None,
                    "surge_amount": float(item.surge_amount) if item.surge_amount else None
                }
                for item in invoice.items
            ]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get invoice: {str(e)}"
        )


@router.get("", response_model=Dict[str, Any])
@require_auth
async def list_invoices(
    client_id: Optional[int] = Query(None, description="Filter by client ID"),
    status: Optional[PaymentStatus] = Query(None, description="Filter by payment status"),
    date_from: Optional[date] = Query(None, description="Filter from date"),
    date_to: Optional[date] = Query(None, description="Filter to date"),
    is_overdue: Optional[bool] = Query(None, description="Filter overdue invoices"),
    min_amount: Optional[Decimal] = Query(None, description="Minimum amount filter"),
    max_amount: Optional[Decimal] = Query(None, description="Maximum amount filter"),
    limit: int = Query(50, le=100, description="Number of results"),
    offset: int = Query(0, ge=0, description="Results offset"),
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    invoice_service: InvoiceService = Depends(get_invoice_service)
):
    """List invoices with filtering and pagination."""
    try:
        # For clients, restrict to their own invoices
        if current_user.role == "client":
            client_id = current_user.user_id
        
        filter_params = InvoiceFilter(
            client_id=client_id,
            status=status,
            date_from=date_from,
            date_to=date_to,
            is_overdue=is_overdue,
            min_amount=min_amount,
            max_amount=max_amount,
            limit=limit,
            offset=offset
        )
        
        invoices = await invoice_service.find_invoices(db, filter_params)
        
        return {
            "invoices": [
                {
                    "invoice_id": inv.invoice_id,
                    "invoice_number": inv.invoice_number,
                    "client_id": inv.client_id,
                    "invoice_date": inv.invoice_date,
                    "due_date": inv.due_date,
                    "total_amount": float(inv.total_amount),
                    "status": inv.status.value,
                    "is_paid": inv.is_paid,
                    "is_overdue": inv.is_overdue,
                    "paid_date": inv.paid_date,
                    "items_count": len(inv.items)
                }
                for inv in invoices
            ],
            "total": len(invoices),  # This would need to be calculated separately in a real implementation
            "limit": limit,
            "offset": offset,
            "has_more": len(invoices) == limit
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list invoices: {str(e)}"
        )


@router.post("/{invoice_id}/mark-paid", response_model=Dict[str, Any])
@require_roles(["manager"])
async def mark_invoice_paid(
    invoice_id: int = Path(..., description="Invoice ID"),
    paid_by_client_id: Optional[int] = Query(None, description="Which client/parent paid"),
    payment_method: str = Query("manual", description="Payment method"),
    stripe_payment_intent_id: Optional[str] = Query(None, description="Stripe payment intent ID"),
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    invoice_service: InvoiceService = Depends(get_invoice_service)
):
    """Mark an invoice as paid."""
    try:
        success = await invoice_service.mark_invoice_paid(
            db=db,
            invoice_id=invoice_id,
            paid_by_client_id=paid_by_client_id,
            payment_method=payment_method,
            stripe_payment_intent_id=stripe_payment_intent_id
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Invoice not found or already paid"
            )
        
        return {
            "success": True,
            "message": f"Invoice {invoice_id} marked as paid",
            "payment_method": payment_method,
            "paid_by_client_id": paid_by_client_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to mark invoice as paid: {str(e)}"
        )


@router.post("/{invoice_id}/create-payment-intent", response_model=Dict[str, Any])
@require_auth
async def create_stripe_payment_intent(
    invoice_id: int = Path(..., description="Invoice ID"),
    paid_by_client_id: Optional[int] = Query(None, description="Which client/parent is paying"),
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    invoice_service: InvoiceService = Depends(get_invoice_service)
):
    """Create Stripe payment intent for invoice payment."""
    try:
        payment_intent = await invoice_service.create_stripe_payment_intent(
            db=db,
            invoice_id=invoice_id,
            paid_by_client_id=paid_by_client_id
        )
        
        return {
            "payment_intent": payment_intent,
            "invoice_id": invoice_id
        }
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create payment intent: {str(e)}"
        )


@router.get("/overdue", response_model=List[Dict[str, Any]])
@require_roles(["manager"])
async def get_overdue_invoices(
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    invoice_service: InvoiceService = Depends(get_invoice_service)
):
    """Get all overdue invoices."""
    try:
        overdue_invoices = await invoice_service.get_overdue_invoices(db)
        
        return [
            {
                "invoice_id": invoice["invoice_id"],
                "invoice_number": invoice["invoice_number"],
                "client_id": invoice["client_id"],
                "client_name": invoice["client_name"],
                "client_email": invoice["client_email"],
                "client_phone": invoice["client_phone"],
                "total_amount": float(invoice["total_amount"]),
                "due_date": invoice["due_date"],
                "days_overdue": invoice["days_overdue"],
                "created_at": invoice["created_at"]
            }
            for invoice in overdue_invoices
        ]
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get overdue invoices: {str(e)}"
        )


@router.post("/send-overdue-reminders", response_model=Dict[str, Any])
@require_roles(["manager"])
async def send_overdue_reminders(
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    invoice_service: InvoiceService = Depends(get_invoice_service)
):
    """Send overdue reminders for all overdue invoices."""
    try:
        sent_count = await invoice_service.send_overdue_reminders(db)
        
        return {
            "success": True,
            "reminders_sent": sent_count,
            "message": f"Sent {sent_count} overdue reminders"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to send overdue reminders: {str(e)}"
        )


@router.post("/stripe-webhook", response_model=Dict[str, Any])
async def handle_stripe_webhook(
    event_type: str,
    event_data: Dict[str, Any],
    db=Depends(get_database),
    invoice_service: InvoiceService = Depends(get_invoice_service)
):
    """Handle Stripe webhook events for invoice payments."""
    try:
        await invoice_service.handle_stripe_webhook(db, event_type, event_data)
        
        return {
            "success": True,
            "message": "Webhook processed successfully"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process webhook: {str(e)}"
        )