import React from 'react';
import { clsx } from 'clsx';

interface ToggleProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  label?: string;
  labelPosition?: 'left' | 'right';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  className?: string;
}

export const Toggle: React.FC<ToggleProps> = ({
  checked,
  onChange,
  label,
  labelPosition = 'right',
  size = 'md',
  disabled = false,
  className,
}) => {
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === ' ' || e.key === 'Enter') {
      e.preventDefault();
      if (!disabled) {
        onChange(!checked);
      }
    }
  };

  const sizes = {
    sm: {
      track: 'w-9 h-5',
      thumb: 'w-4 h-4',
      translate: 'translate-x-4',
      label: 'text-sm',
    },
    md: {
      track: 'w-11 h-6',
      thumb: 'w-5 h-5',
      translate: 'translate-x-5',
      label: 'text-base',
    },
    lg: {
      track: 'w-14 h-8',
      thumb: 'w-7 h-7',
      translate: 'translate-x-6',
      label: 'text-lg',
    },
  };

  const sizeConfig = sizes[size];

  const toggle = (
    <div
      role="switch"
      aria-checked={checked}
      tabIndex={disabled ? -1 : 0}
      onClick={() => !disabled && onChange(!checked)}
      onKeyDown={handleKeyDown}
      className={clsx(
        'relative inline-block rounded-full transition-colors duration-200 cursor-pointer focus:outline-none focus-visible:ring-2 focus-visible:ring-accent-red focus-visible:ring-offset-2',
        sizeConfig.track,
        checked ? 'bg-accent-red' : 'bg-background-tertiary',
        disabled && 'opacity-50 cursor-not-allowed'
      )}
    >
      <span
        className={clsx(
          'absolute left-0.5 top-0.5 bg-white rounded-full shadow-soft transition-transform duration-200',
          sizeConfig.thumb,
          checked && sizeConfig.translate
        )}
      />
    </div>
  );

  if (!label) {
    return toggle;
  }

  return (
    <label
      className={clsx(
        'inline-flex items-center gap-3',
        disabled && 'cursor-not-allowed',
        className
      )}
    >
      {labelPosition === 'left' && (
        <span className={clsx(sizeConfig.label, 'text-text-primary select-none')}>
          {label}
        </span>
      )}
      {toggle}
      {labelPosition === 'right' && (
        <span className={clsx(sizeConfig.label, 'text-text-primary select-none')}>
          {label}
        </span>
      )}
    </label>
  );
};

interface ToggleGroupProps {
  children: React.ReactNode;
  label?: string;
  className?: string;
}

export const ToggleGroup: React.FC<ToggleGroupProps> = ({
  children,
  label,
  className,
}) => {
  return (
    <div className={className}>
      {label && (
        <p className="text-sm font-medium text-text-primary mb-3">
          {label}
        </p>
      )}
      <div className="space-y-3">
        {children}
      </div>
    </div>
  );
};

export default Toggle;