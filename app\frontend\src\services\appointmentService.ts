/**
 * Appointment service for managing appointments and recurring series
 */

import api from './api';
import { 
  AppointmentStatus, 
  LocationType, 
  PackageType 
} from '../types/appointment';

// ============================================
// Type Definitions
// ============================================

export interface TimeSlot {
  start_time: string;
  end_time: string;
  is_available: boolean;
}

export interface LocationDetails {
  type: LocationType;
  address?: string;
  platform?: string;
  meeting_link?: string;
  room_number?: string;
}

export interface Appointment {
  appointment_id: number;
  tutor_id: number;
  tutor_name?: string;
  client_id: number;
  client_name?: string;
  dependant_id?: number;
  dependant_name?: string;
  scheduled_date: string;
  start_time: string;
  end_time: string;
  duration: number;
  subject_area: string;
  session_type: PackageType;
  status: AppointmentStatus;
  location_type: LocationType;
  location_details?: LocationDetails;
  max_participants?: number;
  current_participants: number;
  notes?: string;
  confirmed_by_tutor: boolean;
  confirmation_date?: string;
  created_at: string;
  updated_at: string;
  created_by: number;
  hourly_rate?: number;
  currency?: string;
  actual_duration_minutes?: number;
  duration_adjusted?: boolean;
  recurring_appointment_id?: number;
  is_exception?: boolean;
}

export interface CreateAppointmentRequest {
  tutor_id: number;
  client_id: number;
  dependant_id?: number;
  scheduled_date: string;
  start_time: string;
  end_time: string;
  subject_area: string;
  session_type?: PackageType;
  location_type: LocationType;
  location_details?: string;
  max_participants?: number;
  notes?: string;
  send_notifications?: boolean;
  require_confirmation?: boolean;
}

export interface UpdateAppointmentRequest {
  scheduled_date?: string;
  start_time?: string;
  end_time?: string;
  status?: AppointmentStatus;
  location_type?: LocationType;
  location_details?: string;
  notes?: string;
  cancellation_reason?: string;
  send_notifications?: boolean;
}

export interface ConfirmAppointmentRequest {
  confirmed: boolean;
  confirmation_notes?: string;
}

export interface CompleteAppointmentRequest {
  actual_duration_minutes?: number;
  notes?: string;
  tutor_no_show?: boolean;
  client_no_show?: boolean;
  send_sms_confirmation?: boolean;
}

export interface DurationAdjustmentRequest {
  actual_start_time: string;
  actual_end_time: string;
  adjustment_reason: string;
  affects_billing?: boolean;
}

export interface BulkUpdateRequest {
  appointment_ids: number[];
  updates: UpdateAppointmentRequest;
}

export interface AppointmentListParams {
  start_date?: string;
  end_date?: string;
  tutor_id?: number;
  client_id?: number;
  status?: AppointmentStatus;
  session_type?: PackageType;
  page?: number;
  page_size?: number;
}

export interface AppointmentListResponse {
  items: Appointment[];
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
}

export interface AppointmentStats {
  date_range: {
    start: string;
    end: string;
  };
  total_appointments: number;
  completed: number;
  cancelled: number;
  no_shows: number;
  unique_clients?: number;
  unique_tutors?: number;
  subjects_studied?: number;
  avg_duration_hours: number;
  total_earnings?: number;
  total_cost?: number;
}

export interface ConflictCheck {
  tutor_id: number;
  proposed_date: string;
  proposed_start: string;
  proposed_end: string;
  include_buffer?: boolean;
  exclude_appointment_id?: number;
}

export interface ConflictResult {
  has_conflict: boolean;
  conflict_type?: string;
  conflicting_appointment_id?: number;
  conflicting_time_off_id?: number;
  message?: string;
  suggested_times?: TimeSlot[];
}

export interface SchedulingSuggestion {
  tutor_id: number;
  tutor_name: string;
  suggested_date: string;
  suggested_start: string;
  suggested_end: string;
  confidence_score: number;
  reasons: string[];
  hourly_rate: number;
}

export interface AuditLog {
  audit_id: number;
  appointment_id: number;
  action: string;
  performed_by: number;
  performed_by_role: string;
  performed_by_name: string;
  field_name?: string;
  old_value?: string;
  new_value?: string;
  change_reason?: string;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
}

// Recurring Appointment Types
export interface RecurringAppointment {
  recurring_id: number;
  tutor_id: number;
  tutor_name?: string;
  client_id: number;
  client_name?: string;
  dependant_id?: number;
  dependant_name?: string;
  day_of_week: number;
  start_time: string;
  end_time: string;
  subject_area: string;
  location_details: LocationDetails;
  hourly_rate: number;
  currency: string;
  start_date: string;
  end_date?: string;
  is_active: boolean;
  frequency_weeks: number;
  total_appointments?: number;
  completed_appointments?: number;
  upcoming_appointments?: number;
  cancelled_appointments?: number;
  next_appointment?: Appointment;
  last_appointment?: Appointment;
}

export interface CreateRecurringRequest {
  tutor_id: number;
  client_id: number;
  dependant_id?: number;
  day_of_week: number;
  start_time: string;
  end_time: string;
  subject_area: string;
  location_details: LocationDetails;
  hourly_rate: number;
  start_date: string;
  end_date?: string;
  frequency_weeks?: number;
}

export interface UpdateRecurringRequest {
  day_of_week?: number;
  start_time?: string;
  end_time?: string;
  subject_area?: string;
  location_details?: LocationDetails;
  hourly_rate?: number;
  end_date?: string;
  is_active?: boolean;
  update_mode: 'this_only' | 'future' | 'all';
}

// ============================================
// Appointment Service
// ============================================

export const appointmentService = {
  // Create a new appointment
  async createAppointment(data: CreateAppointmentRequest): Promise<Appointment> {
    const response = await api.post<Appointment>('/appointments', data);
    return response.data;
  },

  // Get appointment by ID
  async getAppointment(appointmentId: number): Promise<Appointment> {
    const response = await api.get<Appointment>(`/appointments/${appointmentId}`);
    return response.data;
  },

  // Update appointment
  async updateAppointment(appointmentId: number, data: UpdateAppointmentRequest): Promise<Appointment> {
    const response = await api.put<Appointment>(`/appointments/${appointmentId}`, data);
    return response.data;
  },

  // Cancel appointment
  async cancelAppointment(appointmentId: number, cancellationReason: string): Promise<void> {
    await api.delete(`/appointments/${appointmentId}`, {
      params: { cancellation_reason: cancellationReason }
    });
  },

  // Confirm appointment
  async confirmAppointment(appointmentId: number, data: ConfirmAppointmentRequest): Promise<any> {
    const response = await api.post(`/appointments/${appointmentId}/confirm`, data);
    return response.data;
  },

  // List appointments
  async listAppointments(params?: AppointmentListParams): Promise<AppointmentListResponse> {
    const response = await api.get<AppointmentListResponse>('/appointments', { params });
    return response.data;
  },

  // Bulk update appointments
  async bulkUpdateAppointments(data: BulkUpdateRequest): Promise<any> {
    const response = await api.post('/appointments/bulk-update', data);
    return response.data;
  },

  // Get appointment statistics
  async getAppointmentStats(startDate?: string, endDate?: string): Promise<AppointmentStats> {
    const response = await api.get<AppointmentStats>('/appointments/stats/summary', {
      params: { start_date: startDate, end_date: endDate }
    });
    return response.data;
  },

  // Complete appointment
  async completeAppointment(appointmentId: number, data: CompleteAppointmentRequest): Promise<any> {
    const response = await api.patch(`/appointments/${appointmentId}/complete`, null, { params: data });
    return response.data;
  },

  // Adjust appointment duration
  async adjustDuration(appointmentId: number, data: DurationAdjustmentRequest): Promise<any> {
    const response = await api.post(`/appointments/${appointmentId}/adjust-duration`, data);
    return response.data;
  },

  // Get duration adjustment history
  async getDurationHistory(appointmentId: number): Promise<any> {
    const response = await api.get(`/appointments/${appointmentId}/duration-history`);
    return response.data;
  },

  // Get audit logs
  async getAuditLogs(appointmentId: number, limit?: number, offset?: number): Promise<{
    appointment_id: number;
    audit_logs: AuditLog[];
    total_logs: number;
    limit: number;
    offset: number;
  }> {
    const response = await api.get(`/appointments/${appointmentId}/audit-logs`, {
      params: { limit, offset }
    });
    return response.data;
  },

  // Check for conflicts
  async checkConflicts(data: ConflictCheck): Promise<ConflictResult> {
    const response = await api.post<ConflictResult>('/appointments/check-conflicts', data);
    return response.data;
  },

  // Get scheduling suggestions
  async getSchedulingSuggestions(params: {
    tutor_id?: number;
    client_id: number;
    subject_area: string;
    preferred_date?: string;
    duration_hours?: number;
    session_type?: string;
  }): Promise<SchedulingSuggestion[]> {
    const response = await api.get<SchedulingSuggestion[]>('/appointments/suggestions', { params });
    return response.data;
  },

  // ============================================
  // Recurring Appointment Methods
  // ============================================

  // Create recurring appointment series
  async createRecurringSeries(data: CreateRecurringRequest): Promise<{
    recurring_id: number;
    series_name: string;
    total_appointments: number;
    appointment_ids: number[];
    start_date: string;
    end_date?: string;
    conflicts: any[];
  }> {
    const response = await api.post('/appointments/recurring', data);
    return response.data;
  },

  // List recurring series
  async listRecurringSeries(params?: {
    tutor_id?: number;
    client_id?: number;
    is_active?: boolean;
    page?: number;
    page_size?: number;
  }): Promise<{
    items: RecurringAppointment[];
    total: number;
    page: number;
    page_size: number;
  }> {
    const response = await api.get('/appointments/recurring', { params });
    return response.data;
  },

  // Get recurring series details
  async getRecurringSeries(recurringId: number): Promise<RecurringAppointment> {
    const response = await api.get<RecurringAppointment>(`/appointments/recurring/${recurringId}`);
    return response.data;
  },

  // Update recurring series
  async updateRecurringSeries(recurringId: number, data: UpdateRecurringRequest): Promise<any> {
    const response = await api.put(`/appointments/recurring/${recurringId}`, data);
    return response.data;
  },

  // Cancel recurring series
  async cancelRecurringSeries(recurringId: number, mode: 'future' | 'all' | 'this_only', reason: string): Promise<void> {
    await api.delete(`/appointments/recurring/${recurringId}`, {
      params: { mode, reason }
    });
  },

  // Pause/resume recurring series
  async toggleRecurringSeries(recurringId: number, isActive: boolean): Promise<any> {
    const response = await api.patch(`/appointments/recurring/${recurringId}/toggle`, {
      is_active: isActive
    });
    return response.data;
  },

  // Get appointments in a recurring series
  async getSeriesAppointments(recurringId: number, params?: {
    status?: AppointmentStatus;
    start_date?: string;
    end_date?: string;
  }): Promise<Appointment[]> {
    const response = await api.get<Appointment[]>(`/appointments/recurring/${recurringId}/appointments`, { params });
    return response.data;
  },

  // ============================================
  // Utility Methods
  // ============================================

  // Format appointment time display
  formatAppointmentTime(appointment: Appointment): string {
    const start = new Date(`${appointment.scheduled_date}T${appointment.start_time}`);
    const end = new Date(`${appointment.scheduled_date}T${appointment.end_time}`);
    
    return `${start.toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit' 
    })} - ${end.toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit' 
    })}`;
  },

  // Get appointment status color
  getStatusColor(status: AppointmentStatus): string {
    const statusColors: Record<AppointmentStatus, string> = {
      [AppointmentStatus.SCHEDULED]: 'blue',
      [AppointmentStatus.CONFIRMED]: 'green',
      [AppointmentStatus.IN_PROGRESS]: 'yellow',
      [AppointmentStatus.COMPLETED]: 'gray',
      [AppointmentStatus.CANCELLED]: 'red',
      [AppointmentStatus.NO_SHOW]: 'orange',
    };
    return statusColors[status] || 'gray';
  },

  // Calculate appointment duration in minutes
  calculateDuration(startTime: string, endTime: string): number {
    const start = new Date(`1970-01-01T${startTime}`);
    const end = new Date(`1970-01-01T${endTime}`);
    return (end.getTime() - start.getTime()) / (1000 * 60);
  },

  // Get day of week name
  getDayOfWeekName(dayNumber: number): string {
    const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
    return days[dayNumber - 1] || '';
  },

  // Format frequency text
  getFrequencyText(weeks: number): string {
    switch (weeks) {
      case 1: return 'Weekly';
      case 2: return 'Biweekly';
      case 4: return 'Monthly';
      default: return `Every ${weeks} weeks`;
    }
  }
};