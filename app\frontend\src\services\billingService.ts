import api from './api';

// Types
export interface Invoice {
  invoice_id: number;
  invoice_number: string;
  client_id: number;
  client_name: string;
  amount: number;
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  due_date: string;
  created_at: string;
  paid_at?: string;
  payment_method?: string;
  stripe_payment_intent_id?: string;
  items: InvoiceItem[];
  parent_payments?: ParentPayment[];
}

export interface InvoiceItem {
  item_id: number;
  description: string;
  quantity: number;
  unit_price: number;
  total: number;
  appointment_id?: number;
}

export interface ParentPayment {
  parent_id: number;
  parent_name: string;
  amount_paid: number;
  payment_date: string;
  payment_method: string;
  stripe_payment_intent_id?: string;
}

export interface TutorPayment {
  payment_id: number;
  tutor_id: number;
  tutor_name: string;
  amount: number;
  status: 'pending' | 'approved' | 'processing' | 'completed' | 'failed';
  payment_date: string;
  period_start: string;
  period_end: string;
  sessions_count: number;
  bank_account_last4?: string;
  stripe_payout_id?: string;
}

export interface PaymentIntent {
  client_secret: string;
  amount: number;
  currency: string;
  status: string;
}

export interface BankAccount {
  account_id: string;
  bank_name: string;
  last4: string;
  routing_number: string;
  status: 'new' | 'validated' | 'verified' | 'verification_failed';
}

export interface CreateInvoiceRequest {
  client_id: number;
  items: {
    description: string;
    quantity: number;
    unit_price: number;
    appointment_id?: number;
  }[];
  due_date: string;
  notes?: string;
}

export interface GenerateInvoicesRequest {
  start_date: string;
  end_date: string;
  client_ids?: number[];
  auto_send?: boolean;
}

export interface RecordPaymentRequest {
  amount: number;
  payment_method: 'stripe' | 'cash' | 'e_transfer' | 'other';
  payment_date?: string;
  parent_id?: number;
  notes?: string;
  stripe_payment_intent_id?: string;
}

export interface SplitInvoiceRequest {
  parent_splits: {
    parent_id: number;
    amount: number;
  }[];
}

export interface ApproveTutorPaymentsRequest {
  payment_ids: number[];
  approved_by_notes?: string;
}

export interface GenerateWeeklyPaymentsRequest {
  week_ending_date: string;
  tutor_ids?: number[];
}

export interface SetupBankAccountRequest {
  account_number: string;
  routing_number: string;
  account_holder_name: string;
  account_holder_type: 'individual' | 'company';
}

// Service
export const billingService = {
  // Invoice Management
  async getInvoices(params?: {
    status?: string;
    client_id?: number;
    start_date?: string;
    end_date?: string;
    page?: number;
    limit?: number;
  }) {
    const queryParams = new URLSearchParams();
    if (params?.status) queryParams.append('status', params.status);
    if (params?.client_id) queryParams.append('client_id', params.client_id.toString());
    if (params?.start_date) queryParams.append('start_date', params.start_date);
    if (params?.end_date) queryParams.append('end_date', params.end_date);
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());

    const response = await api.get<{
      invoices: Invoice[];
      total: number;
      page: number;
      has_more: boolean;
    }>(`/billing/invoices?${queryParams}`);
    return response.data;
  },

  async getInvoice(invoiceId: number) {
    const response = await api.get<Invoice>(`/billing/invoices/${invoiceId}`);
    return response.data;
  },

  async createInvoice(data: CreateInvoiceRequest) {
    const response = await api.post<Invoice>('/billing/invoices', data);
    return response.data;
  },

  async generateInvoices(data: GenerateInvoicesRequest) {
    const response = await api.post<{
      generated_count: number;
      invoices: Invoice[];
    }>('/billing/invoices/generate', data);
    return response.data;
  },

  async downloadInvoice(invoiceId: number) {
    const response = await api.get(`/billing/invoices/${invoiceId}/download`, {
      responseType: 'blob',
    });
    return response.data;
  },

  async recordPayment(invoiceId: number, data: RecordPaymentRequest) {
    const response = await api.post<{
      payment_id: number;
      invoice: Invoice;
    }>(`/billing/invoices/${invoiceId}/payments`, data);
    return response.data;
  },

  async splitInvoiceByParent(invoiceId: number, data: SplitInvoiceRequest) {
    const response = await api.post<{
      parent_invoices: Invoice[];
    }>(`/billing/invoices/${invoiceId}/split-by-parent`, data);
    return response.data;
  },

  async recordParentPayment(invoiceId: number, data: {
    parent_id: number;
    amount: number;
    payment_method: string;
    stripe_payment_intent_id?: string;
  }) {
    const response = await api.post<{
      payment_id: number;
      remaining_balance: number;
    }>(`/billing/invoices/${invoiceId}/parent-payment`, data);
    return response.data;
  },

  // Stripe Payment Processing
  async createPaymentIntent(invoiceId: number) {
    const response = await api.post<PaymentIntent>('/billing/stripe/payment-intents', {
      invoice_id: invoiceId,
    });
    return response.data;
  },

  async confirmPaymentIntent(paymentIntentId: string, paymentMethodId: string) {
    const response = await api.post<{
      status: string;
      invoice: Invoice;
    }>(`/billing/stripe/payment-intents/${paymentIntentId}/confirm`, {
      payment_method_id: paymentMethodId,
    });
    return response.data;
  },

  // Tutor Payments
  async getTutorPayments(params?: {
    tutor_id?: number;
    status?: string;
    start_date?: string;
    end_date?: string;
    page?: number;
    limit?: number;
  }) {
    const queryParams = new URLSearchParams();
    if (params?.tutor_id) queryParams.append('tutor_id', params.tutor_id.toString());
    if (params?.status) queryParams.append('status', params.status);
    if (params?.start_date) queryParams.append('start_date', params.start_date);
    if (params?.end_date) queryParams.append('end_date', params.end_date);
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());

    const response = await api.get<{
      payments: TutorPayment[];
      total: number;
      page: number;
      has_more: boolean;
    }>(`/billing/tutor-payments?${queryParams}`);
    return response.data;
  },

  async createTutorPayment(data: {
    tutor_id: number;
    amount: number;
    period_start: string;
    period_end: string;
    session_ids: number[];
  }) {
    const response = await api.post<TutorPayment>('/billing/tutor-payments', data);
    return response.data;
  },

  async approveTutorPayments(data: ApproveTutorPaymentsRequest) {
    const response = await api.post<{
      approved_count: number;
      failed_count: number;
      payments: TutorPayment[];
    }>('/billing/tutor-payments/approve', data);
    return response.data;
  },

  async generateWeeklyPayments(data: GenerateWeeklyPaymentsRequest) {
    const response = await api.post<{
      generated_count: number;
      total_amount: number;
      payments: TutorPayment[];
    }>('/billing/tutor-payments/generate-weekly', data);
    return response.data;
  },

  async processApprovedPayments() {
    const response = await api.post<{
      processed_count: number;
      failed_count: number;
      total_amount: number;
    }>('/billing/tutor-payments/process-approved');
    return response.data;
  },

  async getTutorPaymentHistory(tutorId: number, params?: {
    start_date?: string;
    end_date?: string;
    page?: number;
    limit?: number;
  }) {
    const queryParams = new URLSearchParams();
    if (params?.start_date) queryParams.append('start_date', params.start_date);
    if (params?.end_date) queryParams.append('end_date', params.end_date);
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());

    const response = await api.get<{
      payments: TutorPayment[];
      total_earnings: number;
      total_sessions: number;
    }>(`/billing/tutors/${tutorId}/payment-history?${queryParams}`);
    return response.data;
  },

  // Stripe Connect for Tutors
  async setupTutorBankAccount(tutorId: number, data: SetupBankAccountRequest) {
    const response = await api.post<{
      account_id: string;
      bank_account: BankAccount;
    }>(`/billing/stripe/tutors/${tutorId}/bank-account`, data);
    return response.data;
  },

  async createTutorPayout(tutorId: number, amount: number) {
    const response = await api.post<{
      payout_id: string;
      amount: number;
      arrival_date: string;
      status: string;
    }>(`/billing/stripe/tutors/${tutorId}/payouts`, { amount });
    return response.data;
  },

  // Parent Payment History
  async getParentPaymentHistory(parentId: number) {
    const response = await api.get<{
      payments: ParentPayment[];
      total_paid: number;
    }>(`/billing/parents/${parentId}/payment-history`);
    return response.data;
  },

  async getParentBalanceSummary(parentId: number) {
    const response = await api.get<{
      total_invoiced: number;
      total_paid: number;
      outstanding_balance: number;
      invoices: Invoice[];
    }>(`/billing/parents/${parentId}/balance-summary`);
    return response.data;
  },

  // Webhook handler (for Stripe events)
  async handleStripeWebhook(signature: string, payload: any) {
    const response = await api.post('/billing/stripe/webhooks', payload, {
      headers: {
        'Stripe-Signature': signature,
      },
    });
    return response.data;
  },
};