import React, { useState, createContext, useContext } from 'react';
import { clsx } from 'clsx';

// Context for sharing tab state
const TabsContext = createContext<{
  value: string;
  onValueChange: (value: string) => void;
}>({ value: '', onValueChange: () => {} });

// Main Tabs component
export const Tabs: React.FC<{
  value: string;
  onValueChange: (value: string) => void;
  className?: string;
  children: React.ReactNode;
}> = ({ value, onValueChange, className, children }) => {
  return (
    <TabsContext.Provider value={{ value, onValueChange }}>
      <div className={className}>
        {children}
      </div>
    </TabsContext.Provider>
  );
};

// TabsList component
export const TabsList: React.FC<{
  className?: string;
  children: React.ReactNode;
}> = ({ className, children }) => {
  return (
    <div className={clsx('flex border-b border-gray-200', className)}>
      {children}
    </div>
  );
};

// TabsTrigger component
export const TabsTrigger: React.FC<{
  value: string;
  className?: string;
  children: React.ReactNode;
}> = ({ value, className, children }) => {
  const { value: selectedValue, onValueChange } = useContext(TabsContext);
  const isActive = selectedValue === value;

  return (
    <button
      onClick={() => onValueChange(value)}
      className={clsx(
        'px-4 py-2 flex items-center gap-2 text-sm font-medium transition-all border-b-2',
        isActive
          ? 'text-accent-red border-accent-red'
          : 'text-gray-600 border-transparent hover:text-gray-900 hover:border-gray-300',
        className
      )}
    >
      {children}
    </button>
  );
};

// TabsContent component
export const TabsContent: React.FC<{
  value: string;
  className?: string;
  children: React.ReactNode;
}> = ({ value, className, children }) => {
  const { value: selectedValue } = useContext(TabsContext);

  if (selectedValue !== value) {
    return null;
  }

  return (
    <div className={className}>
      {children}
    </div>
  );
};