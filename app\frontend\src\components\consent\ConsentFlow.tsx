import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { CheckCircle, AlertCircle, Info } from 'lucide-react';
import Button from '../common/Button';
import { Card } from '../common/Card';
import LoadingSpinner from '../ui/LoadingSpinner';
import { ConsentModal } from './ConsentModal';
import api from '../../services/api';
import toast from 'react-hot-toast';
import { useAuth } from '../../contexts/AuthContext';
import { UserRoleType } from '../../types/auth';

interface ConsentValidation {
  is_valid: boolean;
  missing_mandatory: string[];
  expired_consents: string[];
  message: string;
}

interface ConsentFlowProps {
  onComplete?: () => void;
  blockAccess?: boolean;
}

export const ConsentFlow: React.FC<ConsentFlowProps> = ({
  onComplete,
  blockAccess = true
}) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [validation, setValidation] = useState<ConsentValidation | null>(null);
  const [showConsentModal, setShowConsentModal] = useState(false);

  useEffect(() => {
    checkConsents();
  }, []);

  const checkConsents = async () => {
    try {
      setLoading(true);
      const response = await api.get<ConsentValidation>('/consent/validate');
      setValidation(response.data);
      
      // Auto-show consent modal if there are missing mandatory consents
      if (!response.data.is_valid && response.data.missing_mandatory.length > 0) {
        setShowConsentModal(true);
      }
    } catch (error) {
      console.error('Error checking consents:', error);
      toast.error(t('consent.errors.validationFailed'));
    } finally {
      setLoading(false);
    }
  };

  const handleConsentComplete = () => {
    setShowConsentModal(false);
    checkConsents(); // Re-check after accepting consents
    
    if (onComplete) {
      onComplete();
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!validation) {
    return null;
  }

  // If all consents are valid, don't show anything
  if (validation.is_valid && !blockAccess) {
    return null;
  }

  // If blocking access and consents are valid, allow through
  if (validation.is_valid && blockAccess) {
    if (onComplete) {
      onComplete();
    }
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <Card className="max-w-2xl w-full p-8">
        <div className="text-center mb-8">
          <div className="mx-auto w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
            <AlertCircle className="w-8 h-8 text-yellow-600" />
          </div>
          
          <h1 className="text-2xl font-semibold text-gray-900 mb-2">
            {t('consent.flow.title')}
          </h1>
          
          <p className="text-gray-600">
            {user?.activeRole === UserRoleType.TUTOR
              ? t('consent.flow.tutorDescription')
              : t('consent.flow.clientDescription')}
          </p>
        </div>

        {validation.missing_mandatory.length > 0 && (
          <div className="mb-6 p-4 bg-red-50 rounded-lg">
            <div className="flex items-start">
              <AlertCircle className="w-5 h-5 text-red-400 mt-0.5" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  {t('consent.flow.missingConsents')}
                </h3>
                <ul className="mt-2 text-sm text-red-700 list-disc list-inside">
                  {validation.missing_mandatory.map((consentType) => (
                    <li key={consentType}>
                      {t(`consent.types.${consentType}`)}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        )}

        {validation.expired_consents.length > 0 && (
          <div className="mb-6 p-4 bg-yellow-50 rounded-lg">
            <div className="flex items-start">
              <Info className="w-5 h-5 text-yellow-400 mt-0.5" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800">
                  {t('consent.flow.expiredConsents')}
                </h3>
                <ul className="mt-2 text-sm text-yellow-700 list-disc list-inside">
                  {validation.expired_consents.map((consentType) => (
                    <li key={consentType}>
                      {t(`consent.types.${consentType}`)}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        )}

        <div className="space-y-4">
          <Button
            variant="primary"
            size="lg"
            className="w-full"
            leftIcon={<CheckCircle />}
            onClick={() => setShowConsentModal(true)}
          >
            {t('consent.flow.reviewAndAccept')}
          </Button>
          
          {!blockAccess && (
            <Button
              variant="ghost"
              size="lg"
              className="w-full"
              onClick={() => {
                if (onComplete) {
                  onComplete();
                }
              }}
            >
              {t('consent.flow.remindLater')}
            </Button>
          )}
        </div>

        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <div className="flex items-start">
            <Info className="w-5 h-5 text-blue-400 mt-0.5" />
            <div className="ml-3">
              <p className="text-sm text-blue-700">
                {t('consent.flow.infoText')}
              </p>
            </div>
          </div>
        </div>
      </Card>

      <ConsentModal
        isOpen={showConsentModal}
        onClose={() => setShowConsentModal(false)}
        requiredConsents={validation.missing_mandatory}
        onConsentComplete={handleConsentComplete}
        userRole={user?.activeRole === UserRoleType.TUTOR ? 'tutor' : 'client'}
        allowClose={false}
      />
    </div>
  );
};

export default ConsentFlow;