import React from 'react';
import { clsx } from 'clsx';
import { ChevronRight } from 'lucide-react';

interface ListItemProps {
  title: string;
  subtitle?: string;
  description?: string;
  icon?: React.ReactNode;
  avatar?: React.ReactNode;
  actions?: React.ReactNode;
  onClick?: () => void;
  selected?: boolean;
  showArrow?: boolean;
  className?: string;
}

export const ListItem: React.FC<ListItemProps> = ({
  title,
  subtitle,
  description,
  icon,
  avatar,
  actions,
  onClick,
  selected = false,
  showArrow = false,
  className,
}) => {
  return (
    <div
      onClick={onClick}
      className={clsx(
        'flex items-center gap-4 p-4 transition-all duration-200',
        'bg-white border border-border-primary rounded-lg',
        onClick && 'cursor-pointer hover:shadow-soft hover:border-accent-red',
        selected && 'bg-red-50 border-accent-red',
        className
      )}
    >
      {(icon || avatar) && (
        <div className="flex-shrink-0">
          {avatar || (
            <div className="w-10 h-10 bg-red-50 rounded-lg flex items-center justify-center">
              <span className="text-accent-red">{icon}</span>
            </div>
          )}
        </div>
      )}
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2">
          <h3 className="text-sm font-medium text-text-primary truncate">
            {title}
          </h3>
          {subtitle && (
            <span className="text-xs text-text-muted">
              {subtitle}
            </span>
          )}
        </div>
        {description && (
          <p className="mt-1 text-sm text-text-secondary line-clamp-2">
            {description}
          </p>
        )}
      </div>
      
      {actions && (
        <div className="flex-shrink-0">
          {actions}
        </div>
      )}
      
      {showArrow && onClick && (
        <ChevronRight className="w-5 h-5 text-text-muted flex-shrink-0" />
      )}
    </div>
  );
};

interface ListGroupProps {
  title?: string;
  children: React.ReactNode;
  className?: string;
}

export const ListGroup: React.FC<ListGroupProps> = ({
  title,
  children,
  className,
}) => {
  return (
    <div className={className}>
      {title && (
        <h3 className="text-sm font-medium text-text-secondary uppercase tracking-wider mb-3">
          {title}
        </h3>
      )}
      <div className="space-y-2">
        {children}
      </div>
    </div>
  );
};

interface CompactListItemProps {
  label: string;
  value: React.ReactNode;
  icon?: React.ReactNode;
  className?: string;
}

export const CompactListItem: React.FC<CompactListItemProps> = ({
  label,
  value,
  icon,
  className,
}) => {
  return (
    <div className={clsx('flex items-center justify-between py-2', className)}>
      <div className="flex items-center gap-2 text-sm text-text-secondary">
        {icon && <span className="w-4 h-4">{icon}</span>}
        {label}
      </div>
      <div className="text-sm font-medium text-text-primary">
        {value}
      </div>
    </div>
  );
};

export default ListItem;