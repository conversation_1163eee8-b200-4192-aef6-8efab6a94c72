import React from 'react';
import { clsx } from 'clsx';
import { Calendar } from 'lucide-react';

interface DatePickerProps {
  value?: string;
  onChange: (date: string) => void;
  label?: string;
  error?: string;
  hint?: string;
  min?: string;
  max?: string;
  disabled?: boolean;
  required?: boolean;
  fullWidth?: boolean;
  className?: string;
}

export const DatePicker: React.FC<DatePickerProps> = ({
  value,
  onChange,
  label,
  error,
  hint,
  min,
  max,
  disabled = false,
  required = false,
  fullWidth = true,
  className,
}) => {
  const inputRef = React.useRef<HTMLInputElement>(null);
  const id = React.useId();

  const handleIconClick = () => {
    inputRef.current?.showPicker();
  };

  return (
    <div className={clsx(fullWidth ? 'w-full' : 'inline-block', className)}>
      {label && (
        <label
          htmlFor={id}
          className="block text-sm font-medium text-text-primary mb-2"
        >
          {label}
          {required && <span className="ml-1 text-accent-red">*</span>}
        </label>
      )}

      <div className="relative">
        <input
          ref={inputRef}
          id={id}
          type="date"
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          min={min}
          max={max}
          disabled={disabled}
          required={required}
          className={clsx(
            'w-full px-4 py-3 pr-10 border rounded-lg bg-background-secondary text-text-primary',
            'transition-all duration-200 focus:outline-none focus:border-accent-red focus:bg-white focus:shadow-focus',
            error ? 'border-semantic-error' : 'border-border-primary hover:border-border-secondary',
            disabled && 'opacity-60 cursor-not-allowed bg-background-tertiary',
            '[&::-webkit-calendar-picker-indicator]:hidden [&::-webkit-calendar-picker-indicator]:appearance-none'
          )}
          aria-invalid={Boolean(error)}
          aria-describedby={error ? `${id}-error` : hint ? `${id}-hint` : undefined}
        />
        
        <button
          type="button"
          onClick={handleIconClick}
          disabled={disabled}
          className="absolute right-3 top-1/2 -translate-y-1/2 text-text-muted hover:text-text-secondary transition-colors"
        >
          <Calendar className="w-5 h-5" />
        </button>
      </div>

      {error && (
        <p id={`${id}-error`} className="mt-2 text-sm text-semantic-error">
          {error}
        </p>
      )}

      {hint && !error && (
        <p id={`${id}-hint`} className="mt-2 text-sm text-text-muted">
          {hint}
        </p>
      )}
    </div>
  );
};

interface DateRangePickerProps {
  startDate?: string;
  endDate?: string;
  onStartDateChange: (date: string) => void;
  onEndDateChange: (date: string) => void;
  label?: string;
  startLabel?: string;
  endLabel?: string;
  error?: string;
  hint?: string;
  disabled?: boolean;
  required?: boolean;
  className?: string;
}

export const DateRangePicker: React.FC<DateRangePickerProps> = ({
  startDate,
  endDate,
  onStartDateChange,
  onEndDateChange,
  label,
  startLabel = 'Start Date',
  endLabel = 'End Date',
  error,
  hint,
  disabled = false,
  required = false,
  className,
}) => {
  return (
    <div className={className}>
      {label && (
        <p className="text-sm font-medium text-text-primary mb-2">
          {label}
          {required && <span className="ml-1 text-accent-red">*</span>}
        </p>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <DatePicker
          value={startDate}
          onChange={onStartDateChange}
          label={startLabel}
          max={endDate}
          disabled={disabled}
          required={required}
        />
        
        <DatePicker
          value={endDate}
          onChange={onEndDateChange}
          label={endLabel}
          min={startDate}
          disabled={disabled}
          required={required}
        />
      </div>

      {error && (
        <p className="mt-2 text-sm text-semantic-error">
          {error}
        </p>
      )}

      {hint && !error && (
        <p className="mt-2 text-sm text-text-muted">
          {hint}
        </p>
      )}
    </div>
  );
};

export default DatePicker;