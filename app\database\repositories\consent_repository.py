"""
Repository for consent management operations.
Handles consent documents, user consents, and consent history.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
import asyncpg
from uuid import UUID

from app.database.repositories.base import BaseRepository
from app.models.consent_models import (
    ConsentDocument, ConsentDocumentCreate,
    UserConsent, UserConsentCreate, 
    ConsentLevel, ConsentStatus, ConsentCategory
)
from app.core.exceptions import ValidationError, ResourceNotFoundError
from app.core.timezone import now_est


class ConsentDocumentRepository(BaseRepository):
    """Repository for consent document operations."""
    
    def __init__(self):
        super().__init__("consent_documents", "document_id")
    
    async def find_active_by_type_and_language(
        self, 
        conn: asyncpg.Connection, 
        consent_type: str, 
        language: str = "en"
    ) -> Optional[ConsentDocument]:
        """Find active consent document by type and language."""
        query = """
            SELECT document_id, consent_type, title, content, version, level, category, 
                   language, is_active, effective_date, created_at, updated_at, deleted_at
            FROM consent_documents 
            WHERE consent_type = $1 AND language = $2 AND is_active = true AND deleted_at IS NULL
            ORDER BY effective_date DESC, created_at DESC
            LIMIT 1
        """
        
        row = await conn.fetchrow(query, consent_type, language)
        return ConsentDocument(**dict(row)) if row else None
    
    async def find_by_type_and_language(
        self, 
        conn: asyncpg.Connection, 
        consent_type: str, 
        language: str = "en"
    ) -> List[ConsentDocument]:
        """Find all consent documents by type and language."""
        query = """
            SELECT document_id, consent_type, title, content, version, level, category, 
                   language, is_active, effective_date, created_at, updated_at, deleted_at
            FROM consent_documents 
            WHERE consent_type = $1 AND language = $2 AND deleted_at IS NULL
            ORDER BY effective_date DESC, created_at DESC
        """
        
        rows = await conn.fetch(query, consent_type, language)
        return [ConsentDocument(**dict(row)) for row in rows]
    
    async def find_all_active(
        self, 
        conn: asyncpg.Connection,
        language: str = "en"
    ) -> List[ConsentDocument]:
        """Find all active consent documents for a language."""
        query = """
            SELECT document_id, consent_type, title, content, version, level, category, 
                   language, is_active, effective_date, created_at, updated_at, deleted_at
            FROM consent_documents 
            WHERE language = $1 AND is_active = true AND deleted_at IS NULL
            ORDER BY level DESC, consent_type ASC, effective_date DESC
        """
        
        rows = await conn.fetch(query, language)
        return [ConsentDocument(**dict(row)) for row in rows]
    
    async def find_by_level(
        self, 
        conn: asyncpg.Connection,
        level: ConsentLevel,
        language: str = "en"
    ) -> List[ConsentDocument]:
        """Find consent documents by level."""
        query = """
            SELECT document_id, consent_type, title, content, version, level, category, 
                   language, is_active, effective_date, created_at, updated_at, deleted_at
            FROM consent_documents 
            WHERE level = $1 AND language = $2 AND is_active = true AND deleted_at IS NULL
            ORDER BY consent_type ASC, effective_date DESC
        """
        
        rows = await conn.fetch(query, level.value, language)
        return [ConsentDocument(**dict(row)) for row in rows]
    
    async def create_consent_document(
        self, 
        conn: asyncpg.Connection, 
        document: ConsentDocumentCreate
    ) -> ConsentDocument:
        """Create a new consent document."""
        query = """
            INSERT INTO consent_documents 
            (consent_type, title, content, version, level, category, language, 
             is_active, effective_date, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
            RETURNING document_id, consent_type, title, content, version, level, category, 
                     language, is_active, effective_date, created_at, updated_at, deleted_at
        """
        
        now = now_est()
        row = await conn.fetchrow(
            query,
            document.consent_type,
            document.title,
            document.content,
            document.version,
            document.level.value,
            document.category.value,
            document.language,
            document.is_active,
            document.effective_date,
            now,
            now
        )
        
        return ConsentDocument(**dict(row))


class UserConsentRepository(BaseRepository):
    """Repository for user consent operations."""
    
    def __init__(self):
        super().__init__("user_consents", "consent_id")
    
    async def find_user_consent_by_type(
        self, 
        conn: asyncpg.Connection, 
        user_id: int, 
        consent_type: str
    ) -> Optional[UserConsent]:
        """Find user consent by type."""
        query = """
            SELECT uc.consent_id, uc.user_id, uc.document_id, uc.status,
                   uc.granted_at, uc.withdrawn_at, uc.expires_at,
                   uc.ip_address, uc.user_agent, uc.created_at, uc.updated_at, uc.deleted_at
            FROM user_consents uc
            JOIN consent_documents cd ON uc.document_id = cd.document_id
            WHERE uc.user_id = $1 AND cd.consent_type = $2 AND uc.deleted_at IS NULL
            ORDER BY uc.created_at DESC
            LIMIT 1
        """
        
        row = await conn.fetchrow(query, user_id, consent_type)
        if row:
            row_dict = dict(row)
            # Convert IP address to string if it's an IPv4Address object
            if row_dict.get('ip_address') is not None:
                row_dict['ip_address'] = str(row_dict['ip_address'])
            return UserConsent(**row_dict)
        return None
    
    async def find_user_consents(
        self, 
        conn: asyncpg.Connection, 
        user_id: int
    ) -> List[UserConsent]:
        """Find all user consents."""
        query = """
            SELECT consent_id, user_id, document_id, status,
                   granted_at, withdrawn_at, expires_at,
                   ip_address, user_agent, created_at, updated_at, deleted_at
            FROM user_consents 
            WHERE user_id = $1 AND deleted_at IS NULL
            ORDER BY created_at DESC
        """
        
        rows = await conn.fetch(query, user_id)
        consents = []
        for row in rows:
            row_dict = dict(row)
            # Convert IP address to string if it's an IPv4Address object
            if row_dict.get('ip_address') is not None:
                row_dict['ip_address'] = str(row_dict['ip_address'])
            consents.append(UserConsent(**row_dict))
        return consents
    
    async def create_user_consent(
        self, 
        conn: asyncpg.Connection, 
        consent: UserConsentCreate
    ) -> UserConsent:
        """Create a new user consent."""
        query = """
            INSERT INTO user_consents 
            (user_id, document_id, status, ip_address, user_agent, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            RETURNING consent_id, user_id, document_id, status,
                     granted_at, withdrawn_at, expires_at,
                     ip_address, user_agent, created_at, updated_at, deleted_at
        """
        
        now = now_est()
        row = await conn.fetchrow(
            query,
            consent.user_id,
            consent.document_id,
            consent.status.value,
            consent.ip_address,
            consent.user_agent,
            now,
            now
        )
        
        row_dict = dict(row)
        # Convert IP address to string if it's an IPv4Address object
        if row_dict.get('ip_address') is not None:
            row_dict['ip_address'] = str(row_dict['ip_address'])
        return UserConsent(**row_dict)
    
    async def update_consent_status(
        self, 
        conn: asyncpg.Connection, 
        consent_id: int, 
        status: ConsentStatus,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> Optional[UserConsent]:
        """Update consent status."""
        query = """
            UPDATE user_consents 
            SET status = $2, ip_address = $3, user_agent = $4, updated_at = $5
            WHERE consent_id = $1 AND deleted_at IS NULL
            RETURNING consent_id, user_id, document_id, status,
                     granted_at, withdrawn_at, expires_at,
                     ip_address, user_agent, created_at, updated_at, deleted_at
        """
        
        row = await conn.fetchrow(
            query, consent_id, status.value, ip_address, user_agent, now_est()
        )
        
        if row:
            row_dict = dict(row)
            # Convert IP address to string if it's an IPv4Address object
            if row_dict.get('ip_address') is not None:
                row_dict['ip_address'] = str(row_dict['ip_address'])
            return UserConsent(**row_dict)
        return None
    
    async def find_user_consents_by_level(
        self, 
        conn: asyncpg.Connection, 
        user_id: int, 
        level: ConsentLevel
    ) -> List[UserConsent]:
        """Find user consents by consent level."""
        query = """
            SELECT uc.consent_id, uc.user_id, uc.document_id, uc.status,
                   uc.granted_at, uc.withdrawn_at, uc.expires_at,
                   uc.ip_address, uc.user_agent, uc.created_at, uc.updated_at, uc.deleted_at
            FROM user_consents uc
            JOIN consent_documents cd ON uc.document_id = cd.document_id
            WHERE uc.user_id = $1 AND cd.level = $2 AND uc.deleted_at IS NULL
            ORDER BY uc.created_at DESC
        """
        
        rows = await conn.fetch(query, user_id, level.value)
        consents = []
        for row in rows:
            row_dict = dict(row)
            # Convert IP address to string if it's an IPv4Address object
            if row_dict.get('ip_address') is not None:
                row_dict['ip_address'] = str(row_dict['ip_address'])
            consents.append(UserConsent(**row_dict))
        return consents
    
    async def check_mandatory_consents_complete(
        self, 
        conn: asyncpg.Connection, 
        user_id: int
    ) -> bool:
        """Check if all mandatory consents are granted."""
        query = """
            SELECT COUNT(*) as missing_count
            FROM consent_documents cd
            LEFT JOIN user_consents uc ON cd.document_id = uc.document_id 
                                       AND uc.user_id = $1 
                                       AND uc.status = 'granted'
                                       AND uc.deleted_at IS NULL
            WHERE cd.level = 'level_1_mandatory' 
              AND cd.is_active = true 
              AND cd.deleted_at IS NULL
              AND uc.consent_id IS NULL
        """
        
        row = await conn.fetchrow(query, user_id)
        return row['missing_count'] == 0 if row else False
    
    async def get_missing_mandatory_consents(
        self, 
        conn: asyncpg.Connection, 
        user_id: int,
        language: str = "en"
    ) -> List[str]:
        """Get list of missing mandatory consent types."""
        query = """
            SELECT cd.consent_type
            FROM consent_documents cd
            LEFT JOIN user_consents uc ON cd.document_id = uc.document_id 
                                       AND uc.user_id = $1 
                                       AND uc.status = 'granted'
                                       AND uc.deleted_at IS NULL
            WHERE cd.level = 'level_1_mandatory' 
              AND cd.is_active = true 
              AND cd.language = $2
              AND cd.deleted_at IS NULL
              AND uc.consent_id IS NULL
        """
        
        rows = await conn.fetch(query, user_id, language)
        return [row['consent_type'] for row in rows]


class ConsentHistoryRepository(BaseRepository):
    """Repository for consent history operations."""
    
    def __init__(self):
        super().__init__("consent_history", "history_id")
    
    async def find_user_consent_history(
        self, 
        conn: asyncpg.Connection, 
        user_id: int, 
        consent_type: Optional[str] = None,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """Find user consent history."""
        base_query = """
            SELECT history_id, user_id, consent_type, document_id, action,
                   previous_status, new_status, document_version,
                   ip_address, user_agent, reason, created_at
            FROM consent_history 
            WHERE user_id = $1
        """
        
        params = [user_id]
        
        if consent_type:
            base_query += " AND consent_type = $2"
            params.append(consent_type)
            base_query += f" ORDER BY created_at DESC LIMIT ${len(params) + 1}"
            params.append(limit)
        else:
            base_query += f" ORDER BY created_at DESC LIMIT $2"
            params.append(limit)
        
        rows = await conn.fetch(base_query, *params)
        return [dict(row) for row in rows]
    
    async def create_history_entry(
        self, 
        conn: asyncpg.Connection,
        user_id: int,
        consent_type: str,
        document_id: int,
        action: str,
        previous_status: Optional[ConsentStatus],
        new_status: ConsentStatus,
        document_version: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        reason: Optional[str] = None
    ) -> Dict[str, Any]:
        """Create a new consent history entry."""
        query = """
            INSERT INTO consent_history 
            (user_id, consent_type, document_id, action, previous_status, new_status,
             document_version, ip_address, user_agent, reason, created_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
            RETURNING history_id, user_id, consent_type, document_id, action,
                     previous_status, new_status, document_version,
                     ip_address, user_agent, reason, created_at
        """
        
        row = await conn.fetchrow(
            query,
            user_id,
            consent_type,
            document_id,
            action,
            previous_status.value if previous_status else None,
            new_status.value,
            document_version,
            ip_address,
            user_agent,
            reason,
            now_est()
        )
        
        return dict(row)