-- Rollback: Client Profile Enhancements

-- Drop triggers first
DROP TRIGGER IF EXISTS update_completeness_on_learning_needs_change ON client_learning_needs;
DROP TRIGGER IF EXISTS update_completeness_on_preferences_change ON client_preferences;
DROP TRIGGER IF EXISTS update_completeness_on_emergency_contact_change ON client_emergency_contacts;
DROP TRIGGER IF EXISTS update_completeness_on_address_change ON client_addresses;

DROP TRIGGER IF EXISTS update_client_learning_needs_modtime ON client_learning_needs;
DROP TRIGGER IF EXISTS update_client_preferences_modtime ON client_preferences;
DROP TRIGGER IF EXISTS update_client_emergency_contacts_modtime ON client_emergency_contacts;
DROP TRIGGER IF EXISTS update_client_addresses_modtime ON client_addresses;

-- Drop functions
DROP FUNCTION IF EXISTS update_client_profile_completeness();
DROP FUNCTION IF EXISTS calculate_client_profile_completeness(INTEGER);
DROP FUNCTION IF EXISTS update_modified_column();

-- Drop indexes
DROP INDEX IF EXISTS idx_client_learning_needs_subjects;
DROP INDEX IF EXISTS idx_client_learning_needs_client_id;
DROP INDEX IF EXISTS idx_client_preferences_client_id;
DROP INDEX IF EXISTS idx_client_emergency_contacts_client_id;
DROP INDEX IF EXISTS idx_client_addresses_type;
DROP INDEX IF EXISTS idx_client_addresses_client_id;

-- Drop tables
DROP TABLE IF EXISTS client_learning_needs;
DROP TABLE IF EXISTS client_preferences;
DROP TABLE IF EXISTS client_emergency_contacts;
DROP TABLE IF EXISTS client_addresses;

-- Remove columns from client_profiles
ALTER TABLE client_profiles 
DROP COLUMN IF EXISTS profile_completeness,
DROP COLUMN IF EXISTS last_activity_at,
DROP COLUMN IF EXISTS internal_notes,
DROP COLUMN IF EXISTS referral_source,
DROP COLUMN IF EXISTS profile_photo_url;