import { useCallback, useMemo } from 'react';
import { useLanguage } from './useLanguage';

// Translation function type
type TranslationFunction = (key: string, variables?: Record<string, any>) => string;

// Formatting functions
interface FormattingFunctions {
  formatCurrency: (amount: number) => string;
  formatNumber: (number: number) => string;
  formatDate: (date: Date | string, format?: string) => string;
  formatTime: (time: Date | string, format?: string) => string;
  formatQuebecPhone: (phone: string) => string;
  formatQuebecPostalCode: (postalCode: string) => string;
}

// Pluralization function
type PluralizationFunction = (key: string, count: number, variables?: Record<string, any>) => string;

// Local translation cache
const translationCache = new Map<string, Record<string, any>>();

export const useTranslation = () => {
  const { currentLanguage, languageInfo } = useLanguage();

  // Get nested value from translation object
  const getNestedValue = useCallback((obj: any, path: string): any => {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }, []);

  // Get translations from cache or fetch
  const getTranslations = useCallback(async (namespace?: string): Promise<Record<string, any>> => {
    const cacheKey = `${currentLanguage}_${namespace || 'all'}`;
    
    if (translationCache.has(cacheKey)) {
      return translationCache.get(cacheKey)!;
    }

    try {
      // For now, we'll use a fallback approach
      // In a real implementation, you'd fetch from the API
      const fallbackTranslations = await import(`../locales/${currentLanguage}.json`);
      const translations = namespace 
        ? getNestedValue(fallbackTranslations.default, namespace) || {}
        : fallbackTranslations.default;
      
      translationCache.set(cacheKey, translations);
      return translations;
    } catch (error) {
      console.error(`Failed to load translations for ${currentLanguage}`, error);
      
      // Fallback to English if current language fails
      if (currentLanguage !== 'en') {
        try {
          const fallbackTranslations = await import('../locales/en.json');
          const translations = namespace 
            ? getNestedValue(fallbackTranslations.default, namespace) || {}
            : fallbackTranslations.default;
          
          return translations;
        } catch (fallbackError) {
          console.error('Failed to load fallback translations', fallbackError);
          return {};
        }
      }
      
      return {};
    }
  }, [currentLanguage, getNestedValue]);

  // Main translation function
  const t: TranslationFunction = useCallback((key: string, variables?: Record<string, any>) => {
    // For client-side rendering, we'll use a synchronous approach with cached translations
    const cacheKey = `${currentLanguage}_all`;
    const translations = translationCache.get(cacheKey) || {};
    
    let value = getNestedValue(translations, key);
    
    // Fallback to key if translation not found
    if (value === undefined) {
      value = key;
    }
    
    // Replace variables if provided
    if (typeof value === 'string' && variables) {
      Object.entries(variables).forEach(([varKey, varValue]) => {
        value = value.replace(new RegExp(`{${varKey}}`, 'g'), String(varValue));
      });
    }
    
    return String(value);
  }, [currentLanguage, getNestedValue]);

  // Pluralization function
  const tPlural: PluralizationFunction = useCallback((key: string, count: number, variables?: Record<string, any>) => {
    const pluralForm = count === 1 ? 'one' : 'other';
    const pluralKey = `plurals.${key}.${pluralForm}`;
    
    return t(pluralKey, { count, ...variables });
  }, [t]);

  // Legacy formatting functions for backward compatibility - will be removed later
  const legacyFormatting: FormattingFunctions = useMemo(() => ({
    formatCurrency: (amount: number) => `$${amount.toFixed(2)}`,
    formatNumber: (number: number) => number.toLocaleString(currentLanguage),
    formatDate: (date: Date | string, format?: string) => new Date(date).toLocaleDateString(currentLanguage),
    formatTime: (time: Date | string, format?: string) => new Date(time).toLocaleTimeString(currentLanguage),
    formatQuebecPhone: (phone: string) => phone,
    formatQuebecPostalCode: (postalCode: string) => postalCode
  }), [currentLanguage]);

  // Helper functions
  const helpers = useMemo(() => ({
    isQuebecFrench: () => languageInfo?.quebec_french || false,
    isFrench: currentLanguage === 'fr',
    isEnglish: currentLanguage === 'en',
    currentLanguage,
    languageInfo
  }), [currentLanguage, languageInfo]);

  // Initialize translations cache
  useMemo(() => {
    getTranslations().then(translations => {
      const cacheKey = `${currentLanguage}_all`;
      if (!translationCache.has(cacheKey)) {
        translationCache.set(cacheKey, translations);
      }
    });
  }, [currentLanguage, getTranslations]);

  return {
    t,
    tPlural,
    ...legacyFormatting,
    ...helpers,
    getTranslations
  };
};