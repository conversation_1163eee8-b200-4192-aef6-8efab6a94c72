import React from 'react';

interface RadioOption {
  value: string;
  label: string;
  disabled?: boolean;
}

interface RadioGroupProps {
  name: string;
  value?: string;
  options: RadioOption[];
  onChange?: (value: string) => void;
  error?: string;
  disabled?: boolean;
  className?: string;
}

export const RadioGroup: React.FC<RadioGroupProps> = ({
  name,
  value,
  options,
  onChange,
  error,
  disabled,
  className = ''
}) => {
  return (
    <div className={className}>
      <div className="space-y-2">
        {options.map((option) => (
          <label
            key={option.value}
            className={`flex items-center cursor-pointer ${
              disabled || option.disabled ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            <input
              type="radio"
              name={name}
              value={option.value}
              checked={value === option.value}
              onChange={(e) => onChange?.(e.target.value)}
              disabled={disabled || option.disabled}
              className="w-4 h-4 text-red-600 border-gray-300 focus:ring-red-500"
            />
            <span className="ml-2 text-sm text-gray-700">{option.label}</span>
          </label>
        ))}
      </div>
      {error && (
        <p className="mt-2 text-sm text-red-600">{error}</p>
      )}
    </div>
  );
};

// Export RadioGroupItem for compatibility
export const RadioGroupItem: React.FC<{ value: string; children: React.ReactNode }> = ({ value, children }) => {
  return (
    <label className="flex items-center cursor-pointer">
      <input type="radio" value={value} className="w-4 h-4 text-red-600 border-gray-300 focus:ring-red-500" />
      <span className="ml-2 text-sm text-gray-700">{children}</span>
    </label>
  );
};