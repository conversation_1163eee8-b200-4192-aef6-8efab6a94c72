import React from 'react';
import { clsx } from 'clsx';

interface Tab {
  id: string;
  label: string;
  icon?: React.ReactNode;
  badge?: React.ReactNode;
  disabled?: boolean;
}

interface TabsProps {
  tabs: Tab[];
  activeTab: string;
  onChange: (tabId: string) => void;
  variant?: 'line' | 'pills' | 'solid';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  className?: string;
}

export const Tabs: React.FC<TabsProps> = ({
  tabs,
  activeTab,
  onChange,
  variant = 'line',
  size = 'md',
  fullWidth = false,
  className,
}) => {
  const baseContainerStyles = {
    line: 'border-b border-border-primary',
    pills: 'bg-background-tertiary p-1 rounded-lg',
    solid: '',
  };

  const baseTabStyles = 'relative font-medium transition-all duration-200 flex items-center justify-center gap-2 focus:outline-none';

  const variantStyles = {
    line: {
      base: 'text-text-secondary hover:text-text-primary',
      active: 'text-accent-red',
      indicator: 'absolute bottom-0 left-0 right-0 h-0.5 bg-accent-red',
    },
    pills: {
      base: 'text-text-secondary hover:text-text-primary rounded-md',
      active: 'bg-white text-accent-red shadow-soft',
      indicator: '',
    },
    solid: {
      base: 'text-text-secondary hover:text-text-primary border-b-2 border-transparent',
      active: 'text-accent-red bg-red-50 border-accent-red',
      indicator: '',
    },
  };

  const sizeStyles = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg',
  };

  const iconSizes = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
  };

  return (
    <div
      className={clsx(
        'flex',
        baseContainerStyles[variant],
        fullWidth && 'w-full',
        className
      )}
    >
      <div
        className={clsx(
          'flex',
          variant === 'pills' ? 'gap-1' : 'gap-0',
          fullWidth && 'w-full'
        )}
      >
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => !tab.disabled && onChange(tab.id)}
            disabled={tab.disabled}
            className={clsx(
              baseTabStyles,
              sizeStyles[size],
              variantStyles[variant].base,
              activeTab === tab.id && variantStyles[variant].active,
              tab.disabled && 'opacity-50 cursor-not-allowed',
              fullWidth && 'flex-1',
              variant === 'line' && 'pb-3'
            )}
          >
            {tab.icon && (
              <span className={iconSizes[size]}>{tab.icon}</span>
            )}
            {tab.label}
            {tab.badge && (
              <span className="ml-1">{tab.badge}</span>
            )}
            {activeTab === tab.id && variant === 'line' && (
              <span className={variantStyles[variant].indicator} />
            )}
          </button>
        ))}
      </div>
    </div>
  );
};

interface TabPanelProps {
  isActive: boolean;
  children: React.ReactNode;
  className?: string;
}

export const TabPanel: React.FC<TabPanelProps> = ({
  isActive,
  children,
  className,
}) => {
  if (!isActive) return null;

  return (
    <div className={clsx('animate-fadeIn', className)}>
      {children}
    </div>
  );
};

interface TabContentProps {
  activeTab: string;
  children: React.ReactNode;
  className?: string;
}

export const TabContent: React.FC<TabContentProps> = ({
  activeTab,
  children,
  className,
}) => {
  return (
    <div className={className}>
      {React.Children.map(children, (child) => {
        if (React.isValidElement(child) && child.props.id === activeTab) {
          return child;
        }
        return null;
      })}
    </div>
  );
};

export default Tabs;