import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Input } from '../../common/Input';
import Button from '../../common/Button';
import { Select } from '../../common/Select';
import { 
  Briefcase,
  Users,
  Monitor,
  BookOpen,
  Calendar,
  Target,
  Heart,
  Save
} from 'lucide-react';

interface ExperienceFormProps {
  profile: {
    years_of_experience: number;
    years_online_teaching: number;
    years_tutoring: number;
    students_taught_total: number;
    current_occupation: string | null;
    teaching_methodology: string[];
    special_needs_experience: boolean;
    age_groups_experience: string[];
    subject_expertise?: Record<string, string>;
    success_stories?: string;
  };
  onSave: (data: any) => Promise<void>;
}

export const ExperienceForm: React.FC<ExperienceFormProps> = ({
  profile,
  onSave
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    years_of_experience: profile.years_of_experience?.toString() || '0',
    years_online_teaching: profile.years_online_teaching?.toString() || '0',
    years_tutoring: profile.years_tutoring?.toString() || '0',
    students_taught_total: profile.students_taught_total?.toString() || '0',
    current_occupation: profile.current_occupation || '',
    teaching_methodology: profile.teaching_methodology || [],
    special_needs_experience: profile.special_needs_experience || false,
    age_groups_experience: profile.age_groups_experience || [],
    subject_expertise: profile.subject_expertise || {},
    success_stories: profile.success_stories || ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const methodologyOptions = [
    { value: 'one_on_one', label: t('tutor.experience.methodology.oneOnOne') },
    { value: 'group_sessions', label: t('tutor.experience.methodology.groupSessions') },
    { value: 'interactive', label: t('tutor.experience.methodology.interactive') },
    { value: 'lecture_based', label: t('tutor.experience.methodology.lectureBased') },
    { value: 'project_based', label: t('tutor.experience.methodology.projectBased') },
    { value: 'flipped_classroom', label: t('tutor.experience.methodology.flippedClassroom') },
    { value: 'gamification', label: t('tutor.experience.methodology.gamification') },
    { value: 'differentiated', label: t('tutor.experience.methodology.differentiated') }
  ];

  const ageGroupOptions = [
    { value: 'preschool', label: t('tutor.experience.ageGroups.preschool') },
    { value: 'elementary', label: t('tutor.experience.ageGroups.elementary') },
    { value: 'middle_school', label: t('tutor.experience.ageGroups.middleSchool') },
    { value: 'high_school', label: t('tutor.experience.ageGroups.highSchool') },
    { value: 'college', label: t('tutor.experience.ageGroups.college') },
    { value: 'adult', label: t('tutor.experience.ageGroups.adult') }
  ];

  const subjectOptions = [
    { value: 'mathematics', label: t('tutor.subjects.mathematics') },
    { value: 'science', label: t('tutor.subjects.science') },
    { value: 'english', label: t('tutor.subjects.english') },
    { value: 'french', label: t('tutor.subjects.french') },
    { value: 'history', label: t('tutor.subjects.history') },
    { value: 'geography', label: t('tutor.subjects.geography') },
    { value: 'computer_science', label: t('tutor.subjects.computerScience') },
    { value: 'other', label: t('tutor.subjects.other') }
  ];

  const expertiseOptions = [
    { value: 'beginner', label: t('tutor.experience.expertise.beginner') },
    { value: 'intermediate', label: t('tutor.experience.expertise.intermediate') },
    { value: 'advanced', label: t('tutor.experience.expertise.advanced') },
    { value: 'expert', label: t('tutor.experience.expertise.expert') }
  ];

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    const yearsExp = parseInt(formData.years_of_experience);
    const yearsOnline = parseInt(formData.years_online_teaching);
    const yearsTutoring = parseInt(formData.years_tutoring);
    
    if (yearsOnline > yearsExp || yearsTutoring > yearsExp) {
      newErrors.years_of_experience = t('tutor.experience.errors.invalidYears');
    }
    
    if (!formData.current_occupation.trim()) {
      newErrors.current_occupation = t('tutor.experience.errors.occupationRequired');
    }
    
    if (formData.teaching_methodology.length === 0) {
      newErrors.teaching_methodology = t('tutor.experience.errors.methodologyRequired');
    }
    
    if (formData.age_groups_experience.length === 0) {
      newErrors.age_groups_experience = t('tutor.experience.errors.ageGroupsRequired');
    }
    
    if (Object.keys(formData.subject_expertise).length === 0) {
      newErrors.subject_expertise = t('tutor.experience.errors.subjectsRequired');
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setLoading(true);
    try {
      await onSave({
        ...formData,
        years_of_experience: parseInt(formData.years_of_experience),
        years_online_teaching: parseInt(formData.years_online_teaching),
        years_tutoring: parseInt(formData.years_tutoring),
        students_taught_total: parseInt(formData.students_taught_total)
      });
    } catch (error) {
      // Error is handled in parent component
    } finally {
      setLoading(false);
    }
  };

  const handleMethodologyToggle = (methodology: string) => {
    const newMethodology = formData.teaching_methodology.includes(methodology)
      ? formData.teaching_methodology.filter(m => m !== methodology)
      : [...formData.teaching_methodology, methodology];
    
    setFormData({
      ...formData,
      teaching_methodology: newMethodology
    });
  };

  const handleAgeGroupToggle = (ageGroup: string) => {
    const newAgeGroups = formData.age_groups_experience.includes(ageGroup)
      ? formData.age_groups_experience.filter(a => a !== ageGroup)
      : [...formData.age_groups_experience, ageGroup];
    
    setFormData({
      ...formData,
      age_groups_experience: newAgeGroups
    });
  };

  const handleSubjectExpertise = (subject: string, level: string) => {
    setFormData({
      ...formData,
      subject_expertise: {
        ...formData.subject_expertise,
        [subject]: level
      }
    });
  };

  const removeSubject = (subject: string) => {
    const { [subject]: _, ...rest } = formData.subject_expertise;
    setFormData({
      ...formData,
      subject_expertise: rest
    });
  };

  return (
    <form onSubmit={handleSubmit} className="p-6 space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-text-primary mb-4">
          {t('tutor.experience.title')}
        </h3>
        
        {/* Years of Experience */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              {t('tutor.experience.fields.totalYears')}
            </label>
            <Input
              type="number"
              min="0"
              leftIcon={<Briefcase className="w-4 h-4" />}
              value={formData.years_of_experience}
              onChange={(e) => setFormData({ ...formData, years_of_experience: e.target.value })}
              error={errors.years_of_experience}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              {t('tutor.experience.fields.onlineYears')}
            </label>
            <Input
              type="number"
              min="0"
              leftIcon={<Monitor className="w-4 h-4" />}
              value={formData.years_online_teaching}
              onChange={(e) => setFormData({ ...formData, years_online_teaching: e.target.value })}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              {t('tutor.experience.fields.tutoringYears')}
            </label>
            <Input
              type="number"
              min="0"
              leftIcon={<BookOpen className="w-4 h-4" />}
              value={formData.years_tutoring}
              onChange={(e) => setFormData({ ...formData, years_tutoring: e.target.value })}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              {t('tutor.experience.fields.totalStudents')}
            </label>
            <Input
              type="number"
              min="0"
              leftIcon={<Users className="w-4 h-4" />}
              value={formData.students_taught_total}
              onChange={(e) => setFormData({ ...formData, students_taught_total: e.target.value })}
            />
          </div>
        </div>

        {/* Current Occupation */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-text-primary mb-2">
            {t('tutor.experience.fields.currentOccupation')}
          </label>
          <Input
            leftIcon={<Briefcase className="w-4 h-4" />}
            value={formData.current_occupation}
            onChange={(e) => setFormData({ ...formData, current_occupation: e.target.value })}
            placeholder={t('tutor.experience.placeholders.occupation')}
            error={errors.current_occupation}
          />
        </div>

        {/* Teaching Methodology */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-text-primary mb-2">
            {t('tutor.experience.fields.methodology')}
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
            {methodologyOptions.map((method) => (
              <label
                key={method.value}
                className="flex items-center gap-2 p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50"
              >
                <input
                  type="checkbox"
                  checked={formData.teaching_methodology.includes(method.value)}
                  onChange={() => handleMethodologyToggle(method.value)}
                  className="w-4 h-4 text-accent-red focus:ring-accent-red/20 border-gray-300 rounded"
                />
                <span className="text-sm text-text-primary">{method.label}</span>
              </label>
            ))}
          </div>
          {errors.teaching_methodology && (
            <p className="text-sm text-red-600 mt-1">{errors.teaching_methodology}</p>
          )}
        </div>

        {/* Age Groups */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-text-primary mb-2">
            {t('tutor.experience.fields.ageGroups')}
          </label>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {ageGroupOptions.map((age) => (
              <label
                key={age.value}
                className="flex items-center gap-2 p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50"
              >
                <input
                  type="checkbox"
                  checked={formData.age_groups_experience.includes(age.value)}
                  onChange={() => handleAgeGroupToggle(age.value)}
                  className="w-4 h-4 text-accent-red focus:ring-accent-red/20 border-gray-300 rounded"
                />
                <span className="text-sm text-text-primary">{age.label}</span>
              </label>
            ))}
          </div>
          {errors.age_groups_experience && (
            <p className="text-sm text-red-600 mt-1">{errors.age_groups_experience}</p>
          )}
        </div>

        {/* Special Needs Experience */}
        <div className="mb-6">
          <label className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
            <input
              type="checkbox"
              checked={formData.special_needs_experience}
              onChange={(e) => setFormData({ ...formData, special_needs_experience: e.target.checked })}
              className="w-4 h-4 text-accent-red focus:ring-accent-red/20 border-gray-300 rounded"
            />
            <div className="flex items-center gap-2">
              <Heart className="w-4 h-4 text-accent-red" />
              <span className="text-sm font-medium text-text-primary">
                {t('tutor.experience.fields.specialNeeds')}
              </span>
            </div>
          </label>
        </div>

        {/* Subject Expertise */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-text-primary mb-2">
            <Target className="w-4 h-4 inline mr-2" />
            {t('tutor.experience.fields.subjectExpertise')}
          </label>
          <div className="space-y-3">
            {subjectOptions.map((subject) => (
              <div key={subject.value} className="flex items-center gap-3">
                <span className="w-32 text-sm text-text-primary">{subject.label}</span>
                <Select
                  value={formData.subject_expertise[subject.value] || ''}
                  onChange={(value) => value ? handleSubjectExpertise(subject.value, value) : removeSubject(subject.value)}
                  options={[
                    { value: '', label: t('tutor.experience.notTaught') },
                    ...expertiseOptions
                  ]}
                  placeholder={t('tutor.experience.placeholders.selectLevel')}
                />
              </div>
            ))}
          </div>
          {errors.subject_expertise && (
            <p className="text-sm text-red-600 mt-1">{errors.subject_expertise}</p>
          )}
        </div>

        {/* Success Stories */}
        <div>
          <label className="block text-sm font-medium text-text-primary mb-2">
            {t('tutor.experience.fields.successStories')}
          </label>
          <textarea
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent-red focus:border-accent-red resize-none"
            rows={4}
            value={formData.success_stories}
            onChange={(e) => setFormData({ ...formData, success_stories: e.target.value })}
            placeholder={t('tutor.experience.placeholders.successStories')}
          />
          <p className="text-sm text-text-secondary mt-1">
            {t('tutor.experience.successStoriesHelp')}
          </p>
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-end pt-4 border-t border-gray-200">
        <Button
          type="submit"
          loading={loading}
          leftIcon={<Save className="w-4 h-4" />}
        >
          {t('common.save')}
        </Button>
      </div>
    </form>
  );
};