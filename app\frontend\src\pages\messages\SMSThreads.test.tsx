import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { I18nextProvider } from 'react-i18next';
import SMSThreads from './SMSThreads';
import i18n from '../../i18n';

// Mock date-fns
vi.mock('date-fns', () => ({
  format: vi.fn((date, formatString) => {
    if (formatString === 'HH:mm') return '14:30';
    if (formatString === 'MMM dd, yyyy HH:mm') return 'Jan 15, 2024 14:30';
    return '2024-01-15';
  }),
}));

const renderSMSThreads = () => {
  return render(
    <I18nextProvider i18n={i18n}>
      <SMSThreads />
    </I18nextProvider>
  );
};

describe('SMSThreads', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders SMS threads interface', () => {
    renderSMSThreads();
    
    expect(screen.getByPlaceholderText('Search conversations...')).toBeInTheDocument();
    expect(screen.getByText('All')).toBeInTheDocument(); // Filter dropdown
  });

  it('displays conversation threads', () => {
    renderSMSThreads();
    
    // Check for mock conversation data
    expect(screen.getByText('Marie Dubois')).toBeInTheDocument();
    expect(screen.getByText('John Smith')).toBeInTheDocument();
    expect(screen.getByText('Sophie Martin')).toBeInTheDocument();
  });

  it('shows role indicators for participants', () => {
    renderSMSThreads();
    
    // Should display role emojis
    const roleIndicators = screen.getAllByText('👤'); // Client emoji
    expect(roleIndicators.length).toBeGreaterThan(0);
    
    const tutorIndicators = screen.getAllByText('👨‍🏫'); // Tutor emoji
    expect(tutorIndicators.length).toBeGreaterThan(0);
  });

  it('filters conversations by search query', async () => {
    const user = userEvent.setup();
    renderSMSThreads();
    
    const searchInput = screen.getByPlaceholderText('Search conversations...');
    
    await user.type(searchInput, 'Marie');
    
    await waitFor(() => {
      expect(screen.getByText('Marie Dubois')).toBeInTheDocument();
      expect(screen.queryByText('John Smith')).not.toBeInTheDocument();
    });
  });

  it('filters by role type', async () => {
    renderSMSThreads();
    
    const roleFilter = screen.getByDisplayValue('All');
    fireEvent.change(roleFilter, { target: { value: 'client' } });
    
    await waitFor(() => {
      // Should only show client conversations
      expect(screen.getByText('Marie Dubois')).toBeInTheDocument();
      expect(screen.getByText('Sophie Martin')).toBeInTheDocument();
    });
  });

  it('selects conversation when clicked', async () => {
    renderSMSThreads();
    
    const conversation = screen.getByText('Marie Dubois').closest('div');
    fireEvent.click(conversation!);
    
    await waitFor(() => {
      // Should open conversation view
      expect(screen.getByText('Marie Dubois')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Type a message...')).toBeInTheDocument();
    });
  });

  it('displays message status indicators', () => {
    renderSMSThreads();
    
    // Click on a conversation to view messages
    const conversation = screen.getByText('Marie Dubois').closest('div');
    fireEvent.click(conversation!);
    
    // Should show message status icons (mocked)
    const statusIcons = screen.getAllByTestId('check-circle');
    expect(statusIcons.length).toBeGreaterThan(0);
  });

  it('shows unread message count', () => {
    renderSMSThreads();
    
    // Marie Dubois should have 2 unread messages
    expect(screen.getByText('2')).toBeInTheDocument();
  });

  it('handles message sending', async () => {
    const user = userEvent.setup();
    renderSMSThreads();
    
    // Select a conversation first
    const conversation = screen.getByText('Marie Dubois').closest('div');
    fireEvent.click(conversation!);
    
    const messageInput = screen.getByPlaceholderText('Type a message...');
    const sendButton = screen.getByRole('button', { name: /send/i });
    
    await user.type(messageInput, 'Test message');
    fireEvent.click(sendButton);
    
    await waitFor(() => {
      expect(messageInput).toHaveValue('');
    });
  });

  it('sends message on Enter key press', async () => {
    const user = userEvent.setup();
    renderSMSThreads();
    
    // Select a conversation first
    const conversation = screen.getByText('Marie Dubois').closest('div');
    fireEvent.click(conversation!);
    
    const messageInput = screen.getByPlaceholderText('Type a message...');
    
    await user.type(messageInput, 'Test message{enter}');
    
    await waitFor(() => {
      expect(messageInput).toHaveValue('');
    });
  });

  it('prevents sending empty messages', () => {
    renderSMSThreads();
    
    // Select a conversation first
    const conversation = screen.getByText('Marie Dubois').closest('div');
    fireEvent.click(conversation!);
    
    const sendButton = screen.getByRole('button', { name: /send/i });
    expect(sendButton).toBeDisabled();
  });

  it('displays conversation header with participant info', () => {
    renderSMSThreads();
    
    // Select a conversation
    const conversation = screen.getByText('Marie Dubois').closest('div');
    fireEvent.click(conversation!);
    
    // Should show participant details in header
    expect(screen.getByText('Marie Dubois')).toBeInTheDocument();
    expect(screen.getByText('+****************')).toBeInTheDocument();
  });

  it('shows message timestamps', () => {
    renderSMSThreads();
    
    // Select a conversation to view messages
    const conversation = screen.getByText('Marie Dubois').closest('div');
    fireEvent.click(conversation!);
    
    // Should display formatted timestamps
    expect(screen.getByText('14:30')).toBeInTheDocument();
  });

  it('handles message direction correctly', () => {
    renderSMSThreads();
    
    // Select a conversation
    const conversation = screen.getByText('Marie Dubois').closest('div');
    fireEvent.click(conversation!);
    
    // Should distinguish between incoming and outgoing messages
    const incomingMessage = screen.getByText('Merci pour la séance d\'aujourd\'hui!');
    const outgoingMessage = screen.getByText('Great! See you then.');
    
    expect(incomingMessage).toBeInTheDocument();
    expect(outgoingMessage).toBeInTheDocument();
  });

  it('displays SMS charges warning', () => {
    renderSMSThreads();
    
    // Select a conversation to see the input area
    const conversation = screen.getByText('Marie Dubois').closest('div');
    fireEvent.click(conversation!);
    
    expect(screen.getByText(/SMS charges may apply/)).toBeInTheDocument();
  });

  it('shows empty state when no conversation selected', () => {
    renderSMSThreads();
    
    // Initially no conversation should be selected
    expect(screen.getByText('Select a conversation to start messaging')).toBeInTheDocument();
  });

  it('handles phone number formatting', () => {
    renderSMSThreads();
    
    // Phone numbers should be properly formatted
    expect(screen.getByText('+****************')).toBeInTheDocument();
    expect(screen.getByText('+****************')).toBeInTheDocument();
  });
});