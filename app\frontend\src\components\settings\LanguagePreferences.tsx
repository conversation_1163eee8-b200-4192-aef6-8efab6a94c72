import React, { useState, useEffect } from 'react';
import { Settings, Globe, CheckCircle, AlertCircle, History, Zap, Save } from 'lucide-react';
import { useTranslation } from '../../hooks/useTranslation';
import { useLanguage } from '../../hooks/useLanguage';
import { LanguageSwitcher } from '../common/LanguageSwitcher';

interface LanguagePreference {
  preferred_language: string;
  language_auto_detect: boolean;
  quebec_french_preference: boolean;
  language_source: string;
  updated_at: string;
}

interface LanguageContext {
  user_preference: LanguagePreference | null;
  detected_language: {
    detected_language: string;
    confidence: number;
    source: string;
    quebec_indicators: string[];
  };
  effective_language: string;
  auto_detect_enabled: boolean;
  quebec_french_indicators: string[];
}

interface HistoryItem {
  preference_history_id: number;
  previous_language: string | null;
  new_language: string;
  change_source: string;
  change_reason: string;
  created_at: string;
  browser_language?: string;
}

export const LanguagePreferences: React.FC = () => {
  const { t, formatDate, isQuebecFrench } = useTranslation();
  const { 
    currentLanguage, 
    isLoading, 
    error,
    updateLanguagePreferences,
    getLanguagePreferences,
    autoDetectLanguage,
    getLanguageHistory
  } = useLanguage();

  const [preferences, setPreferences] = useState<LanguageContext | null>(null);
  const [history, setHistory] = useState<HistoryItem[]>([]);
  const [localSettings, setLocalSettings] = useState({
    preferred_language: currentLanguage,
    language_auto_detect: true,
    quebec_french_preference: true
  });
  const [hasChanges, setHasChanges] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  const [showHistory, setShowHistory] = useState(false);

  // Load preferences on mount
  useEffect(() => {
    loadPreferences();
    loadHistory();
  }, []);

  // Update local settings when preferences change
  useEffect(() => {
    if (preferences?.user_preference) {
      setLocalSettings({
        preferred_language: preferences.user_preference.preferred_language,
        language_auto_detect: preferences.user_preference.language_auto_detect,
        quebec_french_preference: preferences.user_preference.quebec_french_preference
      });
    }
  }, [preferences]);

  // Check for changes
  useEffect(() => {
    if (preferences?.user_preference) {
      const hasChanged = (
        localSettings.preferred_language !== preferences.user_preference.preferred_language ||
        localSettings.language_auto_detect !== preferences.user_preference.language_auto_detect ||
        localSettings.quebec_french_preference !== preferences.user_preference.quebec_french_preference
      );
      setHasChanges(hasChanged);
    }
  }, [localSettings, preferences]);

  const loadPreferences = async () => {
    try {
      const data = await getLanguagePreferences();
      if (data) {
        setPreferences(data);
      }
    } catch (err) {
      console.error('Failed to load language preferences:', err);
    }
  };

  const loadHistory = async () => {
    try {
      const data = await getLanguageHistory(20);
      if (data?.history) {
        setHistory(data.history);
      }
    } catch (err) {
      console.error('Failed to load language history:', err);
    }
  };

  const handleSavePreferences = async () => {
    setSaveLoading(true);
    try {
      const result = await updateLanguagePreferences({
        ...localSettings,
        persist: true
      });
      
      if (result) {
        await loadPreferences();
        await loadHistory();
        setHasChanges(false);
      }
    } catch (err) {
      console.error('Failed to save preferences:', err);
    } finally {
      setSaveLoading(false);
    }
  };

  const handleAutoDetect = async () => {
    try {
      const result = await autoDetectLanguage();
      if (result) {
        await loadPreferences();
        await loadHistory();
      }
    } catch (err) {
      console.error('Failed to auto-detect language:', err);
    }
  };

  const getSourceLabel = (source: string) => {
    const labels = {
      'manual': t('settings.language.source.manual'),
      'auto': t('settings.language.source.auto'),
      'browser': t('settings.language.source.browser'),
      'system': t('settings.language.source.system'),
      'user_manual': t('settings.language.source.user_manual'),
      'auto_detect': t('settings.language.source.auto_detect'),
      'browser_detect': t('settings.language.source.browser_detect'),
      'admin_override': t('settings.language.source.admin_override')
    };
    return labels[source] || source;
  };

  const getLanguageName = (code: string) => {
    return code === 'fr' ? 'Français (Québec)' : 'English';
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
        <span className="ml-3 text-gray-600">{t('common.loading')}</span>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3 mb-8">
        <Settings className="w-6 h-6 text-red-600" />
        <h1 className="text-2xl font-bold text-gray-900">
          {t('settings.language.title')}
        </h1>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-3">
          <AlertCircle className="w-5 h-5 text-red-600 flex-shrink-0" />
          <span className="text-red-800">{error}</span>
        </div>
      )}

      {/* Current Status */}
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <h2 className="text-lg font-semibold text-red-900 mb-4 flex items-center gap-2">
          <Globe className="w-5 h-5" />
          {t('settings.language.current_status')}
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium text-red-800">{t('settings.language.current_language')}:</span>
            <span className="ml-2 text-red-900">{getLanguageName(currentLanguage)}</span>
          </div>
          
          {preferences?.detected_language && (
            <div>
              <span className="font-medium text-red-800">{t('settings.language.detected_language')}:</span>
              <span className="ml-2 text-red-900">
                {getLanguageName(preferences.detected_language.detected_language)}
              </span>
              <span className={`ml-2 text-xs ${getConfidenceColor(preferences.detected_language.confidence)}`}>
                ({Math.round(preferences.detected_language.confidence * 100)}% {t('settings.language.confidence')})
              </span>
            </div>
          )}
          
          {preferences?.user_preference && (
            <>
              <div>
                <span className="font-medium text-red-800">{t('settings.language.source')}:</span>
                <span className="ml-2 text-red-900">
                  {getSourceLabel(preferences.user_preference.language_source)}
                </span>
              </div>
              
              <div>
                <span className="font-medium text-red-800">{t('settings.language.last_updated')}:</span>
                <span className="ml-2 text-red-900">
                  {formatDate(preferences.user_preference.updated_at)}
                </span>
              </div>
            </>
          )}
        </div>

        {preferences?.quebec_french_indicators && preferences.quebec_french_indicators.length > 0 && (
          <div className="mt-4 p-3 bg-red-100 rounded-lg">
            <span className="text-xs font-medium text-red-800">
              {t('settings.language.quebec_indicators')}:
            </span>
            <div className="mt-1 flex flex-wrap gap-1">
              {preferences.quebec_french_indicators.map((indicator, index) => (
                <span key={index} className="inline-block px-2 py-1 bg-red-200 text-red-800 text-xs rounded">
                  {indicator}
                </span>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Language Settings */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          {t('settings.language.preferences')}
        </h2>

        <div className="space-y-6">
          {/* Language Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              {t('settings.language.preferred_language')}
            </label>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <button
                onClick={() => setLocalSettings(prev => ({ ...prev, preferred_language: 'en' }))}
                className={`
                  flex items-center gap-3 p-4 rounded-lg border-2 transition-all
                  ${localSettings.preferred_language === 'en'
                    ? 'border-red-500 bg-red-50 text-red-900'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                  }
                `}
              >
                <span className="text-xl">🇨🇦</span>
                <div className="text-left">
                  <div className="font-medium">English</div>
                  <div className="text-sm text-gray-600">Canadian English</div>
                </div>
                {localSettings.preferred_language === 'en' && (
                  <CheckCircle className="w-5 h-5 text-red-600 ml-auto" />
                )}
              </button>

              <button
                onClick={() => setLocalSettings(prev => ({ ...prev, preferred_language: 'fr' }))}
                className={`
                  flex items-center gap-3 p-4 rounded-lg border-2 transition-all
                  ${localSettings.preferred_language === 'fr'
                    ? 'border-red-500 bg-red-50 text-red-900'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                  }
                `}
              >
                <span className="text-xl">🇨🇦</span>
                <div className="text-left">
                  <div className="font-medium">Français (Québec)</div>
                  <div className="text-sm text-gray-600">French Canadian</div>
                </div>
                {localSettings.preferred_language === 'fr' && (
                  <CheckCircle className="w-5 h-5 text-red-600 ml-auto" />
                )}
              </button>
            </div>
          </div>

          {/* Auto-detect Setting */}
          <div className="flex items-start gap-3">
            <input
              type="checkbox"
              id="auto-detect"
              checked={localSettings.language_auto_detect}
              onChange={(e) => setLocalSettings(prev => ({ 
                ...prev, 
                language_auto_detect: e.target.checked 
              }))}
              className="mt-1 h-4 w-4 text-red-600 border-gray-300 rounded focus:ring-red-500"
            />
            <div>
              <label htmlFor="auto-detect" className="text-sm font-medium text-gray-700">
                {t('settings.language.auto_detect')}
              </label>
              <p className="text-sm text-gray-600 mt-1">
                {t('settings.language.auto_detect_description')}
              </p>
            </div>
          </div>

          {/* Quebec French Preference */}
          {localSettings.preferred_language === 'fr' && (
            <div className="flex items-start gap-3">
              <input
                type="checkbox"
                id="quebec-french"
                checked={localSettings.quebec_french_preference}
                onChange={(e) => setLocalSettings(prev => ({ 
                  ...prev, 
                  quebec_french_preference: e.target.checked 
                }))}
                className="mt-1 h-4 w-4 text-red-600 border-gray-300 rounded focus:ring-red-500"
              />
              <div>
                <label htmlFor="quebec-french" className="text-sm font-medium text-gray-700">
                  {t('settings.language.quebec_french_preference')}
                </label>
                <p className="text-sm text-gray-600 mt-1">
                  {t('settings.language.quebec_french_description')}
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="mt-6 flex flex-wrap gap-3">
          <button
            onClick={handleSavePreferences}
            disabled={!hasChanges || saveLoading}
            className={`
              flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all
              ${hasChanges && !saveLoading
                ? 'bg-red-600 text-white hover:bg-red-700'
                : 'bg-gray-100 text-gray-400 cursor-not-allowed'
              }
            `}
          >
            <Save className="w-4 h-4" />
            {saveLoading ? t('common.saving') : t('common.save')}
          </button>

          <button
            onClick={handleAutoDetect}
            disabled={isLoading}
            className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors"
          >
            <Zap className="w-4 h-4" />
            {t('settings.language.auto_detect_now')}
          </button>

          <button
            onClick={() => setShowHistory(!showHistory)}
            className="flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg font-medium hover:bg-gray-700 transition-colors"
          >
            <History className="w-4 h-4" />
            {t('settings.language.view_history')}
          </button>
        </div>
      </div>

      {/* History Section */}
      {showHistory && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            {t('settings.language.change_history')}
          </h2>

          {history.length === 0 ? (
            <p className="text-gray-600 text-center py-8">
              {t('settings.language.no_history')}
            </p>
          ) : (
            <div className="space-y-3">
              {history.map((item) => (
                <div
                  key={item.preference_history_id}
                  className="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
                >
                  <div className="flex items-center gap-4">
                    <div className="w-2 h-2 bg-red-600 rounded-full"></div>
                    <div>
                      <div className="font-medium text-gray-900">
                        {item.previous_language && (
                          <span className="text-gray-600">
                            {getLanguageName(item.previous_language)} →{' '}
                          </span>
                        )}
                        <span className="text-red-600">
                          {getLanguageName(item.new_language)}
                        </span>
                      </div>
                      <div className="text-sm text-gray-600">
                        {getSourceLabel(item.change_source)}
                        {item.change_reason && ` • ${item.change_reason}`}
                      </div>
                    </div>
                  </div>
                  <div className="text-sm text-gray-500">
                    {formatDate(item.created_at)}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Quick Language Switcher */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          {t('settings.language.quick_switch')}
        </h2>
        <p className="text-gray-600 text-sm mb-4">
          {t('settings.language.quick_switch_description')}
        </p>
        <LanguageSwitcher variant="full" showFlags={true} />
      </div>
    </div>
  );
};
