import React, { useState, useEffect } from 'react';
import { X, DollarSign, Calendar, User, Clock, ChevronRight, CreditCard } from 'lucide-react';
import { format } from 'date-fns';
import { useTranslation } from 'react-i18next';
import api from '../../utils/api';

interface TutorPaymentDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  payment: any;
}

interface AuditLog {
  audit_id: number;
  action: string;
  performed_by: number;
  performed_by_name: string;
  performed_by_role: string;
  field_name?: string;
  old_value?: string;
  new_value?: string;
  change_reason?: string;
  amount_before?: number;
  amount_after?: number;
  stripe_transaction_id?: string;
  created_at: string;
}

const TutorPaymentDetailsModal: React.FC<TutorPaymentDetailsModalProps> = ({
  isOpen,
  onClose,
  payment
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<'details' | 'audit'>('details');
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isOpen && payment && activeTab === 'audit') {
      fetchAuditLogs();
    }
  }, [isOpen, payment, activeTab]);

  const fetchAuditLogs = async () => {
    if (!payment?.payment_id) return;
    
    setLoading(true);
    try {
      const response = await api.get(`/billing/tutor-payments/${payment.payment_id}/audit-logs`);
      setAuditLogs(response.data.logs || []);
    } catch (error) {
      console.error('Error fetching audit logs:', error);
    } finally {
      setLoading(false);
    }
  };

  const getActionBadgeColor = (action: string) => {
    switch (action) {
      case 'created': return 'bg-green-100 text-green-800';
      case 'approved': return 'bg-blue-100 text-blue-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'payout_processed': return 'bg-purple-100 text-purple-800';
      case 'updated': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'manager': return 'bg-indigo-100 text-indigo-800';
      case 'tutor': return 'bg-green-100 text-green-800';
      case 'system': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'approved': return 'bg-blue-100 text-blue-800';
      case 'processing': return 'bg-purple-100 text-purple-800';
      case 'paid': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD'
    }).format(amount);
  };

  if (!isOpen || !payment) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20">
        <div className="fixed inset-0 transition-opacity" onClick={onClose}>
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <div className="relative bg-white rounded-lg max-w-4xl w-full shadow-xl transform transition-all">
          {/* Header */}
          <div className="bg-gray-50 px-6 py-4 rounded-t-lg border-b">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <CreditCard className="h-6 w-6 text-gray-600" />
                <h3 className="text-lg font-semibold text-gray-900">
                  {t('billing.tutorPaymentDetails')} - {payment.tutor_name}
                </h3>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-500 transition-colors"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="flex border-b">
            <button
              onClick={() => setActiveTab('details')}
              className={`px-6 py-3 text-sm font-medium transition-colors ${
                activeTab === 'details'
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              {t('billing.details')}
            </button>
            <button
              onClick={() => setActiveTab('audit')}
              className={`px-6 py-3 text-sm font-medium transition-colors ${
                activeTab === 'audit'
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              {t('billing.auditLog')}
            </button>
          </div>

          {/* Content */}
          <div className="p-6 max-h-96 overflow-y-auto">
            {activeTab === 'details' ? (
              <div className="space-y-6">
                {/* Payment Summary */}
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-sm font-medium text-gray-500 mb-2">
                      {t('billing.paymentPeriod')}
                    </h4>
                    <p className="text-sm">
                      {format(new Date(payment.payment_period_start), 'MMM dd, yyyy')} - {' '}
                      {format(new Date(payment.payment_period_end), 'MMM dd, yyyy')}
                    </p>
                  </div>

                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">{t('billing.status')}:</span>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                        getStatusBadgeColor(payment.status)
                      }`}>
                        {t(`billing.paymentStatus.${payment.status}`)}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">{t('billing.totalHours')}:</span>
                      <span className="text-sm font-medium">{payment.total_hours}h</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">{t('billing.totalAmount')}:</span>
                      <span className="text-sm font-semibold">{formatCurrency(payment.total_amount)}</span>
                    </div>
                  </div>
                </div>

                {/* Payment Breakdown */}
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-3">
                    {t('billing.paymentBreakdown')}
                  </h4>
                  <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">{t('billing.baseAmount')}:</span>
                      <span className="text-sm font-medium">{formatCurrency(payment.base_amount)}</span>
                    </div>
                    {payment.bonus_amount > 0 && (
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">{t('billing.bonusAmount')}:</span>
                        <span className="text-sm font-medium text-green-600">
                          +{formatCurrency(payment.bonus_amount)}
                        </span>
                      </div>
                    )}
                    <div className="border-t pt-2 flex justify-between">
                      <span className="text-sm font-medium text-gray-700">{t('billing.total')}:</span>
                      <span className="text-sm font-semibold">{formatCurrency(payment.total_amount)}</span>
                    </div>
                  </div>
                </div>

                {/* Session Details */}
                {payment.payment_items && payment.payment_items.length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-3">
                      {t('billing.sessionDetails')} ({payment.payment_items.length} {t('billing.sessions')})
                    </h4>
                    <div className="bg-gray-50 rounded-lg overflow-hidden">
                      <table className="min-w-full">
                        <thead>
                          <tr className="bg-gray-100">
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-700">
                              {t('billing.date')}
                            </th>
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-700">
                              {t('billing.client')}
                            </th>
                            <th className="px-4 py-2 text-right text-xs font-medium text-gray-700">
                              {t('billing.hours')}
                            </th>
                            <th className="px-4 py-2 text-right text-xs font-medium text-gray-700">
                              {t('billing.rate')}
                            </th>
                            <th className="px-4 py-2 text-right text-xs font-medium text-gray-700">
                              {t('billing.amount')}
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {payment.payment_items.map((item: any, index: number) => (
                            <tr key={index} className="border-t border-gray-200">
                              <td className="px-4 py-2 text-sm">
                                {format(new Date(item.session_date), 'MMM dd')}
                              </td>
                              <td className="px-4 py-2 text-sm">Client #{item.client_id}</td>
                              <td className="px-4 py-2 text-sm text-right">{item.hours_worked}h</td>
                              <td className="px-4 py-2 text-sm text-right">
                                {formatCurrency(item.hourly_rate)}
                              </td>
                              <td className="px-4 py-2 text-sm text-right font-medium">
                                {formatCurrency(item.amount)}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}

                {/* Approval/Processing Info */}
                {(payment.approved_by || payment.stripe_payout_id) && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-2">
                      {t('billing.processingInfo')}
                    </h4>
                    <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                      {payment.approved_by && (
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">{t('billing.approvedBy')}:</span>
                          <span className="text-sm">User #{payment.approved_by}</span>
                        </div>
                      )}
                      {payment.approved_at && (
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">{t('billing.approvedAt')}:</span>
                          <span className="text-sm">
                            {format(new Date(payment.approved_at), 'MMM dd, yyyy HH:mm')}
                          </span>
                        </div>
                      )}
                      {payment.stripe_payout_id && (
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">{t('billing.stripePayoutId')}:</span>
                          <span className="text-sm font-mono">{payment.stripe_payout_id}</span>
                        </div>
                      )}
                      {payment.paid_at && (
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600">{t('billing.paidAt')}:</span>
                          <span className="text-sm">
                            {format(new Date(payment.paid_at), 'MMM dd, yyyy HH:mm')}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="space-y-4">
                {loading ? (
                  <div className="text-center py-8">
                    <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <p className="mt-2 text-sm text-gray-600">{t('common.loading')}</p>
                  </div>
                ) : auditLogs.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Clock className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                    <p>{t('billing.noAuditLogs')}</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {auditLogs.map((log) => (
                      <div key={log.audit_id} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            <span className={`px-2 py-1 text-xs font-medium rounded ${getActionBadgeColor(log.action)}`}>
                              {t(`billing.actions.${log.action}`)}
                            </span>
                            <span className={`px-2 py-1 text-xs font-medium rounded ${getRoleBadgeColor(log.performed_by_role)}`}>
                              {t(`roles.${log.performed_by_role}`)}
                            </span>
                          </div>
                          <span className="text-xs text-gray-500">
                            {format(new Date(log.created_at), 'MMM dd, yyyy HH:mm')}
                          </span>
                        </div>

                        <div className="flex items-center text-sm text-gray-700 mb-2">
                          <User className="h-4 w-4 mr-1 text-gray-500" />
                          <span className="font-medium">{log.performed_by_name}</span>
                        </div>

                        {log.field_name && (
                          <div className="text-sm space-y-1">
                            <div className="flex items-center text-gray-600">
                              <ChevronRight className="h-4 w-4 mr-1" />
                              <span className="font-medium">{t(`billing.fields.${log.field_name}`)}:</span>
                            </div>
                            {log.old_value && (
                              <div className="ml-5 text-gray-500">
                                <span className="text-red-600 line-through">{log.old_value}</span>
                              </div>
                            )}
                            {log.new_value && (
                              <div className="ml-5 text-gray-700">
                                <span className="text-green-600">{log.new_value}</span>
                              </div>
                            )}
                          </div>
                        )}

                        {(log.amount_before !== null || log.amount_after !== null) && (
                          <div className="mt-2 text-sm">
                            <span className="text-gray-600">{t('billing.amount')}:</span>
                            {log.amount_before !== null && (
                              <span className="text-red-600 line-through ml-2">
                                {formatCurrency(log.amount_before)}
                              </span>
                            )}
                            {log.amount_after !== null && (
                              <span className="text-green-600 ml-2">
                                {formatCurrency(log.amount_after)}
                              </span>
                            )}
                          </div>
                        )}

                        {log.change_reason && (
                          <div className="mt-2 text-sm text-gray-600 italic">
                            "{log.change_reason}"
                          </div>
                        )}

                        {log.stripe_transaction_id && (
                          <div className="mt-2 text-xs text-gray-500 font-mono">
                            Stripe: {log.stripe_transaction_id}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="bg-gray-50 px-6 py-4 rounded-b-lg border-t">
            <div className="flex justify-end">
              <button
                onClick={onClose}
                className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
              >
                {t('common.close')}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TutorPaymentDetailsModal;