{"$schema": "https://railway.app/railway.schema.json", "build": {"builder": "NIXPACKS", "nixpacksPlan": {"providers": ["python", "node"], "phases": {"install": {"cmds": ["python3 -m venv /opt/venv && . /opt/venv/bin/activate && pip install -r requirements.txt", "cd app/frontend && npm install"]}, "build": {"cmds": ["cd app/frontend && npm run build"]}}}}, "deploy": {"startCommand": "python3 -m uvicorn app.main:app --host 0.0.0.0 --port $PORT", "restartPolicyType": "ON_FAILURE", "restartPolicyMaxRetries": 10}}