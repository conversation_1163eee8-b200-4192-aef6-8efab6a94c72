# Password Reset Feature for Managers

## Overview
Managers can send password reset emails to clients and tutors directly from their profile pages. This feature allows administrators to help users who are having trouble accessing their accounts.

## Implementation Details

### 1. Permission
- Added `resetUserPassword` permission (only for MANAGER role)
- Located in `/utils/permissions.ts`

### 2. Components Created

#### PasswordResetModal (`/components/modals/PasswordResetModal.tsx`)
- Modal dialog that confirms the password reset action
- Shows user information (name, email, type)
- Displays important warnings about the reset
- Shows email preview
- Success confirmation after sending

### 3. Integration Points

#### Clients Page
- Reset button appears in client detail view (right panel)
- Only visible to managers
- Located with other action buttons (Edit, Send Message, Book Session)

#### Tutors Page
- Reset button appears in each tutor card's action menu
- Only visible to managers
- Compact button with key icon

### 4. Auth Service
- Added `sendPasswordResetEmail` method to auth service
- Parameters: userId, email, initiatedBy
- Mock implementation logs to console in development

## User Flow

1. **Manager navigates to Users section**
   - For Clients: Select a client to view details
   - For Tu<PERSON>: View tutor cards in the list

2. **Manager clicks "Reset Password" button**
   - <PERSON><PERSON> opens showing user details
   - Warning messages explain the implications

3. **Manager confirms sending reset email**
   - Loading state while sending
   - Success message appears
   - Modal auto-closes after 2 seconds

4. **User receives email**
   - Email contains reset link (expires in 24 hours)
   - Email mentions it was initiated by an administrator
   - Includes support contact for concerns

## Security Considerations

1. **Permission Check**: Only managers can access this feature
2. **Audit Trail**: API should log who initiated the reset
3. **Email Notification**: User is informed a manager initiated the reset
4. **Token Expiry**: Reset links expire after 24 hours
5. **Session Handling**: Existing sessions remain active until password is changed

## Email Template Preview
```
Subject: Password Reset Request - TutorAide

Hello [FirstName],

A TutorAide administrator has requested a password reset for your account. 
Click the link below to create a new password:

[Reset Password Link]

This link will expire in 24 hours.

If you did not expect this email, please contact support <NAME_EMAIL>

Best regards,
The TutorAide Team
```

## Testing

1. Login as a manager
2. Go to Users → Clients
3. Select any client
4. Click "Reset Password" button
5. Verify modal appears with correct user info
6. Click "Send Reset Email"
7. Check console for mock log message
8. Verify success toast appears

Repeat for tutors in the Tutors page.

## Future Enhancements

1. Add email template customization
2. Add reset history/audit log viewing
3. Allow custom expiry times
4. Add option to force logout all sessions
5. Add SMS notification option
6. Bulk password reset for multiple users