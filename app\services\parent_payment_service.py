"""
Service for managing payment tracking by parent for separated families.
"""

import asyncpg
import logging
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, date, timedelta
from decimal import Decimal

from app.database.repositories.billing_repository import BillingRepository
from app.database.repositories.client_repository import ClientRepository
from app.core.exceptions import ResourceNotFoundError, BusinessLogicError, ValidationError
from app.core.timezone import now_est
from app.config.database import get_db_connection
from app.models.billing_models import (
    Invoice, InvoiceResponse, PaymentRecord,
    ParentPaymentSplit, ParentPaymentHistory
)
from app.models.base import PaymentStatus

logger = logging.getLogger(__name__)


class ParentPaymentService:
    """Service for managing payment tracking by parent."""
    
    def __init__(self):
        self.billing_repo = BillingRepository()
        self.client_repo = ClientRepository()
    
    async def split_invoice_by_parent(
        self,
        invoice_id: int,
        parent_splits: List[Dict[str, Any]],
        created_by: int
    ) -> Dict[str, Any]:
        """
        Split an invoice between parents based on percentage or amount.
        
        Args:
            invoice_id: Invoice to split
            parent_splits: List of splits with parent_id, amount or percentage
            created_by: User creating the split
            
        Returns:
            Split result with parent assignments
        """
        try:
            async with get_db_connection() as conn:
                # Get invoice details
                invoice = await conn.fetchrow("""
                    SELECT 
                        invoice_id, client_id, total_amount, 
                        status, paid_by_parent
                    FROM invoices
                    WHERE invoice_id = $1
                """, invoice_id)
                
                if not invoice:
                    raise ResourceNotFoundError(f"Invoice {invoice_id} not found")
                
                if invoice['status'] == PaymentStatus.PAID.value:
                    raise BusinessLogicError("Cannot split paid invoice")
                
                total_amount = Decimal(str(invoice['total_amount']))
                
                # Validate parent splits
                total_split = Decimal('0.00')
                split_records = []
                
                for split in parent_splits:
                    parent_id = split.get('parent_id')
                    
                    # Verify parent exists and is linked to client
                    parent = await conn.fetchrow("""
                        SELECT 
                            p.parent_id,
                            p.first_name,
                            p.last_name,
                            p.email,
                            p.is_primary
                        FROM parents p
                        JOIN client_parents cp ON p.parent_id = cp.parent_id
                        WHERE p.parent_id = $1 AND cp.client_id = $2
                    """, parent_id, invoice['client_id'])
                    
                    if not parent:
                        raise ValidationError(
                            f"Parent {parent_id} not found or not linked to client"
                        )
                    
                    # Calculate split amount
                    if 'percentage' in split:
                        percentage = Decimal(str(split['percentage']))
                        if percentage < 0 or percentage > 100:
                            raise ValidationError("Percentage must be between 0 and 100")
                        amount = (total_amount * percentage) / 100
                    elif 'amount' in split:
                        amount = Decimal(str(split['amount']))
                        if amount < 0:
                            raise ValidationError("Amount cannot be negative")
                    else:
                        raise ValidationError("Must specify either percentage or amount")
                    
                    split_records.append({
                        'parent_id': parent_id,
                        'parent_name': f"{parent['first_name']} {parent['last_name']}",
                        'email': parent['email'],
                        'amount': amount,
                        'percentage': (amount / total_amount * 100) if total_amount > 0 else 0
                    })
                    
                    total_split += amount
                
                # Validate total split equals invoice amount
                if abs(total_split - total_amount) > Decimal('0.01'):
                    raise ValidationError(
                        f"Split total ${total_split} does not match invoice total ${total_amount}"
                    )
                
                # Create split records
                for split in split_records:
                    await conn.execute("""
                        INSERT INTO invoice_parent_splits (
                            invoice_id, parent_id, split_amount, 
                            split_percentage, created_by, created_at, updated_at
                        ) VALUES ($1, $2, $3, $4, $5, $6, $6)
                    """,
                        invoice_id, split['parent_id'], split['amount'],
                        split['percentage'], created_by, now_est()
                    )
                
                logger.info(
                    f"Split invoice {invoice_id} between {len(split_records)} parents"
                )
                
                return {
                    'invoice_id': invoice_id,
                    'total_amount': float(total_amount),
                    'splits': split_records
                }
        
        except Exception as e:
            logger.error(f"Error splitting invoice: {str(e)}")
            raise BusinessLogicError(f"Failed to split invoice: {str(e)}")
    
    async def record_parent_payment(
        self,
        invoice_id: int,
        parent_id: int,
        amount: Decimal,
        payment_method: str,
        payment_reference: Optional[str],
        recorded_by: int
    ) -> Dict[str, Any]:
        """
        Record payment from a specific parent.
        
        Args:
            invoice_id: Invoice being paid
            parent_id: Parent making payment
            amount: Payment amount
            payment_method: Payment method used
            payment_reference: Payment reference number
            recorded_by: User recording payment
            
        Returns:
            Payment record details
        """
        try:
            async with get_db_connection() as conn:
                # Get invoice and split details
                invoice = await conn.fetchrow("""
                    SELECT 
                        i.invoice_id,
                        i.client_id,
                        i.total_amount,
                        i.status,
                        COALESCE(
                            (SELECT SUM(amount_paid) 
                             FROM invoice_parent_payments 
                             WHERE invoice_id = i.invoice_id), 0
                        ) as total_paid
                    FROM invoices i
                    WHERE i.invoice_id = $1
                """, invoice_id)
                
                if not invoice:
                    raise ResourceNotFoundError(f"Invoice {invoice_id} not found")
                
                # Verify parent
                parent = await conn.fetchrow("""
                    SELECT 
                        p.parent_id,
                        p.first_name,
                        p.last_name,
                        p.email
                    FROM parents p
                    JOIN client_parents cp ON p.parent_id = cp.parent_id
                    WHERE p.parent_id = $1 AND cp.client_id = $2
                """, parent_id, invoice['client_id'])
                
                if not parent:
                    raise ValidationError("Parent not linked to client")
                
                # Check for split
                split = await conn.fetchrow("""
                    SELECT split_amount 
                    FROM invoice_parent_splits
                    WHERE invoice_id = $1 AND parent_id = $2
                """, invoice_id, parent_id)
                
                if split:
                    expected_amount = Decimal(str(split['split_amount']))
                    if abs(amount - expected_amount) > Decimal('0.01'):
                        logger.warning(
                            f"Payment amount ${amount} differs from split amount ${expected_amount}"
                        )
                
                # Record payment
                payment_id = await conn.fetchval("""
                    INSERT INTO invoice_parent_payments (
                        invoice_id, parent_id, amount_paid, payment_method,
                        payment_reference, payment_date, recorded_by,
                        created_at, updated_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $8)
                    RETURNING payment_id
                """,
                    invoice_id, parent_id, amount, payment_method,
                    payment_reference, date.today(), recorded_by, now_est()
                )
                
                # Check if invoice is fully paid
                new_total_paid = Decimal(str(invoice['total_paid'])) + amount
                invoice_total = Decimal(str(invoice['total_amount']))
                
                if new_total_paid >= invoice_total:
                    # Mark invoice as paid
                    await conn.execute("""
                        UPDATE invoices 
                        SET 
                            status = $1,
                            paid_date = $2,
                            paid_by_parent = $3,
                            payment_method = $4,
                            payment_reference = $5,
                            updated_at = $6
                        WHERE invoice_id = $7
                    """,
                        PaymentStatus.PAID.value, date.today(), parent_id,
                        'multiple', f"Split payment - final by parent {parent_id}",
                        now_est(), invoice_id
                    )
                    
                    logger.info(f"Invoice {invoice_id} fully paid after parent {parent_id} payment")
                
                return {
                    'payment_id': payment_id,
                    'invoice_id': invoice_id,
                    'parent_id': parent_id,
                    'parent_name': f"{parent['first_name']} {parent['last_name']}",
                    'amount_paid': float(amount),
                    'payment_method': payment_method,
                    'payment_reference': payment_reference,
                    'invoice_fully_paid': new_total_paid >= invoice_total,
                    'total_paid': float(new_total_paid),
                    'remaining': float(max(invoice_total - new_total_paid, Decimal('0.00')))
                }
        
        except Exception as e:
            logger.error(f"Error recording parent payment: {str(e)}")
            raise BusinessLogicError(f"Failed to record payment: {str(e)}")
    
    async def get_parent_payment_history(
        self,
        parent_id: int,
        date_from: Optional[date] = None,
        date_to: Optional[date] = None,
        limit: int = 50,
        offset: int = 0
    ) -> Dict[str, Any]:
        """
        Get payment history for a specific parent.
        
        Args:
            parent_id: Parent ID
            date_from: Start date filter
            date_to: End date filter
            limit: Number of results
            offset: Results offset
            
        Returns:
            Payment history with statistics
        """
        try:
            async with get_db_connection() as conn:
                # Build query conditions
                conditions = ["ipp.parent_id = $1"]
                params = [parent_id]
                param_count = 1
                
                if date_from:
                    param_count += 1
                    conditions.append(f"ipp.payment_date >= ${param_count}")
                    params.append(date_from)
                
                if date_to:
                    param_count += 1
                    conditions.append(f"ipp.payment_date <= ${param_count}")
                    params.append(date_to)
                
                where_clause = " AND ".join(conditions)
                
                # Get total count
                total = await conn.fetchval(f"""
                    SELECT COUNT(*) 
                    FROM invoice_parent_payments ipp
                    WHERE {where_clause}
                """, *params)
                
                # Get payments with invoice details
                param_count += 1
                params.append(limit)
                param_count += 1
                params.append(offset)
                
                rows = await conn.fetch(f"""
                    SELECT 
                        ipp.*,
                        i.invoice_number,
                        i.total_amount as invoice_total,
                        i.issue_date,
                        i.status as invoice_status,
                        c.first_name || ' ' || c.last_name as client_name,
                        d.first_name || ' ' || d.last_name as dependant_name
                    FROM invoice_parent_payments ipp
                    JOIN invoices i ON ipp.invoice_id = i.invoice_id
                    JOIN clients c ON i.client_id = c.client_id
                    LEFT JOIN dependants d ON i.dependant_id = d.dependant_id
                    WHERE {where_clause}
                    ORDER BY ipp.payment_date DESC, ipp.created_at DESC
                    LIMIT ${param_count-1} OFFSET ${param_count}
                """, *params)
                
                # Get summary statistics
                stats = await conn.fetchrow(f"""
                    SELECT 
                        COUNT(*) as total_payments,
                        COALESCE(SUM(ipp.amount_paid), 0) as total_amount_paid,
                        COUNT(DISTINCT i.invoice_id) as unique_invoices,
                        COUNT(DISTINCT i.client_id) as unique_clients
                    FROM invoice_parent_payments ipp
                    JOIN invoices i ON ipp.invoice_id = i.invoice_id
                    WHERE {where_clause}
                """, *params[:-2])  # Exclude limit/offset
                
                payments = []
                for row in rows:
                    payment = dict(row)
                    payment['invoice_info'] = {
                        'invoice_number': row['invoice_number'],
                        'invoice_total': float(row['invoice_total']),
                        'issue_date': row['issue_date'],
                        'status': row['invoice_status'],
                        'client_name': row['client_name'],
                        'dependant_name': row['dependant_name']
                    }
                    payments.append(payment)
                
                return {
                    'payments': payments,
                    'total': total,
                    'statistics': {
                        'total_payments': stats['total_payments'],
                        'total_amount_paid': float(stats['total_amount_paid']),
                        'unique_invoices': stats['unique_invoices'],
                        'unique_clients': stats['unique_clients']
                    }
                }
        
        except Exception as e:
            logger.error(f"Error getting parent payment history: {str(e)}")
            raise BusinessLogicError(f"Failed to get payment history: {str(e)}")
    
    async def get_invoice_payment_breakdown(
        self,
        invoice_id: int
    ) -> Dict[str, Any]:
        """
        Get detailed payment breakdown for an invoice by parent.
        
        Args:
            invoice_id: Invoice ID
            
        Returns:
            Payment breakdown with parent details
        """
        try:
            async with get_db_connection() as conn:
                # Get invoice details
                invoice = await conn.fetchrow("""
                    SELECT 
                        i.*,
                        c.first_name || ' ' || c.last_name as client_name
                    FROM invoices i
                    JOIN clients c ON i.client_id = c.client_id
                    WHERE i.invoice_id = $1
                """, invoice_id)
                
                if not invoice:
                    raise ResourceNotFoundError(f"Invoice {invoice_id} not found")
                
                # Get splits if any
                splits = await conn.fetch("""
                    SELECT 
                        ips.*,
                        p.first_name || ' ' || p.last_name as parent_name,
                        p.email as parent_email,
                        p.phone as parent_phone
                    FROM invoice_parent_splits ips
                    JOIN parents p ON ips.parent_id = p.parent_id
                    WHERE ips.invoice_id = $1
                    ORDER BY ips.split_percentage DESC
                """, invoice_id)
                
                # Get payments
                payments = await conn.fetch("""
                    SELECT 
                        ipp.*,
                        p.first_name || ' ' || p.last_name as parent_name,
                        p.email as parent_email
                    FROM invoice_parent_payments ipp
                    JOIN parents p ON ipp.parent_id = p.parent_id
                    WHERE ipp.invoice_id = $1
                    ORDER BY ipp.payment_date DESC, ipp.created_at DESC
                """, invoice_id)
                
                # Calculate totals by parent
                parent_totals = {}
                for payment in payments:
                    parent_id = payment['parent_id']
                    if parent_id not in parent_totals:
                        parent_totals[parent_id] = {
                            'parent_id': parent_id,
                            'parent_name': payment['parent_name'],
                            'parent_email': payment['parent_email'],
                            'total_paid': Decimal('0.00'),
                            'payment_count': 0,
                            'payments': []
                        }
                    
                    amount = Decimal(str(payment['amount_paid']))
                    parent_totals[parent_id]['total_paid'] += amount
                    parent_totals[parent_id]['payment_count'] += 1
                    parent_totals[parent_id]['payments'].append({
                        'payment_id': payment['payment_id'],
                        'amount': float(amount),
                        'payment_date': payment['payment_date'],
                        'payment_method': payment['payment_method'],
                        'payment_reference': payment['payment_reference']
                    })
                
                # Build response
                total_amount = Decimal(str(invoice['total_amount']))
                total_paid = sum(pt['total_paid'] for pt in parent_totals.values())
                
                return {
                    'invoice': {
                        'invoice_id': invoice['invoice_id'],
                        'invoice_number': invoice['invoice_number'],
                        'client_name': invoice['client_name'],
                        'total_amount': float(total_amount),
                        'status': invoice['status'],
                        'issue_date': invoice['issue_date'],
                        'due_date': invoice['due_date']
                    },
                    'splits': [
                        {
                            'parent_id': split['parent_id'],
                            'parent_name': split['parent_name'],
                            'parent_email': split['parent_email'],
                            'split_amount': float(split['split_amount']),
                            'split_percentage': float(split['split_percentage'])
                        }
                        for split in splits
                    ],
                    'payments_by_parent': [
                        {
                            **parent_total,
                            'total_paid': float(parent_total['total_paid'])
                        }
                        for parent_total in parent_totals.values()
                    ],
                    'summary': {
                        'total_amount': float(total_amount),
                        'total_paid': float(total_paid),
                        'remaining': float(max(total_amount - total_paid, Decimal('0.00'))),
                        'is_fully_paid': total_paid >= total_amount,
                        'payment_count': sum(pt['payment_count'] for pt in parent_totals.values()),
                        'parent_count': len(parent_totals)
                    }
                }
        
        except Exception as e:
            logger.error(f"Error getting invoice payment breakdown: {str(e)}")
            raise BusinessLogicError(f"Failed to get payment breakdown: {str(e)}")
    
    async def get_parent_balance_summary(
        self,
        parent_id: int
    ) -> Dict[str, Any]:
        """
        Get balance summary for a parent across all their children.
        
        Args:
            parent_id: Parent ID
            
        Returns:
            Balance summary with outstanding amounts
        """
        try:
            async with get_db_connection() as conn:
                # Get parent details
                parent = await conn.fetchrow("""
                    SELECT 
                        parent_id,
                        first_name,
                        last_name,
                        email
                    FROM parents
                    WHERE parent_id = $1
                """, parent_id)
                
                if not parent:
                    raise ResourceNotFoundError(f"Parent {parent_id} not found")
                
                # Get all invoices where parent has splits
                invoices_with_splits = await conn.fetch("""
                    SELECT 
                        i.invoice_id,
                        i.invoice_number,
                        i.total_amount,
                        i.status,
                        i.due_date,
                        ips.split_amount,
                        ips.split_percentage,
                        c.first_name || ' ' || c.last_name as client_name,
                        COALESCE(
                            (SELECT SUM(amount_paid) 
                             FROM invoice_parent_payments 
                             WHERE invoice_id = i.invoice_id 
                               AND parent_id = $1), 0
                        ) as amount_paid
                    FROM invoices i
                    JOIN invoice_parent_splits ips ON i.invoice_id = ips.invoice_id
                    JOIN clients c ON i.client_id = c.client_id
                    WHERE ips.parent_id = $1
                        AND i.status != 'cancelled'
                    ORDER BY i.due_date DESC
                """, parent_id)
                
                # Calculate totals
                total_owed = Decimal('0.00')
                total_paid = Decimal('0.00')
                outstanding_invoices = []
                
                for invoice in invoices_with_splits:
                    split_amount = Decimal(str(invoice['split_amount']))
                    amount_paid = Decimal(str(invoice['amount_paid']))
                    balance = split_amount - amount_paid
                    
                    total_owed += split_amount
                    total_paid += amount_paid
                    
                    if balance > 0 and invoice['status'] != PaymentStatus.PAID.value:
                        outstanding_invoices.append({
                            'invoice_id': invoice['invoice_id'],
                            'invoice_number': invoice['invoice_number'],
                            'client_name': invoice['client_name'],
                            'split_amount': float(split_amount),
                            'amount_paid': float(amount_paid),
                            'balance': float(balance),
                            'due_date': invoice['due_date'],
                            'days_overdue': (date.today() - invoice['due_date']).days if invoice['due_date'] < date.today() else 0
                        })
                
                return {
                    'parent': {
                        'parent_id': parent['parent_id'],
                        'name': f"{parent['first_name']} {parent['last_name']}",
                        'email': parent['email']
                    },
                    'summary': {
                        'total_owed': float(total_owed),
                        'total_paid': float(total_paid),
                        'outstanding_balance': float(total_owed - total_paid),
                        'invoice_count': len(invoices_with_splits),
                        'outstanding_invoice_count': len(outstanding_invoices)
                    },
                    'outstanding_invoices': outstanding_invoices
                }
        
        except Exception as e:
            logger.error(f"Error getting parent balance summary: {str(e)}")
            raise BusinessLogicError(f"Failed to get balance summary: {str(e)}")