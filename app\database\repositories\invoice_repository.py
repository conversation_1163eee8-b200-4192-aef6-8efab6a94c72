"""
Repository for invoice database operations.
"""

import asyncpg
from typing import List, Optional, Dict, Any
from datetime import date, datetime
from decimal import Decimal

from app.models.invoice_models import (
    Invoice, InvoiceItem, InvoiceFilter, PaymentStatus
)
from app.database.repositories.base import BaseRepository
from app.core.exceptions import ResourceNotFoundError, DatabaseError


class InvoiceRepository(BaseRepository):
    """Repository for invoice operations."""
    
    async def create_invoice(self, db: asyncpg.Connection, invoice: Invoice) -> Invoice:
        """Create a new invoice with items."""
        try:
            # Start transaction
            async with db.transaction():
                # Insert invoice
                query = """
                INSERT INTO billing_invoices (
                    client_id, invoice_date, due_date, 
                    subtotal, tax_amount, total_amount, status,
                    is_subscription_deduction, subscription_id, notes
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                RETURNING invoice_id, invoice_number, created_at
                """
                
                row = await db.fetchrow(
                    query,
                    invoice.client_id,
                    invoice.invoice_date,
                    invoice.due_date,
                    invoice.subtotal,
                    invoice.tax_amount,
                    invoice.total_amount,
                    invoice.status.value,
                    invoice.is_subscription_deduction,
                    invoice.subscription_id,
                    invoice.notes
                )
                
                invoice.invoice_id = row["invoice_id"]
                invoice.invoice_number = row["invoice_number"]
                invoice.created_at = row["created_at"]
                
                # Insert invoice items
                if invoice.items:
                    item_query = """
                    INSERT INTO billing_invoice_items (
                        invoice_id, appointment_id, item_type, description,
                        quantity, unit_price, amount, surge_multiplier, surge_amount
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                    RETURNING item_id, created_at
                    """
                    
                    for item in invoice.items:
                        item_row = await db.fetchrow(
                            item_query,
                            invoice.invoice_id,
                            item.appointment_id,
                            item.item_type.value,
                            item.description,
                            item.quantity,
                            item.unit_price,
                            item.amount,
                            item.surge_multiplier,
                            item.surge_amount
                        )
                        item.item_id = item_row["item_id"]
                        item.invoice_id = invoice.invoice_id
                        item.created_at = item_row["created_at"]
                
                return invoice
                
        except asyncpg.UniqueViolationError as e:
            raise DatabaseError(f"Invoice already exists: {str(e)}")
        except Exception as e:
            raise DatabaseError(f"Failed to create invoice: {str(e)}")
    
    async def get_invoice_by_id(self, db: asyncpg.Connection, invoice_id: int) -> Optional[Invoice]:
        """Get invoice by ID with items."""
        # Get invoice
        invoice_query = """
        SELECT i.*, 
               c.first_name || ' ' || c.last_name as client_name,
               pc.first_name || ' ' || pc.last_name as paid_by_name
        FROM billing_invoices i
        JOIN client_profiles c ON i.client_id = c.client_id
        LEFT JOIN client_profiles pc ON i.paid_by_client_id = pc.client_id
        WHERE i.invoice_id = $1 AND i.deleted_at IS NULL
        """
        
        invoice_row = await db.fetchrow(invoice_query, invoice_id)
        if not invoice_row:
            return None
        
        # Get invoice items
        items_query = """
        SELECT ii.*, 
               a.start_time as appointment_date,
               s.name as service_name
        FROM billing_invoice_items ii
        LEFT JOIN appointment_appointments a ON ii.appointment_id = a.appointment_id
        LEFT JOIN service_catalog s ON a.service_id = s.service_id
        WHERE ii.invoice_id = $1
        ORDER BY ii.created_at
        """
        
        items_rows = await db.fetch(items_query, invoice_id)
        
        # Build invoice object
        invoice = Invoice(
            invoice_id=invoice_row["invoice_id"],
            client_id=invoice_row["client_id"],
            invoice_number=invoice_row["invoice_number"],
            invoice_date=invoice_row["invoice_date"],
            due_date=invoice_row["due_date"],
            subtotal=invoice_row["subtotal"],
            tax_amount=invoice_row["tax_amount"],
            total_amount=invoice_row["total_amount"],
            status=PaymentStatus(invoice_row["status"]),
            paid_by_client_id=invoice_row["paid_by_client_id"],
            paid_date=invoice_row["paid_date"],
            payment_method=invoice_row["payment_method"],
            stripe_payment_intent_id=invoice_row["stripe_payment_intent_id"],
            is_subscription_deduction=invoice_row["is_subscription_deduction"],
            subscription_id=invoice_row["subscription_id"],
            notes=invoice_row["notes"],
            created_at=invoice_row["created_at"],
            updated_at=invoice_row["updated_at"],
            items=[
                InvoiceItem(
                    item_id=item["item_id"],
                    invoice_id=item["invoice_id"],
                    appointment_id=item["appointment_id"],
                    item_type=item["item_type"],
                    description=item["description"],
                    quantity=item["quantity"],
                    unit_price=item["unit_price"],
                    amount=item["amount"],
                    surge_multiplier=item["surge_multiplier"],
                    surge_amount=item["surge_amount"],
                    created_at=item["created_at"]
                )
                for item in items_rows
            ]
        )
        
        return invoice
    
    async def update_invoice_status(
        self, 
        db: asyncpg.Connection, 
        invoice_id: int, 
        status: PaymentStatus,
        paid_by_client_id: Optional[int] = None,
        payment_method: Optional[str] = None,
        stripe_payment_intent_id: Optional[str] = None
    ) -> bool:
        """Update invoice payment status."""
        query = """
        UPDATE billing_invoices
        SET status = $2,
            paid_date = CASE WHEN $2 = 'paid' THEN CURRENT_TIMESTAMP ELSE paid_date END,
            paid_by_client_id = COALESCE($3, paid_by_client_id),
            payment_method = COALESCE($4, payment_method),
            stripe_payment_intent_id = COALESCE($5, stripe_payment_intent_id),
            updated_at = CURRENT_TIMESTAMP
        WHERE invoice_id = $1 AND deleted_at IS NULL
        RETURNING invoice_id
        """
        
        result = await db.fetchrow(
            query, 
            invoice_id, 
            status.value,
            paid_by_client_id,
            payment_method,
            stripe_payment_intent_id
        )
        
        return result is not None
    
    async def find_invoices(self, db: asyncpg.Connection, filter_params: InvoiceFilter) -> List[Invoice]:
        """Find invoices with filtering."""
        query = """
        SELECT i.*, 
               c.first_name || ' ' || c.last_name as client_name,
               pc.first_name || ' ' || pc.last_name as paid_by_name,
               COUNT(*) OVER() as total_count
        FROM billing_invoices i
        JOIN client_profiles c ON i.client_id = c.client_id
        LEFT JOIN client_profiles pc ON i.paid_by_client_id = pc.client_id
        WHERE i.deleted_at IS NULL
        """
        
        params = []
        conditions = []
        param_count = 0
        
        # Build dynamic query
        if filter_params.client_id:
            param_count += 1
            conditions.append(f"i.client_id = ${param_count}")
            params.append(filter_params.client_id)
        
        if filter_params.status:
            param_count += 1
            conditions.append(f"i.status = ${param_count}")
            params.append(filter_params.status.value)
        
        if filter_params.date_from:
            param_count += 1
            conditions.append(f"i.invoice_date >= ${param_count}")
            params.append(filter_params.date_from)
        
        if filter_params.date_to:
            param_count += 1
            conditions.append(f"i.invoice_date <= ${param_count}")
            params.append(filter_params.date_to)
        
        if filter_params.is_overdue:
            conditions.append("i.status = 'pending' AND i.due_date < CURRENT_DATE")
        
        if filter_params.min_amount:
            param_count += 1
            conditions.append(f"i.total_amount >= ${param_count}")
            params.append(filter_params.min_amount)
        
        if filter_params.max_amount:
            param_count += 1
            conditions.append(f"i.total_amount <= ${param_count}")
            params.append(filter_params.max_amount)
        
        if filter_params.paid_by_client_id:
            param_count += 1
            conditions.append(f"i.paid_by_client_id = ${param_count}")
            params.append(filter_params.paid_by_client_id)
        
        if conditions:
            query += " AND " + " AND ".join(conditions)
        
        # Add ordering and pagination
        query += " ORDER BY i.invoice_date DESC, i.invoice_id DESC"
        
        param_count += 1
        query += f" LIMIT ${param_count}"
        params.append(filter_params.limit)
        
        param_count += 1
        query += f" OFFSET ${param_count}"
        params.append(filter_params.offset)
        
        rows = await db.fetch(query, *params)
        
        invoices = []
        for row in rows:
            invoice = Invoice(
                invoice_id=row["invoice_id"],
                client_id=row["client_id"],
                invoice_number=row["invoice_number"],
                invoice_date=row["invoice_date"],
                due_date=row["due_date"],
                subtotal=row["subtotal"],
                tax_amount=row["tax_amount"],
                total_amount=row["total_amount"],
                status=PaymentStatus(row["status"]),
                paid_by_client_id=row["paid_by_client_id"],
                paid_date=row["paid_date"],
                payment_method=row["payment_method"],
                stripe_payment_intent_id=row["stripe_payment_intent_id"],
                is_subscription_deduction=row["is_subscription_deduction"],
                subscription_id=row["subscription_id"],
                notes=row["notes"],
                created_at=row["created_at"],
                updated_at=row["updated_at"]
            )
            invoices.append(invoice)
        
        return invoices
    
    async def get_unbilled_appointments(
        self, 
        db: asyncpg.Connection, 
        client_id: Optional[int] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> List[Dict[str, Any]]:
        """Get completed appointments that haven't been invoiced."""
        query = """
        SELECT a.appointment_id, a.client_id, a.tutor_id, a.service_id,
               a.start_time, a.end_time, a.duration_minutes,
               a.individual_rate, a.transport_fee, a.is_subscription_based,
               a.surge_multiplier, a.surge_amount,
               c.first_name || ' ' || c.last_name as client_name,
               t.first_name || ' ' || t.last_name as tutor_name,
               s.name as service_name,
               ts.client_rate as service_rate
        FROM appointment_appointments a
        JOIN client_profiles c ON a.client_id = c.client_id
        JOIN tutor_profiles t ON a.tutor_id = t.tutor_id
        JOIN service_catalog s ON a.service_id = s.service_id
        JOIN tutor_service_rates ts ON ts.tutor_id = a.tutor_id AND ts.service_id = a.service_id
        WHERE a.status = 'completed'
        AND a.appointment_id NOT IN (
            SELECT DISTINCT appointment_id 
            FROM billing_invoice_items 
            WHERE appointment_id IS NOT NULL
        )
        """
        
        params = []
        conditions = []
        param_count = 0
        
        if client_id:
            param_count += 1
            conditions.append(f"a.client_id = ${param_count}")
            params.append(client_id)
        
        if start_date:
            param_count += 1
            conditions.append(f"DATE(a.start_time) >= ${param_count}")
            params.append(start_date)
        
        if end_date:
            param_count += 1
            conditions.append(f"DATE(a.start_time) <= ${param_count}")
            params.append(end_date)
        
        if conditions:
            query += " AND " + " AND ".join(conditions)
        
        query += " ORDER BY a.client_id, a.start_time"
        
        rows = await db.fetch(query, *params)
        
        return [dict(row) for row in rows]
    
    async def get_client_unpaid_invoices(
        self, 
        db: asyncpg.Connection, 
        client_id: int
    ) -> List[Invoice]:
        """Get all unpaid invoices for a client."""
        return await self.find_invoices(
            db,
            InvoiceFilter(
                client_id=client_id,
                status=PaymentStatus.PENDING
            )
        )
    
    async def get_overdue_invoices(self, db: asyncpg.Connection) -> List[Dict[str, Any]]:
        """Get all overdue invoices with client details."""
        query = """
        SELECT i.*, 
               c.first_name || ' ' || c.last_name as client_name,
               c.email as client_email,
               c.phone as client_phone,
               CURRENT_DATE - i.due_date as days_overdue
        FROM billing_invoices i
        JOIN client_profiles c ON i.client_id = c.client_id
        WHERE i.status = 'pending' 
        AND i.due_date < CURRENT_DATE
        AND i.deleted_at IS NULL
        ORDER BY i.due_date, i.total_amount DESC
        """
        
        rows = await db.fetch(query)
        return [dict(row) for row in rows]
    
    async def create_scheduled_task_log(
        self,
        db: asyncpg.Connection,
        task_name: str,
        task_type: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> int:
        """Create a scheduled task log entry."""
        query = """
        INSERT INTO billing_scheduled_tasks (
            task_name, task_type, parameters
        ) VALUES ($1, $2, $3)
        RETURNING task_id
        """
        
        row = await db.fetchrow(query, task_name, task_type, parameters or {})
        return row["task_id"]
    
    async def update_scheduled_task_log(
        self,
        db: asyncpg.Connection,
        task_id: int,
        status: str,
        records_processed: int = 0,
        records_created: int = 0,
        records_failed: int = 0,
        error_message: Optional[str] = None,
        error_details: Optional[Dict[str, Any]] = None
    ) -> None:
        """Update scheduled task log with results."""
        query = """
        UPDATE billing_scheduled_tasks
        SET completed_at = CURRENT_TIMESTAMP,
            status = $2,
            records_processed = $3,
            records_created = $4,
            records_failed = $5,
            error_message = $6,
            error_details = $7
        WHERE task_id = $1
        """
        
        await db.execute(
            query,
            task_id,
            status,
            records_processed,
            records_created,
            records_failed,
            error_message,
            error_details
        )