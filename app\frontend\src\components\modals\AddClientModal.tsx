import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { X } from 'lucide-react';
import { Modal } from '../common/Modal';
import Button from '../common/Button';
import { Input } from '../common/Input';
import { clientService, ClientCreateRequest } from '../../services/clientService';
import toast from 'react-hot-toast';

interface AddClientModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const AddClientModal: React.FC<AddClientModalProps> = ({
  isOpen,
  onClose,
  onSuccess
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<ClientCreateRequest>({
    first_name: '',
    last_name: '',
    email: '',
    phone_number: '',
    address: {
      street: '',
      city: '',
      province: 'QC',
      postal_code: '',
      country: 'Canada'
    },
    communication_preferences: {
      email_notifications: true,
      sms_notifications: true,
      push_notifications: true,
      language: 'en'
    },
    emergency_contacts: []
  });

  const [emergencyContact, setEmergencyContact] = useState({
    name: '',
    relationship: '',
    phone_number: '',
    email: '',
    is_primary: true
  });

  const handleInputChange = (field: string, value: any) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...(prev as any)[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  const handleEmergencyContactChange = (field: string, value: any) => {
    setEmergencyContact(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addEmergencyContact = () => {
    if (emergencyContact.name && emergencyContact.phone_number) {
      setFormData(prev => ({
        ...prev,
        emergency_contacts: [...(prev.emergency_contacts || []), emergencyContact]
      }));
      setEmergencyContact({
        name: '',
        relationship: '',
        phone_number: '',
        email: '',
        is_primary: false
      });
    }
  };

  const removeEmergencyContact = (index: number) => {
    setFormData(prev => ({
      ...prev,
      emergency_contacts: (prev.emergency_contacts || []).filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.first_name || !formData.last_name || !formData.email) {
      toast.error(t('validation.requiredFields'));
      return;
    }

    setLoading(true);
    try {
      await clientService.createClient(formData);
      toast.success(t('users.clients.addSuccess'));
      onSuccess();
      onClose();
    } catch (error: any) {
      console.error('Error creating client:', error);
      toast.error(error.response?.data?.detail || t('users.clients.addError'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="lg">
      <form onSubmit={handleSubmit} className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">
            {t('users.clients.addTitle')}
          </h2>
          <button
            type="button"
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Form Fields */}
        <div className="space-y-6">
          {/* Basic Information */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {t('users.clients.basicInfo')}
            </h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('users.clients.firstName')} *
                </label>
                <Input
                  type="text"
                  value={formData.first_name}
                  onChange={(e) => handleInputChange('first_name', e.target.value)}
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('users.clients.lastName')} *
                </label>
                <Input
                  type="text"
                  value={formData.last_name}
                  onChange={(e) => handleInputChange('last_name', e.target.value)}
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('users.clients.email')} *
                </label>
                <Input
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('users.clients.phone')}
                </label>
                <Input
                  type="tel"
                  value={formData.phone_number}
                  onChange={(e) => handleInputChange('phone_number', e.target.value)}
                />
              </div>
            </div>
          </div>

          {/* Address */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {t('users.clients.address')}
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('users.clients.street')}
                </label>
                <Input
                  type="text"
                  value={formData.address?.street || ''}
                  onChange={(e) => handleInputChange('address.street', e.target.value)}
                />
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('users.clients.city')}
                  </label>
                  <Input
                    type="text"
                    value={formData.address?.city || ''}
                    onChange={(e) => handleInputChange('address.city', e.target.value)}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('users.clients.province')}
                  </label>
                  <select
                    value={formData.address?.province || 'QC'}
                    onChange={(e) => handleInputChange('address.province', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-red"
                  >
                    <option value="AB">Alberta</option>
                    <option value="BC">British Columbia</option>
                    <option value="MB">Manitoba</option>
                    <option value="NB">New Brunswick</option>
                    <option value="NL">Newfoundland and Labrador</option>
                    <option value="NT">Northwest Territories</option>
                    <option value="NS">Nova Scotia</option>
                    <option value="NU">Nunavut</option>
                    <option value="ON">Ontario</option>
                    <option value="PE">Prince Edward Island</option>
                    <option value="QC">Quebec</option>
                    <option value="SK">Saskatchewan</option>
                    <option value="YT">Yukon</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('users.clients.postalCode')}
                  </label>
                  <Input
                    type="text"
                    value={formData.address?.postal_code || ''}
                    onChange={(e) => handleInputChange('address.postal_code', e.target.value)}
                    placeholder="H0H 0H0"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Emergency Contacts */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {t('users.clients.emergencyContacts')}
            </h3>
            
            {/* Existing Emergency Contacts */}
            {formData.emergency_contacts && formData.emergency_contacts.length > 0 && (
              <div className="mb-4 space-y-2">
                {formData.emergency_contacts.map((contact, index) => (
                  <div key={index} className="flex items-center justify-between bg-gray-50 rounded-lg p-3">
                    <div>
                      <div className="font-medium">{contact.name}</div>
                      <div className="text-sm text-gray-600">
                        {contact.relationship} • {contact.phone_number}
                      </div>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeEmergencyContact(index)}
                    >
                      {t('common.remove')}
                    </Button>
                  </div>
                ))}
              </div>
            )}

            {/* Add New Emergency Contact */}
            <div className="border rounded-lg p-4 space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('users.clients.contactName')}
                  </label>
                  <Input
                    type="text"
                    value={emergencyContact.name}
                    onChange={(e) => handleEmergencyContactChange('name', e.target.value)}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('users.clients.relationship')}
                  </label>
                  <Input
                    type="text"
                    value={emergencyContact.relationship}
                    onChange={(e) => handleEmergencyContactChange('relationship', e.target.value)}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('users.clients.contactPhone')}
                  </label>
                  <Input
                    type="tel"
                    value={emergencyContact.phone_number}
                    onChange={(e) => handleEmergencyContactChange('phone_number', e.target.value)}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('users.clients.contactEmail')}
                  </label>
                  <Input
                    type="email"
                    value={emergencyContact.email}
                    onChange={(e) => handleEmergencyContactChange('email', e.target.value)}
                  />
                </div>
              </div>
              <div className="flex items-center justify-between">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={emergencyContact.is_primary}
                    onChange={(e) => handleEmergencyContactChange('is_primary', e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700">{t('users.clients.primaryContact')}</span>
                </label>
                <Button
                  type="button"
                  variant="secondary"
                  size="sm"
                  onClick={addEmergencyContact}
                  disabled={!emergencyContact.name || !emergencyContact.phone_number}
                >
                  {t('users.clients.addContact')}
                </Button>
              </div>
            </div>
          </div>

          {/* Communication Preferences */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {t('users.clients.communicationPreferences')}
            </h3>
            <div className="grid grid-cols-2 gap-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.communication_preferences?.email_notifications}
                  onChange={(e) => handleInputChange('communication_preferences.email_notifications', e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700">{t('users.clients.emailNotifications')}</span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.communication_preferences?.sms_notifications}
                  onChange={(e) => handleInputChange('communication_preferences.sms_notifications', e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700">{t('users.clients.smsNotifications')}</span>
              </label>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.communication_preferences?.push_notifications}
                  onChange={(e) => handleInputChange('communication_preferences.push_notifications', e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700">{t('users.clients.pushNotifications')}</span>
              </label>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('users.clients.language')}
                </label>
                <select
                  value={formData.communication_preferences?.language || 'en'}
                  onChange={(e) => handleInputChange('communication_preferences.language', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-red"
                >
                  <option value="en">English</option>
                  <option value="fr">Français</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="mt-8 flex justify-end space-x-3">
          <Button type="button" variant="secondary" onClick={onClose}>
            {t('common.cancel')}
          </Button>
          <Button type="submit" variant="primary" loading={loading}>
            {t('users.clients.create')}
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default AddClientModal;