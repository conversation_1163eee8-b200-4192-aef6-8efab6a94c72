import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { 
  Clock, MapPin, GraduationCap, Users, Filter, 
  ChevronRight, Package, Star, TrendingUp, Book,
  Calculator, Globe, Home, Library, Calendar, Plus, Edit, Trash2
} from 'lucide-react';
import api from '../../services/api';
import { serviceApi, ServicePreset, ServicePackage } from '../../services/serviceApi';
import { Card } from '../../components/common/Card';
import Button from '../../components/common/Button';
import { Badge } from '../../components/common/Badge';
import { Select } from '../../components/common/Select';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import { EmptyState } from '../../components/common/EmptyState';
import { useAuth } from '../../contexts/AuthContext';
import { UserRoleType } from '../../types/auth';
import { hasPermission } from '../../utils/permissions';
import { ServiceModal } from '../../components/modals/ServiceModal';
import { PackageModal } from '../../components/modals/PackageModal';
import { useApi } from '../../hooks/useApi';
import { Dropdown } from '../../components/common/Dropdown';
import toast from 'react-hot-toast';

const ServicesPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { del } = useApi();
  const [activeTab, setActiveTab] = useState<'services' | 'packages'>('services');
  const [services, setServices] = useState<ServicePreset[]>([]);
  const [packages, setPackages] = useState<ServicePackage[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedServiceType, setSelectedServiceType] = useState<string>('all');
  const [selectedSubject, setSelectedSubject] = useState<string>('all');
  const [showServiceModal, setShowServiceModal] = useState(false);
  const [selectedService, setSelectedService] = useState<ServicePreset | null>(null);
  const [showPackageModal, setShowPackageModal] = useState(false);
  const [selectedPackage, setSelectedPackage] = useState<ServicePackage | null>(null);
  
  // Safely handle authentication - don't crash if auth is not working
  const isManager = user?.activeRole === UserRoleType.MANAGER || false;
  const canManageServices = user?.activeRole ? hasPermission(user.activeRole, 'manageServices') : false;

  useEffect(() => {
    fetchData();
  }, []);


  const fetchData = async () => {
    try {
      setLoading(true);

      // Fetch services and packages separately to handle individual failures
      let servicesData = [];
      let packagesData = [];

      try {
        servicesData = await serviceApi.getServicePresets({ is_active_only: true });
        console.log('Services loaded:', servicesData?.length || 0);
      } catch (servicesError) {
        console.error('Error fetching services:', servicesError);
        toast.error('Failed to load services');
      }

      try {
        packagesData = await serviceApi.getServicePackages();
        console.log('Packages loaded:', packagesData?.length || 0);
      } catch (packagesError) {
        console.error('Error fetching packages:', packagesError);
        // Don't show error for packages as they're less critical
      }

      setServices(servicesData || []);
      setPackages(packagesData || []);

    } catch (error) {
      console.error('Error in fetchData:', error);
      toast.error(t('services.errors.fetchServices', 'Failed to load services. Please try again.'));
    } finally {
      setLoading(false);
    }
  };

  const getServiceTypeIcon = (type: string) => {
    switch (type) {
      case 'online':
        return <Globe className="w-5 h-5" />;
      case 'in_person':
        return <Home className="w-5 h-5" />;
      case 'library':
        return <Library className="w-5 h-5" />;
      case 'hybrid':
        return <Users className="w-5 h-5" />;
      default:
        return <MapPin className="w-5 h-5" />;
    }
  };

  const getSubjectIcon = (subject: string) => {
    switch (subject.toLowerCase()) {
      case 'mathematics':
        return <Calculator className="w-5 h-5" />;
      case 'science':
        return <Book className="w-5 h-5" />;
      case 'english':
      case 'french':
        return <Book className="w-5 h-5" />;
      default:
        return <GraduationCap className="w-5 h-5" />;
    }
  };

  const filteredServices = services.filter(service => {
    if (selectedServiceType !== 'all' && service.service_type !== selectedServiceType) {
      return false;
    }
    if (selectedSubject !== 'all' && service.subject_area !== selectedSubject) {
      return false;
    }
    return true;
  });

  const uniqueSubjects = Array.from(
    new Set(services.map(s => s.subject_area))
  ).filter(s => s && s !== 'all');

  const handleEditService = (service: ServicePreset) => {
    setSelectedService(service);
    setShowServiceModal(true);
  };

  const handleDeleteService = async (serviceId: number) => {
    if (!window.confirm(t('services.confirmDelete', 'Are you sure you want to delete this service?'))) {
      return;
    }

    try {
      await del(`/services/catalog/${serviceId}`);
      toast.success(t('services.deleteSuccess', 'Service deleted successfully'));
      fetchData();
    } catch (error) {
      console.error('Error deleting service:', error);
      toast.error(t('services.deleteError', 'Failed to delete service'));
    }
  };

  const handleCreateService = () => {
    setSelectedService(null);
    setShowServiceModal(true);
  };

  const handleCreatePackage = () => {
    setSelectedPackage(null);
    setShowPackageModal(true);
  };

  const handleEditPackage = (pkg: ServicePackage) => {
    setSelectedPackage(pkg);
    setShowPackageModal(true);
  };

  const handleDeletePackage = async (packageId: number) => {
    if (!window.confirm(t('services.confirmDeletePackage', 'Are you sure you want to delete this package?'))) {
      return;
    }

    try {
      await serviceApi.deletePackage(packageId);
      toast.success(t('services.deletePackageSuccess', 'Package deleted successfully'));
      fetchData();
    } catch (error) {
      console.error('Error deleting package:', error);
      toast.error(t('services.deletePackageError', 'Failed to delete package'));
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              {t('services.hero.title')}
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('services.hero.description')}
            </p>
            {!user && (
              <Button
                variant="primary"
                size="lg"
                className="mt-8"
                onClick={() => navigate('/login')}
              >
                {t('services.hero.getStarted')}
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="p-6 text-center">
            <GraduationCap className="w-8 h-8 text-accent-red mx-auto mb-2" />
            <p className="text-3xl font-bold text-gray-900">{services.length || '15+'}</p>
            <p className="text-sm text-gray-600">{t('services.stats.availableServices')}</p>
          </Card>
          <Card className="p-6 text-center">
            <Users className="w-8 h-8 text-accent-red mx-auto mb-2" />
            <p className="text-3xl font-bold text-gray-900">50+</p>
            <p className="text-sm text-gray-600">{t('services.stats.qualifiedTutors')}</p>
          </Card>
          <Card className="p-6 text-center">
            <Star className="w-8 h-8 text-accent-red mx-auto mb-2" />
            <p className="text-3xl font-bold text-gray-900">4.8</p>
            <p className="text-sm text-gray-600">{t('services.stats.averageRating')}</p>
          </Card>
          <Card className="p-6 text-center">
            <TrendingUp className="w-8 h-8 text-accent-red mx-auto mb-2" />
            <p className="text-3xl font-bold text-gray-900">95%</p>
            <p className="text-sm text-gray-600">{t('services.stats.successRate')}</p>
          </Card>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Tabs */}
        <div className="flex justify-center mb-8">
          <div className="bg-white rounded-lg shadow-sm p-1 inline-flex">
            <button
              onClick={() => setActiveTab('services')}
              className={`px-6 py-3 rounded-md font-medium transition-all ${
                activeTab === 'services'
                  ? 'bg-accent-red text-white'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Clock className="w-4 h-4 inline-block mr-2" />
              {t('services.tabs.individualSessions')}
            </button>
            <button
              onClick={() => setActiveTab('packages')}
              className={`px-6 py-3 rounded-md font-medium transition-all ${
                activeTab === 'packages'
                  ? 'bg-accent-red text-white'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Package className="w-4 h-4 inline-block mr-2" />
              {t('services.tabs.packages')}
            </button>
          </div>
        </div>

        {/* Individual Sessions */}
        {activeTab === 'services' && (
          <div>
            {/* Header with Create Button for Managers */}
            {canManageServices && (
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-semibold text-gray-900">
                  {t('services.manageCatalog', 'Manage Service Catalog')}
                </h2>
                <Button
                  variant="primary"
                  leftIcon={<Plus className="w-4 h-4" />}
                  onClick={handleCreateService}
                >
                  {t('services.createService', 'Create Service')}
                </Button>
              </div>
            )}

            {/* Filters */}
            <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
              <div className="flex flex-wrap gap-4 items-center">
                <div className="flex items-center">
                  <Filter className="w-5 h-5 text-gray-400 mr-2" />
                  <span className="text-sm font-medium text-gray-700">
                    {t('services.filters.title')}:
                  </span>
                </div>
                <Select
                  value={selectedServiceType}
                  onChange={(e) => setSelectedServiceType(e.target.value)}
                  className="w-48"
                  options={[
                    { value: 'all', label: t('services.filters.allTypes') },
                    { value: 'online', label: t('services.filters.online') },
                    { value: 'in_person', label: t('services.filters.inPerson') },
                    { value: 'library', label: t('services.filters.library') },
                    { value: 'hybrid', label: t('services.filters.hybrid') }
                  ]}
                />
                <Select
                  value={selectedSubject}
                  onChange={(e) => setSelectedSubject(e.target.value)}
                  className="w-48"
                  options={[
                    { value: 'all', label: t('services.filters.allSubjects') },
                    ...uniqueSubjects.map(subject => ({
                      value: subject,
                      label: subject.charAt(0).toUpperCase() + subject.slice(1)
                    }))
                  ]}
                />
              </div>
            </div>

            {/* Services Grid */}
            {filteredServices.length === 0 ? (
              <Card>
                <EmptyState
                  icon={<GraduationCap className="w-12 h-12 text-gray-400" />}
                  title={t('services.noServices')}
                  description={t('services.noServicesDescription')}
                />
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredServices.map((service) => (
                  <Card key={service.service_catalog_id} className="hover:shadow-lg transition-shadow">
                    <div className="p-6">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center">
                          {getServiceTypeIcon(service.service_type)}
                          <h3 className="text-lg font-semibold text-gray-900 ml-2">
                            {service.service_name}
                          </h3>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary" size="sm">
                            {service.service_type.replace('_', ' ')}
                          </Badge>
                          {canManageServices && (
                            <Dropdown
                              trigger={
                                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zM12 13a1 1 0 110-2 1 1 0 010 2zM12 20a1 1 0 110-2 1 1 0 010 2z" />
                                  </svg>
                                </Button>
                              }
                              items={[
                                {
                                  label: t('common.edit', 'Edit'),
                                  onClick: () => handleEditService(service),
                                  icon: <Edit className="w-4 h-4" />
                                },
                                {
                                  label: t('common.delete', 'Delete'),
                                  onClick: () => handleDeleteService(service.service_catalog_id),
                                  variant: 'danger',
                                  icon: <Trash2 className="w-4 h-4" />
                                }
                              ]}
                            />
                          )}
                        </div>
                      </div>

                      {service.description && (
                        <p className="text-sm text-gray-600 mb-4">
                          {service.description}
                        </p>
                      )}

                      <div className="space-y-3">
                        <div className="flex items-center text-sm">
                          <Clock className="w-4 h-4 text-gray-400 mr-2" />
                          <span className="text-gray-700">
                            {serviceApi.formatDuration(service.default_duration_minutes)} session
                          </span>
                        </div>

                        {service.subject_area && (
                          <div className="flex items-start text-sm">
                            <Book className="w-4 h-4 text-gray-400 mr-2 mt-0.5" />
                            <Badge variant="info" size="sm">
                              {service.subject_area.replace(/_/g, ' ')}
                            </Badge>
                          </div>
                        )}

                        {service.service_level && (
                          <div className="flex items-start text-sm">
                            <GraduationCap className="w-4 h-4 text-gray-400 mr-2 mt-0.5" />
                            <Badge variant="secondary" size="sm">
                              {service.service_level.replace(/_/g, ' ')}
                            </Badge>
                          </div>
                        )}

                        <div className="flex items-center justify-between pt-4 border-t">
                          <div>
                            <p className="text-sm text-gray-500">{t('services.durationRange')}</p>
                            <p className="text-lg font-semibold text-gray-900">
                              {service.min_duration_minutes}-{service.max_duration_minutes} min
                            </p>
                          </div>
                          {user && (
                            <Button
                              variant="primary"
                              size="sm"
                              onClick={() => navigate('/appointments/book')}
                              rightIcon={<ChevronRight className="w-4 h-4" />}
                            >
                              {t('services.bookNow')}
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Packages */}
        {activeTab === 'packages' && (
          <div>
            {/* Header with Create Button for Managers */}
            {canManageServices && (
              <div className="flex justify-between items-center mb-6">
                <div>
                  <h2 className="text-2xl font-semibold text-gray-900 mb-2">
                    {t('services.packages.title')}
                  </h2>
                  <p className="text-gray-600">
                    {t('services.packages.description')}
                  </p>
                </div>
                <Button
                  variant="primary"
                  leftIcon={<Plus className="w-4 h-4" />}
                  onClick={handleCreatePackage}
                >
                  {t('services.createPackage', 'Create Package')}
                </Button>
              </div>
            )}
            {!canManageServices && (
              <div className="text-center mb-8">
                <h2 className="text-2xl font-semibold text-gray-900 mb-2">
                  {t('services.packages.title')}
                </h2>
                <p className="text-gray-600">
                  {t('services.packages.description')}
                </p>
              </div>
            )}

            {packages.length === 0 ? (
              <Card>
                <EmptyState
                  icon={<Package className="w-12 h-12 text-gray-400" />}
                  title={t('services.noPackages')}
                  description={t('services.noPackagesDescription')}
                />
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {packages.map((pkg) => (
                  <Card 
                    key={pkg.service_package_id} 
                    className="hover:shadow-lg transition-shadow relative overflow-hidden"
                  >
                    {pkg.package_name.includes('TECFEE') && (
                      <div className="absolute top-0 right-0 bg-accent-red text-white px-3 py-1 text-xs font-medium rounded-bl-lg">
                        {t('services.packages.popular')}
                      </div>
                    )}
                    
                    <div className="p-6">
                      <div className="flex items-start justify-between mb-2">
                        <h3 className="text-xl font-semibold text-gray-900">
                          {pkg.package_name}
                        </h3>
                        {canManageServices && (
                          <Dropdown
                            trigger={
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zM12 13a1 1 0 110-2 1 1 0 010 2zM12 20a1 1 0 110-2 1 1 0 010 2z" />
                                </svg>
                              </Button>
                            }
                            items={[
                              {
                                label: t('common.edit', 'Edit'),
                                onClick: () => handleEditPackage(pkg),
                                icon: <Edit className="w-4 h-4" />
                              },
                              {
                                label: t('common.delete', 'Delete'),
                                onClick: () => handleDeletePackage(pkg.service_package_id),
                                variant: 'danger',
                                icon: <Trash2 className="w-4 h-4" />
                              }
                            ]}
                          />
                        )}
                      </div>
                      
                      <div className="space-y-3 mb-6">
                        <div className="flex items-center text-sm">
                          <Package className="w-4 h-4 text-gray-400 mr-2" />
                          <span className="text-gray-700">
                            {pkg.total_sessions} {t('services.packages.sessions')}
                          </span>
                        </div>
                        <div className="flex items-center text-sm">
                          <Clock className="w-4 h-4 text-gray-400 mr-2" />
                          <span className="text-gray-700">
                            {serviceApi.formatDuration(pkg.session_duration_minutes)} {t('services.packages.perSession')}
                          </span>
                        </div>
                        <div className="flex items-center text-sm">
                          <Calendar className="w-4 h-4 text-gray-400 mr-2" />
                          <span className="text-gray-700">
                            {t('services.packages.validFor')} {pkg.validity_months} {t('common.months')}
                          </span>
                        </div>
                      </div>

                      <div className="border-t pt-4">
                        <div className="flex items-baseline justify-between mb-4">
                          <div>
                            <p className="text-3xl font-bold text-gray-900">
                              ${pkg.package_price}
                            </p>
                            <p className="text-sm text-gray-500">
                              ${(pkg.package_price / pkg.total_sessions).toFixed(2)} {t('services.packages.perSessionPrice')}
                            </p>
                          </div>
                          <Badge variant="success" size="sm">
                            {t('services.packages.save')} ${((pkg.session_duration_minutes / 60 * 50 * pkg.total_sessions) - pkg.package_price).toFixed(0)}
                          </Badge>
                        </div>
                        
                        {user ? (
                          <Button
                            variant="primary"
                            size="sm"
                            className="w-full"
                            onClick={() => navigate('/billing/subscriptions')}
                          >
                            {t('services.packages.purchase')}
                          </Button>
                        ) : (
                          <Button
                            variant="secondary"
                            size="sm"
                            className="w-full"
                            onClick={() => navigate('/login')}
                          >
                            {t('services.packages.signInToPurchase')}
                          </Button>
                        )}
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </div>
        )}

        {/* CTA Section */}
        <div className="mt-16 bg-gradient-to-r from-gray-900 to-gray-700 rounded-2xl p-8 md:p-12 text-center text-white">
          <h2 className="text-3xl font-bold mb-4">
            {t('services.cta.title')}
          </h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto opacity-90">
            {t('services.cta.description')}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              variant="primary"
              size="lg"
              onClick={() => navigate('/appointments/book')}
              className="bg-white text-gray-900 hover:bg-gray-100"
            >
              {t('services.cta.bookSession')}
            </Button>
            <Button
              variant="secondary"
              size="lg"
              onClick={() => navigate('/map')}
              className="border-white text-white hover:bg-white hover:text-gray-900"
            >
              {t('services.cta.findTutor')}
            </Button>
          </div>
        </div>
      </div>

      {/* Service Modal */}
      <ServiceModal
        isOpen={showServiceModal}
        onClose={() => {
          setShowServiceModal(false);
          setSelectedService(null);
        }}
        service={selectedService}
        onSuccess={fetchData}
      />

      {/* Package Modal */}
      <PackageModal
        isOpen={showPackageModal}
        onClose={() => {
          setShowPackageModal(false);
          setSelectedPackage(null);
        }}
        package={selectedPackage}
        onSuccess={fetchData}
      />
    </div>
  );
};

export default ServicesPage;