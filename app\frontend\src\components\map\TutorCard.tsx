import React, { useState } from 'react';
import { 
  Star, 
  MapPin, 
  Clock, 
  GraduationCap,
  DollarSign,
  Phone,
  Video,
  Building,
  User
} from 'lucide-react';

interface TutorCardProps {
  tutor: {
    tutor_id: number;
    first_name: string;
    last_name: string;
    specialties?: string[];
    average_rating?: number;
    total_sessions?: number;
    hourly_rate?: number;
    availability_status?: 'available' | 'limited' | 'busy';
    distance_km?: number;
    profile_picture?: string;
    bio?: string;
    experience_years?: number;
    education?: string;
    languages?: string[];
    service_types?: string[];
    next_available?: string;
  };
  onBookSession: () => void;
  onViewProfile: () => void;
  showDistance?: boolean;
  expanded?: boolean;
}

const StarRating: React.FC<{ rating: number; size?: 'sm' | 'md' }> = ({ 
  rating, 
  size = 'sm' 
}) => {
  const starSize = size === 'sm' ? 'w-4 h-4' : 'w-5 h-5';
  
  return (
    <div className="flex items-center space-x-1">
      {[1, 2, 3, 4, 5].map((star) => (
        <div key={star} className="relative">
          {rating >= star ? (
            <Star className={`${starSize} text-yellow-400 fill-yellow-400`} />
          ) : rating >= star - 0.5 ? (
            <div className="relative">
              <Star className={`${starSize} text-gray-300`} />
              <div className="absolute inset-0 overflow-hidden" style={{ width: '50%' }}>
                <Star className={`${starSize} text-yellow-400 fill-yellow-400`} />
              </div>
            </div>
          ) : (
            <Star className={`${starSize} text-gray-300`} />
          )}
        </div>
      ))}
      <span className={`text-gray-600 ${size === 'sm' ? 'text-xs' : 'text-sm'} ml-1`}>
        ({rating.toFixed(1)})
      </span>
    </div>
  );
};

const AvailabilityBadge: React.FC<{ status: string }> = ({ status }) => {
  const config = {
    available: {
      color: 'bg-green-100 text-green-700 border-green-200',
      label: 'Available',
      icon: '🟢'
    },
    limited: {
      color: 'bg-yellow-100 text-yellow-700 border-yellow-200', 
      label: 'Limited',
      icon: '🟡'
    },
    busy: {
      color: 'bg-red-100 text-red-700 border-red-200',
      label: 'Busy',
      icon: '🔴'
    }
  };

  const { color, label, icon } = config[status as keyof typeof config] || config.busy;

  return (
    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${color}`}>
      <span className="mr-1">{icon}</span>
      {label}
    </span>
  );
};

const ServiceTypeIcons: React.FC<{ serviceTypes: string[] }> = ({ serviceTypes }) => {
  const iconMap = {
    online: { icon: Video, label: 'Online', color: 'text-red-500' },
    in_person: { icon: MapPin, label: 'In-Person', color: 'text-green-500' },
    library: { icon: Building, label: 'Library', color: 'text-purple-500' },
    hybrid: { icon: GraduationCap, label: 'Hybrid', color: 'text-orange-500' }
  };

  return (
    <div className="flex items-center space-x-2">
      {serviceTypes.map((type) => {
        const config = iconMap[type as keyof typeof iconMap];
        if (!config) return null;
        
        const IconComponent = config.icon;
        
        return (
          <div
            key={type}
            className="flex items-center space-x-1"
            title={config.label}
          >
            <IconComponent className={`w-4 h-4 ${config.color}`} />
            <span className="text-xs text-gray-600">{config.label}</span>
          </div>
        );
      })}
    </div>
  );
};

export const TutorCard: React.FC<TutorCardProps> = ({
  tutor,
  onBookSession,
  onViewProfile,
  showDistance = false,
  expanded = false
}) => {
  const {
    first_name,
    last_name,
    specialties = [],
    average_rating = 0,
    total_sessions = 0,
    hourly_rate = 0,
    availability_status = 'busy',
    distance_km,
    profile_picture,
    bio,
    experience_years,
    education,
    languages = [],
    service_types = [],
    next_available
  } = tutor;

  const initials = `${first_name[0]}${last_name[0]}`.toUpperCase();
  
  // State for image loading
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);

  return (
    <div className={`bg-white rounded-lg ${expanded ? 'p-0' : 'p-4'} ${!expanded ? 'shadow-sm border border-gray-200' : ''}`}>
      {/* Header */}
      <div className="flex items-start space-x-3">
        {/* Profile Picture */}
        <div className="flex-shrink-0">
          {profile_picture && !imageError ? (
            <div className="relative w-12 h-12">
              {/* Loading placeholder */}
              {imageLoading && (
                <div className="absolute inset-0 bg-gray-100 rounded-full animate-pulse border-2 border-gray-200" />
              )}
              <img
                src={profile_picture}
                alt={`${first_name} ${last_name}`}
                className={`w-12 h-12 rounded-full object-cover border-2 border-gray-200 transition-opacity duration-300 ${
                  imageLoading ? 'opacity-0' : 'opacity-100'
                }`}
                onLoad={() => setImageLoading(false)}
                onError={() => {
                  setImageError(true);
                  setImageLoading(false);
                }}
              />
            </div>
          ) : (
            <div className="w-12 h-12 bg-gradient-to-br from-red-50 to-red-100 rounded-full flex items-center justify-center border-2 border-red-200 shadow-inner">
              {imageError || !profile_picture ? (
                <span className="text-red-700 font-semibold text-sm">{initials}</span>
              ) : (
                <User className="w-6 h-6 text-red-400" />
              )}
            </div>
          )}
        </div>

        {/* Basic Info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold text-gray-900 truncate">
              {first_name} {last_name}
            </h3>
            <AvailabilityBadge status={availability_status} />
          </div>

          {/* Rating and Reviews */}
          <div className="flex items-center space-x-3 mt-1">
            {average_rating > 0 && (
              <StarRating rating={average_rating} />
            )}
            {total_sessions > 0 && (
              <span className="text-xs text-gray-500">
                {total_sessions} sessions
              </span>
            )}
          </div>

          {/* Distance */}
          {showDistance && distance_km && (
            <div className="flex items-center mt-1 text-xs text-gray-500">
              <MapPin className="w-3 h-3 mr-1" />
              {distance_km.toFixed(1)} km away
            </div>
          )}
        </div>
      </div>

      {/* Specialties */}
      {specialties.length > 0 && (
        <div className="mt-3">
          <div className="flex flex-wrap gap-1">
            {specialties.slice(0, expanded ? specialties.length : 3).map((specialty) => (
              <span
                key={specialty}
                className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-700"
              >
                {specialty.charAt(0).toUpperCase() + specialty.slice(1)}
              </span>
            ))}
            {!expanded && specialties.length > 3 && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-500">
                +{specialties.length - 3} more
              </span>
            )}
          </div>
        </div>
      )}

      {/* Service Types */}
      {service_types.length > 0 && (
        <div className="mt-3">
          <ServiceTypeIcons serviceTypes={service_types} />
        </div>
      )}

      {/* Expanded Details */}
      {expanded && (
        <div className="mt-4 space-y-3">
          {/* Bio */}
          {bio && (
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-1">About</h4>
              <p className="text-sm text-gray-600 line-clamp-3">{bio}</p>
            </div>
          )}

          {/* Experience and Education */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            {experience_years && (
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-1">Experience</h4>
                <p className="text-sm text-gray-600">{experience_years} years</p>
              </div>
            )}
            {education && (
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-1">Education</h4>
                <p className="text-sm text-gray-600">{education}</p>
              </div>
            )}
          </div>

          {/* Languages */}
          {languages.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-1">Languages</h4>
              <p className="text-sm text-gray-600">{languages.join(', ')}</p>
            </div>
          )}

          {/* Next Available */}
          {next_available && (
            <div className="flex items-center text-sm text-gray-600">
              <Clock className="w-4 h-4 mr-1" />
              Next available: {next_available}
            </div>
          )}
        </div>
      )}

      {/* Pricing and Actions */}
      <div className="mt-4 pt-3 border-t border-gray-200">
        <div className="flex items-center justify-between">
          {/* Hourly Rate */}
          <div className="flex items-center text-lg font-semibold text-gray-900">
            <DollarSign className="w-5 h-5 mr-1 text-gray-500" />
            ${hourly_rate}/hr
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-2">
            <button
              onClick={onViewProfile}
              className="px-3 py-1.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-full hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-accent-red transition-colors shadow-subtle"
            >
              View Profile
            </button>
            <button
              onClick={onBookSession}
              disabled={availability_status === 'busy'}
              className={`px-3 py-1.5 text-sm font-medium rounded-full focus:outline-none focus:ring-2 focus:ring-accent-red transition-colors shadow-soft ${
                availability_status === 'busy'
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-accent-red text-white hover:bg-accent-red-dark'
              }`}
            >
              {availability_status === 'busy' ? 'Unavailable' : 'Book Session'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
