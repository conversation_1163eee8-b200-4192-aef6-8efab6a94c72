"""
Subscription API endpoints for managing pre-paid hour packages.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, Path, Body
from typing import List, Optional, Dict, Any
from datetime import date
from decimal import Decimal

from app.services.subscription_service import SubscriptionService, get_subscription_service
from app.core.auth_decorators import require_auth, require_roles
from app.core.dependencies import get_current_user, get_db
from app.models.user_models import User
import asyncpg

router = APIRouter(prefix="/subscriptions")


@router.get("/packages")
@require_auth
async def get_subscription_packages(
    is_active: bool = Query(True, description="Filter by active status"),
    db: asyncpg.Connection = Depends(get_db),
    current_user: User = Depends(get_current_user),
    subscription_service: SubscriptionService = Depends(get_subscription_service)
):
    """Get available subscription packages."""
    try:
        packages = await subscription_service.get_subscription_packages(
            db, is_active_only=is_active
        )
        return packages
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch packages: {str(e)}"
        )


@router.get("/")
@require_auth
async def get_subscriptions(
    status: Optional[str] = Query(None, description="Filter by status"),
    expiring_days: Optional[int] = Query(None, description="Show expiring within days"),
    limit: int = Query(20, ge=1, le=100),
    offset: int = Query(0, ge=0),
    db: asyncpg.Connection = Depends(get_db),
    current_user: User = Depends(get_current_user),
    subscription_service: SubscriptionService = Depends(get_subscription_service)
):
    """Get all subscriptions (manager view) or user's own subscriptions."""
    try:
        if "manager" in [role.role for role in current_user.roles]:
            # Manager can see all subscriptions
            result = await subscription_service.get_all_subscriptions(
                db, status=status, expiring_days=expiring_days, 
                limit=limit, offset=offset
            )
            return result
        else:
            # Clients see only their own
            # Get client_id from user_id
            client_row = await db.fetchrow(
                "SELECT client_id FROM client_profiles WHERE user_id = $1",
                current_user.user_id
            )
            if not client_row:
                return {"subscriptions": [], "total": 0, "limit": limit, "offset": offset}
            
            subscriptions = await subscription_service.get_client_subscriptions(
                db, client_row['client_id']
            )
            return {
                "subscriptions": subscriptions,
                "total": len(subscriptions),
                "limit": limit,
                "offset": offset,
                "has_more": False
            }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch subscriptions: {str(e)}"
        )


@router.get("/expiring")
@require_roles(["manager"])
async def get_expiring_subscriptions(
    days: int = Query(30, description="Days to look ahead"),
    db: asyncpg.Connection = Depends(get_db),
    current_user: User = Depends(get_current_user),
    subscription_service: SubscriptionService = Depends(get_subscription_service)
):
    """Get subscriptions expiring within specified days."""
    try:
        result = await subscription_service.get_all_subscriptions(
            db, expiring_days=days
        )
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch expiring subscriptions: {str(e)}"
        )


@router.get("/clients/{client_id}/subscriptions")
@require_auth
async def get_client_subscriptions(
    client_id: int = Path(..., description="Client ID"),
    db: asyncpg.Connection = Depends(get_db),
    current_user: User = Depends(get_current_user),
    subscription_service: SubscriptionService = Depends(get_subscription_service)
):
    """Get all subscriptions for a specific client."""
    try:
        # Check authorization
        if "manager" not in [role.role for role in current_user.roles]:
            # Clients can only see their own
            client_row = await db.fetchrow(
                "SELECT client_id FROM client_profiles WHERE user_id = $1",
                current_user.user_id
            )
            if not client_row or client_row['client_id'] != client_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access denied"
                )
        
        subscriptions = await subscription_service.get_client_subscriptions(
            db, client_id
        )
        return subscriptions
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch client subscriptions: {str(e)}"
        )


@router.get("/clients/{client_id}/subscription-statistics")
@require_auth
async def get_client_subscription_statistics(
    client_id: int = Path(..., description="Client ID"),
    db: asyncpg.Connection = Depends(get_db),
    current_user: User = Depends(get_current_user),
    subscription_service: SubscriptionService = Depends(get_subscription_service)
):
    """Get subscription statistics for a client."""
    try:
        # Check authorization
        if "manager" not in [role.role for role in current_user.roles]:
            client_row = await db.fetchrow(
                "SELECT client_id FROM client_profiles WHERE user_id = $1",
                current_user.user_id
            )
            if not client_row or client_row['client_id'] != client_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access denied"
                )
        
        statistics = await subscription_service.get_subscription_statistics(
            db, client_id
        )
        return statistics
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch subscription statistics: {str(e)}"
        )


@router.get("/{subscription_id}/usage")
@require_auth
async def get_subscription_usage(
    subscription_id: int = Path(..., description="Subscription ID"),
    db: asyncpg.Connection = Depends(get_db),
    current_user: User = Depends(get_current_user),
    subscription_service: SubscriptionService = Depends(get_subscription_service)
):
    """Get usage history for a subscription."""
    try:
        # Check authorization - get subscription owner
        sub_row = await db.fetchrow(
            "SELECT client_id FROM billing_subscriptions WHERE subscription_id = $1",
            subscription_id
        )
        if not sub_row:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Subscription not found"
            )
        
        if "manager" not in [role.role for role in current_user.roles]:
            client_row = await db.fetchrow(
                "SELECT client_id FROM client_profiles WHERE user_id = $1",
                current_user.user_id
            )
            if not client_row or client_row['client_id'] != sub_row['client_id']:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access denied"
                )
        
        usage_history = await subscription_service.get_subscription_usage(
            db, subscription_id
        )
        return usage_history
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch usage history: {str(e)}"
        )


@router.post("/{subscription_id}/renew")
@require_auth
async def renew_subscription(
    subscription_id: int = Path(..., description="Subscription ID"),
    db: asyncpg.Connection = Depends(get_db),
    current_user: User = Depends(get_current_user),
    subscription_service: SubscriptionService = Depends(get_subscription_service)
):
    """Renew a subscription for another period."""
    try:
        # Check authorization
        sub_row = await db.fetchrow(
            "SELECT client_id FROM billing_subscriptions WHERE subscription_id = $1",
            subscription_id
        )
        if not sub_row:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Subscription not found"
            )
        
        if "manager" not in [role.role for role in current_user.roles]:
            client_row = await db.fetchrow(
                "SELECT client_id FROM client_profiles WHERE user_id = $1",
                current_user.user_id
            )
            if not client_row or client_row['client_id'] != sub_row['client_id']:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access denied"
                )
        
        await subscription_service.renew_subscription(
            db, subscription_id, current_user.user_id
        )
        
        return {"message": "Subscription renewed successfully"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to renew subscription: {str(e)}"
        )


@router.patch("/{subscription_id}/auto-renew")
@require_auth
async def update_auto_renew(
    subscription_id: int = Path(..., description="Subscription ID"),
    auto_renew: bool = Body(..., embed=True),
    db: asyncpg.Connection = Depends(get_db),
    current_user: User = Depends(get_current_user),
    subscription_service: SubscriptionService = Depends(get_subscription_service)
):
    """Update auto-renewal setting for a subscription."""
    try:
        # Get client_id for authorization
        client_row = await db.fetchrow(
            "SELECT client_id FROM client_profiles WHERE user_id = $1",
            current_user.user_id
        )
        if not client_row:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Client profile not found"
            )
        
        await subscription_service.update_auto_renew(
            db, subscription_id, auto_renew, client_row['client_id']
        )
        
        return {"message": "Auto-renewal setting updated"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update auto-renewal: {str(e)}"
        )