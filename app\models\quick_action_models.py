"""
Quick action models for role-based quick actions.
"""

from enum import Enum
from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel, Field, field_validator

from app.models.base import BaseEntity
from app.models.user_models import UserRoleType


class QuickActionType(str, Enum):
    """Types of quick actions available in the system."""
    # Manager actions
    ADD_USER = "add_user"
    INVITE_TUTOR = "invite_tutor"
    CREATE_CLIENT = "create_client"
    SCHEDULE_GROUP = "schedule_group"
    GENERATE_REPORT = "generate_report"
    SEND_BROADCAST = "send_broadcast"
    VIEW_ANALYTICS = "view_analytics"
    MANAGE_BILLING = "manage_billing"
    
    # Tutor actions
    VIEW_SCHEDULE = "view_schedule"
    REQUEST_TIME_OFF = "request_time_off"
    UPDATE_AVAILABILITY = "update_availability"
    SUBMIT_DOCUMENTS = "submit_documents"
    VIEW_MY_STUDENTS = "view_my_students"
    UPDATE_RATES = "update_rates"
    VIEW_EARNINGS = "view_earnings"
    
    # Client actions
    BOOK_SESSION = "book_session"
    ADD_DEPENDANT = "add_dependant"
    VIEW_INVOICES = "view_invoices"
    MESSAGE_TUTOR = "message_tutor"
    UPDATE_PROFILE = "update_profile"
    VIEW_SESSIONS = "view_sessions"
    MAKE_PAYMENT = "make_payment"
    
    # Common actions
    SEARCH_USERS = "search_users"
    VIEW_NOTIFICATIONS = "view_notifications"
    CHANGE_SETTINGS = "change_settings"


class QuickActionCategory(str, Enum):
    """Categories for grouping quick actions."""
    USER_MANAGEMENT = "user_management"
    SCHEDULING = "scheduling"
    FINANCIAL = "financial"
    COMMUNICATION = "communication"
    PROFILE = "profile"
    REPORTING = "reporting"
    SYSTEM = "system"


class QuickActionDefinition(BaseModel):
    """Definition of a quick action."""
    action_type: QuickActionType
    name: str
    description: str
    category: QuickActionCategory
    icon: Optional[str] = None  # Icon name or class
    color: Optional[str] = None  # Color for the action button
    allowed_roles: List[UserRoleType]
    requires_selection: bool = False  # Whether action requires selecting an entity first
    keyboard_shortcut: Optional[str] = None
    mobile_enabled: bool = True
    desktop_enabled: bool = True
    priority: int = Field(default=0, description="Higher priority actions appear first")
    
    @field_validator('allowed_roles')
    def validate_roles(cls, v):
        """Ensure at least one role is specified."""
        if not v:
            raise ValueError("At least one role must be allowed for the action")
        return v


class QuickActionContext(BaseModel):
    """Context for executing a quick action."""
    user_id: int
    user_role: UserRoleType
    action_type: QuickActionType
    selected_entity_id: Optional[int] = None
    selected_entity_type: Optional[str] = None  # 'client', 'tutor', 'dependant', etc.
    additional_params: Optional[Dict[str, Any]] = {}
    client_context: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Client-side context (current page, filters, etc.)"
    )


class QuickActionResult(BaseModel):
    """Result of executing a quick action."""
    success: bool
    action_type: QuickActionType
    message: Optional[str] = None
    redirect_url: Optional[str] = None
    modal_component: Optional[str] = None  # Name of modal to open
    modal_props: Optional[Dict[str, Any]] = None  # Props for the modal
    data: Optional[Dict[str, Any]] = None  # Additional data for the client
    requires_confirmation: bool = False
    confirmation_message: Optional[str] = None
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class QuickActionUsage(BaseEntity):
    """Track usage of quick actions for analytics."""
    usage_id: Optional[int] = None
    user_id: int
    action_type: QuickActionType
    executed_at: datetime
    execution_time_ms: Optional[int] = None  # Time taken to execute
    success: bool
    error_message: Optional[str] = None
    context_data: Optional[Dict[str, Any]] = None
    
    @field_validator('execution_time_ms')
    def validate_execution_time(cls, v):
        """Ensure execution time is positive."""
        if v is not None and v < 0:
            raise ValueError("Execution time must be positive")
        return v


class UserQuickActions(BaseModel):
    """User's available quick actions based on their role and context."""
    user_id: int
    active_role: UserRoleType
    available_actions: List[QuickActionDefinition]
    recent_actions: List[QuickActionType] = []  # Most recently used actions
    pinned_actions: List[QuickActionType] = []  # User's pinned actions
    suggested_actions: List[QuickActionType] = []  # AI/context-suggested actions
    
    @property
    def primary_actions(self) -> List[QuickActionDefinition]:
        """Get primary actions (pinned or high priority)."""
        # First return pinned actions
        pinned = [a for a in self.available_actions if a.action_type in self.pinned_actions]
        if pinned:
            return pinned[:5]  # Max 5 primary actions
        
        # Otherwise return highest priority actions
        return sorted(self.available_actions, key=lambda x: x.priority, reverse=True)[:5]
    
    @property
    def categorized_actions(self) -> Dict[QuickActionCategory, List[QuickActionDefinition]]:
        """Get actions grouped by category."""
        result = {}
        for action in self.available_actions:
            if action.category not in result:
                result[action.category] = []
            result[action.category].append(action)
        return result


# Pre-defined quick actions
QUICK_ACTION_DEFINITIONS = [
    # Manager actions
    QuickActionDefinition(
        action_type=QuickActionType.ADD_USER,
        name="Add New User",
        description="Create a new user account",
        category=QuickActionCategory.USER_MANAGEMENT,
        icon="user-plus",
        color="blue",
        allowed_roles=[UserRoleType.MANAGER],
        keyboard_shortcut="ctrl+alt+u",
        priority=10
    ),
    QuickActionDefinition(
        action_type=QuickActionType.INVITE_TUTOR,
        name="Invite Tutor",
        description="Send invitation to a new tutor",
        category=QuickActionCategory.USER_MANAGEMENT,
        icon="envelope",
        color="green",
        allowed_roles=[UserRoleType.MANAGER],
        keyboard_shortcut="ctrl+alt+i",
        priority=9
    ),
    QuickActionDefinition(
        action_type=QuickActionType.CREATE_CLIENT,
        name="Create Client",
        description="Add a new client profile",
        category=QuickActionCategory.USER_MANAGEMENT,
        icon="user-tie",
        color="purple",
        allowed_roles=[UserRoleType.MANAGER],
        priority=8
    ),
    QuickActionDefinition(
        action_type=QuickActionType.SCHEDULE_GROUP,
        name="Schedule Group Session",
        description="Create a new group tutoring session",
        category=QuickActionCategory.SCHEDULING,
        icon="calendar-plus",
        color="orange",
        allowed_roles=[UserRoleType.MANAGER],
        priority=7
    ),
    QuickActionDefinition(
        action_type=QuickActionType.GENERATE_REPORT,
        name="Generate Report",
        description="Create financial or performance reports",
        category=QuickActionCategory.REPORTING,
        icon="chart-bar",
        color="teal",
        allowed_roles=[UserRoleType.MANAGER],
        keyboard_shortcut="ctrl+alt+r",
        priority=6
    ),
    QuickActionDefinition(
        action_type=QuickActionType.SEND_BROADCAST,
        name="Send Broadcast",
        description="Send message to multiple users",
        category=QuickActionCategory.COMMUNICATION,
        icon="bullhorn",
        color="red",
        allowed_roles=[UserRoleType.MANAGER],
        priority=5
    ),
    
    # Tutor actions
    QuickActionDefinition(
        action_type=QuickActionType.VIEW_SCHEDULE,
        name="View My Schedule",
        description="See your upcoming tutoring sessions",
        category=QuickActionCategory.SCHEDULING,
        icon="calendar",
        color="blue",
        allowed_roles=[UserRoleType.TUTOR],
        keyboard_shortcut="ctrl+s",
        priority=10
    ),
    QuickActionDefinition(
        action_type=QuickActionType.REQUEST_TIME_OFF,
        name="Request Time Off",
        description="Submit a time-off request",
        category=QuickActionCategory.SCHEDULING,
        icon="calendar-times",
        color="orange",
        allowed_roles=[UserRoleType.TUTOR],
        priority=8
    ),
    QuickActionDefinition(
        action_type=QuickActionType.UPDATE_AVAILABILITY,
        name="Update Availability",
        description="Modify your available hours",
        category=QuickActionCategory.SCHEDULING,
        icon="clock",
        color="green",
        allowed_roles=[UserRoleType.TUTOR],
        priority=9
    ),
    QuickActionDefinition(
        action_type=QuickActionType.SUBMIT_DOCUMENTS,
        name="Submit Documents",
        description="Upload verification documents",
        category=QuickActionCategory.PROFILE,
        icon="file-upload",
        color="purple",
        allowed_roles=[UserRoleType.TUTOR],
        priority=7
    ),
    QuickActionDefinition(
        action_type=QuickActionType.VIEW_MY_STUDENTS,
        name="View My Students",
        description="See all assigned students",
        category=QuickActionCategory.USER_MANAGEMENT,
        icon="users",
        color="teal",
        allowed_roles=[UserRoleType.TUTOR],
        keyboard_shortcut="ctrl+m",
        priority=9
    ),
    
    # Client actions
    QuickActionDefinition(
        action_type=QuickActionType.BOOK_SESSION,
        name="Book Session",
        description="Schedule a tutoring session",
        category=QuickActionCategory.SCHEDULING,
        icon="calendar-check",
        color="green",
        allowed_roles=[UserRoleType.CLIENT],
        keyboard_shortcut="ctrl+b",
        priority=10
    ),
    QuickActionDefinition(
        action_type=QuickActionType.ADD_DEPENDANT,
        name="Add Dependant",
        description="Add a child or dependant",
        category=QuickActionCategory.USER_MANAGEMENT,
        icon="child",
        color="blue",
        allowed_roles=[UserRoleType.CLIENT],
        priority=9
    ),
    QuickActionDefinition(
        action_type=QuickActionType.VIEW_INVOICES,
        name="View Invoices",
        description="See billing history and invoices",
        category=QuickActionCategory.FINANCIAL,
        icon="file-invoice",
        color="purple",
        allowed_roles=[UserRoleType.CLIENT],
        priority=7
    ),
    QuickActionDefinition(
        action_type=QuickActionType.MESSAGE_TUTOR,
        name="Message Tutor",
        description="Send a message to a tutor",
        category=QuickActionCategory.COMMUNICATION,
        icon="comment",
        color="orange",
        allowed_roles=[UserRoleType.CLIENT],
        requires_selection=True,
        priority=8
    ),
    QuickActionDefinition(
        action_type=QuickActionType.UPDATE_PROFILE,
        name="Update Profile",
        description="Edit your profile information",
        category=QuickActionCategory.PROFILE,
        icon="user-edit",
        color="gray",
        allowed_roles=[UserRoleType.CLIENT, UserRoleType.TUTOR],
        keyboard_shortcut="ctrl+p",
        priority=6
    ),
    
    # Common actions
    QuickActionDefinition(
        action_type=QuickActionType.SEARCH_USERS,
        name="Search",
        description="Search for users, tutors, or clients",
        category=QuickActionCategory.SYSTEM,
        icon="search",
        color="gray",
        allowed_roles=[UserRoleType.MANAGER, UserRoleType.TUTOR, UserRoleType.CLIENT],
        keyboard_shortcut="ctrl+k",
        priority=10
    ),
]


def get_actions_for_role(role: UserRoleType) -> List[QuickActionDefinition]:
    """Get all quick actions available for a specific role."""
    return [action for action in QUICK_ACTION_DEFINITIONS if role in action.allowed_roles]


def get_action_by_type(action_type: QuickActionType) -> Optional[QuickActionDefinition]:
    """Get a specific action definition by type."""
    for action in QUICK_ACTION_DEFINITIONS:
        if action.action_type == action_type:
            return action
    return None