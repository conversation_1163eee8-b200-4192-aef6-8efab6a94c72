import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { X, Plus, Edit, Trash2, Package } from 'lucide-react';
import { Modal } from '../common/Modal';
import Button from '../common/Button';
import { Input } from '../common/Input';
import { Badge } from '../common/Badge';
import { subscriptionService, SubscriptionPlan, SubscriptionType } from '../../services/subscriptionService';
import toast from 'react-hot-toast';

interface ManagePlansModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUpdate: () => void;
}

interface PlanFormData {
  name: string;
  description: string;
  type: SubscriptionType;
  hours_included: number;
  price: number;
  currency: string;
  duration_months: number;
  is_active: boolean;
  features: string[];
}

const ManagePlansModal: React.FC<ManagePlansModalProps> = ({
  isOpen,
  onClose,
  onUpdate
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [showForm, setShowForm] = useState(false);
  const [editingPlan, setEditingPlan] = useState<SubscriptionPlan | null>(null);
  const [formData, setFormData] = useState<PlanFormData>({
    name: '',
    description: '',
    type: SubscriptionType.MONTHLY,
    hours_included: 0,
    price: 0,
    currency: 'CAD',
    duration_months: 1,
    is_active: true,
    features: []
  });
  const [newFeature, setNewFeature] = useState('');

  useEffect(() => {
    if (isOpen) {
      loadPlans();
    }
  }, [isOpen]);

  const loadPlans = async () => {
    try {
      const plansData = await subscriptionService.getPlans();
      setPlans(plansData);
    } catch (error) {
      console.error('Error loading plans:', error);
      toast.error(t('billing.plans.loadError'));
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      type: SubscriptionType.MONTHLY,
      hours_included: 0,
      price: 0,
      currency: 'CAD',
      duration_months: 1,
      is_active: true,
      features: []
    });
    setNewFeature('');
    setEditingPlan(null);
    setShowForm(false);
  };

  const handleEdit = (plan: SubscriptionPlan) => {
    setEditingPlan(plan);
    setFormData({
      name: plan.name,
      description: plan.description || '',
      type: plan.type,
      hours_included: plan.hours_included,
      price: plan.price,
      currency: plan.currency,
      duration_months: plan.duration_months,
      is_active: plan.is_active,
      features: plan.features || []
    });
    setShowForm(true);
  };

  const handleDelete = async (plan: SubscriptionPlan) => {
    if (!confirm(t('billing.plans.confirmDelete'))) return;

    setLoading(true);
    try {
      await subscriptionService.deletePlan(plan.plan_id);
      toast.success(t('billing.plans.deleteSuccess'));
      loadPlans();
    } catch (error) {
      console.error('Error deleting plan:', error);
      toast.error(t('billing.plans.deleteError'));
    } finally {
      setLoading(false);
    }
  };

  const addFeature = () => {
    if (newFeature && !formData.features.includes(newFeature)) {
      setFormData(prev => ({
        ...prev,
        features: [...prev.features, newFeature]
      }));
      setNewFeature('');
    }
  };

  const removeFeature = (feature: string) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.filter(f => f !== feature)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.hours_included || !formData.price) {
      toast.error(t('validation.requiredFields'));
      return;
    }

    setLoading(true);
    try {
      if (editingPlan) {
        await subscriptionService.updatePlan(editingPlan.plan_id, formData);
        toast.success(t('billing.plans.updateSuccess'));
      } else {
        await subscriptionService.createPlan(formData);
        toast.success(t('billing.plans.createSuccess'));
      }
      loadPlans();
      resetForm();
      onUpdate();
    } catch (error: any) {
      console.error('Error saving plan:', error);
      toast.error(error.response?.data?.detail || t('billing.plans.saveError'));
    } finally {
      setLoading(false);
    }
  };

  const getTypeLabel = (type: SubscriptionType) => {
    switch (type) {
      case SubscriptionType.MONTHLY:
        return t('billing.subscriptions.types.monthly');
      case SubscriptionType.QUARTERLY:
        return t('billing.subscriptions.types.quarterly');
      case SubscriptionType.ANNUAL:
        return t('billing.subscriptions.types.annual');
      case SubscriptionType.CUSTOM:
        return t('billing.subscriptions.types.custom');
      default:
        return type;
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">
            {t('billing.plans.title')}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        {!showForm ? (
          <>
            {/* Add Plan Button */}
            <div className="mb-6">
              <Button
                variant="primary"
                onClick={() => setShowForm(true)}
              >
                <Plus className="w-4 h-4 mr-2" />
                {t('billing.plans.add')}
              </Button>
            </div>

            {/* Plans List */}
            <div className="space-y-4">
              {plans.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Package className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                  <p>{t('billing.plans.empty')}</p>
                </div>
              ) : (
                plans.map((plan) => (
                  <div
                    key={plan.plan_id}
                    className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900">{plan.name}</h3>
                          <Badge variant="info" size="sm">{getTypeLabel(plan.type)}</Badge>
                          <Badge variant={plan.is_active ? 'success' : 'secondary'} size="sm">
                            {plan.is_active ? t('common.active') : t('common.inactive')}
                          </Badge>
                        </div>
                        {plan.description && (
                          <p className="text-sm text-gray-600 mb-3">{plan.description}</p>
                        )}
                        <div className="grid grid-cols-3 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600">{t('billing.plans.hours')}:</span>
                            <span className="font-semibold ml-1">{plan.hours_included} hrs</span>
                          </div>
                          <div>
                            <span className="text-gray-600">{t('billing.plans.price')}:</span>
                            <span className="font-semibold ml-1">
                              ${plan.price} {plan.currency}
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-600">{t('billing.plans.duration')}:</span>
                            <span className="font-semibold ml-1">{plan.duration_months} {t('common.months')}</span>
                          </div>
                        </div>
                        {plan.features && plan.features.length > 0 && (
                          <div className="mt-3">
                            <div className="text-sm text-gray-600 mb-1">{t('billing.plans.features')}:</div>
                            <div className="flex flex-wrap gap-2">
                              {plan.features.map((feature, idx) => (
                                <Badge key={idx} variant="secondary" size="sm">{feature}</Badge>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                      <div className="flex items-center space-x-2 ml-4">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEdit(plan)}
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(plan)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </>
        ) : (
          /* Plan Form */
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('billing.plans.name')} *
                </label>
                <Input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('billing.plans.type')} *
                </label>
                <select
                  value={formData.type}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    type: e.target.value as SubscriptionType,
                    duration_months: e.target.value === SubscriptionType.MONTHLY ? 1 :
                                   e.target.value === SubscriptionType.QUARTERLY ? 3 :
                                   e.target.value === SubscriptionType.ANNUAL ? 12 : 1
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-red"
                >
                  <option value={SubscriptionType.MONTHLY}>{t('billing.subscriptions.types.monthly')}</option>
                  <option value={SubscriptionType.QUARTERLY}>{t('billing.subscriptions.types.quarterly')}</option>
                  <option value={SubscriptionType.ANNUAL}>{t('billing.subscriptions.types.annual')}</option>
                  <option value={SubscriptionType.CUSTOM}>{t('billing.subscriptions.types.custom')}</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('billing.plans.description')}
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={2}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-red"
              />
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('billing.plans.hoursIncluded')} *
                </label>
                <Input
                  type="number"
                  value={formData.hours_included}
                  onChange={(e) => setFormData(prev => ({ ...prev, hours_included: parseFloat(e.target.value) || 0 }))}
                  min="0"
                  step="0.5"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('billing.plans.price')} (CAD) *
                </label>
                <Input
                  type="number"
                  value={formData.price}
                  onChange={(e) => setFormData(prev => ({ ...prev, price: parseFloat(e.target.value) || 0 }))}
                  min="0"
                  step="0.01"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('billing.plans.durationMonths')} *
                </label>
                <Input
                  type="number"
                  value={formData.duration_months}
                  onChange={(e) => setFormData(prev => ({ ...prev, duration_months: parseInt(e.target.value) || 1 }))}
                  min="1"
                  disabled={formData.type !== SubscriptionType.CUSTOM}
                  required
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('billing.plans.features')}
              </label>
              {formData.features.length > 0 && (
                <div className="mb-3 flex flex-wrap gap-2">
                  {formData.features.map((feature, idx) => (
                    <div
                      key={idx}
                      className="inline-flex items-center bg-gray-100 text-gray-800 rounded-full px-3 py-1 text-sm"
                    >
                      <span>{feature}</span>
                      <button
                        type="button"
                        onClick={() => removeFeature(feature)}
                        className="ml-2 text-gray-600 hover:text-gray-800"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
              <div className="flex gap-2">
                <Input
                  type="text"
                  value={newFeature}
                  onChange={(e) => setNewFeature(e.target.value)}
                  placeholder={t('billing.plans.addFeature')}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      addFeature();
                    }
                  }}
                />
                <Button
                  type="button"
                  variant="secondary"
                  onClick={addFeature}
                  disabled={!newFeature}
                >
                  <Plus className="w-4 h-4" />
                </Button>
              </div>
            </div>

            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.is_active}
                onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.checked }))}
                className="mr-2"
              />
              <span className="text-sm text-gray-700">{t('billing.plans.isActive')}</span>
            </label>

            <div className="flex justify-end space-x-3">
              <Button
                type="button"
                variant="secondary"
                onClick={resetForm}
              >
                {t('common.cancel')}
              </Button>
              <Button
                type="submit"
                variant="primary"
                loading={loading}
              >
                {editingPlan ? t('billing.plans.update') : t('billing.plans.create')}
              </Button>
            </div>
          </form>
        )}

        {/* Footer */}
        {!showForm && (
          <div className="mt-6 pt-6 border-t flex justify-end">
            <Button variant="secondary" onClick={onClose}>
              {t('common.close')}
            </Button>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default ManagePlansModal;