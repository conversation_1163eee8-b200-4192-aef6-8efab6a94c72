"""
Branding configuration for TutorAide application.
"""

from typing import Dict, Any
from pathlib import Path


class BrandingConfig:
    """Configuration class for TutorAide branding."""
    
    # Brand Identity
    BRAND_NAME = "TutorAide"
    BRAND_TAGLINE = "Excellence in Educational Support"
    BRAND_DESCRIPTION = "Comprehensive tutoring management platform connecting students with qualified tutors"
    
    # Website URLs
    MAIN_WEBSITE = "https://www.tutoraide.ca"
    LOGO_REDIRECT_URL = MAIN_WEBSITE
    
    # Email Configuration
    EMAIL_BILLING = "<EMAIL>"
    EMAIL_SUPPORT = "<EMAIL>"
    EMAIL_GENERAL = "<EMAIL>"
    
    # Color Palette
    COLORS = {
        # Primary Brand Colors
        "primary": "#e57373",           # Pastel red
        "primary_hover": "#ef5350",     # Red hover state
        "accent": "#ffd700",            # Gold accent
        
        # Text Colors
        "text_primary": "#333333",      # Main text
        "text_secondary": "#666666",    # Supporting text
        "text_muted": "#999999",        # Muted text
        
        # Background Colors
        "background_light": "#f5f5f5",  # Page backgrounds
        "background_white": "#ffffff",  # Card backgrounds
        "background_card": "#fafafa",   # Subtle card background
        
        # Border and UI
        "border": "#e0e0e0",           # Subtle borders
        
        # System Colors
        "success": "#4caf50",          # Success states
        "warning": "#ff9800",          # Warning states
        "error": "#f44336",            # Error states
        "info": "#2196f3",             # Info states
    }
    
    # Typography
    FONT_FAMILY = "'Segoe UI', -apple-system, BlinkMacSystemFont, 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif"
    
    FONT_SIZES = {
        "display": "2.5rem",    # 40px - Hero headings
        "h1": "2rem",           # 32px - Page titles
        "h2": "1.5rem",         # 24px - Section headers
        "h3": "1.25rem",        # 20px - Subsection headers
        "h4": "1.125rem",       # 18px - Card titles
        "body": "1rem",         # 16px - Regular text
        "small": "0.875rem",    # 14px - Captions, metadata
        "xs": "0.75rem",        # 12px - Fine print, labels
    }
    
    FONT_WEIGHTS = {
        "light": 300,
        "regular": 400,
        "medium": 500,
        "semibold": 600,
        "bold": 700,
    }
    
    # Design System
    BORDER_RADIUS = {
        "small": "4px",
        "medium": "8px",
        "large": "16px",
        "round": "50%",
    }
    
    SPACING = {
        "xs": "0.25rem",    # 4px
        "sm": "0.5rem",     # 8px
        "md": "1rem",       # 16px
        "lg": "1.5rem",     # 24px
        "xl": "2rem",       # 32px
        "2xl": "3rem",      # 48px
        "3xl": "4rem",      # 64px
    }
    
    SHADOWS = {
        "light": "0 2px 8px rgba(0,0,0,0.1)",
        "medium": "0 4px 16px rgba(0,0,0,0.15)",
        "heavy": "0 8px 32px rgba(0,0,0,0.2)",
    }
    
    # Asset Paths
    @classmethod
    def get_assets_path(cls) -> Path:
        """Get path to branding assets."""
        return Path(__file__).parent.parent / "frontend" / "public"
    
    @classmethod
    def get_favicon_path(cls) -> Path:
        """Get path to favicon."""
        return cls.get_assets_path() / "favicon_tutoraide.png"
    
    @classmethod
    def get_logo_path(cls) -> Path:
        """Get path to logo."""
        return cls.get_assets_path() / "logo_tutoraide.jpg"
    
    # Cache Configuration for Assets
    STATIC_CACHE_MAX_AGE = 3600  # 1 hour for static assets
    HTML_CACHE_CONTROL = "no-cache"  # No cache for HTML files
    
    # Responsive Breakpoints
    BREAKPOINTS = {
        "sm": "576px",      # Small devices
        "md": "768px",      # Medium devices
        "lg": "992px",      # Large devices
        "xl": "1200px",     # Extra large devices
    }
    
    # Meta Tags for SEO
    META_TAGS = {
        "description": f"{BRAND_DESCRIPTION} for personalized learning experiences.",
        "keywords": "tutoring, education, learning, mathematics, science, french, english, online tutoring, in-person tutoring",
        "author": f"{BRAND_NAME} Inc.",
        "theme_color": COLORS["primary"],
    }
    
    # Open Graph Meta Tags
    OPEN_GRAPH = {
        "title": f"{BRAND_NAME} - {BRAND_TAGLINE}",
        "description": "Connect with qualified tutors for personalized learning experiences in mathematics, science, languages, and more.",
        "type": "website",
        "url": MAIN_WEBSITE,
        "image": "/logo_tutoraide.jpg",
    }
    
    # PWA Configuration
    PWA_CONFIG = {
        "name": BRAND_NAME,
        "short_name": BRAND_NAME,
        "description": BRAND_DESCRIPTION,
        "theme_color": COLORS["primary"],
        "background_color": COLORS["background_white"],
        "display": "standalone",
        "start_url": "/",
        "scope": "/",
    }
    
    @classmethod
    def get_css_variables(cls) -> str:
        """Generate CSS custom properties for the design system."""
        css_vars = [":root {"]
        
        # Colors
        for name, value in cls.COLORS.items():
            css_vars.append(f"  --color-{name.replace('_', '-')}: {value};")
        
        # Typography
        css_vars.append(f"  --font-family: {cls.FONT_FAMILY};")
        
        for name, value in cls.FONT_SIZES.items():
            css_vars.append(f"  --font-size-{name}: {value};")
        
        for name, value in cls.FONT_WEIGHTS.items():
            css_vars.append(f"  --font-weight-{name}: {value};")
        
        # Spacing
        for name, value in cls.SPACING.items():
            css_vars.append(f"  --space-{name}: {value};")
        
        # Border radius
        for name, value in cls.BORDER_RADIUS.items():
            css_vars.append(f"  --radius-{name}: {value};")
        
        # Shadows
        for name, value in cls.SHADOWS.items():
            css_vars.append(f"  --shadow-{name}: {value};")
        
        css_vars.append("}")
        
        return "\n".join(css_vars)
    
    @classmethod
    def get_meta_tags_html(cls) -> str:
        """Generate HTML meta tags for branding."""
        meta_tags = []
        
        # Basic meta tags
        for name, content in cls.META_TAGS.items():
            meta_tags.append(f'<meta name="{name}" content="{content}">')
        
        # Open Graph meta tags
        for property_name, content in cls.OPEN_GRAPH.items():
            meta_tags.append(f'<meta property="og:{property_name}" content="{content}">')
        
        # PWA meta tags
        meta_tags.append(f'<meta name="theme-color" content="{cls.PWA_CONFIG["theme_color"]}">')
        meta_tags.append('<meta name="apple-mobile-web-app-capable" content="yes">')
        meta_tags.append('<meta name="apple-mobile-web-app-status-bar-style" content="default">')
        meta_tags.append(f'<meta name="apple-mobile-web-app-title" content="{cls.PWA_CONFIG["short_name"]}">')
        
        return "\n".join(meta_tags)
    
    @classmethod
    def get_favicon_links_html(cls) -> str:
        """Generate HTML favicon links."""
        favicon_links = [
            '<link rel="icon" type="image/png" href="/favicon_tutoraide.png">',
            '<link rel="shortcut icon" href="/favicon_tutoraide.png">',
            '<link rel="apple-touch-icon" href="/favicon_tutoraide.png">',
        ]
        
        return "\n".join(favicon_links)
    
    @classmethod
    def validate_assets(cls) -> Dict[str, bool]:
        """Validate that all required branding assets exist."""
        assets = {
            "favicon": cls.get_favicon_path().exists(),
            "logo": cls.get_logo_path().exists(),
            "assets_directory": cls.get_assets_path().exists(),
        }
        
        return assets
    
    @classmethod
    def get_brand_info(cls) -> Dict[str, Any]:
        """Get complete brand information for API responses."""
        return {
            "name": cls.BRAND_NAME,
            "tagline": cls.BRAND_TAGLINE,
            "description": cls.BRAND_DESCRIPTION,
            "website": cls.MAIN_WEBSITE,
            "emails": {
                "billing": cls.EMAIL_BILLING,
                "support": cls.EMAIL_SUPPORT,
                "general": cls.EMAIL_GENERAL,
            },
            "colors": cls.COLORS,
            "assets_available": cls.validate_assets(),
        }


# Global instance for easy access
branding = BrandingConfig()