"""
Translation validation utilities for ensuring completeness and quality.
"""

import re
import json
from pathlib import Path
from typing import Dict, List, Set, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

from .constants import (
    SUPPORTED_LANGUAGES, DEFAULT_LANGUAGE, TRANSLATION_VALIDATION,
    TEMPLATE_PATTERNS, CONTENT_CATEGORIES
)
from .translation_service import get_translation_service


@dataclass
class ValidationResult:
    """Result of translation validation."""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    missing_keys: Dict[str, List[str]]
    statistics: Dict[str, Any]
    timestamp: datetime


class TranslationValidator:
    """Comprehensive validator for translation files and content."""
    
    def __init__(self, locales_path: Optional[Path] = None):
        """Initialize validator with translation files."""
        if locales_path is None:
            locales_path = Path(__file__).parent
        
        self.locales_path = locales_path
        self.translation_service = get_translation_service()
    
    def validate_all_translations(self) -> ValidationResult:
        """Perform comprehensive validation of all translation files."""
        errors = []
        warnings = []
        missing_keys = {}
        
        # Load translation files
        translations = {}
        for language in SUPPORTED_LANGUAGES:
            translation_file = self.locales_path / f"{language}.json"
            if translation_file.exists():
                try:
                    with open(translation_file, 'r', encoding='utf-8') as f:
                        translations[language] = json.load(f)
                except json.JSONDecodeError as e:
                    errors.append(f"Invalid JSON in {language}.json: {e}")
                    translations[language] = {}
            else:
                errors.append(f"Translation file {language}.json not found")
                translations[language] = {}
        
        # Validate completeness
        completeness_result = self._validate_completeness(translations)
        errors.extend(completeness_result['errors'])
        warnings.extend(completeness_result['warnings'])
        missing_keys.update(completeness_result['missing_keys'])
        
        # Validate consistency
        consistency_result = self._validate_consistency(translations)
        errors.extend(consistency_result['errors'])
        warnings.extend(consistency_result['warnings'])
        
        # Validate content quality
        quality_result = self._validate_content_quality(translations)
        errors.extend(quality_result['errors'])
        warnings.extend(quality_result['warnings'])
        
        # Generate statistics
        statistics = self._generate_statistics(translations, missing_keys)
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            missing_keys=missing_keys,
            statistics=statistics,
            timestamp=datetime.now()
        )
    
    def _validate_completeness(self, translations: Dict[str, Dict]) -> Dict[str, Any]:
        """Validate that all required keys exist in all languages."""
        errors = []
        warnings = []
        missing_keys = {}
        
        # Get all keys from default language
        default_keys = self._extract_all_keys(translations.get(DEFAULT_LANGUAGE, {}))
        
        for language in SUPPORTED_LANGUAGES:
            language_keys = self._extract_all_keys(translations.get(language, {}))
            missing = default_keys - language_keys
            extra = language_keys - default_keys
            
            missing_keys[language] = list(missing)
            
            if missing:
                if language == DEFAULT_LANGUAGE:
                    errors.append(f"Default language {language} missing required keys: {list(missing)[:5]}...")
                else:
                    warnings.append(f"Language {language} missing {len(missing)} keys")
            
            if extra:
                warnings.append(f"Language {language} has {len(extra)} extra keys not in default language")
        
        # Check required keys from validation config
        required_keys = TRANSLATION_VALIDATION["required_keys"]
        for language in SUPPORTED_LANGUAGES:
            for required_key in required_keys:
                if not self._key_exists(translations.get(language, {}), required_key):
                    errors.append(f"Required key '{required_key}' missing in {language}")
        
        return {
            'errors': errors,
            'warnings': warnings,
            'missing_keys': missing_keys
        }
    
    def _validate_consistency(self, translations: Dict[str, Dict]) -> Dict[str, Any]:
        """Validate consistency across languages."""
        errors = []
        warnings = []
        
        # Check template variable consistency
        default_translations = translations.get(DEFAULT_LANGUAGE, {})
        
        for language in SUPPORTED_LANGUAGES:
            if language == DEFAULT_LANGUAGE:
                continue
            
            language_translations = translations.get(language, {})
            inconsistencies = self._check_template_variable_consistency(
                default_translations, 
                language_translations, 
                language
            )
            errors.extend(inconsistencies)
        
        # Check content length variance
        length_warnings = self._check_content_length_variance(translations)
        warnings.extend(length_warnings)
        
        return {
            'errors': errors,
            'warnings': warnings
        }
    
    def _validate_content_quality(self, translations: Dict[str, Dict]) -> Dict[str, Any]:
        """Validate content quality and safety."""
        errors = []
        warnings = []
        
        forbidden_html = TRANSLATION_VALIDATION["forbidden_html"]
        
        for language, translation_data in translations.items():
            quality_issues = self._check_content_quality_recursive(
                translation_data, 
                forbidden_html, 
                language
            )
            errors.extend(quality_issues['errors'])
            warnings.extend(quality_issues['warnings'])
        
        return {
            'errors': errors,
            'warnings': warnings
        }
    
    def _extract_all_keys(self, data: Dict[str, Any], prefix: str = "") -> Set[str]:
        """Extract all nested keys from translation dictionary."""
        keys = set()
        
        for key, value in data.items():
            full_key = f"{prefix}.{key}" if prefix else key
            keys.add(full_key)
            
            if isinstance(value, dict):
                keys.update(self._extract_all_keys(value, full_key))
        
        return keys
    
    def _key_exists(self, data: Dict[str, Any], key: str) -> bool:
        """Check if a nested key exists in the translation data."""
        keys = key.split('.')
        current = data
        
        for k in keys:
            if isinstance(current, dict) and k in current:
                current = current[k]
            else:
                return False
        
        return True
    
    def _check_template_variable_consistency(
        self, 
        default_translations: Dict[str, Any], 
        language_translations: Dict[str, Any], 
        language: str
    ) -> List[str]:
        """Check that template variables are consistent between languages."""
        errors = []
        
        def check_recursive(default_data, lang_data, path=""):
            for key, default_value in default_data.items():
                current_path = f"{path}.{key}" if path else key
                
                if isinstance(default_value, dict):
                    if key in lang_data and isinstance(lang_data[key], dict):
                        check_recursive(default_value, lang_data[key], current_path)
                elif isinstance(default_value, str) and key in lang_data:
                    lang_value = lang_data[key]
                    if isinstance(lang_value, str):
                        # Extract template variables
                        default_vars = set(re.findall(TEMPLATE_PATTERNS["variable"], default_value))
                        lang_vars = set(re.findall(TEMPLATE_PATTERNS["variable"], lang_value))
                        
                        if default_vars != lang_vars:
                            missing = default_vars - lang_vars
                            extra = lang_vars - default_vars
                            
                            if missing:
                                errors.append(
                                    f"Template variables missing in {language}:{current_path}: {missing}"
                                )
                            if extra:
                                errors.append(
                                    f"Extra template variables in {language}:{current_path}: {extra}"
                                )
        
        check_recursive(default_translations, language_translations)
        return errors
    
    def _check_content_length_variance(self, translations: Dict[str, Dict]) -> List[str]:
        """Check for excessive content length variance between languages."""
        warnings = []
        max_variance = TRANSLATION_VALIDATION["max_length_variance"]
        
        def check_recursive(data_by_lang, path=""):
            # Get all values for this path across languages
            values_by_lang = {}
            for lang, data in data_by_lang.items():
                if isinstance(data, dict):
                    continue
                elif isinstance(data, str):
                    values_by_lang[lang] = data
            
            if len(values_by_lang) > 1:
                lengths = [len(v) for v in values_by_lang.values()]
                if max(lengths) > 0:
                    variance = (max(lengths) - min(lengths)) / max(lengths)
                    if variance > max_variance:
                        warnings.append(
                            f"High length variance ({variance:.1%}) at {path}: "
                            f"{dict(zip(values_by_lang.keys(), lengths))}"
                        )
            
            # Recurse into nested dictionaries
            nested_keys = set()
            for data in data_by_lang.values():
                if isinstance(data, dict):
                    nested_keys.update(data.keys())
            
            for nested_key in nested_keys:
                nested_data = {}
                for lang, data in data_by_lang.items():
                    if isinstance(data, dict) and nested_key in data:
                        nested_data[lang] = data[nested_key]
                
                if nested_data:
                    new_path = f"{path}.{nested_key}" if path else nested_key
                    check_recursive(nested_data, new_path)
        
        # Group translations by key path
        check_recursive(translations)
        return warnings
    
    def _check_content_quality_recursive(
        self, 
        data: Dict[str, Any], 
        forbidden_html: List[str], 
        language: str, 
        path: str = ""
    ) -> Dict[str, List[str]]:
        """Recursively check content quality."""
        errors = []
        warnings = []
        
        for key, value in data.items():
            current_path = f"{path}.{key}" if path else key
            
            if isinstance(value, dict):
                nested_result = self._check_content_quality_recursive(
                    value, forbidden_html, language, current_path
                )
                errors.extend(nested_result['errors'])
                warnings.extend(nested_result['warnings'])
            elif isinstance(value, str):
                # Check for forbidden HTML
                for forbidden in forbidden_html:
                    if forbidden.lower() in value.lower():
                        errors.append(
                            f"Forbidden HTML '{forbidden}' found in {language}:{current_path}"
                        )
                
                # Check for potential issues
                if len(value.strip()) == 0:
                    warnings.append(f"Empty translation in {language}:{current_path}")
                
                if value.count('{') != value.count('}'):
                    warnings.append(f"Unmatched braces in {language}:{current_path}")
                
                # Check for untranslated content (English words in French)
                if language == 'fr' and self._contains_untranslated_english(value):
                    warnings.append(f"Possible untranslated English in {language}:{current_path}")
        
        return {
            'errors': errors,
            'warnings': warnings
        }
    
    def _contains_untranslated_english(self, text: str) -> bool:
        """Detect possible untranslated English content in French text."""
        # Simple heuristic: check for common English words that should be translated
        english_indicators = [
            'login', 'password', 'email', 'user', 'admin', 'dashboard',
            'settings', 'profile', 'save', 'cancel', 'delete', 'edit',
            'search', 'filter', 'loading', 'error', 'success'
        ]
        
        text_lower = text.lower()
        for word in english_indicators:
            if f' {word} ' in f' {text_lower} ' or text_lower.startswith(word) or text_lower.endswith(word):
                return True
        
        return False
    
    def _generate_statistics(
        self, 
        translations: Dict[str, Dict], 
        missing_keys: Dict[str, List[str]]
    ) -> Dict[str, Any]:
        """Generate comprehensive statistics about translations."""
        stats = {
            'total_languages': len(SUPPORTED_LANGUAGES),
            'languages_with_files': len(translations),
            'total_keys_by_language': {},
            'missing_keys_count': {},
            'completion_percentage': {},
            'content_categories': {}
        }
        
        for language in SUPPORTED_LANGUAGES:
            translation_data = translations.get(language, {})
            total_keys = len(self._extract_all_keys(translation_data))
            missing_count = len(missing_keys.get(language, []))
            
            stats['total_keys_by_language'][language] = total_keys
            stats['missing_keys_count'][language] = missing_count
            
            if total_keys > 0:
                completion = ((total_keys - missing_count) / total_keys) * 100
                stats['completion_percentage'][language] = round(completion, 2)
            else:
                stats['completion_percentage'][language] = 0.0
            
            # Category breakdown
            category_stats = {}
            for category in CONTENT_CATEGORIES:
                category_data = translation_data.get(category, {})
                category_keys = len(self._extract_all_keys(category_data))
                category_stats[category] = category_keys
            
            stats['content_categories'][language] = category_stats
        
        return stats
    
    def validate_single_key(self, key: str, languages: List[str] = None) -> Dict[str, Any]:
        """Validate a single translation key across languages."""
        if languages is None:
            languages = SUPPORTED_LANGUAGES
        
        result = {
            'key': key,
            'exists_in': [],
            'missing_from': [],
            'values': {},
            'template_variables': {},
            'length_variance': 0.0
        }
        
        values = {}
        for language in languages:
            value = self.translation_service.get_translation(key, language)
            if value != key:  # Key was found and translated
                result['exists_in'].append(language)
                values[language] = value
                result['values'][language] = value
                
                # Extract template variables
                template_vars = set(re.findall(TEMPLATE_PATTERNS["variable"], value))
                result['template_variables'][language] = list(template_vars)
            else:
                result['missing_from'].append(language)
        
        # Calculate length variance
        if len(values) > 1:
            lengths = [len(v) for v in values.values()]
            if max(lengths) > 0:
                result['length_variance'] = (max(lengths) - min(lengths)) / max(lengths)
        
        return result
    
    def suggest_missing_translations(self, target_language: str) -> List[Dict[str, str]]:
        """Suggest keys that need translation in target language."""
        missing_keys = self.translation_service.get_missing_translations(target_language)
        suggestions = []
        
        for key in missing_keys[:20]:  # Limit to top 20 for practical use
            default_value = self.translation_service.get_translation(key, DEFAULT_LANGUAGE)
            if default_value != key:  # Key exists in default language
                suggestions.append({
                    'key': key,
                    'default_value': default_value,
                    'category': key.split('.')[0] if '.' in key else 'uncategorized',
                    'priority': 'high' if key in TRANSLATION_VALIDATION["required_keys"] else 'medium'
                })
        
        return suggestions


# Global validator instance
_validator: Optional[TranslationValidator] = None


def get_translation_validator() -> TranslationValidator:
    """Get global translation validator instance."""
    global _validator
    if _validator is None:
        _validator = TranslationValidator()
    return _validator


def validate_translations() -> ValidationResult:
    """Convenience function for validating all translations."""
    return get_translation_validator().validate_all_translations()


def validate_translation_key(key: str, languages: List[str] = None) -> Dict[str, Any]:
    """Convenience function for validating a single key."""
    return get_translation_validator().validate_single_key(key, languages)