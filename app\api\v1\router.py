from fastapi import APIRouter

from app.api.v1 import auth_updated as auth, clients, tutors, tutor_invitations, tutor_application_review, dependants, search, onboarding, quick_actions, appointment_confirmations, ai_scheduling, websocket_calendar, appointments, geocoding, tutor_matching, service_areas, distance_calculation, subscriptions, pricing, packages, sms_conversations, notification_preferences_updated as notification_preferences, automated_reminders, language, formatting_updated as formatting, customer_service, websocket_messaging, cron, webhooks, two_factor_auth_updated as two_factor_auth, messaging, consent, analytics, services
from app.api.v1.billing import router as billing_router
from app.api.v1.admin.router import router as admin_router
from app.api.v1.public import tutor_applications as public_tutor_applications, client_requests as public_client_requests

api_router = APIRouter()

# Include sub-routers
api_router.include_router(auth.router, tags=["authentication"])
api_router.include_router(two_factor_auth.router, tags=["two-factor-auth"])
api_router.include_router(clients.router, tags=["clients"])
api_router.include_router(tutors.router, tags=["tutors"])
api_router.include_router(tutor_invitations.router, tags=["tutor-invitations"])
api_router.include_router(services.router, tags=["services"])
api_router.include_router(tutor_application_review.router, tags=["tutor-application-review"])
api_router.include_router(public_tutor_applications.router, tags=["public-tutor-applications"])
api_router.include_router(public_client_requests.router, tags=["public-client-requests"])
api_router.include_router(dependants.router, tags=["dependants"])
api_router.include_router(search.router, tags=["search"])
api_router.include_router(onboarding.router, tags=["onboarding"])
api_router.include_router(quick_actions.router, tags=["quick-actions"])
api_router.include_router(appointment_confirmations.router, tags=["appointment-confirmations"])
api_router.include_router(ai_scheduling.router, tags=["ai-scheduling"])
api_router.include_router(websocket_calendar.router, tags=["websocket"])
api_router.include_router(appointments.router, tags=["appointments"])
api_router.include_router(geocoding.router, tags=["geocoding"])
api_router.include_router(tutor_matching.router, prefix="/tutor-matching", tags=["tutor-matching"])
api_router.include_router(service_areas.router, prefix="/service-areas", tags=["service-areas"])
api_router.include_router(distance_calculation.router, prefix="/distance", tags=["distance-calculation"])
api_router.include_router(billing_router)
api_router.include_router(subscriptions.router, tags=["subscriptions"])
api_router.include_router(pricing.router, tags=["pricing"])
api_router.include_router(packages.router, tags=["packages"])
api_router.include_router(sms_conversations.router, prefix="/sms-conversations", tags=["sms-conversations"])
api_router.include_router(notification_preferences.router, prefix="/notification-preferences", tags=["notification-preferences"])
api_router.include_router(automated_reminders.router, prefix="/automated-reminders", tags=["automated-reminders"])
api_router.include_router(language.router, tags=["language"])
api_router.include_router(formatting.router, prefix="/formatting", tags=["formatting"])
api_router.include_router(customer_service.router, prefix="/customer-service", tags=["customer-service"])
api_router.include_router(messaging.router, tags=["messaging"])
api_router.include_router(websocket_messaging.router, tags=["websocket-messaging"])
api_router.include_router(cron.router, tags=["cron"])
api_router.include_router(webhooks.router, tags=["webhooks"])
api_router.include_router(admin_router, prefix="/admin", tags=["admin"])
api_router.include_router(consent.router, prefix="/consent", tags=["consent"])
api_router.include_router(analytics.router, tags=["analytics"])

# Health check endpoint
@api_router.get("/health")
async def api_health():
    """API health check endpoint."""
    return {
        "status": "healthy",
        "version": "1.0.0",
        "service": "tutoraide-api-v1"
    }