import api from './api';

// 2FA Types and Interfaces
export enum TwoFactorMethod {
  TOTP = 'totp',
  SMS = 'sms',
  EMAIL = 'email'
}

export enum TwoFactorStatus {
  NOT_CONFIGURED = 'not_configured',
  PENDING_VERIFICATION = 'pending_verification',
  ACTIVE = 'active',
  DISABLED = 'disabled'
}

export interface TwoFactorStatusResponse {
  enabled: boolean;
  method?: TwoFactorMethod;
  status: TwoFactorStatus;
  backup_codes_remaining?: number;
  last_used?: string;
  phone_number_masked?: string;
  email_masked?: string;
}

export interface Enable2FARequest {
  method: TwoFactorMethod;
  phone_number?: string; // Required for SMS method
}

export interface Enable2FAResponse {
  method: TwoFactorMethod;
  status: TwoFactorStatus;
  qr_code?: string; // Base64 encoded QR code for TOTP
  secret?: string; // TOTP secret for manual entry
  backup_codes?: string[]; // One-time backup codes
  verification_required: boolean;
  message: string;
}

export interface Verify2FARequest {
  method: TwoFactorMethod;
  code: string;
}

export interface Verify2FAResponse {
  success: boolean;
  message: string;
  backup_codes?: string[]; // Provided after successful verification
}

export interface Disable2FARequest {
  password: string;
  code?: string; // Current 2FA code if 2FA is active
}

export interface GenerateBackupCodesRequest {
  password: string;
  current_code: string; // Current 2FA code
}

export interface BackupCodesResponse {
  backup_codes: string[];
  generated_at: string;
  message: string;
}

export interface ValidateCodeRequest {
  code: string;
  method?: TwoFactorMethod;
}

export interface ValidateCodeResponse {
  valid: boolean;
  method: TwoFactorMethod;
  is_backup_code: boolean;
  message?: string;
}

export interface Send2FACodeRequest {
  method: TwoFactorMethod;
}

export interface Send2FACodeResponse {
  success: boolean;
  message: string;
  expires_in?: number; // Seconds until code expires
}

class TwoFactorService {
  /**
   * Get current 2FA status
   */
  async get2FAStatus(): Promise<TwoFactorStatusResponse> {
    try {
      const response = await api.get<TwoFactorStatusResponse>('/two-factor-auth/status');
      return response.data;
    } catch (error: any) {
      console.error('Error fetching 2FA status:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch 2FA status');
    }
  }

  /**
   * Enable 2FA for user
   */
  async enable2FA(request: Enable2FARequest): Promise<Enable2FAResponse> {
    try {
      const response = await api.post<Enable2FAResponse>('/two-factor-auth/enable', request);
      return response.data;
    } catch (error: any) {
      console.error('Error enabling 2FA:', error);
      throw new Error(error.response?.data?.detail || 'Failed to enable 2FA');
    }
  }

  /**
   * Verify 2FA setup
   */
  async verify2FA(request: Verify2FARequest): Promise<Verify2FAResponse> {
    try {
      const response = await api.post<Verify2FAResponse>('/two-factor-auth/verify', request);
      return response.data;
    } catch (error: any) {
      console.error('Error verifying 2FA:', error);
      throw new Error(error.response?.data?.detail || 'Failed to verify 2FA code');
    }
  }

  /**
   * Disable 2FA
   */
  async disable2FA(request: Disable2FARequest): Promise<{ message: string }> {
    try {
      const response = await api.post<{ message: string }>('/two-factor-auth/disable', request);
      return response.data;
    } catch (error: any) {
      console.error('Error disabling 2FA:', error);
      throw new Error(error.response?.data?.detail || 'Failed to disable 2FA');
    }
  }

  /**
   * Generate new backup codes
   */
  async generateBackupCodes(request: GenerateBackupCodesRequest): Promise<BackupCodesResponse> {
    try {
      const response = await api.post<BackupCodesResponse>('/two-factor-auth/backup-codes', request);
      return response.data;
    } catch (error: any) {
      console.error('Error generating backup codes:', error);
      throw new Error(error.response?.data?.detail || 'Failed to generate backup codes');
    }
  }

  /**
   * Validate a 2FA code (used during login)
   */
  async validateCode(request: ValidateCodeRequest): Promise<ValidateCodeResponse> {
    try {
      const response = await api.post<ValidateCodeResponse>('/two-factor-auth/validate', request);
      return response.data;
    } catch (error: any) {
      console.error('Error validating 2FA code:', error);
      throw new Error(error.response?.data?.detail || 'Invalid 2FA code');
    }
  }

  /**
   * Send 2FA code via SMS or Email
   */
  async sendCode(method: TwoFactorMethod): Promise<Send2FACodeResponse> {
    try {
      const response = await api.post<Send2FACodeResponse>('/two-factor-auth/send-code', { method });
      return response.data;
    } catch (error: any) {
      console.error('Error sending 2FA code:', error);
      throw new Error(error.response?.data?.detail || 'Failed to send 2FA code');
    }
  }

  /**
   * Download backup codes as text file
   */
  downloadBackupCodes(codes: string[], filename: string = 'tutoraide-backup-codes.txt') {
    const content = [
      'TutorAide 2FA Backup Codes',
      '========================',
      '',
      'Keep these codes in a safe place. Each code can only be used once.',
      '',
      ...codes.map((code, index) => `${index + 1}. ${code}`),
      '',
      `Generated: ${new Date().toLocaleString()}`
    ].join('\n');

    const blob = new Blob([content], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }

  /**
   * Format phone number for display (masked)
   */
  formatMaskedPhone(maskedPhone?: string): string {
    if (!maskedPhone) return '';
    // Assumes format like "***-***-1234"
    return maskedPhone;
  }

  /**
   * Format email for display (masked)
   */
  formatMaskedEmail(maskedEmail?: string): string {
    if (!maskedEmail) return '';
    // Assumes format like "j***@example.com"
    return maskedEmail;
  }

  /**
   * Generate QR code URL from base64
   */
  getQRCodeDataURL(base64QR: string): string {
    if (!base64QR) return '';
    // If it's already a data URL, return as is
    if (base64QR.startsWith('data:')) {
      return base64QR;
    }
    // Otherwise, prepend the data URL prefix
    return `data:image/png;base64,${base64QR}`;
  }

  /**
   * Check if 2FA is required for login
   */
  is2FARequired(error: any): boolean {
    return error?.response?.status === 403 && 
           error?.response?.data?.detail?.includes('2FA') ||
           error?.response?.data?.requires_2fa === true;
  }

  /**
   * Format TOTP secret for manual entry (add spaces)
   */
  formatTOTPSecret(secret: string): string {
    if (!secret) return '';
    // Format as groups of 4 characters
    return secret.match(/.{1,4}/g)?.join(' ') || secret;
  }
}

export const twoFactorService = new TwoFactorService();