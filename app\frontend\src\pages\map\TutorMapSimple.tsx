import React, { useState, useEffect, useCallback } from 'react';
import { SearchBar } from '../../components/forms/SearchBar';
import { Card } from '../../components/common/Card';
import Button from '../../components/common/Button';
import { Badge } from '../../components/common/Badge';
import { useAuth } from '../../contexts/AuthContext';
import { useApi } from '../../hooks/useApi';
import { TutorLocation } from '../../types/tutor';
import { GeocodeResponse } from '../../types/geocoding';
import { FilterPanel } from '../../components/map/FilterPanel';
import { TutorCard } from '../../components/map/TutorCard';
import { TutorListView } from '../../components/map/TutorListView';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import { MapPin } from 'lucide-react';

interface TutorMapProps {
  className?: string;
}

interface MapFilters {
  subjectAreas: string[];
  serviceTypes: string[];
  maxDistance: number;
  minRating: number;
  maxHourlyRate: number;
  availability: 'all' | 'available' | 'limited';
}

export const TutorMapSimple: React.FC<TutorMapProps> = ({ className = '' }) => {
  const { user } = useAuth();
  const { apiCall } = useApi();
  
  // State management
  const [tutors, setTutors] = useState<TutorLocation[]>([]);
  const [filteredTutors, setFilteredTutors] = useState<TutorLocation[]>([]);
  const [selectedTutor, setSelectedTutor] = useState<TutorLocation | null>(null);
  const [searchPostalCode, setSearchPostalCode] = useState('');
  const [filters, setFilters] = useState<MapFilters>({
    subjectAreas: [],
    serviceTypes: ['online', 'in_person'],
    maxDistance: 15,
    minRating: 0,
    maxHourlyRate: 100,
    availability: 'all'
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState<'map' | 'split' | 'list'>('split');

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  // Apply filters when they change
  useEffect(() => {
    applyFilters();
  }, [tutors, filters]);


  const loadInitialData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Use user's postal code if available, otherwise default to Montreal
      const defaultPostalCode = user?.postal_code || 'H2X 1Y9';
      setSearchPostalCode(defaultPostalCode);
      
      // Load tutors near this location
      await loadTutorsNearLocation(defaultPostalCode);
      
    } catch (err) {
      console.error('Error loading initial data:', err);
      setError('Failed to load tutor data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const loadTutorsNearLocation = async (postalCode: string) => {
    try {
      // Use the tutor-matching API to find nearest tutors
      const response = await apiCall<any>('POST', '/tutor-matching/nearest', {
        params: {
          client_postal_code: postalCode,
          subject_areas: filters.subjectAreas.length > 0 ? filters.subjectAreas : ['mathematics', 'english', 'french', 'science'],
          service_types: filters.serviceTypes,
          max_distance_km: filters.maxDistance,
          max_results: 20,
          min_rating: filters.minRating > 0 ? filters.minRating : undefined,
          max_hourly_rate: filters.maxHourlyRate < 100 ? filters.maxHourlyRate : undefined,
          availability_filter: filters.availability !== 'all' ? filters.availability : undefined
        }
      });

      // Transform the matches to TutorLocation format
      const tutorsWithLocations: TutorLocation[] = response.matches.map((match: any) => ({
        tutor_id: match.tutor_id,
        first_name: match.first_name,
        last_name: match.last_name,
        lat: match.latitude,
        lng: match.longitude,
        distance_km: match.distance_km,
        profile_picture: match.profile_picture,
        specialties: match.specialties,
        average_rating: match.average_rating,
        total_sessions: match.total_sessions,
        hourly_rate: match.hourly_rate,
        availability_status: match.availability_status,
        bio: match.bio,
        experience_years: match.experience_years,
        education: match.education,
        languages: match.languages,
        service_types: match.service_types,
        next_available: 'Available' // Default text since API doesn't provide this
      }));

      setTutors(tutorsWithLocations);
      
    } catch (err) {
      console.error('Error loading tutors:', err);
      throw err;
    }
  };

  const applyFilters = useCallback(() => {
    let filtered = [...tutors];

    // Subject area filter
    if (filters.subjectAreas.length > 0) {
      filtered = filtered.filter(tutor =>
        tutor.specialties?.some((specialty: string) =>
          filters.subjectAreas.includes(specialty)
        )
      );
    }

    // Distance filter
    filtered = filtered.filter(tutor =>
      !tutor.distance_km || tutor.distance_km <= filters.maxDistance
    );

    // Rating filter
    if (filters.minRating > 0) {
      filtered = filtered.filter(tutor =>
        (tutor.average_rating || 0) >= filters.minRating
      );
    }

    // Hourly rate filter
    filtered = filtered.filter(tutor =>
      (tutor.hourly_rate || 0) <= filters.maxHourlyRate
    );

    // Availability filter
    if (filters.availability !== 'all') {
      filtered = filtered.filter(tutor => {
        if (filters.availability === 'available') {
          return tutor.availability_status === 'available';
        } else if (filters.availability === 'limited') {
          return tutor.availability_status === 'limited';
        }
        return true;
      });
    }

    setFilteredTutors(filtered);
  }, [tutors, filters]);

  const handleSearch = async (postalCode: string) => {
    if (!postalCode.trim()) return;

    try {
      setLoading(true);
      setSearchPostalCode(postalCode);

      // Load tutors near this new location
      await loadTutorsNearLocation(postalCode);

    } catch (err) {
      console.error('Error searching location:', err);
      setError('Could not find location. Please check the postal code.');
    } finally {
      setLoading(false);
    }
  };

  const handleTutorSelect = (tutor: TutorLocation) => {
    setSelectedTutor(tutor);
  };

  const handleFilterChange = (newFilters: Partial<MapFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const handleBookSession = async (tutorId: number) => {
    // Navigate to booking flow
    window.location.href = `/appointments/book?tutor_id=${tutorId}`;
  };

  const handleViewProfile = (tutorId: number) => {
    // Navigate to tutor profile
    window.location.href = `/tutors/${tutorId}`;
  };

  if (loading && tutors.length === 0) {
    return (
      <div className="flex justify-center items-center h-96">
        <LoadingSpinner size="large" message="Loading tutor map..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
        <div className="text-red-600 mb-2">Error Loading Map</div>
        <div className="text-red-500 text-sm">{error}</div>
        <button
          onClick={loadInitialData}
          className="mt-4 px-6 py-3 bg-accent-red text-white rounded-full hover:bg-red-700 transition-colors shadow-soft"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className={`relative bg-white rounded-xl shadow-soft overflow-hidden ${className}`}>
      {/* Header with search and controls */}
      <div className="bg-gray-50 p-4 border-b border-gray-200">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex-1 max-w-md">
            <SearchBar
              placeholder="Search by postal code (e.g., H2X 1Y9)"
              onSearch={handleSearch}
              loading={loading}
            />
          </div>
          
          <div className="flex items-center gap-3">
            {/* View mode toggle */}
            <div className="flex border border-gray-300 rounded-full overflow-hidden">
              <button
                onClick={() => setViewMode('map')}
                className={`px-4 py-2 text-sm transition-colors ${
                  viewMode === 'map' 
                    ? 'bg-accent-red text-white' 
                    : 'bg-white text-gray-600 hover:bg-gray-50'
                }`}
              >
                Map
              </button>
              <button
                onClick={() => setViewMode('split')}
                className={`px-4 py-2 text-sm border-l border-gray-300 transition-colors ${
                  viewMode === 'split' 
                    ? 'bg-accent-red text-white' 
                    : 'bg-white text-gray-600 hover:bg-gray-50'
                }`}
              >
                Split
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`px-4 py-2 text-sm border-l border-gray-300 transition-colors ${
                  viewMode === 'list' 
                    ? 'bg-accent-red text-white' 
                    : 'bg-white text-gray-600 hover:bg-gray-50'
                }`}
              >
                List
              </button>
            </div>

            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`px-5 py-2 text-sm font-medium rounded-full border transition-colors shadow-subtle ${
                showFilters
                  ? 'bg-red-50 text-accent-red border-accent-red'
                  : 'bg-white text-gray-600 border-gray-300 hover:bg-gray-50'
              }`}
            >
              Filters
              {(filters.subjectAreas.length > 0 || filters.minRating > 0) && (
                <span className="ml-1 w-2 h-2 bg-accent-red rounded-full inline-block"></span>
              )}
            </button>
            
            <div className="text-sm text-gray-500">
              {filteredTutors.length} of {tutors.length} tutors
            </div>
          </div>
        </div>
        
        {showFilters && (
          <div className="mt-4">
            <FilterPanel
              filters={filters}
              onFilterChange={handleFilterChange}
              tutorCount={filteredTutors.length}
            />
          </div>
        )}
      </div>

      {/* Main content area */}
      <div className={`flex ${viewMode === 'split' ? 'divide-x divide-gray-200' : ''}`}>
        {/* List view */}
        {(viewMode === 'list' || viewMode === 'split') && (
          <div className={`${viewMode === 'split' ? 'w-1/3' : 'w-full'} ${viewMode === 'list' ? 'h-[600px]' : ''}`}>
            <TutorListView
              tutors={filteredTutors}
              selectedTutor={selectedTutor}
              onTutorSelect={handleTutorSelect}
              onBookSession={handleBookSession}
              onViewProfile={handleViewProfile}
              loading={loading}
              className="h-full border-0 rounded-none"
            />
          </div>
        )}

        {/* Map placeholder or grid view */}
        {(viewMode === 'map' || viewMode === 'split') && (
          <div className={`relative ${viewMode === 'split' ? 'flex-1' : 'w-full'} h-96 md:h-[500px] lg:h-[600px] bg-gray-100`}>
            {/* Map placeholder with grid of tutor cards */}
            <div className="p-6 h-full overflow-y-auto">
              <div className="text-center mb-6">
                <MapPin className="w-12 h-12 text-accent-red mx-auto mb-2" />
                <h3 className="text-lg font-semibold text-gray-900">Tutors Near {searchPostalCode}</h3>
                <p className="text-sm text-gray-500 mt-1">Showing tutors within {filters.maxDistance}km radius</p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredTutors.map((tutor) => (
                  <div
                    key={tutor.tutor_id}
                    className={`cursor-pointer transition-transform hover:scale-105 ${
                      selectedTutor?.tutor_id === tutor.tutor_id ? 'ring-2 ring-accent-red rounded-lg' : ''
                    }`}
                    onClick={() => handleTutorSelect(tutor)}
                  >
                    <TutorCard
                      tutor={tutor}
                      onBookSession={() => handleBookSession(tutor.tutor_id)}
                      onViewProfile={() => handleViewProfile(tutor.tutor_id)}
                      showDistance={true}
                    />
                  </div>
                ))}
              </div>
              
              {filteredTutors.length === 0 && (
                <div className="text-center py-12">
                  <p className="text-gray-500">No tutors found in this area. Try adjusting your filters or search radius.</p>
                </div>
              )}
            </div>
            
            {loading && (
              <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
                <LoadingSpinner message="Loading tutors..." />
              </div>
            )}
          </div>
        )}
      </div>

      {/* Selected tutor sidebar on larger screens - only show in map-only mode */}
      {selectedTutor && viewMode === 'map' && (
        <div className="hidden lg:block absolute top-20 right-4 w-80 bg-white rounded-2xl shadow-elevated border border-gray-200 max-h-[calc(100%-5rem)] overflow-y-auto">
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-gray-900">Tutor Details</h3>
              <button
                onClick={() => setSelectedTutor(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
          <div className="p-4">
            <TutorCard
              tutor={selectedTutor}
              onBookSession={() => handleBookSession(selectedTutor.tutor_id)}
              onViewProfile={() => handleViewProfile(selectedTutor.tutor_id)}
              showDistance={true}
              expanded={true}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default TutorMapSimple;