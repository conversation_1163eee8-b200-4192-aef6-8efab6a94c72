import React, { useState, useEffect } from 'react';
import { Globe, Check, ChevronDown } from 'lucide-react';
import { useTranslation } from '../../hooks/useTranslation';
import { useLanguage } from '../../hooks/useLanguage';

interface LanguageOption {
  code: string;
  name: string;
  nativeName: string;
  flag: string;
}

const LANGUAGE_OPTIONS: LanguageOption[] = [
  {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇨🇦'
  },
  {
    code: 'fr',
    name: 'French (Quebec)',
    nativeName: 'Français (Québec)',
    flag: '🇨🇦'
  }
];

interface LanguageSwitcherProps {
  variant?: 'compact' | 'full' | 'dropdown';
  showFlags?: boolean;
  className?: string;
}

export const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({
  variant = 'dropdown',
  showFlags = true,
  className = ''
}) => {
  const { currentLanguage, switchLanguage, isLoading } = useLanguage();
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState<LanguageOption | null>(null);

  useEffect(() => {
    const option = LANGUAGE_OPTIONS.find(opt => opt.code === currentLanguage);
    setSelectedOption(option || LANGUAGE_OPTIONS[0]);
  }, [currentLanguage]);

  const handleLanguageSwitch = async (languageCode: string) => {
    if (languageCode === currentLanguage) return;
    
    try {
      await switchLanguage(languageCode, true);
      setIsOpen(false);
      
      // Show success message
      const newOption = LANGUAGE_OPTIONS.find(opt => opt.code === languageCode);
      if (newOption) {
        // You could show a toast notification here
        console.log(`Language switched to ${newOption.nativeName}`);
      }
    } catch (error) {
      console.error('Failed to switch language:', error);
      // You could show an error toast here
    }
  };

  if (variant === 'compact') {
    return (
      <div className={`flex gap-1 ${className}`}>
        {LANGUAGE_OPTIONS.map((option) => (
          <button
            key={option.code}
            onClick={() => handleLanguageSwitch(option.code)}
            disabled={isLoading}
            className={`
              px-2 py-1 text-xs font-medium rounded transition-colors
              ${option.code === currentLanguage
                ? 'bg-red-100 text-red-800 border border-red-200'
                : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
              }
              ${isLoading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
            `}
            title={`${t('common.switch_to')} ${option.nativeName}`}
          >
            {showFlags && <span className="mr-1">{option.flag}</span>}
            {option.code.toUpperCase()}
          </button>
        ))}
      </div>
    );
  }

  if (variant === 'full') {
    return (
      <div className={`flex flex-col gap-2 ${className}`}>
        <label className="text-sm font-medium text-gray-700">
          {t('settings.language')}
        </label>
        <div className="grid grid-cols-1 gap-2">
          {LANGUAGE_OPTIONS.map((option) => (
            <button
              key={option.code}
              onClick={() => handleLanguageSwitch(option.code)}
              disabled={isLoading}
              className={`
                flex items-center gap-3 p-3 rounded-lg border transition-all
                ${option.code === currentLanguage
                  ? 'border-red-500 bg-red-50 text-red-900'
                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                }
                ${isLoading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
              `}
            >
              {showFlags && <span className="text-xl">{option.flag}</span>}
              <div className="flex-1 text-left">
                <div className="font-medium">{option.nativeName}</div>
                <div className="text-sm text-gray-600">{option.name}</div>
              </div>
              {option.code === currentLanguage && (
                <Check className="w-5 h-5 text-red-600" />
              )}
            </button>
          ))}
        </div>
      </div>
    );
  }

  // Default dropdown variant
  return (
    <div className={`relative ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        disabled={isLoading}
        className={`
          flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-700
          bg-white border border-gray-300 rounded-md shadow-sm
          hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500
          ${isLoading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        `}
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <Globe className="w-4 h-4" />
        {selectedOption && (
          <>
            {showFlags && <span>{selectedOption.flag}</span>}
            <span>{selectedOption.nativeName}</span>
          </>
        )}
        <ChevronDown className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className="absolute right-0 z-50 mt-1 w-56 bg-white border border-gray-200 rounded-md shadow-lg">
          <div className="py-1">
            {LANGUAGE_OPTIONS.map((option) => (
              <button
                key={option.code}
                onClick={() => handleLanguageSwitch(option.code)}
                disabled={isLoading}
                className={`
                  w-full flex items-center gap-3 px-4 py-2 text-sm text-left
                  transition-colors
                  ${option.code === currentLanguage
                    ? 'bg-red-50 text-red-900'
                    : 'text-gray-700 hover:bg-gray-100'
                  }
                  ${isLoading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                `}
              >
                {showFlags && <span>{option.flag}</span>}
                <div className="flex-1">
                  <div className="font-medium">{option.nativeName}</div>
                  <div className="text-xs text-gray-500">{option.name}</div>
                </div>
                {option.code === currentLanguage && (
                  <Check className="w-4 h-4 text-red-600" />
                )}
              </button>
            ))}
          </div>
        </div>
      )}

      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
          aria-hidden="true"
        />
      )}
    </div>
  );
};
