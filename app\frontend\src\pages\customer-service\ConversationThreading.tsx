/**
 * Conversation Threading and History
 * 
 * Advanced conversation management with threading, history tracking,
 * archival, and comprehensive conversation analytics.
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Typography,
  TextField,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Avatar,
  Chip,
  Divider,
  Paper,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Tab,
  Tabs,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Collapse,
  Alert,
  LinearProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  CircularProgress,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Thread as ThreadIcon,
  History as HistoryIcon,
  Archive as ArchiveIcon,
  Unarchive as UnarchiveIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon,
  MoreVert as MoreVertIcon,
  Message as MessageIcon,
  Person as PersonIcon,
  Schedule as ScheduleIcon,
  Star as StarIcon,
  Link as LinkIcon,
  Analytics as AnalyticsIcon,
  TrendingUp as TrendingUpIcon,
  Assignment as AssignmentIcon,
  Group as GroupIcon,
  Update as UpdateIcon,
  Notes as NotesIcon
} from '@mui/icons-material';
import { formatDistanceToNow, format, isAfter, isBefore } from 'date-fns';
import { fr } from 'date-fns/locale';

import api from '../../services/api';
import { useTranslation } from '../../hooks/useTranslation';
import { useFormatting } from '../../hooks/useFormatting';
import { useNotifications } from '../../hooks/useNotifications';

// Types
interface ConversationThread {
  thread_id: string;
  subject?: string;
  participants: string[];
  message_count: number;
  first_message_at: string;
  last_message_at: string;
  status: 'active' | 'archived' | 'resolved';
  conversations: Conversation[];
  tags: string[];
  priority: string;
  category?: string;
}

interface Conversation {
  conversation_id: number;
  thread_id: string;
  participant_name?: string;
  participant_email?: string;
  participant_phone?: string;
  assigned_agent_id?: number;
  agent_name?: string;
  status: string;
  priority: string;
  category?: string;
  created_at: string;
  resolved_at?: string;
  message_count: number;
  last_activity_at: string;
  satisfaction_rating?: number;
  resolution_time_hours?: number;
  response_time_minutes?: number;
}

interface ConversationHistory {
  conversation_id: number;
  status_changes: StatusChange[];
  assignments: Assignment[];
  messages_summary: MessagesSummary;
  satisfaction_feedback?: SatisfactionFeedback;
  related_conversations: number[];
}

interface StatusChange {
  change_id: number;
  from_status: string;
  to_status: string;
  changed_by: string;
  changed_at: string;
  reason?: string;
}

interface Assignment {
  assignment_id: number;
  agent_name: string;
  assigned_by: string;
  assigned_at: string;
  unassigned_at?: string;
  assignment_reason?: string;
}

interface MessagesSummary {
  total_messages: number;
  inbound_messages: number;
  outbound_messages: number;
  average_response_time: number;
  first_response_time?: number;
}

interface SatisfactionFeedback {
  rating: number;
  feedback?: string;
  submitted_at: string;
}

// Main Component
const ConversationThreading: React.FC = () => {
  const { t } = useTranslation();
  const { formatDate, formatTime, formatDuration } = useFormatting();
  const { showNotification } = useNotifications();
  const queryClient = useQueryClient();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // State
  const [currentTab, setCurrentTab] = useState(0);
  const [selectedThread, setSelectedThread] = useState<ConversationThread | null>(null);
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [dateRange, setDateRange] = useState({ start: '', end: '' });
  const [showArchived, setShowArchived] = useState(false);
  const [historyDialog, setHistoryDialog] = useState(false);
  const [exportDialog, setExportDialog] = useState(false);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [expandedThreads, setExpandedThreads] = useState<Set<string>>(new Set());

  // Queries
  const { data: threads = [], isLoading: threadsLoading, refetch: refetchThreads } = useQuery({
    queryKey: ['conversation-threads', statusFilter, showArchived, searchTerm, dateRange],
    queryFn: async () => {
      const params = new URLSearchParams();
      if (statusFilter !== 'all') params.append('status', statusFilter);
      if (showArchived) params.append('include_archived', 'true');
      if (searchTerm) params.append('search', searchTerm);
      if (dateRange.start) params.append('start_date', dateRange.start);
      if (dateRange.end) params.append('end_date', dateRange.end);
      
      return api.get(`/customer-service/threads?${params}`);
    },
    refetchInterval: 30000
  });

  const { data: threadHistory, isLoading: historyLoading } = useQuery({
    queryKey: ['thread-history', selectedThread?.thread_id],
    queryFn: () => api.get(`/customer-service/threads/${selectedThread?.thread_id}/history`),
    enabled: !!selectedThread?.thread_id
  });

  const { data: conversationHistory, isLoading: conversationHistoryLoading } = useQuery({
    queryKey: ['conversation-history', selectedConversation?.conversation_id],
    queryFn: () => api.get(`/customer-service/conversations/${selectedConversation?.conversation_id}/history`),
    enabled: !!selectedConversation?.conversation_id && historyDialog
  });

  const { data: threadAnalytics } = useQuery({
    queryKey: ['thread-analytics', selectedThread?.thread_id],
    queryFn: () => api.get(`/customer-service/threads/${selectedThread?.thread_id}/analytics`),
    enabled: !!selectedThread?.thread_id
  });

  // Mutations
  const archiveThreadMutation = useMutation({
    mutationFn: (thread_id: string) => api.post(`/customer-service/threads/${thread_id}/archive`),
    onSuccess: () => {
      queryClient.invalidateQueries(['conversation-threads']);
      showNotification('Thread archived successfully', 'success');
    }
  });

  const unarchiveThreadMutation = useMutation({
    mutationFn: (thread_id: string) => api.post(`/customer-service/threads/${thread_id}/unarchive`),
    onSuccess: () => {
      queryClient.invalidateQueries(['conversation-threads']);
      showNotification('Thread unarchived successfully', 'success');
    }
  });

  const mergeConversationsMutation = useMutation({
    mutationFn: (data: { primary_conversation_id: number; conversation_ids: number[] }) =>
      api.post('/customer-service/conversations/merge', data),
    onSuccess: () => {
      queryClient.invalidateQueries(['conversation-threads']);
      showNotification('Conversations merged successfully', 'success');
    }
  });

  const exportThreadMutation = useMutation({
    mutationFn: (data: { thread_id: string; format: 'pdf' | 'csv' | 'json'; include_messages: boolean }) =>
      api.post(`/customer-service/threads/${data.thread_id}/export`, data),
    onSuccess: (data) => {
      // Trigger download
      const url = window.URL.createObjectURL(new Blob([data]));
      const a = document.createElement('a');
      a.href = url;
      a.download = `thread-export-${selectedThread?.thread_id}.${exportDialog}`;
      a.click();
      window.URL.revokeObjectURL(url);
      setExportDialog(false);
    }
  });

  // Handlers
  const handleThreadToggle = useCallback((threadId: string) => {
    setExpandedThreads(prev => {
      const newSet = new Set(prev);
      if (newSet.has(threadId)) {
        newSet.delete(threadId);
      } else {
        newSet.add(threadId);
      }
      return newSet;
    });
  }, []);

  const handleArchiveThread = useCallback((thread: ConversationThread) => {
    if (thread.status === 'archived') {
      unarchiveThreadMutation.mutate(thread.thread_id);
    } else {
      archiveThreadMutation.mutate(thread.thread_id);
    }
  }, [archiveThreadMutation, unarchiveThreadMutation]);

  const handleConversationSelect = useCallback((conversation: Conversation) => {
    setSelectedConversation(conversation);
    setHistoryDialog(true);
  }, []);

  // Filter and sort threads
  const filteredThreads = useMemo(() => {
    return threads
      .filter((thread: ConversationThread) => {
        if (searchTerm) {
          const searchLower = searchTerm.toLowerCase();
          return (
            thread.subject?.toLowerCase().includes(searchLower) ||
            thread.participants.some(p => p.toLowerCase().includes(searchLower)) ||
            thread.tags.some(tag => tag.toLowerCase().includes(searchLower))
          );
        }
        return true;
      })
      .sort((a: ConversationThread, b: ConversationThread) => 
        new Date(b.last_message_at).getTime() - new Date(a.last_message_at).getTime()
      );
  }, [threads, searchTerm]);

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'error';
      case 'high': return 'warning';
      case 'normal': return 'info';
      case 'low': return 'default';
      default: return 'default';
    }
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'resolved': return 'success';
      case 'archived': return 'default';
      case 'active': return 'primary';
      default: return 'default';
    }
  };

  // Render thread list
  const renderThreadList = () => (
    <Card>
      <CardHeader
        title={t('threading.conversation_threads')}
        action={
          <Box sx={{ display: 'flex', gap: 1 }}>
            <IconButton onClick={() => refetchThreads()} size="small">
              <RefreshIcon />
            </IconButton>
            <IconButton size="small">
              <FilterIcon />
            </IconButton>
          </Box>
        }
      />
      
      <CardContent>
        {/* Search and Filters */}
        <Box sx={{ mb: 3 }}>
          <TextField
            fullWidth
            size="small"
            placeholder={t('threading.search_threads')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
            }}
            sx={{ mb: 2 }}
          />
          
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            {['all', 'active', 'resolved', 'archived'].map((status) => (
              <Chip
                key={status}
                label={t(`threading.status.${status}`)}
                color={statusFilter === status ? 'primary' : 'default'}
                onClick={() => setStatusFilter(status)}
                size="small"
              />
            ))}
          </Box>
        </Box>

        {/* Thread List */}
        {threadsLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : filteredThreads.length === 0 ? (
          <Alert severity="info">
            {t('threading.no_threads_found')}
          </Alert>
        ) : (
          <List>
            {filteredThreads.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map((thread: ConversationThread) => (
              <React.Fragment key={thread.thread_id}>
                <ListItem sx={{ flexDirection: 'column', alignItems: 'stretch' }}>
                  {/* Thread Header */}
                  <Box sx={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    width: '100%',
                    cursor: 'pointer',
                    p: 1,
                    borderRadius: 1,
                    '&:hover': { bgcolor: 'action.hover' }
                  }}
                  onClick={() => handleThreadToggle(thread.thread_id)}
                  >
                    <Avatar sx={{ mr: 2, bgcolor: getStatusColor(thread.status) }}>
                      <ThreadIcon />
                    </Avatar>
                    
                    <Box sx={{ flex: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                        <Typography variant="subtitle1" sx={{ fontWeight: 'medium' }}>
                          {thread.subject || `Thread ${thread.thread_id.substring(0, 8)}`}
                        </Typography>
                        <Chip
                          size="small"
                          label={t(`threading.status.${thread.status}`)}
                          color={getStatusColor(thread.status) as any}
                          variant="outlined"
                        />
                        {thread.priority !== 'normal' && (
                          <Chip
                            size="small"
                            label={t(`threading.priority.${thread.priority}`)}
                            color={getPriorityColor(thread.priority) as any}
                            variant="outlined"
                          />
                        )}
                      </Box>
                      
                      <Typography variant="body2" color="text.secondary">
                        {thread.participants.join(', ')} • {thread.message_count} messages
                      </Typography>
                      
                      <Typography variant="caption" color="text.secondary">
                        {t('threading.last_activity')}: {formatDistanceToNow(new Date(thread.last_message_at), { addSuffix: true })}
                      </Typography>
                    </Box>
                    
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <IconButton
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedThread(thread);
                        }}
                      >
                        <AnalyticsIcon />
                      </IconButton>
                      
                      <IconButton
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleArchiveThread(thread);
                        }}
                      >
                        {thread.status === 'archived' ? <UnarchiveIcon /> : <ArchiveIcon />}
                      </IconButton>
                      
                      <IconButton size="small">
                        <ExpandMoreIcon 
                          sx={{ 
                            transform: expandedThreads.has(thread.thread_id) ? 'rotate(180deg)' : 'rotate(0deg)',
                            transition: 'transform 0.2s'
                          }} 
                        />
                      </IconButton>
                    </Box>
                  </Box>
                  
                  {/* Expanded Thread Content */}
                  <Collapse in={expandedThreads.has(thread.thread_id)}>
                    <Box sx={{ mt: 2, pl: 2 }}>
                      {/* Tags */}
                      {thread.tags.length > 0 && (
                        <Box sx={{ mb: 2 }}>
                          <Typography variant="caption" color="text.secondary" sx={{ mr: 1 }}>
                            {t('threading.tags')}:
                          </Typography>
                          {thread.tags.map((tag, index) => (
                            <Chip key={index} label={tag} size="small" sx={{ mr: 0.5 }} />
                          ))}
                        </Box>
                      )}
                      
                      {/* Conversations in Thread */}
                      <Typography variant="subtitle2" sx={{ mb: 1 }}>
                        {t('threading.conversations_in_thread')} ({thread.conversations.length})
                      </Typography>
                      
                      <List dense>
                        {thread.conversations.map((conversation) => (
                          <ListItem
                            key={conversation.conversation_id}
                            button
                            onClick={() => handleConversationSelect(conversation)}
                            sx={{ 
                              border: 1, 
                              borderColor: 'divider', 
                              borderRadius: 1, 
                              mb: 1 
                            }}
                          >
                            <ListItemText
                              primary={
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  <Typography variant="body2">
                                    {conversation.participant_name || conversation.participant_email || 'Unknown'}
                                  </Typography>
                                  {conversation.agent_name && (
                                    <Chip
                                      size="small"
                                      label={conversation.agent_name}
                                      variant="outlined"
                                    />
                                  )}
                                </Box>
                              }
                              secondary={
                                <Box>
                                  <Typography variant="caption" color="text.secondary">
                                    {formatDate(new Date(conversation.created_at))} • 
                                    {conversation.message_count} messages
                                  </Typography>
                                  {conversation.satisfaction_rating && (
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mt: 0.5 }}>
                                      <StarIcon fontSize="small" />
                                      <Typography variant="caption">
                                        {conversation.satisfaction_rating}/5
                                      </Typography>
                                    </Box>
                                  )}
                                </Box>
                              }
                            />
                            <ListItemSecondaryAction>
                              <Chip
                                size="small"
                                label={t(`threading.status.${conversation.status}`)}
                                color={getStatusColor(conversation.status) as any}
                                variant="outlined"
                              />
                            </ListItemSecondaryAction>
                          </ListItem>
                        ))}
                      </List>
                    </Box>
                  </Collapse>
                </ListItem>
                <Divider />
              </React.Fragment>
            ))}
          </List>
        )}
        
        {/* Pagination */}
        <TablePagination
          component="div"
          count={filteredThreads.length}
          page={page}
          onPageChange={(_, newPage) => setPage(newPage)}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={(e) => setRowsPerPage(parseInt(e.target.value, 10))}
          rowsPerPageOptions={[10, 25, 50, 100]}
        />
      </CardContent>
    </Card>
  );

  // Render thread analytics
  const renderThreadAnalytics = () => {
    if (!selectedThread || !threadAnalytics) return null;

    return (
      <Card>
        <CardHeader
          title={t('threading.thread_analytics')}
          subheader={selectedThread.subject || `Thread ${selectedThread.thread_id.substring(0, 8)}`}
        />
        <CardContent>
          <Grid container spacing={3}>
            {/* Key Metrics */}
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 2 }}>
                <Typography variant="h6" gutterBottom>
                  {t('threading.key_metrics')}
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2">{t('threading.total_conversations')}</Typography>
                    <Typography variant="body2" fontWeight="bold">
                      {threadAnalytics.conversation_count}
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2">{t('threading.total_messages')}</Typography>
                    <Typography variant="body2" fontWeight="bold">
                      {threadAnalytics.message_count}
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2">{t('threading.avg_response_time')}</Typography>
                    <Typography variant="body2" fontWeight="bold">
                      {formatDuration(threadAnalytics.avg_response_time_minutes * 60)}
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2">{t('threading.resolution_rate')}</Typography>
                    <Typography variant="body2" fontWeight="bold">
                      {Math.round(threadAnalytics.resolution_rate * 100)}%
                    </Typography>
                  </Box>
                </Box>
              </Paper>
            </Grid>
            
            {/* Participant Engagement */}
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 2 }}>
                <Typography variant="h6" gutterBottom>
                  {t('threading.participant_engagement')}
                </Typography>
                <List dense>
                  {threadAnalytics.participant_stats?.map((participant: any, index: number) => (
                    <ListItem key={index}>
                      <ListItemText
                        primary={participant.name}
                        secondary={`${participant.message_count} messages • ${Math.round(participant.engagement_score * 100)}% engagement`}
                      />
                    </ListItem>
                  ))}
                </List>
              </Paper>
            </Grid>
            
            {/* Timeline */}
            <Grid item xs={12}>
              <Paper sx={{ p: 2 }}>
                <Typography variant="h6" gutterBottom>
                  {t('threading.conversation_timeline')}
                </Typography>
                <Timeline>
                  {threadAnalytics.timeline?.map((event: any, index: number) => (
                    <TimelineItem key={index}>
                      <TimelineSeparator>
                        <TimelineDot color={event.type === 'created' ? 'primary' : 'secondary'}>
                          {event.type === 'created' ? <MessageIcon /> : <UpdateIcon />}
                        </TimelineDot>
                        {index < threadAnalytics.timeline.length - 1 && <TimelineConnector />}
                      </TimelineSeparator>
                      <TimelineContent>
                        <Typography variant="body2" fontWeight="medium">
                          {event.description}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {formatDate(new Date(event.timestamp))}
                        </Typography>
                      </TimelineContent>
                    </TimelineItem>
                  ))}
                </Timeline>
              </Paper>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    );
  };

  // Render conversation history dialog
  const renderHistoryDialog = () => (
    <Dialog
      open={historyDialog}
      onClose={() => setHistoryDialog(false)}
      maxWidth="md"
      fullWidth
      fullScreen={isMobile}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <HistoryIcon />
          {t('threading.conversation_history')}
        </Box>
      </DialogTitle>
      
      <DialogContent>
        {conversationHistoryLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : conversationHistory ? (
          <Box>
            {/* Status Changes */}
            <Accordion defaultExpanded>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="h6">{t('threading.status_changes')}</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Timeline>
                  {conversationHistory.status_changes?.map((change: StatusChange) => (
                    <TimelineItem key={change.change_id}>
                      <TimelineSeparator>
                        <TimelineDot>
                          <UpdateIcon />
                        </TimelineDot>
                        <TimelineConnector />
                      </TimelineSeparator>
                      <TimelineContent>
                        <Typography variant="body2">
                          {t('threading.status_changed_from_to', {
                            from: change.from_status,
                            to: change.to_status,
                            user: change.changed_by
                          })}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {formatDate(new Date(change.changed_at))}
                        </Typography>
                        {change.reason && (
                          <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                            {change.reason}
                          </Typography>
                        )}
                      </TimelineContent>
                    </TimelineItem>
                  ))}
                </Timeline>
              </AccordionDetails>
            </Accordion>
            
            {/* Assignments */}
            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="h6">{t('threading.agent_assignments')}</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Timeline>
                  {conversationHistory.assignments?.map((assignment: Assignment) => (
                    <TimelineItem key={assignment.assignment_id}>
                      <TimelineSeparator>
                        <TimelineDot>
                          <AssignmentIcon />
                        </TimelineDot>
                        <TimelineConnector />
                      </TimelineSeparator>
                      <TimelineContent>
                        <Typography variant="body2">
                          {t('threading.assigned_to_agent', {
                            agent: assignment.agent_name,
                            assigner: assignment.assigned_by
                          })}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {formatDate(new Date(assignment.assigned_at))}
                        </Typography>
                        {assignment.assignment_reason && (
                          <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                            {assignment.assignment_reason}
                          </Typography>
                        )}
                      </TimelineContent>
                    </TimelineItem>
                  ))}
                </Timeline>
              </AccordionDetails>
            </Accordion>
            
            {/* Messages Summary */}
            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="h6">{t('threading.message_summary')}</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h4" color="primary">
                        {conversationHistory.messages_summary.total_messages}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {t('threading.total_messages')}
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={6}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <Typography variant="h4" color="secondary">
                        {formatDuration(conversationHistory.messages_summary.average_response_time * 60)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {t('threading.avg_response_time')}
                      </Typography>
                    </Paper>
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>
          </Box>
        ) : (
          <Alert severity="info">
            {t('threading.no_history_available')}
          </Alert>
        )}
      </DialogContent>
      
      <DialogActions>
        <Button onClick={() => setHistoryDialog(false)}>
          {t('common.close')}
        </Button>
        <Button variant="contained" startIcon={<DownloadIcon />}>
          {t('threading.export_history')}
        </Button>
      </DialogActions>
    </Dialog>
  );

  const tabLabels = ['threading.threads', 'threading.analytics'];

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column', p: 3 }}>
      <Typography variant="h4" gutterBottom>
        {t('threading.conversation_threading')}
      </Typography>

      <Tabs value={currentTab} onChange={(_, newValue) => setCurrentTab(newValue)} sx={{ mb: 3 }}>
        {tabLabels.map((label, index) => (
          <Tab key={index} label={t(label)} />
        ))}
      </Tabs>

      <Box sx={{ flex: 1, overflow: 'auto' }}>
        {currentTab === 0 && renderThreadList()}
        {currentTab === 1 && renderThreadAnalytics()}
      </Box>

      {renderHistoryDialog()}
    </Box>
  );
};

export default ConversationThreading;