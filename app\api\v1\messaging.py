"""
Messaging API endpoints for SMS, in-app chat, templates, and broadcasts.
"""

from typing import Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Body
from pydantic import BaseModel
from datetime import datetime
import asyncpg

from app.core.dependencies import get_current_user, get_database
from app.core.authorization import require_role
from app.services.messaging_service import MessagingService
from app.core.service_dependencies import get_messaging_service
from app.core.exceptions import ValidationError, ResourceNotFoundError
from app.models.auth_models import UserRoleType

router = APIRouter(prefix="/messaging", tags=["messaging"])


# Request/Response Models
class SendMessageRequest(BaseModel):
    content: str
    message_type: str = 'text'


class CreateConversationRequest(BaseModel):
    conversation_type: str
    client_id: Optional[int] = None
    tutor_id: Optional[int] = None
    phone_number: Optional[str] = None
    subject: Optional[str] = None


class CreateTemplateRequest(BaseModel):
    template_name: str
    template_type: str
    category: str
    subject: Optional[str] = None
    content_en: str
    content_fr: str
    variables: Optional[Dict[str, Any]] = None


class CreateBroadcastRequest(BaseModel):
    title: str
    message_type: str
    content_en: str
    content_fr: str
    recipient_type: str
    recipient_filters: Optional[Dict[str, Any]] = None
    scheduled_at: Optional[datetime] = None


# Conversation Endpoints
@router.get("/conversations")
async def get_conversations(
    conversation_type: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    limit: int = Query(50, le=100),
    offset: int = Query(0),
    user = Depends(get_current_user),
    service: MessagingService = Depends(get_messaging_service),
    db: asyncpg.Connection = Depends(get_database)
):
    """Get conversations for the current user."""
    try:
        # Get user's active role
        user_role = user.roles[0] if user.roles else UserRoleType.CLIENT
        
        result = await service.get_conversations(
            db=db,
            user_id=user.user_id,
            user_role=user_role.value,
            conversation_type=conversation_type,
            status=status,
            limit=limit,
            offset=offset
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/conversations")
async def create_conversation(
    request: CreateConversationRequest,
    user = Depends(get_current_user),
    service: MessagingService = Depends(get_messaging_service),
    db: asyncpg.Connection = Depends(get_database)
):
    """Create a new conversation."""
    try:
        # Get user's active role
        user_role = user.roles[0] if user.roles else UserRoleType.CLIENT
        
        # Set manager_id if user is a manager
        manager_id = user.user_id if UserRoleType.MANAGER in user.roles else None
        
        # Set client_id if user is a client
        if UserRoleType.CLIENT in user.roles and not request.client_id:
            client_profile = await db.fetchrow(
                "SELECT client_id FROM client_profiles WHERE user_id = $1",
                user.user_id
            )
            if client_profile:
                request.client_id = client_profile['client_id']
        
        # Set tutor_id if user is a tutor
        if UserRoleType.TUTOR in user.roles and not request.tutor_id:
            tutor_profile = await db.fetchrow(
                "SELECT tutor_id FROM tutor_profiles WHERE user_id = $1",
                user.user_id
            )
            if tutor_profile:
                request.tutor_id = tutor_profile['tutor_id']
        
        conversation_id = await service.create_conversation(
            db=db,
            conversation_type=request.conversation_type,
            client_id=request.client_id,
            tutor_id=request.tutor_id,
            manager_id=manager_id,
            phone_number=request.phone_number,
            subject=request.subject
        )
        
        return {"conversation_id": conversation_id}
        
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/conversations/{conversation_id}/messages")
async def get_conversation_messages(
    conversation_id: int,
    limit: int = Query(50, le=100),
    offset: int = Query(0),
    user = Depends(get_current_user),
    service: MessagingService = Depends(get_messaging_service),
    db: asyncpg.Connection = Depends(get_database)
):
    """Get messages in a conversation."""
    try:
        # Get user's active role
        user_role = user.roles[0] if user.roles else UserRoleType.CLIENT
        
        messages = await service.get_conversation_messages(
            db=db,
            conversation_id=conversation_id,
            user_id=user.user_id,
            user_role=user_role.value,
            limit=limit,
            offset=offset
        )
        return {"messages": messages}
        
    except ResourceNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/conversations/{conversation_id}/messages")
async def send_message(
    conversation_id: int,
    request: SendMessageRequest,
    user = Depends(get_current_user),
    service: MessagingService = Depends(get_messaging_service),
    db: asyncpg.Connection = Depends(get_database)
):
    """Send a message in a conversation."""
    try:
        # Determine sender type based on user role
        user_role = user.roles[0] if user.roles else UserRoleType.CLIENT
        sender_type = user_role.value
        
        message = await service.send_message(
            db=db,
            conversation_id=conversation_id,
            sender_id=user.user_id,
            sender_type=sender_type,
            content=request.content,
            message_type=request.message_type
        )
        
        return {"message": dict(message)}
        
    except ResourceNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Template Endpoints
@router.get("/templates")
async def get_templates(
    template_type: Optional[str] = Query(None),
    category: Optional[str] = Query(None),
    is_active: bool = Query(True),
    user = Depends(get_current_user),
    service: MessagingService = Depends(get_messaging_service),
    db: asyncpg.Connection = Depends(get_database)
):
    """Get message templates."""
    # Only managers can access templates
    require_role(user, [UserRoleType.MANAGER])
    
    try:
        templates = await service.get_templates(
            db=db,
            template_type=template_type,
            category=category,
            is_active=is_active
        )
        return {"templates": templates}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/templates")
async def create_template(
    request: CreateTemplateRequest,
    user = Depends(get_current_user),
    service: MessagingService = Depends(get_messaging_service),
    db: asyncpg.Connection = Depends(get_database)
):
    """Create a new message template."""
    # Only managers can create templates
    require_role(user, [UserRoleType.MANAGER])
    
    try:
        template_id = await service.create_template(
            db=db,
            template_data=request.dict(),
            created_by=user.user_id
        )
        
        return {"template_id": template_id}
        
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Broadcast Endpoints
@router.get("/broadcasts")
async def get_broadcasts(
    status: Optional[str] = Query(None),
    limit: int = Query(20, le=100),
    offset: int = Query(0),
    user = Depends(get_current_user),
    service: MessagingService = Depends(get_messaging_service),
    db: asyncpg.Connection = Depends(get_database)
):
    """Get broadcast messages."""
    # Only managers can access broadcasts
    require_role(user, [UserRoleType.MANAGER])
    
    try:
        result = await service.get_broadcasts(
            db=db,
            status=status,
            limit=limit,
            offset=offset
        )
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/broadcasts")
async def create_broadcast(
    request: CreateBroadcastRequest,
    user = Depends(get_current_user),
    service: MessagingService = Depends(get_messaging_service),
    db: asyncpg.Connection = Depends(get_database)
):
    """Create a new broadcast message."""
    # Only managers can create broadcasts
    require_role(user, [UserRoleType.MANAGER])
    
    try:
        broadcast_id = await service.create_broadcast(
            db=db,
            broadcast_data=request.dict(),
            created_by=user.user_id
        )
        
        return {"broadcast_id": broadcast_id}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/broadcasts/{broadcast_id}/send")
async def send_broadcast(
    broadcast_id: int,
    user = Depends(get_current_user),
    service: MessagingService = Depends(get_messaging_service),
    db: asyncpg.Connection = Depends(get_database)
):
    """Send a broadcast message."""
    # Only managers can send broadcasts
    require_role(user, [UserRoleType.MANAGER])
    
    # TODO: Implement broadcast sending logic
    raise HTTPException(status_code=501, detail="Broadcast sending not yet implemented")


# WebSocket endpoint for real-time messaging
from fastapi import WebSocket, WebSocketDisconnect
from app.core.websocket import ConnectionManager

manager = ConnectionManager()


@router.websocket("/ws/{conversation_id}")
async def websocket_endpoint(
    websocket: WebSocket,
    conversation_id: int,
    token: str = Query(...)
):
    """WebSocket endpoint for real-time messaging."""
    # TODO: Verify token and user permissions
    
    await manager.connect(websocket, f"conversation_{conversation_id}")
    
    try:
        while True:
            # Wait for messages from client
            data = await websocket.receive_text()
            
            # Broadcast message to all connected clients in the conversation
            await manager.broadcast(f"conversation_{conversation_id}", data)
            
    except WebSocketDisconnect:
        manager.disconnect(websocket, f"conversation_{conversation_id}")