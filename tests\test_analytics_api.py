"""
Tests for analytics API endpoints.
"""

import pytest
from datetime import date, datetime, timedelta
from decimal import Decimal
from fastapi import status
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, Mock, patch

from app.main import app
from app.models.user_models import User, UserRoleType
from app.models.analytics_models import (
    TutorPerformanceMetrics, StudentLearningGoal, StudentProgressSummary,
    SessionFeedback, PlatformAnalytics, TutorLeaderboardEntry,
    TutorDashboardData, StudentDashboardData, PlatformDashboardData,
    PeriodType, GoalStatus, FeedbackRole, MetricType
)
from app.core.exceptions import ResourceNotFoundError, ValidationError


@pytest.fixture
def test_client():
    """Create test client."""
    client = TestClient(app)
    # Set a proper user agent to avoid security middleware blocking
    client.headers["User-Agent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    return client


@pytest.fixture
def mock_current_user():
    """Create mock current user."""
    return User(
        user_id=1,
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        roles=[UserRoleType.MANAGER],
        is_active=True,
        created_at=datetime.now(),
        updated_at=datetime.now()
    )


@pytest.fixture
def mock_tutor_user():
    """Create mock tutor user."""
    return User(
        user_id=2,
        email="<EMAIL>",
        first_name="Test",
        last_name="Tutor",
        roles=[UserRoleType.TUTOR],
        is_active=True,
        created_at=datetime.now(),
        updated_at=datetime.now()
    )


@pytest.fixture
def mock_client_user():
    """Create mock client user."""
    return User(
        user_id=3,
        email="<EMAIL>",
        first_name="Test",
        last_name="Client",
        roles=[UserRoleType.CLIENT],
        is_active=True,
        created_at=datetime.now(),
        updated_at=datetime.now()
    )


class TestTutorAnalyticsEndpoints:
    """Test tutor analytics endpoints."""
    
    @pytest.mark.asyncio
    async def test_get_tutor_dashboard_as_manager(
        self, test_client, mock_current_user
    ):
        """Test getting tutor dashboard as manager."""
        tutor_id = 2
        
        mock_dashboard = TutorDashboardData(
            performance_metrics=TutorPerformanceMetrics(
                metric_id=1,
                tutor_id=tutor_id,
                period_start=date.today().replace(day=1),
                period_end=date.today(),
                period_type=PeriodType.MONTHLY,
                total_sessions=10,
                completed_sessions=8,
                average_rating=Decimal("4.5"),
                created_at=datetime.now(),
                updated_at=datetime.now()
            ),
            recent_feedback=[],
            subject_performance={'Math': {'total_sessions': 5}},
            student_progress=[],
            earnings_trend=[],
            upcoming_sessions=3,
            pending_feedback=2
        )
        
        with patch('app.api.v1.analytics.get_current_user', return_value=mock_current_user):
            with patch('app.api.v1.analytics.get_analytics_service') as mock_service:
                mock_service.return_value.get_tutor_dashboard = AsyncMock(
                    return_value=mock_dashboard
                )
                
                response = test_client.get(
                    f"/api/v1/analytics/tutor/dashboard?tutor_id={tutor_id}",
                    headers={"Authorization": "Bearer fake-token"}
                )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data['performance_metrics']['tutor_id'] == tutor_id
        assert data['upcoming_sessions'] == 3
        assert data['pending_feedback'] == 2
    
    @pytest.mark.asyncio
    async def test_get_tutor_dashboard_as_tutor_self(
        self, test_client, mock_tutor_user
    ):
        """Test tutor getting own dashboard."""
        mock_dashboard = TutorDashboardData(
            performance_metrics=TutorPerformanceMetrics(
                metric_id=1,
                tutor_id=mock_tutor_user.user_id,
                period_start=date.today().replace(day=1),
                period_end=date.today(),
                period_type=PeriodType.MONTHLY,
                total_sessions=10,
                completed_sessions=8,
                average_rating=Decimal("4.5")
            ),
            recent_feedback=[],
            subject_performance={},
            student_progress=[],
            earnings_trend=[],
            upcoming_sessions=3,
            pending_feedback=2
        )
        
        with patch('app.api.v1.analytics.get_current_user', return_value=mock_tutor_user):
            with patch('app.api.v1.analytics.get_analytics_service') as mock_service:
                mock_service.return_value.get_tutor_dashboard = AsyncMock(
                    return_value=mock_dashboard
                )
                
                # No tutor_id parameter - should default to current user
                response = test_client.get(
                    "/api/v1/analytics/tutor/dashboard",
                    headers={"Authorization": "Bearer fake-token"}
                )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data['performance_metrics']['tutor_id'] == mock_tutor_user.user_id
    
    @pytest.mark.asyncio
    async def test_get_tutor_dashboard_forbidden(
        self, test_client, mock_tutor_user
    ):
        """Test tutor cannot view another tutor's dashboard."""
        other_tutor_id = 999
        
        with patch('app.api.v1.analytics.get_current_user', return_value=mock_tutor_user):
            response = test_client.get(
                f"/api/v1/analytics/tutor/dashboard?tutor_id={other_tutor_id}",
                headers={"Authorization": "Bearer fake-token"}
            )
        
        assert response.status_code == status.HTTP_403_FORBIDDEN
    
    @pytest.mark.asyncio
    async def test_get_tutor_performance(
        self, test_client, mock_current_user
    ):
        """Test getting tutor performance metrics."""
        tutor_id = 2
        
        mock_metrics = TutorPerformanceMetrics(
            metric_id=1,
            tutor_id=tutor_id,
            period_start=date.today().replace(day=1),
            period_end=date.today(),
            period_type=PeriodType.MONTHLY,
            total_sessions=10,
            completed_sessions=8,
            total_revenue=Decimal("800.00"),
            average_rating=Decimal("4.5"),
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        with patch('app.api.v1.analytics.get_current_user', return_value=mock_current_user):
            with patch('app.api.v1.analytics.get_analytics_service') as mock_service:
                mock_service.return_value.calculate_tutor_metrics = AsyncMock(
                    return_value=mock_metrics
                )
                
                response = test_client.get(
                    f"/api/v1/analytics/tutor/performance?tutor_id={tutor_id}&period_type=monthly",
                    headers={"Authorization": "Bearer fake-token"}
                )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data['tutor_id'] == tutor_id
        assert data['total_sessions'] == 10
        assert data['completed_sessions'] == 8
    
    @pytest.mark.asyncio
    async def test_get_tutor_leaderboard(
        self, test_client, mock_current_user
    ):
        """Test getting tutor leaderboard."""
        mock_leaderboard = [
            TutorLeaderboardEntry(
                user_id=1,
                first_name="Top",
                last_name="Tutor",
                average_rating=Decimal("5.0"),
                total_sessions=50,
                completed_sessions=48,
                total_revenue=Decimal("5000.00"),
                unique_students=20,
                rating_rank=1,
                session_rank=1,
                revenue_rank=1
            ),
            TutorLeaderboardEntry(
                user_id=2,
                first_name="Good",
                last_name="Tutor",
                average_rating=Decimal("4.8"),
                total_sessions=40,
                completed_sessions=38,
                total_revenue=Decimal("4000.00"),
                unique_students=15,
                rating_rank=2,
                session_rank=2,
                revenue_rank=2
            )
        ]
        
        with patch('app.api.v1.analytics.get_current_user', return_value=mock_current_user):
            with patch('app.api.v1.analytics.get_analytics_service') as mock_service:
                mock_service.return_value.get_tutor_leaderboard = AsyncMock(
                    return_value=mock_leaderboard
                )
                
                response = test_client.get(
                    "/api/v1/analytics/tutor/leaderboard?period_type=monthly&limit=10",
                    headers={"Authorization": "Bearer fake-token"}
                )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data) == 2
        assert data[0]['first_name'] == "Top"
        assert data[0]['rating_rank'] == 1


class TestStudentAnalyticsEndpoints:
    """Test student analytics endpoints."""
    
    @pytest.mark.asyncio
    async def test_get_student_dashboard(
        self, test_client, mock_current_user
    ):
        """Test getting student dashboard."""
        student_id = 3
        
        mock_dashboard = StudentDashboardData(
            progress_metrics=[],
            learning_goals=[],
            recent_sessions=[],
            tutor_feedback=[],
            improvement_trends={},
            next_session=None
        )
        
        # Mock database connection and check_student_access
        with patch('app.api.v1.analytics.get_db_manager') as mock_db:
            mock_conn = AsyncMock()
            mock_db.return_value.acquire = AsyncMock()
            mock_db.return_value.acquire.return_value.__aenter__.return_value = mock_conn
            
            with patch('app.api.v1.analytics.check_student_access', return_value=True):
                with patch('app.api.v1.analytics.get_current_user', return_value=mock_current_user):
                    with patch('app.api.v1.analytics.get_analytics_service') as mock_service:
                        mock_service.return_value.get_student_dashboard = AsyncMock(
                            return_value=mock_dashboard
                        )
                        
                        response = test_client.get(
                            f"/api/v1/analytics/student/dashboard?student_id={student_id}",
                            headers={"Authorization": "Bearer fake-token"}
                        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert 'progress_metrics' in data
        assert 'learning_goals' in data
    
    @pytest.mark.asyncio
    async def test_get_student_progress(
        self, test_client, mock_current_user
    ):
        """Test getting student progress."""
        student_id = 3
        
        mock_progress = [
            StudentProgressSummary(
                student_id=student_id,
                subject_area="Math",
                attended_sessions=10,
                session_hours=Decimal("15.0"),
                performance_score=Decimal("85.0"),
                improvement_rate=Decimal("10.0"),
                active_goals=2,
                completed_goals=1
            )
        ]
        
        with patch('app.api.v1.analytics.get_db_manager') as mock_db:
            mock_conn = AsyncMock()
            mock_db.return_value.acquire = AsyncMock()
            mock_db.return_value.acquire.return_value.__aenter__.return_value = mock_conn
            
            with patch('app.api.v1.analytics.check_student_access', return_value=True):
                with patch('app.api.v1.analytics.get_current_user', return_value=mock_current_user):
                    with patch('app.api.v1.analytics.get_analytics_service') as mock_service:
                        mock_repo = Mock()
                        mock_repo.get_progress_summary = AsyncMock(return_value=mock_progress)
                        mock_service.return_value.student_progress_repo = mock_repo
                        
                        response = test_client.get(
                            f"/api/v1/analytics/student/{student_id}/progress?days=30",
                            headers={"Authorization": "Bearer fake-token"}
                        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data) == 1
        assert data[0]['subject_area'] == "Math"
        assert data[0]['attended_sessions'] == 10
    
    @pytest.mark.asyncio
    async def test_create_learning_goal(
        self, test_client, mock_current_user
    ):
        """Test creating a learning goal."""
        student_id = 3
        goal_data = {
            "subject_area": "Mathematics",
            "goal_title": "Master Algebra Basics",
            "goal_description": "Focus on linear equations and graphing",
            "target_date": (date.today() + timedelta(days=30)).isoformat()
        }
        
        created_goal = StudentLearningGoal(
            goal_id=1,
            student_id=student_id,
            subject_area="Mathematics",
            goal_title="Master Algebra Basics",
            goal_description="Focus on linear equations and graphing",
            target_date=date.today() + timedelta(days=30),
            status=GoalStatus.ACTIVE,
            progress_percentage=0,
            created_by=mock_current_user.user_id,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        with patch('app.api.v1.analytics.get_db_manager') as mock_db:
            mock_conn = AsyncMock()
            mock_db.return_value.acquire = AsyncMock()
            mock_db.return_value.acquire.return_value.__aenter__.return_value = mock_conn
            
            with patch('app.api.v1.analytics.check_student_access', return_value=True):
                with patch('app.api.v1.analytics.get_current_user', return_value=mock_current_user):
                    with patch('app.api.v1.analytics.get_analytics_service') as mock_service:
                        mock_service.return_value.create_learning_goal = AsyncMock(
                            return_value=created_goal
                        )
                        
                        response = test_client.post(
                            f"/api/v1/analytics/student/{student_id}/goals",
                            json=goal_data,
                            headers={"Authorization": "Bearer fake-token"}
                        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data['goal_title'] == "Master Algebra Basics"
        assert data['status'] == "active"
    
    @pytest.mark.asyncio
    async def test_update_goal_progress(
        self, test_client, mock_current_user
    ):
        """Test updating goal progress."""
        goal_id = 1
        
        updated_goal = StudentLearningGoal(
            goal_id=goal_id,
            student_id=3,
            subject_area="Mathematics",
            goal_title="Master Algebra Basics",
            status=GoalStatus.ACTIVE,
            progress_percentage=75,
            created_by=1,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        with patch('app.api.v1.analytics.get_db_manager') as mock_db:
            mock_conn = AsyncMock()
            mock_db.return_value.acquire = AsyncMock()
            mock_db.return_value.acquire.return_value.__aenter__.return_value = mock_conn
            
            # Mock goal info query
            mock_conn.fetchrow = AsyncMock(return_value={
                'student_id': 3,
                'created_by': mock_current_user.user_id,
                'assigned_tutor_id': None
            })
            
            with patch('app.api.v1.analytics.get_current_user', return_value=mock_current_user):
                with patch('app.api.v1.analytics.get_analytics_service') as mock_service:
                    mock_service.return_value.update_goal_progress = AsyncMock(
                        return_value=updated_goal
                    )
                    
                    response = test_client.put(
                        f"/api/v1/analytics/goals/{goal_id}/progress?progress_percentage=75",
                        headers={"Authorization": "Bearer fake-token"}
                    )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data['progress_percentage'] == 75


class TestSessionFeedbackEndpoints:
    """Test session feedback endpoints."""
    
    @pytest.mark.asyncio
    async def test_submit_session_feedback(
        self, test_client, mock_client_user
    ):
        """Test submitting session feedback."""
        appointment_id = 1
        feedback_data = {
            "appointment_id": appointment_id,
            "overall_rating": 5,
            "punctuality_rating": 5,
            "preparation_rating": 4,
            "communication_rating": 5,
            "feedback_text": "Great session!",
            "improvement_areas": ["More practice problems"],
            "positive_highlights": ["Clear explanations"]
        }
        
        created_feedback = SessionFeedback(
            feedback_id=1,
            appointment_id=appointment_id,
            given_by=mock_client_user.user_id,
            given_to=2,  # Tutor ID
            role_type=FeedbackRole.STUDENT_TO_TUTOR,
            overall_rating=5,
            punctuality_rating=5,
            preparation_rating=4,
            communication_rating=5,
            feedback_text="Great session!",
            improvement_areas=["More practice problems"],
            positive_highlights=["Clear explanations"],
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        with patch('app.api.v1.analytics.get_current_user', return_value=mock_client_user):
            with patch('app.api.v1.analytics.get_analytics_service') as mock_service:
                mock_service.return_value.submit_session_feedback = AsyncMock(
                    return_value=created_feedback
                )
                
                response = test_client.post(
                    f"/api/v1/analytics/appointments/{appointment_id}/feedback",
                    json=feedback_data,
                    headers={"Authorization": "Bearer fake-token"}
                )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data['overall_rating'] == 5
        assert data['feedback_text'] == "Great session!"
    
    @pytest.mark.asyncio
    async def test_get_pending_feedback(
        self, test_client, mock_current_user
    ):
        """Test getting pending feedback (manager only)."""
        mock_feedback = [
            SessionFeedback(
                feedback_id=1,
                appointment_id=1,
                given_by=3,
                given_to=2,
                role_type=FeedbackRole.STUDENT_TO_TUTOR,
                overall_rating=2,
                feedback_text="Tutor was late and unprepared",
                requires_followup=True,
                followup_completed=False,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
        ]
        
        with patch('app.api.v1.analytics.get_current_user', return_value=mock_current_user):
            with patch('app.api.v1.analytics.get_db_manager') as mock_db:
                mock_conn = AsyncMock()
                mock_db.return_value.acquire = AsyncMock()
                mock_db.return_value.acquire.return_value.__aenter__.return_value = mock_conn
                
                with patch('app.api.v1.analytics.get_analytics_service') as mock_service:
                    mock_repo = Mock()
                    mock_repo.get_pending_followups = AsyncMock(return_value=mock_feedback)
                    mock_service.return_value.feedback_repo = mock_repo
                    
                    response = test_client.get(
                        "/api/v1/analytics/feedback/pending?limit=20",
                        headers={"Authorization": "Bearer fake-token"}
                    )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data) == 1
        assert data[0]['requires_followup'] is True


class TestPlatformAnalyticsEndpoints:
    """Test platform analytics endpoints."""
    
    @pytest.mark.asyncio
    async def test_get_platform_dashboard(
        self, test_client, mock_current_user
    ):
        """Test getting platform dashboard (manager only)."""
        mock_dashboard = PlatformDashboardData(
            platform_metrics=PlatformAnalytics(
                analytics_id=1,
                metric_date=date.today(),
                metric_type=MetricType.USER_ACTIVITY,
                active_clients=50,
                active_tutors=20,
                active_students=45,
                new_registrations=5,
                total_bookings=30,
                completed_sessions=25,
                daily_revenue=Decimal("2500.00")
            ),
            tutor_leaderboard=[],
            revenue_trends={'daily_trends': [], 'total_revenue': 75000.0},
            user_growth={'new_users_trend': [], 'total_new_users': 150},
            geographic_insights={'top_areas': [], 'total_areas': 10},
            popular_subjects=[{'subject': 'Math', 'session_count': 300}],
            peak_usage_times=[{'hour': 15, 'session_count': 125}]
        )
        
        with patch('app.api.v1.analytics.get_current_user', return_value=mock_current_user):
            with patch('app.api.v1.analytics.get_analytics_service') as mock_service:
                mock_service.return_value.get_platform_dashboard = AsyncMock(
                    return_value=mock_dashboard
                )
                
                response = test_client.get(
                    "/api/v1/analytics/platform/dashboard?days=30",
                    headers={"Authorization": "Bearer fake-token"}
                )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data['platform_metrics']['active_clients'] == 50
        assert data['revenue_trends']['total_revenue'] == 75000.0
    
    @pytest.mark.asyncio
    async def test_calculate_platform_metrics(
        self, test_client, mock_current_user
    ):
        """Test manually triggering platform metrics calculation."""
        calculated_metrics = PlatformAnalytics(
            analytics_id=1,
            metric_date=date.today() - timedelta(days=1),
            metric_type=MetricType.USER_ACTIVITY,
            active_clients=50,
            active_tutors=20,
            active_students=45,
            new_registrations=5,
            total_bookings=30,
            completed_sessions=25,
            daily_revenue=Decimal("2500.00")
        )
        
        with patch('app.api.v1.analytics.get_current_user', return_value=mock_current_user):
            with patch('app.api.v1.analytics.get_analytics_service') as mock_service:
                mock_service.return_value.calculate_daily_platform_metrics = AsyncMock(
                    return_value=calculated_metrics
                )
                
                response = test_client.post(
                    "/api/v1/analytics/platform/metrics/calculate",
                    headers={"Authorization": "Bearer fake-token"}
                )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data['success'] is True
        assert 'metrics' in data


class TestAnalyticsExportEndpoints:
    """Test analytics export endpoints."""
    
    @pytest.mark.asyncio
    async def test_export_tutor_performance_not_implemented(
        self, test_client, mock_current_user
    ):
        """Test export tutor performance (not yet implemented)."""
        with patch('app.api.v1.analytics.get_current_user', return_value=mock_current_user):
            response = test_client.get(
                "/api/v1/analytics/export/tutor-performance?period_type=monthly&format=csv",
                headers={"Authorization": "Bearer fake-token"}
            )
        
        assert response.status_code == status.HTTP_501_NOT_IMPLEMENTED
        assert "not yet implemented" in response.json()['detail'].lower()
    
    @pytest.mark.asyncio
    async def test_export_student_progress_not_implemented(
        self, test_client, mock_current_user
    ):
        """Test export student progress (not yet implemented)."""
        start_date = date.today() - timedelta(days=30)
        end_date = date.today()
        
        with patch('app.api.v1.analytics.get_current_user', return_value=mock_current_user):
            response = test_client.get(
                f"/api/v1/analytics/export/student-progress?start_date={start_date.isoformat()}&end_date={end_date.isoformat()}&format=csv",
                headers={"Authorization": "Bearer fake-token"}
            )
        
        assert response.status_code == status.HTTP_501_NOT_IMPLEMENTED
        assert "not yet implemented" in response.json()['detail'].lower()