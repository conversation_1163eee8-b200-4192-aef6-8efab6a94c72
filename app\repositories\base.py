"""
Base Repository - Common functionality for all repositories
"""
from typing import Optional, Dict, Any
from loguru import logger

from app.database import Database


class BaseRepository:
    """Base class for all repositories"""
    
    def __init__(self, db: Database):
        self.db = db
    
    async def execute(self, query: str, *args) -> None:
        """Execute a query without returning results"""
        await self.db.execute(query, *args)
    
    async def fetchrow(self, query: str, *args) -> Optional[Dict[str, Any]]:
        """Fetch a single row"""
        return await self.db.fetchrow(query, *args)
    
    async def fetch(self, query: str, *args) -> list:
        """Fetch multiple rows"""
        return await self.db.fetch(query, *args)
    
    async def fetchval(self, query: str, *args) -> Any:
        """Fetch a single value"""
        return await self.db.fetchval(query, *args)
    
    async def transaction(self):
        """Get a transaction context"""
        return self.db.transaction()
    
    def log_error(self, message: str, **kwargs):
        """Log an error with context"""
        logger.error(f"{self.__class__.__name__}: {message}", **kwargs)
    
    def log_info(self, message: str, **kwargs):
        """Log info with context"""
        logger.info(f"{self.__class__.__name__}: {message}", **kwargs)
    
    def log_debug(self, message: str, **kwargs):
        """Log debug with context"""
        logger.debug(f"{self.__class__.__name__}: {message}", **kwargs)