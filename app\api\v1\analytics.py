"""
API endpoints for analytics and performance tracking.

Provides dashboards and metrics for tutors, students, and platform administrators.
"""

from datetime import date, datetime
from typing import List, Optional, Any
from fastapi import APIRouter, Depends, HTTPException, Query, status
from pydantic import BaseModel, Field

from app.core.dependencies import (
    get_current_user, get_db_manager, get_analytics_service
)
from app.core.auth_decorators import require_roles
from app.models.user_models import User, UserRoleType
from app.models.analytics_models import (
    TutorPerformanceMetrics, TutorPerformanceRequest, TutorLeaderboardEntry,
    StudentLearningGoal, StudentLearningGoalCreate, StudentProgressRequest, StudentProgressSummary,
    SessionFeedback, SessionFeedbackCreate,
    PlatformAnalytics, PlatformAnalyticsRequest,
    TutorDashboardData, StudentDashboardData, PlatformDashboardData,
    PeriodType, GoalStatus
)
from app.services.analytics_service import AnalyticsService
from app.core.exceptions import ResourceNotFoundError, ValidationError
from app.core.logging import TutorAideLogger

logger = TutorAideLogger(__name__)

router = APIRouter(prefix="/analytics", tags=["analytics"])


# =====================================================
# HELPER FUNCTIONS
# =====================================================

async def check_student_access(
    user: User,
    student_id: int,
    conn: Any
) -> bool:
    """
    Check if user has access to student data.
    
    Returns True if:
    - User is a manager
    - User is the student themselves
    - User is a parent of the student (if student is a dependant)
    - User is a tutor assigned to the student
    """
    # Managers have full access
    if UserRoleType.MANAGER in user.roles:
        return True
    
    # Check if user is the student
    if UserRoleType.CLIENT in user.roles:
        client_query = """
            SELECT user_id FROM client_profiles 
            WHERE client_id = $1 AND deleted_at IS NULL
        """
        client_user_id = await conn.fetchval(client_query, student_id)
        if client_user_id == user.user_id:
            return True
    
    # Check if user is parent of student (if student is a dependant)
    if UserRoleType.CLIENT in user.roles:
        parent_query = """
            SELECT COUNT(*)
            FROM dependants d
            JOIN client_profiles cp ON (
                d.primary_client_id = cp.client_id OR 
                d.secondary_client_id = cp.client_id
            )
            WHERE d.dependant_id = $1 
            AND cp.user_id = $2
            AND d.deleted_at IS NULL
        """
        count = await conn.fetchval(parent_query, student_id, user.user_id)
        if count > 0:
            return True
    
    # Check if user is a tutor assigned to this student
    if UserRoleType.TUTOR in user.roles:
        tutor_query = """
            SELECT COUNT(DISTINCT a.appointment_id)
            FROM appointments a
            JOIN tutor_profiles tp ON a.tutor_id = tp.tutor_id
            WHERE tp.user_id = $1 
            AND a.client_id = $2
            AND a.appointment_date >= CURRENT_DATE - INTERVAL '90 days'
            AND a.deleted_at IS NULL
        """
        count = await conn.fetchval(tutor_query, user.user_id, student_id)
        if count > 0:
            return True
    
    return False


# =====================================================
# TUTOR ANALYTICS ENDPOINTS
# =====================================================

@router.get("/tutor/dashboard", response_model=TutorDashboardData)
async def get_tutor_dashboard(
    tutor_id: Optional[int] = None,
    current_user: User = Depends(get_current_user),
    analytics_service: AnalyticsService = Depends(get_analytics_service)
):
    """
    Get complete dashboard data for a tutor.
    
    Tutors can only view their own dashboard.
    Managers can view any tutor's dashboard.
    """
    # Determine which tutor's dashboard to show
    if tutor_id is None:
        # Default to current user if they're a tutor
        if UserRoleType.TUTOR not in current_user.roles:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="tutor_id is required for non-tutor users"
            )
        tutor_id = current_user.user_id
    else:
        # Check permissions if viewing another tutor's dashboard
        if (UserRoleType.MANAGER not in current_user.roles and 
            tutor_id != current_user.user_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You can only view your own dashboard"
            )
    
    try:
        dashboard_data = await analytics_service.get_tutor_dashboard(tutor_id)
        return dashboard_data
    except ResourceNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error getting tutor dashboard: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve dashboard data"
        )


@router.get("/tutor/performance", response_model=TutorPerformanceMetrics)
async def get_tutor_performance(
    tutor_id: int,
    period_type: PeriodType = PeriodType.MONTHLY,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    current_user: User = Depends(get_current_user),
    analytics_service: AnalyticsService = Depends(get_analytics_service)
):
    """
    Get performance metrics for a specific tutor and period.
    
    Accessible by the tutor themselves or managers.
    """
    # Check permissions
    if (UserRoleType.MANAGER not in current_user.roles and 
        tutor_id != current_user.user_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You can only view your own performance metrics"
        )
    
    try:
        metrics = await analytics_service.calculate_tutor_metrics(
            tutor_id, period_type, start_date, end_date
        )
        return metrics
    except Exception as e:
        logger.error(f"Error getting tutor performance: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to calculate performance metrics"
        )


@router.get("/tutor/leaderboard", response_model=List[TutorLeaderboardEntry])
async def get_tutor_leaderboard(
    period_type: PeriodType = PeriodType.MONTHLY,
    limit: int = Query(10, ge=1, le=50),
    current_user: User = Depends(require_roles([UserRoleType.MANAGER, UserRoleType.TUTOR])),
    analytics_service: AnalyticsService = Depends(get_analytics_service)
):
    """
    Get tutor leaderboard rankings.
    
    Shows top performing tutors by various metrics.
    """
    try:
        leaderboard = await analytics_service.get_tutor_leaderboard(
            period_type, limit
        )
        return leaderboard
    except Exception as e:
        logger.error(f"Error getting tutor leaderboard: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve leaderboard"
        )


# =====================================================
# STUDENT ANALYTICS ENDPOINTS
# =====================================================

@router.get("/student/dashboard", response_model=StudentDashboardData)
async def get_student_dashboard(
    student_id: Optional[int] = None,
    current_user: User = Depends(get_current_user),
    analytics_service: AnalyticsService = Depends(get_analytics_service)
):
    """
    Get complete dashboard data for a student.
    
    Students/clients can view their own or their dependants' dashboards.
    Tutors can view their students' dashboards.
    Managers can view any student's dashboard.
    """
    # Determine which student's dashboard to show
    if student_id is None:
        # Default to current user if they're a client/student
        if UserRoleType.CLIENT not in current_user.roles:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="student_id is required for non-student users"
            )
        # Get the client_id for the current user
        db_manager = await get_db_manager()
        async with db_manager.acquire() as conn:
            client_query = """
                SELECT client_id FROM client_profiles 
                WHERE user_id = $1 AND deleted_at IS NULL
            """
            student_id = await conn.fetchval(client_query, current_user.user_id)
            if not student_id:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Client profile not found"
                )
    
    # Check permissions
    db_manager = await get_db_manager()
    async with db_manager.acquire() as conn:
        has_access = await check_student_access(current_user, student_id, conn)
        if not has_access:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to view this dashboard"
            )
    
    try:
        dashboard_data = await analytics_service.get_student_dashboard(student_id)
        return dashboard_data
    except ResourceNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error getting student dashboard: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve dashboard data"
        )


@router.get("/student/{student_id}/progress", response_model=List[StudentProgressSummary])
async def get_student_progress(
    student_id: int,
    subject_area: Optional[str] = None,
    days: int = Query(30, ge=7, le=365),
    current_user: User = Depends(get_current_user),
    analytics_service: AnalyticsService = Depends(get_analytics_service)
):
    """
    Get progress summary for a student.
    
    Shows progress across subjects with goals and metrics.
    """
    # Check permissions
    db_manager = await get_db_manager()
    async with db_manager.acquire() as conn:
        has_access = await check_student_access(current_user, student_id, conn)
        if not has_access:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to view this progress"
            )
    
    try:
        # Get progress using the repository method
        from app.config.database import get_db_manager
        db_manager = await get_db_manager()
        async with db_manager.acquire() as conn:
            progress_repo = analytics_service.student_progress_repo
            progress = await progress_repo.get_progress_summary(
                conn, student_id, days
            )
        
        # Filter by subject if specified
        if subject_area:
            progress = [p for p in progress if p.subject_area == subject_area]
        
        return progress
    except Exception as e:
        logger.error(f"Error getting student progress: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve progress data"
        )


@router.post("/student/{student_id}/goals", response_model=StudentLearningGoal)
async def create_learning_goal(
    student_id: int,
    goal_data: StudentLearningGoalCreate,
    current_user: User = Depends(get_current_user),
    analytics_service: AnalyticsService = Depends(get_analytics_service)
):
    """
    Create a new learning goal for a student.
    
    Can be created by the student, their parent, tutor, or manager.
    """
    # Check permissions
    db_manager = await get_db_manager()
    async with db_manager.acquire() as conn:
        has_access = await check_student_access(current_user, student_id, conn)
        if not has_access:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to create goals for this student"
            )
    
    try:
        goal = await analytics_service.create_learning_goal(
            student_id, goal_data.model_dump(), current_user.user_id
        )
        return goal
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error creating learning goal: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create learning goal"
        )


@router.put("/goals/{goal_id}/progress")
async def update_goal_progress(
    goal_id: int,
    progress_percentage: int = Query(..., ge=0, le=100),
    mark_complete: bool = False,
    current_user: User = Depends(get_current_user),
    analytics_service: AnalyticsService = Depends(get_analytics_service)
):
    """
    Update progress on a learning goal.
    
    Can be updated by tutors, managers, or the student.
    """
    try:
        # Get goal details to check ownership
        db_manager = await get_db_manager()
        async with db_manager.acquire() as conn:
            goal_query = """
                SELECT student_id, created_by, assigned_tutor_id
                FROM student_learning_goals
                WHERE goal_id = $1 AND deleted_at IS NULL
            """
            goal_info = await conn.fetchrow(goal_query, goal_id)
            
            if not goal_info:
                raise ResourceNotFoundError(f"Learning goal {goal_id} not found")
            
            # Check permissions
            allowed = False
            if UserRoleType.MANAGER in current_user.roles:
                allowed = True
            elif goal_info['created_by'] == current_user.user_id:
                allowed = True
            elif goal_info['assigned_tutor_id']:
                # Check if current user is the assigned tutor
                tutor_query = """
                    SELECT user_id FROM tutor_profiles 
                    WHERE tutor_id = $1 AND deleted_at IS NULL
                """
                tutor_user_id = await conn.fetchval(tutor_query, goal_info['assigned_tutor_id'])
                if tutor_user_id == current_user.user_id:
                    allowed = True
            else:
                # Check general student access
                allowed = await check_student_access(
                    current_user, goal_info['student_id'], conn
                )
            
            if not allowed:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="You don't have permission to update this goal"
                )
        
        goal = await analytics_service.update_goal_progress(
            goal_id, progress_percentage, mark_complete
        )
        return goal
    except ResourceNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error updating goal progress: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update goal progress"
        )


# =====================================================
# SESSION FEEDBACK ENDPOINTS
# =====================================================

@router.post("/appointments/{appointment_id}/feedback", response_model=SessionFeedback)
async def submit_session_feedback(
    appointment_id: int,
    feedback: SessionFeedbackCreate,
    current_user: User = Depends(get_current_user),
    analytics_service: AnalyticsService = Depends(get_analytics_service)
):
    """
    Submit feedback for a completed session.
    
    Can be submitted by participants in the session.
    """
    try:
        session_feedback = await analytics_service.submit_session_feedback(
            appointment_id, current_user.user_id, feedback
        )
        return session_feedback
    except ResourceNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error submitting feedback: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to submit feedback"
        )


@router.get("/feedback/pending", response_model=List[SessionFeedback])
async def get_pending_feedback(
    limit: int = Query(20, ge=1, le=100),
    current_user: User = Depends(require_roles([UserRoleType.MANAGER])),
    analytics_service: AnalyticsService = Depends(get_analytics_service)
):
    """
    Get feedback items requiring followup.
    
    Only accessible by managers.
    """
    try:
        from app.config.database import get_db_manager
        db_manager = await get_db_manager()
        async with db_manager.acquire() as conn:
            feedback_repo = analytics_service.feedback_repo
            pending = await feedback_repo.get_pending_followups(conn, limit)
        return pending
    except Exception as e:
        logger.error(f"Error getting pending feedback: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve pending feedback"
        )


# =====================================================
# PLATFORM ANALYTICS ENDPOINTS (MANAGER ONLY)
# =====================================================

@router.get("/platform/dashboard", response_model=PlatformDashboardData)
async def get_platform_dashboard(
    days: int = Query(30, ge=7, le=365),
    current_user: User = Depends(require_roles([UserRoleType.MANAGER])),
    analytics_service: AnalyticsService = Depends(get_analytics_service)
):
    """
    Get platform-wide analytics dashboard.
    
    Only accessible by managers.
    """
    try:
        dashboard_data = await analytics_service.get_platform_dashboard(days)
        return dashboard_data
    except Exception as e:
        logger.error(f"Error getting platform dashboard: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve platform dashboard"
        )


@router.get("/platform/metrics", response_model=List[PlatformAnalytics])
async def get_platform_metrics(
    start_date: date,
    end_date: date,
    metric_type: Optional[str] = None,
    current_user: User = Depends(require_roles([UserRoleType.MANAGER])),
    analytics_service: AnalyticsService = Depends(get_analytics_service)
):
    """
    Get detailed platform metrics for a date range.
    
    Only accessible by managers.
    """
    try:
        from app.config.database import get_db_manager
        from app.models.analytics_models import MetricType
        
        db_manager = await get_db_manager()
        async with db_manager.acquire() as conn:
            platform_repo = analytics_service.platform_repo
            
            # Convert metric_type string to enum if provided
            metric_enum = None
            if metric_type:
                try:
                    metric_enum = MetricType(metric_type)
                except ValueError:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Invalid metric type: {metric_type}"
                    )
            
            metrics = await platform_repo.get_metrics_range(
                conn, start_date, end_date, metric_enum
            )
        return metrics
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting platform metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve platform metrics"
        )


@router.post("/platform/metrics/calculate")
async def calculate_platform_metrics(
    metric_date: Optional[date] = None,
    current_user: User = Depends(require_roles([UserRoleType.MANAGER])),
    analytics_service: AnalyticsService = Depends(get_analytics_service)
):
    """
    Manually trigger platform metrics calculation.
    
    Only accessible by managers.
    """
    try:
        metrics = await analytics_service.calculate_daily_platform_metrics(metric_date)
        return {
            "success": True,
            "message": f"Calculated metrics for {metrics.metric_date}",
            "metrics": metrics
        }
    except Exception as e:
        logger.error(f"Error calculating platform metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to calculate platform metrics"
        )


# =====================================================
# ANALYTICS EXPORT ENDPOINTS
# =====================================================

from enum import Enum

class ExportFormat(str, Enum):
    CSV = "csv"
    EXCEL = "excel"
    JSON = "json"


@router.get("/export/tutor-performance")
async def export_tutor_performance(
    period_type: PeriodType = PeriodType.MONTHLY,
    format: ExportFormat = ExportFormat.CSV,
    current_user: User = Depends(require_roles([UserRoleType.MANAGER])),
    analytics_service: AnalyticsService = Depends(get_analytics_service)
):
    """
    Export tutor performance data.
    
    Only accessible by managers.
    """
    # TODO: Implement export functionality
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="Export functionality not yet implemented"
    )


@router.get("/export/student-progress")
async def export_student_progress(
    start_date: date,
    end_date: date,
    format: ExportFormat = ExportFormat.CSV,
    current_user: User = Depends(require_roles([UserRoleType.MANAGER])),
    analytics_service: AnalyticsService = Depends(get_analytics_service)
):
    """
    Export student progress data.
    
    Only accessible by managers.
    """
    # TODO: Implement export functionality
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="Export functionality not yet implemented"
    )