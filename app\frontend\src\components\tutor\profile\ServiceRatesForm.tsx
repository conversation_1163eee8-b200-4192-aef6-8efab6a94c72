import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Card } from '../../common/Card';
import Button from '../../common/Button';
import { Input } from '../../common/Input';
import { DollarSign, Plus, Trash2, Save, AlertCircle } from 'lucide-react';
import toast from 'react-hot-toast';

interface ServiceRate {
  rateId?: number;
  serviceId: number;
  serviceName: string;
  subjectArea: string;
  hourlyRate: number | string;
  isEditing?: boolean;
}

interface ServiceRatesFormProps {
  tutorId: number;
}

export const ServiceRatesForm: React.FC<ServiceRatesFormProps> = ({ tutorId }) => {
  const { t } = useTranslation();
  const [rates, setRates] = useState<ServiceRate[]>([]);
  const [availableServices, setAvailableServices] = useState<ServiceRate[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Mock data - replace with API calls
  useEffect(() => {
    const mockServices = [
      { serviceId: 1, serviceName: 'Mathematics Tutoring', subjectArea: 'mathematics' },
      { serviceId: 2, serviceName: 'Science Tutoring', subjectArea: 'science' },
      { serviceId: 3, serviceName: 'English Tutoring', subjectArea: 'english' },
      { serviceId: 4, serviceName: 'French Tutoring', subjectArea: 'french' },
      { serviceId: 5, serviceName: 'Computer Science', subjectArea: 'other' },
    ];

    const mockRates = [
      { rateId: 1, serviceId: 1, serviceName: 'Mathematics Tutoring', subjectArea: 'mathematics', hourlyRate: 45 },
      { rateId: 2, serviceId: 3, serviceName: 'English Tutoring', subjectArea: 'english', hourlyRate: 40 },
    ];

    setRates(mockRates);
    
    // Filter out services that already have rates
    const usedServiceIds = mockRates.map(r => r.serviceId);
    setAvailableServices(
      mockServices.filter(s => !usedServiceIds.includes(s.serviceId))
    );
  }, [tutorId]);

  const handleAddService = (service: ServiceRate) => {
    setRates([...rates, { ...service, hourlyRate: '', isEditing: true }]);
    setAvailableServices(availableServices.filter(s => s.serviceId !== service.serviceId));
  };

  const handleRemoveRate = (index: number) => {
    const removedRate = rates[index];
    setRates(rates.filter((_, i) => i !== index));
    
    if (removedRate.serviceId) {
      setAvailableServices([...availableServices, {
        serviceId: removedRate.serviceId,
        serviceName: removedRate.serviceName,
        subjectArea: removedRate.subjectArea,
        hourlyRate: 0
      }]);
    }
  };

  const handleRateChange = (index: number, value: string) => {
    const updatedRates = [...rates];
    updatedRates[index].hourlyRate = value;
    setRates(updatedRates);
  };

  const handleSave = async () => {
    // Validate rates
    const invalidRates = rates.filter(r => !r.hourlyRate || parseFloat(r.hourlyRate.toString()) <= 0);
    if (invalidRates.length > 0) {
      toast.error(t('tutor.rates.errors.invalidRates'));
      return;
    }

    setIsSaving(true);
    try {
      // TODO: Implement API call to save rates
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success(t('tutor.rates.saveSuccess'));
      
      // Mark all as not editing
      setRates(rates.map(r => ({ ...r, isEditing: false })));
    } catch (error) {
      toast.error(t('tutor.rates.saveError'));
    } finally {
      setIsSaving(false);
    }
  };

  const formatCurrency = (value: number | string) => {
    const num = typeof value === 'string' ? parseFloat(value) : value;
    return isNaN(num) ? '' : `$${num.toFixed(2)}`;
  };

  const getSubjectIcon = (subjectArea: string) => {
    const baseClasses = 'w-10 h-10 rounded-full flex items-center justify-center text-white font-semibold';
    switch (subjectArea) {
      case 'mathematics':
        return <div className={`${baseClasses} bg-blue-500`}>M</div>;
      case 'science':
        return <div className={`${baseClasses} bg-green-500`}>S</div>;
      case 'english':
        return <div className={`${baseClasses} bg-purple-500`}>E</div>;
      case 'french':
        return <div className={`${baseClasses} bg-pink-500`}>F</div>;
      default:
        return <div className={`${baseClasses} bg-gray-500`}>O</div>;
    }
  };

  return (
    <div className="space-y-6">
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">
            {t('tutor.rates.title')}
          </h3>
          <DollarSign className="h-5 w-5 text-gray-400" />
        </div>
        
        <p className="text-sm text-gray-500 mb-6">
          {t('tutor.rates.subtitle')}
        </p>

        {/* Current Rates */}
        <div className="space-y-4 mb-6">
          {rates.map((rate, index) => (
            <div key={`${rate.serviceId}-${index}`} className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
              {getSubjectIcon(rate.subjectArea)}
              <div className="flex-1">
                <p className="font-medium text-gray-900">{rate.serviceName}</p>
                <p className="text-sm text-gray-500">
                  {t(`tutor.subjects.${rate.subjectArea}`)}
                </p>
              </div>
              <div className="flex items-center gap-2">
                <div className="relative">
                  <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                    $
                  </span>
                  <Input
                    type="number"
                    min="0"
                    step="0.01"
                    value={rate.hourlyRate}
                    onChange={(e) => handleRateChange(index, e.target.value)}
                    className="w-32 pl-8"
                    placeholder="0.00"
                  />
                </div>
                <span className="text-sm text-gray-500">{t('tutor.rates.perHour')}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleRemoveRate(index)}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>

        {/* Add Service Section */}
        {availableServices.length > 0 && (
          <div className="border-t pt-6">
            <h4 className="text-sm font-medium text-gray-900 mb-3">
              {t('tutor.rates.addService')}
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {availableServices.map((service) => (
                <button
                  key={service.serviceId}
                  onClick={() => handleAddService(service)}
                  className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"
                >
                  {getSubjectIcon(service.subjectArea)}
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">
                      {service.serviceName}
                    </p>
                    <p className="text-xs text-gray-500">
                      {t(`tutor.subjects.${service.subjectArea}`)}
                    </p>
                  </div>
                  <Plus className="h-4 w-4 text-gray-400" />
                </button>
              ))}
            </div>
          </div>
        )}

        {rates.length === 0 && (
          <div className="text-center py-8">
            <DollarSign className="h-12 w-12 text-gray-300 mx-auto mb-3" />
            <p className="text-gray-500">{t('tutor.rates.noRates')}</p>
            <p className="text-sm text-gray-400 mt-1">
              {t('tutor.rates.noRatesDescription')}
            </p>
          </div>
        )}
      </Card>

      {/* Info Box */}
      <Card className="p-4 bg-blue-50 border-blue-200">
        <div className="flex">
          <AlertCircle className="h-5 w-5 text-blue-600 flex-shrink-0 mt-0.5" />
          <div className="ml-3">
            <p className="text-sm text-blue-800">
              {t('tutor.rates.info')}
            </p>
          </div>
        </div>
      </Card>

      {/* Save Button */}
      {rates.length > 0 && (
        <div className="flex justify-end">
          <Button
            onClick={handleSave}
            disabled={isSaving}
            className="flex items-center gap-2"
          >
            <Save className="h-4 w-4" />
            {isSaving ? t('common.saving') : t('common.save')}
          </Button>
        </div>
      )}
    </div>
  );
};