import api from './api';

export interface Conversation {
  conversation_id: number;
  conversation_type: 'sms' | 'in_app' | 'support';
  client_id?: number;
  tutor_id?: number;
  manager_id?: number;
  phone_number?: string;
  subject?: string;
  status: 'active' | 'closed' | 'archived';
  last_message_at?: string;
  unread_count: number;
  created_at: string;
  client_first_name?: string;
  client_last_name?: string;
  tutor_first_name?: string;
  tutor_last_name?: string;
  manager_email?: string;
  last_message_content?: string;
  last_message_sender?: string;
}

export interface Message {
  message_id: number;
  conversation_id: number;
  sender_id: number;
  sender_type: 'client' | 'tutor' | 'manager' | 'system';
  content: string;
  message_type: 'text' | 'file' | 'system';
  status: 'sent' | 'delivered' | 'read' | 'failed';
  created_at: string;
  updated_at?: string;
  file_url?: string;
  file_name?: string;
  sender_name?: string;
}

export interface MessageTemplate {
  template_id: number;
  template_name: string;
  template_type: 'sms' | 'email' | 'in_app';
  category: string;
  subject?: string;
  content_en: string;
  content_fr: string;
  variables?: Record<string, any>;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Broadcast {
  broadcast_id: number;
  title: string;
  message_type: 'sms' | 'email' | 'push' | 'in_app';
  content_en: string;
  content_fr: string;
  recipient_type: string;
  recipient_filters?: Record<string, any>;
  status: 'draft' | 'scheduled' | 'sending' | 'sent' | 'failed';
  scheduled_at?: string;
  sent_at?: string;
  total_recipients?: number;
  successful_sends?: number;
  failed_sends?: number;
  created_at: string;
  created_by: number;
}

class MessagingService {
  // Conversation Management
  async getConversations(params?: {
    conversation_type?: string;
    status?: string;
    limit?: number;
    offset?: number;
  }): Promise<{
    conversations: Conversation[];
    total: number;
  }> {
    const response = await api.get('/api/v1/messaging/conversations', {
      params,
    });
    return response.data;
  }

  async createConversation(data: {
    conversation_type: string;
    client_id?: number;
    tutor_id?: number;
    phone_number?: string;
    subject?: string;
  }): Promise<{ conversation_id: number }> {
    const response = await api.post('/api/v1/messaging/conversations', data);
    return response.data;
  }

  async getConversationMessages(
    conversationId: number,
    params?: {
      limit?: number;
      offset?: number;
    }
  ): Promise<{ messages: Message[] }> {
    const response = await api.get(
      `/api/v1/messaging/conversations/${conversationId}/messages`,
      { params }
    );
    return response.data;
  }

  async sendMessage(
    conversationId: number,
    data: {
      content: string;
      message_type?: string;
    }
  ): Promise<{ message: Message }> {
    const response = await api.post(
      `/api/v1/messaging/conversations/${conversationId}/messages`,
      data
    );
    return response.data;
  }

  async uploadAttachment(
    conversationId: number,
    file: File
  ): Promise<{ 
    message: Message;
    file_url: string; 
    file_name: string;
    file_size?: number;
  }> {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await api.post(
      `/api/v1/messaging/conversations/${conversationId}/attachments`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return response.data;
  }

  async markMessagesAsRead(
    conversationId: number,
    messageIds: number[]
  ): Promise<{ success: boolean; marked_count: number }> {
    const response = await api.post(
      `/api/v1/messaging/conversations/${conversationId}/messages/read`,
      { message_ids: messageIds }
    );
    return response.data;
  }

  // Message Templates
  async getTemplates(params?: {
    template_type?: string;
    category?: string;
    is_active?: boolean;
  }): Promise<{ templates: MessageTemplate[] }> {
    const response = await api.get('/api/v1/messaging/templates', {
      params,
    });
    return response.data;
  }

  async createTemplate(data: {
    template_name: string;
    template_type: string;
    category: string;
    subject?: string;
    content_en: string;
    content_fr: string;
    variables?: Record<string, any>;
  }): Promise<{ template_id: number }> {
    const response = await api.post('/api/v1/messaging/templates', data);
    return response.data;
  }

  async updateTemplate(
    templateId: number,
    data: Partial<MessageTemplate>
  ): Promise<{ success: boolean }> {
    const response = await api.put(
      `/api/v1/messaging/templates/${templateId}`,
      data
    );
    return response.data;
  }

  async deleteTemplate(templateId: number): Promise<{ success: boolean }> {
    const response = await api.delete(
      `/api/v1/messaging/templates/${templateId}`
    );
    return response.data;
  }

  // Broadcasts
  async getBroadcasts(params?: {
    status?: string;
    limit?: number;
    offset?: number;
  }): Promise<{
    broadcasts: Broadcast[];
    total: number;
  }> {
    const response = await api.get('/api/v1/messaging/broadcasts', {
      params,
    });
    return response.data;
  }

  async createBroadcast(data: {
    title: string;
    message_type: string;
    content_en: string;
    content_fr: string;
    recipient_type: string;
    recipient_filters?: Record<string, any>;
    scheduled_at?: string;
  }): Promise<{ broadcast_id: number }> {
    const response = await api.post('/api/v1/messaging/broadcasts', data);
    return response.data;
  }

  async sendBroadcast(broadcastId: number): Promise<{ success: boolean }> {
    const response = await api.post(
      `/api/v1/messaging/broadcasts/${broadcastId}/send`
    );
    return response.data;
  }

  // WebSocket Status
  async getMessagingStatus(): Promise<{
    user_online: boolean;
    total_online_users: number;
    service_status: string;
  }> {
    const response = await api.get('/api/v1/ws/status');
    return response.data;
  }

  async getOnlineParticipants(conversationId: number): Promise<{
    conversation_id: number;
    online_participants: number[];
    total_online: number;
    total_participants: number;
  }> {
    const response = await api.get(
      `/api/v1/ws/conversations/${conversationId}/participants/online`
    );
    return response.data;
  }

  // Search messages
  async searchMessages(params: {
    query: string;
    conversation_id?: number;
    sender_type?: string;
    date_from?: string;
    date_to?: string;
    limit?: number;
    offset?: number;
  }): Promise<{
    results: Message[];
    total: number;
  }> {
    const response = await api.get('/api/v1/messaging/search', {
      params,
    });
    return response.data;
  }
}

export default new MessagingService();