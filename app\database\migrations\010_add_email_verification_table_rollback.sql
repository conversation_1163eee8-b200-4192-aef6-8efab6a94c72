-- Rollback Migration: Remove email verification tokens table
-- Description: Remove email verification table and columns

-- Drop trigger and function
DROP TRIGGER IF EXISTS trigger_email_verification_tokens_updated_at ON email_verification_tokens;
DROP FUNCTION IF EXISTS update_email_verification_tokens_updated_at();

-- Drop indexes
DROP INDEX IF EXISTS idx_email_verification_tokens_user_id;
DROP INDEX IF EXISTS idx_email_verification_tokens_token;
DROP INDEX IF EXISTS idx_email_verification_tokens_email;
DROP INDEX IF EXISTS idx_email_verification_tokens_expires_at;
DROP INDEX IF EXISTS idx_user_accounts_email_verified;

-- Remove columns from user_accounts
ALTER TABLE user_accounts 
DROP COLUMN IF EXISTS email_verified,
DROP COLUMN IF EXISTS email_verified_at;

-- Drop email_verification_tokens table
DROP TABLE IF EXISTS email_verification_tokens;