-- Add appointment completion and SMS confirmation fields
-- Version: 019
-- Description: Adds fields for appointment completion workflow and SMS confirmation
-- Author: TutorAide Development Team
-- Date: 2025-01-14

-- Add completion fields to appointment_sessions
ALTER TABLE appointment_sessions
    ADD COLUMN IF NOT EXISTS completed_at TIMESTAMP WITH TIME ZONE,
    ADD COLUMN IF NOT EXISTS completed_by INTEGER REFERENCES user_accounts(user_id),
    ADD COLUMN IF NOT EXISTS tutor_no_show BOOLEAN DEFAULT FALSE,
    ADD COLUMN IF NOT EXISTS client_no_show BOOLEAN DEFAULT FALSE,
    ADD COLUMN IF NOT EXISTS sms_confirmed BOOLEAN DEFAULT FALSE,
    ADD COLUMN IF NOT EXISTS sms_confirmed_at TIMESTAMP WITH TIME ZONE;

-- Add missing client_id column if not exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'appointment_sessions' 
                  AND column_name = 'client_id') THEN
        ALTER TABLE appointment_sessions 
        ADD COLUMN client_id INTEGER NOT NULL REFERENCES client_profiles(client_id);
    END IF;
END $$;

-- Add missing dependant_id column if not exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'appointment_sessions' 
                  AND column_name = 'dependant_id') THEN
        ALTER TABLE appointment_sessions 
        ADD COLUMN dependant_id INTEGER REFERENCES client_dependants(dependant_id);
    END IF;
END $$;

-- Add missing subject_area column if not exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'appointment_sessions' 
                  AND column_name = 'subject_area') THEN
        ALTER TABLE appointment_sessions 
        ADD COLUMN subject_area VARCHAR(100) NOT NULL DEFAULT 'General';
    END IF;
END $$;

-- Add missing hourly_rate column if not exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'appointment_sessions' 
                  AND column_name = 'hourly_rate') THEN
        ALTER TABLE appointment_sessions 
        ADD COLUMN hourly_rate DECIMAL(10,2) NOT NULL DEFAULT 50.00;
    END IF;
END $$;

-- Add missing currency column if not exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'appointment_sessions' 
                  AND column_name = 'currency') THEN
        ALTER TABLE appointment_sessions 
        ADD COLUMN currency CHAR(3) NOT NULL DEFAULT 'CAD';
    END IF;
END $$;

-- Create table for SMS confirmation codes
CREATE TABLE IF NOT EXISTS appointment_sms_confirmations (
    confirmation_id SERIAL PRIMARY KEY,
    appointment_id INTEGER NOT NULL REFERENCES appointment_sessions(appointment_id) ON DELETE CASCADE,
    confirmation_code VARCHAR(10) NOT NULL,
    sent_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(appointment_id, confirmation_code)
);

-- Create billing invoice items table if not exists
CREATE TABLE IF NOT EXISTS billing_invoice_items (
    item_id SERIAL PRIMARY KEY,
    appointment_id INTEGER UNIQUE REFERENCES appointment_sessions(appointment_id),
    description TEXT NOT NULL,
    quantity DECIMAL(10,2) NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    currency CHAR(3) NOT NULL DEFAULT 'CAD',
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_appointments_completed_at ON appointment_sessions(completed_at) WHERE completed_at IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_appointments_sms_confirmed ON appointment_sessions(sms_confirmed) WHERE sms_confirmed = true;
CREATE INDEX IF NOT EXISTS idx_sms_confirmations_expires ON appointment_sms_confirmations(expires_at) WHERE used_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_billing_items_status ON billing_invoice_items(status) WHERE status = 'pending';

-- Update the appointment status check constraint to include in_progress
ALTER TABLE appointment_sessions DROP CONSTRAINT IF EXISTS appointment_sessions_status_check;
ALTER TABLE appointment_sessions ADD CONSTRAINT appointment_sessions_status_check 
    CHECK (status IN ('scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show'));

-- Add comments for documentation
COMMENT ON COLUMN appointment_sessions.completed_at IS 'Timestamp when appointment was completed';
COMMENT ON COLUMN appointment_sessions.completed_by IS 'User who marked the appointment as completed';
COMMENT ON COLUMN appointment_sessions.tutor_no_show IS 'Whether the tutor was a no-show';
COMMENT ON COLUMN appointment_sessions.client_no_show IS 'Whether the client was a no-show';
COMMENT ON COLUMN appointment_sessions.sms_confirmed IS 'Whether tutor confirmed via SMS';
COMMENT ON COLUMN appointment_sessions.sms_confirmed_at IS 'When tutor confirmed via SMS';

COMMENT ON TABLE appointment_sms_confirmations IS 'SMS confirmation codes for appointment verification';
COMMENT ON TABLE billing_invoice_items IS 'Line items for billing based on completed appointments';