"""
Analytics and performance tracking models.

Defines structures for tutor performance, student progress, and platform analytics.
"""

from datetime import date, datetime
from decimal import Decimal
from typing import List, Optional, Dict, Any
from enum import Enum
from pydantic import BaseModel, Field, field_validator, ConfigDict

from app.models.base import BaseEntity, IdentifiedEntity


class PeriodType(str, Enum):
    """Analytics period types."""
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    YEARLY = "yearly"


class GoalStatus(str, Enum):
    """Learning goal statuses."""
    ACTIVE = "active"
    COMPLETED = "completed"
    PAUSED = "paused"
    CANCELLED = "cancelled"


class FeedbackRole(str, Enum):
    """Feedback role types."""
    TUTOR_TO_STUDENT = "tutor_to_student"
    STUDENT_TO_TUTOR = "student_to_tutor"
    PARENT_TO_TUTOR = "parent_to_tutor"


class MetricType(str, Enum):
    """Platform metric types."""
    USER_ACTIVITY = "user_activity"
    SESSION_METRICS = "session_metrics"
    FINANCIAL_METRICS = "financial_metrics"
    GEOGRAPHIC_METRICS = "geographic_metrics"


# =====================================================
# TUTOR PERFORMANCE MODELS
# =====================================================

class TutorPerformanceMetrics(IdentifiedEntity):
    """Tutor performance metrics for a specific period."""
    
    model_config = ConfigDict(from_attributes=True)
    
    metric_id: int
    tutor_id: int
    period_start: date
    period_end: date
    period_type: PeriodType
    
    # Session metrics
    total_sessions: int = 0
    completed_sessions: int = 0
    cancelled_sessions: int = 0
    no_show_sessions: int = 0
    
    # Rating metrics
    average_rating: Optional[Decimal] = None
    total_ratings: int = 0
    five_star_count: int = 0
    
    # Financial metrics
    total_revenue: Decimal = Decimal("0.00")
    total_hours: Decimal = Decimal("0.00")
    average_hourly_rate: Optional[Decimal] = None
    
    # Student metrics
    unique_students: int = 0
    returning_students: int = 0
    new_students: int = 0
    
    # Subject breakdown
    subject_breakdown: Dict[str, Any] = Field(default_factory=dict)
    
    @property
    def completion_rate(self) -> Optional[float]:
        """Calculate session completion rate."""
        if self.total_sessions > 0:
            return (self.completed_sessions / self.total_sessions) * 100
        return None
    
    @property
    def cancellation_rate(self) -> Optional[float]:
        """Calculate session cancellation rate."""
        if self.total_sessions > 0:
            return (self.cancelled_sessions / self.total_sessions) * 100
        return None


class TutorPerformanceRequest(BaseModel):
    """Request for tutor performance metrics."""
    tutor_id: int
    period_type: PeriodType
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    include_subject_breakdown: bool = True


class TutorLeaderboardEntry(BaseModel):
    """Entry in tutor leaderboard."""
    user_id: int
    first_name: str
    last_name: str
    profile_picture_url: Optional[str] = None
    average_rating: Optional[Decimal] = None
    total_sessions: int
    completed_sessions: int
    total_revenue: Decimal
    unique_students: int
    rating_rank: Optional[int] = None
    session_rank: Optional[int] = None
    revenue_rank: Optional[int] = None


# =====================================================
# STUDENT PROGRESS MODELS
# =====================================================

class StudentLearningGoal(IdentifiedEntity):
    """Student learning goal tracking."""
    
    model_config = ConfigDict(from_attributes=True)
    
    goal_id: int
    student_id: int
    subject_area: str
    goal_title: str = Field(..., min_length=1, max_length=200)
    goal_description: Optional[str] = None
    target_date: Optional[date] = None
    
    # Progress tracking
    status: GoalStatus = GoalStatus.ACTIVE
    progress_percentage: int = Field(default=0, ge=0, le=100)
    
    # Relationships
    created_by: Optional[int] = None
    assigned_tutor_id: Optional[int] = None
    
    # Timestamps
    completed_at: Optional[datetime] = None
    deleted_at: Optional[datetime] = None


class StudentProgressMetrics(BaseModel):
    """Student progress metrics for a subject."""
    
    model_config = ConfigDict(from_attributes=True)
    
    metric_id: int
    student_id: int
    subject_area: str
    period_start: date
    period_end: date
    
    # Session metrics
    total_sessions: int = 0
    attended_sessions: int = 0
    session_hours: Decimal = Decimal("0.00")
    
    # Performance indicators
    performance_score: Optional[Decimal] = None
    improvement_rate: Optional[Decimal] = None
    engagement_score: Optional[Decimal] = None
    
    # Milestone tracking
    milestones_achieved: int = 0
    milestones_total: int = 0
    
    # Tutor feedback
    avg_tutor_rating: Optional[Decimal] = None
    feedback_summary: Dict[str, Any] = Field(default_factory=dict)
    
    @property
    def attendance_rate(self) -> Optional[float]:
        """Calculate attendance rate."""
        if self.total_sessions > 0:
            return (self.attended_sessions / self.total_sessions) * 100
        return None
    
    @property
    def milestone_completion_rate(self) -> Optional[float]:
        """Calculate milestone completion rate."""
        if self.milestones_total > 0:
            return (self.milestones_achieved / self.milestones_total) * 100
        return None


class StudentProgressRequest(BaseModel):
    """Request for student progress data."""
    student_id: int
    subject_area: Optional[str] = None
    start_date: Optional[date] = None
    end_date: Optional[date] = None


class StudentProgressSummary(BaseModel):
    """Summary of student progress across subjects."""
    student_id: int
    subject_area: str
    attended_sessions: int
    session_hours: Decimal
    performance_score: Optional[Decimal] = None
    improvement_rate: Optional[Decimal] = None
    active_goals: int = 0
    completed_goals: int = 0


# =====================================================
# PLATFORM ANALYTICS MODELS
# =====================================================

class PlatformAnalytics(BaseModel):
    """Platform-wide analytics metrics."""
    
    model_config = ConfigDict(from_attributes=True)
    
    analytics_id: int
    metric_date: date
    metric_type: MetricType
    
    # User metrics
    active_clients: int = 0
    active_tutors: int = 0
    active_students: int = 0
    new_registrations: int = 0
    
    # Session metrics
    total_bookings: int = 0
    completed_sessions: int = 0
    booking_conversion_rate: Optional[Decimal] = None
    average_session_duration: Optional[Decimal] = None
    
    # Financial metrics
    daily_revenue: Decimal = Decimal("0.00")
    average_session_value: Optional[Decimal] = None
    
    # Distributions
    subject_distribution: Dict[str, int] = Field(default_factory=dict)
    peak_hours: Dict[str, int] = Field(default_factory=dict)
    time_slot_distribution: Dict[str, int] = Field(default_factory=dict)
    geographic_distribution: Dict[str, int] = Field(default_factory=dict)


class PlatformAnalyticsRequest(BaseModel):
    """Request for platform analytics."""
    start_date: date
    end_date: date
    metric_types: List[MetricType] = Field(default_factory=lambda: list(MetricType))
    include_distributions: bool = True


# =====================================================
# SESSION FEEDBACK MODELS
# =====================================================

class SessionFeedback(IdentifiedEntity):
    """Detailed session feedback and ratings."""
    
    model_config = ConfigDict(from_attributes=True)
    
    feedback_id: int
    appointment_id: int
    given_by: int
    given_to: int
    role_type: FeedbackRole
    
    # Ratings
    overall_rating: Optional[int] = Field(None, ge=1, le=5)
    punctuality_rating: Optional[int] = Field(None, ge=1, le=5)
    preparation_rating: Optional[int] = Field(None, ge=1, le=5)
    communication_rating: Optional[int] = Field(None, ge=1, le=5)
    
    # Feedback
    feedback_text: Optional[str] = None
    improvement_areas: List[str] = Field(default_factory=list)
    positive_highlights: List[str] = Field(default_factory=list)
    
    # Response tracking
    requires_followup: bool = False
    followup_completed: bool = False
    
    @property
    def average_rating(self) -> Optional[float]:
        """Calculate average of all ratings."""
        ratings = [
            r for r in [
                self.overall_rating,
                self.punctuality_rating,
                self.preparation_rating,
                self.communication_rating
            ] if r is not None
        ]
        if ratings:
            return sum(ratings) / len(ratings)
        return None


class SessionFeedbackCreate(BaseModel):
    """Create session feedback request."""
    appointment_id: int
    overall_rating: int = Field(..., ge=1, le=5)
    punctuality_rating: Optional[int] = Field(None, ge=1, le=5)
    preparation_rating: Optional[int] = Field(None, ge=1, le=5)
    communication_rating: Optional[int] = Field(None, ge=1, le=5)
    feedback_text: Optional[str] = Field(None, max_length=1000)
    improvement_areas: List[str] = Field(default_factory=list, max_length=10)
    positive_highlights: List[str] = Field(default_factory=list, max_length=10)
    requires_followup: bool = False


class StudentLearningGoalCreate(BaseModel):
    """Create learning goal request."""
    subject_area: str = Field(..., min_length=1, max_length=50)
    goal_title: str = Field(..., min_length=1, max_length=200)
    goal_description: Optional[str] = Field(None, max_length=1000)
    target_date: Optional[date] = None
    assigned_tutor_id: Optional[int] = None


# =====================================================
# ANALYTICS SNAPSHOTS
# =====================================================

class AnalyticsSnapshot(BaseModel):
    """Pre-calculated analytics snapshot."""
    
    model_config = ConfigDict(from_attributes=True)
    
    snapshot_id: int
    snapshot_type: str
    snapshot_date: date
    entity_type: str  # 'tutor', 'student', 'platform'
    entity_id: Optional[int] = None
    metrics: Dict[str, Any]
    created_at: datetime
    processing_time_ms: Optional[int] = None


class AnalyticsSnapshotRequest(BaseModel):
    """Request for analytics snapshot."""
    snapshot_type: str
    entity_type: str
    entity_id: Optional[int] = None
    date: Optional[date] = None
    force_refresh: bool = False


# =====================================================
# DASHBOARD RESPONSE MODELS
# =====================================================

class TutorDashboardData(BaseModel):
    """Complete tutor dashboard data."""
    performance_metrics: TutorPerformanceMetrics
    recent_feedback: List[SessionFeedback]
    subject_performance: Dict[str, Any]
    student_progress: List[StudentProgressSummary]
    earnings_trend: List[Dict[str, Any]]
    upcoming_sessions: int
    pending_feedback: int


class StudentDashboardData(BaseModel):
    """Complete student dashboard data."""
    progress_metrics: List[StudentProgressMetrics]
    learning_goals: List[StudentLearningGoal]
    recent_sessions: List[Dict[str, Any]]
    tutor_feedback: List[SessionFeedback]
    improvement_trends: Dict[str, Any]
    next_session: Optional[Dict[str, Any]] = None


class PlatformDashboardData(BaseModel):
    """Complete platform dashboard data."""
    platform_metrics: PlatformAnalytics
    tutor_leaderboard: List[TutorLeaderboardEntry]
    revenue_trends: Dict[str, Any]
    user_growth: Dict[str, Any]
    geographic_insights: Dict[str, Any]
    popular_subjects: List[Dict[str, Any]]
    peak_usage_times: List[Dict[str, Any]]