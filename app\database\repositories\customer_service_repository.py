"""
Customer Service Repository

Comprehensive data access layer for customer service operations including
conversations, messages, templates, agents, and analytics.
"""

from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, date, timedelta
from decimal import Decimal
import uuid
import asyncpg
from app.database.repositories.base import BaseRepository
from app.models.customer_service_models import *
from app.core.logging import logger


class CustomerServiceRepository(BaseRepository):
    """Repository for customer service operations"""
    
    def __init__(self):
        """Initialize customer service repository."""
        super().__init__(table_name="cs_conversations", id_column="conversation_id")

    # Agent Management

    async def get_agent_by_user_id(self, user_id: int) -> Optional[CustomerServiceAgent]:
        """Get agent by user ID"""
        query = """
            SELECT agent_id, user_id, agent_name, department, status, 
                   max_concurrent_conversations, specialties, languages, 
                   created_at, updated_at, is_active
            FROM customer_service_agents 
            WHERE user_id = $1 AND is_active = true
        """
        row = await self.db.fetchrow(query, user_id)
        return CustomerServiceAgent(**dict(row)) if row else None

    async def get_all_agents(self, active_only: bool = True) -> List[CustomerServiceAgent]:
        """Get all customer service agents"""
        query = """
            SELECT agent_id, user_id, agent_name, department, status, 
                   max_concurrent_conversations, specialties, languages, 
                   created_at, updated_at, is_active
            FROM customer_service_agents 
        """
        params = []
        if active_only:
            query += " WHERE is_active = true"
        
        query += " ORDER BY agent_name"
        
        rows = await self.db.fetch(query, *params)
        return [CustomerServiceAgent(**dict(row)) for row in rows]

    async def update_agent_status(self, agent_id: int, status: AgentStatus) -> bool:
        """Update agent status"""
        query = """
            UPDATE customer_service_agents 
            SET status = $2, updated_at = CURRENT_TIMESTAMP
            WHERE agent_id = $1
        """
        result = await self.db.execute(query, agent_id, status.value)
        return "UPDATE 1" in result

    async def get_available_agents(self, specialties: List[str] = None) -> List[CustomerServiceAgent]:
        """Get available agents, optionally filtered by specialties"""
        query = """
            SELECT agent_id, user_id, agent_name, department, status, 
                   max_concurrent_conversations, specialties, languages, 
                   created_at, updated_at, is_active
            FROM customer_service_agents 
            WHERE status = 'available' AND is_active = true
        """
        params = []
        
        if specialties:
            query += " AND specialties && $1"
            params.append(specialties)
        
        query += " ORDER BY agent_name"
        
        rows = await self.db.fetch(query, *params)
        return [CustomerServiceAgent(**dict(row)) for row in rows]

    # Conversation Management

    async def create_conversation(
        self,
        participant_user_id: Optional[int] = None,
        participant_phone: Optional[str] = None,
        participant_email: Optional[str] = None,
        participant_name: Optional[str] = None,
        conversation_type: ConversationType = ConversationType.SMS,
        subject: Optional[str] = None,
        priority: Priority = Priority.NORMAL,
        category: Optional[str] = None,
        source_channel: Optional[str] = None,
        language: str = "en"
    ) -> CSConversation:
        """Create a new conversation"""
        thread_id = str(uuid.uuid4())
        
        query = """
            INSERT INTO cs_conversations (
                thread_id, participant_user_id, participant_phone, participant_email,
                participant_name, conversation_type, subject, status, priority,
                category, source_channel, language, created_at, last_activity_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            RETURNING conversation_id, thread_id, participant_user_id, participant_phone,
                     participant_email, participant_name, assigned_agent_id, conversation_type,
                     subject, status, priority, category, source_channel, created_at,
                     first_response_at, last_activity_at, resolved_at, closed_at,
                     response_time_sla_minutes, resolution_time_sla_hours,
                     customer_satisfaction_rating, customer_feedback, internal_notes,
                     tags, escalation_reason, auto_assigned, language, timezone
        """
        
        row = await self.db.fetchrow(
            query, thread_id, participant_user_id, participant_phone, participant_email,
            participant_name, conversation_type.value, subject, ConversationStatus.OPEN.value,
            priority.value, category, source_channel, language
        )
        
        return CSConversation(**dict(row))

    async def get_conversation_by_id(self, conversation_id: int) -> Optional[CSConversation]:
        """Get conversation by ID"""
        query = """
            SELECT conversation_id, thread_id, participant_user_id, participant_phone,
                   participant_email, participant_name, assigned_agent_id, conversation_type,
                   subject, status, priority, category, source_channel, created_at,
                   first_response_at, last_activity_at, resolved_at, closed_at,
                   response_time_sla_minutes, resolution_time_sla_hours,
                   customer_satisfaction_rating, customer_feedback, internal_notes,
                   tags, escalation_reason, auto_assigned, language, timezone
            FROM cs_conversations 
            WHERE conversation_id = $1
        """
        row = await self.db.fetchrow(query, conversation_id)
        return CSConversation(**dict(row)) if row else None

    async def get_conversation_by_thread_id(self, thread_id: str) -> Optional[CSConversation]:
        """Get conversation by thread ID"""
        query = """
            SELECT conversation_id, thread_id, participant_user_id, participant_phone,
                   participant_email, participant_name, assigned_agent_id, conversation_type,
                   subject, status, priority, category, source_channel, created_at,
                   first_response_at, last_activity_at, resolved_at, closed_at,
                   response_time_sla_minutes, resolution_time_sla_hours,
                   customer_satisfaction_rating, customer_feedback, internal_notes,
                   tags, escalation_reason, auto_assigned, language, timezone
            FROM cs_conversations 
            WHERE thread_id = $1
        """
        row = await self.db.fetchrow(query, thread_id)
        return CSConversation(**dict(row)) if row else None

    async def update_conversation(
        self,
        conversation_id: int,
        status: Optional[ConversationStatus] = None,
        priority: Optional[Priority] = None,
        category: Optional[str] = None,
        internal_notes: Optional[str] = None,
        tags: Optional[List[str]] = None,
        satisfaction_rating: Optional[int] = None,
        customer_feedback: Optional[str] = None
    ) -> bool:
        """Update conversation details"""
        updates = []
        params = [conversation_id]
        param_count = 1
        
        if status is not None:
            param_count += 1
            updates.append(f"status = ${param_count}")
            params.append(status.value)
            
            # Set resolved/closed timestamps
            if status == ConversationStatus.RESOLVED:
                param_count += 1
                updates.append(f"resolved_at = ${param_count}")
                params.append(datetime.utcnow())
            elif status == ConversationStatus.CLOSED:
                param_count += 1
                updates.append(f"closed_at = ${param_count}")
                params.append(datetime.utcnow())
        
        if priority is not None:
            param_count += 1
            updates.append(f"priority = ${param_count}")
            params.append(priority.value)
        
        if category is not None:
            param_count += 1
            updates.append(f"category = ${param_count}")
            params.append(category)
        
        if internal_notes is not None:
            param_count += 1
            updates.append(f"internal_notes = ${param_count}")
            params.append(internal_notes)
        
        if tags is not None:
            param_count += 1
            updates.append(f"tags = ${param_count}")
            params.append(tags)
        
        if satisfaction_rating is not None:
            param_count += 1
            updates.append(f"customer_satisfaction_rating = ${param_count}")
            params.append(satisfaction_rating)
        
        if customer_feedback is not None:
            param_count += 1
            updates.append(f"customer_feedback = ${param_count}")
            params.append(customer_feedback)
        
        if not updates:
            return False
        
        updates.append("updated_at = CURRENT_TIMESTAMP")
        
        query = f"""
            UPDATE cs_conversations 
            SET {', '.join(updates)}
            WHERE conversation_id = $1
        """
        
        result = await self.db.execute(query, *params)
        return "UPDATE 1" in result

    async def assign_conversation(
        self,
        conversation_id: int,
        agent_id: int,
        assigned_by: Optional[int] = None,
        assignment_reason: Optional[str] = None,
        auto_assigned: bool = False
    ) -> bool:
        """Assign conversation to an agent"""
        async with self.db.transaction():
            # Update conversation
            update_query = """
                UPDATE cs_conversations 
                SET assigned_agent_id = $2, status = 'assigned', auto_assigned = $3,
                    updated_at = CURRENT_TIMESTAMP
                WHERE conversation_id = $1
            """
            await self.db.execute(update_query, conversation_id, agent_id, auto_assigned)
            
            # Create assignment record
            assignment_query = """
                INSERT INTO cs_conversation_assignments (
                    conversation_id, agent_id, assigned_by, assignment_reason, auto_assigned
                ) VALUES ($1, $2, $3, $4, $5)
            """
            await self.db.execute(
                assignment_query, conversation_id, agent_id, assigned_by, 
                assignment_reason, auto_assigned
            )
        
        return True

    async def get_conversations_by_agent(
        self,
        agent_id: int,
        status: Optional[ConversationStatus] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[CSConversation]:
        """Get conversations assigned to an agent"""
        query = """
            SELECT conversation_id, thread_id, participant_user_id, participant_phone,
                   participant_email, participant_name, assigned_agent_id, conversation_type,
                   subject, status, priority, category, source_channel, created_at,
                   first_response_at, last_activity_at, resolved_at, closed_at,
                   response_time_sla_minutes, resolution_time_sla_hours,
                   customer_satisfaction_rating, customer_feedback, internal_notes,
                   tags, escalation_reason, auto_assigned, language, timezone
            FROM cs_conversations 
            WHERE assigned_agent_id = $1
        """
        params = [agent_id]
        
        if status:
            params.append(status.value)
            query += f" AND status = ${len(params)}"
        
        query += " ORDER BY last_activity_at DESC LIMIT $%d OFFSET $%d" % (len(params) + 1, len(params) + 2)
        params.extend([limit, offset])
        
        rows = await self.db.fetch(query, *params)
        return [CSConversation(**dict(row)) for row in rows]

    async def get_unassigned_conversations(
        self,
        priority: Optional[Priority] = None,
        category: Optional[str] = None,
        limit: int = 50
    ) -> List[CSConversation]:
        """Get unassigned conversations"""
        query = """
            SELECT conversation_id, thread_id, participant_user_id, participant_phone,
                   participant_email, participant_name, assigned_agent_id, conversation_type,
                   subject, status, priority, category, source_channel, created_at,
                   first_response_at, last_activity_at, resolved_at, closed_at,
                   response_time_sla_minutes, resolution_time_sla_hours,
                   customer_satisfaction_rating, customer_feedback, internal_notes,
                   tags, escalation_reason, auto_assigned, language, timezone
            FROM cs_conversations 
            WHERE assigned_agent_id IS NULL AND status = 'open'
        """
        params = []
        
        if priority:
            params.append(priority.value)
            query += f" AND priority = ${len(params)}"
        
        if category:
            params.append(category)
            query += f" AND category = ${len(params)}"
        
        query += " ORDER BY priority DESC, created_at ASC LIMIT $%d" % (len(params) + 1)
        params.append(limit)
        
        rows = await self.db.fetch(query, *params)
        return [CSConversation(**dict(row)) for row in rows]

    # Message Management

    async def create_message(
        self,
        conversation_id: int,
        thread_id: str,
        message_content: str,
        message_direction: MessageDirection,
        channel: str,
        sender_user_id: Optional[int] = None,
        sender_agent_id: Optional[int] = None,
        sender_phone: Optional[str] = None,
        sender_name: Optional[str] = None,
        sender_role: Optional[str] = None,
        message_type: str = "text",
        template_id: Optional[int] = None,
        automated: bool = False,
        system_generated: bool = False,
        internal_note: bool = False,
        attachments: Optional[Dict[str, Any]] = None,
        rich_content: Optional[Dict[str, Any]] = None,
        language: str = "en"
    ) -> CSMessage:
        """Create a new message"""
        query = """
            INSERT INTO cs_messages (
                conversation_id, thread_id, message_content, message_type, message_direction,
                sender_user_id, sender_agent_id, sender_phone, sender_name, sender_role,
                delivery_status, channel, template_id, automated, system_generated,
                internal_note, attachments, rich_content, language, sent_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, CURRENT_TIMESTAMP)
            RETURNING message_id, conversation_id, thread_id, message_content, message_type,
                     message_direction, sender_user_id, sender_agent_id, sender_phone,
                     sender_name, sender_role, delivery_status, external_message_id,
                     channel, template_id, automated, system_generated, internal_note,
                     sent_at, delivered_at, read_at, attachments, rich_content, language
        """
        
        row = await self.db.fetchrow(
            query, conversation_id, thread_id, message_content, message_type, 
            message_direction.value, sender_user_id, sender_agent_id, sender_phone,
            sender_name, sender_role, DeliveryStatus.PENDING.value, channel,
            template_id, automated, system_generated, internal_note,
            attachments, rich_content, language
        )
        
        # Update first response time if this is the first agent response
        if message_direction == MessageDirection.OUTBOUND and sender_agent_id:
            await self._update_first_response_time(conversation_id)
        
        return CSMessage(**dict(row))

    async def get_conversation_messages(
        self,
        conversation_id: int,
        include_internal: bool = False,
        limit: int = 100,
        offset: int = 0
    ) -> List[CSMessage]:
        """Get messages for a conversation"""
        query = """
            SELECT message_id, conversation_id, thread_id, message_content, message_type,
                   message_direction, sender_user_id, sender_agent_id, sender_phone,
                   sender_name, sender_role, delivery_status, external_message_id,
                   channel, template_id, automated, system_generated, internal_note,
                   sent_at, delivered_at, read_at, attachments, rich_content, language
            FROM cs_messages 
            WHERE conversation_id = $1
        """
        params = [conversation_id]
        
        if not include_internal:
            query += " AND internal_note = false"
        
        query += " ORDER BY sent_at ASC LIMIT $%d OFFSET $%d" % (len(params) + 1, len(params) + 2)
        params.extend([limit, offset])
        
        rows = await self.db.fetch(query, *params)
        return [CSMessage(**dict(row)) for row in rows]

    async def update_message_delivery_status(
        self,
        message_id: int,
        delivery_status: DeliveryStatus,
        external_message_id: Optional[str] = None,
        delivered_at: Optional[datetime] = None
    ) -> bool:
        """Update message delivery status"""
        query = """
            UPDATE cs_messages 
            SET delivery_status = $2, external_message_id = $3, delivered_at = $4
            WHERE message_id = $1
        """
        result = await self.db.execute(
            query, message_id, delivery_status.value, external_message_id, 
            delivered_at or datetime.utcnow()
        )
        return "UPDATE 1" in result

    async def mark_message_read(self, message_id: int) -> bool:
        """Mark message as read"""
        query = """
            UPDATE cs_messages 
            SET delivery_status = 'read', read_at = CURRENT_TIMESTAMP
            WHERE message_id = $1
        """
        result = await self.db.execute(query, message_id)
        return "UPDATE 1" in result

    # Template Management

    async def create_template(
        self,
        template_name: str,
        template_key: str,
        category: str,
        message_content: str,
        created_by: int,
        subject: Optional[str] = None,
        variables: Optional[Dict[str, Any]] = None,
        language: str = "en",
        channel: Optional[str] = None,
        department: str = "general"
    ) -> CSMessageTemplate:
        """Create a new message template"""
        query = """
            INSERT INTO cs_message_templates (
                template_name, template_key, category, subject, message_content,
                variables, language, channel, department, created_by
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            RETURNING template_id, template_name, template_key, category, subject,
                     message_content, variables, language, channel, department,
                     usage_count, last_used_at, approved_by, approved_at, status,
                     created_by, created_at, updated_at, version
        """
        
        row = await self.db.fetchrow(
            query, template_name, template_key, category, subject, message_content,
            variables, language, channel, department, created_by
        )
        
        return CSMessageTemplate(**dict(row))

    async def get_templates(
        self,
        category: Optional[str] = None,
        language: str = "en",
        status: TemplateStatus = TemplateStatus.APPROVED,
        department: Optional[str] = None
    ) -> List[CSMessageTemplate]:
        """Get message templates"""
        query = """
            SELECT template_id, template_name, template_key, category, subject,
                   message_content, variables, language, channel, department,
                   usage_count, last_used_at, approved_by, approved_at, status,
                   created_by, created_at, updated_at, version
            FROM cs_message_templates 
            WHERE language = $1 AND status = $2
        """
        params = [language, status.value]
        
        if category:
            params.append(category)
            query += f" AND category = ${len(params)}"
        
        if department:
            params.append(department)
            query += f" AND department = ${len(params)}"
        
        query += " ORDER BY category, template_name"
        
        rows = await self.db.fetch(query, *params)
        return [CSMessageTemplate(**dict(row)) for row in rows]

    async def get_template_by_key(self, template_key: str, language: str = "en") -> Optional[CSMessageTemplate]:
        """Get template by key"""
        query = """
            SELECT template_id, template_name, template_key, category, subject,
                   message_content, variables, language, channel, department,
                   usage_count, last_used_at, approved_by, approved_at, status,
                   created_by, created_at, updated_at, version
            FROM cs_message_templates 
            WHERE template_key = $1 AND language = $2
        """
        row = await self.db.fetchrow(query, template_key, language)
        return CSMessageTemplate(**dict(row)) if row else None

    async def increment_template_usage(self, template_id: int) -> bool:
        """Increment template usage count"""
        query = """
            UPDATE cs_message_templates 
            SET usage_count = usage_count + 1, last_used_at = CURRENT_TIMESTAMP
            WHERE template_id = $1
        """
        result = await self.db.execute(query, template_id)
        return "UPDATE 1" in result

    # Quick Responses

    async def get_quick_responses(
        self,
        category: Optional[str] = None,
        language: str = "en"
    ) -> List[CSQuickResponse]:
        """Get quick responses"""
        query = """
            SELECT quick_response_id, response_text, trigger_keywords, category,
                   language, usage_count, created_by, created_at, is_active
            FROM cs_quick_responses 
            WHERE language = $1 AND is_active = true
        """
        params = [language]
        
        if category:
            params.append(category)
            query += f" AND category = ${len(params)}"
        
        query += " ORDER BY usage_count DESC, response_text"
        
        rows = await self.db.fetch(query, *params)
        return [CSQuickResponse(**dict(row)) for row in rows]

    # Analytics and Metrics

    async def get_conversation_stats(
        self,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        agent_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """Get conversation statistics"""
        if not start_date:
            start_date = date.today() - timedelta(days=30)
        if not end_date:
            end_date = date.today()
        
        query = """
            SELECT 
                COUNT(*) as total_conversations,
                COUNT(*) FILTER (WHERE status = 'open') as open_conversations,
                COUNT(*) FILTER (WHERE status = 'assigned') as assigned_conversations,
                COUNT(*) FILTER (WHERE status = 'resolved' AND DATE(resolved_at) = CURRENT_DATE) as resolved_today,
                COUNT(*) FILTER (WHERE status IN ('open', 'assigned') 
                    AND EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - created_at))/60 > response_time_sla_minutes) as overdue_responses,
                AVG(CASE WHEN first_response_at IS NOT NULL 
                    THEN EXTRACT(EPOCH FROM (first_response_at - created_at))/60 END) as avg_response_time,
                AVG(customer_satisfaction_rating) as satisfaction_average,
                COUNT(*) FILTER (WHERE customer_satisfaction_rating IS NOT NULL) as satisfaction_count
            FROM cs_conversations 
            WHERE DATE(created_at) BETWEEN $1 AND $2
        """
        params = [start_date, end_date]
        
        if agent_id:
            params.append(agent_id)
            query += f" AND assigned_agent_id = ${len(params)}"
        
        row = await self.db.fetchrow(query, *params)
        return dict(row) if row else {}

    async def get_message_stats(
        self,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        agent_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """Get message statistics"""
        if not start_date:
            start_date = date.today()
        if not end_date:
            end_date = date.today()
        
        query = """
            SELECT 
                COUNT(*) FILTER (WHERE DATE(sent_at) = CURRENT_DATE) as total_messages_today,
                COUNT(*) FILTER (WHERE message_direction = 'inbound' AND DATE(sent_at) = CURRENT_DATE) as inbound_messages,
                COUNT(*) FILTER (WHERE message_direction = 'outbound' AND DATE(sent_at) = CURRENT_DATE) as outbound_messages,
                COUNT(*) FILTER (WHERE automated = true AND DATE(sent_at) = CURRENT_DATE) as automated_messages,
                AVG(CASE WHEN delivery_status IN ('delivered', 'read') THEN 1.0 ELSE 0.0 END) as delivery_rate
            FROM cs_messages 
            WHERE DATE(sent_at) BETWEEN $1 AND $2
        """
        params = [start_date, end_date]
        
        if agent_id:
            params.append(agent_id)
            query += f" AND sender_agent_id = ${len(params)}"
        
        row = await self.db.fetchrow(query, *params)
        return dict(row) if row else {}

    # Helper Methods

    async def _update_first_response_time(self, conversation_id: int) -> None:
        """Update first response time for conversation"""
        query = """
            UPDATE cs_conversations 
            SET first_response_at = CURRENT_TIMESTAMP
            WHERE conversation_id = $1 AND first_response_at IS NULL
        """
        await self.db.execute(query, conversation_id)

    async def get_agent_workload(self, agent_id: int) -> Dict[str, Any]:
        """Get current workload for an agent"""
        query = """
            SELECT 
                COUNT(*) as active_conversations,
                COUNT(*) FILTER (WHERE priority = 'urgent') as urgent_conversations,
                COUNT(*) FILTER (WHERE priority = 'high') as high_priority_conversations,
                COUNT(*) FILTER (WHERE EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - last_activity_at))/60 > 30) as stale_conversations
            FROM cs_conversations 
            WHERE assigned_agent_id = $1 AND status IN ('assigned', 'open')
        """
        row = await self.db.fetchrow(query, agent_id)
        return dict(row) if row else {}

    async def search_conversations(
        self,
        search_term: str,
        agent_id: Optional[int] = None,
        status: Optional[ConversationStatus] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        limit: int = 50
    ) -> List[CSConversation]:
        """Search conversations by content"""
        query = """
            SELECT DISTINCT c.conversation_id, c.thread_id, c.participant_user_id, c.participant_phone,
                   c.participant_email, c.participant_name, c.assigned_agent_id, c.conversation_type,
                   c.subject, c.status, c.priority, c.category, c.source_channel, c.created_at,
                   c.first_response_at, c.last_activity_at, c.resolved_at, c.closed_at,
                   c.response_time_sla_minutes, c.resolution_time_sla_hours,
                   c.customer_satisfaction_rating, c.customer_feedback, c.internal_notes,
                   c.tags, c.escalation_reason, c.auto_assigned, c.language, c.timezone
            FROM cs_conversations c
            LEFT JOIN cs_messages m ON c.conversation_id = m.conversation_id
            WHERE (
                c.subject ILIKE $1 OR 
                c.participant_name ILIKE $1 OR 
                c.participant_email ILIKE $1 OR 
                c.participant_phone ILIKE $1 OR
                m.message_content ILIKE $1
            )
        """
        params = [f"%{search_term}%"]
        
        if agent_id:
            params.append(agent_id)
            query += f" AND c.assigned_agent_id = ${len(params)}"
        
        if status:
            params.append(status.value)
            query += f" AND c.status = ${len(params)}"
        
        if start_date:
            params.append(start_date)
            query += f" AND DATE(c.created_at) >= ${len(params)}"
        
        if end_date:
            params.append(end_date)
            query += f" AND DATE(c.created_at) <= ${len(params)}"
        
        query += f" ORDER BY c.last_activity_at DESC LIMIT ${len(params) + 1}"
        params.append(limit)
        
        rows = await self.db.fetch(query, *params)
        return [CSConversation(**dict(row)) for row in rows]

    # In-App Messaging Support Methods

    async def get_conversation_participants(self, conversation_id: int) -> List[Dict[str, Any]]:
        """Get all participants in a conversation"""
        query = """
            SELECT DISTINCT
                CASE 
                    WHEN c.participant_user_id IS NOT NULL THEN c.participant_user_id
                    WHEN a.user_id IS NOT NULL THEN a.user_id
                END as user_id,
                CASE 
                    WHEN c.participant_user_id IS NOT NULL THEN c.participant_name
                    WHEN a.user_id IS NOT NULL THEN a.agent_name
                END as name,
                CASE 
                    WHEN c.participant_user_id IS NOT NULL THEN c.participant_email
                    WHEN a.user_id IS NOT NULL THEN NULL
                END as email,
                CASE 
                    WHEN c.participant_user_id IS NOT NULL THEN 'customer'
                    WHEN a.user_id IS NOT NULL THEN 'agent'
                END as role,
                CASE 
                    WHEN a.user_id IS NOT NULL THEN a.status = 'available'
                    ELSE false
                END as is_online
            FROM cs_conversations c
            LEFT JOIN customer_service_agents a ON c.assigned_agent_id = a.agent_id
            WHERE c.conversation_id = $1
        """
        rows = await self.db.fetch(query, conversation_id)
        return [dict(row) for row in rows if row['user_id'] is not None]

    async def mark_conversation_messages_read(self, conversation_id: int, user_id: int) -> bool:
        """Mark all messages in conversation as read for a user"""
        query = """
            UPDATE cs_messages 
            SET delivery_status = 'read', read_at = CURRENT_TIMESTAMP
            WHERE conversation_id = $1 
            AND (
                (sender_user_id != $2 AND message_direction = 'inbound') OR
                (sender_agent_id IS NOT NULL AND message_direction = 'outbound')
            )
            AND delivery_status != 'read'
        """
        result = await self.db.execute(query, conversation_id, user_id)
        return True

    async def get_conversation_attachments(self, conversation_id: int) -> List[Dict[str, Any]]:
        """Get all attachments from a conversation"""
        query = """
            SELECT 
                m.message_id,
                m.sent_at,
                m.sender_name,
                jsonb_array_elements(m.attachments) as attachment
            FROM cs_messages m
            WHERE m.conversation_id = $1 
            AND m.attachments IS NOT NULL 
            AND jsonb_array_length(m.attachments) > 0
            ORDER BY m.sent_at DESC
        """
        rows = await self.db.fetch(query, conversation_id)
        
        attachments = []
        for row in rows:
            attachment_data = dict(row['attachment'])
            attachment_data.update({
                'message_id': row['message_id'],
                'sent_at': row['sent_at'],
                'sender_name': row['sender_name']
            })
            attachments.append(attachment_data)
        
        return attachments

    async def get_unread_message_count(self, conversation_id: int, user_id: int) -> int:
        """Get count of unread messages for a user in a conversation"""
        query = """
            SELECT COUNT(*) as unread_count
            FROM cs_messages 
            WHERE conversation_id = $1 
            AND delivery_status != 'read'
            AND (
                (sender_user_id != $2 AND message_direction = 'inbound') OR
                (sender_agent_id IS NOT NULL AND message_direction = 'outbound')
            )
        """
        row = await self.db.fetchrow(query, conversation_id, user_id)
        return row['unread_count'] if row else 0

    async def get_active_conversation_count(self, user_id: int) -> int:
        """Get total count of active conversations for a user"""
        query = """
            SELECT COUNT(*) as active_count
            FROM cs_conversations 
            WHERE (participant_user_id = $1 OR assigned_agent_id IN (
                SELECT agent_id FROM customer_service_agents WHERE user_id = $1
            ))
            AND status IN ('open', 'assigned')
        """
        row = await self.db.fetchrow(query, user_id)
        return row['active_count'] if row else 0

    async def update_last_activity(self, conversation_id: int) -> bool:
        """Update last activity timestamp for conversation"""
        query = """
            UPDATE cs_conversations 
            SET last_activity_at = CURRENT_TIMESTAMP
            WHERE conversation_id = $1
        """
        result = await self.db.execute(query, conversation_id)
        return "UPDATE 1" in result

    # Conversation Threading and History Methods

    async def get_conversation_threads(
        self,
        status: Optional[str] = None,
        include_archived: bool = False,
        search_term: Optional[str] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """Get conversation threads with filtering and pagination"""
        query = """
            SELECT 
                c.thread_id,
                c.subject,
                COUNT(DISTINCT c.conversation_id) as conversation_count,
                COUNT(DISTINCT m.message_id) as message_count,
                MIN(c.created_at) as first_message_at,
                MAX(c.last_activity_at) as last_activity_at,
                CASE 
                    WHEN COUNT(*) FILTER (WHERE c.status = 'archived') = COUNT(*) THEN 'archived'
                    WHEN COUNT(*) FILTER (WHERE c.status IN ('resolved', 'closed')) = COUNT(*) THEN 'resolved'
                    ELSE 'active'
                END as status,
                ARRAY_AGG(DISTINCT COALESCE(c.participant_name, c.participant_email, c.participant_phone)) 
                    FILTER (WHERE COALESCE(c.participant_name, c.participant_email, c.participant_phone) IS NOT NULL) as participants,
                ARRAY_AGG(DISTINCT c.category) FILTER (WHERE c.category IS NOT NULL) as categories,
                MAX(c.priority) as priority,
                ARRAY_AGG(DISTINCT tag) FILTER (WHERE tag IS NOT NULL) as tags
            FROM cs_conversations c
            LEFT JOIN cs_messages m ON c.conversation_id = m.conversation_id
            LEFT JOIN LATERAL unnest(c.tags) as tag ON true
            WHERE 1=1
        """
        params = []
        
        if not include_archived:
            query += " AND c.status != 'archived'"
        
        if status and status != 'all':
            if status == 'active':
                query += " AND c.status NOT IN ('resolved', 'closed', 'archived')"
            elif status == 'resolved':
                query += " AND c.status IN ('resolved', 'closed')"
            else:
                params.append(status)
                query += f" AND c.status = ${len(params)}"
        
        if search_term:
            params.append(f"%{search_term}%")
            query += f" AND (c.subject ILIKE ${len(params)} OR c.participant_name ILIKE ${len(params)} OR c.participant_email ILIKE ${len(params)})"
        
        if start_date:
            params.append(start_date)
            query += f" AND DATE(c.created_at) >= ${len(params)}"
        
        if end_date:
            params.append(end_date)
            query += f" AND DATE(c.created_at) <= ${len(params)}"
        
        query += """
            GROUP BY c.thread_id, c.subject
            ORDER BY last_activity_at DESC
        """
        
        params.extend([limit, offset])
        query += f" LIMIT ${len(params)-1} OFFSET ${len(params)}"
        
        rows = await self.db.fetch(query, *params)
        
        # Get conversations for each thread
        threads = []
        for row in rows:
            thread_data = dict(row)
            
            # Get conversations in this thread
            conv_query = """
                SELECT conversation_id, participant_name, participant_email, participant_phone,
                       assigned_agent_id, status, priority, category, created_at, resolved_at,
                       (SELECT COUNT(*) FROM cs_messages WHERE conversation_id = c.conversation_id) as message_count,
                       last_activity_at, customer_satisfaction_rating,
                       EXTRACT(EPOCH FROM (resolved_at - created_at))/3600 as resolution_time_hours,
                       EXTRACT(EPOCH FROM (first_response_at - created_at))/60 as response_time_minutes,
                       (SELECT agent_name FROM customer_service_agents WHERE agent_id = c.assigned_agent_id) as agent_name
                FROM cs_conversations c
                WHERE thread_id = $1
                ORDER BY created_at ASC
            """
            conversations = await self.db.fetch(conv_query, thread_data['thread_id'])
            thread_data['conversations'] = [dict(conv) for conv in conversations]
            
            threads.append(thread_data)
        
        return threads

    async def get_thread_details(self, thread_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a specific thread"""
        query = """
            SELECT 
                thread_id,
                subject,
                COUNT(DISTINCT conversation_id) as conversation_count,
                COUNT(DISTINCT CASE WHEN status = 'resolved' THEN conversation_id END) as resolved_count,
                MIN(created_at) as created_at,
                MAX(last_activity_at) as last_activity_at,
                ARRAY_AGG(DISTINCT COALESCE(participant_name, participant_email, participant_phone)) 
                    FILTER (WHERE COALESCE(participant_name, participant_email, participant_phone) IS NOT NULL) as participants,
                ARRAY_AGG(DISTINCT category) FILTER (WHERE category IS NOT NULL) as categories,
                AVG(customer_satisfaction_rating) as avg_satisfaction,
                MAX(priority) as priority
            FROM cs_conversations
            WHERE thread_id = $1
            GROUP BY thread_id, subject
        """
        row = await self.db.fetchrow(query, thread_id)
        return dict(row) if row else None

    async def get_thread_history(self, thread_id: str) -> Dict[str, Any]:
        """Get complete history for a conversation thread"""
        # Get all conversations in thread
        conversations = await self.db.fetch(
            "SELECT conversation_id FROM cs_conversations WHERE thread_id = $1",
            thread_id
        )
        conversation_ids = [row['conversation_id'] for row in conversations]
        
        if not conversation_ids:
            return {"status_changes": [], "assignments": [], "message_timeline": []}
        
        # Get status changes
        status_changes = await self.db.fetch("""
            SELECT 
                ROW_NUMBER() OVER (ORDER BY created_at) as change_id,
                conversation_id,
                'status_change' as event_type,
                status as to_status,
                LAG(status) OVER (PARTITION BY conversation_id ORDER BY created_at) as from_status,
                created_at as changed_at,
                'System' as changed_by,
                'Automatic status update' as reason
            FROM cs_conversations 
            WHERE conversation_id = ANY($1)
            ORDER BY created_at
        """, conversation_ids)
        
        # Get assignments
        assignments = await self.db.fetch("""
            SELECT 
                assignment_id,
                conversation_id,
                agent_id,
                (SELECT agent_name FROM customer_service_agents WHERE agent_id = a.agent_id) as agent_name,
                (SELECT u.name FROM users u WHERE u.user_id = a.assigned_by) as assigned_by,
                assigned_at,
                assignment_reason
            FROM cs_conversation_assignments a
            WHERE conversation_id = ANY($1)
            ORDER BY assigned_at
        """, conversation_ids)
        
        # Get message timeline
        message_timeline = await self.db.fetch("""
            SELECT 
                message_id,
                conversation_id,
                message_direction,
                sender_name,
                sent_at,
                message_content,
                template_id
            FROM cs_messages
            WHERE conversation_id = ANY($1)
            ORDER BY sent_at
        """, conversation_ids)
        
        return {
            "status_changes": [dict(row) for row in status_changes],
            "assignments": [dict(row) for row in assignments],
            "message_timeline": [dict(row) for row in message_timeline]
        }

    async def get_thread_analytics(self, thread_id: str) -> Dict[str, Any]:
        """Get analytics data for a conversation thread"""
        base_query = """
            SELECT 
                COUNT(DISTINCT c.conversation_id) as conversation_count,
                COUNT(DISTINCT m.message_id) as message_count,
                COUNT(DISTINCT m.message_id) FILTER (WHERE m.message_direction = 'inbound') as inbound_messages,
                COUNT(DISTINCT m.message_id) FILTER (WHERE m.message_direction = 'outbound') as outbound_messages,
                AVG(EXTRACT(EPOCH FROM (c.first_response_at - c.created_at))/60) as avg_response_time_minutes,
                AVG(EXTRACT(EPOCH FROM (c.resolved_at - c.created_at))/3600) as avg_resolution_time_hours,
                COUNT(*) FILTER (WHERE c.status = 'resolved')::float / COUNT(*)::float as resolution_rate,
                AVG(c.customer_satisfaction_rating) as avg_satisfaction,
                COUNT(DISTINCT c.assigned_agent_id) as unique_agents,
                MIN(c.created_at) as thread_start,
                MAX(c.last_activity_at) as thread_last_activity
            FROM cs_conversations c
            LEFT JOIN cs_messages m ON c.conversation_id = m.conversation_id
            WHERE c.thread_id = $1
        """
        
        analytics = await self.db.fetchrow(base_query, thread_id)
        result = dict(analytics) if analytics else {}
        
        # Get participant statistics
        participant_stats = await self.db.fetch("""
            SELECT 
                COALESCE(c.participant_name, c.participant_email, 'Unknown') as name,
                COUNT(DISTINCT m.message_id) as message_count,
                COUNT(DISTINCT m.message_id)::float / 
                    (SELECT COUNT(*) FROM cs_messages m2 
                     JOIN cs_conversations c2 ON m2.conversation_id = c2.conversation_id 
                     WHERE c2.thread_id = $1)::float as engagement_score,
                MIN(m.sent_at) as first_message,
                MAX(m.sent_at) as last_message
            FROM cs_conversations c
            LEFT JOIN cs_messages m ON c.conversation_id = m.conversation_id
            WHERE c.thread_id = $1 AND c.participant_name IS NOT NULL
            GROUP BY c.participant_name, c.participant_email
            ORDER BY message_count DESC
        """, thread_id)
        
        result['participant_stats'] = [dict(row) for row in participant_stats]
        
        # Get timeline events
        timeline = await self.db.fetch("""
            SELECT 
                'conversation_created' as type,
                'New conversation started' as description,
                created_at as timestamp
            FROM cs_conversations
            WHERE thread_id = $1
            
            UNION ALL
            
            SELECT 
                'conversation_resolved' as type,
                'Conversation resolved' as description,
                resolved_at as timestamp
            FROM cs_conversations
            WHERE thread_id = $1 AND resolved_at IS NOT NULL
            
            ORDER BY timestamp
        """, thread_id)
        
        result['timeline'] = [dict(row) for row in timeline]
        
        return result

    async def get_conversation_history(self, conversation_id: int) -> Dict[str, Any]:
        """Get detailed history for a specific conversation"""
        # Get status changes (would need a separate table in real implementation)
        status_changes = await self.db.fetch("""
            SELECT 
                1 as change_id,
                'open' as from_status,
                status as to_status,
                'System' as changed_by,
                updated_at as changed_at,
                'Initial status' as reason
            FROM cs_conversations
            WHERE conversation_id = $1
        """, conversation_id)
        
        # Get assignments
        assignments = await self.db.fetch("""
            SELECT 
                assignment_id,
                agent_id,
                (SELECT agent_name FROM customer_service_agents WHERE agent_id = a.agent_id) as agent_name,
                (SELECT name FROM users WHERE user_id = a.assigned_by) as assigned_by,
                assigned_at,
                assignment_reason
            FROM cs_conversation_assignments a
            WHERE conversation_id = $1
            ORDER BY assigned_at
        """, conversation_id)
        
        # Get message summary
        message_summary = await self.db.fetchrow("""
            SELECT 
                COUNT(*) as total_messages,
                COUNT(*) FILTER (WHERE message_direction = 'inbound') as inbound_messages,
                COUNT(*) FILTER (WHERE message_direction = 'outbound') as outbound_messages,
                AVG(EXTRACT(EPOCH FROM (
                    CASE WHEN message_direction = 'outbound' 
                    THEN sent_at - LAG(sent_at) OVER (ORDER BY sent_at)
                    END
                ))/60) as average_response_time,
                MIN(CASE WHEN message_direction = 'outbound' THEN sent_at END) -
                MIN(CASE WHEN message_direction = 'inbound' THEN sent_at END) as first_response_time
            FROM cs_messages
            WHERE conversation_id = $1
        """, conversation_id)
        
        # Get satisfaction feedback
        satisfaction = await self.db.fetchrow("""
            SELECT 
                customer_satisfaction_rating as rating,
                customer_feedback as feedback,
                updated_at as submitted_at
            FROM cs_conversations
            WHERE conversation_id = $1 AND customer_satisfaction_rating IS NOT NULL
        """, conversation_id)
        
        return {
            "status_changes": [dict(row) for row in status_changes],
            "assignments": [dict(row) for row in assignments],
            "messages_summary": dict(message_summary) if message_summary else {},
            "satisfaction_feedback": dict(satisfaction) if satisfaction else None
        }

    async def archive_thread(self, thread_id: str, archived_by: int) -> bool:
        """Archive all conversations in a thread"""
        query = """
            UPDATE cs_conversations 
            SET status = 'archived', updated_at = CURRENT_TIMESTAMP
            WHERE thread_id = $1 AND status != 'archived'
        """
        result = await self.db.execute(query, thread_id)
        return "UPDATE" in result

    async def unarchive_thread(self, thread_id: str, unarchived_by: int) -> bool:
        """Unarchive all conversations in a thread"""
        query = """
            UPDATE cs_conversations 
            SET status = 'open', updated_at = CURRENT_TIMESTAMP
            WHERE thread_id = $1 AND status = 'archived'
        """
        result = await self.db.execute(query, thread_id)
        return "UPDATE" in result

    async def merge_conversations_into_thread(
        self, 
        target_thread_id: str, 
        conversation_ids: List[int], 
        merged_by: int
    ) -> bool:
        """Merge conversations into a target thread"""
        async with self.db.transaction():
            # Update thread_id for all conversations
            update_query = """
                UPDATE cs_conversations 
                SET thread_id = $1, updated_at = CURRENT_TIMESTAMP
                WHERE conversation_id = ANY($2)
            """
            await self.db.execute(update_query, target_thread_id, conversation_ids)
            
            # Log the merge operation (would need a merge_log table in real implementation)
            for conv_id in conversation_ids:
                log_query = """
                    INSERT INTO cs_conversation_assignments (
                        conversation_id, agent_id, assigned_by, assignment_reason, auto_assigned
                    ) VALUES ($1, NULL, $2, $3, false)
                """
                await self.db.execute(
                    log_query, 
                    conv_id, 
                    merged_by, 
                    f"Merged into thread {target_thread_id}"
                )
        
        return True

    # Dashboard and Analytics Methods

    async def get_real_time_stats(self) -> Dict[str, Any]:
        """Get real-time system statistics"""
        query = """
            SELECT 
                COUNT(*) FILTER (WHERE status IN ('open', 'assigned')) as active_conversations,
                COUNT(DISTINCT assigned_agent_id) FILTER (WHERE assigned_agent_id IS NOT NULL) as active_agents,
                'healthy' as system_status,
                0 as conversation_change,
                0 as response_time_change,
                0 as resolved_change,
                0 as satisfaction_change,
                0 as message_change,
                0 as overdue_change
            FROM cs_conversations
            WHERE DATE(created_at) = CURRENT_DATE
        """
        row = await self.db.fetchrow(query)
        return dict(row) if row else {}

    async def get_dashboard_trends(
        self,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> Dict[str, Any]:
        """Get historical trends for dashboard charts"""
        if not start_date:
            start_date = date.today() - timedelta(days=7)
        if not end_date:
            end_date = date.today()
        
        # Response time trends
        response_trends = await self.db.fetch("""
            SELECT 
                DATE(created_at) as date,
                AVG(EXTRACT(EPOCH FROM (first_response_at - created_at))/60) as avg_response_time,
                15.0 as target_time
            FROM cs_conversations
            WHERE DATE(created_at) BETWEEN $1 AND $2
            AND first_response_at IS NOT NULL
            GROUP BY DATE(created_at)
            ORDER BY date
        """, start_date, end_date)
        
        # Resolution trends
        resolution_trends = await self.db.fetch("""
            SELECT 
                DATE(resolved_at) as date,
                COUNT(*) as resolved_count,
                COUNT(*) FILTER (WHERE status = 'escalated') as escalated_count,
                AVG(customer_satisfaction_rating) as satisfaction_rate
            FROM cs_conversations
            WHERE DATE(resolved_at) BETWEEN $1 AND $2
            GROUP BY DATE(resolved_at)
            ORDER BY date
        """, start_date, end_date)
        
        return {
            "response_time_trends": [dict(row) for row in response_trends],
            "resolution_trends": [dict(row) for row in resolution_trends]
        }

    async def get_channel_distribution(
        self,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> List[Dict[str, Any]]:
        """Get conversation distribution by channel"""
        if not start_date:
            start_date = date.today() - timedelta(days=7)
        if not end_date:
            end_date = date.today()
        
        query = """
            SELECT 
                COALESCE(source_channel, 'unknown') as channel,
                COUNT(DISTINCT c.conversation_id) as conversation_count,
                COUNT(DISTINCT m.message_id) as message_count,
                AVG(EXTRACT(EPOCH FROM (c.first_response_at - c.created_at))/60) as avg_response_time,
                AVG(c.customer_satisfaction_rating) as satisfaction_rate
            FROM cs_conversations c
            LEFT JOIN cs_messages m ON c.conversation_id = m.conversation_id
            WHERE DATE(c.created_at) BETWEEN $1 AND $2
            GROUP BY source_channel
            ORDER BY conversation_count DESC
        """
        rows = await self.db.fetch(query, start_date, end_date)
        return [dict(row) for row in rows]

    async def get_peak_hours_analysis(
        self,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> List[Dict[str, Any]]:
        """Get peak hours analysis for resource planning"""
        if not start_date:
            start_date = date.today() - timedelta(days=7)
        if not end_date:
            end_date = date.today()
        
        query = """
            SELECT 
                EXTRACT(HOUR FROM c.created_at) as hour,
                COUNT(DISTINCT c.conversation_id) as conversation_count,
                COUNT(DISTINCT m.message_id) as message_count
            FROM cs_conversations c
            LEFT JOIN cs_messages m ON c.conversation_id = m.conversation_id
            WHERE DATE(c.created_at) BETWEEN $1 AND $2
            GROUP BY EXTRACT(HOUR FROM c.created_at)
            ORDER BY hour
        """
        rows = await self.db.fetch(query, start_date, end_date)
        return [dict(row) for row in rows]

    async def get_agent_performance_metrics(
        self,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> List[Dict[str, Any]]:
        """Get detailed agent performance metrics"""
        if not start_date:
            start_date = date.today()
        if not end_date:
            end_date = date.today()
        
        query = """
            SELECT 
                a.agent_id,
                a.agent_name,
                a.status,
                a.max_concurrent_conversations as max_conversations,
                a.specialties,
                a.languages,
                COUNT(DISTINCT c.conversation_id) FILTER (WHERE c.status IN ('open', 'assigned')) as current_conversations,
                AVG(EXTRACT(EPOCH FROM (c.first_response_at - c.created_at))/60) as avg_response_time,
                COUNT(*) FILTER (WHERE DATE(c.resolved_at) = CURRENT_DATE) as resolved_today,
                AVG(c.customer_satisfaction_rating) as satisfaction_rating,
                -- Performance score calculation (simplified)
                CASE 
                    WHEN AVG(EXTRACT(EPOCH FROM (c.first_response_at - c.created_at))/60) <= 10 
                    AND AVG(c.customer_satisfaction_rating) >= 4.0 
                    THEN 90 + RANDOM() * 10
                    WHEN AVG(EXTRACT(EPOCH FROM (c.first_response_at - c.created_at))/60) <= 15 
                    THEN 70 + RANDOM() * 20
                    ELSE 50 + RANDOM() * 20
                END as performance_score
            FROM customer_service_agents a
            LEFT JOIN cs_conversations c ON a.agent_id = c.assigned_agent_id
                AND DATE(c.created_at) BETWEEN $1 AND $2
            WHERE a.is_active = true
            GROUP BY a.agent_id, a.agent_name, a.status, a.max_concurrent_conversations, 
                     a.specialties, a.languages
            ORDER BY a.agent_name
        """
        rows = await self.db.fetch(query, start_date, end_date)
        return [dict(row) for row in rows]

    async def get_satisfaction_metrics(
        self,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> Dict[str, Any]:
        """Get customer satisfaction metrics and analysis"""
        if not start_date:
            start_date = date.today() - timedelta(days=30)
        if not end_date:
            end_date = date.today()
        
        # Overall satisfaction
        overall_query = """
            SELECT 
                AVG(customer_satisfaction_rating) as overall_rating,
                COUNT(*) as total_ratings
            FROM cs_conversations
            WHERE customer_satisfaction_rating IS NOT NULL
            AND DATE(updated_at) BETWEEN $1 AND $2
        """
        overall = await self.db.fetchrow(overall_query, start_date, end_date)
        
        # Rating distribution
        distribution_query = """
            SELECT 
                customer_satisfaction_rating as rating,
                COUNT(*) as count
            FROM cs_conversations
            WHERE customer_satisfaction_rating IS NOT NULL
            AND DATE(updated_at) BETWEEN $1 AND $2
            GROUP BY customer_satisfaction_rating
            ORDER BY rating
        """
        distribution = await self.db.fetch(distribution_query, start_date, end_date)
        
        # Get recent feedback highlights
        feedback_query = """
            SELECT customer_feedback
            FROM cs_conversations
            WHERE customer_feedback IS NOT NULL
            AND customer_satisfaction_rating >= 4
            AND DATE(updated_at) BETWEEN $1 AND $2
            ORDER BY updated_at DESC
            LIMIT 5
        """
        feedback = await self.db.fetch(feedback_query, start_date, end_date)
        
        return {
            "overall_rating": float(overall['overall_rating']) if overall and overall['overall_rating'] else 0.0,
            "total_ratings": overall['total_ratings'] if overall else 0,
            "rating_distribution": [dict(row) for row in distribution],
            "feedback_highlights": [row['customer_feedback'] for row in feedback if row['customer_feedback']],
            "trending_up": True  # Would calculate actual trend
        }

    # Broadcast Messaging Methods

    async def create_broadcast_message(
        self,
        title: str,
        content: str,
        message_type: str,
        priority: str,
        channels: List[str],
        target_audience: Dict[str, Any],
        created_by: int,
        scheduled_at: Optional[datetime] = None,
        template_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """Create a new broadcast message"""
        query = """
            INSERT INTO cs_broadcast_messages (
                title, content, message_type, priority, channels, target_audience,
                created_by, scheduled_at, template_id, status
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            RETURNING broadcast_id, title, content, message_type, priority, channels,
                     target_audience, scheduled_at, status, created_by, created_at,
                     sent_at, delivery_stats, template_id
        """
        
        status = 'scheduled' if scheduled_at else 'draft'
        
        row = await self.db.fetchrow(
            query, title, content, message_type, priority, channels,
            target_audience, created_by, scheduled_at, template_id, status
        )
        
        return dict(row) if row else {}

    async def get_broadcast_messages(
        self,
        status: Optional[str] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """Get broadcast messages with filtering"""
        query = """
            SELECT broadcast_id, title, content, message_type, priority, channels,
                   target_audience, scheduled_at, status, created_by, created_at,
                   sent_at, delivery_stats, template_id
            FROM cs_broadcast_messages
        """
        params = []
        
        if status:
            params.append(status)
            query += f" WHERE status = ${len(params)}"
        
        query += f" ORDER BY created_at DESC LIMIT ${len(params) + 1} OFFSET ${len(params) + 2}"
        params.extend([limit, offset])
        
        rows = await self.db.fetch(query, *params)
        return [dict(row) for row in rows]

    async def get_broadcast_message_by_id(self, broadcast_id: int) -> Optional[Dict[str, Any]]:
        """Get broadcast message by ID"""
        query = """
            SELECT broadcast_id, title, content, message_type, priority, channels,
                   target_audience, scheduled_at, status, created_by, created_at,
                   sent_at, delivery_stats, template_id
            FROM cs_broadcast_messages
            WHERE broadcast_id = $1
        """
        row = await self.db.fetchrow(query, broadcast_id)
        return dict(row) if row else None

    async def update_broadcast_message(
        self,
        broadcast_id: int,
        title: Optional[str] = None,
        content: Optional[str] = None,
        message_type: Optional[str] = None,
        priority: Optional[str] = None,
        channels: Optional[List[str]] = None,
        target_audience: Optional[Dict[str, Any]] = None,
        scheduled_at: Optional[datetime] = None,
        template_id: Optional[int] = None
    ) -> bool:
        """Update broadcast message details"""
        updates = []
        params = [broadcast_id]
        param_count = 1
        
        if title is not None:
            param_count += 1
            updates.append(f"title = ${param_count}")
            params.append(title)
        
        if content is not None:
            param_count += 1
            updates.append(f"content = ${param_count}")
            params.append(content)
        
        if message_type is not None:
            param_count += 1
            updates.append(f"message_type = ${param_count}")
            params.append(message_type)
        
        if priority is not None:
            param_count += 1
            updates.append(f"priority = ${param_count}")
            params.append(priority)
        
        if channels is not None:
            param_count += 1
            updates.append(f"channels = ${param_count}")
            params.append(channels)
        
        if target_audience is not None:
            param_count += 1
            updates.append(f"target_audience = ${param_count}")
            params.append(target_audience)
        
        if scheduled_at is not None:
            param_count += 1
            updates.append(f"scheduled_at = ${param_count}")
            params.append(scheduled_at)
        
        if template_id is not None:
            param_count += 1
            updates.append(f"template_id = ${param_count}")
            params.append(template_id)
        
        if not updates:
            return False
        
        updates.append("updated_at = CURRENT_TIMESTAMP")
        
        query = f"""
            UPDATE cs_broadcast_messages 
            SET {', '.join(updates)}
            WHERE broadcast_id = $1 AND status IN ('draft', 'scheduled')
        """
        
        result = await self.db.execute(query, *params)
        return "UPDATE 1" in result

    async def update_broadcast_status(
        self,
        broadcast_id: int,
        status: str,
        delivery_stats: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Update broadcast message status"""
        updates = ["status = $2", "updated_at = CURRENT_TIMESTAMP"]
        params = [broadcast_id, status]
        
        if status == 'sent':
            updates.append("sent_at = CURRENT_TIMESTAMP")
        
        if delivery_stats:
            updates.append(f"delivery_stats = ${len(params) + 1}")
            params.append(delivery_stats)
        
        query = f"""
            UPDATE cs_broadcast_messages 
            SET {', '.join(updates)}
            WHERE broadcast_id = $1
        """
        
        result = await self.db.execute(query, *params)
        return "UPDATE 1" in result

    async def get_audience_preview(self, target_audience: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Preview audience for broadcast message"""
        audience_type = target_audience.get('audience_type', 'all')
        
        if audience_type == 'all':
            query = """
                SELECT user_id, name, email, 'all' as role
                FROM users 
                WHERE is_active = true
                ORDER BY name
                LIMIT 100
            """
            params = []
        
        elif audience_type == 'role_based':
            roles = target_audience.get('roles', [])
            if not roles:
                return []
            
            query = """
                SELECT u.user_id, u.name, u.email, ur.role_name as role
                FROM users u
                JOIN user_roles ur ON u.user_id = ur.user_id
                WHERE u.is_active = true AND ur.role_name = ANY($1)
                ORDER BY u.name
                LIMIT 100
            """
            params = [roles]
        
        elif audience_type == 'custom':
            user_ids = target_audience.get('user_ids', [])
            if not user_ids:
                return []
            
            query = """
                SELECT user_id, name, email, 'custom' as role
                FROM users
                WHERE user_id = ANY($1) AND is_active = true
                ORDER BY name
            """
            params = [user_ids]
        
        elif audience_type == 'conversation_participants':
            conversation_ids = target_audience.get('conversation_ids', [])
            if not conversation_ids:
                return []
            
            query = """
                SELECT DISTINCT 
                    COALESCE(u.user_id, 0) as user_id,
                    COALESCE(u.name, c.participant_name, 'Unknown') as name,
                    COALESCE(u.email, c.participant_email, '') as email,
                    'participant' as role
                FROM cs_conversations c
                LEFT JOIN users u ON c.participant_user_id = u.user_id
                WHERE c.conversation_id = ANY($1)
                ORDER BY name
                LIMIT 100
            """
            params = [conversation_ids]
        
        else:
            return []
        
        rows = await self.db.fetch(query, *params)
        return [dict(row) for row in rows]

    async def get_broadcast_templates(self) -> List[Dict[str, Any]]:
        """Get available broadcast templates"""
        query = """
            SELECT template_id, template_name, content, message_type, 
                   variables, usage_count
            FROM cs_message_templates
            WHERE category = 'broadcast' AND status = 'approved'
            ORDER BY usage_count DESC, template_name
        """
        rows = await self.db.fetch(query)
        return [dict(row) for row in rows]

    async def get_audience_statistics(self) -> Dict[str, Any]:
        """Get audience statistics for broadcast targeting"""
        stats_query = """
            SELECT 
                COUNT(*) FILTER (WHERE is_active = true) as total_active_users,
                COUNT(DISTINCT ur.role_name) FILTER (WHERE u.is_active = true) as total_roles,
                COUNT(*) FILTER (WHERE u.is_active = true AND u.created_at >= CURRENT_DATE - INTERVAL '30 days') as new_users_30d,
                COUNT(*) FILTER (WHERE u.is_active = true AND u.last_login_at >= CURRENT_DATE - INTERVAL '7 days') as active_users_7d
            FROM users u
            LEFT JOIN user_roles ur ON u.user_id = ur.user_id
        """
        
        roles_query = """
            SELECT ur.role_name, COUNT(*) as user_count
            FROM user_roles ur
            JOIN users u ON ur.user_id = u.user_id
            WHERE u.is_active = true
            GROUP BY ur.role_name
            ORDER BY user_count DESC
        """
        
        languages_query = """
            SELECT 
                COALESCE(preferred_language, 'en') as language,
                COUNT(*) as user_count
            FROM users
            WHERE is_active = true
            GROUP BY COALESCE(preferred_language, 'en')
            ORDER BY user_count DESC
        """
        
        stats_row = await self.db.fetchrow(stats_query)
        roles_rows = await self.db.fetch(roles_query)
        languages_rows = await self.db.fetch(languages_query)
        
        return {
            "total_users": stats_row['total_active_users'] if stats_row else 0,
            "total_roles": stats_row['total_roles'] if stats_row else 0,
            "new_users_30d": stats_row['new_users_30d'] if stats_row else 0,
            "active_users_7d": stats_row['active_users_7d'] if stats_row else 0,
            "role_distribution": [dict(row) for row in roles_rows],
            "language_distribution": [dict(row) for row in languages_rows]
        }

    async def create_broadcast_delivery_log(
        self,
        broadcast_id: int,
        user_id: int,
        channel: str,
        status: str,
        external_message_id: Optional[str] = None,
        error_message: Optional[str] = None
    ) -> bool:
        """Log broadcast message delivery attempt"""
        query = """
            INSERT INTO cs_broadcast_delivery_log (
                broadcast_id, user_id, channel, delivery_status,
                external_message_id, error_message, delivered_at
            ) VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP)
        """
        
        try:
            await self.db.execute(
                query, broadcast_id, user_id, channel, status,
                external_message_id, error_message
            )
            return True
        except Exception as e:
            logger.error(f"Failed to log broadcast delivery: {e}")
            return False

    async def get_broadcast_delivery_stats(self, broadcast_id: int) -> Dict[str, Any]:
        """Get delivery statistics for a broadcast message"""
        query = """
            SELECT 
                COUNT(*) as total_recipients,
                COUNT(*) FILTER (WHERE delivery_status = 'sent') as sent_count,
                COUNT(*) FILTER (WHERE delivery_status = 'delivered') as delivered_count,
                COUNT(*) FILTER (WHERE delivery_status = 'failed') as failed_count,
                COUNT(*) FILTER (WHERE delivery_status = 'opened') as opened_count,
                COUNT(*) FILTER (WHERE delivery_status = 'clicked') as clicked_count
            FROM cs_broadcast_delivery_log
            WHERE broadcast_id = $1
        """
        
        row = await self.db.fetchrow(query, broadcast_id)
        return dict(row) if row else {}

    async def get_scheduled_broadcasts(self) -> List[Dict[str, Any]]:
        """Get broadcasts scheduled for sending"""
        query = """
            SELECT broadcast_id, title, content, message_type, priority, channels,
                   target_audience, scheduled_at, created_by, template_id
            FROM cs_broadcast_messages
            WHERE status = 'scheduled' 
            AND scheduled_at <= CURRENT_TIMESTAMP
            ORDER BY scheduled_at ASC
        """
        
        rows = await self.db.fetch(query)
        return [dict(row) for row in rows]

    # Enhanced Template Management Methods

    async def get_template_by_id(self, template_id: int) -> Optional[CSMessageTemplate]:
        """Get template by ID"""
        query = """
            SELECT template_id, template_name, template_key, category, subject,
                   message_content, variables, language, channel, department,
                   usage_count, last_used_at, approved_by, approved_at, status,
                   created_by, created_at, updated_at, version
            FROM cs_message_templates 
            WHERE template_id = $1
        """
        row = await self.db.fetchrow(query, template_id)
        return CSMessageTemplate(**dict(row)) if row else None

    async def update_template(
        self,
        template_id: int,
        **updates
    ) -> bool:
        """Update template with dynamic fields"""
        if not updates:
            return False
        
        set_clauses = []
        params = [template_id]
        param_count = 1
        
        for field, value in updates.items():
            param_count += 1
            set_clauses.append(f"{field} = ${param_count}")
            params.append(value)
        
        set_clauses.append("updated_at = CURRENT_TIMESTAMP")
        set_clauses.append("version = version + 1")
        
        query = f"""
            UPDATE cs_message_templates 
            SET {', '.join(set_clauses)}
            WHERE template_id = $1
        """
        
        result = await self.db.execute(query, *params)
        return "UPDATE 1" in result

    async def delete_template(self, template_id: int) -> bool:
        """Delete template"""
        query = "DELETE FROM cs_message_templates WHERE template_id = $1"
        result = await self.db.execute(query, template_id)
        return "DELETE 1" in result

    async def update_template_status(
        self,
        template_id: int,
        status: TemplateStatus,
        approved_by: Optional[int] = None
    ) -> bool:
        """Update template status"""
        updates = ["status = $2", "updated_at = CURRENT_TIMESTAMP"]
        params = [template_id, status.value]
        
        if status == TemplateStatus.APPROVED and approved_by:
            updates.extend(["approved_by = $3", "approved_at = CURRENT_TIMESTAMP"])
            params.append(approved_by)
        
        query = f"""
            UPDATE cs_message_templates 
            SET {', '.join(updates)}
            WHERE template_id = $1
        """
        
        result = await self.db.execute(query, *params)
        return "UPDATE 1" in result

    async def get_template_categories(self) -> List[Dict[str, Any]]:
        """Get template categories with counts"""
        query = """
            SELECT 
                category,
                COUNT(*) as count,
                COUNT(*) FILTER (WHERE status = 'approved') as approved_count
            FROM cs_message_templates
            GROUP BY category
            ORDER BY count DESC
        """
        rows = await self.db.fetch(query)
        return [dict(row) for row in rows]

    # Enhanced Quick Response Methods

    async def create_quick_response(
        self,
        response_text: str,
        trigger_keywords: List[str],
        category: str,
        language: str,
        created_by: int
    ) -> CSQuickResponse:
        """Create a new quick response"""
        query = """
            INSERT INTO cs_quick_responses (
                response_text, trigger_keywords, category, language, created_by
            ) VALUES ($1, $2, $3, $4, $5)
            RETURNING quick_response_id, response_text, trigger_keywords, category,
                     language, usage_count, created_by, created_at, is_active
        """
        
        row = await self.db.fetchrow(
            query, response_text, trigger_keywords, category, language, created_by
        )
        
        return CSQuickResponse(**dict(row))

    async def get_quick_response_by_id(self, response_id: int) -> Optional[CSQuickResponse]:
        """Get quick response by ID"""
        query = """
            SELECT quick_response_id, response_text, trigger_keywords, category,
                   language, usage_count, created_by, created_at, is_active
            FROM cs_quick_responses 
            WHERE quick_response_id = $1
        """
        row = await self.db.fetchrow(query, response_id)
        return CSQuickResponse(**dict(row)) if row else None

    async def update_quick_response(
        self,
        response_id: int,
        **updates
    ) -> bool:
        """Update quick response with dynamic fields"""
        if not updates:
            return False
        
        set_clauses = []
        params = [response_id]
        param_count = 1
        
        for field, value in updates.items():
            param_count += 1
            set_clauses.append(f"{field} = ${param_count}")
            params.append(value)
        
        set_clauses.append("updated_at = CURRENT_TIMESTAMP")
        
        query = f"""
            UPDATE cs_quick_responses 
            SET {', '.join(set_clauses)}
            WHERE quick_response_id = $1
        """
        
        result = await self.db.execute(query, *params)
        return "UPDATE 1" in result

    async def delete_quick_response(self, response_id: int) -> bool:
        """Delete quick response"""
        query = "DELETE FROM cs_quick_responses WHERE quick_response_id = $1"
        result = await self.db.execute(query, response_id)
        return "DELETE 1" in result

    async def increment_quick_response_usage(self, response_id: int) -> bool:
        """Increment quick response usage count"""
        query = """
            UPDATE cs_quick_responses 
            SET usage_count = usage_count + 1, last_used_at = CURRENT_TIMESTAMP
            WHERE quick_response_id = $1
        """
        result = await self.db.execute(query, response_id)
        return "UPDATE 1" in result