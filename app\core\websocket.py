"""
WebSocket connection manager for real-time features.
"""

from typing import Dict, List, Set
from fastapi import WebSocket
import json
from app.core.logging import TutorAideLogger

logger = TutorAideLogger.get_logger(__name__)


class ConnectionManager:
    """Manages WebSocket connections for real-time features."""
    
    def __init__(self):
        # Active connections by room/channel
        self.active_connections: Dict[str, List[WebSocket]] = {}
        # Track which rooms each websocket is in
        self.websocket_rooms: Dict[WebSocket, Set[str]] = {}
    
    async def connect(self, websocket: WebSocket, room: str):
        """Accept a new WebSocket connection and add to room."""
        await websocket.accept()
        
        # Add to room
        if room not in self.active_connections:
            self.active_connections[room] = []
        self.active_connections[room].append(websocket)
        
        # Track rooms for this websocket
        if websocket not in self.websocket_rooms:
            self.websocket_rooms[websocket] = set()
        self.websocket_rooms[websocket].add(room)
        
        logger.info(f"WebSocket connected to room: {room}")
    
    def disconnect(self, websocket: WebSocket, room: str = None):
        """Remove a WebSocket connection from room(s)."""
        if room:
            # Remove from specific room
            if room in self.active_connections:
                if websocket in self.active_connections[room]:
                    self.active_connections[room].remove(websocket)
                if not self.active_connections[room]:
                    del self.active_connections[room]
            
            # Update websocket rooms tracking
            if websocket in self.websocket_rooms:
                self.websocket_rooms[websocket].discard(room)
                if not self.websocket_rooms[websocket]:
                    del self.websocket_rooms[websocket]
        else:
            # Remove from all rooms
            if websocket in self.websocket_rooms:
                for ws_room in self.websocket_rooms[websocket].copy():
                    if ws_room in self.active_connections:
                        if websocket in self.active_connections[ws_room]:
                            self.active_connections[ws_room].remove(websocket)
                        if not self.active_connections[ws_room]:
                            del self.active_connections[ws_room]
                del self.websocket_rooms[websocket]
        
        logger.info(f"WebSocket disconnected from room: {room or 'all rooms'}")
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        """Send a message to a specific WebSocket connection."""
        try:
            await websocket.send_text(message)
        except Exception as e:
            logger.error(f"Error sending personal message: {str(e)}")
            self.disconnect(websocket)
    
    async def broadcast(self, room: str, message: str, exclude: WebSocket = None):
        """Broadcast a message to all connections in a room."""
        if room in self.active_connections:
            disconnected = []
            for connection in self.active_connections[room]:
                if connection != exclude:
                    try:
                        await connection.send_text(message)
                    except Exception as e:
                        logger.error(f"Error broadcasting message: {str(e)}")
                        disconnected.append(connection)
            
            # Clean up disconnected websockets
            for ws in disconnected:
                self.disconnect(ws, room)
    
    async def send_json(self, websocket: WebSocket, data: dict):
        """Send JSON data to a specific WebSocket connection."""
        try:
            await websocket.send_json(data)
        except Exception as e:
            logger.error(f"Error sending JSON message: {str(e)}")
            self.disconnect(websocket)
    
    async def broadcast_json(self, room: str, data: dict, exclude: WebSocket = None):
        """Broadcast JSON data to all connections in a room."""
        message = json.dumps(data)
        await self.broadcast(room, message, exclude)
    
    def get_room_connections(self, room: str) -> int:
        """Get the number of active connections in a room."""
        return len(self.active_connections.get(room, []))
    
    def get_all_rooms(self) -> List[str]:
        """Get a list of all active rooms."""
        return list(self.active_connections.keys())


# Global connection manager instance
manager = ConnectionManager()