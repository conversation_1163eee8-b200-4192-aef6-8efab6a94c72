"""
Tutor invitation models for manager-initiated tutor onboarding.
"""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field, field_validator, ConfigDict

from app.models.base import BaseEntity


class TutorInvitationBase(BaseModel):
    """Base tutor invitation model."""
    model_config = ConfigDict(from_attributes=True)
    
    email: str = Field(..., description="Email address to send invitation to")
    first_name: Optional[str] = Field(None, max_length=100, description="<PERSON><PERSON>'s first name")
    last_name: Optional[str] = Field(None, max_length=100, description="<PERSON><PERSON>'s last name")
    phone: Optional[str] = Field(None, max_length=20, description="<PERSON><PERSON>'s phone number")
    message: Optional[str] = Field(None, description="Personal message from manager")
    
    @field_validator("email")
    @classmethod
    def validate_email(cls, v: str) -> str:
        """Validate email format."""
        import re
        pattern = r'^[\w\.-]+@[\w\.-]+\.\w+$'
        if not re.match(pattern, v.lower()):
            raise ValueError("Invalid email format")
        return v.lower()
    
    @field_validator("phone")
    @classmethod
    def validate_phone(cls, v: Optional[str]) -> Optional[str]:
        """Validate and format phone numbers."""
        if v is None:
            return v
        
        # Remove all non-numeric characters
        import re
        digits = re.sub(r"\D", "", v)
        
        # Validate length (10 digits for North American numbers)
        if len(digits) == 10:
            # Format as (XXX) XXX-XXXX
            return f"({digits[:3]}) {digits[3:6]}-{digits[6:]}"
        elif len(digits) == 11 and digits[0] == "1":
            # Remove country code
            return f"({digits[1:4]}) {digits[4:7]}-{digits[7:]}"
        else:
            raise ValueError("Invalid phone number format")


class TutorInvitationCreate(TutorInvitationBase):
    """Schema for creating a tutor invitation."""
    pass


class TutorInvitation(TutorInvitationBase, BaseEntity):
    """Full tutor invitation model."""
    invitation_id: int = Field(..., description="Unique invitation ID")
    token: str = Field(..., description="Unique invitation token")
    invited_by: int = Field(..., description="User ID of manager who sent invitation")
    status: str = Field(default="pending", description="Invitation status")
    accepted_at: Optional[datetime] = Field(None, description="When invitation was accepted")
    accepted_user_id: Optional[int] = Field(None, description="User ID who accepted")
    expires_at: datetime = Field(..., description="When invitation expires")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)


class TutorInvitationWithInviter(TutorInvitation):
    """Tutor invitation with inviter information."""
    inviter_email: str
    inviter_first_name: str
    inviter_last_name: str


class TutorInvitationAccept(BaseModel):
    """Schema for accepting a tutor invitation."""
    token: str = Field(..., description="Invitation token")
    password: str = Field(..., min_length=8, description="Password for new account")
    
    @field_validator("password")
    @classmethod
    def validate_password_strength(cls, v: str) -> str:
        """Validate password meets minimum requirements."""
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters")
        
        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)
        
        if not (has_upper and has_lower and has_digit):
            raise ValueError(
                "Password must contain uppercase, lowercase, and numeric characters"
            )
        
        return v