import axios from 'axios';
import { LoginCredentials, AuthResponse, UserRoleType } from '../types/auth';
import { getApiBaseUrl } from '../utils/api-config';
import api from './api';

const API_BASE_URL = getApiBaseUrl();

// Configure axios defaults
axios.defaults.baseURL = API_BASE_URL;
axios.defaults.headers.common['Content-Type'] = 'application/json';

// Auth request/response interfaces
export interface RegisterRequest {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  roles: UserRoleType[];
  google_id?: string;
  phone_number?: string;
}

export interface PasswordResetRequest {
  email: string;
}

export interface PasswordResetConfirm {
  token: string;
  new_password: string;
}

export interface EmailVerificationRequest {
  email: string;
}

export interface EmailVerificationConfirm {
  token: string;
}

export interface RefreshTokenRequest {
  refresh_token: string;
}

export interface GoogleAuthResponse {
  auth_url: string;
}

export interface SwitchRoleRequest {
  role: UserRoleType;
}

class AuthService {
  private authToken: string | null = null;
  private refreshToken: string | null = null;
  private useMockData = false; // Using real API

  setAuthToken(token: string, refreshToken?: string) {
    this.authToken = token;
    if (refreshToken) {
      this.refreshToken = refreshToken;
    }
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    
    // Store in localStorage for persistence
    if (typeof window !== 'undefined') {
      localStorage.setItem('authToken', token);
      if (refreshToken) {
        localStorage.setItem('refreshToken', refreshToken);
      }
    }
  }

  clearAuthToken() {
    this.authToken = null;
    this.refreshToken = null;
    delete axios.defaults.headers.common['Authorization'];
    delete api.defaults.headers.common['Authorization'];
    
    // Clear from localStorage
    if (typeof window !== 'undefined') {
      localStorage.removeItem('authToken');
      localStorage.removeItem('refreshToken');
    }
  }

  getAuthToken(): string | null {
    if (!this.authToken && typeof window !== 'undefined') {
      this.authToken = localStorage.getItem('authToken');
      if (this.authToken) {
        axios.defaults.headers.common['Authorization'] = `Bearer ${this.authToken}`;
        api.defaults.headers.common['Authorization'] = `Bearer ${this.authToken}`;
      }
    }
    return this.authToken;
  }

  getRefreshToken(): string | null {
    if (!this.refreshToken && typeof window !== 'undefined') {
      this.refreshToken = localStorage.getItem('refreshToken');
    }
    return this.refreshToken;
  }

  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      const response = await api.post<AuthResponse>('/auth/login', credentials);
      const authData = response.data;
      
      // Store tokens
      this.setAuthToken(authData.accessToken, authData.refreshToken);
      
      return authData;
    } catch (error: any) {
      console.error('Login error:', error);
      throw new Error(error.response?.data?.detail || 'Invalid credentials');
    }
  }

  async register(data: RegisterRequest): Promise<AuthResponse> {
    try {
      const response = await api.post<AuthResponse>('/auth/register', data);
      const authData = response.data;
      
      // Store tokens
      this.setAuthToken(authData.accessToken, authData.refreshToken);
      
      return authData;
    } catch (error: any) {
      console.error('Registration error:', error);
      throw new Error(error.response?.data?.detail || 'Registration failed');
    }
  }

  async requestPasswordReset(email: string): Promise<{ message: string }> {
    try {
      const response = await api.post<{ message: string }>('/auth/reset-password', { email });
      return response.data;
    } catch (error: any) {
      console.error('Password reset request error:', error);
      throw new Error(error.response?.data?.detail || 'Failed to request password reset');
    }
  }

  async confirmPasswordReset(token: string, newPassword: string): Promise<{ message: string }> {
    try {
      const response = await api.post<{ message: string }>('/auth/reset-password/confirm', {
        token,
        new_password: newPassword
      });
      return response.data;
    } catch (error: any) {
      console.error('Password reset confirm error:', error);
      throw new Error(error.response?.data?.detail || 'Failed to reset password');
    }
  }

  async requestEmailVerification(email: string): Promise<{ message: string }> {
    try {
      const response = await api.post<{ message: string }>('/auth/verify-email', { email });
      return response.data;
    } catch (error: any) {
      console.error('Email verification request error:', error);
      throw new Error(error.response?.data?.detail || 'Failed to request email verification');
    }
  }

  async confirmEmailVerification(token: string): Promise<{ message: string; user: any }> {
    try {
      const response = await api.post<{ message: string; user: any }>('/auth/verify-email/confirm', { token });
      return response.data;
    } catch (error: any) {
      console.error('Email verification confirm error:', error);
      throw new Error(error.response?.data?.detail || 'Failed to verify email');
    }
  }

  async refreshAccessToken(): Promise<AuthResponse> {
    try {
      const refreshToken = this.getRefreshToken();
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await api.post<AuthResponse>('/auth/refresh', {
        refresh_token: refreshToken
      });
      const authData = response.data;
      
      // Update tokens
      this.setAuthToken(authData.accessToken, authData.refreshToken);
      
      return authData;
    } catch (error: any) {
      console.error('Token refresh error:', error);
      this.clearAuthToken();
      throw new Error(error.response?.data?.detail || 'Failed to refresh token');
    }
  }

  async getGoogleAuthUrl(): Promise<string> {
    try {
      const response = await api.get<GoogleAuthResponse>('/auth/google/login');
      return response.data.auth_url;
    } catch (error: any) {
      console.error('Google auth URL error:', error);
      throw new Error(error.response?.data?.detail || 'Failed to get Google auth URL');
    }
  }

  async handleGoogleCallback(code: string, state: string): Promise<AuthResponse> {
    try {
      const response = await api.get<AuthResponse>('/auth/google/callback', {
        params: { code, state }
      });
      const authData = response.data;
      
      // Store tokens
      this.setAuthToken(authData.accessToken, authData.refreshToken);
      
      return authData;
    } catch (error: any) {
      console.error('Google callback error:', error);
      throw new Error(error.response?.data?.detail || 'Google authentication failed');
    }
  }

  async logout(): Promise<void> {
    try {
      // Attempt to logout on server
      await api.post('/auth/logout');
    } catch (error) {
      console.error('Logout error:', error);
      // Continue with local cleanup even if server logout fails
    } finally {
      // Always clear local auth state
      this.clearAuthToken();
    }
  }

  async switchRole(role: UserRoleType): Promise<AuthResponse> {
    try {
      const response = await api.post<AuthResponse>('/auth/switch-role', { role });
      const authData = response.data;
      
      // Update tokens if provided
      if (authData.accessToken) {
        this.setAuthToken(authData.accessToken, authData.refreshToken);
      }
      
      return authData;
    } catch (error: any) {
      console.error('Role switch error:', error);
      throw new Error(error.response?.data?.detail || 'Failed to switch role');
    }
  }

  async getCurrentUser(): Promise<any> {
    try {
      const response = await api.get('/auth/me');
      const userData = response.data;
      
      console.log('getCurrentUser - Raw userData from API:', userData);
      
      // Map backend response to frontend User interface
      // The case conversion happens automatically in the interceptor
      // But we need to handle some specific field mappings
      const user = {
        ...userData,
        // Ensure roles is always an array
        roles: userData.roles || [],
        // Handle activeRole vs active_role (case conversion might not work perfectly)
        activeRole: userData.activeRole || userData.active_role || (userData.roles && userData.roles[0]) || UserRoleType.CLIENT,
        // Map name fields (backend might send first_name/last_name)
        firstName: userData.firstName || userData.first_name || '',
        lastName: userData.lastName || userData.last_name || '',
        // Map user ID fields
        userId: userData.userId || userData.user_id,
        // Map isEmailVerified to emailVerified for consistency
        emailVerified: userData.isEmailVerified || userData.emailVerified || false,
        // Set default values for optional fields
        isActive: userData.isActive !== undefined ? userData.isActive : true,
      };
      
      console.log('getCurrentUser - Mapped user object:', user);
      console.log('getCurrentUser - User roles:', user.roles, 'Length:', user.roles?.length);
      
      return user;
    } catch (error: any) {
      console.error('Get current user error:', error);
      throw new Error(error.response?.data?.detail || 'Failed to get current user');
    }
  }

  /**
   * Initialize auth state from stored tokens
   */
  async initializeAuth(): Promise<AuthResponse | null> {
    try {
      const token = this.getAuthToken();
      if (!token) {
        return null;
      }

      // Verify token is still valid by getting current user
      const response = await api.get<AuthResponse>('/auth/me');
      return response.data;
    } catch (error) {
      // Token is invalid, try to refresh
      try {
        return await this.refreshAccessToken();
      } catch (refreshError) {
        // Refresh failed, clear auth state
        this.clearAuthToken();
        return null;
      }
    }
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return !!this.getAuthToken();
  }

  /**
   * Set up axios request interceptor for token refresh
   */
  setupInterceptors() {
    // Request interceptor to add auth token
    api.interceptors.request.use(
      (config) => {
        const token = this.getAuthToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle 401 errors
    api.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            await this.refreshAccessToken();
            return api(originalRequest);
          } catch (refreshError) {
            // Redirect to login
            this.clearAuthToken();
            window.location.href = '/login';
            return Promise.reject(refreshError);
          }
        }

        return Promise.reject(error);
      }
    );
  }

  async sendPasswordResetEmail(userId: number, email: string, initiatedBy: string): Promise<void> {
    if (this.useMockData) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('Mock: Password reset email sent to', email, 'for user', userId, 'initiated by', initiatedBy);
      return;
    }
    
    await axios.post(`${API_BASE_URL}/auth/send-password-reset`, {
      user_id: userId,
      email,
      initiated_by: initiatedBy,
    });
  }
}

export const authService = new AuthService();