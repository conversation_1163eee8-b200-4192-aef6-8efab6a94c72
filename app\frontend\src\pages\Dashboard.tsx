import React from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../contexts/AuthContext';
import { UserRoleType } from '../types/auth';
import { 
  Users, 
  Calendar, 
  DollarSign, 
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  ArrowUp,
  ArrowDown,
  GraduationCap
} from 'lucide-react';
import { StatCard, StatGrid } from '../components/cards/StatCard';
import { Card } from '../components/common/Card';
import Button from '../components/common/Button';
import { ListItem, ListGroup } from '../components/lists/ListItem';
import { useNavigate } from 'react-router-dom';

interface StatData {
  title: string;
  value: string | number;
  change: number;
  icon: React.ReactNode;
  variant: 'default' | 'primary' | 'success' | 'warning' | 'error';
}

const Dashboard: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const navigate = useNavigate();

  const getStatsByRole = (): StatData[] => {
    switch (user?.activeRole) {
      case UserRoleType.MANAGER:
        return [
          {
            title: 'Total Users',
            value: '1,234',
            change: 12,
            icon: <Users className="w-6 h-6" />,
            variant: 'primary' as const,
          },
          {
            title: 'Active Sessions',
            value: '45',
            change: -5,
            icon: <Calendar className="w-6 h-6" />,
            variant: 'success' as const,
          },
          {
            title: 'Monthly Revenue',
            value: '$24,567',
            change: 18,
            icon: <DollarSign className="w-6 h-6" />,
            variant: 'warning' as const,
          },
          {
            title: 'Growth Rate',
            value: '23%',
            change: 8,
            icon: <TrendingUp className="w-6 h-6" />,
            variant: 'error' as const,
          },
        ];
      case UserRoleType.TUTOR:
        return [
          {
            title: 'My Students',
            value: '12',
            change: 2,
            icon: <Users className="w-6 h-6" />,
            variant: 'primary' as const,
          },
          {
            title: 'Upcoming Sessions',
            value: '8',
            change: 0,
            icon: <Calendar className="w-6 h-6" />,
            variant: 'success' as const,
          },
          {
            title: 'Hours This Week',
            value: '24',
            change: 10,
            icon: <Clock className="w-6 h-6" />,
            variant: 'warning' as const,
          },
          {
            title: 'Completion Rate',
            value: '95%',
            change: 3,
            icon: <CheckCircle className="w-6 h-6" />,
            variant: 'error' as const,
          },
        ];
      case UserRoleType.CLIENT:
        return [
          {
            title: 'Active Tutors',
            value: '3',
            change: 0,
            icon: <Users className="w-6 h-6" />,
            variant: 'primary' as const,
          },
          {
            title: 'Sessions This Month',
            value: '12',
            change: 4,
            icon: <Calendar className="w-6 h-6" />,
            variant: 'success' as const,
          },
          {
            title: 'Outstanding Balance',
            value: '$450',
            change: -25,
            icon: <DollarSign className="w-6 h-6" />,
            variant: 'warning' as const,
          },
          {
            title: 'Learning Progress',
            value: '78%',
            change: 5,
            icon: <TrendingUp className="w-6 h-6" />,
            variant: 'error' as const,
          },
        ];
      default:
        return [];
    }
  };

  const stats = getStatsByRole();

  const iconColors = {
    primary: 'text-accent-red',
    success: 'text-semantic-success',
    warning: 'text-semantic-warning',
    error: 'text-semantic-error',
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-text-primary">
          {t('header.welcome', { name: user?.firstName })}
        </h1>
        <p className="text-lg text-text-secondary mt-2">
          Here's what's happening with your tutoring activities today.
        </p>
      </div>

      {/* Stats Grid */}
      <StatGrid className="mb-8">
        {stats.map((stat, index) => (
          <StatCard
            key={index}
            title={stat.title}
            value={stat.value}
            trend={{
              value: stat.change,
              label: 'vs last month'
            }}
            icon={<span className={iconColors[stat.variant]}>{stat.icon}</span>}
            variant={index === 0 ? 'primary' : 'default'}
          />
        ))}
      </StatGrid>

      {/* Quick Actions */}
      <Card className="mb-8">
        <div className="p-6">
          <h2 className="text-xl font-semibold text-text-primary mb-6">
            Quick Actions
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {user?.activeRole === UserRoleType.MANAGER && (
              <>
                <Button variant="ghost" size="lg" className="flex-col h-auto py-4 hover:bg-red-50 hover:border-accent-red group">
                  <Users className="w-6 h-6 mb-2 text-accent-red group-hover:scale-110 transition-transform" />
                  <span className="text-sm">Add User</span>
                </Button>
                <Button variant="ghost" size="lg" className="flex-col h-auto py-4 hover:bg-red-50 hover:border-accent-red group">
                  <Calendar className="w-6 h-6 mb-2 text-accent-red group-hover:scale-110 transition-transform" />
                  <span className="text-sm">Schedule Session</span>
                </Button>
                <Button variant="ghost" size="lg" className="flex-col h-auto py-4 hover:bg-red-50 hover:border-accent-red group">
                  <DollarSign className="w-6 h-6 mb-2 text-accent-red group-hover:scale-110 transition-transform" />
                  <span className="text-sm">Generate Invoice</span>
                </Button>
                <Button variant="ghost" size="lg" className="flex-col h-auto py-4 hover:bg-red-50 hover:border-accent-red group">
                  <TrendingUp className="w-6 h-6 mb-2 text-accent-red group-hover:scale-110 transition-transform" />
                  <span className="text-sm">View Reports</span>
                </Button>
              </>
            )}
            {user?.activeRole === UserRoleType.TUTOR && (
              <>
                <Button variant="ghost" size="lg" className="flex-col h-auto py-4 hover:bg-red-50 hover:border-accent-red group">
                  <Calendar className="w-6 h-6 mb-2 text-accent-red group-hover:scale-110 transition-transform" />
                  <span className="text-sm">View Schedule</span>
                </Button>
                <Button variant="ghost" size="lg" className="flex-col h-auto py-4 hover:bg-red-50 hover:border-accent-red group">
                  <Clock className="w-6 h-6 mb-2 text-accent-red group-hover:scale-110 transition-transform" />
                  <span className="text-sm">Request Time Off</span>
                </Button>
                <Button variant="ghost" size="lg" className="flex-col h-auto py-4 hover:bg-red-50 hover:border-accent-red group">
                  <Users className="w-6 h-6 mb-2 text-accent-red group-hover:scale-110 transition-transform" />
                  <span className="text-sm">My Students</span>
                </Button>
                <Button variant="ghost" size="lg" className="flex-col h-auto py-4 hover:bg-red-50 hover:border-accent-red group">
                  <CheckCircle className="w-6 h-6 mb-2 text-accent-red group-hover:scale-110 transition-transform" />
                  <span className="text-sm">Submit Report</span>
                </Button>
              </>
            )}
            {user?.activeRole === UserRoleType.CLIENT && (
              <>
                <Button 
                  variant="ghost" 
                  size="lg" 
                  className="flex-col h-auto py-4 hover:bg-red-50 hover:border-accent-red group"
                  onClick={() => navigate('/services')}
                >
                  <GraduationCap className="w-6 h-6 mb-2 text-accent-red group-hover:scale-110 transition-transform" />
                  <span className="text-sm">View Services</span>
                </Button>
                <Button 
                  variant="ghost" 
                  size="lg" 
                  className="flex-col h-auto py-4 hover:bg-red-50 hover:border-accent-red group"
                  onClick={() => navigate('/appointments/book')}
                >
                  <Calendar className="w-6 h-6 mb-2 text-accent-red group-hover:scale-110 transition-transform" />
                  <span className="text-sm">Book Session</span>
                </Button>
                <Button variant="ghost" size="lg" className="flex-col h-auto py-4 hover:bg-red-50 hover:border-accent-red group">
                  <DollarSign className="w-6 h-6 mb-2 text-accent-red group-hover:scale-110 transition-transform" />
                  <span className="text-sm">View Invoices</span>
                </Button>
                <Button variant="ghost" size="lg" className="flex-col h-auto py-4 hover:bg-red-50 hover:border-accent-red group">
                  <Users className="w-6 h-6 mb-2 text-accent-red group-hover:scale-110 transition-transform" />
                  <span className="text-sm">My Tutors</span>
                </Button>
                <Button variant="ghost" size="lg" className="flex-col h-auto py-4 hover:bg-red-50 hover:border-accent-red group">
                  <TrendingUp className="w-6 h-6 mb-2 text-accent-red group-hover:scale-110 transition-transform" />
                  <span className="text-sm">Progress Report</span>
                </Button>
              </>
            )}
          </div>
        </div>
      </Card>

      {/* Recent Activity */}
      <Card>
        <div className="p-6">
          <h2 className="text-xl font-semibold text-text-primary mb-6">
            Recent Activity
          </h2>
          <ListGroup>
            <ListItem
              title="New session scheduled"
              subtitle="2 hours ago"
              description="With Marie Dubois"
              icon={<Calendar className="w-4 h-4" />}
              onClick={() => {}}
            />
            <ListItem
              title="Session completed"
              subtitle="5 hours ago"
              description="With Jean Tremblay"
              icon={<CheckCircle className="w-4 h-4" />}
              onClick={() => {}}
            />
            <ListItem
              title="Invoice #1234 paid"
              subtitle="Yesterday"
              description="Amount: $450.00"
              icon={<DollarSign className="w-4 h-4" />}
              onClick={() => {}}
            />
          </ListGroup>
        </div>
      </Card>
    </div>
  );
};

export default Dashboard;