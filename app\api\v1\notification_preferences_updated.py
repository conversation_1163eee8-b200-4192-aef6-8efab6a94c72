"""
Notification preferences API endpoints - Updated to use unified preference service.
"""

from typing import Dict, Any, List, Optional
from fastapi import API<PERSON>outer, Depends, HTTPException, status
import asyncpg

from app.core.dependencies import get_current_user, get_db
from app.core.auth_decorators import require_roles
from app.models.user_models import User
from app.models.base import UserRoleType
from app.models.notification_models import NotificationType, NotificationChannel
from app.models.user_preference_models import UserPreferenceUpdate
from app.services.user_preference_service import get_user_preference_service

router = APIRouter()


@router.get("/{user_id}")
async def get_user_preferences(
    user_id: int,
    current_user: User = Depends(get_current_user),
    db: asyncpg.Connection = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get user's notification preferences.
    
    Users can view their own preferences.
    Managers can view any user's preferences.
    """
    
    # Check permissions
    if current_user.user_id != user_id and UserRoleType.MANAGER not in current_user.roles:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You can only view your own notification preferences"
        )
    
    preference_service = get_user_preference_service()
    
    try:
        # Get user preferences
        result = await preference_service.get_user_preferences(user_id)
        
        if not result.preferences:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User preferences not found"
            )
        
        # Convert to API response format
        preferences = result.preferences
        response = {
            'user_id': preferences.user_id,
            'global_settings': {
                'language': preferences.preferred_language,
                'timezone': 'America/New_York'  # Default timezone
            },
            'notification_preferences': preferences.notification_settings or {},
            'created_at': preferences.created_at.isoformat() if preferences.created_at else None,
            'updated_at': preferences.updated_at.isoformat() if preferences.updated_at else None
        }
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving preferences: {str(e)}"
        )


@router.put("/{user_id}")
async def update_user_preferences(
    user_id: int,
    preferences_data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: asyncpg.Connection = Depends(get_db)
) -> Dict[str, Any]:
    """
    Update user's notification preferences.
    
    Request body:
    {
        "notification_preferences": {
            "appointment_reminder": {
                "push": true,
                "sms": true,
                "email": false
            },
            "payment_due": {
                "push": true,
                "sms": true,
                "email": true
            }
        }
    }
    """
    
    # Check permissions
    if current_user.user_id != user_id and UserRoleType.MANAGER not in current_user.roles:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You can only update your own notification preferences"
        )
    
    preference_service = get_user_preference_service()
    
    try:
        # Extract notification preferences from request
        notification_prefs = preferences_data.get('notification_preferences', {})
        
        if not notification_prefs:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No notification preferences provided"
            )
        
        # Update preferences
        update_data = UserPreferenceUpdate(
            notification_settings=notification_prefs
        )
        
        result = await preference_service.update_user_preferences(
            user_id=user_id,
            update_data=update_data
        )
        
        if not result.success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to update notification preferences"
            )
        
        return {
            "success": True,
            "message": "Notification preferences updated successfully",
            "user_id": user_id,
            "notification_preferences": result.preferences.notification_settings
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating preferences: {str(e)}"
        )


@router.put("/{user_id}/{notification_type}")
async def update_specific_notification(
    user_id: int,
    notification_type: str,
    channel_settings: Dict[str, bool],
    current_user: User = Depends(get_current_user),
    db: asyncpg.Connection = Depends(get_db)
) -> Dict[str, Any]:
    """
    Update settings for a specific notification type.
    
    Request body:
    {
        "push": true,
        "sms": false,
        "email": true
    }
    """
    
    # Check permissions
    if current_user.user_id != user_id and UserRoleType.MANAGER not in current_user.roles:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You can only update your own notification preferences"
        )
    
    preference_service = get_user_preference_service()
    
    try:
        # Validate notification type
        valid_types = [
            'appointment_reminder', 'payment_due', 'session_confirmed',
            'general', 'payment_ready', 'system_alert'
        ]
        
        if notification_type not in valid_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid notification type. Valid types: {valid_types}"
            )
        
        # Validate channel settings
        valid_channels = ['push', 'sms', 'email']
        for channel in channel_settings:
            if channel not in valid_channels:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid channel: {channel}. Valid channels: {valid_channels}"
                )
        
        # Update specific notification settings
        result = await preference_service.update_notification_settings(
            user_id=user_id,
            notification_type=notification_type,
            settings=channel_settings
        )
        
        if not result.success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to update notification settings"
            )
        
        return {
            "success": True,
            "message": f"Updated {notification_type} notification settings",
            "notification_type": notification_type,
            "settings": channel_settings
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating notification settings: {str(e)}"
        )


@router.get("/{user_id}/summary")
async def get_notification_summary(
    user_id: int,
    current_user: User = Depends(get_current_user),
    db: asyncpg.Connection = Depends(get_db)
) -> Dict[str, Any]:
    """Get a summary of user's notification preferences."""
    
    # Check permissions
    if current_user.user_id != user_id and UserRoleType.MANAGER not in current_user.roles:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You can only view your own notification preferences"
        )
    
    preference_service = get_user_preference_service()
    
    try:
        result = await preference_service.get_user_preferences(user_id)
        
        if not result.preferences:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User preferences not found"
            )
        
        # Create summary
        notification_settings = result.preferences.notification_settings or {}
        
        summary = {
            "user_id": user_id,
            "total_notification_types": len(notification_settings),
            "channels_enabled": {
                "push": 0,
                "sms": 0,
                "email": 0
            },
            "all_enabled": [],
            "all_disabled": [],
            "partially_enabled": []
        }
        
        # Analyze each notification type
        for notif_type, settings in notification_settings.items():
            push = settings.get('push', False)
            sms = settings.get('sms', False)
            email = settings.get('email', False)
            
            # Count enabled channels
            if push:
                summary["channels_enabled"]["push"] += 1
            if sms:
                summary["channels_enabled"]["sms"] += 1
            if email:
                summary["channels_enabled"]["email"] += 1
            
            # Categorize notification types
            enabled_count = sum([push, sms, email])
            if enabled_count == 3:
                summary["all_enabled"].append(notif_type)
            elif enabled_count == 0:
                summary["all_disabled"].append(notif_type)
            else:
                summary["partially_enabled"].append({
                    "type": notif_type,
                    "enabled_channels": [
                        ch for ch, enabled in [("push", push), ("sms", sms), ("email", email)]
                        if enabled
                    ]
                })
        
        return summary
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating summary: {str(e)}"
        )


@router.post("/{user_id}/reset")
@require_roles([UserRoleType.MANAGER])
async def reset_to_defaults(
    user_id: int,
    current_user: User = Depends(get_current_user),
    db: asyncpg.Connection = Depends(get_db)
) -> Dict[str, Any]:
    """Reset user's notification preferences to defaults based on their role."""
    
    preference_service = get_user_preference_service()
    
    try:
        # Get user to determine role
        from app.database.repositories.user_repository import UserRepository
        user_repo = UserRepository()
        
        async with db.transaction():
            target_user = await user_repo.get_user_by_id(db, user_id)
            
            if not target_user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )
            
            # Determine default settings based on role
            primary_role = target_user.roles[0] if target_user.roles else UserRoleType.CLIENT
            
            if primary_role == UserRoleType.MANAGER:
                default_settings = {
                    'appointment_reminder': {'push': True, 'sms': False, 'email': True},
                    'payment_due': {'push': True, 'sms': False, 'email': True},
                    'system_alert': {'push': True, 'sms': True, 'email': True},
                    'general': {'push': True, 'sms': False, 'email': True}
                }
            elif primary_role == UserRoleType.TUTOR:
                default_settings = {
                    'appointment_reminder': {'push': True, 'sms': True, 'email': True},
                    'payment_ready': {'push': True, 'sms': False, 'email': True},
                    'session_confirmed': {'push': True, 'sms': True, 'email': True},
                    'general': {'push': True, 'sms': False, 'email': True}
                }
            else:  # CLIENT
                default_settings = {
                    'appointment_reminder': {'push': True, 'sms': True, 'email': True},
                    'payment_due': {'push': True, 'sms': True, 'email': True},
                    'session_confirmed': {'push': True, 'sms': False, 'email': True},
                    'general': {'push': True, 'sms': False, 'email': True}
                }
            
            # Update to defaults
            update_data = UserPreferenceUpdate(
                notification_settings=default_settings
            )
            
            result = await preference_service.update_user_preferences(
                user_id=user_id,
                update_data=update_data,
                updated_by=current_user.user_id
            )
            
            if not result.success:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Failed to reset notification preferences"
                )
            
            return {
                "success": True,
                "message": f"Reset notification preferences to {primary_role.value} defaults",
                "user_id": user_id,
                "role": primary_role.value,
                "new_settings": default_settings
            }
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error resetting preferences: {str(e)}"
        )