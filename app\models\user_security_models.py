"""
User Security Models for the unified security system.
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, <PERSON>, validator
from enum import Enum


class TwoFactorMethod(str, Enum):
    """Two-factor authentication methods."""
    
    SMS = "sms"
    EMAIL = "email"
    TOTP = "totp"  # Time-based One-Time Password (Authenticator apps)


class SecurityEventType(str, Enum):
    """Types of security events."""
    
    LOGIN_SUCCESS = "login_success"
    LOGIN_FAILED = "login_failed"
    LOGOUT = "logout"
    PASSWORD_CHANGED = "password_changed"
    PASSWORD_RESET_REQUESTED = "password_reset_requested"
    PASSWORD_RESET_COMPLETED = "password_reset_completed"
    TWO_FACTOR_ENABLED = "two_factor_enabled"
    TWO_FACTOR_DISABLED = "two_factor_disabled"
    TWO_FACTOR_SETUP_INITIATED = "two_factor_setup_initiated"
    TWO_FACTOR_VERIFIED = "two_factor_verified"
    TWO_FACTOR_FAILED = "two_factor_failed"
    BACKUP_CODE_USED = "backup_code_used"
    BACKUP_CODES_REGENERATED = "backup_codes_regenerated"
    TRUSTED_DEVICE_ADDED = "trusted_device_added"
    TRUSTED_DEVICE_REMOVED = "trusted_device_removed"
    ACCOUNT_LOCKED = "account_locked"
    ACCOUNT_UNLOCKED = "account_unlocked"
    SETTINGS_UPDATED = "settings_updated"
    PASSWORD_CHANGE_REQUIRED = "password_change_required"
    SUSPICIOUS_ACTIVITY = "suspicious_activity"


class UserSecurity(BaseModel):
    """Complete user security settings model."""
    
    user_id: int
    
    # Two-factor authentication
    two_factor_enabled: bool = False
    two_factor_method: Optional[TwoFactorMethod] = None
    two_factor_secret: Optional[str] = None  # Encrypted
    two_factor_backup_codes: List[str] = Field(default_factory=list)  # Hashed
    two_factor_verified_at: Optional[datetime] = None
    
    # Trusted devices (JSONB array)
    trusted_devices: List[Dict[str, Any]] = Field(default_factory=list)
    
    # Security settings
    require_password_change: bool = False
    last_password_change: datetime
    failed_login_attempts: int = 0
    locked_until: Optional[datetime] = None
    
    # Timestamps
    created_at: datetime
    updated_at: datetime
    
    # Optional metadata (not stored in main table)
    metadata: Optional[Dict[str, Any]] = None


class UserSecurityUpdate(BaseModel):
    """Model for updating user security settings."""
    
    # Two-factor authentication
    two_factor_enabled: Optional[bool] = None
    two_factor_method: Optional[TwoFactorMethod] = None
    two_factor_secret: Optional[str] = None
    two_factor_backup_codes: Optional[List[str]] = None
    two_factor_verified_at: Optional[datetime] = None
    
    # Trusted devices
    trusted_devices: Optional[List[Dict[str, Any]]] = None
    
    # Security settings
    require_password_change: Optional[bool] = None
    last_password_change: Optional[datetime] = None
    failed_login_attempts: Optional[int] = None
    locked_until: Optional[datetime] = None


class TrustedDeviceInfo(BaseModel):
    """Information about a trusted device."""
    
    device_fingerprint: str
    device_name: str = "Unknown Device"
    last_used_at: datetime
    expires_at: datetime
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    trust_token: Optional[str] = None  # Only included when creating


class SecurityChallenge(BaseModel):
    """Security challenge for 2FA."""
    
    challenge_id: str
    user_id: int
    method: TwoFactorMethod
    purpose: str = "verification"  # verification, login, sensitive_action
    created_at: datetime
    expires_at: datetime
    attempts: int = 0
    max_attempts: int = 3


class TwoFactorSetupRequest(BaseModel):
    """Request to setup 2FA."""
    
    method: TwoFactorMethod
    phone_number: Optional[str] = None  # For SMS
    email: Optional[str] = None  # For email (if different from account email)


class TwoFactorVerifyRequest(BaseModel):
    """Request to verify 2FA code."""
    
    code: str
    device_fingerprint: Optional[str] = None
    trust_device: bool = False


class SecuritySettings(BaseModel):
    """User's security preferences."""
    
    enforce_2fa: bool = False
    session_timeout_minutes: Optional[int] = None
    allowed_ip_ranges: List[str] = Field(default_factory=list)
    password_expiry_days: Optional[int] = None
    min_password_strength: int = 3  # 0-5 scale


class BackupCode(BaseModel):
    """Backup code information."""
    
    code: str  # Plain text when generated, hashed when stored
    created_at: datetime
    used_at: Optional[datetime] = None


class SecurityEvent(BaseModel):
    """Security event log entry."""
    
    user_id: int
    event_type: SecurityEventType
    event_data: Dict[str, Any] = Field(default_factory=dict)
    timestamp: datetime
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    risk_score: Optional[float] = None  # 0.0 - 1.0


class SecurityAuditResult(BaseModel):
    """Result of security audit."""
    
    user_id: int
    audit_date: datetime
    has_2fa: bool
    has_strong_password: bool
    days_since_password_change: int
    recent_suspicious_activities: int
    trusted_devices_count: int
    active_sessions_count: int
    recommendations: List[str] = Field(default_factory=list)
    overall_security_score: float  # 0.0 - 100.0


class AccountRecoveryRequest(BaseModel):
    """Request for account recovery."""
    
    email: str
    recovery_method: str  # email, security_questions, support
    additional_info: Optional[Dict[str, Any]] = None


# Re-imports for compatibility
from app.models.user_models import User, UserRoleType