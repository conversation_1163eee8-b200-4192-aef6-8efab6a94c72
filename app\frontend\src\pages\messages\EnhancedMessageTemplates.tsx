import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  FileText, Plus, Edit, Trash2, Copy, Search, Filter,
  MessageSquare, Mail, Phone, Globe, Tag, Clock,
  CheckCircle, XCircle, Info, Eye, Code, Send
} from 'lucide-react';
import { Card } from '../../components/common/Card';
import Button from '../../components/common/Button';
import { Badge } from '../../components/common/Badge';
import { Input } from '../../components/common/Input';
import { Select } from '../../components/common/Select';
import { Modal } from '../../components/common/Modal';
import { Switch } from '../../components/common/Switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/common/TabsAdapter';
import toast from 'react-hot-toast';

interface MessageTemplate {
  template_id: number;
  template_name: string;
  template_key: string;
  category: 'appointment' | 'payment' | 'welcome' | 'reminder' | 'notification' | 'marketing';
  channel: 'sms' | 'email' | 'push' | 'all';
  subject?: string;
  content_en: string;
  content_fr: string;
  variables: string[];
  is_active: boolean;
  usage_count: number;
  last_used?: string;
  created_at: string;
  updated_at: string;
  tags: string[];
}

interface TemplateSummary {
  totalTemplates: number;
  activeTemplates: number;
  smsTemplates: number;
  emailTemplates: number;
  pushTemplates: number;
  recentlyUsed: number;
}

const EnhancedMessageTemplates: React.FC = () => {
  const { t } = useTranslation();
  const [templates, setTemplates] = useState<MessageTemplate[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<MessageTemplate | null>(null);
  const [showTemplateModal, setShowTemplateModal] = useState(false);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [channelFilter, setChannelFilter] = useState('all');
  const [activeLanguage, setActiveLanguage] = useState<'en' | 'fr'>('en');
  const [summary, setSummary] = useState<TemplateSummary>({
    totalTemplates: 45,
    activeTemplates: 38,
    smsTemplates: 18,
    emailTemplates: 15,
    pushTemplates: 12,
    recentlyUsed: 24
  });

  // Mock template data
  useEffect(() => {
    const mockTemplates: MessageTemplate[] = [
      {
        template_id: 1,
        template_name: 'Appointment Confirmation',
        template_key: 'appointment_confirmation',
        category: 'appointment',
        channel: 'all',
        subject: 'Appointment Confirmed - {{date}} at {{time}}',
        content_en: 'Hi {{client_name}}, your {{service}} appointment with {{tutor_name}} is confirmed for {{date}} at {{time}}. Location: {{location}}. Reply CANCEL to cancel.',
        content_fr: 'Bonjour {{client_name}}, votre rendez-vous de {{service}} avec {{tutor_name}} est confirmé pour le {{date}} à {{time}}. Lieu: {{location}}. Répondez ANNULER pour annuler.',
        variables: ['client_name', 'service', 'tutor_name', 'date', 'time', 'location'],
        is_active: true,
        usage_count: 1247,
        last_used: '2024-11-21T10:30:00',
        created_at: '2024-01-15',
        updated_at: '2024-11-20',
        tags: ['appointment', 'confirmation', 'critical']
      },
      {
        template_id: 2,
        template_name: '24-Hour Reminder',
        template_key: 'appointment_reminder_24h',
        category: 'reminder',
        channel: 'sms',
        content_en: 'Reminder: You have a {{service}} session tomorrow at {{time}} with {{tutor_name}}. Location: {{location}}.',
        content_fr: 'Rappel: Vous avez une session de {{service}} demain à {{time}} avec {{tutor_name}}. Lieu: {{location}}.',
        variables: ['service', 'time', 'tutor_name', 'location'],
        is_active: true,
        usage_count: 892,
        last_used: '2024-11-21T08:00:00',
        created_at: '2024-01-20',
        updated_at: '2024-11-18'
      },
      {
        template_id: 3,
        template_name: 'Payment Received',
        template_key: 'payment_received',
        category: 'payment',
        channel: 'email',
        subject: 'Payment Received - Invoice #{{invoice_number}}',
        content_en: 'Dear {{client_name}},\n\nWe have received your payment of {{amount}} for invoice #{{invoice_number}}.\n\nThank you for your prompt payment.\n\nBest regards,\nTutorAide Team',
        content_fr: 'Cher(e) {{client_name}},\n\nNous avons reçu votre paiement de {{amount}} pour la facture #{{invoice_number}}.\n\nMerci pour votre paiement rapide.\n\nCordialement,\nL\'équipe TutorAide',
        variables: ['client_name', 'amount', 'invoice_number'],
        is_active: true,
        usage_count: 456,
        last_used: '2024-11-20T14:45:00',
        created_at: '2024-02-01',
        updated_at: '2024-11-15'
      },
      {
        template_id: 4,
        template_name: 'Welcome New Client',
        template_key: 'welcome_client',
        category: 'welcome',
        channel: 'email',
        subject: 'Welcome to TutorAide!',
        content_en: 'Welcome {{client_name}}!\n\nThank you for joining TutorAide. We\'re excited to help you achieve your learning goals.\n\nTo get started:\n1. Complete your profile\n2. Browse available tutors\n3. Book your first session\n\nIf you have any questions, reply to this email or call us at 1-800-TUTORS.\n\nBest regards,\nThe TutorAide Team',
        content_fr: 'Bienvenue {{client_name}}!\n\nMerci de rejoindre TutorAide. Nous sommes ravis de vous aider à atteindre vos objectifs d\'apprentissage.\n\nPour commencer:\n1. Complétez votre profil\n2. Parcourez les tuteurs disponibles\n3. Réservez votre première session\n\nSi vous avez des questions, répondez à ce courriel ou appelez-nous au 1-800-TUTORS.\n\nCordialement,\nL\'équipe TutorAide',
        variables: ['client_name'],
        is_active: true,
        usage_count: 234,
        last_used: '2024-11-19T16:20:00',
        created_at: '2024-01-10',
        updated_at: '2024-10-05'
      }
    ];
    
    setTemplates(mockTemplates);
  }, []);

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = searchTerm === '' || 
      template.template_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.template_key.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.content_en.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.content_fr.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = categoryFilter === 'all' || template.category === categoryFilter;
    const matchesChannel = channelFilter === 'all' || template.channel === channelFilter || template.channel === 'all';
    
    return matchesSearch && matchesCategory && matchesChannel;
  });

  const handleSaveTemplate = async (templateData: Partial<MessageTemplate>) => {
    setLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    if (editMode && selectedTemplate) {
      setTemplates(templates.map(template => 
        template.template_id === selectedTemplate.template_id 
          ? { ...template, ...templateData, updated_at: new Date().toISOString() }
          : template
      ));
      toast.success('Template updated successfully');
    } else {
      const newTemplate: MessageTemplate = {
        template_id: templates.length + 1,
        ...templateData as MessageTemplate,
        usage_count: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      setTemplates([...templates, newTemplate]);
      toast.success('Template created successfully');
    }
    
    setShowTemplateModal(false);
    setSelectedTemplate(null);
    setEditMode(false);
    setLoading(false);
  };

  const handleDeleteTemplate = async (templateId: number) => {
    if (!confirm('Are you sure you want to delete this template? This action cannot be undone.')) return;
    
    setLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    setTemplates(templates.filter(template => template.template_id !== templateId));
    toast.success('Template deleted successfully');
    setLoading(false);
  };

  const handleDuplicateTemplate = (template: MessageTemplate) => {
    const duplicatedTemplate = {
      ...template,
      template_name: `${template.template_name} (Copy)`,
      template_key: `${template.template_key}_copy`,
      template_id: 0,
      usage_count: 0,
      last_used: undefined
    };
    
    setSelectedTemplate(duplicatedTemplate);
    setEditMode(false);
    setShowTemplateModal(true);
  };

  const handleTestTemplate = async (template: MessageTemplate) => {
    setLoading(true);
    // Simulate sending test
    await new Promise(resolve => setTimeout(resolve, 2000));
    toast.success(`Test ${template.channel} sent successfully`);
    setLoading(false);
  };

  const getCategoryBadge = (category: string) => {
    const categoryConfig: Record<string, { color: string; icon: React.ReactNode }> = {
      appointment: { color: 'bg-blue-100 text-blue-700', icon: <Clock className="w-4 h-4" /> },
      payment: { color: 'bg-green-100 text-green-700', icon: <DollarSign className="w-4 h-4" /> },
      welcome: { color: 'bg-purple-100 text-purple-700', icon: <MessageSquare className="w-4 h-4" /> },
      reminder: { color: 'bg-yellow-100 text-yellow-700', icon: <Clock className="w-4 h-4" /> },
      notification: { color: 'bg-gray-100 text-gray-700', icon: <Info className="w-4 h-4" /> },
      marketing: { color: 'bg-pink-100 text-pink-700', icon: <Tag className="w-4 h-4" /> }
    };

    const config = categoryConfig[category] || categoryConfig.notification;
    return (
      <Badge className={`inline-flex items-center gap-1 ${config.color}`}>
        {config.icon}
        <span className="capitalize">{category}</span>
      </Badge>
    );
  };

  const getChannelIcon = (channel: string) => {
    switch (channel) {
      case 'sms':
        return <Phone className="w-4 h-4" />;
      case 'email':
        return <Mail className="w-4 h-4" />;
      case 'push':
        return <Globe className="w-4 h-4" />;
      default:
        return <MessageSquare className="w-4 h-4" />;
    }
  };

  const extractVariables = (content: string): string[] => {
    const matches = content.match(/\{\{(\w+)\}\}/g);
    if (!matches) return [];
    
    const variables = matches.map(match => match.replace(/\{\{|\}\}/g, ''));
    return [...new Set(variables)];
  };

  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Message Templates</h1>
          <p className="text-gray-600 mt-1">
            Manage email, SMS, and push notification templates in multiple languages
          </p>
        </div>
        <Button
          variant="primary"
          onClick={() => {
            setSelectedTemplate(null);
            setEditMode(false);
            setShowTemplateModal(true);
          }}
        >
          <Plus className="w-4 h-4 mr-2" />
          Create Template
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <FileText className="w-6 h-6 text-blue-600" />
            <span className="text-xs text-gray-500">Total</span>
          </div>
          <p className="text-xl font-bold">{summary.totalTemplates}</p>
          <p className="text-xs text-green-600 mt-1">{summary.activeTemplates} active</p>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Phone className="w-6 h-6 text-green-600" />
            <span className="text-xs text-gray-500">SMS</span>
          </div>
          <p className="text-xl font-bold">{summary.smsTemplates}</p>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Mail className="w-6 h-6 text-purple-600" />
            <span className="text-xs text-gray-500">Email</span>
          </div>
          <p className="text-xl font-bold">{summary.emailTemplates}</p>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Globe className="w-6 h-6 text-red-600" />
            <span className="text-xs text-gray-500">Push</span>
          </div>
          <p className="text-xl font-bold">{summary.pushTemplates}</p>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Clock className="w-6 h-6 text-yellow-600" />
            <span className="text-xs text-gray-500">Used (24h)</span>
          </div>
          <p className="text-xl font-bold">{summary.recentlyUsed}</p>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Globe className="w-6 h-6 text-gray-600" />
            <span className="text-xs text-gray-500">Languages</span>
          </div>
          <div className="flex gap-2">
            <Badge className="bg-blue-100 text-blue-700">EN</Badge>
            <Badge className="bg-red-100 text-red-700">FR</Badge>
          </div>
        </Card>
      </div>

      {/* Filters */}
      <Card className="p-4">
        <div className="flex flex-col lg:flex-row gap-4 items-center">
          <div className="flex gap-3 flex-1">
            <div className="relative flex-1 lg:max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                placeholder="Search templates..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              options={[
                { value: 'all', label: 'All Categories' },
                { value: 'appointment', label: 'Appointment' },
                { value: 'payment', label: 'Payment' },
                { value: 'welcome', label: 'Welcome' },
                { value: 'reminder', label: 'Reminder' },
                { value: 'notification', label: 'Notification' },
                { value: 'marketing', label: 'Marketing' }
              ]}
              className="w-48"
            />
            <Select
              value={channelFilter}
              onChange={(e) => setChannelFilter(e.target.value)}
              options={[
                { value: 'all', label: 'All Channels' },
                { value: 'sms', label: 'SMS' },
                { value: 'email', label: 'Email' },
                { value: 'push', label: 'Push' }
              ]}
              className="w-40"
            />
          </div>
          
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">Language:</span>
            <div className="flex rounded-md shadow-sm">
              <button
                className={`px-3 py-1 text-sm font-medium rounded-l-md ${
                  activeLanguage === 'en' 
                    ? 'bg-accent-red text-white' 
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
                onClick={() => setActiveLanguage('en')}
              >
                EN
              </button>
              <button
                className={`px-3 py-1 text-sm font-medium rounded-r-md ${
                  activeLanguage === 'fr' 
                    ? 'bg-accent-red text-white' 
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
                onClick={() => setActiveLanguage('fr')}
              >
                FR
              </button>
            </div>
          </div>
        </div>
      </Card>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {filteredTemplates.map((template) => (
          <Card key={template.template_id} className="p-6 hover:shadow-elevated transition-all">
            <div className="flex justify-between items-start mb-4">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <h3 className="font-semibold text-gray-900">{template.template_name}</h3>
                  {!template.is_active && (
                    <Badge className="bg-gray-100 text-gray-600">Inactive</Badge>
                  )}
                </div>
                <p className="text-sm text-gray-500 font-mono">{template.template_key}</p>
              </div>
              <div className="flex items-center gap-2">
                {getChannelIcon(template.channel)}
                {template.channel === 'all' && (
                  <>
                    <Phone className="w-4 h-4 text-gray-400" />
                    <Mail className="w-4 h-4 text-gray-400" />
                  </>
                )}
              </div>
            </div>

            <div className="flex gap-2 mb-3">
              {getCategoryBadge(template.category)}
              {template.tags.map(tag => (
                <Badge key={tag} className="bg-gray-100 text-gray-600">
                  {tag}
                </Badge>
              ))}
            </div>

            {template.subject && (
              <div className="mb-3">
                <p className="text-xs text-gray-500 mb-1">Subject:</p>
                <p className="text-sm font-medium">{template.subject}</p>
              </div>
            )}

            <div className="mb-3">
              <p className="text-xs text-gray-500 mb-1">Content ({activeLanguage.toUpperCase()}):</p>
              <p className="text-sm text-gray-700 line-clamp-3 whitespace-pre-wrap">
                {activeLanguage === 'en' ? template.content_en : template.content_fr}
              </p>
            </div>

            <div className="mb-4">
              <p className="text-xs text-gray-500 mb-1">Variables:</p>
              <div className="flex flex-wrap gap-1">
                {template.variables.map(variable => (
                  <Badge key={variable} className="bg-blue-50 text-blue-600 text-xs font-mono">
                    {`{{${variable}}}`}
                  </Badge>
                ))}
              </div>
            </div>

            <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
              <span>Used {template.usage_count} times</span>
              {template.last_used && (
                <span>Last used {new Date(template.last_used).toLocaleDateString()}</span>
              )}
            </div>

            <div className="flex gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setSelectedTemplate(template);
                  setShowPreviewModal(true);
                }}
              >
                <Eye className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setSelectedTemplate(template);
                  setEditMode(true);
                  setShowTemplateModal(true);
                }}
              >
                <Edit className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleDuplicateTemplate(template)}
              >
                <Copy className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleTestTemplate(template)}
              >
                <Send className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleDeleteTemplate(template.template_id)}
                className="text-red-600 hover:text-red-700"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>
          </Card>
        ))}
      </div>

      {/* Template Modal */}
      <Modal
        isOpen={showTemplateModal}
        onClose={() => {
          setShowTemplateModal(false);
          setSelectedTemplate(null);
          setEditMode(false);
        }}
        title={editMode ? 'Edit Template' : 'Create Template'}
        size="xl"
      >
        <TemplateForm
          template={selectedTemplate}
          onSave={handleSaveTemplate}
          onCancel={() => {
            setShowTemplateModal(false);
            setSelectedTemplate(null);
            setEditMode(false);
          }}
          loading={loading}
        />
      </Modal>

      {/* Preview Modal */}
      <Modal
        isOpen={showPreviewModal}
        onClose={() => {
          setShowPreviewModal(false);
          setSelectedTemplate(null);
        }}
        title="Template Preview"
        size="lg"
      >
        {selectedTemplate && (
          <TemplatePreview
            template={selectedTemplate}
            onClose={() => {
              setShowPreviewModal(false);
              setSelectedTemplate(null);
            }}
          />
        )}
      </Modal>
    </div>
  );
};

// Template Form Component
interface TemplateFormProps {
  template: MessageTemplate | null;
  onSave: (data: Partial<MessageTemplate>) => void;
  onCancel: () => void;
  loading: boolean;
}

const TemplateForm: React.FC<TemplateFormProps> = ({ template, onSave, onCancel, loading }) => {
  const [formData, setFormData] = useState({
    template_name: template?.template_name || '',
    template_key: template?.template_key || '',
    category: template?.category || 'notification',
    channel: template?.channel || 'all',
    subject: template?.subject || '',
    content_en: template?.content_en || '',
    content_fr: template?.content_fr || '',
    is_active: template?.is_active ?? true,
    tags: template?.tags || []
  });

  const [newTag, setNewTag] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const variables = [
      ...extractVariables(formData.content_en),
      ...extractVariables(formData.content_fr),
      ...extractVariables(formData.subject)
    ];
    
    onSave({
      ...formData,
      variables: [...new Set(variables)]
    });
  };

  const extractVariables = (content: string): string[] => {
    const matches = content.match(/\{\{(\w+)\}\}/g);
    if (!matches) return [];
    
    const variables = matches.map(match => match.replace(/\{\{|\}\}/g, ''));
    return [...new Set(variables)];
  };

  const handleAddTag = () => {
    if (newTag && !formData.tags.includes(newTag)) {
      setFormData({ ...formData, tags: [...formData.tags, newTag] });
      setNewTag('');
    }
  };

  const handleRemoveTag = (tag: string) => {
    setFormData({ ...formData, tags: formData.tags.filter(t => t !== tag) });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6 p-6">
      <Tabs defaultValue="general" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="english">English</TabsTrigger>
          <TabsTrigger value="french">French</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-4 mt-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Template Name
              </label>
              <Input
                value={formData.template_name}
                onChange={(e) => setFormData({ ...formData, template_name: e.target.value })}
                placeholder="e.g., Appointment Confirmation"
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Template Key
              </label>
              <Input
                value={formData.template_key}
                onChange={(e) => setFormData({ ...formData, template_key: e.target.value.replace(/\s/g, '_').toLowerCase() })}
                placeholder="e.g., appointment_confirmation"
                className="font-mono"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Category
              </label>
              <Select
                value={formData.category}
                onChange={(e) => setFormData({ ...formData, category: e.target.value as any })}
                options={[
                  { value: 'appointment', label: 'Appointment' },
                  { value: 'payment', label: 'Payment' },
                  { value: 'welcome', label: 'Welcome' },
                  { value: 'reminder', label: 'Reminder' },
                  { value: 'notification', label: 'Notification' },
                  { value: 'marketing', label: 'Marketing' }
                ]}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Channel
              </label>
              <Select
                value={formData.channel}
                onChange={(e) => setFormData({ ...formData, channel: e.target.value as any })}
                options={[
                  { value: 'all', label: 'All Channels' },
                  { value: 'sms', label: 'SMS Only' },
                  { value: 'email', label: 'Email Only' },
                  { value: 'push', label: 'Push Only' }
                ]}
              />
            </div>
          </div>

          {(formData.channel === 'email' || formData.channel === 'all') && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email Subject
              </label>
              <Input
                value={formData.subject}
                onChange={(e) => setFormData({ ...formData, subject: e.target.value })}
                placeholder="e.g., Appointment Confirmed - {{date}} at {{time}}"
              />
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Tags
            </label>
            <div className="flex gap-2 mb-2">
              <Input
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                placeholder="Add a tag"
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddTag())}
              />
              <Button type="button" variant="secondary" onClick={handleAddTag}>
                Add
              </Button>
            </div>
            <div className="flex flex-wrap gap-2">
              {formData.tags.map(tag => (
                <Badge key={tag} className="bg-gray-100 text-gray-700 flex items-center gap-1">
                  {tag}
                  <button
                    type="button"
                    onClick={() => handleRemoveTag(tag)}
                    className="ml-1 text-gray-500 hover:text-gray-700"
                  >
                    <XCircle className="w-3 h-3" />
                  </button>
                </Badge>
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="english" className="space-y-4 mt-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              English Content
            </label>
            <textarea
              className="w-full p-3 border rounded-lg min-h-[200px] font-mono text-sm"
              value={formData.content_en}
              onChange={(e) => setFormData({ ...formData, content_en: e.target.value })}
              placeholder="Enter your message content here. Use {{variable_name}} for dynamic values."
              required
            />
          </div>

          <div className="p-3 bg-blue-50 rounded-lg">
            <p className="text-sm text-blue-800 mb-2">Available variables:</p>
            <div className="flex flex-wrap gap-2">
              {['client_name', 'tutor_name', 'service', 'date', 'time', 'location', 'amount', 'invoice_number'].map(variable => (
                <button
                  key={variable}
                  type="button"
                  onClick={() => setFormData({ 
                    ...formData, 
                    content_en: formData.content_en + ` {{${variable}}}` 
                  })}
                  className="px-2 py-1 text-xs bg-white rounded border border-blue-300 hover:bg-blue-100 font-mono"
                >
                  {`{{${variable}}}`}
                </button>
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="french" className="space-y-4 mt-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              French Content
            </label>
            <textarea
              className="w-full p-3 border rounded-lg min-h-[200px] font-mono text-sm"
              value={formData.content_fr}
              onChange={(e) => setFormData({ ...formData, content_fr: e.target.value })}
              placeholder="Entrez votre contenu de message ici. Utilisez {{nom_variable}} pour les valeurs dynamiques."
              required
            />
          </div>

          <div className="p-3 bg-blue-50 rounded-lg">
            <p className="text-sm text-blue-800 mb-2">Variables disponibles:</p>
            <div className="flex flex-wrap gap-2">
              {['client_name', 'tutor_name', 'service', 'date', 'time', 'location', 'amount', 'invoice_number'].map(variable => (
                <button
                  key={variable}
                  type="button"
                  onClick={() => setFormData({ 
                    ...formData, 
                    content_fr: formData.content_fr + ` {{${variable}}}` 
                  })}
                  className="px-2 py-1 text-xs bg-white rounded border border-blue-300 hover:bg-blue-100 font-mono"
                >
                  {`{{${variable}}}`}
                </button>
              ))}
            </div>
          </div>
        </TabsContent>
      </Tabs>

      <div className="flex items-center justify-between pt-6 border-t">
        <div className="flex items-center gap-2">
          <Switch
            checked={formData.is_active}
            onChange={(checked) => setFormData({ ...formData, is_active: checked })}
          />
          <span className="text-sm text-gray-700">Active</span>
        </div>
        
        <div className="flex gap-3">
          <Button
            variant="ghost"
            onClick={onCancel}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="primary"
            disabled={loading}
          >
            {loading ? 'Saving...' : (template ? 'Update' : 'Create')}
          </Button>
        </div>
      </div>
    </form>
  );
};

// Template Preview Component
interface TemplatePreviewProps {
  template: MessageTemplate;
  onClose: () => void;
}

const TemplatePreview: React.FC<TemplatePreviewProps> = ({ template, onClose }) => {
  const [language, setLanguage] = useState<'en' | 'fr'>('en');
  const [sampleData] = useState({
    client_name: 'John Doe',
    tutor_name: 'Jane Smith',
    service: 'Mathematics Tutoring',
    date: 'November 25, 2024',
    time: '3:00 PM',
    location: 'Online',
    amount: '$110.00',
    invoice_number: 'INV-2024-0145'
  });

  const renderContent = (content: string) => {
    let rendered = content;
    Object.entries(sampleData).forEach(([key, value]) => {
      rendered = rendered.replace(new RegExp(`{{${key}}}`, 'g'), value);
    });
    return rendered;
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="font-semibold text-gray-900">{template.template_name}</h3>
          <p className="text-sm text-gray-500">{template.template_key}</p>
        </div>
        <div className="flex items-center gap-2">
          <button
            className={`px-3 py-1 text-sm font-medium rounded-l-md ${
              language === 'en' 
                ? 'bg-accent-red text-white' 
                : 'bg-white text-gray-700 hover:bg-gray-50'
            }`}
            onClick={() => setLanguage('en')}
          >
            EN
          </button>
          <button
            className={`px-3 py-1 text-sm font-medium rounded-r-md ${
              language === 'fr' 
                ? 'bg-accent-red text-white' 
                : 'bg-white text-gray-700 hover:bg-gray-50'
            }`}
            onClick={() => setLanguage('fr')}
          >
            FR
          </button>
        </div>
      </div>

      <div className="border rounded-lg p-4 bg-gray-50">
        {template.subject && (
          <>
            <p className="text-sm font-medium text-gray-700 mb-1">Subject:</p>
            <p className="mb-4">{renderContent(template.subject)}</p>
          </>
        )}
        
        <p className="text-sm font-medium text-gray-700 mb-1">Content:</p>
        <div className="whitespace-pre-wrap">
          {renderContent(language === 'en' ? template.content_en : template.content_fr)}
        </div>
      </div>

      <div>
        <h4 className="font-medium text-gray-900 mb-2">Sample Data</h4>
        <div className="grid grid-cols-2 gap-2 text-sm">
          {Object.entries(sampleData).map(([key, value]) => (
            <div key={key} className="flex justify-between p-2 bg-gray-50 rounded">
              <span className="font-mono text-gray-600">{`{{${key}}}`}</span>
              <span className="text-gray-900">{value}</span>
            </div>
          ))}
        </div>
      </div>

      <div className="flex justify-end">
        <Button variant="primary" onClick={onClose}>
          Close
        </Button>
      </div>
    </div>
  );
};

export default EnhancedMessageTemplates;