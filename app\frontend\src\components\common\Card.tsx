import React from 'react';
import { clsx } from 'clsx';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  hoverable?: boolean;
  padding?: 'none' | 'sm' | 'md' | 'lg';
  shadow?: 'none' | 'subtle' | 'soft' | 'elevated';
  onClick?: () => void;
}

export const Card: React.FC<CardProps> = ({
  children,
  className,
  hoverable = false,
  padding = 'lg',
  shadow = 'subtle',
  onClick,
}) => {
  const baseStyles = 'bg-white rounded-lg border border-border-primary overflow-hidden transition-all duration-200';
  
  const paddingStyles = {
    none: '',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
  };

  const shadowStyles = {
    none: '',
    subtle: 'shadow-subtle',
    soft: 'shadow-soft',
    elevated: 'shadow-elevated',
  };

  return (
    <div
      className={clsx(
        baseStyles,
        paddingStyles[padding],
        shadowStyles[shadow],
        hoverable && 'hover:shadow-soft hover:-translate-y-0.5 cursor-pointer',
        onClick && 'cursor-pointer',
        className
      )}
      onClick={onClick}
    >
      {children}
    </div>
  );
};

interface CardHeaderProps {
  children: React.ReactNode;
  className?: string;
  actions?: React.ReactNode;
}

export const CardHeader: React.FC<CardHeaderProps> = ({
  children,
  className,
  actions,
}) => {
  return (
    <div className={clsx(
      'px-6 py-4 border-b border-border-primary bg-background-secondary',
      className
    )}>
      <div className="flex items-center justify-between">
        <div className="flex-1">{children}</div>
        {actions && <div className="flex items-center gap-2">{actions}</div>}
      </div>
    </div>
  );
};

interface CardBodyProps {
  children: React.ReactNode;
  className?: string;
}

export const CardBody: React.FC<CardBodyProps> = ({
  children,
  className,
}) => {
  return (
    <div className={clsx('px-6 py-4', className)}>
      {children}
    </div>
  );
};

interface CardFooterProps {
  children: React.ReactNode;
  className?: string;
}

export const CardFooter: React.FC<CardFooterProps> = ({
  children,
  className,
}) => {
  return (
    <div className={clsx(
      'px-6 py-4 border-t border-border-primary bg-background-secondary',
      className
    )}>
      {children}
    </div>
  );
};

export default Card;