"""
Webhook handlers for external services (<PERSON><PERSON>, <PERSON><PERSON><PERSON>).
"""

import logging
import json
from typing import Dict, Any
from fastapi import APIRouter, Request, HTTPException, Header
from fastapi.responses import PlainTextResponse, JSONResponse
import stripe

from app.config.settings import settings
from app.services.stripe_service import StripeService
from app.services.twilio_service import TwilioService
from app.services.appointment_service import AppointmentSchedulingService
from app.services.appointment_confirmation_service import AppointmentConfirmationService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/webhooks", tags=["webhooks"])


@router.post("/stripe")
async def handle_stripe_webhook(
    request: Request,
    stripe_signature: str = Header(None, alias="Stripe-Signature")
):
    """
    Handle Stripe webhook events.
    
    Processes:
    - payment_intent.succeeded - Mark invoices as paid
    - payment_intent.payment_failed - Handle failed payments
    - payout.paid - Update tutor payment records
    - payout.failed - Alert about failed payouts
    - account.updated - Update tutor Connect account status
    - charge.dispute.created - Handle chargebacks
    """
    try:
        # Get raw body
        payload = await request.body()
        
        if not stripe_signature:
            logger.error("Missing Stripe signature header")
            raise HTTPException(status_code=400, detail="Missing signature")
        
        # Initialize service
        stripe_service = StripeService()
        
        # Process webhook
        result = await stripe_service.handle_webhook(payload, stripe_signature)
        
        logger.info(f"Processed Stripe webhook: {result['event_type']}")
        
        # Return success
        return JSONResponse(content={"received": True})
        
    except stripe.error.SignatureVerificationError as e:
        logger.error(f"Invalid Stripe signature: {str(e)}")
        raise HTTPException(status_code=400, detail="Invalid signature")
    except Exception as e:
        logger.error(f"Error processing Stripe webhook: {str(e)}")
        # Return 200 to prevent retries for non-recoverable errors
        return JSONResponse(content={"error": str(e)}, status_code=200)


@router.post("/twilio/sms")
async def handle_twilio_sms_webhook(
    From: str = None,
    Body: str = None,
    MessageSid: str = None,
    AccountSid: str = None,
    NumMedia: int = 0,
    MediaUrl0: str = None
):
    """
    Handle incoming SMS messages from Twilio.
    
    This is an alias for the appointment SMS webhook but handles
    all types of SMS responses including:
    - Appointment confirmations (YES/NO)
    - Completion confirmations (DONE/NOSHOW)
    - General customer service messages
    """
    try:
        # Validate Twilio request
        if not From or not Body or not MessageSid:
            logger.error("Invalid Twilio webhook request")
            raise HTTPException(status_code=400, detail="Invalid request")
        
        # Verify account SID
        if AccountSid != settings.TWILIO_ACCOUNT_SID:
            logger.error(f"Invalid Twilio account SID: {AccountSid}")
            raise HTTPException(status_code=403, detail="Unauthorized")
        
        # Log incoming message
        logger.info(f"Incoming SMS from {From}: {Body[:50]}...")
        
        # Initialize services
        twilio_service = TwilioService()
        appointment_service = AppointmentSchedulingService()
        confirmation_service = AppointmentConfirmationService()
        
        # Process the message
        from_number = From
        message_body = Body.strip()
        
        # Collect media URLs if any
        media_urls = []
        if NumMedia > 0 and MediaUrl0:
            media_urls.append(MediaUrl0)
        
        # Store incoming message
        incoming_result = await twilio_service.handle_incoming_sms(
            from_number=from_number,
            body=message_body,
            message_sid=MessageSid,
            media_urls=media_urls
        )
        
        # The service will handle routing to appropriate handlers
        # Return empty TwiML response
        return PlainTextResponse(
            content='<?xml version="1.0" encoding="UTF-8"?><Response></Response>',
            media_type="application/xml"
        )
        
    except Exception as e:
        logger.error(f"Error processing Twilio SMS webhook: {str(e)}")
        # Return empty response to prevent error messages to user
        return PlainTextResponse(
            content='<?xml version="1.0" encoding="UTF-8"?><Response></Response>',
            media_type="application/xml"
        )


@router.post("/twilio/status")
async def handle_twilio_status_webhook(
    MessageSid: str = None,
    MessageStatus: str = None,
    ErrorCode: str = None,
    ErrorMessage: str = None
):
    """
    Handle SMS delivery status updates from Twilio.
    
    Updates the delivery status of sent messages.
    """
    try:
        if not MessageSid or not MessageStatus:
            raise HTTPException(status_code=400, detail="Invalid request")
        
        # Initialize service
        twilio_service = TwilioService()
        
        # Update message status
        await twilio_service.handle_status_callback(
            message_sid=MessageSid,
            status=MessageStatus,
            error_code=ErrorCode,
            error_message=ErrorMessage
        )
        
        logger.info(f"Updated SMS status: {MessageSid} -> {MessageStatus}")
        
        return {"received": True}
        
    except Exception as e:
        logger.error(f"Error processing Twilio status webhook: {str(e)}")
        # Return success to prevent retries
        return {"error": str(e)}


@router.post("/stripe/connect")
async def handle_stripe_connect_webhook(
    request: Request,
    stripe_signature: str = Header(None, alias="Stripe-Signature")
):
    """
    Handle Stripe Connect webhook events for tutor accounts.
    
    Processes:
    - account.updated - Update tutor verification status
    - account.application.deauthorized - Handle disconnections
    - payout.created - Track tutor payouts
    - payout.paid - Confirm payout completion
    """
    try:
        # Get raw body
        payload = await request.body()
        
        if not stripe_signature:
            raise HTTPException(status_code=400, detail="Missing signature")
        
        # Verify webhook signature
        try:
            event = stripe.Webhook.construct_event(
                payload, stripe_signature, settings.STRIPE_WEBHOOK_SECRET
            )
        except stripe.error.SignatureVerificationError:
            raise HTTPException(status_code=400, detail="Invalid signature")
        
        event_type = event['type']
        event_data = event['data']['object']
        
        logger.info(f"Processing Stripe Connect webhook: {event_type}")
        
        # Handle different Connect events
        if event_type == 'account.updated':
            await _handle_connect_account_updated(event_data)
        elif event_type == 'account.application.deauthorized':
            await _handle_connect_account_deauthorized(event_data)
        elif event_type == 'payout.created':
            await _handle_connect_payout_created(event_data)
        elif event_type == 'payout.paid':
            await _handle_connect_payout_paid(event_data)
        elif event_type == 'payout.failed':
            await _handle_connect_payout_failed(event_data)
        else:
            logger.info(f"Unhandled Connect event type: {event_type}")
        
        return {"received": True}
        
    except Exception as e:
        logger.error(f"Error processing Stripe Connect webhook: {str(e)}")
        return JSONResponse(content={"error": str(e)}, status_code=200)


async def _handle_connect_account_updated(account: Dict[str, Any]):
    """Handle Connect account updates."""
    try:
        from app.config.database import get_db_connection
        from app.core.timezone import now_est
        
        account_id = account['id']
        
        async with get_db_connection() as conn:
            # Update tutor's account status
            await conn.execute("""
                UPDATE tutors
                SET 
                    stripe_charges_enabled = $1,
                    stripe_payouts_enabled = $2,
                    stripe_details_submitted = $3,
                    bank_account_verified = $4,
                    updated_at = $5
                WHERE stripe_connect_account_id = $6
            """, 
                account.get('charges_enabled', False),
                account.get('payouts_enabled', False),
                account.get('details_submitted', False),
                account.get('payouts_enabled', False),  # Simplified check
                now_est(),
                account_id
            )
            
            logger.info(f"Updated Connect account {account_id} status")
            
    except Exception as e:
        logger.error(f"Error updating Connect account: {str(e)}")


async def _handle_connect_account_deauthorized(account: Dict[str, Any]):
    """Handle Connect account deauthorization."""
    try:
        from app.config.database import get_db_connection
        from app.core.timezone import now_est
        
        account_id = account['id']
        
        async with get_db_connection() as conn:
            # Clear tutor's Stripe account
            await conn.execute("""
                UPDATE tutors
                SET 
                    stripe_connect_account_id = NULL,
                    stripe_charges_enabled = FALSE,
                    stripe_payouts_enabled = FALSE,
                    stripe_details_submitted = FALSE,
                    bank_account_verified = FALSE,
                    updated_at = $1
                WHERE stripe_connect_account_id = $2
            """, now_est(), account_id)
            
            logger.warning(f"Connect account {account_id} was deauthorized")
            
    except Exception as e:
        logger.error(f"Error handling account deauthorization: {str(e)}")


async def _handle_connect_payout_created(payout: Dict[str, Any]):
    """Handle payout creation in Connect account."""
    logger.info(f"Payout created in Connect account: {payout['id']}")


async def _handle_connect_payout_paid(payout: Dict[str, Any]):
    """Handle successful payout in Connect account."""
    try:
        from app.config.database import get_db_connection
        from app.core.timezone import now_est
        
        payout_id = payout['id']
        
        async with get_db_connection() as conn:
            # Update payout status
            await conn.execute("""
                UPDATE tutor_stripe_payouts
                SET 
                    status = 'paid',
                    paid_at = $1,
                    updated_at = $1
                WHERE stripe_payout_id = $2
            """, now_est(), payout_id)
            
            logger.info(f"Marked payout {payout_id} as paid")
            
    except Exception as e:
        logger.error(f"Error updating payout status: {str(e)}")


async def _handle_connect_payout_failed(payout: Dict[str, Any]):
    """Handle failed payout in Connect account."""
    try:
        from app.config.database import get_db_connection
        from app.core.timezone import now_est
        
        payout_id = payout['id']
        failure_message = payout.get('failure_message', 'Unknown error')
        
        async with get_db_connection() as conn:
            # Update payout status
            await conn.execute("""
                UPDATE tutor_stripe_payouts
                SET 
                    status = 'failed',
                    failure_reason = $1,
                    updated_at = $2
                WHERE stripe_payout_id = $3
            """, failure_message, now_est(), payout_id)
            
            # TODO: Send notification to admin and tutor
            logger.error(f"Payout {payout_id} failed: {failure_message}")
            
    except Exception as e:
        logger.error(f"Error handling failed payout: {str(e)}")


@router.get("/test")
async def test_webhook():
    """Test endpoint to verify webhook configuration."""
    return {
        "status": "ok",
        "message": "Webhook endpoint is configured correctly",
        "endpoints": {
            "stripe": "/api/v1/webhooks/stripe",
            "stripe_connect": "/api/v1/webhooks/stripe/connect",
            "twilio_sms": "/api/v1/webhooks/twilio/sms",
            "twilio_status": "/api/v1/webhooks/twilio/status"
        }
    }