import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Globe, Clock, Calendar, DollarSign, Building,
  Mail, Phone, MapPin, Save, RefreshCw
} from 'lucide-react';

interface SystemConfig {
  general: {
    platformName: string;
    timezone: string;
    dateFormat: string;
    timeFormat: string;
    currency: string;
    language: string;
  };
  company: {
    name: string;
    email: string;
    phone: string;
    address: string;
    city: string;
    province: string;
    postalCode: string;
    country: string;
  };
  features: {
    enableGoogleAuth: boolean;
    enableSMSNotifications: boolean;
    enableEmailNotifications: boolean;
    enablePushNotifications: boolean;
    enableAutoBackup: boolean;
    maintenanceMode: boolean;
  };
  limits: {
    maxFileUploadSize: number;
    sessionTimeout: number;
    maxLoginAttempts: number;
    passwordMinLength: number;
    dataRetentionDays: number;
  };
}

const SystemSettings: React.FC = () => {
  const { t } = useTranslation();
  const [hasChanges, setHasChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  
  const [config, setConfig] = useState<SystemConfig>({
    general: {
      platformName: 'TutorAide',
      timezone: 'America/Montreal',
      dateFormat: 'YYYY-MM-DD',
      timeFormat: '24h',
      currency: 'CAD',
      language: 'en',
    },
    company: {
      name: 'TutorAide Inc.',
      email: '<EMAIL>',
      phone: '+****************',
      address: '1234 Rue Principale',
      city: 'Montreal',
      province: 'Quebec',
      postalCode: 'H1A 1A1',
      country: 'Canada',
    },
    features: {
      enableGoogleAuth: true,
      enableSMSNotifications: true,
      enableEmailNotifications: true,
      enablePushNotifications: true,
      enableAutoBackup: true,
      maintenanceMode: false,
    },
    limits: {
      maxFileUploadSize: 10, // MB
      sessionTimeout: 30, // minutes
      maxLoginAttempts: 5,
      passwordMinLength: 8,
      dataRetentionDays: 365,
    },
  });

  const updateConfig = (section: keyof SystemConfig, field: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value,
      },
    }));
    setHasChanges(true);
  };

  const handleSave = async () => {
    setIsSaving(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    setIsSaving(false);
    setHasChanges(false);
    console.log('Saving system settings:', config);
  };

  const handleReset = () => {
    window.location.reload();
  };

  return (
    <div className="p-6 max-w-6xl mx-auto">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-text-primary">System Settings</h1>
        <p className="text-text-secondary mt-1">Configure system-wide settings and preferences</p>
      </div>

      {/* General Settings */}
      <div className="bg-white rounded-large shadow-soft p-6 mb-6">
        <div className="flex items-center gap-3 mb-6">
          <Globe className="w-5 h-5 text-accent-red" />
          <h2 className="text-lg font-semibold text-text-primary">General Settings</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Platform Name
            </label>
            <input
              type="text"
              value={config.general.platformName}
              onChange={(e) => updateConfig('general', 'platformName', e.target.value)}
              className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Time Zone
            </label>
            <select
              value={config.general.timezone}
              onChange={(e) => updateConfig('general', 'timezone', e.target.value)}
              className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
            >
              <option value="America/Montreal">Eastern Time (Montreal)</option>
              <option value="America/Toronto">Eastern Time (Toronto)</option>
              <option value="America/Vancouver">Pacific Time (Vancouver)</option>
              <option value="America/Halifax">Atlantic Time (Halifax)</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Date Format
            </label>
            <select
              value={config.general.dateFormat}
              onChange={(e) => updateConfig('general', 'dateFormat', e.target.value)}
              className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
            >
              <option value="YYYY-MM-DD">YYYY-MM-DD</option>
              <option value="DD/MM/YYYY">DD/MM/YYYY</option>
              <option value="MM/DD/YYYY">MM/DD/YYYY</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Time Format
            </label>
            <select
              value={config.general.timeFormat}
              onChange={(e) => updateConfig('general', 'timeFormat', e.target.value)}
              className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
            >
              <option value="24h">24-hour</option>
              <option value="12h">12-hour (AM/PM)</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Currency
            </label>
            <select
              value={config.general.currency}
              onChange={(e) => updateConfig('general', 'currency', e.target.value)}
              className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
            >
              <option value="CAD">CAD - Canadian Dollar</option>
              <option value="USD">USD - US Dollar</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Default Language
            </label>
            <select
              value={config.general.language}
              onChange={(e) => updateConfig('general', 'language', e.target.value)}
              className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
            >
              <option value="en">English</option>
              <option value="fr">Français</option>
            </select>
          </div>
        </div>
      </div>

      {/* Company Information */}
      <div className="bg-white rounded-large shadow-soft p-6 mb-6">
        <div className="flex items-center gap-3 mb-6">
          <Building className="w-5 h-5 text-accent-red" />
          <h2 className="text-lg font-semibold text-text-primary">Company Information</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Company Name
            </label>
            <input
              type="text"
              value={config.company.name}
              onChange={(e) => updateConfig('company', 'name', e.target.value)}
              className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Contact Email
            </label>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-text-secondary" />
              <input
                type="email"
                value={config.company.email}
                onChange={(e) => updateConfig('company', 'email', e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Phone Number
            </label>
            <div className="relative">
              <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-text-secondary" />
              <input
                type="tel"
                value={config.company.phone}
                onChange={(e) => updateConfig('company', 'phone', e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Address
            </label>
            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-text-secondary" />
              <input
                type="text"
                value={config.company.address}
                onChange={(e) => updateConfig('company', 'address', e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              City
            </label>
            <input
              type="text"
              value={config.company.city}
              onChange={(e) => updateConfig('company', 'city', e.target.value)}
              className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Province
            </label>
            <select
              value={config.company.province}
              onChange={(e) => updateConfig('company', 'province', e.target.value)}
              className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
            >
              <option value="Quebec">Quebec</option>
              <option value="Ontario">Ontario</option>
              <option value="British Columbia">British Columbia</option>
              <option value="Alberta">Alberta</option>
              <option value="Manitoba">Manitoba</option>
              <option value="Saskatchewan">Saskatchewan</option>
              <option value="Nova Scotia">Nova Scotia</option>
              <option value="New Brunswick">New Brunswick</option>
              <option value="Newfoundland and Labrador">Newfoundland and Labrador</option>
              <option value="Prince Edward Island">Prince Edward Island</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Postal Code
            </label>
            <input
              type="text"
              value={config.company.postalCode}
              onChange={(e) => updateConfig('company', 'postalCode', e.target.value)}
              className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
              placeholder="A1A 1A1"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Country
            </label>
            <input
              type="text"
              value={config.company.country}
              onChange={(e) => updateConfig('company', 'country', e.target.value)}
              className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
              readOnly
            />
          </div>
        </div>
      </div>

      {/* Features */}
      <div className="bg-white rounded-large shadow-soft p-6 mb-6">
        <h2 className="text-lg font-semibold text-text-primary mb-6">Feature Toggles</h2>

        <div className="space-y-4">
          <label className="flex items-center justify-between p-4 bg-background-secondary rounded-medium hover:bg-primary-100 transition-colors cursor-pointer">
            <div>
              <p className="font-medium text-text-primary">Google Authentication</p>
              <p className="text-sm text-text-secondary">Allow users to sign in with Google</p>
            </div>
            <input
              type="checkbox"
              checked={config.features.enableGoogleAuth}
              onChange={(e) => updateConfig('features', 'enableGoogleAuth', e.target.checked)}
              className="w-5 h-5 rounded accent-accent-red"
            />
          </label>

          <label className="flex items-center justify-between p-4 bg-background-secondary rounded-medium hover:bg-primary-100 transition-colors cursor-pointer">
            <div>
              <p className="font-medium text-text-primary">SMS Notifications</p>
              <p className="text-sm text-text-secondary">Send SMS notifications to users</p>
            </div>
            <input
              type="checkbox"
              checked={config.features.enableSMSNotifications}
              onChange={(e) => updateConfig('features', 'enableSMSNotifications', e.target.checked)}
              className="w-5 h-5 rounded accent-accent-red"
            />
          </label>

          <label className="flex items-center justify-between p-4 bg-background-secondary rounded-medium hover:bg-primary-100 transition-colors cursor-pointer">
            <div>
              <p className="font-medium text-text-primary">Email Notifications</p>
              <p className="text-sm text-text-secondary">Send email notifications to users</p>
            </div>
            <input
              type="checkbox"
              checked={config.features.enableEmailNotifications}
              onChange={(e) => updateConfig('features', 'enableEmailNotifications', e.target.checked)}
              className="w-5 h-5 rounded accent-accent-red"
            />
          </label>

          <label className="flex items-center justify-between p-4 bg-background-secondary rounded-medium hover:bg-primary-100 transition-colors cursor-pointer">
            <div>
              <p className="font-medium text-text-primary">Push Notifications</p>
              <p className="text-sm text-text-secondary">Send push notifications to app users</p>
            </div>
            <input
              type="checkbox"
              checked={config.features.enablePushNotifications}
              onChange={(e) => updateConfig('features', 'enablePushNotifications', e.target.checked)}
              className="w-5 h-5 rounded accent-accent-red"
            />
          </label>

          <label className="flex items-center justify-between p-4 bg-background-secondary rounded-medium hover:bg-primary-100 transition-colors cursor-pointer">
            <div>
              <p className="font-medium text-text-primary">Automatic Backup</p>
              <p className="text-sm text-text-secondary">Automatically backup data daily</p>
            </div>
            <input
              type="checkbox"
              checked={config.features.enableAutoBackup}
              onChange={(e) => updateConfig('features', 'enableAutoBackup', e.target.checked)}
              className="w-5 h-5 rounded accent-accent-red"
            />
          </label>

          <label className="flex items-center justify-between p-4 bg-accent-red bg-opacity-5 rounded-medium hover:bg-accent-red hover:bg-opacity-10 transition-colors cursor-pointer">
            <div>
              <p className="font-medium text-text-primary">Maintenance Mode</p>
              <p className="text-sm text-accent-red">Platform is under maintenance (users cannot access)</p>
            </div>
            <input
              type="checkbox"
              checked={config.features.maintenanceMode}
              onChange={(e) => updateConfig('features', 'maintenanceMode', e.target.checked)}
              className="w-5 h-5 rounded accent-accent-red"
            />
          </label>
        </div>
      </div>

      {/* System Limits */}
      <div className="bg-white rounded-large shadow-soft p-6 mb-6">
        <h2 className="text-lg font-semibold text-text-primary mb-6">System Limits</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Max File Upload Size (MB)
            </label>
            <input
              type="number"
              value={config.limits.maxFileUploadSize}
              onChange={(e) => updateConfig('limits', 'maxFileUploadSize', parseInt(e.target.value))}
              className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
              min={1}
              max={100}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Session Timeout (minutes)
            </label>
            <input
              type="number"
              value={config.limits.sessionTimeout}
              onChange={(e) => updateConfig('limits', 'sessionTimeout', parseInt(e.target.value))}
              className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
              min={5}
              max={1440}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Max Login Attempts
            </label>
            <input
              type="number"
              value={config.limits.maxLoginAttempts}
              onChange={(e) => updateConfig('limits', 'maxLoginAttempts', parseInt(e.target.value))}
              className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
              min={3}
              max={10}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Minimum Password Length
            </label>
            <input
              type="number"
              value={config.limits.passwordMinLength}
              onChange={(e) => updateConfig('limits', 'passwordMinLength', parseInt(e.target.value))}
              className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
              min={6}
              max={20}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Data Retention Period (days)
            </label>
            <input
              type="number"
              value={config.limits.dataRetentionDays}
              onChange={(e) => updateConfig('limits', 'dataRetentionDays', parseInt(e.target.value))}
              className="w-full px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
              min={30}
              max={3650}
            />
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center justify-end gap-4">
        <button
          onClick={handleReset}
          disabled={!hasChanges}
          className="flex items-center gap-2 px-6 py-2 border border-primary-300 rounded-soft hover:bg-background-secondary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <RefreshCw className="w-4 h-4" />
          Reset Changes
        </button>
        <button
          onClick={handleSave}
          disabled={!hasChanges || isSaving}
          className={`
            flex items-center gap-2 px-6 py-2 rounded-soft text-white
            ${hasChanges && !isSaving
              ? 'bg-accent-red hover:bg-accent-red-dark'
              : 'bg-primary-300 cursor-not-allowed'
            }
          `}
        >
          <Save className="w-4 h-4" />
          {isSaving ? 'Saving...' : 'Save Changes'}
        </button>
      </div>
    </div>
  );
};

export default SystemSettings;