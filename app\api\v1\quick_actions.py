"""
API endpoints for quick actions.
"""

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, Query
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta

from app.models.quick_action_models import (
    QuickActionType,
    QuickActionContext,
    QuickActionResult,
    UserQuickActions
)
from app.models.user_models import User
from app.services.quick_action_service import get_quick_action_service
from app.core.dependencies import (
    get_current_user,
    get_db_connection
)
from app.core.authorization import require_any_role
from app.core.exceptions import (
    ValidationError,
    AuthorizationError,
    BusinessLogicError
)
from app.core.logging import TutorAideLogger

router = APIRouter(prefix="/quick-actions", tags=["quick-actions"])
logger = TutorAideLogger.get_logger(__name__)


@router.get("/available", response_model=UserQuickActions)
async def get_available_quick_actions(
    current_user: User = Depends(get_current_user),
    conn=Depends(get_db_connection)
):
    """
    Get available quick actions for the current user.
    
    Returns actions based on the user's active role, including:
    - All actions available for the role
    - Recently used actions
    - Pinned actions
    - Suggested actions based on context
    """
    try:
        service = get_quick_action_service()
        
        return await service.get_user_quick_actions(
            conn,
            current_user.user_id,
            current_user.active_role
        )
        
    except Exception as e:
        logger.error(f"Error getting quick actions: {e}")
        raise HTTPException(status_code=500, detail="Failed to get quick actions")


@router.post("/execute", response_model=QuickActionResult)
async def execute_quick_action(
    context: QuickActionContext,
    current_user: User = Depends(get_current_user),
    conn=Depends(get_db_connection)
):
    """
    Execute a quick action.
    
    The action will be validated against the user's permissions before execution.
    """
    try:
        # Ensure user ID and role match
        if context.user_id != current_user.user_id:
            raise AuthorizationError("Cannot execute actions for another user")
        
        if context.user_role != current_user.active_role:
            raise ValidationError(
                "Action role does not match current active role. "
                "Please switch to the appropriate role first."
            )
        
        service = get_quick_action_service()
        
        return await service.execute_action(conn, context)
        
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except AuthorizationError as e:
        raise HTTPException(status_code=403, detail=str(e))
    except BusinessLogicError as e:
        raise HTTPException(status_code=422, detail=str(e))
    except Exception as e:
        logger.error(f"Error executing quick action: {e}")
        raise HTTPException(status_code=500, detail="Failed to execute action")


@router.post("/pin/{action_type}")
async def pin_quick_action(
    action_type: QuickActionType,
    current_user: User = Depends(get_current_user),
    conn=Depends(get_db_connection)
):
    """
    Pin a quick action for quick access.
    
    Pinned actions appear at the top of the quick actions menu.
    """
    try:
        service = get_quick_action_service()
        
        success = await service.pin_action(
            conn,
            current_user.user_id,
            action_type
        )
        
        return {"success": success, "action_type": action_type}
        
    except Exception as e:
        logger.error(f"Error pinning action: {e}")
        raise HTTPException(status_code=500, detail="Failed to pin action")


@router.delete("/pin/{action_type}")
async def unpin_quick_action(
    action_type: QuickActionType,
    current_user: User = Depends(get_current_user),
    conn=Depends(get_db_connection)
):
    """
    Unpin a quick action.
    """
    try:
        service = get_quick_action_service()
        
        success = await service.unpin_action(
            conn,
            current_user.user_id,
            action_type
        )
        
        return {"success": success, "action_type": action_type}
        
    except Exception as e:
        logger.error(f"Error unpinning action: {e}")
        raise HTTPException(status_code=500, detail="Failed to unpin action")


@router.get("/analytics")
async def get_quick_action_analytics(
    start_date: Optional[datetime] = Query(None, description="Start date for analytics"),
    end_date: Optional[datetime] = Query(None, description="End date for analytics"),
    user_id: Optional[int] = Query(None, description="Filter by specific user"),
    current_user: User = Depends(require_any_role(["manager"])),
    conn=Depends(get_db_connection)
):
    """
    Get analytics for quick action usage.
    
    Requires manager role. Returns usage statistics, performance metrics,
    and common errors for quick actions.
    """
    try:
        service = get_quick_action_service()
        
        # Default to last 30 days if no dates provided
        if not end_date:
            end_date = datetime.utcnow()
        if not start_date:
            start_date = end_date - timedelta(days=30)
        
        return await service.get_action_analytics(
            conn,
            start_date,
            end_date,
            user_id
        )
        
    except Exception as e:
        logger.error(f"Error getting action analytics: {e}")
        raise HTTPException(status_code=500, detail="Failed to get analytics")


@router.get("/definitions")
async def get_action_definitions(
    role: Optional[str] = Query(None, description="Filter by role"),
    current_user: User = Depends(get_current_user)
):
    """
    Get all available quick action definitions.
    
    Returns the static definitions of all quick actions, optionally filtered by role.
    This is useful for building UI components and understanding available actions.
    """
    try:
        from app.models.quick_action_models import (
            QUICK_ACTION_DEFINITIONS,
            UserRoleType
        )
        
        definitions = QUICK_ACTION_DEFINITIONS
        
        # Filter by role if specified
        if role:
            try:
                role_enum = UserRoleType(role)
                definitions = [
                    d for d in definitions 
                    if role_enum in d.allowed_roles
                ]
            except ValueError:
                raise ValidationError(f"Invalid role: {role}")
        
        # Convert to dict for JSON serialization
        return [d.dict() for d in definitions]
        
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error getting action definitions: {e}")
        raise HTTPException(status_code=500, detail="Failed to get definitions")


@router.get("/shortcuts")
async def get_keyboard_shortcuts(
    current_user: User = Depends(get_current_user)
):
    """
    Get keyboard shortcuts for quick actions available to the current user.
    
    Returns a mapping of keyboard shortcuts to action types for the user's
    active role.
    """
    try:
        from app.models.quick_action_models import get_actions_for_role
        
        actions = get_actions_for_role(current_user.active_role)
        
        shortcuts = {}
        for action in actions:
            if action.keyboard_shortcut:
                shortcuts[action.keyboard_shortcut] = {
                    "action_type": action.action_type,
                    "name": action.name,
                    "description": action.description
                }
        
        return shortcuts
        
    except Exception as e:
        logger.error(f"Error getting keyboard shortcuts: {e}")
        raise HTTPException(status_code=500, detail="Failed to get shortcuts")