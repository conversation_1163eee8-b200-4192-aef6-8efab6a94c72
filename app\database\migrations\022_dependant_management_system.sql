-- Migration: Dependant Management System
-- Description: Comprehensive dependant management with support for separated families

-- Dependants Table
CREATE TABLE IF NOT EXISTS dependants (
    dependant_id SERIAL PRIMARY KEY,
    first_name VA<PERSON>HAR(50) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
    date_of_birth DATE NOT NULL,
    gender VARCHAR(20) CHECK (gender IN ('male', 'female', 'other', 'prefer_not_to_say')),
    profile_photo_url VARCHAR(500),
    preferred_name VA<PERSON>HAR(50),
    pronouns VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    created_by INTEGER REFERENCES users(user_id),
    CONSTRAINT dependant_age_check CHECK (date_of_birth <= CURRENT_DATE AND date_of_birth >= CURRENT_DATE - INTERVAL '25 years')
);

-- Dependant-Parent Relationships (supports multiple parents)
CREATE TABLE IF NOT EXISTS dependant_parents (
    relationship_id SERIAL PRIMARY KEY,
    dependant_id INTEGER NOT NULL REFERENCES dependants(dependant_id) ON DELETE CASCADE,
    client_id INTEGER NOT NULL REFERENCES client_profiles(client_id) ON DELETE CASCADE,
    relationship_type VARCHAR(50) NOT NULL CHECK (relationship_type IN ('mother', 'father', 'guardian', 'step_parent', 'grandparent', 'other')),
    is_primary_contact BOOLEAN DEFAULT FALSE,
    has_legal_custody BOOLEAN DEFAULT TRUE,
    custody_arrangement VARCHAR(100), -- e.g., 'full', 'joint', 'weekends', 'alternating_weeks'
    can_make_medical_decisions BOOLEAN DEFAULT TRUE,
    can_make_educational_decisions BOOLEAN DEFAULT TRUE,
    can_pickup BOOLEAN DEFAULT TRUE,
    financial_responsibility_percent INTEGER DEFAULT 100 CHECK (financial_responsibility_percent >= 0 AND financial_responsibility_percent <= 100),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    CONSTRAINT unique_dependant_parent UNIQUE (dependant_id, client_id) WHERE deleted_at IS NULL,
    CONSTRAINT at_least_one_primary CHECK (
        NOT EXISTS (
            SELECT 1 FROM dependant_parents dp2 
            WHERE dp2.dependant_id = dependant_id 
            AND dp2.deleted_at IS NULL
            GROUP BY dp2.dependant_id 
            HAVING COUNT(CASE WHEN dp2.is_primary_contact THEN 1 END) = 0
        )
    )
);

-- Dependant Medical Information
CREATE TABLE IF NOT EXISTS dependant_medical_info (
    medical_info_id SERIAL PRIMARY KEY,
    dependant_id INTEGER NOT NULL REFERENCES dependants(dependant_id) ON DELETE CASCADE,
    blood_type VARCHAR(10),
    allergies TEXT[] DEFAULT '{}',
    medications TEXT[] DEFAULT '{}',
    medical_conditions TEXT[] DEFAULT '{}',
    dietary_restrictions TEXT[] DEFAULT '{}',
    emergency_medical_info TEXT,
    physician_name VARCHAR(100),
    physician_phone VARCHAR(20),
    physician_address TEXT,
    health_card_number VARCHAR(50),
    insurance_provider VARCHAR(100),
    insurance_policy_number VARCHAR(100),
    last_physical_exam_date DATE,
    immunization_up_to_date BOOLEAN DEFAULT TRUE,
    special_needs TEXT,
    assistive_devices TEXT,
    behavioral_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT one_medical_info_per_dependant UNIQUE (dependant_id)
);

-- Dependant Education Information
CREATE TABLE IF NOT EXISTS dependant_education (
    education_id SERIAL PRIMARY KEY,
    dependant_id INTEGER NOT NULL REFERENCES dependants(dependant_id) ON DELETE CASCADE,
    school_name VARCHAR(200),
    school_address TEXT,
    school_phone VARCHAR(20),
    grade_level VARCHAR(20),
    academic_year VARCHAR(20),
    teacher_name VARCHAR(100),
    teacher_email VARCHAR(255),
    school_start_time TIME,
    school_end_time TIME,
    transportation_method VARCHAR(50),
    locker_number VARCHAR(20),
    student_id VARCHAR(50),
    special_education_plan BOOLEAN DEFAULT FALSE,
    gifted_program BOOLEAN DEFAULT FALSE,
    french_immersion BOOLEAN DEFAULT FALSE,
    extracurricular_activities TEXT[] DEFAULT '{}',
    academic_strengths TEXT[] DEFAULT '{}',
    academic_challenges TEXT[] DEFAULT '{}',
    homework_routine TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT one_education_per_dependant UNIQUE (dependant_id)
);

-- Dependant Learning Profile
CREATE TABLE IF NOT EXISTS dependant_learning_profiles (
    learning_profile_id SERIAL PRIMARY KEY,
    dependant_id INTEGER NOT NULL REFERENCES dependants(dependant_id) ON DELETE CASCADE,
    learning_style VARCHAR(50) CHECK (learning_style IN ('visual', 'auditory', 'kinesthetic', 'reading_writing', 'mixed')),
    attention_span_minutes INTEGER,
    preferred_subjects TEXT[] DEFAULT '{}',
    struggling_subjects TEXT[] DEFAULT '{}',
    learning_goals TEXT,
    motivators TEXT[] DEFAULT '{}',
    learning_barriers TEXT[] DEFAULT '{}',
    preferred_reward_system TEXT,
    homework_habits TEXT,
    study_environment_preferences TEXT,
    technology_comfort_level VARCHAR(20) CHECK (technology_comfort_level IN ('beginner', 'intermediate', 'advanced')),
    parent_involvement_needed BOOLEAN DEFAULT TRUE,
    peer_learning_preference BOOLEAN DEFAULT FALSE,
    assessment_results JSONB DEFAULT '{}',
    progress_tracking JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT one_learning_profile_per_dependant UNIQUE (dependant_id)
);

-- Emergency Contacts specific to dependants
CREATE TABLE IF NOT EXISTS dependant_emergency_contacts (
    contact_id SERIAL PRIMARY KEY,
    dependant_id INTEGER NOT NULL REFERENCES dependants(dependant_id) ON DELETE CASCADE,
    contact_name VARCHAR(100) NOT NULL,
    relationship VARCHAR(50) NOT NULL,
    phone_primary VARCHAR(20) NOT NULL,
    phone_secondary VARCHAR(20),
    email VARCHAR(255),
    address TEXT,
    is_authorized_pickup BOOLEAN DEFAULT FALSE,
    priority_order INTEGER DEFAULT 1,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for performance
CREATE INDEX idx_dependants_last_name ON dependants(last_name) WHERE deleted_at IS NULL;
CREATE INDEX idx_dependants_dob ON dependants(date_of_birth) WHERE deleted_at IS NULL;
CREATE INDEX idx_dependant_parents_dependant_id ON dependant_parents(dependant_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_dependant_parents_client_id ON dependant_parents(client_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_dependant_medical_info_dependant_id ON dependant_medical_info(dependant_id);
CREATE INDEX idx_dependant_education_dependant_id ON dependant_education(dependant_id);
CREATE INDEX idx_dependant_education_grade ON dependant_education(grade_level);
CREATE INDEX idx_dependant_learning_profiles_dependant_id ON dependant_learning_profiles(dependant_id);
CREATE INDEX idx_dependant_emergency_contacts_dependant_id ON dependant_emergency_contacts(dependant_id) WHERE deleted_at IS NULL;

-- Full text search index for dependant names
CREATE INDEX idx_dependants_fulltext ON dependants 
USING gin(to_tsvector('english', first_name || ' ' || last_name || ' ' || COALESCE(preferred_name, '')));

-- Update triggers
CREATE TRIGGER update_dependants_modtime BEFORE UPDATE ON dependants FOR EACH ROW EXECUTE FUNCTION update_modified_column();
CREATE TRIGGER update_dependant_parents_modtime BEFORE UPDATE ON dependant_parents FOR EACH ROW EXECUTE FUNCTION update_modified_column();
CREATE TRIGGER update_dependant_medical_info_modtime BEFORE UPDATE ON dependant_medical_info FOR EACH ROW EXECUTE FUNCTION update_modified_column();
CREATE TRIGGER update_dependant_education_modtime BEFORE UPDATE ON dependant_education FOR EACH ROW EXECUTE FUNCTION update_modified_column();
CREATE TRIGGER update_dependant_learning_profiles_modtime BEFORE UPDATE ON dependant_learning_profiles FOR EACH ROW EXECUTE FUNCTION update_modified_column();
CREATE TRIGGER update_dependant_emergency_contacts_modtime BEFORE UPDATE ON dependant_emergency_contacts FOR EACH ROW EXECUTE FUNCTION update_modified_column();

-- Function to calculate dependant age
CREATE OR REPLACE FUNCTION calculate_dependant_age(p_dependant_id INTEGER)
RETURNS INTEGER AS $$
DECLARE
    v_dob DATE;
BEGIN
    SELECT date_of_birth INTO v_dob FROM dependants WHERE dependant_id = p_dependant_id;
    RETURN EXTRACT(YEAR FROM AGE(v_dob));
END;
$$ LANGUAGE plpgsql;

-- Function to get primary parent
CREATE OR REPLACE FUNCTION get_primary_parent(p_dependant_id INTEGER)
RETURNS INTEGER AS $$
DECLARE
    v_client_id INTEGER;
BEGIN
    SELECT client_id INTO v_client_id 
    FROM dependant_parents 
    WHERE dependant_id = p_dependant_id 
    AND is_primary_contact = TRUE 
    AND deleted_at IS NULL
    LIMIT 1;
    
    RETURN v_client_id;
END;
$$ LANGUAGE plpgsql;

-- View for dependant summary with parent info
CREATE OR REPLACE VIEW dependant_summary AS
SELECT 
    d.dependant_id,
    d.first_name,
    d.last_name,
    d.preferred_name,
    d.date_of_birth,
    calculate_dependant_age(d.dependant_id) as age,
    d.gender,
    de.grade_level,
    de.school_name,
    array_agg(DISTINCT 
        CASE WHEN dp.is_primary_contact THEN 
            cp.first_name || ' ' || cp.last_name || ' (Primary)'
        ELSE 
            cp.first_name || ' ' || cp.last_name
        END
    ) as parent_names,
    array_agg(DISTINCT dp.client_id) as parent_ids,
    d.created_at,
    d.updated_at
FROM dependants d
LEFT JOIN dependant_education de ON d.dependant_id = de.dependant_id
LEFT JOIN dependant_parents dp ON d.dependant_id = dp.dependant_id AND dp.deleted_at IS NULL
LEFT JOIN client_profiles cp ON dp.client_id = cp.client_id
WHERE d.deleted_at IS NULL
GROUP BY d.dependant_id, d.first_name, d.last_name, d.preferred_name, d.date_of_birth, 
         d.gender, de.grade_level, de.school_name, d.created_at, d.updated_at;