"""
Dependant domain models for the TutorAide application.

This module contains all Pydantic models related to dependant management,
including parent relationships, medical info, education, and learning profiles.
"""

from datetime import datetime, date
from typing import Optional, List, Dict, Any
from enum import Enum

from pydantic import BaseModel, Field, ConfigDict, field_validator, computed_field, EmailStr
from app.models.base import BaseEntity, IdentifiedEntity


class Gender(str, Enum):
    """Gender enumeration."""
    MALE = "male"
    FEMALE = "female"
    OTHER = "other"
    PREFER_NOT_TO_SAY = "prefer_not_to_say"


class RelationshipType(str, Enum):
    """Parent/Guardian relationship type."""
    MOTHER = "mother"
    FATHER = "father"
    GUARDIAN = "guardian"
    STEP_PARENT = "step_parent"
    GRANDPARENT = "grandparent"
    OTHER = "other"


class CustodyArrangement(str, Enum):
    """Custody arrangement types."""
    FULL = "full"
    JOINT = "joint"
    WEEKENDS = "weekends"
    ALTERNATING_WEEKS = "alternating_weeks"
    CUSTOM = "custom"


class LearningStyle(str, Enum):
    """Learning style preferences."""
    VISUAL = "visual"
    AUDITORY = "auditory"
    KINESTHETIC = "kinesthetic"
    READING_WRITING = "reading_writing"
    MIXED = "mixed"


class TechnologyComfortLevel(str, Enum):
    """Technology comfort level."""
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"


class Dependant(BaseEntity):
    """Dependant base model."""
    
    dependant_id: int = Field(..., description="Unique dependant identifier")
    first_name: str = Field(..., min_length=1, max_length=50, description="Dependant's first name")
    last_name: str = Field(..., min_length=1, max_length=50, description="Dependant's last name")
    date_of_birth: date = Field(..., description="Date of birth")
    gender: Optional[Gender] = Field(None, description="Gender")
    profile_photo_url: Optional[str] = Field(None, max_length=500, description="Profile photo URL")
    preferred_name: Optional[str] = Field(None, max_length=50, description="Preferred name")
    pronouns: Optional[str] = Field(None, max_length=50, description="Preferred pronouns")
    created_by: Optional[int] = Field(None, description="User ID who created this record")
    
    @field_validator('date_of_birth')
    @classmethod
    def validate_age(cls, v: date) -> date:
        """Validate dependant age (must be under 25)."""
        from datetime import date as dt
        today = dt.today()
        age = today.year - v.year - ((today.month, today.day) < (v.month, v.day))
        
        if age >= 25:
            raise ValueError("Dependant must be under 25 years old")
        if age < 0:
            raise ValueError("Date of birth cannot be in the future")
        
        return v
    
    @computed_field
    @property
    def age(self) -> int:
        """Calculate current age."""
        from datetime import date as dt
        today = dt.today()
        return today.year - self.date_of_birth.year - (
            (today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day)
        )
    
    @computed_field
    @property
    def full_name(self) -> str:
        """Get dependant's full name."""
        return f"{self.first_name} {self.last_name}"
    
    @computed_field
    @property
    def display_name(self) -> str:
        """Get display name (preferred name or first name)."""
        return self.preferred_name or self.first_name


class DependantParent(BaseEntity):
    """Dependant-Parent relationship model."""
    
    relationship_id: int = Field(..., description="Unique relationship identifier")
    dependant_id: int = Field(..., description="Associated dependant ID")
    client_id: int = Field(..., description="Associated client (parent) ID")
    relationship_type: RelationshipType = Field(..., description="Relationship type")
    is_primary_contact: bool = Field(default=False, description="Primary contact for dependant")
    has_legal_custody: bool = Field(default=True, description="Has legal custody")
    custody_arrangement: Optional[CustodyArrangement] = Field(None, description="Custody arrangement")
    can_make_medical_decisions: bool = Field(default=True, description="Can make medical decisions")
    can_make_educational_decisions: bool = Field(default=True, description="Can make educational decisions")
    can_pickup: bool = Field(default=True, description="Authorized to pick up dependant")
    financial_responsibility_percent: int = Field(
        default=100,
        ge=0,
        le=100,
        description="Financial responsibility percentage"
    )
    notes: Optional[str] = Field(None, description="Additional notes")
    
    @field_validator('financial_responsibility_percent')
    @classmethod
    def validate_percentage(cls, v: int) -> int:
        """Validate percentage is between 0 and 100."""
        if v < 0 or v > 100:
            raise ValueError("Financial responsibility must be between 0 and 100")
        return v


class DependantMedicalInfo(BaseModel):
    """Dependant medical information model."""
    
    model_config = ConfigDict(from_attributes=True)
    
    medical_info_id: int = Field(..., description="Unique medical info identifier")
    dependant_id: int = Field(..., description="Associated dependant ID")
    blood_type: Optional[str] = Field(None, max_length=10, description="Blood type")
    allergies: List[str] = Field(default_factory=list, description="List of allergies")
    medications: List[str] = Field(default_factory=list, description="Current medications")
    medical_conditions: List[str] = Field(default_factory=list, description="Medical conditions")
    dietary_restrictions: List[str] = Field(default_factory=list, description="Dietary restrictions")
    emergency_medical_info: Optional[str] = Field(None, description="Emergency medical information")
    physician_name: Optional[str] = Field(None, max_length=100, description="Primary physician name")
    physician_phone: Optional[str] = Field(None, max_length=20, description="Physician phone number")
    physician_address: Optional[str] = Field(None, description="Physician address")
    health_card_number: Optional[str] = Field(None, max_length=50, description="Health card number")
    insurance_provider: Optional[str] = Field(None, max_length=100, description="Insurance provider")
    insurance_policy_number: Optional[str] = Field(None, max_length=100, description="Insurance policy number")
    last_physical_exam_date: Optional[date] = Field(None, description="Last physical exam date")
    immunization_up_to_date: bool = Field(default=True, description="Immunizations up to date")
    special_needs: Optional[str] = Field(None, description="Special needs description")
    assistive_devices: Optional[str] = Field(None, description="Assistive devices used")
    behavioral_notes: Optional[str] = Field(None, description="Behavioral notes")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    
    @computed_field
    @property
    def has_allergies(self) -> bool:
        """Check if dependant has any allergies."""
        return len(self.allergies) > 0
    
    @computed_field
    @property
    def has_medical_conditions(self) -> bool:
        """Check if dependant has any medical conditions."""
        return len(self.medical_conditions) > 0
    
    @computed_field
    @property
    def requires_special_attention(self) -> bool:
        """Check if dependant requires special attention."""
        return (
            self.has_allergies or 
            self.has_medical_conditions or 
            bool(self.special_needs) or
            bool(self.assistive_devices)
        )


class DependantEducation(BaseModel):
    """Dependant education information model."""
    
    model_config = ConfigDict(from_attributes=True)
    
    education_id: int = Field(..., description="Unique education identifier")
    dependant_id: int = Field(..., description="Associated dependant ID")
    school_name: Optional[str] = Field(None, max_length=200, description="School name")
    school_address: Optional[str] = Field(None, description="School address")
    school_phone: Optional[str] = Field(None, max_length=20, description="School phone number")
    grade_level: Optional[str] = Field(None, max_length=20, description="Current grade level")
    academic_year: Optional[str] = Field(None, max_length=20, description="Academic year")
    teacher_name: Optional[str] = Field(None, max_length=100, description="Primary teacher name")
    teacher_email: Optional[EmailStr] = Field(None, description="Teacher email")
    school_start_time: Optional[str] = Field(None, description="School start time")
    school_end_time: Optional[str] = Field(None, description="School end time")
    transportation_method: Optional[str] = Field(None, max_length=50, description="Transportation method")
    locker_number: Optional[str] = Field(None, max_length=20, description="Locker number")
    student_id: Optional[str] = Field(None, max_length=50, description="Student ID")
    special_education_plan: bool = Field(default=False, description="Has special education plan")
    gifted_program: bool = Field(default=False, description="In gifted program")
    french_immersion: bool = Field(default=False, description="In French immersion")
    extracurricular_activities: List[str] = Field(default_factory=list, description="Extracurricular activities")
    academic_strengths: List[str] = Field(default_factory=list, description="Academic strengths")
    academic_challenges: List[str] = Field(default_factory=list, description="Academic challenges")
    homework_routine: Optional[str] = Field(None, description="Homework routine description")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    
    @computed_field
    @property
    def is_in_special_program(self) -> bool:
        """Check if in any special program."""
        return self.special_education_plan or self.gifted_program or self.french_immersion


class DependantLearningProfile(BaseModel):
    """Dependant learning profile model."""
    
    model_config = ConfigDict(from_attributes=True)
    
    learning_profile_id: int = Field(..., description="Unique learning profile identifier")
    dependant_id: int = Field(..., description="Associated dependant ID")
    learning_style: Optional[LearningStyle] = Field(None, description="Primary learning style")
    attention_span_minutes: Optional[int] = Field(None, ge=5, le=180, description="Attention span in minutes")
    preferred_subjects: List[str] = Field(default_factory=list, description="Preferred subjects")
    struggling_subjects: List[str] = Field(default_factory=list, description="Struggling subjects")
    learning_goals: Optional[str] = Field(None, description="Learning goals")
    motivators: List[str] = Field(default_factory=list, description="What motivates the dependant")
    learning_barriers: List[str] = Field(default_factory=list, description="Learning barriers")
    preferred_reward_system: Optional[str] = Field(None, description="Preferred reward system")
    homework_habits: Optional[str] = Field(None, description="Homework habits description")
    study_environment_preferences: Optional[str] = Field(None, description="Study environment preferences")
    technology_comfort_level: Optional[TechnologyComfortLevel] = Field(None, description="Technology comfort")
    parent_involvement_needed: bool = Field(default=True, description="Parent involvement needed")
    peer_learning_preference: bool = Field(default=False, description="Prefers learning with peers")
    assessment_results: Dict[str, Any] = Field(default_factory=dict, description="Assessment results")
    progress_tracking: Dict[str, Any] = Field(default_factory=dict, description="Progress tracking data")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    
    @computed_field
    @property
    def needs_support_subjects(self) -> List[str]:
        """Get subjects needing support."""
        return list(set(self.struggling_subjects) - set(self.preferred_subjects))


class DependantEmergencyContact(BaseEntity):
    """Dependant emergency contact model."""
    
    contact_id: int = Field(..., description="Unique contact identifier")
    dependant_id: int = Field(..., description="Associated dependant ID")
    contact_name: str = Field(..., min_length=1, max_length=100, description="Contact person's name")
    relationship: str = Field(..., min_length=1, max_length=50, description="Relationship to dependant")
    phone_primary: str = Field(..., min_length=10, max_length=20, description="Primary phone number")
    phone_secondary: Optional[str] = Field(None, max_length=20, description="Secondary phone number")
    email: Optional[EmailStr] = Field(None, description="Contact email address")
    address: Optional[str] = Field(None, description="Contact address")
    is_authorized_pickup: bool = Field(default=False, description="Authorized to pick up dependant")
    priority_order: int = Field(default=1, ge=1, description="Contact priority order")
    notes: Optional[str] = Field(None, description="Additional notes")


class DependantProfile(Dependant):
    """Complete dependant profile with all relationships."""
    
    # Parent relationships
    parents: List[DependantParent] = Field(default_factory=list, description="Parent relationships")
    
    # Related information
    medical_info: Optional[DependantMedicalInfo] = Field(None, description="Medical information")
    education: Optional[DependantEducation] = Field(None, description="Education information")
    learning_profile: Optional[DependantLearningProfile] = Field(None, description="Learning profile")
    emergency_contacts: List[DependantEmergencyContact] = Field(default_factory=list, description="Emergency contacts")
    
    @computed_field
    @property
    def primary_parent(self) -> Optional[DependantParent]:
        """Get primary parent contact."""
        for parent in self.parents:
            if parent.is_primary_contact and parent.is_active():
                return parent
        # Return first active parent if no primary
        active_parents = [p for p in self.parents if p.is_active()]
        return active_parents[0] if active_parents else None
    
    @computed_field
    @property
    def all_parent_ids(self) -> List[int]:
        """Get all parent client IDs."""
        return [p.client_id for p in self.parents if p.is_active()]
    
    @computed_field
    @property
    def grade_display(self) -> str:
        """Get grade level display."""
        if self.education and self.education.grade_level:
            return self.education.grade_level
        return "Not specified"
    
    @computed_field
    @property
    def school_display(self) -> str:
        """Get school name display."""
        if self.education and self.education.school_name:
            return self.education.school_name
        return "Not specified"
    
    @computed_field
    @property
    def has_special_needs(self) -> bool:
        """Check if dependant has special needs."""
        return (
            (self.medical_info and self.medical_info.requires_special_attention) or
            (self.education and self.education.special_education_plan)
        )
    
    @computed_field
    @property
    def authorized_pickup_contacts(self) -> List[DependantEmergencyContact]:
        """Get contacts authorized for pickup."""
        return [c for c in self.emergency_contacts if c.is_authorized_pickup and c.is_active()]


class DependantCreate(BaseModel):
    """Model for creating a dependant."""
    
    model_config = ConfigDict(from_attributes=True)
    
    first_name: str = Field(..., min_length=1, max_length=50, description="Dependant's first name")
    last_name: str = Field(..., min_length=1, max_length=50, description="Dependant's last name")
    date_of_birth: date = Field(..., description="Date of birth")
    gender: Optional[Gender] = Field(None, description="Gender")
    preferred_name: Optional[str] = Field(None, max_length=50, description="Preferred name")
    pronouns: Optional[str] = Field(None, max_length=50, description="Preferred pronouns")
    
    # Initial parent relationship
    parent_client_id: int = Field(..., description="Parent's client ID")
    relationship_type: RelationshipType = Field(..., description="Relationship to dependant")
    is_primary_contact: bool = Field(default=True, description="Is primary contact")


class DependantUpdate(BaseModel):
    """Model for updating a dependant."""
    
    model_config = ConfigDict(from_attributes=True)
    
    first_name: Optional[str] = Field(None, min_length=1, max_length=50, description="Dependant's first name")
    last_name: Optional[str] = Field(None, min_length=1, max_length=50, description="Dependant's last name")
    date_of_birth: Optional[date] = Field(None, description="Date of birth")
    gender: Optional[Gender] = Field(None, description="Gender")
    profile_photo_url: Optional[str] = Field(None, max_length=500, description="Profile photo URL")
    preferred_name: Optional[str] = Field(None, max_length=50, description="Preferred name")
    pronouns: Optional[str] = Field(None, max_length=50, description="Preferred pronouns")


class DependantParentCreate(BaseModel):
    """Model for creating a parent relationship."""
    
    model_config = ConfigDict(from_attributes=True)
    
    client_id: int = Field(..., description="Parent's client ID")
    relationship_type: RelationshipType = Field(..., description="Relationship type")
    is_primary_contact: bool = Field(default=False, description="Primary contact for dependant")
    has_legal_custody: bool = Field(default=True, description="Has legal custody")
    custody_arrangement: Optional[CustodyArrangement] = Field(None, description="Custody arrangement")
    can_make_medical_decisions: bool = Field(default=True, description="Can make medical decisions")
    can_make_educational_decisions: bool = Field(default=True, description="Can make educational decisions")
    can_pickup: bool = Field(default=True, description="Authorized to pick up dependant")
    financial_responsibility_percent: int = Field(default=100, ge=0, le=100, description="Financial responsibility")
    notes: Optional[str] = Field(None, description="Additional notes")


class DependantSummary(BaseModel):
    """Dependant summary for lists and search results."""
    
    model_config = ConfigDict(from_attributes=True)
    
    dependant_id: int
    first_name: str
    last_name: str
    preferred_name: Optional[str]
    date_of_birth: date
    age: int
    gender: Optional[Gender]
    grade_level: Optional[str]
    school_name: Optional[str]
    parent_names: List[str]
    parent_ids: List[int]
    created_at: datetime
    updated_at: datetime
    
    @computed_field
    @property
    def full_name(self) -> str:
        """Get full name."""
        return f"{self.first_name} {self.last_name}"
    
    @computed_field
    @property
    def display_name(self) -> str:
        """Get display name."""
        return self.preferred_name or self.first_name