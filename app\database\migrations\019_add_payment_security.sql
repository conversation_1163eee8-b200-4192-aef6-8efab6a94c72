-- Migration: 019_add_payment_security.sql
-- Description: Add tables for secure payment processing and PCI compliance
-- Author: System
-- Date: 2024-01-15

-- Create secure_payment_records table
CREATE TABLE IF NOT EXISTS secure_payment_records (
    secure_payment_id SERIAL PRIMARY KEY,
    payment_intent_id VARCHAR(255) UNIQUE NOT NULL,
    client_id INTEGER NOT NULL REFERENCES clients(client_id),
    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    currency CHAR(3) NOT NULL DEFAULT 'CAD',
    risk_score INTEGER NOT NULL DEFAULT 0,
    risk_level VARCHAR(20) NOT NULL CHECK (risk_level IN ('low', 'medium', 'high')),
    risk_flags TEXT, -- Comma-separated list of risk flags
    idempotency_key VARCHAR(255) UNIQUE NOT NULL,
    encrypted_metadata TEXT, -- Encrypted sensitive data
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    failure_reason TEXT,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create secure_payment_methods table
CREATE TABLE IF NOT EXISTS secure_payment_methods (
    secure_method_id SERIAL PRIMARY KEY,
    client_id INTEGER NOT NULL REFERENCES clients(client_id),
    stripe_payment_method_id VARCHAR(255) UNIQUE NOT NULL,
    card_last_four CHAR(4) NOT NULL,
    card_brand VARCHAR(50) NOT NULL,
    encrypted_metadata TEXT, -- Encrypted card fingerprint and other data
    is_default BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create payment_attempts table for rate limiting
CREATE TABLE IF NOT EXISTS payment_attempts (
    attempt_id SERIAL PRIMARY KEY,
    client_id INTEGER NOT NULL REFERENCES clients(client_id),
    operation VARCHAR(50) NOT NULL,
    ip_address INET,
    user_agent TEXT,
    attempted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create payment_failures table for risk assessment
CREATE TABLE IF NOT EXISTS payment_failures (
    failure_id SERIAL PRIMARY KEY,
    payment_intent_id VARCHAR(255),
    client_id INTEGER REFERENCES clients(client_id),
    failure_reason TEXT,
    failed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create payment_audit_log table for compliance
CREATE TABLE IF NOT EXISTS payment_audit_log (
    audit_id SERIAL PRIMARY KEY,
    secure_payment_id INTEGER REFERENCES secure_payment_records(secure_payment_id),
    event_type VARCHAR(100) NOT NULL,
    event_description TEXT,
    user_id INTEGER REFERENCES user_accounts(user_id),
    ip_address INET,
    user_agent TEXT,
    encrypted_details TEXT, -- Encrypted audit details
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_secure_payments_client ON secure_payment_records(client_id);
CREATE INDEX IF NOT EXISTS idx_secure_payments_status ON secure_payment_records(status);
CREATE INDEX IF NOT EXISTS idx_secure_payments_created ON secure_payment_records(created_at);
CREATE INDEX IF NOT EXISTS idx_payment_methods_client ON secure_payment_methods(client_id) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_payment_attempts_client ON payment_attempts(client_id, operation, attempted_at);
CREATE INDEX IF NOT EXISTS idx_payment_failures_client ON payment_failures(client_id, failed_at);
CREATE INDEX IF NOT EXISTS idx_audit_log_payment ON payment_audit_log(secure_payment_id);
CREATE INDEX IF NOT EXISTS idx_audit_log_event ON payment_audit_log(event_type, created_at);

-- Add comments
COMMENT ON TABLE secure_payment_records IS 'PCI-compliant payment records with encryption';
COMMENT ON TABLE secure_payment_methods IS 'Tokenized payment methods - no raw card data';
COMMENT ON TABLE payment_attempts IS 'Track payment attempts for rate limiting';
COMMENT ON TABLE payment_failures IS 'Track failed payments for risk assessment';
COMMENT ON TABLE payment_audit_log IS 'Comprehensive audit trail for PCI compliance';

COMMENT ON COLUMN secure_payment_records.encrypted_metadata IS 'AES-256 encrypted sensitive payment data';
COMMENT ON COLUMN secure_payment_records.risk_score IS 'Risk assessment score 0-100';
COMMENT ON COLUMN secure_payment_methods.card_last_four IS 'Only last 4 digits stored for display';
COMMENT ON COLUMN payment_audit_log.encrypted_details IS 'Encrypted audit details for compliance';