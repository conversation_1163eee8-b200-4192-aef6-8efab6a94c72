#!/usr/bin/env python3
"""
Generate password hash using the app's API
"""

import requests
import json

# Create a simple test endpoint that will hash a password
test_code = '''
@router.get("/debug/hash-password/{password}")
async def debug_hash_password(password: str):
    """Debug endpoint to generate password hash"""
    from app.core.security import get_password_hash
    return {"password": password, "hash": get_password_hash(password)}
'''

print("To generate the correct bcrypt hash:")
print("\n1. Add this temporary endpoint to app/api/v1/auth_updated.py:")
print(test_code)
print("\n2. Deploy and visit:")
print("https://tutoraide-production.up.railway.app/api/v1/auth/debug/hash-password/TempPass123!")
print("\n3. Copy the hash from the response")
print("\n4. Use it in the SQL UPDATE statement")
print("\n5. Remove the debug endpoint after use")

# Alternative: Direct SQL to reset password using the app's method
alternative_sql = """
-- Alternative: Create a debug function in PostgreSQL
-- This approach works if you have superuser access

-- First, check what password hashes look like in your system
SELECT 
    user_id, 
    email, 
    LENGTH(password_hash) as hash_length,
    LEFT(password_hash, 7) as hash_prefix
FROM user_accounts 
LIMIT 5;

-- The hash should start with $2b$ (bcrypt)
-- Length should be 60 characters
"""

print("\n" + "="*50)
print("Alternative approach - Check existing hashes:")
print(alternative_sql)