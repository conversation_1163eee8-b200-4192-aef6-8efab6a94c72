"""
Service for managing pricing strategies and platform commission calculations.
"""

import asyncpg
import logging
from typing import List, Optional, Dict, Any, Tu<PERSON>
from datetime import datetime, date, timedelta
from decimal import Decimal, ROUND_HALF_UP

from app.database.repositories.billing_repository import BillingRepository
from app.database.repositories.tutor_repository import TutorRepository
from app.core.exceptions import ResourceNotFoundError, BusinessLogicError, ValidationError
from app.core.timezone import now_est
from app.config.database import get_db_connection
from app.models.pricing_models import (
    PricingRule, PricingRuleCreate, PricingRuleUpdate,
    ServicePricing, ServicePricingCreate,
    CommissionStructure, CommissionTier,
    PricingCalculation, PlatformRevenue
)

logger = logging.getLogger(__name__)


class PricingService:
    """Service for managing pricing and platform commissions."""
    
    # Default commission rates
    DEFAULT_COMMISSION_RATE = Decimal("0.20")  # 20% platform commission
    MIN_COMMISSION_RATE = Decimal("0.10")      # 10% minimum
    MAX_COMMISSION_RATE = Decimal("0.35")      # 35% maximum
    
    # Pricing constraints
    MIN_HOURLY_RATE = Decimal("25.00")         # Minimum $25/hour
    MAX_HOURLY_RATE = Decimal("200.00")        # Maximum $200/hour
    
    def __init__(self):
        self.billing_repo = BillingRepository()
        self.tutor_repo = TutorRepository()
    
    async def create_pricing_rule(
        self,
        rule_data: PricingRuleCreate,
        created_by: int
    ) -> PricingRule:
        """
        Create a new pricing rule for services.
        
        Args:
            rule_data: Pricing rule data
            created_by: User creating the rule
            
        Returns:
            Created pricing rule
        """
        try:
            # Validate rates
            if rule_data.base_client_rate < self.MIN_HOURLY_RATE:
                raise ValidationError(f"Client rate must be at least ${self.MIN_HOURLY_RATE}/hour")
            
            if rule_data.base_client_rate > self.MAX_HOURLY_RATE:
                raise ValidationError(f"Client rate cannot exceed ${self.MAX_HOURLY_RATE}/hour")
            
            if rule_data.base_tutor_rate >= rule_data.base_client_rate:
                raise ValidationError("Tutor rate must be less than client rate")
            
            # Calculate commission
            commission_amount = rule_data.base_client_rate - rule_data.base_tutor_rate
            commission_percentage = (commission_amount / rule_data.base_client_rate) * 100
            
            async with get_db_connection() as conn:
                rule_id = await conn.fetchval("""
                    INSERT INTO pricing_rules (
                        rule_name, rule_code, description,
                        service_type, subject_area, location_type,
                        base_client_rate, base_tutor_rate,
                        commission_percentage, commission_amount,
                        priority, is_active, effective_date,
                        expiry_date, created_by, created_at, updated_at
                    ) VALUES (
                        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10,
                        $11, $12, $13, $14, $15, $16, $16
                    ) RETURNING rule_id
                """,
                    rule_data.rule_name, rule_data.rule_code, rule_data.description,
                    rule_data.service_type, rule_data.subject_area, rule_data.location_type,
                    rule_data.base_client_rate, rule_data.base_tutor_rate,
                    commission_percentage, commission_amount,
                    rule_data.priority or 100, True, rule_data.effective_date or date.today(),
                    rule_data.expiry_date, created_by, now_est()
                )
                
                logger.info(
                    f"Created pricing rule {rule_id}: {rule_data.rule_name}, "
                    f"commission {commission_percentage:.1f}%"
                )
                
                return await self.get_pricing_rule_by_id(rule_id)
        
        except Exception as e:
            logger.error(f"Error creating pricing rule: {str(e)}")
            raise BusinessLogicError(f"Failed to create pricing rule: {str(e)}")
    
    async def get_pricing_rule_by_id(self, rule_id: int) -> Optional[PricingRule]:
        """Get pricing rule by ID."""
        async with get_db_connection() as conn:
            data = await conn.fetchrow("""
                SELECT * FROM pricing_rules WHERE rule_id = $1
            """, rule_id)
            
            if not data:
                return None
            
            return PricingRule(**dict(data))
    
    async def calculate_pricing_for_service(
        self,
        service_type: str,
        subject_area: str,
        location_type: str,
        duration_hours: Decimal,
        tutor_id: Optional[int] = None,
        client_id: Optional[int] = None,
        session_date: Optional[date] = None
    ) -> PricingCalculation:
        """
        Calculate pricing for a service including platform commission.
        
        Args:
            service_type: Type of service (online, in-person, etc)
            subject_area: Subject being taught
            location_type: Location type for in-person
            duration_hours: Session duration
            tutor_id: Optional tutor for custom rates
            client_id: Optional client for custom rates
            session_date: Session date for time-based pricing
            
        Returns:
            Pricing calculation with rates and commission
        """
        try:
            async with get_db_connection() as conn:
                # Find applicable pricing rule
                rule = await self._find_best_pricing_rule(
                    conn, service_type, subject_area, location_type, session_date
                )
                
                if not rule:
                    # Use default pricing
                    base_client_rate = Decimal("60.00")
                    base_tutor_rate = Decimal("45.00")
                    rule_name = "Default Pricing"
                else:
                    base_client_rate = Decimal(str(rule['base_client_rate']))
                    base_tutor_rate = Decimal(str(rule['base_tutor_rate']))
                    rule_name = rule['rule_name']
                
                # Check for tutor custom rate
                if tutor_id:
                    tutor_custom = await conn.fetchrow("""
                        SELECT 
                            custom_rate,
                            rate_type,
                            commission_override_percentage
                        FROM tutor_service_rates
                        WHERE tutor_id = $1
                            AND service_type = $2
                            AND (subject_area = $3 OR subject_area IS NULL)
                            AND is_active = true
                        ORDER BY subject_area NULLS LAST
                        LIMIT 1
                    """, tutor_id, service_type, subject_area)
                    
                    if tutor_custom and tutor_custom['custom_rate']:
                        custom_rate = Decimal(str(tutor_custom['custom_rate']))
                        if tutor_custom['rate_type'] == 'tutor_rate':
                            base_tutor_rate = custom_rate
                            # Maintain minimum commission
                            min_client_rate = base_tutor_rate / (1 - self.MIN_COMMISSION_RATE)
                            base_client_rate = max(base_client_rate, min_client_rate)
                        else:  # client_rate
                            base_client_rate = custom_rate
                
                # Check for client negotiated rate
                if client_id:
                    client_custom = await conn.fetchrow("""
                        SELECT negotiated_rate
                        FROM client_service_rates
                        WHERE client_id = $1
                            AND service_type = $2
                            AND (subject_area = $3 OR subject_area IS NULL)
                            AND is_active = true
                        ORDER BY subject_area NULLS LAST
                        LIMIT 1
                    """, client_id, service_type, subject_area)
                    
                    if client_custom and client_custom['negotiated_rate']:
                        base_client_rate = Decimal(str(client_custom['negotiated_rate']))
                
                # Apply surge pricing if applicable
                surge_multiplier = await self._get_surge_multiplier(
                    conn, session_date, service_type
                )
                
                if surge_multiplier > Decimal("1.00"):
                    base_client_rate = base_client_rate * surge_multiplier
                    # Tutor gets partial surge benefit (50%)
                    tutor_surge_bonus = (base_client_rate - base_client_rate / surge_multiplier) * Decimal("0.50")
                    base_tutor_rate += tutor_surge_bonus
                
                # Calculate amounts
                client_amount = (base_client_rate * duration_hours).quantize(
                    Decimal("0.01"), rounding=ROUND_HALF_UP
                )
                tutor_amount = (base_tutor_rate * duration_hours).quantize(
                    Decimal("0.01"), rounding=ROUND_HALF_UP
                )
                commission_amount = client_amount - tutor_amount
                commission_percentage = (commission_amount / client_amount * 100) if client_amount > 0 else Decimal("0.00")
                
                return PricingCalculation(
                    service_type=service_type,
                    subject_area=subject_area,
                    location_type=location_type,
                    duration_hours=duration_hours,
                    client_rate_per_hour=base_client_rate,
                    tutor_rate_per_hour=base_tutor_rate,
                    client_total_amount=client_amount,
                    tutor_total_amount=tutor_amount,
                    platform_commission_amount=commission_amount,
                    platform_commission_percentage=commission_percentage,
                    surge_multiplier=surge_multiplier,
                    pricing_rule_applied=rule_name,
                    breakdown={
                        'base_calculation': {
                            'client_rate': float(base_client_rate),
                            'tutor_rate': float(base_tutor_rate),
                            'hours': float(duration_hours)
                        },
                        'adjustments': {
                            'surge_pricing': surge_multiplier > Decimal("1.00"),
                            'surge_multiplier': float(surge_multiplier),
                            'custom_tutor_rate': tutor_id is not None and tutor_custom is not None,
                            'custom_client_rate': client_id is not None and client_custom is not None
                        },
                        'commission': {
                            'amount': float(commission_amount),
                            'percentage': float(commission_percentage),
                            'meets_minimum': commission_percentage >= (self.MIN_COMMISSION_RATE * 100)
                        }
                    }
                )
        
        except Exception as e:
            logger.error(f"Error calculating pricing: {str(e)}")
            raise BusinessLogicError(f"Failed to calculate pricing: {str(e)}")
    
    async def _find_best_pricing_rule(
        self,
        conn: asyncpg.Connection,
        service_type: str,
        subject_area: str,
        location_type: str,
        session_date: Optional[date] = None
    ) -> Optional[Dict[str, Any]]:
        """Find the best matching pricing rule."""
        if not session_date:
            session_date = date.today()
        
        # Try exact match first
        rule = await conn.fetchrow("""
            SELECT * FROM pricing_rules
            WHERE service_type = $1
                AND subject_area = $2
                AND location_type = $3
                AND is_active = true
                AND effective_date <= $4
                AND (expiry_date IS NULL OR expiry_date >= $4)
            ORDER BY priority DESC, created_at DESC
            LIMIT 1
        """, service_type, subject_area, location_type, session_date)
        
        if rule:
            return dict(rule)
        
        # Try without location type
        rule = await conn.fetchrow("""
            SELECT * FROM pricing_rules
            WHERE service_type = $1
                AND subject_area = $2
                AND location_type IS NULL
                AND is_active = true
                AND effective_date <= $3
                AND (expiry_date IS NULL OR expiry_date >= $3)
            ORDER BY priority DESC, created_at DESC
            LIMIT 1
        """, service_type, subject_area, session_date)
        
        if rule:
            return dict(rule)
        
        # Try without subject area
        rule = await conn.fetchrow("""
            SELECT * FROM pricing_rules
            WHERE service_type = $1
                AND subject_area IS NULL
                AND is_active = true
                AND effective_date <= $2
                AND (expiry_date IS NULL OR expiry_date >= $2)
            ORDER BY priority DESC, created_at DESC
            LIMIT 1
        """, service_type, session_date)
        
        return dict(rule) if rule else None
    
    async def _get_surge_multiplier(
        self,
        conn: asyncpg.Connection,
        session_date: Optional[date],
        service_type: str
    ) -> Decimal:
        """Get surge pricing multiplier for peak times."""
        if not session_date:
            return Decimal("1.00")
        
        # Check for surge pricing rules
        surge = await conn.fetchrow("""
            SELECT multiplier FROM surge_pricing_rules
            WHERE service_type = $1
                AND is_active = true
                AND (
                    (day_of_week = EXTRACT(DOW FROM $2::date))
                    OR (date_specific = $2::date)
                )
            ORDER BY date_specific DESC NULLS LAST, multiplier DESC
            LIMIT 1
        """, service_type, session_date)
        
        if surge:
            return Decimal(str(surge['multiplier']))
        
        return Decimal("1.00")
    
    async def get_platform_revenue_summary(
        self,
        period_start: date,
        period_end: date
    ) -> PlatformRevenue:
        """
        Get platform revenue summary for a period.
        
        Args:
            period_start: Start date
            period_end: End date
            
        Returns:
            Platform revenue summary
        """
        try:
            async with get_db_connection() as conn:
                # Get revenue from completed appointments
                revenue_data = await conn.fetchrow("""
                    SELECT 
                        COUNT(DISTINCT a.appointment_id) as session_count,
                        COUNT(DISTINCT a.client_id) as unique_clients,
                        COUNT(DISTINCT a.tutor_id) as active_tutors,
                        SUM(a.duration_hours) as total_hours,
                        SUM(a.duration_hours * a.client_rate) as gross_revenue,
                        SUM(a.duration_hours * a.tutor_rate) as tutor_costs,
                        SUM(a.duration_hours * (a.client_rate - a.tutor_rate)) as platform_commission
                    FROM appointments a
                    WHERE a.appointment_date BETWEEN $1 AND $2
                        AND a.status = 'completed'
                        AND a.duration_hours > 0
                """, period_start, period_end)
                
                if not revenue_data or not revenue_data['gross_revenue']:
                    return PlatformRevenue(
                        period_start=period_start,
                        period_end=period_end,
                        gross_revenue=Decimal("0.00"),
                        tutor_payments=Decimal("0.00"),
                        platform_commission=Decimal("0.00"),
                        net_revenue=Decimal("0.00"),
                        average_commission_rate=Decimal("0.00"),
                        session_count=0,
                        unique_clients=0,
                        active_tutors=0,
                        total_hours=Decimal("0.00"),
                        revenue_by_service={},
                        revenue_by_subject={},
                        top_performing_tutors=[]
                    )
                
                gross_revenue = Decimal(str(revenue_data['gross_revenue']))
                tutor_costs = Decimal(str(revenue_data['tutor_costs']))
                platform_commission = Decimal(str(revenue_data['platform_commission']))
                
                # Get revenue breakdown by service type
                service_breakdown = await conn.fetch("""
                    SELECT 
                        a.service_type,
                        COUNT(*) as sessions,
                        SUM(a.duration_hours * (a.client_rate - a.tutor_rate)) as commission
                    FROM appointments a
                    WHERE a.appointment_date BETWEEN $1 AND $2
                        AND a.status = 'completed'
                    GROUP BY a.service_type
                """, period_start, period_end)
                
                revenue_by_service = {
                    row['service_type']: {
                        'sessions': row['sessions'],
                        'commission': float(row['commission'])
                    }
                    for row in service_breakdown
                }
                
                # Get revenue breakdown by subject
                subject_breakdown = await conn.fetch("""
                    SELECT 
                        a.subject,
                        COUNT(*) as sessions,
                        SUM(a.duration_hours * (a.client_rate - a.tutor_rate)) as commission
                    FROM appointments a
                    WHERE a.appointment_date BETWEEN $1 AND $2
                        AND a.status = 'completed'
                    GROUP BY a.subject
                    ORDER BY commission DESC
                    LIMIT 10
                """, period_start, period_end)
                
                revenue_by_subject = {
                    row['subject']: {
                        'sessions': row['sessions'],
                        'commission': float(row['commission'])
                    }
                    for row in subject_breakdown
                }
                
                # Get top performing tutors by commission generated
                top_tutors = await conn.fetch("""
                    SELECT 
                        t.tutor_id,
                        t.first_name || ' ' || t.last_name as tutor_name,
                        COUNT(a.appointment_id) as sessions,
                        SUM(a.duration_hours) as hours,
                        SUM(a.duration_hours * (a.client_rate - a.tutor_rate)) as commission_generated
                    FROM appointments a
                    JOIN tutors t ON a.tutor_id = t.tutor_id
                    WHERE a.appointment_date BETWEEN $1 AND $2
                        AND a.status = 'completed'
                    GROUP BY t.tutor_id, t.first_name, t.last_name
                    ORDER BY commission_generated DESC
                    LIMIT 10
                """, period_start, period_end)
                
                top_performing_tutors = [
                    {
                        'tutor_id': row['tutor_id'],
                        'tutor_name': row['tutor_name'],
                        'sessions': row['sessions'],
                        'hours': float(row['hours']),
                        'commission_generated': float(row['commission_generated'])
                    }
                    for row in top_tutors
                ]
                
                # Calculate average commission rate
                avg_commission_rate = (platform_commission / gross_revenue * 100) if gross_revenue > 0 else Decimal("0.00")
                
                return PlatformRevenue(
                    period_start=period_start,
                    period_end=period_end,
                    gross_revenue=gross_revenue,
                    tutor_payments=tutor_costs,
                    platform_commission=platform_commission,
                    net_revenue=platform_commission,  # Could subtract operating costs here
                    average_commission_rate=avg_commission_rate,
                    session_count=revenue_data['session_count'],
                    unique_clients=revenue_data['unique_clients'],
                    active_tutors=revenue_data['active_tutors'],
                    total_hours=Decimal(str(revenue_data['total_hours'])),
                    revenue_by_service=revenue_by_service,
                    revenue_by_subject=revenue_by_subject,
                    top_performing_tutors=top_performing_tutors
                )
        
        except Exception as e:
            logger.error(f"Error getting platform revenue: {str(e)}")
            raise BusinessLogicError(f"Failed to get revenue summary: {str(e)}")
    
    async def create_commission_structure(
        self,
        structure_data: Dict[str, Any],
        created_by: int
    ) -> CommissionStructure:
        """
        Create tiered commission structure based on performance.
        
        Args:
            structure_data: Commission structure configuration
            created_by: User creating the structure
            
        Returns:
            Created commission structure
        """
        try:
            async with get_db_connection() as conn:
                structure_id = await conn.fetchval("""
                    INSERT INTO commission_structures (
                        structure_name, description,
                        base_commission_rate, is_active,
                        created_by, created_at, updated_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $6)
                    RETURNING structure_id
                """,
                    structure_data['structure_name'],
                    structure_data.get('description'),
                    structure_data['base_commission_rate'],
                    True, created_by, now_est()
                )
                
                # Create commission tiers
                for tier in structure_data.get('tiers', []):
                    await conn.execute("""
                        INSERT INTO commission_tiers (
                            structure_id, tier_name,
                            min_monthly_revenue, max_monthly_revenue,
                            commission_rate, created_at
                        ) VALUES ($1, $2, $3, $4, $5, $6)
                    """,
                        structure_id, tier['tier_name'],
                        tier['min_monthly_revenue'],
                        tier.get('max_monthly_revenue'),
                        tier['commission_rate'], now_est()
                    )
                
                logger.info(f"Created commission structure {structure_id}")
                
                # Return the created structure
                return await self.get_commission_structure(structure_id)
        
        except Exception as e:
            logger.error(f"Error creating commission structure: {str(e)}")
            raise BusinessLogicError(f"Failed to create commission structure: {str(e)}")
    
    async def get_commission_structure(self, structure_id: int) -> Optional[CommissionStructure]:
        """Get commission structure with tiers."""
        async with get_db_connection() as conn:
            structure = await conn.fetchrow("""
                SELECT * FROM commission_structures WHERE structure_id = $1
            """, structure_id)
            
            if not structure:
                return None
            
            tiers = await conn.fetch("""
                SELECT * FROM commission_tiers
                WHERE structure_id = $1
                ORDER BY min_monthly_revenue
            """, structure_id)
            
            return CommissionStructure(
                **dict(structure),
                tiers=[CommissionTier(**dict(tier)) for tier in tiers]
            )