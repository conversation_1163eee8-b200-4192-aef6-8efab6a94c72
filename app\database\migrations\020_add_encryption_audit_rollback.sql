-- Rollback Migration: 020_add_encryption_audit_rollback.sql
-- Description: Rollback encryption audit logging tables
-- Author: System
-- Date: 2024-01-15

-- Drop indexes
DROP INDEX IF EXISTS idx_encrypted_docs_type;
DROP INDEX IF EXISTS idx_encrypted_docs_owner;
DROP INDEX IF EXISTS idx_encryption_audit_date;
DROP INDEX IF EXISTS idx_encryption_audit_user;
DROP INDEX IF EXISTS idx_encryption_audit_resource;
DROP INDEX IF EXISTS idx_encryption_audit_event;

-- Drop encryption columns from existing tables
ALTER TABLE secure_payment_methods 
DROP COLUMN IF EXISTS encryption_version,
DROP COLUMN IF EXISTS encrypted_at;

ALTER TABLE tutor_bank_accounts 
DROP COLUMN IF EXISTS account_number_encrypted,
DROP COLUMN IF EXISTS routing_number_encrypted,
DROP COLUMN IF EXISTS encryption_version,
DROP COLUMN IF EXISTS encrypted_at;

-- Drop tables
DROP TABLE IF EXISTS encrypted_financial_documents;
DROP TABLE IF EXISTS encryption_keys_metadata;
DROP TABLE IF EXISTS encrypted_fields_registry;
DROP TABLE IF EXISTS encryption_audit_log;