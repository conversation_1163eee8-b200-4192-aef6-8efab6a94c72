{"name": "tutoraide-frontend", "version": "1.0.0", "private": true, "type": "module", "packageManager": "npm@9.0.0", "scripts": {"dev": "vite", "build": "vite build --mode production", "build:check": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit"}, "dependencies": {"@heroicons/react": "^2.1.1", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@sentry/react": "^9.33.0", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "@tanstack/react-query": "^5.18.0", "axios": "^1.6.7", "clsx": "^2.1.0", "date-fns": "^3.3.1", "framer-motion": "^11.0.3", "i18next": "^23.8.2", "leaflet": "^1.9.4", "lucide-react": "^0.316.0", "qrcode.react": "^3.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.49.3", "react-hot-toast": "^2.4.1", "react-i18next": "^14.0.5", "react-leaflet": "^4.2.1", "react-router-dom": "^6.22.0", "recharts": "^2.10.4", "zustand": "^4.5.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.2.0", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.2", "@types/qrcode.react": "^1.0.5", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.19.1", "@typescript-eslint/parser": "^6.19.1", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^1.2.2", "@vitest/ui": "^1.2.2", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "jsdom": "^24.0.0", "postcss": "^8.4.33", "puppeteer": "^24.10.2", "tailwindcss": "^3.4.1", "typescript": "^5.3.3", "vite": "^5.0.12", "vitest": "^1.2.2"}}