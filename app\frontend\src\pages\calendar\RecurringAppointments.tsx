import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { format, addWeeks, differenceInDays, parseISO } from 'date-fns';
import { 
  Calendar, Clock, Repeat, Edit, Trash2, Play, Pause, Eye, Users, 
  <PERSON><PERSON><PERSON>riangle, CheckCircle, XCircle, MapPin, DollarSign, User
} from 'lucide-react';
import { appointmentService } from '../../services/appointmentService';
import { tutorService } from '../../services/tutorService';
import { LocationType } from '../../types/appointment';
import { useAuth } from '../../contexts/AuthContext';
import toast from 'react-hot-toast';

interface RecurringSeries {
  recurring_id: number;
  tutor_id: number;
  tutor_name: string;
  client_id: number;
  client_name: string;
  dependant_id?: number;
  dependant_name?: string;
  day_of_week: number;
  start_time: string;
  end_time: string;
  subject_area: string;
  location_details: any;
  hourly_rate: number;
  currency: string;
  start_date: Date;
  end_date?: Date;
  frequency_weeks: number;
  is_active: boolean;
  total_appointments: number;
  completed_appointments: number;
  upcoming_appointments: number;
  cancelled_appointments: number;
  next_appointment?: any;
  last_appointment?: any;
}

interface RecurringAppointmentsProps {
  tutorId?: number;
  clientId?: number;
  isModal?: boolean;
  onClose?: () => void;
  onSeriesSelected?: (series: RecurringSeries) => void;
}

const RecurringAppointments: React.FC<RecurringAppointmentsProps> = ({
  tutorId,
  clientId,
  isModal = false,
  onClose,
  onSeriesSelected
}) => {
  const { t } = useTranslation();
  const [series, setSeries] = useState<RecurringSeries[]>([]);
  const [selectedSeries, setSelectedSeries] = useState<RecurringSeries | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const { user } = useAuth();
  
  const [formData, setFormData] = useState({
    tutor_id: tutorId || 0,
    client_id: clientId || user?.userId || 0,
    dependant_id: 0,
    day_of_week: 1,
    start_time: '14:00',
    end_time: '15:00',
    subject_area: '',
    location_type: LocationType.ONLINE,
    location_details: '',
    hourly_rate: 50,
    start_date: '',
    end_date: '',
    frequency_weeks: 1
  });

  const [tutors, setTutors] = useState<Array<{ id: number; name: string; }>>([]);
  const [clients, setClients] = useState<Array<{ id: number; name: string; }>>([]);

  const daysOfWeek = [
    { id: 1, name: 'Monday', short: 'Mon' },
    { id: 2, name: 'Tuesday', short: 'Tue' },
    { id: 3, name: 'Wednesday', short: 'Wed' },
    { id: 4, name: 'Thursday', short: 'Thu' },
    { id: 5, name: 'Friday', short: 'Fri' },
    { id: 6, name: 'Saturday', short: 'Sat' },
    { id: 7, name: 'Sunday', short: 'Sun' }
  ];

  // Load data from API
  useEffect(() => {
    loadData();
  }, [tutorId, clientId]);

  const loadData = async () => {
    setLoading(true);
    try {
      // Load tutors
      const tutorResponse = await tutorService.searchTutors({ is_active: true, limit: 50 });
      setTutors(tutorResponse.items.map(t => ({
        id: t.tutor_id,
        name: `${t.first_name} ${t.last_name}`
      })));

      // Load clients - for now we'll use mock data since client API is not yet implemented
      // TODO: Replace with actual client API when available
      const mockClients = [
        { id: 1, name: 'Emma Johnson' },
        { id: 2, name: 'Lucas Martin' },
        { id: 3, name: 'Sophia Chen' },
        { id: 4, name: 'Oliver Brown' }
      ];
      setClients(mockClients);

      // Load recurring series
      const params: any = {};
      if (tutorId) params.tutor_id = tutorId;
      if (clientId) params.client_id = clientId;
      
      const seriesResponse = await appointmentService.listRecurringSeries(params);
      setSeries(seriesResponse.items.map((item: any) => ({
        ...item,
        start_date: new Date(item.start_date),
        end_date: item.end_date ? new Date(item.end_date) : undefined,
        next_appointment: item.next_appointment ? {
          ...item.next_appointment,
          scheduled_date: new Date(item.next_appointment.scheduled_date)
        } : undefined
      })));
    } catch (error) {
      console.error('Error loading data:', error);
      toast.error(t('recurring.loadError'));
    } finally {
      setLoading(false);
    }
  };

  const handleCreateSeries = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.tutor_id || !formData.client_id || !formData.subject_area.trim() || !formData.start_date) {
      toast.error(t('recurring.validation.requiredFields'));
      return;
    }

    setSaving(true);
    try {
      const createData = {
        tutor_id: formData.tutor_id,
        client_id: formData.client_id,
        dependant_id: formData.dependant_id || undefined,
        day_of_week: formData.day_of_week,
        start_time: formData.start_time,
        end_time: formData.end_time,
        subject_area: formData.subject_area,
        location_type: formData.location_type,
        location_details: formData.location_details || undefined,
        hourly_rate: formData.hourly_rate,
        start_date: formData.start_date,
        end_date: formData.end_date || undefined,
        frequency_weeks: formData.frequency_weeks
      };

      const result = await appointmentService.createRecurringSeries(createData);
      
      // Reload the series list
      await loadData();
      
      // Reset form
      setFormData({
        tutor_id: tutorId || 0,
        client_id: clientId || user?.userId || 0,
        dependant_id: 0,
        day_of_week: 1,
        start_time: '14:00',
        end_time: '15:00',
        subject_area: '',
        location_type: LocationType.ONLINE,
        location_details: '',
        hourly_rate: 50,
        start_date: '',
        end_date: '',
        frequency_weeks: 1
      });
      
      setShowCreateForm(false);
      toast.success(t('recurring.seriesCreated'));
    } catch (error) {
      console.error('Error creating recurring series:', error);
      toast.error(t('recurring.createError'));
    } finally {
      setSaving(false);
    }
  };

  const handleUpdateSeries = async (seriesId: number, updates: any) => {
    setSaving(true);
    try {
      await appointmentService.updateRecurringSeries(seriesId, updates);
      await loadData();
      toast.success(t('recurring.seriesUpdated'));
    } catch (error) {
      console.error('Error updating recurring series:', error);
      toast.error(t('recurring.updateError'));
    } finally {
      setSaving(false);
    }
  };

  const handleCancelSeries = async (seriesId: number) => {
    if (!confirm(t('recurring.confirmCancel'))) return;

    setSaving(true);
    try {
      await appointmentService.cancelRecurringSeries(seriesId, 'Future appointments cancelled');
      await loadData();
      toast.success(t('recurring.seriesCancelled'));
    } catch (error) {
      console.error('Error cancelling recurring series:', error);
      toast.error(t('recurring.cancelError'));
    } finally {
      setSaving(false);
    }
  };

  const handleToggleActive = async (seriesId: number, isActive: boolean) => {
    setSaving(true);
    try {
      await appointmentService.toggleRecurringSeries(seriesId, isActive);
      await loadData();
      toast.success(isActive ? t('recurring.seriesReactivated') : t('recurring.seriesPaused'));
    } catch (error) {
      console.error('Error toggling series status:', error);
      toast.error(t('recurring.toggleError'));
    } finally {
      setSaving(false);
    }
  };

  const getFrequencyText = (weeks: number): string => {
    switch (weeks) {
      case 1: return t('recurring.weekly');
      case 2: return t('recurring.biweekly');
      case 4: return t('recurring.monthly');
      default: return t('recurring.everyNWeeks', { weeks });
    }
  };

  const getStatusColor = (seriesItem: RecurringSeries): string => {
    if (!seriesItem.is_active) return 'text-gray-500 bg-gray-100';
    if (seriesItem.upcoming_appointments === 0) return 'text-orange-600 bg-orange-100';
    return 'text-green-600 bg-green-100';
  };

  const getProgressPercentage = (seriesItem: RecurringSeries): number => {
    if (seriesItem.total_appointments === 0) return 0;
    return Math.round((seriesItem.completed_appointments / seriesItem.total_appointments) * 100);
  };

  const renderCreateForm = () => (
    <div className="bg-white rounded-lg p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">
          {t('recurring.createNewSeries')}
        </h3>
        <button
          onClick={() => setShowCreateForm(false)}
          className="text-gray-400 hover:text-gray-600"
        >
          <XCircle className="w-6 h-6" />
        </button>
      </div>

      <form onSubmit={handleCreateSeries} className="space-y-4">
        {/* Tutor and Client Selection */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {!tutorId && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <User className="w-4 h-4 inline mr-1" />
                {t('recurring.tutor')}
              </label>
              <select
                value={formData.tutor_id}
                onChange={(e) => setFormData(prev => ({ ...prev, tutor_id: parseInt(e.target.value) }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                required
              >
                <option value="">{t('recurring.selectTutor')}</option>
                {tutors.map((tutor) => (
                  <option key={tutor.id} value={tutor.id}>
                    {tutor.name}
                  </option>
                ))}
              </select>
            </div>
          )}

          {!clientId && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('recurring.client')}
              </label>
              <select
                value={formData.client_id}
                onChange={(e) => setFormData(prev => ({ ...prev, client_id: parseInt(e.target.value) }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                required
              >
                <option value="">{t('recurring.selectClient')}</option>
                {clients.map((client) => (
                  <option key={client.id} value={client.id}>
                    {client.name}
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>

        {/* Subject and Schedule */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('recurring.subjectArea')}
            </label>
            <input
              type="text"
              value={formData.subject_area}
              onChange={(e) => setFormData(prev => ({ ...prev, subject_area: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              placeholder={t('recurring.subjectPlaceholder')}
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('recurring.dayOfWeek')}
            </label>
            <select
              value={formData.day_of_week}
              onChange={(e) => setFormData(prev => ({ ...prev, day_of_week: parseInt(e.target.value) }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
            >
              {daysOfWeek.map((day) => (
                <option key={day.id} value={day.id}>
                  {day.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Time and Rate */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Clock className="w-4 h-4 inline mr-1" />
              {t('recurring.startTime')}
            </label>
            <input
              type="time"
              value={formData.start_time}
              onChange={(e) => setFormData(prev => ({ ...prev, start_time: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('recurring.endTime')}
            </label>
            <input
              type="time"
              value={formData.end_time}
              onChange={(e) => setFormData(prev => ({ ...prev, end_time: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <DollarSign className="w-4 h-4 inline mr-1" />
              {t('recurring.hourlyRate')}
            </label>
            <input
              type="number"
              value={formData.hourly_rate}
              onChange={(e) => setFormData(prev => ({ ...prev, hourly_rate: parseFloat(e.target.value) }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              step="0.01"
              min="0"
              required
            />
          </div>
        </div>

        {/* Frequency and Dates */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Repeat className="w-4 h-4 inline mr-1" />
              {t('recurring.frequency')}
            </label>
            <select
              value={formData.frequency_weeks}
              onChange={(e) => setFormData(prev => ({ ...prev, frequency_weeks: parseInt(e.target.value) }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
            >
              <option value={1}>{t('recurring.weekly')}</option>
              <option value={2}>{t('recurring.biweekly')}</option>
              <option value={4}>{t('recurring.monthly')}</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Calendar className="w-4 h-4 inline mr-1" />
              {t('recurring.startDate')}
            </label>
            <input
              type="date"
              value={formData.start_date}
              onChange={(e) => setFormData(prev => ({ ...prev, start_date: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('recurring.endDate')} ({t('common.optional')})
            </label>
            <input
              type="date"
              value={formData.end_date}
              onChange={(e) => setFormData(prev => ({ ...prev, end_date: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
            />
          </div>
        </div>

        {/* Location Type */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <MapPin className="w-4 h-4 inline mr-1" />
              {t('recurring.locationType')}
            </label>
            <select
              value={formData.location_type}
              onChange={(e) => setFormData(prev => ({ ...prev, location_type: e.target.value as LocationType }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
            >
              <option value={LocationType.ONLINE}>{t('recurring.online')}</option>
              <option value={LocationType.IN_PERSON}>{t('recurring.inPerson')}</option>
              <option value={LocationType.LIBRARY}>{t('recurring.library')}</option>
              <option value={LocationType.HYBRID}>{t('recurring.hybrid')}</option>
            </select>
          </div>
          
          {formData.location_type !== LocationType.ONLINE && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('recurring.locationDetails')}
              </label>
              <input
                type="text"
                value={formData.location_details}
                onChange={(e) => setFormData(prev => ({ ...prev, location_details: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                placeholder={t('recurring.locationDetailsPlaceholder')}
              />
            </div>
          )}
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <button
            type="button"
            onClick={() => setShowCreateForm(false)}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            {t('common.cancel')}
          </button>
          <button
            type="submit"
            disabled={saving}
            className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 disabled:opacity-50"
          >
            {saving ? t('common.creating') : t('recurring.createSeries')}
          </button>
        </div>
      </form>
    </div>
  );

  const renderSeriesList = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">
          {t('recurring.series')}
        </h3>
        <button
          onClick={() => setShowCreateForm(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700"
        >
          <Repeat className="w-4 h-4 mr-2" />
          {t('recurring.createNewSeries')}
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {series.map((seriesItem) => (
          <div
            key={seriesItem.recurring_id}
            className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50"
          >
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-1">
                  <span className="font-medium text-gray-900">
                    {seriesItem.subject_area}
                  </span>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(seriesItem)}`}>
                    {seriesItem.is_active ? t('recurring.active') : t('recurring.paused')}
                  </span>
                </div>
                
                <p className="text-sm text-gray-600">
                  {seriesItem.tutor_name} → {seriesItem.client_name}
                  {seriesItem.dependant_name && ` (${seriesItem.dependant_name})`}
                </p>
                
                <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                  <div className="flex items-center">
                    <Calendar className="w-3 h-3 mr-1" />
                    {daysOfWeek.find(d => d.id === seriesItem.day_of_week)?.short}
                  </div>
                  <div className="flex items-center">
                    <Clock className="w-3 h-3 mr-1" />
                    {seriesItem.start_time} - {seriesItem.end_time}
                  </div>
                  <div className="flex items-center">
                    <Repeat className="w-3 h-3 mr-1" />
                    {getFrequencyText(seriesItem.frequency_weeks)}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-1">
                {seriesItem.is_active ? (
                  <button
                    onClick={() => handleToggleActive(seriesItem.recurring_id, false)}
                    className="p-1 text-yellow-600 hover:text-yellow-800"
                    title={t('recurring.pause')}
                  >
                    <Pause className="w-4 h-4" />
                  </button>
                ) : (
                  <button
                    onClick={() => handleToggleActive(seriesItem.recurring_id, true)}
                    className="p-1 text-green-600 hover:text-green-800"
                    title={t('recurring.reactivate')}
                  >
                    <Play className="w-4 h-4" />
                  </button>
                )}
                
                <button
                  onClick={() => setSelectedSeries(seriesItem)}
                  className="p-1 text-red-600 hover:text-red-800"
                  title={t('recurring.viewDetails')}
                >
                  <Eye className="w-4 h-4" />
                </button>
                
                <button
                  onClick={() => handleCancelSeries(seriesItem.recurring_id)}
                  className="p-1 text-red-600 hover:text-red-800"
                  title={t('recurring.cancel')}
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>
            
            {/* Progress and Stats */}
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">{t('recurring.progress')}</span>
                <span className="font-medium">
                  {seriesItem.completed_appointments}/{seriesItem.total_appointments} 
                  ({getProgressPercentage(seriesItem)}%)
                </span>
              </div>
              
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-red-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${getProgressPercentage(seriesItem)}%` }}
                />
              </div>
              
              <div className="flex items-center justify-between text-xs text-gray-500">
                <span>{t('recurring.upcoming')}: {seriesItem.upcoming_appointments}</span>
                <span>{t('recurring.cancelled')}: {seriesItem.cancelled_appointments}</span>
              </div>
              
              {seriesItem.next_appointment && (
                <div className="text-xs text-red-600">
                  {t('recurring.nextSession')}: {format(seriesItem.next_appointment.scheduled_date, 'MMM dd, yyyy')} at {seriesItem.next_appointment.start_time}
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {series.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <Repeat className="w-12 h-12 mx-auto mb-3 text-gray-300" />
          <p>{t('recurring.noSeries')}</p>
        </div>
      )}
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
      </div>
    );
  }

  const content = (
    <div className="space-y-6">
      {showCreateForm ? renderCreateForm() : renderSeriesList()}
    </div>
  );

  if (isModal) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
          <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">
              {t('recurring.title')}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XCircle className="w-6 h-6" />
            </button>
          </div>
          <div className="p-6">
            {content}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      {content}
    </div>
  );
};

export default RecurringAppointments;
