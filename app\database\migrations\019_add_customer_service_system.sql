-- Migration 019: Add Customer Service System
-- Creates comprehensive customer service infrastructure

-- Customer service agents (managers with support roles)
CREATE TABLE customer_service_agents (
    agent_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    agent_name VARCHAR(255) NOT NULL,
    department VARCHAR(100) DEFAULT 'general',
    status VARCHAR(50) DEFAULT 'available', -- available, busy, away, offline
    max_concurrent_conversations INTEGER DEFAULT 10,
    specialties TEXT[], -- array of specialties like 'billing', 'technical', 'general'
    languages VARCHAR(20)[] DEFAULT ARRAY['en'], -- supported languages
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- Enhanced conversation threads with customer service features
CREATE TABLE cs_conversations (
    conversation_id SERIAL PRIMARY KEY,
    thread_id VARCHAR(255) UNIQUE NOT NULL, -- unique thread identifier
    participant_user_id INTEGER REFERENCES user_accounts(user_id),
    participant_phone VARCHAR(20), -- for non-registered users
    participant_email VARCHAR(255),
    participant_name VARCHAR(255),
    assigned_agent_id INTEGER REFERENCES customer_service_agents(agent_id),
    conversation_type VARCHAR(50) DEFAULT 'sms', -- sms, in_app, email, phone
    subject VARCHAR(500),
    status VARCHAR(50) DEFAULT 'open', -- open, assigned, resolved, closed, escalated
    priority VARCHAR(20) DEFAULT 'normal', -- low, normal, high, urgent
    category VARCHAR(100), -- billing, technical, general, complaint, etc.
    source_channel VARCHAR(50), -- sms, web_widget, email, phone
    
    -- Metrics and tracking
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    first_response_at TIMESTAMP WITH TIME ZONE,
    last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP WITH TIME ZONE,
    closed_at TIMESTAMP WITH TIME ZONE,
    
    -- SLA and metrics
    response_time_sla_minutes INTEGER DEFAULT 60, -- expected first response time
    resolution_time_sla_hours INTEGER DEFAULT 24, -- expected resolution time
    customer_satisfaction_rating INTEGER CHECK (customer_satisfaction_rating >= 1 AND customer_satisfaction_rating <= 5),
    customer_feedback TEXT,
    
    -- Internal tracking
    internal_notes TEXT,
    tags TEXT[], -- searchable tags
    escalation_reason TEXT,
    auto_assigned BOOLEAN DEFAULT false,
    
    -- Language and localization
    language VARCHAR(10) DEFAULT 'en',
    timezone VARCHAR(50) DEFAULT 'America/Montreal',
    
    INDEX(participant_user_id),
    INDEX(assigned_agent_id),
    INDEX(status),
    INDEX(created_at),
    INDEX(last_activity_at),
    INDEX(thread_id)
);

-- Enhanced messages with customer service metadata
CREATE TABLE cs_messages (
    message_id SERIAL PRIMARY KEY,
    conversation_id INTEGER NOT NULL REFERENCES cs_conversations(conversation_id) ON DELETE CASCADE,
    thread_id VARCHAR(255) NOT NULL,
    
    -- Message content
    message_content TEXT NOT NULL,
    message_type VARCHAR(50) DEFAULT 'text', -- text, image, file, system, template
    message_direction VARCHAR(20) NOT NULL, -- inbound, outbound
    
    -- Sender information
    sender_user_id INTEGER REFERENCES user_accounts(user_id),
    sender_agent_id INTEGER REFERENCES customer_service_agents(agent_id),
    sender_phone VARCHAR(20),
    sender_name VARCHAR(255),
    sender_role VARCHAR(50), -- client, tutor, manager, agent, system
    
    -- Delivery and status
    delivery_status VARCHAR(50) DEFAULT 'pending', -- pending, sent, delivered, read, failed
    external_message_id VARCHAR(255), -- ID from SMS/email provider
    channel VARCHAR(50) NOT NULL, -- sms, in_app, email
    
    -- Metadata
    template_id INTEGER, -- if sent from template
    automated BOOLEAN DEFAULT false,
    system_generated BOOLEAN DEFAULT false,
    internal_note BOOLEAN DEFAULT false, -- internal agent notes
    
    -- Timing
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    delivered_at TIMESTAMP WITH TIME ZONE,
    read_at TIMESTAMP WITH TIME ZONE,
    
    -- Content metadata
    attachments JSONB, -- file attachments metadata
    rich_content JSONB, -- buttons, quick replies, etc.
    language VARCHAR(10) DEFAULT 'en',
    
    INDEX(conversation_id),
    INDEX(thread_id),
    INDEX(sent_at),
    INDEX(sender_user_id),
    INDEX(sender_agent_id),
    INDEX(delivery_status)
);

-- Message templates for customer service
CREATE TABLE cs_message_templates (
    template_id SERIAL PRIMARY KEY,
    template_name VARCHAR(255) NOT NULL,
    template_key VARCHAR(255) UNIQUE NOT NULL, -- unique identifier for code
    category VARCHAR(100) NOT NULL, -- greeting, closing, billing, technical, etc.
    
    -- Content
    subject VARCHAR(500), -- for email templates
    message_content TEXT NOT NULL,
    variables JSONB, -- available variables like {customer_name}, {amount}
    
    -- Metadata
    language VARCHAR(10) DEFAULT 'en',
    channel VARCHAR(50), -- sms, email, in_app, all
    department VARCHAR(100) DEFAULT 'general',
    
    -- Usage and approval
    usage_count INTEGER DEFAULT 0,
    last_used_at TIMESTAMP WITH TIME ZONE,
    approved_by INTEGER REFERENCES user_accounts(user_id),
    approved_at TIMESTAMP WITH TIME ZONE,
    status VARCHAR(50) DEFAULT 'draft', -- draft, approved, archived
    
    -- Management
    created_by INTEGER NOT NULL REFERENCES user_accounts(user_id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    version INTEGER DEFAULT 1,
    
    INDEX(category),
    INDEX(language),
    INDEX(status),
    INDEX(template_key)
);

-- Quick responses for common queries
CREATE TABLE cs_quick_responses (
    quick_response_id SERIAL PRIMARY KEY,
    response_text TEXT NOT NULL,
    trigger_keywords TEXT[], -- keywords that suggest this response
    category VARCHAR(100),
    language VARCHAR(10) DEFAULT 'en',
    usage_count INTEGER DEFAULT 0,
    created_by INTEGER NOT NULL REFERENCES user_accounts(user_id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    
    INDEX(category),
    INDEX(language)
);

-- Conversation assignments and transfers
CREATE TABLE cs_conversation_assignments (
    assignment_id SERIAL PRIMARY KEY,
    conversation_id INTEGER NOT NULL REFERENCES cs_conversations(conversation_id),
    agent_id INTEGER NOT NULL REFERENCES customer_service_agents(agent_id),
    assigned_by INTEGER REFERENCES customer_service_agents(agent_id),
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    unassigned_at TIMESTAMP WITH TIME ZONE,
    assignment_reason VARCHAR(255),
    auto_assigned BOOLEAN DEFAULT false,
    
    INDEX(conversation_id),
    INDEX(agent_id),
    INDEX(assigned_at)
);

-- Customer service metrics and analytics
CREATE TABLE cs_conversation_metrics (
    metric_id SERIAL PRIMARY KEY,
    conversation_id INTEGER NOT NULL REFERENCES cs_conversations(conversation_id),
    
    -- Response times (in minutes)
    first_response_time INTEGER,
    average_response_time DECIMAL(10,2),
    total_response_time INTEGER,
    
    -- Resolution metrics (in hours)
    resolution_time DECIMAL(10,2),
    
    -- Message counts
    total_messages INTEGER DEFAULT 0,
    customer_messages INTEGER DEFAULT 0,
    agent_messages INTEGER DEFAULT 0,
    
    -- Satisfaction
    satisfaction_rating INTEGER CHECK (satisfaction_rating >= 1 AND satisfaction_rating <= 5),
    satisfaction_comment TEXT,
    
    -- SLA compliance
    first_response_sla_met BOOLEAN,
    resolution_sla_met BOOLEAN,
    
    -- Timestamps
    calculated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    INDEX(conversation_id),
    INDEX(satisfaction_rating),
    INDEX(first_response_sla_met),
    INDEX(resolution_sla_met)
);

-- Broadcast messages for announcements
CREATE TABLE cs_broadcast_messages (
    broadcast_id SERIAL PRIMARY KEY,
    campaign_name VARCHAR(255) NOT NULL,
    message_content TEXT NOT NULL,
    subject VARCHAR(500), -- for email broadcasts
    
    -- Targeting
    target_audience JSONB NOT NULL, -- criteria for targeting users
    target_user_ids INTEGER[], -- specific user IDs if targeted
    exclude_user_ids INTEGER[], -- users to exclude
    
    -- Channel and delivery
    channels VARCHAR(50)[] NOT NULL, -- sms, email, in_app, push
    
    -- Scheduling
    scheduled_at TIMESTAMP WITH TIME ZONE,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    
    -- Status and metrics
    status VARCHAR(50) DEFAULT 'draft', -- draft, scheduled, sending, completed, failed
    total_recipients INTEGER DEFAULT 0,
    successful_deliveries INTEGER DEFAULT 0,
    failed_deliveries INTEGER DEFAULT 0,
    
    -- Management
    created_by INTEGER NOT NULL REFERENCES user_accounts(user_id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    approved_by INTEGER REFERENCES user_accounts(user_id),
    approved_at TIMESTAMP WITH TIME ZONE,
    
    -- Language and localization
    language VARCHAR(10) DEFAULT 'en',
    timezone VARCHAR(50) DEFAULT 'America/Montreal',
    
    INDEX(status),
    INDEX(scheduled_at),
    INDEX(created_by)
);

-- Broadcast delivery tracking
CREATE TABLE cs_broadcast_deliveries (
    delivery_id SERIAL PRIMARY KEY,
    broadcast_id INTEGER NOT NULL REFERENCES cs_broadcast_messages(broadcast_id) ON DELETE CASCADE,
    user_id INTEGER REFERENCES user_accounts(user_id),
    phone_number VARCHAR(20),
    email_address VARCHAR(255),
    
    channel VARCHAR(50) NOT NULL,
    delivery_status VARCHAR(50) DEFAULT 'pending', -- pending, sent, delivered, failed
    external_message_id VARCHAR(255),
    
    sent_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    
    INDEX(broadcast_id),
    INDEX(user_id),
    INDEX(delivery_status)
);

-- Customer service performance analytics
CREATE TABLE cs_agent_performance (
    performance_id SERIAL PRIMARY KEY,
    agent_id INTEGER NOT NULL REFERENCES customer_service_agents(agent_id),
    date DATE NOT NULL,
    
    -- Volume metrics
    conversations_handled INTEGER DEFAULT 0,
    messages_sent INTEGER DEFAULT 0,
    
    -- Time metrics
    total_active_time INTEGER DEFAULT 0, -- minutes
    average_response_time DECIMAL(10,2), -- minutes
    
    -- Quality metrics
    satisfaction_average DECIMAL(3,2),
    satisfaction_count INTEGER DEFAULT 0,
    escalations_received INTEGER DEFAULT 0,
    
    -- SLA metrics
    first_response_sla_met_count INTEGER DEFAULT 0,
    resolution_sla_met_count INTEGER DEFAULT 0,
    total_sla_conversations INTEGER DEFAULT 0,
    
    -- Efficiency
    conversations_resolved INTEGER DEFAULT 0,
    
    calculated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(agent_id, date),
    INDEX(agent_id),
    INDEX(date)
);

-- Customer service knowledge base (for agent reference)
CREATE TABLE cs_knowledge_base (
    article_id SERIAL PRIMARY KEY,
    title VARCHAR(500) NOT NULL,
    content TEXT NOT NULL,
    category VARCHAR(100) NOT NULL,
    tags TEXT[],
    
    -- Search and discovery
    keywords TEXT[], -- for internal search
    faq_question TEXT, -- if this is an FAQ
    
    -- Metadata
    language VARCHAR(10) DEFAULT 'en',
    visibility VARCHAR(50) DEFAULT 'internal', -- internal, public, client, tutor
    
    -- Management
    created_by INTEGER NOT NULL REFERENCES user_accounts(user_id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    reviewed_by INTEGER REFERENCES user_accounts(user_id),
    reviewed_at TIMESTAMP WITH TIME ZONE,
    
    -- Usage
    view_count INTEGER DEFAULT 0,
    helpful_count INTEGER DEFAULT 0,
    not_helpful_count INTEGER DEFAULT 0,
    
    is_active BOOLEAN DEFAULT true,
    
    INDEX(category),
    INDEX(language),
    INDEX(visibility),
    INDEX(is_active)
);

-- Triggers for updating timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_cs_agents_updated_at BEFORE UPDATE ON customer_service_agents FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_cs_conversations_updated_at BEFORE UPDATE ON cs_conversations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_cs_templates_updated_at BEFORE UPDATE ON cs_message_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_cs_knowledge_updated_at BEFORE UPDATE ON cs_knowledge_base FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Update last_activity_at when new messages are added
CREATE OR REPLACE FUNCTION update_conversation_last_activity()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE cs_conversations 
    SET last_activity_at = NEW.sent_at 
    WHERE conversation_id = NEW.conversation_id;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_conversation_activity AFTER INSERT ON cs_messages FOR EACH ROW EXECUTE FUNCTION update_conversation_last_activity();

-- Create indexes for performance
CREATE INDEX CONCURRENTLY cs_conversations_status_priority_idx ON cs_conversations(status, priority, created_at);
CREATE INDEX CONCURRENTLY cs_messages_thread_sent_idx ON cs_messages(thread_id, sent_at DESC);
CREATE INDEX CONCURRENTLY cs_conversations_agent_status_idx ON cs_conversations(assigned_agent_id, status) WHERE assigned_agent_id IS NOT NULL;
CREATE INDEX CONCURRENTLY cs_conversations_unassigned_idx ON cs_conversations(created_at) WHERE assigned_agent_id IS NULL AND status = 'open';

-- Insert default customer service agent roles
INSERT INTO customer_service_agents (user_id, agent_name, department, specialties, languages)
SELECT 
    ua.user_id,
    COALESCE(cp.first_name || ' ' || cp.last_name, ua.email) as agent_name,
    'general' as department,
    ARRAY['general', 'billing', 'technical'] as specialties,
    ARRAY['en', 'fr'] as languages
FROM user_accounts ua
LEFT JOIN client_profiles cp ON ua.user_id = cp.user_id
WHERE ua.roles @> '["manager"]'::jsonb
ON CONFLICT DO NOTHING;

-- Insert default message templates
INSERT INTO cs_message_templates (template_name, template_key, category, message_content, language, channel, created_by, status) VALUES
-- English templates
('Welcome Greeting', 'welcome_greeting_en', 'greeting', 'Bonjour {customer_name}! Thank you for contacting TutorAide support. How can I help you today?', 'en', 'all', 1, 'approved'),
('Appointment Confirmation', 'appointment_confirm_en', 'appointments', 'Your appointment with {tutor_name} on {date} at {time} has been confirmed. If you need to make changes, please let us know.', 'en', 'sms', 1, 'approved'),
('Payment Reminder', 'payment_reminder_en', 'billing', 'Hi {customer_name}, this is a friendly reminder that your invoice #{invoice_number} for ${amount} is due on {due_date}.', 'en', 'sms', 1, 'approved'),
('Technical Support', 'tech_support_en', 'technical', 'I understand you''re experiencing technical difficulties. Let me help you resolve this issue. Can you describe what''s happening?', 'en', 'all', 1, 'approved'),
('Closing Thank You', 'closing_thanks_en', 'closing', 'Thank you for contacting TutorAide support! If you have any other questions, please don''t hesitate to reach out. Have a great day!', 'en', 'all', 1, 'approved'),

-- French templates  
('Salutation de bienvenue', 'welcome_greeting_fr', 'greeting', 'Bonjour {customer_name}! Merci de contacter le support TutorAide. Comment puis-je vous aider aujourd''hui?', 'fr', 'all', 1, 'approved'),
('Confirmation de rendez-vous', 'appointment_confirm_fr', 'appointments', 'Votre rendez-vous avec {tutor_name} le {date} à {time} a été confirmé. Si vous devez apporter des modifications, veuillez nous en informer.', 'fr', 'sms', 1, 'approved'),
('Rappel de paiement', 'payment_reminder_fr', 'billing', 'Bonjour {customer_name}, ceci est un rappel amical que votre facture #{invoice_number} de {amount}$ est due le {due_date}.', 'fr', 'sms', 1, 'approved'),
('Support technique', 'tech_support_fr', 'technical', 'Je comprends que vous rencontrez des difficultés techniques. Laissez-moi vous aider à résoudre ce problème. Pouvez-vous décrire ce qui se passe?', 'fr', 'all', 1, 'approved'),
('Remerciement de clôture', 'closing_thanks_fr', 'closing', 'Merci d''avoir contacté le support TutorAide! Si vous avez d''autres questions, n''hésitez pas à nous contacter. Bonne journée!', 'fr', 'all', 1, 'approved');

-- Insert quick responses
INSERT INTO cs_quick_responses (response_text, trigger_keywords, category, language, created_by) VALUES
-- English quick responses
('Thank you for your message. Let me look into this for you right away.', ARRAY['help', 'problem', 'issue'], 'general', 'en', 1),
('I understand your concern about billing. Let me check your account details.', ARRAY['billing', 'payment', 'invoice', 'charge'], 'billing', 'en', 1),
('Your appointment has been updated. You should receive a confirmation shortly.', ARRAY['appointment', 'booking', 'schedule'], 'appointments', 'en', 1),
('Let me transfer you to our technical support specialist.', ARRAY['technical', 'login', 'password', 'website'], 'technical', 'en', 1),

-- French quick responses
('Merci pour votre message. Laissez-moi examiner cela pour vous immédiatement.', ARRAY['aide', 'problème', 'question'], 'general', 'fr', 1),
('Je comprends votre préoccupation concernant la facturation. Laissez-moi vérifier les détails de votre compte.', ARRAY['facturation', 'paiement', 'facture', 'frais'], 'billing', 'fr', 1),
('Votre rendez-vous a été mis à jour. Vous devriez recevoir une confirmation sous peu.', ARRAY['rendez-vous', 'réservation', 'horaire'], 'appointments', 'fr', 1),
('Laissez-moi vous transférer vers notre spécialiste du support technique.', ARRAY['technique', 'connexion', 'mot de passe', 'site web'], 'technical', 'fr', 1);

COMMIT;