"""
Repository for automated reminder data access.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from asyncpg import Connection
import json

from app.database.repositories.base import BaseRepository
from app.models.notification_models import NotificationType
from app.core.exceptions import DatabaseOperationError, ResourceNotFoundError
from app.core.timezone import now_est

logger = logging.getLogger(__name__)


class ReminderRepository(BaseRepository):
    """Repository for automated reminder operations."""
    
    def __init__(self):
        """Initialize reminder repository."""
        super().__init__(table_name="automated_reminders", id_column="reminder_id")
    
    async def create_reminder(
        self,
        appointment_id: int,
        user_id: int,
        reminder_type: str,
        scheduled_for: datetime,
        metadata: Optional[Dict[str, Any]] = None,
        conn: Optional[Connection] = None
    ) -> Dict[str, Any]:
        """Create a new reminder."""
        query = """
            INSERT INTO automated_reminders (
                appointment_id, user_id, reminder_type,
                scheduled_for, status, metadata,
                created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            RETURNING *
        """
        
        now = now_est()
        row = await self.fetch_one(
            conn,
            query,
            appointment_id,
            user_id,
            reminder_type,
            scheduled_for,
            'pending',
            json.dumps(metadata) if metadata else None,
            now,
            now
        )
        
        return dict(row) if row else None
    
    async def get_pending_reminders(
        self,
        check_time: Optional[datetime] = None,
        limit: int = 100,
        conn: Optional[Connection] = None
    ) -> List[Dict[str, Any]]:
        """Get pending reminders ready to be sent."""
        if not check_time:
            check_time = now_est()
        
        query = """
            SELECT 
                r.*,
                a.appointment_datetime,
                a.tutor_id,
                a.client_id,
                a.dependant_id,
                t.user_id as tutor_user_id,
                c.user_id as client_user_id
            FROM automated_reminders r
            JOIN appointment_sessions a ON r.appointment_id = a.appointment_id
            LEFT JOIN tutor_profiles t ON a.tutor_id = t.tutor_id
            LEFT JOIN client_profiles c ON a.client_id = c.client_id
            WHERE r.status = 'pending'
            AND r.scheduled_for <= $1
            AND r.deleted_at IS NULL
            AND a.deleted_at IS NULL
            ORDER BY r.scheduled_for
            LIMIT $2
        """
        
        rows = await self.fetch_many(conn, query, check_time, limit)
        return [dict(row) for row in rows]
    
    async def update_reminder_status(
        self,
        reminder_id: int,
        status: str,
        sent_at: Optional[datetime] = None,
        error_message: Optional[str] = None,
        conn: Optional[Connection] = None
    ) -> bool:
        """Update reminder status."""
        update_fields = ["status = $1", "updated_at = $2"]
        values = [status, now_est()]
        param_count = 3
        
        if sent_at:
            update_fields.append(f"sent_at = ${param_count}")
            values.append(sent_at)
            param_count += 1
        
        if error_message:
            update_fields.append(f"error_message = ${param_count}")
            values.append(error_message)
            param_count += 1
        
        values.append(reminder_id)
        
        query = f"""
            UPDATE automated_reminders
            SET {', '.join(update_fields)}
            WHERE reminder_id = ${param_count}
            AND deleted_at IS NULL
            RETURNING reminder_id
        """
        
        result = await self.fetch_one(conn, query, *values)
        return result is not None
    
    async def get_reminders_by_appointment(
        self,
        appointment_id: int,
        conn: Optional[Connection] = None
    ) -> List[Dict[str, Any]]:
        """Get all reminders for an appointment."""
        query = """
            SELECT * FROM automated_reminders
            WHERE appointment_id = $1
            AND deleted_at IS NULL
            ORDER BY scheduled_for
        """
        
        rows = await self.fetch_many(conn, query, appointment_id)
        return [dict(row) for row in rows]
    
    async def cancel_reminders(
        self,
        appointment_id: int,
        conn: Optional[Connection] = None
    ) -> int:
        """Cancel all pending reminders for an appointment."""
        query = """
            UPDATE automated_reminders
            SET status = 'cancelled', updated_at = $1
            WHERE appointment_id = $2
            AND status = 'pending'
            AND deleted_at IS NULL
            RETURNING reminder_id
        """
        
        rows = await self.fetch_many(conn, query, now_est(), appointment_id)
        return len(rows)
    
    async def get_reminder_statistics(
        self,
        date_range: Optional[Tuple[datetime, datetime]] = None,
        conn: Optional[Connection] = None
    ) -> Dict[str, Any]:
        """Get reminder statistics."""
        base_query = """
            SELECT 
                status,
                reminder_type,
                COUNT(*) as count,
                MIN(scheduled_for) as earliest,
                MAX(scheduled_for) as latest
            FROM automated_reminders
            WHERE deleted_at IS NULL
        """
        
        if date_range:
            base_query += " AND scheduled_for BETWEEN $1 AND $2"
            params = list(date_range)
        else:
            params = []
        
        base_query += " GROUP BY status, reminder_type"
        
        rows = await self.fetch_many(conn, base_query, *params)
        
        # Aggregate statistics
        stats = {
            'total': 0,
            'by_status': {},
            'by_type': {},
            'pending': 0,
            'sent': 0,
            'failed': 0,
            'cancelled': 0
        }
        
        for row in rows:
            count = row['count']
            status = row['status']
            reminder_type = row['reminder_type']
            
            stats['total'] += count
            
            if status not in stats['by_status']:
                stats['by_status'][status] = 0
            stats['by_status'][status] += count
            
            if reminder_type not in stats['by_type']:
                stats['by_type'][reminder_type] = 0
            stats['by_type'][reminder_type] += count
            
            # Update specific status counters
            if status == 'pending':
                stats['pending'] += count
            elif status == 'sent':
                stats['sent'] += count
            elif status == 'failed':
                stats['failed'] += count
            elif status == 'cancelled':
                stats['cancelled'] += count
        
        # Calculate rates
        if stats['total'] > 0:
            stats['success_rate'] = (stats['sent'] / stats['total']) * 100
            stats['failure_rate'] = (stats['failed'] / stats['total']) * 100
        else:
            stats['success_rate'] = 0.0
            stats['failure_rate'] = 0.0
        
        return stats
    
    async def cleanup_old_reminders(
        self,
        days_to_keep: int = 90,
        conn: Optional[Connection] = None
    ) -> int:
        """Clean up old sent/cancelled reminders."""
        cutoff_date = now_est() - timedelta(days=days_to_keep)
        
        query = """
            UPDATE automated_reminders
            SET deleted_at = $1
            WHERE (status IN ('sent', 'cancelled', 'failed'))
            AND created_at < $2
            AND deleted_at IS NULL
            RETURNING reminder_id
        """
        
        rows = await self.fetch_many(conn, query, now_est(), cutoff_date)
        return len(rows)
    
    async def check_duplicate_reminder(
        self,
        appointment_id: int,
        reminder_type: str,
        user_id: int,
        conn: Optional[Connection] = None
    ) -> bool:
        """Check if a reminder already exists."""
        query = """
            SELECT COUNT(*) as count
            FROM automated_reminders
            WHERE appointment_id = $1
            AND reminder_type = $2
            AND user_id = $3
            AND status = 'pending'
            AND deleted_at IS NULL
        """
        
        row = await self.fetch_one(
            conn, query, appointment_id, reminder_type, user_id
        )
        
        return row['count'] > 0 if row else False