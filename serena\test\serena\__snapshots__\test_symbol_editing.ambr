# serializer version: 1
# name: test_delete_symbol[test_case0]
  '''
  """
  Test module for variable declarations and usage.
  
  This module tests various types of variable declarations and usages including:
  - Module-level variables
  - Class-level variables
  - Instance variables
  - Variable reassignments
  """
  
  from dataclasses import dataclass, field
  
  # Module-level variables
  module_var = "Initial module value"
  
  reassignable_module_var = 10
  reassignable_module_var = 20  # Reassigned
  
  # Module-level variable with type annotation
  typed_module_var: int = 42
  
  
  # Regular class with class and instance variables
  
  
  
  # Dataclass with variables
  @dataclass
  class VariableDataclass:
      """Dataclass that contains various fields."""
  
      # Field variables with type annotations
      id: int
      name: str
      items: list[str] = field(default_factory=list)
      metadata: dict[str, str] = field(default_factory=dict)
      optional_value: float | None = None
  
      # This will be reassigned in various places
      status: str = "pending"
  
  
  # Function that uses the module variables
  def use_module_variables():
      """Function that uses module-level variables."""
      result = module_var + " used in function"
      other_result = reassignable_module_var * 2
      return result, other_result
  
  
  # Create instances and use variables
  dataclass_instance = VariableDataclass(id=1, name="Test")
  dataclass_instance.status = "active"  # Reassign dataclass field
  
  # Use variables at module level
  module_result = module_var + " used at module level"
  other_module_result = reassignable_module_var + 30
  
  # Create a second dataclass instance with different status
  second_dataclass = VariableDataclass(id=2, name="Another Test")
  second_dataclass.status = "completed"  # Another reassignment of status
  
  '''
# ---
# name: test_delete_symbol[test_case1]
  '''
  
  
  export function helperFunction() {
      const demo = new DemoClass(42);
      demo.printValue();
  }
  
  helperFunction();
  
  '''
# ---
# name: test_insert_in_rel_to_symbol[test_case0-after]
  '''
  """
  Test module for variable declarations and usage.
  
  This module tests various types of variable declarations and usages including:
  - Module-level variables
  - Class-level variables
  - Instance variables
  - Variable reassignments
  """
  
  from dataclasses import dataclass, field
  
  # Module-level variables
  module_var = "Initial module value"
  
  reassignable_module_var = 10
  reassignable_module_var = 20  # Reassigned
  
  # Module-level variable with type annotation
  typed_module_var: int = 42
  
  new_module_var = "Inserted after typed_module_var"
  
  # Regular class with class and instance variables
  class VariableContainer:
      """Class that contains various variables."""
  
      # Class-level variables
      class_var = "Initial class value"
  
      reassignable_class_var = True
      reassignable_class_var = False  # Reassigned #noqa: PIE794
  
      # Class-level variable with type annotation
      typed_class_var: str = "typed value"
  
      def __init__(self):
          # Instance variables
          self.instance_var = "Initial instance value"
          self.reassignable_instance_var = 100
  
          # Instance variable with type annotation
          self.typed_instance_var: list[str] = ["item1", "item2"]
  
      def modify_instance_var(self):
          # Reassign instance variable
          self.instance_var = "Modified instance value"
          self.reassignable_instance_var = 200  # Reassigned
  
      def use_module_var(self):
          # Use module-level variables
          result = module_var + " used in method"
          other_result = reassignable_module_var + 5
          return result, other_result
  
      def use_class_var(self):
          # Use class-level variables
          result = VariableContainer.class_var + " used in method"
          other_result = VariableContainer.reassignable_class_var
          return result, other_result
  
  
  # Dataclass with variables
  @dataclass
  class VariableDataclass:
      """Dataclass that contains various fields."""
  
      # Field variables with type annotations
      id: int
      name: str
      items: list[str] = field(default_factory=list)
      metadata: dict[str, str] = field(default_factory=dict)
      optional_value: float | None = None
  
      # This will be reassigned in various places
      status: str = "pending"
  
  
  # Function that uses the module variables
  def use_module_variables():
      """Function that uses module-level variables."""
      result = module_var + " used in function"
      other_result = reassignable_module_var * 2
      return result, other_result
  
  
  # Create instances and use variables
  dataclass_instance = VariableDataclass(id=1, name="Test")
  dataclass_instance.status = "active"  # Reassign dataclass field
  
  # Use variables at module level
  module_result = module_var + " used at module level"
  other_module_result = reassignable_module_var + 30
  
  # Create a second dataclass instance with different status
  second_dataclass = VariableDataclass(id=2, name="Another Test")
  second_dataclass.status = "completed"  # Another reassignment of status
  
  '''
# ---
# name: test_insert_in_rel_to_symbol[test_case0-before]
  '''
  """
  Test module for variable declarations and usage.
  
  This module tests various types of variable declarations and usages including:
  - Module-level variables
  - Class-level variables
  - Instance variables
  - Variable reassignments
  """
  
  from dataclasses import dataclass, field
  
  # Module-level variables
  module_var = "Initial module value"
  
  reassignable_module_var = 10
  reassignable_module_var = 20  # Reassigned
  
  new_module_var = "Inserted after typed_module_var"
  # Module-level variable with type annotation
  typed_module_var: int = 42
  
  
  # Regular class with class and instance variables
  class VariableContainer:
      """Class that contains various variables."""
  
      # Class-level variables
      class_var = "Initial class value"
  
      reassignable_class_var = True
      reassignable_class_var = False  # Reassigned #noqa: PIE794
  
      # Class-level variable with type annotation
      typed_class_var: str = "typed value"
  
      def __init__(self):
          # Instance variables
          self.instance_var = "Initial instance value"
          self.reassignable_instance_var = 100
  
          # Instance variable with type annotation
          self.typed_instance_var: list[str] = ["item1", "item2"]
  
      def modify_instance_var(self):
          # Reassign instance variable
          self.instance_var = "Modified instance value"
          self.reassignable_instance_var = 200  # Reassigned
  
      def use_module_var(self):
          # Use module-level variables
          result = module_var + " used in method"
          other_result = reassignable_module_var + 5
          return result, other_result
  
      def use_class_var(self):
          # Use class-level variables
          result = VariableContainer.class_var + " used in method"
          other_result = VariableContainer.reassignable_class_var
          return result, other_result
  
  
  # Dataclass with variables
  @dataclass
  class VariableDataclass:
      """Dataclass that contains various fields."""
  
      # Field variables with type annotations
      id: int
      name: str
      items: list[str] = field(default_factory=list)
      metadata: dict[str, str] = field(default_factory=dict)
      optional_value: float | None = None
  
      # This will be reassigned in various places
      status: str = "pending"
  
  
  # Function that uses the module variables
  def use_module_variables():
      """Function that uses module-level variables."""
      result = module_var + " used in function"
      other_result = reassignable_module_var * 2
      return result, other_result
  
  
  # Create instances and use variables
  dataclass_instance = VariableDataclass(id=1, name="Test")
  dataclass_instance.status = "active"  # Reassign dataclass field
  
  # Use variables at module level
  module_result = module_var + " used at module level"
  other_module_result = reassignable_module_var + 30
  
  # Create a second dataclass instance with different status
  second_dataclass = VariableDataclass(id=2, name="Another Test")
  second_dataclass.status = "completed"  # Another reassignment of status
  
  '''
# ---
# name: test_insert_in_rel_to_symbol[test_case1-after]
  '''
  """
  Test module for variable declarations and usage.
  
  This module tests various types of variable declarations and usages including:
  - Module-level variables
  - Class-level variables
  - Instance variables
  - Variable reassignments
  """
  
  from dataclasses import dataclass, field
  
  # Module-level variables
  module_var = "Initial module value"
  
  reassignable_module_var = 10
  reassignable_module_var = 20  # Reassigned
  
  # Module-level variable with type annotation
  typed_module_var: int = 42
  
  
  # Regular class with class and instance variables
  class VariableContainer:
      """Class that contains various variables."""
  
      # Class-level variables
      class_var = "Initial class value"
  
      reassignable_class_var = True
      reassignable_class_var = False  # Reassigned #noqa: PIE794
  
      # Class-level variable with type annotation
      typed_class_var: str = "typed value"
  
      def __init__(self):
          # Instance variables
          self.instance_var = "Initial instance value"
          self.reassignable_instance_var = 100
  
          # Instance variable with type annotation
          self.typed_instance_var: list[str] = ["item1", "item2"]
  
      def modify_instance_var(self):
          # Reassign instance variable
          self.instance_var = "Modified instance value"
          self.reassignable_instance_var = 200  # Reassigned
  
      def use_module_var(self):
          # Use module-level variables
          result = module_var + " used in method"
          other_result = reassignable_module_var + 5
          return result, other_result
  
      def use_class_var(self):
          # Use class-level variables
          result = VariableContainer.class_var + " used in method"
          other_result = VariableContainer.reassignable_class_var
          return result, other_result
  
  
  # Dataclass with variables
  @dataclass
  class VariableDataclass:
      """Dataclass that contains various fields."""
  
      # Field variables with type annotations
      id: int
      name: str
      items: list[str] = field(default_factory=list)
      metadata: dict[str, str] = field(default_factory=dict)
      optional_value: float | None = None
  
      # This will be reassigned in various places
      status: str = "pending"
  
  
  # Function that uses the module variables
  def use_module_variables():
      """Function that uses module-level variables."""
      result = module_var + " used in function"
      other_result = reassignable_module_var * 2
      return result, other_result
  
  def new_inserted_function():
      print("This is a new function inserted before another.")
  
  # Create instances and use variables
  dataclass_instance = VariableDataclass(id=1, name="Test")
  dataclass_instance.status = "active"  # Reassign dataclass field
  
  # Use variables at module level
  module_result = module_var + " used at module level"
  other_module_result = reassignable_module_var + 30
  
  # Create a second dataclass instance with different status
  second_dataclass = VariableDataclass(id=2, name="Another Test")
  second_dataclass.status = "completed"  # Another reassignment of status
  
  '''
# ---
# name: test_insert_in_rel_to_symbol[test_case1-before]
  '''
  """
  Test module for variable declarations and usage.
  
  This module tests various types of variable declarations and usages including:
  - Module-level variables
  - Class-level variables
  - Instance variables
  - Variable reassignments
  """
  
  from dataclasses import dataclass, field
  
  # Module-level variables
  module_var = "Initial module value"
  
  reassignable_module_var = 10
  reassignable_module_var = 20  # Reassigned
  
  # Module-level variable with type annotation
  typed_module_var: int = 42
  
  
  # Regular class with class and instance variables
  class VariableContainer:
      """Class that contains various variables."""
  
      # Class-level variables
      class_var = "Initial class value"
  
      reassignable_class_var = True
      reassignable_class_var = False  # Reassigned #noqa: PIE794
  
      # Class-level variable with type annotation
      typed_class_var: str = "typed value"
  
      def __init__(self):
          # Instance variables
          self.instance_var = "Initial instance value"
          self.reassignable_instance_var = 100
  
          # Instance variable with type annotation
          self.typed_instance_var: list[str] = ["item1", "item2"]
  
      def modify_instance_var(self):
          # Reassign instance variable
          self.instance_var = "Modified instance value"
          self.reassignable_instance_var = 200  # Reassigned
  
      def use_module_var(self):
          # Use module-level variables
          result = module_var + " used in method"
          other_result = reassignable_module_var + 5
          return result, other_result
  
      def use_class_var(self):
          # Use class-level variables
          result = VariableContainer.class_var + " used in method"
          other_result = VariableContainer.reassignable_class_var
          return result, other_result
  
  
  # Dataclass with variables
  @dataclass
  class VariableDataclass:
      """Dataclass that contains various fields."""
  
      # Field variables with type annotations
      id: int
      name: str
      items: list[str] = field(default_factory=list)
      metadata: dict[str, str] = field(default_factory=dict)
      optional_value: float | None = None
  
      # This will be reassigned in various places
      status: str = "pending"
  
  
  def new_inserted_function():
      print("This is a new function inserted before another.")
  # Function that uses the module variables
  def use_module_variables():
      """Function that uses module-level variables."""
      result = module_var + " used in function"
      other_result = reassignable_module_var * 2
      return result, other_result
  
  
  # Create instances and use variables
  dataclass_instance = VariableDataclass(id=1, name="Test")
  dataclass_instance.status = "active"  # Reassign dataclass field
  
  # Use variables at module level
  module_result = module_var + " used at module level"
  other_module_result = reassignable_module_var + 30
  
  # Create a second dataclass instance with different status
  second_dataclass = VariableDataclass(id=2, name="Another Test")
  second_dataclass.status = "completed"  # Another reassignment of status
  
  '''
# ---
# name: test_insert_in_rel_to_symbol[test_case2-after]
  '''
  export class DemoClass {
      value: number;
      constructor(value: number) {
          this.value = value;
      }
      printValue() {
          console.log(this.value);
      }
  }
  
  function newFunctionAfterClass(): void {
      console.log("This function is after DemoClass.");
  }
  export function helperFunction() {
      const demo = new DemoClass(42);
      demo.printValue();
  }
  
  helperFunction();
  
  '''
# ---
# name: test_insert_in_rel_to_symbol[test_case2-before]
  '''
  function newFunctionAfterClass(): void {
      console.log("This function is after DemoClass.");
  }
  export class DemoClass {
      value: number;
      constructor(value: number) {
          this.value = value;
      }
      printValue() {
          console.log(this.value);
      }
  }
  
  export function helperFunction() {
      const demo = new DemoClass(42);
      demo.printValue();
  }
  
  helperFunction();
  
  '''
# ---
# name: test_insert_in_rel_to_symbol[test_case3-after]
  '''
  export class DemoClass {
      value: number;
      constructor(value: number) {
          this.value = value;
      }
      printValue() {
          console.log(this.value);
      }
  }
  
  export function helperFunction() {
      const demo = new DemoClass(42);
      demo.printValue();
  }
  
  function newInsertedFunction(): void {
      console.log("This is a new function inserted before another.");
  }
  helperFunction();
  
  '''
# ---
# name: test_insert_in_rel_to_symbol[test_case3-before]
  '''
  export class DemoClass {
      value: number;
      constructor(value: number) {
          this.value = value;
      }
      printValue() {
          console.log(this.value);
      }
  }
  function newInsertedFunction(): void {
      console.log("This is a new function inserted before another.");
  }
  
  export function helperFunction() {
      const demo = new DemoClass(42);
      demo.printValue();
  }
  
  helperFunction();
  
  '''
# ---
# name: test_replace_body[test_case0]
  '''
  """
  Test module for variable declarations and usage.
  
  This module tests various types of variable declarations and usages including:
  - Module-level variables
  - Class-level variables
  - Instance variables
  - Variable reassignments
  """
  
  from dataclasses import dataclass, field
  
  # Module-level variables
  module_var = "Initial module value"
  
  reassignable_module_var = 10
  reassignable_module_var = 20  # Reassigned
  
  # Module-level variable with type annotation
  typed_module_var: int = 42
  
  
  # Regular class with class and instance variables
  class VariableContainer:
      """Class that contains various variables."""
  
      # Class-level variables
      class_var = "Initial class value"
  
      reassignable_class_var = True
      reassignable_class_var = False  # Reassigned #noqa: PIE794
  
      # Class-level variable with type annotation
      typed_class_var: str = "typed value"
  
      def __init__(self):
          # Instance variables
          self.instance_var = "Initial instance value"
          self.reassignable_instance_var = 100
  
          # Instance variable with type annotation
          self.typed_instance_var: list[str] = ["item1", "item2"]
  
              
      def modify_instance_var(self):
          # This body has been replaced
          self.instance_var = "Replaced!"
          self.reassignable_instance_var = 999
    # Reassigned
  
      def use_module_var(self):
          # Use module-level variables
          result = module_var + " used in method"
          other_result = reassignable_module_var + 5
          return result, other_result
  
      def use_class_var(self):
          # Use class-level variables
          result = VariableContainer.class_var + " used in method"
          other_result = VariableContainer.reassignable_class_var
          return result, other_result
  
  
  # Dataclass with variables
  @dataclass
  class VariableDataclass:
      """Dataclass that contains various fields."""
  
      # Field variables with type annotations
      id: int
      name: str
      items: list[str] = field(default_factory=list)
      metadata: dict[str, str] = field(default_factory=dict)
      optional_value: float | None = None
  
      # This will be reassigned in various places
      status: str = "pending"
  
  
  # Function that uses the module variables
  def use_module_variables():
      """Function that uses module-level variables."""
      result = module_var + " used in function"
      other_result = reassignable_module_var * 2
      return result, other_result
  
  
  # Create instances and use variables
  dataclass_instance = VariableDataclass(id=1, name="Test")
  dataclass_instance.status = "active"  # Reassign dataclass field
  
  # Use variables at module level
  module_result = module_var + " used at module level"
  other_module_result = reassignable_module_var + 30
  
  # Create a second dataclass instance with different status
  second_dataclass = VariableDataclass(id=2, name="Another Test")
  second_dataclass.status = "completed"  # Another reassignment of status
  
  '''
# ---
# name: test_replace_body[test_case1]
  '''
  export class DemoClass {
      value: number;
      constructor(value: number) {
          this.value = value;
      }
      
      function printValue() {
          // This body has been replaced
          console.warn("New value: " + this.value);
      }
  
  }
  
  export function helperFunction() {
      const demo = new DemoClass(42);
      demo.printValue();
  }
  
  helperFunction();
  
  '''
# ---
