"""
Tutor payment service for managing weekly payments.
"""

import asyncpg
from typing import List, Optional, Dict, Any, Tuple
from datetime import date, datetime, timedelta
from decimal import Decimal
import logging

from app.models.payment_models import (
    TutorPayment, PaymentBatch, PaymentFilter, WeeklyPaymentReport,
    PaymentStatus, BatchStatus, ScheduledTaskResult
)
from app.database.repositories.payment_repository import PaymentRepository
from app.services.stripe_service import StripeService
from app.services.notification_service import NotificationService
from app.services.email_service import EmailService
from app.core.exceptions import BusinessLogicError, ValidationError


logger = logging.getLogger(__name__)


class TutorPaymentService:
    """Service for managing tutor payments."""
    
    def __init__(
        self,
        payment_repo: PaymentRepository,
        stripe_service: StripeService,
        notification_service: NotificationService,
        email_service: EmailService
    ):
        self.payment_repo = payment_repo
        self.stripe_service = stripe_service
        self.notification_service = notification_service
        self.email_service = email_service
    
    def _get_payment_week_dates(self, reference_date: date) -> <PERSON><PERSON>[date, date]:
        """
        Calculate Thursday-Wednesday payment week dates.
        Returns (week_start_thursday, week_end_wednesday)
        """
        # Find the Thursday of the week containing reference_date
        days_since_thursday = (reference_date.weekday() - 3) % 7
        week_start = reference_date - timedelta(days=days_since_thursday)
        week_end = week_start + timedelta(days=6)
        
        return week_start, week_end
    
    async def calculate_weekly_payments(
        self,
        db: asyncpg.Connection,
        week_date: Optional[date] = None
    ) -> WeeklyPaymentReport:
        """
        Calculate weekly tutor payments (Thursday-Wednesday cycle).
        This is the main method called by the weekly cron job.
        """
        # Default to current week if no date provided
        if not week_date:
            week_date = date.today()
        
        week_start, week_end = self._get_payment_week_dates(week_date)
        
        logger.info(f"Calculating weekly payments for {week_start} to {week_end}")
        
        # Create task log
        task_id = await self.payment_repo.create_scheduled_task_log(
            db,
            task_name="weekly_payment_calculation",
            task_type="payment",
            parameters={
                "week_start": week_start.isoformat(),
                "week_end": week_end.isoformat()
            }
        )
        
        try:
            # Check if payments already exist for this week
            existing_payments = await self.payment_repo.find_payments(
                db,
                PaymentFilter(
                    week_start=week_start,
                    week_end=week_end,
                    limit=1
                )
            )
            
            if existing_payments:
                logger.info(f"Payments already calculated for week {week_start} to {week_end}")
                # Return existing report
                return await self.payment_repo.get_weekly_payment_report(
                    db, week_start, week_end
                )
            
            # Get unbilled appointments for the week
            unbilled = await self.payment_repo.get_unpaid_appointments_for_week(
                db, week_start, week_end
            )
            
            logger.info(f"Found {len(unbilled)} unbilled appointments")
            
            # Group appointments by tutor
            tutor_appointments: Dict[int, List[Dict[str, Any]]] = {}
            for appointment in unbilled:
                tutor_id = appointment["tutor_id"]
                if tutor_id not in tutor_appointments:
                    tutor_appointments[tutor_id] = []
                tutor_appointments[tutor_id].append(appointment)
            
            # Create payments for each tutor
            created_payments = []
            for tutor_id, appointments in tutor_appointments.items():
                for appointment in appointments:
                    payment = await self._create_payment_for_appointment(
                        appointment, week_start, week_end
                    )
                    
                    created_payment = await self.payment_repo.create_payment(db, payment)
                    created_payments.append(created_payment)
            
            logger.info(f"Created {len(created_payments)} tutor payments")
            
            # Create payment batch if there are payments
            batch = None
            if created_payments:
                batch = await self._create_payment_batch(
                    db, created_payments, week_start, week_end
                )
                
                # Send manager notification
                await self._send_weekly_payment_notification(batch)
            
            # Update task log with success
            await self.payment_repo.update_scheduled_task_log(
                db,
                task_id=task_id,
                status="completed",
                records_processed=len(unbilled),
                records_created=len(created_payments)
            )
            
            # Generate and return report
            report = await self.payment_repo.get_weekly_payment_report(
                db, week_start, week_end
            )
            
            logger.info(
                f"Weekly payment calculation completed: "
                f"{len(created_payments)} payments created for "
                f"{len(tutor_appointments)} tutors"
            )
            
            return report
            
        except Exception as e:
            # Update task log with failure
            await self.payment_repo.update_scheduled_task_log(
                db,
                task_id=task_id,
                status="failed",
                error_message=str(e),
                error_details={"type": type(e).__name__}
            )
            raise BusinessLogicError(f"Weekly payment calculation failed: {str(e)}")
    
    async def _create_payment_for_appointment(
        self,
        appointment: Dict[str, Any],
        week_start: date,
        week_end: date
    ) -> TutorPayment:
        """Create a payment record for a completed appointment."""
        # Calculate duration in hours
        duration_hours = Decimal(appointment["duration_minutes"]) / 60
        
        # Get base tutor rate
        base_rate = appointment["base_rate"]
        
        # Calculate service amount (including surge pricing if applicable)
        surge_multiplier = Decimal(str(appointment.get("surge_multiplier", 1.0)))
        service_amount = base_rate * duration_hours * surge_multiplier
        
        # Transport fee
        transport_fee = appointment.get("transport_fee", 0) or 0
        
        # TODO: Calculate bonus amount based on performance metrics
        # For now, set to 0 but this could include:
        # - Client satisfaction bonuses
        # - Perfect attendance bonuses
        # - Holiday bonuses
        bonus_amount = Decimal("0.00")
        
        payment = TutorPayment(
            tutor_id=appointment["tutor_id"],
            appointment_id=appointment["appointment_id"],
            service_amount=service_amount,
            transport_amount=Decimal(str(transport_fee)),
            bonus_amount=bonus_amount,
            payment_week_start=week_start,
            payment_week_end=week_end,
            status=PaymentStatus.PENDING
        )
        
        # Total is calculated automatically by the model
        return payment
    
    async def _create_payment_batch(
        self,
        db: asyncpg.Connection,
        payments: List[TutorPayment],
        week_start: date,
        week_end: date
    ) -> PaymentBatch:
        """Create a payment batch for manager approval."""
        batch = PaymentBatch(
            week_start=week_start,
            week_end=week_end,
            status=BatchStatus.PENDING
        )
        
        # Create batch in database
        created_batch = await self.payment_repo.create_payment_batch(db, batch)
        
        # Assign payments to batch
        payment_ids = [p.payment_id for p in payments if p.payment_id]
        if payment_ids:
            assigned_count = await self.payment_repo.assign_payments_to_batch(
                db, payment_ids, created_batch.batch_id
            )
            
            logger.info(f"Assigned {assigned_count} payments to batch {created_batch.batch_number}")
        
        return created_batch
    
    async def _send_weekly_payment_notification(self, batch: PaymentBatch) -> None:
        """Send notification to managers about new payment batch."""
        try:
            await self.email_service.send_weekly_payment_notification(
                batch_id=batch.batch_id,
                batch_number=batch.batch_number,
                week_start=batch.week_start,
                week_end=batch.week_end,
                total_amount=batch.total_amount,
                total_payments=batch.total_payments
            )
            
            # TODO: Send push notification to managers
            # await self.notification_service.send_manager_notification(...)
            
        except Exception as e:
            logger.error(f"Failed to send weekly payment notification: {str(e)}")
    
    async def get_weekly_payment_report(
        self,
        db: asyncpg.Connection,
        week_date: Optional[date] = None
    ) -> WeeklyPaymentReport:
        """Get weekly payment report for managers."""
        if not week_date:
            week_date = date.today()
        
        week_start, week_end = self._get_payment_week_dates(week_date)
        
        return await self.payment_repo.get_weekly_payment_report(
            db, week_start, week_end
        )
    
    async def approve_payment_batch(
        self,
        db: asyncpg.Connection,
        batch_id: int,
        manager_id: int,
        notes: Optional[str] = None
    ) -> PaymentBatch:
        """Approve a payment batch for processing."""
        batch = await self.payment_repo.get_batch_by_id(db, batch_id)
        if not batch:
            raise ValidationError(f"Payment batch {batch_id} not found")
        
        if batch.status != BatchStatus.PENDING:
            raise ValidationError(f"Batch {batch_id} is not in pending status")
        
        # Update batch status
        success = await self.payment_repo.update_batch_status(
            db,
            batch_id=batch_id,
            status=BatchStatus.APPROVED,
            approved_by=manager_id
        )
        
        if not success:
            raise BusinessLogicError(f"Failed to approve batch {batch_id}")
        
        # Get updated batch
        updated_batch = await self.payment_repo.get_batch_by_id(db, batch_id)
        
        # Send confirmation email
        await self.email_service.send_batch_approval_confirmation(
            batch_id=batch_id,
            batch_number=updated_batch.batch_number,
            manager_id=manager_id,
            total_amount=updated_batch.total_amount,
            notes=notes
        )
        
        logger.info(f"Payment batch {batch_id} approved by manager {manager_id}")
        
        return updated_batch
    
    async def process_payment_batch(
        self,
        db: asyncpg.Connection,
        batch_id: int
    ) -> Dict[str, Any]:
        """Process an approved payment batch through Stripe."""
        batch = await self.payment_repo.get_batch_by_id(db, batch_id)
        if not batch:
            raise ValidationError(f"Payment batch {batch_id} not found")
        
        if batch.status != BatchStatus.APPROVED:
            raise ValidationError(f"Batch {batch_id} is not approved")
        
        logger.info(f"Processing payment batch {batch_id} with {len(batch.payments)} payments")
        
        # Update batch status to processing
        await self.payment_repo.update_batch_status(
            db, batch_id, BatchStatus.PROCESSING
        )
        
        processed_count = 0
        failed_count = 0
        failed_payments = []
        
        for payment in batch.payments:
            try:
                # Process individual payment
                success = await self._process_individual_payment(db, payment)
                if success:
                    processed_count += 1
                else:
                    failed_count += 1
                    failed_payments.append(payment.payment_id)
                    
            except Exception as e:
                logger.error(f"Failed to process payment {payment.payment_id}: {str(e)}")
                failed_count += 1
                failed_payments.append(payment.payment_id)
        
        # Update batch with results
        await self.payment_repo.update_batch_status(
            db,
            batch_id=batch_id,
            status=BatchStatus.COMPLETED,
            processed_count=processed_count,
            failed_count=failed_count
        )
        
        result = {
            "batch_id": batch_id,
            "total_payments": len(batch.payments),
            "processed_count": processed_count,
            "failed_count": failed_count,
            "failed_payment_ids": failed_payments,
            "success_rate": (processed_count / len(batch.payments)) * 100 if batch.payments else 0
        }
        
        # Send completion notification
        await self.email_service.send_batch_processing_results(
            batch_id=batch_id,
            batch_number=batch.batch_number,
            results=result
        )
        
        logger.info(
            f"Batch {batch_id} processing completed: "
            f"{processed_count} succeeded, {failed_count} failed"
        )
        
        return result
    
    async def _process_individual_payment(
        self,
        db: asyncpg.Connection,
        payment: TutorPayment
    ) -> bool:
        """Process an individual tutor payment through Stripe."""
        try:
            # TODO: Get tutor bank account details
            # For now, simulate successful processing
            # In real implementation, this would:
            # 1. Get tutor's bank account info
            # 2. Create Stripe payout
            # 3. Handle payout response
            
            # Simulate Stripe payout creation
            payout_id = f"po_test_{payment.payment_id}_{datetime.now().timestamp()}"
            
            # Update payment status
            success = await self.payment_repo.update_payment_status(
                db,
                payment_id=payment.payment_id,
                status=PaymentStatus.PAID,
                stripe_payout_id=payout_id,
                stripe_payout_status="pending"
            )
            
            if success:
                # Send payment confirmation to tutor
                await self.notification_service.send_payment_confirmation_to_tutor(
                    tutor_id=payment.tutor_id,
                    amount=payment.total_amount,
                    week_start=payment.payment_week_start,
                    week_end=payment.payment_week_end
                )
                
                logger.info(f"Payment {payment.payment_id} processed successfully")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to process payment {payment.payment_id}: {str(e)}")
            
            # Update payment status to failed
            await self.payment_repo.update_payment_status(
                db,
                payment_id=payment.payment_id,
                status=PaymentStatus.FAILED
            )
            
            return False
    
    async def get_tutor_payment_history(
        self,
        db: asyncpg.Connection,
        tutor_id: int,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """Get payment history for a tutor."""
        return await self.payment_repo.get_tutor_payment_history(
            db, tutor_id, limit
        )
    
    async def get_payment_stats(
        self,
        db: asyncpg.Connection,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> Dict[str, Any]:
        """Get payment statistics for a period."""
        if not start_date:
            start_date = date.today() - timedelta(days=30)
        if not end_date:
            end_date = date.today()
        
        return await self.payment_repo.get_payment_stats_for_period(
            db, start_date, end_date
        )
    
    async def find_payments(
        self,
        db: asyncpg.Connection,
        filter_params: PaymentFilter
    ) -> List[TutorPayment]:
        """Find payments with filtering."""
        return await self.payment_repo.find_payments(db, filter_params)
    
    async def get_payment(
        self,
        db: asyncpg.Connection,
        payment_id: int
    ) -> Optional[TutorPayment]:
        """Get payment by ID."""
        return await self.payment_repo.get_payment_by_id(db, payment_id)
    
    async def get_batch(
        self,
        db: asyncpg.Connection,
        batch_id: int
    ) -> Optional[PaymentBatch]:
        """Get payment batch by ID."""
        return await self.payment_repo.get_batch_by_id(db, batch_id)

