import React from 'react';
import { useTranslation } from 'react-i18next';

const DependantsPage: React.FC = () => {
  const { t } = useTranslation();
  
  return (
    <div className="p-6">
      <h1 className="text-2xl font-semibold text-text-primary">{t('dependants.title')}</h1>
      <p className="text-text-secondary mt-2">Dependant management interface coming soon...</p>
    </div>
  );
};

export default DependantsPage;