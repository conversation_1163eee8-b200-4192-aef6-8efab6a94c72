import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Users, Calendar, DollarSign, TrendingUp, Clock, CheckCircle,
  AlertCircle, ArrowUp, ArrowDown, Activity, BookOpen, Star,
  MessageSquare, CreditCard, MapPin, Award, BarChart2, <PERSON><PERSON><PERSON>,
  RefreshCw
} from 'lucide-react';
import { Card } from '../../components/common/Card';
import Button from '../../components/common/Button';
import { Badge } from '../../components/common/Badge';
import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON><PERSON>, Line, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>hart as <PERSON><PERSON><PERSON><PERSON>hart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import toast from 'react-hot-toast';
import { useApi } from '../../hooks/useApi';

interface DashboardMetrics {
  totalUsers: number;
  activeClients: number;
  activeTutors: number;
  pendingApplications: number;
  totalRevenue: number;
  monthlyRevenue: number;
  pendingPayments: number;
  activeSessions: number;
  completedSessions: number;
  upcomingSessions: number;
  averageRating: number;
  newUsersThisMonth: number;
  revenueChange?: number;
  userGrowthChange?: number;
  sessionChange?: number;
  ratingChange?: number;
}

interface RevenueData {
  month: string;
  revenue: number;
  sessions: number;
}

interface SubjectData {
  name: string;
  value: number;
  color: string;
}

interface TopTutor {
  tutor_id: number;
  name: string;
  rating: number;
  sessions: number;
  revenue: number;
}

const ManagerDashboard: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { get, loading } = useApi();
  
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [revenueData, setRevenueData] = useState<RevenueData[]>([]);
  const [subjectData, setSubjectData] = useState<SubjectData[]>([]);
  const [topTutors, setTopTutors] = useState<TopTutor[]>([]);
  const [refreshing, setRefreshing] = useState(false);

  // Load dashboard data
  const loadDashboardData = async () => {
    try {
      setRefreshing(true);
      
      // Load all dashboard data in parallel
      const [metricsResponse, revenueResponse, subjectsResponse, tutorsResponse] = await Promise.all([
        get<DashboardMetrics>('/manager/dashboard-metrics'),
        get<RevenueData[]>('/reports/revenue-trend', { params: { period: '6months' } }),
        get<SubjectData[]>('/reports/subject-distribution'),
        get<TopTutor[]>('/tutors/performance-ranking', { params: { limit: 5 } })
      ]);
      
      setMetrics(metricsResponse);
      setRevenueData(revenueResponse);
      setSubjectData(subjectsResponse);
      setTopTutors(tutorsResponse);
      
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setRefreshing(false);
    }
  };
  
  useEffect(() => {
    loadDashboardData();
  }, []);
  
  const handleRefresh = () => {
    loadDashboardData();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD',
    }).format(amount);
  };

  const MetricCard = ({ title, value, change, icon, color, onClick }: any) => (
    <Card 
      className={`p-6 cursor-pointer hover:shadow-elevated transition-all ${onClick ? 'hover:scale-105' : ''}`}
      onClick={onClick}
    >
      <div className="flex items-center justify-between mb-4">
        <div className={`p-3 rounded-full ${color} bg-opacity-10`}>
          {icon}
        </div>
        {change !== undefined && (
          <div className={`flex items-center gap-1 text-sm ${
            change >= 0 ? 'text-green-600' : 'text-red-600'
          }`}>
            {change >= 0 ? <ArrowUp className="w-4 h-4" /> : <ArrowDown className="w-4 h-4" />}
            <span>{Math.abs(change)}%</span>
          </div>
        )}
      </div>
      <h3 className="text-2xl font-bold text-gray-900">{value}</h3>
      <p className="text-sm text-gray-600 mt-1">{title}</p>
    </Card>
  );

  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="mb-8 flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Manager Dashboard</h1>
          <p className="text-lg text-gray-600 mt-2">
            Monitor platform performance and manage operations
          </p>
        </div>
        <Button
          variant="outline"
          onClick={handleRefresh}
          disabled={loading || refreshing}
          className="flex items-center gap-2"
        >
          <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Key Metrics */}
      {loading && !metrics ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="p-6 animate-pulse">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                <div className="w-16 h-4 bg-gray-200 rounded"></div>
              </div>
              <div className="w-20 h-8 bg-gray-200 rounded mb-2"></div>
              <div className="w-24 h-4 bg-gray-200 rounded"></div>
            </Card>
          ))}
        </div>
      ) : metrics ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <MetricCard
            title="Total Users"
            value={metrics.totalUsers.toLocaleString()}
            change={metrics.userGrowthChange}
            icon={<Users className="w-6 h-6 text-blue-600" />}
            color="bg-blue-600"
            onClick={() => navigate('/users')}
          />
          <MetricCard
            title="Monthly Revenue"
            value={formatCurrency(metrics.monthlyRevenue)}
            change={metrics.revenueChange}
            icon={<DollarSign className="w-6 h-6 text-green-600" />}
            color="bg-green-600"
            onClick={() => navigate('/billing/invoices')}
          />
          <MetricCard
            title="Active Sessions"
            value={metrics.activeSessions}
            change={metrics.sessionChange}
            icon={<Calendar className="w-6 h-6 text-purple-600" />}
            color="bg-purple-600"
            onClick={() => navigate('/calendar')}
          />
          <MetricCard
            title="Average Rating"
            value={metrics.averageRating.toFixed(1)}
            change={metrics.ratingChange}
            icon={<Star className="w-6 h-6 text-yellow-600" />}
            color="bg-yellow-600"
          />
        </div>
      ) : null}

      {/* Quick Actions & Alerts */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Alerts */}
        <Card className="p-6 lg:col-span-1">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Action Required</h2>
          {loading && !metrics ? (
            <div className="space-y-3">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="p-3 bg-gray-50 rounded-lg animate-pulse">
                  <div className="flex items-center gap-3">
                    <div className="w-5 h-5 bg-gray-200 rounded"></div>
                    <div className="flex-1">
                      <div className="w-32 h-4 bg-gray-200 rounded mb-1"></div>
                      <div className="w-24 h-3 bg-gray-200 rounded"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : metrics ? (
            <div className="space-y-3">
              {metrics.pendingApplications > 0 && (
                <div 
                  className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg cursor-pointer hover:bg-yellow-100"
                  onClick={() => navigate('/tutors/applications')}
                >
                  <div className="flex items-center gap-3">
                    <AlertCircle className="w-5 h-5 text-yellow-600" />
                    <div>
                      <p className="font-medium text-gray-900">{metrics.pendingApplications} Pending Applications</p>
                      <p className="text-sm text-gray-600">Tutor applications awaiting review</p>
                    </div>
                  </div>
                  <Badge className="bg-yellow-100 text-yellow-700">Review</Badge>
                </div>
              )}
              
              {metrics.pendingPayments > 0 && (
                <div 
                  className="flex items-center justify-between p-3 bg-red-50 rounded-lg cursor-pointer hover:bg-red-100"
                  onClick={() => navigate('/billing/tutor-payments')}
                >
                  <div className="flex items-center gap-3">
                    <CreditCard className="w-5 h-5 text-red-600" />
                    <div>
                      <p className="font-medium text-gray-900">Weekly Payments Due</p>
                      <p className="text-sm text-gray-600">{formatCurrency(metrics.pendingPayments)} pending</p>
                    </div>
                  </div>
                  <Badge className="bg-red-100 text-red-700">Process</Badge>
                </div>
              )}
              
              <div 
                className="flex items-center justify-between p-3 bg-blue-50 rounded-lg cursor-pointer hover:bg-blue-100"
                onClick={() => navigate('/messages/sms')}
              >
                <div className="flex items-center gap-3">
                  <MessageSquare className="w-5 h-5 text-blue-600" />
                  <div>
                    <p className="font-medium text-gray-900">Support Messages</p>
                    <p className="text-sm text-gray-600">Customer service center</p>
                  </div>
                </div>
                <Badge className="bg-blue-100 text-blue-700">View</Badge>
              </div>
            </div>
          ) : null}
        </Card>

        {/* Revenue Chart */}
        <Card className="p-6 lg:col-span-2">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Revenue Overview</h2>
          {loading && revenueData.length === 0 ? (
            <div className="w-full h-[250px] bg-gray-100 rounded animate-pulse flex items-center justify-center">
              <span className="text-gray-400">Loading chart...</span>
            </div>
          ) : revenueData.length > 0 ? (
            <ResponsiveContainer width="100%" height={250}>
              <LineChart data={revenueData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis yAxisId="left" />
                <YAxis yAxisId="right" orientation="right" />
                <Tooltip formatter={(value: any) => formatCurrency(value)} />
                <Legend />
                <Line 
                  yAxisId="left"
                  type="monotone" 
                  dataKey="revenue" 
                  stroke="#dc2626" 
                  strokeWidth={2}
                  name="Revenue"
                />
                <Line 
                  yAxisId="right"
                  type="monotone" 
                  dataKey="sessions" 
                  stroke="#3b82f6" 
                  strokeWidth={2}
                  name="Sessions"
                />
              </LineChart>
            </ResponsiveContainer>
          ) : (
            <div className="w-full h-[250px] flex items-center justify-center text-gray-500">
              No revenue data available
            </div>
          )}
        </Card>
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Subject Distribution */}
        <Card className="p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Sessions by Subject</h2>
          {loading && subjectData.length === 0 ? (
            <div className="w-full h-[250px] bg-gray-100 rounded animate-pulse flex items-center justify-center">
              <span className="text-gray-400">Loading chart...</span>
            </div>
          ) : subjectData.length > 0 ? (
            <>
              <ResponsiveContainer width="100%" height={250}>
                <RePieChart>
                  <Pie
                    data={subjectData}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={80}
                    paddingAngle={5}
                    dataKey="value"
                  >
                    {subjectData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value: any) => `${value}%`} />
                </RePieChart>
              </ResponsiveContainer>
              <div className="mt-4 space-y-2">
                {subjectData.map((subject) => (
                  <div key={subject.name} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full" style={{ backgroundColor: subject.color }} />
                      <span className="text-sm text-gray-600">{subject.name}</span>
                    </div>
                    <span className="text-sm font-medium">{subject.value}%</span>
                  </div>
                ))}
              </div>
            </>
          ) : (
            <div className="w-full h-[250px] flex items-center justify-center text-gray-500">
              No subject data available
            </div>
          )}
        </Card>

        {/* Top Performing Tutors */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Top Performing Tutors</h2>
            <Button variant="ghost" size="sm" onClick={() => navigate('/tutors')}>
              View All
            </Button>
          </div>
          {loading && topTutors.length === 0 ? (
            <div className="space-y-3">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex items-center justify-between p-3 border rounded-lg animate-pulse">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                    <div>
                      <div className="w-24 h-4 bg-gray-200 rounded mb-1"></div>
                      <div className="w-16 h-3 bg-gray-200 rounded"></div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="w-16 h-4 bg-gray-200 rounded mb-1"></div>
                    <div className="w-12 h-3 bg-gray-200 rounded"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : topTutors.length > 0 ? (
            <div className="space-y-3">
              {topTutors.map((tutor, index) => (
                <div key={tutor.tutor_id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-accent-red bg-opacity-10 rounded-full flex items-center justify-center">
                      <span className="font-semibold text-accent-red">{index + 1}</span>
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{tutor.name}</p>
                      <div className="flex items-center gap-3 text-sm text-gray-600">
                        <span className="flex items-center gap-1">
                          <Star className="w-3 h-3 text-yellow-500 fill-yellow-500" />
                          {tutor.rating.toFixed(1)}
                        </span>
                        <span>{tutor.sessions} sessions</span>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-gray-900">{formatCurrency(tutor.revenue)}</p>
                    <p className="text-sm text-gray-600">revenue</p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              No tutor performance data available
            </div>
          )}
        </Card>
      </div>

      {/* Quick Stats */}
      {loading && !metrics ? (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="p-4 text-center animate-pulse">
              <div className="w-8 h-8 bg-gray-200 rounded mx-auto mb-2"></div>
              <div className="w-16 h-8 bg-gray-200 rounded mx-auto mb-2"></div>
              <div className="w-20 h-4 bg-gray-200 rounded mx-auto"></div>
            </Card>
          ))}
        </div>
      ) : metrics ? (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card className="p-4 text-center">
            <Users className="w-8 h-8 text-blue-600 mx-auto mb-2" />
            <p className="text-2xl font-bold">{metrics.activeClients}</p>
            <p className="text-sm text-gray-600">Active Clients</p>
          </Card>
          <Card className="p-4 text-center">
            <Activity className="w-8 h-8 text-green-600 mx-auto mb-2" />
            <p className="text-2xl font-bold">{metrics.activeTutors}</p>
            <p className="text-sm text-gray-600">Active Tutors</p>
          </Card>
          <Card className="p-4 text-center">
            <BookOpen className="w-8 h-8 text-purple-600 mx-auto mb-2" />
            <p className="text-2xl font-bold">{metrics.completedSessions}</p>
            <p className="text-sm text-gray-600">Sessions This Month</p>
          </Card>
          <Card className="p-4 text-center">
            <TrendingUp className="w-8 h-8 text-orange-600 mx-auto mb-2" />
            <p className="text-2xl font-bold">+{metrics.newUsersThisMonth}</p>
            <p className="text-sm text-gray-600">New Users</p>
          </Card>
        </div>
      ) : null}

      {/* Action Buttons */}
      <Card className="p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Button 
            variant="outline" 
            className="flex-col h-auto py-4"
            onClick={() => navigate('/users/add')}
          >
            <Users className="w-6 h-6 mb-2 text-accent-red" />
            <span>Add User</span>
          </Button>
          <Button 
            variant="outline" 
            className="flex-col h-auto py-4"
            onClick={() => navigate('/billing/invoices/generate')}
          >
            <DollarSign className="w-6 h-6 mb-2 text-accent-red" />
            <span>Generate Invoices</span>
          </Button>
          <Button 
            variant="outline" 
            className="flex-col h-auto py-4"
            onClick={() => navigate('/messages/broadcast')}
          >
            <MessageSquare className="w-6 h-6 mb-2 text-accent-red" />
            <span>Send Broadcast</span>
          </Button>
          <Button 
            variant="outline" 
            className="flex-col h-auto py-4"
            onClick={() => navigate('/reports')}
          >
            <BarChart2 className="w-6 h-6 mb-2 text-accent-red" />
            <span>View Reports</span>
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default ManagerDashboard;