import React, { useState } from 'react';
import { Badge, Plus, X } from 'lucide-react';

interface CertificationSelectorProps {
  value: string[];
  onChange: (value: string[]) => void;
  error?: string;
}

const CERTIFICATION_OPTIONS = [
  // Quebec-specific
  { value: 'qc_teaching_license', label: 'Quebec Teaching License', category: 'Quebec' },
  { value: 'qc_brevets', label: "Brevet d'enseignement du Québec", category: 'Quebec' },
  
  // Subject-specific
  { value: 'ap_certified', label: 'AP Certified Teacher', category: 'Subject' },
  { value: 'ib_certified', label: 'IB Certified', category: 'Subject' },
  { value: 'math_specialist', label: 'Mathematics Specialist', category: 'Subject' },
  { value: 'science_specialist', label: 'Science Specialist', category: 'Subject' },
  
  // Language
  { value: 'tesol', label: 'TESOL/TEFL Certification', category: 'Language' },
  { value: 'delf_dalf', label: 'DELF/DALF Examiner', category: 'Language' },
  { value: 'celta', label: 'CELTA (English Teaching)', category: 'Language' },
  
  // Special needs
  { value: 'special_ed', label: 'Special Education Certified', category: 'Special Needs' },
  { value: 'learning_disabilities', label: 'Learning Disabilities Specialist', category: 'Special Needs' },
  { value: 'autism_specialist', label: 'Autism Spectrum Specialist', category: 'Special Needs' },
  
  // Technology
  { value: 'google_educator', label: 'Google Certified Educator', category: 'Technology' },
  { value: 'microsoft_educator', label: 'Microsoft Innovative Educator', category: 'Technology' }
];

export const CertificationSelector: React.FC<CertificationSelectorProps> = ({
  value = [],
  onChange,
  error
}) => {
  const [showCustomInput, setShowCustomInput] = useState(false);
  const [customCert, setCustomCert] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  const categories = Array.from(new Set(CERTIFICATION_OPTIONS.map(cert => cert.category)));

  const handleToggleCertification = (certValue: string) => {
    if (value.includes(certValue)) {
      onChange(value.filter(v => v !== certValue));
    } else {
      onChange([...value, certValue]);
    }
  };

  const handleAddCustom = () => {
    if (customCert.trim() && !value.includes(customCert.trim())) {
      onChange([...value, customCert.trim()]);
      setCustomCert('');
      setShowCustomInput(false);
    }
  };

  const handleRemoveCertification = (cert: string) => {
    onChange(value.filter(v => v !== cert));
  };

  const getCertLabel = (certValue: string): string => {
    const cert = CERTIFICATION_OPTIONS.find(c => c.value === certValue);
    return cert ? cert.label : certValue;
  };

  const filteredOptions = selectedCategory
    ? CERTIFICATION_OPTIONS.filter(cert => cert.category === selectedCategory)
    : CERTIFICATION_OPTIONS;

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Teaching Certifications
        </label>
        
        {/* Selected certifications */}
        {value.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-4">
            {value.map(cert => (
              <Badge 
                key={cert}
                className="bg-red-100 text-red-700 px-3 py-1 flex items-center gap-1"
              >
                {getCertLabel(cert)}
                <button
                  type="button"
                  onClick={() => handleRemoveCertification(cert)}
                  className="ml-1 hover:text-red-900"
                >
                  <X className="w-3 h-3" />
                </button>
              </Badge>
            ))}
          </div>
        )}

        {/* Category filter */}
        <div className="flex gap-2 mb-3 flex-wrap">
          <button
            type="button"
            onClick={() => setSelectedCategory(null)}
            className={`px-3 py-1 text-xs rounded-full transition-colors ${
              selectedCategory === null
                ? 'bg-red-600 text-white'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            All
          </button>
          {categories.map(category => (
            <button
              key={category}
              type="button"
              onClick={() => setSelectedCategory(category)}
              className={`px-3 py-1 text-xs rounded-full transition-colors ${
                selectedCategory === category
                  ? 'bg-red-600 text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              {category}
            </button>
          ))}
        </div>

        {/* Certification options */}
        <div className="border border-gray-200 rounded-lg p-3 max-h-48 overflow-y-auto">
          <div className="grid grid-cols-1 gap-2">
            {filteredOptions.map(cert => {
              const isSelected = value.includes(cert.value);
              return (
                <label
                  key={cert.value}
                  className={`flex items-center space-x-3 p-2 rounded cursor-pointer transition-colors ${
                    isSelected ? 'bg-red-50' : 'hover:bg-gray-50'
                  }`}
                >
                  <input
                    type="checkbox"
                    checked={isSelected}
                    onChange={() => handleToggleCertification(cert.value)}
                    className="rounded border-gray-300 text-red-600 focus:ring-red-500"
                  />
                  <span className="text-sm">{cert.label}</span>
                </label>
              );
            })}
          </div>
        </div>

        {/* Add custom certification */}
        <div className="mt-3">
          {showCustomInput ? (
            <div className="flex gap-2">
              <input
                type="text"
                value={customCert}
                onChange={(e) => setCustomCert(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleAddCustom()}
                placeholder="Enter certification name..."
                className="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"
                autoFocus
              />
              <button
                type="button"
                onClick={handleAddCustom}
                disabled={!customCert.trim()}
                className="px-3 py-2 bg-red-600 text-white rounded-lg text-sm hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Add
              </button>
              <button
                type="button"
                onClick={() => {
                  setShowCustomInput(false);
                  setCustomCert('');
                }}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm hover:bg-gray-50"
              >
                Cancel
              </button>
            </div>
          ) : (
            <button
              type="button"
              onClick={() => setShowCustomInput(true)}
              className="flex items-center gap-2 text-sm text-red-600 hover:text-red-700"
            >
              <Plus className="w-4 h-4" />
              Add Custom Certification
            </button>
          )}
        </div>

        {error && (
          <p className="mt-2 text-sm text-red-600">{error}</p>
        )}
      </div>
    </div>
  );
};