"""
WebSocket endpoints for real-time messaging and notifications.
"""

import json
import logging
from typing import Dict, Any, List, Optional, Set
from datetime import datetime
import asyncio
from fastapi import (
    <PERSON><PERSON>outer, WebSocket, WebSocketDisconnect, 
    Depends, HTTPException, status
)
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from app.core.security import verify_token
from app.models.user_models import User
from app.models.websocket_models import (
    MessageType, WSMessage, WSEvent,
    ConnectionStatus, TypingStatus
)
from app.database.repositories.message_repository import MessageRepository
from app.services.notification_orchestrator import NotificationOrchestrator
from app.services.role_indicator_service import RoleIndicatorService, MessageContext
from app.core.redis_client import redis_client

logger = logging.getLogger(__name__)

router = APIRouter()
security = HTTPBearer()


class ConnectionManager:
    """
    Manages WebSocket connections and message routing.
    
    Features:
    - Connection lifecycle management
    - Message broadcasting
    - Online status tracking
    - Typing indicators
    - Read receipts
    - Reconnection handling
    """
    
    def __init__(self):
        # Active connections: {user_id: {connection_id: websocket}}
        self.active_connections: Dict[int, Dict[str, WebSocket]] = {}
        # Typing status: {conversation_id: {user_id: timestamp}}
        self.typing_status: Dict[str, Dict[int, datetime]] = {}
        # Message repository
        self.message_repo = MessageRepository()
        # Redis for distributed state
        self.redis = redis_client
        # Notification orchestrator
        self.notification = NotificationOrchestrator()
        # Role indicator service
        self.role_indicator = RoleIndicatorService()
        
    async def connect(
        self,
        websocket: WebSocket,
        user_id: int,
        connection_id: str
    ):
        """Accept and register new connection."""
        await websocket.accept()
        
        # Initialize user connections if needed
        if user_id not in self.active_connections:
            self.active_connections[user_id] = {}
        
        # Store connection
        self.active_connections[user_id][connection_id] = websocket
        
        # Update online status
        await self._update_online_status(user_id, True)
        
        # Send connection confirmation
        await self.send_personal_message(
            user_id=user_id,
            message=WSMessage(
                type=MessageType.SYSTEM,
                event=WSEvent.CONNECTED,
                data={
                    'connection_id': connection_id,
                    'timestamp': datetime.now().isoformat()
                }
            )
        )
        
        # Notify contacts about online status
        await self._broadcast_online_status(user_id, True)
        
        logger.info(f"User {user_id} connected with ID {connection_id}")
    
    async def disconnect(
        self,
        user_id: int,
        connection_id: str
    ):
        """Handle connection disconnect."""
        if user_id in self.active_connections:
            self.active_connections[user_id].pop(connection_id, None)
            
            # Remove user entry if no more connections
            if not self.active_connections[user_id]:
                del self.active_connections[user_id]
                
                # Update online status
                await self._update_online_status(user_id, False)
                
                # Notify contacts about offline status
                await self._broadcast_online_status(user_id, False)
        
        logger.info(f"User {user_id} disconnected (ID: {connection_id})")
    
    async def send_personal_message(
        self,
        user_id: int,
        message: WSMessage
    ):
        """Send message to specific user (all their connections)."""
        if user_id in self.active_connections:
            disconnected = []
            
            for conn_id, websocket in self.active_connections[user_id].items():
                try:
                    await websocket.send_json(message.model_dump())
                except Exception as e:
                    logger.error(f"Error sending to {user_id}/{conn_id}: {e}")
                    disconnected.append(conn_id)
            
            # Clean up disconnected connections
            for conn_id in disconnected:
                await self.disconnect(user_id, conn_id)
    
    async def broadcast_to_conversation(
        self,
        conversation_id: str,
        message: WSMessage,
        exclude_user: Optional[int] = None
    ):
        """Broadcast message to all users in a conversation."""
        # Get conversation participants
        participants = await self.message_repo.get_conversation_participants(
            conversation_id
        )
        
        for user_id in participants:
            if user_id != exclude_user:
                await self.send_personal_message(user_id, message)
    
    async def handle_message(
        self,
        user_id: int,
        data: Dict[str, Any]
    ):
        """Handle incoming WebSocket message."""
        try:
            message_type = MessageType(data.get('type'))
            
            if message_type == MessageType.CHAT:
                await self._handle_chat_message(user_id, data)
                
            elif message_type == MessageType.TYPING:
                await self._handle_typing_indicator(user_id, data)
                
            elif message_type == MessageType.READ_RECEIPT:
                await self._handle_read_receipt(user_id, data)
                
            elif message_type == MessageType.PRESENCE:
                await self._handle_presence_update(user_id, data)
                
            else:
                logger.warning(f"Unknown message type: {message_type}")
                
        except Exception as e:
            logger.error(f"Error handling message: {e}")
            
            # Send error response
            await self.send_personal_message(
                user_id=user_id,
                message=WSMessage(
                    type=MessageType.ERROR,
                    event=WSEvent.ERROR,
                    data={'error': str(e)}
                )
            )
    
    async def _handle_chat_message(
        self,
        sender_id: int,
        data: Dict[str, Any]
    ):
        """Handle chat message."""
        # Extract message data
        conversation_id = data.get('conversation_id')
        content = data.get('content')
        metadata = data.get('metadata', {})
        
        if not conversation_id or not content:
            raise ValueError("Missing conversation_id or content")
        
        # Save message to database
        message = await self.message_repo.create_message(
            conversation_id=conversation_id,
            sender_id=sender_id,
            content=content,
            metadata=metadata
        )
        
        # Add role indicator to content
        role_indicator = await self.role_indicator.add_role_indicator(
            message=content,
            user_id=sender_id,
            context=MessageContext.WEBSOCKET,
            language='en'  # TODO: Get user's language preference
        )
        
        # Prepare WebSocket message
        ws_message = WSMessage(
            type=MessageType.CHAT,
            event=WSEvent.MESSAGE_SENT,
            data={
                'message_id': message.message_id,
                'conversation_id': conversation_id,
                'sender_id': sender_id,
                'content': content,
                'formatted_content': role_indicator,
                'metadata': metadata,
                'timestamp': message.created_at.isoformat()
            }
        )
        
        # Broadcast to conversation participants
        await self.broadcast_to_conversation(
            conversation_id=conversation_id,
            message=ws_message,
            exclude_user=sender_id
        )
        
        # Send confirmation to sender
        await self.send_personal_message(
            user_id=sender_id,
            message=WSMessage(
                type=MessageType.CHAT,
                event=WSEvent.MESSAGE_DELIVERED,
                data={'message_id': message.message_id}
            )
        )
        
        # Send push notification to offline users
        await self._notify_offline_users(
            conversation_id=conversation_id,
            sender_id=sender_id,
            content=content
        )
    
    async def _handle_typing_indicator(
        self,
        user_id: int,
        data: Dict[str, Any]
    ):
        """Handle typing indicator."""
        conversation_id = data.get('conversation_id')
        is_typing = data.get('is_typing', False)
        
        if not conversation_id:
            return
        
        # Update typing status
        if is_typing:
            if conversation_id not in self.typing_status:
                self.typing_status[conversation_id] = {}
            self.typing_status[conversation_id][user_id] = datetime.now()
        else:
            if conversation_id in self.typing_status:
                self.typing_status[conversation_id].pop(user_id, None)
        
        # Broadcast typing status
        ws_message = WSMessage(
            type=MessageType.TYPING,
            event=WSEvent.TYPING_START if is_typing else WSEvent.TYPING_STOP,
            data={
                'conversation_id': conversation_id,
                'user_id': user_id,
                'is_typing': is_typing
            }
        )
        
        await self.broadcast_to_conversation(
            conversation_id=conversation_id,
            message=ws_message,
            exclude_user=user_id
        )
    
    async def _handle_read_receipt(
        self,
        user_id: int,
        data: Dict[str, Any]
    ):
        """Handle read receipt."""
        message_ids = data.get('message_ids', [])
        conversation_id = data.get('conversation_id')
        
        if not message_ids or not conversation_id:
            return
        
        # Mark messages as read
        await self.message_repo.mark_messages_read(
            message_ids=message_ids,
            user_id=user_id
        )
        
        # Broadcast read receipt
        ws_message = WSMessage(
            type=MessageType.READ_RECEIPT,
            event=WSEvent.MESSAGE_READ,
            data={
                'conversation_id': conversation_id,
                'message_ids': message_ids,
                'reader_id': user_id,
                'read_at': datetime.now().isoformat()
            }
        )
        
        await self.broadcast_to_conversation(
            conversation_id=conversation_id,
            message=ws_message,
            exclude_user=user_id
        )
    
    async def _handle_presence_update(
        self,
        user_id: int,
        data: Dict[str, Any]
    ):
        """Handle presence/status update."""
        status = data.get('status', 'online')
        status_message = data.get('status_message', '')
        
        # Update user status
        await self.redis.hset(
            f"user:status:{user_id}",
            mapping={
                'status': status,
                'status_message': status_message,
                'updated_at': datetime.now().isoformat()
            }
        )
        
        # Broadcast to contacts
        contacts = await self.message_repo.get_user_contacts(user_id)
        
        ws_message = WSMessage(
            type=MessageType.PRESENCE,
            event=WSEvent.USER_STATUS_CHANGED,
            data={
                'user_id': user_id,
                'status': status,
                'status_message': status_message
            }
        )
        
        for contact_id in contacts:
            await self.send_personal_message(contact_id, ws_message)
    
    async def _update_online_status(
        self,
        user_id: int,
        is_online: bool
    ):
        """Update user's online status in Redis."""
        key = f"user:online:{user_id}"
        
        if is_online:
            await self.redis.setex(key, 300, "1")  # 5 minute TTL
        else:
            await self.redis.delete(key)
    
    async def _broadcast_online_status(
        self,
        user_id: int,
        is_online: bool
    ):
        """Broadcast online status change to contacts."""
        contacts = await self.message_repo.get_user_contacts(user_id)
        
        ws_message = WSMessage(
            type=MessageType.PRESENCE,
            event=WSEvent.USER_ONLINE if is_online else WSEvent.USER_OFFLINE,
            data={
                'user_id': user_id,
                'is_online': is_online,
                'timestamp': datetime.now().isoformat()
            }
        )
        
        for contact_id in contacts:
            await self.send_personal_message(contact_id, ws_message)
    
    async def _notify_offline_users(
        self,
        conversation_id: str,
        sender_id: int,
        content: str
    ):
        """Send push notifications to offline users."""
        participants = await self.message_repo.get_conversation_participants(
            conversation_id
        )
        
        # Get sender info
        sender = await self.message_repo.get_user(sender_id)
        
        for user_id in participants:
            if user_id != sender_id and user_id not in self.active_connections:
                # User is offline, send push notification
                await self.notification.send_notification(
                    user_id=user_id,
                    notification_type='new_message',
                    title=f"New message from {sender.first_name}",
                    message=content[:100],  # Preview
                    data={
                        'conversation_id': conversation_id,
                        'sender_id': sender_id
                    }
                )
    
    async def cleanup_stale_typing_indicators(self):
        """Clean up old typing indicators (run periodically)."""
        now = datetime.now()
        stale_threshold = timedelta(seconds=5)
        
        for conversation_id in list(self.typing_status.keys()):
            for user_id, timestamp in list(self.typing_status[conversation_id].items()):
                if now - timestamp > stale_threshold:
                    # Remove stale indicator
                    self.typing_status[conversation_id].pop(user_id, None)
                    
                    # Broadcast typing stop
                    await self.broadcast_to_conversation(
                        conversation_id=conversation_id,
                        message=WSMessage(
                            type=MessageType.TYPING,
                            event=WSEvent.TYPING_STOP,
                            data={
                                'conversation_id': conversation_id,
                                'user_id': user_id,
                                'is_typing': False
                            }
                        ),
                        exclude_user=user_id
                    )


# Global connection manager instance
manager = ConnectionManager()


async def get_current_user_from_ws(
    websocket: WebSocket,
    token: Optional[str] = None
) -> User:
    """Extract and verify user from WebSocket connection."""
    if not token:
        # Try to get token from query params
        token = websocket.query_params.get("token")
    
    if not token:
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required"
        )
    
    try:
        # Verify token
        payload = verify_token(token)
        user_id = payload.get("sub")
        
        if not user_id:
            raise ValueError("Invalid token payload")
        
        # Get user from database
        user = await manager.message_repo.get_user(int(user_id))
        
        if not user:
            raise ValueError("User not found")
        
        return user
        
    except Exception as e:
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Authentication failed: {str(e)}"
        )


@router.websocket("/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    token: Optional[str] = None
):
    """Main WebSocket endpoint for real-time messaging."""
    connection_id = None
    user = None
    
    try:
        # Authenticate user
        user = await get_current_user_from_ws(websocket, token)
        
        # Generate connection ID
        connection_id = f"{user.user_id}_{datetime.now().timestamp()}"
        
        # Accept connection
        await manager.connect(websocket, user.user_id, connection_id)
        
        # Main message loop
        while True:
            # Receive message
            data = await websocket.receive_json()
            
            # Handle message
            await manager.handle_message(user.user_id, data)
            
    except WebSocketDisconnect:
        if user and connection_id:
            await manager.disconnect(user.user_id, connection_id)
            
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        
        if user and connection_id:
            await manager.disconnect(user.user_id, connection_id)
            
        await websocket.close(code=status.WS_1011_INTERNAL_ERROR)


@router.get("/ws/status")
async def get_websocket_status():
    """Get WebSocket server status."""
    active_users = len(manager.active_connections)
    total_connections = sum(
        len(conns) for conns in manager.active_connections.values()
    )
    
    return {
        "status": "online",
        "active_users": active_users,
        "total_connections": total_connections,
        "typing_conversations": len(manager.typing_status)
    }


# Background task to clean up stale typing indicators
async def typing_cleanup_task():
    """Background task to clean up stale typing indicators."""
    while True:
        try:
            await manager.cleanup_stale_typing_indicators()
        except Exception as e:
            logger.error(f"Error in typing cleanup: {e}")
        
        await asyncio.sleep(5)  # Run every 5 seconds


# Start background task on module load
asyncio.create_task(typing_cleanup_task())