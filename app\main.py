import os
import logging
import sys

import sentry_sdk
from sentry_sdk.integrations.fastapi import FastApiIntegration
from sentry_sdk.integrations.starlette import StarletteIntegration
from sentry_sdk.integrations.logging import LoggingIntegration

# Only initialize Sentry if DSN is provided
if sentry_dsn := os.environ.get("SENTRY_DSN", "https://<EMAIL>/4509579436818432"):
    sentry_sdk.init(
        dsn=sentry_dsn,
        integrations=[
            FastApiIntegration(
                transaction_style="endpoint",
            ),
            StarletteIntegration(
                transaction_style="endpoint",
            ),
            LoggingIntegration(
                level=logging.INFO,        # Capture info and above as breadcrumbs
                event_level=logging.ERROR,  # Send errors as events
            ),
        ],
        # Performance Monitoring
        traces_sample_rate=float(os.environ.get("SENTRY_TRACES_SAMPLE_RATE", "1.0")),
        profiles_sample_rate=float(os.environ.get("SENTRY_PROFILES_SAMPLE_RATE", "1.0")),
        # Session Tracking
        release=os.environ.get("RAILWAY_GIT_COMMIT_SHA", "development"),
        environment=os.environ.get("ENVIRONMENT", "development"),
        # Additional options
        send_default_pii=True,  # Include user data
        attach_stacktrace=True,
        before_send=lambda event, hint: event if os.environ.get("ENVIRONMENT") != "test" else None,  # Don't send events in test environment
    )
    print(f"Sentry initialized for environment: {os.environ.get('ENVIRONMENT', 'development')}")
else:
    print("Sentry DSN not provided, skipping Sentry initialization")

from fastapi import FastAPI, Request, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, HTMLResponse
from contextlib import asynccontextmanager
from pathlib import Path

from app.config.settings import settings
from app.config.database import get_database_pool, close_database_pool
from app.core.middleware import LoggingMiddleware, SecurityHeadersMiddleware
from app.core.authorization import AuthorizationMiddleware
from app.core.security_middleware import (
    SecurityMiddleware, 
    InputValidationMiddleware
)
from app.core.sentry_middleware import SentryMiddleware
from app.locales.middleware import LocalizationMiddleware
from app.api.v1.router import api_router

# Configure basic logging to stdout for Railway
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)],
    force=True  # Force reconfiguration
)


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    print("=== APPLICATION STARTUP ===")
    print(f"Environment: {os.environ.get('ENVIRONMENT', 'Not set')}")
    print(f"Railway Environment: {os.environ.get('RAILWAY_ENVIRONMENT', 'Not set')}")
    print(f"SMTP Configuration from environment:")
    print(f"  SMTP_HOST: {os.environ.get('SMTP_HOST', 'Not set')}")
    print(f"  SMTP_PORT: {os.environ.get('SMTP_PORT', 'Not set')}")
    print(f"  SMTP_USERNAME: {os.environ.get('SMTP_USERNAME', 'Not set')}")
    print(f"  SMTP_PASSWORD: {'Set' if os.environ.get('SMTP_PASSWORD') else 'Not set'}")
    print(f"  SMTP_TLS: {os.environ.get('SMTP_TLS', 'Not set')}")
    print(f"Settings loaded with:")
    print(f"  SMTP_HOST: {settings.SMTP_HOST}")
    print(f"  SMTP_PORT: {settings.SMTP_PORT}")
    print(f"  SMTP_USERNAME: {settings.SMTP_USERNAME}")
    print("=========================")
    
    await get_database_pool()
    yield
    # Shutdown
    await close_database_pool()


def create_app() -> FastAPI:
    app = FastAPI(
        title=settings.PROJECT_NAME,
        version=settings.VERSION,
        description="TutorAide - Comprehensive tutoring management platform",
        openapi_url=f"{settings.API_V1_STR}/openapi.json",
        lifespan=lifespan,
        docs_url="/docs" if settings.DEBUG else None,
        redoc_url="/redoc" if settings.DEBUG else None,
    )

    # Security middleware
    app.add_middleware(
        TrustedHostMiddleware, 
        allowed_hosts=settings.ALLOWED_HOSTS
    )
    
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.CORS_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Custom middleware (order matters - security first, Sentry early to catch all errors)
    app.add_middleware(SentryMiddleware)  # Add early to catch all errors with context
    app.add_middleware(SecurityMiddleware)
    app.add_middleware(InputValidationMiddleware)
    app.add_middleware(SecurityHeadersMiddleware)
    app.add_middleware(LoggingMiddleware)
    app.add_middleware(LocalizationMiddleware)
    app.add_middleware(AuthorizationMiddleware)

    # Include API routers first (before static files)
    app.include_router(api_router, prefix=settings.API_V1_STR)
    
    # Health check endpoint
    @app.get("/health")
    async def health_check():
        return {"status": "healthy", "service": "tutoraide-api"}
    
    # Sentry test endpoint (only in debug mode)
    if settings.DEBUG:
        @app.get("/sentry-debug")
        async def trigger_error():
            """Endpoint to test Sentry error reporting"""
            division_by_zero = 1 / 0
            return {"message": "This should not be reached"}
    
    # Serve React frontend static files
    frontend_dist_path = Path(__file__).parent / "frontend" / "dist"
    
    # Check if the frontend has been built
    if frontend_dist_path.exists():
        # Serve static assets (JS, CSS, images) from the dist/assets directory
        assets_path = frontend_dist_path / "assets"
        if assets_path.exists():
            app.mount("/assets", StaticFiles(directory=str(assets_path)), name="assets")
        
        # Serve other static files directly from dist root (like favicon)
        @app.get("/favicon.ico")
        async def favicon():
            favicon_path = frontend_dist_path / "favicon.ico"
            if favicon_path.exists():
                return FileResponse(favicon_path)
            return {"detail": "Favicon not found"}
        
        # Catch-all route - serve index.html for all routes not handled above
        # This allows React Router to handle client-side routing
        # IMPORTANT: Exclude API routes to prevent conflicts
        @app.get("/{path:path}")
        async def serve_react_app(path: str):
            # Skip API routes - let them be handled by the API router
            if path.startswith("api/"):
                # This should not happen since API routes are handled first,
                # but this is a safety check
                from fastapi import HTTPException
                raise HTTPException(status_code=404, detail="API endpoint not found")

            # API routes and health check are already handled above
            # This catches all other routes and serves the React app
            index_path = frontend_dist_path / "index.html"
            if index_path.exists():
                return FileResponse(
                    index_path,
                    media_type="text/html",
                    headers={
                        "Cache-Control": "no-cache, no-store, must-revalidate",
                        "Pragma": "no-cache",
                        "Expires": "0"
                    }
                )
            
            # Fallback to the template index.html if React app not built
            template_path = Path(__file__).parent / "templates" / "index.html"
            if template_path.exists():
                with open(template_path, "r") as f:
                    html_content = f.read()
                return HTMLResponse(content=html_content)
            
            # If no frontend is available, return API info
            return {
                "name": settings.PROJECT_NAME,
                "version": settings.VERSION,
                "status": "operational",
                "frontend": "not built",
                "hint": "Run ./build_frontend.sh to build the React frontend"
            }
    
    else:
        # If frontend not built, at least serve branding assets if available
        static_path = Path(__file__).parent / "frontend" / "public"
        if static_path.exists():
            # Serve favicon and logo directly from public folder
            @app.get("/favicon_tutoraide.png")
            async def favicon_png():
                favicon_path = static_path / "favicon_tutoraide.png"
                if favicon_path.exists():
                    return FileResponse(
                        favicon_path, 
                        media_type="image/png",
                        headers={"Cache-Control": "public, max-age=3600"}
                    )
                return {"detail": "Favicon not available"}
            
            @app.get("/logo_tutoraide.jpg")
            async def logo():
                logo_path = static_path / "logo_tutoraide.jpg"
                if logo_path.exists():
                    return FileResponse(
                        logo_path, 
                        media_type="image/jpeg",
                        headers={"Cache-Control": "public, max-age=3600"}
                    )
                return {"detail": "Logo not available"}

    return app


app = create_app()