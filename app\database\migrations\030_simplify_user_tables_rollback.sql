-- ================================================
-- Rollback: Simplify User Tables Structure
-- ================================================
-- This rollback script reverses the user table simplification
-- and restores the original 16-table structure
--
-- Version: 030_rollback
-- Date: 2025-06-24
-- ================================================

-- ============================================
-- Step 1: Recreate Original Tables
-- ============================================

-- Recreate language preferences history
CREATE TABLE IF NOT EXISTS user_language_preferences_history (
    preference_history_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    previous_language VARCHAR(5),
    new_language VARCHAR(5) NOT NULL,
    change_source VARCHAR(20) NOT NULL CHECK (change_source IN ('user_manual', 'auto_detect', 'browser_detect', 'admin_override', 'system_migration')),
    change_reason TEXT,
    browser_language VARCHAR(100),
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(100),
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES user_accounts(user_id)
);

-- Recreate language usage analytics
CREATE TABLE IF NOT EXISTS language_usage_analytics (
    analytics_id SERIAL PRIMARY KEY,
    date_recorded DATE NOT NULL DEFAULT CURRENT_DATE,
    language_code VARCHAR(5) NOT NULL,
    total_users INTEGER DEFAULT 0,
    new_users INTEGER DEFAULT 0,
    active_sessions INTEGER DEFAULT 0,
    manual_switches INTEGER DEFAULT 0,
    auto_detections INTEGER DEFAULT 0,
    quebec_french_users INTEGER DEFAULT 0,
    browser_preferences JSONB,
    geographic_data JSONB,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(date_recorded, language_code)
);

-- Recreate formatting preferences
CREATE TABLE IF NOT EXISTS user_formatting_preferences (
    formatting_preference_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    date_format VARCHAR(20) DEFAULT 'medium' CHECK (date_format IN ('short', 'medium', 'long', 'full')),
    time_format VARCHAR(10) DEFAULT 'auto' CHECK (time_format IN ('auto', '12h', '24h')),
    currency_display VARCHAR(20) DEFAULT 'symbol' CHECK (currency_display IN ('symbol', 'code', 'name')),
    number_precision INTEGER DEFAULT 2 CHECK (number_precision >= 0 AND number_precision <= 6),
    show_quebec_indicators BOOLEAN DEFAULT true,
    use_relative_dates BOOLEAN DEFAULT true,
    phone_format VARCHAR(20) DEFAULT 'national' CHECK (phone_format IN ('national', 'international')),
    address_format VARCHAR(20) DEFAULT 'standard' CHECK (address_format IN ('standard', 'compact', 'multiline')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id)
);

-- Recreate formatting preferences history
CREATE TABLE IF NOT EXISTS user_formatting_preferences_history (
    history_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    formatting_preference_id INTEGER REFERENCES user_formatting_preferences(formatting_preference_id) ON DELETE SET NULL,
    field_name VARCHAR(50) NOT NULL,
    old_value TEXT,
    new_value TEXT NOT NULL,
    change_source VARCHAR(50) DEFAULT 'user_action' CHECK (change_source IN ('user_action', 'admin_override', 'system_update', 'language_change')),
    change_reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by_user_id INTEGER REFERENCES user_accounts(user_id) ON DELETE SET NULL,
    session_id VARCHAR(255),
    user_agent TEXT,
    ip_address INET
);

-- Recreate notification preferences
CREATE TABLE IF NOT EXISTS notification_preferences (
    preference_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    notification_type notification_type NOT NULL,
    push_enabled BOOLEAN DEFAULT true,
    sms_enabled BOOLEAN DEFAULT true,
    email_enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, notification_type)
);

-- Recreate quick action usage
CREATE TABLE IF NOT EXISTS quick_action_usage (
    usage_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id),
    action_type VARCHAR(50) NOT NULL,
    executed_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    execution_time_ms INTEGER,
    success BOOLEAN NOT NULL DEFAULT true,
    error_message TEXT,
    context_data JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Recreate user pinned actions
CREATE TABLE IF NOT EXISTS user_pinned_actions (
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id),
    action_type VARCHAR(50) NOT NULL,
    pinned_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, action_type)
);

-- Recreate password reset tokens
CREATE TABLE IF NOT EXISTS password_reset_tokens (
    reset_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    token UUID NOT NULL DEFAULT uuid_generate_v4() UNIQUE,
    expires_at TIMESTAMP NOT NULL,
    used_at TIMESTAMP,
    initiated_by VARCHAR(50) DEFAULT 'user',
    initiated_by_user_id INTEGER REFERENCES user_accounts(user_id),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Recreate email verification tokens
CREATE TABLE IF NOT EXISTS email_verification_tokens (
    verification_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    token UUID UNIQUE NOT NULL DEFAULT uuid_generate_v4(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    verified_at TIMESTAMP WITH TIME ZONE NULL,
    ip_address INET NULL,
    user_agent TEXT NULL,
    verified_from_ip INET NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT current_timestamp_est(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT current_timestamp_est(),
    deleted_at TIMESTAMP WITH TIME ZONE NULL
);

-- Recreate user sessions
CREATE TABLE IF NOT EXISTS user_sessions (
    session_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL UNIQUE,
    role_type user_role_type NOT NULL,
    ip_address INET,
    user_agent TEXT,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    last_activity TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Recreate two-factor setups
CREATE TABLE IF NOT EXISTS two_factor_setups (
    setup_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    method two_factor_method NOT NULL,
    secret TEXT NOT NULL,
    backup_codes TEXT,
    phone_number VARCHAR(20),
    email VARCHAR(255),
    is_primary BOOLEAN DEFAULT true,
    is_enabled BOOLEAN DEFAULT false,
    verified_at TIMESTAMP WITH TIME ZONE,
    last_used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    CONSTRAINT unique_user_method UNIQUE (user_id, method),
    CONSTRAINT phone_required_for_sms CHECK (method != 'sms' OR phone_number IS NOT NULL),
    CONSTRAINT email_required_for_email CHECK (method != 'email' OR email IS NOT NULL)
);

-- Recreate two-factor challenges
CREATE TABLE IF NOT EXISTS two_factor_challenges (
    challenge_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    setup_id INTEGER NOT NULL REFERENCES two_factor_setups(setup_id) ON DELETE CASCADE,
    method two_factor_method NOT NULL,
    code TEXT NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    attempts INTEGER DEFAULT 0,
    verified_at TIMESTAMP WITH TIME ZONE,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CHECK (attempts >= 0)
);

-- Recreate trusted devices
CREATE TABLE IF NOT EXISTS trusted_devices (
    device_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    device_fingerprint TEXT NOT NULL,
    device_name VARCHAR(255),
    last_used_at TIMESTAMP WITH TIME ZONE NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    CONSTRAINT unique_user_device UNIQUE (user_id, device_fingerprint)
);

-- ============================================
-- Step 2: Restore Columns to user_accounts
-- ============================================

ALTER TABLE user_accounts 
ADD COLUMN IF NOT EXISTS preferred_language VARCHAR(5) DEFAULT 'en' CHECK (preferred_language IN ('en', 'fr')),
ADD COLUMN IF NOT EXISTS language_auto_detect BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS language_source VARCHAR(20) DEFAULT 'auto' CHECK (language_source IN ('auto', 'manual', 'browser', 'system')),
ADD COLUMN IF NOT EXISTS language_updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS quebec_french_preference BOOLEAN DEFAULT true;

-- ============================================
-- Step 3: Migrate Data Back
-- ============================================

-- Restore language preferences to user_accounts
UPDATE user_accounts ua
SET 
    preferred_language = p.preferred_language,
    quebec_french_preference = p.quebec_french_preference
FROM user_preferences p
WHERE ua.user_id = p.user_id;

-- Restore formatting preferences
INSERT INTO user_formatting_preferences (
    user_id, date_format, time_format, currency_display, 
    number_precision, created_at, updated_at
)
SELECT 
    user_id, date_format, time_format, currency_display,
    number_precision, created_at, updated_at
FROM user_preferences
ON CONFLICT (user_id) DO NOTHING;

-- Restore notification preferences
INSERT INTO notification_preferences (user_id, notification_type, push_enabled, sms_enabled, email_enabled)
SELECT 
    p.user_id,
    nt.notification_type::notification_type,
    (p.notification_settings->nt.notification_type->>'push')::boolean,
    (p.notification_settings->nt.notification_type->>'sms')::boolean,
    (p.notification_settings->nt.notification_type->>'email')::boolean
FROM user_preferences p
CROSS JOIN (
    SELECT unnest(ARRAY['appointment_reminder', 'payment_due', 'session_confirmed', 'general']) as notification_type
) nt
WHERE p.notification_settings ? nt.notification_type
ON CONFLICT (user_id, notification_type) DO NOTHING;

-- Restore pinned actions
INSERT INTO user_pinned_actions (user_id, action_type, pinned_at)
SELECT 
    user_id,
    unnest(pinned_actions),
    created_at
FROM user_preferences
WHERE pinned_actions IS NOT NULL AND array_length(pinned_actions, 1) > 0
ON CONFLICT DO NOTHING;

-- Restore auth tokens to original tables
-- Password reset tokens
INSERT INTO password_reset_tokens (user_id, token, expires_at, initiated_by, initiated_by_user_id, created_at)
SELECT 
    user_id,
    token_hash::uuid,
    expires_at,
    COALESCE(metadata->>'initiated_by', 'user'),
    (metadata->>'initiated_by_user_id')::integer,
    created_at
FROM auth_tokens
WHERE token_type = 'password_reset' AND used_at IS NULL
ON CONFLICT DO NOTHING;

-- Email verification tokens
INSERT INTO email_verification_tokens (user_id, email, token, expires_at, ip_address, user_agent, created_at)
SELECT 
    user_id,
    COALESCE(metadata->>'email', (SELECT email FROM user_accounts WHERE user_accounts.user_id = auth_tokens.user_id)),
    token_hash::uuid,
    expires_at,
    ip_address,
    user_agent,
    created_at
FROM auth_tokens
WHERE token_type = 'email_verify' AND used_at IS NULL
ON CONFLICT DO NOTHING;

-- User sessions
INSERT INTO user_sessions (session_id, user_id, token_hash, role_type, ip_address, user_agent, expires_at, last_activity, created_at)
SELECT 
    COALESCE((metadata->>'session_id')::uuid, uuid_generate_v4()),
    user_id,
    token_hash,
    COALESCE((metadata->>'role_type')::user_role_type, 'client'),
    ip_address,
    user_agent,
    expires_at,
    COALESCE((metadata->>'last_activity')::timestamp with time zone, created_at),
    created_at
FROM auth_tokens
WHERE token_type = 'session' AND used_at IS NULL
ON CONFLICT DO NOTHING;

-- Restore 2FA settings
INSERT INTO two_factor_setups (
    user_id, method, secret, is_enabled, verified_at, created_at, updated_at
)
SELECT 
    user_id,
    COALESCE(two_factor_method::two_factor_method, 'totp'),
    two_factor_secret,
    two_factor_enabled,
    two_factor_verified_at,
    created_at,
    updated_at
FROM user_security
WHERE two_factor_enabled = true
ON CONFLICT DO NOTHING;

-- Restore trusted devices
INSERT INTO trusted_devices (
    user_id, device_fingerprint, device_name, last_used_at, 
    expires_at, ip_address, user_agent, created_at
)
SELECT 
    s.user_id,
    td->>'device_fingerprint',
    td->>'device_name',
    (td->>'last_used_at')::timestamp with time zone,
    (td->>'expires_at')::timestamp with time zone,
    (td->>'ip_address')::inet,
    td->>'user_agent',
    s.created_at
FROM user_security s
CROSS JOIN LATERAL jsonb_array_elements(s.trusted_devices) AS td
WHERE s.trusted_devices IS NOT NULL AND jsonb_array_length(s.trusted_devices) > 0
ON CONFLICT DO NOTHING;

-- ============================================
-- Step 4: Recreate Indexes
-- ============================================

-- All original indexes
CREATE INDEX IF NOT EXISTS idx_language_history_user_id ON user_language_preferences_history(user_id);
CREATE INDEX IF NOT EXISTS idx_language_history_created_at ON user_language_preferences_history(created_at);
CREATE INDEX IF NOT EXISTS idx_language_history_change_source ON user_language_preferences_history(change_source);
CREATE INDEX IF NOT EXISTS idx_language_analytics_date ON language_usage_analytics(date_recorded);
CREATE INDEX IF NOT EXISTS idx_language_analytics_language ON language_usage_analytics(language_code);
CREATE INDEX IF NOT EXISTS idx_formatting_preferences_user_id ON user_formatting_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_formatting_preferences_updated_at ON user_formatting_preferences(updated_at);
CREATE INDEX IF NOT EXISTS idx_formatting_preferences_history_user_id ON user_formatting_preferences_history(user_id);
CREATE INDEX IF NOT EXISTS idx_formatting_preferences_history_created_at ON user_formatting_preferences_history(created_at);
CREATE INDEX IF NOT EXISTS idx_formatting_preferences_history_field_name ON user_formatting_preferences_history(field_name);
CREATE INDEX IF NOT EXISTS idx_quick_action_usage_user_id ON quick_action_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_quick_action_usage_action_type ON quick_action_usage(action_type);
CREATE INDEX IF NOT EXISTS idx_quick_action_usage_executed_at ON quick_action_usage(executed_at DESC);
CREATE INDEX IF NOT EXISTS idx_quick_action_usage_success ON quick_action_usage(success);
CREATE INDEX IF NOT EXISTS idx_quick_action_analytics ON quick_action_usage(action_type, executed_at DESC, success);
CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_token ON password_reset_tokens(token) WHERE used_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_expires_at ON password_reset_tokens(expires_at) WHERE used_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_email_verification_tokens_user_id ON email_verification_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_email_verification_tokens_token ON email_verification_tokens(token);
CREATE INDEX IF NOT EXISTS idx_email_verification_tokens_email ON email_verification_tokens(email);
CREATE INDEX IF NOT EXISTS idx_email_verification_tokens_expires_at ON email_verification_tokens(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token_hash ON user_sessions(token_hash) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_user_sessions_last_activity ON user_sessions(last_activity) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_2fa_setup_user ON two_factor_setups(user_id) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_2fa_setup_enabled ON two_factor_setups(user_id, is_enabled) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_2fa_setup_primary ON two_factor_setups(user_id, is_primary) WHERE deleted_at IS NULL AND is_enabled = true;
CREATE INDEX IF NOT EXISTS idx_challenge_user_expires ON two_factor_challenges(user_id, expires_at);
CREATE INDEX IF NOT EXISTS idx_challenge_verified ON two_factor_challenges(verified_at);
CREATE INDEX IF NOT EXISTS idx_device_user_expires ON trusted_devices(user_id, expires_at);
CREATE INDEX IF NOT EXISTS idx_device_fingerprint ON trusted_devices(device_fingerprint);

-- ============================================
-- Step 5: Recreate Triggers
-- ============================================

CREATE TRIGGER IF NOT EXISTS update_formatting_preferences_updated_at 
BEFORE UPDATE ON user_formatting_preferences
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER IF NOT EXISTS update_notification_preferences_updated_at 
BEFORE UPDATE ON notification_preferences
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER IF NOT EXISTS update_quick_action_usage_updated_at 
BEFORE UPDATE ON quick_action_usage
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER IF NOT EXISTS update_email_verification_tokens_updated_at 
BEFORE UPDATE ON email_verification_tokens
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER IF NOT EXISTS update_user_sessions_updated_at 
BEFORE UPDATE ON user_sessions
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER IF NOT EXISTS update_two_factor_setups_updated_at 
BEFORE UPDATE ON two_factor_setups
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER IF NOT EXISTS update_two_factor_challenges_updated_at 
BEFORE UPDATE ON two_factor_challenges
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER IF NOT EXISTS update_trusted_devices_updated_at 
BEFORE UPDATE ON trusted_devices
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================
-- Step 6: Drop Simplified Tables
-- ============================================

DROP TABLE IF EXISTS user_preferences CASCADE;
DROP TABLE IF EXISTS auth_tokens CASCADE;
DROP TABLE IF EXISTS user_security CASCADE;

-- Drop helper functions
DROP FUNCTION IF EXISTS cleanup_expired_tokens();
DROP FUNCTION IF EXISTS has_valid_session(INTEGER, VARCHAR);

-- ============================================
-- Step 7: Recreate Original Views
-- ============================================

DROP VIEW IF EXISTS users CASCADE;

CREATE OR REPLACE VIEW users AS
SELECT 
    user_id,
    email,
    password_hash,
    google_id,
    created_at,
    updated_at,
    deleted_at,
    preferred_language,
    language_auto_detect,
    language_source,
    language_updated_at,
    quebec_french_preference,
    email_verified,
    email_verified_at
FROM user_accounts;