"""
Models for service frequency and duration configuration.
"""

from datetime import time
from typing import List, Optional, Dict, Any
from enum import Enum
from pydantic import BaseModel, Field, field_validator

from app.models.base import BaseEntity


class SessionFrequency(str, Enum):
    """Frequency options for tutoring sessions."""
    ONCE = "once"
    WEEKLY = "weekly"
    BIWEEKLY = "biweekly"
    MONTHLY = "monthly"
    CUSTOM = "custom"


class DurationOption(str, Enum):
    """Standard duration options in minutes."""
    THIRTY_MIN = "30"
    FORTY_FIVE_MIN = "45"
    SIXTY_MIN = "60"
    SEVENTY_FIVE_MIN = "75"
    NINETY_MIN = "90"
    ONE_TWENTY_MIN = "120"
    ONE_FIFTY_MIN = "150"
    ONE_EIGHTY_MIN = "180"
    TWO_FORTY_MIN = "240"
    
    @property
    def minutes(self) -> int:
        return int(self.value)
    
    @property
    def display_name(self) -> str:
        hours = self.minutes // 60
        mins = self.minutes % 60
        
        if hours == 0:
            return f"{mins} minutes"
        elif mins == 0:
            return f"{hours} hour{'s' if hours > 1 else ''}"
        else:
            return f"{hours} hour{'s' if hours > 1 else ''} {mins} minutes"


class TimeSlotPreference(str, Enum):
    """Preferred time slots for sessions."""
    EARLY_MORNING = "early_morning"     # 6:00 - 9:00
    MORNING = "morning"                  # 9:00 - 12:00
    EARLY_AFTERNOON = "early_afternoon" # 12:00 - 15:00
    AFTERNOON = "afternoon"              # 15:00 - 18:00
    EVENING = "evening"                  # 18:00 - 21:00
    LATE_EVENING = "late_evening"       # 21:00 - 22:00
    
    @property
    def time_range(self) -> tuple[time, time]:
        ranges = {
            "early_morning": (time(6, 0), time(9, 0)),
            "morning": (time(9, 0), time(12, 0)),
            "early_afternoon": (time(12, 0), time(15, 0)),
            "afternoon": (time(15, 0), time(18, 0)),
            "evening": (time(18, 0), time(21, 0)),
            "late_evening": (time(21, 0), time(22, 0))
        }
        return ranges[self.value]


class DayOfWeekPreference(str, Enum):
    """Days of the week for scheduling preferences."""
    MONDAY = "monday"
    TUESDAY = "tuesday"
    WEDNESDAY = "wednesday"
    THURSDAY = "thursday"
    FRIDAY = "friday"
    SATURDAY = "saturday"
    SUNDAY = "sunday"
    
    @property
    def weekday_number(self) -> int:
        """Return Python weekday number (0=Monday, 6=Sunday)."""
        days = {
            "monday": 0,
            "tuesday": 1,
            "wednesday": 2,
            "thursday": 3,
            "friday": 4,
            "saturday": 5,
            "sunday": 6
        }
        return days[self.value]


# Service Configuration Models
class ServiceFrequencyConfiguration(BaseModel):
    """Configuration for service frequency options."""
    service_catalog_id: int = Field(..., gt=0)
    
    # Available frequencies
    available_frequencies: List[SessionFrequency] = Field(
        default=[SessionFrequency.ONCE, SessionFrequency.WEEKLY]
    )
    default_frequency: SessionFrequency = SessionFrequency.ONCE
    
    # Recurring session settings
    min_sessions_per_month: Optional[int] = Field(None, ge=1, le=20)
    max_sessions_per_month: Optional[int] = Field(None, ge=1, le=30)
    
    # Custom frequency settings
    allow_custom_frequency: bool = False
    custom_frequency_rules: Optional[Dict[str, Any]] = None
    
    @field_validator('max_sessions_per_month')
    @classmethod
    def validate_session_range(cls, v, info):
        if v and 'min_sessions_per_month' in values:
            if info.data['min_sessions_per_month'] and v < info.data['min_sessions_per_month']:
                raise ValueError('max_sessions_per_month must be >= min_sessions_per_month')
        return v


class ServiceDurationConfiguration(BaseModel):
    """Configuration for service duration options."""
    service_catalog_id: int = Field(..., gt=0)
    
    # Available durations
    available_durations: List[DurationOption] = Field(
        default=[DurationOption.SIXTY_MIN, DurationOption.NINETY_MIN]
    )
    default_duration: DurationOption = DurationOption.SIXTY_MIN
    
    # Custom duration settings
    allow_custom_duration: bool = False
    min_custom_duration_minutes: Optional[int] = Field(None, ge=15, le=480)
    max_custom_duration_minutes: Optional[int] = Field(None, ge=15, le=480)
    
    # Duration modifiers
    allow_extension: bool = True
    extension_increment_minutes: int = Field(default=15, ge=15, le=60)
    max_extensions_per_session: int = Field(default=2, ge=1, le=4)
    
    @field_validator('available_durations')
    def validate_durations_sorted(cls, v):
        """Ensure durations are sorted from shortest to longest."""
        return sorted(v, key=lambda d: d.minutes)
    
    @field_validator('max_custom_duration_minutes')
    @classmethod
    def validate_custom_duration_range(cls, v, info):
        if v and 'min_custom_duration_minutes' in values:
            if info.data['min_custom_duration_minutes'] and v < info.data['min_custom_duration_minutes']:
                raise ValueError('max_custom_duration must be >= min_custom_duration')
        return v


class SchedulingPreferences(BaseModel):
    """User/Client scheduling preferences."""
    user_id: Optional[int] = Field(None, gt=0)
    client_id: Optional[int] = Field(None, gt=0)
    tutor_id: Optional[int] = Field(None, gt=0)
    
    # Frequency preferences
    preferred_frequency: SessionFrequency = SessionFrequency.WEEKLY
    sessions_per_week: Optional[int] = Field(None, ge=1, le=7)
    sessions_per_month: Optional[int] = Field(None, ge=1, le=30)
    
    # Duration preferences
    preferred_duration: DurationOption = DurationOption.SIXTY_MIN
    min_acceptable_duration: Optional[DurationOption] = None
    max_acceptable_duration: Optional[DurationOption] = None
    
    # Time preferences
    preferred_time_slots: List[TimeSlotPreference] = []
    preferred_days: List[DayOfWeekPreference] = []
    
    # Availability windows
    earliest_start_time: time = Field(default=time(8, 0))
    latest_end_time: time = Field(default=time(21, 0))
    
    # Flexibility
    is_flexible_on_time: bool = True
    is_flexible_on_duration: bool = False
    is_flexible_on_frequency: bool = True
    
    # Notes
    scheduling_notes: Optional[str] = Field(None, max_length=500)
    
    @field_validator('sessions_per_week', 'sessions_per_month')
    @classmethod
    def validate_session_counts(cls, v, info):
        if 'preferred_frequency' in info.data:
            freq = info.data['preferred_frequency']
            if freq == SessionFrequency.ONCE and v and v > 1:
                raise ValueError('Cannot have multiple sessions with ONCE frequency')
        return v
    
    @field_validator('latest_end_time')
    @classmethod
    def validate_time_range(cls, v, info):
        if 'earliest_start_time' in info.data and v <= info.data['earliest_start_time']:
            raise ValueError('latest_end_time must be after earliest_start_time')
        return v


class RecurringSchedulePattern(BaseModel):
    """Pattern for recurring appointments."""
    pattern_name: str = Field(..., min_length=1, max_length=100)
    frequency: SessionFrequency
    
    # Weekly pattern
    days_of_week: List[DayOfWeekPreference] = []
    
    # Monthly pattern
    day_of_month: Optional[int] = Field(None, ge=1, le=31)
    week_of_month: Optional[int] = Field(None, ge=1, le=5)  # 5 = last week
    
    # Time settings
    start_time: time
    duration: DurationOption
    
    # Validity
    start_date: date
    end_date: Optional[date] = None
    total_sessions: Optional[int] = Field(None, gt=0)
    
    # Flexibility
    allow_reschedule: bool = True
    require_confirmation: bool = False
    auto_confirm_hours_before: Optional[int] = Field(None, ge=1, le=72)
    
    @field_validator('days_of_week')
    @classmethod
    def validate_weekly_pattern(cls, v, info):
        if 'frequency' in info.data and info.data['frequency'] == SessionFrequency.WEEKLY:
            if not v:
                raise ValueError('Weekly frequency requires at least one day of week')
        return v
    
    @field_validator('day_of_month', 'week_of_month')
    @classmethod
    def validate_monthly_pattern(cls, v, info):
        if 'frequency' in info.data and info.data['frequency'] == SessionFrequency.MONTHLY:
            if not v and not info.data.get('week_of_month'):
                raise ValueError('Monthly frequency requires day_of_month or week_of_month')
        return v


# Request/Response Models
class UpdateSchedulingPreferencesRequest(BaseModel):
    """Request to update scheduling preferences."""
    preferred_frequency: Optional[SessionFrequency] = None
    sessions_per_week: Optional[int] = Field(None, ge=1, le=7)
    sessions_per_month: Optional[int] = Field(None, ge=1, le=30)
    preferred_duration: Optional[DurationOption] = None
    preferred_time_slots: Optional[List[TimeSlotPreference]] = None
    preferred_days: Optional[List[DayOfWeekPreference]] = None
    earliest_start_time: Optional[time] = None
    latest_end_time: Optional[time] = None
    is_flexible_on_time: Optional[bool] = None
    is_flexible_on_duration: Optional[bool] = None
    is_flexible_on_frequency: Optional[bool] = None
    scheduling_notes: Optional[str] = Field(None, max_length=500)


class SchedulingPreferencesResponse(SchedulingPreferences, TimestampedModel):
    """Response model for scheduling preferences."""
    scheduling_preference_id: int
    
    class Config:
        from_attributes = True


class CreateRecurringPatternRequest(RecurringSchedulePattern):
    """Request to create a recurring schedule pattern."""
    client_id: int = Field(..., gt=0)
    tutor_id: int = Field(..., gt=0)
    service_catalog_id: int = Field(..., gt=0)


class RecurringPatternResponse(RecurringSchedulePattern, TimestampedModel):
    """Response model for recurring patterns."""
    recurring_pattern_id: int
    client_id: int
    tutor_id: int
    service_catalog_id: int
    created_by: int
    
    # Statistics
    sessions_completed: int = 0
    sessions_remaining: Optional[int] = None
    next_session_date: Optional[date] = None
    
    class Config:
        from_attributes = True


class AvailableScheduleOptionsResponse(BaseModel):
    """Response model for available scheduling options."""
    service_catalog_id: int
    service_name: str
    
    # Frequency options
    available_frequencies: List[SessionFrequency]
    default_frequency: SessionFrequency
    
    # Duration options
    available_durations: List[Dict[str, Any]]  # {value, minutes, display_name}
    default_duration: DurationOption
    
    # Time slot suggestions
    suggested_time_slots: List[TimeSlotPreference]
    popular_days: List[DayOfWeekPreference]
    
    # Constraints
    min_duration_minutes: int
    max_duration_minutes: int
    earliest_start_time: time
    latest_end_time: time
    
    # Pricing impact
    duration_pricing: Dict[str, float]  # duration -> price multiplier
    frequency_discounts: Dict[str, float]  # frequency -> discount percentage


class ScheduleOptimizationRequest(BaseModel):
    """Request for schedule optimization suggestions."""
    client_id: int = Field(..., gt=0)
    subject_areas: List[str]
    total_hours_per_week: float = Field(..., gt=0, le=20)
    preferred_tutors: Optional[List[int]] = None
    constraints: Optional[Dict[str, Any]] = None
    optimize_for: str = Field(default="consistency", pattern="^(consistency|flexibility|cost)$")


class ScheduleOptimizationResponse(BaseModel):
    """Response with optimized schedule suggestions."""
    optimization_score: float = Field(..., ge=0, le=1)
    suggested_patterns: List[RecurringSchedulePattern]
    estimated_monthly_cost: float
    time_distribution: Dict[str, float]  # subject -> hours per week
    conflict_warnings: List[str] = []
    alternative_options: List[Dict[str, Any]] = []