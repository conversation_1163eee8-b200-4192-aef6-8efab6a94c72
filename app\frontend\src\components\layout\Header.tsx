import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Bell,
  ChevronDown,
  User,
  LogOut,
  Settings,
  HelpCircle,
  GraduationCap,
  Search,
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { UserRoleType } from '../../types/auth';
import { useTranslation } from '../../hooks/useTranslation';
import { LanguageSwitcher } from '../common/LanguageSwitcher';
import { QuickActions } from '../search/QuickActions';
import * as DropdownMenu from '@radix-ui/react-dropdown-menu';
import clsx from 'clsx';

interface HeaderProps {
  onSearchClick?: () => void;
}

export const Header: React.FC<HeaderProps> = ({ onSearchClick }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { user, logout, switchRole } = useAuth();
  const [notificationCount] = useState(3);

  const handleRoleSwitch = async (role: UserRoleType) => {
    await switchRole(role);
    navigate('/dashboard');
  };

  const roleLabels: Record<UserRoleType, string> = {
    [UserRoleType.MANAGER]: t('roles.manager'),
    [UserRoleType.TUTOR]: t('roles.tutor'),
    [UserRoleType.CLIENT]: t('roles.client'),
  };

  // Global keyboard shortcut for search
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        onSearchClick?.();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [onSearchClick]);

  return (
    <header className="fixed top-0 right-0 left-0 lg:left-72 h-16 bg-white/95 backdrop-blur-sm border-b border-border-primary z-30">
        <div className="h-full px-6 flex items-center justify-between">
          {/* Left side - Search and Quick Actions */}
          <div className="flex items-center gap-4">
            {/* Global Search Button */}
            <button
              onClick={onSearchClick}
              className="flex items-center gap-2 px-3 py-2 bg-background-secondary hover:bg-background-tertiary rounded-xl transition-all group"
            >
              <Search className="w-4 h-4 text-text-secondary group-hover:text-text-primary" />
              <span className="text-sm text-text-secondary group-hover:text-text-primary hidden sm:inline">
                {t('search.searchButton')}
              </span>
              <kbd className="hidden sm:inline-flex items-center gap-1 px-2 py-0.5 text-xs bg-white rounded-lg border border-border-primary">
                <span className="text-xs">⌘</span>K
              </kbd>
            </button>

            {/* Quick Actions */}
            <QuickActions />
          </div>

        {/* Right side actions */}
        <div className="flex items-center gap-4">
          {/* Language switcher */}
          <LanguageSwitcher variant="compact" className="hidden sm:flex" />

          {/* Notifications */}
          <button className="relative p-2 hover:bg-background-secondary rounded-lg transition-all">
            <Bell className="w-5 h-5 text-text-secondary" />
            {notificationCount > 0 && (
              <span className="absolute top-1 right-1 w-2 h-2 bg-accent-red rounded-full animate-pulse" />
            )}
          </button>

          {/* User menu */}
          <DropdownMenu.Root>
            <DropdownMenu.Trigger asChild>
              <button className="flex items-center gap-2 p-2 hover:bg-background-secondary rounded-lg transition-all">
                <div className="w-8 h-8 bg-accent-red rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">
                    {user?.firstName?.[0]?.toUpperCase()}
                  </span>
                </div>
                <div className="text-left hidden md:block">
                  <p className="text-sm font-medium text-text-primary">
                    {user?.firstName} {user?.lastName}
                  </p>
                  <p className="text-xs text-text-secondary">
                    {roleLabels[user?.activeRole as UserRoleType]}
                  </p>
                </div>
                <ChevronDown className="w-4 h-4 text-text-secondary" />
              </button>
            </DropdownMenu.Trigger>

            <DropdownMenu.Portal>
              <DropdownMenu.Content
                className="min-w-[220px] bg-white rounded-lg p-1 shadow-soft animate-scale-in border border-border-primary"
                sideOffset={5}
              >
                {/* User info */}
                <div className="px-3 py-2 border-b border-border-primary">
                  <p className="text-sm font-medium text-text-primary">
                    {user?.email}
                  </p>
                  <p className="text-xs text-text-secondary mt-1">
                    {t('header.activeRole')}: {roleLabels[user?.activeRole as UserRoleType]}
                  </p>
                </div>

                {/* Role switcher */}
                {(() => {
                  console.log('Header - User object:', user);
                  console.log('Header - User roles:', user?.roles);
                  console.log('Header - Roles length:', user?.roles?.length);
                  console.log('Header - Should show role switcher:', user?.roles && user.roles.length > 1);
                  return user?.roles && user.roles.length > 1;
                })() && (
                  <>
                    <DropdownMenu.Label className="px-3 py-2 text-xs font-medium text-text-secondary">
                      {t('header.switchRole')}
                    </DropdownMenu.Label>
                    {user.roles.map((role) => (
                      <DropdownMenu.Item
                        key={role}
                        className={clsx(
                          'flex items-center justify-between px-3 py-2 text-sm hover:bg-background-secondary rounded-md cursor-pointer transition-colors',
                          role === user.activeRole
                            ? 'text-accent-red font-medium'
                            : 'text-text-primary'
                        )}
                        onSelect={() => handleRoleSwitch(role)}
                      >
                        {roleLabels[role]}
                        {role === user.activeRole && (
                          <span className="w-2 h-2 bg-accent-red rounded-full" />
                        )}
                      </DropdownMenu.Item>
                    ))}
                    <DropdownMenu.Separator className="h-px bg-border-primary my-1" />
                  </>
                )}

                {/* Menu items */}
                <DropdownMenu.Item
                  className="flex items-center gap-2 px-3 py-2 text-sm text-text-primary hover:bg-background-secondary rounded-md cursor-pointer transition-colors"
                  onSelect={() => navigate('/profile')}
                >
                  <User className="w-4 h-4" />
                  {t('header.profile')}
                </DropdownMenu.Item>
                <DropdownMenu.Item
                  className="flex items-center gap-2 px-3 py-2 text-sm text-text-primary hover:bg-background-secondary rounded-md cursor-pointer transition-colors"
                  onSelect={() => navigate('/settings')}
                >
                  <Settings className="w-4 h-4" />
                  {t('header.settings')}
                </DropdownMenu.Item>
                <DropdownMenu.Item
                  className="flex items-center gap-2 px-3 py-2 text-sm text-text-primary hover:bg-background-secondary rounded-md cursor-pointer transition-colors"
                  onSelect={() => navigate('/help')}
                >
                  <HelpCircle className="w-4 h-4" />
                  {t('header.help')}
                </DropdownMenu.Item>

                <DropdownMenu.Separator className="h-px bg-border-primary my-1" />

                <DropdownMenu.Item
                  className="flex items-center gap-2 px-3 py-2 text-sm text-semantic-error hover:bg-red-50 rounded-md cursor-pointer transition-colors"
                  onSelect={logout}
                >
                  <LogOut className="w-4 h-4" />
                  {t('header.logout')}
                </DropdownMenu.Item>
              </DropdownMenu.Content>
            </DropdownMenu.Portal>
          </DropdownMenu.Root>
        </div>
      </div>
    </header>
  );
};