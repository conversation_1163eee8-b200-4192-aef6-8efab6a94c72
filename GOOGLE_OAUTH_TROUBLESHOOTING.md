# Google OAuth Troubleshooting Guide

## Common Error: "deleted_client"

If you're seeing "The OAuth client was deleted" error, follow these steps:

### 1. Verify Current Configuration

Visit the debug endpoint to see what credentials are being used:
```
https://tutoraide-production.up.railway.app/api/v1/auth/google/debug
```

This will show:
- Current OAuth client ID being used
- Whether credentials are properly loaded
- A test OAuth URL to verify the client ID

### 2. Check Railway Environment Variables

1. Go to your Railway project dashboard
2. Navigate to the Variables tab
3. Verify these environment variables are set:
   - `GOOGLE_CLIENT_ID` - Your new OAuth client ID
   - `GOOGLE_CLIENT_SECRET` - Your new OAuth client secret
   - `GOOGLE_REDIRECT_URI` - Should be: `https://tutoraide-production.up.railway.app/api/v1/auth/google/callback`
   - `FRONTEND_URL` - Should be: `https://tutoraide-production.up.railway.app`

### 3. Force Redeploy in Railway

After updating environment variables:
1. Go to your Railway project
2. Click on the deployment
3. Click "Redeploy" button (or trigger a new deployment from GitHub)
4. Wait for the deployment to complete

### 4. Google Cloud Console Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/apis/credentials)
2. Create a new OAuth 2.0 Client ID (Web application)
3. Configure it with:
   - **Authorized JavaScript origins:**
     - `https://tutoraide-production.up.railway.app`
     - `https://www.tutoraide.ca` (if using custom domain)
   - **Authorized redirect URIs:**
     - `https://tutoraide-production.up.railway.app/api/v1/auth/google/callback`
     - `https://www.tutoraide.ca/api/v1/auth/google/callback` (if using custom domain)

### 5. OAuth Consent Screen Configuration

1. In Google Cloud Console, go to "OAuth consent screen"
2. Fill in required fields:
   - App name: TutorAide
   - User support email: <EMAIL>
   - Privacy policy: `https://tutoraide-production.up.railway.app/privacy`
   - Terms of service: (optional)
   - Authorized domains: `tutoraide-production.up.railway.app` and `tutoraide.ca`
3. Save the configuration

### 6. Testing the Fix

1. Visit the debug endpoint again to confirm new credentials are loaded
2. Click on the `test_oauth_url` from the debug response
3. Check the URL in your browser - it should contain your new client_id
4. Try the "Continue with Google" button on your login page

### 7. If Still Not Working

If you're still seeing "deleted_client":

1. **Clear browser cache and cookies** for your domain
2. **Check deployment logs** in Railway for any errors
3. **Verify the client ID** in the OAuth URL matches exactly what's in Google Cloud Console
4. **Ensure no spaces or quotes** in environment variables
5. **Try incognito/private browsing** to rule out cache issues

### Common Mistakes to Avoid

- ❌ Copying client ID with extra spaces or quotes
- ❌ Using localhost URLs in production
- ❌ Forgetting to redeploy after updating environment variables
- ❌ Mixing up client ID and client secret
- ❌ Not saving OAuth consent screen configuration

### Environment Variable Format

Make sure your Railway environment variables look exactly like this (no quotes):
```
GOOGLE_CLIENT_ID=123456789012-abcdefghijklmnopqrstuvwxyz123456.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=GOCSPX-1234567890abcdefghijklmno
GOOGLE_REDIRECT_URI=https://tutoraide-production.up.railway.app/api/v1/auth/google/callback
FRONTEND_URL=https://tutoraide-production.up.railway.app
```

### Need More Help?

1. Check Railway deployment logs for specific errors
2. Visit the debug endpoint to see current configuration
3. Test with the generated OAuth URL from debug endpoint
4. Verify all URLs match between Google Console and your app