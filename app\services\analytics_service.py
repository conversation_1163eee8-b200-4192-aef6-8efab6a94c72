"""
Analytics service for performance tracking and reporting.

Handles aggregation and calculation of analytics metrics for tutors,
students, and platform-wide performance tracking.
"""

from datetime import date, datetime, timedelta
from decimal import Decimal
from typing import List, Optional, Dict, Any, Tuple
import asyncio
from collections import defaultdict

from app.database.repositories.analytics_repository import (
    TutorPerformanceRepository, StudentProgressRepository,
    SessionFeedbackRepository, PlatformAnalyticsRepository,
    AnalyticsSnapshotRepository
)
from app.database.repositories.appointment_repository import AppointmentRepository
from app.database.repositories.payment_repository import PaymentRepository
from app.database.repositories.user_repository import UserRepository
from app.models.analytics_models import (
    TutorPerformanceMetrics, StudentLearningGoal, StudentProgressMetrics,
    PlatformAnalytics, SessionFeedback, AnalyticsSnapshot,
    PeriodType, GoalStatus, FeedbackRole, MetricType,
    TutorLeaderboardEntry, StudentProgressSummary,
    TutorDashboardData, StudentDashboardData, PlatformDashboardData,
    SessionFeedbackCreate
)
from app.models.appointment_models import AppointmentStatus
from app.core.exceptions import (
    ResourceNotFoundError, ValidationError, BusinessLogicError
)
from app.core.logging import TutorAideLogger
from app.config.database import DatabaseManager

logger = TutorAideLogger(__name__)


class AnalyticsService:
    """Service for analytics and performance tracking."""
    
    def __init__(self, db_manager: DatabaseManager):
        """Initialize analytics service."""
        self.db_manager = db_manager
        self.tutor_perf_repo = TutorPerformanceRepository()
        self.student_progress_repo = StudentProgressRepository()
        self.feedback_repo = SessionFeedbackRepository()
        self.platform_repo = PlatformAnalyticsRepository()
        self.snapshot_repo = AnalyticsSnapshotRepository()
        self.appointment_repo = AppointmentRepository()
        self.payment_repo = PaymentRepository("billing_tutor_payments")
        self.user_repo = UserRepository()
    
    # =====================================================
    # TUTOR PERFORMANCE METHODS
    # =====================================================
    
    async def calculate_tutor_metrics(
        self,
        tutor_id: int,
        period_type: PeriodType,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> TutorPerformanceMetrics:
        """Calculate and update tutor performance metrics."""
        try:
            async with self.db_manager.acquire() as conn:
                # Determine period dates if not provided
                if not start_date or not end_date:
                    start_date, end_date = self._get_period_dates(period_type)
                
                # Get or create metrics record
                metrics = await self.tutor_perf_repo.get_or_create_metrics(
                    conn, tutor_id, start_date, end_date, period_type
                )
                
                # Calculate session metrics
                session_data = await self._calculate_session_metrics(
                    conn, tutor_id, start_date, end_date
                )
                metrics = await self.tutor_perf_repo.update_session_metrics(
                    conn, tutor_id, start_date, end_date, period_type, session_data
                )
                
                # Calculate rating metrics
                rating_data = await self._calculate_rating_metrics(
                    conn, tutor_id, start_date, end_date
                )
                metrics = await self.tutor_perf_repo.update_rating_metrics(
                    conn, tutor_id, start_date, end_date, period_type, rating_data
                )
                
                # Calculate financial metrics
                financial_data = await self._calculate_financial_metrics(
                    conn, tutor_id, start_date, end_date
                )
                metrics = await self.tutor_perf_repo.update_financial_metrics(
                    conn, tutor_id, start_date, end_date, period_type, financial_data
                )
                
                # Save snapshot for caching
                await self._save_metrics_snapshot(
                    conn, 'tutor_performance', tutor_id, metrics
                )
                
                logger.info(f"Calculated metrics for tutor {tutor_id} ({period_type.value})")
                return metrics
                
        except Exception as e:
            logger.error(f"Error calculating tutor metrics: {e}")
            raise BusinessLogicError(f"Failed to calculate tutor metrics: {str(e)}")
    
    async def get_tutor_dashboard(self, tutor_id: int) -> TutorDashboardData:
        """Get complete dashboard data for a tutor."""
        try:
            async with self.db_manager.acquire() as conn:
                # Get current month metrics
                metrics = await self.calculate_tutor_metrics(
                    tutor_id, PeriodType.MONTHLY
                )
                
                # Get recent feedback
                feedback = await self.feedback_repo.get_feedback_for_user(
                    conn, tutor_id, 'given_to', limit=5
                )
                
                # Get subject performance breakdown
                subject_perf = await self._get_subject_performance(
                    conn, tutor_id, days=30
                )
                
                # Get student progress summaries
                student_progress = await self._get_tutor_student_progress(
                    conn, tutor_id, limit=10
                )
                
                # Get earnings trend
                earnings_trend = await self._get_earnings_trend(
                    conn, tutor_id, months=6
                )
                
                # Get upcoming session count
                upcoming = await self._count_upcoming_sessions(conn, tutor_id)
                
                # Get pending feedback count
                pending_feedback = await self._count_pending_feedback(conn, tutor_id)
                
                return TutorDashboardData(
                    performance_metrics=metrics,
                    recent_feedback=feedback,
                    subject_performance=subject_perf,
                    student_progress=student_progress,
                    earnings_trend=earnings_trend,
                    upcoming_sessions=upcoming,
                    pending_feedback=pending_feedback
                )
                
        except Exception as e:
            logger.error(f"Error getting tutor dashboard: {e}")
            raise BusinessLogicError(f"Failed to get tutor dashboard: {str(e)}")
    
    async def get_tutor_leaderboard(
        self,
        period_type: PeriodType = PeriodType.MONTHLY,
        limit: int = 10
    ) -> List[TutorLeaderboardEntry]:
        """Get tutor leaderboard."""
        try:
            async with self.db_manager.acquire() as conn:
                return await self.tutor_perf_repo.get_leaderboard(
                    conn, period_type, limit
                )
        except Exception as e:
            logger.error(f"Error getting leaderboard: {e}")
            raise BusinessLogicError(f"Failed to get leaderboard: {str(e)}")
    
    # =====================================================
    # STUDENT PROGRESS METHODS
    # =====================================================
    
    async def create_learning_goal(
        self,
        student_id: int,
        goal_data: Dict[str, Any],
        created_by: int
    ) -> StudentLearningGoal:
        """Create a new learning goal for a student."""
        try:
            async with self.db_manager.acquire() as conn:
                goal_data['student_id'] = student_id
                goal_data['created_by'] = created_by
                
                goal = await self.student_progress_repo.create_learning_goal(
                    conn, goal_data
                )
                
                logger.info(f"Created learning goal {goal.goal_id} for student {student_id}")
                return goal
                
        except Exception as e:
            logger.error(f"Error creating learning goal: {e}")
            raise BusinessLogicError(f"Failed to create learning goal: {str(e)}")
    
    async def update_goal_progress(
        self,
        goal_id: int,
        progress_percentage: int,
        mark_complete: bool = False
    ) -> StudentLearningGoal:
        """Update learning goal progress."""
        try:
            async with self.db_manager.acquire() as conn:
                status = GoalStatus.COMPLETED if mark_complete else None
                
                goal = await self.student_progress_repo.update_goal_progress(
                    conn, goal_id, progress_percentage, status
                )
                
                logger.info(f"Updated goal {goal_id} progress to {progress_percentage}%")
                return goal
                
        except Exception as e:
            logger.error(f"Error updating goal progress: {e}")
            if isinstance(e, ResourceNotFoundError):
                raise
            raise BusinessLogicError(f"Failed to update goal progress: {str(e)}")
    
    async def calculate_student_progress(
        self,
        student_id: int,
        subject_area: str,
        start_date: date,
        end_date: date
    ) -> StudentProgressMetrics:
        """Calculate student progress metrics."""
        try:
            async with self.db_manager.acquire() as conn:
                # Get existing or create new metrics
                metrics_data = await self._calculate_student_metrics(
                    conn, student_id, subject_area, start_date, end_date
                )
                
                # Save or update metrics
                query = """
                    INSERT INTO student_progress_metrics (
                        student_id, subject_area, period_start, period_end,
                        total_sessions, attended_sessions, session_hours,
                        performance_score, improvement_rate, engagement_score,
                        milestones_achieved, milestones_total, avg_tutor_rating,
                        feedback_summary
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14::jsonb)
                    ON CONFLICT (student_id, subject_area, period_start, period_end)
                    DO UPDATE SET
                        total_sessions = EXCLUDED.total_sessions,
                        attended_sessions = EXCLUDED.attended_sessions,
                        session_hours = EXCLUDED.session_hours,
                        performance_score = EXCLUDED.performance_score,
                        improvement_rate = EXCLUDED.improvement_rate,
                        engagement_score = EXCLUDED.engagement_score,
                        milestones_achieved = EXCLUDED.milestones_achieved,
                        milestones_total = EXCLUDED.milestones_total,
                        avg_tutor_rating = EXCLUDED.avg_tutor_rating,
                        feedback_summary = EXCLUDED.feedback_summary,
                        updated_at = CURRENT_TIMESTAMP
                    RETURNING *
                """
                
                import json
                result = await conn.fetchrow(
                    query,
                    student_id, subject_area, start_date, end_date,
                    metrics_data['total_sessions'],
                    metrics_data['attended_sessions'],
                    metrics_data['session_hours'],
                    metrics_data.get('performance_score'),
                    metrics_data.get('improvement_rate'),
                    metrics_data.get('engagement_score'),
                    metrics_data['milestones_achieved'],
                    metrics_data['milestones_total'],
                    metrics_data.get('avg_tutor_rating'),
                    json.dumps(metrics_data.get('feedback_summary', {}))
                )
                
                return StudentProgressMetrics(**dict(result))
                
        except Exception as e:
            logger.error(f"Error calculating student progress: {e}")
            raise BusinessLogicError(f"Failed to calculate student progress: {str(e)}")
    
    async def get_student_dashboard(self, student_id: int) -> StudentDashboardData:
        """Get complete dashboard data for a student."""
        try:
            async with self.db_manager.acquire() as conn:
                # Get progress metrics for all subjects
                progress_metrics = await self._get_student_all_progress(
                    conn, student_id
                )
                
                # Get learning goals
                goals = await self.student_progress_repo.get_student_goals(
                    conn, student_id
                )
                
                # Get recent sessions
                recent_sessions = await self._get_recent_sessions(
                    conn, student_id, limit=5
                )
                
                # Get tutor feedback
                feedback = await self.feedback_repo.get_feedback_for_user(
                    conn, student_id, 'given_to', limit=5
                )
                
                # Get improvement trends
                trends = await self._get_improvement_trends(
                    conn, student_id
                )
                
                # Get next session
                next_session = await self._get_next_session(conn, student_id)
                
                return StudentDashboardData(
                    progress_metrics=progress_metrics,
                    learning_goals=goals,
                    recent_sessions=recent_sessions,
                    tutor_feedback=feedback,
                    improvement_trends=trends,
                    next_session=next_session
                )
                
        except Exception as e:
            logger.error(f"Error getting student dashboard: {e}")
            raise BusinessLogicError(f"Failed to get student dashboard: {str(e)}")
    
    # =====================================================
    # SESSION FEEDBACK METHODS
    # =====================================================
    
    async def submit_session_feedback(
        self,
        appointment_id: int,
        user_id: int,
        feedback_data: SessionFeedbackCreate
    ) -> SessionFeedback:
        """Submit feedback for a completed session."""
        try:
            async with self.db_manager.acquire() as conn:
                # Get appointment details
                appointment = await self.appointment_repo.find_by_id(
                    conn, appointment_id
                )
                if not appointment:
                    raise ResourceNotFoundError(f"Appointment {appointment_id} not found")
                
                # Verify appointment is completed
                if appointment['status'] != AppointmentStatus.COMPLETED.value:
                    raise ValidationError("Feedback can only be submitted for completed sessions")
                
                # Determine role and recipient
                role_type, given_to = await self._determine_feedback_role(
                    conn, appointment, user_id
                )
                
                # Create feedback
                feedback_dict = feedback_data.model_dump()
                feedback_dict.update({
                    'appointment_id': appointment_id,
                    'given_by': user_id,
                    'given_to': given_to,
                    'role_type': role_type.value
                })
                
                feedback = await self.feedback_repo.create_feedback(
                    conn, feedback_dict
                )
                
                # Update tutor metrics if feedback is for tutor
                if role_type in [FeedbackRole.STUDENT_TO_TUTOR, FeedbackRole.PARENT_TO_TUTOR]:
                    await self._update_tutor_rating_metrics(conn, given_to)
                
                logger.info(f"Created feedback {feedback.feedback_id} for appointment {appointment_id}")
                return feedback
                
        except Exception as e:
            logger.error(f"Error submitting feedback: {e}")
            if isinstance(e, (ResourceNotFoundError, ValidationError)):
                raise
            raise BusinessLogicError(f"Failed to submit feedback: {str(e)}")
    
    # =====================================================
    # PLATFORM ANALYTICS METHODS
    # =====================================================
    
    async def calculate_daily_platform_metrics(
        self,
        metric_date: Optional[date] = None
    ) -> PlatformAnalytics:
        """Calculate daily platform-wide metrics."""
        try:
            if not metric_date:
                metric_date = date.today() - timedelta(days=1)  # Yesterday's metrics
            
            async with self.db_manager.acquire() as conn:
                metrics = {}
                
                # User activity metrics
                user_metrics = await self._calculate_user_activity_metrics(
                    conn, metric_date
                )
                metrics.update(user_metrics)
                
                # Session metrics
                session_metrics = await self._calculate_session_metrics_platform(
                    conn, metric_date
                )
                metrics.update(session_metrics)
                
                # Financial metrics
                financial_metrics = await self._calculate_financial_metrics_platform(
                    conn, metric_date
                )
                metrics.update(financial_metrics)
                
                # Distribution metrics
                distributions = await self._calculate_distribution_metrics(
                    conn, metric_date
                )
                metrics.update(distributions)
                
                # Record metrics
                platform_analytics = await self.platform_repo.record_daily_metrics(
                    conn, metric_date, metrics
                )
                
                logger.info(f"Calculated platform metrics for {metric_date}")
                return platform_analytics
                
        except Exception as e:
            logger.error(f"Error calculating platform metrics: {e}")
            raise BusinessLogicError(f"Failed to calculate platform metrics: {str(e)}")
    
    async def get_platform_dashboard(
        self,
        days: int = 30
    ) -> PlatformDashboardData:
        """Get platform-wide dashboard data."""
        try:
            async with self.db_manager.acquire() as conn:
                # Get latest metrics
                end_date = date.today()
                start_date = end_date - timedelta(days=days)
                
                metrics_list = await self.platform_repo.get_metrics_range(
                    conn, start_date, end_date
                )
                
                # Get latest daily metrics
                latest_metrics = metrics_list[-1] if metrics_list else None
                if not latest_metrics:
                    # Calculate if not available
                    latest_metrics = await self.calculate_daily_platform_metrics()
                
                # Get tutor leaderboard
                leaderboard = await self.get_tutor_leaderboard()
                
                # Aggregate trends
                revenue_trends = self._aggregate_revenue_trends(metrics_list)
                user_growth = self._aggregate_user_growth(metrics_list)
                geographic_insights = self._aggregate_geographic_data(metrics_list)
                popular_subjects = self._aggregate_subject_popularity(metrics_list)
                peak_usage = self._aggregate_peak_usage(metrics_list)
                
                return PlatformDashboardData(
                    platform_metrics=latest_metrics,
                    tutor_leaderboard=leaderboard,
                    revenue_trends=revenue_trends,
                    user_growth=user_growth,
                    geographic_insights=geographic_insights,
                    popular_subjects=popular_subjects,
                    peak_usage_times=peak_usage
                )
                
        except Exception as e:
            logger.error(f"Error getting platform dashboard: {e}")
            raise BusinessLogicError(f"Failed to get platform dashboard: {str(e)}")
    
    # =====================================================
    # PRIVATE HELPER METHODS
    # =====================================================
    
    def _get_period_dates(self, period_type: PeriodType) -> Tuple[date, date]:
        """Get start and end dates for a period type."""
        today = date.today()
        
        if period_type == PeriodType.DAILY:
            return today, today
        elif period_type == PeriodType.WEEKLY:
            start = today - timedelta(days=today.weekday())
            end = start + timedelta(days=6)
            return start, end
        elif period_type == PeriodType.MONTHLY:
            start = today.replace(day=1)
            if today.month == 12:
                end = today.replace(year=today.year + 1, month=1, day=1) - timedelta(days=1)
            else:
                end = today.replace(month=today.month + 1, day=1) - timedelta(days=1)
            return start, end
        elif period_type == PeriodType.QUARTERLY:
            quarter = (today.month - 1) // 3
            start_month = quarter * 3 + 1
            start = today.replace(month=start_month, day=1)
            end_month = start_month + 2
            if end_month > 12:
                end = today.replace(year=today.year + 1, month=1, day=1) - timedelta(days=1)
            else:
                end = today.replace(month=end_month + 1, day=1) - timedelta(days=1)
            return start, end
        else:  # YEARLY
            start = today.replace(month=1, day=1)
            end = today.replace(month=12, day=31)
            return start, end
    
    async def _calculate_session_metrics(
        self,
        conn: Any,
        tutor_id: int,
        start_date: date,
        end_date: date
    ) -> Dict[str, Any]:
        """Calculate session-related metrics for a tutor."""
        query = """
            SELECT 
                COUNT(*) as total_sessions,
                COUNT(*) FILTER (WHERE status = 'completed') as completed_sessions,
                COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled_sessions,
                COUNT(*) FILTER (WHERE status = 'no_show') as no_show_sessions,
                SUM(CASE 
                    WHEN status = 'completed' THEN 
                        EXTRACT(EPOCH FROM (end_time - start_time)) / 3600.0
                    ELSE 0 
                END) as total_hours,
                COUNT(DISTINCT client_id) as unique_students,
                COUNT(DISTINCT CASE 
                    WHEN EXISTS (
                        SELECT 1 FROM appointments a2 
                        WHERE a2.tutor_id = a.tutor_id 
                        AND a2.client_id = a.client_id 
                        AND a2.appointment_date < a.appointment_date
                    ) THEN client_id 
                END) as returning_students,
                COUNT(DISTINCT CASE 
                    WHEN NOT EXISTS (
                        SELECT 1 FROM appointments a2 
                        WHERE a2.tutor_id = a.tutor_id 
                        AND a2.client_id = a.client_id 
                        AND a2.appointment_date < a.appointment_date
                    ) THEN client_id 
                END) as new_students
            FROM appointments a
            WHERE tutor_id = $1 
            AND appointment_date >= $2 
            AND appointment_date <= $3
        """
        
        result = await conn.fetchrow(query, tutor_id, start_date, end_date)
        
        # Get subject breakdown
        subject_query = """
            SELECT 
                s.subject_area,
                COUNT(*) as session_count,
                SUM(EXTRACT(EPOCH FROM (a.end_time - a.start_time)) / 3600.0) as hours
            FROM appointments a
            JOIN services s ON a.service_id = s.service_id
            WHERE a.tutor_id = $1 
            AND a.appointment_date >= $2 
            AND a.appointment_date <= $3
            AND a.status = 'completed'
            GROUP BY s.subject_area
        """
        
        subject_results = await conn.fetch(subject_query, tutor_id, start_date, end_date)
        subject_breakdown = {
            row['subject_area']: {
                'sessions': row['session_count'],
                'hours': float(row['hours']) if row['hours'] else 0
            }
            for row in subject_results
        }
        
        return {
            'total_sessions': result['total_sessions'] or 0,
            'completed_sessions': result['completed_sessions'] or 0,
            'cancelled_sessions': result['cancelled_sessions'] or 0,
            'no_show_sessions': result['no_show_sessions'] or 0,
            'total_hours': Decimal(str(result['total_hours'] or 0)),
            'unique_students': result['unique_students'] or 0,
            'returning_students': result['returning_students'] or 0,
            'new_students': result['new_students'] or 0,
            'subject_breakdown': subject_breakdown
        }
    
    async def _calculate_rating_metrics(
        self,
        conn: Any,
        tutor_id: int,
        start_date: date,
        end_date: date
    ) -> Dict[str, Any]:
        """Calculate rating metrics for a tutor."""
        query = """
            SELECT 
                AVG(overall_rating) as average_rating,
                COUNT(*) as total_ratings,
                COUNT(*) FILTER (WHERE overall_rating = 5) as five_star_count
            FROM session_feedback sf
            JOIN appointments a ON sf.appointment_id = a.appointment_id
            WHERE sf.given_to = $1
            AND a.appointment_date >= $2
            AND a.appointment_date <= $3
            AND sf.overall_rating IS NOT NULL
        """
        
        result = await conn.fetchrow(query, tutor_id, start_date, end_date)
        
        return {
            'average_rating': Decimal(str(result['average_rating'])) if result['average_rating'] else None,
            'total_ratings': result['total_ratings'] or 0,
            'five_star_count': result['five_star_count'] or 0
        }
    
    async def _calculate_financial_metrics(
        self,
        conn: Any,
        tutor_id: int,
        start_date: date,
        end_date: date
    ) -> Dict[str, Any]:
        """Calculate financial metrics for a tutor."""
        query = """
            SELECT 
                SUM(tp.amount) as total_revenue,
                SUM(tp.hours_worked) as total_hours
            FROM tutor_payments tp
            WHERE tp.tutor_id = $1
            AND tp.payment_date >= $2
            AND tp.payment_date <= $3
            AND tp.status = 'completed'
        """
        
        result = await conn.fetchrow(query, tutor_id, start_date, end_date)
        
        return {
            'total_revenue': result['total_revenue'] or Decimal("0"),
            'total_hours': result['total_hours'] or Decimal("0")
        }
    
    async def _save_metrics_snapshot(
        self,
        conn: Any,
        snapshot_type: str,
        entity_id: int,
        metrics: Any
    ) -> None:
        """Save analytics snapshot for caching."""
        snapshot_data = {
            'snapshot_type': snapshot_type,
            'snapshot_date': date.today(),
            'entity_type': 'tutor',
            'entity_id': entity_id,
            'metrics': metrics.model_dump(mode='json')
        }
        
        await self.snapshot_repo.save_snapshot(conn, snapshot_data)
    
    async def _get_subject_performance(
        self,
        conn: Any,
        tutor_id: int,
        days: int = 30
    ) -> Dict[str, Any]:
        """Get subject-wise performance breakdown for a tutor."""
        end_date = date.today()
        start_date = end_date - timedelta(days=days)
        
        query = """
            SELECT 
                s.subject_area,
                COUNT(DISTINCT a.appointment_id) as total_sessions,
                COUNT(DISTINCT a.appointment_id) FILTER (WHERE a.status = 'completed') as completed_sessions,
                COUNT(DISTINCT a.client_id) as unique_students,
                SUM(CASE 
                    WHEN a.status = 'completed' THEN 
                        EXTRACT(EPOCH FROM (a.end_time - a.start_time)) / 3600.0
                    ELSE 0 
                END) as total_hours,
                AVG(sf.overall_rating) as avg_rating
            FROM appointments a
            JOIN services s ON a.service_id = s.service_id
            LEFT JOIN session_feedback sf ON a.appointment_id = sf.appointment_id 
                AND sf.given_to = a.tutor_id
            WHERE a.tutor_id = $1 
            AND a.appointment_date >= $2 
            AND a.appointment_date <= $3
            GROUP BY s.subject_area
            ORDER BY total_sessions DESC
        """
        
        results = await conn.fetch(query, tutor_id, start_date, end_date)
        
        return {
            row['subject_area']: {
                'total_sessions': row['total_sessions'],
                'completed_sessions': row['completed_sessions'],
                'unique_students': row['unique_students'],
                'total_hours': float(row['total_hours']) if row['total_hours'] else 0,
                'avg_rating': float(row['avg_rating']) if row['avg_rating'] else None,
                'completion_rate': (
                    (row['completed_sessions'] / row['total_sessions'] * 100) 
                    if row['total_sessions'] > 0 else 0
                )
            }
            for row in results
        }
    
    async def _get_tutor_student_progress(
        self,
        conn: Any,
        tutor_id: int,
        limit: int = 10
    ) -> List[StudentProgressSummary]:
        """Get progress summaries for students taught by this tutor."""
        query = """
            SELECT DISTINCT ON (sps.student_id, sps.subject_area)
                sps.*
            FROM student_progress_summary sps
            JOIN appointments a ON sps.student_id = a.client_id
            WHERE a.tutor_id = $1
            AND a.appointment_date >= CURRENT_DATE - INTERVAL '30 days'
            ORDER BY sps.student_id, sps.subject_area, sps.session_hours DESC
            LIMIT $2
        """
        
        results = await conn.fetch(query, tutor_id, limit)
        return [StudentProgressSummary(**dict(row)) for row in results]
    
    async def _get_earnings_trend(
        self,
        conn: Any,
        tutor_id: int,
        months: int = 6
    ) -> List[Dict[str, Any]]:
        """Get earnings trend for the past N months."""
        query = """
            SELECT 
                DATE_TRUNC('month', tp.payment_date) as month,
                SUM(tp.amount) as total_earnings,
                SUM(tp.hours_worked) as total_hours,
                COUNT(DISTINCT tp.payment_id) as payment_count
            FROM tutor_payments tp
            WHERE tp.tutor_id = $1
            AND tp.payment_date >= CURRENT_DATE - INTERVAL '%s months'
            AND tp.status = 'completed'
            GROUP BY DATE_TRUNC('month', tp.payment_date)
            ORDER BY month DESC
        """ % months
        
        results = await conn.fetch(query, tutor_id)
        
        return [
            {
                'month': row['month'].isoformat(),
                'total_earnings': float(row['total_earnings']),
                'total_hours': float(row['total_hours']),
                'payment_count': row['payment_count'],
                'avg_hourly_rate': (
                    float(row['total_earnings']) / float(row['total_hours'])
                    if row['total_hours'] > 0 else 0
                )
            }
            for row in results
        ]
    
    async def _count_upcoming_sessions(self, conn: Any, tutor_id: int) -> int:
        """Count upcoming sessions for a tutor."""
        query = """
            SELECT COUNT(*) 
            FROM appointments 
            WHERE tutor_id = $1 
            AND appointment_date >= CURRENT_DATE
            AND status IN ('scheduled', 'confirmed')
            AND deleted_at IS NULL
        """
        return await conn.fetchval(query, tutor_id) or 0
    
    async def _count_pending_feedback(self, conn: Any, tutor_id: int) -> int:
        """Count sessions pending feedback from tutor."""
        query = """
            SELECT COUNT(*)
            FROM appointments a
            WHERE a.tutor_id = $1
            AND a.status = 'completed'
            AND a.appointment_date >= CURRENT_DATE - INTERVAL '7 days'
            AND NOT EXISTS (
                SELECT 1 FROM session_feedback sf
                WHERE sf.appointment_id = a.appointment_id
                AND sf.given_by = $1
            )
            AND a.deleted_at IS NULL
        """
        return await conn.fetchval(query, tutor_id) or 0
    
    async def _get_student_all_progress(
        self,
        conn: Any,
        student_id: int
    ) -> List[StudentProgressMetrics]:
        """Get all progress metrics for a student."""
        query = """
            SELECT * FROM student_progress_metrics
            WHERE student_id = $1
            AND period_end >= CURRENT_DATE - INTERVAL '6 months'
            ORDER BY subject_area, period_start DESC
        """
        
        results = await conn.fetch(query, student_id)
        return [StudentProgressMetrics(**dict(row)) for row in results]
    
    async def _get_recent_sessions(
        self,
        conn: Any,
        student_id: int,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """Get recent sessions for a student."""
        query = """
            SELECT 
                a.appointment_id,
                a.appointment_date,
                a.start_time,
                a.end_time,
                a.status,
                s.subject_area,
                s.service_name,
                u.first_name as tutor_first_name,
                u.last_name as tutor_last_name,
                sf.overall_rating
            FROM appointments a
            JOIN services s ON a.service_id = s.service_id
            JOIN tutor_profiles tp ON a.tutor_id = tp.tutor_id
            JOIN users u ON tp.user_id = u.user_id
            LEFT JOIN session_feedback sf ON a.appointment_id = sf.appointment_id
                AND sf.given_by = a.client_id
            WHERE a.client_id = $1
            AND a.deleted_at IS NULL
            ORDER BY a.appointment_date DESC, a.start_time DESC
            LIMIT $2
        """
        
        results = await conn.fetch(query, student_id, limit)
        
        return [
            {
                'appointment_id': row['appointment_id'],
                'appointment_date': row['appointment_date'].isoformat(),
                'start_time': row['start_time'].isoformat() if row['start_time'] else None,
                'end_time': row['end_time'].isoformat() if row['end_time'] else None,
                'status': row['status'],
                'subject_area': row['subject_area'],
                'service_name': row['service_name'],
                'tutor_name': f"{row['tutor_first_name']} {row['tutor_last_name']}",
                'rating': float(row['overall_rating']) if row['overall_rating'] else None
            }
            for row in results
        ]
    
    async def _get_improvement_trends(
        self,
        conn: Any,
        student_id: int
    ) -> Dict[str, Any]:
        """Get improvement trends for a student."""
        query = """
            SELECT 
                subject_area,
                period_start,
                performance_score,
                improvement_rate,
                engagement_score
            FROM student_progress_metrics
            WHERE student_id = $1
            AND period_end >= CURRENT_DATE - INTERVAL '6 months'
            ORDER BY subject_area, period_start
        """
        
        results = await conn.fetch(query, student_id)
        
        trends = defaultdict(list)
        for row in results:
            trends[row['subject_area']].append({
                'date': row['period_start'].isoformat(),
                'performance_score': float(row['performance_score']) if row['performance_score'] else None,
                'improvement_rate': float(row['improvement_rate']) if row['improvement_rate'] else None,
                'engagement_score': float(row['engagement_score']) if row['engagement_score'] else None
            })
        
        return dict(trends)
    
    async def _get_next_session(
        self,
        conn: Any,
        student_id: int
    ) -> Optional[Dict[str, Any]]:
        """Get next upcoming session for a student."""
        query = """
            SELECT 
                a.appointment_id,
                a.appointment_date,
                a.start_time,
                a.end_time,
                s.subject_area,
                s.service_name,
                u.first_name as tutor_first_name,
                u.last_name as tutor_last_name
            FROM appointments a
            JOIN services s ON a.service_id = s.service_id
            JOIN tutor_profiles tp ON a.tutor_id = tp.tutor_id
            JOIN users u ON tp.user_id = u.user_id
            WHERE a.client_id = $1
            AND a.appointment_date >= CURRENT_DATE
            AND a.status IN ('scheduled', 'confirmed')
            AND a.deleted_at IS NULL
            ORDER BY a.appointment_date, a.start_time
            LIMIT 1
        """
        
        result = await conn.fetchrow(query, student_id)
        
        if result:
            return {
                'appointment_id': result['appointment_id'],
                'appointment_date': result['appointment_date'].isoformat(),
                'start_time': result['start_time'].isoformat() if result['start_time'] else None,
                'end_time': result['end_time'].isoformat() if result['end_time'] else None,
                'subject_area': result['subject_area'],
                'service_name': result['service_name'],
                'tutor_name': f"{result['tutor_first_name']} {result['tutor_last_name']}"
            }
        return None
    
    async def _determine_feedback_role(
        self,
        conn: Any,
        appointment: Dict[str, Any],
        user_id: int
    ) -> Tuple[FeedbackRole, int]:
        """Determine feedback role and recipient."""
        # Check if user is the tutor
        if appointment['tutor_id']:
            tutor_query = """
                SELECT user_id FROM tutor_profiles 
                WHERE tutor_id = $1 AND deleted_at IS NULL
            """
            tutor_user_id = await conn.fetchval(tutor_query, appointment['tutor_id'])
            if tutor_user_id == user_id:
                return FeedbackRole.TUTOR_TO_STUDENT, appointment['client_id']
        
        # Check if user is the student/client
        if appointment['client_id']:
            client_query = """
                SELECT user_id FROM client_profiles 
                WHERE client_id = $1 AND deleted_at IS NULL
            """
            client_user_id = await conn.fetchval(client_query, appointment['client_id'])
            if client_user_id == user_id:
                return FeedbackRole.STUDENT_TO_TUTOR, appointment['tutor_id']
        
        # Check if user is parent of student (if client is a dependant)
        parent_query = """
            SELECT cp.user_id
            FROM dependants d
            JOIN client_profiles cp ON (
                d.primary_client_id = cp.client_id OR 
                d.secondary_client_id = cp.client_id
            )
            WHERE d.dependant_id = $1 AND cp.user_id = $2
            AND d.deleted_at IS NULL
        """
        parent_check = await conn.fetchval(parent_query, appointment['client_id'], user_id)
        if parent_check:
            return FeedbackRole.PARENT_TO_TUTOR, appointment['tutor_id']
        
        raise ValidationError("User is not a participant in this session")
    
    async def _update_tutor_rating_metrics(self, conn: Any, tutor_id: int) -> None:
        """Update tutor's average rating after new feedback."""
        # This is handled by database triggers, but we can force a recalculation if needed
        query = """
            UPDATE tutor_profiles
            SET average_rating = (
                SELECT AVG(overall_rating)
                FROM session_feedback
                WHERE given_to = $1
                AND overall_rating IS NOT NULL
            ),
            total_reviews = (
                SELECT COUNT(*)
                FROM session_feedback
                WHERE given_to = $1
                AND overall_rating IS NOT NULL
            ),
            updated_at = CURRENT_TIMESTAMP
            WHERE tutor_id = $1
        """
        await conn.execute(query, tutor_id)
    
    async def _calculate_user_activity_metrics(
        self,
        conn: Any,
        metric_date: date
    ) -> Dict[str, Any]:
        """Calculate user activity metrics for a given date."""
        # Active users
        active_query = """
            SELECT 
                COUNT(DISTINCT CASE WHEN 'client' = ANY(u.roles) THEN u.user_id END) as active_clients,
                COUNT(DISTINCT CASE WHEN 'tutor' = ANY(u.roles) THEN u.user_id END) as active_tutors,
                COUNT(DISTINCT a.client_id) as active_students
            FROM users u
            LEFT JOIN appointments a ON (
                a.appointment_date = $1 OR
                (a.created_at::date = $1 AND a.appointment_date > $1)
            )
            WHERE u.last_login::date = $1 OR a.appointment_id IS NOT NULL
        """
        
        active_result = await conn.fetchrow(active_query, metric_date)
        
        # New registrations
        reg_query = """
            SELECT COUNT(*) as new_registrations
            FROM users
            WHERE created_at::date = $1
            AND deleted_at IS NULL
        """
        
        reg_count = await conn.fetchval(reg_query, metric_date)
        
        return {
            'active_clients': active_result['active_clients'] or 0,
            'active_tutors': active_result['active_tutors'] or 0,
            'active_students': active_result['active_students'] or 0,
            'new_registrations': reg_count or 0
        }
    
    async def _calculate_session_metrics_platform(
        self,
        conn: Any,
        metric_date: date
    ) -> Dict[str, Any]:
        """Calculate session metrics for the platform."""
        query = """
            SELECT 
                COUNT(*) as total_bookings,
                COUNT(*) FILTER (WHERE status = 'completed') as completed_sessions,
                COUNT(*) FILTER (WHERE created_at::date = appointment_date) as same_day_bookings,
                AVG(CASE 
                    WHEN status = 'completed' THEN 
                        EXTRACT(EPOCH FROM (end_time - start_time)) / 3600.0
                    ELSE NULL 
                END) as average_session_duration
            FROM appointments
            WHERE appointment_date = $1
            AND deleted_at IS NULL
        """
        
        result = await conn.fetchrow(query, metric_date)
        
        # Calculate conversion rate
        conversion_rate = None
        if result['total_bookings'] > 0:
            conversion_rate = Decimal(str(
                result['completed_sessions'] / result['total_bookings'] * 100
            ))
        
        return {
            'total_bookings': result['total_bookings'] or 0,
            'completed_sessions': result['completed_sessions'] or 0,
            'booking_conversion_rate': conversion_rate,
            'average_session_duration': (
                Decimal(str(result['average_session_duration'])) 
                if result['average_session_duration'] else None
            )
        }
    
    async def _calculate_financial_metrics_platform(
        self,
        conn: Any,
        metric_date: date
    ) -> Dict[str, Any]:
        """Calculate financial metrics for the platform."""
        # Daily revenue from invoices
        invoice_query = """
            SELECT 
                SUM(amount_paid) as invoice_revenue
            FROM invoices
            WHERE payment_date::date = $1
            AND payment_status = 'paid'
            AND deleted_at IS NULL
        """
        
        invoice_revenue = await conn.fetchval(invoice_query, metric_date) or Decimal("0")
        
        # Daily revenue from subscriptions
        subscription_query = """
            SELECT 
                SUM(amount) as subscription_revenue
            FROM subscription_payments
            WHERE payment_date::date = $1
            AND status = 'succeeded'
        """
        
        subscription_revenue = await conn.fetchval(subscription_query, metric_date) or Decimal("0")
        
        # Average session value
        session_value_query = """
            SELECT 
                AVG(i.total_amount) as avg_session_value
            FROM invoices i
            JOIN invoice_items ii ON i.invoice_id = ii.invoice_id
            WHERE i.payment_date::date = $1
            AND i.payment_status = 'paid'
            AND ii.item_type = 'session'
        """
        
        avg_session_value = await conn.fetchval(session_value_query, metric_date)
        
        return {
            'daily_revenue': invoice_revenue + subscription_revenue,
            'average_session_value': avg_session_value
        }
    
    async def _calculate_distribution_metrics(
        self,
        conn: Any,
        metric_date: date
    ) -> Dict[str, Any]:
        """Calculate various distribution metrics."""
        # Subject distribution
        subject_query = """
            SELECT 
                s.subject_area,
                COUNT(*) as session_count
            FROM appointments a
            JOIN services s ON a.service_id = s.service_id
            WHERE a.appointment_date = $1
            AND a.deleted_at IS NULL
            GROUP BY s.subject_area
        """
        
        subject_results = await conn.fetch(subject_query, metric_date)
        subject_dist = {row['subject_area']: row['session_count'] for row in subject_results}
        
        # Peak hours
        peak_query = """
            SELECT 
                EXTRACT(HOUR FROM start_time) as hour,
                COUNT(*) as session_count
            FROM appointments
            WHERE appointment_date = $1
            AND deleted_at IS NULL
            GROUP BY EXTRACT(HOUR FROM start_time)
            ORDER BY hour
        """
        
        peak_results = await conn.fetch(peak_query, metric_date)
        peak_hours = {str(int(row['hour'])): row['session_count'] for row in peak_results}
        
        # Time slot distribution
        time_slot_query = """
            SELECT 
                CASE 
                    WHEN EXTRACT(HOUR FROM start_time) < 12 THEN 'morning'
                    WHEN EXTRACT(HOUR FROM start_time) < 17 THEN 'afternoon'
                    ELSE 'evening'
                END as time_slot,
                COUNT(*) as session_count
            FROM appointments
            WHERE appointment_date = $1
            AND deleted_at IS NULL
            GROUP BY time_slot
        """
        
        time_slot_results = await conn.fetch(time_slot_query, metric_date)
        time_slots = {row['time_slot']: row['session_count'] for row in time_slot_results}
        
        # Geographic distribution (by postal code prefix)
        geo_query = """
            SELECT 
                SUBSTRING(cp.postal_code FROM 1 FOR 3) as area_code,
                COUNT(DISTINCT a.appointment_id) as session_count
            FROM appointments a
            JOIN client_profiles cp ON a.client_id = cp.client_id
            WHERE a.appointment_date = $1
            AND a.deleted_at IS NULL
            AND cp.postal_code IS NOT NULL
            GROUP BY area_code
        """
        
        geo_results = await conn.fetch(geo_query, metric_date)
        geo_dist = {row['area_code']: row['session_count'] for row in geo_results}
        
        return {
            'subject_distribution': subject_dist,
            'peak_hours': peak_hours,
            'time_slot_distribution': time_slots,
            'geographic_distribution': geo_dist
        }
    
    async def _calculate_student_metrics(
        self,
        conn: Any,
        student_id: int,
        subject_area: str,
        start_date: date,
        end_date: date
    ) -> Dict[str, Any]:
        """Calculate detailed metrics for a student."""
        # Session metrics
        session_query = """
            SELECT 
                COUNT(*) as total_sessions,
                COUNT(*) FILTER (WHERE a.status = 'completed') as attended_sessions,
                SUM(CASE 
                    WHEN a.status = 'completed' THEN 
                        EXTRACT(EPOCH FROM (a.end_time - a.start_time)) / 3600.0
                    ELSE 0 
                END) as session_hours
            FROM appointments a
            JOIN services s ON a.service_id = s.service_id
            WHERE a.client_id = $1
            AND s.subject_area = $2
            AND a.appointment_date >= $3
            AND a.appointment_date <= $4
            AND a.deleted_at IS NULL
        """
        
        session_result = await conn.fetchrow(
            session_query, student_id, subject_area, start_date, end_date
        )
        
        # Performance metrics (from feedback)
        performance_query = """
            SELECT 
                AVG(sf.overall_rating) as avg_performance,
                AVG(sf.preparation_rating) as avg_preparation,
                AVG(sf.communication_rating) as avg_communication
            FROM session_feedback sf
            JOIN appointments a ON sf.appointment_id = a.appointment_id
            JOIN services s ON a.service_id = s.service_id
            WHERE a.client_id = $1
            AND s.subject_area = $2
            AND a.appointment_date >= $3
            AND a.appointment_date <= $4
            AND sf.role_type = 'tutor_to_student'
        """
        
        performance_result = await conn.fetchrow(
            performance_query, student_id, subject_area, start_date, end_date
        )
        
        # Milestones (from learning goals)
        milestone_query = """
            SELECT 
                COUNT(*) as total_goals,
                COUNT(*) FILTER (WHERE status = 'completed') as completed_goals
            FROM student_learning_goals
            WHERE student_id = $1
            AND subject_area = $2
            AND created_at >= $3
            AND created_at <= $4
            AND deleted_at IS NULL
        """
        
        milestone_result = await conn.fetchrow(
            milestone_query, student_id, subject_area, start_date, end_date
        )
        
        # Calculate scores
        performance_score = None
        if performance_result['avg_performance']:
            performance_score = Decimal(str(performance_result['avg_performance'] * 20))  # Convert 1-5 to 0-100
        
        engagement_score = None
        if session_result['total_sessions'] > 0:
            attendance_rate = session_result['attended_sessions'] / session_result['total_sessions']
            preparation_score = performance_result['avg_preparation'] or 3
            engagement_score = Decimal(str((attendance_rate * 50) + (preparation_score * 10)))
        
        return {
            'total_sessions': session_result['total_sessions'] or 0,
            'attended_sessions': session_result['attended_sessions'] or 0,
            'session_hours': Decimal(str(session_result['session_hours'] or 0)),
            'performance_score': performance_score,
            'improvement_rate': None,  # Would need historical data to calculate
            'engagement_score': engagement_score,
            'milestones_achieved': milestone_result['completed_goals'] or 0,
            'milestones_total': milestone_result['total_goals'] or 0,
            'avg_tutor_rating': (
                Decimal(str(performance_result['avg_performance'])) 
                if performance_result['avg_performance'] else None
            ),
            'feedback_summary': {
                'avg_preparation': float(performance_result['avg_preparation']) if performance_result['avg_preparation'] else None,
                'avg_communication': float(performance_result['avg_communication']) if performance_result['avg_communication'] else None
            }
        }
    
    def _aggregate_revenue_trends(
        self,
        metrics_list: List[PlatformAnalytics]
    ) -> Dict[str, Any]:
        """Aggregate revenue trends from platform metrics."""
        trends = []
        for metric in metrics_list:
            trends.append({
                'date': metric.metric_date.isoformat(),
                'revenue': float(metric.daily_revenue),
                'avg_session_value': float(metric.average_session_value) if metric.average_session_value else None
            })
        
        # Calculate growth
        if len(trends) >= 2:
            current = trends[-1]['revenue']
            previous = trends[-2]['revenue']
            growth = ((current - previous) / previous * 100) if previous > 0 else 0
        else:
            growth = 0
        
        return {
            'daily_trends': trends,
            'total_revenue': sum(t['revenue'] for t in trends),
            'average_daily': sum(t['revenue'] for t in trends) / len(trends) if trends else 0,
            'growth_percentage': growth
        }
    
    def _aggregate_user_growth(
        self,
        metrics_list: List[PlatformAnalytics]
    ) -> Dict[str, Any]:
        """Aggregate user growth metrics."""
        return {
            'new_users_trend': [
                {
                    'date': m.metric_date.isoformat(),
                    'count': m.new_registrations
                }
                for m in metrics_list
            ],
            'active_users_trend': [
                {
                    'date': m.metric_date.isoformat(),
                    'clients': m.active_clients,
                    'tutors': m.active_tutors,
                    'students': m.active_students
                }
                for m in metrics_list
            ],
            'total_new_users': sum(m.new_registrations for m in metrics_list)
        }
    
    def _aggregate_geographic_data(
        self,
        metrics_list: List[PlatformAnalytics]
    ) -> Dict[str, Any]:
        """Aggregate geographic distribution data."""
        combined_geo = defaultdict(int)
        
        for metric in metrics_list:
            for area, count in metric.geographic_distribution.items():
                combined_geo[area] += count
        
        # Get top areas
        top_areas = sorted(
            combined_geo.items(), 
            key=lambda x: x[1], 
            reverse=True
        )[:10]
        
        return {
            'top_areas': [
                {'area_code': area, 'session_count': count}
                for area, count in top_areas
            ],
            'total_areas': len(combined_geo)
        }
    
    def _aggregate_subject_popularity(
        self,
        metrics_list: List[PlatformAnalytics]
    ) -> List[Dict[str, Any]]:
        """Aggregate subject popularity data."""
        combined_subjects = defaultdict(int)
        
        for metric in metrics_list:
            for subject, count in metric.subject_distribution.items():
                combined_subjects[subject] += count
        
        # Sort by popularity
        sorted_subjects = sorted(
            combined_subjects.items(),
            key=lambda x: x[1],
            reverse=True
        )
        
        total_sessions = sum(count for _, count in sorted_subjects)
        
        return [
            {
                'subject': subject,
                'session_count': count,
                'percentage': (count / total_sessions * 100) if total_sessions > 0 else 0
            }
            for subject, count in sorted_subjects
        ]
    
    def _aggregate_peak_usage(
        self,
        metrics_list: List[PlatformAnalytics]
    ) -> List[Dict[str, Any]]:
        """Aggregate peak usage times."""
        combined_hours = defaultdict(int)
        
        for metric in metrics_list:
            for hour, count in metric.peak_hours.items():
                combined_hours[hour] += count
        
        # Get all 24 hours
        peak_times = []
        for hour in range(24):
            hour_str = str(hour)
            peak_times.append({
                'hour': hour,
                'session_count': combined_hours.get(hour_str, 0),
                'label': f"{hour:02d}:00"
            })
        
        return peak_times