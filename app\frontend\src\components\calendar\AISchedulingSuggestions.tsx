import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Brain, 
  Clock, 
  Calendar, 
  User, 
  Star, 
  TrendingUp, 
  Target,
  Lightbulb,
  Check,
  X,
  Loader2
} from 'lucide-react';
import { toast } from 'react-hot-toast';

interface AISchedulingSuggestion {
  tutor_id: number;
  tutor_name: string;
  client_id: number;
  dependant_id?: number;
  suggested_date: string;
  suggested_start_time: string;
  suggested_end_time: string;
  duration: number;
  subject_area: string;
  session_type: string;
  priority: 'high' | 'medium' | 'low';
  confidence_score: number;
  reasons: string[];
  explanation: string;
  alternative_slots: any[];
  estimated_cost?: number;
  estimated_travel_time?: number;
}

interface AISchedulingSuggestionsResponse {
  suggestions: AISchedulingSuggestion[];
  total_count: number;
  generation_time_ms: number;
  analysis_summary: {
    total_tutors_analyzed: number;
    average_confidence: number;
    high_priority_count: number;
    date_range_days: number;
    subject_area: string;
  };
}

interface AISchedulingSuggestionsProps {
  clientId: number;
  subjectArea: string;
  duration?: number;
  sessionType?: string;
  dependantId?: number;
  onSuggestionSelect?: (suggestion: AISchedulingSuggestion) => void;
  onClose?: () => void;
  className?: string;
}

const AISchedulingSuggestions: React.FC<AISchedulingSuggestionsProps> = ({
  clientId,
  subjectArea,
  duration = 60,
  sessionType = 'individual',
  dependantId,
  onSuggestionSelect,
  onClose,
  className = ''
}) => {
  const { t } = useTranslation();
  const [suggestions, setSuggestions] = useState<AISchedulingSuggestion[]>([]);
  const [analysisSummary, setAnalysisSummary] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [selectedSuggestion, setSelectedSuggestion] = useState<AISchedulingSuggestion | null>(null);
  const [showAlternatives, setShowAlternatives] = useState<number | null>(null);

  useEffect(() => {
    if (clientId && subjectArea) {
      fetchSuggestions();
    }
  }, [clientId, subjectArea, duration, sessionType, dependantId]);

  const fetchSuggestions = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/v1/ai-scheduling/suggestions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          client_id: clientId,
          subject_area: subjectArea,
          duration,
          session_type: sessionType,
          dependant_id: dependantId,
          max_suggestions: 8
        })
      });

      if (!response.ok) {
        throw new Error('Failed to fetch AI suggestions');
      }

      const data: AISchedulingSuggestionsResponse = await response.json();
      setSuggestions(data.suggestions);
      setAnalysisSummary(data.analysis_summary);

    } catch (error) {
      console.error('Error fetching AI suggestions:', error);
      toast.error('Failed to get AI scheduling suggestions');
    } finally {
      setLoading(false);
    }
  };

  const handleSuggestionSelect = (suggestion: AISchedulingSuggestion) => {
    setSelectedSuggestion(suggestion);
    if (onSuggestionSelect) {
      onSuggestionSelect(suggestion);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getConfidenceColor = (score: number) => {
    if (score >= 0.8) return 'text-green-600';
    if (score >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      weekday: 'short', 
      month: 'short', 
      day: 'numeric' 
    });
  };

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-md border p-6 ${className}`}>
        <div className="flex items-center justify-center space-x-3">
          <Loader2 className="w-6 h-6 animate-spin text-red-600" />
          <div className="text-center">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              <Brain className="w-5 h-5 inline mr-2" />
              {t('ai.generating_suggestions')}
            </h3>
            <p className="text-sm text-gray-600">
              {t('ai.analyzing_patterns')}
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-md border ${className}`}>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-red-100 rounded-lg">
              <Brain className="w-5 h-5 text-red-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {t('ai.scheduling_suggestions')}
              </h3>
              <p className="text-sm text-gray-600">
                {t('ai.powered_by_ai')} • {subjectArea}
              </p>
            </div>
          </div>
          
          {onClose && (
            <button
              onClick={onClose}
              className="p-1 text-gray-400 hover:text-gray-600 rounded-md transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          )}
        </div>

        {/* Analysis Summary */}
        {analysisSummary && (
          <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div className="text-center">
              <div className="font-medium text-gray-900">{analysisSummary.total_tutors_analyzed}</div>
              <div className="text-gray-600">{t('ai.tutors_analyzed')}</div>
            </div>
            <div className="text-center">
              <div className="font-medium text-gray-900">
                {Math.round(analysisSummary.average_confidence * 100)}%
              </div>
              <div className="text-gray-600">{t('ai.avg_confidence')}</div>
            </div>
            <div className="text-center">
              <div className="font-medium text-gray-900">{analysisSummary.high_priority_count}</div>
              <div className="text-gray-600">{t('ai.high_priority')}</div>
            </div>
            <div className="text-center">
              <div className="font-medium text-gray-900">{analysisSummary.date_range_days}</div>
              <div className="text-gray-600">{t('ai.days_analyzed')}</div>
            </div>
          </div>
        )}
      </div>

      {/* Suggestions List */}
      <div className="max-h-96 overflow-y-auto">
        {suggestions.length === 0 ? (
          <div className="px-6 py-8 text-center text-gray-500">
            <Lightbulb className="w-8 h-8 mx-auto mb-3 opacity-50" />
            <p>{t('ai.no_suggestions_found')}</p>
            <p className="text-sm mt-1">{t('ai.try_different_criteria')}</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-100">
            {suggestions.map((suggestion, index) => (
              <div key={index} className="p-4 hover:bg-gray-50 transition-colors">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    {/* Suggestion Header */}
                    <div className="flex items-center space-x-3 mb-2">
                      <div className="flex items-center space-x-2">
                        <User className="w-4 h-4 text-gray-500" />
                        <span className="font-medium text-gray-900">
                          {suggestion.tutor_name}
                        </span>
                      </div>
                      
                      <span className={`px-2 py-1 text-xs rounded-full font-medium ${getPriorityColor(suggestion.priority)}`}>
                        {suggestion.priority}
                      </span>
                      
                      <div className="flex items-center space-x-1">
                        <Star className="w-4 h-4 text-yellow-500" />
                        <span className={`text-sm font-medium ${getConfidenceColor(suggestion.confidence_score)}`}>
                          {Math.round(suggestion.confidence_score * 100)}%
                        </span>
                      </div>
                    </div>

                    {/* Date and Time */}
                    <div className="flex items-center space-x-4 mb-2 text-sm text-gray-600">
                      <div className="flex items-center space-x-1">
                        <Calendar className="w-4 h-4" />
                        <span>{formatDate(suggestion.suggested_date)}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="w-4 h-4" />
                        <span>
                          {suggestion.suggested_start_time} - {suggestion.suggested_end_time}
                        </span>
                      </div>
                      <div className="text-xs text-gray-500">
                        {suggestion.duration} min
                      </div>
                    </div>

                    {/* Explanation */}
                    <p className="text-sm text-gray-700 mb-2">
                      {suggestion.explanation}
                    </p>

                    {/* Reasons */}
                    <div className="flex flex-wrap gap-1 mb-3">
                      {suggestion.reasons.map((reason, reasonIndex) => (
                        <span
                          key={reasonIndex}
                          className="px-2 py-1 text-xs bg-red-50 text-red-700 rounded-md"
                        >
                          {reason.replace('_', ' ')}
                        </span>
                      ))}
                    </div>

                    {/* Additional Info */}
                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      {suggestion.estimated_travel_time && (
                        <span>Travel: {suggestion.estimated_travel_time} min</span>
                      )}
                      {suggestion.estimated_cost && (
                        <span>Est. ${suggestion.estimated_cost}</span>
                      )}
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex flex-col space-y-2 ml-4">
                    <button
                      onClick={() => handleSuggestionSelect(suggestion)}
                      className="px-3 py-1.5 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 transition-colors flex items-center space-x-1"
                    >
                      <Check className="w-4 h-4" />
                      <span>{t('ai.select')}</span>
                    </button>
                    
                    {suggestion.alternative_slots && suggestion.alternative_slots.length > 0 && (
                      <button
                        onClick={() => setShowAlternatives(
                          showAlternatives === index ? null : index
                        )}
                        className="px-3 py-1.5 bg-gray-100 text-gray-700 text-sm rounded-md hover:bg-gray-200 transition-colors"
                      >
                        {t('ai.alternatives')}
                      </button>
                    )}
                  </div>
                </div>

                {/* Alternative Slots */}
                {showAlternatives === index && suggestion.alternative_slots && (
                  <div className="mt-3 pt-3 border-t border-gray-100">
                    <h5 className="text-sm font-medium text-gray-900 mb-2">
                      {t('ai.alternative_times')}
                    </h5>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                      {suggestion.alternative_slots.map((alt, altIndex) => (
                        <button
                          key={altIndex}
                          className="px-3 py-2 text-sm bg-gray-50 hover:bg-gray-100 rounded-md transition-colors"
                          onClick={() => {
                            // Handle alternative selection
                            console.log('Alternative selected:', alt);
                          }}
                        >
                          <div className="font-medium">{alt.date}</div>
                          <div className="text-xs text-gray-600">
                            {alt.start_time} - {alt.end_time}
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      {suggestions.length > 0 && (
        <div className="px-6 py-3 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <div className="flex items-center space-x-2">
              <TrendingUp className="w-4 h-4" />
              <span>{t('ai.suggestions_optimized')}</span>
            </div>
            <button
              onClick={fetchSuggestions}
              className="text-red-600 hover:text-red-800 font-medium transition-colors"
            >
              {t('ai.refresh_suggestions')}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default AISchedulingSuggestions;
