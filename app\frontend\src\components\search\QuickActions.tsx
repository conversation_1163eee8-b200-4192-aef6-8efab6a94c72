import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Plus, Calendar, UserPlus, FileText, Send, MapPin } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { UserRoleType } from '../../types/auth';
import { useTranslation } from '../../hooks/useTranslation';
import { motion } from 'framer-motion';

interface QuickAction {
  id: string;
  label: string;
  icon: React.ReactNode;
  path: string;
  roles: UserRoleType[];
  color: string;
}

const quickActions: QuickAction[] = [
  {
    id: 'add-client',
    label: 'quickActions.addClient',
    icon: <UserPlus className="w-4 h-4" />,
    path: '/users/clients/new',
    roles: [UserRoleType.MANAGER],
    color: 'bg-blue-100 text-blue-600 hover:bg-blue-200'
  },
  {
    id: 'add-tutor',
    label: 'quickActions.addTutor',
    icon: <UserPlus className="w-4 h-4" />,
    path: '/tutors/invitations/new',
    roles: [UserRoleType.MANAGER],
    color: 'bg-green-100 text-green-600 hover:bg-green-200'
  },
  {
    id: 'book-session',
    label: 'quickActions.bookSession',
    icon: <Calendar className="w-4 h-4" />,
    path: '/calendar/day?action=book',
    roles: [UserRoleType.MANAGER, UserRoleType.CLIENT],
    color: 'bg-orange-100 text-orange-600 hover:bg-orange-200'
  },
  {
    id: 'create-invoice',
    label: 'quickActions.createInvoice',
    icon: <FileText className="w-4 h-4" />,
    path: '/billing/invoices/new',
    roles: [UserRoleType.MANAGER],
    color: 'bg-red-100 text-red-600 hover:bg-red-200'
  },
  {
    id: 'send-message',
    label: 'quickActions.sendMessage',
    icon: <Send className="w-4 h-4" />,
    path: '/messages/chat/new',
    roles: [UserRoleType.MANAGER, UserRoleType.TUTOR, UserRoleType.CLIENT],
    color: 'bg-purple-100 text-purple-600 hover:bg-purple-200'
  },
  {
    id: 'find-tutor',
    label: 'quickActions.findTutor',
    icon: <MapPin className="w-4 h-4" />,
    path: '/map',
    roles: [UserRoleType.CLIENT],
    color: 'bg-indigo-100 text-indigo-600 hover:bg-indigo-200'
  }
];

export const QuickActions: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { user } = useAuth();

  // Filter actions based on user role
  const availableActions = quickActions.filter(action => 
    action.roles.includes(user?.activeRole as UserRoleType)
  );

  if (availableActions.length === 0) return null;

  return (
    <div className="flex items-center gap-2">
      {availableActions.map((action, index) => (
        <motion.button
          key={action.id}
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: index * 0.05 }}
          onClick={() => navigate(action.path)}
          className={`
            flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-medium
            transition-all duration-200 hover:scale-105 hover:shadow-soft
            ${action.color}
          `}
          title={t(action.label)}
        >
          {action.icon}
          <span className="hidden lg:inline">{t(action.label)}</span>
        </motion.button>
      ))}
    </div>
  );
};