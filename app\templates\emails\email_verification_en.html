<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Your Email - {{ company_name }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px 30px;
            text-align: center;
            color: white;
        }
        
        .header h1 {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .welcome {
            font-size: 18px;
            color: #2d3748;
            margin-bottom: 24px;
        }
        
        .message {
            font-size: 16px;
            color: #4a5568;
            margin-bottom: 32px;
            line-height: 1.7;
        }
        
        .verify-button {
            text-align: center;
            margin: 32px 0;
        }
        
        .verify-button a {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            transition: transform 0.2s ease;
        }
        
        .verify-button a:hover {
            transform: translateY(-2px);
        }
        
        .security-note {
            background-color: #f7fafc;
            border-left: 4px solid #4299e1;
            padding: 16px;
            margin: 24px 0;
            border-radius: 4px;
        }
        
        .security-note h3 {
            color: #2b6cb0;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .security-note p {
            color: #4a5568;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .alternative-link {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 24px 0;
        }
        
        .alternative-link p {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 8px;
        }
        
        .alternative-link code {
            background-color: #e2e8f0;
            padding: 8px 12px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            word-break: break-all;
            display: block;
            margin-top: 8px;
        }
        
        .footer {
            background-color: #f8f9fa;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e2e8f0;
        }
        
        .footer p {
            color: #6b7280;
            font-size: 14px;
            margin-bottom: 8px;
        }
        
        .footer a {
            color: #667eea;
            text-decoration: none;
        }
        
        .footer a:hover {
            text-decoration: underline;
        }
        
        @media only screen and (max-width: 600px) {
            .container {
                margin: 0;
                border-radius: 0;
            }
            
            .header, .content, .footer {
                padding: 24px 20px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .verify-button a {
                padding: 14px 24px;
                font-size: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{ company_name }}</h1>
            <p>Verify Your Email Address</p>
        </div>
        
        <div class="content">
            <div class="welcome">
                Welcome to {{ company_name }}!
            </div>
            
            <div class="message">
                Thank you for creating your account. To complete your registration and start using {{ company_name }}, 
                please verify your email address by clicking the button below.
            </div>
            
            <div class="verify-button">
                <a href="{{ verify_link }}">Verify Email Address</a>
            </div>
            
            <div class="security-note">
                <h3>🔒 Security Note</h3>
                <p>
                    This verification link will expire in 24 hours for security reasons. 
                    If you didn't create an account with {{ company_name }}, you can safely ignore this email.
                </p>
            </div>
            
            <div class="alternative-link">
                <p>If the button doesn't work, copy and paste this link into your browser:</p>
                <code>{{ verify_link }}</code>
            </div>
            
            <div class="message">
                Once verified, you'll be able to:
                <ul style="margin: 16px 0; padding-left: 20px;">
                    <li>Access your personalized dashboard</li>
                    <li>Book tutoring sessions</li>
                    <li>Manage your learning preferences</li>
                    <li>Receive important notifications</li>
                </ul>
            </div>
        </div>
        
        <div class="footer">
            <p>If you need help, contact us at <a href="mailto:{{ support_email }}">{{ support_email }}</a></p>
            <p>&copy; {{ year }} {{ company_name }}. All rights reserved.</p>
            <p>This email was sent to {{ user_email }}</p>
        </div>
    </div>
</body>
</html>