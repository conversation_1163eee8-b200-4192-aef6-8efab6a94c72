import React, { useEffect } from 'react';
import { BrowserRouter as Router, useLocation, useNavigationType, createRoutesFromChildren, matchRoutes } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from 'react-hot-toast';
import { I18nextProvider } from 'react-i18next';
import * as Sentry from '@sentry/react';
import i18n from './i18n';
import { AuthProvider } from './contexts/AuthContext';
import { AppLayout } from './components/layout/AppLayout';
import { AppRoutes } from './routes';
import { SessionManager } from './components/auth/SessionManager';
import ErrorBoundary from './components/ErrorBoundary';

// Enhanced routing with Sentry
const SentryRoutes = Sentry.withSentryRouting(AppRoutes);

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

function App() {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <I18nextProvider i18n={i18n}>
          <Router>
            <AuthProvider>
              <SessionManager>
                <AppLayout>
                  <SentryRoutes />
                </AppLayout>
              </SessionManager>
              <Toaster
              position="top-right"
              toastOptions={{
                duration: 4000,
                style: {
                  background: '#FFFFFF',
                  color: '#1D1D1F',
                  boxShadow: '0 4px 16px rgba(0, 0, 0, 0.08)',
                  borderRadius: '12px',
                  padding: '16px',
                },
                success: {
                  iconTheme: {
                    primary: '#34C759',
                    secondary: '#FFFFFF',
                  },
                },
                error: {
                  iconTheme: {
                    primary: '#FF3B30',
                    secondary: '#FFFFFF',
                  },
                },
              }}
            />
          </AuthProvider>
        </Router>
      </I18nextProvider>
    </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;