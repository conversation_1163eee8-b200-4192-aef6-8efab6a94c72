"""
Redis client configuration and utilities.
"""

import redis.asyncio as redis
import logging
from typing import Optional, Any, Dict
from app.config.settings import settings

logger = logging.getLogger(__name__)


class RedisClient:
    """Async Redis client wrapper."""
    
    def __init__(self):
        self._client: Optional[redis.Redis] = None
    
    async def get_client(self) -> redis.Redis:
        """Get or create Redis client."""
        if not self._client:
            try:
                self._client = redis.Redis(
                    host=settings.REDIS_HOST,
                    port=settings.REDIS_PORT,
                    password=settings.REDIS_PASSWORD if hasattr(settings, 'REDIS_PASSWORD') else None,
                    decode_responses=True,
                    socket_connect_timeout=5,
                    socket_timeout=5
                )
                # Test connection
                await self._client.ping()
                logger.info("Redis connection established")
            except Exception as e:
                logger.error(f"Failed to connect to Redis: {e}")
                # Return a mock client that does nothing
                return MockRedisClient()
        
        return self._client
    
    async def close(self):
        """Close Redis connection."""
        if self._client:
            await self._client.close()
            self._client = None
            logger.info("Redis connection closed")


class MockRedisClient:
    """Mock Redis client for when Redis is not available."""
    
    async def get(self, key: str) -> Optional[str]:
        return None
    
    async def set(self, key: str, value: Any, ex: Optional[int] = None) -> bool:
        return True
    
    async def delete(self, *keys: str) -> int:
        return len(keys)
    
    async def exists(self, *keys: str) -> int:
        return 0
    
    async def expire(self, key: str, seconds: int) -> bool:
        return True
    
    async def ttl(self, key: str) -> int:
        return -2
    
    async def hget(self, name: str, key: str) -> Optional[str]:
        return None
    
    async def hset(self, name: str, key: Optional[str] = None, value: Optional[str] = None, mapping: Optional[Dict] = None) -> int:
        return 1
    
    async def hdel(self, name: str, *keys: str) -> int:
        return len(keys)
    
    async def hgetall(self, name: str) -> Dict[str, str]:
        return {}
    
    async def sadd(self, name: str, *values: str) -> int:
        return len(values)
    
    async def srem(self, name: str, *values: str) -> int:
        return len(values)
    
    async def smembers(self, name: str) -> set:
        return set()
    
    async def sismember(self, name: str, value: str) -> bool:
        return False
    
    async def zadd(self, name: str, mapping: Dict[str, float]) -> int:
        return len(mapping)
    
    async def zrem(self, name: str, *members: str) -> int:
        return len(members)
    
    async def zrange(self, name: str, start: int, end: int, withscores: bool = False) -> list:
        return []
    
    async def zrevrange(self, name: str, start: int, end: int, withscores: bool = False) -> list:
        return []
    
    async def lpush(self, name: str, *values: str) -> int:
        return len(values)
    
    async def rpush(self, name: str, *values: str) -> int:
        return len(values)
    
    async def lpop(self, name: str, count: Optional[int] = None) -> Optional[str]:
        return None
    
    async def rpop(self, name: str, count: Optional[int] = None) -> Optional[str]:
        return None
    
    async def lrange(self, name: str, start: int, end: int) -> list:
        return []
    
    async def ping(self) -> bool:
        return True
    
    async def close(self):
        pass


# Global Redis client instance
redis_client = RedisClient()


async def get_redis_client() -> redis.Redis:
    """Get Redis client for dependency injection."""
    return await redis_client.get_client()