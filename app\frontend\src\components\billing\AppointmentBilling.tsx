import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';
import { 
  CreditCard, Package, Clock, AlertCircle, 
  CheckCircle, DollarSign, FileText, Info
} from 'lucide-react';
import api from '../../services/api';
import { Card } from '../common/Card';
import Button from '../common/Button';
import { Badge } from '../common/Badge';
import { Modal } from '../common/Modal';
import LoadingSpinner from '../ui/LoadingSpinner';
import toast from 'react-hot-toast';

interface Appointment {
  appointment_id: number;
  client_id: number;
  client_name: string;
  tutor_id: number;
  tutor_name: string;
  appointment_date: string;
  start_time: string;
  end_time: string;
  duration_hours: number;
  subject: string;
  client_rate: number;
  status: string;
  billing_method?: string;
  invoice_id?: number;
  subscription_usage_id?: number;
}

interface ActiveSubscription {
  subscription_id: number;
  package_name: string;
  hours_remaining: number;
  end_date: string;
  status: string;
}

interface BillingOption {
  method: 'subscription' | 'invoice';
  subscription?: ActiveSubscription;
  estimatedCost?: number;
  recommended?: boolean;
  reason?: string;
}

interface AppointmentBillingProps {
  appointmentId: number;
  onBillingComplete?: () => void;
}

const AppointmentBilling: React.FC<AppointmentBillingProps> = ({
  appointmentId,
  onBillingComplete
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [appointment, setAppointment] = useState<Appointment | null>(null);
  const [billingOptions, setBillingOptions] = useState<BillingOption[]>([]);
  const [selectedOption, setSelectedOption] = useState<BillingOption | null>(null);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [processing, setProcessing] = useState(false);

  useEffect(() => {
    fetchBillingOptions();
  }, [appointmentId]);

  const fetchBillingOptions = async () => {
    try {
      setLoading(true);
      
      // Fetch appointment details
      const appointmentResponse = await api.get<Appointment>(`/appointments/${appointmentId}`);
      setAppointment(appointmentResponse.data);
      
      // Check if already billed
      if (appointmentResponse.data.billing_method) {
        toast.info(t('billing.appointment.alreadyBilled'));
        return;
      }
      
      // Fetch available billing options
      const optionsResponse = await api.get<{
        subscriptions: ActiveSubscription[];
        estimatedInvoiceCost: number;
      }>(`/billing/appointments/${appointmentId}/billing-options`);
      
      const options: BillingOption[] = [];
      
      // Add subscription options
      optionsResponse.data.subscriptions.forEach(subscription => {
        if (subscription.hours_remaining >= appointmentResponse.data.duration_hours) {
          options.push({
            method: 'subscription',
            subscription,
            recommended: true,
            reason: t('billing.appointment.subscriptionRecommended')
          });
        }
      });
      
      // Add invoice option
      options.push({
        method: 'invoice',
        estimatedCost: optionsResponse.data.estimatedInvoiceCost,
        recommended: options.length === 0,
        reason: options.length === 0 
          ? t('billing.appointment.noSubscriptionAvailable')
          : t('billing.appointment.invoiceAlternative')
      });
      
      setBillingOptions(options);
      
      // Auto-select recommended option
      const recommended = options.find(opt => opt.recommended);
      if (recommended) {
        setSelectedOption(recommended);
      }
      
    } catch (error) {
      console.error('Error fetching billing options:', error);
      toast.error(t('billing.errors.fetchBillingOptions'));
    } finally {
      setLoading(false);
    }
  };

  const handleProcessBilling = async () => {
    if (!selectedOption || !appointment) return;
    
    try {
      setProcessing(true);
      
      const response = await api.post(`/billing/appointments/${appointmentId}/process-billing`, {
        force_invoice: selectedOption.method === 'invoice'
      });
      
      toast.success(
        selectedOption.method === 'subscription'
          ? t('billing.appointment.subscriptionBillingSuccess', {
              hours: appointment.duration_hours,
              remaining: response.data.hours_remaining
            })
          : t('billing.appointment.invoiceBillingSuccess')
      );
      
      setShowConfirmModal(false);
      onBillingComplete?.();
      
    } catch (error) {
      console.error('Error processing billing:', error);
      toast.error(t('billing.errors.processBilling'));
    } finally {
      setProcessing(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD'
    }).format(amount);
  };

  const getOptionIcon = (option: BillingOption) => {
    return option.method === 'subscription' ? Package : FileText;
  };

  const getOptionColor = (option: BillingOption) => {
    if (option.recommended) return 'text-green-600';
    return 'text-gray-600';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!appointment) {
    return (
      <Card className="p-6">
        <p className="text-gray-600">{t('billing.appointment.notFound')}</p>
      </Card>
    );
  }

  if (appointment.billing_method) {
    return (
      <Card className="p-6">
        <div className="flex items-center space-x-3">
          <CheckCircle className="w-6 h-6 text-green-600" />
          <div>
            <p className="font-medium text-gray-900">
              {t('billing.appointment.alreadyProcessed')}
            </p>
            <p className="text-sm text-gray-500">
              {t('billing.appointment.billingMethod')}: {' '}
              <Badge variant={appointment.billing_method === 'subscription' ? 'success' : 'info'}>
                {t(`billing.appointment.${appointment.billing_method}`)}
              </Badge>
            </p>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Appointment Details */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          {t('billing.appointment.details')}
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-gray-500">{t('billing.appointment.client')}</p>
            <p className="font-medium text-gray-900">{appointment.client_name}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">{t('billing.appointment.tutor')}</p>
            <p className="font-medium text-gray-900">{appointment.tutor_name}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">{t('billing.appointment.dateTime')}</p>
            <p className="font-medium text-gray-900">
              {format(new Date(appointment.appointment_date), 'MMM d, yyyy')} • {appointment.start_time} - {appointment.end_time}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-500">{t('billing.appointment.duration')}</p>
            <p className="font-medium text-gray-900">
              {appointment.duration_hours} {t('common.hours')}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-500">{t('billing.appointment.subject')}</p>
            <p className="font-medium text-gray-900">{appointment.subject}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">{t('billing.appointment.rate')}</p>
            <p className="font-medium text-gray-900">
              {formatCurrency(appointment.client_rate)}/hr
            </p>
          </div>
        </div>
      </Card>

      {/* Billing Options */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          {t('billing.appointment.billingOptions')}
        </h3>
        <p className="text-sm text-gray-500 mb-4">
          {t('billing.appointment.selectBillingMethod')}
        </p>
        
        <div className="space-y-3">
          {billingOptions.map((option, index) => {
            const Icon = getOptionIcon(option);
            const isSelected = selectedOption === option;
            
            return (
              <div
                key={index}
                className={`
                  relative rounded-lg border-2 p-4 cursor-pointer transition-all
                  ${isSelected 
                    ? 'border-accent-red bg-red-50' 
                    : 'border-gray-200 hover:border-gray-300'
                  }
                `}
                onClick={() => setSelectedOption(option)}
              >
                <div className="flex items-start space-x-3">
                  <Icon className={`w-5 h-5 mt-0.5 ${getOptionColor(option)}`} />
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium text-gray-900">
                        {option.method === 'subscription' 
                          ? t('billing.appointment.useSubscription')
                          : t('billing.appointment.createInvoice')
                        }
                      </h4>
                      {option.recommended && (
                        <Badge variant="success" size="sm">
                          {t('billing.appointment.recommended')}
                        </Badge>
                      )}
                    </div>
                    
                    {option.subscription && (
                      <div className="mt-2 space-y-1">
                        <p className="text-sm text-gray-600">
                          <span className="font-medium">{option.subscription.package_name}</span>
                        </p>
                        <div className="flex items-center space-x-4 text-sm">
                          <div className="flex items-center space-x-1">
                            <Clock className="w-4 h-4 text-gray-400" />
                            <span className="text-gray-600">
                              {option.subscription.hours_remaining}h {t('billing.appointment.remaining')}
                            </span>
                          </div>
                          <div className="text-gray-500">
                            {t('billing.appointment.expiresOn')} {format(new Date(option.subscription.end_date), 'MMM d, yyyy')}
                          </div>
                        </div>
                      </div>
                    )}
                    
                    {option.method === 'invoice' && (
                      <div className="mt-2">
                        <p className="text-sm text-gray-600">
                          {t('billing.appointment.estimatedCost')}: {' '}
                          <span className="font-medium text-gray-900">
                            {formatCurrency(option.estimatedCost || 0)}
                          </span>
                        </p>
                      </div>
                    )}
                    
                    {option.reason && (
                      <p className="text-xs text-gray-500 mt-2">{option.reason}</p>
                    )}
                  </div>
                </div>
                
                {isSelected && (
                  <div className="absolute top-4 right-4">
                    <CheckCircle className="w-5 h-5 text-accent-red" />
                  </div>
                )}
              </div>
            );
          })}
        </div>
        
        {/* How It Works Info */}
        <div className="mt-6 bg-blue-50 rounded-lg p-4">
          <div className="flex items-start">
            <Info className="w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-blue-800">
              <p className="font-semibold mb-1">{t('billing.appointment.howItWorks')}</p>
              <ul className="space-y-1">
                <li className="flex items-start">
                  <span className="mr-2">•</span>
                  <span>{t('billing.appointment.dualBillingInfo1')}</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2">•</span>
                  <span>{t('billing.appointment.dualBillingInfo2')}</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2">•</span>
                  <span>{t('billing.appointment.dualBillingInfo3')}</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
        
        <div className="mt-6 flex justify-end">
          <Button
            variant="primary"
            leftIcon={<CreditCard className="w-4 h-4" />}
            onClick={() => setShowConfirmModal(true)}
            disabled={!selectedOption}
          >
            {t('billing.appointment.processBilling')}
          </Button>
        </div>
      </Card>

      {/* Confirmation Modal */}
      <Modal
        isOpen={showConfirmModal}
        onClose={() => setShowConfirmModal(false)}
        title={t('billing.appointment.confirmBilling')}
      >
        <div className="p-6 space-y-4">
          {selectedOption && appointment && (
            <>
              <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                <p className="text-sm text-gray-600">
                  <span className="font-medium">{t('billing.appointment.method')}:</span> {' '}
                  {selectedOption.method === 'subscription' 
                    ? t('billing.appointment.subscriptionDeduction')
                    : t('billing.appointment.invoiceCreation')
                  }
                </p>
                <p className="text-sm text-gray-600">
                  <span className="font-medium">{t('billing.appointment.amount')}:</span> {' '}
                  {selectedOption.method === 'subscription'
                    ? `${appointment.duration_hours}h`
                    : formatCurrency(selectedOption.estimatedCost || 0)
                  }
                </p>
                {selectedOption.subscription && (
                  <p className="text-sm text-gray-600">
                    <span className="font-medium">{t('billing.appointment.remainingAfter')}:</span> {' '}
                    {selectedOption.subscription.hours_remaining - appointment.duration_hours}h
                  </p>
                )}
              </div>
              
              <div className="bg-yellow-50 rounded-lg p-4">
                <div className="flex items-start">
                  <AlertCircle className="w-5 h-5 text-yellow-600 mr-2 mt-0.5 flex-shrink-0" />
                  <p className="text-sm text-yellow-800">
                    {t('billing.appointment.confirmationWarning')}
                  </p>
                </div>
              </div>
            </>
          )}
          
          <div className="flex justify-end space-x-3">
            <Button
              variant="secondary"
              onClick={() => setShowConfirmModal(false)}
            >
              {t('common.cancel')}
            </Button>
            <Button
              variant="primary"
              onClick={handleProcessBilling}
              loading={processing}
              leftIcon={<CheckCircle className="w-4 h-4" />}
            >
              {t('billing.appointment.confirmProcess')}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default AppointmentBilling;
