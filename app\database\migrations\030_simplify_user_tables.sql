-- ================================================
-- Migration: Simplify User Tables Structure
-- ================================================
-- This migration consolidates 16 user-related tables into 7 simplified tables
-- for better maintainability and performance
--
-- Version: 030
-- Date: 2025-06-24
-- ================================================

-- ============================================
-- Step 1: Create New Simplified Tables
-- ============================================

-- Unified user preferences table
CREATE TABLE user_preferences (
    user_id INTEGER PRIMARY KEY REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    
    -- Language preferences
    preferred_language VARCHAR(5) DEFAULT 'en' CHECK (preferred_language IN ('en', 'fr')),
    quebec_french_preference BOOLEAN DEFAULT true,
    
    -- Date and time formatting
    date_format VARCHAR(20) DEFAULT 'medium' CHECK (date_format IN ('short', 'medium', 'long', 'full')),
    time_format VARCHAR(10) DEFAULT 'auto' CHECK (time_format IN ('auto', '12h', '24h')),
    
    -- Currency and numbers
    currency_display VARCHAR(20) DEFAULT 'symbol' CHECK (currency_display IN ('symbol', 'code', 'name')),
    number_precision INTEGER DEFAULT 2 CHECK (number_precision >= 0 AND number_precision <= 6),
    
    -- UI preferences (JSONB for flexibility)
    ui_settings JSONB DEFAULT '{}'::jsonb,
    
    -- Notification preferences (JSONB for all notification types)
    notification_settings JSONB DEFAULT '{
        "appointment_reminder": {"push": true, "sms": true, "email": true},
        "payment_due": {"push": true, "sms": true, "email": true},
        "session_confirmed": {"push": true, "sms": false, "email": true},
        "general": {"push": true, "sms": false, "email": true}
    }'::jsonb,
    
    -- Quick actions and pinned items
    pinned_actions TEXT[] DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Unified authentication tokens table
CREATE TABLE auth_tokens (
    token_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    token_type VARCHAR(20) NOT NULL CHECK (token_type IN ('password_reset', 'email_verify', 'session', 'api_key')),
    token_hash VARCHAR(255) NOT NULL UNIQUE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    
    -- Metadata for different token types (JSONB for flexibility)
    metadata JSONB DEFAULT '{}',
    
    -- Usage tracking
    used_at TIMESTAMP WITH TIME ZONE,
    ip_address INET,
    user_agent TEXT,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes will be created below
    CHECK (expires_at > created_at)
);

-- Simplified user security settings
CREATE TABLE user_security (
    user_id INTEGER PRIMARY KEY REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    
    -- Two-factor authentication
    two_factor_enabled BOOLEAN DEFAULT false,
    two_factor_method VARCHAR(10) CHECK (two_factor_method IN ('sms', 'email', 'totp')),
    two_factor_secret TEXT, -- Encrypted
    two_factor_backup_codes TEXT[], -- Encrypted array
    two_factor_verified_at TIMESTAMP WITH TIME ZONE,
    
    -- Trusted devices (JSONB array)
    trusted_devices JSONB DEFAULT '[]'::jsonb,
    
    -- Security settings
    require_password_change BOOLEAN DEFAULT false,
    last_password_change TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP WITH TIME ZONE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ============================================
-- Step 2: Migrate Existing Data
-- ============================================

-- Migrate user preferences from multiple tables
INSERT INTO user_preferences (
    user_id,
    preferred_language,
    quebec_french_preference,
    date_format,
    time_format,
    currency_display,
    number_precision,
    created_at,
    updated_at
)
SELECT DISTINCT
    ua.user_id,
    COALESCE(ua.preferred_language, 'en'),
    COALESCE(ua.quebec_french_preference, true),
    COALESCE(fp.date_format, 'medium'),
    COALESCE(fp.time_format, 'auto'),
    COALESCE(fp.currency_display, 'symbol'),
    COALESCE(fp.number_precision, 2),
    COALESCE(fp.created_at, ua.created_at),
    COALESCE(fp.updated_at, ua.created_at)
FROM user_accounts ua
LEFT JOIN user_formatting_preferences fp ON ua.user_id = fp.user_id
WHERE ua.deleted_at IS NULL
ON CONFLICT (user_id) DO NOTHING;

-- Migrate notification preferences
UPDATE user_preferences p
SET notification_settings = (
    SELECT jsonb_object_agg(
        np.notification_type::text,
        jsonb_build_object(
            'push', np.push_enabled,
            'sms', np.sms_enabled,
            'email', np.email_enabled
        )
    )
    FROM notification_preferences np
    WHERE np.user_id = p.user_id
)
WHERE EXISTS (
    SELECT 1 FROM notification_preferences np WHERE np.user_id = p.user_id
);

-- Migrate pinned actions
UPDATE user_preferences p
SET pinned_actions = (
    SELECT array_agg(pa.action_type ORDER BY pa.pinned_at)
    FROM user_pinned_actions pa
    WHERE pa.user_id = p.user_id
)
WHERE EXISTS (
    SELECT 1 FROM user_pinned_actions pa WHERE pa.user_id = p.user_id
);

-- Migrate password reset tokens
INSERT INTO auth_tokens (user_id, token_type, token_hash, expires_at, metadata, used_at, created_at)
SELECT 
    user_id,
    'password_reset',
    token::text, -- UUID to text
    expires_at,
    jsonb_build_object(
        'initiated_by', initiated_by,
        'initiated_by_user_id', initiated_by_user_id
    ),
    used_at,
    created_at
FROM password_reset_tokens
WHERE used_at IS NULL AND expires_at > CURRENT_TIMESTAMP;

-- Migrate email verification tokens
INSERT INTO auth_tokens (user_id, token_type, token_hash, expires_at, metadata, used_at, ip_address, user_agent, created_at)
SELECT 
    user_id,
    'email_verify',
    token::text,
    expires_at,
    jsonb_build_object(
        'email', email,
        'verified_from_ip', verified_from_ip
    ),
    verified_at,
    ip_address,
    user_agent,
    created_at
FROM email_verification_tokens
WHERE verified_at IS NULL AND expires_at > CURRENT_TIMESTAMP;

-- Migrate active sessions
INSERT INTO auth_tokens (user_id, token_type, token_hash, expires_at, metadata, ip_address, user_agent, created_at)
SELECT 
    user_id,
    'session',
    token_hash,
    expires_at,
    jsonb_build_object(
        'session_id', session_id,
        'role_type', role_type,
        'last_activity', last_activity
    ),
    ip_address,
    user_agent,
    created_at
FROM user_sessions
WHERE is_active = true AND expires_at > CURRENT_TIMESTAMP;

-- Migrate 2FA settings
INSERT INTO user_security (
    user_id,
    two_factor_enabled,
    two_factor_method,
    two_factor_secret,
    two_factor_verified_at,
    created_at,
    updated_at
)
SELECT 
    user_id,
    is_enabled,
    method::varchar,
    secret,
    verified_at,
    created_at,
    updated_at
FROM two_factor_setups
WHERE deleted_at IS NULL AND is_primary = true
ON CONFLICT (user_id) DO NOTHING;

-- Migrate trusted devices to JSONB
UPDATE user_security s
SET trusted_devices = (
    SELECT jsonb_agg(
        jsonb_build_object(
            'device_fingerprint', td.device_fingerprint,
            'device_name', td.device_name,
            'last_used_at', td.last_used_at,
            'expires_at', td.expires_at,
            'ip_address', td.ip_address,
            'user_agent', td.user_agent
        ) ORDER BY td.last_used_at DESC
    )
    FROM trusted_devices td
    WHERE td.user_id = s.user_id 
    AND td.deleted_at IS NULL 
    AND td.expires_at > CURRENT_TIMESTAMP
)
WHERE EXISTS (
    SELECT 1 FROM trusted_devices td 
    WHERE td.user_id = s.user_id 
    AND td.deleted_at IS NULL
);

-- ============================================
-- Step 3: Create Indexes
-- ============================================

-- Indexes for user_preferences
CREATE INDEX idx_user_preferences_language ON user_preferences(preferred_language);
CREATE INDEX idx_user_preferences_updated ON user_preferences(updated_at);

-- Indexes for auth_tokens
CREATE INDEX idx_auth_tokens_user_id ON auth_tokens(user_id);
CREATE INDEX idx_auth_tokens_type ON auth_tokens(token_type);
CREATE INDEX idx_auth_tokens_hash ON auth_tokens(token_hash) WHERE used_at IS NULL;
CREATE INDEX idx_auth_tokens_expires ON auth_tokens(expires_at) WHERE used_at IS NULL;
CREATE INDEX idx_auth_tokens_composite ON auth_tokens(token_type, user_id, expires_at) WHERE used_at IS NULL;

-- Indexes for user_security
CREATE INDEX idx_user_security_2fa ON user_security(two_factor_enabled) WHERE two_factor_enabled = true;
CREATE INDEX idx_user_security_locked ON user_security(locked_until) WHERE locked_until IS NOT NULL;

-- ============================================
-- Step 4: Create Triggers
-- ============================================

-- Update trigger for user_preferences
CREATE TRIGGER update_user_preferences_updated_at 
BEFORE UPDATE ON user_preferences
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Update trigger for user_security
CREATE TRIGGER update_user_security_updated_at 
BEFORE UPDATE ON user_security
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================
-- Step 5: Create Helper Functions
-- ============================================

-- Function to clean up expired tokens
CREATE OR REPLACE FUNCTION cleanup_expired_tokens() RETURNS void AS $$
BEGIN
    DELETE FROM auth_tokens 
    WHERE expires_at < CURRENT_TIMESTAMP 
    AND used_at IS NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to check if user has valid session
CREATE OR REPLACE FUNCTION has_valid_session(p_user_id INTEGER, p_token_hash VARCHAR) RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM auth_tokens
        WHERE user_id = p_user_id
        AND token_hash = p_token_hash
        AND token_type = 'session'
        AND expires_at > CURRENT_TIMESTAMP
        AND used_at IS NULL
    );
END;
$$ LANGUAGE plpgsql;

-- ============================================
-- Step 6: Update Views
-- ============================================

-- Update the users view to remove old preference columns
DROP VIEW IF EXISTS users CASCADE;

CREATE OR REPLACE VIEW users AS
SELECT 
    user_id,
    email,
    password_hash,
    google_id,
    created_at,
    updated_at,
    deleted_at,
    email_verified,
    email_verified_at,
    two_factor_required,
    two_factor_grace_ends_at
FROM user_accounts;

-- ============================================
-- Step 7: Drop Old Tables (After Verification)
-- ============================================

-- IMPORTANT: Only run this after verifying data migration was successful!
-- These drops are commented out for safety. Uncomment when ready.

/*
-- Drop preference history tables
DROP TABLE IF EXISTS user_language_preferences_history CASCADE;
DROP TABLE IF EXISTS user_formatting_preferences_history CASCADE;
DROP TABLE IF EXISTS language_usage_analytics CASCADE;

-- Drop old preference tables
DROP TABLE IF EXISTS user_formatting_preferences CASCADE;
DROP TABLE IF EXISTS notification_preferences CASCADE;
DROP TABLE IF EXISTS user_pinned_actions CASCADE;
DROP TABLE IF EXISTS quick_action_usage CASCADE;

-- Drop old token tables
DROP TABLE IF EXISTS password_reset_tokens CASCADE;
DROP TABLE IF EXISTS email_verification_tokens CASCADE;
DROP TABLE IF EXISTS user_sessions CASCADE;

-- Drop old 2FA tables
DROP TABLE IF EXISTS two_factor_challenges CASCADE;
DROP TABLE IF EXISTS two_factor_setups CASCADE;
DROP TABLE IF EXISTS trusted_devices CASCADE;

-- Remove old columns from user_accounts
ALTER TABLE user_accounts 
DROP COLUMN IF EXISTS preferred_language,
DROP COLUMN IF EXISTS language_auto_detect,
DROP COLUMN IF EXISTS language_source,
DROP COLUMN IF EXISTS language_updated_at,
DROP COLUMN IF EXISTS quebec_french_preference;
*/

-- ============================================
-- Comments for Documentation
-- ============================================

COMMENT ON TABLE user_preferences IS 'Unified user preferences including language, formatting, UI, and notifications';
COMMENT ON TABLE auth_tokens IS 'Unified authentication tokens for sessions, password resets, email verification, etc.';
COMMENT ON TABLE user_security IS 'User security settings including 2FA and trusted devices';

COMMENT ON COLUMN user_preferences.ui_settings IS 'JSON object containing UI preferences like theme, layout, etc.';
COMMENT ON COLUMN user_preferences.notification_settings IS 'JSON object with notification preferences by type and channel';
COMMENT ON COLUMN auth_tokens.metadata IS 'JSON object with token-specific data (session info, reset initiator, etc.)';
COMMENT ON COLUMN user_security.trusted_devices IS 'JSON array of trusted device objects';