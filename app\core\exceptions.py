"""
Comprehensive exception handling framework for TutorAide.

This module provides custom exception classes, error codes, and centralized
error handling with proper logging and user-friendly error responses.
"""

import traceback
from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional, Union, List

from app.core.logging import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Error<PERSON>ontext
from app.core.timezone import now_est


class ErrorCode(Enum):
    """Enumeration of error codes for consistent error handling."""
    
    # Generic errors
    GENERIC_ERROR = "GENERIC_ERROR"
    INTERNAL_ERROR = "INTERNAL_ERROR"
    
    # Validation errors
    VALIDATION_ERROR = "VALIDATION_ERROR"
    INVALID_INPUT = "INVALID_INPUT"
    MISSING_REQUIRED_FIELD = "MISSING_REQUIRED_FIELD"
    INVALID_FORMAT = "INVALID_FORMAT"
    
    # Authentication errors
    AUTHENTICATION_ERROR = "AUTHENTICATION_ERROR"
    INVALID_CREDENTIALS = "INVALID_CREDENTIALS"
    TOKEN_EXPIRED = "TOKEN_EXPIRED"
    TOKEN_INVALID = "TOKEN_INVALID"
    ACCOUNT_LOCKED = "ACCOUNT_LOCKED"
    ACCOUNT_DISABLED = "ACCOUNT_DISABLED"
    
    # Authorization errors
    AUTHORIZATION_ERROR = "AUTHORIZATION_ERROR"
    INSUFFICIENT_PERMISSIONS = "INSUFFICIENT_PERMISSIONS"
    ROLE_REQUIRED = "ROLE_REQUIRED"
    RESOURCE_ACCESS_DENIED = "RESOURCE_ACCESS_DENIED"
    
    # Business logic errors
    BUSINESS_LOGIC_ERROR = "BUSINESS_LOGIC_ERROR"
    APPOINTMENT_CONFLICT = "APPOINTMENT_CONFLICT"
    TUTOR_UNAVAILABLE = "TUTOR_UNAVAILABLE"
    BOOKING_WINDOW_CLOSED = "BOOKING_WINDOW_CLOSED"
    INSUFFICIENT_SUBSCRIPTION_HOURS = "INSUFFICIENT_SUBSCRIPTION_HOURS"
    PAYMENT_REQUIRED = "PAYMENT_REQUIRED"
    
    # Resource errors
    RESOURCE_NOT_FOUND = "RESOURCE_NOT_FOUND"
    RESOURCE_ALREADY_EXISTS = "RESOURCE_ALREADY_EXISTS"
    RESOURCE_LOCKED = "RESOURCE_LOCKED"
    RESOURCE_EXPIRED = "RESOURCE_EXPIRED"
    
    # External service errors
    EXTERNAL_SERVICE_ERROR = "EXTERNAL_SERVICE_ERROR"
    PAYMENT_SERVICE_ERROR = "PAYMENT_SERVICE_ERROR"
    SMS_SERVICE_ERROR = "SMS_SERVICE_ERROR"
    EMAIL_SERVICE_ERROR = "EMAIL_SERVICE_ERROR"
    GEOCODING_SERVICE_ERROR = "GEOCODING_SERVICE_ERROR"
    
    # Database errors
    DATABASE_ERROR = "DATABASE_ERROR"
    CONNECTION_ERROR = "CONNECTION_ERROR"
    QUERY_ERROR = "QUERY_ERROR"
    CONSTRAINT_VIOLATION = "CONSTRAINT_VIOLATION"
    TRANSACTION_ERROR = "TRANSACTION_ERROR"
    
    # Rate limiting
    RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED"
    TOO_MANY_REQUESTS = "TOO_MANY_REQUESTS"


class TutorAideException(Exception):
    """Base exception class for TutorAide application."""
    
    def __init__(
        self,
        message: str,
        error_code: ErrorCode = ErrorCode.GENERIC_ERROR,
        user_message: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        http_status_code: int = 500
    ):
        """
        Initialize TutorAide exception.
        
        Args:
            message: Technical error message for logging
            error_code: Specific error code for categorization
            user_message: User-friendly message for display
            details: Additional error context
            http_status_code: HTTP status code for API responses
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.user_message = user_message or "An error occurred. Please try again."
        self.details = details or {}
        self.http_status_code = http_status_code
        self.timestamp = now_est()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for API responses."""
        return {
            "error_code": self.error_code.value,
            "message": self.user_message,
            "details": self.details,
            "timestamp": self.timestamp.isoformat()
        }


class ValidationError(TutorAideException):
    """Exception for input validation errors."""
    
    def __init__(
        self,
        message: str,
        field: Optional[str] = None,
        value: Optional[Any] = None,
        **kwargs
    ):
        """
        Initialize validation error.
        
        Args:
            message: Technical error message
            field: Field name that failed validation
            value: Invalid value that was provided
        """
        self.field = field
        self.value = value
        
        details = kwargs.get('details', {})
        if field:
            details['field'] = field
        if value is not None:
            details['value'] = str(value)
        
        super().__init__(
            message=message,
            error_code=ErrorCode.VALIDATION_ERROR,
            user_message=f"Invalid input provided{f' for {field}' if field else ''}. Please check your data and try again.",
            details=details,
            http_status_code=400,
            **kwargs
        )


class SecurityError(TutorAideException):
    """Exception for security-related errors."""
    
    def __init__(
        self,
        message: str,
        security_event: Optional[str] = None,
        ip_address: Optional[str] = None,
        **kwargs
    ):
        """
        Initialize security error.
        
        Args:
            message: Technical error message
            security_event: Type of security event (xss_attempt, sql_injection, etc.)
            ip_address: IP address of the request
        """
        self.security_event = security_event
        self.ip_address = ip_address
        
        details = kwargs.get('details', {})
        if security_event:
            details['security_event'] = security_event
        if ip_address:
            details['ip_address'] = ip_address
        
        super().__init__(
            message=message,
            error_code=ErrorCode.VALIDATION_ERROR,  # Use validation error code for security issues
            user_message="Invalid request detected. Please check your input and try again.",
            details=details,
            http_status_code=400,
            **kwargs
        )


class TokenExpiredError(TutorAideException):
    """Exception for expired tokens."""
    
    def __init__(
        self,
        message: str,
        token_type: Optional[str] = None,
        **kwargs
    ):
        """
        Initialize token expired error.
        
        Args:
            message: Technical error message
            token_type: Type of token that expired
        """
        self.token_type = token_type
        
        details = kwargs.get('details', {})
        if token_type:
            details['token_type'] = token_type
        
        super().__init__(
            message=message,
            error_code=ErrorCode.TOKEN_EXPIRED,
            user_message="Your session has expired. Please log in again.",
            details=details,
            http_status_code=401,
            **kwargs
        )


class AuthenticationError(TutorAideException):
    """Exception for authentication failures."""
    
    def __init__(
        self,
        message: str,
        auth_method: Optional[str] = None,
        **kwargs
    ):
        """
        Initialize authentication error.
        
        Args:
            message: Technical error message
            auth_method: Authentication method that failed (password, oauth, token)
        """
        self.auth_method = auth_method
        
        details = kwargs.get('details', {})
        if auth_method:
            details['auth_method'] = auth_method
        
        super().__init__(
            message=message,
            error_code=ErrorCode.AUTHENTICATION_ERROR,
            user_message="Authentication failed. Please check your credentials and try again.",
            details=details,
            http_status_code=401,
            **kwargs
        )


class AuthorizationError(TutorAideException):
    """Exception for authorization failures."""
    
    def __init__(
        self,
        message: str,
        required_permission: Optional[str] = None,
        user_role: Optional[str] = None,
        **kwargs
    ):
        """
        Initialize authorization error.
        
        Args:
            message: Technical error message
            required_permission: Permission required for the operation
            user_role: User's current role
        """
        self.required_permission = required_permission
        self.user_role = user_role
        
        details = kwargs.pop('details', {})
        if required_permission:
            details['required_permission'] = required_permission
        if user_role:
            details['user_role'] = user_role
        
        super().__init__(
            message=message,
            error_code=ErrorCode.AUTHORIZATION_ERROR,
            user_message="You don't have permission to perform this action.",
            details=details,
            http_status_code=403,
            **kwargs
        )


class BusinessLogicError(TutorAideException):
    """Exception for business rule violations."""
    
    def __init__(
        self,
        message: str,
        business_rule: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        """
        Initialize business logic error.
        
        Args:
            message: Technical error message
            business_rule: Business rule that was violated
            context: Additional context about the violation
        """
        self.business_rule = business_rule
        self.context = context or {}
        
        details = kwargs.get('details', {})
        if business_rule:
            details['business_rule'] = business_rule
        details.update(self.context)
        
        super().__init__(
            message=message,
            error_code=ErrorCode.BUSINESS_LOGIC_ERROR,
            user_message="This action cannot be completed due to business rules. Please check the requirements and try again.",
            details=details,
            http_status_code=422,
            **kwargs
        )


class ResourceNotFoundError(TutorAideException):
    """Exception for when a resource is not found."""
    
    def __init__(
        self,
        message: str,
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        **kwargs
    ):
        """
        Initialize resource not found error.
        
        Args:
            message: Technical error message
            resource_type: Type of resource that was not found
            resource_id: ID of the resource that was not found
        """
        self.resource_type = resource_type
        self.resource_id = resource_id
        
        details = kwargs.get('details', {})
        if resource_type:
            details['resource_type'] = resource_type
        if resource_id:
            details['resource_id'] = resource_id
        
        super().__init__(
            message=message,
            error_code=ErrorCode.RESOURCE_NOT_FOUND,
            user_message="The requested resource was not found.",
            details=details,
            http_status_code=404,
            **kwargs
        )


class ForbiddenError(TutorAideException):
    """Exception for forbidden access."""
    
    def __init__(
        self,
        message: str,
        **kwargs
    ):
        """
        Initialize forbidden error.
        
        Args:
            message: Technical error message
        """
        super().__init__(
            message=message,
            error_code=ErrorCode.AUTHORIZATION_ERROR,
            user_message="Access denied. You don't have permission to perform this action.",
            http_status_code=403,
            **kwargs
        )


class PermissionDeniedError(AuthorizationError):
    """Exception for when user lacks specific permission."""
    
    def __init__(
        self,
        message: str,
        resource_type: Optional[str] = None,
        action: Optional[str] = None,
        resource_id: Optional[Union[str, int]] = None,
        **kwargs
    ):
        """
        Initialize permission denied error.
        
        Args:
            message: Technical error message
            resource_type: Type of resource being accessed
            action: Action being attempted
            resource_id: ID of the specific resource
        """
        self.resource_type = resource_type
        self.action = action
        self.resource_id = resource_id
        
        details = kwargs.pop('details', {})
        if resource_type:
            details['resource_type'] = resource_type
        if action:
            details['action'] = action
        if resource_id:
            details['resource_id'] = str(resource_id)
        
        super().__init__(
            message=message,
            required_permission=f"{action} {resource_type}" if action and resource_type else None,
            details=details,
            **kwargs
        )


class ResourceOwnershipError(AuthorizationError):
    """Exception for when user doesn't own the requested resource."""
    
    def __init__(
        self,
        message: str,
        resource_type: Optional[str] = None,
        resource_id: Optional[Union[str, int]] = None,
        owner_id: Optional[Union[str, int]] = None,
        **kwargs
    ):
        """
        Initialize resource ownership error.
        
        Args:
            message: Technical error message
            resource_type: Type of resource being accessed
            resource_id: ID of the resource
            owner_id: ID of the actual owner
        """
        self.resource_type = resource_type
        self.resource_id = resource_id
        self.owner_id = owner_id
        
        details = kwargs.get('details', {})
        if resource_type:
            details['resource_type'] = resource_type
        if resource_id:
            details['resource_id'] = str(resource_id)
        if owner_id:
            details['owner_id'] = str(owner_id)
        
        super().__init__(
            message=message,
            required_permission=f"ownership of {resource_type} {resource_id}",
            details=details,
            **kwargs
        )


class InsufficientRoleError(AuthorizationError):
    """Exception for when user lacks required role."""
    
    def __init__(
        self,
        message: str,
        required_roles: Optional[List[str]] = None,
        user_roles: Optional[List[str]] = None,
        **kwargs
    ):
        """
        Initialize insufficient role error.
        
        Args:
            message: Technical error message
            required_roles: List of roles required for access
            user_roles: List of roles the user currently has
        """
        self.required_roles = required_roles or []
        self.user_roles = user_roles or []
        
        details = kwargs.get('details', {})
        if required_roles:
            details['required_roles'] = required_roles
        if user_roles:
            details['user_roles'] = user_roles
        
        super().__init__(
            message=message,
            required_permission=f"one of roles: {', '.join(required_roles)}" if required_roles else None,
            user_role=', '.join(user_roles) if user_roles else None,
            details=details,
            **kwargs
        )


class RelationshipAccessError(AuthorizationError):
    """Exception for when user lacks required relationship for access."""
    
    def __init__(
        self,
        message: str,
        relationship_type: Optional[str] = None,
        resource_id: Optional[Union[str, int]] = None,
        **kwargs
    ):
        """
        Initialize relationship access error.
        
        Args:
            message: Technical error message
            relationship_type: Type of relationship required (e.g., "parent-child")
            resource_id: ID of the resource requiring relationship access
        """
        self.relationship_type = relationship_type
        self.resource_id = resource_id
        
        details = kwargs.get('details', {})
        if relationship_type:
            details['relationship_type'] = relationship_type
        if resource_id:
            details['resource_id'] = str(resource_id)
        
        super().__init__(
            message=message,
            required_permission=f"{relationship_type} relationship to resource {resource_id}",
            details=details,
            **kwargs
        )


class DuplicateResourceError(TutorAideException):
    """Exception for when a resource already exists."""
    
    def __init__(
        self,
        message: str,
        resource_type: Optional[str] = None,
        conflicting_field: Optional[str] = None,
        **kwargs
    ):
        """
        Initialize duplicate resource error.
        
        Args:
            message: Technical error message
            resource_type: Type of resource that already exists
            conflicting_field: Field that has the conflict
        """
        self.resource_type = resource_type
        self.conflicting_field = conflicting_field
        
        details = kwargs.get('details', {})
        if resource_type:
            details['resource_type'] = resource_type
        if conflicting_field:
            details['conflicting_field'] = conflicting_field
        
        super().__init__(
            message=message,
            error_code=ErrorCode.RESOURCE_ALREADY_EXISTS,
            user_message="A resource with these details already exists.",
            details=details,
            http_status_code=409,
            **kwargs
        )


class ConflictError(TutorAideException):
    """Exception for general resource conflicts."""
    
    def __init__(
        self,
        message: str,
        conflict_type: Optional[str] = None,
        conflicting_resource: Optional[str] = None,
        **kwargs
    ):
        """
        Initialize conflict error.
        
        Args:
            message: Technical error message
            conflict_type: Type of conflict
            conflicting_resource: Resource causing the conflict
        """
        self.conflict_type = conflict_type
        self.conflicting_resource = conflicting_resource
        
        details = kwargs.get('details', {})
        if conflict_type:
            details['conflict_type'] = conflict_type
        if conflicting_resource:
            details['conflicting_resource'] = conflicting_resource
        
        super().__init__(
            message=message,
            error_code=ErrorCode.RESOURCE_ALREADY_EXISTS,
            user_message="The requested operation conflicts with existing data.",
            details=details,
            http_status_code=409,
            **kwargs
        )


class DatabaseError(TutorAideException):
    """Base exception for database-related errors."""
    
    def __init__(
        self,
        message: str,
        **kwargs
    ):
        """
        Initialize database error.
        
        Args:
            message: Technical error message
        """
        super().__init__(
            message=message,
            error_code=ErrorCode.DATABASE_ERROR,
            user_message="A database error occurred. Please try again later.",
            http_status_code=500,
            **kwargs
        )


class DatabaseOperationError(DatabaseError):
    """Exception for database operation failures."""
    
    def __init__(
        self,
        message: str,
        operation: Optional[str] = None,
        **kwargs
    ):
        """
        Initialize database operation error.
        
        Args:
            message: Technical error message
            operation: Database operation that failed
        """
        self.operation = operation
        
        details = kwargs.get('details', {})
        if operation:
            details['operation'] = operation
        
        super().__init__(
            message=message,
            error_code=ErrorCode.DATABASE_ERROR,
            user_message="A database error occurred. Please try again later.",
            details=details,
            http_status_code=500,
            **kwargs
        )


class ExternalServiceError(TutorAideException):
    """Exception for external service failures."""
    
    def __init__(
        self,
        message: str,
        service_name: Optional[str] = None,
        service_error_code: Optional[str] = None,
        retry_after: Optional[int] = None,
        **kwargs
    ):
        """
        Initialize external service error.
        
        Args:
            message: Technical error message
            service_name: Name of the external service
            service_error_code: Error code from the external service
            retry_after: Seconds to wait before retrying
        """
        self.service_name = service_name
        self.service_error_code = service_error_code
        self.retry_after = retry_after
        
        details = kwargs.get('details', {})
        if service_name:
            details['service_name'] = service_name
        if service_error_code:
            details['service_error_code'] = service_error_code
        if retry_after:
            details['retry_after'] = retry_after
        
        super().__init__(
            message=message,
            error_code=ErrorCode.EXTERNAL_SERVICE_ERROR,
            user_message="A service is temporarily unavailable. Please try again in a few moments.",
            details=details,
            http_status_code=503,
            **kwargs
        )



class RateLimitError(TutorAideException):
    """Exception for rate limiting violations."""
    
    def __init__(
        self,
        message: str,
        limit: Optional[int] = None,
        window_seconds: Optional[int] = None,
        retry_after: Optional[int] = None,
        **kwargs
    ):
        """
        Initialize rate limit error.
        
        Args:
            message: Technical error message
            limit: Rate limit that was exceeded
            window_seconds: Time window for the rate limit
            retry_after: Seconds to wait before retrying
        """
        self.limit = limit
        self.window_seconds = window_seconds
        self.retry_after = retry_after
        
        details = kwargs.get('details', {})
        if limit:
            details['limit'] = limit
        if window_seconds:
            details['window_seconds'] = window_seconds
        if retry_after:
            details['retry_after'] = retry_after
        
        super().__init__(
            message=message,
            error_code=ErrorCode.RATE_LIMIT_EXCEEDED,
            user_message="Too many requests. Please wait a moment before trying again.",
            details=details,
            http_status_code=429,
            **kwargs
        )


class ErrorHandler:
    """Centralized error handler for the application."""
    
    def __init__(self):
        """Initialize error handler."""
        self.logger = TutorAideLogger("error_handler")
    
    def handle_exception(
        self,
        exception: Exception,
        context: Optional[ErrorContext] = None
    ) -> Dict[str, Any]:
        """
        Handle exception and return standardized error response.
        
        Args:
            exception: Exception that occurred
            context: Error context information
            
        Returns:
            Standardized error response dictionary
        """
        # Create context if not provided
        if context is None:
            context = ErrorContext()
        
        # Handle TutorAide exceptions
        if isinstance(exception, TutorAideException):
            self._log_tutoraide_exception(exception, context)
            response = exception.to_dict()
        else:
            # Handle unexpected exceptions
            self._log_unexpected_exception(exception, context)
            response = self._create_generic_error_response()
        
        # Add request context to response
        if context.request_id:
            response["request_id"] = context.request_id
        
        return response
    
    def _log_tutoraide_exception(
        self,
        exception: TutorAideException,
        context: ErrorContext
    ):
        """Log TutorAide exception with context."""
        extra = context.to_dict()
        extra.update({
            "error_code": exception.error_code.value,
            "http_status_code": exception.http_status_code,
            "exception_details": exception.details
        })
        
        if exception.http_status_code >= 500:
            self.logger.error(
                f"TutorAide exception: {exception.message}",
                exception=exception,
                extra=extra
            )
        else:
            self.logger.warning(
                f"TutorAide exception: {exception.message}",
                extra=extra
            )
    
    def _log_unexpected_exception(
        self,
        exception: Exception,
        context: ErrorContext
    ):
        """Log unexpected exception with context."""
        extra = context.to_dict()
        extra.update({
            "error_code": ErrorCode.INTERNAL_ERROR.value,
            "exception_type": type(exception).__name__
        })
        
        self.logger.error(
            f"Unexpected exception: {str(exception)}",
            exception=exception,
            extra=extra
        )
    
    def _create_generic_error_response(self) -> Dict[str, Any]:
        """Create generic error response for unexpected exceptions."""
        return {
            "error_code": ErrorCode.INTERNAL_ERROR.value,
            "message": "An internal error occurred. Please try again later.",
            "details": {},
            "timestamp": now_est().isoformat()
        }
    
    def log_security_event(
        self,
        event_type: str,
        details: Optional[Dict[str, Any]] = None,
        context: Optional[ErrorContext] = None
    ):
        """Log security event for monitoring and auditing."""
        extra = {}
        
        if context:
            extra.update(context.to_dict())
        
        if details:
            extra.update(details)
        
        extra["event_type"] = event_type
        
        self.logger.security(
            f"Security event: {event_type}",
            extra=extra
        )
    
    def log_performance_metric(
        self,
        operation: str,
        duration_ms: float,
        details: Optional[Dict[str, Any]] = None,
        context: Optional[ErrorContext] = None
    ):
        """Log performance metric for monitoring."""
        extra = {}
        
        if context:
            extra.update(context.to_dict())
        
        if details:
            extra.update(details)
        
        extra["operation"] = operation
        
        self.logger.performance(
            f"Performance metric: {operation}",
            duration_ms=duration_ms,
            extra=extra
        )
    
    def log_audit_event(
        self,
        action: str,
        actor_id: Optional[str] = None,
        target_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        context: Optional[ErrorContext] = None
    ):
        """Log audit event for compliance and tracking."""
        extra = {}
        
        if context:
            extra.update(context.to_dict())
        
        if details:
            extra.update(details)
        
        extra.update({
            "action": action,
            "actor_id": actor_id,
            "target_id": target_id
        })
        
        self.logger.audit(
            f"Audit event: {action}",
            extra=extra
        )


# Global error handler instance
error_handler = ErrorHandler()