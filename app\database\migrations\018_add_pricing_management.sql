-- Migration: 018_add_pricing_management.sql (Fixed)
-- Description: Add pricing management system for platform commission tracking
-- Author: System
-- Date: 2024-01-15

-- Drop indexes that might fail if tables already exist
DROP INDEX IF EXISTS idx_pricing_rules_service;
DROP INDEX IF EXISTS idx_pricing_rules_dates;
DROP INDEX IF EXISTS idx_tutor_rates_tutor;
DROP INDEX IF EXISTS idx_client_rates_client;
DROP INDEX IF EXISTS idx_surge_rules_active;

-- Create pricing_rules table for base service pricing
CREATE TABLE IF NOT EXISTS pricing_rules (
    rule_id SERIAL PRIMARY KEY,
    service_type VARCHAR(50) NOT NULL,
    subject_area VARCHAR(100),
    base_client_rate DECIMAL(10,2) NOT NULL CHECK (base_client_rate > 0),
    base_tutor_rate DECIMAL(10,2) NOT NULL CHECK (base_tutor_rate > 0),
    commission_percentage DECIMAL(5,2) NOT NULL CHECK (commission_percentage >= 0 AND commission_percentage <= 100),
    commission_amount DECIMAL(10,2) NOT NULL CHECK (commission_amount >= 0),
    priority INTEGER DEFAULT 100, -- Higher priority rules take precedence
    is_active BOOLEAN DEFAULT TRUE,
    effective_date DATE NOT NULL DEFAULT CURRENT_DATE,
    expiry_date DATE,
    created_by INTEGER REFERENCES user_accounts(user_id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    CONSTRAINT client_rate_higher CHECK (base_client_rate > base_tutor_rate)
);

-- Add is_active column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'pricing_rules' 
                   AND column_name = 'is_active') THEN
        ALTER TABLE pricing_rules ADD COLUMN is_active BOOLEAN DEFAULT TRUE;
    END IF;
END $$;

-- Create tutor_service_rates table for custom tutor rates
CREATE TABLE IF NOT EXISTS tutor_service_rates (
    rate_id SERIAL PRIMARY KEY,
    tutor_id INTEGER NOT NULL REFERENCES tutor_profiles(tutor_id),
    service_type VARCHAR(50) NOT NULL,
    subject_area VARCHAR(100),
    custom_rate DECIMAL(10,2) CHECK (custom_rate > 0),
    rate_type VARCHAR(20) NOT NULL CHECK (rate_type IN ('client_rate', 'tutor_rate')),
    commission_override_percentage DECIMAL(5,2) CHECK (commission_override_percentage >= 0 AND commission_override_percentage <= 100),
    is_active BOOLEAN DEFAULT TRUE,
    effective_date DATE NOT NULL DEFAULT CURRENT_DATE,
    expiry_date DATE,
    approved_by INTEGER REFERENCES user_accounts(user_id),
    approval_date TIMESTAMP WITH TIME ZONE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(tutor_id, service_type, subject_area)
);

-- Add is_active column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'tutor_service_rates' 
                   AND column_name = 'is_active') THEN
        ALTER TABLE tutor_service_rates ADD COLUMN is_active BOOLEAN DEFAULT TRUE;
    END IF;
END $$;

-- Create client_service_rates table for negotiated client rates
CREATE TABLE IF NOT EXISTS client_service_rates (
    rate_id SERIAL PRIMARY KEY,
    client_id INTEGER NOT NULL REFERENCES client_profiles(client_id),
    service_type VARCHAR(50) NOT NULL,
    subject_area VARCHAR(100),
    negotiated_rate DECIMAL(10,2) NOT NULL CHECK (negotiated_rate > 0),
    discount_percentage DECIMAL(5,2) CHECK (discount_percentage >= 0 AND discount_percentage <= 100),
    is_active BOOLEAN DEFAULT TRUE,
    effective_date DATE NOT NULL DEFAULT CURRENT_DATE,
    expiry_date DATE,
    approved_by INTEGER REFERENCES user_accounts(user_id),
    approval_date TIMESTAMP WITH TIME ZONE,
    contract_reference VARCHAR(255),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(client_id, service_type, subject_area)
);

-- Add is_active column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'client_service_rates' 
                   AND column_name = 'is_active') THEN
        ALTER TABLE client_service_rates ADD COLUMN is_active BOOLEAN DEFAULT TRUE;
    END IF;
END $$;

-- Create surge_pricing_rules table
CREATE TABLE IF NOT EXISTS surge_pricing_rules (
    surge_rule_id SERIAL PRIMARY KEY,
    rule_name VARCHAR(100) NOT NULL,
    service_type VARCHAR(50),
    day_of_week INTEGER CHECK (day_of_week >= 0 AND day_of_week <= 6), -- 0=Sunday, 6=Saturday
    time_start TIME,
    time_end TIME,
    date_specific DATE, -- For specific dates like holidays
    multiplier DECIMAL(3,2) NOT NULL CHECK (multiplier >= 1.00 AND multiplier <= 3.00),
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER REFERENCES user_accounts(user_id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add is_active column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'surge_pricing_rules' 
                   AND column_name = 'is_active') THEN
        ALTER TABLE surge_pricing_rules ADD COLUMN is_active BOOLEAN DEFAULT TRUE;
    END IF;
END $$;

-- Create commission_structures table
CREATE TABLE IF NOT EXISTS commission_structures (
    structure_id SERIAL PRIMARY KEY,
    structure_name VARCHAR(100) NOT NULL,
    description TEXT,
    base_commission_rate DECIMAL(5,2) NOT NULL CHECK (base_commission_rate >= 0 AND base_commission_rate <= 1),
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER REFERENCES user_accounts(user_id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Add is_active column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'commission_structures' 
                   AND column_name = 'is_active') THEN
        ALTER TABLE commission_structures ADD COLUMN is_active BOOLEAN DEFAULT TRUE;
    END IF;
END $$;

-- Create commission_tiers table
CREATE TABLE IF NOT EXISTS commission_tiers (
    tier_id SERIAL PRIMARY KEY,
    structure_id INTEGER NOT NULL REFERENCES commission_structures(structure_id),
    min_monthly_revenue DECIMAL(10,2) NOT NULL CHECK (min_monthly_revenue >= 0),
    max_monthly_revenue DECIMAL(10,2) CHECK (max_monthly_revenue > min_monthly_revenue),
    commission_rate DECIMAL(5,2) NOT NULL CHECK (commission_rate >= 0 AND commission_rate <= 1),
    bonus_percentage DECIMAL(5,2) DEFAULT 0 CHECK (bonus_percentage >= 0),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(structure_id, min_monthly_revenue)
);

-- Create platform_revenue_tracking table
CREATE TABLE IF NOT EXISTS platform_revenue_tracking (
    tracking_id SERIAL PRIMARY KEY,
    appointment_id INTEGER NOT NULL REFERENCES appointment_sessions(appointment_id),
    client_amount DECIMAL(10,2) NOT NULL,
    tutor_amount DECIMAL(10,2) NOT NULL,
    platform_commission DECIMAL(10,2) NOT NULL,
    commission_percentage DECIMAL(5,2) NOT NULL,
    pricing_rule_id INTEGER REFERENCES pricing_rules(rule_id),
    surge_applied BOOLEAN DEFAULT FALSE,
    surge_multiplier DECIMAL(3,2) DEFAULT 1.00,
    tracked_date DATE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for performance (check if columns exist first)
DO $$
BEGIN
    -- Create indexes only if the is_active column exists
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'pricing_rules' AND column_name = 'is_active') THEN
        CREATE INDEX IF NOT EXISTS idx_pricing_rules_service ON pricing_rules(service_type, subject_area) WHERE is_active = true;
        CREATE INDEX IF NOT EXISTS idx_pricing_rules_dates ON pricing_rules(effective_date, expiry_date) WHERE is_active = true;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'tutor_service_rates' AND column_name = 'is_active') THEN
        CREATE INDEX IF NOT EXISTS idx_tutor_rates_tutor ON tutor_service_rates(tutor_id, service_type) WHERE is_active = true;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'client_service_rates' AND column_name = 'is_active') THEN
        CREATE INDEX IF NOT EXISTS idx_client_rates_client ON client_service_rates(client_id, service_type) WHERE is_active = true;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'surge_pricing_rules' AND column_name = 'is_active') THEN
        CREATE INDEX IF NOT EXISTS idx_surge_rules_active ON surge_pricing_rules(service_type, day_of_week) WHERE is_active = true;
    END IF;
END $$;

CREATE INDEX IF NOT EXISTS idx_revenue_tracking_date ON platform_revenue_tracking(tracked_date);
CREATE INDEX IF NOT EXISTS idx_revenue_tracking_appointment ON platform_revenue_tracking(appointment_id);

-- Create function to calculate pricing for an appointment
CREATE OR REPLACE FUNCTION calculate_appointment_pricing(
    p_service_type VARCHAR(50),
    p_subject_area VARCHAR(100),
    p_tutor_id INTEGER,
    p_client_id INTEGER,
    p_appointment_datetime TIMESTAMP WITH TIME ZONE
) RETURNS TABLE (
    client_rate DECIMAL(10,2),
    tutor_rate DECIMAL(10,2),
    platform_commission DECIMAL(10,2),
    commission_percentage DECIMAL(5,2),
    surge_multiplier DECIMAL(3,2),
    pricing_rule_id INTEGER
) AS $$
DECLARE
    v_base_client_rate DECIMAL(10,2);
    v_base_tutor_rate DECIMAL(10,2);
    v_commission_percentage DECIMAL(5,2);
    v_pricing_rule_id INTEGER;
    v_surge_multiplier DECIMAL(3,2) := 1.00;
    v_final_client_rate DECIMAL(10,2);
    v_final_tutor_rate DECIMAL(10,2);
    v_platform_commission DECIMAL(10,2);
BEGIN
    -- Get base pricing from rules
    SELECT 
        pr.base_client_rate,
        pr.base_tutor_rate,
        pr.commission_percentage,
        pr.rule_id
    INTO 
        v_base_client_rate,
        v_base_tutor_rate,
        v_commission_percentage,
        v_pricing_rule_id
    FROM pricing_rules pr
    WHERE pr.service_type = p_service_type
        AND (pr.subject_area = p_subject_area OR pr.subject_area IS NULL)
        AND pr.is_active = true
        AND pr.effective_date <= CURRENT_DATE
        AND (pr.expiry_date IS NULL OR pr.expiry_date >= CURRENT_DATE)
    ORDER BY 
        pr.priority DESC,
        CASE WHEN pr.subject_area IS NOT NULL THEN 1 ELSE 0 END DESC
    LIMIT 1;
    
    -- Check for tutor custom rates
    SELECT tsr.custom_rate
    INTO v_base_tutor_rate
    FROM tutor_service_rates tsr
    WHERE tsr.tutor_id = p_tutor_id
        AND tsr.service_type = p_service_type
        AND (tsr.subject_area = p_subject_area OR tsr.subject_area IS NULL)
        AND tsr.rate_type = 'tutor_rate'
        AND tsr.is_active = true
        AND tsr.effective_date <= CURRENT_DATE
        AND (tsr.expiry_date IS NULL OR tsr.expiry_date >= CURRENT_DATE)
    LIMIT 1;
    
    -- Check for client negotiated rates
    SELECT csr.negotiated_rate
    INTO v_base_client_rate
    FROM client_service_rates csr
    WHERE csr.client_id = p_client_id
        AND csr.service_type = p_service_type
        AND (csr.subject_area = p_subject_area OR csr.subject_area IS NULL)
        AND csr.is_active = true
        AND csr.effective_date <= CURRENT_DATE
        AND (csr.expiry_date IS NULL OR csr.expiry_date >= CURRENT_DATE)
    LIMIT 1;
    
    -- Check for surge pricing
    SELECT spr.multiplier
    INTO v_surge_multiplier
    FROM surge_pricing_rules spr
    WHERE (spr.service_type = p_service_type OR spr.service_type IS NULL)
        AND spr.is_active = true
        AND (
            -- Check specific date
            (spr.date_specific = p_appointment_datetime::DATE)
            OR
            -- Check day of week and time
            (spr.day_of_week = EXTRACT(DOW FROM p_appointment_datetime)
             AND p_appointment_datetime::TIME BETWEEN spr.time_start AND spr.time_end)
        )
    ORDER BY spr.multiplier DESC
    LIMIT 1;
    
    -- Calculate final rates
    v_final_client_rate := v_base_client_rate * v_surge_multiplier;
    v_final_tutor_rate := v_base_tutor_rate * v_surge_multiplier;
    v_platform_commission := v_final_client_rate - v_final_tutor_rate;
    
    RETURN QUERY SELECT 
        v_final_client_rate,
        v_final_tutor_rate,
        v_platform_commission,
        v_commission_percentage,
        v_surge_multiplier,
        v_pricing_rule_id;
END;
$$ LANGUAGE plpgsql;

-- Create view for revenue analytics
CREATE OR REPLACE VIEW platform_revenue_analytics AS
SELECT 
    DATE_TRUNC('month', prt.tracked_date) as month,
    COUNT(DISTINCT prt.appointment_id) as total_appointments,
    SUM(prt.client_amount) as total_client_revenue,
    SUM(prt.tutor_amount) as total_tutor_payments,
    SUM(prt.platform_commission) as total_platform_commission,
    AVG(prt.commission_percentage) as avg_commission_percentage,
    COUNT(DISTINCT CASE WHEN prt.surge_applied THEN prt.appointment_id END) as surge_appointments
FROM platform_revenue_tracking prt
GROUP BY DATE_TRUNC('month', prt.tracked_date);

-- Add comments
COMMENT ON TABLE pricing_rules IS 'Base pricing rules for different service types and subjects';
COMMENT ON TABLE tutor_service_rates IS 'Custom rates for individual tutors';
COMMENT ON TABLE client_service_rates IS 'Negotiated rates for specific clients';
COMMENT ON TABLE surge_pricing_rules IS 'Dynamic pricing rules for peak times';
COMMENT ON TABLE commission_structures IS 'Tiered commission structures';
COMMENT ON TABLE platform_revenue_tracking IS 'Tracks revenue and commission for each appointment';
COMMENT ON FUNCTION calculate_appointment_pricing IS 'Calculates pricing for an appointment including surge and custom rates';
COMMENT ON VIEW platform_revenue_analytics IS 'Monthly revenue analytics for the platform';