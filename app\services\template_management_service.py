"""
Template management service for emails and SMS with multi-language support.
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from enum import Enum

from app.database.repositories.template_repository import TemplateRepository
from app.database.repositories.user_repository import UserRepository
from app.models.template_models import (
    Template, TemplateType, TemplateStatus,
    TemplateVariable, TemplatePreview
)
from app.models.user_models import User, UserRole
from app.core.exceptions import TemplateError, ValidationError

logger = logging.getLogger(__name__)


class TemplateCategory(Enum):
    """Template categories for organization."""
    APPOINTMENT = "appointment"
    BILLING = "billing"
    AUTHENTICATION = "authentication"
    NOTIFICATION = "notification"
    MARKETING = "marketing"
    SYSTEM = "system"


class TemplateManagementService:
    """
    Service for managing email and SMS templates.
    
    Features:
    - Multi-language support (EN/FR)
    - Template versioning
    - Variable validation
    - Preview generation
    - Template categories
    - Approval workflow
    - Template inheritance
    - A/B testing support
    """
    
    def __init__(self):
        self.template_repo = TemplateRepository()
        self.user_repo = UserRepository()
        
        # Default template variables
        self.common_variables = {
            'user_name': 'User\'s full name',
            'first_name': 'User\'s first name',
            'last_name': 'User\'s last name',
            'email': 'User\'s email address',
            'phone_number': 'User\'s phone number',
            'company_name': 'TutorAide',
            'website_url': 'www.tutoraide.ca',
            'support_email': '<EMAIL>',
            'current_date': 'Current date',
            'current_time': 'Current time'
        }
        
        # Template-specific variables
        self.template_variables = {
            'appointment_reminder': {
                'appointment_date': 'Date of the appointment',
                'appointment_time': 'Time of the appointment',
                'tutor_name': 'Name of the tutor',
                'student_name': 'Name of the student',
                'subject': 'Subject being tutored',
                'location': 'Location of the session',
                'session_type': 'Type of session (online/in-person)',
                'duration': 'Duration of the session'
            },
            'billing_notification': {
                'invoice_number': 'Invoice number',
                'amount_due': 'Amount due',
                'due_date': 'Payment due date',
                'payment_url': 'Link to payment portal',
                'items': 'List of billable items'
            },
            'password_reset': {
                'reset_link': 'Password reset link',
                'expiry_time': 'Link expiration time',
                'ip_address': 'IP address of request'
            },
            'email_verification': {
                'verification_link': 'Email verification link',
                'verification_code': 'Verification code'
            }
        }
    
    async def create_template(
        self,
        name: str,
        template_type: TemplateType,
        category: TemplateCategory,
        subject: Optional[str],
        content: str,
        language: str,
        created_by: int,
        variables: Optional[List[str]] = None,
        parent_template_id: Optional[int] = None,
        is_default: bool = False
    ) -> Template:
        """
        Create a new template.
        
        Args:
            name: Template name
            template_type: Type (email/sms)
            category: Template category
            subject: Subject line (email only)
            content: Template content
            language: Language code (en/fr)
            created_by: User ID creating template
            variables: List of template variables
            parent_template_id: Parent template for inheritance
            is_default: Whether this is a default template
            
        Returns:
            Created template
        """
        try:
            # Verify user has permission to create templates
            user = await self.user_repo.get_user(created_by)
            if not user or UserRole.MANAGER not in user.roles:
                raise ValidationError("Only managers can create templates")
            
            # Validate template content
            self._validate_template_content(content, template_type)
            
            # Extract variables from content
            extracted_variables = self._extract_variables(content)
            if subject:
                extracted_variables.update(self._extract_variables(subject))
            
            # Validate variables
            if variables:
                self._validate_variables(variables, extracted_variables)
            
            # Create template
            template = await self.template_repo.create_template(
                name=name,
                template_type=template_type,
                category=category.value,
                subject=subject,
                content=content,
                language=language,
                created_by=created_by,
                variables=list(extracted_variables),
                parent_template_id=parent_template_id,
                is_default=is_default,
                status=TemplateStatus.DRAFT
            )
            
            logger.info(f"Created template '{name}' (ID: {template.template_id})")
            
            return template
            
        except Exception as e:
            logger.error(f"Error creating template: {e}")
            raise TemplateError(f"Failed to create template: {str(e)}")
    
    async def update_template(
        self,
        template_id: int,
        updates: Dict[str, Any],
        updated_by: int
    ) -> Template:
        """
        Update an existing template.
        
        Args:
            template_id: Template ID to update
            updates: Fields to update
            updated_by: User ID making update
            
        Returns:
            Updated template
        """
        try:
            # Verify user permissions
            user = await self.user_repo.get_user(updated_by)
            if not user or UserRole.MANAGER not in user.roles:
                raise ValidationError("Only managers can update templates")
            
            # Get existing template
            template = await self.template_repo.get_template(template_id)
            if not template:
                raise ValidationError("Template not found")
            
            # Validate updates
            if 'content' in updates:
                self._validate_template_content(updates['content'], template.template_type)
                
                # Extract and validate variables
                extracted_variables = self._extract_variables(updates['content'])
                if 'subject' in updates:
                    extracted_variables.update(self._extract_variables(updates['subject']))
                
                updates['variables'] = list(extracted_variables)
            
            # Create new version if template is published
            if template.status == TemplateStatus.PUBLISHED:
                # Archive current version
                await self.template_repo.archive_template(template_id)
                
                # Create new version
                updates['version'] = template.version + 1
                updates['status'] = TemplateStatus.DRAFT
                updates['parent_template_id'] = template_id
            
            # Update template
            updated_template = await self.template_repo.update_template(
                template_id,
                updates,
                updated_by
            )
            
            logger.info(f"Updated template {template_id}")
            
            return updated_template
            
        except Exception as e:
            logger.error(f"Error updating template: {e}")
            raise TemplateError(f"Failed to update template: {str(e)}")
    
    async def get_template(
        self,
        template_id: int,
        language: Optional[str] = None
    ) -> Optional[Template]:
        """
        Get a template by ID.
        
        Args:
            template_id: Template ID
            language: Optional language filter
            
        Returns:
            Template or None
        """
        try:
            template = await self.template_repo.get_template(template_id)
            
            if template and language and template.language != language:
                # Try to find template in requested language
                alternative = await self.template_repo.get_template_by_name(
                    template.name,
                    template.template_type,
                    language
                )
                return alternative or template
            
            return template
            
        except Exception as e:
            logger.error(f"Error getting template: {e}")
            raise
    
    async def get_template_by_name(
        self,
        name: str,
        template_type: TemplateType,
        language: str = 'en'
    ) -> Optional[Template]:
        """
        Get a template by name and type.
        
        Args:
            name: Template name
            template_type: Template type
            language: Language code
            
        Returns:
            Template or None
        """
        try:
            return await self.template_repo.get_template_by_name(
                name, template_type, language
            )
            
        except Exception as e:
            logger.error(f"Error getting template by name: {e}")
            raise
    
    async def list_templates(
        self,
        template_type: Optional[TemplateType] = None,
        category: Optional[TemplateCategory] = None,
        language: Optional[str] = None,
        status: Optional[TemplateStatus] = None,
        search_query: Optional[str] = None,
        limit: int = 50,
        offset: int = 0
    ) -> Dict[str, Any]:
        """
        List templates with filtering and pagination.
        
        Args:
            template_type: Filter by type
            category: Filter by category
            language: Filter by language
            status: Filter by status
            search_query: Search in name/content
            limit: Results limit
            offset: Results offset
            
        Returns:
            Paginated template list
        """
        try:
            filters = {}
            if template_type:
                filters['template_type'] = template_type
            if category:
                filters['category'] = category.value
            if language:
                filters['language'] = language
            if status:
                filters['status'] = status
            if search_query:
                filters['search_query'] = search_query
            
            templates, total_count = await self.template_repo.list_templates(
                filters=filters,
                limit=limit,
                offset=offset
            )
            
            return {
                'templates': templates,
                'pagination': {
                    'total': total_count,
                    'limit': limit,
                    'offset': offset,
                    'has_more': offset + limit < total_count
                }
            }
            
        except Exception as e:
            logger.error(f"Error listing templates: {e}")
            raise
    
    async def publish_template(
        self,
        template_id: int,
        published_by: int
    ) -> Template:
        """
        Publish a template (make it available for use).
        
        Args:
            template_id: Template ID to publish
            published_by: User ID publishing template
            
        Returns:
            Published template
        """
        try:
            # Verify user permissions
            user = await self.user_repo.get_user(published_by)
            if not user or UserRole.MANAGER not in user.roles:
                raise ValidationError("Only managers can publish templates")
            
            # Get template
            template = await self.template_repo.get_template(template_id)
            if not template:
                raise ValidationError("Template not found")
            
            # Validate template before publishing
            await self._validate_template_for_publishing(template)
            
            # Publish template
            published_template = await self.template_repo.publish_template(
                template_id,
                published_by
            )
            
            logger.info(f"Published template {template_id}")
            
            return published_template
            
        except Exception as e:
            logger.error(f"Error publishing template: {e}")
            raise TemplateError(f"Failed to publish template: {str(e)}")
    
    async def archive_template(
        self,
        template_id: int,
        archived_by: int,
        reason: Optional[str] = None
    ) -> bool:
        """
        Archive a template.
        
        Args:
            template_id: Template ID to archive
            archived_by: User ID archiving template
            reason: Optional reason for archiving
            
        Returns:
            Success status
        """
        try:
            # Verify user permissions
            user = await self.user_repo.get_user(archived_by)
            if not user or UserRole.MANAGER not in user.roles:
                raise ValidationError("Only managers can archive templates")
            
            success = await self.template_repo.archive_template(
                template_id,
                archived_by,
                reason
            )
            
            if success:
                logger.info(f"Archived template {template_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error archiving template: {e}")
            raise
    
    async def preview_template(
        self,
        template_id: int,
        variables: Dict[str, str]
    ) -> TemplatePreview:
        """
        Generate a preview of the template with sample data.
        
        Args:
            template_id: Template ID
            variables: Sample variable values
            
        Returns:
            Template preview
        """
        try:
            template = await self.template_repo.get_template(template_id)
            if not template:
                raise ValidationError("Template not found")
            
            # Render template with variables
            rendered_subject = None
            if template.subject:
                rendered_subject = self._render_template(template.subject, variables)
            
            rendered_content = self._render_template(template.content, variables)
            
            # Generate preview
            preview = TemplatePreview(
                template_id=template_id,
                subject=rendered_subject,
                content=rendered_content,
                variables_used=list(variables.keys()),
                missing_variables=self._find_missing_variables(template, variables),
                generated_at=datetime.now()
            )
            
            return preview
            
        except Exception as e:
            logger.error(f"Error generating template preview: {e}")
            raise TemplateError(f"Failed to generate preview: {str(e)}")
    
    async def get_template_variables(
        self,
        template_name: str
    ) -> List[TemplateVariable]:
        """
        Get available variables for a template.
        
        Args:
            template_name: Template name
            
        Returns:
            List of available variables
        """
        try:
            variables = []
            
            # Add common variables
            for var_name, description in self.common_variables.items():
                variables.append(TemplateVariable(
                    name=var_name,
                    description=description,
                    required=False,
                    example=self._get_variable_example(var_name)
                ))
            
            # Add template-specific variables
            if template_name in self.template_variables:
                for var_name, description in self.template_variables[template_name].items():
                    variables.append(TemplateVariable(
                        name=var_name,
                        description=description,
                        required=True,
                        example=self._get_variable_example(var_name)
                    ))
            
            return variables
            
        except Exception as e:
            logger.error(f"Error getting template variables: {e}")
            raise
    
    async def duplicate_template(
        self,
        template_id: int,
        new_name: str,
        new_language: Optional[str] = None,
        created_by: int
    ) -> Template:
        """
        Duplicate an existing template.
        
        Args:
            template_id: Template ID to duplicate
            new_name: Name for new template
            new_language: Optional new language
            created_by: User creating duplicate
            
        Returns:
            Duplicated template
        """
        try:
            # Get original template
            original = await self.template_repo.get_template(template_id)
            if not original:
                raise ValidationError("Template not found")
            
            # Create duplicate
            duplicate = await self.create_template(
                name=new_name,
                template_type=original.template_type,
                category=TemplateCategory(original.category),
                subject=original.subject,
                content=original.content,
                language=new_language or original.language,
                created_by=created_by,
                variables=original.variables,
                parent_template_id=original.template_id
            )
            
            logger.info(f"Duplicated template {template_id} as {duplicate.template_id}")
            
            return duplicate
            
        except Exception as e:
            logger.error(f"Error duplicating template: {e}")
            raise TemplateError(f"Failed to duplicate template: {str(e)}")
    
    async def validate_template_syntax(
        self,
        content: str,
        template_type: TemplateType
    ) -> Dict[str, Any]:
        """
        Validate template syntax and variables.
        
        Args:
            content: Template content
            template_type: Template type
            
        Returns:
            Validation results
        """
        try:
            errors = []
            warnings = []
            variables = self._extract_variables(content)
            
            # Validate template content
            try:
                self._validate_template_content(content, template_type)
            except ValidationError as e:
                errors.append(str(e))
            
            # Check for common issues
            if template_type == TemplateType.SMS and len(content) > 160:
                warnings.append("SMS content exceeds 160 characters")
            
            # Check for unmatched braces
            open_braces = content.count('{')
            close_braces = content.count('}')
            if open_braces != close_braces:
                errors.append("Unmatched curly braces in template")
            
            # Check for nested variables
            if '{{' in content or '}}' in content:
                warnings.append("Possible nested variables detected")
            
            return {
                'valid': len(errors) == 0,
                'errors': errors,
                'warnings': warnings,
                'variables': list(variables),
                'variable_count': len(variables)
            }
            
        except Exception as e:
            logger.error(f"Error validating template: {e}")
            return {
                'valid': False,
                'errors': [str(e)],
                'warnings': [],
                'variables': [],
                'variable_count': 0
            }
    
    def _validate_template_content(
        self,
        content: str,
        template_type: TemplateType
    ) -> None:
        """Validate template content."""
        if not content or not content.strip():
            raise ValidationError("Template content cannot be empty")
        
        if template_type == TemplateType.SMS:
            # SMS specific validations
            if len(content) > 1600:  # MMS limit
                raise ValidationError("SMS content too long (max 1600 characters)")
        
        # Check for dangerous content
        dangerous_patterns = ['<script', 'javascript:', 'data:']
        content_lower = content.lower()
        for pattern in dangerous_patterns:
            if pattern in content_lower:
                raise ValidationError(f"Potentially dangerous content detected: {pattern}")
    
    def _extract_variables(self, content: str) -> set:
        """Extract template variables from content."""
        import re
        pattern = r'\{([^}]+)\}'
        matches = re.findall(pattern, content)
        return set(matches)
    
    def _validate_variables(
        self,
        provided_variables: List[str],
        extracted_variables: set
    ) -> None:
        """Validate that provided variables match extracted ones."""
        provided_set = set(provided_variables)
        
        # Check for variables in content but not in provided list
        missing = extracted_variables - provided_set
        if missing:
            raise ValidationError(f"Variables found in content but not declared: {missing}")
        
        # Check for declared variables not in content
        extra = provided_set - extracted_variables
        if extra:
            raise ValidationError(f"Declared variables not found in content: {extra}")
    
    def _render_template(
        self,
        template: str,
        variables: Dict[str, str]
    ) -> str:
        """Render template with variables."""
        rendered = template
        for key, value in variables.items():
            rendered = rendered.replace(f"{{{key}}}", str(value))
        return rendered
    
    def _find_missing_variables(
        self,
        template: Template,
        provided_variables: Dict[str, str]
    ) -> List[str]:
        """Find variables in template that weren't provided."""
        template_vars = set(template.variables or [])
        provided_vars = set(provided_variables.keys())
        return list(template_vars - provided_vars)
    
    def _get_variable_example(self, variable_name: str) -> str:
        """Get example value for a variable."""
        examples = {
            'user_name': 'John Doe',
            'first_name': 'John',
            'last_name': 'Doe',
            'email': '<EMAIL>',
            'phone_number': '+****************',
            'appointment_date': '2024-01-15',
            'appointment_time': '14:00',
            'tutor_name': 'Jane Smith',
            'student_name': 'Alex Johnson',
            'subject': 'Mathematics',
            'amount_due': '$75.00',
            'invoice_number': 'INV-2024-001',
            'company_name': 'TutorAide'
        }
        return examples.get(variable_name, 'Sample Value')
    
    async def _validate_template_for_publishing(self, template: Template) -> None:
        """Validate template before publishing."""
        # Check that template has content
        if not template.content.strip():
            raise ValidationError("Template content cannot be empty")
        
        # Check that all variables are valid
        variables = self._extract_variables(template.content)
        if template.subject:
            variables.update(self._extract_variables(template.subject))
        
        # Ensure no undefined variables
        if not variables.issubset(set(template.variables or [])):
            undefined = variables - set(template.variables or [])
            raise ValidationError(f"Undefined variables: {undefined}")
        
        # Check for language-specific template if this is default
        if template.is_default and template.language == 'en':
            # Ensure French version exists
            french_template = await self.template_repo.get_template_by_name(
                template.name,
                template.template_type,
                'fr'
            )
            if not french_template:
                raise ValidationError("French version required for default templates")