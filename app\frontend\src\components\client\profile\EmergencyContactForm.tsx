import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Input } from '../../common/Input';
import Button from '../../common/Button';
import { Select } from '../../common/Select';
import { Shield, Phone, User, Users } from 'lucide-react';

interface EmergencyContactFormProps {
  profile: {
    emergency_contact_name: string | null;
    emergency_contact_phone: string | null;
    emergency_contact_relation: string | null;
  };
  onUpdate: (data: any) => void;
  saving: boolean;
}

export const EmergencyContactForm: React.FC<EmergencyContactFormProps> = ({
  profile,
  onUpdate,
  saving
}) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    emergency_contact_name: profile.emergency_contact_name || '',
    emergency_contact_phone: profile.emergency_contact_phone || '',
    emergency_contact_relation: profile.emergency_contact_relation || ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  const relationshipOptions = [
    { value: 'spouse', label: t('client.profile.relationships.spouse') },
    { value: 'parent', label: t('client.profile.relationships.parent') },
    { value: 'sibling', label: t('client.profile.relationships.sibling') },
    { value: 'child', label: t('client.profile.relationships.child') },
    { value: 'friend', label: t('client.profile.relationships.friend') },
    { value: 'other', label: t('client.profile.relationships.other') }
  ];

  const formatPhoneNumber = (value: string) => {
    const cleaned = value.replace(/\D/g, '');
    
    if (cleaned.length <= 3) {
      return cleaned;
    } else if (cleaned.length <= 6) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3)}`;
    } else {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6, 10)}`;
    }
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPhoneNumber(e.target.value);
    setFormData({ ...formData, emergency_contact_phone: formatted });
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    // If any emergency contact field is filled, all are required
    const hasAnyEmergencyInfo = formData.emergency_contact_name || 
                               formData.emergency_contact_phone || 
                               formData.emergency_contact_relation;
    
    if (hasAnyEmergencyInfo) {
      if (!formData.emergency_contact_name.trim()) {
        newErrors.emergency_contact_name = t('client.profile.errors.emergencyNameRequired');
      }
      
      if (!formData.emergency_contact_phone) {
        newErrors.emergency_contact_phone = t('client.profile.errors.emergencyPhoneRequired');
      } else if (formData.emergency_contact_phone.replace(/\D/g, '').length !== 10) {
        newErrors.emergency_contact_phone = t('client.profile.errors.invalidPhone');
      }
      
      if (!formData.emergency_contact_relation) {
        newErrors.emergency_contact_relation = t('client.profile.errors.emergencyRelationRequired');
      }
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      // If all fields are empty, send null values
      const dataToSend = (!formData.emergency_contact_name && 
                         !formData.emergency_contact_phone && 
                         !formData.emergency_contact_relation)
        ? {
            emergency_contact_name: null,
            emergency_contact_phone: null,
            emergency_contact_relation: null
          }
        : formData;
        
      onUpdate(dataToSend);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-6">
        <div className="flex items-start gap-3">
          <Shield className="w-5 h-5 text-amber-600 flex-shrink-0 mt-0.5" />
          <div>
            <p className="text-sm text-amber-800 font-medium">
              {t('client.profile.emergencyContactInfo')}
            </p>
            <p className="text-sm text-amber-700 mt-1">
              {t('client.profile.emergencyContactDescription')}
            </p>
          </div>
        </div>
      </div>

      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-text-primary mb-2">
            {t('client.profile.fields.emergencyContactName')}
          </label>
          <Input
            leftIcon={<User className="w-4 h-4" />}
            value={formData.emergency_contact_name}
            onChange={(e) => setFormData({ ...formData, emergency_contact_name: e.target.value })}
            placeholder={t('client.profile.placeholders.emergencyContactName')}
            error={errors.emergency_contact_name}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              {t('client.profile.fields.emergencyContactPhone')}
            </label>
            <Input
              type="tel"
              leftIcon={<Phone className="w-4 h-4" />}
              value={formData.emergency_contact_phone}
              onChange={handlePhoneChange}
              placeholder="(*************"
              error={errors.emergency_contact_phone}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              {t('client.profile.fields.emergencyContactRelation')}
            </label>
            <Select
              value={formData.emergency_contact_relation}
              onChange={(value) => setFormData({ ...formData, emergency_contact_relation: value })}
              options={relationshipOptions}
              placeholder={t('client.profile.placeholders.selectRelationship')}
              error={errors.emergency_contact_relation}
              leftIcon={<Users className="w-4 h-4" />}
            />
          </div>
        </div>
      </div>

      <div className="flex justify-end gap-3 pt-4">
        <Button
          type="button"
          variant="ghost"
          onClick={() => setFormData({
            emergency_contact_name: profile.emergency_contact_name || '',
            emergency_contact_phone: profile.emergency_contact_phone || '',
            emergency_contact_relation: profile.emergency_contact_relation || ''
          })}
          disabled={saving}
        >
          {t('common.cancel')}
        </Button>
        <Button
          type="submit"
          loading={saving}
          disabled={saving}
        >
          {t('common.save')}
        </Button>
      </div>
    </form>
  );
};