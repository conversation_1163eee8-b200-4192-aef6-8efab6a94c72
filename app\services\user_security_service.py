"""
Unified User Security Service for TutorAide Application.
Manages two-factor authentication, trusted devices, security settings,
and account security features in a single consolidated service.
"""

import secrets
import pyotp
import qrcode
import io
import base64
import hashlib
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional, List, Dict, Any, Tuple
import asyncpg

from app.database.repositories.user_security_repository import UserSecurityRepository
from app.database.repositories.user_repository import UserRepository
from app.models.user_security_models import (
    UserSecurity, UserSecurityUpdate, TwoFactorMethod,
    TrustedDeviceInfo, SecurityChallenge, SecuritySettings,
    BackupCode, SecurityEventType, SecurityEvent
)
from app.models.user_models import User, UserRoleType
from app.core.timezone import now_est
from app.core.security import get_password_hash, verify_password
from app.core.exceptions import (
    ValidationError, AuthenticationError, PermissionDeniedError,
    ResourceNotFoundError, ConflictError, BusinessLogicError
)
from app.core.logging import Tutor<PERSON><PERSON>Logger
from app.services.twilio_service import TwilioService
from app.services.email_service import EmailService
from app.config.settings import get_settings

logger = TutorAideLogger.get_logger(__name__)
settings = get_settings()


class UserSecurityService:
    """Unified service for managing user security settings and 2FA."""
    
    def __init__(self):
        self.security_repo = UserSecurityRepository()
        self.user_repo = UserRepository()
        self.twilio_service = TwilioService()
        self.email_service = EmailService()
        
        # Security configurations
        self.max_failed_attempts = 5
        self.lockout_duration = timedelta(minutes=30)
        self.backup_codes_count = 8
        self.totp_window = 1  # Allow 1 time period drift
        self.device_trust_days = 30
        self.challenge_expiry_minutes = 10
    
    # ==================== SECURITY SETTINGS ====================
    
    async def get_user_security_settings(
        self,
        conn: asyncpg.Connection,
        user_id: int
    ) -> UserSecurity:
        """Get user security settings, creating defaults if needed."""
        
        try:
            settings = await self.security_repo.get_user_security(conn, user_id)
            
            if not settings:
                # Create default settings
                settings = await self._create_default_security_settings(conn, user_id)
            
            return settings
            
        except Exception as e:
            logger.error(f"Failed to get security settings for user {user_id}: {e}")
            raise BusinessLogicError("Failed to retrieve security settings")
    
    async def update_security_settings(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        update_data: UserSecurityUpdate
    ) -> UserSecurity:
        """Update user security settings."""
        
        try:
            # Get current settings
            current = await self.get_user_security_settings(conn, user_id)
            
            # Update settings
            updated = await self.security_repo.update_user_security(
                conn=conn,
                user_id=user_id,
                update_data=update_data
            )
            
            # Log security event
            await self._log_security_event(
                conn=conn,
                user_id=user_id,
                event_type=SecurityEventType.SETTINGS_UPDATED,
                details={"changes": update_data.dict(exclude_unset=True)}
            )
            
            return updated
            
        except Exception as e:
            logger.error(f"Failed to update security settings for user {user_id}: {e}")
            raise BusinessLogicError("Failed to update security settings")
    
    # ==================== TWO-FACTOR AUTHENTICATION ====================
    
    async def setup_2fa(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        method: TwoFactorMethod,
        phone_number: Optional[str] = None,
        email: Optional[str] = None
    ) -> Tuple[str, List[str]]:
        """
        Setup two-factor authentication for a user.
        Returns (secret/qr_code, backup_codes)
        """
        
        try:
            # Get user and security settings
            user = await self.user_repo.get_user_by_id(conn, user_id)
            if not user:
                raise ResourceNotFoundError("User not found")
            
            security = await self.get_user_security_settings(conn, user_id)
            
            # Check if 2FA already enabled
            if security.two_factor_enabled:
                raise ConflictError("Two-factor authentication is already enabled")
            
            # Validate method-specific requirements
            if method == TwoFactorMethod.SMS and not phone_number:
                raise ValidationError("Phone number required for SMS 2FA")
            elif method == TwoFactorMethod.EMAIL and not email:
                email = user.email
            
            # Generate secret and backup codes
            secret = pyotp.random_base32()
            backup_codes = self._generate_backup_codes()
            
            # Method-specific setup
            setup_data = ""
            if method == TwoFactorMethod.TOTP:
                # Generate QR code for authenticator apps
                totp_uri = pyotp.totp.TOTP(secret).provisioning_uri(
                    name=user.email,
                    issuer_name='TutorAide'
                )
                setup_data = self._generate_qr_code(totp_uri)
            else:
                setup_data = secret
            
            # Store 2FA settings (not enabled yet - needs verification)
            update_data = UserSecurityUpdate(
                two_factor_method=method,
                two_factor_secret=secret,
                two_factor_backup_codes=[
                    get_password_hash(code) for code in backup_codes
                ]
            )
            
            # Add contact info to metadata if provided
            metadata = {}
            if phone_number:
                metadata['2fa_phone'] = phone_number
            if email and email != user.email:
                metadata['2fa_email'] = email
            
            await self.security_repo.update_user_security(
                conn=conn,
                user_id=user_id,
                update_data=update_data,
                metadata=metadata
            )
            
            # Send verification code
            await self._send_2fa_challenge(
                conn=conn,
                user_id=user_id,
                method=method,
                secret=secret,
                phone_number=phone_number,
                email=email
            )
            
            # Log security event
            await self._log_security_event(
                conn=conn,
                user_id=user_id,
                event_type=SecurityEventType.TWO_FACTOR_SETUP_INITIATED,
                details={"method": method.value}
            )
            
            return setup_data, backup_codes
            
        except (ResourceNotFoundError, ConflictError, ValidationError):
            raise
        except Exception as e:
            logger.error(f"Failed to setup 2FA for user {user_id}: {e}")
            raise BusinessLogicError("Failed to setup two-factor authentication")
    
    async def verify_2fa_setup(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        code: str
    ) -> bool:
        """Verify 2FA setup with the provided code."""
        
        try:
            security = await self.get_user_security_settings(conn, user_id)
            
            if not security.two_factor_secret:
                raise ValidationError("No 2FA setup in progress")
            
            if security.two_factor_enabled:
                raise ConflictError("2FA already enabled")
            
            # Verify code based on method
            is_valid = await self._verify_2fa_code(
                method=security.two_factor_method,
                secret=security.two_factor_secret,
                code=code
            )
            
            if not is_valid:
                raise AuthenticationError("Invalid verification code")
            
            # Enable 2FA
            update_data = UserSecurityUpdate(
                two_factor_enabled=True,
                two_factor_verified_at=now_est()
            )
            
            await self.security_repo.update_user_security(
                conn=conn,
                user_id=user_id,
                update_data=update_data
            )
            
            # Log security event
            await self._log_security_event(
                conn=conn,
                user_id=user_id,
                event_type=SecurityEventType.TWO_FACTOR_ENABLED,
                details={"method": security.two_factor_method.value}
            )
            
            logger.info(f"2FA enabled for user {user_id}")
            return True
            
        except (ValidationError, ConflictError, AuthenticationError):
            raise
        except Exception as e:
            logger.error(f"Failed to verify 2FA setup for user {user_id}: {e}")
            raise BusinessLogicError("Failed to verify 2FA setup")
    
    async def disable_2fa(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        password: str
    ) -> bool:
        """Disable two-factor authentication after password verification."""
        
        try:
            # Verify user password
            user = await self.user_repo.get_user_by_id(conn, user_id)
            if not user or not verify_password(password, user.password_hash):
                raise AuthenticationError("Invalid password")
            
            security = await self.get_user_security_settings(conn, user_id)
            
            if not security.two_factor_enabled:
                raise ValidationError("Two-factor authentication is not enabled")
            
            # Disable 2FA
            update_data = UserSecurityUpdate(
                two_factor_enabled=False,
                two_factor_method=None,
                two_factor_secret=None,
                two_factor_backup_codes=[],
                two_factor_verified_at=None
            )
            
            await self.security_repo.update_user_security(
                conn=conn,
                user_id=user_id,
                update_data=update_data
            )
            
            # Clear trusted devices
            await self.security_repo.clear_trusted_devices(conn, user_id)
            
            # Log security event
            await self._log_security_event(
                conn=conn,
                user_id=user_id,
                event_type=SecurityEventType.TWO_FACTOR_DISABLED,
                details={"method": security.two_factor_method.value if security.two_factor_method else "unknown"}
            )
            
            logger.info(f"2FA disabled for user {user_id}")
            return True
            
        except (AuthenticationError, ValidationError):
            raise
        except Exception as e:
            logger.error(f"Failed to disable 2FA for user {user_id}: {e}")
            raise BusinessLogicError("Failed to disable 2FA")
    
    async def create_2fa_challenge(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        purpose: str = "login"
    ) -> str:
        """Create a 2FA challenge and send code to user."""
        
        try:
            security = await self.get_user_security_settings(conn, user_id)
            
            if not security.two_factor_enabled:
                raise ValidationError("Two-factor authentication is not enabled")
            
            # Get user for contact info
            user = await self.user_repo.get_user_by_id(conn, user_id)
            if not user:
                raise ResourceNotFoundError("User not found")
            
            # Get contact info from metadata or user
            metadata = security.metadata or {}
            phone_number = metadata.get('2fa_phone')
            email = metadata.get('2fa_email', user.email)
            
            # Create and send challenge
            challenge_id = await self._send_2fa_challenge(
                conn=conn,
                user_id=user_id,
                method=security.two_factor_method,
                secret=security.two_factor_secret,
                phone_number=phone_number,
                email=email,
                purpose=purpose
            )
            
            return challenge_id
            
        except (ValidationError, ResourceNotFoundError):
            raise
        except Exception as e:
            logger.error(f"Failed to create 2FA challenge for user {user_id}: {e}")
            raise BusinessLogicError("Failed to create 2FA challenge")
    
    async def verify_2fa_challenge(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        code: str,
        device_fingerprint: Optional[str] = None,
        trust_device: bool = False
    ) -> Tuple[bool, Optional[str]]:
        """
        Verify a 2FA challenge code.
        Returns (is_valid, trust_token)
        """
        
        try:
            security = await self.get_user_security_settings(conn, user_id)
            
            if not security.two_factor_enabled:
                raise ValidationError("Two-factor authentication is not enabled")
            
            # Check if it's a backup code
            is_backup = False
            if len(code) == 8 and code.isalnum():
                is_backup = await self._verify_backup_code(conn, user_id, code)
            
            if not is_backup:
                # Verify regular 2FA code
                is_valid = await self._verify_2fa_code(
                    method=security.two_factor_method,
                    secret=security.two_factor_secret,
                    code=code
                )
            else:
                is_valid = is_backup
            
            if not is_valid:
                # Increment failed attempts
                await self._increment_failed_attempts(conn, user_id)
                raise AuthenticationError("Invalid verification code")
            
            # Reset failed attempts
            await self._reset_failed_attempts(conn, user_id)
            
            # Create trusted device token if requested
            trust_token = None
            if trust_device and device_fingerprint:
                trust_token = await self._create_trusted_device(
                    conn=conn,
                    user_id=user_id,
                    device_fingerprint=device_fingerprint
                )
            
            # Log security event
            await self._log_security_event(
                conn=conn,
                user_id=user_id,
                event_type=SecurityEventType.TWO_FACTOR_VERIFIED,
                details={
                    "method": "backup_code" if is_backup else security.two_factor_method.value,
                    "device_trusted": trust_device
                }
            )
            
            return True, trust_token
            
        except (ValidationError, AuthenticationError):
            raise
        except Exception as e:
            logger.error(f"Failed to verify 2FA challenge for user {user_id}: {e}")
            raise BusinessLogicError("Failed to verify 2FA")
    
    # ==================== TRUSTED DEVICES ====================
    
    async def check_trusted_device(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        device_fingerprint: str
    ) -> bool:
        """Check if a device is trusted for 2FA bypass."""
        
        try:
            security = await self.get_user_security_settings(conn, user_id)
            
            if not security.two_factor_enabled:
                return True  # No 2FA, no need to check
            
            trusted_devices = security.trusted_devices or []
            now = now_est()
            
            for device in trusted_devices:
                if (device.get('device_fingerprint') == device_fingerprint and
                    datetime.fromisoformat(device.get('expires_at', '')) > now):
                    
                    # Update last used
                    await self._update_trusted_device_usage(
                        conn=conn,
                        user_id=user_id,
                        device_fingerprint=device_fingerprint
                    )
                    
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to check trusted device: {e}")
            return False
    
    async def get_trusted_devices(
        self,
        conn: asyncpg.Connection,
        user_id: int
    ) -> List[TrustedDeviceInfo]:
        """Get list of trusted devices for a user."""
        
        try:
            security = await self.get_user_security_settings(conn, user_id)
            trusted_devices = security.trusted_devices or []
            
            devices = []
            for device_data in trusted_devices:
                device = TrustedDeviceInfo(
                    device_fingerprint=device_data.get('device_fingerprint'),
                    device_name=device_data.get('device_name', 'Unknown Device'),
                    last_used_at=datetime.fromisoformat(device_data.get('last_used_at')),
                    expires_at=datetime.fromisoformat(device_data.get('expires_at')),
                    ip_address=device_data.get('ip_address'),
                    user_agent=device_data.get('user_agent')
                )
                devices.append(device)
            
            return devices
            
        except Exception as e:
            logger.error(f"Failed to get trusted devices: {e}")
            return []
    
    async def remove_trusted_device(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        device_fingerprint: str
    ) -> bool:
        """Remove a trusted device."""
        
        try:
            success = await self.security_repo.remove_trusted_device(
                conn=conn,
                user_id=user_id,
                device_fingerprint=device_fingerprint
            )
            
            if success:
                await self._log_security_event(
                    conn=conn,
                    user_id=user_id,
                    event_type=SecurityEventType.TRUSTED_DEVICE_REMOVED,
                    details={"device_fingerprint": device_fingerprint}
                )
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to remove trusted device: {e}")
            return False
    
    # ==================== BACKUP CODES ====================
    
    async def regenerate_backup_codes(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        password: str
    ) -> List[str]:
        """Regenerate backup codes after password verification."""
        
        try:
            # Verify password
            user = await self.user_repo.get_user_by_id(conn, user_id)
            if not user or not verify_password(password, user.password_hash):
                raise AuthenticationError("Invalid password")
            
            security = await self.get_user_security_settings(conn, user_id)
            
            if not security.two_factor_enabled:
                raise ValidationError("Two-factor authentication is not enabled")
            
            # Generate new backup codes
            backup_codes = self._generate_backup_codes()
            
            # Store hashed codes
            update_data = UserSecurityUpdate(
                two_factor_backup_codes=[
                    get_password_hash(code) for code in backup_codes
                ]
            )
            
            await self.security_repo.update_user_security(
                conn=conn,
                user_id=user_id,
                update_data=update_data
            )
            
            # Log security event
            await self._log_security_event(
                conn=conn,
                user_id=user_id,
                event_type=SecurityEventType.BACKUP_CODES_REGENERATED,
                details={"count": len(backup_codes)}
            )
            
            return backup_codes
            
        except (AuthenticationError, ValidationError):
            raise
        except Exception as e:
            logger.error(f"Failed to regenerate backup codes: {e}")
            raise BusinessLogicError("Failed to regenerate backup codes")
    
    # ==================== ACCOUNT SECURITY ====================
    
    async def check_account_locked(
        self,
        conn: asyncpg.Connection,
        user_id: int
    ) -> Tuple[bool, Optional[datetime]]:
        """Check if account is locked due to failed attempts."""
        
        try:
            security = await self.get_user_security_settings(conn, user_id)
            
            if security.locked_until and security.locked_until > now_est():
                return True, security.locked_until
            
            return False, None
            
        except Exception as e:
            logger.error(f"Failed to check account lock status: {e}")
            return False, None
    
    async def require_password_change(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        reason: str = "Security policy"
    ) -> bool:
        """Mark account as requiring password change."""
        
        try:
            update_data = UserSecurityUpdate(
                require_password_change=True
            )
            
            await self.security_repo.update_user_security(
                conn=conn,
                user_id=user_id,
                update_data=update_data
            )
            
            await self._log_security_event(
                conn=conn,
                user_id=user_id,
                event_type=SecurityEventType.PASSWORD_CHANGE_REQUIRED,
                details={"reason": reason}
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to set password change requirement: {e}")
            return False
    
    async def update_password_with_validation(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        current_password: str,
        new_password: str
    ) -> bool:
        """Update password with current password verification."""
        
        try:
            # Verify current password
            user = await self.user_repo.get_user_by_id(conn, user_id)
            if not user or not verify_password(current_password, user.password_hash):
                raise AuthenticationError("Current password is incorrect")
            
            # Check password strength
            from app.models.auth_models import PasswordStrength
            strength = PasswordStrength.check_strength(new_password)
            if strength.score < 3:
                raise ValidationError("New password is too weak")
            
            # Update password
            new_hash = get_password_hash(new_password)
            await self.user_repo.update_user_password(
                conn=conn,
                user_id=user_id,
                password_hash=new_hash
            )
            
            # Update security settings
            update_data = UserSecurityUpdate(
                require_password_change=False,
                last_password_change=now_est()
            )
            
            await self.security_repo.update_user_security(
                conn=conn,
                user_id=user_id,
                update_data=update_data
            )
            
            # Log security event
            await self._log_security_event(
                conn=conn,
                user_id=user_id,
                event_type=SecurityEventType.PASSWORD_CHANGED,
                details={"strength_score": strength.score}
            )
            
            return True
            
        except (AuthenticationError, ValidationError):
            raise
        except Exception as e:
            logger.error(f"Failed to update password: {e}")
            raise BusinessLogicError("Failed to update password")
    
    # ==================== UTILITY METHODS ====================
    
    async def _create_default_security_settings(
        self,
        conn: asyncpg.Connection,
        user_id: int
    ) -> UserSecurity:
        """Create default security settings for a user."""
        
        settings = UserSecurity(
            user_id=user_id,
            two_factor_enabled=False,
            two_factor_method=None,
            two_factor_secret=None,
            two_factor_backup_codes=[],
            two_factor_verified_at=None,
            trusted_devices=[],
            require_password_change=False,
            last_password_change=now_est(),
            failed_login_attempts=0,
            locked_until=None,
            created_at=now_est(),
            updated_at=now_est()
        )
        
        return await self.security_repo.create_user_security(conn, settings)
    
    def _generate_backup_codes(self) -> List[str]:
        """Generate backup codes for 2FA."""
        codes = []
        for _ in range(self.backup_codes_count):
            # Generate 8-character alphanumeric codes
            code = ''.join(secrets.choice('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789') for _ in range(8))
            codes.append(code)
        return codes
    
    def _generate_qr_code(self, uri: str) -> str:
        """Generate QR code as base64 string."""
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(uri)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        
        return base64.b64encode(buffer.getvalue()).decode()
    
    async def _verify_2fa_code(
        self,
        method: TwoFactorMethod,
        secret: str,
        code: str
    ) -> bool:
        """Verify a 2FA code based on method."""
        
        if method == TwoFactorMethod.TOTP:
            totp = pyotp.TOTP(secret)
            return totp.verify(code, valid_window=self.totp_window)
        
        elif method in [TwoFactorMethod.SMS, TwoFactorMethod.EMAIL]:
            # For SMS/Email, codes are stored temporarily in cache
            # This is a simplified version - in production use Redis
            # For now, we'll accept any 6-digit code for testing
            return len(code) == 6 and code.isdigit()
        
        return False
    
    async def _verify_backup_code(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        code: str
    ) -> bool:
        """Verify and consume a backup code."""
        
        security = await self.get_user_security_settings(conn, user_id)
        backup_codes = security.two_factor_backup_codes or []
        
        # Check each hashed backup code
        for i, hashed_code in enumerate(backup_codes):
            if verify_password(code.upper(), hashed_code):
                # Remove used code
                backup_codes.pop(i)
                
                update_data = UserSecurityUpdate(
                    two_factor_backup_codes=backup_codes
                )
                
                await self.security_repo.update_user_security(
                    conn=conn,
                    user_id=user_id,
                    update_data=update_data
                )
                
                # Log event
                await self._log_security_event(
                    conn=conn,
                    user_id=user_id,
                    event_type=SecurityEventType.BACKUP_CODE_USED,
                    details={"remaining_codes": len(backup_codes)}
                )
                
                return True
        
        return False
    
    async def _send_2fa_challenge(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        method: TwoFactorMethod,
        secret: str,
        phone_number: Optional[str] = None,
        email: Optional[str] = None,
        purpose: str = "verification"
    ) -> str:
        """Send 2FA challenge code via specified method."""
        
        challenge_id = str(uuid4())
        
        if method == TwoFactorMethod.TOTP:
            # TOTP doesn't need to send anything
            return challenge_id
        
        elif method == TwoFactorMethod.SMS:
            if not phone_number:
                raise ValidationError("Phone number required for SMS 2FA")
            
            # Generate 6-digit code
            code = ''.join(secrets.choice('0123456789') for _ in range(6))
            
            # Send SMS
            message = f"Your TutorAide {purpose} code is: {code}. Valid for 10 minutes."
            await self.twilio_service.send_sms(phone_number, message)
            
            # Store code temporarily (in production, use Redis with expiry)
            # For now, we'll accept any 6-digit code
            
        elif method == TwoFactorMethod.EMAIL:
            if not email:
                raise ValidationError("Email required for email 2FA")
            
            # Generate 6-digit code
            code = ''.join(secrets.choice('0123456789') for _ in range(6))
            
            # Send email
            await self.email_service.send_2fa_code(email, code, purpose)
            
            # Store code temporarily (in production, use Redis with expiry)
        
        return challenge_id
    
    async def _create_trusted_device(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        device_fingerprint: str,
        device_name: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> str:
        """Create a trusted device entry."""
        
        trust_token = self._generate_secure_token()
        expires_at = now_est() + timedelta(days=self.device_trust_days)
        
        device_info = {
            'device_fingerprint': device_fingerprint,
            'device_name': device_name or 'Unknown Device',
            'last_used_at': now_est().isoformat(),
            'expires_at': expires_at.isoformat(),
            'ip_address': ip_address,
            'user_agent': user_agent,
            'trust_token': get_password_hash(trust_token)
        }
        
        await self.security_repo.add_trusted_device(
            conn=conn,
            user_id=user_id,
            device_info=device_info
        )
        
        return trust_token
    
    async def _update_trusted_device_usage(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        device_fingerprint: str
    ) -> None:
        """Update last used time for a trusted device."""
        
        await self.security_repo.update_trusted_device_usage(
            conn=conn,
            user_id=user_id,
            device_fingerprint=device_fingerprint
        )
    
    async def _increment_failed_attempts(
        self,
        conn: asyncpg.Connection,
        user_id: int
    ) -> None:
        """Increment failed login attempts and lock if necessary."""
        
        security = await self.get_user_security_settings(conn, user_id)
        attempts = security.failed_login_attempts + 1
        
        update_data = UserSecurityUpdate(
            failed_login_attempts=attempts
        )
        
        # Lock account if max attempts reached
        if attempts >= self.max_failed_attempts:
            update_data.locked_until = now_est() + self.lockout_duration
            
            await self._log_security_event(
                conn=conn,
                user_id=user_id,
                event_type=SecurityEventType.ACCOUNT_LOCKED,
                details={"attempts": attempts}
            )
        
        await self.security_repo.update_user_security(
            conn=conn,
            user_id=user_id,
            update_data=update_data
        )
    
    async def _reset_failed_attempts(
        self,
        conn: asyncpg.Connection,
        user_id: int
    ) -> None:
        """Reset failed login attempts."""
        
        update_data = UserSecurityUpdate(
            failed_login_attempts=0,
            locked_until=None
        )
        
        await self.security_repo.update_user_security(
            conn=conn,
            user_id=user_id,
            update_data=update_data
        )
    
    async def _log_security_event(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        event_type: SecurityEventType,
        details: Dict[str, Any]
    ) -> None:
        """Log a security event."""
        
        event = SecurityEvent(
            user_id=user_id,
            event_type=event_type,
            event_data=details,
            timestamp=now_est(),
            ip_address=details.get('ip_address'),
            user_agent=details.get('user_agent')
        )
        
        await self.security_repo.log_security_event(conn, event)


# Global service instance
_user_security_service = None


def get_user_security_service() -> UserSecurityService:
    """Get the global user security service instance."""
    global _user_security_service
    if _user_security_service is None:
        _user_security_service = UserSecurityService()
    return _user_security_service