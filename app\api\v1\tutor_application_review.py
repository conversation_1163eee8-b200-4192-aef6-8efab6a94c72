"""
Tutor application review endpoints for managers.
"""

from typing import List, Optional
from datetime import datetime, timedelta

from fastapi import APIRouter, Depends, HTTPException, Query, status
from pydantic import BaseModel, Field

from app.models.auth_models import TokenData, UserRole
from app.models.tutor_invitation_models import TutorInvitation
from app.config.database import DatabaseManager
from app.core.dependencies import get_db_manager, get_current_user
from app.core.exceptions import ResourceNotFoundError, ForbiddenError, BusinessLogicError
from app.services.tutor_invitation_service import TutorInvitationService
from app.services.auth_service import AuthService
from app.services.email_service import EmailService
from app.core.logging import TutorAideLogger

logger = TutorAideLogger(__name__)

router = APIRouter(prefix="/tutor-applications", tags=["tutor-application-review"])


class ApplicationReviewRequest(BaseModel):
    """Request to review a tutor application."""
    action: str = Field(..., description="approve, reject, or waitlist")
    notes: Optional[str] = Field(None, description="Review notes")
    rejection_reason: Optional[str] = Field(None, description="Reason if rejected")
    can_reapply_after: Optional[str] = Field(None, description="Date when can reapply")


class TutorApplicationDetail(TutorInvitation):
    """Detailed tutor application with parsed data."""
    parsed_education: Optional[dict] = None
    parsed_experience: Optional[dict] = None
    parsed_preferences: Optional[dict] = None
    parsed_references: Optional[dict] = None
    review_status: Optional[str] = None


@router.get("/pending", response_model=List[TutorApplicationDetail])
async def get_pending_applications(
    status: Optional[str] = Query("under_review", description="Filter by status"),
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
    current_user: TokenData = Depends(get_current_user),
    db_manager: DatabaseManager = Depends(get_db_manager)
):
    """Get list of pending tutor applications (managers only)."""
    if current_user.role != UserRole.MANAGER:
        raise ForbiddenError("Only managers can review applications")
    
    async with db_manager.acquire() as conn:
        applications = await conn.fetch(
            """
            SELECT 
                ti.*,
                ti.application_data->'education' as parsed_education,
                ti.application_data->'experience' as parsed_experience,
                ti.application_data->'preferences' as parsed_preferences,
                ti.application_data->'references' as parsed_references,
                CASE 
                    WHEN ti.application_status = 'under_review' THEN 'Pending Review'
                    WHEN ti.application_status = 'approved' THEN 'Approved'
                    WHEN ti.application_status = 'rejected' THEN 'Rejected'
                    WHEN ti.application_status = 'waitlisted' THEN 'Waitlisted'
                    ELSE ti.application_status
                END as review_status
            FROM tutor_invitations ti
            WHERE ti.status = 'applied'
            AND ($1::text IS NULL OR ti.application_status = $1)
            ORDER BY ti.application_submitted_at DESC
            LIMIT $2 OFFSET $3
            """,
            status,
            limit,
            offset
        )
        
        return [TutorApplicationDetail(**app) for app in applications]


@router.get("/{application_id}", response_model=TutorApplicationDetail)
async def get_application_detail(
    application_id: int,
    current_user: TokenData = Depends(get_current_user),
    db_manager: DatabaseManager = Depends(get_db_manager)
):
    """Get detailed view of a tutor application (managers only)."""
    if current_user.role != UserRole.MANAGER:
        raise ForbiddenError("Only managers can view applications")
    
    async with db_manager.acquire() as conn:
        application = await conn.fetchrow(
            """
            SELECT 
                ti.*,
                ti.application_data->'education' as parsed_education,
                ti.application_data->'experience' as parsed_experience,
                ti.application_data->'preferences' as parsed_preferences,
                ti.application_data->'references' as parsed_references,
                ti.application_status as review_status,
                u.email as reviewer_email,
                u.first_name as reviewer_first_name,
                u.last_name as reviewer_last_name
            FROM tutor_invitations ti
            LEFT JOIN users u ON ti.reviewed_by = u.user_id
            WHERE ti.invitation_id = $1 AND ti.status = 'applied'
            """,
            application_id
        )
        
        if not application:
            raise ResourceNotFoundError("Application not found")
        
        return TutorApplicationDetail(**application)


@router.post("/{application_id}/review")
async def review_application(
    application_id: int,
    review: ApplicationReviewRequest,
    current_user: TokenData = Depends(get_current_user),
    db_manager: DatabaseManager = Depends(get_db_manager)
):
    """Review a tutor application - approve, reject, or waitlist."""
    if current_user.role != UserRole.MANAGER:
        raise ForbiddenError("Only managers can review applications")
    
    if review.action not in ["approve", "reject", "waitlist"]:
        raise ValidationError("Invalid action. Must be approve, reject, or waitlist")
    
    async with db_manager.acquire() as conn:
        # Get application
        application = await conn.fetchrow(
            """
            SELECT * FROM tutor_invitations 
            WHERE invitation_id = $1 AND status = 'applied'
            """,
            application_id
        )
        
        if not application:
            raise ResourceNotFoundError("Application not found")
        
        if application['application_status'] not in ['under_review', 'waitlisted']:
            raise BusinessLogicError(f"Application already {application['application_status']}")
        
        email_service = EmailService()
        
        if review.action == "approve":
            # Update application to approved
            await conn.execute(
                """
                UPDATE tutor_invitations 
                SET 
                    application_status = 'approved',
                    status = 'pending',
                    reviewed_by = $2,
                    reviewed_at = NOW(),
                    review_notes = $3,
                    updated_at = NOW()
                WHERE invitation_id = $1
                """,
                application_id,
                int(current_user.user_id),
                review.notes
            )
            
            # Get frontend URL for invitation
            from app.config.settings import get_settings
            settings = get_settings()
            invitation_link = f"{settings.frontend_url}/accept-invitation?token={application['token']}"
            
            # Send approval email with account creation link
            await email_service.send_email(
                to_email=application['email'],
                subject="Congratulations! Your TutorAide Application is Approved",
                template="tutor_application_approved",
                context={
                    "first_name": application['first_name'],
                    "invitation_link": invitation_link,
                    "expires_in_days": 7
                }
            )
            
            logger.info(f"Application {application_id} approved by manager {current_user.user_id}")
            
        elif review.action == "reject":
            if not review.rejection_reason:
                raise ValidationError("Rejection reason is required")
            
            # Calculate reapply date
            can_reapply_date = None
            if review.can_reapply_after:
                can_reapply_date = datetime.strptime(review.can_reapply_after, "%Y-%m-%d")
            else:
                # Default 6 months
                can_reapply_date = datetime.utcnow() + timedelta(days=180)
            
            # Update application to rejected
            await conn.execute(
                """
                UPDATE tutor_invitations 
                SET 
                    application_status = 'rejected',
                    status = 'expired',
                    reviewed_by = $2,
                    reviewed_at = NOW(),
                    review_notes = $3,
                    rejection_reason = $4,
                    can_reapply_after = $5,
                    updated_at = NOW()
                WHERE invitation_id = $1
                """,
                application_id,
                int(current_user.user_id),
                review.notes,
                review.rejection_reason,
                can_reapply_date
            )
            
            # Store in rejected applications for history
            await conn.execute(
                """
                INSERT INTO rejected_applications (
                    email, first_name, last_name,
                    application_data, rejection_reason,
                    rejection_notes, rejected_by,
                    can_reapply_after, original_invitation_id
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                """,
                application['email'],
                application['first_name'],
                application['last_name'],
                application['application_data'],
                review.rejection_reason,
                review.notes,
                int(current_user.user_id),
                can_reapply_date,
                application_id
            )
            
            # Send rejection email
            rejection_messages = {
                "insufficient_education": "education requirements",
                "insufficient_experience": "teaching experience requirements",
                "no_relevant_subjects": "subject expertise requirements",
                "failed_reference_check": "reference verification",
                "geographic_limitation": "service area limitations",
                "other": review.notes or "specific requirements"
            }
            
            await email_service.send_email(
                to_email=application['email'],
                subject="TutorAide Application Update",
                template="tutor_application_rejected",
                context={
                    "first_name": application['first_name'],
                    "rejection_reason": rejection_messages.get(review.rejection_reason, "requirements"),
                    "can_reapply_date": can_reapply_date.strftime("%B %d, %Y"),
                    "feedback": review.notes
                }
            )
            
            logger.info(f"Application {application_id} rejected by manager {current_user.user_id}")
            
        elif review.action == "waitlist":
            # Update application to waitlisted
            await conn.execute(
                """
                UPDATE tutor_invitations 
                SET 
                    application_status = 'waitlisted',
                    reviewed_by = $2,
                    reviewed_at = NOW(),
                    review_notes = $3,
                    updated_at = NOW()
                WHERE invitation_id = $1
                """,
                application_id,
                int(current_user.user_id),
                review.notes
            )
            
            # Send waitlist email
            await email_service.send_email(
                to_email=application['email'],
                subject="TutorAide Application - Waitlist Status",
                template="tutor_application_waitlisted",
                context={
                    "first_name": application['first_name'],
                    "reason": review.notes or "We have received many qualified applications and are reviewing them based on current needs."
                }
            )
            
            logger.info(f"Application {application_id} waitlisted by manager {current_user.user_id}")
        
        return {
            "success": True,
            "action": review.action,
            "message": f"Application successfully {review.action}d"
        }


@router.get("/stats/summary")
async def get_application_stats(
    current_user: TokenData = Depends(get_current_user),
    db_manager: DatabaseManager = Depends(get_db_manager)
):
    """Get application statistics for dashboard (managers only)."""
    if current_user.role != UserRole.MANAGER:
        raise ForbiddenError("Only managers can view statistics")
    
    async with db_manager.acquire() as conn:
        stats = await conn.fetchrow(
            """
            SELECT 
                COUNT(*) FILTER (WHERE application_status = 'under_review') as pending_count,
                COUNT(*) FILTER (WHERE application_status = 'approved' AND reviewed_at > NOW() - INTERVAL '7 days') as approved_this_week,
                COUNT(*) FILTER (WHERE application_status = 'rejected' AND reviewed_at > NOW() - INTERVAL '7 days') as rejected_this_week,
                COUNT(*) FILTER (WHERE application_status = 'waitlisted') as waitlisted_count,
                COUNT(*) as total_applications,
                AVG(EXTRACT(EPOCH FROM (reviewed_at - application_submitted_at))/3600)::numeric(10,1) as avg_review_time_hours
            FROM tutor_invitations
            WHERE status = 'applied'
            """
        )
        
        # Get top rejection reasons
        rejection_reasons = await conn.fetch(
            """
            SELECT 
                rejection_reason,
                COUNT(*) as count
            FROM tutor_invitations
            WHERE status = 'applied' AND application_status = 'rejected'
            GROUP BY rejection_reason
            ORDER BY count DESC
            LIMIT 5
            """
        )
        
        return {
            "pending_applications": stats['pending_count'],
            "approved_this_week": stats['approved_this_week'],
            "rejected_this_week": stats['rejected_this_week'],
            "waitlisted": stats['waitlisted_count'],
            "total_applications": stats['total_applications'],
            "avg_review_time_hours": float(stats['avg_review_time_hours']) if stats['avg_review_time_hours'] else 0,
            "top_rejection_reasons": [
                {"reason": r['rejection_reason'], "count": r['count']} 
                for r in rejection_reasons
            ]
        }