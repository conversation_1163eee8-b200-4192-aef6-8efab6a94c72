"""
Package management API endpoints for TECFEE and bundle packages.
"""

from typing import List, Optional, Annotated
from datetime import date
from decimal import Decimal
from fastapi import APIRouter, Depends, HTTPException, status, Query
import asyncpg

from app.core.dependencies import get_database, get_current_active_user
from app.core.exceptions import ResourceNotFoundError, ValidationError, BusinessLogicError
from app.models.user_models import User, UserRoleType
from app.models.billing_models import (
    PackageCreate, PackageUpdate, PackageResponse,
    PackagePurchaseCreate, PackagePurchaseResponse,
    PackageType, PackageStatus
)
from app.services.package_service import PackageService, get_package_service
from app.core.logging import Tutor<PERSON>ideLogger
from app.core.auth_decorators import require_roles

router = APIRouter(prefix="/packages", tags=["packages"])
logger = TutorAideLogger.get_logger(__name__)


@router.get("/", response_model=List[PackageResponse])
async def list_packages(
    db: Annotated[asyncpg.Connection, Depends(get_database)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    package_type: Optional[PackageType] = Query(None, description="Filter by package type"),
    is_active: Optional[bool] = Query(True, description="Filter by active status"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100)
):
    """
    List all available packages.
    
    - **package_type**: Filter by type (tecfee, bundle, custom)
    - **is_active**: Show only active packages
    - **skip**: Number of records to skip
    - **limit**: Maximum number of records to return
    """
    try:
        query = """
            SELECT 
                p.*,
                s.service_name,
                s.subject_area
            FROM billing_packages p
            LEFT JOIN service_catalog s ON p.service_id = s.service_id
            WHERE ($1::varchar IS NULL OR p.package_type = $1)
            AND ($2::boolean IS NULL OR p.is_active = $2)
            AND p.deleted_at IS NULL
            ORDER BY p.package_name
            OFFSET $3 LIMIT $4
        """
        
        rows = await db.fetch(
            query,
            package_type.value if package_type else None,
            is_active,
            skip,
            limit
        )
        
        packages = []
        for row in rows:
            package = PackageResponse(
                package_id=row['package_id'],
                package_name=row['package_name'],
                package_type=PackageType(row['package_type']),
                service_id=row['service_id'],
                service_name=row['service_name'],
                subject_area=row['subject_area'],
                session_count=row['session_count'],
                session_duration_minutes=row['session_duration_minutes'],
                total_price=Decimal(str(row['total_price'])),
                currency_code=row['currency_code'],
                is_group_package=row['is_group_package'],
                min_participants=row['min_participants'],
                max_participants=row['max_participants'],
                is_active=row['is_active'],
                valid_from=row['valid_from'],
                valid_until=row['valid_until'],
                description=row['description'],
                terms_and_conditions=row['terms_and_conditions'],
                created_at=row['created_at'],
                updated_at=row['updated_at']
            )
            packages.append(package)
            
        return packages
        
    except Exception as e:
        logger.error(f"Error listing packages: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve packages"
        )


@router.get("/{package_id}", response_model=PackageResponse)
async def get_package(
    package_id: int,
    db: Annotated[asyncpg.Connection, Depends(get_database)],
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    """Get details of a specific package."""
    query = """
        SELECT 
            p.*,
            s.service_name,
            s.subject_area
        FROM billing_packages p
        LEFT JOIN service_catalog s ON p.service_id = s.service_id
        WHERE p.package_id = $1 AND p.deleted_at IS NULL
    """
    
    row = await db.fetchrow(query, package_id)
    if not row:
        raise ResourceNotFoundError(f"Package {package_id} not found")
        
    return PackageResponse(
        package_id=row['package_id'],
        package_name=row['package_name'],
        package_type=PackageType(row['package_type']),
        service_id=row['service_id'],
        service_name=row['service_name'],
        subject_area=row['subject_area'],
        session_count=row['session_count'],
        session_duration_minutes=row['session_duration_minutes'],
        total_price=Decimal(str(row['total_price'])),
        currency_code=row['currency_code'],
        is_group_package=row['is_group_package'],
        min_participants=row['min_participants'],
        max_participants=row['max_participants'],
        is_active=row['is_active'],
        valid_from=row['valid_from'],
        valid_until=row['valid_until'],
        description=row['description'],
        terms_and_conditions=row['terms_and_conditions'],
        created_at=row['created_at'],
        updated_at=row['updated_at']
    )


@router.post("/", response_model=PackageResponse, status_code=status.HTTP_201_CREATED)
@require_roles([UserRoleType.MANAGER])
async def create_package(
    package_data: PackageCreate,
    db: Annotated[asyncpg.Connection, Depends(get_database)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    package_service: Annotated[PackageService, Depends(get_package_service)]
):
    """
    Create a new package (Manager only).
    
    - **package_name**: Unique name for the package
    - **package_type**: Type of package (tecfee, bundle, custom)
    - **session_count**: Number of sessions included
    - **total_price**: Package price in CAD
    - **is_group_package**: Whether this is for group sessions
    """
    try:
        package_id = await package_service.create_package(
            db,
            package_data,
            current_user.user_id
        )
        
        # Fetch and return the created package
        return await get_package(package_id, db, current_user)
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error creating package: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create package"
        )


@router.put("/{package_id}", response_model=PackageResponse)
@require_roles([UserRoleType.MANAGER])
async def update_package(
    package_id: int,
    package_data: PackageUpdate,
    db: Annotated[asyncpg.Connection, Depends(get_database)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    package_service: Annotated[PackageService, Depends(get_package_service)]
):
    """Update an existing package (Manager only)."""
    try:
        await package_service.update_package(
            db,
            package_id,
            package_data
        )
        
        return await get_package(package_id, db, current_user)
        
    except ResourceNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete("/{package_id}", status_code=status.HTTP_204_NO_CONTENT)
@require_roles([UserRoleType.MANAGER])
async def delete_package(
    package_id: int,
    db: Annotated[asyncpg.Connection, Depends(get_database)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    package_service: Annotated[PackageService, Depends(get_package_service)]
):
    """Soft delete a package (Manager only)."""
    try:
        await package_service.delete_package(db, package_id)
    except ResourceNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except BusinessLogicError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/{package_id}/purchase", response_model=PackagePurchaseResponse, status_code=status.HTTP_201_CREATED)
async def purchase_package(
    package_id: int,
    purchase_data: PackagePurchaseCreate,
    db: Annotated[asyncpg.Connection, Depends(get_database)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    package_service: Annotated[PackageService, Depends(get_package_service)]
):
    """
    Purchase a package for a client or dependant.
    
    - **client_id**: ID of the client making the purchase
    - **dependant_id**: Optional ID of the dependant the package is for
    - **payment_method**: Payment method used
    """
    try:
        # Verify the user has permission to purchase for this client
        if current_user.user_id != purchase_data.client_id:
            # Check if user is a manager
            has_manager_role = any(
                role.role == UserRoleType.MANAGER 
                for role in current_user.roles
            )
            if not has_manager_role:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="You can only purchase packages for yourself"
                )
        
        purchase_id = await package_service.purchase_package(
            db,
            package_id,
            purchase_data,
            current_user.user_id
        )
        
        # Fetch and return the purchase details
        query = """
            SELECT 
                pp.*,
                p.package_name,
                p.package_type,
                p.session_count as total_sessions,
                c.first_name || ' ' || c.last_name as client_name,
                d.first_name || ' ' || d.last_name as dependant_name
            FROM billing_package_purchases pp
            JOIN billing_packages p ON pp.package_id = p.package_id
            JOIN client_profiles c ON pp.client_id = c.client_id
            LEFT JOIN client_dependants d ON pp.dependant_id = d.dependant_id
            WHERE pp.purchase_id = $1
        """
        
        row = await db.fetchrow(query, purchase_id)
        
        return PackagePurchaseResponse(
            purchase_id=row['purchase_id'],
            package_id=row['package_id'],
            package_name=row['package_name'],
            package_type=PackageType(row['package_type']),
            client_id=row['client_id'],
            client_name=row['client_name'],
            dependant_id=row['dependant_id'],
            dependant_name=row['dependant_name'],
            purchase_date=row['purchase_date'],
            sessions_remaining=row['sessions_remaining'],
            total_sessions=row['total_sessions'],
            expire_date=row['expire_date'],
            amount_paid=Decimal(str(row['amount_paid'])),
            payment_method=row['payment_method'],
            stripe_payment_id=row['stripe_payment_id'],
            status=PackageStatus(row['status']),
            created_at=row['created_at']
        )
        
    except BusinessLogicError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error purchasing package: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to purchase package"
        )


@router.get("/purchases/my", response_model=List[PackagePurchaseResponse])
async def get_my_package_purchases(
    db: Annotated[asyncpg.Connection, Depends(get_database)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    status: Optional[PackageStatus] = Query(None, description="Filter by status"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100)
):
    """Get all package purchases for the current user."""
    # Get client_id for current user
    client_id_query = """
        SELECT client_id FROM client_profiles WHERE user_id = $1
    """
    client_id = await db.fetchval(client_id_query, current_user.user_id)
    
    if not client_id:
        return []  # User is not a client
    
    query = """
        SELECT 
            pp.*,
            p.package_name,
            p.package_type,
            p.session_count as total_sessions,
            c.first_name || ' ' || c.last_name as client_name,
            d.first_name || ' ' || d.last_name as dependant_name
        FROM billing_package_purchases pp
        JOIN billing_packages p ON pp.package_id = p.package_id
        JOIN client_profiles c ON pp.client_id = c.client_id
        LEFT JOIN client_dependants d ON pp.dependant_id = d.dependant_id
        WHERE pp.client_id = $1
        AND ($2::varchar IS NULL OR pp.status = $2)
        ORDER BY pp.purchase_date DESC
        OFFSET $3 LIMIT $4
    """
    
    rows = await db.fetch(
        query,
        client_id,
        status.value if status else None,
        skip,
        limit
    )
    
    purchases = []
    for row in rows:
        purchase = PackagePurchaseResponse(
            purchase_id=row['purchase_id'],
            package_id=row['package_id'],
            package_name=row['package_name'],
            package_type=PackageType(row['package_type']),
            client_id=row['client_id'],
            client_name=row['client_name'],
            dependant_id=row['dependant_id'],
            dependant_name=row['dependant_name'],
            purchase_date=row['purchase_date'],
            sessions_remaining=row['sessions_remaining'],
            total_sessions=row['total_sessions'],
            expire_date=row['expire_date'],
            amount_paid=Decimal(str(row['amount_paid'])),
            payment_method=row['payment_method'],
            stripe_payment_id=row['stripe_payment_id'],
            status=PackageStatus(row['status']),
            created_at=row['created_at']
        )
        purchases.append(purchase)
        
    return purchases