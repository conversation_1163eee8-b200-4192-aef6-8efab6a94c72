"""
Billing domain models for invoices, payments, subscriptions, and financial tracking.
"""

from datetime import datetime, date
from typing import List, Optional, Dict, Any
from decimal import Decimal
from enum import Enum
from pydantic import BaseModel, Field, field_validator, ConfigDict

from app.models.base import (
    BaseEntity,
    IdentifiedEntity,
    PaymentStatus,
    TutorPaymentStatus,
    Currency,
    SearchFilters
)


class Invoice(IdentifiedEntity):
    """Invoice model."""
    
    invoice_id: int = Field(..., description="Unique invoice identifier")
    client_id: int = Field(..., description="Billed client")
    invoice_number: str = Field(..., description="Unique invoice number")
    amount: Decimal = Field(..., description="Invoice amount")
    currency: str = Field(default="CAD", description="Currency")
    tax_amount: Decimal = Field(default=Decimal("0.00"), description="Tax amount")
    total_amount: Decimal = Field(..., description="Total amount including tax")
    status: PaymentStatus = Field(default=PaymentStatus.PENDING, description="Invoice status")
    issue_date: date = Field(..., description="Invoice issue date")
    due_date: date = Field(..., description="Invoice due date")
    paid_date: Optional[date] = Field(None, description="Date invoice was paid")
    payment_method: Optional[str] = Field(None, description="Payment method used")
    payment_reference: Optional[str] = Field(None, description="Payment reference/transaction ID")
    billing_address: Dict[str, Any] = Field(..., description="Billing address")
    notes: Optional[str] = Field(None, description="Invoice notes")
    paid_by_parent: Optional[str] = Field(None, description="Which parent paid (for separated families)")
    
    @field_validator('amount', 'tax_amount', 'total_amount')
    @classmethod
    def validate_amounts(cls, v: Decimal) -> Decimal:
        """Validate amounts are non-negative."""
        if v < 0:
            raise ValueError('Amount cannot be negative')
        return v
    
    @field_validator('due_date')
    @classmethod
    def validate_due_date(cls, v: date, info) -> date:
        """Validate due date is not before issue date."""
        if 'issue_date' in info.data and v < info.data['issue_date']:
            raise ValueError('Due date cannot be before issue date')
        return v
    
    @field_validator('payment_method')
    @classmethod
    def validate_payment_method(cls, v: Optional[str]) -> Optional[str]:
        """Validate payment method."""
        if v is None:
            return v
        valid_methods = {'stripe', 'bank_transfer', 'cash', 'cheque', 'subscription_deduction'}
        if v.lower() not in valid_methods:
            raise ValueError(f'Payment method must be one of: {", ".join(valid_methods)}')
        return v.lower()


class InvoiceLineItem(IdentifiedEntity):
    """Invoice line item model."""
    
    line_item_id: int = Field(..., description="Unique line item identifier")
    invoice_id: int = Field(..., description="Associated invoice")
    appointment_id: Optional[int] = Field(None, description="Associated appointment if applicable")
    description: str = Field(..., description="Line item description")
    quantity: Decimal = Field(..., description="Quantity (hours, sessions, etc.)")
    unit_price: Decimal = Field(..., description="Price per unit")
    line_total: Decimal = Field(..., description="Line total amount")
    
    @field_validator('quantity', 'unit_price', 'line_total')
    @classmethod
    def validate_amounts(cls, v: Decimal) -> Decimal:
        """Validate amounts are positive."""
        if v <= 0:
            raise ValueError('Amount must be positive')
        return v


class TutorPayment(IdentifiedEntity):
    """Tutor payment model."""
    
    payment_id: int = Field(..., description="Unique payment identifier")
    tutor_id: int = Field(..., description="Tutor being paid")
    payment_period_start: date = Field(..., description="Payment period start (Thursday)")
    payment_period_end: date = Field(..., description="Payment period end (Wednesday)")
    total_hours: Decimal = Field(..., description="Total hours worked")
    base_amount: Decimal = Field(..., description="Base payment amount")
    bonus_amount: Decimal = Field(default=Decimal("0.00"), description="Bonus amount")
    total_amount: Decimal = Field(..., description="Total payment amount")
    currency: str = Field(default="CAD", description="Currency")
    status: TutorPaymentStatus = Field(default=TutorPaymentStatus.PENDING, description="Payment status")
    approved_by: Optional[int] = Field(None, description="Manager who approved")
    approved_at: Optional[datetime] = Field(None, description="When approved")
    paid_date: Optional[date] = Field(None, description="Date payment was processed")
    payment_reference: Optional[str] = Field(None, description="Payment reference/transaction ID")
    bank_details: Optional[Dict[str, Any]] = Field(None, description="Bank details for direct deposit")
    
    @field_validator('total_hours', 'base_amount', 'bonus_amount', 'total_amount')
    @classmethod
    def validate_amounts(cls, v: Decimal) -> Decimal:
        """Validate amounts are non-negative."""
        if v < 0:
            raise ValueError('Amount cannot be negative')
        return v


class TutorPaymentItem(IdentifiedEntity):
    """Individual tutor payment item model."""
    
    payment_item_id: int = Field(..., description="Unique payment item identifier")
    payment_id: int = Field(..., description="Associated payment")
    appointment_id: int = Field(..., description="Associated appointment")
    client_id: int = Field(..., description="Client for this session")
    session_date: date = Field(..., description="Session date")
    hours_worked: Decimal = Field(..., description="Hours worked")
    hourly_rate: Decimal = Field(..., description="Hourly rate for this session")
    amount: Decimal = Field(..., description="Payment amount for this item")
    
    @field_validator('hours_worked', 'hourly_rate', 'amount')
    @classmethod
    def validate_amounts(cls, v: Decimal) -> Decimal:
        """Validate amounts are positive."""
        if v <= 0:
            raise ValueError('Amount must be positive')
        return v


class StripePayment(IdentifiedEntity):
    """Stripe payment tracking model."""
    
    stripe_payment_id: int = Field(..., description="Unique Stripe payment identifier")
    invoice_id: Optional[int] = Field(None, description="Associated invoice if applicable")
    client_id: int = Field(..., description="Client making payment")
    stripe_payment_intent_id: str = Field(..., description="Stripe PaymentIntent ID")
    stripe_charge_id: Optional[str] = Field(None, description="Stripe Charge ID")
    amount: Decimal = Field(..., description="Payment amount")
    currency: str = Field(default="CAD", description="Currency")
    status: str = Field(..., description="Stripe payment status")
    payment_method_type: str = Field(..., description="Payment method type")
    receipt_url: Optional[str] = Field(None, description="Stripe receipt URL")
    failure_reason: Optional[str] = Field(None, description="Failure reason if applicable")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional Stripe metadata")
    webhook_events: List[str] = Field(default_factory=list, description="Processed webhook events")
    
    @field_validator('status')
    @classmethod
    def validate_status(cls, v: str) -> str:
        """Validate Stripe payment status."""
        valid_statuses = {
            'requires_payment_method', 'requires_confirmation', 'requires_action',
            'processing', 'requires_capture', 'canceled', 'succeeded'
        }
        if v.lower() not in valid_statuses:
            raise ValueError(f'Status must be one of: {", ".join(valid_statuses)}')
        return v.lower()


class SubscriptionTransaction(IdentifiedEntity):
    """Subscription hour usage transaction model."""
    
    transaction_id: int = Field(..., description="Unique transaction identifier")
    subscription_id: int = Field(..., description="Associated subscription")
    appointment_id: int = Field(..., description="Associated appointment")
    hours_used: Decimal = Field(..., description="Hours deducted from subscription")
    rate_per_hour: Decimal = Field(..., description="Rate per hour for this transaction")
    amount_deducted: Decimal = Field(..., description="Amount deducted from subscription")
    transaction_date: datetime = Field(..., description="Transaction timestamp")
    remaining_hours_after: Decimal = Field(..., description="Remaining hours after this transaction")
    
    @field_validator('hours_used', 'rate_per_hour', 'amount_deducted')
    @classmethod
    def validate_amounts(cls, v: Decimal) -> Decimal:
        """Validate amounts are positive."""
        if v <= 0:
            raise ValueError('Amount must be positive')
        return v


class BillingReport(IdentifiedEntity):
    """Billing report model for monthly/periodic reporting."""
    
    report_id: int = Field(..., description="Unique report identifier")
    report_type: str = Field(..., description="Type of report")
    period_start: date = Field(..., description="Report period start")
    period_end: date = Field(..., description="Report period end")
    total_revenue: Decimal = Field(..., description="Total revenue for period")
    total_tutor_payments: Decimal = Field(..., description="Total tutor payments for period")
    net_income: Decimal = Field(..., description="Net income (revenue - payments)")
    total_invoices: int = Field(..., description="Number of invoices issued")
    paid_invoices: int = Field(..., description="Number of paid invoices")
    outstanding_amount: Decimal = Field(..., description="Outstanding invoice amount")
    currency: str = Field(default="CAD", description="Currency")
    generated_by: int = Field(..., description="Manager who generated report")
    report_data: Dict[str, Any] = Field(..., description="Detailed report data")
    
    @field_validator('report_type')
    @classmethod
    def validate_report_type(cls, v: str) -> str:
        """Validate report type."""
        valid_types = {'monthly', 'weekly', 'quarterly', 'annual', 'custom'}
        if v.lower() not in valid_types:
            raise ValueError(f'Report type must be one of: {", ".join(valid_types)}')
        return v.lower()


# ============================================
# Create/Update Schemas
# ============================================

class InvoiceCreate(BaseModel):
    """Schema for creating invoice."""
    
    model_config = ConfigDict(from_attributes=True)
    
    client_id: int = Field(..., description="Billed client")
    amount: Decimal = Field(..., gt=0, description="Invoice amount")
    tax_amount: Optional[Decimal] = Field(Decimal("0.00"), ge=0, description="Tax amount")
    due_days: Optional[int] = Field(30, ge=1, description="Days until due")
    billing_address: Dict[str, Any] = Field(..., description="Billing address")
    notes: Optional[str] = Field(None, description="Invoice notes")
    line_items: List[Dict[str, Any]] = Field(..., description="Invoice line items")


class InvoiceUpdate(BaseModel):
    """Schema for updating invoice."""
    
    model_config = ConfigDict(from_attributes=True)
    
    status: Optional[PaymentStatus] = Field(None, description="Invoice status")
    due_date: Optional[date] = Field(None, description="Due date")
    notes: Optional[str] = Field(None, description="Invoice notes")
    payment_method: Optional[str] = Field(None, description="Payment method")
    payment_reference: Optional[str] = Field(None, description="Payment reference")
    paid_by_parent: Optional[str] = Field(None, description="Which parent paid")


class PaymentRecord(BaseModel):
    """Schema for recording payment."""
    
    model_config = ConfigDict(from_attributes=True)
    
    invoice_id: int = Field(..., description="Invoice being paid")
    payment_method: str = Field(..., description="Payment method")
    payment_reference: Optional[str] = Field(None, description="Payment reference")
    paid_by_parent: Optional[str] = Field(None, description="Which parent paid")
    payment_date: Optional[date] = Field(None, description="Payment date")


class TutorPaymentCreate(BaseModel):
    """Schema for creating tutor payment."""
    
    model_config = ConfigDict(from_attributes=True)
    
    tutor_id: int = Field(..., description="Tutor being paid")
    payment_period_start: date = Field(..., description="Period start")
    payment_period_end: date = Field(..., description="Period end")
    appointment_ids: List[int] = Field(..., description="Appointments to include")
    bonus_amount: Optional[Decimal] = Field(Decimal("0.00"), ge=0, description="Bonus amount")


class TutorPaymentApproval(BaseModel):
    """Schema for approving tutor payment."""
    
    model_config = ConfigDict(from_attributes=True)
    
    payment_id: int = Field(..., description="Payment to approve")
    approved: bool = Field(..., description="Approval status")
    notes: Optional[str] = Field(None, description="Approval notes")


class BulkPaymentApproval(BaseModel):
    """Schema for bulk payment approval."""
    
    model_config = ConfigDict(from_attributes=True)
    
    payment_ids: List[int] = Field(..., description="Payments to approve")
    approved: bool = Field(..., description="Approval status")
    notes: Optional[str] = Field(None, description="Approval notes")


class ReportGeneration(BaseModel):
    """Schema for generating billing report."""
    
    model_config = ConfigDict(from_attributes=True)
    
    report_type: str = Field(..., description="Type of report")
    period_start: date = Field(..., description="Period start")
    period_end: date = Field(..., description="Period end")
    include_details: Optional[bool] = Field(True, description="Include detailed breakdown")


# ============================================
# Response Schemas
# ============================================

class InvoiceResponse(BaseModel):
    """Invoice response schema."""
    
    model_config = ConfigDict(from_attributes=True)
    
    invoice_id: int
    client_id: int
    invoice_number: str
    amount: Decimal
    currency: str
    tax_amount: Decimal
    total_amount: Decimal
    status: PaymentStatus
    issue_date: date
    due_date: date
    paid_date: Optional[date]
    payment_method: Optional[str]
    payment_reference: Optional[str]
    billing_address: Dict[str, Any]
    notes: Optional[str]
    paid_by_parent: Optional[str]
    days_overdue: Optional[int]
    line_items: List[Dict[str, Any]]
    client_name: Optional[str]  # For display
    created_at: datetime
    updated_at: datetime


class TutorPaymentResponse(BaseModel):
    """Tutor payment response schema."""
    
    model_config = ConfigDict(from_attributes=True)
    
    payment_id: int
    tutor_id: int
    payment_period_start: date
    payment_period_end: date
    total_hours: Decimal
    base_amount: Decimal
    bonus_amount: Decimal
    total_amount: Decimal
    currency: str
    status: TutorPaymentStatus
    approved_by: Optional[int]
    approved_at: Optional[datetime]
    paid_date: Optional[date]
    payment_reference: Optional[str]
    tutor_name: Optional[str]  # For display
    period_description: str  # For display
    payment_items: List[Dict[str, Any]]
    created_at: datetime
    updated_at: datetime


class StripePaymentResponse(BaseModel):
    """Stripe payment response schema."""
    
    model_config = ConfigDict(from_attributes=True)
    
    stripe_payment_id: int
    invoice_id: Optional[int]
    client_id: int
    stripe_payment_intent_id: str
    stripe_charge_id: Optional[str]
    amount: Decimal
    currency: str
    status: str
    payment_method_type: str
    receipt_url: Optional[str]
    failure_reason: Optional[str]
    client_name: Optional[str]  # For display
    created_at: datetime
    updated_at: datetime


class SubscriptionTransactionResponse(BaseModel):
    """Subscription transaction response schema."""
    
    model_config = ConfigDict(from_attributes=True)
    
    transaction_id: int
    subscription_id: int
    appointment_id: int
    hours_used: Decimal
    rate_per_hour: Decimal
    amount_deducted: Decimal
    transaction_date: datetime
    remaining_hours_after: Decimal
    appointment_details: Optional[str]  # For display
    created_at: datetime


class BillingReportResponse(BaseModel):
    """Billing report response schema."""
    
    model_config = ConfigDict(from_attributes=True)
    
    report_id: int
    report_type: str
    period_start: date
    period_end: date
    total_revenue: Decimal
    total_tutor_payments: Decimal
    net_income: Decimal
    profit_margin_percentage: float
    total_invoices: int
    paid_invoices: int
    outstanding_amount: Decimal
    currency: str
    generated_by: int
    report_data: Dict[str, Any]
    generated_by_name: Optional[str]  # For display
    created_at: datetime


class BillingSearchFilters(SearchFilters):
    """Billing-specific search filters."""
    
    client_id: Optional[int] = Field(None, description="Filter by client")
    tutor_id: Optional[int] = Field(None, description="Filter by tutor")
    status: Optional[PaymentStatus] = Field(None, description="Filter by status")
    payment_method: Optional[str] = Field(None, description="Filter by payment method")
    date_from: Optional[date] = Field(None, description="Start date filter")
    date_to: Optional[date] = Field(None, description="End date filter")
    amount_min: Optional[Decimal] = Field(None, description="Minimum amount filter")
    amount_max: Optional[Decimal] = Field(None, description="Maximum amount filter")
    overdue_only: Optional[bool] = Field(None, description="Only overdue invoices")
    approved_only: Optional[bool] = Field(None, description="Only approved payments")


class ParentPaymentSplit(IdentifiedEntity):
    """Invoice split between parents for separated families."""
    
    split_id: int = Field(..., description="Unique split identifier")
    invoice_id: int = Field(..., description="Invoice being split")
    parent_id: int = Field(..., description="Parent assigned portion")
    split_amount: Decimal = Field(..., description="Amount assigned to parent")
    split_percentage: Optional[Decimal] = Field(None, description="Percentage of invoice")
    notes: Optional[str] = Field(None, description="Split notes")


class ParentPaymentHistory(IdentifiedEntity):
    """Payment history from a specific parent."""
    
    payment_id: int = Field(..., description="Unique payment identifier")
    invoice_id: int = Field(..., description="Invoice being paid")
    parent_id: int = Field(..., description="Parent making payment")
    amount_paid: Decimal = Field(..., description="Amount paid")
    payment_method: str = Field(..., description="Payment method")
    payment_reference: Optional[str] = Field(None, description="Payment reference")
    payment_date: date = Field(..., description="Payment date")
    stripe_payment_intent_id: Optional[str] = Field(None, description="Stripe payment ID")
    notes: Optional[str] = Field(None, description="Payment notes")
    recorded_by: int = Field(..., description="User who recorded payment")


# ============================================
# Package Management Models
# ============================================

class PackageType(str, Enum):
    """Package type enumeration."""
    TECFEE = "tecfee"
    BUNDLE = "bundle"
    CUSTOM = "custom"


class PackageStatus(str, Enum):
    """Package purchase status."""
    ACTIVE = "active"
    COMPLETED = "completed"
    EXPIRED = "expired"
    REFUNDED = "refunded"


class PackageBase(BaseModel):
    """Base package attributes."""
    package_name: str = Field(..., min_length=1, max_length=255)
    package_type: PackageType
    service_id: Optional[int] = None
    session_count: int = Field(..., gt=0)
    session_duration_minutes: int = Field(default=60, gt=0)
    total_price: Decimal = Field(..., gt=0)
    currency_code: str = Field(default="CAD", max_length=3)
    is_group_package: bool = Field(default=False)
    min_participants: Optional[int] = Field(None, ge=1)
    max_participants: Optional[int] = Field(None, ge=1)
    is_active: bool = Field(default=True)
    valid_from: Optional[date] = None
    valid_until: Optional[date] = None
    description: Optional[str] = None
    terms_and_conditions: Optional[str] = None


class PackageCreate(PackageBase):
    """Package creation model."""
    pass


class PackageUpdate(BaseModel):
    """Package update model."""
    package_name: Optional[str] = Field(None, min_length=1, max_length=255)
    is_active: Optional[bool] = None
    valid_until: Optional[date] = None
    description: Optional[str] = None
    terms_and_conditions: Optional[str] = None
    session_count: Optional[int] = Field(None, gt=0)
    session_duration_minutes: Optional[int] = Field(None, gt=0)
    total_price: Optional[Decimal] = Field(None, gt=0)
    min_participants: Optional[int] = Field(None, ge=1)
    max_participants: Optional[int] = Field(None, ge=1)


class PackageResponse(PackageBase):
    """Package response model."""
    package_id: int
    service_name: Optional[str] = None
    subject_area: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    model_config = ConfigDict(from_attributes=True)


class PackagePurchaseBase(BaseModel):
    """Base package purchase attributes."""
    client_id: int
    dependant_id: Optional[int] = None
    payment_method: str = Field(..., max_length=50)
    notes: Optional[str] = None


class PackagePurchaseCreate(PackagePurchaseBase):
    """Package purchase creation model."""
    pass


class PackagePurchaseResponse(BaseModel):
    """Package purchase response model."""
    purchase_id: int
    package_id: int
    package_name: str
    package_type: PackageType
    client_id: int
    client_name: str
    dependant_id: Optional[int] = None
    dependant_name: Optional[str] = None
    purchase_date: date
    sessions_remaining: int
    total_sessions: int
    expire_date: Optional[date] = None
    amount_paid: Decimal
    payment_method: str
    stripe_payment_id: Optional[str] = None
    status: PackageStatus
    created_at: datetime
    
    model_config = ConfigDict(from_attributes=True)