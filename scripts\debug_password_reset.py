"""
Debug script to test password reset functionality step by step.
Run this script to identify where the password reset is failing.
"""

import asyncio
import asyncpg
import os
from datetime import datetime, timedelta

# Database connection
DATABASE_URL = os.environ.get('DATABASE_URL')

async def test_database_connection():
    """Test basic database connection."""
    print("=== Testing Database Connection ===")
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        print("✅ Database connection successful")
        
        # Check tables
        tables = await conn.fetch("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name
        """)
        print(f"\nTables found: {len(tables)}")
        for table in tables:
            print(f"  - {table['table_name']}")
        
        await conn.close()
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

async def test_user_lookup(email):
    """Test looking up a user by email."""
    print(f"\n=== Testing User Lookup for {email} ===")
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        
        # Look up user
        user = await conn.fetchrow("""
            SELECT user_id, email, email_verified, created_at
            FROM user_accounts
            WHERE LOWER(email) = LOWER($1) AND deleted_at IS NULL
        """, email)
        
        if user:
            print(f"✅ User found:")
            print(f"  - ID: {user['user_id']}")
            print(f"  - Email: {user['email']}")
            print(f"  - Verified: {user['email_verified']}")
            print(f"  - Created: {user['created_at']}")
        else:
            print(f"❌ No user found with email: {email}")
        
        await conn.close()
        return user
    except Exception as e:
        print(f"❌ User lookup failed: {e}")
        return None

async def test_token_creation(user_id, email):
    """Test creating a password reset token."""
    print(f"\n=== Testing Token Creation for User {user_id} ===")
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        
        # Create test token
        token_hash = f"test_hash_{datetime.now().timestamp()}"
        expires_at = datetime.now() + timedelta(hours=1)
        metadata = {
            'email': email,
            'initiated_by': 'debug_script'
        }
        
        # Try to insert token
        token = await conn.fetchrow("""
            INSERT INTO auth_tokens (
                user_id, token_type, token_hash, expires_at,
                metadata, ip_address, user_agent, created_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            RETURNING token_id, user_id, token_type
        """, 
            user_id, 
            'password_reset', 
            token_hash, 
            expires_at,
            metadata,  # Pass as dict for JSONB
            '127.0.0.1',
            'Debug Script',
            datetime.now()
        )
        
        print(f"✅ Token created successfully:")
        print(f"  - Token ID: {token['token_id']}")
        print(f"  - User ID: {token['user_id']}")
        print(f"  - Type: {token['token_type']}")
        
        await conn.close()
        return True
    except Exception as e:
        print(f"❌ Token creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def check_existing_tokens():
    """Check for any existing tokens in the database."""
    print("\n=== Checking Existing Tokens ===")
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        
        # Count tokens by type
        tokens = await conn.fetch("""
            SELECT 
                token_type,
                COUNT(*) as count,
                MAX(created_at) as latest
            FROM auth_tokens
            GROUP BY token_type
        """)
        
        if tokens:
            print("✅ Tokens found:")
            for token in tokens:
                print(f"  - {token['token_type']}: {token['count']} tokens, latest: {token['latest']}")
        else:
            print("❌ No tokens found in database")
        
        await conn.close()
    except Exception as e:
        print(f"❌ Failed to check tokens: {e}")

async def main():
    """Run all tests."""
    print("Password Reset Debug Script")
    print("=" * 50)
    
    # Test database connection
    if not await test_database_connection():
        return
    
    # Test with a specific email
    test_email = input("\nEnter email to test (or press Enter for default): ").strip()
    if not test_email:
        test_email = "<EMAIL>"
    
    # Look up user
    user = await test_user_lookup(test_email)
    
    if user:
        # Try to create a token
        await test_token_creation(user['user_id'], user['email'])
    
    # Check existing tokens
    await check_existing_tokens()

if __name__ == "__main__":
    if not DATABASE_URL:
        print("❌ DATABASE_URL environment variable not set!")
    else:
        asyncio.run(main())