"""
Tutor payment management API endpoints.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from typing import List, Optional, Dict, Any
from datetime import date, datetime
from decimal import Decimal

from app.services.tutor_payment_service import TutorPaymentService
from app.core.service_dependencies import get_tutor_payment_service
from app.models.payment_models import (
    PaymentFilter, PaymentStatus, BatchStatus
)
from app.core.auth_decorators import require_auth, require_roles
from app.core.dependencies import get_current_user, get_database
from app.core.exceptions import ValidationError, BusinessLogicError
from app.models.user_models import User

router = APIRouter(prefix="/payments", tags=["payments"])


@router.post("/calculate-weekly", response_model=Dict[str, Any])
@require_roles(["manager"])
async def calculate_weekly_payments(
    week_date: Optional[date] = Query(None, description="Reference date for week calculation (defaults to current week)"),
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    payment_service: TutorPaymentService = Depends(get_tutor_payment_service)
):
    """
    Calculate weekly tutor payments for Thursday-Wednesday cycle.
    This is typically called by the weekly cron job.
    """
    try:
        report = await payment_service.calculate_weekly_payments(db, week_date)
        
        return {
            "success": True,
            "week_start": report.week_start,
            "week_end": report.week_end,
            "total_tutors": report.total_tutors,
            "total_appointments": report.total_appointments,
            "total_service_amount": float(report.total_service_amount),
            "total_transport_amount": float(report.total_transport_amount),
            "total_bonus_amount": float(report.total_bonus_amount),
            "grand_total": float(report.grand_total),
            "batch": {
                "batch_id": report.batch.batch_id if report.batch else None,
                "batch_number": report.batch.batch_number if report.batch else None,
                "status": report.batch.status.value if report.batch else None,
                "total_payments": report.batch.total_payments if report.batch else 0
            } if report.batch else None,
            "tutor_summaries": report.tutor_summaries
        }
        
    except ServiceError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to calculate weekly payments: {str(e)}"
        )


@router.get("/weekly-report", response_model=Dict[str, Any])
@require_roles(["manager"])
async def get_weekly_payment_report(
    week_date: Optional[date] = Query(None, description="Reference date for week calculation"),
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    payment_service: TutorPaymentService = Depends(get_tutor_payment_service)
):
    """Get weekly payment report for managers."""
    try:
        report = await payment_service.get_weekly_payment_report(db, week_date)
        
        return {
            "week_start": report.week_start,
            "week_end": report.week_end,
            "total_tutors": report.total_tutors,
            "total_appointments": report.total_appointments,
            "total_service_amount": float(report.total_service_amount),
            "total_transport_amount": float(report.total_transport_amount),
            "total_bonus_amount": float(report.total_bonus_amount),
            "grand_total": float(report.grand_total),
            "batch": {
                "batch_id": report.batch.batch_id,
                "batch_number": report.batch.batch_number,
                "status": report.batch.status.value,
                "total_payments": report.batch.total_payments,
                "total_amount": float(report.batch.total_amount),
                "approved_by": report.batch.approved_by,
                "approved_at": report.batch.approved_at,
                "processed_count": report.batch.processed_count,
                "failed_count": report.batch.failed_count,
                "success_rate": report.batch.success_rate
            } if report.batch else None,
            "tutor_summaries": report.tutor_summaries
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get weekly payment report: {str(e)}"
        )


@router.get("/{payment_id}", response_model=Dict[str, Any])
@require_auth
async def get_payment(
    payment_id: int = Path(..., description="Payment ID"),
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    payment_service: TutorPaymentService = Depends(get_tutor_payment_service)
):
    """Get payment by ID with authorization checks."""
    try:
        payment = await payment_service.get_payment(db, payment_id)
        
        if not payment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Payment not found"
            )
        
        # Authorization check - tutors can only see their own payments
        if current_user.role == "tutor" and payment.tutor_id != current_user.user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        return {
            "payment_id": payment.payment_id,
            "tutor_id": payment.tutor_id,
            "appointment_id": payment.appointment_id,
            "service_amount": float(payment.service_amount),
            "transport_amount": float(payment.transport_amount),
            "bonus_amount": float(payment.bonus_amount),
            "total_amount": float(payment.total_amount),
            "payment_week_start": payment.payment_week_start,
            "payment_week_end": payment.payment_week_end,
            "week_label": payment.week_label,
            "status": payment.status.value,
            "is_paid": payment.is_paid,
            "payment_date": payment.payment_date,
            "payment_batch_id": payment.payment_batch_id,
            "stripe_payout_id": payment.stripe_payout_id,
            "stripe_payout_status": payment.stripe_payout_status,
            "created_at": payment.created_at,
            "updated_at": payment.updated_at
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get payment: {str(e)}"
        )


@router.get("", response_model=Dict[str, Any])
@require_auth
async def list_payments(
    tutor_id: Optional[int] = Query(None, description="Filter by tutor ID"),
    status: Optional[PaymentStatus] = Query(None, description="Filter by payment status"),
    week_start: Optional[date] = Query(None, description="Filter by week start"),
    week_end: Optional[date] = Query(None, description="Filter by week end"),
    batch_id: Optional[int] = Query(None, description="Filter by batch ID (0 for unassigned)"),
    min_amount: Optional[Decimal] = Query(None, description="Minimum amount filter"),
    max_amount: Optional[Decimal] = Query(None, description="Maximum amount filter"),
    limit: int = Query(100, le=1000, description="Number of results"),
    offset: int = Query(0, ge=0, description="Results offset"),
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    payment_service: TutorPaymentService = Depends(get_tutor_payment_service)
):
    """List payments with filtering and pagination."""
    try:
        # For tutors, restrict to their own payments
        if current_user.role == "tutor":
            tutor_id = current_user.user_id
        
        filter_params = PaymentFilter(
            tutor_id=tutor_id,
            status=status,
            week_start=week_start,
            week_end=week_end,
            batch_id=batch_id,
            min_amount=min_amount,
            max_amount=max_amount,
            limit=limit,
            offset=offset
        )
        
        payments = await payment_service.find_payments(db, filter_params)
        
        return {
            "payments": [
                {
                    "payment_id": payment.payment_id,
                    "tutor_id": payment.tutor_id,
                    "appointment_id": payment.appointment_id,
                    "service_amount": float(payment.service_amount),
                    "transport_amount": float(payment.transport_amount),
                    "bonus_amount": float(payment.bonus_amount),
                    "total_amount": float(payment.total_amount),
                    "payment_week_start": payment.payment_week_start,
                    "payment_week_end": payment.payment_week_end,
                    "week_label": payment.week_label,
                    "status": payment.status.value,
                    "is_paid": payment.is_paid,
                    "payment_date": payment.payment_date,
                    "payment_batch_id": payment.payment_batch_id
                }
                for payment in payments
            ],
            "total": len(payments),  # This would need to be calculated separately in a real implementation
            "limit": limit,
            "offset": offset,
            "has_more": len(payments) == limit
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list payments: {str(e)}"
        )


@router.get("/batches/{batch_id}", response_model=Dict[str, Any])
@require_roles(["manager"])
async def get_payment_batch(
    batch_id: int = Path(..., description="Batch ID"),
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    payment_service: TutorPaymentService = Depends(get_tutor_payment_service)
):
    """Get payment batch by ID with all payments."""
    try:
        batch = await payment_service.get_batch(db, batch_id)
        
        if not batch:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Payment batch not found"
            )
        
        return {
            "batch_id": batch.batch_id,
            "batch_number": batch.batch_number,
            "week_start": batch.week_start,
            "week_end": batch.week_end,
            "total_payments": batch.total_payments,
            "total_amount": float(batch.total_amount),
            "status": batch.status.value,
            "is_approved": batch.is_approved,
            "is_complete": batch.is_complete,
            "approved_by": batch.approved_by,
            "approved_at": batch.approved_at,
            "processed_at": batch.processed_at,
            "processed_count": batch.processed_count,
            "failed_count": batch.failed_count,
            "success_rate": batch.success_rate,
            "notes": batch.notes,
            "created_at": batch.created_at,
            "updated_at": batch.updated_at,
            "payments": [
                {
                    "payment_id": payment.payment_id,
                    "tutor_id": payment.tutor_id,
                    "appointment_id": payment.appointment_id,
                    "service_amount": float(payment.service_amount),
                    "transport_amount": float(payment.transport_amount),
                    "bonus_amount": float(payment.bonus_amount),
                    "total_amount": float(payment.total_amount),
                    "status": payment.status.value,
                    "is_paid": payment.is_paid
                }
                for payment in batch.payments
            ]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get payment batch: {str(e)}"
        )


@router.post("/batches/{batch_id}/approve", response_model=Dict[str, Any])
@require_roles(["manager"])
async def approve_payment_batch(
    batch_id: int = Path(..., description="Batch ID"),
    notes: Optional[str] = Query(None, description="Approval notes"),
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    payment_service: TutorPaymentService = Depends(get_tutor_payment_service)
):
    """Approve a payment batch for processing."""
    try:
        batch = await payment_service.approve_payment_batch(
            db=db,
            batch_id=batch_id,
            manager_id=current_user.user_id,
            notes=notes
        )
        
        return {
            "success": True,
            "message": f"Payment batch {batch.batch_number} approved",
            "batch_id": batch.batch_id,
            "batch_number": batch.batch_number,
            "status": batch.status.value,
            "approved_by": batch.approved_by,
            "approved_at": batch.approved_at,
            "total_amount": float(batch.total_amount),
            "total_payments": batch.total_payments
        }
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except ServiceError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to approve payment batch: {str(e)}"
        )


@router.post("/batches/{batch_id}/process", response_model=Dict[str, Any])
@require_roles(["manager"])
async def process_payment_batch(
    batch_id: int = Path(..., description="Batch ID"),
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    payment_service: TutorPaymentService = Depends(get_tutor_payment_service)
):
    """Process an approved payment batch through Stripe."""
    try:
        result = await payment_service.process_payment_batch(db, batch_id)
        
        return {
            "success": True,
            "message": f"Payment batch {batch_id} processing completed",
            "batch_id": result["batch_id"],
            "total_payments": result["total_payments"],
            "processed_count": result["processed_count"],
            "failed_count": result["failed_count"],
            "failed_payment_ids": result["failed_payment_ids"],
            "success_rate": result["success_rate"]
        }
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process payment batch: {str(e)}"
        )


@router.get("/tutors/{tutor_id}/history", response_model=Dict[str, Any])
@require_auth
async def get_tutor_payment_history(
    tutor_id: int = Path(..., description="Tutor ID"),
    limit: int = Query(50, le=100, description="Number of results"),
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    payment_service: TutorPaymentService = Depends(get_tutor_payment_service)
):
    """Get payment history for a tutor."""
    try:
        # Authorization check - tutors can only see their own history
        if current_user.role == "tutor" and tutor_id != current_user.user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        history = await payment_service.get_tutor_payment_history(db, tutor_id, limit)
        
        return {
            "tutor_id": tutor_id,
            "payment_count": len(history),
            "payments": [
                {
                    "payment_id": payment["payment_id"],
                    "appointment_id": payment["appointment_id"],
                    "appointment_date": payment["appointment_date"],
                    "client_name": payment["client_name"],
                    "service_name": payment["service_name"],
                    "service_amount": float(payment["service_amount"]),
                    "transport_amount": float(payment["transport_amount"]),
                    "bonus_amount": float(payment["bonus_amount"]),
                    "total_amount": float(payment["total_amount"]),
                    "payment_week_start": payment["payment_week_start"],
                    "payment_week_end": payment["payment_week_end"],
                    "status": payment["status"],
                    "payment_date": payment["payment_date"],
                    "batch_number": payment["batch_number"],
                    "created_at": payment["created_at"]
                }
                for payment in history
            ]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get tutor payment history: {str(e)}"
        )


@router.get("/stats", response_model=Dict[str, Any])
@require_roles(["manager"])
async def get_payment_stats(
    start_date: Optional[date] = Query(None, description="Start date for statistics"),
    end_date: Optional[date] = Query(None, description="End date for statistics"),
    current_user: User = Depends(get_current_user),
    db=Depends(get_database),
    payment_service: TutorPaymentService = Depends(get_tutor_payment_service)
):
    """Get payment statistics for a period."""
    try:
        stats = await payment_service.get_payment_stats(db, start_date, end_date)
        
        return {
            "period": {
                "start_date": start_date,
                "end_date": end_date
            },
            "statistics": stats
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get payment statistics: {str(e)}"
        )