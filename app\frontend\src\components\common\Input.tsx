import React from 'react';
import { clsx } from 'clsx';
import { designTokens, componentTokens } from '../styles/design-tokens';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  hint?: string;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
}

export const Input: React.FC<InputProps> = ({
  label,
  error,
  hint,
  icon,
  iconPosition = 'left',
  fullWidth = true,
  className,
  id,
  ...props
}) => {
  const inputId = id || label?.toLowerCase().replace(/\s+/g, '-');
  const hasIcon = Boolean(icon);
  
  const baseStyles = 'w-full px-4 py-3 border rounded-xl bg-background-secondary text-text-primary transition-all duration-200 focus:outline-none focus:border-accent-red focus:bg-white focus:shadow-focus';
  
  const iconStyles = {
    left: 'pl-11',
    right: 'pr-11',
  };

  return (
    <div className={clsx(fullWidth ? 'w-full' : 'inline-block', className)}>
      {label && (
        <label
          htmlFor={inputId}
          className="block text-sm font-medium text-text-primary mb-2"
        >
          {label}
          {props.required && <span className="text-semantic-error ml-1" aria-label="required">*</span>}
        </label>
      )}
      
      <div className="relative">
        {icon && iconPosition === 'left' && (
          <div className="absolute left-3 top-1/2 -translate-y-1/2 text-text-muted">
            {icon}
          </div>
        )}
        
        <input
          id={inputId}
          className={clsx(
            baseStyles,
            hasIcon && iconStyles[iconPosition],
            error ? 'border-semantic-error focus:border-semantic-error focus:shadow-error' : 'border-border-primary hover:border-border-secondary',
            props.disabled && 'opacity-60 cursor-not-allowed bg-background-tertiary'
          )}
          aria-invalid={Boolean(error)}
          aria-describedby={error ? `${inputId}-error` : hint ? `${inputId}-hint` : undefined}
          {...props}
        />
        
        {icon && iconPosition === 'right' && (
          <div className="absolute right-3 top-1/2 -translate-y-1/2 text-text-muted">
            {icon}
          </div>
        )}
      </div>
      
      {error && (
        <p id={`${inputId}-error`} className="mt-2 text-sm text-semantic-error">
          {error}
        </p>
      )}
      
      {hint && !error && (
        <p id={`${inputId}-hint`} className="mt-2 text-sm text-text-muted">
          {hint}
        </p>
      )}
    </div>
  );
};

interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  error?: string;
  hint?: string;
  fullWidth?: boolean;
}

export const Textarea: React.FC<TextareaProps> = ({
  label,
  error,
  hint,
  fullWidth = true,
  className,
  id,
  rows = 4,
  ...props
}) => {
  const textareaId = id || label?.toLowerCase().replace(/\s+/g, '-');
  
  const baseStyles = 'w-full px-4 py-3 border rounded-xl bg-background-secondary text-text-primary transition-all duration-200 focus:outline-none focus:border-accent-red focus:bg-white focus:shadow-focus resize-y min-h-[120px]';

  return (
    <div className={clsx(fullWidth ? 'w-full' : 'inline-block', className)}>
      {label && (
        <label
          htmlFor={textareaId}
          className="block text-sm font-medium text-text-primary mb-2"
        >
          {label}
          {props.required && <span className="text-semantic-error ml-1" aria-label="required">*</span>}
        </label>
      )}
      
      <textarea
        id={textareaId}
        rows={rows}
        className={clsx(
          baseStyles,
          error ? 'border-semantic-error focus:border-semantic-error focus:shadow-error' : 'border-border-primary hover:border-border-secondary',
          props.disabled && 'opacity-60 cursor-not-allowed bg-background-tertiary'
        )}
        aria-invalid={Boolean(error)}
        aria-describedby={error ? `${textareaId}-error` : hint ? `${textareaId}-hint` : undefined}
        {...props}
      />
      
      {error && (
        <p id={`${textareaId}-error`} className="mt-2 text-sm text-semantic-error">
          {error}
        </p>
      )}
      
      {hint && !error && (
        <p id={`${textareaId}-hint`} className="mt-2 text-sm text-text-muted">
          {hint}
        </p>
      )}
    </div>
  );
};

export default Input;