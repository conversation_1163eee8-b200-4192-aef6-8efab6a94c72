# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# PyCharm
.idea/

# VS Code
.vscode/
*.code-workspace

# Jupyter Notebook
.ipynb_checkpoints
*.ipynb

# pyenv
.python-version

# Database
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log
app/logs/*.log
uvicorn.log
uvicorn_new.log

# Environment variables
.env
.env.local
.env.*.local
# Keep example files
!.env.example
!.env.google_oauth_example

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Backup files
*.bak
*.backup
*.old
*.orig
*.swp
*.swo
*~

# Test files in root (keep test directory)
/test_*.py
/test_*.html
/simple_frontend.html

# Database scripts in root
/check_*.py
/create_test_*.py
/setup_*.py
/reset_*.py
/mark_migrations_*.py

# Debug scripts
/debug_*.py

# Multiple startup scripts (keep only necessary ones)
/start.py
/start_minimal.py
/run_server.py

# Frontend
node_modules/
/app/frontend/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Frontend build
/app/frontend/dist/
/app/frontend/build/
/app/frontend/.next/
/app/frontend/out/

# Parcel cache
/app/frontend/.cache/
/app/frontend/.parcel-cache/

# Next.js
/app/frontend/.next/
/app/frontend/out/

# Gatsby
/app/frontend/public/
/app/frontend/.cache/

# Coverage
coverage/
*.lcov
.nyc_output/

# Temporary files
*.tmp
*.temp
tmp/
temp/

# Uploads (if not tracking uploaded files)
uploads/
media/

# Stripe CLI
stripe/

# Railway
.railway/

# Documentation build
docs/_build/
docs/.doctrees/

# MyPy
.mypy_cache/
.dmypy.json
dmypy.json

# Pytest
.pytest_cache/
.coverage
htmlcov/

# Ruff
.ruff_cache/

# UV
.uv/

# Local development
local_settings.py
*.local

# Migrations marked as applied
migrations_applied.txt

# SSL certificates (for local development)
*.pem
*.key
*.crt

# Editor temporary files
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Vim
[._]*.s[a-v][a-z]
[._]*.sw[a-p]
[._]s[a-rt-v][a-z]
[._]ss[a-gi-z]
[._]sw[a-p]
Session.vim
Sessionx.vim
.netrwhist
tags
[._]*.un~