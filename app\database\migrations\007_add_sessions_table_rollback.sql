-- Rollback migration 007_add_sessions_table
-- Version: 007
-- Description: Removes sessions table
-- Author: TutorAide Development Team
-- Date: 2025-01-10

-- Drop indexes
DROP INDEX IF EXISTS idx_user_sessions_token_hash;
DROP INDEX IF EXISTS idx_user_sessions_user_id;
DROP INDEX IF EXISTS idx_user_sessions_expires_at;
DROP INDEX IF EXISTS idx_user_sessions_last_activity;

-- Drop trigger
DROP TRIGGER IF EXISTS update_user_sessions_updated_at ON user_sessions;

-- Drop table
DROP TABLE IF EXISTS user_sessions;