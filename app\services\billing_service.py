"""
Billing service for invoice generation, payment processing, and financial management.
"""

import asyncpg
import logging
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, date, timedelta
from decimal import Decimal
import uuid

from app.database.repositories.billing_repository import BillingRepository
from app.database.repositories.appointment_repository import AppointmentRepository
from app.database.repositories.client_repository import ClientRepository
from app.database.repositories.tutor_repository import TutorRepository
from app.core.exceptions import ResourceNotFoundError, BusinessLogicError, ValidationError
from app.core.timezone import now_est
from app.config.database import get_db_connection
from app.models.billing_models import (
    Invoice, InvoiceCreate, InvoiceUpdate, PaymentRecord,
    TutorPayment, TutorPaymentCreate, TutorPaymentApproval,
    InvoiceLineItem, StripePayment, SubscriptionTransaction,
    BillingReport, ReportGeneration, BulkPaymentApproval,
    InvoiceResponse, TutorPaymentResponse
)
from app.models.base import PaymentStatus, TutorPaymentStatus, AppointmentStatus

logger = logging.getLogger(__name__)


class BillingService:
    """Comprehensive billing service for invoice and payment management."""
    
    def __init__(self):
        self.billing_repo = BillingRepository()
        self.appointment_repo = AppointmentRepository()
        self.client_repo = ClientRepository()
        self.tutor_repo = TutorRepository()
        self.tax_rates = self._get_canadian_tax_rates()
        # Import subscription service for dual billing
        from app.services.subscription_service import SubscriptionService
        self.subscription_service = SubscriptionService()
    
    def _get_canadian_tax_rates(self) -> Dict[str, Dict[str, Decimal]]:
        """Get Canadian tax rates by province/territory."""
        return {
            'AB': {'gst': Decimal('0.05'), 'pst': Decimal('0.00'), 'hst': Decimal('0.00')},
            'BC': {'gst': Decimal('0.05'), 'pst': Decimal('0.07'), 'hst': Decimal('0.00')},
            'MB': {'gst': Decimal('0.05'), 'pst': Decimal('0.07'), 'hst': Decimal('0.00')},
            'NB': {'gst': Decimal('0.00'), 'pst': Decimal('0.00'), 'hst': Decimal('0.15')},
            'NL': {'gst': Decimal('0.00'), 'pst': Decimal('0.00'), 'hst': Decimal('0.15')},
            'NS': {'gst': Decimal('0.00'), 'pst': Decimal('0.00'), 'hst': Decimal('0.15')},
            'NT': {'gst': Decimal('0.05'), 'pst': Decimal('0.00'), 'hst': Decimal('0.00')},
            'NU': {'gst': Decimal('0.05'), 'pst': Decimal('0.00'), 'hst': Decimal('0.00')},
            'ON': {'gst': Decimal('0.00'), 'pst': Decimal('0.00'), 'hst': Decimal('0.13')},
            'PE': {'gst': Decimal('0.00'), 'pst': Decimal('0.00'), 'hst': Decimal('0.15')},
            'QC': {'gst': Decimal('0.05'), 'pst': Decimal('0.09975'), 'hst': Decimal('0.00')},
            'SK': {'gst': Decimal('0.05'), 'pst': Decimal('0.06'), 'hst': Decimal('0.00')},
            'YT': {'gst': Decimal('0.05'), 'pst': Decimal('0.00'), 'hst': Decimal('0.00')}
        }
    
    async def generate_invoice_from_completed_sessions(
        self,
        client_id: int,
        session_date_start: date,
        session_date_end: date,
        created_by: int,
        auto_generate: bool = True
    ) -> Optional[InvoiceResponse]:
        """
        Generate invoice from completed tutoring sessions.
        
        Args:
            client_id: Client to bill
            session_date_start: Start date for session range
            session_date_end: End date for session range
            created_by: User creating the invoice
            auto_generate: Whether to auto-generate or return preview
        
        Returns:
            Generated invoice or None if no billable sessions
        """
        try:
            # Get completed appointments for the period
            completed_sessions = await self._get_billable_sessions(
                client_id, session_date_start, session_date_end
            )
            
            if not completed_sessions:
                logger.info(f"No billable sessions found for client {client_id} in period {session_date_start} to {session_date_end}")
                return None
            
            # Get client details for billing
            client = await self.client_repo.find_by_id(client_id)
            if not client:
                raise ResourceNotFoundError(f"Client {client_id} not found")
            
            # Calculate invoice amounts
            line_items = []
            subtotal = Decimal('0.00')
            
            for session in completed_sessions:
                duration_hours = Decimal(str(session['duration_hours']))
                client_rate = Decimal(str(session['client_rate']))
                line_total = duration_hours * client_rate
                
                line_items.append({
                    'appointment_id': session['appointment_id'],
                    'description': f"Tutoring session - {session['subject']} ({session['tutor_name']})",
                    'session_date': session['appointment_date'].isoformat(),
                    'duration_hours': duration_hours,
                    'hourly_rate': client_rate,
                    'line_total': line_total
                })
                
                subtotal += line_total
            
            # Calculate taxes based on client's province
            province = client.get('province', 'ON')
            tax_rates = self.tax_rates.get(province, self.tax_rates['ON'])
            tax_amount = self._calculate_tax_amount(subtotal, tax_rates)
            total_amount = subtotal + tax_amount
            
            # Generate unique invoice number
            invoice_number = await self._generate_invoice_number()
            
            # Prepare billing address
            billing_address = {
                'name': f"{client.get('first_name', '')} {client.get('last_name', '')}".strip(),
                'address_line_1': client.get('address_line_1', ''),
                'address_line_2': client.get('address_line_2', ''),
                'city': client.get('city', ''),
                'province': client.get('province', ''),
                'postal_code': client.get('postal_code', ''),
                'country': 'Canada'
            }
            
            if auto_generate:
                # Create the invoice
                async with get_db_connection() as conn:
                    # Insert invoice
                    invoice_id = await conn.fetchval("""
                        INSERT INTO invoices (
                            client_id, invoice_number, amount, currency,
                            tax_amount, total_amount, status, issue_date, due_date,
                            billing_address, created_by, created_at, updated_at
                        ) VALUES (
                            $1, $2, $3, 'CAD', $4, $5, $6, $7, $8, $9, $10, $11, $11
                        ) RETURNING invoice_id
                    """, 
                        client_id, invoice_number, subtotal, tax_amount, total_amount,
                        PaymentStatus.PENDING.value, date.today(), 
                        date.today() + timedelta(days=30),
                        billing_address, created_by, now_est()
                    )
                    
                    # Insert line items
                    for item in line_items:
                        await conn.execute("""
                            INSERT INTO invoice_line_items (
                                invoice_id, appointment_id, description,
                                quantity, unit_price, line_total,
                                created_at, updated_at
                            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $7)
                        """,
                            invoice_id, item['appointment_id'], item['description'],
                            item['duration_hours'], item['hourly_rate'], item['line_total'],
                            now_est()
                        )
                    
                    # Mark appointments as invoiced
                    appointment_ids = [session['appointment_id'] for session in completed_sessions]
                    await conn.execute("""
                        UPDATE appointments 
                        SET invoice_id = $1, updated_at = $2
                        WHERE appointment_id = ANY($3)
                    """, invoice_id, now_est(), appointment_ids)
                
                # Log the invoice creation
                await self._log_billing_change(
                    conn=conn,
                    entity_type='invoice',
                    entity_id=invoice_id,
                    action='created',
                    performed_by=created_by,
                    change_reason=f"Generated from {len(completed_sessions)} completed sessions",
                    amount_after=total_amount
                )
                
                logger.info(f"Generated invoice {invoice_number} for client {client_id} with {len(line_items)} sessions")
                
                # Return the created invoice
                return await self.get_invoice_by_id(invoice_id)
            
            else:
                # Return preview data
                return InvoiceResponse(
                    invoice_id=0,  # Preview, no ID yet
                    client_id=client_id,
                    invoice_number=f"PREVIEW-{invoice_number}",
                    amount=subtotal,
                    currency="CAD",
                    tax_amount=tax_amount,
                    total_amount=total_amount,
                    status=PaymentStatus.PENDING,
                    issue_date=date.today(),
                    due_date=date.today() + timedelta(days=30),
                    paid_date=None,
                    payment_method=None,
                    payment_reference=None,
                    billing_address=billing_address,
                    notes=None,
                    paid_by_parent=None,
                    days_overdue=None,
                    line_items=line_items,
                    client_name=billing_address['name'],
                    created_at=now_est(),
                    updated_at=now_est()
                )
        
        except Exception as e:
            logger.error(f"Error generating invoice for client {client_id}: {str(e)}")
            raise BusinessLogicError(f"Failed to generate invoice: {str(e)}")
    
    async def _get_billable_sessions(
        self,
        client_id: int,
        date_start: date,
        date_end: date
    ) -> List[Dict[str, Any]]:
        """Get completed sessions ready for billing."""
        async with get_db_connection() as conn:
            return await conn.fetch("""
                SELECT 
                    a.appointment_id,
                    a.appointment_date,
                    a.start_time,
                    a.end_time,
                    a.duration_hours,
                    a.client_rate,
                    a.tutor_rate,
                    a.subject,
                    a.session_type,
                    t.first_name || ' ' || t.last_name as tutor_name,
                    c.first_name || ' ' || c.last_name as client_name
                FROM appointments a
                JOIN tutors t ON a.tutor_id = t.tutor_id
                JOIN clients c ON a.client_id = c.client_id
                WHERE a.client_id = $1
                    AND a.appointment_date BETWEEN $2 AND $3
                    AND a.status = $4
                    AND a.invoice_id IS NULL  -- Not already invoiced
                    AND a.duration_hours > 0  -- Valid duration
                ORDER BY a.appointment_date, a.start_time
            """, client_id, date_start, date_end, AppointmentStatus.COMPLETED.value)
    
    def _calculate_tax_amount(self, subtotal: Decimal, tax_rates: Dict[str, Decimal]) -> Decimal:
        """Calculate Canadian tax amount based on province."""
        gst = subtotal * tax_rates['gst']
        pst = subtotal * tax_rates['pst']
        hst = subtotal * tax_rates['hst']
        
        # Round to 2 decimal places
        return round(gst + pst + hst, 2)
    
    async def _generate_invoice_number(self) -> str:
        """Generate unique invoice number."""
        current_date = date.today()
        year_month = current_date.strftime("%Y%m")
        
        async with get_db_connection() as conn:
            # Get the next sequence number for this month
            sequence = await conn.fetchval("""
                SELECT COALESCE(MAX(
                    CAST(SUBSTRING(invoice_number FROM 8) AS INTEGER)
                ), 0) + 1
                FROM invoices 
                WHERE invoice_number LIKE $1
            """, f"INV{year_month}%")
            
            return f"INV{year_month}{sequence:04d}"
    
    async def get_invoice_by_id(self, invoice_id: int) -> Optional[InvoiceResponse]:
        """Get invoice by ID with full details."""
        async with get_db_connection() as conn:
            invoice_data = await conn.fetchrow("""
                SELECT 
                    i.*,
                    c.first_name || ' ' || c.last_name as client_name,
                    CASE 
                        WHEN i.status = 'pending' AND i.due_date < CURRENT_DATE 
                        THEN CURRENT_DATE - i.due_date 
                        ELSE NULL 
                    END as days_overdue
                FROM invoices i
                JOIN clients c ON i.client_id = c.client_id
                WHERE i.invoice_id = $1
            """, invoice_id)
            
            if not invoice_data:
                return None
            
            # Get line items
            line_items = await conn.fetch("""
                SELECT * FROM invoice_line_items 
                WHERE invoice_id = $1 
                ORDER BY line_item_id
            """, invoice_id)
            
            # Convert to response format
            return InvoiceResponse(
                **dict(invoice_data),
                line_items=[dict(item) for item in line_items]
            )
    
    async def record_payment(
        self,
        payment_data: PaymentRecord,
        recorded_by: int
    ) -> InvoiceResponse:
        """Record payment against an invoice."""
        try:
            invoice = await self.get_invoice_by_id(payment_data.invoice_id)
            if not invoice:
                raise ResourceNotFoundError(f"Invoice {payment_data.invoice_id} not found")
            
            if invoice.status == PaymentStatus.PAID:
                raise BusinessLogicError("Invoice is already paid")
            
            payment_date = payment_data.payment_date or date.today()
            
            async with get_db_connection() as conn:
                await conn.execute("""
                    UPDATE invoices 
                    SET 
                        status = $1,
                        paid_date = $2,
                        payment_method = $3,
                        payment_reference = $4,
                        paid_by_parent = $5,
                        updated_at = $6
                    WHERE invoice_id = $7
                """,
                    PaymentStatus.PAID.value, payment_date,
                    payment_data.payment_method, payment_data.payment_reference,
                    payment_data.paid_by_parent, now_est(), payment_data.invoice_id
                )
                
                # Log the payment
                await self._log_billing_change(
                    conn=conn,
                    entity_type='invoice',
                    entity_id=payment_data.invoice_id,
                    action='paid',
                    performed_by=recorded_by,
                    change_reason=f"Payment via {payment_data.payment_method}",
                    field_name='status',
                    old_value='pending',
                    new_value='paid',
                    amount_before=invoice.total_amount,
                    amount_after=invoice.total_amount,
                    stripe_transaction_id=payment_data.payment_reference if payment_data.payment_method == 'stripe' else None
                )
            
            logger.info(f"Recorded payment for invoice {payment_data.invoice_id} via {payment_data.payment_method}")
            
            return await self.get_invoice_by_id(payment_data.invoice_id)
        
        except Exception as e:
            logger.error(f"Error recording payment for invoice {payment_data.invoice_id}: {str(e)}")
            raise BusinessLogicError(f"Failed to record payment: {str(e)}")
    
    async def create_tutor_payment(
        self,
        payment_data: TutorPaymentCreate,
        created_by: int
    ) -> TutorPaymentResponse:
        """Create tutor payment for weekly period."""
        try:
            # Validate tutor exists
            tutor = await self.tutor_repo.find_by_id(payment_data.tutor_id)
            if not tutor:
                raise ResourceNotFoundError(f"Tutor {payment_data.tutor_id} not found")
            
            # Get payment period sessions
            sessions = await self._get_tutor_sessions_for_payment(
                payment_data.tutor_id,
                payment_data.appointment_ids
            )
            
            if not sessions:
                raise BusinessLogicError("No valid sessions found for payment")
            
            # Calculate payment amounts
            total_hours = Decimal('0.00')
            base_amount = Decimal('0.00')
            
            payment_items = []
            for session in sessions:
                duration = Decimal(str(session['duration_hours']))
                rate = Decimal(str(session['tutor_rate']))
                amount = duration * rate
                
                total_hours += duration
                base_amount += amount
                
                payment_items.append({
                    'appointment_id': session['appointment_id'],
                    'client_id': session['client_id'],
                    'session_date': session['appointment_date'],
                    'hours_worked': duration,
                    'hourly_rate': rate,
                    'amount': amount
                })
            
            total_amount = base_amount + payment_data.bonus_amount
            
            async with get_db_connection() as conn:
                # Create payment record
                payment_id = await conn.fetchval("""
                    INSERT INTO tutor_payments (
                        tutor_id, payment_period_start, payment_period_end,
                        total_hours, base_amount, bonus_amount, total_amount,
                        currency, status, created_by, created_at, updated_at
                    ) VALUES (
                        $1, $2, $3, $4, $5, $6, $7, 'CAD', $8, $9, $10, $10
                    ) RETURNING payment_id
                """,
                    payment_data.tutor_id, payment_data.payment_period_start,
                    payment_data.payment_period_end, total_hours, base_amount,
                    payment_data.bonus_amount, total_amount,
                    TutorPaymentStatus.PENDING.value, created_by, now_est()
                )
                
                # Create payment items
                for item in payment_items:
                    await conn.execute("""
                        INSERT INTO tutor_payment_items (
                            payment_id, appointment_id, client_id, session_date,
                            hours_worked, hourly_rate, amount, created_at, updated_at
                        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $8)
                    """,
                        payment_id, item['appointment_id'], item['client_id'],
                        item['session_date'], item['hours_worked'],
                        item['hourly_rate'], item['amount'], now_est()
                    )
                
                # Mark appointments as included in payment
                await conn.execute("""
                    UPDATE appointments 
                    SET tutor_payment_id = $1, updated_at = $2
                    WHERE appointment_id = ANY($3)
                """, payment_id, now_est(), payment_data.appointment_ids)
                
                # Log the tutor payment creation
                await self._log_billing_change(
                    conn=conn,
                    entity_type='tutor_payment',
                    entity_id=payment_id,
                    action='created',
                    performed_by=created_by,
                    change_reason=f"Payment for {len(payment_items)} sessions, period {payment_data.payment_period_start} to {payment_data.payment_period_end}",
                    amount_after=total_amount
                )
            
            logger.info(f"Created tutor payment {payment_id} for tutor {payment_data.tutor_id}")
            
            return await self.get_tutor_payment_by_id(payment_id)
        
        except Exception as e:
            logger.error(f"Error creating tutor payment: {str(e)}")
            raise BusinessLogicError(f"Failed to create tutor payment: {str(e)}")
    
    async def _get_tutor_sessions_for_payment(
        self,
        tutor_id: int,
        appointment_ids: List[int]
    ) -> List[Dict[str, Any]]:
        """Get tutor sessions for payment calculation."""
        async with get_db_connection() as conn:
            return await conn.fetch("""
                SELECT 
                    a.appointment_id,
                    a.client_id,
                    a.appointment_date,
                    a.duration_hours,
                    a.tutor_rate,
                    a.subject,
                    c.first_name || ' ' || c.last_name as client_name
                FROM appointments a
                JOIN clients c ON a.client_id = c.client_id
                WHERE a.tutor_id = $1
                    AND a.appointment_id = ANY($2)
                    AND a.status = $3
                    AND a.tutor_payment_id IS NULL  -- Not already paid
                ORDER BY a.appointment_date
            """, tutor_id, appointment_ids, AppointmentStatus.COMPLETED.value)
    
    async def get_tutor_payment_by_id(self, payment_id: int) -> Optional[TutorPaymentResponse]:
        """Get tutor payment by ID with details."""
        async with get_db_connection() as conn:
            payment_data = await conn.fetchrow("""
                SELECT 
                    tp.*,
                    t.first_name || ' ' || t.last_name as tutor_name,
                    tp.payment_period_start::text || ' to ' || tp.payment_period_end::text as period_description
                FROM tutor_payments tp
                JOIN tutors t ON tp.tutor_id = t.tutor_id
                WHERE tp.payment_id = $1
            """, payment_id)
            
            if not payment_data:
                return None
            
            # Get payment items
            payment_items = await conn.fetch("""
                SELECT * FROM tutor_payment_items 
                WHERE payment_id = $1 
                ORDER BY session_date
            """, payment_id)
            
            return TutorPaymentResponse(
                **dict(payment_data),
                payment_items=[dict(item) for item in payment_items]
            )
    
    async def approve_tutor_payments(
        self,
        approval_data: BulkPaymentApproval,
        approved_by: int
    ) -> List[TutorPaymentResponse]:
        """Approve multiple tutor payments."""
        try:
            approved_payments = []
            
            async with get_db_connection() as conn:
                for payment_id in approval_data.payment_ids:
                    # Check payment exists and is pending
                    payment = await conn.fetchrow("""
                        SELECT payment_id, status FROM tutor_payments 
                        WHERE payment_id = $1
                    """, payment_id)
                    
                    if not payment:
                        logger.warning(f"Payment {payment_id} not found, skipping")
                        continue
                    
                    if payment['status'] not in [TutorPaymentStatus.PENDING.value, TutorPaymentStatus.APPROVED.value]:
                        logger.warning(f"Payment {payment_id} not in valid state for approval, skipping")
                        continue
                    
                    # Update payment status
                    new_status = TutorPaymentStatus.APPROVED if approval_data.approved else TutorPaymentStatus.REJECTED
                    
                    # Get payment details for audit logging
                    payment_details = await conn.fetchrow("""
                        SELECT total_amount, status FROM tutor_payments WHERE payment_id = $1
                    """, payment_id)
                    
                    old_status = payment_details['status']
                    total_amount = payment_details['total_amount']
                    
                    await conn.execute("""
                        UPDATE tutor_payments 
                        SET 
                            status = $1,
                            approved_by = $2,
                            approved_at = $3,
                            updated_at = $3
                        WHERE payment_id = $4
                    """, new_status.value, approved_by, now_est(), payment_id)
                    
                    # Log the approval/rejection
                    action = 'approved' if approval_data.approved else 'rejected'
                    await self._log_billing_change(
                        conn=conn,
                        entity_type='tutor_payment',
                        entity_id=payment_id,
                        action=action,
                        performed_by=approved_by,
                        change_reason=approval_data.notes if hasattr(approval_data, 'notes') else None,
                        field_name='status',
                        old_value=old_status,
                        new_value=new_status.value,
                        amount_before=total_amount,
                        amount_after=total_amount
                    )
                    
                    # Get updated payment
                    updated_payment = await self.get_tutor_payment_by_id(payment_id)
                    if updated_payment:
                        approved_payments.append(updated_payment)
            
            action = "approved" if approval_data.approved else "rejected"
            logger.info(f"{action.capitalize()} {len(approved_payments)} tutor payments")
            
            return approved_payments
        
        except Exception as e:
            logger.error(f"Error approving tutor payments: {str(e)}")
            raise BusinessLogicError(f"Failed to approve payments: {str(e)}")
    
    async def get_weekly_payment_periods(self, year: int) -> List[Dict[str, Any]]:
        """Get all weekly payment periods for a year (Thursday to Wednesday)."""
        periods = []
        
        # Start from first Thursday of the year
        start_date = date(year, 1, 1)
        while start_date.weekday() != 3:  # 3 = Thursday
            start_date += timedelta(days=1)
        
        current_date = start_date
        end_date = date(year, 12, 31)
        
        week_number = 1
        while current_date <= end_date:
            period_end = current_date + timedelta(days=6)  # Wednesday
            if period_end > end_date:
                period_end = end_date
            
            periods.append({
                'week_number': week_number,
                'period_start': current_date,
                'period_end': period_end,
                'year': year
            })
            
            current_date += timedelta(days=7)
            week_number += 1
        
        return periods
    
    async def process_appointment_billing(
        self,
        appointment_id: int,
        force_invoice: bool = False,
        performed_by: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Process billing for a completed appointment using dual billing logic.
        
        Logic:
        1. Check if client has active subscription with enough hours
        2. If yes and not force_invoice, deduct from subscription
        3. If no or force_invoice, create invoice or add to pending invoice
        
        Args:
            appointment_id: Completed appointment to bill
            force_invoice: Force invoice creation even if subscription available
            
        Returns:
            Billing result with method used and details
        """
        try:
            async with get_db_connection() as conn:
                # Get appointment details
                appointment = await conn.fetchrow("""
                    SELECT 
                        a.*,
                        c.first_name || ' ' || c.last_name as client_name,
                        t.first_name || ' ' || t.last_name as tutor_name
                    FROM appointments a
                    JOIN clients c ON a.client_id = c.client_id
                    JOIN tutors t ON a.tutor_id = t.tutor_id
                    WHERE a.appointment_id = $1
                """, appointment_id)
                
                if not appointment:
                    raise ResourceNotFoundError(f"Appointment {appointment_id} not found")
                
                if appointment['status'] != AppointmentStatus.COMPLETED.value:
                    raise BusinessLogicError("Can only bill completed appointments")
                
                if appointment['invoice_id'] is not None:
                    raise BusinessLogicError("Appointment already invoiced")
                
                if appointment['subscription_usage_id'] is not None:
                    raise BusinessLogicError("Appointment already billed to subscription")
                
                client_id = appointment['client_id']
                duration_hours = Decimal(str(appointment['duration_hours']))
                appointment_date = appointment['appointment_date']
                
                # Check for active subscription unless forced to invoice
                subscription = None
                if not force_invoice:
                    subscription = await self.subscription_service.get_active_subscription_for_appointment(
                        client_id=client_id,
                        appointment_date=appointment_date,
                        required_hours=duration_hours
                    )
                
                if subscription:
                    # Use subscription hours
                    from app.models.subscription_models import SubscriptionUsageCreate
                    
                    usage_data = SubscriptionUsageCreate(
                        subscription_id=subscription.subscription_id,
                        appointment_id=appointment_id,
                        hours_to_deduct=duration_hours
                    )
                    
                    usage = await self.subscription_service.deduct_subscription_hours(usage_data)
                    
                    # Update appointment with subscription usage
                    await conn.execute("""
                        UPDATE appointments 
                        SET 
                            subscription_usage_id = $1,
                            billing_method = 'subscription',
                            updated_at = $2
                        WHERE appointment_id = $3
                    """, usage.usage_id, now_est(), appointment_id)
                    
                    # Log the subscription usage
                    if performed_by:
                        await self._log_billing_change(
                            conn=conn,
                            entity_type='subscription',
                            entity_id=subscription.subscription_id,
                            action='hours_deducted',
                            performed_by=performed_by,
                            change_reason=f"Used for appointment {appointment_id}",
                            field_name='hours_remaining',
                            old_value=str(subscription.hours_remaining),
                            new_value=str(usage.remaining_hours_after),
                            amount_before=None,
                            amount_after=None
                        )
                    
                    logger.info(
                        f"Billed appointment {appointment_id} to subscription {subscription.subscription_id}, "
                        f"deducted {duration_hours} hours"
                    )
                    
                    return {
                        'billing_method': 'subscription',
                        'subscription_id': subscription.subscription_id,
                        'usage_id': usage.usage_id,
                        'hours_deducted': duration_hours,
                        'hours_remaining': usage.remaining_hours_after,
                        'client_name': appointment['client_name'],
                        'tutor_name': appointment['tutor_name'],
                        'session_date': appointment_date
                    }
                else:
                    # Add to invoice queue or existing pending invoice
                    pending_invoice = await conn.fetchrow("""
                        SELECT invoice_id 
                        FROM invoices 
                        WHERE client_id = $1 
                            AND status = 'pending'
                            AND issue_date >= CURRENT_DATE - INTERVAL '30 days'
                        ORDER BY created_at DESC
                        LIMIT 1
                    """, client_id)
                    
                    if pending_invoice:
                        # Add to existing pending invoice
                        invoice_id = pending_invoice['invoice_id']
                        
                        # Add line item
                        client_rate = Decimal(str(appointment['client_rate']))
                        line_total = duration_hours * client_rate
                        
                        await conn.execute("""
                            INSERT INTO invoice_line_items (
                                invoice_id, appointment_id, description,
                                quantity, unit_price, line_total,
                                created_at, updated_at
                            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $7)
                        """,
                            invoice_id, appointment_id,
                            f"Tutoring session - {appointment['subject']} ({appointment['tutor_name']})",
                            duration_hours, client_rate, line_total, now_est()
                        )
                        
                        # Update invoice totals
                        await conn.execute("""
                            UPDATE invoices 
                            SET 
                                amount = amount + $1,
                                total_amount = amount + $1 + tax_amount,
                                updated_at = $2
                            WHERE invoice_id = $3
                        """, line_total, now_est(), invoice_id)
                        
                        # Mark appointment as invoiced
                        await conn.execute("""
                            UPDATE appointments 
                            SET 
                                invoice_id = $1,
                                billing_method = 'invoice',
                                updated_at = $2
                            WHERE appointment_id = $3
                        """, invoice_id, now_est(), appointment_id)
                        
                        # Log the invoice update
                        if performed_by:
                            # Get current invoice total
                            invoice_total = await conn.fetchval("""
                                SELECT total_amount FROM invoices WHERE invoice_id = $1
                            """, invoice_id)
                            
                            await self._log_billing_change(
                                conn=conn,
                                entity_type='invoice',
                                entity_id=invoice_id,
                                action='updated',
                                performed_by=performed_by,
                                change_reason=f"Added appointment {appointment_id}",
                                field_name='line_items',
                                old_value=None,
                                new_value=f"Added session for {line_total}",
                                amount_before=invoice_total - line_total,
                                amount_after=invoice_total
                            )
                        
                        logger.info(
                            f"Added appointment {appointment_id} to existing invoice {invoice_id}"
                        )
                        
                        return {
                            'billing_method': 'invoice',
                            'invoice_id': invoice_id,
                            'invoice_action': 'added_to_existing',
                            'amount': line_total,
                            'client_name': appointment['client_name'],
                            'tutor_name': appointment['tutor_name'],
                            'session_date': appointment_date
                        }
                    else:
                        # Mark for batch invoice generation
                        await conn.execute("""
                            UPDATE appointments 
                            SET 
                                billing_method = 'invoice',
                                billing_status = 'pending_invoice',
                                updated_at = $1
                            WHERE appointment_id = $2
                        """, now_est(), appointment_id)
                        
                        logger.info(
                            f"Marked appointment {appointment_id} for invoice generation"
                        )
                        
                        return {
                            'billing_method': 'invoice',
                            'invoice_action': 'marked_for_generation',
                            'client_name': appointment['client_name'],
                            'tutor_name': appointment['tutor_name'],
                            'session_date': appointment_date,
                            'message': 'Appointment marked for invoice generation'
                        }
        
        except Exception as e:
            logger.error(f"Error processing appointment billing: {str(e)}")
            raise BusinessLogicError(f"Failed to process billing: {str(e)}")
    
    async def rollback_appointment_billing(
        self,
        appointment_id: int,
        reason: str,
        rolled_back_by: int
    ) -> Dict[str, Any]:
        """
        Rollback billing for an appointment (subscription or invoice).
        
        Args:
            appointment_id: Appointment to rollback billing
            reason: Reason for rollback
            rolled_back_by: User performing rollback
            
        Returns:
            Rollback result
        """
        try:
            async with get_db_connection() as conn:
                # Get appointment billing details
                appointment = await conn.fetchrow("""
                    SELECT 
                        appointment_id,
                        client_id,
                        invoice_id,
                        subscription_usage_id,
                        billing_method,
                        status
                    FROM appointments
                    WHERE appointment_id = $1
                """, appointment_id)
                
                if not appointment:
                    raise ResourceNotFoundError(f"Appointment {appointment_id} not found")
                
                if appointment['billing_method'] == 'subscription' and appointment['subscription_usage_id']:
                    # Rollback subscription usage
                    usage = await self.subscription_service.rollback_usage(
                        usage_id=appointment['subscription_usage_id'],
                        reason=reason,
                        rolled_back_by=rolled_back_by
                    )
                    
                    # Clear billing references
                    await conn.execute("""
                        UPDATE appointments 
                        SET 
                            subscription_usage_id = NULL,
                            billing_method = NULL,
                            billing_status = NULL,
                            updated_at = $1
                        WHERE appointment_id = $2
                    """, now_est(), appointment_id)
                    
                    return {
                        'rollback_type': 'subscription',
                        'usage_id': usage.usage_id,
                        'hours_restored': usage.hours_deducted,
                        'reason': reason
                    }
                    
                elif appointment['billing_method'] == 'invoice' and appointment['invoice_id']:
                    # Check if invoice is paid
                    invoice = await conn.fetchrow("""
                        SELECT status FROM invoices WHERE invoice_id = $1
                    """, appointment['invoice_id'])
                    
                    if invoice and invoice['status'] == PaymentStatus.PAID.value:
                        raise BusinessLogicError("Cannot rollback billing for paid invoice")
                    
                    # Remove from invoice
                    line_item = await conn.fetchrow("""
                        SELECT line_total FROM invoice_line_items 
                        WHERE invoice_id = $1 AND appointment_id = $2
                    """, appointment['invoice_id'], appointment_id)
                    
                    if line_item:
                        # Delete line item
                        await conn.execute("""
                            DELETE FROM invoice_line_items 
                            WHERE invoice_id = $1 AND appointment_id = $2
                        """, appointment['invoice_id'], appointment_id)
                        
                        # Update invoice totals
                        line_total = Decimal(str(line_item['line_total']))
                        await conn.execute("""
                            UPDATE invoices 
                            SET 
                                amount = amount - $1,
                                total_amount = amount - $1 + tax_amount,
                                updated_at = $2
                            WHERE invoice_id = $3
                        """, line_total, now_est(), appointment['invoice_id'])
                    
                    # Clear billing references
                    await conn.execute("""
                        UPDATE appointments 
                        SET 
                            invoice_id = NULL,
                            billing_method = NULL,
                            billing_status = NULL,
                            updated_at = $1
                        WHERE appointment_id = $2
                    """, now_est(), appointment_id)
                    
                    return {
                        'rollback_type': 'invoice',
                        'invoice_id': appointment['invoice_id'],
                        'amount_removed': line_total if line_item else Decimal('0.00'),
                        'reason': reason
                    }
                else:
                    return {
                        'rollback_type': 'none',
                        'message': 'No billing to rollback for this appointment'
                    }
        
        except Exception as e:
            logger.error(f"Error rolling back appointment billing: {str(e)}")
            raise BusinessLogicError(f"Failed to rollback billing: {str(e)}")
    
    async def list_invoices(
        self,
        client_id: Optional[int] = None,
        status: Optional[PaymentStatus] = None,
        date_from: Optional[date] = None,
        date_to: Optional[date] = None,
        limit: int = 50,
        offset: int = 0
    ) -> Dict[str, Any]:
        """
        List invoices with filtering and pagination.
        
        Args:
            client_id: Filter by client ID
            status: Filter by payment status
            date_from: Filter from issue date
            date_to: Filter to issue date
            limit: Number of results (max 100)
            offset: Results offset for pagination
            
        Returns:
            Dictionary with invoices list and total count
        """
        try:
            async with get_db_connection() as conn:
                # Build WHERE clause based on filters
                where_conditions = []
                params = []
                param_count = 0
                
                if client_id is not None:
                    param_count += 1
                    where_conditions.append(f"i.client_id = ${param_count}")
                    params.append(client_id)
                
                if status is not None:
                    param_count += 1
                    where_conditions.append(f"i.status = ${param_count}")
                    params.append(status.value)
                
                if date_from is not None:
                    param_count += 1
                    where_conditions.append(f"i.issue_date >= ${param_count}")
                    params.append(date_from)
                
                if date_to is not None:
                    param_count += 1
                    where_conditions.append(f"i.issue_date <= ${param_count}")
                    params.append(date_to)
                
                where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
                
                # Get total count
                count_query = f"""
                    SELECT COUNT(*) as total
                    FROM invoices i
                    WHERE {where_clause}
                """
                total_count = await conn.fetchval(count_query, *params)
                
                # Get invoices with pagination
                param_count += 1
                params.append(limit)
                param_count += 1
                params.append(offset)
                
                invoices_query = f"""
                    SELECT 
                        i.*,
                        c.first_name || ' ' || c.last_name as client_name,
                        CASE 
                            WHEN i.status = 'pending' AND i.due_date < CURRENT_DATE 
                            THEN CURRENT_DATE - i.due_date 
                            ELSE NULL 
                        END as days_overdue,
                        COALESCE(
                            (SELECT COUNT(*) FROM invoice_line_items WHERE invoice_id = i.invoice_id),
                            0
                        ) as line_item_count
                    FROM invoices i
                    JOIN clients c ON i.client_id = c.client_id
                    WHERE {where_clause}
                    ORDER BY i.issue_date DESC, i.invoice_id DESC
                    LIMIT ${param_count - 1} OFFSET ${param_count}
                """
                
                invoices_data = await conn.fetch(invoices_query, *params)
                
                # Convert to response format
                invoices = []
                for invoice_row in invoices_data:
                    invoice_dict = dict(invoice_row)
                    
                    # Get line items for each invoice
                    line_items = await conn.fetch("""
                        SELECT * FROM invoice_line_items 
                        WHERE invoice_id = $1 
                        ORDER BY line_item_id
                    """, invoice_dict['invoice_id'])
                    
                    invoice_dict['line_items'] = [dict(item) for item in line_items]
                    
                    # Create InvoiceResponse object
                    invoice_response = InvoiceResponse(**invoice_dict)
                    invoices.append(invoice_response)
                
                return {
                    "invoices": invoices,
                    "total": total_count,
                    "limit": limit,
                    "offset": offset,
                    "has_more": (offset + limit) < total_count
                }
        
        except Exception as e:
            logger.error(f"Error listing invoices: {str(e)}")
            raise BusinessLogicError(f"Failed to list invoices: {str(e)}")
    
    async def list_tutor_payments(
        self,
        tutor_id: Optional[int] = None,
        status: Optional[TutorPaymentStatus] = None,
        period_start: Optional[date] = None,
        period_end: Optional[date] = None,
        limit: int = 50,
        offset: int = 0
    ) -> Dict[str, Any]:
        """
        List tutor payments with filtering and pagination.
        
        Args:
            tutor_id: Filter by tutor ID
            status: Filter by payment status
            period_start: Filter by payment period start
            period_end: Filter by payment period end
            limit: Number of results (max 100)
            offset: Results offset for pagination
            
        Returns:
            Dictionary with payments list and total count
        """
        try:
            async with get_db_connection() as conn:
                # Build WHERE clause based on filters
                where_conditions = []
                params = []
                param_count = 0
                
                if tutor_id is not None:
                    param_count += 1
                    where_conditions.append(f"tp.tutor_id = ${param_count}")
                    params.append(tutor_id)
                
                if status is not None:
                    param_count += 1
                    where_conditions.append(f"tp.status = ${param_count}")
                    params.append(status.value)
                
                if period_start is not None:
                    param_count += 1
                    where_conditions.append(f"tp.payment_period_start >= ${param_count}")
                    params.append(period_start)
                
                if period_end is not None:
                    param_count += 1
                    where_conditions.append(f"tp.payment_period_end <= ${param_count}")
                    params.append(period_end)
                
                where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
                
                # Get total count
                count_query = f"""
                    SELECT COUNT(*) as total
                    FROM tutor_payments tp
                    WHERE {where_clause}
                """
                total_count = await conn.fetchval(count_query, *params)
                
                # Get payments with pagination
                param_count += 1
                params.append(limit)
                param_count += 1
                params.append(offset)
                
                payments_query = f"""
                    SELECT 
                        tp.*,
                        t.first_name || ' ' || t.last_name as tutor_name,
                        tp.payment_period_start::text || ' to ' || tp.payment_period_end::text as period_description,
                        COALESCE(
                            (SELECT COUNT(*) FROM tutor_payment_items WHERE payment_id = tp.payment_id),
                            0
                        ) as session_count
                    FROM tutor_payments tp
                    JOIN tutors t ON tp.tutor_id = t.tutor_id
                    WHERE {where_clause}
                    ORDER BY tp.payment_period_end DESC, tp.payment_id DESC
                    LIMIT ${param_count - 1} OFFSET ${param_count}
                """
                
                payments_data = await conn.fetch(payments_query, *params)
                
                # Convert to response format
                payments = []
                for payment_row in payments_data:
                    payment_dict = dict(payment_row)
                    
                    # Get payment items for each payment
                    payment_items = await conn.fetch("""
                        SELECT * FROM tutor_payment_items 
                        WHERE payment_id = $1 
                        ORDER BY session_date
                    """, payment_dict['payment_id'])
                    
                    payment_dict['payment_items'] = [dict(item) for item in payment_items]
                    
                    # Create TutorPaymentResponse object
                    payment_response = TutorPaymentResponse(**payment_dict)
                    payments.append(payment_response)
                
                return {
                    "payments": payments,
                    "total": total_count,
                    "limit": limit,
                    "offset": offset,
                    "has_more": (offset + limit) < total_count
                }
        
        except Exception as e:
            logger.error(f"Error listing tutor payments: {str(e)}")
            raise BusinessLogicError(f"Failed to list tutor payments: {str(e)}")
    
    async def _log_billing_change(
        self,
        conn: asyncpg.Connection,
        entity_type: str,
        entity_id: int,
        action: str,
        performed_by: int,
        change_reason: Optional[str] = None,
        field_name: Optional[str] = None,
        old_value: Optional[Any] = None,
        new_value: Optional[Any] = None,
        amount_before: Optional[Decimal] = None,
        amount_after: Optional[Decimal] = None,
        stripe_transaction_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> None:
        """Log billing changes for audit trail."""
        # Get user role
        user_query = """
            SELECT role_type FROM user_roles 
            WHERE user_id = $1 AND is_active = TRUE
            ORDER BY created_at DESC LIMIT 1
        """
        user_role_result = await conn.fetchrow(user_query, performed_by)
        user_role = user_role_result['role_type'] if user_role_result else 'client'
        
        # Convert values to JSON strings if they're not None
        old_value_json = None if old_value is None else str(old_value)
        new_value_json = None if new_value is None else str(new_value)
        
        # Insert audit log entry
        audit_query = """
            INSERT INTO billing_audit_logs (
                entity_type, entity_id, action, performed_by, performed_by_role,
                field_name, old_value, new_value, change_reason,
                amount_before, amount_after, stripe_transaction_id,
                ip_address, user_agent, created_at
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15
            )
        """
        
        await conn.execute(
            audit_query,
            entity_type, entity_id, action, performed_by, user_role,
            field_name, old_value_json, new_value_json, change_reason,
            amount_before, amount_after, stripe_transaction_id,
            ip_address, user_agent, now_est()
        )
        
        logger.info(f"Billing audit: {entity_type} {entity_id} {action} by user {performed_by}")
    
    async def get_billing_audit_logs(
        self,
        entity_type: str,
        entity_id: int,
        limit: int = 50,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """
        Get audit logs for a specific billing entity.
        
        Args:
            entity_type: Type of entity (invoice, payment, subscription, tutor_payment)
            entity_id: ID of the entity
            limit: Maximum number of logs to return
            offset: Number of logs to skip
            
        Returns:
            List of audit log entries
        """
        async with get_db_connection() as conn:
            query = """
                SELECT 
                    al.audit_id,
                    al.entity_type,
                    al.entity_id,
                    al.action,
                    al.performed_by,
                    al.performed_by_role,
                    u.first_name || ' ' || u.last_name as performed_by_name,
                    al.field_name,
                    al.old_value,
                    al.new_value,
                    al.change_reason,
                    al.amount_before,
                    al.amount_after,
                    al.stripe_transaction_id,
                    al.ip_address,
                    al.user_agent,
                    al.created_at
                FROM billing_audit_logs al
                JOIN user_accounts u ON al.performed_by = u.user_id
                WHERE al.entity_type = $1 AND al.entity_id = $2
                ORDER BY al.created_at DESC
                LIMIT $3 OFFSET $4
            """
            
            rows = await conn.fetch(query, entity_type, entity_id, limit, offset)
            
            logs = []
            for row in rows:
                logs.append({
                    'audit_id': row['audit_id'],
                    'entity_type': row['entity_type'],
                    'entity_id': row['entity_id'],
                    'action': row['action'],
                    'performed_by': row['performed_by'],
                    'performed_by_role': row['performed_by_role'],
                    'performed_by_name': row['performed_by_name'],
                    'field_name': row['field_name'],
                    'old_value': row['old_value'],
                    'new_value': row['new_value'],
                    'change_reason': row['change_reason'],
                    'amount_before': float(row['amount_before']) if row['amount_before'] else None,
                    'amount_after': float(row['amount_after']) if row['amount_after'] else None,
                    'stripe_transaction_id': row['stripe_transaction_id'],
                    'ip_address': str(row['ip_address']) if row['ip_address'] else None,
                    'user_agent': row['user_agent'],
                    'created_at': row['created_at'].isoformat() if row['created_at'] else None
                })
            
            return logs