import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { I18nextProvider } from 'react-i18next';
import MessageTemplates from './MessageTemplates';
import i18n from '../../i18n';

const renderMessageTemplates = () => {
  return render(
    <I18nextProvider i18n={i18n}>
      <MessageTemplates />
    </I18nextProvider>
  );
};

describe('MessageTemplates', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders message templates interface', () => {
    renderMessageTemplates();
    
    expect(screen.getByText('Message Templates')).toBeInTheDocument();
    expect(screen.getByText('Create and manage reusable message templates')).toBeInTheDocument();
  });

  it('displays template categories', () => {
    renderMessageTemplates();
    
    expect(screen.getByText('Appointment Reminders')).toBeInTheDocument();
    expect(screen.getByText('Scheduling')).toBeInTheDocument();
    expect(screen.getByText('Billing')).toBeInTheDocument();
    expect(screen.getByText('General')).toBeInTheDocument();
  });

  it('shows create template button', () => {
    renderMessageTemplates();
    
    const createButton = screen.getByRole('button', { name: /create template/i });
    expect(createButton).toBeInTheDocument();
  });

  it('displays existing templates list', () => {
    renderMessageTemplates();
    
    // Check for sample templates
    expect(screen.getByText('24-Hour Reminder')).toBeInTheDocument();
    expect(screen.getByText('Session Confirmation')).toBeInTheDocument();
    expect(screen.getByText('Payment Due Notice')).toBeInTheDocument();
  });

  it('shows template preview content', () => {
    renderMessageTemplates();
    
    const template = screen.getByText('24-Hour Reminder').closest('.template-card');
    expect(template).toBeInTheDocument();
    
    // Should show template preview
    expect(screen.getByText(/Hi {firstName}, this is a reminder/)).toBeInTheDocument();
  });

  it('handles template editing', async () => {
    renderMessageTemplates();
    
    const editButton = screen.getAllByRole('button', { name: /edit/i })[0];
    fireEvent.click(editButton);
    
    await waitFor(() => {
      expect(screen.getByText('Edit Template')).toBeInTheDocument();
      expect(screen.getByDisplayValue('24-Hour Reminder')).toBeInTheDocument();
    });
  });

  it('opens template creation modal', async () => {
    renderMessageTemplates();
    
    const createButton = screen.getByRole('button', { name: /create template/i });
    fireEvent.click(createButton);
    
    await waitFor(() => {
      expect(screen.getByText('Create New Template')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Template name')).toBeInTheDocument();
    });
  });

  it('validates template form fields', async () => {
    const user = userEvent.setup();
    renderMessageTemplates();
    
    const createButton = screen.getByRole('button', { name: /create template/i });
    fireEvent.click(createButton);
    
    await waitFor(() => {
      const saveButton = screen.getByRole('button', { name: /save template/i });
      fireEvent.click(saveButton);
    });
    
    // Should show validation errors
    await waitFor(() => {
      expect(screen.getByText('Template name is required')).toBeInTheDocument();
    });
  });

  it('shows variable placeholders help', async () => {
    renderMessageTemplates();
    
    const createButton = screen.getByRole('button', { name: /create template/i });
    fireEvent.click(createButton);
    
    await waitFor(() => {
      expect(screen.getByText('Available Variables')).toBeInTheDocument();
      expect(screen.getByText('{firstName}')).toBeInTheDocument();
      expect(screen.getByText('{lastName}')).toBeInTheDocument();
      expect(screen.getByText('{appointmentDate}')).toBeInTheDocument();
    });
  });

  it('inserts variables into template content', async () => {
    const user = userEvent.setup();
    renderMessageTemplates();
    
    const createButton = screen.getByRole('button', { name: /create template/i });
    fireEvent.click(createButton);
    
    await waitFor(() => {
      const contentTextarea = screen.getByPlaceholderText('Message content...');
      const firstNameVar = screen.getByText('{firstName}');
      
      fireEvent.click(firstNameVar);
      
      expect(contentTextarea).toHaveValue('{firstName}');
    });
  });

  it('filters templates by category', async () => {
    renderMessageTemplates();
    
    const categoryFilter = screen.getByDisplayValue('All Categories');
    fireEvent.change(categoryFilter, { target: { value: 'appointment' } });
    
    await waitFor(() => {
      expect(screen.getByText('24-Hour Reminder')).toBeInTheDocument();
      expect(screen.getByText('Session Confirmation')).toBeInTheDocument();
      expect(screen.queryByText('Payment Due Notice')).not.toBeInTheDocument();
    });
  });

  it('searches templates by name', async () => {
    const user = userEvent.setup();
    renderMessageTemplates();
    
    const searchInput = screen.getByPlaceholderText('Search templates...');
    await user.type(searchInput, 'Reminder');
    
    await waitFor(() => {
      expect(screen.getByText('24-Hour Reminder')).toBeInTheDocument();
      expect(screen.queryByText('Payment Due Notice')).not.toBeInTheDocument();
    });
  });

  it('deletes template with confirmation', async () => {
    renderMessageTemplates();
    
    const deleteButton = screen.getAllByRole('button', { name: /delete/i })[0];
    fireEvent.click(deleteButton);
    
    await waitFor(() => {
      expect(screen.getByText('Confirm Deletion')).toBeInTheDocument();
      expect(screen.getByText(/Are you sure you want to delete/)).toBeInTheDocument();
    });
    
    const confirmButton = screen.getByRole('button', { name: /confirm/i });
    fireEvent.click(confirmButton);
    
    // Template should be removed
    await waitFor(() => {
      expect(screen.queryByText('Confirm Deletion')).not.toBeInTheDocument();
    });
  });

  it('duplicates existing template', async () => {
    renderMessageTemplates();
    
    const duplicateButton = screen.getAllByRole('button', { name: /duplicate/i })[0];
    fireEvent.click(duplicateButton);
    
    await waitFor(() => {
      expect(screen.getByText('Create New Template')).toBeInTheDocument();
      expect(screen.getByDisplayValue('24-Hour Reminder (Copy)')).toBeInTheDocument();
    });
  });

  it('shows template usage statistics', () => {
    renderMessageTemplates();
    
    // Should show how many times template was used
    expect(screen.getByText('Used 45 times')).toBeInTheDocument();
    expect(screen.getByText('Used 32 times')).toBeInTheDocument();
  });

  it('handles template preview', async () => {
    renderMessageTemplates();
    
    const previewButton = screen.getAllByRole('button', { name: /preview/i })[0];
    fireEvent.click(previewButton);
    
    await waitFor(() => {
      expect(screen.getByText('Template Preview')).toBeInTheDocument();
      expect(screen.getByText(/Hi John, this is a reminder/)).toBeInTheDocument();
    });
  });

  it('saves template successfully', async () => {
    const user = userEvent.setup();
    renderMessageTemplates();
    
    const createButton = screen.getByRole('button', { name: /create template/i });
    fireEvent.click(createButton);
    
    await waitFor(async () => {
      const nameInput = screen.getByPlaceholderText('Template name');
      const contentTextarea = screen.getByPlaceholderText('Message content...');
      
      await user.type(nameInput, 'New Template');
      await user.type(contentTextarea, 'Hello {firstName}, this is a test template.');
      
      const saveButton = screen.getByRole('button', { name: /save template/i });
      fireEvent.click(saveButton);
    });
    
    await waitFor(() => {
      expect(screen.getByText('Template saved successfully')).toBeInTheDocument();
    });
  });

  it('handles multi-language templates', () => {
    renderMessageTemplates();
    
    expect(screen.getByText('English')).toBeInTheDocument();
    expect(screen.getByText('French')).toBeInTheDocument();
    
    const frenchTab = screen.getByText('French');
    fireEvent.click(frenchTab);
    
    // Should show French template content
    expect(screen.getByText(/Bonjour {firstName}/)).toBeInTheDocument();
  });

  it('validates variable syntax', async () => {
    const user = userEvent.setup();
    renderMessageTemplates();
    
    const createButton = screen.getByRole('button', { name: /create template/i });
    fireEvent.click(createButton);
    
    await waitFor(async () => {
      const contentTextarea = screen.getByPlaceholderText('Message content...');
      await user.type(contentTextarea, 'Hello {invalidVariable}');
    });
    
    // Should show validation warning for unknown variables
    expect(screen.getByText(/Unknown variable: {invalidVariable}/)).toBeInTheDocument();
  });

  it('exports templates', async () => {
    renderMessageTemplates();
    
    const exportButton = screen.getByRole('button', { name: /export/i });
    fireEvent.click(exportButton);
    
    // Should trigger export functionality
    expect(exportButton).toBeInTheDocument();
  });

  it('imports templates', async () => {
    renderMessageTemplates();
    
    const importButton = screen.getByRole('button', { name: /import/i });
    fireEvent.click(importButton);
    
    await waitFor(() => {
      expect(screen.getByText('Import Templates')).toBeInTheDocument();
    });
  });

  it('shows empty state when no templates', async () => {
    renderMessageTemplates();
    
    // Search for non-existent template
    const user = userEvent.setup();
    const searchInput = screen.getByPlaceholderText('Search templates...');
    await user.type(searchInput, 'NonexistentTemplate');
    
    await waitFor(() => {
      expect(screen.getByText('No templates found')).toBeInTheDocument();
    });
  });

  it('handles template content formatting', async () => {
    const user = userEvent.setup();
    renderMessageTemplates();
    
    const createButton = screen.getByRole('button', { name: /create template/i });
    fireEvent.click(createButton);
    
    await waitFor(() => {
      const contentTextarea = screen.getByPlaceholderText('Message content...');
      expect(contentTextarea).toHaveAttribute('rows', '6');
    });
  });
});