"""
API endpoints for AI-powered scheduling suggestions.
"""

import logging
from datetime import datetime, timedelta
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel, Field

from app.core.dependencies import get_current_user, get_db_connection
from app.core.authorization import require_role
from app.models.auth_models import UserRoleType
from app.models.service_models import ServiceType, PackageType
from app.services.ai_scheduling_service import (
    AISchedulingService, SchedulingSuggestion, SuggestionPriority
)
from app.database.repositories.appointment_repository import AppointmentRepository
from app.database.repositories.tutor_repository import TutorRepository
from app.database.repositories.client_repository import ClientRepository
from app.core.validation import DataValidator

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/ai-scheduling", tags=["ai-scheduling"])


# Request Models
class SchedulingSuggestionRequest(BaseModel):
    """Request model for scheduling suggestions."""
    client_id: int = Field(..., description="Client ID requesting the appointment")
    subject_area: str = Field(..., min_length=1, max_length=100, description="Subject area")
    duration: int = Field(60, ge=15, le=240, description="Session duration in minutes")
    session_type: PackageType = Field(PackageType.INDIVIDUAL, description="Type of session")
    dependant_id: Optional[int] = Field(None, description="Optional dependant ID")
    preferred_start_date: Optional[datetime] = Field(None, description="Preferred start date for suggestions")
    preferred_end_date: Optional[datetime] = Field(None, description="Preferred end date for suggestions")
    max_suggestions: int = Field(5, ge=1, le=20, description="Maximum number of suggestions")


class RecurringScheduleRequest(BaseModel):
    """Request model for recurring schedule suggestions."""
    client_id: int = Field(..., description="Client ID")
    subject_area: str = Field(..., min_length=1, max_length=100, description="Subject area")
    sessions_per_week: int = Field(..., ge=1, le=5, description="Number of sessions per week")
    duration: int = Field(60, ge=15, le=240, description="Session duration in minutes")
    weeks_ahead: int = Field(8, ge=1, le=26, description="Number of weeks to schedule ahead")


class ReschedulingRequest(BaseModel):
    """Request model for rescheduling suggestions."""
    appointment_id: int = Field(..., description="ID of appointment to reschedule")
    reason: str = Field("conflict", max_length=200, description="Reason for rescheduling")


# Response Models
class SchedulingSuggestionResponse(BaseModel):
    """Response model for scheduling suggestions."""
    tutor_id: int
    tutor_name: str
    client_id: int
    dependant_id: Optional[int]
    suggested_date: datetime
    suggested_start_time: str
    suggested_end_time: str
    duration: int
    subject_area: str
    session_type: PackageType
    priority: SuggestionPriority
    confidence_score: float
    reasons: List[str]
    explanation: str
    alternative_slots: List[dict]
    estimated_cost: Optional[float] = None
    estimated_travel_time: Optional[int] = None
    
    class Config:
        from_attributes = True


class SchedulingSuggestionsResponse(BaseModel):
    """Response containing multiple scheduling suggestions."""
    suggestions: List[SchedulingSuggestionResponse]
    total_count: int
    generation_time_ms: int
    analysis_summary: dict


# Dependencies
async def get_ai_scheduling_service(db = Depends(get_db_connection)) -> AISchedulingService:
    """Dependency to get AI scheduling service."""
    appointment_repo = AppointmentRepository(db)
    tutor_repo = TutorRepository(db)
    client_repo = ClientRepository(db)
    
    return AISchedulingService(appointment_repo, tutor_repo, client_repo)


# Endpoints
@router.post("/suggestions", response_model=SchedulingSuggestionsResponse)
async def get_scheduling_suggestions(
    request: SchedulingSuggestionRequest,
    ai_service: AISchedulingService = Depends(get_ai_scheduling_service),
    current_user = Depends(get_current_user)
):
    """
    Get AI-powered scheduling suggestions for a client.
    
    Analyzes availability patterns, client preferences, tutor quality,
    and other factors to provide intelligent scheduling recommendations.
    """
    try:
        # Validate authorization
        require_role(current_user, [UserRoleType.MANAGER, UserRoleType.CLIENT])
        
        # If client role, ensure they can only request for themselves
        if UserRoleType.CLIENT in current_user.roles and current_user.user_id != request.client_id:
            raise HTTPException(
                status_code=403,
                detail="Clients can only request suggestions for themselves"
            )
        
        # Validate and sanitize input
        request.subject_area = DataValidator.validate_text_field(
            request.subject_area, "subject_area", max_length=100
        )
        
        # Set default date range if not provided
        if not request.preferred_start_date:
            request.preferred_start_date = datetime.now() + timedelta(hours=2)
        if not request.preferred_end_date:
            request.preferred_end_date = request.preferred_start_date + timedelta(days=14)
        
        # Generate suggestions
        start_time = datetime.now()
        
        suggestions = await ai_service.get_scheduling_suggestions(
            client_id=request.client_id,
            subject_area=request.subject_area,
            duration=request.duration,
            session_type=request.session_type,
            dependant_id=request.dependant_id,
            preferred_date_range=(request.preferred_start_date, request.preferred_end_date),
            max_suggestions=request.max_suggestions
        )
        
        generation_time = int((datetime.now() - start_time).total_seconds() * 1000)
        
        # Convert to response format
        suggestion_responses = []
        for suggestion in suggestions:
            # Get tutor name (would be fetched from database)
            tutor_name = f"Tutor {suggestion.tutor_id}"  # Mock data
            
            suggestion_responses.append(SchedulingSuggestionResponse(
                tutor_id=suggestion.tutor_id,
                tutor_name=tutor_name,
                client_id=suggestion.client_id,
                dependant_id=suggestion.dependant_id,
                suggested_date=suggestion.suggested_date,
                suggested_start_time=suggestion.suggested_start_time.strftime("%H:%M"),
                suggested_end_time=suggestion.suggested_end_time.strftime("%H:%M"),
                duration=suggestion.duration,
                subject_area=suggestion.subject_area,
                session_type=suggestion.session_type,
                priority=suggestion.priority,
                confidence_score=suggestion.confidence_score,
                reasons=[reason.value for reason in suggestion.reasons],
                explanation=suggestion.explanation,
                alternative_slots=suggestion.alternative_slots,
                estimated_travel_time=suggestion.estimated_travel_time
            ))
        
        # Create analysis summary
        analysis_summary = {
            "total_tutors_analyzed": len(set(s.tutor_id for s in suggestions)),
            "average_confidence": sum(s.confidence_score for s in suggestions) / len(suggestions) if suggestions else 0,
            "high_priority_count": len([s for s in suggestions if s.priority == SuggestionPriority.HIGH]),
            "date_range_days": (request.preferred_end_date - request.preferred_start_date).days,
            "subject_area": request.subject_area
        }
        
        return SchedulingSuggestionsResponse(
            suggestions=suggestion_responses,
            total_count=len(suggestion_responses),
            generation_time_ms=generation_time,
            analysis_summary=analysis_summary
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating scheduling suggestions: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to generate scheduling suggestions"
        )


@router.post("/recurring-schedule", response_model=SchedulingSuggestionsResponse)
async def get_recurring_schedule(
    request: RecurringScheduleRequest,
    ai_service: AISchedulingService = Depends(get_ai_scheduling_service),
    current_user = Depends(get_current_user)
):
    """
    Get AI-powered recurring schedule suggestions.
    
    Generates an optimal recurring schedule pattern based on client preferences,
    tutor availability, and historical booking patterns.
    """
    try:
        # Validate authorization
        require_role(current_user, [UserRoleType.MANAGER, UserRoleType.CLIENT])
        
        # If client role, ensure they can only request for themselves
        if UserRoleType.CLIENT in current_user.roles and current_user.user_id != request.client_id:
            raise HTTPException(
                status_code=403,
                detail="Clients can only request schedules for themselves"
            )
        
        # Validate input
        request.subject_area = DataValidator.validate_text_field(
            request.subject_area, "subject_area", max_length=100
        )
        
        # Generate recurring schedule
        start_time = datetime.now()
        
        suggestions = await ai_service.get_optimal_recurring_schedule(
            client_id=request.client_id,
            subject_area=request.subject_area,
            sessions_per_week=request.sessions_per_week,
            duration=request.duration,
            weeks_ahead=request.weeks_ahead
        )
        
        generation_time = int((datetime.now() - start_time).total_seconds() * 1000)
        
        # Convert to response format
        suggestion_responses = []
        for suggestion in suggestions:
            tutor_name = f"Tutor {suggestion.tutor_id}"  # Mock data
            
            suggestion_responses.append(SchedulingSuggestionResponse(
                tutor_id=suggestion.tutor_id,
                tutor_name=tutor_name,
                client_id=suggestion.client_id,
                dependant_id=suggestion.dependant_id,
                suggested_date=suggestion.suggested_date,
                suggested_start_time=suggestion.suggested_start_time.strftime("%H:%M"),
                suggested_end_time=suggestion.suggested_end_time.strftime("%H:%M"),
                duration=suggestion.duration,
                subject_area=suggestion.subject_area,
                session_type=suggestion.session_type,
                priority=suggestion.priority,
                confidence_score=suggestion.confidence_score,
                reasons=[reason.value for reason in suggestion.reasons],
                explanation=suggestion.explanation,
                alternative_slots=suggestion.alternative_slots
            ))
        
        analysis_summary = {
            "total_sessions": len(suggestion_responses),
            "sessions_per_week": request.sessions_per_week,
            "weeks_covered": request.weeks_ahead,
            "consistent_tutor": len(set(s.tutor_id for s in suggestions)) == 1,
            "average_confidence": sum(s.confidence_score for s in suggestions) / len(suggestions) if suggestions else 0
        }
        
        return SchedulingSuggestionsResponse(
            suggestions=suggestion_responses,
            total_count=len(suggestion_responses),
            generation_time_ms=generation_time,
            analysis_summary=analysis_summary
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating recurring schedule: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to generate recurring schedule"
        )


@router.post("/reschedule-suggestions", response_model=SchedulingSuggestionsResponse)
async def get_rescheduling_suggestions(
    request: ReschedulingRequest,
    ai_service: AISchedulingService = Depends(get_ai_scheduling_service),
    current_user = Depends(get_current_user)
):
    """
    Get AI-powered rescheduling suggestions for an existing appointment.
    
    Provides alternative time slots with preference for similar times and days.
    """
    try:
        # Validate authorization
        require_role(current_user, [UserRoleType.MANAGER, UserRoleType.TUTOR, UserRoleType.CLIENT])
        
        # Generate rescheduling suggestions
        start_time = datetime.now()
        
        suggestions = await ai_service.suggest_appointment_rescheduling(
            appointment_id=request.appointment_id,
            reason=request.reason
        )
        
        generation_time = int((datetime.now() - start_time).total_seconds() * 1000)
        
        # Convert to response format
        suggestion_responses = []
        for suggestion in suggestions:
            tutor_name = f"Tutor {suggestion.tutor_id}"  # Mock data
            
            suggestion_responses.append(SchedulingSuggestionResponse(
                tutor_id=suggestion.tutor_id,
                tutor_name=tutor_name,
                client_id=suggestion.client_id,
                dependant_id=suggestion.dependant_id,
                suggested_date=suggestion.suggested_date,
                suggested_start_time=suggestion.suggested_start_time.strftime("%H:%M"),
                suggested_end_time=suggestion.suggested_end_time.strftime("%H:%M"),
                duration=suggestion.duration,
                subject_area=suggestion.subject_area,
                session_type=suggestion.session_type,
                priority=suggestion.priority,
                confidence_score=suggestion.confidence_score,
                reasons=[reason.value for reason in suggestion.reasons],
                explanation=suggestion.explanation,
                alternative_slots=suggestion.alternative_slots
            ))
        
        analysis_summary = {
            "original_appointment_id": request.appointment_id,
            "reschedule_reason": request.reason,
            "alternative_count": len(suggestion_responses),
            "best_confidence": max((s.confidence_score for s in suggestions), default=0)
        }
        
        return SchedulingSuggestionsResponse(
            suggestions=suggestion_responses,
            total_count=len(suggestion_responses),
            generation_time_ms=generation_time,
            analysis_summary=analysis_summary
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating rescheduling suggestions: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to generate rescheduling suggestions"
        )


@router.get("/tutor-workload-analysis/{tutor_id}")
async def get_tutor_workload_analysis(
    tutor_id: int,
    days_ahead: int = Query(14, ge=1, le=90, description="Days to analyze ahead"),
    current_user = Depends(get_current_user),
    ai_service: AISchedulingService = Depends(get_ai_scheduling_service)
):
    """
    Get AI analysis of tutor workload and optimal scheduling windows.
    
    Provides insights into tutor availability patterns and recommendations
    for optimal appointment scheduling.
    """
    try:
        # Validate authorization
        require_role(current_user, [UserRoleType.MANAGER])
        
        # This would analyze tutor workload patterns
        # For now, return mock analysis
        
        analysis = {
            "tutor_id": tutor_id,
            "analysis_period_days": days_ahead,
            "current_load_percentage": 75.5,
            "optimal_booking_windows": [
                {
                    "day_of_week": "Monday",
                    "time_range": "16:00-20:00",
                    "recommendation_score": 0.9,
                    "reason": "Highest success rate and client satisfaction"
                },
                {
                    "day_of_week": "Wednesday", 
                    "time_range": "17:00-19:00",
                    "recommendation_score": 0.85,
                    "reason": "Low conflict rate, good energy levels"
                }
            ],
            "workload_distribution": {
                "monday": 80,
                "tuesday": 70,
                "wednesday": 60,
                "thursday": 85,
                "friday": 75,
                "saturday": 40,
                "sunday": 20
            },
            "recommendations": [
                "Consider reducing Thursday bookings to prevent burnout",
                "Weekend availability could be expanded for better work-life balance",
                "Monday-Wednesday pattern shows optimal performance"
            ]
        }
        
        return analysis
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error analyzing tutor workload: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to analyze tutor workload"
        )


@router.get("/scheduling-insights")
async def get_scheduling_insights(
    client_id: Optional[int] = Query(None, description="Specific client ID for insights"),
    days_back: int = Query(90, ge=30, le=365, description="Days of history to analyze"),
    current_user = Depends(get_current_user)
):
    """
    Get AI-generated insights about scheduling patterns and optimization opportunities.
    
    Provides data-driven recommendations for improving scheduling efficiency.
    """
    try:
        # Validate authorization
        require_role(current_user, [UserRoleType.MANAGER])
        
        # This would analyze historical scheduling data
        # For now, return mock insights
        
        insights = {
            "analysis_period": f"Last {days_back} days",
            "total_appointments_analyzed": 234,
            "key_insights": [
                {
                    "category": "Peak Demand",
                    "insight": "Highest demand on Wednesdays 17:00-19:00",
                    "impact": "Consider adding more tutors during this window",
                    "confidence": 0.92
                },
                {
                    "category": "Cancellation Patterns", 
                    "insight": "20% higher cancellation rate for Friday evening sessions",
                    "impact": "Implement reminder system for Friday bookings",
                    "confidence": 0.87
                },
                {
                    "category": "Tutor Efficiency",
                    "insight": "Travel time between appointments could be reduced by 15%",
                    "impact": "Geographic clustering of appointments",
                    "confidence": 0.78
                }
            ],
            "optimization_opportunities": [
                {
                    "area": "Schedule Density",
                    "current_efficiency": 72,
                    "potential_improvement": 85,
                    "suggested_actions": [
                        "Group appointments by geographic area",
                        "Minimize gaps between sessions",
                        "Optimize tutor-client matching"
                    ]
                }
            ],
            "client_satisfaction_correlation": {
                "preferred_time_slots": ["16:00-18:00", "19:00-20:00"],
                "optimal_session_length": 75,
                "best_performing_tutors": [1, 3, 5]
            }
        }
        
        return insights
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating scheduling insights: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to generate scheduling insights"
        )