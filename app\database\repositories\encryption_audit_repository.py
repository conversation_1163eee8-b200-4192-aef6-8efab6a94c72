"""
Repository for encryption audit logging and compliance tracking.
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

from app.database.repositories.base import BaseRepository

logger = logging.getLogger(__name__)


class EncryptionAuditRepository(BaseRepository):
    """Repository for encryption audit operations."""
    
    async def log_encryption_event(
        self,
        event_type: str,
        resource_type: str,
        resource_id: str,
        user_id: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Log an encryption-related event for audit."""
        try:
            query = """
                INSERT INTO encryption_audit_log (
                    event_type, resource_type, resource_id,
                    user_id, metadata, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s, %s
                ) RETURNING audit_id, created_at
            """
            
            params = (
                event_type,
                resource_type,
                resource_id,
                user_id,
                metadata if metadata else {},
                datetime.now()
            )
            
            result = await self._fetchone(query, params)
            return dict(result)
            
        except Exception as e:
            logger.error(f"Error logging encryption event: {e}")
            raise
    
    async def get_encryption_events(
        self,
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        user_id: Optional[int] = None,
        event_type: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Get encryption audit events with filters."""
        try:
            query = """
                SELECT 
                    audit_id,
                    event_type,
                    resource_type,
                    resource_id,
                    user_id,
                    metadata,
                    created_at
                FROM encryption_audit_log
                WHERE 1=1
            """
            params = []
            
            if resource_type:
                query += " AND resource_type = %s"
                params.append(resource_type)
            
            if resource_id:
                query += " AND resource_id = %s"
                params.append(resource_id)
            
            if user_id:
                query += " AND user_id = %s"
                params.append(user_id)
            
            if event_type:
                query += " AND event_type = %s"
                params.append(event_type)
            
            if start_date:
                query += " AND created_at >= %s"
                params.append(start_date)
            
            if end_date:
                query += " AND created_at <= %s"
                params.append(end_date)
            
            query += " ORDER BY created_at DESC LIMIT %s"
            params.append(limit)
            
            results = await self._fetchall(query, params)
            return [dict(row) for row in results]
            
        except Exception as e:
            logger.error(f"Error getting encryption events: {e}")
            raise
    
    async def get_encryption_access_report(
        self,
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, Any]:
        """Generate encryption access report for compliance."""
        try:
            # Get access statistics
            access_stats_query = """
                SELECT 
                    event_type,
                    COUNT(*) as event_count,
                    COUNT(DISTINCT user_id) as unique_users,
                    COUNT(DISTINCT resource_id) as unique_resources
                FROM encryption_audit_log
                WHERE created_at BETWEEN %s AND %s
                GROUP BY event_type
            """
            
            access_stats = await self._fetchall(
                access_stats_query,
                (start_date, end_date)
            )
            
            # Get user access patterns
            user_access_query = """
                SELECT 
                    u.user_id,
                    u.email,
                    COUNT(*) as access_count,
                    array_agg(DISTINCT eal.event_type) as event_types
                FROM encryption_audit_log eal
                JOIN users u ON u.user_id = eal.user_id
                WHERE eal.created_at BETWEEN %s AND %s
                GROUP BY u.user_id, u.email
                ORDER BY access_count DESC
                LIMIT 20
            """
            
            user_access = await self._fetchall(
                user_access_query,
                (start_date, end_date)
            )
            
            # Get resource access patterns
            resource_access_query = """
                SELECT 
                    resource_type,
                    COUNT(*) as access_count,
                    COUNT(DISTINCT resource_id) as unique_resources,
                    COUNT(DISTINCT user_id) as unique_users
                FROM encryption_audit_log
                WHERE created_at BETWEEN %s AND %s
                GROUP BY resource_type
                ORDER BY access_count DESC
            """
            
            resource_access = await self._fetchall(
                resource_access_query,
                (start_date, end_date)
            )
            
            # Check for suspicious patterns
            suspicious_query = """
                SELECT 
                    user_id,
                    COUNT(*) as decrypt_count,
                    array_agg(DISTINCT resource_type) as resource_types
                FROM encryption_audit_log
                WHERE created_at BETWEEN %s AND %s
                AND event_type LIKE '%%decrypt%%'
                GROUP BY user_id
                HAVING COUNT(*) > 50
            """
            
            suspicious_access = await self._fetchall(
                suspicious_query,
                (start_date, end_date)
            )
            
            return {
                'report_period': {
                    'start': start_date.isoformat(),
                    'end': end_date.isoformat()
                },
                'access_statistics': [dict(row) for row in access_stats],
                'top_users': [dict(row) for row in user_access],
                'resource_access': [dict(row) for row in resource_access],
                'suspicious_patterns': [dict(row) for row in suspicious_access],
                'generated_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error generating encryption access report: {e}")
            raise
    
    async def get_key_rotation_history(
        self,
        days_back: int = 365
    ) -> List[Dict[str, Any]]:
        """Get key rotation history."""
        try:
            query = """
                SELECT 
                    audit_id,
                    metadata,
                    user_id,
                    created_at
                FROM encryption_audit_log
                WHERE event_type = 'keys_rotated'
                AND created_at >= %s
                ORDER BY created_at DESC
            """
            
            cutoff_date = datetime.now() - timedelta(days=days_back)
            results = await self._fetchall(query, (cutoff_date,))
            
            return [dict(row) for row in results]
            
        except Exception as e:
            logger.error(f"Error getting key rotation history: {e}")
            raise
    
    async def cleanup_old_audit_logs(
        self,
        retention_days: int = 730  # 2 years default
    ) -> int:
        """Clean up old audit logs while maintaining compliance."""
        try:
            # Archive before deletion in production
            cutoff_date = datetime.now() - timedelta(days=retention_days)
            
            query = """
                DELETE FROM encryption_audit_log
                WHERE created_at < %s
                AND event_type NOT IN (
                    'keys_rotated',
                    'compliance_report_generated',
                    'security_incident'
                )
            """
            
            result = await self._execute(query, (cutoff_date,))
            
            # Log cleanup event
            await self.log_encryption_event(
                event_type='audit_logs_cleaned',
                resource_type='audit_logs',
                resource_id='system',
                metadata={
                    'retention_days': retention_days,
                    'records_deleted': result
                }
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Error cleaning up audit logs: {e}")
            raise