"""
API endpoints for geocoding and mapping services.
"""

import logging
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel, Field

from app.core.dependencies import get_db_connection, get_current_user
from app.core.exceptions import ValidationError, ExternalServiceError
from app.services.geocoding_service import GeocodingService
from app.models.user_models import UserRole

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/geocoding", tags=["geocoding"])


# Request/Response Models
class GeocodeRequest(BaseModel):
    """Request to geocode a postal code."""
    postal_code: str = Field(..., min_length=3, max_length=10)
    country: str = Field(default="CA", pattern="^[A-Z]{2}$")


class GeocodeResponse(BaseModel):
    """Response from geocoding service."""
    postal_code: str
    latitude: float
    longitude: float
    city: Optional[str] = None
    province: Optional[str] = None
    area: Optional[str] = None
    source: Optional[str] = None


class BatchGeocodeRequest(BaseModel):
    """Request to geocode multiple postal codes."""
    postal_codes: List[str] = Field(..., min_items=1, max_items=100)
    country: str = Field(default="CA", pattern="^[A-Z]{2}$")


class BatchGeocodeResponse(BaseModel):
    """Response from batch geocoding."""
    results: Dict[str, Optional[GeocodeResponse]]
    success_count: int
    error_count: int


class DistanceCalculationRequest(BaseModel):
    """Request to calculate distance between postal codes."""
    postal_code1: str = Field(..., min_length=3, max_length=10)
    postal_code2: str = Field(..., min_length=3, max_length=10)


class DistanceCalculationResponse(BaseModel):
    """Response from distance calculation."""
    postal_code1: str
    postal_code2: str
    distance_km: float
    estimated_travel_time_minutes: int
    travel_mode: str = "driving"


class NearestLocationRequest(BaseModel):
    """Request to find nearest locations."""
    center_postal_code: str = Field(..., min_length=3, max_length=10)
    candidate_locations: List[Dict[str, Any]] = Field(..., min_items=1)
    max_distance_km: Optional[float] = Field(None, ge=0.1, le=100)
    limit: int = Field(default=5, ge=1, le=50)


class NearestLocationResponse(BaseModel):
    """Response from nearest location search."""
    center_coordinates: Dict[str, float]
    nearest_locations: List[Dict[str, Any]]
    search_radius_km: Optional[float]


class PostalCodeRadiusRequest(BaseModel):
    """Request to find postal codes within radius."""
    center_postal_code: str = Field(..., min_length=3, max_length=10)
    radius_km: float = Field(..., ge=0.1, le=100)
    province: str = Field(default="QC", pattern="^[A-Z]{2}$")


class PostalCodeRadiusResponse(BaseModel):
    """Response from postal code radius search."""
    center_postal_code: str
    center_coordinates: Dict[str, float]
    radius_km: float
    postal_codes: List[Dict[str, Any]]
    total_found: int


class RouteInfoRequest(BaseModel):
    """Request for route information."""
    origin_postal_code: str = Field(..., min_length=3, max_length=10)
    destination_postal_code: str = Field(..., min_length=3, max_length=10)
    mode: str = Field(default="driving", pattern="^(driving|walking|transit)$")


class RouteInfoResponse(BaseModel):
    """Response from route information service."""
    origin_postal_code: str
    destination_postal_code: str
    distance_km: float
    duration_minutes: int
    mode: str
    estimated: bool
    route_summary: Optional[str] = None
    straight_line_distance_km: Optional[float] = None


# Helper function to get geocoding service
async def get_geocoding_service(db = Depends(get_db_connection)) -> GeocodingService:
    """Get geocoding service instance."""
    return GeocodingService(db_connection=db)


@router.post("/geocode", response_model=GeocodeResponse)
async def geocode_postal_code(
    request: GeocodeRequest,
    geocoding_service: GeocodingService = Depends(get_geocoding_service),
    current_user = Depends(get_current_user)
):
    """
    Geocode a single postal code to coordinates.
    
    Converts a postal code to latitude/longitude coordinates using:
    1. Local Quebec postal code database
    2. External geocoding APIs (if enabled)
    3. Cached results for performance
    """
    try:
        # Validate postal code format
        if not await geocoding_service.validate_postal_code_format(request.postal_code, request.country):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid postal code format for country {request.country}"
            )
        
        # Geocode the postal code
        coords = await geocoding_service.geocode_postal_code(request.postal_code)
        
        if not coords:
            raise HTTPException(
                status_code=404,
                detail=f"Postal code {request.postal_code} not found"
            )
        
        return GeocodeResponse(
            postal_code=request.postal_code.upper(),
            latitude=coords['lat'],
            longitude=coords['lng'],
            city=coords.get('city'),
            province=coords.get('province'),
            area=coords.get('area'),
            source=coords.get('source', 'local_database')
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error geocoding postal code {request.postal_code}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/geocode/batch", response_model=BatchGeocodeResponse)
async def batch_geocode_postal_codes(
    request: BatchGeocodeRequest,
    geocoding_service: GeocodingService = Depends(get_geocoding_service),
    current_user = Depends(get_current_user)
):
    """
    Geocode multiple postal codes efficiently.
    
    Processes up to 100 postal codes at once with intelligent batching:
    - Local Quebec codes processed quickly
    - External API calls minimized and cached
    - Partial results returned for mixed success/failure
    """
    try:
        # Validate all postal codes first
        for postal_code in request.postal_codes:
            if not await geocoding_service.validate_postal_code_format(postal_code, request.country):
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid postal code format: {postal_code}"
                )
        
        # Batch geocode
        results_dict = await geocoding_service.batch_geocode_postal_codes(request.postal_codes)
        
        # Convert to response format
        response_results = {}
        success_count = 0
        error_count = 0
        
        for postal_code, coords in results_dict.items():
            if coords:
                response_results[postal_code] = GeocodeResponse(
                    postal_code=postal_code.upper(),
                    latitude=coords['lat'],
                    longitude=coords['lng'],
                    city=coords.get('city'),
                    province=coords.get('province'),
                    area=coords.get('area'),
                    source=coords.get('source', 'local_database')
                )
                success_count += 1
            else:
                response_results[postal_code] = None
                error_count += 1
        
        return BatchGeocodeResponse(
            results=response_results,
            success_count=success_count,
            error_count=error_count
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in batch geocoding: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/distance", response_model=DistanceCalculationResponse)
async def calculate_distance(
    request: DistanceCalculationRequest,
    geocoding_service: GeocodingService = Depends(get_geocoding_service),
    current_user = Depends(get_current_user)
):
    """
    Calculate distance between two postal codes.
    
    Returns:
    - Straight-line distance using Haversine formula
    - Estimated travel time for different transportation modes
    - Cached results for performance
    """
    try:
        # Calculate distance
        distance = await geocoding_service.calculate_distance_between_postal_codes(
            request.postal_code1,
            request.postal_code2
        )
        
        if distance is None:
            raise HTTPException(
                status_code=400,
                detail="Could not calculate distance - one or both postal codes not found"
            )
        
        # Estimate travel time (default to driving)
        travel_time = geocoding_service.estimate_travel_time(distance, 'driving')
        
        return DistanceCalculationResponse(
            postal_code1=request.postal_code1.upper(),
            postal_code2=request.postal_code2.upper(),
            distance_km=distance,
            estimated_travel_time_minutes=travel_time,
            travel_mode="driving"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error calculating distance: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/nearest", response_model=NearestLocationResponse)
async def find_nearest_locations(
    request: NearestLocationRequest,
    geocoding_service: GeocodingService = Depends(get_geocoding_service),
    current_user = Depends(get_current_user)
):
    """
    Find nearest locations to a center postal code.
    
    Useful for:
    - Finding nearest tutors to a client
    - Locating nearby libraries or meeting points
    - Service area analysis
    """
    try:
        # Geocode center postal code
        center_coords = await geocoding_service.geocode_postal_code(request.center_postal_code)
        
        if not center_coords:
            raise HTTPException(
                status_code=404,
                detail=f"Center postal code {request.center_postal_code} not found"
            )
        
        # Find nearest locations
        nearest = await geocoding_service.find_nearest_locations(
            center_coords=center_coords,
            candidate_locations=request.candidate_locations,
            max_distance_km=request.max_distance_km,
            limit=request.limit
        )
        
        return NearestLocationResponse(
            center_coordinates={
                'lat': center_coords['lat'],
                'lng': center_coords['lng']
            },
            nearest_locations=nearest,
            search_radius_km=request.max_distance_km
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error finding nearest locations: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/radius", response_model=PostalCodeRadiusResponse)
async def get_postal_codes_in_radius(
    request: PostalCodeRadiusRequest,
    geocoding_service: GeocodingService = Depends(get_geocoding_service),
    current_user = Depends(get_current_user)
):
    """
    Get all postal codes within a radius of a center postal code.
    
    Useful for:
    - Defining tutor service areas
    - Market analysis by geographic region
    - Delivery/service zone planning
    """
    try:
        # Get center coordinates
        center_coords = await geocoding_service.geocode_postal_code(request.center_postal_code)
        
        if not center_coords:
            raise HTTPException(
                status_code=404,
                detail=f"Center postal code {request.center_postal_code} not found"
            )
        
        # Find postal codes in radius
        postal_codes = await geocoding_service.get_postal_codes_in_radius(
            center_postal_code=request.center_postal_code,
            radius_km=request.radius_km,
            province=request.province
        )
        
        return PostalCodeRadiusResponse(
            center_postal_code=request.center_postal_code.upper(),
            center_coordinates={
                'lat': center_coords['lat'],
                'lng': center_coords['lng']
            },
            radius_km=request.radius_km,
            postal_codes=postal_codes,
            total_found=len(postal_codes)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting postal codes in radius: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/route", response_model=RouteInfoResponse)
async def get_route_information(
    request: RouteInfoRequest,
    geocoding_service: GeocodingService = Depends(get_geocoding_service),
    current_user = Depends(get_current_user)
):
    """
    Get route information between two postal codes.
    
    Returns:
    - Road distance and travel time
    - Route summary (if external API available)
    - Traffic information (when available)
    - Falls back to estimated calculations
    """
    try:
        # Get route information
        route_info = await geocoding_service.get_route_distance_and_duration(
            origin_postal_code=request.origin_postal_code,
            destination_postal_code=request.destination_postal_code,
            mode=request.mode
        )
        
        if not route_info:
            raise HTTPException(
                status_code=400,
                detail="Could not calculate route - postal codes not found or invalid"
            )
        
        return RouteInfoResponse(
            origin_postal_code=request.origin_postal_code.upper(),
            destination_postal_code=request.destination_postal_code.upper(),
            distance_km=route_info['distance_km'],
            duration_minutes=route_info['duration_minutes'],
            mode=route_info['mode'],
            estimated=route_info.get('estimated', True),
            route_summary=route_info.get('route_summary'),
            straight_line_distance_km=route_info.get('straight_line_distance_km')
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting route information: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/validate/{postal_code}")
async def validate_postal_code(
    postal_code: str,
    country: str = Query(default="CA", pattern="^[A-Z]{2}$"),
    geocoding_service: GeocodingService = Depends(get_geocoding_service),
    current_user = Depends(get_current_user)
):
    """
    Validate postal code format without geocoding.
    
    Quick validation check for:
    - Form field validation
    - Input sanitization
    - Format verification
    """
    try:
        is_valid = await geocoding_service.validate_postal_code_format(postal_code, country)
        
        return {
            'postal_code': postal_code.upper(),
            'country': country,
            'is_valid': is_valid,
            'normalized': postal_code.upper().replace(' ', '') if is_valid else None
        }
        
    except Exception as e:
        logger.error(f"Error validating postal code: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/area/{postal_code}")
async def get_area_name(
    postal_code: str,
    geocoding_service: GeocodingService = Depends(get_geocoding_service),
    current_user = Depends(get_current_user)
):
    """
    Get area/neighborhood name for a postal code.
    
    Returns human-readable area names like:
    - "Downtown Montreal"
    - "Westmount"
    - "Laval"
    """
    try:
        area_name = geocoding_service.get_area_name(postal_code)
        
        if not area_name:
            raise HTTPException(
                status_code=404,
                detail=f"Area name not found for postal code {postal_code}"
            )
        
        return {
            'postal_code': postal_code.upper(),
            'area_name': area_name,
            'province': 'QC'
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting area name: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")