import React, { useState } from 'react';
import { TutorCard } from './TutorCard';
import { Star, MapPin, Clock, DollarSign } from 'lucide-react';

interface Tutor {
  tutor_id: number;
  first_name: string;
  last_name: string;
  specialties?: string[];
  average_rating?: number;
  total_sessions?: number;
  hourly_rate?: number;
  availability_status?: 'available' | 'limited' | 'busy';
  distance_km?: number;
  profile_picture?: string;
  bio?: string;
  experience_years?: number;
  education?: string;
  languages?: string[];
  service_types?: string[];
  next_available?: string;
  lat: number;
  lng: number;
}

interface TutorListViewProps {
  tutors: <PERSON><PERSON>[];
  selectedTutor: Tutor | null;
  onTutorSelect: (tutor: Tutor) => void;
  onBookSession: (tutorId: number) => void;
  onViewProfile: (tutorId: number) => void;
  loading?: boolean;
  className?: string;
}

type SortOption = 'distance' | 'rating' | 'price_low' | 'price_high' | 'experience';

const StarRating: React.FC<{ rating: number; size?: 'sm' | 'md' }> = ({ 
  rating, 
  size = 'sm' 
}) => {
  const starSize = size === 'sm' ? 'w-4 h-4' : 'w-5 h-5';
  
  return (
    <div className="flex items-center space-x-1">
      {[1, 2, 3, 4, 5].map((star) => (
        <div key={star} className="relative">
          {rating >= star ? (
            <Star className={`${starSize} text-yellow-400 fill-yellow-400`} />
          ) : rating >= star - 0.5 ? (
            <div className="relative">
              <Star className={`${starSize} text-gray-300`} />
              <div className="absolute inset-0 overflow-hidden" style={{ width: '50%' }}>
                <Star className={`${starSize} text-yellow-400 fill-yellow-400`} />
              </div>
            </div>
          ) : (
            <Star className={`${starSize} text-gray-300`} />
          )}
        </div>
      ))}
      <span className={`text-gray-600 ${size === 'sm' ? 'text-xs' : 'text-sm'} ml-1`}>
        ({rating.toFixed(1)})
      </span>
    </div>
  );
};

const TutorListItem: React.FC<{
  tutor: Tutor;
  isSelected: boolean;
  onSelect: () => void;
  onBookSession: () => void;
  onViewProfile: () => void;
}> = ({ tutor, isSelected, onSelect, onBookSession, onViewProfile }) => {
  const {
    first_name,
    last_name,
    specialties = [],
    average_rating = 0,
    total_sessions = 0,
    hourly_rate = 0,
    availability_status = 'busy',
    distance_km,
    profile_picture,
    experience_years
  } = tutor;

  const initials = `${first_name[0]}${last_name[0]}`.toUpperCase();

  return (
    <div 
      className={`p-4 border-b border-gray-200 cursor-pointer transition-colors ${
        isSelected ? 'bg-red-50 border-red-200' : 'hover:bg-gray-50'
      }`}
      onClick={onSelect}
    >
      <div className="flex items-start space-x-3">
        {/* Profile Picture */}
        <div className="flex-shrink-0">
          {profile_picture ? (
            <img
              src={profile_picture}
              alt={`${first_name} ${last_name}`}
              className="w-12 h-12 rounded-full object-cover border-2 border-gray-200"
            />
          ) : (
            <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center border-2 border-red-200">
              <span className="text-red-700 font-semibold text-sm">{initials}</span>
            </div>
          )}
        </div>

        {/* Tutor Info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold text-gray-900 truncate">
              {first_name} {last_name}
            </h3>
            <div className="flex items-center space-x-2">
              {/* Availability badge */}
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                availability_status === 'available' 
                  ? 'bg-green-100 text-green-700'
                  : availability_status === 'limited'
                  ? 'bg-yellow-100 text-yellow-700'
                  : 'bg-red-100 text-red-700'
              }`}>
                {availability_status === 'available' ? '🟢' : availability_status === 'limited' ? '🟡' : '🔴'}
                {availability_status}
              </span>
            </div>
          </div>

          {/* Rating and sessions */}
          <div className="flex items-center space-x-4 mt-1">
            {average_rating > 0 && (
              <StarRating rating={average_rating} />
            )}
            {total_sessions > 0 && (
              <span className="text-xs text-gray-500">
                {total_sessions} sessions
              </span>
            )}
            {experience_years && (
              <span className="text-xs text-gray-500">
                {experience_years} years exp.
              </span>
            )}
          </div>

          {/* Distance and rate */}
          <div className="flex items-center justify-between mt-2">
            <div className="flex items-center space-x-4">
              {distance_km && (
                <div className="flex items-center text-xs text-gray-500">
                  <MapPin className="w-3 h-3 mr-1" />
                  {distance_km.toFixed(1)} km
                </div>
              )}
              <div className="flex items-center text-sm font-medium text-gray-900">
                <DollarSign className="w-4 h-4 mr-1 text-gray-500" />
                ${hourly_rate}/hr
              </div>
            </div>
          </div>

          {/* Specialties */}
          {specialties.length > 0 && (
            <div className="mt-2">
              <div className="flex flex-wrap gap-1">
                {specialties.slice(0, 3).map((specialty) => (
                  <span
                    key={specialty}
                    className="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-700"
                  >
                    {specialty.charAt(0).toUpperCase() + specialty.slice(1)}
                  </span>
                ))}
                {specialties.length > 3 && (
                  <span className="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-500">
                    +{specialties.length - 3} more
                  </span>
                )}
              </div>
            </div>
          )}

          {/* Action buttons */}
          <div className="flex space-x-2 mt-3">
            <button
              onClick={(e) => {
                e.stopPropagation();
                onViewProfile();
              }}
              className="px-3 py-1 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-full hover:bg-gray-50 transition-colors shadow-subtle"
            >
              View Profile
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                onBookSession();
              }}
              disabled={availability_status === 'busy'}
              className={`px-3 py-1 text-xs font-medium rounded-full transition-colors shadow-soft ${
                availability_status === 'busy'
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-accent-red text-white hover:bg-accent-red-dark'
              }`}
            >
              {availability_status === 'busy' ? 'Unavailable' : 'Book Session'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export const TutorListView: React.FC<TutorListViewProps> = ({
  tutors,
  selectedTutor,
  onTutorSelect,
  onBookSession,
  onViewProfile,
  loading = false,
  className = ''
}) => {
  const [sortBy, setSortBy] = useState<SortOption>('distance');
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list');

  // Sort tutors based on selected option
  const sortedTutors = [...tutors].sort((a, b) => {
    switch (sortBy) {
      case 'distance':
        return (a.distance_km || 0) - (b.distance_km || 0);
      case 'rating':
        return (b.average_rating || 0) - (a.average_rating || 0);
      case 'price_low':
        return (a.hourly_rate || 0) - (b.hourly_rate || 0);
      case 'price_high':
        return (b.hourly_rate || 0) - (a.hourly_rate || 0);
      case 'experience':
        return (b.experience_years || 0) - (a.experience_years || 0);
      default:
        return 0;
    }
  });

  const sortOptions = [
    { value: 'distance', label: 'Distance' },
    { value: 'rating', label: 'Rating' },
    { value: 'price_low', label: 'Price: Low to High' },
    { value: 'price_high', label: 'Price: High to Low' },
    { value: 'experience', label: 'Experience' }
  ];

  if (loading) {
    return (
      <div className={`bg-white border border-gray-200 rounded-lg ${className}`}>
        <div className="p-4 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-accent-red mx-auto"></div>
          <p className="mt-2 text-sm text-gray-500">Loading tutors...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg overflow-hidden ${className}`}>
      {/* Header */}
      <div className="bg-gray-50 p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-medium text-gray-900">
              {tutors.length} Tutors Found
            </h3>
            <p className="text-sm text-gray-500">
              Select a tutor to view on map
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            {/* Sort dropdown */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as SortOption)}
              className="text-sm border border-gray-300 rounded-full px-3 py-1 bg-white focus:outline-none focus:ring-2 focus:ring-accent-red"
            >
              {sortOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  Sort by {option.label}
                </option>
              ))}
            </select>

            {/* View mode toggle */}
            <div className="flex border border-gray-300 rounded">
              <button
                onClick={() => setViewMode('list')}
                className={`px-2 py-1 text-xs ${
                  viewMode === 'list' 
                    ? 'bg-red-100 text-accent-red' 
                    : 'bg-white text-gray-600 hover:bg-gray-50'
                }`}
              >
                List
              </button>
              <button
                onClick={() => setViewMode('grid')}
                className={`px-2 py-1 text-xs border-l border-gray-300 ${
                  viewMode === 'grid' 
                    ? 'bg-red-100 text-accent-red' 
                    : 'bg-white text-gray-600 hover:bg-gray-50'
                }`}
              >
                Grid
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Tutor list */}
      <div className="max-h-96 overflow-y-auto">
        {sortedTutors.length === 0 ? (
          <div className="p-8 text-center">
            <div className="text-gray-400 mb-2">
              <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-1">No tutors found</h3>
            <p className="text-gray-500">Try adjusting your filters or search area</p>
          </div>
        ) : viewMode === 'list' ? (
          <div>
            {sortedTutors.map((tutor) => (
              <TutorListItem
                key={tutor.tutor_id}
                tutor={tutor}
                isSelected={selectedTutor?.tutor_id === tutor.tutor_id}
                onSelect={() => onTutorSelect(tutor)}
                onBookSession={() => onBookSession(tutor.tutor_id)}
                onViewProfile={() => onViewProfile(tutor.tutor_id)}
              />
            ))}
          </div>
        ) : (
          // Grid view
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 p-4">
            {sortedTutors.map((tutor) => (
              <div
                key={tutor.tutor_id}
                className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                  selectedTutor?.tutor_id === tutor.tutor_id
                    ? 'border-accent-red bg-red-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => onTutorSelect(tutor)}
              >
                <TutorCard
                  tutor={tutor}
                  onBookSession={() => onBookSession(tutor.tutor_id)}
                  onViewProfile={() => onViewProfile(tutor.tutor_id)}
                  showDistance={true}
                />
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
