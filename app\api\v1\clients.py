"""
Client profile API endpoints with authorization protection.
"""

from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status

from app.models.client_profile_models import (
    ClientProfile,
    ClientProfileCreate,
    ClientProfileUpdate,
    ClientProfileWithUser,
    ClientProfilePublic
)
from app.models.user_models import User
from app.services.client_service import ClientService
from app.services.rate_limiting_service import RateLimitingService
from app.core.dependencies import (
    get_database,
    get_current_active_user,
    check_rate_limit
)
from app.core.exceptions import (
    ResourceNotFoundError,
    ForbiddenError,
    ValidationError,
    DuplicateResourceError
)
from app.core.auth_decorators import (
    require_permission,
    require_role_or_ownership,
    audit_access,
    PermissionDependency
)
from app.core.permissions import ResourceType, PermissionAction
from app.models.user_models import UserRoleType
import asyncpg


router = APIRouter(prefix="/clients", tags=["clients"])


@router.post(
    "/",
    response_model=ClientProfile,
    status_code=status.HTTP_201_CREATED,
    summary="Create client profile",
    description="Create a new client profile for a user",
    dependencies=[
        Depends(PermissionDependency(ResourceType.CLIENT_PROFILE, PermissionAction.CREATE)),
        Depends(check_rate_limit)
    ]
)
@audit_access("created client profile")
async def create_client_profile(
    profile_data: ClientProfileCreate,
    current_user: User = Depends(get_current_active_user),
    db: asyncpg.Connection = Depends(get_database)
):
    """Create a new client profile with authorization."""
    service = ClientService()
    return await service.create_client_profile(profile_data, current_user, db)


@router.get(
    "/",
    response_model=List[ClientProfile],
    summary="List client profiles",
    description="Get list of client profiles accessible to current user",
    dependencies=[Depends(check_rate_limit)]
)
@audit_access("listed client profiles")
async def list_client_profiles(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search by name or email"),
    current_user: User = Depends(get_current_active_user),
    db: asyncpg.Connection = Depends(get_database)
):
    """List client profiles with authorization filtering."""
    service = ClientService()
    return await service.list_accessible_clients(
        current_user=current_user, 
        db=db, 
        skip=skip, 
        limit=limit, 
        search=search
    )


@router.get(
    "/{client_id}",
    response_model=ClientProfile,
    summary="Get client profile",
    description="Get a specific client profile by ID",
    dependencies=[
        Depends(PermissionDependency(ResourceType.CLIENT_PROFILE, PermissionAction.VIEW, "client_id")),
        Depends(check_rate_limit)
    ]
)
@audit_access("viewed client profile")
async def get_client_profile(
    client_id: int,
    current_user: User = Depends(get_current_active_user),
    db: asyncpg.Connection = Depends(get_database)
):
    """Get client profile by ID with authorization."""
    service = ClientService()
    return await service.get_client_profile_by_id(client_id, current_user, db)


@router.put(
    "/{client_id}",
    response_model=ClientProfile,
    summary="Update client profile",
    description="Update a client profile",
    dependencies=[
        Depends(PermissionDependency(ResourceType.CLIENT_PROFILE, PermissionAction.UPDATE, "client_id")),
        Depends(check_rate_limit)
    ]
)
@audit_access("updated client profile")
async def update_client_profile(
    client_id: int,
    profile_data: ClientProfileUpdate,
    current_user: User = Depends(get_current_active_user),
    db: asyncpg.Connection = Depends(get_database)
):
    """Update client profile with authorization."""
    service = ClientService()
    return await service.update_client_profile(client_id, profile_data, current_user, db)


@router.delete(
    "/{client_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete client profile",
    description="Delete a client profile (soft delete)",
    dependencies=[
        Depends(PermissionDependency(ResourceType.CLIENT_PROFILE, PermissionAction.DELETE, "client_id")),
        Depends(check_rate_limit)
    ]
)
@audit_access("deleted client profile")
async def delete_client_profile(
    client_id: int,
    current_user: User = Depends(get_current_active_user),
    db: asyncpg.Connection = Depends(get_database)
):
    """Delete client profile with authorization."""
    service = ClientService()
    await service.delete_client_profile(client_id, current_user, db)


@router.get(
    "/{client_id}/dependants",
    response_model=List[dict],  # Will be replaced with proper Dependant model
    summary="Get client dependants",
    description="Get all dependants for a client",
    dependencies=[
        Depends(PermissionDependency(ResourceType.CLIENT_PROFILE, PermissionAction.VIEW, "client_id")),
        Depends(check_rate_limit)
    ]
)
@audit_access("viewed client dependants")
async def get_client_dependants(
    client_id: int,
    current_user: User = Depends(get_current_active_user),
    db: asyncpg.Connection = Depends(get_database)
):
    """Get client dependants with authorization."""
    service = ClientService()
    return await service.get_client_dependants(client_id, current_user, db)


@router.get(
    "/user/{user_id}",
    response_model=Optional[ClientProfile],
    summary="Get client profile by user ID",
    description="Get client profile for a specific user",
    dependencies=[Depends(check_rate_limit)]
)
@audit_access("viewed client profile by user ID")
async def get_client_profile_by_user(
    user_id: int,
    current_user: User = Depends(get_current_active_user),
    db: asyncpg.Connection = Depends(get_database)
):
    """Get client profile by user ID with authorization."""
    service = ClientService()
    return await service.get_client_profile_by_user_id(user_id, current_user, db)