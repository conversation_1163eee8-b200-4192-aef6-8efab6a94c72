import React from 'react';
import { Outlet } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { TabNavigation, Tab } from '../../components/layout/TabNavigation';
import { Receipt, CreditCard, Package } from 'lucide-react';

const BillingLayout: React.FC = () => {
  const { t } = useTranslation();

  const tabs: Tab[] = [
    {
      id: 'invoices',
      label: t('sidebar.invoices'),
      path: '/billing/invoices',
      icon: <Receipt />,
    },
    {
      id: 'tutor-payments',
      label: t('sidebar.tutorPayments'),
      path: '/billing/tutor-payments',
      icon: <CreditCard />,
    },
    {
      id: 'subscriptions',
      label: t('sidebar.subscriptions'),
      path: '/billing/subscriptions',
      icon: <Package />,
    },
    {
      id: 'packages',
      label: t('sidebar.packages'),
      path: '/billing/packages',
      icon: <Package />,
    },
  ];

  return (
    <div className="h-full flex flex-col">
      <TabNavigation tabs={tabs} />
      <div className="flex-1 overflow-hidden">
        <Outlet />
      </div>
    </div>
  );
};

export default BillingLayout;