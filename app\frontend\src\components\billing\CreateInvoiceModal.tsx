import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { X, Plus, Trash2, Calculator } from 'lucide-react';
import { billingService, CreateInvoiceRequest } from '../../services/billingService';
import { Modal } from '../common/Modal';
import Button from '../common/Button';
import { Input } from '../common/Input';
import { Select } from '../common/Select';
import { Card } from '../common/Card';
import toast from 'react-hot-toast';
import api from '../../services/api';

interface CreateInvoiceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

interface LineItem {
  description: string;
  quantity: number;
  unit_price: number;
  appointment_id?: number;
}

interface Client {
  client_id: number;
  first_name: string;
  last_name: string;
  email: string;
}

export const CreateInvoiceModal: React.FC<CreateInvoiceModalProps> = ({
  isOpen,
  onClose,
  onSuccess
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [clients, setClients] = useState<Client[]>([]);
  const [selectedClient, setSelectedClient] = useState<number | null>(null);
  const [dueDate, setDueDate] = useState('');
  const [notes, setNotes] = useState('');
  const [lineItems, setLineItems] = useState<LineItem[]>([
    { description: '', quantity: 1, unit_price: 0 }
  ]);

  useEffect(() => {
    if (isOpen) {
      loadClients();
      // Set default due date to 30 days from now
      const defaultDueDate = new Date();
      defaultDueDate.setDate(defaultDueDate.getDate() + 30);
      setDueDate(defaultDueDate.toISOString().split('T')[0]);
    }
  }, [isOpen]);

  const loadClients = async () => {
    try {
      const response = await api.get<Client[]>('/clients');
      setClients(response.data);
    } catch (error) {
      console.error('Error loading clients:', error);
      toast.error('Failed to load clients');
    }
  };

  const addLineItem = () => {
    setLineItems([...lineItems, { description: '', quantity: 1, unit_price: 0 }]);
  };

  const removeLineItem = (index: number) => {
    setLineItems(lineItems.filter((_, i) => i !== index));
  };

  const updateLineItem = (index: number, field: keyof LineItem, value: any) => {
    const updated = [...lineItems];
    updated[index] = { ...updated[index], [field]: value };
    setLineItems(updated);
  };

  const calculateTotal = () => {
    return lineItems.reduce((sum, item) => sum + (item.quantity * item.unit_price), 0);
  };

  const handleSubmit = async () => {
    if (!selectedClient) {
      toast.error('Please select a client');
      return;
    }

    if (lineItems.length === 0 || lineItems.some(item => !item.description || item.unit_price <= 0)) {
      toast.error('Please add at least one valid line item');
      return;
    }

    try {
      setLoading(true);

      const request: CreateInvoiceRequest = {
        client_id: selectedClient,
        items: lineItems,
        due_date: dueDate,
        notes: notes || undefined
      };

      await billingService.createInvoice(request);
      toast.success('Invoice created successfully');
      onSuccess();
    } catch (error) {
      console.error('Error creating invoice:', error);
      toast.error('Failed to create invoice');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={t('billing.createInvoice')}
      size="xl"
    >
      <div className="space-y-6">
        {/* Client Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {t('billing.selectClient')}
          </label>
          <Select
            value={selectedClient?.toString() || ''}
            onChange={(e) => setSelectedClient(parseInt(e.target.value))}
            required
          >
            <option value="">{t('billing.selectClientPlaceholder')}</option>
            {clients.map((client) => (
              <option key={client.client_id} value={client.client_id}>
                {client.first_name} {client.last_name} - {client.email}
              </option>
            ))}
          </Select>
        </div>

        {/* Due Date */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {t('billing.dueDate')}
          </label>
          <Input
            type="date"
            value={dueDate}
            onChange={(e) => setDueDate(e.target.value)}
            required
          />
        </div>

        {/* Line Items */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <label className="text-sm font-medium text-gray-700">
              {t('billing.lineItems')}
            </label>
            <Button
              variant="secondary"
              size="sm"
              leftIcon={<Plus className="w-4 h-4" />}
              onClick={addLineItem}
            >
              {t('billing.addItem')}
            </Button>
          </div>

          <div className="space-y-3">
            {lineItems.map((item, index) => (
              <Card key={index} className="p-4">
                <div className="grid grid-cols-12 gap-3 items-end">
                  <div className="col-span-5">
                    <label className="block text-xs font-medium text-gray-600 mb-1">
                      {t('billing.description')}
                    </label>
                    <Input
                      value={item.description}
                      onChange={(e) => updateLineItem(index, 'description', e.target.value)}
                      placeholder="e.g., Math tutoring session"
                      required
                    />
                  </div>
                  <div className="col-span-2">
                    <label className="block text-xs font-medium text-gray-600 mb-1">
                      {t('billing.quantity')}
                    </label>
                    <Input
                      type="number"
                      min="1"
                      value={item.quantity}
                      onChange={(e) => updateLineItem(index, 'quantity', parseInt(e.target.value) || 1)}
                      required
                    />
                  </div>
                  <div className="col-span-3">
                    <label className="block text-xs font-medium text-gray-600 mb-1">
                      {t('billing.unitPrice')}
                    </label>
                    <Input
                      type="number"
                      min="0"
                      step="0.01"
                      value={item.unit_price}
                      onChange={(e) => updateLineItem(index, 'unit_price', parseFloat(e.target.value) || 0)}
                      required
                    />
                  </div>
                  <div className="col-span-1 text-right font-medium">
                    ${(item.quantity * item.unit_price).toFixed(2)}
                  </div>
                  <div className="col-span-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeLineItem(index)}
                      disabled={lineItems.length === 1}
                    >
                      <Trash2 className="w-4 h-4 text-red-500" />
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>

        {/* Total */}
        <Card className="p-4 bg-gray-50">
          <div className="flex items-center justify-between">
            <span className="text-lg font-medium text-gray-900">
              {t('billing.total')}
            </span>
            <span className="text-2xl font-bold text-gray-900">
              ${calculateTotal().toFixed(2)}
            </span>
          </div>
        </Card>

        {/* Notes */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {t('billing.notes')} ({t('common.optional')})
          </label>
          <textarea
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent-red focus:border-transparent resize-none"
            placeholder={t('billing.notesPlaceholder')}
          />
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-3">
          <Button
            variant="secondary"
            onClick={onClose}
            disabled={loading}
          >
            {t('common.cancel')}
          </Button>
          <Button
            variant="primary"
            onClick={handleSubmit}
            loading={loading}
            leftIcon={<Calculator className="w-4 h-4" />}
          >
            {t('billing.createInvoice')}
          </Button>
        </div>
      </div>
    </Modal>
  );
};