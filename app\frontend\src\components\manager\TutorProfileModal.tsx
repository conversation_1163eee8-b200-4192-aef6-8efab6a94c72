import React, { useState } from 'react';
import { Modal } from '../common/Modal';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '../common/TabsAdapter';
import { Badge } from '../common/Badge';
import Button from '../common/Button';
import { Card } from '../common/Card';
import { 
  User, GraduationCap, Calendar, MapPin, FileText, BarChart, 
  MessageSquare, DollarSign, Clock, CheckCircle, XCircle,
  Mail, Phone, Globe, Star, Briefcase, Shield, AlertCircle,
  Download, Eye, Edit2, X
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import toast from 'react-hot-toast';

interface TutorProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
  tutor: any; // Will be properly typed with Tutor interface
}

export const TutorProfileModal: React.FC<TutorProfileModalProps> = ({ isOpen, onClose, tutor }) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('overview');

  if (!tutor) return null;

  const getVerificationStatusColor = (status: string) => {
    switch (status) {
      case 'verified': return 'text-green-600 bg-green-50';
      case 'pending': return 'text-yellow-600 bg-yellow-50';
      case 'rejected': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD',
    }).format(amount);
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`${tutor.first_name} ${tutor.last_name} - Tutor Profile`}
      size="xl"
    >
      <div className="h-[700px] flex flex-col">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
          <TabsList className="grid grid-cols-7 w-full">
            <TabsTrigger value="overview">
              <User className="w-4 h-4 mr-2" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="availability">
              <Calendar className="w-4 h-4 mr-2" />
              Availability
            </TabsTrigger>
            <TabsTrigger value="areas">
              <MapPin className="w-4 h-4 mr-2" />
              Areas
            </TabsTrigger>
            <TabsTrigger value="documents">
              <FileText className="w-4 h-4 mr-2" />
              Documents
            </TabsTrigger>
            <TabsTrigger value="performance">
              <BarChart className="w-4 h-4 mr-2" />
              Performance
            </TabsTrigger>
            <TabsTrigger value="payments">
              <DollarSign className="w-4 h-4 mr-2" />
              Payments
            </TabsTrigger>
            <TabsTrigger value="messages">
              <MessageSquare className="w-4 h-4 mr-2" />
              Messages
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="mt-6 space-y-6 overflow-y-auto max-h-[580px]">
            {/* Profile Header */}
            <div className="flex items-start gap-6">
              {tutor.profile_picture ? (
                <img 
                  src={tutor.profile_picture} 
                  alt={`${tutor.first_name} ${tutor.last_name}`}
                  className="w-24 h-24 rounded-full object-cover border-4 border-gray-200"
                />
              ) : (
                <div className="w-24 h-24 bg-accent-red bg-opacity-10 rounded-full flex items-center justify-center">
                  <User className="w-12 h-12 text-accent-red" />
                </div>
              )}
              
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <h3 className="text-2xl font-semibold text-gray-900">
                    {tutor.first_name} {tutor.last_name}
                  </h3>
                  <Badge className={getVerificationStatusColor(tutor.verification_status)}>
                    {tutor.verification_status}
                  </Badge>
                </div>
                
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div className="flex items-center gap-2 text-gray-600">
                    <Mail className="w-4 h-4" />
                    {tutor.email}
                  </div>
                  <div className="flex items-center gap-2 text-gray-600">
                    <Phone className="w-4 h-4" />
                    {tutor.phone || 'Not provided'}
                  </div>
                  <div className="flex items-center gap-2 text-gray-600">
                    <MapPin className="w-4 h-4" />
                    {tutor.postal_code || 'Not provided'}
                  </div>
                  <div className="flex items-center gap-2 text-gray-600">
                    <Calendar className="w-4 h-4" />
                    Member since {new Date(tutor.created_at).toLocaleDateString()}
                  </div>
                </div>

                {/* Stats Row */}
                <div className="flex items-center gap-6 mt-4">
                  {tutor.average_rating && (
                    <div className="flex items-center gap-1">
                      <Star className="w-5 h-5 text-yellow-500 fill-yellow-500" />
                      <span className="font-semibold">{tutor.average_rating.toFixed(1)}</span>
                      <span className="text-gray-500">rating</span>
                    </div>
                  )}
                  <div className="text-gray-600">
                    <span className="font-semibold">{tutor.total_sessions || 0}</span> sessions
                  </div>
                  <div className="text-gray-600">
                    <span className="font-semibold">{tutor.years_of_experience || 0}</span> years exp.
                  </div>
                </div>
              </div>
            </div>

            {/* Bio Section */}
            {tutor.bio && (
              <Card className="p-4">
                <h4 className="font-medium text-gray-900 mb-2">Professional Bio</h4>
                <p className="text-gray-600">{tutor.bio}</p>
              </Card>
            )}

            {/* Education & Certifications */}
            <Card className="p-4">
              <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                <GraduationCap className="w-5 h-5 mr-2" />
                Education & Certifications
              </h4>
              
              {tutor.highest_degree_name && (
                <div className="mb-3">
                  <p className="font-medium">{tutor.highest_degree_name}</p>
                  <p className="text-sm text-gray-600">
                    {tutor.degree_major} • {tutor.university_name} • {tutor.graduation_year}
                  </p>
                </div>
              )}
              
              {tutor.teaching_certifications && tutor.teaching_certifications.length > 0 && (
                <div>
                  <p className="text-sm font-medium text-gray-700 mb-1">Certifications:</p>
                  <div className="flex flex-wrap gap-2">
                    {tutor.teaching_certifications.map((cert: string, index: number) => (
                      <Badge key={index} variant="secondary">{cert}</Badge>
                    ))}
                  </div>
                </div>
              )}
            </Card>

            {/* Experience & Skills */}
            <Card className="p-4">
              <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                <Briefcase className="w-5 h-5 mr-2" />
                Experience & Skills
              </h4>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-600">Current Occupation</p>
                  <p className="font-medium">{tutor.current_occupation || 'Not specified'}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Years Teaching Online</p>
                  <p className="font-medium">{tutor.years_online_teaching || 0} years</p>
                </div>
              </div>

              {tutor.subject_expertise && Object.keys(tutor.subject_expertise).length > 0 && (
                <div className="mt-4">
                  <p className="text-sm font-medium text-gray-700 mb-2">Subject Expertise:</p>
                  <div className="space-y-1">
                    {Object.entries(tutor.subject_expertise).map(([subject, level]) => (
                      <div key={subject} className="flex justify-between items-center">
                        <span className="text-sm capitalize">{subject}</span>
                        <Badge 
                          className={
                            level === 'expert' ? 'bg-green-100 text-green-700' :
                            level === 'advanced' ? 'bg-blue-100 text-blue-700' :
                            'bg-gray-100 text-gray-700'
                          }
                        >
                          {level}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {tutor.language_proficiency && Object.keys(tutor.language_proficiency).length > 0 && (
                <div className="mt-4">
                  <p className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                    <Globe className="w-4 h-4 mr-1" />
                    Languages:
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {Object.entries(tutor.language_proficiency).map(([lang, level]) => (
                      <Badge key={lang} variant="secondary">
                        {lang}: {level}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {tutor.special_needs_experience && (
                <div className="mt-4">
                  <Badge className="bg-purple-100 text-purple-700">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Special Needs Experience
                  </Badge>
                </div>
              )}
            </Card>

            {/* Verification Status */}
            <Card className="p-4">
              <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                <Shield className="w-5 h-5 mr-2" />
                Verification & Compliance
              </h4>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Background Check</span>
                  <Badge className={
                    tutor.background_check_status === 'passed' ? 'bg-green-100 text-green-700' :
                    tutor.background_check_status === 'pending' ? 'bg-yellow-100 text-yellow-700' :
                    'bg-red-100 text-red-700'
                  }>
                    {tutor.background_check_status || 'Not started'}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">References</span>
                  <Badge className={tutor.reference_check_completed ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'}>
                    {tutor.reference_check_completed ? 'Verified' : 'Pending'}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Working with Children</span>
                  <Badge className={tutor.working_with_children_clearance ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'}>
                    {tutor.working_with_children_clearance ? 'Cleared' : 'Required'}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Liability Insurance</span>
                  <Badge className={tutor.has_liability_insurance ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'}>
                    {tutor.has_liability_insurance ? 'Active' : 'Not provided'}
                  </Badge>
                </div>
              </div>
            </Card>

            {/* Rates Information */}
            <Card className="p-4">
              <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                <DollarSign className="w-5 h-5 mr-2" />
                Service Rates
              </h4>
              
              {tutor.rates ? (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">Base Rate</p>
                    <p className="font-semibold">{formatCurrency(tutor.rates.base_hourly_rate)}/hr</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Online</p>
                    <p className="font-semibold">{formatCurrency(tutor.rates.online_rate || tutor.rates.base_hourly_rate)}/hr</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">In-Person</p>
                    <p className="font-semibold">{formatCurrency(tutor.rates.in_person_rate || tutor.rates.base_hourly_rate)}/hr</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Library</p>
                    <p className="font-semibold">{formatCurrency(tutor.rates.library_rate || tutor.rates.base_hourly_rate)}/hr</p>
                  </div>
                </div>
              ) : (
                <p className="text-gray-500">No rates configured</p>
              )}
              
              {tutor.rates?.notes && (
                <p className="mt-3 text-sm text-gray-600 italic">{tutor.rates.notes}</p>
              )}
            </Card>
          </TabsContent>

          {/* Availability Tab */}
          <TabsContent value="availability" className="mt-6 space-y-6">
            <Card className="p-6">
              <h4 className="font-medium text-gray-900 mb-4 flex items-center">
                <Calendar className="w-5 h-5 mr-2" />
                Weekly Availability Schedule
              </h4>
              
              {/* Mock availability data - replace with actual data */}
              <div className="space-y-3">
                {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map((day, index) => (
                  <div key={day} className="flex items-center justify-between py-2 border-b">
                    <span className="font-medium w-24">{day}</span>
                    <div className="flex-1 flex gap-2">
                      {index < 5 ? (
                        <>
                          <Badge variant="secondary">9:00 AM - 12:00 PM</Badge>
                          <Badge variant="secondary">2:00 PM - 6:00 PM</Badge>
                        </>
                      ) : (
                        <span className="text-gray-500">Not available</span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            <Card className="p-6">
              <h4 className="font-medium text-gray-900 mb-4 flex items-center">
                <Clock className="w-5 h-5 mr-2" />
                Time Off Requests
              </h4>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                  <div>
                    <p className="font-medium">Christmas Break</p>
                    <p className="text-sm text-gray-600">Dec 24, 2024 - Dec 26, 2024</p>
                  </div>
                  <Badge className="bg-yellow-100 text-yellow-700">Pending</Badge>
                </div>
                
                <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                  <div>
                    <p className="font-medium">Personal Day</p>
                    <p className="text-sm text-gray-600">Nov 15, 2024</p>
                  </div>
                  <Badge className="bg-green-100 text-green-700">Approved</Badge>
                </div>
              </div>
            </Card>
          </TabsContent>

          {/* Service Areas Tab */}
          <TabsContent value="areas" className="mt-6 space-y-6">
            <Card className="p-6">
              <h4 className="font-medium text-gray-900 mb-4 flex items-center">
                <MapPin className="w-5 h-5 mr-2" />
                Service Areas
              </h4>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <MapPin className="w-5 h-5 text-accent-red" />
                    <div>
                      <p className="font-medium">H3H 2R9</p>
                      <p className="text-sm text-gray-600">Downtown Montreal</p>
                    </div>
                  </div>
                  <Badge variant="secondary">15 km radius</Badge>
                </div>
                
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <MapPin className="w-5 h-5 text-accent-red" />
                    <div>
                      <p className="font-medium">H4A</p>
                      <p className="text-sm text-gray-600">Notre-Dame-de-Grâce</p>
                    </div>
                  </div>
                  <Badge variant="secondary">10 km radius</Badge>
                </div>
              </div>
              
              <div className="mt-6 h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                <p className="text-gray-500">Map visualization coming soon</p>
              </div>
            </Card>
          </TabsContent>

          {/* Documents Tab */}
          <TabsContent value="documents" className="mt-6 space-y-6">
            <Card className="p-6">
              <h4 className="font-medium text-gray-900 mb-4 flex items-center">
                <FileText className="w-5 h-5 mr-2" />
                Uploaded Documents
              </h4>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <FileText className="w-5 h-5 text-gray-500" />
                    <div>
                      <p className="font-medium">Teaching Certificate</p>
                      <p className="text-sm text-gray-600">Uploaded on Nov 1, 2024</p>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button size="sm" variant="ghost">
                      <Eye className="w-4 h-4" />
                    </Button>
                    <Button size="sm" variant="ghost">
                      <Download className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <FileText className="w-5 h-5 text-gray-500" />
                    <div>
                      <p className="font-medium">Background Check</p>
                      <p className="text-sm text-gray-600">Uploaded on Oct 15, 2024</p>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button size="sm" variant="ghost">
                      <Eye className="w-4 h-4" />
                    </Button>
                    <Button size="sm" variant="ghost">
                      <Download className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <FileText className="w-5 h-5 text-gray-500" />
                    <div>
                      <p className="font-medium">Insurance Certificate</p>
                      <p className="text-sm text-gray-600">Uploaded on Sep 20, 2024</p>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button size="sm" variant="ghost">
                      <Eye className="w-4 h-4" />
                    </Button>
                    <Button size="sm" variant="ghost">
                      <Download className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <h4 className="font-medium text-gray-900 mb-4">References</h4>
              
              <div className="space-y-3">
                <div className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <p className="font-medium">Dr. Jane Smith</p>
                    <Badge className="bg-green-100 text-green-700">Verified</Badge>
                  </div>
                  <p className="text-sm text-gray-600">Principal, Westmount High School</p>
                  <p className="text-sm text-gray-600"><EMAIL> • (514) 555-0100</p>
                </div>
                
                <div className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <p className="font-medium">Prof. Robert Johnson</p>
                    <Badge className="bg-yellow-100 text-yellow-700">Pending</Badge>
                  </div>
                  <p className="text-sm text-gray-600">McGill University, Mathematics Department</p>
                  <p className="text-sm text-gray-600"><EMAIL> • (514) 555-0200</p>
                </div>
              </div>
            </Card>
          </TabsContent>

          {/* Performance Tab */}
          <TabsContent value="performance" className="mt-6 space-y-6">
            <div className="grid grid-cols-4 gap-4">
              <Card className="p-4">
                <p className="text-sm text-gray-600">Total Sessions</p>
                <p className="text-2xl font-semibold">{tutor.total_sessions || 0}</p>
              </Card>
              <Card className="p-4">
                <p className="text-sm text-gray-600">Completion Rate</p>
                <p className="text-2xl font-semibold">98%</p>
              </Card>
              <Card className="p-4">
                <p className="text-sm text-gray-600">Average Rating</p>
                <p className="text-2xl font-semibold">{tutor.average_rating?.toFixed(1) || 'N/A'}</p>
              </Card>
              <Card className="p-4">
                <p className="text-sm text-gray-600">Response Time</p>
                <p className="text-2xl font-semibold">2h</p>
              </Card>
            </div>

            <Card className="p-6">
              <h4 className="font-medium text-gray-900 mb-4">Recent Reviews</h4>
              
              <div className="space-y-4">
                <div className="border-b pb-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <div className="flex">
                        {[...Array(5)].map((_, i) => (
                          <Star key={i} className={`w-4 h-4 ${i < 5 ? 'text-yellow-500 fill-yellow-500' : 'text-gray-300'}`} />
                        ))}
                      </div>
                      <span className="text-sm text-gray-600">by Marie D.</span>
                    </div>
                    <span className="text-sm text-gray-500">Nov 10, 2024</span>
                  </div>
                  <p className="text-sm text-gray-700">
                    Excellent tutor! Very patient and explains concepts clearly. My daughter's math grades have improved significantly.
                  </p>
                </div>
                
                <div className="border-b pb-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <div className="flex">
                        {[...Array(5)].map((_, i) => (
                          <Star key={i} className={`w-4 h-4 ${i < 4 ? 'text-yellow-500 fill-yellow-500' : 'text-gray-300'}`} />
                        ))}
                      </div>
                      <span className="text-sm text-gray-600">by Jean T.</span>
                    </div>
                    <span className="text-sm text-gray-500">Oct 28, 2024</span>
                  </div>
                  <p className="text-sm text-gray-700">
                    Good tutor, sometimes runs a few minutes late but always makes up the time. Very knowledgeable.
                  </p>
                </div>
              </div>
            </Card>
          </TabsContent>

          {/* Payments Tab */}
          <TabsContent value="payments" className="mt-6 space-y-6">
            <Card className="p-6">
              <h4 className="font-medium text-gray-900 mb-4">Payment History</h4>
              
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead>
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Period
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Hours
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Amount
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Paid On
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    <tr>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">Nov 14-20, 2024</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">22.5</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">{formatCurrency(1012.50)}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Badge className="bg-yellow-100 text-yellow-700">Pending</Badge>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">-</td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">Nov 7-13, 2024</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">28</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">{formatCurrency(1260)}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Badge className="bg-green-100 text-green-700">Paid</Badge>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">Nov 16, 2024</td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">Oct 31-Nov 6, 2024</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">25.5</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">{formatCurrency(1147.50)}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Badge className="bg-green-100 text-green-700">Paid</Badge>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">Nov 9, 2024</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </Card>

            <Card className="p-6">
              <h4 className="font-medium text-gray-900 mb-4">Earnings Summary</h4>
              
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <p className="text-sm text-gray-600">This Month</p>
                  <p className="text-2xl font-semibold">{formatCurrency(3420)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Last Month</p>
                  <p className="text-2xl font-semibold">{formatCurrency(4850)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Year to Date</p>
                  <p className="text-2xl font-semibold">{formatCurrency(42350)}</p>
                </div>
              </div>
            </Card>
          </TabsContent>

          {/* Messages Tab */}
          <TabsContent value="messages" className="mt-6 space-y-6">
            <Card className="p-6">
              <h4 className="font-medium text-gray-900 mb-4">Recent Communications</h4>
              
              <div className="space-y-4">
                <div className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <MessageSquare className="w-5 h-5 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <p className="font-medium text-sm">System Notification</p>
                      <span className="text-xs text-gray-500">2 hours ago</span>
                    </div>
                    <p className="text-sm text-gray-600">
                      Your profile verification has been approved. You can now accept new students.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                  <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                    <MessageSquare className="w-5 h-5 text-green-600" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <p className="font-medium text-sm">Marie Dubois</p>
                      <span className="text-xs text-gray-500">Yesterday</span>
                    </div>
                    <p className="text-sm text-gray-600">
                      Thank you for the great session! Emma really enjoyed the math lesson.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                  <div className="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                    <AlertCircle className="w-5 h-5 text-yellow-600" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <p className="font-medium text-sm">Platform Admin</p>
                      <span className="text-xs text-gray-500">3 days ago</span>
                    </div>
                    <p className="text-sm text-gray-600">
                      Reminder: Please update your availability for the upcoming holiday season.
                    </p>
                  </div>
                </div>
              </div>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Action Buttons */}
        <div className="flex justify-between items-center pt-4 border-t">
          <div className="flex gap-2">
            <Button variant="secondary" size="sm" onClick={() => toast.info('Edit profile coming soon')}>
              <Edit2 className="w-4 h-4 mr-2" />
              Edit Profile
            </Button>
            <Button variant="secondary" size="sm" onClick={() => toast.info('Send message coming soon')}>
              <MessageSquare className="w-4 h-4 mr-2" />
              Send Message
            </Button>
          </div>
          <Button variant="ghost" onClick={onClose}>
            Close
          </Button>
        </div>
      </div>
    </Modal>
  );
};