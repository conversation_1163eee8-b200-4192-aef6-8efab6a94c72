/**
 * Subscription service for managing client subscriptions
 */

import api from './api';

export enum SubscriptionStatus {
  ACTIVE = 'active',
  PAUSED = 'paused',
  CANCELLED = 'cancelled',
  EXPIRED = 'expired'
}

export enum SubscriptionType {
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  ANNUAL = 'annual',
  CUSTOM = 'custom'
}

export interface Subscription {
  subscription_id: number;
  client_id: number;
  client_name: string;
  plan_id: number;
  plan_name: string;
  status: SubscriptionStatus;
  start_date: string;
  end_date?: string;
  next_billing_date?: string;
  hours_purchased: number;
  hours_used: number;
  hours_remaining: number;
  amount: number;
  currency: string;
  payment_method?: string;
  auto_renew: boolean;
  created_at: string;
  updated_at: string;
}

export interface SubscriptionPlan {
  plan_id: number;
  name: string;
  description?: string;
  type: SubscriptionType;
  hours_included: number;
  price: number;
  currency: string;
  duration_months: number;
  is_active: boolean;
  features?: string[];
  created_at: string;
  updated_at: string;
}

export interface SubscriptionUsage {
  usage_id: number;
  subscription_id: number;
  appointment_id: number;
  hours_used: number;
  session_date: string;
  tutor_name: string;
  subject: string;
  notes?: string;
  created_at: string;
}

export interface SubscriptionSearchParams {
  client_id?: number;
  status?: SubscriptionStatus;
  plan_id?: number;
  search?: string;
  start_date?: string;
  end_date?: string;
  page?: number;
  limit?: number;
}

export interface SubscriptionListResponse {
  items: Subscription[];
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
}

export interface CreateSubscriptionRequest {
  client_id: number;
  plan_id: number;
  start_date?: string;
  payment_method?: string;
  auto_renew?: boolean;
}

export interface UpdateSubscriptionRequest {
  status?: SubscriptionStatus;
  auto_renew?: boolean;
  payment_method?: string;
}

export interface AddSubscriptionHoursRequest {
  hours: number;
  reason: string;
  expires_at?: string;
}

export interface DeductSubscriptionHoursRequest {
  appointment_id: number;
  hours: number;
  notes?: string;
}

export const subscriptionService = {
  // Get all subscriptions with filters
  async getSubscriptions(params?: SubscriptionSearchParams): Promise<SubscriptionListResponse> {
    const response = await api.get<SubscriptionListResponse>('/subscriptions', { params });
    return response.data;
  },

  // Get subscription by ID
  async getSubscription(subscriptionId: number): Promise<Subscription> {
    const response = await api.get<Subscription>(`/subscriptions/${subscriptionId}`);
    return response.data;
  },

  // Get subscriptions for a client
  async getClientSubscriptions(clientId: number): Promise<Subscription[]> {
    const response = await api.get<Subscription[]>(`/clients/${clientId}/subscriptions`);
    return response.data;
  },

  // Create new subscription
  async createSubscription(data: CreateSubscriptionRequest): Promise<Subscription> {
    const response = await api.post<Subscription>('/subscriptions', data);
    return response.data;
  },

  // Update subscription
  async updateSubscription(subscriptionId: number, data: UpdateSubscriptionRequest): Promise<Subscription> {
    const response = await api.put<Subscription>(`/subscriptions/${subscriptionId}`, data);
    return response.data;
  },

  // Cancel subscription
  async cancelSubscription(subscriptionId: number, reason?: string): Promise<Subscription> {
    const response = await api.post<Subscription>(`/subscriptions/${subscriptionId}/cancel`, { reason });
    return response.data;
  },

  // Pause subscription
  async pauseSubscription(subscriptionId: number, resumeDate?: string): Promise<Subscription> {
    const response = await api.post<Subscription>(`/subscriptions/${subscriptionId}/pause`, { resume_date: resumeDate });
    return response.data;
  },

  // Resume subscription
  async resumeSubscription(subscriptionId: number): Promise<Subscription> {
    const response = await api.post<Subscription>(`/subscriptions/${subscriptionId}/resume`);
    return response.data;
  },

  // Get subscription usage history
  async getSubscriptionUsage(subscriptionId: number, params?: {
    start_date?: string;
    end_date?: string;
    page?: number;
    limit?: number;
  }): Promise<{
    items: SubscriptionUsage[];
    total: number;
    total_hours: number;
  }> {
    const response = await api.get(`/subscriptions/${subscriptionId}/usage`, { params });
    return response.data;
  },

  // Add hours to subscription (manager only)
  async addHours(subscriptionId: number, data: AddSubscriptionHoursRequest): Promise<Subscription> {
    const response = await api.post<Subscription>(`/subscriptions/${subscriptionId}/add-hours`, data);
    return response.data;
  },

  // Deduct hours from subscription (when appointment is completed)
  async deductHours(subscriptionId: number, data: DeductSubscriptionHoursRequest): Promise<Subscription> {
    const response = await api.post<Subscription>(`/subscriptions/${subscriptionId}/deduct-hours`, data);
    return response.data;
  },

  // Get all subscription plans
  async getPlans(params?: {
    is_active?: boolean;
    type?: SubscriptionType;
  }): Promise<SubscriptionPlan[]> {
    const response = await api.get<SubscriptionPlan[]>('/subscription-plans', { params });
    return response.data;
  },

  // Get plan by ID
  async getPlan(planId: number): Promise<SubscriptionPlan> {
    const response = await api.get<SubscriptionPlan>(`/subscription-plans/${planId}`);
    return response.data;
  },

  // Create subscription plan (manager only)
  async createPlan(data: Omit<SubscriptionPlan, 'plan_id' | 'created_at' | 'updated_at'>): Promise<SubscriptionPlan> {
    const response = await api.post<SubscriptionPlan>('/subscription-plans', data);
    return response.data;
  },

  // Update subscription plan (manager only)
  async updatePlan(planId: number, data: Partial<Omit<SubscriptionPlan, 'plan_id' | 'created_at' | 'updated_at'>>): Promise<SubscriptionPlan> {
    const response = await api.put<SubscriptionPlan>(`/subscription-plans/${planId}`, data);
    return response.data;
  },

  // Delete subscription plan (manager only)
  async deletePlan(planId: number): Promise<void> {
    await api.delete(`/subscription-plans/${planId}`);
  },

  // Get subscription statistics
  async getSubscriptionStats(): Promise<{
    active_subscriptions: number;
    total_revenue: number;
    total_hours_purchased: number;
    total_hours_used: number;
    average_usage_rate: number;
    popular_plans: Array<{
      plan_name: string;
      subscriber_count: number;
    }>;
  }> {
    const response = await api.get('/subscriptions/stats');
    return response.data;
  },

  // Check if client has active subscription
  async checkClientSubscription(clientId: number): Promise<{
    has_active_subscription: boolean;
    subscription?: Subscription;
    hours_remaining?: number;
  }> {
    const response = await api.get(`/clients/${clientId}/subscription-status`);
    return response.data;
  },

  // Get upcoming renewals
  async getUpcomingRenewals(days: number = 7): Promise<Subscription[]> {
    const response = await api.get<Subscription[]>('/subscriptions/upcoming-renewals', {
      params: { days }
    });
    return response.data;
  },

  // Process subscription renewal (automated or manual)
  async processRenewal(subscriptionId: number): Promise<{
    success: boolean;
    new_subscription?: Subscription;
    error?: string;
  }> {
    const response = await api.post(`/subscriptions/${subscriptionId}/renew`);
    return response.data;
  },

  // Get expiring subscriptions
  async getExpiringSubscriptions(days: number = 30): Promise<Subscription[]> {
    const response = await api.get<Subscription[]>('/subscriptions/expiring', {
      params: { days }
    });
    return response.data;
  }
};