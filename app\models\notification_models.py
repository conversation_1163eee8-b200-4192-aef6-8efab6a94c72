"""
Notification Models

Data models for the notification system including SMS, push notifications,
email, and notification preferences.
"""

from typing import Optional, Dict, Any, List
from datetime import datetime, time
from enum import Enum
from pydantic import BaseModel, Field, field_validator
from app.models.base import BaseEntity


class NotificationChannel(str, Enum):
    """Available notification channels"""
    PUSH = "push"
    SMS = "sms"
    EMAIL = "email"
    IN_APP = "in_app"


class NotificationPriority(str, Enum):
    """Notification priority levels"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class NotificationStatus(str, Enum):
    """Notification status"""
    PENDING = "pending"
    SENT = "sent"
    DELIVERED = "delivered"
    READ = "read"
    FAILED = "failed"
    CANCELLED = "cancelled"


class NotificationType(str, Enum):
    """Types of notifications"""
    # Appointment related
    APPOINTMENT_REMINDER = "appointment_reminder"
    APPOINTMENT_CONFIRMATION = "appointment_confirmation"
    APPOINTMENT_CANCELLED = "appointment_cancelled"
    APPOINTMENT_RESCHEDULED = "appointment_rescheduled"
    SESSION_COMPLETION = "session_completion"
    
    # Billing related
    INVOICE_CREATED = "invoice_created"
    PAYMENT_RECEIVED = "payment_received"
    PAYMENT_FAILED = "payment_failed"
    
    # User management
    ACCOUNT_CREATED = "account_created"
    PASSWORD_RESET = "password_reset"
    PROFILE_UPDATED = "profile_updated"
    
    # Communication
    NEW_MESSAGE = "new_message"
    BROADCAST_MESSAGE = "broadcast_message"
    
    # System
    SYSTEM_ANNOUNCEMENT = "system_announcement"
    MAINTENANCE_NOTICE = "maintenance_notice"


class SMSStatus(str, Enum):
    """SMS delivery status"""
    PENDING = "pending"
    QUEUED = "queued"
    SENT = "sent"
    DELIVERED = "delivered"
    FAILED = "failed"
    UNDELIVERED = "undelivered"


class EmailStatus(str, Enum):
    """Email delivery status"""
    PENDING = "pending"
    SENT = "sent"
    DELIVERED = "delivered"
    OPENED = "opened"
    CLICKED = "clicked"
    BOUNCED = "bounced"
    FAILED = "failed"


class PushStatus(str, Enum):
    """Push notification status"""
    PENDING = "pending"
    SENT = "sent"
    DELIVERED = "delivered"
    OPENED = "opened"
    FAILED = "failed"


# Database Models

class SMSMessage(BaseEntity):
    """SMS message model"""
    sms_id: int
    user_id: Optional[int] = None
    phone_number: str
    message: str
    direction: str  # inbound or outbound
    status: SMSStatus = SMSStatus.PENDING
    twilio_sid: Optional[str] = None
    
    # Error tracking
    error_code: Optional[str] = None
    error_message: Optional[str] = None
    
    # Metadata
    metadata: Optional[Dict[str, Any]] = None
    template_id: Optional[int] = None
    conversation_id: Optional[int] = None
    
    # Timestamps
    created_at: datetime
    sent_at: Optional[datetime] = None
    delivered_at: Optional[datetime] = None
    
    @field_validator('phone_number')
    def validate_phone(cls, v):
        # Basic phone validation
        import re
        if not re.match(r'^\+?1?\d{10,14}$', v.replace('-', '').replace(' ', '')):
            raise ValueError('Invalid phone number format')
        return v


class SMSConversation(BaseEntity):
    """SMS conversation thread model"""
    conversation_id: int
    user_id: Optional[int] = None
    phone_number: str
    participant_name: Optional[str] = None
    
    # Status
    status: str = "active"  # active, resolved, waiting, escalated, closed
    assigned_to: Optional[int] = None
    
    # Metadata
    tags: List[str] = []
    notes: Optional[str] = None
    
    # Timestamps
    created_at: datetime
    updated_at: datetime
    last_message_at: datetime
    
    # Statistics
    message_count: int = 0
    unread_count: int = 0


class EmailNotification(BaseEntity):
    """Email notification model"""
    email_id: int
    user_id: Optional[int] = None
    to_email: str
    from_email: str
    subject: str
    body_html: str
    body_text: str
    
    # Status
    status: EmailStatus = EmailStatus.PENDING
    provider: str = "sendgrid"  # sendgrid, ses, smtp
    external_id: Optional[str] = None
    
    # Tracking
    opens: int = 0
    clicks: int = 0
    
    # Error tracking
    error_message: Optional[str] = None
    
    # Metadata
    template_id: Optional[int] = None
    metadata: Optional[Dict[str, Any]] = None
    
    # Timestamps
    created_at: datetime
    sent_at: Optional[datetime] = None
    delivered_at: Optional[datetime] = None
    opened_at: Optional[datetime] = None
    
    @field_validator('to_email', 'from_email')
    def validate_email(cls, v):
        # Basic email validation
        import re
        if not re.match(r'^[\w\.-]+@[\w\.-]+\.\w+$', v):
            raise ValueError('Invalid email format')
        return v


class PushNotification(BaseEntity):
    """Push notification model"""
    push_id: int
    user_id: int
    
    # Content
    title: str
    body: str
    data: Optional[Dict[str, Any]] = None
    
    # Targeting
    device_tokens: List[str] = []
    topic: Optional[str] = None
    
    # Status
    status: PushStatus = PushStatus.PENDING
    provider: str = "onesignal"  # onesignal, fcm, apns
    external_id: Optional[str] = None
    
    # Delivery
    delivered_count: int = 0
    failed_count: int = 0
    
    # Metadata
    notification_type: NotificationType
    priority: str = "normal"  # normal, high
    
    # Timestamps
    created_at: datetime
    sent_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None


class NotificationPreference(BaseEntity):
    """User notification preference model"""
    preference_id: int
    user_id: int
    notification_type: NotificationType
    
    # Channel preferences
    enabled: bool = True
    push_enabled: bool = True
    sms_enabled: bool = False
    email_enabled: bool = False
    
    # Timing preferences
    quiet_hours_start: Optional[time] = None
    quiet_hours_end: Optional[time] = None
    
    # Frequency
    frequency: str = "immediate"  # immediate, daily_digest, weekly_digest
    
    # Metadata
    created_at: datetime
    updated_at: datetime


class NotificationLog(BaseEntity):
    """Unified notification log model"""
    log_id: int
    user_id: Optional[int] = None
    
    # Notification details
    notification_type: NotificationType
    channel: NotificationChannel
    
    # Reference to specific notification
    reference_id: int  # ID in SMS, Email, or Push table
    
    # Content summary
    title: Optional[str] = None
    preview: str
    
    # Status
    status: str  # pending, sent, delivered, failed
    
    # Metadata
    metadata: Optional[Dict[str, Any]] = None
    
    # Timestamps
    created_at: datetime
    sent_at: Optional[datetime] = None
    delivered_at: Optional[datetime] = None
    read_at: Optional[datetime] = None


class NotificationTemplate(BaseEntity):
    """Notification template model"""
    template_id: int
    template_key: str
    name: str
    
    # Channel-specific content
    sms_template: Optional[str] = None
    email_subject_template: Optional[str] = None
    email_body_template: Optional[str] = None
    push_title_template: Optional[str] = None
    push_body_template: Optional[str] = None
    
    # Variables
    required_variables: List[str] = []
    optional_variables: List[str] = []
    
    # Metadata
    category: str
    language: str = "en"
    is_active: bool = True
    
    # Usage
    usage_count: int = 0
    last_used_at: Optional[datetime] = None
    
    # Management
    created_by: int
    created_at: datetime
    updated_at: datetime


class UserNotificationPreferences(BaseEntity):
    """Aggregated user notification preferences"""
    user_id: int
    
    # Global settings
    timezone: str = "America/Toronto"
    language: str = "en"
    
    # Global opt-outs
    sms_opted_out: bool = False
    email_opted_out: bool = False
    push_opted_out: bool = False
    
    # Preferences by type
    preferences: Dict[NotificationType, NotificationPreference]
    
    # Metadata
    created_at: datetime
    updated_at: datetime


# API Request/Response Models

class SendNotificationRequest(BaseModel):
    """Request to send a notification"""
    user_id: Optional[int] = None
    notification_type: NotificationType
    channels: List[NotificationChannel]
    
    # Content
    title: Optional[str] = None
    message: str
    template_id: Optional[int] = None
    template_variables: Optional[Dict[str, Any]] = None
    
    # Targeting
    phone_number: Optional[str] = None
    email: Optional[str] = None
    
    # Options
    priority: str = "normal"
    schedule_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None


class NotificationResponse(BaseModel):
    """Response for notification send"""
    success: bool
    notification_ids: Dict[str, Any]  # channel -> id mapping
    errors: Optional[Dict[str, str]] = None


class UpdatePreferencesRequest(BaseModel):
    """Request to update notification preferences"""
    notification_type: NotificationType
    enabled: Optional[bool] = None
    push_enabled: Optional[bool] = None
    sms_enabled: Optional[bool] = None
    email_enabled: Optional[bool] = None
    quiet_hours_start: Optional[time] = None
    quiet_hours_end: Optional[time] = None
    frequency: Optional[str] = None


class BulkNotificationRequest(BaseModel):
    """Request for bulk notifications"""
    user_ids: List[int]
    notification_type: NotificationType
    channels: List[NotificationChannel]
    
    # Content
    title: Optional[str] = None
    message: str
    template_id: Optional[int] = None
    
    # Options
    respect_preferences: bool = True
    priority: str = "normal"
    schedule_at: Optional[datetime] = None