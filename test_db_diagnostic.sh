#!/bin/bash
# Test the database diagnostic endpoint

echo "Testing Database Connection Diagnostic"
echo "====================================="
echo ""

# Test the db-test endpoint
curl -s "https://tutoraide-production.up.railway.app/api/v1/auth/db-test" | python3 -m json.tool

echo ""
echo "====================================="
echo ""
echo "Key things to check:"
echo "1. current_database - should be 'railway'"
echo "2. search_path - should include 'public'"
echo "3. public_tables - should list your tables if they exist"
echo "4. user_accounts_accessible - should be true if table exists"