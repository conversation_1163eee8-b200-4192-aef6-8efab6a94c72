import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { X, Plus, Trash2 } from 'lucide-react';
import { Modal } from '../common/Modal';
import Button from '../common/Button';
import { Input } from '../common/Input';
import { serviceService, ServiceCreateRequest, ServiceType, SubjectArea, Service } from '../../services/serviceService';
import toast from 'react-hot-toast';

interface AddServiceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  mode: 'services' | 'packages';
}

const AddServiceModal: React.FC<AddServiceModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  mode
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [services, setServices] = useState<Service[]>([]);
  
  // Service form state
  const [serviceForm, setServiceForm] = useState<ServiceCreateRequest>({
    name: '',
    description: '',
    subject_area: SubjectArea.MATH,
    service_type: ServiceType.ONLINE,
    grade_levels: [],
    duration_options: [30, 60],
    frequency_options: [],
    requires_location: false,
    max_students: 1,
    min_students: 1,
    default_client_rate: 0,
    default_tutor_rate: 0,
    tags: []
  });

  // Package form state
  const [packageForm, setPackageForm] = useState({
    name: '',
    description: '',
    service_id: 0,
    session_count: 12,
    total_hours: 12,
    price: 0,
    discount_percentage: 0,
    valid_days: 90,
    is_active: true,
    is_tecfee: false,
    features: [] as string[]
  });

  // Form helpers
  const [newGradeLevel, setNewGradeLevel] = useState('');
  const [newDuration, setNewDuration] = useState('');
  const [newFrequency, setNewFrequency] = useState('');
  const [newTag, setNewTag] = useState('');
  const [newFeature, setNewFeature] = useState('');

  useEffect(() => {
    if (mode === 'packages' && isOpen) {
      loadServices();
    }
  }, [mode, isOpen]);

  const loadServices = async () => {
    try {
      const response = await serviceService.getServices({ is_active: true });
      setServices(response.items);
    } catch (error) {
      console.error('Error loading services:', error);
    }
  };

  const handleServiceSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!serviceForm.name || !serviceForm.duration_options.length) {
      toast.error(t('validation.requiredFields'));
      return;
    }

    setLoading(true);
    try {
      await serviceService.createService(serviceForm);
      toast.success(t('services.createSuccess'));
      onSuccess();
      onClose();
    } catch (error: any) {
      console.error('Error creating service:', error);
      toast.error(error.response?.data?.detail || t('services.createError'));
    } finally {
      setLoading(false);
    }
  };

  const handlePackageSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!packageForm.name || !packageForm.service_id || !packageForm.price) {
      toast.error(t('validation.requiredFields'));
      return;
    }

    setLoading(true);
    try {
      await serviceService.createPackage(packageForm);
      toast.success(t('services.packages.createSuccess'));
      onSuccess();
      onClose();
    } catch (error: any) {
      console.error('Error creating package:', error);
      toast.error(error.response?.data?.detail || t('services.packages.createError'));
    } finally {
      setLoading(false);
    }
  };

  const addGradeLevel = () => {
    if (newGradeLevel && !serviceForm.grade_levels?.includes(newGradeLevel)) {
      setServiceForm(prev => ({
        ...prev,
        grade_levels: [...(prev.grade_levels || []), newGradeLevel]
      }));
      setNewGradeLevel('');
    }
  };

  const removeGradeLevel = (level: string) => {
    setServiceForm(prev => ({
      ...prev,
      grade_levels: (prev.grade_levels || []).filter(l => l !== level)
    }));
  };

  const addDuration = () => {
    const duration = parseInt(newDuration);
    if (duration && !serviceForm.duration_options.includes(duration)) {
      setServiceForm(prev => ({
        ...prev,
        duration_options: [...prev.duration_options, duration].sort((a, b) => a - b)
      }));
      setNewDuration('');
    }
  };

  const removeDuration = (duration: number) => {
    setServiceForm(prev => ({
      ...prev,
      duration_options: prev.duration_options.filter(d => d !== duration)
    }));
  };

  const addFrequency = () => {
    if (newFrequency && !serviceForm.frequency_options?.includes(newFrequency)) {
      setServiceForm(prev => ({
        ...prev,
        frequency_options: [...(prev.frequency_options || []), newFrequency]
      }));
      setNewFrequency('');
    }
  };

  const removeFrequency = (freq: string) => {
    setServiceForm(prev => ({
      ...prev,
      frequency_options: (prev.frequency_options || []).filter(f => f !== freq)
    }));
  };

  const addTag = () => {
    if (newTag && !serviceForm.tags?.includes(newTag)) {
      setServiceForm(prev => ({
        ...prev,
        tags: [...(prev.tags || []), newTag]
      }));
      setNewTag('');
    }
  };

  const removeTag = (tag: string) => {
    setServiceForm(prev => ({
      ...prev,
      tags: (prev.tags || []).filter(t => t !== tag)
    }));
  };

  const addFeature = () => {
    if (newFeature && !packageForm.features.includes(newFeature)) {
      setPackageForm(prev => ({
        ...prev,
        features: [...prev.features, newFeature]
      }));
      setNewFeature('');
    }
  };

  const removeFeature = (feature: string) => {
    setPackageForm(prev => ({
      ...prev,
      features: prev.features.filter(f => f !== feature)
    }));
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="lg">
      <form onSubmit={mode === 'services' ? handleServiceSubmit : handlePackageSubmit} className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">
            {mode === 'services' ? t('services.addTitle') : t('services.packages.addTitle')}
          </h2>
          <button
            type="button"
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Form Fields */}
        {mode === 'services' ? (
          <div className="space-y-6">
            {/* Basic Information */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {t('services.basicInfo')}
              </h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('services.name')} *
                  </label>
                  <Input
                    type="text"
                    value={serviceForm.name}
                    onChange={(e) => setServiceForm(prev => ({ ...prev, name: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('services.description')}
                  </label>
                  <textarea
                    value={serviceForm.description}
                    onChange={(e) => setServiceForm(prev => ({ ...prev, description: e.target.value }))}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-red"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('services.subjectArea')} *
                    </label>
                    <select
                      value={serviceForm.subject_area}
                      onChange={(e) => setServiceForm(prev => ({ ...prev, subject_area: e.target.value as SubjectArea }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-red"
                    >
                      <option value={SubjectArea.MATH}>{t('services.subjects.math')}</option>
                      <option value={SubjectArea.SCIENCE}>{t('services.subjects.science')}</option>
                      <option value={SubjectArea.FRENCH}>{t('services.subjects.french')}</option>
                      <option value={SubjectArea.ENGLISH}>{t('services.subjects.english')}</option>
                      <option value={SubjectArea.OTHERS}>{t('services.subjects.others')}</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {t('services.serviceType')} *
                    </label>
                    <select
                      value={serviceForm.service_type}
                      onChange={(e) => setServiceForm(prev => ({ 
                        ...prev, 
                        service_type: e.target.value as ServiceType,
                        requires_location: e.target.value === ServiceType.IN_PERSON || e.target.value === ServiceType.LIBRARY
                      }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-red"
                    >
                      <option value={ServiceType.ONLINE}>{t('services.types.online')}</option>
                      <option value={ServiceType.IN_PERSON}>{t('services.types.inPerson')}</option>
                      <option value={ServiceType.LIBRARY}>{t('services.types.library')}</option>
                      <option value={ServiceType.HYBRID}>{t('services.types.hybrid')}</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            {/* Grade Levels */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {t('services.gradeLevels')}
              </h3>
              {serviceForm.grade_levels && serviceForm.grade_levels.length > 0 && (
                <div className="mb-3 flex flex-wrap gap-2">
                  {serviceForm.grade_levels.map((level) => (
                    <div
                      key={level}
                      className="inline-flex items-center bg-gray-100 text-gray-800 rounded-full px-3 py-1 text-sm"
                    >
                      <span>{level}</span>
                      <button
                        type="button"
                        onClick={() => removeGradeLevel(level)}
                        className="ml-2 text-gray-600 hover:text-gray-800"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
              <div className="flex gap-2">
                <select
                  value={newGradeLevel}
                  onChange={(e) => setNewGradeLevel(e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-red"
                >
                  <option value="">{t('common.select')}</option>
                  <option value="Preschool">Preschool</option>
                  <option value="Kindergarten">Kindergarten</option>
                  {[...Array(12)].map((_, i) => (
                    <option key={i + 1} value={`Grade ${i + 1}`}>Grade {i + 1}</option>
                  ))}
                  <option value="College">College/CEGEP</option>
                  <option value="University">University</option>
                  <option value="Adult">Adult Education</option>
                </select>
                <Button
                  type="button"
                  variant="secondary"
                  onClick={addGradeLevel}
                  disabled={!newGradeLevel}
                >
                  <Plus className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Duration Options */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {t('services.durationOptions')} *
              </h3>
              <div className="mb-3 flex flex-wrap gap-2">
                {serviceForm.duration_options.map((duration) => (
                  <div
                    key={duration}
                    className="inline-flex items-center bg-blue-100 text-blue-800 rounded-full px-3 py-1 text-sm"
                  >
                    <span>{duration} min</span>
                    <button
                      type="button"
                      onClick={() => removeDuration(duration)}
                      className="ml-2 text-blue-600 hover:text-blue-800"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </div>
                ))}
              </div>
              <div className="flex gap-2">
                <Input
                  type="number"
                  value={newDuration}
                  onChange={(e) => setNewDuration(e.target.value)}
                  placeholder={t('services.minutesPlaceholder')}
                  min="15"
                  step="15"
                />
                <Button
                  type="button"
                  variant="secondary"
                  onClick={addDuration}
                  disabled={!newDuration}
                >
                  <Plus className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Frequency Options */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {t('services.frequencyOptions')}
              </h3>
              {serviceForm.frequency_options && serviceForm.frequency_options.length > 0 && (
                <div className="mb-3 flex flex-wrap gap-2">
                  {serviceForm.frequency_options.map((freq) => (
                    <div
                      key={freq}
                      className="inline-flex items-center bg-gray-100 text-gray-800 rounded-full px-3 py-1 text-sm"
                    >
                      <span>{freq}</span>
                      <button
                        type="button"
                        onClick={() => removeFrequency(freq)}
                        className="ml-2 text-gray-600 hover:text-gray-800"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
              <div className="flex gap-2">
                <select
                  value={newFrequency}
                  onChange={(e) => setNewFrequency(e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-red"
                >
                  <option value="">{t('common.select')}</option>
                  <option value="Once">Once</option>
                  <option value="Weekly">Weekly</option>
                  <option value="Bi-weekly">Bi-weekly</option>
                  <option value="Monthly">Monthly</option>
                  <option value="As needed">As needed</option>
                </select>
                <Button
                  type="button"
                  variant="secondary"
                  onClick={addFrequency}
                  disabled={!newFrequency}
                >
                  <Plus className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Capacity and Rates */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {t('services.capacityAndRates')}
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('services.minStudents')} *
                  </label>
                  <Input
                    type="number"
                    value={serviceForm.min_students}
                    onChange={(e) => setServiceForm(prev => ({ ...prev, min_students: parseInt(e.target.value) || 1 }))}
                    min="1"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('services.maxStudents')} *
                  </label>
                  <Input
                    type="number"
                    value={serviceForm.max_students}
                    onChange={(e) => setServiceForm(prev => ({ ...prev, max_students: parseInt(e.target.value) || 1 }))}
                    min={serviceForm.min_students}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('services.defaultClientRate')} (CAD)
                  </label>
                  <Input
                    type="number"
                    value={serviceForm.default_client_rate}
                    onChange={(e) => setServiceForm(prev => ({ ...prev, default_client_rate: parseFloat(e.target.value) || 0 }))}
                    min="0"
                    step="0.01"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('services.defaultTutorRate')} (CAD)
                  </label>
                  <Input
                    type="number"
                    value={serviceForm.default_tutor_rate}
                    onChange={(e) => setServiceForm(prev => ({ ...prev, default_tutor_rate: parseFloat(e.target.value) || 0 }))}
                    min="0"
                    step="0.01"
                  />
                </div>
              </div>
              <label className="flex items-center mt-4">
                <input
                  type="checkbox"
                  checked={serviceForm.requires_location}
                  onChange={(e) => setServiceForm(prev => ({ ...prev, requires_location: e.target.checked }))}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700">{t('services.requiresLocation')}</span>
              </label>
            </div>

            {/* Tags */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {t('services.tags')}
              </h3>
              {serviceForm.tags && serviceForm.tags.length > 0 && (
                <div className="mb-3 flex flex-wrap gap-2">
                  {serviceForm.tags.map((tag) => (
                    <div
                      key={tag}
                      className="inline-flex items-center bg-purple-100 text-purple-800 rounded-full px-3 py-1 text-sm"
                    >
                      <span>{tag}</span>
                      <button
                        type="button"
                        onClick={() => removeTag(tag)}
                        className="ml-2 text-purple-600 hover:text-purple-800"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
              <div className="flex gap-2">
                <Input
                  type="text"
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  placeholder={t('services.addTag')}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      addTag();
                    }
                  }}
                />
                <Button
                  type="button"
                  variant="secondary"
                  onClick={addTag}
                  disabled={!newTag}
                >
                  <Plus className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        ) : (
          /* Package Form */
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {t('services.packages.basicInfo')}
              </h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('services.packages.selectService')} *
                  </label>
                  <select
                    value={packageForm.service_id}
                    onChange={(e) => setPackageForm(prev => ({ ...prev, service_id: parseInt(e.target.value) || 0 }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-red"
                    required
                  >
                    <option value="0">{t('common.select')}</option>
                    {services.map((service) => (
                      <option key={service.service_id} value={service.service_id}>
                        {service.name} ({service.subject_area})
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('services.packages.name')} *
                  </label>
                  <Input
                    type="text"
                    value={packageForm.name}
                    onChange={(e) => setPackageForm(prev => ({ ...prev, name: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('services.packages.description')}
                  </label>
                  <textarea
                    value={packageForm.description}
                    onChange={(e) => setPackageForm(prev => ({ ...prev, description: e.target.value }))}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-red"
                  />
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {t('services.packages.details')}
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('services.packages.sessionCount')} *
                  </label>
                  <Input
                    type="number"
                    value={packageForm.session_count}
                    onChange={(e) => setPackageForm(prev => ({ ...prev, session_count: parseInt(e.target.value) || 0 }))}
                    min="1"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('services.packages.totalHours')} *
                  </label>
                  <Input
                    type="number"
                    value={packageForm.total_hours}
                    onChange={(e) => setPackageForm(prev => ({ ...prev, total_hours: parseFloat(e.target.value) || 0 }))}
                    min="0"
                    step="0.5"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('services.packages.price')} (CAD) *
                  </label>
                  <Input
                    type="number"
                    value={packageForm.price}
                    onChange={(e) => setPackageForm(prev => ({ ...prev, price: parseFloat(e.target.value) || 0 }))}
                    min="0"
                    step="0.01"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('services.packages.discount')} (%)
                  </label>
                  <Input
                    type="number"
                    value={packageForm.discount_percentage}
                    onChange={(e) => setPackageForm(prev => ({ ...prev, discount_percentage: parseFloat(e.target.value) || 0 }))}
                    min="0"
                    max="100"
                    step="1"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('services.packages.validDays')} *
                  </label>
                  <Input
                    type="number"
                    value={packageForm.valid_days}
                    onChange={(e) => setPackageForm(prev => ({ ...prev, valid_days: parseInt(e.target.value) || 0 }))}
                    min="1"
                    required
                  />
                </div>
              </div>
              <div className="flex items-center space-x-4 mt-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={packageForm.is_active}
                    onChange={(e) => setPackageForm(prev => ({ ...prev, is_active: e.target.checked }))}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700">{t('services.packages.isActive')}</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={packageForm.is_tecfee}
                    onChange={(e) => setPackageForm(prev => ({ ...prev, is_tecfee: e.target.checked }))}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700">{t('services.packages.isTecfee')}</span>
                </label>
              </div>
            </div>

            {/* Features */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {t('services.packages.features')}
              </h3>
              {packageForm.features.length > 0 && (
                <div className="mb-3 flex flex-wrap gap-2">
                  {packageForm.features.map((feature) => (
                    <div
                      key={feature}
                      className="inline-flex items-center bg-green-100 text-green-800 rounded-full px-3 py-1 text-sm"
                    >
                      <span>{feature}</span>
                      <button
                        type="button"
                        onClick={() => removeFeature(feature)}
                        className="ml-2 text-green-600 hover:text-green-800"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
              <div className="flex gap-2">
                <Input
                  type="text"
                  value={newFeature}
                  onChange={(e) => setNewFeature(e.target.value)}
                  placeholder={t('services.packages.addFeature')}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      addFeature();
                    }
                  }}
                />
                <Button
                  type="button"
                  variant="secondary"
                  onClick={addFeature}
                  disabled={!newFeature}
                >
                  <Plus className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="mt-8 flex justify-end space-x-3">
          <Button type="button" variant="secondary" onClick={onClose}>
            {t('common.cancel')}
          </Button>
          <Button type="submit" variant="primary" loading={loading}>
            {mode === 'services' ? t('services.create') : t('services.packages.create')}
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default AddServiceModal;