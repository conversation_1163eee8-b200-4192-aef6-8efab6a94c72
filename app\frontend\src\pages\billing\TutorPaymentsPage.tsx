import React from 'react';
import { useTranslation } from 'react-i18next';
import { DollarSign, Users, Calendar, TrendingUp } from 'lucide-react';
import TutorPaymentList from '../../components/billing/TutorPaymentList';
import TutorPaymentHistory from '../../components/tutor/TutorPaymentHistory';
import { Card } from '../../components/common/Card';
import { useAuth } from '../../contexts/AuthContext';

const TutorPaymentsPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const isManager = user?.activeRole === 'manager';
  const isTutor = user?.activeRole === 'tutor';
  
  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900 flex items-center">
              <Users className="w-6 h-6 mr-2 text-accent-red" />
              {t('billing.tutorPayments.title')}
            </h1>
            <p className="text-sm text-gray-500 mt-1">
              {isManager 
                ? t('billing.tutorPayments.managerDescription')
                : t('billing.tutorPayments.tutorDescription')
              }
            </p>
          </div>
        </div>
        
        {/* Weekly Payment Info */}
        <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="p-4">
            <div className="flex items-center">
              <Calendar className="w-8 h-8 text-accent-red mr-3" />
              <div>
                <p className="text-sm text-gray-500">{t('billing.tutorPayments.paymentCycle')}</p>
                <p className="text-lg font-semibold text-gray-900">
                  {t('billing.tutorPayments.thursdayToWednesday')}
                </p>
              </div>
            </div>
          </Card>
          
          <Card className="p-4">
            <div className="flex items-center">
              <DollarSign className="w-8 h-8 text-green-600 mr-3" />
              <div>
                <p className="text-sm text-gray-500">{t('billing.tutorPayments.paymentMethod')}</p>
                <p className="text-lg font-semibold text-gray-900">
                  {t('billing.tutorPayments.directDeposit')}
                </p>
              </div>
            </div>
          </Card>
          
          <Card className="p-4">
            <div className="flex items-center">
              <TrendingUp className="w-8 h-8 text-red-600 mr-3" />
              <div>
                <p className="text-sm text-gray-500">{t('billing.tutorPayments.processingTime')}</p>
                <p className="text-lg font-semibold text-gray-900">
                  {t('billing.tutorPayments.within48Hours')}
                </p>
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-6 bg-gray-50">
        {isManager ? (
          <TutorPaymentList />
        ) : isTutor ? (
          <TutorPaymentHistory />
        ) : (
          <Card className="p-6">
            <p className="text-gray-600">
              {t('billing.tutorPayments.notAvailableForRole')}
            </p>
          </Card>
        )}
      </div>
    </div>
  );
};

export default TutorPaymentsPage;
