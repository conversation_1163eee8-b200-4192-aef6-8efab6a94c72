"""
OneSignal push notification service for web and mobile push notifications.
"""

import logging
import aiohttp
import json
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
import hashlib
import hmac

from app.config.settings import settings
from app.core.exceptions import ExternalServiceError
from app.models.notification_models import NotificationType, NotificationPriority

logger = logging.getLogger(__name__)


class OneSignalService:
    """
    Service for managing push notifications through OneSignal.
    
    Features:
    - Web push notifications
    - Mobile push notifications (iOS/Android)
    - User segmentation
    - Device registration
    - Notification scheduling
    - Delivery tracking
    """
    
    def __init__(self):
        self.app_id = settings.ONESIGNAL_APP_ID
        self.api_key = settings.ONESIGNAL_API_KEY
        self.api_url = "https://onesignal.com/api/v1"
        self.session = None
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create aiohttp session."""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession(
                headers={
                    "Authorization": f"Basic {self.api_key}",
                    "Content-Type": "application/json"
                }
            )
        return self.session
    
    async def close(self):
        """Close the aiohttp session."""
        if self.session and not self.session.closed:
            await self.session.close()
    
    async def register_device(
        self,
        user_id: int,
        device_token: str,
        device_type: str,
        device_model: Optional[str] = None,
        app_version: Optional[str] = None,
        tags: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """
        Register a device for push notifications.
        
        Args:
            user_id: User ID in our system
            device_token: Push token from browser/device
            device_type: Type of device (web, ios, android)
            device_model: Device model information
            app_version: App version for mobile
            tags: Additional tags for segmentation
            
        Returns:
            Device registration response
        """
        try:
            session = await self._get_session()
            
            # Map device type to OneSignal device type
            device_type_map = {
                'web': 5,      # Chrome/Safari web push
                'ios': 0,      # iOS native
                'android': 1,  # Android native
                'firefox': 4,  # Firefox web push
                'safari': 7    # Safari web push
            }
            
            payload = {
                "app_id": self.app_id,
                "device_type": device_type_map.get(device_type, 5),
                "identifier": device_token,
                "external_user_id": str(user_id),
                "tags": tags or {}
            }
            
            # Add user tags for segmentation
            payload["tags"].update({
                "user_id": str(user_id),
                "device_type": device_type,
                "registered_at": datetime.now().isoformat()
            })
            
            if device_model:
                payload["device_model"] = device_model
            
            if app_version:
                payload["game_version"] = app_version
            
            url = f"{self.api_url}/players"
            
            async with session.post(url, json=payload) as response:
                data = await response.json()
                
                if response.status != 200:
                    logger.error(f"OneSignal device registration failed: {data}")
                    raise ExternalServiceError(f"Failed to register device: {data.get('errors', 'Unknown error')}")
                
                logger.info(f"Device registered for user {user_id}: {data.get('id')}")
                
                return {
                    "player_id": data.get("id"),
                    "success": data.get("success", True),
                    "external_user_id": str(user_id)
                }
                
        except Exception as e:
            logger.error(f"Error registering device: {e}")
            raise ExternalServiceError(f"Device registration failed: {str(e)}")
    
    async def update_user_tags(
        self,
        user_id: int,
        tags: Dict[str, str]
    ) -> bool:
        """
        Update user tags for segmentation.
        
        Args:
            user_id: User ID
            tags: Tags to update (role, preferences, etc)
            
        Returns:
            Success status
        """
        try:
            session = await self._get_session()
            
            payload = {
                "app_id": self.app_id,
                "tags": tags,
                "external_user_id": str(user_id)
            }
            
            url = f"{self.api_url}/players/{user_id}/update"
            
            async with session.put(url, json=payload) as response:
                if response.status == 200:
                    logger.info(f"Updated tags for user {user_id}")
                    return True
                else:
                    logger.warning(f"Failed to update tags for user {user_id}")
                    return False
                    
        except Exception as e:
            logger.error(f"Error updating user tags: {e}")
            return False
    
    async def send_notification(
        self,
        user_ids: Union[int, List[int]],
        title: str,
        message: str,
        data: Optional[Dict[str, Any]] = None,
        url: Optional[str] = None,
        buttons: Optional[List[Dict[str, str]]] = None,
        schedule_for: Optional[datetime] = None,
        priority: NotificationPriority = NotificationPriority.NORMAL,
        ttl: int = 86400,  # 24 hours
        badge_count: Optional[int] = None,
        sound: Optional[str] = None,
        image: Optional[str] = None,
        language: str = "en"
    ) -> Dict[str, Any]:
        """
        Send push notification to users.
        
        Args:
            user_ids: Single user ID or list of user IDs
            title: Notification title
            message: Notification message
            data: Additional data payload
            url: URL to open on click (web)
            buttons: Action buttons
            schedule_for: Schedule notification for future
            priority: Notification priority
            ttl: Time to live in seconds
            badge_count: Badge count for mobile
            sound: Custom sound file
            image: Rich media image URL
            language: Language code (en/fr)
            
        Returns:
            Notification send response
        """
        try:
            session = await self._get_session()
            
            # Ensure user_ids is a list
            if isinstance(user_ids, int):
                user_ids = [user_ids]
            
            # Build notification payload
            payload = {
                "app_id": self.app_id,
                "include_external_user_ids": [str(uid) for uid in user_ids],
                "contents": {language: message},
                "headings": {language: title},
                "priority": self._map_priority(priority),
                "ttl": ttl
            }
            
            # Add optional fields
            if data:
                payload["data"] = data
            
            if url:
                payload["url"] = url
                payload["web_url"] = url
            
            if buttons:
                payload["buttons"] = buttons
                payload["web_buttons"] = buttons
            
            if schedule_for:
                payload["send_after"] = schedule_for.strftime("%Y-%m-%d %H:%M:%S GMT%z")
            
            if badge_count is not None:
                payload["ios_badgeType"] = "SetTo"
                payload["ios_badgeCount"] = badge_count
            
            if sound:
                payload["ios_sound"] = sound
                payload["android_sound"] = sound
            else:
                # Default sounds
                payload["ios_sound"] = "notification.wav"
                payload["android_sound"] = "notification"
            
            if image:
                payload["big_picture"] = image
                payload["ios_attachments"] = {"image": image}
            
            # Add collapse ID to prevent duplicate notifications
            if data and data.get("notification_id"):
                payload["collapse_id"] = data["notification_id"]
            
            url = f"{self.api_url}/notifications"
            
            async with session.post(url, json=payload) as response:
                result = await response.json()
                
                if response.status != 200:
                    logger.error(f"OneSignal notification failed: {result}")
                    raise ExternalServiceError(f"Failed to send notification: {result.get('errors', 'Unknown error')}")
                
                logger.info(f"Notification sent to {len(user_ids)} users: {result.get('id')}")
                
                return {
                    "notification_id": result.get("id"),
                    "recipients": result.get("recipients", 0),
                    "external_id": result.get("external_id"),
                    "errors": result.get("errors", {})
                }
                
        except Exception as e:
            logger.error(f"Error sending notification: {e}")
            raise ExternalServiceError(f"Notification send failed: {str(e)}")
    
    async def send_to_segment(
        self,
        segment_name: str,
        title: str,
        message: str,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Send notification to a user segment.
        
        Args:
            segment_name: Name of the segment
            title: Notification title
            message: Notification message
            **kwargs: Additional notification options
            
        Returns:
            Notification send response
        """
        try:
            session = await self._get_session()
            
            payload = {
                "app_id": self.app_id,
                "included_segments": [segment_name],
                "contents": {"en": message, "fr": message},
                "headings": {"en": title, "fr": title},
                **kwargs
            }
            
            url = f"{self.api_url}/notifications"
            
            async with session.post(url, json=payload) as response:
                result = await response.json()
                
                if response.status != 200:
                    raise ExternalServiceError(f"Failed to send to segment: {result}")
                
                return result
                
        except Exception as e:
            logger.error(f"Error sending to segment: {e}")
            raise ExternalServiceError(f"Segment notification failed: {str(e)}")
    
    async def cancel_notification(self, notification_id: str) -> bool:
        """
        Cancel a scheduled notification.
        
        Args:
            notification_id: OneSignal notification ID
            
        Returns:
            Success status
        """
        try:
            session = await self._get_session()
            
            url = f"{self.api_url}/notifications/{notification_id}?app_id={self.app_id}"
            
            async with session.delete(url) as response:
                if response.status == 200:
                    logger.info(f"Cancelled notification {notification_id}")
                    return True
                else:
                    logger.warning(f"Failed to cancel notification {notification_id}")
                    return False
                    
        except Exception as e:
            logger.error(f"Error cancelling notification: {e}")
            return False
    
    async def get_notification_status(
        self,
        notification_id: str
    ) -> Dict[str, Any]:
        """
        Get notification delivery status.
        
        Args:
            notification_id: OneSignal notification ID
            
        Returns:
            Notification status details
        """
        try:
            session = await self._get_session()
            
            url = f"{self.api_url}/notifications/{notification_id}?app_id={self.app_id}"
            
            async with session.get(url) as response:
                data = await response.json()
                
                if response.status != 200:
                    raise ExternalServiceError(f"Failed to get notification status: {data}")
                
                return {
                    "id": data.get("id"),
                    "successful": data.get("successful", 0),
                    "failed": data.get("failed", 0),
                    "converted": data.get("converted", 0),
                    "remaining": data.get("remaining", 0),
                    "queued_at": data.get("queued_at"),
                    "send_after": data.get("send_after"),
                    "completed_at": data.get("completed_at"),
                    "platform_delivery_stats": data.get("platform_delivery_stats", {})
                }
                
        except Exception as e:
            logger.error(f"Error getting notification status: {e}")
            raise ExternalServiceError(f"Status check failed: {str(e)}")
    
    async def create_segment(
        self,
        name: str,
        filters: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Create a user segment for targeting.
        
        Args:
            name: Segment name
            filters: List of filter conditions
            
        Returns:
            Created segment details
        """
        try:
            session = await self._get_session()
            
            payload = {
                "name": name,
                "filters": filters
            }
            
            url = f"{self.api_url}/apps/{self.app_id}/segments"
            
            async with session.post(url, json=payload) as response:
                data = await response.json()
                
                if response.status != 200:
                    raise ExternalServiceError(f"Failed to create segment: {data}")
                
                logger.info(f"Created segment '{name}': {data.get('id')}")
                
                return data
                
        except Exception as e:
            logger.error(f"Error creating segment: {e}")
            raise ExternalServiceError(f"Segment creation failed: {str(e)}")
    
    def _map_priority(self, priority: NotificationPriority) -> int:
        """Map our priority to OneSignal priority."""
        priority_map = {
            NotificationPriority.LOW: 1,
            NotificationPriority.NORMAL: 5,
            NotificationPriority.HIGH: 8,
            NotificationPriority.URGENT: 10
        }
        return priority_map.get(priority, 5)
    
    def verify_webhook_signature(
        self,
        signature: str,
        timestamp: str,
        body: bytes
    ) -> bool:
        """
        Verify OneSignal webhook signature.
        
        Args:
            signature: Signature from header
            timestamp: Timestamp from header
            body: Raw request body
            
        Returns:
            True if signature is valid
        """
        try:
            # Construct the signed content
            signed_content = f"{timestamp}.{body.decode('utf-8')}"
            
            # Calculate expected signature
            expected_signature = hmac.new(
                settings.ONESIGNAL_WEBHOOK_SECRET.encode(),
                signed_content.encode(),
                hashlib.sha256
            ).hexdigest()
            
            # Compare signatures
            return hmac.compare_digest(signature, expected_signature)
            
        except Exception as e:
            logger.error(f"Error verifying webhook signature: {e}")
            return False
    
    async def handle_webhook(
        self,
        event_type: str,
        data: Dict[str, Any]
    ) -> None:
        """
        Handle OneSignal webhook events.
        
        Args:
            event_type: Type of webhook event
            data: Event data
        """
        try:
            if event_type == "notification.opened":
                # Handle notification opened event
                logger.info(f"Notification opened: {data.get('notification_id')}")
                
            elif event_type == "notification.dismissed":
                # Handle notification dismissed
                logger.info(f"Notification dismissed: {data.get('notification_id')}")
                
            elif event_type == "device.registered":
                # Handle new device registration
                logger.info(f"New device registered: {data.get('player_id')}")
                
            # Add more event handlers as needed
            
        except Exception as e:
            logger.error(f"Error handling webhook: {e}")