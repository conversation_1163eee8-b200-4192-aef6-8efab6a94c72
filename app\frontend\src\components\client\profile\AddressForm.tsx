import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Input } from '../../common/Input';
import Button from '../../common/Button';
import { Select } from '../../common/Select';
import { MapPin, Home, Building, Map } from 'lucide-react';

interface AddressFormProps {
  profile: {
    address_line_1: string | null;
    address_line_2: string | null;
    city: string | null;
    province: string | null;
    postal_code: string | null;
  };
  onUpdate: (data: any) => void;
  saving: boolean;
}

export const AddressForm: React.FC<AddressFormProps> = ({
  profile,
  onUpdate,
  saving
}) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    address_line_1: profile.address_line_1 || '',
    address_line_2: profile.address_line_2 || '',
    city: profile.city || '',
    province: profile.province || '',
    postal_code: profile.postal_code || ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  const provinceOptions = [
    { value: 'AB', label: 'Alberta' },
    { value: 'BC', label: 'British Columbia' },
    { value: 'MB', label: 'Manitoba' },
    { value: 'NB', label: 'New Brunswick' },
    { value: 'NL', label: 'Newfoundland and Labrador' },
    { value: 'NT', label: 'Northwest Territories' },
    { value: 'NS', label: 'Nova Scotia' },
    { value: 'NU', label: 'Nunavut' },
    { value: 'ON', label: 'Ontario' },
    { value: 'PE', label: 'Prince Edward Island' },
    { value: 'QC', label: 'Quebec' },
    { value: 'SK', label: 'Saskatchewan' },
    { value: 'YT', label: 'Yukon' }
  ];

  const formatPostalCode = (value: string) => {
    // Remove all non-alphanumeric characters and convert to uppercase
    const cleaned = value.replace(/[^A-Za-z0-9]/g, '').toUpperCase();
    
    // Format as A1A 1A1
    if (cleaned.length <= 3) {
      return cleaned;
    } else {
      return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)}`;
    }
  };

  const handlePostalCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPostalCode(e.target.value);
    setFormData({ ...formData, postal_code: formatted });
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    // If any address field is filled, validate the complete address
    const hasAnyAddressInfo = formData.address_line_1 || 
                             formData.city || 
                             formData.province || 
                             formData.postal_code;
    
    if (hasAnyAddressInfo) {
      if (!formData.address_line_1.trim()) {
        newErrors.address_line_1 = t('client.profile.errors.addressRequired');
      }
      
      if (!formData.city.trim()) {
        newErrors.city = t('client.profile.errors.cityRequired');
      }
      
      if (!formData.province) {
        newErrors.province = t('client.profile.errors.provinceRequired');
      }
      
      if (!formData.postal_code) {
        newErrors.postal_code = t('client.profile.errors.postalCodeRequired');
      } else {
        // Validate Canadian postal code format
        const postalCodeRegex = /^[A-Z]\d[A-Z]\s?\d[A-Z]\d$/;
        if (!postalCodeRegex.test(formData.postal_code)) {
          newErrors.postal_code = t('client.profile.errors.invalidPostalCode');
        }
      }
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      // If all fields are empty, send null values
      const dataToSend = (!formData.address_line_1 && 
                         !formData.city && 
                         !formData.province && 
                         !formData.postal_code)
        ? {
            address_line_1: null,
            address_line_2: null,
            city: null,
            province: null,
            postal_code: null
          }
        : formData;
        
      onUpdate(dataToSend);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <div className="flex items-start gap-3">
          <MapPin className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
          <div>
            <p className="text-sm text-blue-800 font-medium">
              {t('client.profile.addressInfo')}
            </p>
            <p className="text-sm text-blue-700 mt-1">
              {t('client.profile.addressDescription')}
            </p>
          </div>
        </div>
      </div>

      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-text-primary mb-2">
            {t('client.profile.fields.addressLine1')}
          </label>
          <Input
            leftIcon={<Home className="w-4 h-4" />}
            value={formData.address_line_1}
            onChange={(e) => setFormData({ ...formData, address_line_1: e.target.value })}
            placeholder={t('client.profile.placeholders.addressLine1')}
            error={errors.address_line_1}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-text-primary mb-2">
            {t('client.profile.fields.addressLine2')} 
            <span className="text-text-muted font-normal ml-1">({t('common.optional')})</span>
          </label>
          <Input
            leftIcon={<Building className="w-4 h-4" />}
            value={formData.address_line_2}
            onChange={(e) => setFormData({ ...formData, address_line_2: e.target.value })}
            placeholder={t('client.profile.placeholders.addressLine2')}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-1">
            <label className="block text-sm font-medium text-text-primary mb-2">
              {t('client.profile.fields.city')}
            </label>
            <Input
              value={formData.city}
              onChange={(e) => setFormData({ ...formData, city: e.target.value })}
              placeholder={t('client.profile.placeholders.city')}
              error={errors.city}
            />
          </div>

          <div className="md:col-span-1">
            <label className="block text-sm font-medium text-text-primary mb-2">
              {t('client.profile.fields.province')}
            </label>
            <Select
              value={formData.province}
              onChange={(value) => setFormData({ ...formData, province: value })}
              options={provinceOptions}
              placeholder={t('client.profile.placeholders.selectProvince')}
              error={errors.province}
              leftIcon={<Map className="w-4 h-4" />}
            />
          </div>

          <div className="md:col-span-1">
            <label className="block text-sm font-medium text-text-primary mb-2">
              {t('client.profile.fields.postalCode')}
            </label>
            <Input
              value={formData.postal_code}
              onChange={handlePostalCodeChange}
              placeholder="A1A 1A1"
              maxLength={7}
              error={errors.postal_code}
            />
          </div>
        </div>
      </div>

      <div className="flex justify-end gap-3 pt-4">
        <Button
          type="button"
          variant="ghost"
          onClick={() => setFormData({
            address_line_1: profile.address_line_1 || '',
            address_line_2: profile.address_line_2 || '',
            city: profile.city || '',
            province: profile.province || '',
            postal_code: profile.postal_code || ''
          })}
          disabled={saving}
        >
          {t('common.cancel')}
        </Button>
        <Button
          type="submit"
          loading={saving}
          disabled={saving}
        >
          {t('common.save')}
        </Button>
      </div>
    </form>
  );
};