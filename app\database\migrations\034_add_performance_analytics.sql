-- Migration: Add Performance Analytics System
-- Description: Creates tables and views for comprehensive performance tracking and analytics

-- =====================================================
-- TUTOR PERFORMANCE METRICS
-- =====================================================

-- Table to store aggregated tutor performance metrics
CREATE TABLE IF NOT EXISTS tutor_performance_metrics (
    metric_id SERIAL PRIMARY KEY,
    tutor_id INTEGER NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    period_type VARCHAR(20) NOT NULL CHECK (period_type IN ('daily', 'weekly', 'monthly', 'quarterly', 'yearly')),
    
    -- Session metrics
    total_sessions INTEGER DEFAULT 0,
    completed_sessions INTEGER DEFAULT 0,
    cancelled_sessions INTEGER DEFAULT 0,
    no_show_sessions INTEGER DEFAULT 0,
    
    -- Rating metrics
    average_rating DECIMAL(3,2),
    total_ratings INTEGER DEFAULT 0,
    five_star_count INTEGER DEFAULT 0,
    
    -- Financial metrics
    total_revenue DECIMAL(10,2) DEFAULT 0,
    total_hours DECIMAL(10,2) DEFAULT 0,
    average_hourly_rate DECIMAL(10,2),
    
    -- Student metrics
    unique_students INTEGER DEFAULT 0,
    returning_students INTEGER DEFAULT 0,
    new_students INTEGER DEFAULT 0,
    
    -- Subject breakdown (JSONB for flexibility)
    subject_breakdown JSONB DEFAULT '{}',
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT unique_tutor_period UNIQUE (tutor_id, period_start, period_end, period_type)
);

-- =====================================================
-- STUDENT PROGRESS TRACKING
-- =====================================================

-- Table to track student learning goals
CREATE TABLE IF NOT EXISTS student_learning_goals (
    goal_id SERIAL PRIMARY KEY,
    student_id INTEGER NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    subject_area VARCHAR(50) NOT NULL,
    goal_title VARCHAR(200) NOT NULL,
    goal_description TEXT,
    target_date DATE,
    
    -- Progress tracking
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'paused', 'cancelled')),
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    
    -- Relationships
    created_by INTEGER REFERENCES users(user_id),
    assigned_tutor_id INTEGER REFERENCES users(user_id),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Table to track student progress metrics
CREATE TABLE IF NOT EXISTS student_progress_metrics (
    metric_id SERIAL PRIMARY KEY,
    student_id INTEGER NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    subject_area VARCHAR(50) NOT NULL,
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    
    -- Session metrics
    total_sessions INTEGER DEFAULT 0,
    attended_sessions INTEGER DEFAULT 0,
    session_hours DECIMAL(10,2) DEFAULT 0,
    
    -- Performance indicators
    performance_score DECIMAL(5,2), -- Custom scoring based on various factors
    improvement_rate DECIMAL(5,2), -- Percentage improvement
    engagement_score DECIMAL(5,2), -- Based on participation, homework, etc.
    
    -- Milestone tracking
    milestones_achieved INTEGER DEFAULT 0,
    milestones_total INTEGER DEFAULT 0,
    
    -- Tutor feedback summary
    avg_tutor_rating DECIMAL(3,2),
    feedback_summary JSONB DEFAULT '{}',
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT unique_student_subject_period UNIQUE (student_id, subject_area, period_start, period_end)
);

-- =====================================================
-- PLATFORM ANALYTICS
-- =====================================================

-- Table for platform-wide analytics
CREATE TABLE IF NOT EXISTS platform_analytics (
    analytics_id SERIAL PRIMARY KEY,
    metric_date DATE NOT NULL,
    metric_type VARCHAR(50) NOT NULL,
    
    -- User metrics
    active_clients INTEGER DEFAULT 0,
    active_tutors INTEGER DEFAULT 0,
    active_students INTEGER DEFAULT 0,
    new_registrations INTEGER DEFAULT 0,
    
    -- Session metrics
    total_bookings INTEGER DEFAULT 0,
    completed_sessions INTEGER DEFAULT 0,
    booking_conversion_rate DECIMAL(5,2),
    average_session_duration DECIMAL(10,2),
    
    -- Financial metrics
    daily_revenue DECIMAL(10,2) DEFAULT 0,
    average_session_value DECIMAL(10,2),
    
    -- Subject popularity (JSONB for flexibility)
    subject_distribution JSONB DEFAULT '{}',
    
    -- Time slot analysis
    peak_hours JSONB DEFAULT '{}',
    time_slot_distribution JSONB DEFAULT '{}',
    
    -- Geographic data
    geographic_distribution JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT unique_platform_metric_date UNIQUE (metric_date, metric_type)
);

-- =====================================================
-- SESSION FEEDBACK AND RATINGS
-- =====================================================

-- Enhanced session feedback table
CREATE TABLE IF NOT EXISTS session_feedback (
    feedback_id SERIAL PRIMARY KEY,
    appointment_id INTEGER NOT NULL REFERENCES appointments(appointment_id) ON DELETE CASCADE,
    given_by INTEGER NOT NULL REFERENCES users(user_id),
    given_to INTEGER NOT NULL REFERENCES users(user_id),
    role_type VARCHAR(20) NOT NULL CHECK (role_type IN ('tutor_to_student', 'student_to_tutor', 'parent_to_tutor')),
    
    -- Ratings
    overall_rating INTEGER CHECK (overall_rating >= 1 AND overall_rating <= 5),
    punctuality_rating INTEGER CHECK (punctuality_rating >= 1 AND punctuality_rating <= 5),
    preparation_rating INTEGER CHECK (preparation_rating >= 1 AND preparation_rating <= 5),
    communication_rating INTEGER CHECK (communication_rating >= 1 AND communication_rating <= 5),
    
    -- Feedback
    feedback_text TEXT,
    improvement_areas TEXT[],
    positive_highlights TEXT[],
    
    -- Response tracking
    requires_followup BOOLEAN DEFAULT FALSE,
    followup_completed BOOLEAN DEFAULT FALSE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT unique_appointment_feedback UNIQUE (appointment_id, given_by, given_to)
);

-- =====================================================
-- ANALYTICS SNAPSHOTS
-- =====================================================

-- Table to store pre-calculated analytics snapshots for performance
CREATE TABLE IF NOT EXISTS analytics_snapshots (
    snapshot_id SERIAL PRIMARY KEY,
    snapshot_type VARCHAR(50) NOT NULL,
    snapshot_date DATE NOT NULL,
    entity_type VARCHAR(50) NOT NULL, -- 'tutor', 'student', 'platform'
    entity_id INTEGER, -- NULL for platform-wide snapshots
    
    -- Snapshot data
    metrics JSONB NOT NULL,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    processing_time_ms INTEGER,
    
    CONSTRAINT unique_snapshot UNIQUE (snapshot_type, snapshot_date, entity_type, entity_id)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Tutor performance indexes
CREATE INDEX idx_tutor_metrics_tutor_period ON tutor_performance_metrics(tutor_id, period_start, period_end);
CREATE INDEX idx_tutor_metrics_period_type ON tutor_performance_metrics(period_type, period_start);
CREATE INDEX idx_tutor_metrics_rating ON tutor_performance_metrics(average_rating) WHERE average_rating IS NOT NULL;

-- Student progress indexes
CREATE INDEX idx_student_goals_student ON student_learning_goals(student_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_student_goals_tutor ON student_learning_goals(assigned_tutor_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_student_goals_status ON student_learning_goals(status) WHERE deleted_at IS NULL;
CREATE INDEX idx_student_metrics_student_subject ON student_progress_metrics(student_id, subject_area);

-- Platform analytics indexes
CREATE INDEX idx_platform_analytics_date ON platform_analytics(metric_date);
CREATE INDEX idx_platform_analytics_type_date ON platform_analytics(metric_type, metric_date);

-- Session feedback indexes
CREATE INDEX idx_session_feedback_appointment ON session_feedback(appointment_id);
CREATE INDEX idx_session_feedback_given_to ON session_feedback(given_to, created_at);
CREATE INDEX idx_session_feedback_rating ON session_feedback(overall_rating) WHERE overall_rating IS NOT NULL;
CREATE INDEX idx_session_feedback_followup ON session_feedback(requires_followup) WHERE requires_followup = TRUE;

-- Analytics snapshots indexes
CREATE INDEX idx_analytics_snapshots_lookup ON analytics_snapshots(snapshot_type, entity_type, entity_id, snapshot_date);
CREATE INDEX idx_analytics_snapshots_date ON analytics_snapshots(snapshot_date);

-- =====================================================
-- VIEWS FOR COMMON QUERIES
-- =====================================================

-- View for tutor leaderboard
CREATE OR REPLACE VIEW tutor_leaderboard AS
SELECT 
    u.user_id,
    u.first_name,
    u.last_name,
    u.profile_picture_url,
    tpm.average_rating,
    tpm.total_sessions,
    tpm.completed_sessions,
    tpm.total_revenue,
    tpm.unique_students,
    RANK() OVER (ORDER BY tpm.average_rating DESC NULLS LAST) as rating_rank,
    RANK() OVER (ORDER BY tpm.total_sessions DESC) as session_rank,
    RANK() OVER (ORDER BY tpm.total_revenue DESC) as revenue_rank
FROM tutor_performance_metrics tpm
JOIN users u ON tpm.tutor_id = u.user_id
WHERE tpm.period_type = 'monthly'
    AND tpm.period_start = DATE_TRUNC('month', CURRENT_DATE)
    AND u.deleted_at IS NULL;

-- View for student progress summary
CREATE OR REPLACE VIEW student_progress_summary AS
SELECT 
    s.student_id,
    s.subject_area,
    s.attended_sessions,
    s.session_hours,
    s.performance_score,
    s.improvement_rate,
    COUNT(g.goal_id) as active_goals,
    SUM(CASE WHEN g.status = 'completed' THEN 1 ELSE 0 END) as completed_goals
FROM student_progress_metrics s
LEFT JOIN student_learning_goals g ON s.student_id = g.student_id 
    AND s.subject_area = g.subject_area
    AND g.deleted_at IS NULL
WHERE s.period_end >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY s.student_id, s.subject_area, s.attended_sessions, 
         s.session_hours, s.performance_score, s.improvement_rate;

-- =====================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

-- Function to update timestamps
CREATE OR REPLACE FUNCTION update_analytics_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply triggers
CREATE TRIGGER update_tutor_metrics_timestamp
    BEFORE UPDATE ON tutor_performance_metrics
    FOR EACH ROW
    EXECUTE FUNCTION update_analytics_timestamp();

CREATE TRIGGER update_student_goals_timestamp
    BEFORE UPDATE ON student_learning_goals
    FOR EACH ROW
    EXECUTE FUNCTION update_analytics_timestamp();

CREATE TRIGGER update_student_metrics_timestamp
    BEFORE UPDATE ON student_progress_metrics
    FOR EACH ROW
    EXECUTE FUNCTION update_analytics_timestamp();

CREATE TRIGGER update_session_feedback_timestamp
    BEFORE UPDATE ON session_feedback
    FOR EACH ROW
    EXECUTE FUNCTION update_analytics_timestamp();

-- =====================================================
-- INITIAL DATA AND PERMISSIONS
-- =====================================================

-- Grant permissions
GRANT SELECT ON ALL TABLES IN SCHEMA public TO tutoraide_read;
GRANT SELECT, INSERT, UPDATE ON tutor_performance_metrics TO tutoraide_write;
GRANT SELECT, INSERT, UPDATE ON student_learning_goals TO tutoraide_write;
GRANT SELECT, INSERT, UPDATE ON student_progress_metrics TO tutoraide_write;
GRANT SELECT, INSERT, UPDATE ON platform_analytics TO tutoraide_write;
GRANT SELECT, INSERT, UPDATE ON session_feedback TO tutoraide_write;
GRANT SELECT, INSERT, UPDATE ON analytics_snapshots TO tutoraide_write;

-- Grant sequence permissions
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO tutoraide_write;

-- Add comments for documentation
COMMENT ON TABLE tutor_performance_metrics IS 'Stores aggregated performance metrics for tutors by various time periods';
COMMENT ON TABLE student_learning_goals IS 'Tracks learning goals and milestones for students';
COMMENT ON TABLE student_progress_metrics IS 'Stores progress metrics and performance indicators for students';
COMMENT ON TABLE platform_analytics IS 'Platform-wide analytics and business metrics';
COMMENT ON TABLE session_feedback IS 'Detailed feedback and ratings for tutoring sessions';
COMMENT ON TABLE analytics_snapshots IS 'Pre-calculated analytics snapshots for performance optimization';