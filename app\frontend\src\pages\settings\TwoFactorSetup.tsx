import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import Button from '../../components/common/Button';
import { Card } from '../../components/common/Card';
import { Shield, Smartphone, Mail, Check, Copy, AlertCircle } from 'lucide-react';
import toast from 'react-hot-toast';
import QRCode from 'qrcode.react';
import { motion } from 'framer-motion';
import clsx from 'clsx';

interface TwoFactorMethod {
  id: 'authenticator' | 'sms' | 'email';
  name: string;
  description: string;
  icon: React.ReactNode;
  enabled: boolean;
  preferred: boolean;
}

const TwoFactorSetup: React.FC = () => {
  const { t } = useTranslation();
  const [selectedMethod, setSelectedMethod] = useState<string | null>(null);
  const [secretKey, setSecretKey] = useState('JBSWY3DPEHPK3PXP'); // Mock secret
  const [verificationCode, setVerificationCode] = useState('');
  const [isVerifying, setIsVerifying] = useState(false);
  const [methods, setMethods] = useState<TwoFactorMethod[]>([
    {
      id: 'authenticator',
      name: t('auth.twoFactor.methods.authenticator'),
      description: t('auth.twoFactor.authenticatorDescription'),
      icon: <Shield className="w-5 h-5" />,
      enabled: false,
      preferred: false,
    },
    {
      id: 'sms',
      name: t('auth.twoFactor.methods.sms'),
      description: t('auth.twoFactor.smsDescription'),
      icon: <Smartphone className="w-5 h-5" />,
      enabled: true,
      preferred: true,
    },
    {
      id: 'email',
      name: t('auth.twoFactor.methods.email'),
      description: t('auth.twoFactor.emailDescription'),
      icon: <Mail className="w-5 h-5" />,
      enabled: false,
      preferred: false,
    },
  ]);

  const handleCopySecret = () => {
    navigator.clipboard.writeText(secretKey);
    toast.success(t('common.copiedToClipboard'));
  };

  const handleToggleMethod = async (methodId: string) => {
    const method = methods.find(m => m.id === methodId);
    if (!method) return;

    if (method.enabled) {
      // Disable method
      setMethods(prev => prev.map(m => 
        m.id === methodId ? { ...m, enabled: false, preferred: false } : m
      ));
      toast.success(t('auth.twoFactor.methodDisabled', { method: method.name }));
    } else {
      // Enable method - show setup
      setSelectedMethod(methodId);
    }
  };

  const handleSetPreferred = (methodId: string) => {
    setMethods(prev => prev.map(m => ({
      ...m,
      preferred: m.id === methodId && m.enabled,
    })));
    toast.success(t('auth.twoFactor.preferredMethodSet'));
  };

  const handleVerifyAndEnable = async () => {
    if (!verificationCode || verificationCode.length !== 6) {
      toast.error(t('auth.twoFactor.invalidCode'));
      return;
    }

    setIsVerifying(true);
    try {
      // Mock verification
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setMethods(prev => prev.map(m => 
        m.id === selectedMethod ? { ...m, enabled: true } : m
      ));
      
      toast.success(t('auth.twoFactor.methodEnabled'));
      setSelectedMethod(null);
      setVerificationCode('');
    } catch (error) {
      toast.error(t('auth.twoFactor.verificationFailed'));
    } finally {
      setIsVerifying(false);
    }
  };

  const renderSetupModal = () => {
    if (!selectedMethod) return null;

    const method = methods.find(m => m.id === selectedMethod);
    if (!method) return null;

    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <div 
          className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          onClick={() => setSelectedMethod(null)}
        />
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="relative bg-white rounded-2xl shadow-elevated p-6 max-w-md w-full"
        >
          <h3 className="text-lg font-semibold text-text-primary mb-4">
            {t('auth.twoFactor.setupTitle', { method: method.name })}
          </h3>

          {selectedMethod === 'authenticator' && (
            <div className="space-y-4">
              <div className="bg-background-secondary p-4 rounded-xl">
                <p className="text-sm text-text-secondary mb-4">
                  {t('auth.twoFactor.scanQRCode')}
                </p>
                <div className="bg-white p-4 rounded-lg flex justify-center">
                  <QRCode value={`otpauth://totp/TutorAide:<EMAIL>?secret=${secretKey}&issuer=TutorAide`} />
                </div>
              </div>

              <div className="space-y-2">
                <p className="text-sm text-text-secondary">
                  {t('auth.twoFactor.manualEntry')}
                </p>
                <div className="flex items-center gap-2">
                  <code className="flex-1 px-3 py-2 bg-background-secondary rounded-lg text-sm font-mono">
                    {secretKey}
                  </code>
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={handleCopySecret}
                  >
                    <Copy className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          )}

          {selectedMethod === 'sms' && (
            <div className="space-y-4">
              <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
                <div className="flex gap-3">
                  <AlertCircle className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
                  <div>
                    <p className="text-sm text-blue-800">
                      {t('auth.twoFactor.smsVerification')}
                    </p>
                    <p className="text-sm text-blue-600 mt-1">
                      +1 (514) ***-**23
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {selectedMethod === 'email' && (
            <div className="space-y-4">
              <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
                <div className="flex gap-3">
                  <AlertCircle className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
                  <div>
                    <p className="text-sm text-blue-800">
                      {t('auth.twoFactor.emailVerification')}
                    </p>
                    <p className="text-sm text-blue-600 mt-1">
                      u***@example.com
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="mt-6 space-y-4">
            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                {t('auth.twoFactor.enterCode')}
              </label>
              <input
                type="text"
                value={verificationCode}
                onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                placeholder="000000"
                className="w-full px-4 py-3 bg-background-secondary rounded-xl text-center text-xl font-mono tracking-widest"
              />
            </div>

            <div className="flex gap-3">
              <Button
                variant="secondary"
                onClick={() => setSelectedMethod(null)}
                className="flex-1"
              >
                {t('common.cancel')}
              </Button>
              <Button
                onClick={handleVerifyAndEnable}
                disabled={verificationCode.length !== 6}
                loading={isVerifying}
                className="flex-1"
              >
                {t('auth.twoFactor.enableMethod')}
              </Button>
            </div>
          </div>
        </motion.div>
      </div>
    );
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-text-primary">
          {t('auth.twoFactor.title')}
        </h1>
        <p className="text-text-secondary mt-2">
          {t('auth.twoFactor.subtitle')}
        </p>
      </div>

      <Card className="p-6 mb-6">
        <div className="flex items-start gap-4">
          <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center flex-shrink-0">
            <Shield className="w-6 h-6 text-green-600" />
          </div>
          <div className="flex-1">
            <h3 className="font-medium text-text-primary mb-1">
              {t('auth.twoFactor.statusEnabled')}
            </h3>
            <p className="text-sm text-text-secondary">
              {t('auth.twoFactor.statusDescription')}
            </p>
          </div>
        </div>
      </Card>

      <div className="space-y-4">
        {methods.map((method) => (
          <Card key={method.id} className="p-6">
            <div className="flex items-start gap-4">
              <div className={clsx(
                'w-10 h-10 rounded-xl flex items-center justify-center flex-shrink-0',
                method.enabled ? 'bg-green-100' : 'bg-gray-100'
              )}>
                {React.cloneElement(method.icon as React.ReactElement, {
                  className: clsx(
                    'w-5 h-5',
                    method.enabled ? 'text-green-600' : 'text-gray-500'
                  )
                })}
              </div>

              <div className="flex-1">
                <div className="flex items-center justify-between mb-1">
                  <h3 className="font-medium text-text-primary">
                    {method.name}
                  </h3>
                  <div className="flex items-center gap-2">
                    {method.enabled && method.preferred && (
                      <span className="px-2 py-1 bg-accent-red/10 text-accent-red text-xs font-medium rounded-lg">
                        {t('auth.twoFactor.preferred')}
                      </span>
                    )}
                    {method.enabled && (
                      <Check className="w-5 h-5 text-green-600" />
                    )}
                  </div>
                </div>
                <p className="text-sm text-text-secondary mb-3">
                  {method.description}
                </p>
                <div className="flex gap-2">
                  <Button
                    variant={method.enabled ? 'secondary' : 'primary'}
                    size="sm"
                    onClick={() => handleToggleMethod(method.id)}
                  >
                    {method.enabled ? t('auth.twoFactor.disable') : t('auth.twoFactor.setup')}
                  </Button>
                  {method.enabled && !method.preferred && (
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={() => handleSetPreferred(method.id)}
                    >
                      {t('auth.twoFactor.makePreferred')}
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>

      <Card className="mt-6 p-6 bg-orange-50 border-orange-200">
        <div className="flex gap-3">
          <AlertCircle className="w-5 h-5 text-orange-600 flex-shrink-0 mt-0.5" />
          <div>
            <h3 className="font-medium text-orange-800 mb-1">
              {t('auth.twoFactor.backupCodesTitle')}
            </h3>
            <p className="text-sm text-orange-700 mb-3">
              {t('auth.twoFactor.backupCodesDescription')}
            </p>
            <Button variant="secondary" size="sm">
              {t('auth.twoFactor.generateBackupCodes')}
            </Button>
          </div>
        </div>
      </Card>

      {renderSetupModal()}
    </div>
  );
};

export default TwoFactorSetup;