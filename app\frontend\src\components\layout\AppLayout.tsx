import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { Sidebar } from './Sidebar';
import { Header } from './Header';
import { GlobalSearch } from '../search/GlobalSearch';
import { SentryTest } from '../SentryTest';

interface AppLayoutProps {
  children: React.ReactNode;
}

export const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  const { isAuthenticated } = useAuth();
  const location = window.location.pathname;
  const [isSearchOpen, setIsSearchOpen] = useState(false);

  // Don't show layout for unauthenticated users or test pages
  if (!isAuthenticated || location.startsWith('/test') || location.startsWith('/simple') || location.startsWith('/startup') || location.startsWith('/route-checker') || location.startsWith('/reset-password')) {
    return <>{children}</>;
  }

  return (
    <div className="min-h-screen bg-background-secondary">
      <Sidebar onSearchClick={() => setIsSearchOpen(true)} />
      <div className="lg:pl-72">
        <Header onSearchClick={() => setIsSearchOpen(true)} />
        <main className="pt-16 px-6 py-8">
          <div className="max-w-7xl mx-auto">
            {children}
          </div>
        </main>
      </div>
      <GlobalSearch 
        isOpen={isSearchOpen} 
        onClose={() => setIsSearchOpen(false)} 
      />
      {import.meta.env.DEV && <SentryTest />}
    </div>
  );
};