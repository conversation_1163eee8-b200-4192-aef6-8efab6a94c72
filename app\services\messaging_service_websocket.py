"""
Enhanced Messaging Service with WebSocket Integration

Extends the base messaging service to include real-time WebSocket notifications
for new messages, typing indicators, and presence updates.
"""

from typing import Dict, Any
import asyncpg
from app.services.messaging_service import MessagingService
from app.services.websocket_messaging_service import websocket_messaging_service
from app.core.logging import Tu<PERSON><PERSON>ideLogger

logger = TutorAideLogger.get_logger(__name__)


class WebSocketMessagingService(MessagingService):
    """Enhanced messaging service with WebSocket integration"""
    
    async def send_message(
        self,
        db: asyncpg.Connection,
        conversation_id: int,
        sender_id: int,
        sender_type: str,
        content: str,
        message_type: str = 'text'
    ) -> Dict[str, Any]:
        """Send a message and notify via WebSocket"""
        
        # Call parent method to create message
        message = await super().send_message(
            db, conversation_id, sender_id, sender_type, content, message_type
        )
        
        # Get sender name for WebSocket notification
        sender_name = await self._get_sender_name(db, sender_id, sender_type)
        
        # Prepare message data for WebSocket
        message_data = {
            'message_id': message['message_id'],
            'conversation_id': conversation_id,
            'sender_id': sender_id,
            'sender_type': sender_type,
            'sender_name': sender_name,
            'content': content,
            'message_type': message_type,
            'status': message.get('delivery_status', 'sent'),
            'created_at': message['created_at'].isoformat() if message.get('created_at') else None,
        }
        
        # Notify via WebSocket
        try:
            await websocket_messaging_service.notify_new_message(
                conversation_id, 
                message_data
            )
            logger.info(f"WebSocket notification sent for message {message['message_id']}")
        except Exception as e:
            logger.error(f"Failed to send WebSocket notification: {e}")
            # Don't fail the message send if WebSocket fails
        
        return message
    
    async def mark_message_as_read(
        self,
        db: asyncpg.Connection,
        message_id: int,
        user_id: int
    ) -> None:
        """Mark a message as read and notify via WebSocket"""
        
        # Update message status
        await db.execute("""
            UPDATE messaging_messages
            SET is_read = TRUE,
                read_at = NOW(),
                delivery_status = 'read'
            WHERE message_id = $1 AND sender_id != $2
        """, message_id, user_id)
        
        # Get conversation ID for WebSocket notification
        conversation_id = await db.fetchval(
            "SELECT conversation_id FROM messaging_messages WHERE message_id = $1",
            message_id
        )
        
        if conversation_id:
            # Notify via WebSocket
            try:
                await websocket_messaging_service.notify_conversation_update(
                    conversation_id,
                    {
                        'type': 'message_read',
                        'message_id': message_id,
                        'read_by': user_id,
                        'read_at': 'now'
                    }
                )
            except Exception as e:
                logger.error(f"Failed to send read receipt via WebSocket: {e}")
    
    async def update_conversation_status(
        self,
        db: asyncpg.Connection,
        conversation_id: int,
        status: str,
        updated_by: int
    ) -> None:
        """Update conversation status and notify via WebSocket"""
        
        # Update status
        await db.execute("""
            UPDATE messaging_conversations
            SET status = $1,
                updated_at = NOW()
            WHERE conversation_id = $2
        """, status, conversation_id)
        
        # Notify via WebSocket
        try:
            await websocket_messaging_service.notify_conversation_update(
                conversation_id,
                {
                    'type': 'status_change',
                    'status': status,
                    'updated_by': updated_by
                }
            )
        except Exception as e:
            logger.error(f"Failed to send status update via WebSocket: {e}")
    
    async def _get_sender_name(
        self,
        db: asyncpg.Connection,
        sender_id: int,
        sender_type: str
    ) -> str:
        """Get the display name for a sender"""
        
        if sender_type == 'client':
            result = await db.fetchrow("""
                SELECT cp.first_name, cp.last_name
                FROM client_profiles cp
                JOIN user_accounts u ON u.user_id = cp.user_id
                WHERE u.user_id = $1
            """, sender_id)
            if result:
                return f"{result['first_name']} {result['last_name']}"
        
        elif sender_type == 'tutor':
            result = await db.fetchrow("""
                SELECT tp.first_name, tp.last_name
                FROM tutor_profiles tp
                JOIN user_accounts u ON u.user_id = tp.user_id
                WHERE u.user_id = $1
            """, sender_id)
            if result:
                return f"{result['first_name']} {result['last_name']}"
        
        elif sender_type == 'manager':
            result = await db.fetchrow(
                "SELECT email FROM user_accounts WHERE user_id = $1",
                sender_id
            )
            if result:
                return result['email'].split('@')[0]  # Use email prefix as name
        
        return 'Unknown User'
    
    async def _mark_messages_as_read(
        self,
        db: asyncpg.Connection,
        conversation_id: int,
        user_id: int
    ) -> None:
        """Mark all unread messages in a conversation as read"""
        
        # Get unread message IDs before marking as read
        unread_messages = await db.fetch("""
            SELECT message_id FROM messaging_messages
            WHERE conversation_id = $1 
            AND sender_id != $2 
            AND is_read = FALSE
        """, conversation_id, user_id)
        
        if unread_messages:
            # Mark messages as read
            await db.execute("""
                UPDATE messaging_messages
                SET is_read = TRUE,
                    read_at = NOW(),
                    delivery_status = 'read'
                WHERE conversation_id = $1 
                AND sender_id != $2 
                AND is_read = FALSE
            """, conversation_id, user_id)
            
            # Reset unread count
            await db.execute("""
                UPDATE messaging_conversations
                SET unread_count = 0
                WHERE conversation_id = $1
            """, conversation_id)
            
            # Notify via WebSocket for each message
            try:
                for msg in unread_messages:
                    await websocket_messaging_service.notify_conversation_update(
                        conversation_id,
                        {
                            'type': 'message_read',
                            'message_id': msg['message_id'],
                            'read_by': user_id
                        }
                    )
            except Exception as e:
                logger.error(f"Failed to send bulk read receipts via WebSocket: {e}")