import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Input } from '../../common/Input';
import Button from '../../common/Button';
import { Select } from '../../common/Select';
import { 
  User, 
  Phone, 
  Calendar,
  Globe,
  FileText,
  Languages,
  Save
} from 'lucide-react';

interface PersonalInfoFormProps {
  profile: {
    bio: string;
    languages_spoken: string[];
    teaching_languages: string[];
    user: {
      first_name: string;
      last_name: string;
      email: string;
      phone_number: string;
      date_of_birth: string;
    };
  };
  onSave: (data: any) => Promise<void>;
}

export const PersonalInfoForm: React.FC<PersonalInfoFormProps> = ({
  profile,
  onSave
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    first_name: profile.user.first_name,
    last_name: profile.user.last_name,
    phone_number: profile.user.phone_number,
    bio: profile.bio || '',
    languages_spoken: profile.languages_spoken || [],
    teaching_languages: profile.teaching_languages || []
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const languageOptions = [
    { value: 'english', label: t('tutor.languages.english') },
    { value: 'french', label: t('tutor.languages.french') },
    { value: 'spanish', label: t('tutor.languages.spanish') },
    { value: 'mandarin', label: t('tutor.languages.mandarin') },
    { value: 'arabic', label: t('tutor.languages.arabic') },
    { value: 'hindi', label: t('tutor.languages.hindi') },
    { value: 'portuguese', label: t('tutor.languages.portuguese') },
    { value: 'russian', label: t('tutor.languages.russian') },
    { value: 'german', label: t('tutor.languages.german') },
    { value: 'italian', label: t('tutor.languages.italian') },
    { value: 'other', label: t('tutor.languages.other') }
  ];

  const formatPhoneNumber = (value: string) => {
    const cleaned = value.replace(/\D/g, '');
    if (cleaned.length <= 3) {
      return cleaned;
    } else if (cleaned.length <= 6) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3)}`;
    } else {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6, 10)}`;
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.first_name.trim()) {
      newErrors.first_name = t('tutor.profile.errors.firstNameRequired');
    }
    
    if (!formData.last_name.trim()) {
      newErrors.last_name = t('tutor.profile.errors.lastNameRequired');
    }
    
    const phoneDigits = formData.phone_number.replace(/\D/g, '');
    if (phoneDigits.length !== 10) {
      newErrors.phone_number = t('tutor.profile.errors.invalidPhone');
    }
    
    if (!formData.bio.trim()) {
      newErrors.bio = t('tutor.profile.errors.bioRequired');
    } else if (formData.bio.length < 100) {
      newErrors.bio = t('tutor.profile.errors.bioTooShort');
    }
    
    if (formData.languages_spoken.length === 0) {
      newErrors.languages_spoken = t('tutor.profile.errors.languagesRequired');
    }
    
    if (formData.teaching_languages.length === 0) {
      newErrors.teaching_languages = t('tutor.profile.errors.teachingLanguagesRequired');
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setLoading(true);
    try {
      await onSave(formData);
    } catch (error) {
      // Error is handled in parent component
    } finally {
      setLoading(false);
    }
  };

  const handleLanguageToggle = (language: string, field: 'languages_spoken' | 'teaching_languages') => {
    const currentLanguages = formData[field];
    const newLanguages = currentLanguages.includes(language)
      ? currentLanguages.filter(l => l !== language)
      : [...currentLanguages, language];
    
    setFormData({
      ...formData,
      [field]: newLanguages
    });
  };

  return (
    <form onSubmit={handleSubmit} className="p-6 space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-text-primary mb-4">
          {t('tutor.profile.personalInfo')}
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              {t('tutor.profile.fields.firstName')}
            </label>
            <Input
              leftIcon={<User className="w-4 h-4" />}
              value={formData.first_name}
              onChange={(e) => setFormData({ ...formData, first_name: e.target.value })}
              placeholder={t('tutor.profile.placeholders.firstName')}
              error={errors.first_name}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              {t('tutor.profile.fields.lastName')}
            </label>
            <Input
              leftIcon={<User className="w-4 h-4" />}
              value={formData.last_name}
              onChange={(e) => setFormData({ ...formData, last_name: e.target.value })}
              placeholder={t('tutor.profile.placeholders.lastName')}
              error={errors.last_name}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              {t('tutor.profile.fields.email')}
            </label>
            <Input
              type="email"
              leftIcon={<User className="w-4 h-4" />}
              value={profile.user.email}
              disabled
              className="bg-gray-50"
            />
            <p className="text-sm text-text-secondary mt-1">
              {t('tutor.profile.emailCannotBeChanged')}
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              {t('tutor.profile.fields.phoneNumber')}
            </label>
            <Input
              type="tel"
              leftIcon={<Phone className="w-4 h-4" />}
              value={formData.phone_number}
              onChange={(e) => setFormData({ ...formData, phone_number: formatPhoneNumber(e.target.value) })}
              placeholder="(*************"
              error={errors.phone_number}
            />
          </div>
        </div>

        {/* Bio */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-text-primary mb-2">
            {t('tutor.profile.fields.bio')}
          </label>
          <textarea
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent-red focus:border-accent-red resize-none"
            rows={6}
            value={formData.bio}
            onChange={(e) => setFormData({ ...formData, bio: e.target.value })}
            placeholder={t('tutor.profile.placeholders.bio')}
          />
          {errors.bio && (
            <p className="text-sm text-red-600 mt-1">{errors.bio}</p>
          )}
          <p className="text-sm text-text-secondary mt-1">
            {t('tutor.profile.bioHelp', { chars: formData.bio.length })}
          </p>
        </div>

        {/* Languages Spoken */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-text-primary mb-2">
            <Languages className="w-4 h-4 inline mr-2" />
            {t('tutor.profile.fields.languagesSpoken')}
          </label>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {languageOptions.map((lang) => (
              <label
                key={lang.value}
                className="flex items-center gap-2 p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50"
              >
                <input
                  type="checkbox"
                  checked={formData.languages_spoken.includes(lang.value)}
                  onChange={() => handleLanguageToggle(lang.value, 'languages_spoken')}
                  className="w-4 h-4 text-accent-red focus:ring-accent-red/20 border-gray-300 rounded"
                />
                <span className="text-sm text-text-primary">{lang.label}</span>
              </label>
            ))}
          </div>
          {errors.languages_spoken && (
            <p className="text-sm text-red-600 mt-1">{errors.languages_spoken}</p>
          )}
        </div>

        {/* Teaching Languages */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-text-primary mb-2">
            <Globe className="w-4 h-4 inline mr-2" />
            {t('tutor.profile.fields.teachingLanguages')}
          </label>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {languageOptions.slice(0, 2).map((lang) => (
              <label
                key={lang.value}
                className="flex items-center gap-2 p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50"
              >
                <input
                  type="checkbox"
                  checked={formData.teaching_languages.includes(lang.value)}
                  onChange={() => handleLanguageToggle(lang.value, 'teaching_languages')}
                  className="w-4 h-4 text-accent-red focus:ring-accent-red/20 border-gray-300 rounded"
                />
                <span className="text-sm text-text-primary">{lang.label}</span>
              </label>
            ))}
          </div>
          {errors.teaching_languages && (
            <p className="text-sm text-red-600 mt-1">{errors.teaching_languages}</p>
          )}
          <p className="text-sm text-text-secondary mt-1">
            {t('tutor.profile.teachingLanguagesHelp')}
          </p>
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-end pt-4 border-t border-gray-200">
        <Button
          type="submit"
          loading={loading}
          leftIcon={<Save className="w-4 h-4" />}
        >
          {t('common.save')}
        </Button>
      </div>
    </form>
  );
};