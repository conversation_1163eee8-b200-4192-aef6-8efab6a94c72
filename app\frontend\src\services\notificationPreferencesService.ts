import api from './api';

// Notification Types and Interfaces
export enum NotificationChannel {
  EMAIL = 'email',
  SMS = 'sms',
  PUSH = 'push',
  IN_APP = 'in_app'
}

export enum NotificationCategory {
  // Appointment notifications
  APPOINTMENT_REMINDER = 'appointment_reminder',
  APPOINTMENT_CONFIRMATION = 'appointment_confirmation',
  APPOINTMENT_CANCELLATION = 'appointment_cancellation',
  APPOINTMENT_RESCHEDULING = 'appointment_rescheduling',
  
  // Billing notifications
  INVOICE_GENERATED = 'invoice_generated',
  PAYMENT_RECEIVED = 'payment_received',
  PAYMENT_FAILED = 'payment_failed',
  PAYMENT_REMINDER = 'payment_reminder',
  
  // Tutor notifications
  TUTOR_ASSIGNMENT = 'tutor_assignment',
  TUTOR_APPLICATION_STATUS = 'tutor_application_status',
  TUTOR_PAYMENT_APPROVED = 'tutor_payment_approved',
  SCHEDULE_CHANGE = 'schedule_change',
  
  // Communication
  NEW_MESSAGE = 'new_message',
  BROADCAST_MESSAGE = 'broadcast_message',
  
  // System notifications
  ACCOUNT_ACTIVITY = 'account_activity',
  SECURITY_ALERT = 'security_alert',
  SYSTEM_UPDATE = 'system_update',
  
  // Marketing (optional)
  PROMOTIONAL = 'promotional',
  NEWSLETTER = 'newsletter',
  TIPS_AND_UPDATES = 'tips_and_updates'
}

export interface NotificationPreference {
  category: NotificationCategory;
  channels: NotificationChannel[];
  is_enabled: boolean;
  frequency?: 'immediate' | 'hourly' | 'daily' | 'weekly';
  quiet_hours?: {
    enabled: boolean;
    start_time: string; // HH:MM format
    end_time: string; // HH:MM format
    timezone: string;
  };
}

export interface NotificationSettings {
  user_id: number;
  preferences: NotificationPreference[];
  global_settings: {
    email_enabled: boolean;
    sms_enabled: boolean;
    push_enabled: boolean;
    in_app_enabled: boolean;
    language: 'en' | 'fr';
    timezone: string;
  };
  contact_info: {
    email?: string;
    phone_number?: string;
    push_token?: string;
  };
  quiet_hours: {
    enabled: boolean;
    start_time: string;
    end_time: string;
    days: string[]; // ['monday', 'tuesday', etc.]
  };
  last_updated: string;
}

export interface UpdatePreferencesRequest {
  preferences?: NotificationPreference[];
  global_settings?: Partial<NotificationSettings['global_settings']>;
  quiet_hours?: Partial<NotificationSettings['quiet_hours']>;
}

export interface TestNotificationRequest {
  category: NotificationCategory;
  channel: NotificationChannel;
}

export interface NotificationStats {
  total_sent: number;
  by_channel: Record<NotificationChannel, number>;
  by_category: Record<NotificationCategory, number>;
  last_30_days: Array<{
    date: string;
    count: number;
  }>;
}

export interface UnsubscribeRequest {
  token: string;
  categories?: NotificationCategory[];
  all?: boolean;
}

class NotificationPreferencesService {
  /**
   * Get notification preferences
   */
  async getPreferences(): Promise<NotificationSettings> {
    try {
      const response = await api.get<NotificationSettings>('/notification-preferences');
      return response.data;
    } catch (error: any) {
      console.error('Error fetching notification preferences:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch preferences');
    }
  }

  /**
   * Update notification preferences
   */
  async updatePreferences(request: UpdatePreferencesRequest): Promise<NotificationSettings> {
    try {
      const response = await api.put<NotificationSettings>('/notification-preferences', request);
      return response.data;
    } catch (error: any) {
      console.error('Error updating notification preferences:', error);
      throw new Error(error.response?.data?.detail || 'Failed to update preferences');
    }
  }

  /**
   * Update single category preference
   */
  async updateCategoryPreference(
    category: NotificationCategory,
    preference: Partial<NotificationPreference>
  ): Promise<NotificationSettings> {
    try {
      const response = await api.patch<NotificationSettings>(
        `/notification-preferences/category/${category}`,
        preference
      );
      return response.data;
    } catch (error: any) {
      console.error('Error updating category preference:', error);
      throw new Error(error.response?.data?.detail || 'Failed to update category preference');
    }
  }

  /**
   * Enable/disable channel globally
   */
  async toggleChannel(channel: NotificationChannel, enabled: boolean): Promise<NotificationSettings> {
    try {
      const response = await api.post<NotificationSettings>('/notification-preferences/channel', {
        channel,
        enabled
      });
      return response.data;
    } catch (error: any) {
      console.error('Error toggling channel:', error);
      throw new Error(error.response?.data?.detail || 'Failed to toggle channel');
    }
  }

  /**
   * Test notification delivery
   */
  async testNotification(request: TestNotificationRequest): Promise<{ message: string }> {
    try {
      const response = await api.post<{ message: string }>('/notification-preferences/test', request);
      return response.data;
    } catch (error: any) {
      console.error('Error testing notification:', error);
      throw new Error(error.response?.data?.detail || 'Failed to send test notification');
    }
  }

  /**
   * Get notification statistics
   */
  async getStats(): Promise<NotificationStats> {
    try {
      const response = await api.get<NotificationStats>('/notification-preferences/stats');
      return response.data;
    } catch (error: any) {
      console.error('Error fetching notification stats:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch stats');
    }
  }

  /**
   * Unsubscribe via token (for email links)
   */
  async unsubscribe(request: UnsubscribeRequest): Promise<{ message: string }> {
    try {
      const response = await api.post<{ message: string }>('/notification-preferences/unsubscribe', request);
      return response.data;
    } catch (error: any) {
      console.error('Error unsubscribing:', error);
      throw new Error(error.response?.data?.detail || 'Failed to unsubscribe');
    }
  }

  /**
   * Update push notification token
   */
  async updatePushToken(token: string): Promise<{ message: string }> {
    try {
      const response = await api.post<{ message: string }>('/notification-preferences/push-token', { token });
      return response.data;
    } catch (error: any) {
      console.error('Error updating push token:', error);
      throw new Error(error.response?.data?.detail || 'Failed to update push token');
    }
  }

  /**
   * Get default preferences for new users
   */
  getDefaultPreferences(): NotificationPreference[] {
    const essentialCategories = [
      NotificationCategory.APPOINTMENT_REMINDER,
      NotificationCategory.APPOINTMENT_CONFIRMATION,
      NotificationCategory.APPOINTMENT_CANCELLATION,
      NotificationCategory.INVOICE_GENERATED,
      NotificationCategory.PAYMENT_RECEIVED,
      NotificationCategory.NEW_MESSAGE,
      NotificationCategory.SECURITY_ALERT
    ];

    const optionalCategories = [
      NotificationCategory.PROMOTIONAL,
      NotificationCategory.NEWSLETTER,
      NotificationCategory.TIPS_AND_UPDATES
    ];

    const preferences: NotificationPreference[] = [];

    // Essential notifications - enabled by default
    essentialCategories.forEach(category => {
      preferences.push({
        category,
        channels: [NotificationChannel.EMAIL, NotificationChannel.PUSH],
        is_enabled: true,
        frequency: 'immediate'
      });
    });

    // Optional notifications - disabled by default
    optionalCategories.forEach(category => {
      preferences.push({
        category,
        channels: [NotificationChannel.EMAIL],
        is_enabled: false,
        frequency: 'weekly'
      });
    });

    return preferences;
  }

  /**
   * Format category name for display
   */
  formatCategoryName(category: NotificationCategory): string {
    const names: Record<NotificationCategory, string> = {
      [NotificationCategory.APPOINTMENT_REMINDER]: 'Appointment Reminders',
      [NotificationCategory.APPOINTMENT_CONFIRMATION]: 'Appointment Confirmations',
      [NotificationCategory.APPOINTMENT_CANCELLATION]: 'Appointment Cancellations',
      [NotificationCategory.APPOINTMENT_RESCHEDULING]: 'Appointment Rescheduling',
      [NotificationCategory.INVOICE_GENERATED]: 'New Invoices',
      [NotificationCategory.PAYMENT_RECEIVED]: 'Payment Confirmations',
      [NotificationCategory.PAYMENT_FAILED]: 'Payment Failures',
      [NotificationCategory.PAYMENT_REMINDER]: 'Payment Reminders',
      [NotificationCategory.TUTOR_ASSIGNMENT]: 'Tutor Assignments',
      [NotificationCategory.TUTOR_APPLICATION_STATUS]: 'Application Updates',
      [NotificationCategory.TUTOR_PAYMENT_APPROVED]: 'Payment Approvals',
      [NotificationCategory.SCHEDULE_CHANGE]: 'Schedule Changes',
      [NotificationCategory.NEW_MESSAGE]: 'New Messages',
      [NotificationCategory.BROADCAST_MESSAGE]: 'Broadcast Messages',
      [NotificationCategory.ACCOUNT_ACTIVITY]: 'Account Activity',
      [NotificationCategory.SECURITY_ALERT]: 'Security Alerts',
      [NotificationCategory.SYSTEM_UPDATE]: 'System Updates',
      [NotificationCategory.PROMOTIONAL]: 'Promotional Offers',
      [NotificationCategory.NEWSLETTER]: 'Newsletter',
      [NotificationCategory.TIPS_AND_UPDATES]: 'Tips & Updates'
    };

    return names[category] || category;
  }

  /**
   * Get category description
   */
  getCategoryDescription(category: NotificationCategory): string {
    const descriptions: Record<NotificationCategory, string> = {
      [NotificationCategory.APPOINTMENT_REMINDER]: '24-hour and 1-hour reminders before appointments',
      [NotificationCategory.APPOINTMENT_CONFIRMATION]: 'Confirmations when appointments are booked',
      [NotificationCategory.APPOINTMENT_CANCELLATION]: 'Alerts when appointments are cancelled',
      [NotificationCategory.APPOINTMENT_RESCHEDULING]: 'Notifications about schedule changes',
      [NotificationCategory.INVOICE_GENERATED]: 'New invoices ready for payment',
      [NotificationCategory.PAYMENT_RECEIVED]: 'Confirmation of successful payments',
      [NotificationCategory.PAYMENT_FAILED]: 'Alerts about failed payment attempts',
      [NotificationCategory.PAYMENT_REMINDER]: 'Reminders for overdue payments',
      [NotificationCategory.TUTOR_ASSIGNMENT]: 'New student assignments for tutors',
      [NotificationCategory.TUTOR_APPLICATION_STATUS]: 'Updates on tutor application status',
      [NotificationCategory.TUTOR_PAYMENT_APPROVED]: 'Weekly payment approval notifications',
      [NotificationCategory.SCHEDULE_CHANGE]: 'Changes to your tutoring schedule',
      [NotificationCategory.NEW_MESSAGE]: 'Direct messages from users',
      [NotificationCategory.BROADCAST_MESSAGE]: 'Important announcements from TutorAide',
      [NotificationCategory.ACCOUNT_ACTIVITY]: 'Login alerts and profile changes',
      [NotificationCategory.SECURITY_ALERT]: 'Security-related notifications',
      [NotificationCategory.SYSTEM_UPDATE]: 'Platform updates and maintenance',
      [NotificationCategory.PROMOTIONAL]: 'Special offers and promotions',
      [NotificationCategory.NEWSLETTER]: 'Monthly newsletter with tips and updates',
      [NotificationCategory.TIPS_AND_UPDATES]: 'Educational tips and platform features'
    };

    return descriptions[category] || '';
  }

  /**
   * Group categories by type
   */
  getCategoryGroups(): Record<string, NotificationCategory[]> {
    return {
      'Appointments': [
        NotificationCategory.APPOINTMENT_REMINDER,
        NotificationCategory.APPOINTMENT_CONFIRMATION,
        NotificationCategory.APPOINTMENT_CANCELLATION,
        NotificationCategory.APPOINTMENT_RESCHEDULING
      ],
      'Billing & Payments': [
        NotificationCategory.INVOICE_GENERATED,
        NotificationCategory.PAYMENT_RECEIVED,
        NotificationCategory.PAYMENT_FAILED,
        NotificationCategory.PAYMENT_REMINDER
      ],
      'Tutor Updates': [
        NotificationCategory.TUTOR_ASSIGNMENT,
        NotificationCategory.TUTOR_APPLICATION_STATUS,
        NotificationCategory.TUTOR_PAYMENT_APPROVED,
        NotificationCategory.SCHEDULE_CHANGE
      ],
      'Messages': [
        NotificationCategory.NEW_MESSAGE,
        NotificationCategory.BROADCAST_MESSAGE
      ],
      'Account & Security': [
        NotificationCategory.ACCOUNT_ACTIVITY,
        NotificationCategory.SECURITY_ALERT,
        NotificationCategory.SYSTEM_UPDATE
      ],
      'Marketing': [
        NotificationCategory.PROMOTIONAL,
        NotificationCategory.NEWSLETTER,
        NotificationCategory.TIPS_AND_UPDATES
      ]
    };
  }
}

export const notificationPreferencesService = new NotificationPreferencesService();