"""
Search API endpoints for user and entity search functionality.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, Query, HTTPException, status
from fastapi.responses import JSONResponse

from app.services.search_service import SearchService
from app.models.search_models import (
    UserSearchRequest, UserSearchResponse, 
    AutocompleteRequest, AutocompleteResponse,
    SearchEntityType, SearchSortBy, SearchSortOrder, UserSearchResult
)
from app.models.user_models import User, UserRoleType
from app.core.dependencies import get_current_active_user, require_manager_or_client, require_manager_or_tutor
from app.core.exceptions import ValidationError, AuthorizationError, error_handler
from app.core.logging import TutorAideLogger

router = APIRouter(prefix="/search", tags=["search"])
logger = TutorAideLogger("search_api")


@router.post("/users", response_model=UserSearchResponse)
async def search_users(
    request: UserSearchRequest,
    current_user: User = Depends(get_current_active_user),
    search_service: SearchService = Depends(lambda: SearchService())
):
    """
    Search users with advanced filtering and pagination.
    
    Supports searching across users, clients, tutors, and dependants with:
    - Text search across names, emails, phone numbers
    - Role-based filtering
    - Geographic filtering for tutors
    - Verification status filtering
    - Date range filtering
    - Pagination and sorting
    """
    try:
        response = await search_service.search_users(request, current_user)
        
        logger.info(
            f"User search performed by {current_user.user_id}",
            extra={
                "user_id": current_user.user_id,
                "query": request.query,
                "entity_type": request.entity_type.value,
                "results_count": len(response.results),
                "duration_ms": response.search_duration_ms
            }
        )
        
        return response
        
    except (ValidationError, AuthorizationError) as e:
        logger.warning(f"Search validation error: {str(e)}")
        raise HTTPException(status_code=e.http_status_code, detail=str(e))
    except Exception as e:
        logger.error(f"Search error: {str(e)}")
        error_response = error_handler.handle_exception(e)
        raise HTTPException(
            status_code=error_response.get('http_status_code', 500),
            detail=error_response
        )


@router.get("/autocomplete", response_model=AutocompleteResponse)
async def get_autocomplete_suggestions(
    query: str = Query(..., min_length=1, max_length=50, description="Search query for autocomplete"),
    entity_type: SearchEntityType = Query(SearchEntityType.ALL, description="Type of entity to search"),
    limit: int = Query(10, ge=1, le=50, description="Maximum suggestions to return"),
    current_user: User = Depends(get_current_active_user),
    search_service: SearchService = Depends(lambda: SearchService())
):
    """
    Get autocomplete suggestions for search queries.
    
    Returns suggestions based on:
    - User names (first name, last name, full name)
    - Client information (names, phone numbers)
    - Tutor information (names, specializations)
    - Dependant names
    
    Results are filtered based on user permissions and privacy settings.
    """
    try:
        request = AutocompleteRequest(
            query=query,
            entity_type=entity_type,
            limit=limit
        )
        
        response = await search_service.get_autocomplete_suggestions(request, current_user)
        
        logger.debug(
            f"Autocomplete performed by {current_user.user_id}",
            extra={
                "user_id": current_user.user_id,
                "query": query,
                "entity_type": entity_type.value,
                "suggestions_count": len(response.suggestions)
            }
        )
        
        return response
        
    except (ValidationError, AuthorizationError) as e:
        logger.warning(f"Autocomplete validation error: {str(e)}")
        raise HTTPException(status_code=e.http_status_code, detail=str(e))
    except Exception as e:
        logger.error(f"Autocomplete error: {str(e)}")
        error_response = error_handler.handle_exception(e)
        raise HTTPException(
            status_code=error_response.get('http_status_code', 500),
            detail=error_response
        )


@router.get("/quick", response_model=List[UserSearchResult])
async def quick_search(
    q: str = Query(..., min_length=2, max_length=100, description="Quick search query"),
    limit: int = Query(5, ge=1, le=20, description="Maximum results to return"),
    current_user: User = Depends(get_current_active_user),
    search_service: SearchService = Depends(lambda: SearchService())
):
    """
    Quick search for global search bar.
    
    Returns a small number of most relevant results across all entity types.
    Optimized for fast response times and immediate user feedback.
    """
    try:
        results = await search_service.get_quick_search_results(q, current_user, limit)
        
        logger.debug(
            f"Quick search performed by {current_user.user_id}",
            extra={
                "user_id": current_user.user_id,
                "query": q,
                "results_count": len(results)
            }
        )
        
        return results
        
    except (ValidationError, AuthorizationError) as e:
        logger.warning(f"Quick search validation error: {str(e)}")
        raise HTTPException(status_code=e.http_status_code, detail=str(e))
    except Exception as e:
        logger.error(f"Quick search error: {str(e)}")
        error_response = error_handler.handle_exception(e)
        raise HTTPException(
            status_code=error_response.get('http_status_code', 500),
            detail=error_response
        )


@router.get("/clients", response_model=List[UserSearchResult])
async def search_clients(
    query: Optional[str] = Query(None, max_length=100, description="Search query for clients"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
    sort_by: SearchSortBy = Query(SearchSortBy.NAME, description="Sort field"),
    sort_order: SearchSortOrder = Query(SearchSortOrder.ASC, description="Sort order"),
    active_only: bool = Query(True, description="Include only active clients"),
    current_user: User = Depends(require_manager_or_client),
    search_service: SearchService = Depends(lambda: SearchService())
):
    """
    Search clients with filtering options.
    
    Restricted to managers and clients only.
    Clients can only see their own family members and dependants.
    """
    try:
        request = UserSearchRequest(
            query=query,
            entity_type=SearchEntityType.CLIENT,
            page=page,
            page_size=page_size,
            sort_by=sort_by,
            sort_order=sort_order,
            active_only=active_only,
            roles=[UserRoleType.CLIENT]
        )
        
        response = await search_service.search_users(request, current_user)
        return response.results
        
    except (ValidationError, AuthorizationError) as e:
        logger.warning(f"Client search validation error: {str(e)}")
        raise HTTPException(status_code=e.http_status_code, detail=str(e))
    except Exception as e:
        logger.error(f"Client search error: {str(e)}")
        error_response = error_handler.handle_exception(e)
        raise HTTPException(
            status_code=error_response.get('http_status_code', 500),
            detail=error_response
        )


@router.get("/tutors", response_model=List[UserSearchResult])
async def search_tutors(
    query: Optional[str] = Query(None, max_length=100, description="Search query for tutors"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
    sort_by: SearchSortBy = Query(SearchSortBy.NAME, description="Sort field"),
    sort_order: SearchSortOrder = Query(SearchSortOrder.ASC, description="Sort order"),
    verified_only: bool = Query(True, description="Include only verified tutors"),
    postal_code: Optional[str] = Query(None, description="Filter by postal code"),
    radius_km: Optional[int] = Query(None, ge=1, le=100, description="Search radius in km"),
    current_user: User = Depends(get_current_active_user),
    search_service: SearchService = Depends(lambda: SearchService())
):
    """
    Search tutors with geographic and verification filtering.
    
    Available to all authenticated users.
    Non-managers can only see verified tutors.
    Supports location-based filtering for finding nearby tutors.
    """
    try:
        # Non-managers can only search verified tutors
        if UserRoleType.MANAGER not in current_user.roles:
            verified_only = True
        
        request = UserSearchRequest(
            query=query,
            entity_type=SearchEntityType.TUTOR,
            page=page,
            page_size=page_size,
            sort_by=sort_by,
            sort_order=sort_order,
            verified_only=verified_only,
            postal_code=postal_code,
            radius_km=radius_km,
            active_only=True,
            roles=[UserRoleType.TUTOR]
        )
        
        response = await search_service.search_users(request, current_user)
        return response.results
        
    except (ValidationError, AuthorizationError) as e:
        logger.warning(f"Tutor search validation error: {str(e)}")
        raise HTTPException(status_code=e.http_status_code, detail=str(e))
    except Exception as e:
        logger.error(f"Tutor search error: {str(e)}")
        error_response = error_handler.handle_exception(e)
        raise HTTPException(
            status_code=error_response.get('http_status_code', 500),
            detail=error_response
        )


@router.get("/tutors/nearby", response_model=List[UserSearchResult])
async def search_nearby_tutors(
    postal_code: str = Query(..., description="Canadian postal code for location search"),
    radius_km: int = Query(25, ge=1, le=100, description="Search radius in kilometers"),
    subject: Optional[str] = Query(None, description="Subject specialization filter"),
    limit: int = Query(10, ge=1, le=50, description="Maximum tutors to return"),
    current_user: User = Depends(get_current_active_user),
    search_service: SearchService = Depends(lambda: SearchService())
):
    """
    Find tutors near a specific location.
    
    Searches for verified tutors within the specified radius of a postal code.
    Useful for clients looking for in-person tutoring services.
    Returns tutors sorted by relevance and distance.
    """
    try:
        results = await search_service.search_nearby_tutors(
            postal_code, radius_km, current_user, subject, limit
        )
        
        logger.info(
            f"Nearby tutor search by {current_user.user_id}",
            extra={
                "user_id": current_user.user_id,
                "postal_code": postal_code,
                "radius_km": radius_km,
                "subject": subject,
                "results_count": len(results)
            }
        )
        
        return results
        
    except (ValidationError, AuthorizationError) as e:
        logger.warning(f"Nearby tutor search validation error: {str(e)}")
        raise HTTPException(status_code=e.http_status_code, detail=str(e))
    except Exception as e:
        logger.error(f"Nearby tutor search error: {str(e)}")
        error_response = error_handler.handle_exception(e)
        raise HTTPException(
            status_code=error_response.get('http_status_code', 500),
            detail=error_response
        )


@router.get("/managers", response_model=List[UserSearchResult])
async def search_managers(
    query: Optional[str] = Query(None, max_length=100, description="Search query for managers"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
    current_user: User = Depends(require_manager_or_tutor),
    search_service: SearchService = Depends(lambda: SearchService())
):
    """
    Search managers.
    
    Restricted to managers and tutors only.
    Useful for finding managers for escalations or approvals.
    """
    try:
        results = await search_service.search_by_role(
            UserRoleType.MANAGER, current_user, query, page_size
        )
        
        # Apply pagination manually since search_by_role doesn't support it
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paginated_results = results[start_idx:end_idx]
        
        return paginated_results
        
    except (ValidationError, AuthorizationError) as e:
        logger.warning(f"Manager search validation error: {str(e)}")
        raise HTTPException(status_code=e.http_status_code, detail=str(e))
    except Exception as e:
        logger.error(f"Manager search error: {str(e)}")
        error_response = error_handler.handle_exception(e)
        raise HTTPException(
            status_code=error_response.get('http_status_code', 500),
            detail=error_response
        )


@router.get("/dependants", response_model=List[UserSearchResult])
async def search_dependants(
    query: Optional[str] = Query(None, max_length=100, description="Search query for dependants"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
    current_user: User = Depends(require_manager_or_client),
    search_service: SearchService = Depends(lambda: SearchService())
):
    """
    Search dependants (children/students).
    
    Restricted to managers and clients only.
    Clients can only see their own dependants and family members.
    """
    try:
        request = UserSearchRequest(
            query=query,
            entity_type=SearchEntityType.DEPENDANT,
            page=page,
            page_size=page_size,
            sort_by=SearchSortBy.NAME,
            sort_order=SearchSortOrder.ASC,
            active_only=True
        )
        
        response = await search_service.search_users(request, current_user)
        return response.results
        
    except (ValidationError, AuthorizationError) as e:
        logger.warning(f"Dependant search validation error: {str(e)}")
        raise HTTPException(status_code=e.http_status_code, detail=str(e))
    except Exception as e:
        logger.error(f"Dependant search error: {str(e)}")
        error_response = error_handler.handle_exception(e)
        raise HTTPException(
            status_code=error_response.get('http_status_code', 500),
            detail=error_response
        )


@router.get("/health")
async def search_health_check():
    """
    Health check endpoint for search service.
    """
    return JSONResponse(
        content={
            "status": "healthy",
            "service": "search",
            "timestamp": "2025-01-06T23:30:00Z"
        },
        status_code=status.HTTP_200_OK
    )