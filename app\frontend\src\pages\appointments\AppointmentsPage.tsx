import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { 
  Calendar as CalendarIcon, Plus, Filter, Download, 
  ChevronLeft, ChevronRight, Clock, MapPin, User,
  AlertCircle, CheckCircle, XCircle, Edit, Trash2,
  RefreshCw, FileText, Search
} from 'lucide-react';
import { format, addDays, startOfWeek, endOfWeek, startOfMonth, endOfMonth, isSameDay, isToday, addMonths, subMonths } from 'date-fns';
import { appointmentService, Appointment, AppointmentListParams } from '../../services/appointmentService';
import { AppointmentStatus, LocationType } from '../../types/appointment';
import { Card } from '../../components/common/Card';
import Button from '../../components/common/Button';
import { Badge } from '../../components/common/Badge';
import { Select } from '../../components/common/Select';
import { Input } from '../../components/common/Input';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import { EmptyState } from '../../components/common/EmptyState';
import { useAuth } from '../../contexts/AuthContext';
import { UserRoleType } from '../../types/auth';
import { hasPermission } from '../../utils/permissions';
import { AppointmentModal } from '../../components/modals/AppointmentModal';
import AppointmentDetailsModal from '../../components/modals/AppointmentDetailsModal';
import { Dropdown } from '../../components/common/Dropdown';
import toast from 'react-hot-toast';

type ViewType = 'day' | 'week' | 'month' | 'list';

const AppointmentsPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { user } = useAuth();
  
  // State
  const [viewType, setViewType] = useState<ViewType>('week');
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAppointmentModal, setShowAppointmentModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(null);
  const [selectedDateForNew, setSelectedDateForNew] = useState<Date | null>(null);
  
  // Filters
  const [filters, setFilters] = useState<AppointmentListParams>({
    start_date: format(startOfWeek(new Date()), 'yyyy-MM-dd'),
    end_date: format(endOfWeek(new Date()), 'yyyy-MM-dd'),
    status: undefined,
    tutor_id: undefined,
    client_id: undefined
  });
  const [searchTerm, setSearchTerm] = useState('');
  
  // Permissions
  const isManager = user?.activeRole === UserRoleType.MANAGER;
  const isTutor = user?.activeRole === UserRoleType.TUTOR;
  const isClient = user?.activeRole === UserRoleType.CLIENT;
  const canCreateAppointments = hasPermission(user?.activeRole, 'createAppointments');
  const canEditAppointments = hasPermission(user?.activeRole, 'editAppointments');
  const canDeleteAppointments = hasPermission(user?.activeRole, 'deleteAppointments');

  // Load appointments
  useEffect(() => {
    loadAppointments();
  }, [filters, viewType, selectedDate]);

  const loadAppointments = async () => {
    try {
      setLoading(true);
      
      // Calculate date range based on view type
      let startDate: Date, endDate: Date;
      
      switch (viewType) {
        case 'day':
          startDate = selectedDate;
          endDate = selectedDate;
          break;
        case 'week':
          startDate = startOfWeek(selectedDate);
          endDate = endOfWeek(selectedDate);
          break;
        case 'month':
          startDate = startOfMonth(selectedDate);
          endDate = endOfMonth(selectedDate);
          break;
        case 'list':
        default:
          startDate = new Date();
          endDate = addDays(new Date(), 30);
      }

      const params: AppointmentListParams = {
        ...filters,
        start_date: format(startDate, 'yyyy-MM-dd'),
        end_date: format(endDate, 'yyyy-MM-dd'),
        page_size: 100
      };

      // Add role-based filters
      if (isTutor && user?.profileData?.tutor_id) {
        params.tutor_id = user.profileData.tutor_id;
      } else if (isClient && user?.profileData?.client_id) {
        params.client_id = user.profileData.client_id;
      }

      const response = await appointmentService.listAppointments(params);
      setAppointments(response.items || []);
    } catch (error) {
      console.error('Error loading appointments:', error);
      toast.error(t('appointments.errors.loadAppointments', 'Failed to load appointments'));
    } finally {
      setLoading(false);
    }
  };

  const handleCreateAppointment = (date?: Date) => {
    setSelectedDateForNew(date || null);
    setSelectedAppointment(null);
    setShowAppointmentModal(true);
  };

  const handleEditAppointment = (appointment: Appointment) => {
    setSelectedAppointment(appointment);
    setShowAppointmentModal(true);
  };

  const handleViewDetails = (appointment: Appointment) => {
    setSelectedAppointment(appointment);
    setShowDetailsModal(true);
  };

  const handleDeleteAppointment = async (appointment: Appointment) => {
    if (!window.confirm(t('appointments.confirmDelete', 'Are you sure you want to delete this appointment?'))) {
      return;
    }

    const reason = window.prompt(t('appointments.deleteReason', 'Please provide a reason for deletion:'));
    if (!reason) return;

    try {
      await appointmentService.cancelAppointment(appointment.appointment_id, reason);
      toast.success(t('appointments.deleteSuccess', 'Appointment deleted successfully'));
      loadAppointments();
    } catch (error) {
      console.error('Error deleting appointment:', error);
      toast.error(t('appointments.deleteError', 'Failed to delete appointment'));
    }
  };

  const handleConfirmAppointment = async (appointment: Appointment) => {
    try {
      await appointmentService.confirmAppointment(appointment.appointment_id, {
        confirmed: true,
        confirmation_notes: 'Confirmed by tutor'
      });
      toast.success(t('appointments.confirmSuccess', 'Appointment confirmed successfully'));
      loadAppointments();
    } catch (error) {
      console.error('Error confirming appointment:', error);
      toast.error(t('appointments.confirmError', 'Failed to confirm appointment'));
    }
  };

  const handleCompleteAppointment = async (appointment: Appointment) => {
    try {
      await appointmentService.completeAppointment(appointment.appointment_id, {
        notes: 'Session completed successfully'
      });
      toast.success(t('appointments.completeSuccess', 'Appointment marked as completed'));
      loadAppointments();
    } catch (error) {
      console.error('Error completing appointment:', error);
      toast.error(t('appointments.completeError', 'Failed to complete appointment'));
    }
  };

  const getStatusBadgeVariant = (status: AppointmentStatus) => {
    switch (status) {
      case AppointmentStatus.SCHEDULED:
        return 'secondary';
      case AppointmentStatus.CONFIRMED:
        return 'info';
      case AppointmentStatus.IN_PROGRESS:
        return 'warning';
      case AppointmentStatus.COMPLETED:
        return 'success';
      case AppointmentStatus.CANCELLED:
        return 'error';
      case AppointmentStatus.NO_SHOW:
        return 'error';
      default:
        return 'secondary';
    }
  };

  const getLocationIcon = (type: LocationType) => {
    switch (type) {
      case LocationType.ONLINE:
        return '💻';
      case LocationType.IN_PERSON:
        return '🏠';
      case LocationType.LIBRARY:
        return '📚';
      case LocationType.HYBRID:
        return '🔄';
      default:
        return '📍';
    }
  };

  const navigateDate = (direction: 'prev' | 'next') => {
    switch (viewType) {
      case 'day':
        setSelectedDate(direction === 'prev' 
          ? addDays(selectedDate, -1) 
          : addDays(selectedDate, 1)
        );
        break;
      case 'week':
        setSelectedDate(direction === 'prev'
          ? addDays(selectedDate, -7)
          : addDays(selectedDate, 7)
        );
        break;
      case 'month':
        setSelectedDate(direction === 'prev'
          ? subMonths(selectedDate, 1)
          : addMonths(selectedDate, 1)
        );
        break;
    }
  };

  const getDateRangeText = () => {
    switch (viewType) {
      case 'day':
        return format(selectedDate, 'EEEE, MMMM d, yyyy');
      case 'week':
        const weekStart = startOfWeek(selectedDate);
        const weekEnd = endOfWeek(selectedDate);
        return `${format(weekStart, 'MMM d')} - ${format(weekEnd, 'MMM d, yyyy')}`;
      case 'month':
        return format(selectedDate, 'MMMM yyyy');
      case 'list':
        return 'Upcoming Appointments';
    }
  };

  const renderAppointmentCard = (appointment: Appointment) => {
    const canEdit = canEditAppointments && (isManager || (isTutor && appointment.tutor_id === user?.profileData?.tutor_id));
    const canDelete = canDeleteAppointments && isManager;
    const canConfirm = isTutor && appointment.tutor_id === user?.profileData?.tutor_id && 
                      appointment.status === AppointmentStatus.SCHEDULED && !appointment.confirmed_by_tutor;
    const canComplete = (isManager || (isTutor && appointment.tutor_id === user?.profileData?.tutor_id)) &&
                       appointment.status === AppointmentStatus.CONFIRMED;

    return (
      <Card key={appointment.appointment_id} className="p-4 hover:shadow-lg transition-shadow cursor-pointer">
        <div onClick={() => handleViewDetails(appointment)}>
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center gap-2">
              <Badge variant={getStatusBadgeVariant(appointment.status)} size="sm">
                {appointment.status}
              </Badge>
              {appointment.confirmed_by_tutor && (
                <CheckCircle className="w-4 h-4 text-green-500" />
              )}
              {appointment.recurring_appointment_id && (
                <RefreshCw className="w-4 h-4 text-blue-500" />
              )}
            </div>
            
            <Dropdown
              trigger={
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zM12 13a1 1 0 110-2 1 1 0 010 2zM12 20a1 1 0 110-2 1 1 0 010 2z" />
                  </svg>
                </Button>
              }
              items={[
                ...(canEdit ? [{
                  label: t('common.edit', 'Edit'),
                  onClick: () => handleEditAppointment(appointment),
                  icon: <Edit className="w-4 h-4" />
                }] : []),
                ...(canConfirm ? [{
                  label: t('appointments.confirm', 'Confirm'),
                  onClick: () => handleConfirmAppointment(appointment),
                  icon: <CheckCircle className="w-4 h-4" />
                }] : []),
                ...(canComplete ? [{
                  label: t('appointments.markComplete', 'Mark Complete'),
                  onClick: () => handleCompleteAppointment(appointment),
                  icon: <CheckCircle className="w-4 h-4" />
                }] : []),
                ...(canDelete ? [{
                  label: t('common.delete', 'Delete'),
                  onClick: () => handleDeleteAppointment(appointment),
                  variant: 'danger',
                  icon: <Trash2 className="w-4 h-4" />
                }] : [])
              ]}
            />
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm">
              <Clock className="w-4 h-4 text-gray-400" />
              <span className="font-medium">
                {appointmentService.formatAppointmentTime(appointment)}
              </span>
              <span className="text-gray-500">({appointment.duration} min)</span>
            </div>

            <div className="flex items-center gap-2 text-sm">
              <User className="w-4 h-4 text-gray-400" />
              <span>{appointment.tutor_name}</span>
              <span className="text-gray-500">→</span>
              <span>{appointment.client_name}</span>
              {appointment.dependant_name && (
                <span className="text-gray-500">({appointment.dependant_name})</span>
              )}
            </div>

            <div className="flex items-center gap-2 text-sm">
              <MapPin className="w-4 h-4 text-gray-400" />
              <span>{getLocationIcon(appointment.location_type)}</span>
              <span>{appointment.location_type.replace('_', ' ')}</span>
            </div>

            <div className="flex items-center gap-2 text-sm">
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                {appointment.subject_area}
              </span>
              {appointment.session_type !== 'individual' && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {appointment.session_type}
                </span>
              )}
            </div>
          </div>
        </div>
      </Card>
    );
  };

  const renderCalendarView = () => {
    if (viewType === 'list') {
      return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {appointments.map(renderAppointmentCard)}
        </div>
      );
    }

    // Calendar grid view (simplified for now)
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="text-center py-8">
          <CalendarIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">Calendar view coming soon</p>
          <p className="text-sm text-gray-400 mt-2">Use list view to see appointments</p>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-semibold text-gray-900">
              {t('appointments.title', 'Appointments')}
            </h1>
            
            <div className="flex items-center gap-3">
              {/* View Type Selector */}
              <div className="flex bg-gray-100 rounded-lg p-1">
                {(['day', 'week', 'month', 'list'] as ViewType[]).map((type) => (
                  <button
                    key={type}
                    onClick={() => setViewType(type)}
                    className={`px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${
                      viewType === type
                        ? 'bg-white text-gray-900 shadow-sm'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    {t(`appointments.view.${type}`, type.charAt(0).toUpperCase() + type.slice(1))}
                  </button>
                ))}
              </div>

              {canCreateAppointments && (
                <Button
                  variant="primary"
                  leftIcon={<Plus className="w-4 h-4" />}
                  onClick={() => handleCreateAppointment()}
                >
                  {t('appointments.create', 'New Appointment')}
                </Button>
              )}
            </div>
          </div>

          {/* Date Navigation */}
          {viewType !== 'list' && (
            <div className="flex items-center justify-between mt-4">
              <div className="flex items-center gap-2">
                <button
                  onClick={() => navigateDate('prev')}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <ChevronLeft className="w-5 h-5" />
                </button>
                <button
                  onClick={() => setSelectedDate(new Date())}
                  className="px-3 py-1.5 text-sm font-medium hover:bg-gray-100 rounded-lg transition-colors"
                >
                  {t('common.today', 'Today')}
                </button>
                <button
                  onClick={() => navigateDate('next')}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <ChevronRight className="w-5 h-5" />
                </button>
              </div>

              <h2 className="text-lg font-medium text-gray-900">
                {getDateRangeText()}
              </h2>

              <div className="w-32" /> {/* Spacer for alignment */}
            </div>
          )}
        </div>
      </div>

      {/* Filters */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <Card className="p-4">
          <div className="flex flex-wrap gap-4 items-center">
            <div className="flex items-center gap-2">
              <Filter className="w-5 h-5 text-gray-400" />
              <span className="text-sm font-medium text-gray-700">
                {t('common.filters', 'Filters')}:
              </span>
            </div>

            <div className="flex-1 max-w-xs">
              <Input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder={t('appointments.search', 'Search appointments...')}
                icon={Search}
              />
            </div>

            <Select
              value={filters.status || ''}
              onChange={(e) => setFilters({ ...filters, status: e.target.value as AppointmentStatus || undefined })}
              className="w-40"
              options={[
                { value: '', label: t('appointments.allStatuses', 'All Statuses') },
                { value: AppointmentStatus.SCHEDULED, label: 'Scheduled' },
                { value: AppointmentStatus.CONFIRMED, label: 'Confirmed' },
                { value: AppointmentStatus.IN_PROGRESS, label: 'In Progress' },
                { value: AppointmentStatus.COMPLETED, label: 'Completed' },
                { value: AppointmentStatus.CANCELLED, label: 'Cancelled' },
                { value: AppointmentStatus.NO_SHOW, label: 'No Show' }
              ]}
            />

            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setFilters({
                  start_date: format(startOfWeek(new Date()), 'yyyy-MM-dd'),
                  end_date: format(endOfWeek(new Date()), 'yyyy-MM-dd')
                });
                setSearchTerm('');
              }}
            >
              {t('common.clearFilters', 'Clear Filters')}
            </Button>
          </div>
        </Card>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        {appointments.length === 0 ? (
          <Card>
            <EmptyState
              icon={<CalendarIcon className="w-12 h-12 text-gray-400" />}
              title={t('appointments.noAppointments', 'No appointments found')}
              description={t('appointments.noAppointmentsDescription', 'There are no appointments scheduled for this period.')}
              action={
                canCreateAppointments ? (
                  <Button
                    variant="primary"
                    leftIcon={<Plus className="w-4 h-4" />}
                    onClick={() => handleCreateAppointment()}
                  >
                    {t('appointments.create', 'New Appointment')}
                  </Button>
                ) : undefined
              }
            />
          </Card>
        ) : (
          renderCalendarView()
        )}
      </div>

      {/* Modals */}
      <AppointmentModal
        isOpen={showAppointmentModal}
        onClose={() => {
          setShowAppointmentModal(false);
          setSelectedAppointment(null);
          setSelectedDateForNew(null);
        }}
        appointment={selectedAppointment}
        onSuccess={loadAppointments}
        defaultDate={selectedDateForNew || undefined}
      />

      {selectedAppointment && (
        <AppointmentDetailsModal
          isOpen={showDetailsModal}
          onClose={() => {
            setShowDetailsModal(false);
            setSelectedAppointment(null);
          }}
          appointment={{
            id: selectedAppointment.appointment_id,
            clientName: selectedAppointment.client_name || '',
            dependantName: selectedAppointment.dependant_name,
            tutorName: selectedAppointment.tutor_name || '',
            subject: selectedAppointment.subject_area,
            status: selectedAppointment.status,
            startTime: `${selectedAppointment.scheduled_date}T${selectedAppointment.start_time}`,
            endTime: `${selectedAppointment.scheduled_date}T${selectedAppointment.end_time}`,
            duration: selectedAppointment.duration,
            location: {
              type: selectedAppointment.location_type,
              details: selectedAppointment.location_details?.address || selectedAppointment.location_details?.meeting_link
            },
            notes: selectedAppointment.notes
          }}
          onEdit={() => {
            setShowDetailsModal(false);
            handleEditAppointment(selectedAppointment);
          }}
        />
      )}
    </div>
  );
};

export default AppointmentsPage;