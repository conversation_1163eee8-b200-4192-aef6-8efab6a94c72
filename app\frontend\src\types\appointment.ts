/**
 * Appointment type definitions
 */

export enum AppointmentStatus {
  SCHEDULED = 'scheduled',
  CONFIRMED = 'confirmed',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  NO_SHOW = 'no_show'
}

export enum LocationType {
  ONLINE = 'online',
  IN_PERSON = 'in_person',
  LIBRARY = 'library',
  HYBRID = 'hybrid'
}

export enum PackageType {
  INDIVIDUAL = 'individual',
  GROUP = 'group',
  TECFEE = 'tecfee_program'
}

export interface AppointmentConflict {
  type: 'appointment' | 'time_off' | 'availability';
  message: string;
  conflictingId?: number;
}

export interface TutorAvailability {
  availability_id: number;
  tutor_id: number;
  day_of_week: number;
  start_time: string;
  end_time: string;
  is_available: boolean;
  hourly_rate?: number;
  effective_date: string;
  expiry_date?: string;
}

export interface TimeOffRequest {
  request_id: number;
  tutor_id: number;
  start_date: string;
  end_date: string;
  start_time?: string;
  end_time?: string;
  reason: string;
  request_type: 'vacation' | 'sick_leave' | 'personal' | 'holiday' | 'emergency' | 'other';
  status: 'pending' | 'approved' | 'denied' | 'cancelled';
  approved_by?: number;
  approved_at?: string;
}