"""
Service for managing tutor profiles, availability, and verification.
"""

from datetime import datetime, date, time
from typing import Optional, List, Dict, Any
from decimal import Decimal

from app.database.repositories.tutor_repository import TutorRepository
from app.database.repositories.user_repository import UserRepository
from app.models.tutor_models import (
    TutorProfile,
    TutorAvailability,
    TutorTimeOff,
    TutorServiceRate
)
from app.models.auth_models import UserRole
from app.core.exceptions import (
    ResourceNotFoundError,
    ForbiddenError,
    ValidationError,
    BusinessLogicError
)
from app.core.logging import TutorAideLogger
from app.config.database import DatabaseManager


logger = TutorAideLogger(__name__)


class TutorService:
    """Service for managing tutor profiles and related operations."""
    
    def __init__(self, db_manager: DatabaseManager):
        """Initialize the service."""
        self.db_manager = db_manager
        self.tutor_repo = TutorRepository()
        self.user_repo = UserRepository()
    
    async def create_profile(
        self,
        user_id: int,
        profile_data: Dict[str, Any],
        current_user_id: int,
        current_user_role: UserRole
    ) -> TutorProfile:
        """Create a new tutor profile."""
        # Only the user themselves or a manager can create a tutor profile
        if user_id != current_user_id and current_user_role != UserRole.MANAGER:
            raise ForbiddenError("You can only create your own tutor profile")
        
        async with self.db_manager.acquire() as conn:
            # Verify user exists and has tutor role
            user = await self.user_repo.get(conn, user_id)
            if not user:
                raise ResourceNotFoundError("User not found")
            
            # Check if user has tutor role
            has_tutor_role = any(
                role.get("role") == UserRole.TUTOR 
                for role in user.get("roles", [])
            )
            if not has_tutor_role:
                raise ValidationError("User does not have tutor role")
            
            # Check if profile already exists
            existing = await self.tutor_repo.find_by_user_id(conn, user_id)
            if existing:
                raise BusinessLogicError("Tutor profile already exists for this user")
            
            # Create profile
            profile_data["user_id"] = user_id
            profile_data["verification_status"] = "pending"
            
            profile = await self.tutor_repo.create(conn, profile_data)
            
            logger.info(
                f"Tutor profile created for user {user_id} by {current_user_id}"
            )
            
            return TutorProfile(**dict(profile))
    
    async def get_profile(
        self,
        tutor_id: int,
        current_user_id: int,
        current_user_role: UserRole
    ) -> TutorProfile:
        """Get a tutor profile."""
        async with self.db_manager.acquire() as conn:
            profile = await self.tutor_repo.get(conn, tutor_id)
            if not profile:
                raise ResourceNotFoundError("Tutor profile not found")
            
            profile_dict = dict(profile)
            
            # Check permissions for sensitive data
            if not await self._can_view_full_profile(
                profile_dict, current_user_id, current_user_role
            ):
                # Remove sensitive information for public view
                profile_dict.pop("phone", None)
                profile_dict.pop("email", None)
                profile_dict.pop("verification_status", None)
            
            return TutorProfile(**profile_dict)
    
    async def get_profile_by_user_id(
        self,
        user_id: int,
        current_user_id: int,
        current_user_role: UserRole
    ) -> Optional[TutorProfile]:
        """Get tutor profile by user ID."""
        async with self.db_manager.acquire() as conn:
            profile = await self.tutor_repo.find_by_user_id(conn, user_id)
            if not profile:
                return None
            
            profile_dict = dict(profile)
            
            # Check permissions
            if not await self._can_view_full_profile(
                profile_dict, current_user_id, current_user_role
            ):
                # Remove sensitive information
                profile_dict.pop("phone", None)
                profile_dict.pop("email", None)
                profile_dict.pop("verification_status", None)
            
            return TutorProfile(**profile_dict)
    
    async def update_profile(
        self,
        tutor_id: int,
        update_data: Dict[str, Any],
        current_user_id: int,
        current_user_role: UserRole
    ) -> TutorProfile:
        """Update a tutor profile."""
        async with self.db_manager.acquire() as conn:
            # Get existing profile
            profile = await self.tutor_repo.get(conn, tutor_id)
            if not profile:
                raise ResourceNotFoundError("Tutor profile not found")
            
            # Check permissions
            if not await self._can_edit_profile(
                dict(profile), current_user_id, current_user_role
            ):
                raise ForbiddenError("You don't have permission to edit this profile")
            
            # Only managers can update verification status
            if "verification_status" in update_data and current_user_role != UserRole.MANAGER:
                update_data.pop("verification_status")
            
            # Update profile
            updated_profile = await self.tutor_repo.update(
                conn, tutor_id, update_data
            )
            
            logger.info(
                f"Tutor profile {tutor_id} updated by {current_user_id}"
            )
            
            return TutorProfile(**dict(updated_profile))
    
    async def update_verification_status(
        self,
        tutor_id: int,
        status: str,
        current_user_id: int,
        current_user_role: UserRole
    ) -> TutorProfile:
        """Update tutor verification status (managers only)."""
        if current_user_role != UserRole.MANAGER:
            raise ForbiddenError("Only managers can update verification status")
        
        valid_statuses = ["pending", "basic_approved", "fully_verified", "premium"]
        if status not in valid_statuses:
            raise ValidationError(f"Invalid verification status. Must be one of: {valid_statuses}")
        
        async with self.db_manager.acquire() as conn:
            profile = await self.tutor_repo.get(conn, tutor_id)
            if not profile:
                raise ResourceNotFoundError("Tutor profile not found")
            
            updated_profile = await self.tutor_repo.update(
                conn, tutor_id, {"verification_status": status}
            )
            
            logger.info(
                f"Tutor {tutor_id} verification status updated to {status} by manager {current_user_id}"
            )
            
            return TutorProfile(**dict(updated_profile))
    
    async def search_tutors(
        self,
        query: str = None,
        postal_code: str = None,
        subject: str = None,
        limit: int = 10,
        current_user_role: UserRole = None
    ) -> List[TutorProfile]:
        """Search for tutors."""
        async with self.db_manager.acquire() as conn:
            if postal_code:
                # Search by proximity to postal code
                results = await self.tutor_repo.find_nearby_tutors(
                    conn, postal_code, subject, limit
                )
            else:
                # General search
                results = await self.tutor_repo.search(
                    conn, query, subject, limit
                )
            
            tutors = []
            for record in results:
                profile_dict = dict(record)
                
                # Remove sensitive data for non-managers
                if current_user_role != UserRole.MANAGER:
                    profile_dict.pop("phone", None)
                    profile_dict.pop("email", None)
                    profile_dict.pop("verification_status", None)
                
                tutors.append(TutorProfile(**profile_dict))
            
            return tutors
    
    async def get_availability(
        self,
        tutor_id: int
    ) -> List[TutorAvailability]:
        """Get tutor availability schedule."""
        async with self.db_manager.acquire() as conn:
            # Verify tutor exists
            tutor = await self.tutor_repo.get(conn, tutor_id)
            if not tutor:
                raise ResourceNotFoundError("Tutor not found")
            
            availability = await self.tutor_repo.get_availability(conn, tutor_id)
            
            return [
                TutorAvailability(**dict(record))
                for record in availability
            ]
    
    async def update_availability(
        self,
        tutor_id: int,
        availability_data: List[Dict[str, Any]],
        current_user_id: int,
        current_user_role: UserRole
    ) -> List[TutorAvailability]:
        """Update tutor availability schedule."""
        async with self.db_manager.acquire() as conn:
            # Get tutor profile
            tutor = await self.tutor_repo.get(conn, tutor_id)
            if not tutor:
                raise ResourceNotFoundError("Tutor not found")
            
            # Check permissions
            if not await self._can_edit_profile(
                dict(tutor), current_user_id, current_user_role
            ):
                raise ForbiddenError("You don't have permission to update this availability")
            
            # Clear existing availability
            await self.tutor_repo.clear_availability(conn, tutor_id)
            
            # Add new availability
            results = []
            for slot in availability_data:
                slot["tutor_id"] = tutor_id
                record = await self.tutor_repo.add_availability(conn, slot)
                results.append(TutorAvailability(**dict(record)))
            
            logger.info(
                f"Tutor {tutor_id} availability updated by {current_user_id}"
            )
            
            return results
    
    async def request_time_off(
        self,
        tutor_id: int,
        start_date: date,
        end_date: date,
        reason: str,
        current_user_id: int
    ) -> TutorTimeOff:
        """Request time off for a tutor."""
        async with self.db_manager.acquire() as conn:
            # Get tutor profile
            tutor = await self.tutor_repo.get(conn, tutor_id)
            if not tutor:
                raise ResourceNotFoundError("Tutor not found")
            
            # Check permissions (only the tutor can request their own time off)
            if tutor["user_id"] != current_user_id:
                raise ForbiddenError("You can only request time off for yourself")
            
            # Create time off request
            time_off_data = {
                "tutor_id": tutor_id,
                "start_date": start_date,
                "end_date": end_date,
                "reason": reason,
                "status": "requested"
            }
            
            time_off = await self.tutor_repo.create_time_off(conn, time_off_data)
            
            logger.info(
                f"Time off requested by tutor {tutor_id} from {start_date} to {end_date}"
            )
            
            return TutorTimeOff(**dict(time_off))
    
    async def approve_time_off(
        self,
        time_off_id: int,
        approved: bool,
        current_user_id: int,
        current_user_role: UserRole
    ) -> TutorTimeOff:
        """Approve or reject time off request (managers only)."""
        if current_user_role != UserRole.MANAGER:
            raise ForbiddenError("Only managers can approve time off requests")
        
        async with self.db_manager.acquire() as conn:
            # Get time off request
            time_off = await self.tutor_repo.get_time_off(conn, time_off_id)
            if not time_off:
                raise ResourceNotFoundError("Time off request not found")
            
            # Update status
            status = "approved" if approved else "rejected"
            update_data = {
                "status": status,
                "approved_by": current_user_id,
                "approved_at": datetime.utcnow()
            }
            
            updated = await self.tutor_repo.update_time_off(
                conn, time_off_id, update_data
            )
            
            logger.info(
                f"Time off request {time_off_id} {status} by manager {current_user_id}"
            )
            
            return TutorTimeOff(**dict(updated))
    
    async def _can_view_full_profile(
        self,
        profile: Dict[str, Any],
        current_user_id: int,
        current_user_role: UserRole
    ) -> bool:
        """Check if user can view full profile details."""
        # Managers can view all profiles
        if current_user_role == UserRole.MANAGER:
            return True
        
        # Users can view their own profile
        if profile.get("user_id") == current_user_id:
            return True
        
        # Other users get limited view
        return False
    
    async def _can_edit_profile(
        self,
        profile: Dict[str, Any],
        current_user_id: int,
        current_user_role: UserRole
    ) -> bool:
        """Check if user can edit a profile."""
        # Managers can edit all profiles
        if current_user_role == UserRole.MANAGER:
            return True
        
        # Users can edit their own profile
        if profile.get("user_id") == current_user_id:
            return True
        
        return False