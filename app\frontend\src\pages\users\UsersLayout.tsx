import React from 'react';
import { Outlet } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { TabNavigation, Tab } from '../../components/layout/TabNavigation';
import { UserCircle, GraduationCap, Baby } from 'lucide-react';

const UsersLayout: React.FC = () => {
  const { t } = useTranslation();

  const tabs: Tab[] = [
    {
      id: 'clients',
      label: t('sidebar.clients'),
      path: '/users/clients',
      icon: <UserCircle />,
    },
    {
      id: 'tutors',
      label: t('sidebar.tutors'),
      path: '/users/tutors',
      icon: <GraduationCap />,
    },
    {
      id: 'dependants',
      label: t('sidebar.dependants'),
      path: '/users/dependants',
      icon: <Baby />,
    },
  ];

  return (
    <div className="h-full flex flex-col">
      <TabNavigation tabs={tabs} />
      <div className="flex-1 overflow-hidden">
        <Outlet />
      </div>
    </div>
  );
};

export default UsersLayout;