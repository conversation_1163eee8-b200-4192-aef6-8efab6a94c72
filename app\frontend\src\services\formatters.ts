/**
 * Locale-Specific Formatting Service for TutorAide
 * 
 * Provides comprehensive formatting for Quebec market requirements:
 * - Date/time formatting (EN-CA vs FR-CA)
 * - Currency formatting (CAD with Quebec standards)
 * - Number formatting with proper separators
 * - Phone number formatting (Canadian standards)
 * - Address formatting (Quebec standards)
 */

import { format, formatDistanceToNow, parseISO, isValid } from 'date-fns';
import { enCA, frCA } from 'date-fns/locale';

export type Locale = 'en' | 'fr';
export type LocaleContext = 'en-CA' | 'fr-CA';

export interface FormatOptions {
  locale?: Locale;
  quebecFrench?: boolean;
  timezone?: string;
  userPreferences?: {
    dateFormat?: 'short' | 'medium' | 'long';
    timeFormat?: '12h' | '24h';
    currencyDisplay?: 'symbol' | 'code' | 'name';
  };
}

export interface AddressFormat {
  street: string;
  city: string;
  province: string;
  postalCode: string;
  country?: string;
}

export interface PhoneFormat {
  number: string;
  extension?: string;
  international?: boolean;
}

/**
 * Quebec-specific locale mappings and configurations
 */
const LOCALE_CONFIG = {
  'en': {
    intlLocale: 'en-CA',
    dateFnsLocale: enCA,
    currencySymbol: '$',
    currencyPosition: 'before',
    thousandsSeparator: ',',
    decimalSeparator: '.',
    timeFormat: '12h',
    dateFormat: 'MM/dd/yyyy'
  },
  'fr': {
    intlLocale: 'fr-CA',
    dateFnsLocale: frCA,
    currencySymbol: '$',
    currencyPosition: 'after',
    thousandsSeparator: ' ',
    decimalSeparator: ',',
    timeFormat: '24h',
    dateFormat: 'dd/MM/yyyy'
  }
} as const;

/**
 * Quebec French month names (proper Quebec terminology)
 */
const QUEBEC_MONTHS = {
  short: [
    'janv.', 'févr.', 'mars', 'avr.', 'mai', 'juin',
    'juill.', 'août', 'sept.', 'oct.', 'nov.', 'déc.'
  ],
  long: [
    'janvier', 'février', 'mars', 'avril', 'mai', 'juin',
    'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre'
  ]
};

/**
 * Quebec province codes and names
 */
const QUEBEC_PROVINCES = {
  'en': {
    'QC': 'Quebec',
    'ON': 'Ontario',
    'BC': 'British Columbia',
    'AB': 'Alberta',
    'MB': 'Manitoba',
    'SK': 'Saskatchewan',
    'NS': 'Nova Scotia',
    'NB': 'New Brunswick',
    'NL': 'Newfoundland and Labrador',
    'PE': 'Prince Edward Island',
    'NT': 'Northwest Territories',
    'NU': 'Nunavut',
    'YT': 'Yukon'
  },
  'fr': {
    'QC': 'Québec',
    'ON': 'Ontario',
    'BC': 'Colombie-Britannique',
    'AB': 'Alberta',
    'MB': 'Manitoba',
    'SK': 'Saskatchewan',
    'NS': 'Nouvelle-Écosse',
    'NB': 'Nouveau-Brunswick',
    'NL': 'Terre-Neuve-et-Labrador',
    'PE': 'Île-du-Prince-Édouard',
    'NT': 'Territoires du Nord-Ouest',
    'NU': 'Nunavut',
    'YT': 'Yukon'
  }
};

/**
 * Quebec area codes for validation
 */
const QUEBEC_AREA_CODES = [
  '418', '438', '450', '514', '579', '581', '819', '873'
];

/**
 * Formatting cache for performance optimization
 */
class FormatCache {
  private cache = new Map<string, any>();
  private maxSize = 1000;

  get(key: string): any {
    return this.cache.get(key);
  }

  set(key: string, value: any): void {
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(key, value);
  }

  clear(): void {
    this.cache.clear();
  }
}

const formatCache = new FormatCache();

/**
 * Main formatting service class
 */
export class LocaleFormatter {
  private locale: Locale;
  private quebecFrench: boolean;
  private config: typeof LOCALE_CONFIG[keyof typeof LOCALE_CONFIG];

  constructor(options: FormatOptions = {}) {
    this.locale = options.locale || 'en';
    this.quebecFrench = options.quebecFrench ?? (this.locale === 'fr');
    this.config = LOCALE_CONFIG[this.locale];
  }

  /**
   * Update formatter locale and preferences
   */
  updateLocale(options: FormatOptions): void {
    this.locale = options.locale || this.locale;
    this.quebecFrench = options.quebecFrench ?? (this.locale === 'fr');
    this.config = LOCALE_CONFIG[this.locale];
    formatCache.clear(); // Clear cache when locale changes
  }

  /**
   * Format date according to locale preferences
   */
  formatDate(
    date: Date | string | number,
    formatType: 'short' | 'medium' | 'long' | 'full' | string = 'medium'
  ): string {
    try {
      const parsedDate = typeof date === 'string' ? parseISO(date) : new Date(date);
      
      if (!isValid(parsedDate)) {
        throw new Error('Invalid date');
      }

      const cacheKey = `date_${this.locale}_${formatType}_${parsedDate.getTime()}`;
      const cached = formatCache.get(cacheKey);
      if (cached) return cached;

      let formatted: string;

      if (typeof formatType === 'string' && !['short', 'medium', 'long', 'full'].includes(formatType)) {
        // Custom format string
        formatted = format(parsedDate, formatType, { 
          locale: this.config.dateFnsLocale 
        });
      } else {
        // Predefined format types
        const intl = new Intl.DateTimeFormat(this.config.intlLocale, {
          dateStyle: formatType as any,
          timeZone: 'America/Toronto' // Eastern Time (Quebec/Ontario)
        });
        formatted = intl.format(parsedDate);
      }

      // Quebec French specific adjustments
      if (this.quebecFrench && this.locale === 'fr') {
        // Replace standard French month names with Quebec variants
        QUEBEC_MONTHS.long.forEach((quebecMonth, index) => {
          const standardMonth = new Date(2024, index, 1).toLocaleDateString('fr-CA', { month: 'long' });
          formatted = formatted.replace(standardMonth, quebecMonth);
        });
      }

      formatCache.set(cacheKey, formatted);
      return formatted;

    } catch (error) {
      console.error('Date formatting error:', error);
      return String(date);
    }
  }

  /**
   * Format time according to locale preferences
   */
  formatTime(
    date: Date | string | number,
    format24h?: boolean
  ): string {
    try {
      const parsedDate = typeof date === 'string' ? parseISO(date) : new Date(date);
      
      if (!isValid(parsedDate)) {
        throw new Error('Invalid date');
      }

      const use24h = format24h ?? (this.config.timeFormat === '24h');
      
      const cacheKey = `time_${this.locale}_${use24h}_${parsedDate.getTime()}`;
      const cached = formatCache.get(cacheKey);
      if (cached) return cached;

      const intl = new Intl.DateTimeFormat(this.config.intlLocale, {
        hour: 'numeric',
        minute: '2-digit',
        hour12: !use24h,
        timeZone: 'America/Toronto'
      });

      const formatted = intl.format(parsedDate);
      formatCache.set(cacheKey, formatted);
      return formatted;

    } catch (error) {
      console.error('Time formatting error:', error);
      return String(date);
    }
  }

  /**
   * Format datetime combined
   */
  formatDateTime(
    date: Date | string | number,
    dateFormat: 'short' | 'medium' | 'long' = 'medium',
    format24h?: boolean
  ): string {
    const formattedDate = this.formatDate(date, dateFormat);
    const formattedTime = this.formatTime(date, format24h);
    
    if (this.locale === 'fr') {
      return `${formattedDate} à ${formattedTime}`;
    }
    return `${formattedDate} at ${formattedTime}`;
  }

  /**
   * Format relative time ("2 days ago" vs "il y a 2 jours")
   */
  formatRelativeTime(date: Date | string | number): string {
    try {
      const parsedDate = typeof date === 'string' ? parseISO(date) : new Date(date);
      
      if (!isValid(parsedDate)) {
        throw new Error('Invalid date');
      }

      const cacheKey = `relative_${this.locale}_${parsedDate.getTime()}`;
      const cached = formatCache.get(cacheKey);
      if (cached) return cached;

      const formatted = formatDistanceToNow(parsedDate, {
        addSuffix: true,
        locale: this.config.dateFnsLocale
      });

      formatCache.set(cacheKey, formatted);
      return formatted;

    } catch (error) {
      console.error('Relative time formatting error:', error);
      return String(date);
    }
  }

  /**
   * Format currency with Quebec standards
   */
  formatCurrency(
    amount: number,
    options: {
      showCode?: boolean;
      precision?: number;
    } = {}
  ): string {
    try {
      const { showCode = false, precision = 2 } = options;
      
      const cacheKey = `currency_${this.locale}_${amount}_${showCode}_${precision}`;
      const cached = formatCache.get(cacheKey);
      if (cached) return cached;

      let formatted: string;

      if (this.locale === 'fr' && this.quebecFrench) {
        // Quebec French: 1 234,56 $ CAD
        const numberPart = this.formatNumber(amount, { precision });
        formatted = showCode ? `${numberPart} $ CAD` : `${numberPart} $`;
      } else {
        // English Canadian: $1,234.56 CAD
        const intl = new Intl.NumberFormat(this.config.intlLocale, {
          style: 'currency',
          currency: 'CAD',
          minimumFractionDigits: precision,
          maximumFractionDigits: precision
        });
        formatted = intl.format(amount);
        
        if (showCode && !formatted.includes('CAD')) {
          formatted += ' CAD';
        }
      }

      formatCache.set(cacheKey, formatted);
      return formatted;

    } catch (error) {
      console.error('Currency formatting error:', error);
      return `$${amount.toFixed(2)}`;
    }
  }

  /**
   * Format Quebec tax display (QST + GST)
   */
  formatTaxes(amount: number, quebecTax: number = 0.09975, federalTax: number = 0.05): string {
    const qstAmount = amount * quebecTax;
    const gstAmount = amount * federalTax;
    
    if (this.locale === 'fr') {
      return `TPS: ${this.formatCurrency(gstAmount)}, TVQ: ${this.formatCurrency(qstAmount)}`;
    }
    return `GST: ${this.formatCurrency(gstAmount)}, QST: ${this.formatCurrency(qstAmount)}`;
  }

  /**
   * Format numbers with locale-specific separators
   */
  formatNumber(
    num: number,
    options: {
      precision?: number;
      useGrouping?: boolean;
      style?: 'decimal' | 'percent';
    } = {}
  ): string {
    try {
      const { precision, useGrouping = true, style = 'decimal' } = options;
      
      const cacheKey = `number_${this.locale}_${num}_${precision}_${useGrouping}_${style}`;
      const cached = formatCache.get(cacheKey);
      if (cached) return cached;

      const intlOptions: Intl.NumberFormatOptions = {
        style,
        useGrouping,
        ...(precision !== undefined && {
          minimumFractionDigits: precision,
          maximumFractionDigits: precision
        })
      };

      const intl = new Intl.NumberFormat(this.config.intlLocale, intlOptions);
      const formatted = intl.format(num);

      formatCache.set(cacheKey, formatted);
      return formatted;

    } catch (error) {
      console.error('Number formatting error:', error);
      return String(num);
    }
  }

  /**
   * Format large numbers with abbreviations (1K, 1M vs 1 k, 1 M)
   */
  formatLargeNumber(num: number): string {
    const absNum = Math.abs(num);
    
    if (absNum >= 1000000000) {
      const formatted = this.formatNumber(num / 1000000000, { precision: 1 });
      return this.locale === 'fr' ? `${formatted} G` : `${formatted}B`;
    }
    if (absNum >= 1000000) {
      const formatted = this.formatNumber(num / 1000000, { precision: 1 });
      return this.locale === 'fr' ? `${formatted} M` : `${formatted}M`;
    }
    if (absNum >= 1000) {
      const formatted = this.formatNumber(num / 1000, { precision: 1 });
      return this.locale === 'fr' ? `${formatted} k` : `${formatted}K`;
    }
    
    return this.formatNumber(num);
  }

  /**
   * Format Canadian phone numbers
   */
  formatPhone(phone: string | PhoneFormat, international: boolean = false): string {
    try {
      const phoneStr = typeof phone === 'string' ? phone : phone.number;
      const extension = typeof phone === 'object' ? phone.extension : undefined;
      
      // Remove all non-digits
      const digits = phoneStr.replace(/\D/g, '');
      
      // Validate Canadian phone number (10 digits)
      if (digits.length !== 10) {
        return phoneStr; // Return original if invalid
      }

      const areaCode = digits.slice(0, 3);
      const exchange = digits.slice(3, 6);
      const number = digits.slice(6, 10);

      let formatted: string;
      
      if (international) {
        formatted = `+1 (${areaCode}) ${exchange}-${number}`;
      } else {
        formatted = `(${areaCode}) ${exchange}-${number}`;
      }

      if (extension) {
        formatted += this.locale === 'fr' ? ` poste ${extension}` : ` ext. ${extension}`;
      }

      return formatted;

    } catch (error) {
      console.error('Phone formatting error:', error);
      return typeof phone === 'string' ? phone : phone.number;
    }
  }

  /**
   * Validate Quebec area code
   */
  isQuebecAreaCode(phone: string): boolean {
    const digits = phone.replace(/\D/g, '');
    if (digits.length >= 3) {
      const areaCode = digits.slice(0, 3);
      return QUEBEC_AREA_CODES.includes(areaCode);
    }
    return false;
  }

  /**
   * Format Canadian postal code
   */
  formatPostalCode(postalCode: string): string {
    try {
      // Remove spaces and convert to uppercase
      const clean = postalCode.replace(/\s/g, '').toUpperCase();
      
      // Validate Canadian postal code format
      const canadianPostalRegex = /^[A-Z]\d[A-Z]\d[A-Z]\d$/;
      
      if (clean.length === 6 && canadianPostalRegex.test(clean)) {
        return `${clean.slice(0, 3)} ${clean.slice(3)}`;
      }
      
      return postalCode; // Return original if invalid

    } catch (error) {
      console.error('Postal code formatting error:', error);
      return postalCode;
    }
  }

  /**
   * Format complete address with Quebec standards
   */
  formatAddress(address: AddressFormat, multiline: boolean = false): string {
    try {
      const separator = multiline ? '\n' : ', ';
      const province = QUEBEC_PROVINCES[this.locale][address.province] || address.province;
      
      const formattedPostal = this.formatPostalCode(address.postalCode);
      
      const parts = [
        address.street,
        address.city,
        `${province} ${formattedPostal}`
      ];

      if (address.country && address.country !== 'Canada') {
        parts.push(address.country);
      }

      return parts.filter(Boolean).join(separator);

    } catch (error) {
      console.error('Address formatting error:', error);
      return `${address.street}, ${address.city}, ${address.province} ${address.postalCode}`;
    }
  }

  /**
   * Format percentage with locale-specific rules
   */
  formatPercentage(value: number, precision: number = 1): string {
    return this.formatNumber(value, { precision, style: 'percent' });
  }

  /**
   * Get locale-specific separator information
   */
  getSeparators(): { thousands: string; decimal: string } {
    return {
      thousands: this.config.thousandsSeparator,
      decimal: this.config.decimalSeparator
    };
  }

  /**
   * Clear formatting cache
   */
  clearCache(): void {
    formatCache.clear();
  }
}

/**
 * Default formatter instance
 */
export const defaultFormatter = new LocaleFormatter();

/**
 * Utility functions for common formatting operations
 */
export const formatters = {
  /**
   * Quick date formatting
   */
  date: (date: Date | string | number, locale: Locale = 'en', format: string = 'medium') => {
    const formatter = new LocaleFormatter({ locale, quebecFrench: locale === 'fr' });
    return formatter.formatDate(date, format);
  },

  /**
   * Quick currency formatting
   */
  currency: (amount: number, locale: Locale = 'en', showCode: boolean = false) => {
    const formatter = new LocaleFormatter({ locale, quebecFrench: locale === 'fr' });
    return formatter.formatCurrency(amount, { showCode });
  },

  /**
   * Quick number formatting
   */
  number: (num: number, locale: Locale = 'en', precision?: number) => {
    const formatter = new LocaleFormatter({ locale, quebecFrench: locale === 'fr' });
    return formatter.formatNumber(num, { precision });
  },

  /**
   * Quick phone formatting
   */
  phone: (phone: string, locale: Locale = 'en', international: boolean = false) => {
    const formatter = new LocaleFormatter({ locale, quebecFrench: locale === 'fr' });
    return formatter.formatPhone(phone, international);
  },

  /**
   * Quick address formatting
   */
  address: (address: AddressFormat, locale: Locale = 'en', multiline: boolean = false) => {
    const formatter = new LocaleFormatter({ locale, quebecFrench: locale === 'fr' });
    return formatter.formatAddress(address, multiline);
  }
};

export default LocaleFormatter;