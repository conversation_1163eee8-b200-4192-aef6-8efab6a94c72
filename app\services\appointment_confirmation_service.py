"""
Appointment confirmation service for managing tutor SMS confirmations and notifications.
"""

import asyncpg
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, date, time, timedelta
from enum import Enum

from app.config.database import DatabaseManager
from app.database.repositories.base import BaseRepository
from app.database.repositories.appointment_repository import AppointmentRepository
from app.core.exceptions import ResourceNotFoundError, BusinessLogicError, ValidationError
from app.core.timezone import now_est
from app.models.appointment_models import AppointmentStatus

logger = logging.getLogger(__name__)


class ConfirmationStatus(str, Enum):
    """Confirmation status enumeration."""
    PENDING = "pending"
    SENT = "sent"
    CONFIRMED = "confirmed"
    DECLINED = "declined"
    EXPIRED = "expired"
    CANCELLED = "cancelled"


class NotificationType(str, Enum):
    """Notification type enumeration."""
    CONFIRMATION_REQUEST = "confirmation_request"
    REMINDER_24H = "reminder_24h"
    REMINDER_2H = "reminder_2h"
    COMPLETION_REQUEST = "completion_request"
    FOLLOW_UP = "follow_up"


class AppointmentConfirmationService:
    """Service for managing appointment confirmations and notifications."""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.appointment_repo = AppointmentRepository()
        self.confirmation_repo = AppointmentConfirmationRepository()
        self.notification_repo = AppointmentNotificationRepository()
    
    async def request_tutor_confirmation(
        self, appointment_id: int, send_immediately: bool = True
    ) -> Dict[str, Any]:
        """
        Request tutor confirmation for an appointment.
        
        Args:
            appointment_id: Appointment ID
            send_immediately: Whether to send SMS immediately
        
        Returns:
            Confirmation request details
        """
        try:
            async with self.db_manager.acquire() as conn:
                # Get appointment details
                appointment = await self.appointment_repo.find_by_id(conn, appointment_id)
                if not appointment:
                    raise ResourceNotFoundError(f"Appointment {appointment_id} not found")
                
                # Check if appointment is in confirmable state
                if appointment['status'] not in ['scheduled']:
                    raise BusinessLogicError(
                        "Appointment is not in confirmable state",
                        {"current_status": appointment['status']}
                    )
                
                # Check if confirmation already exists
                existing_confirmation = await self.confirmation_repo.find_by_appointment(
                    conn, appointment_id
                )
                
                if existing_confirmation and existing_confirmation['status'] == ConfirmationStatus.CONFIRMED:
                    return {
                        'confirmation_id': existing_confirmation['confirmation_id'],
                        'status': 'already_confirmed',
                        'confirmed_at': existing_confirmation['confirmed_at']
                    }
                
                # Create or update confirmation request
                confirmation = await self._create_or_update_confirmation(
                    conn, appointment_id, ConfirmationStatus.PENDING
                )
                
                # Send SMS if requested
                notification_id = None
                if send_immediately:
                    notification_id = await self._send_confirmation_sms(
                        conn, appointment, confirmation
                    )
                
                logger.info(f"Requested confirmation for appointment {appointment_id}")
                
                return {
                    'confirmation_id': confirmation['confirmation_id'],
                    'status': 'request_sent',
                    'notification_id': notification_id,
                    'expires_at': confirmation['expires_at']
                }
                
        except Exception as e:
            logger.error(f"Error requesting tutor confirmation: {e}")
            raise
    
    async def process_tutor_response(
        self, confirmation_token: str, response: str, metadata: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """
        Process tutor's confirmation response (usually from SMS).
        
        Args:
            confirmation_token: Unique confirmation token
            response: Response ('YES', 'NO', or custom)
            metadata: Additional response metadata
        
        Returns:
            Processing results
        """
        try:
            async with self.db_manager.acquire() as conn:
                async with conn.transaction():
                    # Find confirmation by token
                    confirmation = await self.confirmation_repo.find_by_token(
                        conn, confirmation_token
                    )
                    
                    if not confirmation:
                        raise ResourceNotFoundError("Invalid confirmation token")
                    
                    # Check if confirmation is still valid
                    if confirmation['expires_at'] < now_est():
                        await self.confirmation_repo.update(
                            conn, confirmation['confirmation_id'], 
                            {'status': ConfirmationStatus.EXPIRED}
                        )
                        raise BusinessLogicError("Confirmation has expired")
                    
                    # Process response
                    normalized_response = response.upper().strip()
                    
                    if normalized_response in ['YES', 'Y', 'CONFIRM', 'OK']:
                        new_status = ConfirmationStatus.CONFIRMED
                        appointment_status = AppointmentStatus.CONFIRMED
                        result_message = "Appointment confirmed successfully"
                    elif normalized_response in ['NO', 'N', 'DECLINE', 'CANCEL']:
                        new_status = ConfirmationStatus.DECLINED
                        appointment_status = AppointmentStatus.CANCELLED
                        result_message = "Appointment declined and cancelled"
                    else:
                        # Handle custom responses or unclear responses
                        new_status = ConfirmationStatus.PENDING
                        appointment_status = None
                        result_message = "Response recorded, awaiting clarification"
                    
                    # Update confirmation
                    updated_confirmation = await self.confirmation_repo.update(
                        conn, confirmation['confirmation_id'], {
                            'status': new_status,
                            'response': response,
                            'response_metadata': metadata or {},
                            'confirmed_at': now_est() if new_status == ConfirmationStatus.CONFIRMED else None,
                            'updated_at': now_est()
                        }
                    )
                    
                    # Update appointment status if needed
                    if appointment_status:
                        await self.appointment_repo.update(
                            conn, confirmation['appointment_id'], {
                                'status': appointment_status,
                                'confirmed_by_tutor': new_status == ConfirmationStatus.CONFIRMED,
                                'confirmed_at': now_est() if new_status == ConfirmationStatus.CONFIRMED else None,
                                'updated_at': now_est()
                            }
                        )
                    
                    # Send follow-up notification if needed
                    follow_up_sent = False
                    if new_status == ConfirmationStatus.CONFIRMED:
                        follow_up_sent = await self._send_confirmation_follow_up(
                            conn, confirmation['appointment_id'], 'confirmed'
                        )
                    elif new_status == ConfirmationStatus.DECLINED:
                        follow_up_sent = await self._send_confirmation_follow_up(
                            conn, confirmation['appointment_id'], 'declined'
                        )
                    
                    logger.info(f"Processed confirmation response for appointment {confirmation['appointment_id']}: {new_status}")
                    
                    return {
                        'confirmation_id': confirmation['confirmation_id'],
                        'appointment_id': confirmation['appointment_id'],
                        'status': new_status,
                        'message': result_message,
                        'follow_up_sent': follow_up_sent
                    }
                    
        except Exception as e:
            logger.error(f"Error processing tutor response: {e}")
            raise
    
    async def send_appointment_reminders(
        self, hours_before: int = 24, batch_size: int = 50
    ) -> Dict[str, Any]:
        """
        Send appointment reminders to tutors and clients.
        
        Args:
            hours_before: How many hours before appointment to send reminder
            batch_size: Number of reminders to send in batch
        
        Returns:
            Reminder sending results
        """
        try:
            async with self.db_manager.acquire() as conn:
                # Find appointments needing reminders
                reminder_time = now_est() + timedelta(hours=hours_before)
                
                appointments = await self._find_appointments_needing_reminders(
                    conn, reminder_time, hours_before, batch_size
                )
                
                sent_reminders = []
                failed_reminders = []
                
                for appointment in appointments:
                    try:
                        notification_id = await self._send_appointment_reminder(
                            conn, appointment, hours_before
                        )
                        
                        sent_reminders.append({
                            'appointment_id': appointment['appointment_id'],
                            'notification_id': notification_id,
                            'recipient_count': 2  # Tutor + Client
                        })
                        
                        # Mark reminder as sent
                        field_name = f"reminder_sent_{hours_before}h"
                        if hasattr(appointment, field_name):
                            await self.appointment_repo.update(
                                conn, appointment['appointment_id'],
                                {field_name: True, 'updated_at': now_est()}
                            )
                        
                    except Exception as e:
                        logger.error(f"Failed to send reminder for appointment {appointment['appointment_id']}: {e}")
                        failed_reminders.append({
                            'appointment_id': appointment['appointment_id'],
                            'error': str(e)
                        })
                
                logger.info(f"Sent {len(sent_reminders)} reminders, {len(failed_reminders)} failed")
                
                return {
                    'sent_count': len(sent_reminders),
                    'failed_count': len(failed_reminders),
                    'sent_reminders': sent_reminders,
                    'failed_reminders': failed_reminders
                }
                
        except Exception as e:
            logger.error(f"Error sending appointment reminders: {e}")
            raise
    
    async def request_completion_confirmation(
        self, appointment_id: int, send_immediately: bool = True
    ) -> Dict[str, Any]:
        """
        Request tutor to confirm appointment completion after session.
        
        Args:
            appointment_id: Appointment ID
            send_immediately: Whether to send SMS immediately
        
        Returns:
            Completion request details
        """
        try:
            async with self.db_manager.acquire() as conn:
                appointment = await self.appointment_repo.find_by_id(conn, appointment_id)
                if not appointment:
                    raise ResourceNotFoundError(f"Appointment {appointment_id} not found")
                
                # Check if appointment is past its end time
                appointment_end = datetime.combine(
                    appointment['scheduled_date'], appointment['end_time']
                )
                
                if appointment_end > now_est():
                    raise BusinessLogicError(
                        "Cannot request completion confirmation before appointment ends"
                    )
                
                # Create completion confirmation request
                confirmation = await self._create_or_update_confirmation(
                    conn, appointment_id, ConfirmationStatus.PENDING, 
                    confirmation_type='completion'
                )
                
                # Send SMS if requested
                notification_id = None
                if send_immediately:
                    notification_id = await self._send_completion_sms(
                        conn, appointment, confirmation
                    )
                
                logger.info(f"Requested completion confirmation for appointment {appointment_id}")
                
                return {
                    'confirmation_id': confirmation['confirmation_id'],
                    'status': 'completion_request_sent',
                    'notification_id': notification_id
                }
                
        except Exception as e:
            logger.error(f"Error requesting completion confirmation: {e}")
            raise
    
    async def get_confirmation_statistics(
        self, start_date: date, end_date: date
    ) -> Dict[str, Any]:
        """
        Get confirmation statistics for a date range.
        
        Args:
            start_date: Start date
            end_date: End date
        
        Returns:
            Confirmation statistics
        """
        try:
            async with self.db_manager.acquire() as conn:
                stats = await self.confirmation_repo.get_confirmation_stats(
                    conn, start_date, end_date
                )
                
                return {
                    'period': {
                        'start_date': start_date,
                        'end_date': end_date
                    },
                    'total_appointments': stats['total_appointments'],
                    'confirmations_requested': stats['confirmations_requested'],
                    'confirmations_received': stats['confirmations_received'],
                    'confirmations_declined': stats['confirmations_declined'],
                    'confirmations_expired': stats['confirmations_expired'],
                    'confirmation_rate': stats['confirmation_rate'],
                    'avg_response_time_hours': stats['avg_response_time_hours'],
                    'breakdown_by_status': stats['status_breakdown']
                }
                
        except Exception as e:
            logger.error(f"Error getting confirmation statistics: {e}")
            raise
    
    # Private helper methods
    async def _create_or_update_confirmation(
        self, conn: asyncpg.Connection, appointment_id: int, status: ConfirmationStatus,
        confirmation_type: str = 'appointment'
    ) -> asyncpg.Record:
        """Create or update confirmation record."""
        existing = await self.confirmation_repo.find_by_appointment(conn, appointment_id)
        
        confirmation_data = {
            'appointment_id': appointment_id,
            'status': status,
            'confirmation_type': confirmation_type,
            'confirmation_token': self._generate_confirmation_token(),
            'expires_at': now_est() + timedelta(hours=24),  # 24 hour expiry
            'created_at': now_est(),
            'updated_at': now_est()
        }
        
        if existing:
            return await self.confirmation_repo.update(
                conn, existing['confirmation_id'], confirmation_data
            )
        else:
            return await self.confirmation_repo.create(conn, confirmation_data)
    
    def _generate_confirmation_token(self) -> str:
        """Generate unique confirmation token."""
        import secrets
        return secrets.token_urlsafe(32)
    
    async def _send_confirmation_sms(
        self, conn: asyncpg.Connection, appointment: asyncpg.Record, confirmation: asyncpg.Record
    ) -> Optional[int]:
        """Send confirmation request SMS to tutor."""
        # This would integrate with Twilio SMS service
        # For now, create notification record
        
        message = f"Please confirm your appointment on {appointment['scheduled_date']} at {appointment['start_time']}. Reply YES to confirm or NO to decline. Token: {confirmation['confirmation_token'][:8]}"
        
        notification_data = {
            'appointment_id': appointment['appointment_id'],
            'confirmation_id': confirmation['confirmation_id'],
            'recipient_id': appointment['tutor_id'],
            'notification_type': NotificationType.CONFIRMATION_REQUEST,
            'message_content': message,
            'scheduled_for': now_est(),
            'status': 'pending',
            'created_at': now_est()
        }
        
        notification = await self.notification_repo.create(conn, notification_data)
        
        # TODO: Integrate with actual SMS service (Twilio)
        logger.info(f"SMS confirmation request queued for appointment {appointment['appointment_id']}")
        
        return notification['notification_id']
    
    async def _send_completion_sms(
        self, conn: asyncpg.Connection, appointment: asyncpg.Record, confirmation: asyncpg.Record
    ) -> Optional[int]:
        """Send completion confirmation SMS to tutor."""
        message = f"Please confirm completion of your session on {appointment['scheduled_date']} at {appointment['start_time']}. Reply DONE if completed or NOSHOW if client didn't attend."
        
        notification_data = {
            'appointment_id': appointment['appointment_id'],
            'confirmation_id': confirmation['confirmation_id'],
            'recipient_id': appointment['tutor_id'],
            'notification_type': NotificationType.COMPLETION_REQUEST,
            'message_content': message,
            'scheduled_for': now_est(),
            'status': 'pending',
            'created_at': now_est()
        }
        
        notification = await self.notification_repo.create(conn, notification_data)
        logger.info(f"SMS completion request queued for appointment {appointment['appointment_id']}")
        
        return notification['notification_id']
    
    async def _send_appointment_reminder(
        self, conn: asyncpg.Connection, appointment: asyncpg.Record, hours_before: int
    ) -> Optional[int]:
        """Send appointment reminder to tutor and client."""
        if hours_before == 24:
            message = f"Reminder: You have an appointment tomorrow at {appointment['start_time']} for {appointment['subject_area']}."
        elif hours_before == 2:
            message = f"Reminder: You have an appointment in 2 hours at {appointment['start_time']} for {appointment['subject_area']}."
        else:
            message = f"Reminder: You have an appointment in {hours_before} hours at {appointment['start_time']} for {appointment['subject_area']}."
        
        notification_data = {
            'appointment_id': appointment['appointment_id'],
            'recipient_id': appointment['tutor_id'],
            'notification_type': NotificationType.REMINDER_24H if hours_before == 24 else NotificationType.REMINDER_2H,
            'message_content': message,
            'scheduled_for': now_est(),
            'status': 'pending',
            'created_at': now_est()
        }
        
        notification = await self.notification_repo.create(conn, notification_data)
        logger.info(f"Reminder queued for appointment {appointment['appointment_id']} ({hours_before}h)")
        
        return notification['notification_id']
    
    async def _find_appointments_needing_reminders(
        self, conn: asyncpg.Connection, reminder_time: datetime, hours_before: int, limit: int
    ) -> List[asyncpg.Record]:
        """Find appointments that need reminders."""
        field_name = f"reminder_sent_{hours_before}h"
        
        query = f"""
            SELECT * FROM appointment_sessions
            WHERE scheduled_date >= CURRENT_DATE
            AND status IN ('scheduled', 'confirmed')
            AND ({field_name} IS FALSE OR {field_name} IS NULL)
            AND (scheduled_date + start_time::interval) <= $1
            ORDER BY scheduled_date, start_time
            LIMIT $2
        """
        
        return await conn.fetch(query, reminder_time, limit)
    
    async def _send_confirmation_follow_up(
        self, conn: asyncpg.Connection, appointment_id: int, action: str
    ) -> bool:
        """Send follow-up notification after confirmation."""
        try:
            # This would send notifications to clients about confirmation status
            logger.info(f"Follow-up notification for appointment {appointment_id}: {action}")
            return True
        except Exception as e:
            logger.error(f"Failed to send follow-up notification: {e}")
            return False


class AppointmentConfirmationRepository(BaseRepository):
    """Repository for appointment confirmations."""
    
    def __init__(self):
        super().__init__(table_name="appointment_confirmations", id_column="confirmation_id")
    
    async def find_by_appointment(
        self, conn: asyncpg.Connection, appointment_id: int
    ) -> Optional[asyncpg.Record]:
        """Find confirmation by appointment ID."""
        query = f"SELECT * FROM {self.table_name} WHERE appointment_id = $1 ORDER BY created_at DESC LIMIT 1"
        return await conn.fetchrow(query, appointment_id)
    
    async def find_by_token(
        self, conn: asyncpg.Connection, token: str
    ) -> Optional[asyncpg.Record]:
        """Find confirmation by token."""
        query = f"SELECT * FROM {self.table_name} WHERE confirmation_token = $1 AND deleted_at IS NULL"
        return await conn.fetchrow(query, token)
    
    async def get_confirmation_stats(
        self, conn: asyncpg.Connection, start_date: date, end_date: date
    ) -> Dict[str, Any]:
        """Get confirmation statistics for date range."""
        query = """
            SELECT 
                COUNT(DISTINCT a.appointment_id) as total_appointments,
                COUNT(DISTINCT c.confirmation_id) as confirmations_requested,
                COUNT(DISTINCT CASE WHEN c.status = 'confirmed' THEN c.confirmation_id END) as confirmations_received,
                COUNT(DISTINCT CASE WHEN c.status = 'declined' THEN c.confirmation_id END) as confirmations_declined,
                COUNT(DISTINCT CASE WHEN c.status = 'expired' THEN c.confirmation_id END) as confirmations_expired,
                AVG(EXTRACT(EPOCH FROM (c.confirmed_at - c.created_at)) / 3600.0) as avg_response_time_hours
            FROM appointment_sessions a
            LEFT JOIN appointment_confirmations c ON a.appointment_id = c.appointment_id
            WHERE a.scheduled_date BETWEEN $1 AND $2
        """
        
        result = await conn.fetchrow(query, start_date, end_date)
        
        # Calculate confirmation rate
        confirmation_rate = 0.0
        if result['confirmations_requested'] > 0:
            confirmation_rate = (result['confirmations_received'] / result['confirmations_requested']) * 100
        
        return {
            **dict(result),
            'confirmation_rate': confirmation_rate,
            'status_breakdown': await self._get_status_breakdown(conn, start_date, end_date)
        }
    
    async def _get_status_breakdown(
        self, conn: asyncpg.Connection, start_date: date, end_date: date
    ) -> Dict[str, int]:
        """Get breakdown of confirmation statuses."""
        query = """
            SELECT c.status, COUNT(*) as count
            FROM appointment_confirmations c
            JOIN appointment_sessions a ON c.appointment_id = a.appointment_id
            WHERE a.scheduled_date BETWEEN $1 AND $2
            GROUP BY c.status
        """
        
        results = await conn.fetch(query, start_date, end_date)
        return {row['status']: row['count'] for row in results}


class AppointmentNotificationRepository(BaseRepository):
    """Repository for appointment notifications."""
    
    def __init__(self):
        super().__init__(table_name="appointment_notifications", id_column="notification_id")
    
    async def find_pending_notifications(
        self, conn: asyncpg.Connection, limit: int = 100
    ) -> List[asyncpg.Record]:
        """Find pending notifications to send."""
        query = f"""
            SELECT * FROM {self.table_name}
            WHERE status = 'pending'
            AND scheduled_for <= $1
            ORDER BY scheduled_for
            LIMIT $2
        """
        return await conn.fetch(query, now_est(), limit)
    
    async def mark_as_sent(
        self, conn: asyncpg.Connection, notification_id: int, sent_at: Optional[datetime] = None
    ) -> None:
        """Mark notification as sent."""
        await self.update(conn, notification_id, {
            'status': 'sent',
            'sent_at': sent_at or now_est(),
            'updated_at': now_est()
        })