import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import clsx from 'clsx';
import {
  Menu,
  X,
  Users,
  Calendar,
  DollarSign,
  FileText,
  MessageSquare,
  Settings,
  Search,
  ChevronDown,
  UserCircle,
  GraduationCap,
  Baby,
  CreditCard,
  Receipt,
  Package,
  BarChart3,
  TrendingUp,
  PieChart,
  FileBarChart,
  MessageCircle,
  Send,
  File,
  Radio,
  Cog,
  Shield,
  Bell,
  Key,
  MapPin,
  Wrench,
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { UserRoleType } from '../../types/auth';
import { filterSidebarSections } from '../../utils/permissions';

interface SidebarSection {
  id: string;
  title: string;
  icon: React.ReactNode;
  path: string;
  subItems?: {
    id: string;
    title: string;
    icon: React.ReactNode;
    path: string;
  }[];
  roles: UserRoleType[];
}

const sidebarSections: SidebarSection[] = [
  {
    id: 'services',
    title: 'sidebar.ourServices',
    icon: <GraduationCap className="w-5 h-5" />,
    path: '/services',
    roles: [UserRoleType.MANAGER, UserRoleType.TUTOR, UserRoleType.CLIENT],
  },
  {
    id: 'users',
    title: 'sidebar.users',
    icon: <Users className="w-5 h-5" />,
    path: '/users',
    roles: [UserRoleType.MANAGER, UserRoleType.TUTOR, UserRoleType.CLIENT],
    subItems: [
      {
        id: 'clients',
        title: 'sidebar.clients',
        icon: <UserCircle className="w-4 h-4" />,
        path: '/users/clients',
      },
      {
        id: 'tutors',
        title: 'sidebar.tutors',
        icon: <GraduationCap className="w-4 h-4" />,
        path: '/users/tutors',
      },
      {
        id: 'dependants',
        title: 'sidebar.dependants',
        icon: <Baby className="w-4 h-4" />,
        path: '/users/dependants',
      },
    ],
  },
  {
    id: 'tutors_management',
    title: 'sidebar.tutors_management',
    icon: <GraduationCap className="w-5 h-5" />,
    path: '/tutors',
    roles: [UserRoleType.MANAGER],
    subItems: [
      {
        id: 'tutor_profiles',
        title: 'tutors.profiles',
        icon: <GraduationCap className="w-4 h-4" />,
        path: '/tutors/profiles',
      },
      {
        id: 'tutor_invitations',
        title: 'tutors.invitations',
        icon: <Send className="w-4 h-4" />,
        path: '/tutors/invitations',
      },
      {
        id: 'tutor_applications',
        title: 'tutors.applications',
        icon: <File className="w-4 h-4" />,
        path: '/tutors/applications',
      },
    ],
  },
  {
    id: 'calendar',
    title: 'sidebar.calendar',
    icon: <Calendar className="w-5 h-5" />,
    path: '/calendar',
    roles: [UserRoleType.MANAGER, UserRoleType.TUTOR, UserRoleType.CLIENT],
  },
  {
    id: 'map',
    title: 'sidebar.map',
    icon: <MapPin className="w-5 h-5" />,
    path: '/map',
    roles: [UserRoleType.MANAGER, UserRoleType.TUTOR, UserRoleType.CLIENT],
  },
  {
    id: 'billing',
    title: 'sidebar.billing',
    icon: <DollarSign className="w-5 h-5" />,
    path: '/billing',
    roles: [UserRoleType.MANAGER, UserRoleType.CLIENT],
    subItems: [
      {
        id: 'invoices',
        title: 'sidebar.invoices',
        icon: <Receipt className="w-4 h-4" />,
        path: '/billing/invoices',
      },
      {
        id: 'tutor-payments',
        title: 'sidebar.tutorPayments',
        icon: <CreditCard className="w-4 h-4" />,
        path: '/billing/tutor-payments',
      },
      {
        id: 'subscriptions',
        title: 'sidebar.subscriptions',
        icon: <Package className="w-4 h-4" />,
        path: '/billing/subscriptions',
      },
      {
        id: 'packages',
        title: 'sidebar.packages',
        icon: <Package className="w-4 h-4" />,
        path: '/billing/packages',
      },
    ],
  },
  {
    id: 'reports',
    title: 'sidebar.reports',
    icon: <FileText className="w-5 h-5" />,
    path: '/reports',
    roles: [UserRoleType.MANAGER],
    subItems: [
      {
        id: 'financial',
        title: 'sidebar.financial',
        icon: <DollarSign className="w-4 h-4" />,
        path: '/reports/financial',
      },
      {
        id: 'performance',
        title: 'sidebar.performance',
        icon: <TrendingUp className="w-4 h-4" />,
        path: '/reports/performance',
      },
      {
        id: 'analytics',
        title: 'sidebar.analytics',
        icon: <BarChart3 className="w-4 h-4" />,
        path: '/reports/analytics',
      },
      {
        id: 'usage',
        title: 'sidebar.usage',
        icon: <PieChart className="w-4 h-4" />,
        path: '/reports/usage',
      },
      {
        id: 'monthly',
        title: 'sidebar.monthly',
        icon: <FileBarChart className="w-4 h-4" />,
        path: '/reports/monthly',
      },
    ],
  },
  {
    id: 'payment-history',
    title: 'sidebar.paymentHistory',
    icon: <DollarSign className="w-5 h-5" />,
    path: '/tutor/payment-history',
    roles: [UserRoleType.TUTOR],
  },
  {
    id: 'messages',
    title: 'sidebar.messages',
    icon: <MessageSquare className="w-5 h-5" />,
    path: '/messages',
    roles: [UserRoleType.MANAGER, UserRoleType.TUTOR, UserRoleType.CLIENT],
    subItems: [
      {
        id: 'sms-threads',
        title: 'sidebar.smsThreads',
        icon: <MessageCircle className="w-4 h-4" />,
        path: '/messages/sms',
      },
      {
        id: 'in-app-chat',
        title: 'sidebar.inAppChat',
        icon: <MessageSquare className="w-4 h-4" />,
        path: '/messages/chat',
      },
      {
        id: 'templates',
        title: 'sidebar.templates',
        icon: <File className="w-4 h-4" />,
        path: '/messages/templates',
      },
      {
        id: 'broadcasts',
        title: 'sidebar.broadcasts',
        icon: <Radio className="w-4 h-4" />,
        path: '/messages/broadcasts',
      },
    ],
  },
  {
    id: 'settings',
    title: 'sidebar.settings',
    icon: <Settings className="w-5 h-5" />,
    path: '/settings',
    roles: [UserRoleType.MANAGER, UserRoleType.TUTOR, UserRoleType.CLIENT],
    subItems: [
      {
        id: 'system',
        title: 'sidebar.system',
        icon: <Cog className="w-4 h-4" />,
        path: '/settings/system',
      },
      {
        id: 'users',
        title: 'sidebar.userSettings',
        icon: <Users className="w-4 h-4" />,
        path: '/settings/users',
      },
      {
        id: 'payments',
        title: 'sidebar.payments',
        icon: <CreditCard className="w-4 h-4" />,
        path: '/settings/payments',
      },
      {
        id: 'notifications',
        title: 'sidebar.notifications',
        icon: <Bell className="w-4 h-4" />,
        path: '/settings/notifications',
      },
      {
        id: 'api',
        title: 'sidebar.api',
        icon: <Key className="w-4 h-4" />,
        path: '/settings/api',
      },
      {
        id: 'services',
        title: 'sidebar.services',
        icon: <Wrench className="w-4 h-4" />,
        path: '/settings/services',
      },
    ],
  },
];

interface SidebarProps {
  onSearchClick?: () => void;
}

export const Sidebar: React.FC<SidebarProps> = ({ onSearchClick }) => {
  const { t } = useTranslation();
  const location = useLocation();
  const { user } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [expandedSections, setExpandedSections] = useState<string[]>([]);

  const toggleSection = (sectionId: string) => {
    setExpandedSections((prev) =>
      prev.includes(sectionId)
        ? prev.filter((id) => id !== sectionId)
        : [...prev, sectionId]
    );
  };

  const filteredSections = React.useMemo(() => {
    const filtered = sidebarSections.filter((section) =>
      section.roles.includes(user?.activeRole as UserRoleType)
    );
    
    // Apply additional filtering for clients
    if (user?.activeRole === UserRoleType.CLIENT) {
      return filtered.map(section => {
        if (section.subItems) {
          const clientAllowedSubPaths: Record<string, string[]> = {
            'users': ['/users/clients'], // Clients can only see their own profile
            'billing': ['/billing/invoices', '/billing/subscriptions'], // Only their invoices and subscription
            'messages': ['/messages/chat'], // Only chat, not SMS or broadcasts
            'settings': ['/settings/notifications'], // Only notification settings
          };
          
          const allowedPaths = clientAllowedSubPaths[section.id] || [];
          return {
            ...section,
            subItems: section.subItems.filter(item => allowedPaths.includes(item.path))
          };
        }
        return section;
      }).filter(section => {
        // Remove sections with no sub-items for clients
        if (section.subItems && section.subItems.length === 0) {
          return false;
        }
        // Remove reports section entirely for clients
        if (section.id === 'reports') {
          return false;
        }
        return true;
      });
    }
    
    // Apply additional filtering for tutors
    if (user?.activeRole === UserRoleType.TUTOR) {
      return filtered.map(section => {
        if (section.subItems) {
          const tutorAllowedSubPaths: Record<string, string[]> = {
            'users': ['/users/clients'], // Tutors see their assigned clients
            'messages': ['/messages/chat'], // Only chat
            'settings': ['/settings/notifications'], // Only notification settings
          };
          
          const allowedPaths = tutorAllowedSubPaths[section.id] || [];
          return {
            ...section,
            subItems: section.subItems.filter(item => allowedPaths.includes(item.path))
          };
        }
        return section;
      }).filter(section => {
        // Remove sections with no sub-items for tutors
        if (section.subItems && section.subItems.length === 0) {
          return false;
        }
        // Remove billing and reports sections entirely for tutors
        if (section.id === 'billing' || section.id === 'reports') {
          return false;
        }
        return true;
      });
    }
    
    return filtered;
  }, [user?.activeRole]);

  const isActiveSection = (section: SidebarSection) => {
    if (section.subItems) {
      return section.subItems.some((item) => location.pathname.startsWith(item.path));
    }
    return location.pathname.startsWith(section.path);
  };

  const isActiveItem = (path: string) => {
    return location.pathname === path;
  };

  return (
    <>
      {/* Mobile menu button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="lg:hidden fixed top-4 left-4 z-50 p-2 bg-white rounded-medium shadow-soft hover:shadow-medium transition-shadow"
      >
        {isOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
      </button>

      {/* Overlay for mobile */}
      {isOpen && (
        <div
          className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}

      {/* Sidebar */}
      <aside
        className={clsx(
          'fixed left-0 top-0 h-full z-40 transition-transform duration-200',
          'w-72 overflow-y-auto',
          'bg-white/95 backdrop-blur-xl border-r border-border-primary',
          isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'
        )}
      >
        {/* Logo */}
        <div className="p-6 border-b border-border-primary">
          <a
            href="https://www.tutoraide.ca"
            target="_blank"
            rel="noopener noreferrer"
            className="block"
          >
            <img
              src="/logo_tutoraide.jpg"
              alt="TutorAide"
              className="h-10 w-auto"
            />
          </a>
        </div>

        {/* Search */}
        <div className="p-4">
          <button 
            onClick={onSearchClick}
            className="w-full flex items-center gap-2 px-4 py-3 bg-background-secondary rounded-full text-text-secondary hover:bg-background-tertiary hover:text-accent-red transition-all"
          >
            <Search className="w-4 h-4" />
            <span className="text-sm font-medium">{t('sidebar.search')}</span>
            <span className="ml-auto text-xs bg-background-tertiary px-2.5 py-1 rounded-full">
              ⌘K
            </span>
          </button>
        </div>

        {/* Navigation */}
        <nav className="px-4 pb-6">
          {filteredSections.map((section) => {
            const isActive = isActiveSection(section);
            const isExpanded = expandedSections.includes(section.id);

            return (
              <div key={section.id} className="mb-2">
                {section.subItems ? (
                  <>
                    <button
                      onClick={() => toggleSection(section.id)}
                      className={clsx(
                        'w-full flex items-center justify-between px-4 py-3 rounded-lg transition-all duration-200',
                        isActive
                          ? 'bg-accent-red text-white shadow-soft'
                          : 'text-text-secondary hover:bg-red-50 hover:text-accent-red'
                      )}
                    >
                      <div className="flex items-center gap-3">
                        {section.icon}
                        <span className="text-sm font-medium">
                          {t(section.title)}
                        </span>
                      </div>
                      <ChevronDown
                        className={clsx(
                          'w-4 h-4 transition-transform',
                          isExpanded ? 'rotate-180' : ''
                        )}
                      />
                    </button>
                    {isExpanded && (
                      <div className="mt-1 ml-4">
                        {section.subItems.map((item) => (
                          <Link
                            key={item.id}
                            to={item.path}
                            onClick={() => setIsOpen(false)}
                            className={clsx(
                              'flex items-center gap-3 px-4 py-2.5 rounded-lg transition-all duration-200',
                              isActiveItem(item.path)
                                ? 'bg-red-50 text-accent-red font-medium'
                                : 'text-text-muted hover:bg-background-secondary hover:text-text-primary'
                            )}
                          >
                            {item.icon}
                            <span className="text-sm">{t(item.title)}</span>
                          </Link>
                        ))}
                      </div>
                    )}
                  </>
                ) : (
                  <Link
                    to={section.path}
                    onClick={() => setIsOpen(false)}
                    className={clsx(
                      'flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-200',
                      isActive
                        ? 'bg-accent-red text-white shadow-soft'
                        : 'text-text-secondary hover:bg-red-50 hover:text-accent-red'
                    )}
                  >
                    {section.icon}
                    <span className="text-sm font-medium">
                      {t(section.title)}
                    </span>
                  </Link>
                )}
              </div>
            );
          })}
        </nav>
      </aside>
    </>
  );
};