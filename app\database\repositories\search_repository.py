"""
Search repository for user and entity search functionality.
"""

import asyncio
import time
from typing import List, Optional, Dict, Any, <PERSON>ple
from datetime import datetime
import asyncpg

from app.database.repositories.base import BaseRepository
from app.models.search_models import (
    UserSearchRequest, UserSearchResult, AutocompleteRequest, 
    AutocompleteSuggestion, SearchEntityType, SearchSortBy, SearchSortOrder
)
from app.models.user_models import UserRoleType
from app.core.exceptions import DatabaseOperationError, ValidationError
from app.core.logging import TutorAideLogger


class SearchRepository(BaseRepository):
    """Repository for search operations."""
    
    def __init__(self):
        super().__init__()
        self.logger = TutorAideLogger("search_repository")
    
    async def search_users(
        self,
        conn: asyncpg.Connection,
        request: UserSearchRequest,
        requester_user_id: int,
        requester_roles: List[UserRoleType]
    ) -> <PERSON>ple[List[UserSearchResult], int]:
        """
        Search users with filtering and pagination.
        
        Args:
            conn: Database connection
            request: Search request parameters
            requester_user_id: ID of user performing search
            requester_roles: Roles of user performing search
            
        Returns:
            Tuple of (search results, total count)
        """
        try:
            start_time = time.time()
            
            # Build the query based on entity type and filters
            query, count_query, params = self._build_search_query(request, requester_roles)
            
            # Execute count query for pagination
            total_count = await conn.fetchval(count_query, *params)
            
            # Add pagination to main query
            offset = (request.page - 1) * request.page_size
            paginated_query = f"{query} LIMIT ${len(params) + 1} OFFSET ${len(params) + 2}"
            params.extend([request.page_size, offset])
            
            # Execute main search query
            rows = await conn.fetch(paginated_query, *params)
            
            # Convert rows to UserSearchResult objects
            results = []
            for row in rows:
                result = self._row_to_search_result(row)
                results.append(result)
            
            duration = (time.time() - start_time) * 1000
            
            # Log search analytics
            await self._log_search_analytics(
                conn, requester_user_id, request, len(results), duration
            )
            
            return results, total_count
            
        except Exception as e:
            self.logger.error(f"Error searching users: {str(e)}")
            raise DatabaseOperationError(f"Failed to search users: {str(e)}", operation="search_users")
    
    async def get_autocomplete_suggestions(
        self,
        conn: asyncpg.Connection,
        request: AutocompleteRequest,
        requester_roles: List[UserRoleType]
    ) -> List[AutocompleteSuggestion]:
        """
        Get autocomplete suggestions for user search.
        
        Args:
            conn: Database connection
            request: Autocomplete request parameters
            requester_roles: Roles of user performing search
            
        Returns:
            List of autocomplete suggestions
        """
        try:
            suggestions = []
            query_lower = request.query.lower()
            
            # Different suggestion strategies based on entity type
            if request.entity_type in [SearchEntityType.USER, SearchEntityType.ALL]:
                user_suggestions = await self._get_user_name_suggestions(
                    conn, query_lower, request.limit, requester_roles
                )
                suggestions.extend(user_suggestions)
            
            if request.entity_type in [SearchEntityType.CLIENT, SearchEntityType.ALL]:
                client_suggestions = await self._get_client_suggestions(
                    conn, query_lower, request.limit, requester_roles
                )
                suggestions.extend(client_suggestions)
            
            if request.entity_type in [SearchEntityType.TUTOR, SearchEntityType.ALL]:
                tutor_suggestions = await self._get_tutor_suggestions(
                    conn, query_lower, request.limit, requester_roles
                )
                suggestions.extend(tutor_suggestions)
            
            if request.entity_type in [SearchEntityType.DEPENDANT, SearchEntityType.ALL]:
                dependant_suggestions = await self._get_dependant_suggestions(
                    conn, query_lower, request.limit, requester_roles
                )
                suggestions.extend(dependant_suggestions)
            
            # Sort by relevance and limit results
            suggestions = sorted(suggestions, key=lambda x: x.text.lower())[:request.limit]
            
            return suggestions
            
        except Exception as e:
            self.logger.error(f"Error getting autocomplete suggestions: {str(e)}")
            raise DatabaseOperationError(
                f"Failed to get autocomplete suggestions: {str(e)}", 
                operation="get_autocomplete_suggestions"
            )
    
    def _build_search_query(
        self,
        request: UserSearchRequest,
        requester_roles: List[UserRoleType]
    ) -> Tuple[str, str, List[Any]]:
        """Build SQL query for user search."""
        # Base query with all necessary joins
        base_query = """
        SELECT DISTINCT
            u.user_id,
            u.email,
            u.first_name,
            u.last_name,
            CONCAT(u.first_name, ' ', u.last_name) as full_name,
            u.created_at,
            u.last_login,
            u.deleted_at IS NULL as is_active,
            
            -- Client fields
            cp.client_id,
            cp.phone_number,
            cp.postal_code,
            
            -- Tutor fields
            tp.tutor_id,
            tp.verification_status,
            array_agg(DISTINCT ts.subject_name) FILTER (WHERE ts.subject_name IS NOT NULL) as specializations,
            
            -- Dependant fields
            cd.dependant_id,
            CONCAT(pc.first_name, ' ', pc.last_name) as primary_client_name,
            
            -- Role information
            array_agg(DISTINCT ur.role_type) as roles
        FROM user_accounts u
        LEFT JOIN user_roles ur ON u.user_id = ur.user_id
        LEFT JOIN client_profiles cp ON u.user_id = cp.user_id AND cp.deleted_at IS NULL
        LEFT JOIN tutor_profiles tp ON u.user_id = tp.user_id AND tp.deleted_at IS NULL
        LEFT JOIN tutor_specializations ts ON tp.tutor_id = ts.tutor_id
        LEFT JOIN client_dependants cd ON u.user_id = cd.user_id AND cd.deleted_at IS NULL
        LEFT JOIN client_profiles pc ON cd.primary_client_id = pc.client_id
        """
        
        # Build WHERE conditions
        where_conditions = []
        params = []
        param_count = 0
        
        # Base conditions
        if request.active_only:
            where_conditions.append("u.deleted_at IS NULL")
        
        # Search query condition
        if request.query:
            param_count += 1
            search_condition = f"""
            (
                LOWER(u.first_name) ILIKE $%d OR
                LOWER(u.last_name) ILIKE $%d OR
                LOWER(u.email) ILIKE $%d OR
                LOWER(CONCAT(u.first_name, ' ', u.last_name)) ILIKE $%d OR
                LOWER(cp.phone_number) ILIKE $%d OR
                LOWER(cd.first_name) ILIKE $%d OR
                LOWER(cd.last_name) ILIKE $%d
            )
            """ % tuple([param_count] * 7)
            where_conditions.append(search_condition)
            search_pattern = f"%{request.query.lower()}%"
            params.extend([search_pattern] * 7)
        
        # Role filtering
        if request.roles:
            param_count += 1
            where_conditions.append(f"ur.role_type = ANY(${param_count})")
            params.append([role.value for role in request.roles])
        
        # Entity type filtering
        if request.entity_type != SearchEntityType.ALL:
            if request.entity_type == SearchEntityType.CLIENT:
                where_conditions.append("cp.client_id IS NOT NULL")
            elif request.entity_type == SearchEntityType.TUTOR:
                where_conditions.append("tp.tutor_id IS NOT NULL")
            elif request.entity_type == SearchEntityType.DEPENDANT:
                where_conditions.append("cd.dependant_id IS NOT NULL")
        
        # Verification status filtering (for tutors)
        if request.verified_only is not None and request.entity_type in [SearchEntityType.TUTOR, SearchEntityType.ALL]:
            if request.verified_only:
                where_conditions.append("tp.verification_status IN ('basic_approved', 'fully_verified', 'premium')")
            else:
                where_conditions.append("(tp.verification_status = 'pending' OR tp.verification_status IS NULL)")
        
        # Date filtering
        if request.created_after:
            param_count += 1
            where_conditions.append(f"u.created_at >= ${param_count}")
            params.append(request.created_after)
        
        if request.created_before:
            param_count += 1
            where_conditions.append(f"u.created_at <= ${param_count}")
            params.append(request.created_before)
        
        # Location filtering (for tutors and clients)
        if request.postal_code:
            param_count += 1
            where_conditions.append(f"(cp.postal_code = ${param_count} OR tp.postal_code = ${param_count})")
            params.append(request.postal_code)
        
        # Apply privacy rules based on requester roles
        privacy_conditions = self._get_privacy_conditions(requester_roles)
        if privacy_conditions:
            where_conditions.extend(privacy_conditions)
        
        # Combine WHERE conditions
        where_clause = ""
        if where_conditions:
            where_clause = f"WHERE {' AND '.join(where_conditions)}"
        
        # GROUP BY clause
        group_by = """
        GROUP BY u.user_id, u.email, u.first_name, u.last_name, u.created_at, u.last_login, u.deleted_at,
                 cp.client_id, cp.phone_number, cp.postal_code,
                 tp.tutor_id, tp.verification_status,
                 cd.dependant_id, pc.first_name, pc.last_name
        """
        
        # ORDER BY clause
        order_by = self._build_order_clause(request.sort_by, request.sort_order)
        
        # Complete query
        main_query = f"{base_query} {where_clause} {group_by} {order_by}"
        
        # Count query (simpler version for performance)
        count_query = f"""
        SELECT COUNT(DISTINCT u.user_id)
        FROM user_accounts u
        LEFT JOIN user_roles ur ON u.user_id = ur.user_id
        LEFT JOIN client_profiles cp ON u.user_id = cp.user_id AND cp.deleted_at IS NULL
        LEFT JOIN tutor_profiles tp ON u.user_id = tp.user_id AND tp.deleted_at IS NULL
        LEFT JOIN client_dependants cd ON u.user_id = cd.user_id AND cd.deleted_at IS NULL
        {where_clause}
        """
        
        return main_query, count_query, params
    
    def _get_privacy_conditions(self, requester_roles: List[UserRoleType]) -> List[str]:
        """Get privacy conditions based on requester roles."""
        conditions = []
        
        # Non-managers have limited access
        if UserRoleType.MANAGER not in requester_roles:
            # Tutors can't see client details until paired
            if UserRoleType.TUTOR in requester_roles:
                conditions.append(
                    "(tp.tutor_id IS NOT NULL OR cp.client_id IS NULL OR cd.dependant_id IS NOT NULL)"
                )
            
            # Clients can only see their own data and public tutor profiles
            if UserRoleType.CLIENT in requester_roles:
                conditions.append(
                    "(cp.client_id IS NOT NULL OR tp.verification_status IN ('basic_approved', 'fully_verified', 'premium'))"
                )
        
        return conditions
    
    def _build_order_clause(self, sort_by: SearchSortBy, sort_order: SearchSortOrder) -> str:
        """Build ORDER BY clause."""
        order_direction = "ASC" if sort_order == SearchSortOrder.ASC else "DESC"
        
        if sort_by == SearchSortBy.NAME:
            return f"ORDER BY u.last_name {order_direction}, u.first_name {order_direction}"
        elif sort_by == SearchSortBy.EMAIL:
            return f"ORDER BY u.email {order_direction}"
        elif sort_by == SearchSortBy.CREATED_DATE:
            return f"ORDER BY u.created_at {order_direction}"
        elif sort_by == SearchSortBy.LAST_ACTIVITY:
            return f"ORDER BY u.last_login {order_direction} NULLS LAST"
        elif sort_by == SearchSortBy.VERIFICATION_STATUS:
            return f"ORDER BY tp.verification_status {order_direction} NULLS LAST"
        else:  # RELEVANCE
            return "ORDER BY u.last_name ASC, u.first_name ASC"
    
    def _row_to_search_result(self, row: asyncpg.Record) -> UserSearchResult:
        """Convert database row to UserSearchResult."""
        # Determine entity type
        entity_type = SearchEntityType.USER
        if row['client_id']:
            entity_type = SearchEntityType.CLIENT
        elif row['tutor_id']:
            entity_type = SearchEntityType.TUTOR
        elif row['dependant_id']:
            entity_type = SearchEntityType.DEPENDANT
        
        # Parse roles
        roles = [UserRoleType(role) for role in (row['roles'] or [])]
        
        return UserSearchResult(
            user_id=row['user_id'],
            email=row['email'],
            first_name=row['first_name'],
            last_name=row['last_name'],
            full_name=row['full_name'],
            roles=roles,
            client_id=row['client_id'],
            phone_number=row['phone_number'],
            postal_code=row['postal_code'],
            tutor_id=row['tutor_id'],
            verification_status=row['verification_status'],
            specializations=row['specializations'] or [],
            dependant_id=row['dependant_id'],
            primary_client_name=row['primary_client_name'],
            entity_type=entity_type,
            created_at=row['created_at'],
            last_activity=row['last_login'],
            is_active=row['is_active']
        )
    
    async def _get_user_name_suggestions(
        self,
        conn: asyncpg.Connection,
        query: str,
        limit: int,
        requester_roles: List[UserRoleType]
    ) -> List[AutocompleteSuggestion]:
        """Get user name autocomplete suggestions."""
        sql = """
        SELECT DISTINCT
            u.user_id,
            u.first_name,
            u.last_name,
            u.email,
            CONCAT(u.first_name, ' ', u.last_name) as full_name
        FROM user_accounts u
        WHERE u.deleted_at IS NULL
        AND (
            LOWER(u.first_name) ILIKE $1 OR
            LOWER(u.last_name) ILIKE $1 OR
            LOWER(CONCAT(u.first_name, ' ', u.last_name)) ILIKE $1
        )
        ORDER BY u.last_name, u.first_name
        LIMIT $2
        """
        
        rows = await conn.fetch(sql, f"%{query}%", limit)
        
        suggestions = []
        for row in rows:
            suggestions.append(AutocompleteSuggestion(
                id=f"user_{row['user_id']}",
                text=row['full_name'] or f"{row['first_name']} {row['last_name']}",
                subtitle=row['email'],
                entity_type=SearchEntityType.USER,
                match_field="name"
            ))
        
        return suggestions
    
    async def _get_client_suggestions(
        self,
        conn: asyncpg.Connection,
        query: str,
        limit: int,
        requester_roles: List[UserRoleType]
    ) -> List[AutocompleteSuggestion]:
        """Get client autocomplete suggestions."""
        sql = """
        SELECT DISTINCT
            cp.client_id,
            u.first_name,
            u.last_name,
            u.email,
            cp.phone_number
        FROM client_profiles cp
        JOIN user_accounts u ON cp.user_id = u.user_id
        WHERE cp.deleted_at IS NULL AND u.deleted_at IS NULL
        AND (
            LOWER(u.first_name) ILIKE $1 OR
            LOWER(u.last_name) ILIKE $1 OR
            LOWER(cp.phone_number) ILIKE $1
        )
        ORDER BY u.last_name, u.first_name
        LIMIT $2
        """
        
        rows = await conn.fetch(sql, f"%{query}%", limit)
        
        suggestions = []
        for row in rows:
            full_name = f"{row['first_name']} {row['last_name']}"
            suggestions.append(AutocompleteSuggestion(
                id=f"client_{row['client_id']}",
                text=full_name,
                subtitle=f"Client • {row['phone_number'] or row['email']}",
                entity_type=SearchEntityType.CLIENT,
                match_field="name"
            ))
        
        return suggestions
    
    async def _get_tutor_suggestions(
        self,
        conn: asyncpg.Connection,
        query: str,
        limit: int,
        requester_roles: List[UserRoleType]
    ) -> List[AutocompleteSuggestion]:
        """Get tutor autocomplete suggestions."""
        # Privacy: Only show approved tutors to non-managers
        verification_filter = ""
        if UserRoleType.MANAGER not in requester_roles:
            verification_filter = "AND tp.verification_status IN ('basic_approved', 'fully_verified', 'premium')"
        
        sql = f"""
        SELECT DISTINCT
            tp.tutor_id,
            u.first_name,
            u.last_name,
            u.email,
            tp.verification_status,
            array_agg(DISTINCT ts.subject_name) FILTER (WHERE ts.subject_name IS NOT NULL) as specializations
        FROM tutor_profiles tp
        JOIN user_accounts u ON tp.user_id = u.user_id
        LEFT JOIN tutor_specializations ts ON tp.tutor_id = ts.tutor_id
        WHERE tp.deleted_at IS NULL AND u.deleted_at IS NULL
        {verification_filter}
        AND (
            LOWER(u.first_name) ILIKE $1 OR
            LOWER(u.last_name) ILIKE $1 OR
            LOWER(ts.subject_name) ILIKE $1
        )
        GROUP BY tp.tutor_id, u.first_name, u.last_name, u.email, tp.verification_status
        ORDER BY u.last_name, u.first_name
        LIMIT $2
        """
        
        rows = await conn.fetch(sql, f"%{query}%", limit)
        
        suggestions = []
        for row in rows:
            full_name = f"{row['first_name']} {row['last_name']}"
            specializations = row['specializations'] or []
            subtitle = f"Tutor • {', '.join(specializations[:2])}" if specializations else "Tutor"
            
            suggestions.append(AutocompleteSuggestion(
                id=f"tutor_{row['tutor_id']}",
                text=full_name,
                subtitle=subtitle,
                entity_type=SearchEntityType.TUTOR,
                match_field="name"
            ))
        
        return suggestions
    
    async def _get_dependant_suggestions(
        self,
        conn: asyncpg.Connection,
        query: str,
        limit: int,
        requester_roles: List[UserRoleType]
    ) -> List[AutocompleteSuggestion]:
        """Get dependant autocomplete suggestions."""
        sql = """
        SELECT DISTINCT
            cd.dependant_id,
            cd.first_name,
            cd.last_name,
            CONCAT(pc.first_name, ' ', pc.last_name) as primary_client_name
        FROM client_dependants cd
        JOIN client_profiles pc ON cd.primary_client_id = pc.client_id
        WHERE cd.deleted_at IS NULL AND pc.deleted_at IS NULL
        AND (
            LOWER(cd.first_name) ILIKE $1 OR
            LOWER(cd.last_name) ILIKE $1
        )
        ORDER BY cd.last_name, cd.first_name
        LIMIT $2
        """
        
        rows = await conn.fetch(sql, f"%{query}%", limit)
        
        suggestions = []
        for row in rows:
            full_name = f"{row['first_name']} {row['last_name']}"
            suggestions.append(AutocompleteSuggestion(
                id=f"dependant_{row['dependant_id']}",
                text=full_name,
                subtitle=f"Dependant of {row['primary_client_name']}",
                entity_type=SearchEntityType.DEPENDANT,
                match_field="name"
            ))
        
        return suggestions
    
    async def _log_search_analytics(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        request: UserSearchRequest,
        results_count: int,
        duration_ms: float
    ):
        """Log search analytics for monitoring and optimization."""
        try:
            from uuid import uuid4
            search_id = str(uuid4())
            
            sql = """
            INSERT INTO search_analytics (
                search_id, user_id, query, entity_type, results_count, 
                duration_ms, timestamp, filters_used
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            """
            
            filters_used = {
                'roles': [role.value for role in (request.roles or [])],
                'entity_type': request.entity_type.value,
                'active_only': request.active_only,
                'verified_only': request.verified_only,
                'postal_code': request.postal_code,
                'radius_km': request.radius_km
            }
            
            await conn.execute(
                sql,
                search_id,
                user_id,
                request.query,
                request.entity_type.value,
                results_count,
                duration_ms,
                datetime.utcnow(),
                filters_used
            )
            
        except Exception as e:
            # Don't fail the search if analytics logging fails
            self.logger.warning(f"Failed to log search analytics: {str(e)}")