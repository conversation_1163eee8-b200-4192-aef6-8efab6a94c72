import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { format, addDays, subDays, startOfDay, endOfDay, eachHourOfInterval, isSameDay } from 'date-fns';
import { ChevronLeft, ChevronRight, Clock, MapPin, User, Users, Calendar, Plus } from 'lucide-react';
import { Card } from '../../components/common/Card';
import Button from '../../components/common/Button';
import { Badge } from '../../components/common/Badge';
import { Modal } from '../../components/common/Modal';
import toast from 'react-hot-toast';

interface Tutor {
  id: number;
  name: string;
  avatar?: string;
  specialties: string[];
  isActive: boolean;
  currentLoad: number;
  maxLoad: number;
}

interface Appointment {
  id: number;
  tutorId: number;
  clientName: string;
  dependantName?: string;
  subject: string;
  startTime: Date;
  endTime: Date;
  duration: number;
  status: 'scheduled' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled';
  type: 'individual' | 'group' | 'tecfee';
  location: {
    type: 'online' | 'in_person' | 'library';
    details?: string;
  };
  participants?: number;
  maxParticipants?: number;
  notes?: string;
  confirmed: boolean;
}

interface CalendarDayViewProps {
  selectedDate?: Date;
}

const CalendarDayViewSimple: React.FC<CalendarDayViewProps> = ({ selectedDate: initialDate }) => {
  const { t } = useTranslation();
  const [selectedDate, setSelectedDate] = useState(initialDate || new Date());
  const [tutors, setTutors] = useState<Tutor[]>([]);
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [loading, setLoading] = useState(true);
  const [showNewAppointmentModal, setShowNewAppointmentModal] = useState(false);
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<{ tutorId: number; time: Date } | null>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Mock data
  useEffect(() => {
    const mockTutors: Tutor[] = [
      {
        id: 1,
        name: 'Marie Dubois',
        avatar: '/avatars/marie.jpg',
        specialties: ['Mathematics', 'Physics'],
        isActive: true,
        currentLoad: 6,
        maxLoad: 8
      },
      {
        id: 2,
        name: 'John Smith',
        avatar: '/avatars/john.jpg',
        specialties: ['English', 'French'],
        isActive: true,
        currentLoad: 4,
        maxLoad: 6
      },
      {
        id: 3,
        name: 'Sophie Martin',
        avatar: '/avatars/sophie.jpg',
        specialties: ['Science', 'Chemistry'],
        isActive: true,
        currentLoad: 3,
        maxLoad: 7
      }
    ];

    const mockAppointments: Appointment[] = [
      {
        id: 1,
        tutorId: 1,
        clientName: 'Emma Johnson',
        subject: 'Mathematics',
        startTime: new Date(selectedDate.getFullYear(), selectedDate.getMonth(), selectedDate.getDate(), 9, 0),
        endTime: new Date(selectedDate.getFullYear(), selectedDate.getMonth(), selectedDate.getDate(), 10, 0),
        duration: 60,
        status: 'confirmed',
        type: 'individual',
        location: { type: 'online' },
        confirmed: true
      },
      {
        id: 2,
        tutorId: 1,
        clientName: 'Lucas Martin',
        dependantName: 'Alice Martin',
        subject: 'Physics',
        startTime: new Date(selectedDate.getFullYear(), selectedDate.getMonth(), selectedDate.getDate(), 14, 0),
        endTime: new Date(selectedDate.getFullYear(), selectedDate.getMonth(), selectedDate.getDate(), 15, 30),
        duration: 90,
        status: 'scheduled',
        type: 'individual',
        location: { type: 'in_person', details: '123 Main St, Montreal' },
        confirmed: false
      },
      {
        id: 3,
        tutorId: 2,
        clientName: 'TECFEE Group A',
        subject: 'French Writing',
        startTime: new Date(selectedDate.getFullYear(), selectedDate.getMonth(), selectedDate.getDate(), 10, 0),
        endTime: new Date(selectedDate.getFullYear(), selectedDate.getMonth(), selectedDate.getDate(), 12, 0),
        duration: 120,
        status: 'confirmed',
        type: 'tecfee',
        location: { type: 'library', details: 'Bibliothèque Centrale' },
        participants: 6,
        maxParticipants: 8,
        confirmed: true
      }
    ];

    setTutors(mockTutors);
    setAppointments(mockAppointments);
    setLoading(false);
  }, [selectedDate]);

  // Scroll to current time on mount
  useEffect(() => {
    if (scrollContainerRef.current && isSameDay(selectedDate, new Date())) {
      const currentHour = new Date().getHours();
      const hourHeight = 60;
      const scrollTop = Math.max(0, (currentHour - 2) * hourHeight);
      scrollContainerRef.current.scrollTop = scrollTop;
    }
  }, [selectedDate]);

  const timeSlots = eachHourOfInterval({
    start: startOfDay(selectedDate),
    end: endOfDay(selectedDate)
  }).slice(6, 22); // 6 AM to 10 PM

  const navigateDate = (direction: 'prev' | 'next') => {
    setSelectedDate(direction === 'prev' ? subDays(selectedDate, 1) : addDays(selectedDate, 1));
  };

  const getAppointmentStyle = (appointment: Appointment) => {
    const startHour = appointment.startTime.getHours();
    const startMinute = appointment.startTime.getMinutes();
    const durationMinutes = appointment.duration;
    
    const top = ((startHour - 6) * 60) + startMinute;
    const height = durationMinutes;
    
    return {
      top: `${top}px`,
      height: `${height}px`
    };
  };

  const getAppointmentColor = (appointment: Appointment) => {
    const colors = {
      scheduled: 'bg-yellow-50 border-yellow-400 text-yellow-900',
      confirmed: 'bg-red-50 border-accent-red text-red-900',
      in_progress: 'bg-green-50 border-green-400 text-green-900',
      completed: 'bg-gray-50 border-gray-400 text-gray-700',
      cancelled: 'bg-red-100 border-red-400 text-red-700'
    };
    return colors[appointment.status];
  };

  const getLocationIcon = (type: string) => {
    switch (type) {
      case 'online': return '💻';
      case 'in_person': return '🏠';
      case 'library': return '📚';
      default: return '📍';
    }
  };

  const handleTimeSlotClick = (tutorId: number, time: Date) => {
    setSelectedTimeSlot({ tutorId, time });
    setShowNewAppointmentModal(true);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-accent-red"></div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Date Navigation */}
      <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigateDate('prev')}
            className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <ChevronLeft className="w-5 h-5" />
          </button>
          
          <h2 className="text-lg font-semibold text-gray-900">
            {format(selectedDate, 'EEEE, MMMM d, yyyy')}
          </h2>
          
          <button
            onClick={() => navigateDate('next')}
            className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <ChevronRight className="w-5 h-5" />
          </button>
          
          <Button
            variant="secondary"
            size="sm"
            onClick={() => setSelectedDate(new Date())}
          >
            Today
          </Button>
        </div>

        <Button
          leftIcon={<Plus className="w-4 h-4" />}
          onClick={() => setShowNewAppointmentModal(true)}
        >
          New Appointment
        </Button>
      </div>

      {/* Calendar Grid */}
      <div className="flex-1 flex overflow-hidden">
        {/* Time Column */}
        <div className="w-20 flex-shrink-0 border-r border-gray-200">
          <div className="h-16 border-b border-gray-200"></div>
          {timeSlots.map((time) => (
            <div key={time.getTime()} className="h-15 flex items-start justify-end pr-2 text-xs text-gray-500 border-b border-gray-100">
              {format(time, 'HH:mm')}
            </div>
          ))}
        </div>

        {/* Tutors Columns */}
        <div className="flex-1 overflow-x-auto">
          <div className="flex min-w-full">
            {tutors.map((tutor) => (
              <div key={tutor.id} className="flex-1 min-w-48 border-r border-gray-200">
                {/* Tutor Header */}
                <div className="h-16 p-3 border-b border-gray-200 bg-gray-50">
                  <div className="flex items-center space-x-3">
                    <div className="relative">
                      <div className="w-8 h-8 bg-accent-red rounded-full flex items-center justify-center text-white text-sm font-medium">
                        {tutor.name.split(' ').map(n => n[0]).join('')}
                      </div>
                      <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${
                        tutor.isActive ? 'bg-green-400' : 'bg-gray-400'
                      }`}></div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {tutor.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {tutor.currentLoad}/{tutor.maxLoad} sessions
                      </p>
                    </div>
                  </div>
                  <div className="mt-1 flex flex-wrap gap-1">
                    {tutor.specialties.slice(0, 2).map((specialty) => (
                      <span
                        key={specialty}
                        className="inline-block px-1.5 py-0.5 text-xs font-medium bg-red-100 text-red-700 rounded"
                      >
                        {specialty}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Time Slots */}
                <div className="relative" ref={scrollContainerRef}>
                  {timeSlots.map((time) => (
                    <div
                      key={time.getTime()}
                      className="h-15 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors relative"
                      onClick={() => handleTimeSlotClick(tutor.id, time)}
                    >
                      {time.getMinutes() === 0 && (
                        <div className="absolute top-0 left-0 right-0 border-t border-gray-300"></div>
                      )}
                    </div>
                  ))}

                  {/* Appointments */}
                  {appointments
                    .filter((apt) => apt.tutorId === tutor.id)
                    .map((appointment) => (
                      <div
                        key={appointment.id}
                        className={`absolute left-1 right-1 rounded-lg border-l-4 p-2 shadow-sm cursor-move transition-all hover:shadow-md ${getAppointmentColor(appointment)} ${
                          !appointment.confirmed ? 'opacity-75 border-dashed' : ''
                        }`}
                        style={getAppointmentStyle(appointment)}
                        title={`${appointment.clientName} - ${appointment.subject}`}
                      >
                        <div className="text-xs font-medium truncate">
                          {appointment.clientName}
                          {appointment.dependantName && ` (${appointment.dependantName})`}
                        </div>
                        
                        <div className="text-xs mt-0.5 truncate">
                          {appointment.subject}
                        </div>
                        
                        <div className="flex items-center justify-between mt-1">
                          <div className="flex items-center space-x-1 text-xs text-gray-600">
                            <Clock className="w-3 h-3" />
                            <span>{format(appointment.startTime, 'HH:mm')}</span>
                          </div>
                          
                          <div className="flex items-center space-x-1">
                            <span className="text-xs">{getLocationIcon(appointment.location.type)}</span>
                            
                            {appointment.type === 'group' || appointment.type === 'tecfee' ? (
                              <div className="flex items-center text-xs text-gray-500">
                                <Users className="w-3 h-3 mr-1" />
                                <span>{appointment.participants}/{appointment.maxParticipants}</span>
                              </div>
                            ) : (
                              <User className="w-3 h-3 text-gray-500" />
                            )}
                          </div>
                        </div>
                        
                        {!appointment.confirmed && (
                          <div className="text-xs text-orange-600 mt-1 font-medium">
                            Pending Confirmation
                          </div>
                        )}
                      </div>
                    ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Current Time Indicator */}
      {isSameDay(selectedDate, new Date()) && (
        <div
          className="absolute left-20 right-0 h-0.5 bg-accent-red z-10 pointer-events-none"
          style={{
            top: `${80 + ((new Date().getHours() - 6) * 60) + new Date().getMinutes()}px`
          }}
        >
          <div className="absolute left-0 top-0 w-2 h-2 bg-accent-red rounded-full -translate-y-1"></div>
        </div>
      )}

      {/* New Appointment Modal */}
      <Modal
        isOpen={showNewAppointmentModal}
        onClose={() => setShowNewAppointmentModal(false)}
        title="New Appointment"
      >
        <div className="p-6">
          <p className="text-gray-600 mb-4">
            {selectedTimeSlot && (
              <>Scheduling for {tutors.find(t => t.id === selectedTimeSlot.tutorId)?.name} at {format(selectedTimeSlot.time, 'HH:mm')}</>
            )}
          </p>
          <div className="flex justify-end gap-3">
            <Button
              variant="secondary"
              onClick={() => setShowNewAppointmentModal(false)}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={() => {
                toast.success('Appointment created successfully!');
                setShowNewAppointmentModal(false);
              }}
            >
              Create Appointment
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default CalendarDayViewSimple;