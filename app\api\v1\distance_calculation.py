"""
Distance Calculation API endpoints for advanced routing and travel time calculations.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel, Field, field_validator
from datetime import datetime
import logging

from app.services.distance_calculation_service import (
    DistanceCalculationService, 
    get_distance_calculation_service,
    TransportMode,
    TrafficLevel
)
from app.core.dependencies import get_current_user
from app.core.exceptions import ValidationError, BusinessLogicError

logger = logging.getLogger(__name__)

router = APIRouter()

# Request/Response Models
class RouteRequest(BaseModel):
    """Request model for route calculation."""
    origin_postal_code: str = Field(..., description="Origin postal code")
    destination_postal_code: str = Field(..., description="Destination postal code")
    transport_mode: str = Field("driving", description="Transport mode")
    departure_time: Optional[datetime] = Field(None, description="Departure time for traffic estimation")
    
    @field_validator('origin_postal_code', 'destination_postal_code')
    def validate_postal_codes(cls, v):
        if not v or len(v.strip()) < 6:
            raise ValueError('Postal code must be at least 6 characters')
        return v.strip().upper()
    
    @field_validator('transport_mode')
    def validate_transport_mode(cls, v):
        valid_modes = ['driving', 'walking', 'cycling', 'transit', 'straight_line']
        if v not in valid_modes:
            raise ValueError(f'Transport mode must be one of: {valid_modes}')
        return v

class DistanceMatrixRequest(BaseModel):
    """Request model for distance matrix calculation."""
    origins: List[str] = Field(..., description="List of origin postal codes")
    destinations: List[str] = Field(..., description="List of destination postal codes")
    transport_mode: str = Field("driving", description="Transport mode")
    
    @field_validator('origins', 'destinations')
    def validate_postal_code_lists(cls, v):
        if not v:
            raise ValueError('At least one postal code is required')
        if len(v) > 10:
            raise ValueError('Maximum 10 postal codes allowed')
        return [pc.strip().upper() for pc in v]
    
    @field_validator('transport_mode')
    def validate_transport_mode(cls, v):
        valid_modes = ['driving', 'walking', 'cycling', 'transit', 'straight_line']
        if v not in valid_modes:
            raise ValueError(f'Transport mode must be one of: {valid_modes}')
        return v

class OptimalRouteRequest(BaseModel):
    """Request model for optimal route calculation."""
    origin: str = Field(..., description="Origin postal code")
    destinations: List[str] = Field(..., description="Destinations to visit")
    transport_mode: str = Field("driving", description="Transport mode")
    return_to_origin: bool = Field(False, description="Whether to return to origin")
    
    @field_validator('origin')
    def validate_origin(cls, v):
        if not v or len(v.strip()) < 6:
            raise ValueError('Origin postal code must be at least 6 characters')
        return v.strip().upper()
    
    @field_validator('destinations')
    def validate_destinations(cls, v):
        if not v:
            raise ValueError('At least one destination is required')
        if len(v) > 8:
            raise ValueError('Maximum 8 destinations allowed for optimization')
        return [pc.strip().upper() for pc in v]
    
    @field_validator('transport_mode')
    def validate_transport_mode(cls, v):
        valid_modes = ['driving', 'walking', 'cycling', 'transit']
        if v not in valid_modes:
            raise ValueError(f'Transport mode must be one of: {valid_modes}')
        return v

class RouteSegmentResponse(BaseModel):
    """Response model for route segment."""
    from_postal_code: str
    to_postal_code: str
    distance_km: float
    duration_minutes: int
    mode: str
    traffic_factor: float
    estimated_cost: Optional[float]

class TravelRouteResponse(BaseModel):
    """Response model for travel route."""
    origin: str
    destination: str
    total_distance_km: float
    total_duration_minutes: int
    estimated_cost: float
    segments: List[RouteSegmentResponse]
    mode: str
    optimal: bool
    traffic_conditions: str

class DistanceMatrixResponse(BaseModel):
    """Response model for distance matrix."""
    origins: List[str]
    destinations: List[str]
    distances_km: List[List[float]]
    durations_minutes: List[List[int]]
    mode: str
    cost_matrix: Optional[List[List[float]]] = None

class ServiceAreaDistancesResponse(BaseModel):
    """Response model for service area distance calculations."""
    center_postal_code: str
    total_locations: int
    successful_calculations: int
    failed_calculations: int
    distances: dict
    summary_stats: dict

# API Endpoints
@router.post("/route", response_model=TravelRouteResponse)
async def calculate_route(
    request: RouteRequest,
    distance_service: DistanceCalculationService = Depends(get_distance_calculation_service),
    current_user = Depends(get_current_user)
):
    """
    Calculate detailed route between two postal codes.
    
    Provides comprehensive routing information including:
    - Distance and travel time estimates
    - Traffic-adjusted duration
    - Cost estimation
    - Route optimization
    """
    try:
        # Convert string to enum
        mode = TransportMode(request.transport_mode)
        
        route = await distance_service.calculate_route(
            origin_postal_code=request.origin_postal_code,
            destination_postal_code=request.destination_postal_code,
            mode=mode,
            departure_time=request.departure_time
        )
        
        # Convert segments to response format
        segment_responses = [
            RouteSegmentResponse(
                from_postal_code=seg.from_postal_code,
                to_postal_code=seg.to_postal_code,
                distance_km=round(seg.distance_km, 2),
                duration_minutes=seg.duration_minutes,
                mode=seg.mode.value,
                traffic_factor=seg.traffic_factor,
                estimated_cost=seg.estimated_cost
            )
            for seg in route.segments
        ]
        
        return TravelRouteResponse(
            origin=route.origin,
            destination=route.destination,
            total_distance_km=round(route.total_distance_km, 2),
            total_duration_minutes=route.total_duration_minutes,
            estimated_cost=route.estimated_cost,
            segments=segment_responses,
            mode=route.mode.value,
            optimal=route.optimal,
            traffic_conditions=route.traffic_conditions.value
        )
        
    except ValidationError as e:
        logger.warning(f"Validation error in route calculation: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error calculating route: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/distance-matrix", response_model=DistanceMatrixResponse)
async def calculate_distance_matrix(
    request: DistanceMatrixRequest,
    include_costs: bool = Query(False, description="Include cost estimates"),
    distance_service: DistanceCalculationService = Depends(get_distance_calculation_service),
    current_user = Depends(get_current_user)
):
    """
    Calculate distance matrix for multiple origins and destinations.
    
    Useful for:
    - Bulk distance calculations
    - Service area analysis
    - Route optimization preprocessing
    """
    try:
        mode = TransportMode(request.transport_mode)
        
        matrix = await distance_service.calculate_distance_matrix(
            origins=request.origins,
            destinations=request.destinations,
            mode=mode
        )
        
        # Calculate costs if requested
        cost_matrix = None
        if include_costs:
            cost_matrix = distance_service.estimate_travel_cost_matrix(matrix)
        
        return DistanceMatrixResponse(
            origins=matrix.origins,
            destinations=matrix.destinations,
            distances_km=[[round(d, 2) if d is not None else None for d in row] for row in matrix.distances_km],
            durations_minutes=matrix.durations_minutes,
            mode=matrix.mode.value,
            cost_matrix=cost_matrix
        )
        
    except ValidationError as e:
        logger.warning(f"Validation error in distance matrix: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error calculating distance matrix: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/optimal-route", response_model=TravelRouteResponse)
async def find_optimal_route(
    request: OptimalRouteRequest,
    distance_service: DistanceCalculationService = Depends(get_distance_calculation_service),
    current_user = Depends(get_current_user)
):
    """
    Find optimal route visiting multiple destinations.
    
    Uses optimization algorithms to minimize total travel time or distance
    when visiting multiple locations. Useful for:
    - Tutor scheduling multiple appointments
    - Service route planning
    - Multi-location visits
    """
    try:
        mode = TransportMode(request.transport_mode)
        
        route = await distance_service.find_optimal_route(
            origin=request.origin,
            destinations=request.destinations,
            mode=mode,
            return_to_origin=request.return_to_origin
        )
        
        segment_responses = [
            RouteSegmentResponse(
                from_postal_code=seg.from_postal_code,
                to_postal_code=seg.to_postal_code,
                distance_km=round(seg.distance_km, 2),
                duration_minutes=seg.duration_minutes,
                mode=seg.mode.value,
                traffic_factor=seg.traffic_factor,
                estimated_cost=seg.estimated_cost
            )
            for seg in route.segments
        ]
        
        return TravelRouteResponse(
            origin=route.origin,
            destination=route.destination,
            total_distance_km=round(route.total_distance_km, 2),
            total_duration_minutes=route.total_duration_minutes,
            estimated_cost=route.estimated_cost,
            segments=segment_responses,
            mode=route.mode.value,
            optimal=route.optimal,
            traffic_conditions=route.traffic_conditions.value
        )
        
    except ValidationError as e:
        logger.warning(f"Validation error in optimal route: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except BusinessLogicError as e:
        logger.warning(f"Business logic error in optimal route: {str(e)}")
        raise HTTPException(status_code=422, detail=str(e))
    except Exception as e:
        logger.error(f"Error finding optimal route: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/service-area-distances", response_model=ServiceAreaDistancesResponse)
async def calculate_service_area_distances(
    center_postal_code: str = Query(..., description="Center postal code"),
    postal_codes: List[str] = Query(..., description="Postal codes in service area"),
    transport_mode: str = Query("driving", description="Transport mode"),
    distance_service: DistanceCalculationService = Depends(get_distance_calculation_service),
    current_user = Depends(get_current_user)
):
    """
    Calculate distances from center to all postal codes in a service area.
    
    Optimized for batch processing of service area coverage calculations.
    Returns detailed distance and time information for each postal code.
    """
    try:
        # Validate inputs
        if len(postal_codes) > 100:
            raise HTTPException(status_code=400, detail="Maximum 100 postal codes allowed")
        
        mode = TransportMode(transport_mode)
        
        distances = await distance_service.calculate_service_area_distances(
            center_postal_code=center_postal_code.strip().upper(),
            postal_codes=[pc.strip().upper() for pc in postal_codes],
            mode=mode
        )
        
        # Calculate summary statistics
        successful = sum(1 for result in distances.values() if result.get('distance_km') is not None)
        failed = len(distances) - successful
        
        valid_distances = [result['distance_km'] for result in distances.values() if result.get('distance_km') is not None]
        valid_durations = [result['duration_minutes'] for result in distances.values() if result.get('duration_minutes') is not None]
        
        summary_stats = {
            'avg_distance_km': round(sum(valid_distances) / len(valid_distances), 2) if valid_distances else 0,
            'max_distance_km': round(max(valid_distances), 2) if valid_distances else 0,
            'min_distance_km': round(min(valid_distances), 2) if valid_distances else 0,
            'avg_duration_minutes': round(sum(valid_durations) / len(valid_durations), 1) if valid_durations else 0,
            'max_duration_minutes': max(valid_durations) if valid_durations else 0,
            'min_duration_minutes': min(valid_durations) if valid_durations else 0
        }
        
        return ServiceAreaDistancesResponse(
            center_postal_code=center_postal_code.strip().upper(),
            total_locations=len(postal_codes),
            successful_calculations=successful,
            failed_calculations=failed,
            distances=distances,
            summary_stats=summary_stats
        )
        
    except ValidationError as e:
        logger.warning(f"Validation error in service area distances: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error calculating service area distances: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/travel-time-estimate")
async def get_travel_time_estimate(
    origin: str = Query(..., description="Origin postal code"),
    destination: str = Query(..., description="Destination postal code"),
    departure_time: Optional[datetime] = Query(None, description="Departure time"),
    transport_mode: str = Query("driving", description="Transport mode"),
    distance_service: DistanceCalculationService = Depends(get_distance_calculation_service),
    current_user = Depends(get_current_user)
):
    """
    Get quick travel time estimate between two postal codes.
    
    Lightweight endpoint for simple time estimates with traffic consideration.
    """
    try:
        mode = TransportMode(transport_mode)
        
        route = await distance_service.calculate_route(
            origin_postal_code=origin.strip().upper(),
            destination_postal_code=destination.strip().upper(),
            mode=mode,
            departure_time=departure_time
        )
        
        return {
            "origin": route.origin,
            "destination": route.destination,
            "distance_km": round(route.total_distance_km, 2),
            "duration_minutes": route.total_duration_minutes,
            "traffic_conditions": route.traffic_conditions.value,
            "estimated_cost": route.estimated_cost,
            "departure_time": departure_time.isoformat() if departure_time else None
        }
        
    except ValidationError as e:
        logger.warning(f"Validation error in travel time estimate: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error getting travel time estimate: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/cost-estimate")
async def get_travel_cost_estimate(
    distance_km: float = Query(..., ge=0, description="Distance in kilometers"),
    transport_mode: str = Query("driving", description="Transport mode"),
    custom_rate: Optional[float] = Query(None, ge=0, description="Custom rate per km"),
    distance_service: DistanceCalculationService = Depends(get_distance_calculation_service),
    current_user = Depends(get_current_user)
):
    """
    Get travel cost estimate based on distance and transport mode.
    
    Useful for budgeting and cost planning for tutoring sessions.
    """
    try:
        mode = TransportMode(transport_mode)
        
        if custom_rate is not None:
            cost = distance_km * custom_rate
        else:
            cost = distance_service._estimate_travel_cost(distance_km, mode)
        
        return {
            "distance_km": distance_km,
            "transport_mode": transport_mode,
            "estimated_cost_cad": round(cost, 2),
            "rate_per_km": custom_rate if custom_rate is not None else distance_service._get_default_cost_per_km(mode),
            "currency": "CAD"
        }
        
    except Exception as e:
        logger.error(f"Error calculating cost estimate: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")