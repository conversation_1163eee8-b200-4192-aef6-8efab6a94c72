"""
Admin Translation Management API Endpoints

Provides administrative interface for managing translations, templates,
and translation quality control.
"""

from fastapi import APIRouter, Depends, HTTPException, Query, Body
from typing import List, Dict, Any, Optional
import json
import os
from datetime import datetime, timezone
from pathlib import Path
import re

from app.core.dependencies import get_current_user, get_database
from app.core.authorization import require_role
from app.models.user_models import User
from app.services.auth_service import AuthService
from app.core.logging import Tutor<PERSON>ideLogger

logger = TutorAideLogger.get_logger(__name__)
router = APIRouter()

# Translation file paths
TRANSLATION_DIR = Path(__file__).parent.parent.parent.parent / "frontend" / "src" / "locales"
EN_FILE = TRANSLATION_DIR / "en.json"
FR_FILE = TRANSLATION_DIR / "fr.json"

class TranslationKey:
    def __init__(self, key: str, category: str, en: str = "", fr: str = "", description: str = ""):
        self.key = key
        self.category = category
        self.en = en
        self.fr = fr
        self.description = description
        self.last_modified = datetime.now(timezone.utc).isoformat()
        self.modified_by = "admin"

    def to_dict(self):
        return {
            "key": self.key,
            "category": self.category,
            "en": self.en,
            "fr": self.fr,
            "description": self.description,
            "last_modified": self.last_modified,
            "modified_by": self.modified_by
        }

def load_translation_file(file_path: Path) -> Dict[str, Any]:
    """Load and parse a translation JSON file."""
    try:
        if file_path.exists():
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    except Exception as e:
        logger.error(f"Failed to load translation file {file_path}: {e}")
        return {}

def save_translation_file(file_path: Path, data: Dict[str, Any]) -> bool:
    """Save translation data to JSON file with proper formatting."""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2, sort_keys=True)
        return True
    except Exception as e:
        logger.error(f"Failed to save translation file {file_path}: {e}")
        return False

def flatten_translations(data: Dict[str, Any], prefix: str = "") -> Dict[str, str]:
    """Flatten nested translation structure into dot notation keys."""
    flattened = {}
    for key, value in data.items():
        full_key = f"{prefix}.{key}" if prefix else key
        if isinstance(value, dict):
            flattened.update(flatten_translations(value, full_key))
        else:
            flattened[full_key] = str(value)
    return flattened

def unflatten_translations(flattened: Dict[str, str]) -> Dict[str, Any]:
    """Convert flattened dot notation back to nested structure."""
    result = {}
    for key, value in flattened.items():
        parts = key.split('.')
        current = result
        for part in parts[:-1]:
            if part not in current:
                current[part] = {}
            current = current[part]
        current[parts[-1]] = value
    return result

def get_category_from_key(key: str) -> str:
    """Extract category from translation key (first part before dot)."""
    return key.split('.')[0] if '.' in key else 'general'

def validate_translation_content(content: str, variables: List[str] = None) -> List[str]:
    """Validate translation content for common issues."""
    issues = []
    
    # Check for unmatched placeholders
    placeholders = re.findall(r'\{\{[^}]+\}\}', content)
    unmatched_braces = re.findall(r'(?:\{[^{]|\}[^}]|[^{]\{|[^}]\})', content)
    
    if unmatched_braces:
        issues.append("Unmatched braces found")
    
    # Check variable consistency if provided
    if variables:
        found_vars = [match.strip('{}').strip() for match in placeholders]
        missing_vars = set(variables) - set(found_vars)
        extra_vars = set(found_vars) - set(variables)
        
        if missing_vars:
            issues.append(f"Missing variables: {', '.join(missing_vars)}")
        if extra_vars:
            issues.append(f"Unexpected variables: {', '.join(extra_vars)}")
    
    return issues

@router.get("/stats")
async def get_translation_stats(
    current_user: User = Depends(get_current_user)
):
    """Get comprehensive translation statistics."""
    require_role(current_user, ["manager"])
    
    try:
        en_data = load_translation_file(EN_FILE)
        fr_data = load_translation_file(FR_FILE)
        
        en_flat = flatten_translations(en_data)
        fr_flat = flatten_translations(fr_data)
        
        all_keys = set(en_flat.keys()) | set(fr_flat.keys())
        categories = set(get_category_from_key(key) for key in all_keys)
        
        # Calculate completion stats
        en_missing = [key for key in all_keys if key not in en_flat or not en_flat[key]]
        fr_missing = [key for key in all_keys if key not in fr_flat or not fr_flat[key]]
        
        stats = {
            "languages": ["en", "fr"],
            "total_keys_by_language": {
                "en": len(en_flat),
                "fr": len(fr_flat)
            },
            "completion_percentage": {
                "en": ((len(all_keys) - len(en_missing)) / len(all_keys) * 100) if all_keys else 100,
                "fr": ((len(all_keys) - len(fr_missing)) / len(all_keys) * 100) if all_keys else 100
            },
            "missing_keys_count": {
                "en": len(en_missing),
                "fr": len(fr_missing)
            },
            "categories": sorted(list(categories))
        }
        
        return {
            "success": True,
            "data": stats,
            "message": "Translation statistics retrieved"
        }
        
    except Exception as e:
        logger.error(f"Failed to get translation stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve translation statistics")

@router.get("/keys")
async def get_translation_keys(
    category: Optional[str] = Query(None, description="Filter by category"),
    search: Optional[str] = Query(None, description="Search in keys or values"),
    missing_only: bool = Query(False, description="Show only missing translations"),
    limit: int = Query(100, ge=1, le=1000),
    offset: int = Query(0, ge=0),
    current_user: User = Depends(get_current_user)
):
    """Get translation keys with filtering and pagination."""
    require_role(current_user, ["manager"])
    
    try:
        en_data = load_translation_file(EN_FILE)
        fr_data = load_translation_file(FR_FILE)
        
        en_flat = flatten_translations(en_data)
        fr_flat = flatten_translations(fr_data)
        
        all_keys = set(en_flat.keys()) | set(fr_flat.keys())
        
        # Build translation key objects
        translation_keys = []
        for key in sorted(all_keys):
            en_value = en_flat.get(key, "")
            fr_value = fr_flat.get(key, "")
            key_category = get_category_from_key(key)
            
            # Apply filters
            if category and key_category != category:
                continue
                
            if missing_only and en_value and fr_value:
                continue
                
            if search:
                search_lower = search.lower()
                if not any(search_lower in text.lower() for text in [key, en_value, fr_value, key_category]):
                    continue
            
            translation_keys.append({
                "key": key,
                "category": key_category,
                "en": en_value,
                "fr": fr_value,
                "description": "",  # Could be enhanced with metadata
                "last_modified": datetime.now(timezone.utc).isoformat(),
                "modified_by": "system"
            })
        
        # Apply pagination
        total = len(translation_keys)
        paginated_keys = translation_keys[offset:offset + limit]
        
        return {
            "success": True,
            "data": {
                "keys": paginated_keys,
                "total": total,
                "offset": offset,
                "limit": limit,
                "has_more": (offset + limit) < total
            },
            "message": f"Retrieved {len(paginated_keys)} translation keys"
        }
        
    except Exception as e:
        logger.error(f"Failed to get translation keys: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve translation keys")

@router.put("/keys/{key_path:path}")
async def update_translation_key(
    key_path: str,
    translations: Dict[str, str] = Body(..., description="Language translations"),
    current_user: User = Depends(get_current_user)
):
    """Update a specific translation key."""
    require_role(current_user, ["manager"])
    
    try:
        # Load current data
        en_data = load_translation_file(EN_FILE)
        fr_data = load_translation_file(FR_FILE)
        
        # Update translations
        if "en" in translations:
            en_flat = flatten_translations(en_data)
            en_flat[key_path] = translations["en"]
            en_data = unflatten_translations(en_flat)
            
        if "fr" in translations:
            fr_flat = flatten_translations(fr_data)
            fr_flat[key_path] = translations["fr"]
            fr_data = unflatten_translations(fr_flat)
        
        # Save files
        success = True
        if "en" in translations:
            success = success and save_translation_file(EN_FILE, en_data)
        if "fr" in translations:
            success = success and save_translation_file(FR_FILE, fr_data)
            
        if not success:
            raise HTTPException(status_code=500, detail="Failed to save translation files")
        
        logger.info(f"Translation key '{key_path}' updated by {current_user.email}")
        
        return {
            "success": True,
            "data": {"key": key_path, "updated": list(translations.keys())},
            "message": "Translation updated successfully"
        }
        
    except Exception as e:
        logger.error(f"Failed to update translation key '{key_path}': {e}")
        raise HTTPException(status_code=500, detail="Failed to update translation")

@router.post("/keys")
async def create_translation_key(
    key: str = Body(..., description="Translation key"),
    category: str = Body(..., description="Category"),
    en: str = Body("", description="English translation"),
    fr: str = Body("", description="French translation"),
    description: str = Body("", description="Description"),
    current_user: User = Depends(get_current_user)
):
    """Create a new translation key."""
    require_role(current_user, ["manager"])
    
    try:
        # Validate key format
        if not re.match(r'^[a-zA-Z][a-zA-Z0-9_.]*$', key):
            raise HTTPException(status_code=400, detail="Invalid key format")
        
        # Load current data
        en_data = load_translation_file(EN_FILE)
        fr_data = load_translation_file(FR_FILE)
        
        # Check if key already exists
        en_flat = flatten_translations(en_data)
        fr_flat = flatten_translations(fr_data)
        
        if key in en_flat or key in fr_flat:
            raise HTTPException(status_code=409, detail="Translation key already exists")
        
        # Add new translations
        if en:
            en_flat[key] = en
            en_data = unflatten_translations(en_flat)
            
        if fr:
            fr_flat[key] = fr
            fr_data = unflatten_translations(fr_flat)
        
        # Save files
        success = True
        if en:
            success = success and save_translation_file(EN_FILE, en_data)
        if fr:
            success = success and save_translation_file(FR_FILE, fr_data)
            
        if not success:
            raise HTTPException(status_code=500, detail="Failed to save translation files")
        
        logger.info(f"Translation key '{key}' created by {current_user.email}")
        
        return {
            "success": True,
            "data": {"key": key, "category": category},
            "message": "Translation key created successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create translation key '{key}': {e}")
        raise HTTPException(status_code=500, detail="Failed to create translation")

@router.delete("/keys/{key_path:path}")
async def delete_translation_key(
    key_path: str,
    current_user: User = Depends(get_current_user)
):
    """Delete a translation key from all languages."""
    require_role(current_user, ["manager"])
    
    try:
        # Load current data
        en_data = load_translation_file(EN_FILE)
        fr_data = load_translation_file(FR_FILE)
        
        en_flat = flatten_translations(en_data)
        fr_flat = flatten_translations(fr_data)
        
        # Remove key
        removed_from = []
        if key_path in en_flat:
            del en_flat[key_path]
            en_data = unflatten_translations(en_flat)
            removed_from.append("en")
            
        if key_path in fr_flat:
            del fr_flat[key_path]
            fr_data = unflatten_translations(fr_flat)
            removed_from.append("fr")
        
        if not removed_from:
            raise HTTPException(status_code=404, detail="Translation key not found")
        
        # Save files
        success = True
        if "en" in removed_from:
            success = success and save_translation_file(EN_FILE, en_data)
        if "fr" in removed_from:
            success = success and save_translation_file(FR_FILE, fr_data)
            
        if not success:
            raise HTTPException(status_code=500, detail="Failed to save translation files")
        
        logger.info(f"Translation key '{key_path}' deleted by {current_user.email}")
        
        return {
            "success": True,
            "data": {"key": key_path, "removed_from": removed_from},
            "message": "Translation key deleted successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete translation key '{key_path}': {e}")
        raise HTTPException(status_code=500, detail="Failed to delete translation")

@router.post("/validate")
async def validate_translations(
    current_user: User = Depends(get_current_user)
):
    """Validate all translations for consistency and issues."""
    require_role(current_user, ["manager"])
    
    try:
        en_data = load_translation_file(EN_FILE)
        fr_data = load_translation_file(FR_FILE)
        
        en_flat = flatten_translations(en_data)
        fr_flat = flatten_translations(fr_data)
        
        all_keys = set(en_flat.keys()) | set(fr_flat.keys())
        
        errors = []
        warnings = []
        missing_keys = {"en": [], "fr": []}
        
        for key in all_keys:
            en_value = en_flat.get(key, "")
            fr_value = fr_flat.get(key, "")
            
            # Check for missing translations
            if not en_value:
                missing_keys["en"].append(key)
            if not fr_value:
                missing_keys["fr"].append(key)
            
            # Validate placeholder consistency
            if en_value and fr_value:
                en_placeholders = set(re.findall(r'\{\{[^}]+\}\}', en_value))
                fr_placeholders = set(re.findall(r'\{\{[^}]+\}\}', fr_value))
                
                if en_placeholders != fr_placeholders:
                    errors.append(f"Placeholder mismatch in '{key}': EN{en_placeholders} != FR{fr_placeholders}")
            
            # Check for potential issues
            if en_value:
                en_issues = validate_translation_content(en_value)
                for issue in en_issues:
                    warnings.append(f"EN '{key}': {issue}")
                    
            if fr_value:
                fr_issues = validate_translation_content(fr_value)
                for issue in fr_issues:
                    warnings.append(f"FR '{key}': {issue}")
        
        is_valid = len(errors) == 0
        
        return {
            "success": True,
            "data": {
                "is_valid": is_valid,
                "errors": errors,
                "warnings": warnings,
                "missing_keys": missing_keys,
                "statistics": {
                    "total_keys": len(all_keys),
                    "complete_keys": len([k for k in all_keys if en_flat.get(k) and fr_flat.get(k)]),
                    "error_count": len(errors),
                    "warning_count": len(warnings)
                }
            },
            "message": "Translation validation completed"
        }
        
    except Exception as e:
        logger.error(f"Failed to validate translations: {e}")
        raise HTTPException(status_code=500, detail="Failed to validate translations")

@router.post("/export")
async def export_translations(
    format: str = Body("json", description="Export format (json, csv)"),
    languages: List[str] = Body(["en", "fr"], description="Languages to export"),
    current_user: User = Depends(get_current_user)
):
    """Export translations in various formats."""
    require_role(current_user, ["manager"])
    
    try:
        if format not in ["json", "csv"]:
            raise HTTPException(status_code=400, detail="Unsupported export format")
        
        translations = {}
        
        if "en" in languages:
            translations["en"] = load_translation_file(EN_FILE)
        if "fr" in languages:
            translations["fr"] = load_translation_file(FR_FILE)
        
        logger.info(f"Translation export requested by {current_user.email} (format: {format})")
        
        return {
            "success": True,
            "data": {
                "format": format,
                "languages": languages,
                "translations": translations,
                "exported_at": datetime.now(timezone.utc).isoformat()
            },
            "message": "Translations exported successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to export translations: {e}")
        raise HTTPException(status_code=500, detail="Failed to export translations")

@router.post("/reload")
async def reload_translations(
    current_user: User = Depends(get_current_user)
):
    """Reload translation files (useful after external changes)."""
    require_role(current_user, ["manager"])
    
    try:
        # Verify files exist and are readable
        en_exists = EN_FILE.exists()
        fr_exists = FR_FILE.exists()
        
        if not en_exists or not fr_exists:
            raise HTTPException(
                status_code=404, 
                detail=f"Translation files not found: EN={en_exists}, FR={fr_exists}"
            )
        
        # Test loading
        en_data = load_translation_file(EN_FILE)
        fr_data = load_translation_file(FR_FILE)
        
        logger.info(f"Translation files reloaded by {current_user.email}")
        
        return {
            "success": True,
            "data": {
                "en_keys": len(flatten_translations(en_data)),
                "fr_keys": len(flatten_translations(fr_data)),
                "reloaded_at": datetime.now(timezone.utc).isoformat()
            },
            "message": "Translation files reloaded successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to reload translations: {e}")
        raise HTTPException(status_code=500, detail="Failed to reload translations")