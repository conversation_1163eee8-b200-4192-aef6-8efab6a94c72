import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { DollarSign, Users, CreditCard, TrendingUp } from 'lucide-react';
import { PageHeader } from '../../components/layout/PageHeader';
import { Tabs } from '../../components/common/Tabs';
import { Card } from '../../components/common/Card';
import { TutorPaymentManagement } from '../../components/billing/TutorPaymentManagement';
import { ParentPaymentTracking } from '../../components/billing/ParentPaymentTracking';
import { PaymentStatistics } from '../../components/billing/PaymentStatistics';
import { useAuth } from '../../contexts/AuthContext';
import { UserRoleType } from '../../types/auth';

const PaymentsPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('tutor-payments');

  const isManager = user?.activeRole === UserRoleType.MANAGER;
  const isTutor = user?.activeRole === UserRoleType.TUTOR;

  const tabs = [
    { 
      id: 'tutor-payments', 
      label: isTutor ? t('billing.myPayments') : t('billing.tutorPayments'), 
      icon: <Users className="w-4 h-4" /> 
    },
  ];

  if (isManager) {
    tabs.push(
      { 
        id: 'parent-tracking', 
        label: t('billing.parentPayments'), 
        icon: <CreditCard className="w-4 h-4" /> 
      },
      { 
        id: 'statistics', 
        label: t('billing.paymentStatistics'), 
        icon: <TrendingUp className="w-4 h-4" /> 
      }
    );
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title={t('billing.payments')}
        description={
          isManager 
            ? t('billing.managerPaymentDescription') 
            : t('billing.tutorPaymentDescription')
        }
      />

      <Tabs
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={setActiveTab}
      />

      {/* Content based on active tab */}
      {activeTab === 'tutor-payments' && (
        <TutorPaymentManagement
          tutorId={isTutor ? user?.userId : undefined}
          showApprovalActions={isManager}
        />
      )}

      {activeTab === 'parent-tracking' && isManager && (
        <ParentPaymentTracking />
      )}

      {activeTab === 'statistics' && isManager && (
        <PaymentStatistics />
      )}
    </div>
  );
};

export default PaymentsPage;