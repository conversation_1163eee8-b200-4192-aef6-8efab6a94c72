-- Add missing timestamp columns to tables
-- Version: 005
-- Description: Add updated_at and deleted_at columns where missing
-- Author: TutorAide Development Team
-- Date: 2025-06-10

-- Add updated_at and deleted_at to user_roles
ALTER TABLE user_roles
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE;

-- Add triggers to automatically update updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for user_roles
DROP TRIGGER IF EXISTS update_user_roles_updated_at ON user_roles;
CREATE TRIGGER update_user_roles_updated_at 
BEFORE UPDATE ON user_roles 
FOR EACH ROW 
EXECUTE FUNCTION update_updated_at_column();

-- Create trigger for user_accounts
DROP TRIGGER IF EXISTS update_user_accounts_updated_at ON user_accounts;
CREATE TRIGGER update_user_accounts_updated_at 
BEFORE UPDATE ON user_accounts 
FOR EACH ROW 
EXECUTE FUNCTION update_updated_at_column();

-- Add updated_at column to any other tables that might be missing it
DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (
        SELECT DISTINCT t.table_name
        FROM information_schema.tables t
        WHERE t.table_schema = 'public'
        AND t.table_type = 'BASE TABLE'
        AND t.table_name NOT IN ('migrations')
        AND EXISTS (
            SELECT 1 FROM information_schema.columns c 
            WHERE c.table_name = t.table_name 
            AND c.column_name = 'created_at'
        )
        AND NOT EXISTS (
            SELECT 1 FROM information_schema.columns c 
            WHERE c.table_name = t.table_name 
            AND c.column_name = 'updated_at'
        )
    )
    LOOP
        EXECUTE format('ALTER TABLE %I ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP', r.table_name);
        EXECUTE format('CREATE TRIGGER update_%I_updated_at BEFORE UPDATE ON %I FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()', 
                      r.table_name, r.table_name);
        RAISE NOTICE 'Added updated_at column and trigger to %', r.table_name;
    END LOOP;
END $$;