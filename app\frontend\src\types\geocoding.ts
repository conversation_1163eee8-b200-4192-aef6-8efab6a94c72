export interface GeocodeResponse {
  postal_code: string;
  latitude: number;
  longitude: number;
  city?: string;
  province?: string;
  area?: string;
  source?: string;
}

export interface BatchGeocodeResponse {
  results: Record<string, GeocodeResponse | null>;
  success_count: number;
  error_count: number;
}

export interface DistanceCalculationResponse {
  postal_code1: string;
  postal_code2: string;
  distance_km: number;
  estimated_travel_time_minutes: number;
  travel_mode: string;
}

export interface NearestLocationResponse {
  center_coordinates: {
    lat: number;
    lng: number;
  };
  nearest_locations: any[];
  search_radius_km?: number;
}

export interface PostalCodeRadiusResponse {
  center_postal_code: string;
  center_coordinates: {
    lat: number;
    lng: number;
  };
  radius_km: number;
  postal_codes: Array<{
    postal_code: string;
    lat: number;
    lng: number;
    area?: string;
    distance_km: number;
  }>;
  total_found: number;
}

export interface RouteInfoResponse {
  origin_postal_code: string;
  destination_postal_code: string;
  distance_km: number;
  duration_minutes: number;
  mode: string;
  estimated: boolean;
  route_summary?: string;
  straight_line_distance_km?: number;
}

export interface MapBounds {
  north: number;
  south: number;
  east: number;
  west: number;
}

export interface MapViewport {
  latitude: number;
  longitude: number;
  zoom: number;
}

export interface TutorMapLocation {
  tutor_id: number;
  latitude: number;
  longitude: number;
  postal_code: string;
  area?: string;
  distance_km?: number;
  service_types: string[];
  availability_status: 'available' | 'limited' | 'busy';
}

export interface MapSearchResult {
  type: 'postal_code' | 'address' | 'area';
  value: string;
  display_name: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  postal_code?: string;
  area?: string;
}

export interface MapFilters {
  subjectAreas: string[];
  serviceTypes: string[];
  maxDistance: number;
  minRating: number;
  maxHourlyRate: number;
  availability: 'all' | 'available' | 'limited';
  priceRange?: {
    min: number;
    max: number;
  };
  sessionTypes?: string[];
}

export interface GeolocationError {
  code: number;
  message: string;
  type: 'permission_denied' | 'position_unavailable' | 'timeout' | 'network_error';
}

export interface ServiceArea {
  tutor_id: number;
  postal_codes: string[];
  radius_km: number;
  center_postal_code: string;
  service_types: string[];
  is_active: boolean;
}

export interface TravelTime {
  distance_km: number;
  duration_minutes: number;
  mode: 'driving' | 'walking' | 'transit';
  estimated: boolean;
  traffic_factor?: number;
}

export interface MapMarkerCluster {
  id: string;
  center: {
    lat: number;
    lng: number;
  };
  count: number;
  tutors: TutorMapLocation[];
  bounds: MapBounds;
}