"""
Repository for subscription database operations.
"""

import asyncpg
from typing import List, Optional
from datetime import date, datetime

from app.models.subscription_models import SubscriptionUsage
from app.database.repositories.base import BaseRepository


class SubscriptionRepository(BaseRepository):
    """Repository for subscription operations."""
    
    async def get_client_active_subscriptions(
        self, 
        db: asyncpg.Connection, 
        client_id: int
    ) -> List[dict]:
        """Get active subscriptions for a client."""
        query = """
        SELECT s.*
        FROM client_subscriptions s
        WHERE s.client_id = $1
        AND s.status = 'active'
        AND s.hours_remaining > 0
        AND (s.expiry_date IS NULL OR s.expiry_date > CURRENT_DATE)
        ORDER BY s.created_at DESC
        """
        
        rows = await db.fetch(query, client_id)
        return [dict(row) for row in rows]
    
    async def record_subscription_usage(
        self,
        db: asyncpg.Connection,
        usage: SubscriptionUsage
    ) -> None:
        """Record subscription hour usage."""
        # Insert usage record
        usage_query = """
        INSERT INTO subscription_usage (
            subscription_id, appointment_id, hours_used, usage_date
        ) VALUES ($1, $2, $3, $4)
        """
        
        await db.execute(
            usage_query,
            usage.subscription_id,
            usage.appointment_id,
            usage.hours_used,
            usage.usage_date
        )
        
        # Update subscription hours remaining
        update_query = """
        UPDATE client_subscriptions
        SET hours_remaining = hours_remaining - $2,
            updated_at = CURRENT_TIMESTAMP
        WHERE subscription_id = $1
        """
        
        await db.execute(update_query, usage.subscription_id, usage.hours_used)


# Dependency injection
def get_subscription_repository() -> SubscriptionRepository:
    """Get subscription repository instance."""
    return SubscriptionRepository()