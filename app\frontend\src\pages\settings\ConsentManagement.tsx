import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Shield, Mail, BarChart, Settings, CheckCircle, XCircle, Clock, Info, History, FileText } from 'lucide-react';
import { Card } from '../../components/common/Card';
import Button from '../../components/common/Button';
import { Badge } from '../../components/common/Badge';
import { Modal } from '../../components/common/Modal';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import api from '../../services/api';
import toast from 'react-hot-toast';
import { format } from 'date-fns';

interface ConsentStatus {
  consent_type: string;
  title: string;
  level: 'level_1_mandatory' | 'level_2_optional';
  category: 'legal' | 'marketing' | 'analytics' | 'functional';
  status: 'granted' | 'withdrawn' | 'expired';
  granted_at?: string;
  withdrawn_at?: string;
  expires_at?: string;
  document_version: string;
  can_withdraw: boolean;
}

interface ConsentHistoryEntry {
  action: string;
  timestamp: string;
  document_version: string;
  ip_address?: string;
  reason?: string;
}

interface ConsentDocument {
  consent_type: string;
  title: string;
  content: string;
  version: string;
  effective_date: string;
}

const ConsentManagement: React.FC = () => {
  const { t, i18n } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [consents, setConsents] = useState<ConsentStatus[]>([]);
  const [selectedConsent, setSelectedConsent] = useState<ConsentStatus | null>(null);
  const [showHistoryModal, setShowHistoryModal] = useState(false);
  const [showDocumentModal, setShowDocumentModal] = useState(false);
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);
  const [history, setHistory] = useState<ConsentHistoryEntry[]>([]);
  const [document, setDocument] = useState<ConsentDocument | null>(null);
  const [withdrawReason, setWithdrawReason] = useState('');
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    fetchConsents();
  }, []);

  const fetchConsents = async () => {
    try {
      setLoading(true);
      const response = await api.get<{ consents: ConsentStatus[] }>('/consent/summary');
      setConsents(response.data.consents);
    } catch (error) {
      console.error('Error fetching consents:', error);
      toast.error(t('consent.errors.fetchFailed'));
    } finally {
      setLoading(false);
    }
  };

  const fetchHistory = async (consentType: string) => {
    try {
      const response = await api.get<{ history: ConsentHistoryEntry[] }>(
        `/consent/history?consent_type=${consentType}`
      );
      setHistory(response.data.history);
      setShowHistoryModal(true);
    } catch (error) {
      console.error('Error fetching history:', error);
      toast.error(t('consent.errors.historyFetchFailed'));
    }
  };

  const fetchDocument = async (consent: ConsentStatus) => {
    try {
      const response = await api.get<ConsentDocument[]>(
        `/consent/documents?consent_type=${consent.consent_type}&language=${i18n.language}`
      );
      if (response.data.length > 0) {
        setDocument(response.data[0]);
        setSelectedConsent(consent);
        setShowDocumentModal(true);
      }
    } catch (error) {
      console.error('Error fetching document:', error);
      toast.error(t('consent.errors.documentFetchFailed'));
    }
  };

  const handleWithdraw = async () => {
    if (!selectedConsent) return;

    try {
      setSubmitting(true);
      await api.post('/consent/withdraw', {
        consent_type: selectedConsent.consent_type,
        reason: withdrawReason
      });
      
      toast.success(t('consent.success.withdrawn'));
      setShowWithdrawModal(false);
      setWithdrawReason('');
      setSelectedConsent(null);
      fetchConsents();
    } catch (error) {
      console.error('Error withdrawing consent:', error);
      toast.error(t('consent.errors.withdrawFailed'));
    } finally {
      setSubmitting(false);
    }
  };

  const handleAccept = async (consentType: string) => {
    try {
      setSubmitting(true);
      await api.post('/consent/accept', {
        consent_type: consentType,
        language: i18n.language
      });
      
      toast.success(t('consent.success.accepted'));
      fetchConsents();
    } catch (error) {
      console.error('Error accepting consent:', error);
      toast.error(t('consent.errors.acceptFailed'));
    } finally {
      setSubmitting(false);
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'legal':
        return <Shield className="w-5 h-5" />;
      case 'marketing':
        return <Mail className="w-5 h-5" />;
      case 'analytics':
        return <BarChart className="w-5 h-5" />;
      case 'functional':
        return <Settings className="w-5 h-5" />;
      default:
        return <Info className="w-5 h-5" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'legal':
        return 'primary';
      case 'marketing':
        return 'info';
      case 'analytics':
        return 'warning';
      case 'functional':
        return 'secondary';
      default:
        return 'secondary';
    }
  };

  const getStatusBadge = (consent: ConsentStatus) => {
    switch (consent.status) {
      case 'granted':
        return <Badge variant="success" icon={<CheckCircle className="w-3 h-3" />}>{t('consent.status.granted')}</Badge>;
      case 'withdrawn':
        return <Badge variant="secondary" icon={<XCircle className="w-3 h-3" />}>{t('consent.status.withdrawn')}</Badge>;
      case 'expired':
        return <Badge variant="error" icon={<Clock className="w-3 h-3" />}>{t('consent.status.expired')}</Badge>;
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  const mandatoryConsents = consents.filter(c => c.level === 'level_1_mandatory');
  const optionalConsents = consents.filter(c => c.level === 'level_2_optional');

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-semibold text-gray-900">{t('consent.management.title')}</h1>
        <p className="mt-2 text-sm text-gray-600">{t('consent.management.description')}</p>
      </div>

      {/* Info Banner */}
      <Card className="bg-blue-50 border-blue-200">
        <div className="flex items-start p-4">
          <Info className="w-5 h-5 text-blue-400 mt-0.5" />
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">
              {t('consent.management.infoTitle')}
            </h3>
            <p className="mt-1 text-sm text-blue-700">
              {t('consent.management.infoDescription')}
            </p>
          </div>
        </div>
      </Card>

      {/* Mandatory Consents */}
      <div>
        <h2 className="text-lg font-medium text-gray-900 mb-4">
          {t('consent.management.mandatoryConsents')}
        </h2>
        <div className="space-y-4">
          {mandatoryConsents.map((consent) => (
            <Card key={consent.consent_type} className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-lg bg-${getCategoryColor(consent.category)}-50`}>
                      {getCategoryIcon(consent.category)}
                    </div>
                    <div>
                      <h3 className="text-base font-medium text-gray-900">{consent.title}</h3>
                      <p className="text-sm text-gray-500">{t(`consent.categories.${consent.category}`)}</p>
                    </div>
                  </div>
                  
                  <div className="mt-4 flex items-center gap-4">
                    {getStatusBadge(consent)}
                    
                    {consent.granted_at && (
                      <span className="text-sm text-gray-500">
                        {t('consent.grantedOn')}: {format(new Date(consent.granted_at), 'MMM d, yyyy')}
                      </span>
                    )}
                    
                    <span className="text-sm text-gray-500">
                      {t('consent.version')}: {consent.document_version}
                    </span>
                  </div>
                </div>
                
                <div className="flex items-center gap-2 ml-4">
                  <Button
                    variant="ghost"
                    size="sm"
                    leftIcon={<FileText className="w-4 h-4" />}
                    onClick={() => fetchDocument(consent)}
                  >
                    {t('consent.viewDocument')}
                  </Button>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    leftIcon={<History className="w-4 h-4" />}
                    onClick={() => {
                      setSelectedConsent(consent);
                      fetchHistory(consent.consent_type);
                    }}
                  >
                    {t('consent.viewHistory')}
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>

      {/* Optional Consents */}
      <div>
        <h2 className="text-lg font-medium text-gray-900 mb-4">
          {t('consent.management.optionalConsents')}
        </h2>
        <div className="space-y-4">
          {optionalConsents.map((consent) => (
            <Card key={consent.consent_type} className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-lg bg-${getCategoryColor(consent.category)}-50`}>
                      {getCategoryIcon(consent.category)}
                    </div>
                    <div>
                      <h3 className="text-base font-medium text-gray-900">{consent.title}</h3>
                      <p className="text-sm text-gray-500">{t(`consent.categories.${consent.category}`)}</p>
                    </div>
                  </div>
                  
                  <div className="mt-4 flex items-center gap-4">
                    {getStatusBadge(consent)}
                    
                    {consent.granted_at && (
                      <span className="text-sm text-gray-500">
                        {t('consent.grantedOn')}: {format(new Date(consent.granted_at), 'MMM d, yyyy')}
                      </span>
                    )}
                    
                    {consent.withdrawn_at && (
                      <span className="text-sm text-gray-500">
                        {t('consent.withdrawnOn')}: {format(new Date(consent.withdrawn_at), 'MMM d, yyyy')}
                      </span>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center gap-2 ml-4">
                  <Button
                    variant="ghost"
                    size="sm"
                    leftIcon={<FileText className="w-4 h-4" />}
                    onClick={() => fetchDocument(consent)}
                  >
                    {t('consent.viewDocument')}
                  </Button>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    leftIcon={<History className="w-4 h-4" />}
                    onClick={() => {
                      setSelectedConsent(consent);
                      fetchHistory(consent.consent_type);
                    }}
                  >
                    {t('consent.viewHistory')}
                  </Button>
                  
                  {consent.status === 'granted' && consent.can_withdraw && (
                    <Button
                      variant="secondary"
                      size="sm"
                      leftIcon={<XCircle className="w-4 h-4" />}
                      onClick={() => {
                        setSelectedConsent(consent);
                        setShowWithdrawModal(true);
                      }}
                    >
                      {t('consent.withdraw')}
                    </Button>
                  )}
                  
                  {consent.status === 'withdrawn' && (
                    <Button
                      variant="primary"
                      size="sm"
                      leftIcon={<CheckCircle className="w-4 h-4" />}
                      onClick={() => handleAccept(consent.consent_type)}
                      disabled={submitting}
                    >
                      {t('consent.accept')}
                    </Button>
                  )}
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>

      {/* History Modal */}
      <Modal
        isOpen={showHistoryModal}
        onClose={() => {
          setShowHistoryModal(false);
          setHistory([]);
          setSelectedConsent(null);
        }}
        title={t('consent.history.title')}
        size="lg"
      >
        <div className="space-y-4">
          {history.length === 0 ? (
            <p className="text-center text-gray-500 py-8">{t('consent.history.empty')}</p>
          ) : (
            <div className="space-y-3">
              {history.map((entry, index) => (
                <div key={index} className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg">
                  <div className={`p-2 rounded-full ${
                    entry.action === 'granted' ? 'bg-green-100' : 'bg-gray-100'
                  }`}>
                    {entry.action === 'granted' ? (
                      <CheckCircle className="w-4 h-4 text-green-600" />
                    ) : (
                      <XCircle className="w-4 h-4 text-gray-600" />
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">
                      {t(`consent.actions.${entry.action}`)}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      {format(new Date(entry.timestamp), 'MMM d, yyyy HH:mm')}
                    </p>
                    {entry.reason && (
                      <p className="text-sm text-gray-600 mt-2">
                        {t('consent.reason')}: {entry.reason}
                      </p>
                    )}
                  </div>
                  
                  <div className="text-right">
                    <p className="text-xs text-gray-500">
                      {t('consent.version')}: {entry.document_version}
                    </p>
                    {entry.ip_address && (
                      <p className="text-xs text-gray-500 mt-1">
                        IP: {entry.ip_address}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </Modal>

      {/* Document Modal */}
      <Modal
        isOpen={showDocumentModal}
        onClose={() => {
          setShowDocumentModal(false);
          setDocument(null);
          setSelectedConsent(null);
        }}
        title={document?.title || ''}
        size="xl"
      >
        {document && (
          <div className="space-y-4">
            <div className="prose prose-sm max-w-none">
              <div className="whitespace-pre-wrap text-gray-700 leading-relaxed">
                {document.content}
              </div>
            </div>
            
            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600">
                <strong>{t('consent.effectiveDate')}:</strong>{' '}
                {format(new Date(document.effective_date), 'MMM d, yyyy')}
              </p>
              <p className="text-sm text-gray-600 mt-1">
                <strong>{t('consent.version')}:</strong> {document.version}
              </p>
            </div>
          </div>
        )}
      </Modal>

      {/* Withdraw Modal */}
      <Modal
        isOpen={showWithdrawModal}
        onClose={() => {
          setShowWithdrawModal(false);
          setWithdrawReason('');
          setSelectedConsent(null);
        }}
        title={t('consent.withdraw.title')}
        size="md"
      >
        <div className="space-y-4">
          <p className="text-sm text-gray-600">
            {t('consent.withdraw.confirmation', { consent: selectedConsent?.title })}
          </p>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('consent.withdraw.reasonLabel')}
            </label>
            <textarea
              value={withdrawReason}
              onChange={(e) => setWithdrawReason(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              rows={3}
              placeholder={t('consent.withdraw.reasonPlaceholder')}
            />
          </div>
          
          <div className="flex justify-end gap-3">
            <Button
              variant="secondary"
              onClick={() => {
                setShowWithdrawModal(false);
                setWithdrawReason('');
                setSelectedConsent(null);
              }}
            >
              {t('common.cancel')}
            </Button>
            
            <Button
              variant="primary"
              onClick={handleWithdraw}
              disabled={submitting}
              leftIcon={submitting ? <LoadingSpinner size="sm" /> : <XCircle />}
            >
              {submitting ? t('consent.withdrawing') : t('consent.confirmWithdraw')}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default ConsentManagement;