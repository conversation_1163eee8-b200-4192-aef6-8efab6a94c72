"""
Comprehensive logging and error handling framework for TutorAide.

This module provides structured logging, performance monitoring, security event tracking,
and audit trail capabilities with proper error context management.
"""

import json
import logging
import logging.config
import sys
import traceback
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, asdict

from app.config.settings import settings
from app.core.timezone import now_est, format_est_datetime


class LogLevel(Enum):
    """Log level enumeration."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


@dataclass
class ErrorContext:
    """Context information for error handling and logging."""
    
    user_id: Optional[str] = None
    request_id: Optional[str] = None
    operation: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    additional_data: Optional[Dict[str, Any]] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        """Initialize timestamp if not provided."""
        if self.timestamp is None:
            self.timestamp = now_est()
        
        if self.additional_data is None:
            self.additional_data = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert context to dictionary for logging."""
        context_dict = asdict(self)
        context_dict["timestamp"] = self.timestamp.isoformat()
        return context_dict
    
    @classmethod
    def from_request(cls, request, user_id: Optional[str] = None, operation: Optional[str] = None):
        """Create error context from FastAPI request object."""
        return cls(
            user_id=user_id,
            request_id=request.headers.get("X-Request-ID"),
            operation=operation,
            ip_address=getattr(request.client, 'host', None),
            user_agent=request.headers.get("User-Agent"),
            additional_data={
                "method": request.method,
                "path": str(request.url.path),
                "query_params": dict(request.query_params)
            }
        )


class StructuredFormatter(logging.Formatter):
    """JSON structured log formatter."""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as structured JSON."""
        log_data = {
            "timestamp": format_est_datetime(datetime.fromtimestamp(record.created)),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }
        
        # Add exception information if present
        if record.exc_info:
            log_data["exception"] = self.formatException(record.exc_info)
        
        # Add extra fields from the record
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                          'filename', 'module', 'exc_info', 'exc_text', 'stack_info',
                          'lineno', 'funcName', 'created', 'msecs', 'relativeCreated',
                          'thread', 'threadName', 'processName', 'process', 'getMessage']:
                log_data[key] = value
        
        return json.dumps(log_data, default=str)


class SecurityLogFilter(logging.Filter):
    """Filter to only allow security-related log messages."""
    
    def filter(self, record: logging.LogRecord) -> bool:
        """Filter security logs."""
        return getattr(record, 'log_type', None) == 'security'


class PerformanceLogFilter(logging.Filter):
    """Filter to only allow performance-related log messages."""
    
    def filter(self, record: logging.LogRecord) -> bool:
        """Filter performance logs."""
        return getattr(record, 'log_type', None) == 'performance'


class AuditLogFilter(logging.Filter):
    """Filter to only allow audit-related log messages."""
    
    def filter(self, record: logging.LogRecord) -> bool:
        """Filter audit logs."""
        return getattr(record, 'log_type', None) == 'audit'


class LoggingConfig:
    """Logging configuration management."""
    
    def __init__(self):
        """Initialize logging configuration."""
        self.level = getattr(settings, 'LOG_LEVEL', 'INFO')
        self.format_type = "structured"
        # Always enable console output in Railway/production
        self.enable_console = True  # Changed: always True for Railway
        self.enable_file = True
        self.enable_security_filter = True
        self.enable_performance_filter = True
        self.enable_audit_filter = True
        
        # File paths
        self.log_dir = Path("logs")
        self.log_dir.mkdir(exist_ok=True)
        
        self.app_log_file = self.log_dir / "tutoraide.log"
        self.security_log_file = self.log_dir / "security.log"
        self.performance_log_file = self.log_dir / "performance.log"
        self.audit_log_file = self.log_dir / "audit.log"
        self.error_log_file = self.log_dir / "errors.log"
    
    def get_log_level(self) -> int:
        """Get numeric log level."""
        level_map = {
            "DEBUG": logging.DEBUG,
            "INFO": logging.INFO,
            "WARNING": logging.WARNING,
            "ERROR": logging.ERROR,
            "CRITICAL": logging.CRITICAL
        }
        return level_map.get(self.level, logging.INFO)
    
    def get_logging_config(self) -> Dict[str, Any]:
        """Get complete logging configuration."""
        config = {
            "version": 1,
            "disable_existing_loggers": False,
            "formatters": {
                "structured": {
                    "()": StructuredFormatter,
                },
                "simple": {
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
                }
            },
            "filters": {
                "security": {
                    "()": SecurityLogFilter,
                },
                "performance": {
                    "()": PerformanceLogFilter,
                },
                "audit": {
                    "()": AuditLogFilter,
                }
            },
            "handlers": {
                "error_file": {
                    "class": "logging.handlers.RotatingFileHandler",
                    "filename": str(self.error_log_file),
                    "maxBytes": 10485760,  # 10MB
                    "backupCount": 5,
                    "formatter": "structured",
                    "level": "ERROR"
                },
                "app_file": {
                    "class": "logging.handlers.RotatingFileHandler",
                    "filename": str(self.app_log_file),
                    "maxBytes": 10485760,  # 10MB
                    "backupCount": 5,
                    "formatter": "structured",
                    "level": self.level
                },
                "security_file": {
                    "class": "logging.handlers.RotatingFileHandler",
                    "filename": str(self.security_log_file),
                    "maxBytes": 10485760,  # 10MB
                    "backupCount": 10,  # Keep more security logs
                    "formatter": "structured",
                    "filters": ["security"],
                    "level": "WARNING"
                },
                "performance_file": {
                    "class": "logging.handlers.RotatingFileHandler",
                    "filename": str(self.performance_log_file),
                    "maxBytes": 10485760,  # 10MB
                    "backupCount": 3,
                    "formatter": "structured",
                    "filters": ["performance"],
                    "level": "INFO"
                },
                "audit_file": {
                    "class": "logging.handlers.RotatingFileHandler",
                    "filename": str(self.audit_log_file),
                    "maxBytes": 10485760,  # 10MB
                    "backupCount": 10,  # Keep more audit logs
                    "formatter": "structured",
                    "filters": ["audit"],
                    "level": "INFO"
                }
            },
            "loggers": {
                "tutoraide": {
                    "level": self.level,
                    "handlers": ["app_file", "error_file"],
                    "propagate": False
                },
                "tutoraide.security": {
                    "level": "WARNING",
                    "handlers": ["security_file", "error_file"],
                    "propagate": False
                },
                "tutoraide.performance": {
                    "level": "INFO",
                    "handlers": ["performance_file"],
                    "propagate": False
                },
                "tutoraide.audit": {
                    "level": "INFO",
                    "handlers": ["audit_file"],
                    "propagate": False
                }
            },
            "root": {
                "level": self.level,
                "handlers": ["app_file"]
            }
        }
        
        # Add console handler (always for Railway)
        if self.enable_console:
            config["handlers"]["console"] = {
                "class": "logging.StreamHandler",
                "stream": sys.stdout,
                "formatter": "simple",  # Use simple format for Railway logs
                "level": self.level
            }
            
            # Add console to all loggers
            for logger_config in config["loggers"].values():
                logger_config["handlers"].append("console")
            config["root"]["handlers"].append("console")
        
        return config


class TutorAideLogger:
    """Custom logger for TutorAide application."""
    
    def __init__(self, name: str):
        """Initialize logger with TutorAide namespace."""
        self.name = f"tutoraide.{name}"
        self.logger = logging.getLogger(self.name)
        
        # Ensure logging is configured
        if not hasattr(TutorAideLogger, '_configured'):
            self._configure_logging()
            TutorAideLogger._configured = True
    
    @staticmethod
    def _configure_logging():
        """Configure logging system."""
        config = LoggingConfig()
        logging.config.dictConfig(config.get_logging_config())
    
    @staticmethod
    def get_logger(name: str) -> 'TutorAideLogger':
        """Get a logger instance for the given name."""
        return TutorAideLogger(name)
    
    def debug(self, message: str, **kwargs):
        """Log debug message."""
        self._log(logging.DEBUG, message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """Log info message."""
        self._log(logging.INFO, message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message."""
        self._log(logging.WARNING, message, **kwargs)
    
    def error(self, message: str, exception: Optional[Exception] = None, **kwargs):
        """Log error message with optional exception."""
        extra = kwargs.get('extra', {})
        
        if exception:
            extra.update({
                'exception_type': type(exception).__name__,
                'exception_message': str(exception),
                'exception_traceback': traceback.format_exc()
            })
        
        kwargs['extra'] = extra
        self._log(logging.ERROR, message, **kwargs)
    
    def critical(self, message: str, exception: Optional[Exception] = None, **kwargs):
        """Log critical message with optional exception."""
        extra = kwargs.get('extra', {})
        
        if exception:
            extra.update({
                'exception_type': type(exception).__name__,
                'exception_message': str(exception),
                'exception_traceback': traceback.format_exc()
            })
        
        kwargs['extra'] = extra
        self._log(logging.CRITICAL, message, **kwargs)
    
    def security(self, message: str, **kwargs):
        """Log security event."""
        extra = kwargs.get('extra', {})
        extra['log_type'] = 'security'
        kwargs['extra'] = extra
        
        # Use security logger
        security_logger = logging.getLogger('tutoraide.security')
        security_logger.warning(message, **kwargs)
    
    def performance(self, message: str, duration_ms: Optional[float] = None, **kwargs):
        """Log performance metric."""
        extra = kwargs.get('extra', {})
        extra['log_type'] = 'performance'
        
        if duration_ms is not None:
            extra['duration_ms'] = duration_ms
        
        kwargs['extra'] = extra
        
        # Use performance logger
        perf_logger = logging.getLogger('tutoraide.performance')
        perf_logger.info(message, **kwargs)
    
    def audit(self, message: str, **kwargs):
        """Log audit event."""
        extra = kwargs.get('extra', {})
        extra['log_type'] = 'audit'
        kwargs['extra'] = extra
        
        # Use audit logger
        audit_logger = logging.getLogger('tutoraide.audit')
        audit_logger.info(message, **kwargs)
    
    def _log(self, level: int, message: str, **kwargs):
        """Internal logging method."""
        self.logger.log(level, message, **kwargs)


# Global logger instance
logger = TutorAideLogger("app")