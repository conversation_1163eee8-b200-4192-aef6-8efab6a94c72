"""
Session management service - Wrapper around AuthTokenService for backward compatibility.
This service now uses auth_tokens table instead of user_sessions.
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from uuid import UUID
import asyncpg

from app.services.auth_token_service import get_auth_token_service, AuthTokenService
from app.models.user_models import UserRoleType
from app.models.auth_token_models import AuthTokenType
from app.core.exceptions import AuthenticationError, ResourceNotFoundError
from app.core.logging import TutorAideLogger

logger = TutorAideLogger.get_logger(__name__)


class SessionService:
    """
    Service for managing user sessions.
    This is now a wrapper around AuthTokenService to maintain backward compatibility.
    """
    
    def __init__(self):
        self.auth_token_service = get_auth_token_service()
    
    def generate_session_token(self) -> str:
        """Generate a secure session token."""
        return self.auth_token_service._generate_secure_token()
    
    async def create_session(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        token: str,
        role_type: UserRoleType,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> Dict[str, Any]:
        """Create a new session for a user using auth_tokens table."""
        try:
            # Use auth_token_service to create a session
            _, session_info = await self.auth_token_service.create_session(
                conn=conn,
                user_id=user_id,
                role_type=role_type,
                ip_address=ip_address,
                user_agent=user_agent,
                device_info=None
            )
            
            # Return in the format expected by old code
            return {
                'session_id': str(session_info.session_id),
                'user_id': user_id,
                'role_type': role_type.value,
                'expires_at': session_info.expires_at,
                'created_at': session_info.created_at,
                'is_active': True
            }
            
        except Exception as e:
            logger.error(f"Error creating session: {e}")
            raise
    
    async def validate_session(
        self,
        conn: asyncpg.Connection,
        token: str
    ) -> Dict[str, Any]:
        """Validate a session token and return session info."""
        try:
            # Use auth_token_service to validate
            session_info = await self.auth_token_service.validate_session(
                conn=conn,
                token=token,
                update_activity=True
            )
            
            # Return in the format expected by old code
            return {
                'session_id': str(session_info.session_id),
                'user_id': session_info.user_id,
                'role_type': session_info.role_type.value,
                'expires_at': session_info.expires_at,
                'created_at': session_info.created_at,
                'last_activity': session_info.last_activity,
                'is_active': True
            }
            
        except Exception as e:
            logger.error(f"Error validating session: {e}")
            raise AuthenticationError("Session validation failed")
    
    async def update_activity(
        self,
        conn: asyncpg.Connection,
        session_id: UUID,
        ip_address: Optional[str] = None
    ) -> None:
        """Update session last activity timestamp."""
        # The auth_token_service updates activity during validate_session
        # This method is kept for backward compatibility but doesn't need to do anything
        logger.debug(f"Session activity update called for {session_id}")
    
    async def invalidate_session(
        self,
        conn: asyncpg.Connection,
        token: str
    ) -> bool:
        """Invalidate a session by token."""
        try:
            return await self.auth_token_service.invalidate_session(
                conn=conn,
                token=token
            )
        except Exception as e:
            logger.error(f"Error invalidating session: {e}")
            return False
    
    async def invalidate_user_sessions(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        except_session_id: Optional[UUID] = None
    ) -> int:
        """Invalidate all sessions for a user."""
        try:
            # auth_token_service expects token hash, not session_id
            # Since we can't convert session_id to token, we'll invalidate all
            return await self.auth_token_service.invalidate_all_user_sessions(
                conn=conn,
                user_id=user_id,
                except_token=None  # Can't use except_session_id
            )
        except Exception as e:
            logger.error(f"Error invalidating user sessions: {e}")
            return 0
    
    async def get_user_sessions(
        self,
        conn: asyncpg.Connection,
        user_id: int
    ) -> List[Dict[str, Any]]:
        """Get all active sessions for a user."""
        try:
            # Get tokens from auth_token_service
            tokens = await self.auth_token_service.token_repo.get_user_tokens(
                conn=conn,
                user_id=user_id,
                token_type=AuthTokenType.SESSION,
                include_used=False,
                include_expired=False
            )
            
            # Convert to session format
            sessions = []
            for token in tokens:
                metadata = token.metadata or {}
                sessions.append({
                    'session_id': metadata.get('session_id', str(token.token_id)),
                    'user_id': token.user_id,
                    'role_type': metadata.get('role_type', 'unknown'),
                    'expires_at': token.expires_at,
                    'created_at': token.created_at,
                    'last_activity': datetime.fromisoformat(metadata.get('last_activity', token.created_at.isoformat())),
                    'ip_address': token.ip_address,
                    'user_agent': token.user_agent,
                    'is_active': True
                })
            
            return sessions
            
        except Exception as e:
            logger.error(f"Error getting user sessions: {e}")
            return []
    
    async def check_session_limit(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        max_sessions: int = 5
    ) -> bool:
        """Check if user is within session limit."""
        try:
            count = await self.auth_token_service.get_active_sessions_count(
                conn=conn,
                user_id=user_id
            )
            return count < max_sessions
        except Exception as e:
            logger.error(f"Error checking session limit: {e}")
            return False
    
    async def extend_session(
        self,
        conn: asyncpg.Connection,
        session_id: UUID
    ) -> Optional[Dict[str, Any]]:
        """Extend session expiration."""
        # This is complex because we don't have the token, only session_id
        # For now, log warning and return None
        logger.warning(f"Session extension not implemented for auth_tokens: {session_id}")
        return None
    
    async def cleanup_expired_sessions(
        self,
        conn: asyncpg.Connection
    ) -> int:
        """Clean up expired sessions."""
        try:
            count = await self.auth_token_service.cleanup_expired_tokens(
                conn=conn,
                batch_size=1000
            )
            logger.info(f"Cleaned up {count} expired sessions")
            return count
        except Exception as e:
            logger.error(f"Error cleaning up sessions: {e}")
            return 0
    
    async def cleanup_inactive_sessions(
        self,
        conn: asyncpg.Connection,
        inactive_minutes: int = 60
    ) -> int:
        """Clean up inactive sessions."""
        # auth_token_service doesn't have inactive cleanup, only expired
        # Return 0 for backward compatibility
        logger.debug("Inactive session cleanup not implemented for auth_tokens")
        return 0


# Singleton instance
_session_service = None


def get_session_service() -> SessionService:
    """Get the session service instance."""
    global _session_service
    if _session_service is None:
        _session_service = SessionService()
    return _session_service