import React, { useState, useEffect, useCallback, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap } from 'react-leaflet';
import { Icon, LatLngBounds } from 'leaflet';
import 'leaflet/dist/leaflet.css';

import { SearchBar } from '../../components/forms/SearchBar';
import { Card } from '../../components/common/Card';
import Button from '../../components/common/Button';
import { Badge } from '../../components/common/Badge';
import { useAuth } from '../../contexts/AuthContext';
import { useApi } from '../../hooks/useApi';
import { Tu<PERSON>, TutorLocation } from '../../types/tutor';
import { GeocodeResponse } from '../../types/geocoding';
import { FilterPanel } from '../../components/map/FilterPanel';
import { TutorCard } from '../../components/map/TutorCard';
import { <PERSON>torListView } from '../../components/map/TutorListView';
import LoadingSpinner from '../../components/ui/LoadingSpinner';

// Leaflet icon fix for React - use CDN URLs to avoid missing local files
delete (Icon.Default.prototype as any)._getIconUrl;
Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon-2x.png',
  iconUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png',
  shadowUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png',
});

// Custom tutor marker icons
const createTutorIcon = (isOnline: boolean, rating: number) => {
  const color = isOnline ? '#dc2626' : '#fbbf24';
  const size = rating >= 4.5 ? 40 : 35;
  
  return new Icon({
    iconUrl: `data:image/svg+xml;base64,${btoa(`
      <svg width="${size}" height="${size}" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
        <circle cx="20" cy="20" r="18" fill="${color}" stroke="white" stroke-width="3"/>
        <circle cx="20" cy="20" r="12" fill="white" opacity="0.9"/>
        <text x="20" y="26" text-anchor="middle" font-family="system-ui" font-size="14" font-weight="600" fill="${color}">T</text>
      </svg>
    `)}`,
    iconSize: [size, size],
    iconAnchor: [size/2, size],
    popupAnchor: [0, -size]
  });
};

interface TutorMapProps {
  className?: string;
}

interface MapFilters {
  subjectAreas: string[];
  serviceTypes: string[];
  maxDistance: number;
  minRating: number;
  maxHourlyRate: number;
  availability: 'all' | 'available' | 'limited';
}

const MapController: React.FC<{
  tutors: TutorLocation[];
  selectedTutor: TutorLocation | null;
  centerCoords: { lat: number; lng: number } | null;
}> = ({ tutors, selectedTutor, centerCoords }) => {
  const map = useMap();

  useEffect(() => {
    if (selectedTutor) {
      map.setView([selectedTutor.lat, selectedTutor.lng], 15, { animate: true });
    } else if (centerCoords && tutors.length > 0) {
      // Fit map to show all tutors with some padding
      const bounds = new LatLngBounds(
        tutors.map(tutor => [tutor.lat, tutor.lng])
      );
      bounds.extend([centerCoords.lat, centerCoords.lng]);
      map.fitBounds(bounds, { padding: [20, 20] });
    } else if (centerCoords) {
      map.setView([centerCoords.lat, centerCoords.lng], 12);
    }
  }, [map, tutors, selectedTutor, centerCoords]);

  return null;
};

export const TutorMap: React.FC<TutorMapProps> = ({ className = '' }) => {
  const { user } = useAuth();
  const { apiCall } = useApi();
  
  // State management
  const [tutors, setTutors] = useState<TutorLocation[]>([]);
  const [filteredTutors, setFilteredTutors] = useState<TutorLocation[]>([]);
  const [selectedTutor, setSelectedTutor] = useState<TutorLocation | null>(null);
  const [centerCoords, setCenterCoords] = useState<{ lat: number; lng: number } | null>(null);
  const [searchPostalCode, setSearchPostalCode] = useState('');
  const [filters, setFilters] = useState<MapFilters>({
    subjectAreas: [],
    serviceTypes: ['online', 'in_person'],
    maxDistance: 15,
    minRating: 0,
    maxHourlyRate: 100,
    availability: 'all'
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [showListView, setShowListView] = useState(true);
  const [viewMode, setViewMode] = useState<'map' | 'split' | 'list'>('split');
  
  const mapRef = useRef<any>(null);

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  // Apply filters when they change
  useEffect(() => {
    applyFilters();
  }, [tutors, filters]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Use user's postal code if available, otherwise default to Montreal
      const defaultPostalCode = user?.postal_code || 'H2X 1Y9';
      
      // Geocode the center postal code
      const geocodeResponse = await apiCall<GeocodeResponse>('POST', '/geocoding/geocode', {
        postal_code: defaultPostalCode
      });

      setCenterCoords({
        lat: geocodeResponse.latitude,
        lng: geocodeResponse.longitude
      });

      // Load tutors near this location
      await loadTutorsNearLocation(defaultPostalCode);
      
    } catch (err) {
      console.error('Error loading initial data:', err);
      setError('Failed to load map data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const loadTutorsNearLocation = async (postalCode: string) => {
    try {
      // Get tutors with service rates and locations
      const response = await apiCall<any>('GET', '/tutors/map-locations', {
        params: {
          center_postal_code: postalCode,
          radius_km: filters.maxDistance,
          include_online: filters.serviceTypes.includes('online'),
          include_in_person: filters.serviceTypes.includes('in_person')
        }
      });

      // Transform tutors to include location data
      const tutorsWithLocations: TutorLocation[] = response.tutors.map((tutor: any) => ({
        ...tutor,
        lat: tutor.latitude || tutor.lat,
        lng: tutor.longitude || tutor.lng,
        distance_km: tutor.distance_km
      }));

      setTutors(tutorsWithLocations);
      
    } catch (err) {
      console.error('Error loading tutors:', err);
      throw err;
    }
  };

  const applyFilters = useCallback(() => {
    let filtered = [...tutors];

    // Subject area filter
    if (filters.subjectAreas.length > 0) {
      filtered = filtered.filter(tutor =>
        tutor.specialties?.some((specialty: string) =>
          filters.subjectAreas.includes(specialty)
        )
      );
    }

    // Distance filter
    filtered = filtered.filter(tutor =>
      !tutor.distance_km || tutor.distance_km <= filters.maxDistance
    );

    // Rating filter
    if (filters.minRating > 0) {
      filtered = filtered.filter(tutor =>
        tutor.average_rating >= filters.minRating
      );
    }

    // Hourly rate filter
    filtered = filtered.filter(tutor =>
      tutor.hourly_rate <= filters.maxHourlyRate
    );

    // Availability filter
    if (filters.availability !== 'all') {
      filtered = filtered.filter(tutor => {
        if (filters.availability === 'available') {
          return tutor.availability_status === 'available';
        } else if (filters.availability === 'limited') {
          return tutor.availability_status === 'limited';
        }
        return true;
      });
    }

    setFilteredTutors(filtered);
  }, [tutors, filters]);

  const handleSearch = async (postalCode: string) => {
    if (!postalCode.trim()) return;

    try {
      setLoading(true);
      setSearchPostalCode(postalCode);

      // Geocode the search postal code
      const geocodeResponse = await apiCall<GeocodeResponse>('POST', '/geocoding/geocode', {
        postal_code: postalCode
      });

      setCenterCoords({
        lat: geocodeResponse.latitude,
        lng: geocodeResponse.longitude
      });

      // Load tutors near this new location
      await loadTutorsNearLocation(postalCode);

    } catch (err) {
      console.error('Error searching location:', err);
      setError('Could not find location. Please check the postal code.');
    } finally {
      setLoading(false);
    }
  };

  const handleTutorSelect = (tutor: TutorLocation) => {
    setSelectedTutor(tutor);
  };

  const handleFilterChange = (newFilters: Partial<MapFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const handleBookSession = async (tutorId: number) => {
    // Navigate to booking flow
    window.location.href = `/appointments/book?tutor_id=${tutorId}`;
  };

  const handleViewProfile = (tutorId: number) => {
    // Navigate to tutor profile
    window.location.href = `/tutors/${tutorId}`;
  };

  if (loading && tutors.length === 0) {
    return (
      <div className="flex justify-center items-center h-96">
        <LoadingSpinner size="large" message="Loading tutor map..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
        <div className="text-red-600 mb-2">Error Loading Map</div>
        <div className="text-red-500 text-sm">{error}</div>
        <button
          onClick={loadInitialData}
          className="mt-4 px-6 py-3 bg-accent-red text-white rounded-full hover:bg-accent-red-dark transition-colors shadow-soft"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className={`relative bg-white rounded-lg shadow-sm overflow-hidden ${className}`}>
      {/* Header with search and controls */}
      <div className="bg-gray-50 p-4 border-b border-gray-200">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex-1 max-w-md">
            <SearchBar
              placeholder="Search by postal code (e.g., H2X 1Y9)"
              onSearch={handleSearch}
              loading={loading}
            />
          </div>
          
          <div className="flex items-center gap-3">
            {/* View mode toggle */}
            <div className="flex border border-gray-300 rounded">
              <button
                onClick={() => setViewMode('map')}
                className={`px-4 py-2 text-sm rounded-l-full transition-colors ${
                  viewMode === 'map' 
                    ? 'bg-accent-red text-white' 
                    : 'bg-white text-gray-600 hover:bg-gray-50'
                }`}
              >
                Map
              </button>
              <button
                onClick={() => setViewMode('split')}
                className={`px-4 py-2 text-sm border-l border-gray-300 transition-colors ${
                  viewMode === 'split' 
                    ? 'bg-accent-red text-white' 
                    : 'bg-white text-gray-600 hover:bg-gray-50'
                }`}
              >
                Split
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`px-4 py-2 text-sm border-l border-gray-300 rounded-r-full transition-colors ${
                  viewMode === 'list' 
                    ? 'bg-accent-red text-white' 
                    : 'bg-white text-gray-600 hover:bg-gray-50'
                }`}
              >
                List
              </button>
            </div>

            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`px-5 py-2 text-sm font-medium rounded-full border transition-colors ${
                showFilters
                  ? 'bg-red-50 text-accent-red border-accent-red'
                  : 'bg-white text-gray-600 border-gray-300 hover:bg-gray-50'
              }`}
            >
              Filters
              {(filters.subjectAreas.length > 0 || filters.minRating > 0) && (
                <span className="ml-1 w-2 h-2 bg-accent-red rounded-full inline-block"></span>
              )}
            </button>
            
            <div className="text-sm text-gray-500">
              {filteredTutors.length} of {tutors.length} tutors
            </div>
          </div>
        </div>
        
        {showFilters && (
          <div className="mt-4">
            <FilterPanel
              filters={filters}
              onFilterChange={handleFilterChange}
              tutorCount={filteredTutors.length}
            />
          </div>
        )}
      </div>

      {/* Main content area */}
      <div className={`flex ${viewMode === 'split' ? 'divide-x divide-gray-200' : ''}`}>
        {/* List view */}
        {(viewMode === 'list' || viewMode === 'split') && (
          <div className={`${viewMode === 'split' ? 'w-1/3' : 'w-full'} ${viewMode === 'list' ? 'h-[600px]' : ''}`}>
            <TutorListView
              tutors={filteredTutors}
              selectedTutor={selectedTutor}
              onTutorSelect={handleTutorSelect}
              onBookSession={handleBookSession}
              onViewProfile={handleViewProfile}
              loading={loading}
              className="h-full border-0 rounded-none"
            />
          </div>
        )}

        {/* Map container */}
        {(viewMode === 'map' || viewMode === 'split') && (
          <div className={`relative ${viewMode === 'split' ? 'flex-1' : 'w-full'} h-96 md:h-[500px] lg:h-[600px]`}>
        {centerCoords && (
          <MapContainer
            ref={mapRef}
            center={[centerCoords.lat, centerCoords.lng]}
            zoom={12}
            className="w-full h-full"
            zoomControl={true}
            attributionControl={true}
          >
            <TileLayer
              attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
              url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            />
            
            {/* Map controller for programmatic updates */}
            <MapController
              tutors={filteredTutors}
              selectedTutor={selectedTutor}
              centerCoords={centerCoords}
            />
            
            {/* Center marker */}
            {centerCoords && (
              <Marker
                position={[centerCoords.lat, centerCoords.lng]}
                icon={new Icon({
                  iconUrl: `data:image/svg+xml;base64,${btoa(`
                    <svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
                      <circle cx="15" cy="15" r="12" fill="#dc2626" stroke="white" stroke-width="3"/>
                      <circle cx="15" cy="15" r="6" fill="white"/>
                    </svg>
                  `)}`,
                  iconSize: [30, 30],
                  iconAnchor: [15, 15]
                })}
              >
                <Popup>
                  <div className="text-center">
                    <div className="font-medium text-gray-900">Search Center</div>
                    <div className="text-sm text-gray-500">
                      {searchPostalCode || user?.postal_code || 'H2X 1Y9'}
                    </div>
                  </div>
                </Popup>
              </Marker>
            )}
            
            {/* Tutor markers */}
            {filteredTutors.map((tutor) => (
              <Marker
                key={tutor.tutor_id}
                position={[tutor.lat, tutor.lng]}
                icon={createTutorIcon(
                  tutor.availability_status === 'available',
                  tutor.average_rating || 0
                )}
                eventHandlers={{
                  click: () => handleTutorSelect(tutor)
                }}
              >
                <Popup>
                  <TutorCard
                    tutor={tutor}
                    onBookSession={() => handleBookSession(tutor.tutor_id)}
                    onViewProfile={() => handleViewProfile(tutor.tutor_id)}
                    showDistance={true}
                  />
                </Popup>
              </Marker>
            ))}
          </MapContainer>
        )}
        
        {loading && (
          <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
            <LoadingSpinner message="Loading tutors..." />
          </div>
        )}
          </div>
        )}
      </div>

      {/* Selected tutor sidebar on larger screens - only show in map-only mode */}
      {selectedTutor && viewMode === 'map' && (
        <div className="hidden lg:block absolute top-20 right-4 w-80 bg-white rounded-2xl shadow-elevated border border-gray-200 max-h-[calc(100%-5rem)] overflow-y-auto">
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-gray-900">Tutor Details</h3>
              <button
                onClick={() => setSelectedTutor(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
          <div className="p-4">
            <TutorCard
              tutor={selectedTutor}
              onBookSession={() => handleBookSession(selectedTutor.tutor_id)}
              onViewProfile={() => handleViewProfile(selectedTutor.tutor_id)}
              showDistance={true}
              expanded={true}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default TutorMap;