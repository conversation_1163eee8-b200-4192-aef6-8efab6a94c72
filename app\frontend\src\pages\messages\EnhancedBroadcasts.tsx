import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Send, Users, Filter, Calendar, MessageSquare, Mail, Phone,
  Globe, Clock, CheckCircle, AlertCircle, PauseCircle, XCircle,
  Play, Pause, RefreshCw, Target, BarChart, TrendingUp, Eye,
  FileText, Edit, Download
} from 'lucide-react';
import { Card } from '../../components/common/Card';
import Button from '../../components/common/Button';
import { Badge } from '../../components/common/Badge';
import { Input } from '../../components/common/Input';
import { Select } from '../../components/common/Select';
import { Modal } from '../../components/common/Modal';
import { Checkbox } from '../../components/common/Checkbox';
import { RadioGroup, RadioGroupItem } from '../../components/common/RadioGroup';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '../../components/common/TabsAdapter';
import toast from 'react-hot-toast';
import { format, addDays, addHours } from 'date-fns';

interface Broadcast {
  broadcast_id: number;
  campaign_name: string;
  channel: 'sms' | 'email' | 'push' | 'all';
  audience_type: 'all' | 'clients' | 'tutors' | 'custom';
  audience_filters?: AudienceFilter[];
  audience_count: number;
  template_id?: number;
  template_name?: string;
  custom_message?: string;
  subject?: string;
  status: 'draft' | 'scheduled' | 'sending' | 'sent' | 'failed' | 'cancelled';
  scheduled_time?: string;
  sent_time?: string;
  sent_count: number;
  delivered_count: number;
  opened_count: number;
  clicked_count: number;
  failed_count: number;
  created_by: string;
  created_at: string;
  updated_at: string;
}

interface AudienceFilter {
  field: string;
  operator: 'equals' | 'contains' | 'greater_than' | 'less_than' | 'in' | 'not_in';
  value: any;
}

interface BroadcastStats {
  totalBroadcasts: number;
  scheduledBroadcasts: number;
  sentToday: number;
  totalRecipients: number;
  avgOpenRate: number;
  avgClickRate: number;
}

interface MessageTemplate {
  template_id: number;
  template_name: string;
  category: string;
  channel: string;
  content_en: string;
  content_fr: string;
}

const EnhancedBroadcasts: React.FC = () => {
  const { t } = useTranslation();
  const [broadcasts, setBroadcasts] = useState<Broadcast[]>([]);
  const [templates, setTemplates] = useState<MessageTemplate[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedBroadcast, setSelectedBroadcast] = useState<Broadcast | null>(null);
  const [showBroadcastModal, setShowBroadcastModal] = useState(false);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [showStatsModal, setShowStatsModal] = useState(false);
  const [statusFilter, setStatusFilter] = useState('all');
  const [channelFilter, setChannelFilter] = useState('all');
  const [activeTab, setActiveTab] = useState('active');
  const [stats, setStats] = useState<BroadcastStats>({
    totalBroadcasts: 156,
    scheduledBroadcasts: 8,
    sentToday: 3,
    totalRecipients: 4580,
    avgOpenRate: 68.5,
    avgClickRate: 12.3
  });

  // Mock broadcast data
  useEffect(() => {
    const mockBroadcasts: Broadcast[] = [
      {
        broadcast_id: 1,
        campaign_name: 'Holiday Season Promotion',
        channel: 'email',
        audience_type: 'clients',
        audience_filters: [
          { field: 'subscription_status', operator: 'equals', value: 'active' }
        ],
        audience_count: 245,
        template_id: 10,
        template_name: 'Holiday Promotion',
        status: 'scheduled',
        scheduled_time: addDays(new Date(), 2).toISOString(),
        sent_count: 0,
        delivered_count: 0,
        opened_count: 0,
        clicked_count: 0,
        failed_count: 0,
        created_by: 'Admin User',
        created_at: '2024-11-20',
        updated_at: '2024-11-20'
      },
      {
        broadcast_id: 2,
        campaign_name: 'Tutor Schedule Reminder',
        channel: 'sms',
        audience_type: 'tutors',
        audience_count: 85,
        template_id: 5,
        template_name: 'Schedule Reminder',
        status: 'sent',
        sent_time: '2024-11-20T14:00:00',
        sent_count: 85,
        delivered_count: 83,
        opened_count: 0,
        clicked_count: 0,
        failed_count: 2,
        created_by: 'System',
        created_at: '2024-11-20',
        updated_at: '2024-11-20'
      },
      {
        broadcast_id: 3,
        campaign_name: 'New Feature Announcement',
        channel: 'all',
        audience_type: 'all',
        audience_count: 450,
        custom_message: 'Exciting news! We\'ve launched a new mobile app...',
        status: 'draft',
        sent_count: 0,
        delivered_count: 0,
        opened_count: 0,
        clicked_count: 0,
        failed_count: 0,
        created_by: 'Marketing Team',
        created_at: '2024-11-19',
        updated_at: '2024-11-21'
      }
    ];

    const mockTemplates: MessageTemplate[] = [
      {
        template_id: 1,
        template_name: 'Welcome Message',
        category: 'welcome',
        channel: 'all',
        content_en: 'Welcome to TutorAide! We\'re excited to have you.',
        content_fr: 'Bienvenue à TutorAide! Nous sommes ravis de vous avoir.'
      },
      {
        template_id: 2,
        template_name: 'Payment Reminder',
        category: 'payment',
        channel: 'email',
        content_en: 'Your invoice is due soon. Please make a payment.',
        content_fr: 'Votre facture est due bientôt. Veuillez effectuer un paiement.'
      }
    ];
    
    setBroadcasts(mockBroadcasts);
    setTemplates(mockTemplates);
  }, []);

  const filteredBroadcasts = broadcasts.filter(broadcast => {
    const matchesStatus = statusFilter === 'all' || broadcast.status === statusFilter;
    const matchesChannel = channelFilter === 'all' || broadcast.channel === channelFilter || broadcast.channel === 'all';
    
    if (activeTab === 'active') {
      return matchesStatus && matchesChannel && ['draft', 'scheduled', 'sending'].includes(broadcast.status);
    } else {
      return matchesStatus && matchesChannel && ['sent', 'failed', 'cancelled'].includes(broadcast.status);
    }
  });

  const handleSendBroadcast = async (broadcast: Broadcast) => {
    if (!confirm(`Are you sure you want to send this broadcast to ${broadcast.audience_count} recipients?`)) return;
    
    setLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setBroadcasts(broadcasts.map(b => 
      b.broadcast_id === broadcast.broadcast_id 
        ? { 
            ...b, 
            status: 'sending' as const,
            sent_time: new Date().toISOString()
          }
        : b
    ));
    
    toast.success('Broadcast is being sent');
    setLoading(false);
    
    // Simulate completion after a delay
    setTimeout(() => {
      setBroadcasts(prev => prev.map(b => 
        b.broadcast_id === broadcast.broadcast_id 
          ? { 
              ...b, 
              status: 'sent' as const,
              sent_count: b.audience_count,
              delivered_count: Math.floor(b.audience_count * 0.95),
              opened_count: Math.floor(b.audience_count * 0.68),
              clicked_count: Math.floor(b.audience_count * 0.12)
            }
          : b
      ));
    }, 5000);
  };

  const handleCancelBroadcast = async (broadcast: Broadcast) => {
    if (!confirm('Are you sure you want to cancel this broadcast?')) return;
    
    setLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    setBroadcasts(broadcasts.map(b => 
      b.broadcast_id === broadcast.broadcast_id 
        ? { ...b, status: 'cancelled' as const }
        : b
    ));
    
    toast.success('Broadcast cancelled');
    setLoading(false);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig: Record<string, { color: string; icon: React.ReactNode }> = {
      draft: { color: 'bg-gray-100 text-gray-700', icon: <FileText className="w-4 h-4" /> },
      scheduled: { color: 'bg-blue-100 text-blue-700', icon: <Clock className="w-4 h-4" /> },
      sending: { color: 'bg-yellow-100 text-yellow-700', icon: <RefreshCw className="w-4 h-4 animate-spin" /> },
      sent: { color: 'bg-green-100 text-green-700', icon: <CheckCircle className="w-4 h-4" /> },
      failed: { color: 'bg-red-100 text-red-700', icon: <XCircle className="w-4 h-4" /> },
      cancelled: { color: 'bg-gray-100 text-gray-700', icon: <XCircle className="w-4 h-4" /> }
    };

    const config = statusConfig[status] || statusConfig.draft;
    return (
      <Badge className={`inline-flex items-center gap-1 ${config.color}`}>
        {config.icon}
        <span className="capitalize">{status}</span>
      </Badge>
    );
  };

  const getChannelIcon = (channel: string) => {
    switch (channel) {
      case 'sms':
        return <Phone className="w-4 h-4" />;
      case 'email':
        return <Mail className="w-4 h-4" />;
      case 'push':
        return <Globe className="w-4 h-4" />;
      default:
        return <MessageSquare className="w-4 h-4" />;
    }
  };

  const getDeliveryRate = (broadcast: Broadcast) => {
    if (broadcast.sent_count === 0) return 0;
    return ((broadcast.delivered_count / broadcast.sent_count) * 100).toFixed(1);
  };

  const getOpenRate = (broadcast: Broadcast) => {
    if (broadcast.delivered_count === 0) return 0;
    return ((broadcast.opened_count / broadcast.delivered_count) * 100).toFixed(1);
  };

  const getClickRate = (broadcast: Broadcast) => {
    if (broadcast.opened_count === 0) return 0;
    return ((broadcast.clicked_count / broadcast.opened_count) * 100).toFixed(1);
  };

  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Broadcast Messages</h1>
          <p className="text-gray-600 mt-1">
            Send targeted messages to your users via SMS, email, and push notifications
          </p>
        </div>
        <Button
          variant="primary"
          onClick={() => {
            setSelectedBroadcast(null);
            setShowBroadcastModal(true);
          }}
        >
          <Send className="w-4 h-4 mr-2" />
          Create Broadcast
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <MessageSquare className="w-6 h-6 text-blue-600" />
            <span className="text-xs text-gray-500">Total</span>
          </div>
          <p className="text-xl font-bold">{stats.totalBroadcasts}</p>
          <p className="text-xs text-gray-600 mt-1">Campaigns</p>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Clock className="w-6 h-6 text-yellow-600" />
            <span className="text-xs text-gray-500">Pending</span>
          </div>
          <p className="text-xl font-bold">{stats.scheduledBroadcasts}</p>
          <p className="text-xs text-gray-600 mt-1">Scheduled</p>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Send className="w-6 h-6 text-green-600" />
            <span className="text-xs text-gray-500">Today</span>
          </div>
          <p className="text-xl font-bold">{stats.sentToday}</p>
          <p className="text-xs text-gray-600 mt-1">Sent</p>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Users className="w-6 h-6 text-purple-600" />
            <span className="text-xs text-gray-500">Recipients</span>
          </div>
          <p className="text-xl font-bold">{stats.totalRecipients.toLocaleString()}</p>
          <p className="text-xs text-gray-600 mt-1">This month</p>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Eye className="w-6 h-6 text-red-600" />
            <span className="text-xs text-gray-500">Open Rate</span>
          </div>
          <p className="text-xl font-bold">{stats.avgOpenRate}%</p>
          <p className="text-xs text-green-600 mt-1">↑ 5.2%</p>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <TrendingUp className="w-6 h-6 text-gray-600" />
            <span className="text-xs text-gray-500">Click Rate</span>
          </div>
          <p className="text-xl font-bold">{stats.avgClickRate}%</p>
          <p className="text-xs text-green-600 mt-1">↑ 2.1%</p>
        </Card>
      </div>

      {/* Tabs and Filters */}
      <Card className="p-4">
        <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full lg:w-auto">
            <TabsList>
              <TabsTrigger value="active">Active</TabsTrigger>
              <TabsTrigger value="history">History</TabsTrigger>
            </TabsList>
          </Tabs>
          
          <div className="flex gap-3 w-full lg:w-auto">
            <Select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              options={[
                { value: 'all', label: 'All Status' },
                { value: 'draft', label: 'Draft' },
                { value: 'scheduled', label: 'Scheduled' },
                { value: 'sending', label: 'Sending' },
                { value: 'sent', label: 'Sent' },
                { value: 'failed', label: 'Failed' },
                { value: 'cancelled', label: 'Cancelled' }
              ]}
              className="w-40"
            />
            <Select
              value={channelFilter}
              onChange={(e) => setChannelFilter(e.target.value)}
              options={[
                { value: 'all', label: 'All Channels' },
                { value: 'sms', label: 'SMS' },
                { value: 'email', label: 'Email' },
                { value: 'push', label: 'Push' }
              ]}
              className="w-40"
            />
          </div>
        </div>
      </Card>

      {/* Broadcasts List */}
      <div className="space-y-4">
        {filteredBroadcasts.map((broadcast) => (
          <Card key={broadcast.broadcast_id} className="p-6">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <h3 className="font-semibold text-gray-900">{broadcast.campaign_name}</h3>
                  {getStatusBadge(broadcast.status)}
                  <div className="flex items-center gap-1">
                    {getChannelIcon(broadcast.channel)}
                    {broadcast.channel === 'all' && (
                      <>
                        <Phone className="w-4 h-4 text-gray-400" />
                        <Mail className="w-4 h-4 text-gray-400" />
                      </>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                  <span className="flex items-center gap-1">
                    <Users className="w-4 h-4" />
                    {broadcast.audience_count} recipients
                  </span>
                  <span className="flex items-center gap-1">
                    <Target className="w-4 h-4" />
                    {broadcast.audience_type === 'all' ? 'All users' : 
                     broadcast.audience_type === 'clients' ? 'Clients only' :
                     broadcast.audience_type === 'tutors' ? 'Tutors only' : 'Custom audience'}
                  </span>
                  {broadcast.scheduled_time && broadcast.status === 'scheduled' && (
                    <span className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      {format(new Date(broadcast.scheduled_time), 'MMM d, yyyy h:mm a')}
                    </span>
                  )}
                </div>

                {broadcast.template_name ? (
                  <p className="text-sm text-gray-700">
                    Using template: <span className="font-medium">{broadcast.template_name}</span>
                  </p>
                ) : broadcast.custom_message && (
                  <p className="text-sm text-gray-700 line-clamp-2">
                    {broadcast.custom_message}
                  </p>
                )}

                {broadcast.status === 'sent' && (
                  <div className="mt-4 grid grid-cols-4 gap-4">
                    <div>
                      <p className="text-xs text-gray-500">Delivered</p>
                      <p className="font-semibold">{getDeliveryRate(broadcast)}%</p>
                      <p className="text-xs text-gray-600">{broadcast.delivered_count}/{broadcast.sent_count}</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">Opened</p>
                      <p className="font-semibold">{getOpenRate(broadcast)}%</p>
                      <p className="text-xs text-gray-600">{broadcast.opened_count} opens</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">Clicked</p>
                      <p className="font-semibold">{getClickRate(broadcast)}%</p>
                      <p className="text-xs text-gray-600">{broadcast.clicked_count} clicks</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">Failed</p>
                      <p className="font-semibold text-red-600">{broadcast.failed_count}</p>
                      <p className="text-xs text-gray-600">bounced</p>
                    </div>
                  </div>
                )}
              </div>

              <div className="flex gap-2">
                {broadcast.status === 'draft' && (
                  <>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setSelectedBroadcast(broadcast);
                        setShowBroadcastModal(true);
                      }}
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="primary"
                      size="sm"
                      onClick={() => handleSendBroadcast(broadcast)}
                    >
                      <Send className="w-4 h-4 mr-1" />
                      Send Now
                    </Button>
                  </>
                )}
                
                {broadcast.status === 'scheduled' && (
                  <>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleCancelBroadcast(broadcast)}
                    >
                      <XCircle className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="primary"
                      size="sm"
                      onClick={() => handleSendBroadcast(broadcast)}
                    >
                      <Play className="w-4 h-4 mr-1" />
                      Send Now
                    </Button>
                  </>
                )}
                
                {broadcast.status === 'sending' && (
                  <Button
                    variant="secondary"
                    size="sm"
                    disabled
                  >
                    <RefreshCw className="w-4 h-4 animate-spin mr-1" />
                    Sending...
                  </Button>
                )}
                
                {broadcast.status === 'sent' && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setSelectedBroadcast(broadcast);
                      setShowStatsModal(true);
                    }}
                  >
                    <BarChart className="w-4 h-4 mr-1" />
                    View Stats
                  </Button>
                )}
              </div>
            </div>
          </Card>
        ))}

        {filteredBroadcasts.length === 0 && (
          <Card className="p-12 text-center">
            <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No broadcasts found</p>
            <Button
              variant="primary"
              className="mt-4"
              onClick={() => {
                setSelectedBroadcast(null);
                setShowBroadcastModal(true);
              }}
            >
              Create your first broadcast
            </Button>
          </Card>
        )}
      </div>

      {/* Create/Edit Broadcast Modal */}
      <Modal
        isOpen={showBroadcastModal}
        onClose={() => {
          setShowBroadcastModal(false);
          setSelectedBroadcast(null);
        }}
        title={selectedBroadcast ? 'Edit Broadcast' : 'Create Broadcast'}
        size="xl"
      >
        <BroadcastForm
          broadcast={selectedBroadcast}
          templates={templates}
          onSave={(data) => {
            // Handle save
            setShowBroadcastModal(false);
            toast.success('Broadcast saved');
          }}
          onCancel={() => {
            setShowBroadcastModal(false);
            setSelectedBroadcast(null);
          }}
        />
      </Modal>

      {/* Stats Modal */}
      <Modal
        isOpen={showStatsModal}
        onClose={() => {
          setShowStatsModal(false);
          setSelectedBroadcast(null);
        }}
        title="Broadcast Statistics"
        size="lg"
      >
        {selectedBroadcast && (
          <BroadcastStats broadcast={selectedBroadcast} />
        )}
      </Modal>
    </div>
  );
};

// Broadcast Form Component
interface BroadcastFormProps {
  broadcast: Broadcast | null;
  templates: MessageTemplate[];
  onSave: (data: Partial<Broadcast>) => void;
  onCancel: () => void;
}

const BroadcastForm: React.FC<BroadcastFormProps> = ({ broadcast, templates, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    campaign_name: broadcast?.campaign_name || '',
    channel: broadcast?.channel || 'all',
    audience_type: broadcast?.audience_type || 'all',
    message_type: broadcast?.template_id ? 'template' : 'custom',
    template_id: broadcast?.template_id || '',
    custom_message: broadcast?.custom_message || '',
    subject: broadcast?.subject || '',
    schedule_type: broadcast?.scheduled_time ? 'scheduled' : 'immediate',
    scheduled_time: broadcast?.scheduled_time || ''
  });

  const [estimatedAudience, setEstimatedAudience] = useState(0);

  useEffect(() => {
    // Simulate audience calculation
    let count = 0;
    switch (formData.audience_type) {
      case 'all':
        count = 450;
        break;
      case 'clients':
        count = 320;
        break;
      case 'tutors':
        count = 85;
        break;
      case 'custom':
        count = 150;
        break;
    }
    setEstimatedAudience(count);
  }, [formData.audience_type]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const broadcastData: Partial<Broadcast> = {
      campaign_name: formData.campaign_name,
      channel: formData.channel as any,
      audience_type: formData.audience_type as any,
      audience_count: estimatedAudience,
      status: formData.schedule_type === 'scheduled' ? 'scheduled' : 'draft'
    };

    if (formData.message_type === 'template') {
      broadcastData.template_id = Number(formData.template_id);
      const template = templates.find(t => t.template_id === Number(formData.template_id));
      if (template) {
        broadcastData.template_name = template.template_name;
      }
    } else {
      broadcastData.custom_message = formData.custom_message;
      if (formData.channel === 'email' || formData.channel === 'all') {
        broadcastData.subject = formData.subject;
      }
    }

    if (formData.schedule_type === 'scheduled' && formData.scheduled_time) {
      broadcastData.scheduled_time = new Date(formData.scheduled_time).toISOString();
    }

    onSave(broadcastData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6 p-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Campaign Name
        </label>
        <Input
          value={formData.campaign_name}
          onChange={(e) => setFormData({ ...formData, campaign_name: e.target.value })}
          placeholder="e.g., Holiday Season Promotion"
          required
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Channel
          </label>
          <Select
            value={formData.channel}
            onChange={(e) => setFormData({ ...formData, channel: e.target.value })}
            options={[
              { value: 'all', label: 'All Channels' },
              { value: 'sms', label: 'SMS Only' },
              { value: 'email', label: 'Email Only' },
              { value: 'push', label: 'Push Only' }
            ]}
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Audience
          </label>
          <Select
            value={formData.audience_type}
            onChange={(e) => setFormData({ ...formData, audience_type: e.target.value })}
            options={[
              { value: 'all', label: 'All Users' },
              { value: 'clients', label: 'Clients Only' },
              { value: 'tutors', label: 'Tutors Only' },
              { value: 'custom', label: 'Custom Filters' }
            ]}
          />
        </div>
      </div>

      {formData.audience_type === 'custom' && (
        <Card className="p-4 bg-blue-50">
          <p className="text-sm text-blue-800 mb-2">Custom audience filters coming soon!</p>
          <p className="text-xs text-blue-600">You'll be able to filter by location, activity, subscription status, and more.</p>
        </Card>
      )}

      <div className="p-4 bg-gray-50 rounded-lg">
        <p className="text-sm text-gray-600">Estimated audience size:</p>
        <p className="text-2xl font-bold text-gray-900">{estimatedAudience} recipients</p>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Message Content
        </label>
        <RadioGroup
          value={formData.message_type}
          onValueChange={(value) => setFormData({ ...formData, message_type: value })}
        >
          <div className="flex items-center space-x-2 mb-2">
            <RadioGroupItem value="template" id="template" />
            <label htmlFor="template" className="text-sm font-medium">Use Template</label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="custom" id="custom" />
            <label htmlFor="custom" className="text-sm font-medium">Custom Message</label>
          </div>
        </RadioGroup>
      </div>

      {formData.message_type === 'template' ? (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Select Template
          </label>
          <Select
            value={formData.template_id}
            onChange={(e) => setFormData({ ...formData, template_id: e.target.value })}
            required
          >
            <option value="">Choose a template</option>
            {templates
              .filter(t => t.channel === formData.channel || t.channel === 'all' || formData.channel === 'all')
              .map(template => (
                <option key={template.template_id} value={template.template_id}>
                  {template.template_name} ({template.category})
                </option>
              ))
            }
          </Select>
        </div>
      ) : (
        <>
          {(formData.channel === 'email' || formData.channel === 'all') && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email Subject
              </label>
              <Input
                value={formData.subject}
                onChange={(e) => setFormData({ ...formData, subject: e.target.value })}
                placeholder="Enter email subject"
              />
            </div>
          )}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Message
            </label>
            <textarea
              className="w-full p-3 border rounded-lg min-h-[120px]"
              value={formData.custom_message}
              onChange={(e) => setFormData({ ...formData, custom_message: e.target.value })}
              placeholder="Enter your message here..."
              required
            />
          </div>
        </>
      )}

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Send Time
        </label>
        <RadioGroup
          value={formData.schedule_type}
          onValueChange={(value) => setFormData({ ...formData, schedule_type: value })}
        >
          <div className="flex items-center space-x-2 mb-2">
            <RadioGroupItem value="immediate" id="immediate" />
            <label htmlFor="immediate" className="text-sm font-medium">Send Immediately</label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="scheduled" id="scheduled" />
            <label htmlFor="scheduled" className="text-sm font-medium">Schedule for Later</label>
          </div>
        </RadioGroup>

        {formData.schedule_type === 'scheduled' && (
          <div className="mt-3">
            <Input
              type="datetime-local"
              value={formData.scheduled_time}
              onChange={(e) => setFormData({ ...formData, scheduled_time: e.target.value })}
              min={new Date().toISOString().slice(0, 16)}
              required
            />
          </div>
        )}
      </div>

      <div className="flex justify-end gap-3 pt-6 border-t">
        <Button variant="ghost" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" variant="primary">
          {formData.schedule_type === 'scheduled' ? 'Schedule Broadcast' : 'Save as Draft'}
        </Button>
      </div>
    </form>
  );
};

// Broadcast Stats Component
interface BroadcastStatsProps {
  broadcast: Broadcast;
}

const BroadcastStats: React.FC<BroadcastStatsProps> = ({ broadcast }) => {
  return (
    <div className="p-6 space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <p className="text-sm text-gray-600">Campaign</p>
          <p className="font-semibold">{broadcast.campaign_name}</p>
        </div>
        <div>
          <p className="text-sm text-gray-600">Sent Time</p>
          <p className="font-semibold">
            {broadcast.sent_time && format(new Date(broadcast.sent_time), 'MMM d, yyyy h:mm a')}
          </p>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <Card className="p-4">
          <p className="text-sm text-gray-600 mb-1">Total Sent</p>
          <p className="text-2xl font-bold">{broadcast.sent_count}</p>
        </Card>
        <Card className="p-4">
          <p className="text-sm text-gray-600 mb-1">Delivered</p>
          <p className="text-2xl font-bold text-green-600">{broadcast.delivered_count}</p>
          <p className="text-xs text-gray-500">
            {((broadcast.delivered_count / broadcast.sent_count) * 100).toFixed(1)}% delivery rate
          </p>
        </Card>
      </div>

      <div className="grid grid-cols-3 gap-4">
        <Card className="p-4">
          <p className="text-sm text-gray-600 mb-1">Opened</p>
          <p className="text-xl font-bold">{broadcast.opened_count}</p>
          <p className="text-xs text-gray-500">
            {((broadcast.opened_count / broadcast.delivered_count) * 100).toFixed(1)}% open rate
          </p>
        </Card>
        <Card className="p-4">
          <p className="text-sm text-gray-600 mb-1">Clicked</p>
          <p className="text-xl font-bold">{broadcast.clicked_count}</p>
          <p className="text-xs text-gray-500">
            {((broadcast.clicked_count / broadcast.opened_count) * 100).toFixed(1)}% CTR
          </p>
        </Card>
        <Card className="p-4">
          <p className="text-sm text-gray-600 mb-1">Failed</p>
          <p className="text-xl font-bold text-red-600">{broadcast.failed_count}</p>
          <p className="text-xs text-gray-500">
            {((broadcast.failed_count / broadcast.sent_count) * 100).toFixed(1)}% failure rate
          </p>
        </Card>
      </div>

      <div className="pt-4 border-t">
        <Button variant="secondary" className="w-full">
          <Download className="w-4 h-4 mr-2" />
          Export Detailed Report
        </Button>
      </div>
    </div>
  );
};

export default EnhancedBroadcasts;