import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { format, differenceInDays, parseISO } from 'date-fns';
import { Calendar, Clock, AlertTriangle, CheckCircle, XCircle, Eye, MessageSquare, User } from 'lucide-react';

interface TimeOffRequest {
  id: number;
  tutorId: number;
  tutorName: string;
  startDate: Date;
  endDate: Date;
  startTime?: string;
  endTime?: string;
  reason: string;
  requestType: 'vacation' | 'sick_leave' | 'personal' | 'holiday' | 'emergency' | 'other';
  status: 'pending' | 'approved' | 'denied' | 'cancelled';
  requestedAt: Date;
  approvedBy?: number;
  approvedAt?: Date;
  deniedReason?: string;
  affectedAppointments: number;
  isPartialDay: boolean;
  notes?: string;
}

interface TimeOffRequestProps {
  tutorId?: number;
  isModal?: boolean;
  onClose?: () => void;
  mode?: 'request' | 'manage' | 'view';
}

const TimeOffRequest: React.FC<TimeOffRequestProps> = ({
  tutorId,
  isModal = false,
  onClose,
  mode = 'request'
}) => {
  const { t } = useTranslation();
  const [requests, setRequests] = useState<TimeOffRequest[]>([]);
  const [selectedRequest, setSelectedRequest] = useState<TimeOffRequest | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  
  const [formData, setFormData] = useState({
    tutorId: tutorId || 0,
    startDate: '',
    endDate: '',
    startTime: '',
    endTime: '',
    reason: '',
    requestType: 'vacation' as const,
    isPartialDay: false
  });

  const [tutors, setTutors] = useState<Array<{ id: number; name: string; }>>([]);
  const [conflicts, setConflicts] = useState<Array<{ date: string; time: string; client: string; }>>([]);

  const requestTypeOptions = [
    { value: 'vacation', label: t('timeOff.vacation'), color: 'blue' },
    { value: 'sick_leave', label: t('timeOff.sickLeave'), color: 'red' },
    { value: 'personal', label: t('timeOff.personal'), color: 'green' },
    { value: 'holiday', label: t('timeOff.holiday'), color: 'purple' },
    { value: 'emergency', label: t('timeOff.emergency'), color: 'orange' },
    { value: 'other', label: t('timeOff.other'), color: 'gray' }
  ];

  // Mock data - replace with actual API calls
  useEffect(() => {
    const mockTutors = [
      { id: 1, name: 'Marie Dubois' },
      { id: 2, name: 'John Smith' },
      { id: 3, name: 'Sophie Martin' },
      { id: 4, name: 'David Wilson' }
    ];

    const mockRequests: TimeOffRequest[] = [
      {
        id: 1,
        tutorId: 1,
        tutorName: 'Marie Dubois',
        startDate: new Date(2024, 1, 15),
        endDate: new Date(2024, 1, 20),
        reason: 'Family vacation to Europe',
        requestType: 'vacation',
        status: 'pending',
        requestedAt: new Date(2024, 0, 10),
        affectedAppointments: 3,
        isPartialDay: false
      },
      {
        id: 2,
        tutorId: 2,
        tutorName: 'John Smith',
        startDate: new Date(2024, 1, 8),
        endDate: new Date(2024, 1, 8),
        startTime: '14:00',
        endTime: '17:00',
        reason: 'Medical appointment',
        requestType: 'personal',
        status: 'approved',
        requestedAt: new Date(2024, 0, 5),
        approvedAt: new Date(2024, 0, 6),
        approvedBy: 1,
        affectedAppointments: 1,
        isPartialDay: true
      },
      {
        id: 3,
        tutorId: 3,
        tutorName: 'Sophie Martin',
        startDate: new Date(2024, 0, 25),
        endDate: new Date(2024, 0, 26),
        reason: 'Flu symptoms',
        requestType: 'sick_leave',
        status: 'approved',
        requestedAt: new Date(2024, 0, 24),
        approvedAt: new Date(2024, 0, 24),
        approvedBy: 1,
        affectedAppointments: 4,
        isPartialDay: false
      }
    ];

    setTutors(mockTutors);
    setRequests(mockRequests);
    setLoading(false);
  }, []);

  // Check for conflicts when dates change
  useEffect(() => {
    if (formData.tutorId && formData.startDate && formData.endDate) {
      checkConflicts();
    }
  }, [formData.tutorId, formData.startDate, formData.endDate, formData.startTime, formData.endTime]);

  const checkConflicts = async () => {
    // Mock conflict checking - replace with actual API call
    const mockConflicts = [
      { date: '2024-02-16', time: '14:00-15:30', client: 'Emma Johnson' },
      { date: '2024-02-18', time: '10:00-11:00', client: 'Lucas Martin' }
    ];
    
    setConflicts(mockConflicts);
  };

  const handleSubmitRequest = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.tutorId || !formData.startDate || !formData.endDate || !formData.reason.trim()) {
      alert(t('timeOff.validation.requiredFields'));
      return;
    }

    if (new Date(formData.startDate) > new Date(formData.endDate)) {
      alert(t('timeOff.validation.endDateAfterStart'));
      return;
    }

    setSaving(true);
    try {
      // API call would go here
      console.log('Submitting time-off request:', formData);
      
      // Reset form
      setFormData({
        tutorId: tutorId || 0,
        startDate: '',
        endDate: '',
        startTime: '',
        endTime: '',
        reason: '',
        requestType: 'vacation',
        isPartialDay: false
      });
      
      setShowForm(false);
      alert(t('timeOff.requestSubmitted'));
    } catch (error) {
      console.error('Error submitting request:', error);
      alert(t('timeOff.submitError'));
    } finally {
      setSaving(false);
    }
  };

  const handleApproveRequest = async (requestId: number, notes?: string) => {
    setSaving(true);
    try {
      // API call would go here
      console.log('Approving request:', requestId, notes);
      
      // Update local state
      setRequests(prev => prev.map(req => 
        req.id === requestId 
          ? { ...req, status: 'approved' as const, approvedAt: new Date(), approvedBy: 1 }
          : req
      ));
      
      alert(t('timeOff.requestApproved'));
    } catch (error) {
      console.error('Error approving request:', error);
      alert(t('timeOff.approveError'));
    } finally {
      setSaving(false);
    }
  };

  const handleDenyRequest = async (requestId: number, reason: string) => {
    if (!reason.trim()) {
      alert(t('timeOff.validation.denialReasonRequired'));
      return;
    }

    setSaving(true);
    try {
      // API call would go here
      console.log('Denying request:', requestId, reason);
      
      // Update local state
      setRequests(prev => prev.map(req => 
        req.id === requestId 
          ? { ...req, status: 'denied' as const, deniedReason: reason }
          : req
      ));
      
      alert(t('timeOff.requestDenied'));
    } catch (error) {
      console.error('Error denying request:', error);
      alert(t('timeOff.denyError'));
    } finally {
      setSaving(false);
    }
  };

  const getStatusColor = (status: string) => {
    const colors = {
      pending: 'text-yellow-600 bg-yellow-100',
      approved: 'text-green-600 bg-green-100',
      denied: 'text-red-600 bg-red-100',
      cancelled: 'text-gray-600 bg-gray-100'
    };
    return colors[status as keyof typeof colors] || colors.pending;
  };

  const getTypeColor = (type: string) => {
    const typeConfig = requestTypeOptions.find(opt => opt.value === type);
    const colorMap = {
      blue: 'text-red-600 bg-red-100',
      red: 'text-red-600 bg-red-100',
      green: 'text-green-600 bg-green-100',
      purple: 'text-purple-600 bg-purple-100',
      orange: 'text-orange-600 bg-orange-100',
      gray: 'text-gray-600 bg-gray-100'
    };
    return colorMap[typeConfig?.color as keyof typeof colorMap] || colorMap.gray;
  };

  const renderRequestForm = () => (
    <div className="bg-white rounded-lg p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">
          {t('timeOff.newRequest')}
        </h3>
        <button
          onClick={() => setShowForm(false)}
          className="text-gray-400 hover:text-gray-600"
        >
          <XCircle className="w-6 h-6" />
        </button>
      </div>

      <form onSubmit={handleSubmitRequest} className="space-y-4">
        {/* Tutor Selection (if not pre-selected) */}
        {!tutorId && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <User className="w-4 h-4 inline mr-1" />
              {t('timeOff.tutor')}
            </label>
            <select
              value={formData.tutorId}
              onChange={(e) => setFormData(prev => ({ ...prev, tutorId: parseInt(e.target.value) }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              required
            >
              <option value="">{t('timeOff.selectTutor')}</option>
              {tutors.map((tutor) => (
                <option key={tutor.id} value={tutor.id}>
                  {tutor.name}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Request Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t('timeOff.requestType')}
          </label>
          <select
            value={formData.requestType}
            onChange={(e) => setFormData(prev => ({ ...prev, requestType: e.target.value as any }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
            required
          >
            {requestTypeOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Dates */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Calendar className="w-4 h-4 inline mr-1" />
              {t('timeOff.startDate')}
            </label>
            <input
              type="date"
              value={formData.startDate}
              onChange={(e) => setFormData(prev => ({ ...prev, startDate: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('timeOff.endDate')}
            </label>
            <input
              type="date"
              value={formData.endDate}
              onChange={(e) => setFormData(prev => ({ ...prev, endDate: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              required
            />
          </div>
        </div>

        {/* Partial Day Option */}
        <div>
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={formData.isPartialDay}
              onChange={(e) => setFormData(prev => ({ ...prev, isPartialDay: e.target.checked }))}
              className="rounded border-gray-300 text-red-600 focus:ring-red-500"
            />
            <span className="text-sm text-gray-700">{t('timeOff.partialDay')}</span>
          </label>
        </div>

        {/* Time Range (if partial day) */}
        {formData.isPartialDay && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Clock className="w-4 h-4 inline mr-1" />
                {t('timeOff.startTime')}
              </label>
              <input
                type="time"
                value={formData.startTime}
                onChange={(e) => setFormData(prev => ({ ...prev, startTime: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('timeOff.endTime')}
              </label>
              <input
                type="time"
                value={formData.endTime}
                onChange={(e) => setFormData(prev => ({ ...prev, endTime: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              />
            </div>
          </div>
        )}

        {/* Reason */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t('timeOff.reason')}
          </label>
          <textarea
            value={formData.reason}
            onChange={(e) => setFormData(prev => ({ ...prev, reason: e.target.value }))}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
            placeholder={t('timeOff.reasonPlaceholder')}
            required
          />
        </div>

        {/* Conflicts Warning */}
        {conflicts.length > 0 && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
            <div className="flex items-center">
              <AlertTriangle className="w-5 h-5 text-yellow-600 mr-2" />
              <h4 className="text-sm font-medium text-yellow-800">
                {t('timeOff.conflictsDetected')} ({conflicts.length})
              </h4>
            </div>
            <div className="mt-2 space-y-1">
              {conflicts.map((conflict, index) => (
                <div key={index} className="text-sm text-yellow-700">
                  • {conflict.date} {conflict.time} - {conflict.client}
                </div>
              ))}
            </div>
            <p className="text-sm text-yellow-700 mt-2">
              {t('timeOff.conflictWarning')}
            </p>
          </div>
        )}

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <button
            type="button"
            onClick={() => setShowForm(false)}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            {t('common.cancel')}
          </button>
          <button
            type="submit"
            disabled={saving}
            className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 disabled:opacity-50"
          >
            {saving ? t('common.submitting') : t('timeOff.submitRequest')}
          </button>
        </div>
      </form>
    </div>
  );

  const renderRequestsList = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">
          {t('timeOff.requests')}
        </h3>
        {mode === 'request' && (
          <button
            onClick={() => setShowForm(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700"
          >
            {t('timeOff.newRequest')}
          </button>
        )}
      </div>

      <div className="space-y-3">
        {requests.map((request) => (
          <div
            key={request.id}
            className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 cursor-pointer"
            onClick={() => setSelectedRequest(request)}
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-2">
                  <span className="font-medium text-gray-900">{request.tutorName}</span>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(request.requestType)}`}>
                    {requestTypeOptions.find(opt => opt.value === request.requestType)?.label}
                  </span>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(request.status)}`}>
                    {t(`timeOff.status.${request.status}`)}
                  </span>
                </div>
                
                <div className="text-sm text-gray-600 mb-1">
                  {format(request.startDate, 'MMM dd, yyyy')} - {format(request.endDate, 'MMM dd, yyyy')}
                  {request.isPartialDay && request.startTime && request.endTime && (
                    <span className="ml-2">({request.startTime} - {request.endTime})</span>
                  )}
                  <span className="ml-2">
                    ({differenceInDays(request.endDate, request.startDate) + 1} {t('timeOff.days')})
                  </span>
                </div>
                
                <p className="text-sm text-gray-700 truncate">{request.reason}</p>
                
                {request.affectedAppointments > 0 && (
                  <div className="flex items-center mt-2 text-xs text-orange-600">
                    <AlertTriangle className="w-3 h-3 mr-1" />
                    {request.affectedAppointments} {t('timeOff.affectedAppointments')}
                  </div>
                )}
              </div>
              
              <div className="flex items-center space-x-2">
                {mode === 'manage' && request.status === 'pending' && (
                  <>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleApproveRequest(request.id);
                      }}
                      className="p-1 text-green-600 hover:text-green-800"
                      title={t('timeOff.approve')}
                    >
                      <CheckCircle className="w-5 h-5" />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        const reason = prompt(t('timeOff.denialReason'));
                        if (reason) handleDenyRequest(request.id, reason);
                      }}
                      className="p-1 text-red-600 hover:text-red-800"
                      title={t('timeOff.deny')}
                    >
                      <XCircle className="w-5 h-5" />
                    </button>
                  </>
                )}
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedRequest(request);
                  }}
                  className="p-1 text-gray-400 hover:text-gray-600"
                  title={t('timeOff.viewDetails')}
                >
                  <Eye className="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {requests.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          {t('timeOff.noRequests')}
        </div>
      )}
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
      </div>
    );
  }

  const content = (
    <div className="space-y-6">
      {showForm ? renderRequestForm() : renderRequestsList()}
    </div>
  );

  if (isModal) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
          <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">
              {t('timeOff.title')}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XCircle className="w-6 h-6" />
            </button>
          </div>
          <div className="p-6">
            {content}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      {content}
    </div>
  );
};

export default TimeOffRequest;
