import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import api from '../../services/api';
import { ConsentFlow } from '../consent/ConsentFlow';
import LoadingSpinner from '../ui/LoadingSpinner';

interface ConsentGuardProps {
  children: React.ReactNode;
  requiredConsents?: string[];
}

export const ConsentGuard: React.FC<ConsentGuardProps> = ({ 
  children, 
  requiredConsents 
}) => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [hasValidConsents, setHasValidConsents] = useState(false);
  const [needsConsent, setNeedsConsent] = useState(false);

  useEffect(() => {
    checkConsents();
  }, []);

  const checkConsents = async () => {
    try {
      const response = await api.get('/consent/validate');
      const validation = response.data;
      
      setHasValidConsents(validation.is_valid);
      setNeedsConsent(!validation.is_valid);
    } catch (error) {
      console.error('Error checking consents:', error);
      // If consent check fails, allow access but log the error
      setHasValidConsents(true);
      setNeedsConsent(false);
    } finally {
      setLoading(false);
    }
  };

  const handleConsentComplete = () => {
    setNeedsConsent(false);
    setHasValidConsents(true);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (needsConsent) {
    return (
      <ConsentFlow 
        onComplete={handleConsentComplete}
        blockAccess={true}
      />
    );
  }

  return <>{children}</>;
};

export default ConsentGuard;