import React from 'react';
import { useTranslation } from 'react-i18next';
import { 
  AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell, LineChart, Line,
  XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer
} from 'recharts';
import { TrendingUp, Users, BookOpen, Clock, Target } from 'lucide-react';

// Mock data
const userGrowth = [
  { month: 'Jan', clients: 120, tutors: 45, total: 165 },
  { month: 'Feb', clients: 145, tutors: 52, total: 197 },
  { month: 'Mar', clients: 168, tutors: 58, total: 226 },
  { month: 'Apr', clients: 195, tutors: 65, total: 260 },
  { month: 'May', clients: 220, tutors: 72, total: 292 },
  { month: 'Jun', clients: 248, tutors: 78, total: 326 },
];

const subjectDistribution = [
  { subject: 'Mathematics', value: 35, color: '#007AFF' },
  { subject: 'Science', value: 25, color: '#34C759' },
  { subject: 'French', value: 20, color: '#FF9500' },
  { subject: 'English', value: 15, color: '#FF3B30' },
  { subject: 'Other', value: 5, color: '#8E8E93' },
];

const sessionTypes = [
  { type: 'Online', percentage: 65 },
  { type: 'In-Person', percentage: 25 },
  { type: 'Hybrid', percentage: 10 },
];

const conversionFunnel = [
  { stage: 'Website Visits', value: 1000 },
  { stage: 'Sign-ups', value: 250 },
  { stage: 'Profile Complete', value: 180 },
  { stage: 'First Session', value: 150 },
  { stage: 'Repeat Client', value: 120 },
];

const AnalyticsReport: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-text-primary">
          {t('sidebar.analytics')} Report
        </h1>
        <p className="text-text-secondary mt-1">
          Comprehensive analytics and insights for data-driven decisions
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
        <div className="bg-white rounded-large shadow-soft p-6">
          <div className="flex items-center justify-between mb-4">
            <Users className="w-6 h-6 text-accent-red" />
            <span className="text-xs text-accent-green">+24%</span>
          </div>
          <h3 className="text-2xl font-semibold text-text-primary">326</h3>
          <p className="text-sm text-text-secondary mt-1">Total Users</p>
        </div>

        <div className="bg-white rounded-large shadow-soft p-6">
          <div className="flex items-center justify-between mb-4">
            <BookOpen className="w-6 h-6 text-accent-green" />
            <span className="text-xs text-accent-green">+18%</span>
          </div>
          <h3 className="text-2xl font-semibold text-text-primary">1,234</h3>
          <p className="text-sm text-text-secondary mt-1">Sessions/Month</p>
        </div>

        <div className="bg-white rounded-large shadow-soft p-6">
          <div className="flex items-center justify-between mb-4">
            <Clock className="w-6 h-6 text-accent-orange" />
            <span className="text-xs text-accent-green">+5%</span>
          </div>
          <h3 className="text-2xl font-semibold text-text-primary">1.5h</h3>
          <p className="text-sm text-text-secondary mt-1">Avg Duration</p>
        </div>

        <div className="bg-white rounded-large shadow-soft p-6">
          <div className="flex items-center justify-between mb-4">
            <Target className="w-6 h-6 text-purple-500" />
            <span className="text-xs text-accent-green">+12%</span>
          </div>
          <h3 className="text-2xl font-semibold text-text-primary">92%</h3>
          <p className="text-sm text-text-secondary mt-1">Satisfaction</p>
        </div>

        <div className="bg-white rounded-large shadow-soft p-6">
          <div className="flex items-center justify-between mb-4">
            <TrendingUp className="w-6 h-6 text-accent-red" />
            <span className="text-xs text-accent-green">+8%</span>
          </div>
          <h3 className="text-2xl font-semibold text-text-primary">85%</h3>
          <p className="text-sm text-text-secondary mt-1">Retention</p>
        </div>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* User Growth */}
        <div className="bg-white rounded-large shadow-soft p-6">
          <h2 className="text-lg font-semibold text-text-primary mb-4">
            User Growth Trend
          </h2>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={userGrowth}>
              <CartesianGrid strokeDasharray="3 3" stroke="#E9ECEF" />
              <XAxis dataKey="month" stroke="#6C757D" />
              <YAxis stroke="#6C757D" />
              <Tooltip />
              <Legend />
              <Area 
                type="monotone" 
                dataKey="clients" 
                stackId="1"
                stroke="#007AFF" 
                fill="#007AFF"
                fillOpacity={0.6}
                name="Clients"
              />
              <Area 
                type="monotone" 
                dataKey="tutors" 
                stackId="1"
                stroke="#34C759" 
                fill="#34C759"
                fillOpacity={0.6}
                name="Tutors"
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>

        {/* Subject Distribution */}
        <div className="bg-white rounded-large shadow-soft p-6">
          <h2 className="text-lg font-semibold text-text-primary mb-4">
            Subject Distribution
          </h2>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={subjectDistribution}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ subject, value }) => `${subject}: ${value}%`}
                outerRadius={100}
                fill="#8884d8"
                dataKey="value"
              >
                {subjectDistribution.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>

        {/* Session Types */}
        <div className="bg-white rounded-large shadow-soft p-6">
          <h2 className="text-lg font-semibold text-text-primary mb-4">
            Session Type Preferences
          </h2>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={sessionTypes} layout="horizontal">
              <CartesianGrid strokeDasharray="3 3" stroke="#E9ECEF" />
              <XAxis type="number" stroke="#6C757D" domain={[0, 100]} />
              <YAxis dataKey="type" type="category" stroke="#6C757D" width={80} />
              <Tooltip formatter={(value) => `${value}%`} />
              <Bar dataKey="percentage" fill="#007AFF" radius={[0, 8, 8, 0]}>
                <Cell fill="#007AFF" />
                <Cell fill="#34C759" />
                <Cell fill="#FF9500" />
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Conversion Funnel */}
        <div className="bg-white rounded-large shadow-soft p-6">
          <h2 className="text-lg font-semibold text-text-primary mb-4">
            User Conversion Funnel
          </h2>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={conversionFunnel}>
              <CartesianGrid strokeDasharray="3 3" stroke="#E9ECEF" />
              <XAxis dataKey="stage" stroke="#6C757D" angle={-45} textAnchor="end" height={80} />
              <YAxis stroke="#6C757D" />
              <Tooltip />
              <Bar dataKey="value" fill="#007AFF" radius={[8, 8, 0, 0]}>
                {conversionFunnel.map((entry, index) => (
                  <Cell 
                    key={`cell-${index}`} 
                    fill={`rgba(0, 122, 255, ${1 - (index * 0.15)})`} 
                  />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Insights */}
      <div className="mt-8 grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="bg-white rounded-large shadow-soft p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-3">
            Key Insights
          </h3>
          <ul className="space-y-2">
            <li className="flex items-start gap-2">
              <span className="text-accent-green">•</span>
              <span className="text-sm text-text-secondary">
                Online sessions increased by 15% this month
              </span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-accent-red">•</span>
              <span className="text-sm text-text-secondary">
                Math tutoring remains the most popular subject
              </span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-accent-orange">•</span>
              <span className="text-sm text-text-secondary">
                Peak usage hours are 4-7 PM on weekdays
              </span>
            </li>
          </ul>
        </div>

        <div className="bg-white rounded-large shadow-soft p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-3">
            Recommendations
          </h3>
          <ul className="space-y-2">
            <li className="flex items-start gap-2">
              <span className="text-accent-red">•</span>
              <span className="text-sm text-text-secondary">
                Expand online tutoring capacity
              </span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-accent-green">•</span>
              <span className="text-sm text-text-secondary">
                Recruit more science tutors
              </span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-accent-orange">•</span>
              <span className="text-sm text-text-secondary">
                Optimize scheduling for peak hours
              </span>
            </li>
          </ul>
        </div>

        <div className="bg-white rounded-large shadow-soft p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-3">
            Action Items
          </h3>
          <ul className="space-y-2">
            <li className="flex items-start gap-2">
              <input type="checkbox" className="mt-0.5" />
              <span className="text-sm text-text-secondary">
                Launch tutor recruitment campaign
              </span>
            </li>
            <li className="flex items-start gap-2">
              <input type="checkbox" className="mt-0.5" />
              <span className="text-sm text-text-secondary">
                Improve onboarding conversion
              </span>
            </li>
            <li className="flex items-start gap-2">
              <input type="checkbox" className="mt-0.5" />
              <span className="text-sm text-text-secondary">
                Analyze retention patterns
              </span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsReport;