"""
Language Preference Repository for TutorAide Application.
Handles database operations for user language preferences with Quebec French support.
"""

import logging
from typing import Optional, List, Dict, Any, Tuple
from datetime import datetime, date, timedelta
import asyncpg
from contextlib import asynccontextmanager

from app.database.repositories.base import BaseRepository
from app.models.language_preference_models import (
    UserLanguagePreference, LanguagePreferenceHistory, LanguageUsageAnalytics,
    LanguageSource, ChangeSource, LanguagePreferenceStats, BulkLanguageUpdateResult
)
from app.locales.constants import SUPPORTED_LANGUAGES, DEFAULT_LANGUAGE


logger = logging.getLogger(__name__)


class LanguagePreferenceRepository(BaseRepository):
    """Repository for managing user language preferences."""
    
    async def get_user_language_preference(self, user_id: int) -> Optional[UserLanguagePreference]:
        """Get user's language preference."""
        query = """
            SELECT user_id, preferred_language, language_auto_detect, 
                   language_source, language_updated_at, quebec_french_preference
            FROM users 
            WHERE user_id = $1 AND deleted_at IS NULL
        """
        
        async with self.get_db_connection() as conn:
            row = await conn.fetchrow(query, user_id)
            if row:
                return UserLanguagePreference(**dict(row))
            return None
    
    async def update_user_language_preference(
        self,
        user_id: int,
        preferred_language: str,
        language_auto_detect: Optional[bool] = None,
        language_source: LanguageSource = LanguageSource.MANUAL,
        quebec_french_preference: Optional[bool] = None,
        change_context: Optional[Dict[str, Any]] = None
    ) -> UserLanguagePreference:
        """Update user's language preference with history tracking."""
        
        # Validate language
        if preferred_language not in SUPPORTED_LANGUAGES:
            raise ValueError(f"Unsupported language: {preferred_language}")
        
        update_fields = ["preferred_language = $2", "language_source = $3"]
        params = [user_id, preferred_language, language_source.value]
        param_count = 3
        
        if language_auto_detect is not None:
            param_count += 1
            update_fields.append(f"language_auto_detect = ${param_count}")
            params.append(language_auto_detect)
        
        if quebec_french_preference is not None:
            param_count += 1
            update_fields.append(f"quebec_french_preference = ${param_count}")
            params.append(quebec_french_preference)
        
        # Add updated timestamp
        param_count += 1
        update_fields.append(f"language_updated_at = ${param_count}")
        params.append(datetime.utcnow())
        
        query = f"""
            UPDATE users 
            SET {', '.join(update_fields)}
            WHERE user_id = $1 AND deleted_at IS NULL
            RETURNING user_id, preferred_language, language_auto_detect, 
                      language_source, language_updated_at, quebec_french_preference
        """
        
        async with self.get_db_connection() as conn:
            row = await conn.fetchrow(query, *params)
            if not row:
                raise ValueError(f"User {user_id} not found or inactive")
            
            # Log the change with context if provided
            if change_context:
                await self._log_preference_change(
                    conn, user_id, change_context, language_source
                )
            
            return UserLanguagePreference(**dict(row))
    
    async def _log_preference_change(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        context: Dict[str, Any],
        change_source: LanguageSource
    ) -> None:
        """Log language preference change with context."""
        
        # Map language source to change source
        change_source_mapping = {
            LanguageSource.MANUAL: ChangeSource.USER_MANUAL,
            LanguageSource.AUTO: ChangeSource.AUTO_DETECT,
            LanguageSource.BROWSER: ChangeSource.BROWSER_DETECT,
            LanguageSource.SYSTEM: ChangeSource.SYSTEM_MIGRATION
        }
        
        query = """
            INSERT INTO user_language_preferences_history (
                user_id, new_language, change_source, change_reason,
                browser_language, ip_address, user_agent, session_id
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        """
        
        await conn.execute(
            query,
            user_id,
            context.get('new_language'),
            change_source_mapping.get(change_source, ChangeSource.USER_MANUAL).value,
            context.get('reason', 'Language preference updated'),
            context.get('browser_language'),
            context.get('ip_address'),
            context.get('user_agent'),
            context.get('session_id')
        )
    
    async def get_language_preference_history(
        self,
        user_id: int,
        limit: int = 50,
        offset: int = 0
    ) -> Tuple[List[LanguagePreferenceHistory], int]:
        """Get user's language preference change history."""
        
        count_query = """
            SELECT COUNT(*) 
            FROM user_language_preferences_history 
            WHERE user_id = $1
        """
        
        history_query = """
            SELECT preference_history_id, user_id, previous_language, new_language,
                   change_source, change_reason, browser_language, ip_address,
                   user_agent, session_id, created_at, created_by
            FROM user_language_preferences_history 
            WHERE user_id = $1
            ORDER BY created_at DESC
            LIMIT $2 OFFSET $3
        """
        
        async with self.get_db_connection() as conn:
            total_count = await conn.fetchval(count_query, user_id)
            rows = await conn.fetch(history_query, user_id, limit, offset)
            
            history = [LanguagePreferenceHistory(**dict(row)) for row in rows]
            return history, total_count
    
    async def get_language_usage_analytics(
        self,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        language_code: Optional[str] = None
    ) -> List[LanguageUsageAnalytics]:
        """Get language usage analytics for date range."""
        
        conditions = ["1=1"]
        params = []
        param_count = 0
        
        if start_date:
            param_count += 1
            conditions.append(f"date_recorded >= ${param_count}")
            params.append(start_date)
        
        if end_date:
            param_count += 1
            conditions.append(f"date_recorded <= ${param_count}")
            params.append(end_date)
        
        if language_code:
            param_count += 1
            conditions.append(f"language_code = ${param_count}")
            params.append(language_code)
        
        query = f"""
            SELECT analytics_id, date_recorded, language_code, total_users,
                   new_users, active_sessions, manual_switches, auto_detections,
                   quebec_french_users, browser_preferences, geographic_data, created_at
            FROM language_usage_analytics
            WHERE {' AND '.join(conditions)}
            ORDER BY date_recorded DESC, language_code
        """
        
        async with self.get_db_connection() as conn:
            rows = await conn.fetch(query, *params)
            return [LanguageUsageAnalytics(**dict(row)) for row in rows]
    
    async def update_daily_analytics(self, analytics_date: date = None) -> None:
        """Update daily language usage analytics."""
        if analytics_date is None:
            analytics_date = date.today()
        
        query = """
            INSERT INTO language_usage_analytics (
                date_recorded, language_code, total_users, new_users,
                active_sessions, manual_switches, auto_detections, quebec_french_users
            )
            WITH language_stats AS (
                SELECT 
                    preferred_language as language_code,
                    COUNT(*) as total_users,
                    COUNT(*) FILTER (WHERE DATE(created_at) = $1) as new_users,
                    COUNT(*) FILTER (WHERE quebec_french_preference = true) as quebec_french_users
                FROM users 
                WHERE deleted_at IS NULL 
                  AND preferred_language IS NOT NULL
                GROUP BY preferred_language
            ),
            manual_switches AS (
                SELECT 
                    new_language as language_code,
                    COUNT(*) as manual_switches
                FROM user_language_preferences_history
                WHERE DATE(created_at) = $1 
                  AND change_source = 'user_manual'
                GROUP BY new_language
            ),
            auto_detections AS (
                SELECT 
                    new_language as language_code,
                    COUNT(*) as auto_detections
                FROM user_language_preferences_history
                WHERE DATE(created_at) = $1 
                  AND change_source IN ('auto_detect', 'browser_detect')
                GROUP BY new_language
            )
            SELECT 
                $1 as date_recorded,
                ls.language_code,
                ls.total_users,
                ls.new_users,
                0 as active_sessions, -- This would be updated from session tracking
                COALESCE(ms.manual_switches, 0) as manual_switches,
                COALESCE(ad.auto_detections, 0) as auto_detections,
                ls.quebec_french_users
            FROM language_stats ls
            LEFT JOIN manual_switches ms ON ls.language_code = ms.language_code
            LEFT JOIN auto_detections ad ON ls.language_code = ad.language_code
            ON CONFLICT (date_recorded, language_code) 
            DO UPDATE SET
                total_users = EXCLUDED.total_users,
                new_users = EXCLUDED.new_users,
                manual_switches = EXCLUDED.manual_switches,
                auto_detections = EXCLUDED.auto_detections,
                quebec_french_users = EXCLUDED.quebec_french_users,
                created_at = CURRENT_TIMESTAMP
        """
        
        async with self.get_db_connection() as conn:
            await conn.execute(query, analytics_date)
    
    async def get_language_preference_stats(self) -> LanguagePreferenceStats:
        """Get comprehensive language preference statistics."""
        
        query = """
            WITH user_stats AS (
                SELECT 
                    COUNT(*) as total_users,
                    COUNT(*) FILTER (WHERE language_auto_detect = true) as auto_detect_enabled,
                    COUNT(*) FILTER (WHERE quebec_french_preference = true) as quebec_french_preference
                FROM users 
                WHERE deleted_at IS NULL
            ),
            language_dist AS (
                SELECT 
                    preferred_language,
                    COUNT(*) as count
                FROM users 
                WHERE deleted_at IS NULL AND preferred_language IS NOT NULL
                GROUP BY preferred_language
            ),
            recent_changes AS (
                SELECT COUNT(*) as recent_changes
                FROM user_language_preferences_history
                WHERE created_at >= CURRENT_TIMESTAMP - INTERVAL '7 days'
            ),
            source_stats AS (
                SELECT 
                    language_source,
                    COUNT(*) as count
                FROM users 
                WHERE deleted_at IS NULL
                GROUP BY language_source
                ORDER BY count DESC
                LIMIT 1
            ),
            avg_changes AS (
                SELECT 
                    COALESCE(AVG(change_count), 0) as avg_changes_per_user
                FROM (
                    SELECT user_id, COUNT(*) as change_count
                    FROM user_language_preferences_history
                    GROUP BY user_id
                ) user_change_counts
            )
            SELECT 
                us.total_users,
                us.auto_detect_enabled,
                us.quebec_french_preference,
                rc.recent_changes,
                ss.language_source as most_common_source,
                ac.avg_changes_per_user,
                json_object_agg(ld.preferred_language, ld.count) as language_distribution
            FROM user_stats us
            CROSS JOIN recent_changes rc
            CROSS JOIN source_stats ss
            CROSS JOIN avg_changes ac
            LEFT JOIN language_dist ld ON true
            GROUP BY us.total_users, us.auto_detect_enabled, us.quebec_french_preference,
                     rc.recent_changes, ss.language_source, ac.avg_changes_per_user
        """
        
        async with self.get_db_connection() as conn:
            row = await conn.fetchrow(query)
            if row:
                return LanguagePreferenceStats(
                    total_users=row['total_users'],
                    language_distribution=row['language_distribution'] or {},
                    auto_detect_enabled_count=row['auto_detect_enabled'],
                    quebec_french_preference_count=row['quebec_french_preference'],
                    recent_changes_count=row['recent_changes'],
                    most_common_source=LanguageSource(row['most_common_source']),
                    average_changes_per_user=float(row['avg_changes_per_user'])
                )
            
            # Return empty stats if no data
            return LanguagePreferenceStats(
                total_users=0,
                language_distribution={},
                auto_detect_enabled_count=0,
                quebec_french_preference_count=0,
                recent_changes_count=0,
                most_common_source=LanguageSource.AUTO,
                average_changes_per_user=0.0
            )
    
    async def bulk_update_language_preferences(
        self,
        user_ids: List[int],
        preferred_language: str,
        language_auto_detect: Optional[bool] = None,
        quebec_french_preference: Optional[bool] = None,
        change_reason: str = "Bulk update",
        updated_by: Optional[int] = None
    ) -> BulkLanguageUpdateResult:
        """Bulk update language preferences for multiple users."""
        
        if preferred_language not in SUPPORTED_LANGUAGES:
            raise ValueError(f"Unsupported language: {preferred_language}")
        
        if not user_ids:
            return BulkLanguageUpdateResult(
                success=True,
                updated_count=0,
                failed_count=0,
                failed_user_ids=[],
                errors=[],
                message="No users to update"
            )
        
        update_fields = ["preferred_language = $2", "language_source = 'system'", "language_updated_at = CURRENT_TIMESTAMP"]
        params = [user_ids, preferred_language]
        param_count = 2
        
        if language_auto_detect is not None:
            param_count += 1
            update_fields.append(f"language_auto_detect = ${param_count}")
            params.append(language_auto_detect)
        
        if quebec_french_preference is not None:
            param_count += 1
            update_fields.append(f"quebec_french_preference = ${param_count}")
            params.append(quebec_french_preference)
        
        async with self.get_db_connection() as conn:
            async with conn.transaction():
                try:
                    # Update users
                    update_query = f"""
                        UPDATE users 
                        SET {', '.join(update_fields)}
                        WHERE user_id = ANY($1) AND deleted_at IS NULL
                        RETURNING user_id
                    """
                    
                    updated_rows = await conn.fetch(update_query, *params)
                    updated_user_ids = [row['user_id'] for row in updated_rows]
                    updated_count = len(updated_user_ids)
                    failed_user_ids = [uid for uid in user_ids if uid not in updated_user_ids]
                    
                    # Log changes for updated users
                    if updated_user_ids:
                        history_query = """
                            INSERT INTO user_language_preferences_history (
                                user_id, new_language, change_source, change_reason, created_by
                            )
                            SELECT 
                                unnest($1::int[]),
                                $2,
                                'admin_override',
                                $3,
                                $4
                        """
                        
                        await conn.execute(
                            history_query,
                            updated_user_ids,
                            preferred_language,
                            change_reason,
                            updated_by
                        )
                    
                    return BulkLanguageUpdateResult(
                        success=True,
                        updated_count=updated_count,
                        failed_count=len(failed_user_ids),
                        failed_user_ids=failed_user_ids,
                        errors=[],
                        message=f"Successfully updated {updated_count} users"
                    )
                    
                except Exception as e:
                    logger.error(f"Bulk language update failed: {e}")
                    return BulkLanguageUpdateResult(
                        success=False,
                        updated_count=0,
                        failed_count=len(user_ids),
                        failed_user_ids=user_ids,
                        errors=[str(e)],
                        message="Bulk update failed"
                    )
    
    async def get_users_by_language_preference(
        self,
        language: str,
        auto_detect_only: bool = False,
        quebec_french_only: bool = False,
        limit: int = 100,
        offset: int = 0
    ) -> Tuple[List[int], int]:
        """Get users filtered by language preferences."""
        
        conditions = ["deleted_at IS NULL", "preferred_language = $1"]
        params = [language]
        param_count = 1
        
        if auto_detect_only:
            param_count += 1
            conditions.append(f"language_auto_detect = ${param_count}")
            params.append(True)
        
        if quebec_french_only and language == 'fr':
            param_count += 1
            conditions.append(f"quebec_french_preference = ${param_count}")
            params.append(True)
        
        count_query = f"""
            SELECT COUNT(*) 
            FROM users 
            WHERE {' AND '.join(conditions)}
        """
        
        users_query = f"""
            SELECT user_id
            FROM users 
            WHERE {' AND '.join(conditions)}
            ORDER BY created_at DESC
            LIMIT ${param_count + 1} OFFSET ${param_count + 2}
        """
        
        params.extend([limit, offset])
        
        async with self.get_db_connection() as conn:
            total_count = await conn.fetchval(count_query, *params[:-2])
            rows = await conn.fetch(users_query, *params)
            
            user_ids = [row['user_id'] for row in rows]
            return user_ids, total_count
    
    async def cleanup_old_preference_history(self, days_to_keep: int = 365) -> int:
        """Clean up old preference history records."""
        
        query = """
            DELETE FROM user_language_preferences_history
            WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '%s days'
        """
        
        async with self.get_db_connection() as conn:
            result = await conn.execute(query, days_to_keep)
            # Extract number of deleted rows from result string
            deleted_count = int(result.split()[-1]) if result.startswith('DELETE') else 0
            
            logger.info(f"Cleaned up {deleted_count} old language preference history records")
            return deleted_count