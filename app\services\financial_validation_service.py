"""
Financial data validation service for billing and payment operations.

This service provides comprehensive validation for all financial data including:
- Amount validation with currency precision
- Invoice data validation
- Payment method validation
- Tax calculation validation
- Subscription validation
- Price validation
"""

import re
import logging
from decimal import Decimal, InvalidOperation
from datetime import date, datetime
from typing import Dict, Any, List, Optional, Union

from app.core.exceptions import ValidationError
from app.models.base import PaymentStatus
from app.config.settings import settings

logger = logging.getLogger(__name__)


class FinancialValidationService:
    """
    Service for validating all financial data in the system.
    
    Ensures data integrity, prevents fraud, and maintains compliance.
    """
    
    # Currency configurations
    SUPPORTED_CURRENCIES = {'CAD', 'USD'}
    CURRENCY_PRECISION = {
        'CAD': 2,
        'USD': 2
    }
    
    # Amount limits
    MIN_AMOUNT = Decimal('0.01')
    MAX_SINGLE_TRANSACTION = Decimal('10000.00')
    MAX_INVOICE_AMOUNT = Decimal('50000.00')
    MAX_SUBSCRIPTION_AMOUNT = Decimal('5000.00')
    
    # Rate limits
    MIN_HOURLY_RATE = Decimal('10.00')
    MAX_HOURLY_RATE = Decimal('500.00')
    
    # Valid payment methods
    VALID_PAYMENT_METHODS = {
        'stripe', 'cash', 'cheque', 'e_transfer', 
        'bank_transfer', 'credit_card', 'debit_card'
    }
    
    # Tax rates for Canadian provinces
    TAX_RATES = {
        'AB': {'GST': Decimal('0.05'), 'PST': Decimal('0.00'), 'HST': Decimal('0.00')},
        'BC': {'GST': Decimal('0.05'), 'PST': Decimal('0.07'), 'HST': Decimal('0.00')},
        'MB': {'GST': Decimal('0.05'), 'PST': Decimal('0.07'), 'HST': Decimal('0.00')},
        'NB': {'GST': Decimal('0.00'), 'PST': Decimal('0.00'), 'HST': Decimal('0.15')},
        'NL': {'GST': Decimal('0.00'), 'PST': Decimal('0.00'), 'HST': Decimal('0.15')},
        'NS': {'GST': Decimal('0.00'), 'PST': Decimal('0.00'), 'HST': Decimal('0.15')},
        'NT': {'GST': Decimal('0.05'), 'PST': Decimal('0.00'), 'HST': Decimal('0.00')},
        'NU': {'GST': Decimal('0.05'), 'PST': Decimal('0.00'), 'HST': Decimal('0.00')},
        'ON': {'GST': Decimal('0.00'), 'PST': Decimal('0.00'), 'HST': Decimal('0.13')},
        'PE': {'GST': Decimal('0.00'), 'PST': Decimal('0.00'), 'HST': Decimal('0.15')},
        'QC': {'GST': Decimal('0.05'), 'PST': Decimal('0.09975'), 'HST': Decimal('0.00')},
        'SK': {'GST': Decimal('0.05'), 'PST': Decimal('0.06'), 'HST': Decimal('0.00')},
        'YT': {'GST': Decimal('0.05'), 'PST': Decimal('0.00'), 'HST': Decimal('0.00')}
    }
    
    def validate_amount(
        self,
        amount: Union[str, float, Decimal],
        currency: str = 'CAD',
        min_amount: Optional[Decimal] = None,
        max_amount: Optional[Decimal] = None,
        field_name: str = 'amount'
    ) -> Decimal:
        """
        Validate and normalize monetary amount.
        
        Args:
            amount: Amount to validate
            currency: Currency code
            min_amount: Minimum allowed amount
            max_amount: Maximum allowed amount
            field_name: Field name for error messages
            
        Returns:
            Validated Decimal amount
            
        Raises:
            ValidationError: If amount is invalid
        """
        try:
            # Convert to Decimal
            if isinstance(amount, str):
                # Remove currency symbols and whitespace
                amount = re.sub(r'[^\d.-]', '', amount)
            
            decimal_amount = Decimal(str(amount))
            
            # Check if negative
            if decimal_amount < 0:
                raise ValidationError(f"{field_name} cannot be negative")
            
            # Check precision
            precision = self.CURRENCY_PRECISION.get(currency, 2)
            if decimal_amount.as_tuple().exponent < -precision:
                raise ValidationError(
                    f"{field_name} has too many decimal places for {currency}"
                )
            
            # Check minimum
            min_check = min_amount if min_amount is not None else self.MIN_AMOUNT
            if decimal_amount < min_check:
                raise ValidationError(
                    f"{field_name} must be at least {min_check}"
                )
            
            # Check maximum
            max_check = max_amount if max_amount is not None else self.MAX_SINGLE_TRANSACTION
            if decimal_amount > max_check:
                raise ValidationError(
                    f"{field_name} cannot exceed {max_check}"
                )
            
            # Quantize to currency precision
            quantized = decimal_amount.quantize(Decimal(f'0.{"0" * precision}'))
            
            return quantized
            
        except (InvalidOperation, ValueError) as e:
            raise ValidationError(f"Invalid {field_name}: {str(e)}")
    
    def validate_currency(self, currency: str) -> str:
        """Validate currency code."""
        if not currency:
            raise ValidationError("Currency is required")
        
        currency = currency.upper()
        
        if currency not in self.SUPPORTED_CURRENCIES:
            raise ValidationError(
                f"Currency {currency} not supported. Use: {', '.join(self.SUPPORTED_CURRENCIES)}"
            )
        
        return currency
    
    def validate_invoice_data(self, invoice_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate invoice creation/update data.
        
        Args:
            invoice_data: Invoice data to validate
            
        Returns:
            Validated invoice data
        """
        validated = {}
        
        # Required fields
        required_fields = ['client_id', 'currency']
        for field in required_fields:
            if field not in invoice_data:
                raise ValidationError(f"Missing required field: {field}")
        
        # Validate client ID
        if not isinstance(invoice_data['client_id'], int) or invoice_data['client_id'] <= 0:
            raise ValidationError("Invalid client_id")
        validated['client_id'] = invoice_data['client_id']
        
        # Validate currency
        validated['currency'] = self.validate_currency(invoice_data['currency'])
        
        # Validate amounts if present
        if 'subtotal' in invoice_data:
            validated['subtotal'] = self.validate_amount(
                invoice_data['subtotal'],
                validated['currency'],
                max_amount=self.MAX_INVOICE_AMOUNT,
                field_name='subtotal'
            )
        
        if 'tax_amount' in invoice_data:
            validated['tax_amount'] = self.validate_amount(
                invoice_data['tax_amount'],
                validated['currency'],
                min_amount=Decimal('0.00'),
                field_name='tax_amount'
            )
        
        if 'total_amount' in invoice_data:
            validated['total_amount'] = self.validate_amount(
                invoice_data['total_amount'],
                validated['currency'],
                max_amount=self.MAX_INVOICE_AMOUNT,
                field_name='total_amount'
            )
        
        # Validate dates
        if 'due_date' in invoice_data:
            validated['due_date'] = self._validate_date(
                invoice_data['due_date'],
                'due_date',
                allow_past=False
            )
        
        # Validate status if present
        if 'status' in invoice_data:
            if invoice_data['status'] not in InvoiceStatus.__members__.values():
                raise ValidationError(f"Invalid invoice status: {invoice_data['status']}")
            validated['status'] = invoice_data['status']
        
        # Validate line items if present
        if 'line_items' in invoice_data:
            validated['line_items'] = self._validate_line_items(
                invoice_data['line_items'],
                validated['currency']
            )
        
        return validated
    
    def validate_payment_data(self, payment_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate payment creation/update data.
        
        Args:
            payment_data: Payment data to validate
            
        Returns:
            Validated payment data
        """
        validated = {}
        
        # Required fields
        required_fields = ['amount', 'currency', 'payment_method']
        for field in required_fields:
            if field not in payment_data:
                raise ValidationError(f"Missing required field: {field}")
        
        # Validate amount and currency
        validated['currency'] = self.validate_currency(payment_data['currency'])
        validated['amount'] = self.validate_amount(
            payment_data['amount'],
            validated['currency'],
            field_name='payment amount'
        )
        
        # Validate payment method
        if payment_data['payment_method'] not in self.VALID_PAYMENT_METHODS:
            raise ValidationError(f"Invalid payment method: {payment_data['payment_method']}")
        validated['payment_method'] = payment_data['payment_method']
        
        # Validate IDs if present
        if 'invoice_id' in payment_data:
            if not isinstance(payment_data['invoice_id'], int) or payment_data['invoice_id'] <= 0:
                raise ValidationError("Invalid invoice_id")
            validated['invoice_id'] = payment_data['invoice_id']
        
        if 'client_id' in payment_data:
            if not isinstance(payment_data['client_id'], int) or payment_data['client_id'] <= 0:
                raise ValidationError("Invalid client_id")
            validated['client_id'] = payment_data['client_id']
        
        # Validate card data if present (ensure no raw card numbers)
        if 'card_number' in payment_data:
            raise ValidationError(
                "Raw card numbers cannot be processed. Use tokenization."
            )
        
        # Validate metadata
        if 'metadata' in payment_data:
            validated['metadata'] = self._validate_metadata(payment_data['metadata'])
        
        return validated
    
    def validate_subscription_data(self, subscription_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate subscription creation/update data.
        
        Args:
            subscription_data: Subscription data to validate
            
        Returns:
            Validated subscription data
        """
        validated = {}
        
        # Required fields
        required_fields = ['client_id', 'package_id', 'payment_method']
        for field in required_fields:
            if field not in subscription_data:
                raise ValidationError(f"Missing required field: {field}")
        
        # Validate IDs
        for id_field in ['client_id', 'package_id']:
            if not isinstance(subscription_data[id_field], int) or subscription_data[id_field] <= 0:
                raise ValidationError(f"Invalid {id_field}")
            validated[id_field] = subscription_data[id_field]
        
        # Validate hours if present
        if 'total_hours' in subscription_data:
            hours = Decimal(str(subscription_data['total_hours']))
            if hours <= 0 or hours > 1000:
                raise ValidationError("Invalid total_hours: must be between 0 and 1000")
            validated['total_hours'] = hours
        
        # Validate payment data
        if 'payment_amount' in subscription_data:
            validated['payment_amount'] = self.validate_amount(
                subscription_data['payment_amount'],
                subscription_data.get('currency', 'CAD'),
                max_amount=self.MAX_SUBSCRIPTION_AMOUNT,
                field_name='payment_amount'
            )
        
        return validated
    
    def validate_tax_calculation(
        self,
        subtotal: Decimal,
        province: str,
        tax_exempt: bool = False
    ) -> Dict[str, Decimal]:
        """
        Validate and calculate taxes for Canadian provinces.
        
        Args:
            subtotal: Pre-tax amount
            province: Two-letter province code
            tax_exempt: Whether the client is tax exempt
            
        Returns:
            Dict with tax amounts (gst, pst, hst, total_tax)
        """
        if tax_exempt:
            return {
                'gst': Decimal('0.00'),
                'pst': Decimal('0.00'),
                'hst': Decimal('0.00'),
                'total_tax': Decimal('0.00')
            }
        
        if province not in self.TAX_RATES:
            raise ValidationError(f"Invalid province code: {province}")
        
        rates = self.TAX_RATES[province]
        
        # Calculate taxes
        gst = (subtotal * rates['GST']).quantize(Decimal('0.01'))
        pst = (subtotal * rates['PST']).quantize(Decimal('0.01'))
        hst = (subtotal * rates['HST']).quantize(Decimal('0.01'))
        
        # In Quebec, PST is calculated on subtotal + GST
        if province == 'QC' and pst > 0:
            pst = ((subtotal + gst) * rates['PST']).quantize(Decimal('0.01'))
        
        total_tax = gst + pst + hst
        
        return {
            'gst': gst,
            'pst': pst,
            'hst': hst,
            'total_tax': total_tax
        }
    
    def validate_hourly_rate(
        self,
        rate: Union[str, float, Decimal],
        rate_type: str = 'tutor'
    ) -> Decimal:
        """
        Validate hourly rate for tutors or clients.
        
        Args:
            rate: Hourly rate to validate
            rate_type: Type of rate ('tutor' or 'client')
            
        Returns:
            Validated rate as Decimal
        """
        validated_rate = self.validate_amount(
            rate,
            'CAD',
            min_amount=self.MIN_HOURLY_RATE,
            max_amount=self.MAX_HOURLY_RATE,
            field_name=f'{rate_type} hourly rate'
        )
        
        return validated_rate
    
    def validate_commission(
        self,
        client_rate: Decimal,
        tutor_rate: Decimal
    ) -> Dict[str, Decimal]:
        """
        Validate commission calculation.
        
        Args:
            client_rate: Rate charged to client
            tutor_rate: Rate paid to tutor
            
        Returns:
            Commission amount and percentage
        """
        if tutor_rate > client_rate:
            raise ValidationError(
                "Tutor rate cannot exceed client rate"
            )
        
        commission_amount = client_rate - tutor_rate
        
        if client_rate > 0:
            commission_percentage = (commission_amount / client_rate * 100).quantize(
                Decimal('0.01')
            )
        else:
            commission_percentage = Decimal('0.00')
        
        return {
            'commission_amount': commission_amount,
            'commission_percentage': commission_percentage
        }
    
    def validate_refund_data(self, refund_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate refund request data.
        
        Args:
            refund_data: Refund data to validate
            
        Returns:
            Validated refund data
        """
        validated = {}
        
        # Required fields
        if 'payment_id' not in refund_data:
            raise ValidationError("Missing required field: payment_id")
        
        if not isinstance(refund_data['payment_id'], int) or refund_data['payment_id'] <= 0:
            raise ValidationError("Invalid payment_id")
        validated['payment_id'] = refund_data['payment_id']
        
        # Validate refund amount
        if 'amount' in refund_data:
            validated['amount'] = self.validate_amount(
                refund_data['amount'],
                refund_data.get('currency', 'CAD'),
                field_name='refund amount'
            )
        
        # Validate reason
        if 'reason' in refund_data:
            reason = str(refund_data['reason']).strip()
            if len(reason) < 10:
                raise ValidationError("Refund reason must be at least 10 characters")
            if len(reason) > 500:
                raise ValidationError("Refund reason cannot exceed 500 characters")
            validated['reason'] = reason
        else:
            raise ValidationError("Refund reason is required")
        
        return validated
    
    def validate_bank_account(self, bank_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate Canadian bank account information.
        
        Args:
            bank_data: Bank account data
            
        Returns:
            Validated bank data
        """
        validated = {}
        
        # Validate institution number (3 digits)
        if 'institution_number' in bank_data:
            inst_num = str(bank_data['institution_number']).strip()
            if not re.match(r'^\d{3}$', inst_num):
                raise ValidationError("Institution number must be 3 digits")
            validated['institution_number'] = inst_num
        
        # Validate transit number (5 digits)
        if 'transit_number' in bank_data:
            transit_num = str(bank_data['transit_number']).strip()
            if not re.match(r'^\d{5}$', transit_num):
                raise ValidationError("Transit number must be 5 digits")
            validated['transit_number'] = transit_num
        
        # Validate account number (5-12 digits)
        if 'account_number' in bank_data:
            account_num = str(bank_data['account_number']).strip()
            if not re.match(r'^\d{5,12}$', account_num):
                raise ValidationError("Account number must be 5-12 digits")
            # Only store masked version
            validated['account_number_masked'] = '*' * (len(account_num) - 4) + account_num[-4:]
        
        return validated
    
    def _validate_date(
        self,
        date_value: Any,
        field_name: str,
        allow_past: bool = True
    ) -> date:
        """Validate date value."""
        if isinstance(date_value, str):
            try:
                date_obj = datetime.strptime(date_value, '%Y-%m-%d').date()
            except ValueError:
                raise ValidationError(f"Invalid {field_name} format. Use YYYY-MM-DD")
        elif isinstance(date_value, datetime):
            date_obj = date_value.date()
        elif isinstance(date_value, date):
            date_obj = date_value
        else:
            raise ValidationError(f"Invalid {field_name} type")
        
        if not allow_past and date_obj < date.today():
            raise ValidationError(f"{field_name} cannot be in the past")
        
        return date_obj
    
    def _validate_line_items(
        self,
        line_items: List[Dict[str, Any]],
        currency: str
    ) -> List[Dict[str, Any]]:
        """Validate invoice line items."""
        if not isinstance(line_items, list):
            raise ValidationError("Line items must be a list")
        
        if len(line_items) == 0:
            raise ValidationError("At least one line item is required")
        
        if len(line_items) > 100:
            raise ValidationError("Cannot exceed 100 line items")
        
        validated_items = []
        
        for idx, item in enumerate(line_items):
            validated_item = {}
            
            # Validate description
            if 'description' not in item or not item['description']:
                raise ValidationError(f"Line item {idx + 1}: description required")
            validated_item['description'] = str(item['description'])[:200]
            
            # Validate quantity
            if 'quantity' not in item:
                raise ValidationError(f"Line item {idx + 1}: quantity required")
            
            try:
                quantity = Decimal(str(item['quantity']))
                if quantity <= 0:
                    raise ValidationError(f"Line item {idx + 1}: quantity must be positive")
                validated_item['quantity'] = quantity
            except (InvalidOperation, ValueError):
                raise ValidationError(f"Line item {idx + 1}: invalid quantity")
            
            # Validate unit price
            if 'unit_price' not in item:
                raise ValidationError(f"Line item {idx + 1}: unit_price required")
            
            validated_item['unit_price'] = self.validate_amount(
                item['unit_price'],
                currency,
                field_name=f'Line item {idx + 1} unit price'
            )
            
            # Calculate line total
            validated_item['total'] = (
                validated_item['quantity'] * validated_item['unit_price']
            ).quantize(Decimal('0.01'))
            
            validated_items.append(validated_item)
        
        return validated_items
    
    def _validate_metadata(self, metadata: Any) -> Dict[str, str]:
        """Validate metadata dictionary."""
        if not isinstance(metadata, dict):
            raise ValidationError("Metadata must be a dictionary")
        
        # Limit metadata size
        if len(metadata) > 50:
            raise ValidationError("Metadata cannot exceed 50 keys")
        
        validated_metadata = {}
        
        for key, value in metadata.items():
            # Validate key
            if not isinstance(key, str) or not key:
                raise ValidationError("Metadata keys must be non-empty strings")
            
            if len(key) > 100:
                raise ValidationError(f"Metadata key '{key}' exceeds 100 characters")
            
            # Validate value
            if value is None:
                continue
            
            str_value = str(value)
            if len(str_value) > 500:
                raise ValidationError(f"Metadata value for '{key}' exceeds 500 characters")
            
            validated_metadata[key] = str_value
        
        return validated_metadata