-- Rollback Migration: 019_add_payment_security_rollback.sql
-- Description: Rollback secure payment processing tables
-- Author: System
-- Date: 2024-01-15

-- Drop indexes
DROP INDEX IF EXISTS idx_audit_log_event;
DROP INDEX IF EXISTS idx_audit_log_payment;
DROP INDEX IF EXISTS idx_payment_failures_client;
DROP INDEX IF EXISTS idx_payment_attempts_client;
DROP INDEX IF EXISTS idx_payment_methods_client;
DROP INDEX IF EXISTS idx_secure_payments_created;
DROP INDEX IF EXISTS idx_secure_payments_status;
DROP INDEX IF EXISTS idx_secure_payments_client;

-- Drop tables in reverse order of dependencies
DROP TABLE IF EXISTS payment_audit_log;
DROP TABLE IF EXISTS payment_failures;
DROP TABLE IF EXISTS payment_attempts;
DROP TABLE IF EXISTS secure_payment_methods;
DROP TABLE IF EXISTS secure_payment_records;