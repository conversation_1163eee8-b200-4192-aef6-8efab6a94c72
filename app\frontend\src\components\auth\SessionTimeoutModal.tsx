import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import Button from '../common/Button';
import { Clock, AlertTriangle } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';

interface SessionTimeoutModalProps {
  isOpen: boolean;
  timeRemaining: number; // in seconds
  onExtend: () => void;
  onLogout: () => void;
}

export const SessionTimeoutModal: React.FC<SessionTimeoutModalProps> = ({
  isOpen,
  timeRemaining,
  onExtend,
  onLogout,
}) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [displayTime, setDisplayTime] = useState(timeRemaining);

  useEffect(() => {
    setDisplayTime(timeRemaining);
  }, [timeRemaining]);

  useEffect(() => {
    if (isOpen && displayTime > 0) {
      const timer = setTimeout(() => setDisplayTime(displayTime - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [isOpen, displayTime]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50">
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        />

        {/* Modal */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          transition={{ duration: 0.2, ease: 'easeOut' }}
          className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-md"
        >
          <div className="bg-white rounded-2xl shadow-elevated p-6">
            {/* Icon */}
            <div className="mx-auto w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mb-4">
              {displayTime > 30 ? (
                <Clock className="w-8 h-8 text-orange-600" />
              ) : (
                <AlertTriangle className="w-8 h-8 text-orange-600 animate-pulse" />
              )}
            </div>

            {/* Content */}
            <div className="text-center mb-6">
              <h2 className="text-xl font-semibold text-text-primary mb-2">
                {t('auth.session.timeoutTitle')}
              </h2>
              <p className="text-text-secondary">
                {t('auth.session.timeoutMessage')}
              </p>
              
              {/* Timer Display */}
              <div className="mt-4">
                <div className="text-3xl font-bold text-accent-red">
                  {formatTime(displayTime)}
                </div>
                <p className="text-sm text-text-muted mt-1">
                  {t('auth.session.timeRemaining')}
                </p>
              </div>
            </div>

            {/* Actions */}
            <div className="flex gap-3">
              <Button
                variant="secondary"
                onClick={onLogout}
                className="flex-1"
                size="lg"
              >
                {t('auth.session.logout')}
              </Button>
              <Button
                onClick={onExtend}
                className="flex-1"
                size="lg"
              >
                {t('auth.session.stayLoggedIn')}
              </Button>
            </div>

            {/* Info */}
            <p className="text-xs text-text-muted text-center mt-4">
              {t('auth.session.securityNote')}
            </p>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};