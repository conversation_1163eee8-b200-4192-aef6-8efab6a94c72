import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { X, Search, CreditCard, Calendar, Clock } from 'lucide-react';
import { Modal } from '../common/Modal';
import Button from '../common/Button';
import { Input } from '../common/Input';
import { Badge } from '../common/Badge';
import { subscriptionService, SubscriptionPlan, CreateSubscriptionRequest, SubscriptionType } from '../../services/subscriptionService';
import { clientService, ClientProfile } from '../../services/clientService';
import toast from 'react-hot-toast';
import { format } from 'date-fns';

interface AddSubscriptionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  defaultClientId?: number;
}

const AddSubscriptionModal: React.FC<AddSubscriptionModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  defaultClientId
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null);
  const [selectedClient, setSelectedClient] = useState<ClientProfile | null>(null);
  const [clientSearchTerm, setClientSearchTerm] = useState('');
  const [clientSearchResults, setClientSearchResults] = useState<ClientProfile[]>([]);
  const [searchingClients, setSearchingClients] = useState(false);
  const [formData, setFormData] = useState<CreateSubscriptionRequest>({
    client_id: defaultClientId || 0,
    plan_id: 0,
    start_date: new Date().toISOString().split('T')[0],
    payment_method: '',
    auto_renew: true
  });

  useEffect(() => {
    loadPlans();
    if (defaultClientId) {
      loadDefaultClient();
    }
  }, [defaultClientId]);

  const loadPlans = async () => {
    try {
      const plansData = await subscriptionService.getPlans({ is_active: true });
      setPlans(plansData);
    } catch (error) {
      console.error('Error loading plans:', error);
      toast.error(t('billing.subscriptions.plansLoadError'));
    }
  };

  const loadDefaultClient = async () => {
    if (!defaultClientId) return;
    
    try {
      const client = await clientService.getClient(defaultClientId);
      setSelectedClient(client);
    } catch (error) {
      console.error('Error loading default client:', error);
    }
  };

  const searchClients = async () => {
    if (!clientSearchTerm) return;
    
    setSearchingClients(true);
    try {
      const response = await clientService.searchClients({
        search: clientSearchTerm,
        limit: 10
      });
      setClientSearchResults(response.items);
    } catch (error) {
      console.error('Error searching clients:', error);
      toast.error(t('billing.subscriptions.clientSearchError'));
    } finally {
      setSearchingClients(false);
    }
  };

  const selectClient = (client: ClientProfile) => {
    setSelectedClient(client);
    setFormData(prev => ({
      ...prev,
      client_id: client.client_id
    }));
    setClientSearchResults([]);
    setClientSearchTerm('');
  };

  const selectPlan = (plan: SubscriptionPlan) => {
    setSelectedPlan(plan);
    setFormData(prev => ({
      ...prev,
      plan_id: plan.plan_id
    }));
  };

  const getTypeLabel = (type: SubscriptionType) => {
    switch (type) {
      case SubscriptionType.MONTHLY:
        return t('billing.subscriptions.types.monthly');
      case SubscriptionType.QUARTERLY:
        return t('billing.subscriptions.types.quarterly');
      case SubscriptionType.ANNUAL:
        return t('billing.subscriptions.types.annual');
      case SubscriptionType.CUSTOM:
        return t('billing.subscriptions.types.custom');
      default:
        return type;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.client_id || !formData.plan_id) {
      toast.error(t('validation.requiredFields'));
      return;
    }

    setLoading(true);
    try {
      await subscriptionService.createSubscription(formData);
      toast.success(t('billing.subscriptions.createSuccess'));
      onSuccess();
      onClose();
    } catch (error: any) {
      console.error('Error creating subscription:', error);
      toast.error(error.response?.data?.detail || t('billing.subscriptions.createError'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="lg">
      <form onSubmit={handleSubmit} className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">
            {t('billing.subscriptions.addTitle')}
          </h2>
          <button
            type="button"
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Form Fields */}
        <div className="space-y-6">
          {/* Client Selection */}
          {!defaultClientId && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {t('billing.subscriptions.selectClient')}
              </h3>
              
              {selectedClient ? (
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-gray-900">
                        {selectedClient.first_name} {selectedClient.last_name}
                      </div>
                      <div className="text-sm text-gray-600">{selectedClient.email}</div>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setSelectedClient(null);
                        setFormData(prev => ({ ...prev, client_id: 0 }));
                      }}
                    >
                      {t('common.change')}
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="space-y-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <Input
                      type="text"
                      value={clientSearchTerm}
                      onChange={(e) => setClientSearchTerm(e.target.value)}
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          searchClients();
                        }
                      }}
                      placeholder={t('billing.subscriptions.searchClient')}
                      className="pl-10"
                    />
                  </div>
                  
                  {clientSearchResults.length > 0 && (
                    <div className="border rounded-lg max-h-48 overflow-y-auto">
                      {clientSearchResults.map((client) => (
                        <button
                          key={client.client_id}
                          type="button"
                          onClick={() => selectClient(client)}
                          className="w-full text-left px-4 py-2 hover:bg-gray-50 border-b last:border-b-0"
                        >
                          <div className="font-medium">{client.first_name} {client.last_name}</div>
                          <div className="text-sm text-gray-600">{client.email}</div>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Plan Selection */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {t('billing.subscriptions.selectPlan')}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {plans.map((plan) => (
                <div
                  key={plan.plan_id}
                  onClick={() => selectPlan(plan)}
                  className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
                    selectedPlan?.plan_id === plan.plan_id
                      ? 'border-accent-red bg-red-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="font-semibold text-gray-900">{plan.name}</h4>
                    <Badge variant="info" size="sm">{getTypeLabel(plan.type)}</Badge>
                  </div>
                  {plan.description && (
                    <p className="text-sm text-gray-600 mb-3">{plan.description}</p>
                  )}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">{t('billing.subscriptions.hours')}</span>
                      <span className="font-semibold">{plan.hours_included} hrs</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">{t('billing.subscriptions.price')}</span>
                      <span className="font-semibold text-lg">
                        ${plan.price} {plan.currency}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">{t('billing.subscriptions.duration')}</span>
                      <span className="font-medium">{plan.duration_months} {t('common.months')}</span>
                    </div>
                  </div>
                  {plan.features && plan.features.length > 0 && (
                    <div className="mt-3 pt-3 border-t">
                      <ul className="text-sm text-gray-600 space-y-1">
                        {plan.features.map((feature, idx) => (
                          <li key={idx} className="flex items-start">
                            <span className="text-green-500 mr-1">✓</span>
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Additional Options */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {t('billing.subscriptions.additionalOptions')}
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('billing.subscriptions.startDate')}
                </label>
                <Input
                  type="date"
                  value={formData.start_date}
                  onChange={(e) => setFormData(prev => ({ ...prev, start_date: e.target.value }))}
                  min={new Date().toISOString().split('T')[0]}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('billing.subscriptions.paymentMethod')}
                </label>
                <select
                  value={formData.payment_method}
                  onChange={(e) => setFormData(prev => ({ ...prev, payment_method: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-red"
                >
                  <option value="">{t('common.select')}</option>
                  <option value="card">{t('billing.subscriptions.creditCard')}</option>
                  <option value="bank_transfer">{t('billing.subscriptions.bankTransfer')}</option>
                  <option value="cash">{t('billing.subscriptions.cash')}</option>
                  <option value="cheque">{t('billing.subscriptions.cheque')}</option>
                </select>
              </div>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.auto_renew}
                  onChange={(e) => setFormData(prev => ({ ...prev, auto_renew: e.target.checked }))}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700">{t('billing.subscriptions.enableAutoRenew')}</span>
              </label>
            </div>
          </div>

          {/* Summary */}
          {selectedClient && selectedPlan && (
            <div className="bg-blue-50 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 mb-2">{t('billing.subscriptions.summary')}</h4>
              <div className="space-y-1 text-sm">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">{t('billing.subscriptions.client')}:</span>
                  <span className="font-medium">{selectedClient.first_name} {selectedClient.last_name}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">{t('billing.subscriptions.plan')}:</span>
                  <span className="font-medium">{selectedPlan.name}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">{t('billing.subscriptions.total')}:</span>
                  <span className="font-semibold text-lg">
                    ${selectedPlan.price} {selectedPlan.currency}
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="mt-8 flex justify-end space-x-3">
          <Button type="button" variant="secondary" onClick={onClose}>
            {t('common.cancel')}
          </Button>
          <Button 
            type="submit" 
            variant="primary" 
            loading={loading}
            disabled={!selectedClient || !selectedPlan}
          >
            {t('billing.subscriptions.create')}
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default AddSubscriptionModal;