"""
Enhanced user models with comprehensive input validation and sanitization.
"""

from datetime import datetime, date
from typing import List, Optional, Dict, Any
from uuid import UUID
from pydantic import BaseModel, Field, field_validator, ConfigDict, ValidationInfo
from typing import Annotated
import re

from app.models.base import BaseEntity, IdentifiedEntity, UserRoleType
from app.core.validation import (
    email_validator, password_validator, phone_validator, name_validator,
    text_sanitizer, InputValidator, SecuritySanitizer, RateLimitValidator
)
from app.core.exceptions import ValidationError
from app.core.timezone import now_est


class EnhancedUserCreate(BaseModel):
    """Enhanced user creation model with comprehensive validation."""
    
    email: str = Field(..., description="User email address")
    password: Optional[str] = Field(None, description="User password (for local auth)")
    google_id: Optional[str] = Field(None, description="Google OAuth ID")
    full_name: str = Field(..., description="User's full name")
    first_name: Optional[str] = Field(None, description="First name")
    last_name: Optional[str] = Field(None, description="Last name")
    phone_number: Optional[str] = Field(None, description="Phone number")
    birth_date: Optional[date] = Field(None, description="Date of birth")
    roles: List[UserRoleType] = Field(default_factory=lambda: [UserRoleType.CLIENT], description="User roles")
    language_preference: str = Field(default="en", description="Preferred language")
    marketing_consent: bool = Field(False, description="Marketing communications consent")
    terms_accepted: bool = Field(..., description="Terms of service acceptance")
    privacy_accepted: bool = Field(..., description="Privacy policy acceptance")
    
    model_config = ConfigDict(from_attributes=True)
    
    @field_validator('email')
    @classmethod
    def validate_email(cls, v: str) -> str:
        """Validate email address with comprehensive checks."""
        try:
            return email_validator(v)
        except ValidationError as e:
            raise ValueError(str(e))
    
    @field_validator('password')
    @classmethod
    def validate_password(cls, v: Optional[str], info: ValidationInfo) -> Optional[str]:
        """Validate password strength and security."""
        if v is None:
            return v
        
        # Check if this is a Google OAuth registration
        google_id = info.data.get('google_id') if info.data else None
        if google_id and not v:
            return None  # Google OAuth users don't need password
        
        if not google_id and not v:
            raise ValueError("Password is required for local authentication")
        
        try:
            return password_validator(v, info)
        except ValidationError as e:
            raise ValueError(str(e))
    
    @field_validator('google_id')
    @classmethod
    def validate_google_id(cls, v: Optional[str]) -> Optional[str]:
        """Validate Google OAuth ID format."""
        if not v:
            return v
        
        # Google IDs are typically 21-character numeric strings
        if not re.match(r'^\d{10,30}$', v):
            raise ValueError("Invalid Google ID format")
        
        return v
    
    @field_validator('full_name')
    @classmethod
    def validate_full_name(cls, v: str) -> str:
        """Validate and sanitize full name."""
        try:
            return name_validator('full name')(v)
        except ValidationError as e:
            raise ValueError(str(e))
    
    @field_validator('first_name')
    @classmethod
    def validate_first_name(cls, v: Optional[str]) -> Optional[str]:
        """Validate and sanitize first name."""
        if not v:
            return v
        try:
            return name_validator('first name')(v)
        except ValidationError as e:
            raise ValueError(str(e))
    
    @field_validator('last_name')
    @classmethod
    def validate_last_name(cls, v: Optional[str]) -> Optional[str]:
        """Validate and sanitize last name."""
        if not v:
            return v
        try:
            return name_validator('last name')(v)
        except ValidationError as e:
            raise ValueError(str(e))
    
    @field_validator('phone_number')
    @classmethod
    def validate_phone_number(cls, v: Optional[str]) -> Optional[str]:
        """Validate and format phone number."""
        if not v:
            return v
        try:
            return phone_validator(v)
        except ValidationError as e:
            raise ValueError(str(e))
    
    @field_validator('birth_date')
    @classmethod
    def validate_birth_date(cls, v: Optional[date]) -> Optional[date]:
        """Validate birth date and age requirements."""
        if not v:
            return v
        
        today = now_est().date()
        age = today.year - v.year
        
        if today < v.replace(year=today.year):
            age -= 1
        
        if v > today:
            raise ValueError("Birth date cannot be in the future")
        
        if age < 13:
            raise ValueError("User must be at least 13 years old")
        
        if age > 120:
            raise ValueError("Invalid birth date")
        
        return v
    
    @field_validator('roles')
    @classmethod
    def validate_roles(cls, v: List[UserRoleType]) -> List[UserRoleType]:
        """Validate user roles."""
        if not v:
            return [UserRoleType.CLIENT]  # Default role
        
        # Check for valid role combinations
        if len(v) > 3:
            raise ValueError("Too many roles assigned")
        
        # Managers cannot have other roles initially
        if UserRoleType.MANAGER in v and len(v) > 1:
            raise ValueError("Manager role cannot be combined with other roles")
        
        return v
    
    @field_validator('language_preference')
    @classmethod
    def validate_language(cls, v: str) -> str:
        """Validate language preference."""
        return InputValidator.validate_language_code(v)
    
    @field_validator('terms_accepted')
    @classmethod
    def validate_terms_accepted(cls, v: bool) -> bool:
        """Ensure terms of service are accepted."""
        if not v:
            raise ValueError("Terms of service must be accepted")
        return v
    
    @field_validator('privacy_accepted')
    @classmethod
    def validate_privacy_accepted(cls, v: bool) -> bool:
        """Ensure privacy policy is accepted."""
        if not v:
            raise ValueError("Privacy policy must be accepted")
        return v
    
    def validate_auth_requirements(self) -> None:
        """Validate that user has at least one authentication method."""
        if not self.password and not self.google_id:
            raise ValueError("User must have either password or Google authentication")


class EnhancedLoginRequest(BaseModel):
    """Enhanced login request with validation and rate limiting."""
    
    email: str = Field(..., description="User email address")
    password: str = Field(..., description="User password")
    remember_me: bool = Field(False, description="Remember login session")
    role: Optional[UserRoleType] = Field(None, description="Specific role to login as")
    
    model_config = ConfigDict(from_attributes=True)
    
    @field_validator('email')
    @classmethod
    def validate_email(cls, v: str) -> str:
        """Validate email format and apply rate limiting."""
        email = email_validator(v)
        
        # Apply rate limiting for login attempts
        try:
            RateLimitValidator.check_rate_limit(email, 'login')
        except ValidationError as e:
            raise ValueError(f"Login rate limit exceeded: {str(e)}")
        
        return email
    
    @field_validator('password')
    @classmethod
    def validate_password(cls, v: str) -> str:
        """Basic password validation for login."""
        if not v:
            raise ValueError("Password is required")
        
        if len(v) > 128:  # Prevent DoS through very long passwords
            raise ValueError("Password too long")
        
        # Sanitize input
        return SecuritySanitizer.sanitize_text(v, max_length=128)
    
    @field_validator('role')
    @classmethod
    def validate_role(cls, v: Optional[UserRoleType]) -> Optional[UserRoleType]:
        """Validate requested login role."""
        if v and v not in [UserRoleType.CLIENT, UserRoleType.TUTOR, UserRoleType.MANAGER]:
            raise ValueError("Invalid role specified")
        return v


class EnhancedPasswordResetRequest(BaseModel):
    """Enhanced password reset request with validation."""
    
    email: str = Field(..., description="Email address for password reset")
    
    model_config = ConfigDict(from_attributes=True)
    
    @field_validator('email')
    @classmethod
    def validate_email(cls, v: str) -> str:
        """Validate email and apply rate limiting."""
        email = email_validator(v)
        
        # Apply rate limiting for password reset requests
        try:
            RateLimitValidator.check_rate_limit(email, 'password_reset')
        except ValidationError as e:
            raise ValueError(f"Password reset rate limit exceeded: {str(e)}")
        
        return email


class EnhancedPasswordResetConfirm(BaseModel):
    """Enhanced password reset confirmation with validation."""
    
    token: UUID = Field(..., description="Password reset token")
    new_password: str = Field(..., description="New password")
    confirm_password: str = Field(..., description="Password confirmation")
    
    model_config = ConfigDict(from_attributes=True)
    
    @field_validator('new_password')
    @classmethod
    def validate_new_password(cls, v: str) -> str:
        """Validate new password strength."""
        return password_validator(v, None)
    
    @field_validator('confirm_password')
    @classmethod
    def validate_password_match(cls, v: str, info: ValidationInfo) -> str:
        """Ensure password confirmation matches."""
        new_password = info.data.get('new_password') if info.data else None
        if new_password and v != new_password:
            raise ValueError("Password confirmation does not match")
        return v


class EnhancedUserUpdate(BaseModel):
    """Enhanced user update model with validation."""
    
    full_name: Optional[str] = Field(None, description="User's full name")
    first_name: Optional[str] = Field(None, description="First name")
    last_name: Optional[str] = Field(None, description="Last name")
    phone_number: Optional[str] = Field(None, description="Phone number")
    language_preference: Optional[str] = Field(None, description="Preferred language")
    marketing_consent: Optional[bool] = Field(None, description="Marketing consent")
    
    model_config = ConfigDict(from_attributes=True)
    
    @field_validator('full_name')
    @classmethod
    def validate_full_name(cls, v: Optional[str]) -> Optional[str]:
        """Validate and sanitize full name."""
        if not v:
            return v
        return name_validator('full name')(v)
    
    @field_validator('first_name')
    @classmethod
    def validate_first_name(cls, v: Optional[str]) -> Optional[str]:
        """Validate and sanitize first name."""
        if not v:
            return v
        try:
            return name_validator('first name')(v)
        except ValidationError as e:
            raise ValueError(str(e))
    
    @field_validator('last_name')
    @classmethod
    def validate_last_name(cls, v: Optional[str]) -> Optional[str]:
        """Validate and sanitize last name."""
        if not v:
            return v
        try:
            return name_validator('last name')(v)
        except ValidationError as e:
            raise ValueError(str(e))
    
    @field_validator('phone_number')
    @classmethod
    def validate_phone_number(cls, v: Optional[str]) -> Optional[str]:
        """Validate and format phone number."""
        if not v:
            return v
        try:
            return phone_validator(v)
        except ValidationError as e:
            raise ValueError(str(e))
    
    @field_validator('language_preference')
    @classmethod
    def validate_language(cls, v: Optional[str]) -> Optional[str]:
        """Validate language preference."""
        if not v:
            return v
        return InputValidator.validate_language_code(v)


class EnhancedSessionRequest(BaseModel):
    """Enhanced session management request with validation."""
    
    action: str = Field(..., description="Session action")
    session_id: Optional[UUID] = Field(None, description="Session ID for specific actions")
    
    model_config = ConfigDict(from_attributes=True)
    
    @field_validator('action')
    @classmethod
    def validate_action(cls, v: str) -> str:
        """Validate session action."""
        if not v:
            raise ValueError("Action is required")
        
        v = SecuritySanitizer.sanitize_text(v).lower()
        
        valid_actions = ['list', 'invalidate', 'invalidate_all', 'extend']
        if v not in valid_actions:
            raise ValueError(f"Invalid action. Must be one of: {', '.join(valid_actions)}")
        
        return v


class SecurityQuestion(BaseModel):
    """Security question model with validation."""
    
    question: str = Field(..., description="Security question")
    answer: str = Field(..., description="Security answer")
    
    model_config = ConfigDict(from_attributes=True)
    
    @field_validator('question')
    @classmethod
    def validate_question(cls, v: str) -> str:
        """Validate security question."""
        if not v:
            raise ValueError("Security question is required")
        
        v = SecuritySanitizer.sanitize_text(v, max_length=200)
        
        if len(v.strip()) < 10:
            raise ValueError("Security question must be at least 10 characters")
        
        return v.strip()
    
    @field_validator('answer')
    @classmethod
    def validate_answer(cls, v: str) -> str:
        """Validate security answer."""
        if not v:
            raise ValueError("Security answer is required")
        
        v = SecuritySanitizer.sanitize_text(v, max_length=100)
        
        if len(v.strip()) < 3:
            raise ValueError("Security answer must be at least 3 characters")
        
        return v.strip().lower()  # Normalize for comparison


class EnhancedFileUpload(BaseModel):
    """Enhanced file upload model with validation."""
    
    filename: str = Field(..., description="Original filename")
    content_type: str = Field(..., description="MIME content type")
    file_size: int = Field(..., description="File size in bytes")
    file_hash: Optional[str] = Field(None, description="File content hash")
    
    model_config = ConfigDict(from_attributes=True)
    
    @field_validator('filename')
    @classmethod
    def validate_filename(cls, v: str) -> str:
        """Validate filename security."""
        if not v:
            raise ValueError("Filename is required")
        
        # Sanitize filename
        v = SecuritySanitizer.sanitize_text(v, max_length=255)
        
        # Check for dangerous characters
        dangerous_chars = ['..', '/', '\\', '<', '>', ':', '"', '|', '?', '*']
        if any(char in v for char in dangerous_chars):
            raise ValueError("Filename contains invalid characters")
        
        # Check extension
        allowed_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.pdf', '.doc', '.docx', '.txt'}
        ext = '.' + v.lower().split('.')[-1] if '.' in v else ''
        
        if ext not in allowed_extensions:
            raise ValueError(f"File type not allowed: {ext}")
        
        return v
    
    @field_validator('content_type')
    @classmethod
    def validate_content_type(cls, v: str) -> str:
        """Validate MIME content type."""
        if not v:
            raise ValueError("Content type is required")
        
        allowed_types = {
            'image/jpeg', 'image/png', 'image/gif',
            'application/pdf', 'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain'
        }
        
        if v not in allowed_types:
            raise ValueError(f"Content type not allowed: {v}")
        
        return v
    
    @field_validator('file_size')
    @classmethod
    def validate_file_size(cls, v: int) -> int:
        """Validate file size limits."""
        if v <= 0:
            raise ValueError("Invalid file size")
        
        # 10MB limit
        max_size = 10 * 1024 * 1024
        if v > max_size:
            raise ValueError(f"File too large (max {max_size // (1024*1024)}MB)")
        
        return v


class ValidationResult(BaseModel):
    """Result of validation operations."""
    
    is_valid: bool = Field(..., description="Whether validation passed")
    errors: List[str] = Field(default_factory=list, description="Validation error messages")
    warnings: List[str] = Field(default_factory=list, description="Validation warnings")
    field_errors: Dict[str, List[str]] = Field(default_factory=dict, description="Field-specific errors")
    
    model_config = ConfigDict(from_attributes=True)
    
    def add_error(self, message: str, field: Optional[str] = None) -> None:
        """Add a validation error."""
        self.is_valid = False
        self.errors.append(message)
        
        if field:
            if field not in self.field_errors:
                self.field_errors[field] = []
            self.field_errors[field].append(message)
    
    def add_warning(self, message: str) -> None:
        """Add a validation warning."""
        self.warnings.append(message)
    
    def has_errors(self) -> bool:
        """Check if there are any validation errors."""
        return not self.is_valid or len(self.errors) > 0
    
    def get_error_summary(self) -> str:
        """Get a summary of all errors."""
        if not self.errors:
            return "No errors"
        
        if len(self.errors) == 1:
            return self.errors[0]
        
        return f"{len(self.errors)} validation errors: {'; '.join(self.errors[:3])}"