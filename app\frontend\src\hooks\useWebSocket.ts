import { useEffect, useRef, useState, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';

export interface WebSocketMessage {
  type: string;
  [key: string]: any;
}

export interface WebSocketConfig {
  url?: string;
  room?: string;
  autoConnect?: boolean;
  reconnectAttempts?: number;
  reconnectInterval?: number;
}

export interface WebSocketState {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  lastMessage: WebSocketMessage | null;
  connectionCount: number;
  roomUsers: any[];
}

const DEFAULT_CONFIG: Required<WebSocketConfig> = {
  url: '',
  room: '',
  autoConnect: true,
  reconnectAttempts: 5,
  reconnectInterval: 1000,
};

export const useWebSocket = (config: WebSocketConfig = {}) => {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  const { user, token } = useAuth();
  
  const [state, setState] = useState<WebSocketState>({
    isConnected: false,
    isConnecting: false,
    error: null,
    lastMessage: null,
    connectionCount: 0,
    roomUsers: [],
  });
  
  const websocketRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const messageHandlersRef = useRef<Map<string, ((message: WebSocketMessage) => void)[]>>(new Map());
  const pingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const getWebSocketUrl = useCallback(() => {
    if (finalConfig.url) return finalConfig.url;
    
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;
    const room = finalConfig.room ? `?room=${finalConfig.room}` : '';
    const tokenParam = token ? `${room ? '&' : '?'}token=${token}` : '';
    
    return `${protocol}//${host}/api/v1/ws/calendar${room}${tokenParam}`;
  }, [finalConfig.url, finalConfig.room, token]);

  const connect = useCallback(() => {
    if (!token || !user) {
      setState(prev => ({ ...prev, error: 'Authentication required' }));
      return;
    }

    if (websocketRef.current?.readyState === WebSocket.CONNECTING || 
        websocketRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    setState(prev => ({ ...prev, isConnecting: true, error: null }));

    try {
      const wsUrl = getWebSocketUrl();
      websocketRef.current = new WebSocket(wsUrl);

      websocketRef.current.onopen = () => {
        console.log('WebSocket connected');
        setState(prev => ({ 
          ...prev, 
          isConnected: true, 
          isConnecting: false, 
          error: null 
        }));
        
        reconnectAttemptsRef.current = 0;
        
        // Start ping interval to keep connection alive
        pingIntervalRef.current = setInterval(() => {
          sendMessage({ type: 'ping' });
        }, 30000); // Ping every 30 seconds
      };

      websocketRef.current.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          
          setState(prev => ({ ...prev, lastMessage: message }));
          
          // Handle specific message types
          if (message.type === 'connection_status') {
            setState(prev => ({
              ...prev,
              connectionCount: message.connection_count || 0,
              roomUsers: message.users || [],
            }));
          }
          
          // Call registered message handlers
          const handlers = messageHandlersRef.current.get(message.type) || [];
          const globalHandlers = messageHandlersRef.current.get('*') || [];
          
          [...handlers, ...globalHandlers].forEach(handler => {
            try {
              handler(message);
            } catch (error) {
              console.error('Error in message handler:', error);
            }
          });
          
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      websocketRef.current.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason);
        setState(prev => ({ 
          ...prev, 
          isConnected: false, 
          isConnecting: false 
        }));
        
        // Clear ping interval
        if (pingIntervalRef.current) {
          clearInterval(pingIntervalRef.current);
          pingIntervalRef.current = null;
        }
        
        // Attempt to reconnect if not a normal closure
        if (event.code !== 1000 && reconnectAttemptsRef.current < finalConfig.reconnectAttempts) {
          const delay = Math.min(1000 * Math.pow(2, reconnectAttemptsRef.current), 30000);
          
          setState(prev => ({ 
            ...prev, 
            error: `Connection lost. Reconnecting in ${delay / 1000}s...` 
          }));
          
          reconnectTimeoutRef.current = setTimeout(() => {
            reconnectAttemptsRef.current++;
            connect();
          }, delay);
        } else if (reconnectAttemptsRef.current >= finalConfig.reconnectAttempts) {
          setState(prev => ({ 
            ...prev, 
            error: 'Connection failed after multiple attempts' 
          }));
        }
      };

      websocketRef.current.onerror = (error) => {
        console.error('WebSocket error:', error);
        setState(prev => ({ 
          ...prev, 
          error: 'Connection error occurred' 
        }));
      };

    } catch (error) {
      console.error('Error creating WebSocket connection:', error);
      setState(prev => ({ 
        ...prev, 
        isConnecting: false, 
        error: 'Failed to create connection' 
      }));
    }
  }, [token, user, getWebSocketUrl, finalConfig.reconnectAttempts]);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    if (pingIntervalRef.current) {
      clearInterval(pingIntervalRef.current);
      pingIntervalRef.current = null;
    }

    if (websocketRef.current) {
      websocketRef.current.close(1000, 'Manual disconnect');
      websocketRef.current = null;
    }

    setState(prev => ({ 
      ...prev, 
      isConnected: false, 
      isConnecting: false,
      error: null
    }));
  }, []);

  const sendMessage = useCallback((message: WebSocketMessage) => {
    if (websocketRef.current?.readyState === WebSocket.OPEN) {
      try {
        websocketRef.current.send(JSON.stringify(message));
        return true;
      } catch (error) {
        console.error('Error sending WebSocket message:', error);
        return false;
      }
    }
    console.warn('WebSocket not connected. Cannot send message:', message);
    return false;
  }, []);

  const addMessageHandler = useCallback((messageType: string, handler: (message: WebSocketMessage) => void) => {
    const handlers = messageHandlersRef.current.get(messageType) || [];
    handlers.push(handler);
    messageHandlersRef.current.set(messageType, handlers);

    // Return cleanup function
    return () => {
      const currentHandlers = messageHandlersRef.current.get(messageType) || [];
      const filteredHandlers = currentHandlers.filter(h => h !== handler);
      
      if (filteredHandlers.length === 0) {
        messageHandlersRef.current.delete(messageType);
      } else {
        messageHandlersRef.current.set(messageType, filteredHandlers);
      }
    };
  }, []);

  const removeMessageHandler = useCallback((messageType: string, handler?: (message: WebSocketMessage) => void) => {
    if (handler) {
      const handlers = messageHandlersRef.current.get(messageType) || [];
      const filteredHandlers = handlers.filter(h => h !== handler);
      
      if (filteredHandlers.length === 0) {
        messageHandlersRef.current.delete(messageType);
      } else {
        messageHandlersRef.current.set(messageType, filteredHandlers);
      }
    } else {
      messageHandlersRef.current.delete(messageType);
    }
  }, []);

  // Auto-connect effect
  useEffect(() => {
    if (finalConfig.autoConnect && token && user) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [finalConfig.autoConnect, token, user, connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    ...state,
    connect,
    disconnect,
    sendMessage,
    addMessageHandler,
    removeMessageHandler,
  };
};

export default useWebSocket;