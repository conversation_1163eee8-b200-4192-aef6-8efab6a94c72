import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { I18nextProvider } from 'react-i18next';
import UserSettings from './UserSettings';
import i18n from '../../i18n';

const renderUserSettings = () => {
  return render(
    <I18nextProvider i18n={i18n}>
      <UserSettings />
    </I18nextProvider>
  );
};

describe('UserSettings', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders user settings page', () => {
    renderUserSettings();
    
    expect(screen.getByText('User Settings')).toBeInTheDocument();
    expect(screen.getByText('Manage user accounts and permissions')).toBeInTheDocument();
  });

  it('displays role management section', () => {
    renderUserSettings();
    
    expect(screen.getByText('Role Management')).toBeInTheDocument();
    expect(screen.getByText('Manager')).toBeInTheDocument();
    expect(screen.getByText('Tutor')).toBeInTheDocument();
    expect(screen.getByText('Client')).toBeInTheDocument();
  });

  it('shows permissions for each role', () => {
    renderUserSettings();
    
    // Manager permissions
    expect(screen.getByText('Full platform access')).toBeInTheDocument();
    expect(screen.getByText('User management')).toBeInTheDocument();
    expect(screen.getByText('Financial reports')).toBeInTheDocument();
    
    // Tutor permissions
    expect(screen.getByText('Schedule management')).toBeInTheDocument();
    expect(screen.getByText('Student communication')).toBeInTheDocument();
    
    // Client permissions
    expect(screen.getByText('Book appointments')).toBeInTheDocument();
    expect(screen.getByText('View invoices')).toBeInTheDocument();
  });

  it('displays account security section', () => {
    renderUserSettings();
    
    expect(screen.getByText('Account Security')).toBeInTheDocument();
    expect(screen.getByText('Two-Factor Authentication')).toBeInTheDocument();
    expect(screen.getByText('Password Requirements')).toBeInTheDocument();
  });

  it('shows password requirement settings', () => {
    renderUserSettings();
    
    expect(screen.getByText('Minimum 8 characters')).toBeInTheDocument();
    expect(screen.getByText('Require uppercase letter')).toBeInTheDocument();
    expect(screen.getByText('Require number')).toBeInTheDocument();
    expect(screen.getByText('Require special character')).toBeInTheDocument();
  });

  it('toggles 2FA requirement', async () => {
    renderUserSettings();
    
    const twoFAToggle = screen.getByRole('checkbox', { name: /require two-factor authentication/i });
    expect(twoFAToggle).toBeChecked();
    
    fireEvent.click(twoFAToggle);
    expect(twoFAToggle).not.toBeChecked();
  });

  it('updates session timeout settings', async () => {
    const user = userEvent.setup();
    renderUserSettings();
    
    const timeoutInput = screen.getByDisplayValue('30');
    await user.clear(timeoutInput);
    await user.type(timeoutInput, '60');
    
    expect(timeoutInput).toHaveValue('60');
  });

  it('shows user registration settings', () => {
    renderUserSettings();
    
    expect(screen.getByText('User Registration')).toBeInTheDocument();
    expect(screen.getByText('Allow self-registration')).toBeInTheDocument();
    expect(screen.getByText('Email verification required')).toBeInTheDocument();
    expect(screen.getByText('Manager approval required')).toBeInTheDocument();
  });

  it('handles role permission updates', async () => {
    renderUserSettings();
    
    const manageSchedulePermission = screen.getByRole('checkbox', { name: /manage schedule/i });
    fireEvent.click(manageSchedulePermission);
    
    // Should update permission state
    expect(manageSchedulePermission).toBeInTheDocument();
  });

  it('displays consent requirements section', () => {
    renderUserSettings();
    
    expect(screen.getByText('Consent Requirements')).toBeInTheDocument();
    expect(screen.getByText('Level 1 (Required)')).toBeInTheDocument();
    expect(screen.getByText('Level 2 (Optional)')).toBeInTheDocument();
  });

  it('shows account lockout settings', () => {
    renderUserSettings();
    
    expect(screen.getByText('Account Lockout')).toBeInTheDocument();
    expect(screen.getByDisplayValue('5')).toBeInTheDocument(); // Max failed attempts
    expect(screen.getByDisplayValue('15')).toBeInTheDocument(); // Lockout duration
  });

  it('validates password complexity settings', async () => {
    const user = userEvent.setup();
    renderUserSettings();
    
    const minLengthInput = screen.getByDisplayValue('8');
    await user.clear(minLengthInput);
    await user.type(minLengthInput, '4');
    
    // Should show warning for weak password requirements
    expect(screen.getByText(/Minimum 6 characters recommended/)).toBeInTheDocument();
  });

  it('handles user data retention settings', () => {
    renderUserSettings();
    
    expect(screen.getByText('Data Retention')).toBeInTheDocument();
    expect(screen.getByDisplayValue('365')).toBeInTheDocument(); // Days to retain data
    expect(screen.getByText('Auto-delete inactive accounts')).toBeInTheDocument();
  });

  it('shows invitation settings', () => {
    renderUserSettings();
    
    expect(screen.getByText('Invitation Settings')).toBeInTheDocument();
    expect(screen.getByDisplayValue('7')).toBeInTheDocument(); // Invitation expiry days
    expect(screen.getByText('Allow tutor self-invitation')).toBeInTheDocument();
  });

  it('displays audit log settings', () => {
    renderUserSettings();
    
    expect(screen.getByText('Audit Logging')).toBeInTheDocument();
    expect(screen.getByText('Log user actions')).toBeInTheDocument();
    expect(screen.getByText('Log login attempts')).toBeInTheDocument();
    expect(screen.getByText('Log permission changes')).toBeInTheDocument();
  });

  it('handles GDPR compliance settings', () => {
    renderUserSettings();
    
    expect(screen.getByText('GDPR Compliance')).toBeInTheDocument();
    expect(screen.getByText('Enable data download')).toBeInTheDocument();
    expect(screen.getByText('Enable account deletion')).toBeInTheDocument();
    expect(screen.getByText('Data processing consent')).toBeInTheDocument();
  });

  it('saves settings successfully', async () => {
    const user = userEvent.setup();
    renderUserSettings();
    
    // Make a change
    const twoFAToggle = screen.getByRole('checkbox', { name: /require two-factor authentication/i });
    fireEvent.click(twoFAToggle);
    
    const saveButton = screen.getByRole('button', { name: /save changes/i });
    fireEvent.click(saveButton);
    
    await waitFor(() => {
      expect(screen.getByText('Settings saved successfully')).toBeInTheDocument();
    });
  });

  it('shows reset button when changes are made', async () => {
    renderUserSettings();
    
    const saveButton = screen.getByRole('button', { name: /save changes/i });
    const resetButton = screen.getByRole('button', { name: /reset changes/i });
    
    // Initially disabled
    expect(saveButton).toBeDisabled();
    expect(resetButton).toBeDisabled();
    
    // Make a change
    const twoFAToggle = screen.getByRole('checkbox', { name: /require two-factor authentication/i });
    fireEvent.click(twoFAToggle);
    
    // Should now be enabled
    expect(saveButton).not.toBeDisabled();
    expect(resetButton).not.toBeDisabled();
  });

  it('validates numeric input ranges', async () => {
    const user = userEvent.setup();
    renderUserSettings();
    
    const timeoutInput = screen.getByDisplayValue('30');
    await user.clear(timeoutInput);
    await user.type(timeoutInput, '300'); // Too high
    
    // Should constrain to max value
    expect(timeoutInput).toHaveAttribute('max', '180');
  });

  it('shows security warnings for weak settings', async () => {
    renderUserSettings();
    
    const twoFAToggle = screen.getByRole('checkbox', { name: /require two-factor authentication/i });
    fireEvent.click(twoFAToggle); // Disable 2FA
    
    // Should show security warning
    expect(screen.getByText(/Security Warning/)).toBeInTheDocument();
    expect(screen.getByText(/Two-factor authentication is recommended/)).toBeInTheDocument();
  });

  it('handles bulk user operations settings', () => {
    renderUserSettings();
    
    expect(screen.getByText('Bulk Operations')).toBeInTheDocument();
    expect(screen.getByText('Allow bulk user import')).toBeInTheDocument();
    expect(screen.getByText('Allow bulk role changes')).toBeInTheDocument();
    expect(screen.getByText('Require approval for bulk operations')).toBeInTheDocument();
  });

  it('displays default user settings', () => {
    renderUserSettings();
    
    expect(screen.getByText('Default User Settings')).toBeInTheDocument();
    expect(screen.getByDisplayValue('English')).toBeInTheDocument(); // Default language
    expect(screen.getByDisplayValue('America/Montreal')).toBeInTheDocument(); // Default timezone
  });

  it('shows user limit settings', () => {
    renderUserSettings();
    
    expect(screen.getByText('User Limits')).toBeInTheDocument();
    expect(screen.getByDisplayValue('1000')).toBeInTheDocument(); // Max users
    expect(screen.getByDisplayValue('50')).toBeInTheDocument(); // Max tutors
  });

  it('handles guest access settings', () => {
    renderUserSettings();
    
    expect(screen.getByText('Guest Access')).toBeInTheDocument();
    expect(screen.getByText('Allow guest booking')).toBeInTheDocument();
    expect(screen.getByText('Guest session duration')).toBeInTheDocument();
  });

  it('validates email domain restrictions', async () => {
    const user = userEvent.setup();
    renderUserSettings();
    
    const domainInput = screen.getByPlaceholderText('Allowed email domains...');
    await user.type(domainInput, 'example.com, tutoraide.ca');
    
    expect(domainInput).toHaveValue('example.com, tutoraide.ca');
  });

  it('shows active users count', () => {
    renderUserSettings();
    
    expect(screen.getByText('Active Users: 125')).toBeInTheDocument();
    expect(screen.getByText('Active Tutors: 15')).toBeInTheDocument();
    expect(screen.getByText('Active Clients: 98')).toBeInTheDocument();
  });
});