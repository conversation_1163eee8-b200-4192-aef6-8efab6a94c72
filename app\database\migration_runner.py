"""
Database migration runner for TutorAide.
Handles applying and rolling back database schema changes.
"""

import asyncpg
import logging
import os
from pathlib import Path
from typing import List, Dict, Optional
from datetime import datetime

from app.config.database import get_database_pool
from app.core.exceptions import DatabaseOperationError

logger = logging.getLogger(__name__)


class MigrationRunner:
    """Manages database migrations."""
    
    def __init__(self):
        self.migrations_dir = Path(__file__).parent / "migrations"
        self.migration_table = "schema_migrations"
    
    async def initialize_migration_table(self, conn: asyncpg.Connection) -> None:
        """Create the migration tracking table if it doesn't exist."""
        await conn.execute(f"""
            CREATE TABLE IF NOT EXISTS {self.migration_table} (
                migration_id SERIAL PRIMARY KEY,
                migration_name VARCHAR(255) NOT NULL UNIQUE,
                applied_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                rollback_file VARCHAR(255)
            )
        """)
        logger.info("Migration tracking table initialized")
    
    async def get_applied_migrations(self, conn: asyncpg.Connection) -> List[str]:
        """Get list of applied migrations."""
        try:
            rows = await conn.fetch(f"""
                SELECT migration_name 
                FROM {self.migration_table} 
                ORDER BY applied_at
            """)
            return [row['migration_name'] for row in rows]
        except asyncpg.UndefinedTableError:
            return []
    
    def get_available_migrations(self) -> List[Dict[str, str]]:
        """Get list of available migration files."""
        migrations = []
        
        if not self.migrations_dir.exists():
            logger.warning(f"Migrations directory does not exist: {self.migrations_dir}")
            return migrations
        
        for file_path in sorted(self.migrations_dir.glob("*.sql")):
            if file_path.name.endswith("_rollback.sql"):
                continue
            
            # Check for corresponding rollback file
            rollback_file = file_path.stem + "_rollback.sql"
            rollback_path = self.migrations_dir / rollback_file
            
            migrations.append({
                "name": file_path.stem,
                "file": file_path.name,
                "rollback_file": rollback_file if rollback_path.exists() else None,
                "path": str(file_path)
            })
        
        return migrations
    
    async def apply_migration(
        self, 
        conn: asyncpg.Connection, 
        migration: Dict[str, str]
    ) -> None:
        """Apply a single migration."""
        try:
            # Read migration file
            with open(migration["path"], "r", encoding="utf-8") as f:
                sql_content = f.read()
            
            # Execute migration in a transaction
            async with conn.transaction():
                # Apply the migration
                await conn.execute(sql_content)
                
                # Record migration as applied
                await conn.execute(f"""
                    INSERT INTO {self.migration_table} 
                    (migration_name, rollback_file) 
                    VALUES ($1, $2)
                """, migration["name"], migration["rollback_file"])
            
            logger.info(f"Applied migration: {migration['name']}")
            
        except Exception as e:
            logger.error(f"Failed to apply migration {migration['name']}: {e}")
            raise DatabaseOperationError(f"Migration failed: {migration['name']}", operation="apply_migration")
    
    async def rollback_migration(
        self, 
        conn: asyncpg.Connection, 
        migration_name: str
    ) -> None:
        """Rollback a single migration."""
        try:
            # Get migration info
            row = await conn.fetchrow(f"""
                SELECT rollback_file 
                FROM {self.migration_table} 
                WHERE migration_name = $1
            """, migration_name)
            
            if not row:
                raise DatabaseOperationError(f"Migration not found: {migration_name}", operation="rollback_migration")
            
            rollback_file = row['rollback_file']
            if not rollback_file:
                raise DatabaseOperationError(f"No rollback file for migration: {migration_name}", operation="rollback_migration")
            
            rollback_path = self.migrations_dir / rollback_file
            if not rollback_path.exists():
                raise DatabaseOperationError(f"Rollback file not found: {rollback_file}", operation="rollback_migration")
            
            # Read rollback file
            with open(rollback_path, "r", encoding="utf-8") as f:
                sql_content = f.read()
            
            # Execute rollback in a transaction
            async with conn.transaction():
                # Apply the rollback
                await conn.execute(sql_content)
                
                # Remove migration record
                await conn.execute(f"""
                    DELETE FROM {self.migration_table} 
                    WHERE migration_name = $1
                """, migration_name)
            
            logger.info(f"Rolled back migration: {migration_name}")
            
        except Exception as e:
            logger.error(f"Failed to rollback migration {migration_name}: {e}")
            raise DatabaseOperationError(f"Rollback failed: {migration_name}", operation="rollback_migration")
    
    async def migrate_up(self, target_migration: Optional[str] = None) -> None:
        """Apply all pending migrations up to target."""
        pool = await get_database_pool()
        
        async with pool.acquire() as conn:
            # Initialize migration table
            await self.initialize_migration_table(conn)
            
            # Get current state
            applied = await self.get_applied_migrations(conn)
            available = self.get_available_migrations()
            
            # Filter pending migrations
            pending = [m for m in available if m["name"] not in applied]
            
            if target_migration:
                # Find target index
                target_index = None
                for i, migration in enumerate(pending):
                    if migration["name"] == target_migration:
                        target_index = i + 1
                        break
                
                if target_index is None:
                    raise DatabaseOperationError(f"Target migration not found: {target_migration}", operation="migrate_up")
                
                pending = pending[:target_index]
            
            if not pending:
                logger.info("No pending migrations to apply")
                return
            
            logger.info(f"Applying {len(pending)} migrations...")
            
            for migration in pending:
                await self.apply_migration(conn, migration)
            
            logger.info("All migrations applied successfully")
    
    async def migrate_down(self, steps: int = 1) -> None:
        """Rollback the last N migrations."""
        pool = await get_database_pool()
        
        async with pool.acquire() as conn:
            # Get applied migrations in reverse order
            applied = await self.get_applied_migrations(conn)
            applied.reverse()
            
            if len(applied) < steps:
                raise DatabaseOperationError(f"Cannot rollback {steps} migrations, only {len(applied)} applied", operation="migrate_down")
            
            to_rollback = applied[:steps]
            
            logger.info(f"Rolling back {len(to_rollback)} migrations...")
            
            for migration_name in to_rollback:
                await self.rollback_migration(conn, migration_name)
            
            logger.info("Rollback completed successfully")
    
    async def get_migration_status(self) -> Dict[str, any]:
        """Get current migration status."""
        try:
            pool = await get_database_pool()
            
            async with pool.acquire() as conn:
                # Initialize migration table if needed
                await self.initialize_migration_table(conn)
                
                applied = await self.get_applied_migrations(conn)
                available = self.get_available_migrations()
                
                pending = [m["name"] for m in available if m["name"] not in applied]
                
                return {
                    "applied_count": len(applied),
                    "pending_count": len(pending),
                    "total_count": len(available),
                    "applied_migrations": applied,
                    "pending_migrations": pending,
                    "last_applied": applied[-1] if applied else None
                }
        except Exception as e:
            logger.error(f"Failed to get migration status: {e}")
            raise DatabaseOperationError("Failed to get migration status", operation="get_migration_status")
    
    async def reset_database(self) -> None:
        """Reset database by rolling back all migrations."""
        logger.warning("Resetting database - this will destroy all data!")
        
        pool = await get_database_pool()
        
        async with pool.acquire() as conn:
            try:
                applied = await self.get_applied_migrations(conn)
                applied.reverse()
                
                for migration_name in applied:
                    await self.rollback_migration(conn, migration_name)
                
                # Drop migration table
                await conn.execute(f"DROP TABLE IF EXISTS {self.migration_table}")
                
                logger.info("Database reset completed")
                
            except asyncpg.UndefinedTableError:
                logger.info("Database already clean - no migrations to rollback")


# Convenience functions
async def migrate_up(target: Optional[str] = None) -> None:
    """Apply pending migrations."""
    runner = MigrationRunner()
    await runner.migrate_up(target)


async def migrate_down(steps: int = 1) -> None:
    """Rollback migrations."""
    runner = MigrationRunner()
    await runner.migrate_down(steps)


async def migration_status() -> Dict[str, any]:
    """Get migration status."""
    runner = MigrationRunner()
    return await runner.get_migration_status()


async def reset_database() -> None:
    """Reset database (WARNING: destroys all data)."""
    runner = MigrationRunner()
    await runner.reset_database()