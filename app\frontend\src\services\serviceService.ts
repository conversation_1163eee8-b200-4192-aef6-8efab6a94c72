/**
 * Service catalog and pricing service
 */

import api from './api';

export enum ServiceType {
  ONLINE = 'online',
  IN_PERSON = 'in_person',
  LIBRARY = 'library',
  HYBRID = 'hybrid'
}

export enum SubjectArea {
  MATH = 'math',
  SCIENCE = 'science',
  FRENCH = 'french',
  ENGLISH = 'english',
  OTHERS = 'others'
}

export interface ServiceRate {
  rate_id: number;
  tutor_id: number;
  service_id: number;
  tutor_name: string;
  base_rate: number;
  client_rate: number;
  platform_fee: number;
  currency: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Service {
  service_id: number;
  name: string;
  description?: string;
  subject_area: SubjectArea;
  service_type: ServiceType;
  grade_levels?: string[];
  duration_options: number[]; // in minutes
  frequency_options?: string[];
  is_active: boolean;
  requires_location: boolean;
  max_students: number;
  min_students: number;
  default_client_rate?: number;
  default_tutor_rate?: number;
  tags?: string[];
  created_at: string;
  updated_at: string;
}

export interface ServicePackage {
  package_id: number;
  name: string;
  description?: string;
  service_id: number;
  service_name: string;
  session_count: number;
  total_hours: number;
  price: number;
  discount_percentage?: number;
  valid_days: number;
  is_active: boolean;
  is_tecfee: boolean;
  features?: string[];
  created_at: string;
  updated_at: string;
}

export interface TutorService {
  tutor_service_id: number;
  tutor_id: number;
  service_id: number;
  tutor_name: string;
  service_name: string;
  hourly_rate: number;
  currency: string;
  is_available: boolean;
  experience_level?: string;
  certifications?: string[];
  specializations?: string[];
  max_distance_km?: number;
  online_platform_preferences?: string[];
  created_at: string;
  updated_at: string;
}

export interface ServiceSearchParams {
  subject_area?: SubjectArea;
  service_type?: ServiceType;
  grade_level?: string;
  search?: string;
  is_active?: boolean;
  page?: number;
  limit?: number;
}

export interface ServiceListResponse {
  items: Service[];
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
}

export interface ServiceCreateRequest {
  name: string;
  description?: string;
  subject_area: SubjectArea;
  service_type: ServiceType;
  grade_levels?: string[];
  duration_options: number[];
  frequency_options?: string[];
  requires_location: boolean;
  max_students: number;
  min_students: number;
  default_client_rate?: number;
  default_tutor_rate?: number;
  tags?: string[];
}

export interface ServiceUpdateRequest {
  name?: string;
  description?: string;
  subject_area?: SubjectArea;
  service_type?: ServiceType;
  grade_levels?: string[];
  duration_options?: number[];
  frequency_options?: string[];
  is_active?: boolean;
  requires_location?: boolean;
  max_students?: number;
  min_students?: number;
  default_client_rate?: number;
  default_tutor_rate?: number;
  tags?: string[];
}

export interface TutorServiceRequest {
  service_id: number;
  hourly_rate: number;
  is_available?: boolean;
  experience_level?: string;
  certifications?: string[];
  specializations?: string[];
  max_distance_km?: number;
  online_platform_preferences?: string[];
}

export const serviceService = {
  // Get all services with filters
  async getServices(params?: ServiceSearchParams): Promise<ServiceListResponse> {
    const response = await api.get<ServiceListResponse>('/services', { params });
    return response.data;
  },

  // Get service by ID
  async getService(serviceId: number): Promise<Service> {
    const response = await api.get<Service>(`/services/${serviceId}`);
    return response.data;
  },

  // Create new service (manager only)
  async createService(data: ServiceCreateRequest): Promise<Service> {
    const response = await api.post<Service>('/services', data);
    return response.data;
  },

  // Update service (manager only)
  async updateService(serviceId: number, data: ServiceUpdateRequest): Promise<Service> {
    const response = await api.put<Service>(`/services/${serviceId}`, data);
    return response.data;
  },

  // Delete service (manager only)
  async deleteService(serviceId: number): Promise<void> {
    await api.delete(`/services/${serviceId}`);
  },

  // Get service rates
  async getServiceRates(serviceId: number): Promise<ServiceRate[]> {
    const response = await api.get<ServiceRate[]>(`/services/${serviceId}/rates`);
    return response.data;
  },

  // Update service rate (manager only)
  async updateServiceRate(serviceId: number, tutorId: number, data: {
    base_rate: number;
    client_rate: number;
  }): Promise<ServiceRate> {
    const response = await api.put<ServiceRate>(`/services/${serviceId}/rates/${tutorId}`, data);
    return response.data;
  },

  // Get services by subject area
  async getServicesBySubject(subjectArea: SubjectArea): Promise<Service[]> {
    const response = await api.get<Service[]>(`/services/subject/${subjectArea}`);
    return response.data;
  },

  // Get tutor services
  async getTutorServices(tutorId: number): Promise<TutorService[]> {
    const response = await api.get<TutorService[]>(`/tutors/${tutorId}/services`);
    return response.data;
  },

  // Add service to tutor
  async addTutorService(tutorId: number, data: TutorServiceRequest): Promise<TutorService> {
    const response = await api.post<TutorService>(`/tutors/${tutorId}/services`, data);
    return response.data;
  },

  // Update tutor service
  async updateTutorService(tutorId: number, serviceId: number, data: Partial<TutorServiceRequest>): Promise<TutorService> {
    const response = await api.put<TutorService>(`/tutors/${tutorId}/services/${serviceId}`, data);
    return response.data;
  },

  // Remove service from tutor
  async removeTutorService(tutorId: number, serviceId: number): Promise<void> {
    await api.delete(`/tutors/${tutorId}/services/${serviceId}`);
  },

  // Get service packages
  async getServicePackages(serviceId?: number): Promise<ServicePackage[]> {
    const params = serviceId ? { service_id: serviceId } : undefined;
    const response = await api.get<ServicePackage[]>('/service-packages', { params });
    return response.data;
  },

  // Get package by ID
  async getPackage(packageId: number): Promise<ServicePackage> {
    const response = await api.get<ServicePackage>(`/service-packages/${packageId}`);
    return response.data;
  },

  // Create service package (manager only)
  async createPackage(data: Omit<ServicePackage, 'package_id' | 'created_at' | 'updated_at' | 'service_name'>): Promise<ServicePackage> {
    const response = await api.post<ServicePackage>('/service-packages', data);
    return response.data;
  },

  // Update service package (manager only)
  async updatePackage(packageId: number, data: Partial<Omit<ServicePackage, 'package_id' | 'created_at' | 'updated_at' | 'service_name'>>): Promise<ServicePackage> {
    const response = await api.put<ServicePackage>(`/service-packages/${packageId}`, data);
    return response.data;
  },

  // Delete service package (manager only)
  async deletePackage(packageId: number): Promise<void> {
    await api.delete(`/service-packages/${packageId}`);
  },

  // Get TECFEE packages
  async getTecfeePackages(): Promise<ServicePackage[]> {
    const response = await api.get<ServicePackage[]>('/service-packages/tecfee');
    return response.data;
  },

  // Find tutors for service
  async findTutorsForService(serviceId: number, params?: {
    location?: { lat: number; lng: number };
    max_distance_km?: number;
    availability_date?: string;
    min_rating?: number;
  }): Promise<{
    items: TutorService[];
    total: number;
  }> {
    const response = await api.get(`/services/${serviceId}/find-tutors`, { params });
    return response.data;
  },

  // Get service statistics (manager only)
  async getServiceStats(): Promise<{
    total_services: number;
    active_services: number;
    services_by_type: Record<ServiceType, number>;
    services_by_subject: Record<SubjectArea, number>;
    popular_services: Array<{
      service_name: string;
      booking_count: number;
      revenue: number;
    }>;
    average_rates: {
      client_rate: number;
      tutor_rate: number;
      platform_fee_percentage: number;
    };
  }> {
    const response = await api.get('/services/stats');
    return response.data;
  },

  // Get recommended services for client
  async getRecommendedServices(clientId: number): Promise<Service[]> {
    const response = await api.get<Service[]>(`/clients/${clientId}/recommended-services`);
    return response.data;
  },

  // Bulk update service rates (manager only)
  async bulkUpdateRates(updates: Array<{
    service_id: number;
    percentage_increase?: number;
    fixed_increase?: number;
  }>): Promise<{
    updated: number;
    failed: number;
    errors?: string[];
  }> {
    const response = await api.post('/services/bulk-update-rates', { updates });
    return response.data;
  }
};