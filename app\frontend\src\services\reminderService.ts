import api from './api';

// Reminder Types and Interfaces
export enum ReminderType {
  APPOINTMENT_24H = 'appointment_24h',
  APPOINTMENT_1H = 'appointment_1h',
  PAYMENT_DUE = 'payment_due',
  PAYMENT_OVERDUE = 'payment_overdue',
  DOCUMENT_EXPIRY = 'document_expiry',
  SCHEDULE_CONFIRMATION = 'schedule_confirmation',
  SESSION_COMPLETION = 'session_completion',
  CUSTOM = 'custom'
}

export enum ReminderStatus {
  SCHEDULED = 'scheduled',
  SENT = 'sent',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  DELIVERED = 'delivered',
  READ = 'read'
}

export enum ReminderChannel {
  SMS = 'sms',
  EMAIL = 'email',
  PUSH = 'push',
  IN_APP = 'in_app'
}

export interface Reminder {
  reminder_id: number;
  type: ReminderType;
  status: ReminderStatus;
  scheduled_for: string;
  sent_at?: string;
  delivered_at?: string;
  read_at?: string;
  recipient_user_id: number;
  recipient_name: string;
  recipient_contact: string;
  channel: ReminderChannel;
  subject?: string;
  message: string;
  metadata?: {
    appointment_id?: number;
    invoice_id?: number;
    payment_id?: number;
    custom_data?: Record<string, any>;
  };
  retry_count: number;
  error_message?: string;
  created_at: string;
  updated_at: string;
}

export interface ScheduleReminderRequest {
  type: ReminderType;
  scheduled_for: string;
  recipient_user_id: number;
  channel: ReminderChannel;
  subject?: string;
  message?: string;
  metadata?: Record<string, any>;
  custom_template?: string;
}

export interface BulkScheduleRequest {
  type: ReminderType;
  scheduled_for: string;
  recipients: Array<{
    user_id: number;
    channel: ReminderChannel;
    custom_data?: Record<string, any>;
  }>;
  subject?: string;
  message?: string;
}

export interface ReminderTemplate {
  template_id: number;
  type: ReminderType;
  name: string;
  subject_template: string;
  message_template: string;
  channel: ReminderChannel;
  language: 'en' | 'fr';
  variables: string[];
  is_active: boolean;
  preview?: string;
}

export interface ReminderSettings {
  user_id: number;
  reminder_preferences: Array<{
    type: ReminderType;
    enabled: boolean;
    channels: ReminderChannel[];
    advance_notice_hours?: number;
  }>;
  quiet_hours: {
    enabled: boolean;
    start_time: string;
    end_time: string;
    timezone: string;
  };
  language_preference: 'en' | 'fr';
}

export interface ReminderStats {
  total_scheduled: number;
  total_sent: number;
  total_delivered: number;
  total_failed: number;
  by_type: Record<ReminderType, number>;
  by_channel: Record<ReminderChannel, number>;
  delivery_rate: number;
  read_rate: number;
  recent_activity: Array<{
    date: string;
    sent: number;
    delivered: number;
    failed: number;
  }>;
}

export interface AppointmentRemindersRequest {
  appointment_id: number;
  appointment_datetime: string;
  tutor_id: number;
  client_id: number;
  dependant_id?: number;
}

export interface ReminderListParams {
  status?: ReminderStatus;
  type?: ReminderType;
  channel?: ReminderChannel;
  recipient_user_id?: number;
  date_from?: string;
  date_to?: string;
  limit?: number;
  offset?: number;
}

class ReminderService {
  /**
   * Schedule a single reminder
   */
  async scheduleReminder(request: ScheduleReminderRequest): Promise<Reminder> {
    try {
      const response = await api.post<Reminder>('/automated-reminders/schedule', request);
      return response.data;
    } catch (error: any) {
      console.error('Error scheduling reminder:', error);
      throw new Error(error.response?.data?.detail || 'Failed to schedule reminder');
    }
  }

  /**
   * Schedule reminders for multiple recipients
   */
  async scheduleBulkReminders(request: BulkScheduleRequest): Promise<{
    scheduled: Reminder[];
    failed: Array<{ user_id: number; error: string }>;
  }> {
    try {
      const response = await api.post<{
        scheduled: Reminder[];
        failed: Array<{ user_id: number; error: string }>;
      }>('/automated-reminders/schedule-bulk', request);
      return response.data;
    } catch (error: any) {
      console.error('Error scheduling bulk reminders:', error);
      throw new Error(error.response?.data?.detail || 'Failed to schedule reminders');
    }
  }

  /**
   * Schedule all reminders for an appointment
   */
  async scheduleAppointmentReminders(request: AppointmentRemindersRequest): Promise<{
    reminders_scheduled: number;
    reminder_ids: number[];
  }> {
    try {
      const response = await api.post<{
        reminders_scheduled: number;
        reminder_ids: number[];
      }>(`/automated-reminders/appointments/${request.appointment_id}/schedule`, request);
      return response.data;
    } catch (error: any) {
      console.error('Error scheduling appointment reminders:', error);
      throw new Error(error.response?.data?.detail || 'Failed to schedule appointment reminders');
    }
  }

  /**
   * Get reminders list
   */
  async getReminders(params?: ReminderListParams): Promise<{
    reminders: Reminder[];
    total: number;
    limit: number;
    offset: number;
  }> {
    try {
      const response = await api.get<{
        reminders: Reminder[];
        total: number;
        limit: number;
        offset: number;
      }>('/automated-reminders', { params });
      return response.data;
    } catch (error: any) {
      console.error('Error fetching reminders:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch reminders');
    }
  }

  /**
   * Get specific reminder
   */
  async getReminder(reminderId: number): Promise<Reminder> {
    try {
      const response = await api.get<Reminder>(`/automated-reminders/${reminderId}`);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching reminder:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch reminder');
    }
  }

  /**
   * Cancel a reminder
   */
  async cancelReminder(reminderId: number): Promise<{ message: string }> {
    try {
      const response = await api.post<{ message: string }>(`/automated-reminders/${reminderId}/cancel`);
      return response.data;
    } catch (error: any) {
      console.error('Error cancelling reminder:', error);
      throw new Error(error.response?.data?.detail || 'Failed to cancel reminder');
    }
  }

  /**
   * Retry failed reminder
   */
  async retryReminder(reminderId: number): Promise<Reminder> {
    try {
      const response = await api.post<Reminder>(`/automated-reminders/${reminderId}/retry`);
      return response.data;
    } catch (error: any) {
      console.error('Error retrying reminder:', error);
      throw new Error(error.response?.data?.detail || 'Failed to retry reminder');
    }
  }

  /**
   * Mark reminder as read
   */
  async markAsRead(reminderId: number): Promise<Reminder> {
    try {
      const response = await api.post<Reminder>(`/automated-reminders/${reminderId}/read`);
      return response.data;
    } catch (error: any) {
      console.error('Error marking reminder as read:', error);
      throw new Error(error.response?.data?.detail || 'Failed to mark as read');
    }
  }

  /**
   * Get reminder templates
   */
  async getTemplates(type?: ReminderType, language?: 'en' | 'fr'): Promise<ReminderTemplate[]> {
    try {
      const params = { type, language };
      const response = await api.get<ReminderTemplate[]>('/automated-reminders/templates', { params });
      return response.data;
    } catch (error: any) {
      console.error('Error fetching templates:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch templates');
    }
  }

  /**
   * Create custom reminder template
   */
  async createTemplate(template: Omit<ReminderTemplate, 'template_id'>): Promise<ReminderTemplate> {
    try {
      const response = await api.post<ReminderTemplate>('/automated-reminders/templates', template);
      return response.data;
    } catch (error: any) {
      console.error('Error creating template:', error);
      throw new Error(error.response?.data?.detail || 'Failed to create template');
    }
  }

  /**
   * Get user reminder settings
   */
  async getSettings(userId?: number): Promise<ReminderSettings> {
    try {
      const params = userId ? { user_id: userId } : {};
      const response = await api.get<ReminderSettings>('/automated-reminders/settings', { params });
      return response.data;
    } catch (error: any) {
      console.error('Error fetching reminder settings:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch settings');
    }
  }

  /**
   * Update user reminder settings
   */
  async updateSettings(settings: Partial<ReminderSettings>): Promise<ReminderSettings> {
    try {
      const response = await api.put<ReminderSettings>('/automated-reminders/settings', settings);
      return response.data;
    } catch (error: any) {
      console.error('Error updating reminder settings:', error);
      throw new Error(error.response?.data?.detail || 'Failed to update settings');
    }
  }

  /**
   * Get reminder statistics
   */
  async getStats(dateFrom?: string, dateTo?: string): Promise<ReminderStats> {
    try {
      const params = { date_from: dateFrom, date_to: dateTo };
      const response = await api.get<ReminderStats>('/automated-reminders/stats', { params });
      return response.data;
    } catch (error: any) {
      console.error('Error fetching reminder stats:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch stats');
    }
  }

  /**
   * Preview reminder message
   */
  async previewReminder(request: {
    type: ReminderType;
    template_id?: number;
    variables?: Record<string, any>;
    language?: 'en' | 'fr';
  }): Promise<{
    subject: string;
    message: string;
    channel: ReminderChannel;
  }> {
    try {
      const response = await api.post<{
        subject: string;
        message: string;
        channel: ReminderChannel;
      }>('/automated-reminders/preview', request);
      return response.data;
    } catch (error: any) {
      console.error('Error previewing reminder:', error);
      throw new Error(error.response?.data?.detail || 'Failed to preview reminder');
    }
  }

  /**
   * Format reminder type for display
   */
  formatReminderType(type: ReminderType): string {
    const labels: Record<ReminderType, string> = {
      [ReminderType.APPOINTMENT_24H]: '24-Hour Appointment Reminder',
      [ReminderType.APPOINTMENT_1H]: '1-Hour Appointment Reminder',
      [ReminderType.PAYMENT_DUE]: 'Payment Due Reminder',
      [ReminderType.PAYMENT_OVERDUE]: 'Overdue Payment Reminder',
      [ReminderType.DOCUMENT_EXPIRY]: 'Document Expiry Reminder',
      [ReminderType.SCHEDULE_CONFIRMATION]: 'Schedule Confirmation',
      [ReminderType.SESSION_COMPLETION]: 'Session Completion Reminder',
      [ReminderType.CUSTOM]: 'Custom Reminder'
    };

    return labels[type] || type;
  }

  /**
   * Get reminder icon
   */
  getReminderIcon(type: ReminderType): string {
    const icons: Record<ReminderType, string> = {
      [ReminderType.APPOINTMENT_24H]: 'Clock',
      [ReminderType.APPOINTMENT_1H]: 'AlertCircle',
      [ReminderType.PAYMENT_DUE]: 'DollarSign',
      [ReminderType.PAYMENT_OVERDUE]: 'AlertTriangle',
      [ReminderType.DOCUMENT_EXPIRY]: 'FileAlert',
      [ReminderType.SCHEDULE_CONFIRMATION]: 'Calendar',
      [ReminderType.SESSION_COMPLETION]: 'CheckCircle',
      [ReminderType.CUSTOM]: 'Bell'
    };

    return icons[type] || 'Bell';
  }

  /**
   * Calculate reminder time
   */
  calculateReminderTime(
    eventTime: Date | string, 
    advanceNotice: { value: number; unit: 'minutes' | 'hours' | 'days' }
  ): Date {
    const event = typeof eventTime === 'string' ? new Date(eventTime) : eventTime;
    const reminder = new Date(event);

    switch (advanceNotice.unit) {
      case 'minutes':
        reminder.setMinutes(reminder.getMinutes() - advanceNotice.value);
        break;
      case 'hours':
        reminder.setHours(reminder.getHours() - advanceNotice.value);
        break;
      case 'days':
        reminder.setDate(reminder.getDate() - advanceNotice.value);
        break;
    }

    return reminder;
  }

  /**
   * Check if reminder should be sent now
   */
  shouldSendNow(reminder: Reminder, settings: ReminderSettings): boolean {
    if (!settings.quiet_hours.enabled) {
      return true;
    }

    const now = new Date();
    const currentHour = now.getHours();
    const startHour = parseInt(settings.quiet_hours.start_time.split(':')[0]);
    const endHour = parseInt(settings.quiet_hours.end_time.split(':')[0]);

    // Check if current time is within quiet hours
    if (startHour < endHour) {
      // Quiet hours don't cross midnight
      return currentHour < startHour || currentHour >= endHour;
    } else {
      // Quiet hours cross midnight
      return currentHour < startHour && currentHour >= endHour;
    }
  }
}

export const reminderService = new ReminderService();