"""
Service catalog and rate management business logic.
"""

import logging
from datetime import datetime, date, timedelta
from decimal import Decimal
from typing import List, Optional, Dict, Any, Tuple
import asyncpg

from app.database.repositories.service_repository import (
    ServiceCatalogRepository, TutorServiceRateRepository,
    ServicePackageRepository, LocationPreferenceRepository
)
from app.models.service_models import (
    SubjectArea, ServiceType, ServiceLevel, PackageType, PricingTier,
    CreateServiceCatalogRequest, UpdateServiceCatalogRequest, ServiceCatalogResponse,
    CreateTutorServiceRateRequest, UpdateTutorServiceRateRequest, TutorServiceRateResponse,
    CreateServicePackageRequest, UpdateServicePackageRequest, ServicePackageResponse,
    ServiceSearchRequest, ServiceRecommendationRequest,
    PricingCalculationRequest, PricingCalculationResponse,
    LocationPreference, DistanceCalculationRequest, DistanceCalculationResponse,
    TutorServiceSummary, ServiceCatalogSummary
)
from app.core.exceptions import ValidationError, ResourceNotFoundError, BusinessLogicError
from app.core.timezone import now_est
from app.services.geocoding_service import GeocodingService

logger = logging.getLogger(__name__)


class ServiceCatalogService:
    """Service for managing the service catalog."""
    
    def __init__(self, db_connection: asyncpg.Connection):
        self.db = db_connection
        self.catalog_repo = ServiceCatalogRepository(db_connection)
        self.rate_repo = TutorServiceRateRepository(db_connection)
        self.package_repo = ServicePackageRepository(db_connection)
        self.location_repo = LocationPreferenceRepository(db_connection)
    
    async def create_service(
        self,
        request: CreateServiceCatalogRequest,
        created_by: int
    ) -> ServiceCatalogResponse:
        """Create a new service in the catalog."""
        try:
            # Validate service configuration
            self._validate_service_configuration(request)
            
            # Check for duplicate services
            existing_services, _ = await self.catalog_repo.get_services_by_filters(
                subject_area=request.subject_area,
                service_type=request.service_type,
                service_level=request.service_level
            )
            
            if existing_services:
                similar_service = next((s for s in existing_services if s.service_name == request.service_name), None)
                if similar_service:
                    raise ValidationError(f"Service '{request.service_name}' already exists for this configuration")
            
            # Create service
            service_data = request.model_dump()
            service = await self.catalog_repo.create_service(service_data, created_by)
            
            logger.info(f"Created service {service.service_catalog_id}: {service.service_name}")
            return service
            
        except Exception as e:
            logger.error(f"Error creating service: {e}")
            raise
    
    async def update_service(
        self,
        service_catalog_id: int,
        request: UpdateServiceCatalogRequest,
        updated_by: int
    ) -> ServiceCatalogResponse:
        """Update a service in the catalog."""
        try:
            # Get existing service
            existing_service = await self.catalog_repo.get_by_id(service_catalog_id)
            if not existing_service:
                raise ResourceNotFoundError(f"Service {service_catalog_id} not found")
            
            # Validate updates
            updates = request.model_dump(exclude_unset=True)
            if updates:
                # Validate duration changes
                if 'min_duration_minutes' in updates or 'max_duration_minutes' in updates:
                    min_duration = updates.get('min_duration_minutes', existing_service.min_duration_minutes)
                    max_duration = updates.get('max_duration_minutes', existing_service.max_duration_minutes)
                    
                    if min_duration > max_duration:
                        raise ValidationError("Minimum duration cannot exceed maximum duration")
                
                service = await self.catalog_repo.update_service(service_catalog_id, updates, updated_by)
                logger.info(f"Updated service {service_catalog_id}")
                return service
            
            return existing_service
            
        except Exception as e:
            logger.error(f"Error updating service: {e}")
            raise
    
    async def search_services(
        self,
        request: ServiceSearchRequest
    ) -> Tuple[List[ServiceCatalogResponse], int]:
        """Search services based on filters."""
        try:
            services, total = await self.catalog_repo.get_services_by_filters(
                subject_area=request.subject_area,
                service_type=request.service_type,
                service_level=request.service_level,
                is_active_only=request.is_active_only,
                page=request.page,
                page_size=request.page_size
            )
            
            # If tutor_id specified, filter by tutor's offerings
            if request.tutor_id:
                tutor_rates = await self.rate_repo.get_tutor_rates(
                    request.tutor_id,
                    is_available_only=request.is_active_only
                )
                
                tutor_service_ids = {rate.service_catalog_id for rate in tutor_rates}
                services = [s for s in services if s.service_catalog_id in tutor_service_ids]
                total = len(services)
            
            # Apply rate filtering if specified
            if request.max_hourly_rate and request.tutor_id:
                filtered_services = []
                for service in services:
                    rates = [r for r in tutor_rates if r.service_catalog_id == service.service_catalog_id]
                    if any(r.hourly_rate <= request.max_hourly_rate for r in rates):
                        filtered_services.append(service)
                
                services = filtered_services
                total = len(services)
            
            return services, total
            
        except Exception as e:
            logger.error(f"Error searching services: {e}")
            raise
    
    async def get_catalog_summary(self) -> ServiceCatalogSummary:
        """Get summary statistics for the service catalog."""
        try:
            # Get all services
            all_services, total = await self.catalog_repo.get_services_by_filters(
                is_active_only=False,
                page_size=1000  # Get all
            )
            
            active_services = [s for s in all_services if s.is_active]
            
            # Count by categories
            services_by_subject = {}
            services_by_type = {}
            services_by_level = {}
            
            for service in all_services:
                services_by_subject[service.subject_area] = services_by_subject.get(service.subject_area, 0) + 1
                services_by_type[service.service_type] = services_by_type.get(service.service_type, 0) + 1
                services_by_level[service.service_level] = services_by_level.get(service.service_level, 0) + 1
            
            # Get pricing summary (would need actual rate data)
            # For now, return mock data
            return ServiceCatalogSummary(
                total_services=len(all_services),
                active_services=len(active_services),
                services_by_subject=services_by_subject,
                services_by_type=services_by_type,
                services_by_level=services_by_level,
                average_hourly_rate=Decimal("45.00"),
                min_hourly_rate=Decimal("25.00"),
                max_hourly_rate=Decimal("150.00"),
                most_booked_services=[],
                highest_rated_services=[]
            )
            
        except Exception as e:
            logger.error(f"Error getting catalog summary: {e}")
            raise
    
    def _validate_service_configuration(self, request: CreateServiceCatalogRequest):
        """Validate service configuration rules."""
        # Validate service type compatibility
        if request.service_type == ServiceType.ONLINE:
            # Online services can be offered at any level
            pass
        elif request.service_type == ServiceType.LIBRARY:
            # Library services typically for students
            if request.service_level == ServiceLevel.ADULT:
                logger.warning("Library service configured for adult level - unusual configuration")
        
        # Validate subject-level combinations
        if request.subject_area in [SubjectArea.MATHEMATICS, SubjectArea.SCIENCE]:
            # STEM subjects available at all levels
            pass
        elif request.subject_area == SubjectArea.FRENCH:
            # French is mandatory in Quebec
            if request.service_level == ServiceLevel.ELEMENTARY and request.service_type != ServiceType.ONLINE:
                logger.info("French elementary service - consider TECFEE program package")
        
        # Validate duration ranges by level
        if request.service_level == ServiceLevel.ELEMENTARY:
            if request.max_duration_minutes > 90:
                logger.warning("Elementary service with >90 min max duration - may be too long for young students")
        elif request.service_level in [ServiceLevel.UNIVERSITY, ServiceLevel.ADULT]:
            if request.max_duration_minutes < 60:
                logger.warning("University/Adult service with <60 min max duration - may be too short")


class TutorServiceRateService:
    """Service for managing tutor service rates."""
    
    def __init__(self, db_connection: asyncpg.Connection):
        self.db = db_connection
        self.rate_repo = TutorServiceRateRepository(db_connection)
        self.catalog_repo = ServiceCatalogRepository(db_connection)
    
    async def create_tutor_rate(
        self,
        request: CreateTutorServiceRateRequest,
        created_by: int
    ) -> TutorServiceRateResponse:
        """Create a new tutor service rate."""
        try:
            # Validate service exists
            service = await self.catalog_repo.get_by_id(request.service_catalog_id)
            if not service:
                raise ResourceNotFoundError(f"Service {request.service_catalog_id} not found")
            
            # Validate rate configuration
            self._validate_rate_configuration(request, service)
            
            # Create rate
            rate_data = request.model_dump()
            rate = await self.rate_repo.create_tutor_rate(rate_data, created_by)
            
            logger.info(f"Created tutor rate {rate.tutor_service_rate_id} for tutor {rate.tutor_id}")
            return rate
            
        except Exception as e:
            logger.error(f"Error creating tutor rate: {e}")
            raise
    
    async def update_tutor_rate(
        self,
        tutor_service_rate_id: int,
        request: UpdateTutorServiceRateRequest,
        updated_by: int
    ) -> TutorServiceRateResponse:
        """Update a tutor service rate."""
        try:
            # Get existing rate
            existing_rate = await self.rate_repo.get_tutor_rate_with_service(tutor_service_rate_id)
            if not existing_rate:
                raise ResourceNotFoundError(f"Tutor service rate {tutor_service_rate_id} not found")
            
            # Validate updates
            updates = request.model_dump(exclude_unset=True)
            
            if 'hourly_rate' in updates:
                self._validate_hourly_rate(updates['hourly_rate'], existing_rate.pricing_tier)
            
            if updates:
                # Apply updates
                rate = await self.rate_repo.update(tutor_service_rate_id, updates, updated_by)
                logger.info(f"Updated tutor rate {tutor_service_rate_id}")
                
                # Reload with service details
                return await self.rate_repo.get_tutor_rate_with_service(tutor_service_rate_id)
            
            return existing_rate
            
        except Exception as e:
            logger.error(f"Error updating tutor rate: {e}")
            raise
    
    async def get_tutor_services(
        self,
        tutor_id: int,
        service_type: Optional[ServiceType] = None,
        is_available_only: bool = True
    ) -> List[TutorServiceRateResponse]:
        """Get all services offered by a tutor."""
        try:
            rates = await self.rate_repo.get_tutor_rates(
                tutor_id,
                service_type,
                is_available_only
            )
            
            return rates
            
        except Exception as e:
            logger.error(f"Error getting tutor services: {e}")
            raise
    
    async def get_tutor_service_summary(self, tutor_id: int) -> TutorServiceSummary:
        """Get summary of all services offered by a tutor."""
        try:
            # Get all rates for tutor
            all_rates = await self.rate_repo.get_tutor_rates(tutor_id, is_available_only=False)
            
            if not all_rates:
                return TutorServiceSummary(
                    tutor_id=tutor_id,
                    total_services=0,
                    active_services=0,
                    min_hourly_rate=Decimal("0"),
                    max_hourly_rate=Decimal("0"),
                    average_hourly_rate=Decimal("0"),
                    services_by_type={},
                    services_by_subject={},
                    available_service_types=[]
                )
            
            active_rates = [r for r in all_rates if r.is_available]
            
            # Calculate statistics
            hourly_rates = [r.hourly_rate for r in all_rates]
            services_by_type = {}
            services_by_subject = {}
            available_types = set()
            
            for rate in all_rates:
                # Count by type
                services_by_type[rate.service_type] = services_by_type.get(rate.service_type, 0) + 1
                
                # Count by subject (from service catalog)
                if rate.service_catalog:
                    subject = rate.service_catalog.subject_area
                    services_by_subject[subject] = services_by_subject.get(subject, 0) + 1
                
                # Track available types
                if rate.is_available:
                    available_types.add(rate.service_type)
            
            # Find max distance for in-person services
            max_distances = [r.max_distance_km for r in all_rates 
                           if r.max_distance_km and r.service_type in [ServiceType.IN_PERSON, ServiceType.LIBRARY]]
            max_distance = max(max_distances) if max_distances else None
            
            return TutorServiceSummary(
                tutor_id=tutor_id,
                total_services=len(all_rates),
                active_services=len(active_rates),
                min_hourly_rate=min(hourly_rates),
                max_hourly_rate=max(hourly_rates),
                average_hourly_rate=sum(hourly_rates) / len(hourly_rates),
                services_by_type=services_by_type,
                services_by_subject=services_by_subject,
                max_service_distance_km=max_distance,
                available_service_types=list(available_types)
            )
            
        except Exception as e:
            logger.error(f"Error getting tutor service summary: {e}")
            raise
    
    async def find_available_tutors(
        self,
        service_catalog_id: int,
        service_type: ServiceType,
        max_hourly_rate: Optional[Decimal] = None,
        location_postal_code: Optional[str] = None,
        max_distance_km: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """Find available tutors for a specific service."""
        try:
            tutors = await self.rate_repo.find_available_tutors(
                service_catalog_id,
                service_type,
                max_hourly_rate,
                location_postal_code,
                max_distance_km
            )
            
            # Sort by rating and price
            tutors.sort(key=lambda t: (
                -t.get('average_rating', 0),  # Highest rating first
                t.get('hourly_rate', 999)      # Lowest price second
            ))
            
            return tutors
            
        except Exception as e:
            logger.error(f"Error finding available tutors: {e}")
            raise
    
    def _validate_rate_configuration(self, request: CreateTutorServiceRateRequest, service: ServiceCatalogResponse):
        """Validate tutor rate configuration."""
        # Validate service type compatibility
        if request.service_type not in [ServiceType.ONLINE, ServiceType.HYBRID]:
            if not request.max_distance_km:
                logger.warning(f"In-person service without max_distance_km specified")
        
        # Validate pricing tier rates
        self._validate_hourly_rate(request.hourly_rate, request.pricing_tier)
        
        # Validate surcharges
        if request.rush_hour_surcharge and request.rush_hour_surcharge > request.hourly_rate * Decimal("0.5"):
            raise ValidationError("Rush hour surcharge cannot exceed 50% of hourly rate")
        
        if request.weekend_surcharge and request.weekend_surcharge > request.hourly_rate * Decimal("0.3"):
            raise ValidationError("Weekend surcharge cannot exceed 30% of hourly rate")
    
    def _validate_hourly_rate(self, hourly_rate: Decimal, pricing_tier: PricingTier):
        """Validate hourly rate based on pricing tier."""
        tier_ranges = {
            PricingTier.STANDARD: (Decimal("25"), Decimal("60")),
            PricingTier.PREMIUM: (Decimal("50"), Decimal("100")),
            PricingTier.SPECIALIZED: (Decimal("75"), Decimal("200"))
        }
        
        min_rate, max_rate = tier_ranges.get(pricing_tier, (Decimal("25"), Decimal("200")))
        
        if hourly_rate < min_rate:
            raise ValidationError(f"{pricing_tier.value} tier requires minimum rate of ${min_rate}")
        
        if hourly_rate > max_rate:
            logger.warning(f"Hourly rate ${hourly_rate} exceeds typical {pricing_tier.value} tier maximum of ${max_rate}")


class ServicePackageService:
    """Service for managing service packages."""
    
    def __init__(self, db_connection: asyncpg.Connection):
        self.db = db_connection
        self.package_repo = ServicePackageRepository(db_connection)
    
    async def create_package(
        self,
        request: CreateServicePackageRequest,
        created_by: int
    ) -> ServicePackageResponse:
        """Create a new service package."""
        try:
            # Validate package configuration
            self._validate_package_configuration(request)
            
            # Create package
            package_data = request.model_dump()
            package = await self.package_repo.create_package(package_data, created_by)
            
            logger.info(f"Created package {package.service_package_id}: {package.package_name}")
            return package
            
        except Exception as e:
            logger.error(f"Error creating package: {e}")
            raise
    
    async def update_package(
        self,
        service_package_id: int,
        request: UpdateServicePackageRequest,
        updated_by: int
    ) -> ServicePackageResponse:
        """Update a service package."""
        try:
            # Get existing package
            existing_package = await self.package_repo.get_package_with_stats(service_package_id)
            if not existing_package:
                raise ResourceNotFoundError(f"Package {service_package_id} not found")
            
            # Check if package has active purchases
            if existing_package.active_subscriptions > 0:
                # Limit updates for active packages
                allowed_updates = ['description', 'is_active']
                updates = request.model_dump(exclude_unset=True)
                
                restricted_updates = [k for k in updates.keys() if k not in allowed_updates]
                if restricted_updates:
                    raise BusinessLogicError(
                        f"Cannot update {', '.join(restricted_updates)} for package with active subscriptions"
                    )
            
            # Apply updates
            updates = request.model_dump(exclude_unset=True)
            if updates:
                package = await self.package_repo.update(service_package_id, updates, updated_by)
                logger.info(f"Updated package {service_package_id}")
                
                # Reload with stats
                return await self.package_repo.get_package_with_stats(service_package_id)
            
            return existing_package
            
        except Exception as e:
            logger.error(f"Error updating package: {e}")
            raise
    
    async def get_active_packages(
        self,
        package_type: Optional[PackageType] = None,
        subject_area: Optional[SubjectArea] = None,
        service_type: Optional[ServiceType] = None
    ) -> List[ServicePackageResponse]:
        """Get active packages by filters."""
        try:
            packages = await self.package_repo.get_active_packages(
                package_type,
                subject_area,
                service_type
            )
            
            return packages
            
        except Exception as e:
            logger.error(f"Error getting active packages: {e}")
            raise
    
    async def purchase_package(
        self,
        package_id: int,
        client_id: int,
        created_by: int,
        dependant_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """Purchase a package for a client."""
        try:
            purchase = await self.package_repo.create_package_purchase(
                package_id,
                client_id,
                created_by,
                dependant_id
            )
            
            logger.info(f"Created package purchase {purchase['package_purchase_id']} for client {client_id}")
            return purchase
            
        except Exception as e:
            logger.error(f"Error purchasing package: {e}")
            raise
    
    def _validate_package_configuration(self, request: CreateServicePackageRequest):
        """Validate package configuration."""
        # Validate TECFEE packages
        if request.package_type == PackageType.TECFEE:
            if SubjectArea.FRENCH not in request.subject_areas:
                raise ValidationError("TECFEE packages must include French subject area")
            
            if request.total_sessions != 12:
                raise ValidationError("TECFEE packages must have exactly 12 sessions")
            
            if request.session_duration_minutes < 60:
                raise ValidationError("TECFEE sessions must be at least 60 minutes")
        
        # Validate group packages
        if request.max_participants:
            if request.package_type != PackageType.TECFEE:
                logger.warning("Group size specified for non-TECFEE package")
            
            if request.min_participants and request.min_participants < 3:
                logger.warning("Very small minimum group size - consider individual sessions")
        
        # Validate pricing
        if request.package_price > 0:
            price_per_session = request.package_price / request.total_sessions
            if price_per_session < 20:
                raise ValidationError("Package price per session is below minimum viable rate")


class ServicePricingService:
    """Service for calculating service pricing."""
    
    def __init__(self, db_connection: asyncpg.Connection):
        self.db = db_connection
        self.rate_repo = TutorServiceRateRepository(db_connection)
    
    async def calculate_pricing(
        self,
        request: PricingCalculationRequest
    ) -> PricingCalculationResponse:
        """Calculate pricing for a service."""
        try:
            # Get tutor rate
            rate = await self.rate_repo.get_tutor_rate_with_service(request.tutor_service_rate_id)
            if not rate:
                raise ResourceNotFoundError(f"Tutor service rate {request.tutor_service_rate_id} not found")
            
            # Calculate base amount
            hours = Decimal(request.duration_minutes) / 60
            base_amount = rate.hourly_rate * hours
            
            # Calculate surcharges
            surcharge_amount = Decimal("0")
            surcharge_breakdown = {}
            
            if request.apply_surcharges:
                # Rush hour surcharge (weekdays 7-9 AM, 5-7 PM)
                if request.is_rush_hour and rate.rush_hour_surcharge:
                    surcharge_amount += rate.rush_hour_surcharge
                    surcharge_breakdown['rush_hour'] = rate.rush_hour_surcharge
                
                # Weekend surcharge
                if request.appointment_datetime.weekday() >= 5 and rate.weekend_surcharge:
                    surcharge_amount += rate.weekend_surcharge
                    surcharge_breakdown['weekend'] = rate.weekend_surcharge
                
                # Holiday surcharge (would check holiday calendar)
                # For now, skip holiday check
            
            # Calculate totals
            client_total = base_amount + surcharge_amount
            platform_fee = client_total * (rate.platform_fee_percentage / 100)
            tutor_earnings = client_total - platform_fee
            
            # Check if package session
            is_package_session = request.package_id is not None
            package_sessions_remaining = None
            
            if is_package_session:
                # Would check package purchase details
                # For now, return mock data
                package_sessions_remaining = 8
            
            return PricingCalculationResponse(
                base_amount=base_amount,
                surcharge_amount=surcharge_amount,
                platform_fee=platform_fee,
                tutor_earnings=tutor_earnings,
                client_total=client_total,
                surcharge_breakdown=surcharge_breakdown,
                is_package_session=is_package_session,
                package_sessions_remaining=package_sessions_remaining,
                hourly_rate=rate.hourly_rate,
                platform_fee_percentage=rate.platform_fee_percentage
            )
            
        except Exception as e:
            logger.error(f"Error calculating pricing: {e}")
            raise


class LocationService:
    """Service for location preferences and distance calculations."""
    
    def __init__(self, db_connection: asyncpg.Connection):
        self.db = db_connection
        self.location_repo = LocationPreferenceRepository(db_connection)
        self.geocoding_service = GeocodingService()
    
    async def save_location_preference(
        self,
        entity_type: str,
        entity_id: int,
        location: LocationPreference
    ) -> Dict[str, Any]:
        """Save location preferences for a user/tutor/client."""
        try:
            location_data = location.model_dump()
            
            # Geocode postal code if coordinates not provided
            if not location_data.get('latitude') or not location_data.get('longitude'):
                coords = await self.geocoding_service.geocode_postal_code(location.postal_code)
                if coords:
                    location_data['latitude'] = coords['lat']
                    location_data['longitude'] = coords['lng']
            
            preference = await self.location_repo.save_location_preference(
                entity_type,
                entity_id,
                location_data
            )
            
            return preference
            
        except Exception as e:
            logger.error(f"Error saving location preference: {e}")
            raise
    
    async def calculate_distance(
        self,
        request: DistanceCalculationRequest
    ) -> DistanceCalculationResponse:
        """Calculate distance between two postal codes."""
        try:
            distance_km = await self.geocoding_service.calculate_distance_between_postal_codes(
                request.origin_postal_code,
                request.destination_postal_code
            )
            
            if distance_km is None:
                raise BusinessLogicError("Unable to calculate distance")
            
            # Estimate duration based on travel mode
            duration_minutes = self.geocoding_service.estimate_travel_time(
                distance_km,
                request.travel_mode
            )
            
            # Check if within typical service area (15km for tutoring)
            is_within_service_area = distance_km <= 15
            
            return DistanceCalculationResponse(
                distance_km=distance_km,
                duration_minutes=duration_minutes,
                travel_mode=request.travel_mode,
                is_within_service_area=is_within_service_area
            )
            
        except Exception as e:
            logger.error(f"Error calculating distance: {e}")
            raise
    


class ServiceRecommendationService:
    """Service for generating service recommendations."""
    
    def __init__(self, db_connection: asyncpg.Connection):
        self.db = db_connection
        self.catalog_service = ServiceCatalogService(db_session)
        self.rate_service = TutorServiceRateService(db_session)
    
    async def get_recommendations(
        self,
        request: ServiceRecommendationRequest
    ) -> List[Dict[str, Any]]:
        """Get personalized service recommendations for a client."""
        try:
            recommendations = []
            
            # Get client's past appointment history (would be from appointment service)
            # For now, use request preferences
            
            # Find services matching preferences
            if request.preferred_subject_areas:
                for subject in request.preferred_subject_areas:
                    services, _ = await self.catalog_service.search_services(
                        ServiceSearchRequest(
                            subject_area=subject,
                            service_type=request.preferred_service_types[0] if request.preferred_service_types else None,
                            is_active_only=True,
                            page_size=5
                        )
                    )
                    
                    for service in services[:2]:  # Top 2 per subject
                        # Find available tutors
                        tutors = await self.rate_service.find_available_tutors(
                            service.service_catalog_id,
                            request.preferred_service_types[0] if request.preferred_service_types else ServiceType.ONLINE,
                            location_postal_code=request.location_postal_code
                        )
                        
                        if tutors:
                            recommendations.append({
                                'service': service,
                                'recommended_tutors': tutors[:3],  # Top 3 tutors
                                'reason': f"Based on your interest in {subject.value}",
                                'match_score': 0.85
                            })
            
            # Sort by match score
            recommendations.sort(key=lambda r: r['match_score'], reverse=True)
            
            return recommendations[:request.max_recommendations]
            
        except Exception as e:
            logger.error(f"Error getting recommendations: {e}")
            raise