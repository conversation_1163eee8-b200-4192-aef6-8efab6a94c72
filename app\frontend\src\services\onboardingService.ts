import api from './api';
import { UserRoleType } from '../types/auth';

// Onboarding Types and Interfaces
export enum OnboardingStep {
  // Common Steps
  WELCOME = 'welcome',
  PROFILE_SETUP = 'profile_setup',
  CONSENT_AGREEMENTS = 'consent_agreements',
  PREFERENCES = 'preferences',
  
  // Client Steps
  EMERGENCY_CONTACTS = 'emergency_contacts',
  DEPENDANTS_SETUP = 'dependants_setup',
  LOCATION_SETUP = 'location_setup',
  PAYMENT_METHOD = 'payment_method',
  TUTOR_PREFERENCES = 'tutor_preferences',
  
  // Tutor Steps
  QUALIFICATIONS = 'qualifications',
  DOCUMENT_UPLOAD = 'document_upload',
  SERVICE_AREAS = 'service_areas',
  AVAILABILITY = 'availability',
  BANKING_INFO = 'banking_info',
  BACKGROUND_CHECK = 'background_check',
  
  // Final Steps
  TOUR = 'tour',
  COMPLETION = 'completion'
}

export enum OnboardingStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  SKIPPED = 'skipped'
}

export interface OnboardingProgress {
  user_id: number;
  role: UserRoleType;
  current_step: OnboardingStep;
  completed_steps: OnboardingStep[];
  skipped_steps: OnboardingStep[];
  total_steps: number;
  progress_percentage: number;
  status: OnboardingStatus;
  started_at?: string;
  completed_at?: string;
  last_updated: string;
}

export interface OnboardingStepConfig {
  step: OnboardingStep;
  title: string;
  description: string;
  icon: string;
  component: string;
  is_required: boolean;
  estimated_time_minutes: number;
  dependencies?: OnboardingStep[];
  validation_rules?: Record<string, any>;
  help_text?: string;
}

export interface OnboardingFlow {
  role: UserRoleType;
  steps: OnboardingStepConfig[];
  total_estimated_time: number;
  can_skip_optional: boolean;
  completion_rewards?: string[];
}

export interface UpdateProgressRequest {
  step: OnboardingStep;
  data?: Record<string, any>;
  action: 'complete' | 'skip' | 'navigate';
}

export interface ValidateStepRequest {
  step: OnboardingStep;
  data: Record<string, any>;
}

export interface ValidateStepResponse {
  is_valid: boolean;
  errors?: Record<string, string[]>;
  warnings?: string[];
}

export interface OnboardingCompletionData {
  completed_at: string;
  total_time_minutes: number;
  steps_completed: number;
  steps_skipped: number;
  profile_completeness: number;
  next_actions: Array<{
    title: string;
    description: string;
    route: string;
    icon: string;
  }>;
}

class OnboardingService {
  /**
   * Get onboarding progress for current user
   */
  async getProgress(): Promise<OnboardingProgress> {
    try {
      const response = await api.get<OnboardingProgress>('/onboarding/progress');
      return response.data;
    } catch (error: any) {
      console.error('Error fetching onboarding progress:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch onboarding progress');
    }
  }

  /**
   * Get onboarding flow for specific role
   */
  async getFlow(role?: UserRoleType): Promise<OnboardingFlow> {
    try {
      const params = role ? { role } : {};
      const response = await api.get<OnboardingFlow>('/onboarding/flow', { params });
      return response.data;
    } catch (error: any) {
      console.error('Error fetching onboarding flow:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch onboarding flow');
    }
  }

  /**
   * Update onboarding progress
   */
  async updateProgress(request: UpdateProgressRequest): Promise<OnboardingProgress> {
    try {
      const response = await api.post<OnboardingProgress>('/onboarding/progress', request);
      return response.data;
    } catch (error: any) {
      console.error('Error updating onboarding progress:', error);
      throw new Error(error.response?.data?.detail || 'Failed to update progress');
    }
  }

  /**
   * Validate step data before proceeding
   */
  async validateStep(request: ValidateStepRequest): Promise<ValidateStepResponse> {
    try {
      const response = await api.post<ValidateStepResponse>('/onboarding/validate', request);
      return response.data;
    } catch (error: any) {
      console.error('Error validating step:', error);
      throw new Error(error.response?.data?.detail || 'Failed to validate step');
    }
  }

  /**
   * Complete a step
   */
  async completeStep(step: OnboardingStep, data?: Record<string, any>): Promise<OnboardingProgress> {
    return this.updateProgress({
      step,
      data,
      action: 'complete'
    });
  }

  /**
   * Skip a step
   */
  async skipStep(step: OnboardingStep): Promise<OnboardingProgress> {
    return this.updateProgress({
      step,
      action: 'skip'
    });
  }

  /**
   * Navigate to a specific step
   */
  async navigateToStep(step: OnboardingStep): Promise<OnboardingProgress> {
    return this.updateProgress({
      step,
      action: 'navigate'
    });
  }

  /**
   * Reset onboarding progress
   */
  async resetProgress(): Promise<{ message: string }> {
    try {
      const response = await api.post<{ message: string }>('/onboarding/reset');
      return response.data;
    } catch (error: any) {
      console.error('Error resetting onboarding:', error);
      throw new Error(error.response?.data?.detail || 'Failed to reset onboarding');
    }
  }

  /**
   * Get completion data
   */
  async getCompletionData(): Promise<OnboardingCompletionData> {
    try {
      const response = await api.get<OnboardingCompletionData>('/onboarding/completion');
      return response.data;
    } catch (error: any) {
      console.error('Error fetching completion data:', error);
      throw new Error(error.response?.data?.detail || 'Failed to fetch completion data');
    }
  }

  /**
   * Get next step in flow
   */
  getNextStep(flow: OnboardingFlow, progress: OnboardingProgress): OnboardingStepConfig | null {
    const currentIndex = flow.steps.findIndex(s => s.step === progress.current_step);
    
    for (let i = currentIndex + 1; i < flow.steps.length; i++) {
      const nextStep = flow.steps[i];
      
      // Skip if already completed or skipped
      if (progress.completed_steps.includes(nextStep.step) || 
          progress.skipped_steps.includes(nextStep.step)) {
        continue;
      }
      
      // Check dependencies
      if (nextStep.dependencies) {
        const allDependenciesMet = nextStep.dependencies.every(dep => 
          progress.completed_steps.includes(dep)
        );
        
        if (!allDependenciesMet) {
          continue;
        }
      }
      
      return nextStep;
    }
    
    return null;
  }

  /**
   * Get previous step in flow
   */
  getPreviousStep(flow: OnboardingFlow, progress: OnboardingProgress): OnboardingStepConfig | null {
    const currentIndex = flow.steps.findIndex(s => s.step === progress.current_step);
    
    for (let i = currentIndex - 1; i >= 0; i--) {
      const prevStep = flow.steps[i];
      
      // Skip if skipped (user can go back to skipped steps)
      if (progress.skipped_steps.includes(prevStep.step)) {
        return prevStep;
      }
      
      // Can go back to completed steps
      if (progress.completed_steps.includes(prevStep.step)) {
        return prevStep;
      }
    }
    
    return null;
  }

  /**
   * Check if step can be skipped
   */
  canSkipStep(step: OnboardingStepConfig, flow: OnboardingFlow): boolean {
    return !step.is_required && flow.can_skip_optional;
  }

  /**
   * Calculate time remaining
   */
  calculateTimeRemaining(flow: OnboardingFlow, progress: OnboardingProgress): number {
    let timeRemaining = 0;
    
    flow.steps.forEach(step => {
      if (!progress.completed_steps.includes(step.step) && 
          !progress.skipped_steps.includes(step.step)) {
        timeRemaining += step.estimated_time_minutes;
      }
    });
    
    return timeRemaining;
  }

  /**
   * Format step title for display
   */
  formatStepTitle(step: OnboardingStep): string {
    return step
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  /**
   * Get step icon component name
   */
  getStepIcon(icon: string): string {
    const iconMap: Record<string, string> = {
      'user': 'User',
      'shield': 'Shield',
      'settings': 'Settings',
      'users': 'Users',
      'map-pin': 'MapPin',
      'credit-card': 'CreditCard',
      'graduation-cap': 'GraduationCap',
      'file-text': 'FileText',
      'calendar': 'Calendar',
      'bank': 'Building',
      'check-circle': 'CheckCircle',
      'sparkles': 'Sparkles',
      'flag': 'Flag'
    };

    return iconMap[icon] || 'Circle';
  }

  /**
   * Check if onboarding is complete
   */
  isOnboardingComplete(progress: OnboardingProgress): boolean {
    return progress.status === OnboardingStatus.COMPLETED;
  }

  /**
   * Get role-specific welcome message
   */
  getWelcomeMessage(role: UserRoleType): string {
    const messages: Record<UserRoleType, string> = {
      [UserRoleType.MANAGER]: 'Welcome to TutorAide! Let\'s set up your manager account.',
      [UserRoleType.TUTOR]: 'Welcome to TutorAide! Let\'s get you set up to start tutoring.',
      [UserRoleType.CLIENT]: 'Welcome to TutorAide! Let\'s help you find the perfect tutor.'
    };

    return messages[role] || 'Welcome to TutorAide!';
  }
}

export const onboardingService = new OnboardingService();