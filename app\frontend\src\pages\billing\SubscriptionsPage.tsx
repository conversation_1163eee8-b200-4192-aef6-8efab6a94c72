import React from 'react';
import { useTranslation } from 'react-i18next';
import { Package, Clock, TrendingUp, Info } from 'lucide-react';
import SubscriptionList from '../../components/billing/SubscriptionList';
import ClientSubscriptions from '../../components/client/ClientSubscriptions';
import { Card } from '../../components/common/Card';
import { useAuth } from '../../contexts/AuthContext';
import { UserRoleType } from '../../types/auth';

const SubscriptionsPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const isManager = user?.activeRole === UserRoleType.MANAGER;
  const isClient = user?.activeRole === UserRoleType.CLIENT;
  
  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900 flex items-center">
              <Package className="w-6 h-6 mr-2 text-accent-red" />
              {isClient 
                ? t('billing.subscriptions.mySubscription', 'My Subscription')
                : t('billing.subscriptions.title')
              }
            </h1>
            <p className="text-sm text-gray-500 mt-1">
              {isManager 
                ? t('billing.subscriptions.managerDescription')
                : t('billing.subscriptions.clientDescription')
              }
            </p>
          </div>
        </div>
        
        {/* How It Works */}
        <div className="mt-4 bg-red-50 rounded-lg p-4">
          <div className="flex items-start">
            <Info className="w-5 h-5 text-red-600 mr-3 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-red-800">
              <p className="font-semibold mb-1">{t('billing.subscriptions.howItWorks')}</p>
              <ul className="space-y-1">
                <li className="flex items-center">
                  <span className="mr-2">•</span>
                  <span>{t('billing.subscriptions.dualBillingExplanation')}</span>
                </li>
                <li className="flex items-center">
                  <span className="mr-2">•</span>
                  <span>{t('billing.subscriptions.autoDeductionExplanation')}</span>
                </li>
                <li className="flex items-center">
                  <span className="mr-2">•</span>
                  <span>{t('billing.subscriptions.noSubscriptionExplanation')}</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
        
        {/* Benefits Cards */}
        <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="p-4">
            <div className="flex items-center">
              <Package className="w-8 h-8 text-accent-red mr-3" />
              <div>
                <p className="text-sm text-gray-500">{t('billing.subscriptions.prepaidHours')}</p>
                <p className="text-lg font-semibold text-gray-900">
                  {t('billing.subscriptions.buyInAdvance')}
                </p>
              </div>
            </div>
          </Card>
          
          <Card className="p-4">
            <div className="flex items-center">
              <Clock className="w-8 h-8 text-green-600 mr-3" />
              <div>
                <p className="text-sm text-gray-500">{t('billing.subscriptions.automaticBilling')}</p>
                <p className="text-lg font-semibold text-gray-900">
                  {t('billing.subscriptions.noInvoices')}
                </p>
              </div>
            </div>
          </Card>
          
          <Card className="p-4">
            <div className="flex items-center">
              <TrendingUp className="w-8 h-8 text-red-600 mr-3" />
              <div>
                <p className="text-sm text-gray-500">{t('billing.subscriptions.trackUsage')}</p>
                <p className="text-lg font-semibold text-gray-900">
                  {t('billing.subscriptions.realTimeTracking')}
                </p>
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-6 bg-gray-50">
        {isManager ? (
          <SubscriptionList />
        ) : isClient ? (
          <ClientSubscriptions />
        ) : (
          <Card className="p-6">
            <p className="text-gray-600">
              {t('billing.subscriptions.notAvailableForRole')}
            </p>
          </Card>
        )}
      </div>
    </div>
  );
};

export default SubscriptionsPage;
