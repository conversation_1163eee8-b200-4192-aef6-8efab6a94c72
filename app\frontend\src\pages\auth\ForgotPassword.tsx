import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Mail, ArrowLeft, CheckCircle, AlertCircle } from 'lucide-react';
import { Card } from '../../components/common/Card';
import Button from '../../components/common/Button';
import { Input } from '../../components/common/Input';
import api from '../../services/api';
import toast from 'react-hot-toast';

export const ForgotPassword: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!email) {
      setError(t('auth.forgotPassword.emailRequired'));
      return;
    }

    if (!validateEmail(email)) {
      setError(t('auth.forgotPassword.invalidEmail'));
      return;
    }

    try {
      setLoading(true);
      
      await api.post('/api/v1/auth/password/reset-request', {
        email: email.toLowerCase().trim()
      });

      // Always show success for security reasons
      setSuccess(true);
      
    } catch (error: any) {
      console.error('Password reset request error:', error);
      
      // Check for rate limiting
      if (error.response?.status === 429) {
        setError(t('auth.forgotPassword.tooManyRequests'));
      } else {
        // Still show success for security (don't reveal if email exists)
        setSuccess(true);
      }
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4 py-12">
        <Card className="w-full max-w-md">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              {t('auth.forgotPassword.emailSentTitle')}
            </h2>
            <p className="text-gray-600 mb-6">
              {t('auth.forgotPassword.emailSentMessage', { email })}
            </p>
            <p className="text-sm text-gray-500 mb-8">
              {t('auth.forgotPassword.checkSpam')}
            </p>
            <Button
              variant="secondary"
              onClick={() => navigate('/login')}
              className="w-full"
            >
              {t('auth.forgotPassword.backToLogin')}
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4 py-12">
      <div className="w-full max-w-md">
        {/* Back to login link */}
        <Link
          to="/login"
          className="inline-flex items-center text-sm text-gray-600 hover:text-gray-900 mb-8"
        >
          <ArrowLeft className="w-4 h-4 mr-1" />
          {t('auth.forgotPassword.backToLogin')}
        </Link>

        <Card>
          <div className="text-center mb-8">
            <div className="mx-auto flex items-center justify-center w-16 h-16 bg-accent-red/10 rounded-full mb-4">
              <Mail className="w-8 h-8 text-accent-red" />
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              {t('auth.forgotPassword.title')}
            </h1>
            <p className="text-gray-600">
              {t('auth.forgotPassword.subtitle')}
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-start">
                  <AlertCircle className="w-5 h-5 text-red-600 flex-shrink-0 mt-0.5" />
                  <p className="ml-3 text-sm text-red-800">{error}</p>
                </div>
              </div>
            )}

            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                {t('auth.forgotPassword.emailLabel')}
              </label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder={t('auth.forgotPassword.emailPlaceholder')}
                disabled={loading}
                autoComplete="email"
                autoFocus
              />
            </div>

            <Button
              type="submit"
              loading={loading}
              disabled={loading || !email}
              className="w-full"
            >
              {loading
                ? t('auth.forgotPassword.sending')
                : t('auth.forgotPassword.sendResetLink')}
            </Button>
          </form>

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600">
              {t('auth.forgotPassword.rememberPassword')}{' '}
              <Link
                to="/login"
                className="font-medium text-accent-red hover:text-accent-red-dark"
              >
                {t('auth.forgotPassword.signIn')}
              </Link>
            </p>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default ForgotPassword;