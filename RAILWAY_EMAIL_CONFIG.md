# Railway Email Configuration Guide

## Environment Variables Required

Make sure these environment variables are set in your Railway project:

```
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-specific-password
SMTP_TLS=true
```

**IMPORTANT**: These must be set as Railway environment variables, NOT in a .env file.

## Gmail Setup

1. **Enable 2-Factor Authentication** on your Gmail account
2. **Generate App-Specific Password**:
   - Go to https://myaccount.google.com/security
   - Click on "2-Step Verification"
   - Scroll down and click on "App passwords"
   - Generate a new password for "Mail"
   - Use this password for `SMTP_PASSWORD`

## Verify Configuration

After deployment, you can verify the email configuration:

1. **Check logs** during password reset attempts. Look for:
   ```
   Initializing EmailService for the first time...
   Raw environment variables:
     SMTP_HOST env: smtp.gmail.com
   ```

2. **Use the config check endpoint** (managers only):
   ```
   GET /api/v1/auth/email/config-check
   ```
   This endpoint shows:
   - Raw environment variables
   - Loaded settings values
   - Email service initialization status

3. **Run the test script** via Railway shell:
   ```bash
   python test_email_config.py
   ```

## Troubleshooting

### Issue: Emails not sending
1. Check Railway logs for SMTP configuration
2. Verify environment variables are set correctly
3. Ensure Gmail app password is correct
4. Check if Gmail is blocking sign-in attempts

### Issue: Environment variables not loading
1. Restart the Railway deployment
2. Check for typos in variable names
3. Ensure no trailing spaces in values

### Issue: Connection timeout
1. Gmail might be blocking Railway's IP
2. Try using a different email provider
3. Consider using a transactional email service (SendGrid, Mailgun)

## Testing Password Reset

1. Go to `/auth/forgot-password`
2. Enter your email
3. Check your inbox for the reset email
4. Check Railway logs for any errors

## Notes

- The application will skip .env file loading when `RAILWAY_ENVIRONMENT` is set
- SMTP configuration is logged at startup (password is masked)
- Email sending is done asynchronously to prevent request hanging
- Rate limiting is applied to password reset requests (3 per hour)