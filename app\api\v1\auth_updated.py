"""
Authentication API endpoints - Updated to use unified services.
"""

from typing import Annotated, Optional, Dict, Any, List
from datetime import timed<PERSON><PERSON>
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, status, Response, Request, Query, BackgroundTasks
from fastapi.responses import RedirectResponse
from fastapi.security import OAuth2PasswordRequestForm
import asyncpg
from urllib.parse import urlencode
import os
import time
import asyncio

from app.core.dependencies import get_database, get_current_active_user
from app.core.exceptions import ValidationError, AuthenticationError, AuthorizationError, BusinessLogicError, ResourceNotFoundError, TokenExpiredError
from app.core.security import create_access_token, verify_token, create_refresh_token
from app.services.auth_service import AuthService, get_auth_service
from app.services.google_oauth_service import GoogleOAuthService

# Import unified services
from app.services.auth_token_service import get_auth_token_service
from app.services.user_security_service import get_user_security_service

from app.services.email_service import get_email_service
from app.models.user_models import (
    User, User<PERSON><PERSON>, UserResponse, AuthResponse, TokenResponse,
    UserRoleType, ConsentAcceptance, LoginRequest, RefreshTokenRequest
)
from app.models.auth_token_models import (
    PasswordResetRequest, PasswordResetConfirm,
    EmailVerificationRequest, EmailVerificationConfirm
)
from app.models.consent_models import (
    ConsentAcceptanceRequest, ConsentWithdrawalRequest,
    ConsentStatusResponse, ConsentSummaryResponse,
    ConsentHistoryResponse, ConsentValidationResult,
    BulkConsentRequest, BulkConsentResponse, ConsentDocument
)
from app.services.consent_service import ConsentService
from app.core.logging import TutorAideLogger
from app.config.settings import settings
from app.core.timezone import now_est

router = APIRouter(prefix="/auth", tags=["authentication"])
logger = TutorAideLogger.get_logger(__name__)


@router.post("/register", response_model=AuthResponse, status_code=status.HTTP_201_CREATED)
async def register(
    user_data: UserCreate,
    request: Request,
    response: Response,
    db: Annotated[asyncpg.Connection, Depends(get_database)],
    auth_service: Annotated[AuthService, Depends(get_auth_service)]
):
    """
    Register a new user account.
    
    - **email**: Valid email address
    - **password**: Strong password (min 8 chars, upper, lower, digit)
    - **google_id**: Optional Google OAuth ID
    - **roles**: List of initial roles (manager, tutor, client)
    
    Returns JWT access token and user info. Automatically records mandatory consents.
    """
    # Get client IP for rate limiting and consent tracking
    client_ip = str(request.client.host) if request.client else '127.0.0.1'
    
    # Import rate limiting service
    from app.services.rate_limiting_service import rate_limiting_service
    from app.core.validation import RateLimitValidator
    
    try:
        # TODO: Fix rate limiting - temporarily disabled due to missing method
        # Check registration rate limits
        # try:
        #     RateLimitValidator.check_rate_limit(
        #         identifier=client_ip,  # Use IP for registration rate limiting
        #         action='register',
        #         ip_address=client_ip
        #     )
        #     
        #     # Add rate limit headers to response
        #     response.headers["X-RateLimit-Limit"] = "3"  # 3 registrations per hour
        #     response.headers["X-RateLimit-Window"] = "3600"  # 1 hour window
        #     
        # except ValidationError as e:
        #     # Rate limit exceeded
        #     logger.security(f"Registration rate limit exceeded from {client_ip}")
        #     raise HTTPException(
        #         status_code=status.HTTP_429_TOO_MANY_REQUESTS,
        #         detail="Too many registration attempts. Please try again later.",
        #         headers={
        #             "Retry-After": "3600",
        #             "X-RateLimit-Limit": "3",
        #             "X-RateLimit-Remaining": "0"
        #         }
        #     )
        
        # Register the user
        user_profile = await auth_service.register_user(
            db, user_data, ip_address=client_ip
        )
        
        # Create JWT tokens
        access_token = create_access_token(
            subject=str(user_profile.user.user_id),
            user_roles=[role.value for role in user_profile.user.roles],
            primary_role=user_profile.user.roles[0].value if user_profile.user.roles else None
        )
        
        # Create refresh token
        refresh_token = create_refresh_token(
            subject=str(user_profile.user.user_id),
            user_roles=[role.value for role in user_profile.user.roles]
        )
        
        auth_response = AuthResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            user=UserResponse(
                user_id=user_profile.user.user_id,
                email=user_profile.user.email,
                is_email_verified=user_profile.user.is_email_verified,
                has_local_auth=user_profile.user.has_local_auth(),
                has_google_auth=user_profile.user.has_google_auth(),
                roles=user_profile.get_active_roles(),
                created_at=user_profile.user.created_at,
                updated_at=user_profile.user.updated_at
            )
        )
        
        # Record mandatory consents
        consent_service = ConsentService()
        await consent_service.record_initial_consents(
            db, user_profile.user.user_id, ip_address=client_ip
        )
        
        logger.info(f"New user registered: {user_data.email}")
        return auth_response
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except BusinessLogicError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )
    except HTTPException:
        # Re-raise HTTP exceptions (like rate limit)
        raise
    except Exception as e:
        logger.error(f"Registration error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )


# Session management endpoints using unified AuthTokenService
@router.get("/sessions", response_model=Dict[str, Any])
async def get_user_sessions(
    current_user: Annotated[User, Depends(get_current_active_user)],
    db: Annotated[asyncpg.Connection, Depends(get_database)]
):
    """
    Get all active sessions for the current user.
    
    Returns list of active sessions with details like IP, device, last activity.
    """
    try:
        auth_token_service = get_auth_token_service()
        
        # Get active sessions
        sessions = await auth_token_service.token_repo.get_user_tokens(
            conn=db,
            user_id=current_user.user_id,
            token_type=auth_token_service.AuthTokenType.SESSION,
            include_used=False,
            include_expired=False
        )
        
        # Format sessions for response
        formatted_sessions = []
        for token in sessions:
            metadata = token.metadata or {}
            formatted_sessions.append({
                "session_id": metadata.get('session_id', str(token.token_id)),
                "role_type": metadata.get('role_type', 'unknown'),
                "ip_address": token.ip_address,
                "user_agent": token.user_agent,
                "last_activity": metadata.get('last_activity', token.created_at.isoformat()),
                "created_at": token.created_at.isoformat(),
                "expires_at": token.expires_at.isoformat()
            })
        
        return {
            "sessions": formatted_sessions,
            "count": len(formatted_sessions)
        }
        
    except Exception as e:
        logger.error(f"Error getting user sessions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve sessions"
        )


@router.delete("/sessions", status_code=status.HTTP_204_NO_CONTENT)
async def invalidate_all_sessions(
    current_user: Annotated[User, Depends(get_current_active_user)],
    db: Annotated[asyncpg.Connection, Depends(get_database)],
    request: Request,
    except_current: bool = Query(True, description="Keep current session active")
):
    """
    Invalidate all sessions for the current user.
    
    - **except_current**: If true, keeps the current session active (default: true)
    
    Useful for security purposes or when user wants to log out all devices.
    """
    try:
        auth_token_service = get_auth_token_service()
        
        # Get current token from Authorization header if we need to except it
        except_token = None
        if except_current:
            auth_header = request.headers.get("Authorization", "")
            if auth_header.startswith("Bearer "):
                except_token = auth_header.split(" ")[1]
        
        # Invalidate sessions
        count = await auth_token_service.invalidate_all_user_sessions(
            conn=db,
            user_id=current_user.user_id,
            except_token=except_token
        )
        
        logger.info(f"User {current_user.user_id} invalidated {count} sessions")
        
    except Exception as e:
        logger.error(f"Error invalidating all sessions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to invalidate sessions"
        )


# Password reset endpoints using unified AuthTokenService
@router.post("/password/reset-request", response_model=Dict[str, str])
async def request_password_reset(
    reset_request: PasswordResetRequest,
    request: Request,
    response: Response,
    db: Annotated[asyncpg.Connection, Depends(get_database)]
):
    """
    Request a password reset link.
    
    - **email**: Email address to send reset link to
    
    Returns success message regardless of whether email exists (security).
    """
    # Get client IP for rate limiting
    client_ip = str(request.client.host) if request.client else '127.0.0.1'
    
    # Import rate limiting validator
    from app.core.validation import RateLimitValidator
    
    try:
        # TODO: Fix rate limiting - temporarily disabled due to missing method
        # Check password reset rate limits
        # try:
        #     RateLimitValidator.check_rate_limit(
        #         identifier=reset_request.email,
        #         action='password_reset',
        #         ip_address=client_ip
        #     )
        #     
        #     # Add rate limit headers to response
        #     response.headers["X-RateLimit-Limit"] = "3"  # 3 reset requests per hour
        #     response.headers["X-RateLimit-Window"] = "3600"  # 1 hour window
        #     
        # except ValidationError as e:
        #     # Rate limit exceeded
        #     logger.security(f"Password reset rate limit exceeded for {reset_request.email} from {client_ip}")
        #     # Still return success message for security
        #     response.headers["Retry-After"] = "3600"
        #     response.headers["X-RateLimit-Limit"] = "3"
        #     response.headers["X-RateLimit-Remaining"] = "0"
        #     return {"message": "If the email exists, a password reset link has been sent"}
        
        auth_token_service = get_auth_token_service()
        
        # Get client info
        user_agent = request.headers.get("User-Agent")
        
        # Request reset using unified service
        logger.info(f"=== PASSWORD RESET REQUEST DEBUG ===")
        logger.info(f"Requesting password reset for email: {reset_request.email}")
        
        result = await auth_token_service.create_password_reset_token(
            conn=db,
            email=reset_request.email,
            ip_address=client_ip,
            user_agent=user_agent
        )
        
        # Debug logging to understand the result
        logger.info(f"Password reset token creation result: {result}")
        logger.info(f"Result type: {type(result)}, Has token: {'token' in result if result else False}")
        
        # Send email synchronously with timing protection
        if result and "token" in result:
            # Start timing for consistent response
            import time
            import asyncio
            start_time = time.time()
            
            # Determine user's preferred language (default to English)
            language = request.headers.get("Accept-Language", "en")[:2]
            if language not in ["en", "fr"]:
                language = "en"
            
            # Send email synchronously
            try:
                logger.info("=== SYNCHRONOUS EMAIL SEND ===")
                logger.info(f"Sending password reset email to: {result['email']}")
                logger.info(f"Environment: {os.environ.get('ENVIRONMENT', 'Not set')}")
                logger.info(f"Railway: {os.environ.get('RAILWAY_ENVIRONMENT', 'Not set')}")
                
                # Get email service
                email_service_instance = get_email_service()
                
                # Send email with a timeout
                success = await email_service_instance.send_password_reset_email(
                    email=result["email"],
                    reset_token=result["token"],
                    language=language
                )
                
                if success:
                    logger.info(f"Password reset email sent successfully to {result['email']}")
                else:
                    logger.error(f"Failed to send password reset email to {result['email']}")
                    
            except Exception as e:
                logger.error(f"Error sending password reset email: {type(e).__name__}: {str(e)}")
                import traceback
                logger.error(f"Full traceback: {traceback.format_exc()}")
            
            # Ensure consistent response time (2 seconds) to prevent timing attacks
            elapsed = time.time() - start_time
            if elapsed < 2.0:
                await asyncio.sleep(2.0 - elapsed)
                logger.info(f"Added delay of {2.0 - elapsed:.2f}s for timing protection")
        
        # Always return success for security
        return {"message": "If the email exists, a password reset link has been sent"}
        
    except Exception as e:
        logger.error(f"Password reset request error: {e}")
        # Still return success for security
        return {"message": "If the email exists, a password reset link has been sent"}


@router.post("/password/reset-confirm", response_model=Dict[str, str])
async def confirm_password_reset(
    reset_confirm: PasswordResetConfirm,
    request: Request,
    db: Annotated[asyncpg.Connection, Depends(get_database)]
):
    """
    Confirm password reset with token.
    
    - **token**: Password reset token from email
    - **new_password**: New password (min 8 chars, upper, lower, digit)
    
    Resets password and invalidates all sessions for security.
    """
    logger.info("=== PASSWORD RESET CONFIRMATION ===")
    logger.info(f"Token received: {reset_confirm.token[:10]}...{reset_confirm.token[-10:]}")
    logger.info(f"New password length: {len(reset_confirm.new_password)}")
    
    try:
        auth_token_service = get_auth_token_service()
        email_service = get_email_service()
        
        # Get client IP
        ip_address = str(request.client.host) if request.client else None
        logger.info(f"Client IP: {ip_address}")
        
        # First validate the token and get user info BEFORE resetting
        logger.info("Validating token first to get user info...")
        user = await auth_token_service.validate_password_reset_token(db, reset_confirm.token)
        if not user:
            logger.error("Token validation failed - user not found")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired reset token"
            )
        
        logger.info(f"Token validated for user: {user.email}")
        user_email = user.email  # Store email before token is used
        
        # Reset password using unified service
        logger.info("Calling reset_password_with_token...")
        success = await auth_token_service.reset_password_with_token(
            conn=db,
            token=reset_confirm.token,
            new_password=reset_confirm.new_password,
            ip_address=ip_address
        )
        
        logger.info(f"Password reset result: {success}")
        
        if not success:
            logger.error("Password reset failed - success=False")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired reset token"
            )
        
        # Send confirmation email using stored email
        try:
            logger.info(f"Sending password changed email to {user_email}")
            language = request.headers.get("Accept-Language", "en")[:2]
            if language not in ["en", "fr"]:
                language = "en"
            
            await email_service.send_password_changed_email(
                email=user_email,
                language=language
            )
            logger.info("Password changed email sent successfully")
        except Exception as email_error:
            # Email sending is optional, don't fail the request
            logger.warning(f"Failed to send password changed email: {email_error}")
        
        logger.info("=== PASSWORD RESET COMPLETED SUCCESSFULLY ===")
        return {"message": "Password has been reset successfully"}
        
    except HTTPException:
        raise
    except ValidationError as e:
        logger.error(f"ValidationError in password reset: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except TokenExpiredError as e:
        logger.error(f"TokenExpiredError in password reset: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Password reset token has expired"
        )
    except Exception as e:
        logger.error(f"Unexpected error in password reset: {type(e).__name__}: {e}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to reset password"
        )


@router.get("/password/reset-validate/{token}", response_model=Dict[str, Any])
async def validate_reset_token(
    token: str,
    db: Annotated[asyncpg.Connection, Depends(get_database)]
):
    """
    Validate a password reset token.
    
    - **token**: Reset token to validate
    
    Returns token validity and associated email if valid.
    """
    logger.info("=== PASSWORD RESET TOKEN VALIDATION ENDPOINT ===")
    logger.info(f"Token received: {token[:10]}...{token[-10:]}")
    logger.info(f"Full token: {token}")
    logger.info(f"Token length: {len(token)}")
    
    # Check for URL encoding issues
    import urllib.parse
    decoded_token = urllib.parse.unquote(token)
    if decoded_token != token:
        logger.warning(f"Token was URL encoded. Decoded: {decoded_token}")
        token = decoded_token
    
    try:
        auth_token_service = get_auth_token_service()
        
        # Validate token
        logger.info("Calling validate_password_reset_token...")
        user = await auth_token_service.validate_password_reset_token(db, token)
        
        if not user:
            logger.error("validate_password_reset_token returned None")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired token"
            )
        
        logger.info(f"Token validated successfully for user: {user.email}")
        return {
            "valid": True,
            "email": user.email
        }
        
    except ValidationError as e:
        logger.error(f"ValidationError: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except TokenExpiredError as e:
        logger.error(f"TokenExpiredError: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Password reset token has expired"
        )
    except ResourceNotFoundError as e:
        logger.error(f"ResourceNotFoundError: {e}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error validating token: {type(e).__name__}: {e}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to validate token"
        )


# Email Verification Endpoints using unified AuthTokenService
@router.post("/email/verification/request", response_model=dict)
async def request_email_verification(
    request_data: EmailVerificationRequest,
    request: Request,
    response: Response,
    db: Annotated[asyncpg.Connection, Depends(get_database)],
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    """
    Request email verification for current user.
    
    This endpoint allows users to request email verification for a specific
    email address. A verification email will be sent with a secure token.
    """
    # Get client IP for rate limiting
    client_ip = str(request.client.host) if request.client else '127.0.0.1'
    
    # Import rate limiting validator
    from app.core.validation import RateLimitValidator
    
    try:
        # TODO: Fix rate limiting - temporarily disabled due to missing method
        # Check email verification rate limits
        # try:
        #     RateLimitValidator.check_rate_limit(
        #         identifier=current_user.email,
        #         action='email_verification',
        #         ip_address=client_ip
        #     )
        #     
        #     # Add rate limit headers to response
        #     response.headers["X-RateLimit-Limit"] = "5"  # 5 verification requests per hour
        #     response.headers["X-RateLimit-Window"] = "3600"  # 1 hour window
        #     
        # except ValidationError as e:
        #     # Rate limit exceeded
        #     logger.security(f"Email verification rate limit exceeded for {current_user.email} from {client_ip}")
        #     raise HTTPException(
        #         status_code=status.HTTP_429_TOO_MANY_REQUESTS,
        #         detail="Too many verification requests. Please try again later.",
        #         headers={
        #             "Retry-After": "1800",  # 30 minutes
        #             "X-RateLimit-Limit": "5",
        #             "X-RateLimit-Remaining": "0"
        #         }
        #     )
        
        auth_token_service = get_auth_token_service()
        email_service = get_email_service()
        
        # Get client info
        user_agent = request.headers.get("user-agent")
        
        # Request verification token using unified service
        token_info = await auth_token_service.create_email_verification_token(
            conn=db,
            user_id=current_user.user_id,
            email=request_data.email,
            ip_address=client_ip,
            user_agent=user_agent
        )
        
        # Send verification email
        email_sent = await email_service.send_email_verification(
            email=request_data.email,
            verification_token=token_info['token'],
            language=request_data.language
        )
        
        if not email_sent:
            logger.warning(f"Failed to send verification email to {request_data.email}")
        
        return {
            "message": "Verification email sent successfully",
            "email": request_data.email,
            "expires_at": token_info['expires_at'].isoformat()
        }
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Email verification request error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send verification email"
        )


@router.post("/email/verification/confirm", response_model=dict)
async def confirm_email_verification(
    confirm_data: EmailVerificationConfirm,
    request: Request,
    db: Annotated[asyncpg.Connection, Depends(get_database)]
):
    """
    Confirm email verification with token.
    
    This endpoint verifies the email using the token sent to the user's email.
    """
    try:
        auth_token_service = get_auth_token_service()
        
        # Get client IP
        ip_address = request.client.host if request.client else None
        
        # Verify email using unified service
        success = await auth_token_service.verify_email_with_token(
            conn=db,
            token=confirm_data.token,
            ip_address=ip_address
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired verification token"
            )
        
        logger.info("Email verification completed successfully")
        return {
            "message": "Email verified successfully",
            "verified": True
        }
        
    except HTTPException:
        raise
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Email verification error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to verify email"
        )


@router.get("/email/config-check", response_model=Dict[str, Any])
async def check_email_configuration(
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    """
    Check email configuration status (managers only).
    
    Returns current email configuration status for debugging.
    """
    # Check if user is a manager
    if UserRoleType.MANAGER not in current_user.roles:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only managers can check email configuration"
        )
    
    # Get raw environment variables
    raw_env = {
        "SMTP_HOST": os.environ.get('SMTP_HOST', 'Not set'),
        "SMTP_PORT": os.environ.get('SMTP_PORT', 'Not set'),
        "SMTP_USERNAME": os.environ.get('SMTP_USERNAME', 'Not set'),
        "SMTP_PASSWORD": "Set" if os.environ.get('SMTP_PASSWORD') else "Not set",
        "SMTP_TLS": os.environ.get('SMTP_TLS', 'Not set'),
        "RAILWAY_ENVIRONMENT": os.environ.get('RAILWAY_ENVIRONMENT', 'Not set'),
        "ENVIRONMENT": os.environ.get('ENVIRONMENT', 'Not set')
    }
    
    # Force reload settings to get fresh values
    from app.config.settings import reload_settings, find_env_file
    fresh_settings = reload_settings()
    
    # Get current configuration
    config_status = {
        "raw_env_vars": raw_env,
        "env_file_detected": find_env_file(),
        "loaded_settings": {
            "smtp_configured": bool(fresh_settings.SMTP_HOST),
            "smtp_host": fresh_settings.SMTP_HOST if fresh_settings.SMTP_HOST else "Not configured",
            "smtp_port": fresh_settings.SMTP_PORT,
            "smtp_username": fresh_settings.SMTP_USERNAME if fresh_settings.SMTP_USERNAME else "Not configured",
            "smtp_tls": fresh_settings.SMTP_TLS,
            "environment": fresh_settings.ENVIRONMENT,
        },
        "env_vars_present": {
            "SMTP_HOST": "SMTP_HOST" in os.environ,
            "SMTP_PORT": "SMTP_PORT" in os.environ,
            "SMTP_USERNAME": "SMTP_USERNAME" in os.environ,
            "SMTP_PASSWORD": "SMTP_PASSWORD" in os.environ
        }
    }
    
    # Test email service initialization
    try:
        # Clear email service singleton to force reinitialization
        from app.services import email_service as email_module
        email_module._email_service = None
        
        email_service = get_email_service()
        config_status["email_service_initialized"] = True
        config_status["email_service_ready"] = bool(email_service)
    except Exception as e:
        config_status["email_service_initialized"] = False
        config_status["email_service_error"] = str(e)
        import traceback
        config_status["email_service_traceback"] = traceback.format_exc()
    
    logger.info(f"Email configuration check by {current_user.email}: {config_status}")
    
    return config_status


@router.post("/email/test-send", response_model=Dict[str, Any])
async def test_email_send(
    email_data: Dict[str, str],
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    """
    Test sending an email directly (managers only).
    
    This endpoint bypasses background tasks to test email sending directly.
    """
    # Check if user is a manager
    if UserRoleType.MANAGER not in current_user.roles:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only managers can test email sending"
        )
    
    to_email = email_data.get("to_email", current_user.email)
    subject = email_data.get("subject", "Test Email from TutorAide")
    
    logger.info(f"=== DIRECT EMAIL TEST ===")
    logger.info(f"Sending test email to: {to_email}")
    logger.info(f"Environment: {os.environ.get('ENVIRONMENT', 'Not set')}")
    logger.info(f"Railway: {os.environ.get('RAILWAY_ENVIRONMENT', 'Not set')}")
    
    try:
        # Clear and reinitialize email service
        from app.services import email_service as email_module
        email_module._email_service = None
        
        # Get fresh email service
        email_service = get_email_service()
        
        # Create a simple test context
        test_context = {
            "user_email": to_email,
            "test_message": "This is a test email from TutorAide.",
            "timestamp": now_est().strftime("%Y-%m-%d %H:%M:%S EST")
        }
        
        # Send test email
        success = await email_service.send_email(
            to_email=to_email,
            subject=subject,
            template_name="test",  # We'll need to create this template
            context=test_context,
            language="en"
        )
        
        result = {
            "success": success,
            "email_sent_to": to_email,
            "smtp_config": {
                "host": settings.SMTP_HOST,
                "port": settings.SMTP_PORT,
                "username": settings.SMTP_USERNAME
            },
            "timestamp": now_est().isoformat()
        }
        
        logger.info(f"Test email result: {result}")
        
        return result
        
    except Exception as e:
        logger.error(f"Test email failed: {type(e).__name__}: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "email_sent_to": to_email,
            "timestamp": now_est().isoformat()
        }


@router.post("/password/reset-request-sync", response_model=Dict[str, str])
async def request_password_reset_sync(
    reset_request: PasswordResetRequest,
    request: Request,
    db: Annotated[asyncpg.Connection, Depends(get_database)]
):
    """
    Request a password reset link (synchronous version for debugging).
    
    This endpoint sends the email synchronously instead of using background tasks.
    """
    logger.info("=== SYNC PASSWORD RESET REQUEST ===")
    
    try:
        client_ip = str(request.client.host) if request.client else '127.0.0.1'
        auth_token_service = get_auth_token_service()
        
        # Request reset token
        result = await auth_token_service.create_password_reset_token(
            conn=db,
            email=reset_request.email,
            ip_address=client_ip,
            user_agent=request.headers.get("User-Agent")
        )
        
        if result and "token" in result:
            logger.info(f"Token created for {result['email']}, sending email synchronously...")
            
            # Get language
            language = request.headers.get("Accept-Language", "en")[:2]
            if language not in ["en", "fr"]:
                language = "en"
            
            # Clear and get fresh email service
            from app.services import email_service as email_module
            email_module._email_service = None
            email_service = get_email_service()
            
            # Send email synchronously
            logger.info(f"Sending password reset email to {result['email']}")
            success = await email_service.send_password_reset_email(
                email=result["email"],
                reset_token=result["token"],
                language=language
            )
            
            logger.info(f"Email send result: {success}")
            
            if success:
                return {"message": "Password reset email sent successfully"}
            else:
                return {"message": "Failed to send email, check logs"}
        
        return {"message": "If the email exists, a password reset link has been sent"}
        
    except Exception as e:
        logger.error(f"Sync password reset error: {type(e).__name__}: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return {"message": "Error occurred, check logs"}


@router.get("/test-background-task")
async def test_background_task(background_tasks: BackgroundTasks):
    """
    Test if background tasks execute in Railway.
    
    This is a simple test endpoint to verify BackgroundTasks work.
    """
    logger.info("=== TEST BACKGROUND TASK ENDPOINT CALLED ===")
    
    async def simple_background_task():
        logger.info("=== BACKGROUND TASK EXECUTED! ===")
        logger.info("Background tasks are working correctly")
        logger.info("=== BACKGROUND TASK FINISHED ===")
    
    background_tasks.add_task(simple_background_task)
    logger.info("Background task has been queued")
    
    return {"message": "Background task queued, check logs for execution"}


@router.get("/debug-logs")
async def debug_logs():
    """Test endpoint to verify logging is working."""
    import logging
    import sys
    
    # Try different logging methods
    print("=== DEBUG LOGS TEST ===", file=sys.stdout)
    print("1. Print to stdout")
    
    logger.info("2. Logger.info message")
    logger.warning("3. Logger.warning message")
    logger.error("4. Logger.error message")
    
    # Direct logging
    logging.info("5. Direct logging.info")
    logging.warning("6. Direct logging.warning")
    logging.error("7. Direct logging.error")
    
    # Check logger configuration
    root_logger = logging.getLogger()
    app_logger = logging.getLogger("app")
    
    return {
        "message": "Check logs for debug output",
        "root_logger_level": logging.getLevelName(root_logger.level),
        "root_handlers": [str(h) for h in root_logger.handlers],
        "app_logger_level": logging.getLevelName(app_logger.level) if app_logger else "No app logger",
        "environment": os.environ.get("ENVIRONMENT", "not set"),
        "railway_env": os.environ.get("RAILWAY_ENVIRONMENT", "not set")
    }


@router.post("/test-email-direct")
async def test_email_direct(
    email_data: Dict[str, str]
):
    """
    Test email sending directly without any user validation.
    
    This is a temporary endpoint for debugging email issues.
    """
    to_email = email_data.get("email", "<EMAIL>")
    
    # Also print to stdout to ensure it shows in logs
    print(f"=== DIRECT EMAIL TEST (NO AUTH) ===")
    print(f"Testing email to: {to_email}")
    logger.info(f"=== DIRECT EMAIL TEST (NO AUTH) ===")
    logger.info(f"Testing email to: {to_email}")
    
    try:
        # Get email service
        email_service = get_email_service()
        
        # Generate a test token
        test_token = "test-token-12345"
        
        # Send password reset email
        success = await email_service.send_password_reset_email(
            email=to_email,
            reset_token=test_token,
            language="en"
        )
        
        print(f"Email send result: {success}")
        logger.info(f"Email send result: {success}")
        
        return {
            "success": success,
            "email": to_email,
            "message": "Check logs for details"
        }
        
    except Exception as e:
        error_msg = f"Direct email test failed: {type(e).__name__}: {str(e)}"
        print(error_msg)
        logger.error(error_msg)
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        
        return {
            "success": False,
            "error": str(e),
            "email": to_email
        }


@router.get("/debug/validate-token/{token}")
async def debug_validate_token(
    token: str,
    db: Annotated[asyncpg.Connection, Depends(get_database)]
):
    """
    Debug endpoint to test token validation directly.
    
    This bypasses any frontend issues and tests the backend validation.
    """
    logger.info("=== DEBUG TOKEN VALIDATION ===")
    logger.info(f"Token received: {token}")
    logger.info(f"Token length: {len(token)}")
    logger.info(f"Token first 10 chars: {token[:10]}")
    logger.info(f"Token last 10 chars: {token[-10:]}")
    
    try:
        # First, let's see what's in the database
        query = """
            SELECT token_id, user_id, token_hash, expires_at, used_at, created_at
            FROM auth_tokens 
            WHERE token_type = 'password_reset' 
            ORDER BY created_at DESC 
            LIMIT 5
        """
        rows = await db.fetch(query)
        logger.info("Recent password reset tokens in DB:")
        for row in rows:
            logger.info(f"  ID: {row['token_id']}, User: {row['user_id']}, "
                       f"Hash: {row['token_hash'][:20]}..., "
                       f"Expires: {row['expires_at']}, Used: {row['used_at']}")
        
        # Calculate the hash of the provided token
        import hashlib
        token_hash = hashlib.sha256(token.encode()).hexdigest()
        logger.info(f"Calculated token hash: {token_hash}")
        
        # Try to find this exact hash
        exact_query = "SELECT * FROM auth_tokens WHERE token_hash = $1"
        exact_row = await db.fetchrow(exact_query, token_hash)
        
        if exact_row:
            logger.info(f"Token found! ID: {exact_row['token_id']}, Type: {exact_row['token_type']}")
        else:
            logger.info("Token hash not found in database")
        
        # Now try the service validation
        auth_token_service = get_auth_token_service()
        logger.info("Calling validate_password_reset_token...")
        
        user = await auth_token_service.validate_password_reset_token(db, token)
        
        if user:
            return {
                "valid": True,
                "email": user.email,
                "user_id": user.user_id,
                "debug_info": {
                    "token_length": len(token),
                    "token_hash": token_hash,
                    "found_in_db": exact_row is not None
                }
            }
        else:
            return {
                "valid": False,
                "error": "Token validation failed",
                "debug_info": {
                    "token_length": len(token),
                    "token_hash": token_hash,
                    "found_in_db": exact_row is not None
                }
            }
            
    except Exception as e:
        logger.error(f"Debug validation error: {type(e).__name__}: {e}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        
        return {
            "valid": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "debug_info": {
                "token_length": len(token),
                "traceback": traceback.format_exc()
            }
        }


@router.get("/google/check-config")
async def check_google_config():
    """Debug endpoint to check Google OAuth configuration."""
    return {
        "client_id": settings.GOOGLE_CLIENT_ID[:20] + "..." if settings.GOOGLE_CLIENT_ID else "Not set",
        "has_secret": bool(settings.GOOGLE_CLIENT_SECRET),
        "redirect_uri": settings.GOOGLE_REDIRECT_URI,
        "frontend_url": settings.FRONTEND_URL
    }


@router.get("/google/login")
async def google_login(
    redirect_uri: Optional[str] = Query(None, description="Optional custom redirect URI"),
    role: Optional[UserRoleType] = Query(UserRoleType.CLIENT, description="Role for new users")
):
    """
    Initiate Google OAuth login flow.
    
    Redirects user to Google's OAuth consent screen.
    After authentication, Google will redirect back to the callback URL.
    
    - **redirect_uri**: Optional custom redirect URI (defaults to configured value)
    - **role**: Role to assign to new users (defaults to CLIENT)
    """
    try:
        google_oauth = GoogleOAuthService()
        
        # Generate state token with role information
        state_data = {"role": role.value if role else UserRoleType.CLIENT.value}
        state = google_oauth.generate_state_token(state_data)
        
        # Get authorization URL
        auth_url = google_oauth.get_authorization_url(state, redirect_uri)
        
        logger.info(f"Initiating Google OAuth flow with state: {state}")
        
        # Redirect to Google
        return RedirectResponse(url=auth_url)
        
    except Exception as e:
        logger.error(f"Google OAuth initiation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to initiate Google authentication"
        )


@router.get("/google/callback")
async def google_callback(
    code: str = Query(..., description="Authorization code from Google"),
    state: str = Query(..., description="State token for CSRF protection"),
    error: Optional[str] = Query(None, description="Error from Google if authentication failed"),
    db: asyncpg.Connection = Depends(get_database),
    request: Request = None
):
    """
    Handle Google OAuth callback.
    
    This endpoint is called by Google after user authentication.
    It exchanges the authorization code for tokens and authenticates the user.
    
    - **code**: Authorization code from Google
    - **state**: State token for CSRF protection
    - **error**: Error message if authentication failed
    """
    # Handle errors from Google
    if error:
        logger.error(f"Google OAuth error: {error}")
        # Redirect to frontend with error
        error_params = urlencode({"error": "google_auth_failed", "message": error})
        return RedirectResponse(url=f"{settings.FRONTEND_URL}/login?{error_params}")
    
    try:
        google_oauth = GoogleOAuthService()
        
        logger.info(f"=== GOOGLE OAUTH CALLBACK ===")
        logger.info(f"State token: {state[:10]}...")
        logger.info(f"Code: {code[:10]}...")
        logger.info(f"Environment: {settings.ENVIRONMENT}")
        
        # Verify state token
        if not google_oauth.verify_state_token(state):
            logger.error(f"State token verification failed: {state[:10]}...")
            raise AuthenticationError("Invalid state token")
        
        logger.info("State token verified successfully")
        
        # Get state data (including role)
        state_data = google_oauth.get_state_data(state) or {}
        role_value = state_data.get("role", UserRoleType.CLIENT.value)
        logger.info(f"State data retrieved: {state_data}")
        
        try:
            role = UserRoleType(role_value)
        except ValueError:
            role = UserRoleType.CLIENT
        
        # Step 1: Exchange code for tokens
        logger.info("Step 1: Exchanging authorization code for tokens...")
        try:
            tokens = await google_oauth.exchange_code_for_tokens(code)
            logger.info("✓ Token exchange successful")
        except Exception as e:
            logger.error(f"✗ Token exchange failed: {type(e).__name__}: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            raise AuthenticationError(f"Failed to exchange authorization code: {str(e)}")
        
        # Step 2: Get user info from Google
        logger.info("Step 2: Getting user info from Google...")
        try:
            google_user_info = await google_oauth.get_user_info(tokens["access_token"])
            logger.info(f"✓ Got user info for: {google_user_info.get('email', 'unknown')}")
            logger.debug(f"Google user info: {google_user_info}")
        except Exception as e:
            logger.error(f"✗ Failed to get user info: {type(e).__name__}: {str(e)}")
            raise AuthenticationError(f"Failed to get user info from Google: {str(e)}")
        
        # Get user's IP address for consent tracking
        ip_address = None
        if request:
            ip_address = request.client.host if request.client else None
        
        # Step 3: Authenticate or create user
        logger.info("Step 3: Authenticating/creating user in database...")
        try:
            auth_response = await google_oauth.authenticate_user(
                db,
                google_user_info,
                roles=[role],
                ip_address=ip_address
            )
            logger.info(f"✓ User authenticated successfully")
        except Exception as e:
            logger.error(f"✗ User authentication failed: {type(e).__name__}: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            raise AuthenticationError(f"Failed to authenticate user: {str(e)}")
        
        # Step 4: Prepare tokens for redirect
        logger.info("Step 4: Preparing tokens for redirect...")
        try:
            success_params = urlencode({
                "access_token": auth_response.access_token,
                "refresh_token": auth_response.refresh_token,
                "token_type": auth_response.token_type,
                "expires_in": str(auth_response.expires_in)
            })
            logger.info(f"✓ Tokens prepared successfully")
        except Exception as e:
            logger.error(f"✗ Failed to prepare tokens: {type(e).__name__}: {str(e)}")
            raise AuthenticationError(f"Failed to prepare authentication tokens: {str(e)}")
        
        logger.info(f"=== GOOGLE OAUTH SUCCESS for {google_user_info['email']} ===")
        
        # Redirect to frontend with tokens
        return RedirectResponse(url=f"{settings.FRONTEND_URL}/auth/callback?{success_params}")
        
    except AuthenticationError as e:
        logger.error(f"Google OAuth authentication error: {e}")
        error_params = urlencode({"error": "authentication_failed", "message": str(e)})
        return RedirectResponse(url=f"{settings.FRONTEND_URL}/login?{error_params}")
        
    except Exception as e:
        logger.error(f"Google OAuth callback error: {e}")
        error_params = urlencode({"error": "oauth_callback_failed", "message": "Authentication failed"})
        return RedirectResponse(url=f"{settings.FRONTEND_URL}/login?{error_params}")


@router.post("/debug/password-reset-test", response_model=Dict[str, Any])
async def debug_password_reset_test(
    email_data: Dict[str, str],
    db: Annotated[asyncpg.Connection, Depends(get_database)]
):
    """
    Debug endpoint to test password reset flow step by step.
    
    This endpoint tests each step of the password reset process.
    """
    email = email_data.get("email", "<EMAIL>")
    results = {
        "email": email,
        "steps": {}
    }
    
    logger.info(f"=== DEBUG PASSWORD RESET TEST for {email} ===")
    
    # Step 1: Test user lookup
    try:
        from app.database.repositories.user_repository import UserRepository
        user_repo = UserRepository()
        user = await user_repo.find_by_email(db, email)
        
        if user:
            results["steps"]["user_lookup"] = {
                "success": True,
                "user_id": user.get("user_id"),
                "email_verified": user.get("email_verified"),
                "created_at": str(user.get("created_at"))
            }
            logger.info(f"✅ User found: ID={user.get('user_id')}")
        else:
            results["steps"]["user_lookup"] = {
                "success": False,
                "error": "User not found"
            }
            logger.warning(f"❌ User not found: {email}")
            return results
    except Exception as e:
        results["steps"]["user_lookup"] = {
            "success": False,
            "error": str(e),
            "type": type(e).__name__
        }
        logger.error(f"❌ User lookup failed: {e}")
        return results
    
    # Step 2: Test token creation
    try:
        from app.models.auth_token_models import AuthTokenType
        from datetime import datetime, timedelta
        
        # Create test token directly
        token_hash = f"debug_test_{datetime.now().timestamp()}"
        expires_at = datetime.now() + timedelta(hours=1)
        
        token_row = await db.fetchrow("""
            INSERT INTO auth_tokens (
                user_id, token_type, token_hash, expires_at,
                metadata, ip_address, user_agent
            ) VALUES ($1, $2, $3, $4, $5, $6, $7)
            RETURNING token_id, user_id, token_type, created_at
        """, 
            user["user_id"],
            "password_reset",
            token_hash,
            expires_at,
            {"email": email, "test": True},  # JSONB metadata
            "127.0.0.1",
            "Debug Test"
        )
        
        results["steps"]["token_creation"] = {
            "success": True,
            "token_id": token_row["token_id"],
            "created_at": str(token_row["created_at"])
        }
        logger.info(f"✅ Token created: ID={token_row['token_id']}")
        
    except Exception as e:
        results["steps"]["token_creation"] = {
            "success": False,
            "error": str(e),
            "type": type(e).__name__
        }
        logger.error(f"❌ Token creation failed: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
    
    # Step 3: Test email sending
    try:
        from app.services.email_service import get_email_service
        email_service = get_email_service()
        
        # Send test email
        success = await email_service.send_password_reset_email(
            email=email,
            reset_token="debug-test-token",
            language="en"
        )
        
        results["steps"]["email_sending"] = {
            "success": success,
            "smtp_host": settings.SMTP_HOST,
            "smtp_port": settings.SMTP_PORT,
            "smtp_username": settings.SMTP_USERNAME
        }
        logger.info(f"✅ Email send result: {success}")
        
    except Exception as e:
        results["steps"]["email_sending"] = {
            "success": False,
            "error": str(e),
            "type": type(e).__name__
        }
        logger.error(f"❌ Email sending failed: {e}")
    
    # Step 4: Check auth_tokens table
    try:
        count = await db.fetchval("""
            SELECT COUNT(*) FROM auth_tokens 
            WHERE user_id = $1 AND token_type = 'password_reset'
        """, user["user_id"])
        
        results["steps"]["token_count"] = {
            "success": True,
            "count": count
        }
        logger.info(f"✅ Token count for user: {count}")
        
    except Exception as e:
        results["steps"]["token_count"] = {
            "success": False,
            "error": str(e)
        }
    
    logger.info(f"=== DEBUG TEST COMPLETE ===")
    return results


@router.get("/db-check")
async def check_database_schema(
    db: Annotated[asyncpg.Connection, Depends(get_database)]
):
    """
    Check database schema for OAuth-related tables and columns.
    
    This endpoint verifies that all required tables and columns exist.
    """
    results = {
        "tables": {},
        "columns": {},
        "constraints": {},
        "summary": {
            "all_tables_exist": True,
            "all_columns_exist": True,
            "issues": []
        }
    }
    
    # Check if tables exist
    table_check_query = """
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name IN ('user_accounts', 'user_roles', 'auth_tokens', 'user_consents')
    """
    
    try:
        existing_tables = await db.fetch(table_check_query)
        existing_table_names = [row['table_name'] for row in existing_tables]
        
        for table in ['user_accounts', 'user_roles', 'auth_tokens', 'user_consents']:
            results['tables'][table] = table in existing_table_names
            if table not in existing_table_names:
                results['summary']['all_tables_exist'] = False
                results['summary']['issues'].append(f"Table '{table}' does not exist")
    except Exception as e:
        results['tables']['error'] = str(e)
        results['summary']['all_tables_exist'] = False
    
    # Check user_accounts columns
    if results['tables'].get('user_accounts', False):
        column_check_query = """
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns
            WHERE table_schema = 'public' 
            AND table_name = 'user_accounts'
        """
        
        try:
            columns = await db.fetch(column_check_query)
            column_info = {row['column_name']: {
                'data_type': row['data_type'],
                'is_nullable': row['is_nullable']
            } for row in columns}
            
            results['columns']['user_accounts'] = column_info
            
            # Check for required columns
            required_columns = ['user_id', 'email', 'password_hash', 'google_id', 'created_at', 'updated_at']
            for col in required_columns:
                if col not in column_info:
                    results['summary']['all_columns_exist'] = False
                    results['summary']['issues'].append(f"Column 'user_accounts.{col}' does not exist")
            
            # Check if is_email_verified exists (common issue)
            if 'is_email_verified' in column_info:
                results['columns']['has_email_verified'] = True
            else:
                results['columns']['has_email_verified'] = False
                results['summary']['issues'].append("Column 'is_email_verified' not found - this might cause issues")
                
        except Exception as e:
            results['columns']['user_accounts_error'] = str(e)
            results['summary']['all_columns_exist'] = False
    
    # Check auth_tokens table structure
    if results['tables'].get('auth_tokens', False):
        try:
            auth_token_columns = await db.fetch("""
                SELECT column_name, data_type
                FROM information_schema.columns
                WHERE table_schema = 'public' 
                AND table_name = 'auth_tokens'
            """)
            
            results['columns']['auth_tokens'] = {
                row['column_name']: row['data_type'] for row in auth_token_columns
            }
        except Exception as e:
            results['columns']['auth_tokens_error'] = str(e)
    
    # Check constraints
    try:
        constraint_check = await db.fetch("""
            SELECT 
                tc.table_name,
                tc.constraint_name,
                tc.constraint_type
            FROM information_schema.table_constraints tc
            WHERE tc.table_schema = 'public'
            AND tc.table_name IN ('user_accounts', 'user_roles', 'auth_tokens')
            ORDER BY tc.table_name, tc.constraint_type
        """)
        
        for row in constraint_check:
            table = row['table_name']
            if table not in results['constraints']:
                results['constraints'][table] = []
            results['constraints'][table].append({
                'name': row['constraint_name'],
                'type': row['constraint_type']
            })
    except Exception as e:
        results['constraints']['error'] = str(e)
    
    # Check for password_hash nullable
    if 'user_accounts' in results['columns']:
        password_col = results['columns']['user_accounts'].get('password_hash', {})
        if password_col.get('is_nullable') == 'NO':
            results['summary']['issues'].append("password_hash is NOT NULL - this will cause issues with Google OAuth users")
    
    return results


@router.post("/google/test-create")
async def test_google_user_creation(
    test_data: Dict[str, Any],
    db: Annotated[asyncpg.Connection, Depends(get_database)]
):
    """
    Test Google user creation without actual OAuth flow.
    
    This endpoint simulates creating a Google OAuth user to help debug issues.
    Required fields in test_data:
    - email: User's email address
    - google_id: Simulated Google ID (can be any unique string)
    """
    results = {
        "steps": {},
        "success": False,
        "user_id": None
    }
    
    email = test_data.get("email", "<EMAIL>")
    google_id = test_data.get("google_id", f"test_google_{int(time.time())}")
    
    logger.info(f"=== TEST GOOGLE USER CREATION ===")
    logger.info(f"Email: {email}, Google ID: {google_id}")
    
    # Step 1: Check if user exists
    try:
        from app.database.repositories.user_repository import UserRepository
        user_repo = UserRepository()
        
        existing_by_email = await user_repo.find_by_email(db, email)
        existing_by_google = await user_repo.find_by_google_id(db, google_id)
        
        results["steps"]["user_check"] = {
            "exists_by_email": bool(existing_by_email),
            "exists_by_google": bool(existing_by_google)
        }
        
        if existing_by_email or existing_by_google:
            results["steps"]["user_check"]["message"] = "User already exists"
            return results
            
    except Exception as e:
        results["steps"]["user_check"] = {
            "error": str(e),
            "type": type(e).__name__
        }
        return results
    
    # Step 2: Create user with Google ID
    try:
        # Direct insert to test database constraints
        user_row = await db.fetchrow("""
            INSERT INTO user_accounts (email, google_id, created_at, updated_at)
            VALUES ($1, $2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            RETURNING user_id, email, google_id, created_at
        """, email.lower(), google_id)
        
        results["steps"]["user_creation"] = {
            "success": True,
            "user_id": user_row["user_id"],
            "email": user_row["email"]
        }
        results["user_id"] = user_row["user_id"]
        
    except Exception as e:
        results["steps"]["user_creation"] = {
            "error": str(e),
            "type": type(e).__name__,
            "detail": "This error indicates database constraints or missing columns"
        }
        return results
    
    # Step 3: Add user role
    try:
        await db.execute("""
            INSERT INTO user_roles (user_id, role_type, is_active)
            VALUES ($1, 'client', true)
        """, user_row["user_id"])
        
        results["steps"]["role_assignment"] = {
            "success": True,
            "role": "client"
        }
        
    except Exception as e:
        results["steps"]["role_assignment"] = {
            "error": str(e),
            "type": type(e).__name__
        }
        # Continue even if role fails
    
    # Step 4: Test JWT token generation
    try:
        from app.core.security import create_access_token, create_refresh_token
        from app.models.user_models import UserRoleType
        
        access_token = create_access_token(
            subject=str(user_row["user_id"]),
            user_roles=["client"],
            active_role="client"
        )
        
        refresh_token = create_refresh_token(
            subject=str(user_row["user_id"]),
            user_roles=["client"]
        )
        
        results["steps"]["token_generation"] = {
            "success": True,
            "access_token": access_token[:20] + "...",
            "refresh_token": refresh_token[:20] + "..."
        }
        results["success"] = True
        
    except Exception as e:
        results["steps"]["token_generation"] = {
            "error": str(e),
            "type": type(e).__name__
        }
    
    # Cleanup - delete test user
    if results["user_id"] and test_data.get("cleanup", True):
        try:
            await db.execute("DELETE FROM user_accounts WHERE user_id = $1", results["user_id"])
            results["cleanup"] = "Test user deleted"
        except:
            results["cleanup"] = "Failed to delete test user"
    
    return results


@router.get("/db-test")
async def test_database_connection(
    db: Annotated[asyncpg.Connection, Depends(get_database)]
):
    """
    Test database connection and check what tables exist.
    
    This endpoint helps diagnose database connection issues.
    """
    try:
        results = {}
        
        # Test 1: Check current database
        results["current_database"] = await db.fetchval("SELECT current_database()")
        
        # Test 2: Check current user
        results["current_user"] = await db.fetchval("SELECT current_user")
        
        # Test 3: Check search_path
        results["search_path"] = await db.fetchval("SHOW search_path")
        
        # Test 4: List all schemas
        schemas = await db.fetch("SELECT schema_name FROM information_schema.schemata ORDER BY schema_name")
        results["schemas"] = [r['schema_name'] for r in schemas]
        
        # Test 5: Count tables in public schema
        results["public_tables_count"] = await db.fetchval("""
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_schema = 'public'
        """)
        
        # Test 6: List all tables in public schema
        tables = await db.fetch("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name
        """)
        results["public_tables"] = [r['table_name'] for r in tables]
        
        # Test 7: Try to query user_accounts directly
        try:
            count = await db.fetchval("SELECT COUNT(*) FROM user_accounts")
            results["user_accounts_count"] = count
            results["user_accounts_accessible"] = True
        except Exception as e:
            results["user_accounts_error"] = str(e)
            results["user_accounts_accessible"] = False
        
        # Test 8: Try with schema prefix
        try:
            count = await db.fetchval("SELECT COUNT(*) FROM public.user_accounts")
            results["public_user_accounts_count"] = count
            results["public_user_accounts_accessible"] = True
        except Exception as e:
            results["public_user_accounts_error"] = str(e)
            results["public_user_accounts_accessible"] = False
            
        return results
        
    except Exception as e:
        return {
            "error": str(e),
            "type": type(e).__name__
        }


@router.get("/google/debug")
async def google_oauth_debug():
    """
    Debug endpoint to verify Google OAuth configuration.
    
    This endpoint shows the current OAuth configuration being used by the application.
    Use this to verify that environment variables are properly loaded.
    """
    import os
    from app.config.settings import settings, reload_settings
    
    # Force reload settings to get fresh values
    fresh_settings = reload_settings()
    
    # Get raw environment variables
    raw_env = {
        "GOOGLE_CLIENT_ID": os.environ.get('GOOGLE_CLIENT_ID', 'Not set'),
        "GOOGLE_CLIENT_SECRET": "***" if os.environ.get('GOOGLE_CLIENT_SECRET') else "Not set",
        "GOOGLE_REDIRECT_URI": os.environ.get('GOOGLE_REDIRECT_URI', 'Not set'),
        "FRONTEND_URL": os.environ.get('FRONTEND_URL', 'Not set'),
        "RAILWAY_ENVIRONMENT": os.environ.get('RAILWAY_ENVIRONMENT', 'Not set'),
        "ENVIRONMENT": os.environ.get('ENVIRONMENT', 'Not set')
    }
    
    # Get settings values
    settings_values = {
        "GOOGLE_CLIENT_ID": fresh_settings.GOOGLE_CLIENT_ID or "Not configured",
        "GOOGLE_CLIENT_SECRET": "***" if fresh_settings.GOOGLE_CLIENT_SECRET else "Not configured",
        "GOOGLE_REDIRECT_URI": fresh_settings.GOOGLE_REDIRECT_URI or "Not configured",
        "FRONTEND_URL": fresh_settings.FRONTEND_URL,
        "ENVIRONMENT": fresh_settings.ENVIRONMENT
    }
    
    # Check if OAuth is properly configured
    oauth_configured = bool(
        fresh_settings.GOOGLE_CLIENT_ID and 
        fresh_settings.GOOGLE_CLIENT_SECRET and 
        fresh_settings.GOOGLE_REDIRECT_URI
    )
    
    # Generate test OAuth URL
    test_oauth_url = None
    if oauth_configured:
        from app.services.google_oauth_service import GoogleOAuthService
        try:
            google_oauth = GoogleOAuthService()
            state = "test_state_123"
            test_oauth_url = google_oauth.get_authorization_url(state)
        except Exception as e:
            test_oauth_url = f"Error generating URL: {str(e)}"
    
    return {
        "status": "OAuth Debug Information",
        "oauth_configured": oauth_configured,
        "raw_environment_variables": raw_env,
        "loaded_settings": settings_values,
        "test_oauth_url": test_oauth_url,
        "instructions": {
            "1": "Check if GOOGLE_CLIENT_ID matches your new OAuth client ID",
            "2": "Verify GOOGLE_REDIRECT_URI matches what's configured in Google Cloud Console",
            "3": "If values show 'Not set', update them in Railway environment variables",
            "4": "After updating Railway variables, redeploy the application",
            "5": "Visit the test_oauth_url to see if it shows the correct client_id parameter"
        },
        "google_cloud_console": {
            "url": "https://console.cloud.google.com/apis/credentials",
            "steps": [
                "Go to APIs & Services > Credentials",
                "Create or edit OAuth 2.0 Client ID",
                "Add authorized redirect URI: " + (fresh_settings.GOOGLE_REDIRECT_URI or "[YOUR_DOMAIN]/api/v1/auth/google/callback"),
                "Add authorized JavaScript origin: " + (fresh_settings.FRONTEND_URL or "[YOUR_DOMAIN]"),
                "Save and copy the new Client ID and Secret"
            ]
        }
    }


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: Annotated[User, Depends(get_current_active_user)],
    db: Annotated[asyncpg.Connection, Depends(get_database)],
    auth_service: Annotated[AuthService, Depends(get_auth_service)]
):
    """
    Get current user information.
    
    Returns the authenticated user's profile information.
    """
    try:
        # Get full user profile
        user_profile = await auth_service.get_user_profile(db, current_user.user_id)
        
        # Get active roles
        active_roles = user_profile.get_active_roles()
        
        # Get the primary/active role (first role in the list)
        active_role = active_roles[0] if active_roles else UserRoleType.CLIENT
        
        response = UserResponse(
            user_id=user_profile.user.user_id,
            email=user_profile.user.email,
            is_email_verified=user_profile.user.is_email_verified,
            has_local_auth=user_profile.user.has_local_auth(),
            has_google_auth=user_profile.user.has_google_auth(),
            roles=active_roles,
            active_role=active_role,
            created_at=user_profile.user.created_at,
            updated_at=user_profile.user.updated_at
        )
        
        return response
        
    except Exception as e:
        logger.error(f"Error getting user info: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user information"
        )


# Keep other endpoints (login, refresh, etc.) as they are