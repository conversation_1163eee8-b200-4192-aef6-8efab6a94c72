import React from 'react';
import { Outlet } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { TabNavigation, Tab } from '../../components/layout/TabNavigation';
import { DollarSign, TrendingUp, BarChart3, <PERSON><PERSON><PERSON>, FileBar<PERSON>hart } from 'lucide-react';

const ReportsLayout: React.FC = () => {
  const { t } = useTranslation();

  const tabs: Tab[] = [
    {
      id: 'financial',
      label: t('sidebar.financial'),
      path: '/reports/financial',
      icon: <DollarSign />,
    },
    {
      id: 'performance',
      label: t('sidebar.performance'),
      path: '/reports/performance',
      icon: <TrendingUp />,
    },
    {
      id: 'analytics',
      label: t('sidebar.analytics'),
      path: '/reports/analytics',
      icon: <BarChart3 />,
    },
    {
      id: 'usage',
      label: t('sidebar.usage'),
      path: '/reports/usage',
      icon: <PieChart />,
    },
    {
      id: 'monthly',
      label: t('sidebar.monthly'),
      path: '/reports/monthly',
      icon: <FileBarChart />,
    },
  ];

  return (
    <div className="h-full flex flex-col">
      <TabNavigation tabs={tabs} />
      <div className="flex-1 overflow-hidden">
        <Outlet />
      </div>
    </div>
  );
};

export default ReportsLayout;