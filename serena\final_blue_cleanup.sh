#!/bin/bash

# Final cleanup script to replace remaining blue colors

FRONTEND_PATH="/mnt/c/Users/<USER>/OneDrive/Documents/tutoraide_app/app/frontend/src"

echo "Performing final blue color cleanup..."

# Fix hover states from blue to red
find "$FRONTEND_PATH" -name "*.tsx" -o -name "*.jsx" | xargs sed -i 's/hover:bg-blue-700/hover:bg-red-700/g'
find "$FRONTEND_PATH" -name "*.tsx" -o -name "*.jsx" | xargs sed -i 's/hover:bg-blue-600/hover:bg-red-600/g'
find "$FRONTEND_PATH" -name "*.tsx" -o -name "*.jsx" | xargs sed -i 's/hover:bg-blue-500/hover:bg-red-500/g'
find "$FRONTEND_PATH" -name "*.tsx" -o -name "*.jsx" | xargs sed -i 's/hover:text-blue-900/hover:text-red-900/g'
find "$FRONTEND_PATH" -name "*.tsx" -o -name "*.jsx" | xargs sed -i 's/hover:text-blue-800/hover:text-red-800/g'

# Fix focus rings from blue to red
find "$FRONTEND_PATH" -name "*.tsx" -o -name "*.jsx" | xargs sed -i 's/focus:ring-blue-500/focus:ring-red-500/g'
find "$FRONTEND_PATH" -name "*.tsx" -o -name "*.jsx" | xargs sed -i 's/focus:border-blue-500/focus:border-red-500/g'
find "$FRONTEND_PATH" -name "*.tsx" -o -name "*.jsx" | xargs sed -i 's/focus:ring-2 focus:ring-blue-500/focus:ring-2 focus:ring-red-500/g'

# Fix gradients - change to red gradient
sed -i 's/from-blue-600 to-blue-700/from-red-600 to-red-700/g' "$FRONTEND_PATH/components/billing/InvoiceDisplay.tsx"

# Leave debug panel colors as-is (they're for debugging purposes)
echo "Note: Debug panel colors left as blue for visibility"

echo ""
echo "Final cleanup completed!"
echo ""

# Show remaining blue instances for manual review
echo "Remaining blue color instances (excluding debug files):"
grep -r "blue-" "$FRONTEND_PATH" --include="*.tsx" --include="*.jsx" | grep -v "debug" | grep -v "Debug" | wc -l