import api from './api';

// Geocoding Types and Interfaces
export interface Coordinates {
  latitude: number;
  longitude: number;
}

export interface GeocodeRequest {
  postal_code: string;
  country?: string; // Default: 'CA'
}

export interface GeocodeResponse {
  postal_code: string;
  latitude: number;
  longitude: number;
  city?: string;
  province?: string;
  area?: string;
  source?: string;
  formatted_address?: string;
  confidence?: number;
}

export interface BatchGeocodeRequest {
  postal_codes: string[];
  country?: string;
}

export interface BatchGeocodeResponse {
  results: Record<string, GeocodeResponse | null>;
  success_count: number;
  error_count: number;
}

export interface ReverseGeocodeRequest {
  latitude: number;
  longitude: number;
}

export interface ReverseGeocodeResponse {
  postal_code?: string;
  city?: string;
  province?: string;
  country?: string;
  formatted_address?: string;
  neighborhood?: string;
  street?: string;
}

export interface LocationValidationRequest {
  postal_code: string;
  country?: string;
}

export interface LocationValidationResponse {
  is_valid: boolean;
  formatted_postal_code?: string;
  suggested_postal_codes?: string[];
  error?: string;
}

export interface LocationBounds {
  northeast: Coordinates;
  southwest: Coordinates;
}

export interface NearbyLocation {
  postal_code: string;
  distance_km: number;
  city?: string;
  province?: string;
}

export interface GeocacheSummary {
  total_cached: number;
  cache_hits: number;
  cache_misses: number;
  last_updated: string;
}

class GeocodingService {
  private cache = new Map<string, GeocodeResponse>();
  private cacheExpiry = 24 * 60 * 60 * 1000; // 24 hours

  /**
   * Geocode a single postal code
   */
  async geocode(request: GeocodeRequest): Promise<GeocodeResponse> {
    try {
      // Check cache first
      const cacheKey = `${request.postal_code}_${request.country || 'CA'}`;
      const cached = this.getFromCache(cacheKey);
      if (cached) {
        return cached;
      }

      const response = await api.post<GeocodeResponse>('/geocoding/geocode', request);
      
      // Cache the result
      this.addToCache(cacheKey, response.data);
      
      return response.data;
    } catch (error: any) {
      console.error('Error geocoding postal code:', error);
      throw new Error(error.response?.data?.detail || 'Failed to geocode postal code');
    }
  }

  /**
   * Batch geocode multiple postal codes
   */
  async batchGeocode(request: BatchGeocodeRequest): Promise<BatchGeocodeResponse> {
    try {
      // Check cache for any already geocoded postal codes
      const uncachedPostalCodes: string[] = [];
      const cachedResults: Record<string, GeocodeResponse> = {};
      
      request.postal_codes.forEach(postalCode => {
        const cacheKey = `${postalCode}_${request.country || 'CA'}`;
        const cached = this.getFromCache(cacheKey);
        if (cached) {
          cachedResults[postalCode] = cached;
        } else {
          uncachedPostalCodes.push(postalCode);
        }
      });

      // Only request uncached postal codes
      if (uncachedPostalCodes.length === 0) {
        return {
          results: cachedResults,
          success_count: Object.keys(cachedResults).length,
          error_count: 0
        };
      }

      const response = await api.post<BatchGeocodeResponse>('/geocoding/batch', {
        ...request,
        postal_codes: uncachedPostalCodes
      });

      // Cache new results
      Object.entries(response.data.results).forEach(([postalCode, result]) => {
        if (result) {
          const cacheKey = `${postalCode}_${request.country || 'CA'}`;
          this.addToCache(cacheKey, result);
        }
      });

      // Combine cached and new results
      return {
        results: { ...cachedResults, ...response.data.results },
        success_count: response.data.success_count + Object.keys(cachedResults).length,
        error_count: response.data.error_count
      };
    } catch (error: any) {
      console.error('Error batch geocoding:', error);
      throw new Error(error.response?.data?.detail || 'Failed to batch geocode');
    }
  }

  /**
   * Reverse geocode coordinates to location
   */
  async reverseGeocode(request: ReverseGeocodeRequest): Promise<ReverseGeocodeResponse> {
    try {
      const response = await api.post<ReverseGeocodeResponse>('/geocoding/reverse', request);
      return response.data;
    } catch (error: any) {
      console.error('Error reverse geocoding:', error);
      throw new Error(error.response?.data?.detail || 'Failed to reverse geocode');
    }
  }

  /**
   * Validate postal code format
   */
  async validatePostalCode(request: LocationValidationRequest): Promise<LocationValidationResponse> {
    try {
      const response = await api.post<LocationValidationResponse>('/geocoding/validate', request);
      return response.data;
    } catch (error: any) {
      console.error('Error validating postal code:', error);
      throw new Error(error.response?.data?.detail || 'Failed to validate postal code');
    }
  }

  /**
   * Get nearby postal codes
   */
  async getNearbyPostalCodes(
    postalCode: string, 
    radiusKm: number = 10, 
    limit: number = 10
  ): Promise<NearbyLocation[]> {
    try {
      const response = await api.get<{ locations: NearbyLocation[] }>('/geocoding/nearby', {
        params: { postal_code: postalCode, radius_km: radiusKm, limit }
      });
      return response.data.locations;
    } catch (error: any) {
      console.error('Error getting nearby postal codes:', error);
      throw new Error(error.response?.data?.detail || 'Failed to get nearby postal codes');
    }
  }

  /**
   * Get geocache summary
   */
  async getCacheSummary(): Promise<GeocacheSummary> {
    try {
      const response = await api.get<GeocacheSummary>('/geocoding/cache-summary');
      return response.data;
    } catch (error: any) {
      console.error('Error getting cache summary:', error);
      throw new Error(error.response?.data?.detail || 'Failed to get cache summary');
    }
  }

  /**
   * Clear geocoding cache
   */
  async clearCache(): Promise<{ message: string }> {
    try {
      this.cache.clear();
      const response = await api.post<{ message: string }>('/geocoding/clear-cache');
      return response.data;
    } catch (error: any) {
      console.error('Error clearing cache:', error);
      throw new Error(error.response?.data?.detail || 'Failed to clear cache');
    }
  }

  /**
   * Format postal code for display
   */
  formatPostalCode(postalCode: string, country: string = 'CA'): string {
    if (country === 'CA') {
      // Format Canadian postal code: A1A 1A1
      const cleaned = postalCode.toUpperCase().replace(/\s+/g, '');
      if (cleaned.length === 6) {
        return `${cleaned.slice(0, 3)} ${cleaned.slice(3)}`;
      }
    }
    return postalCode.toUpperCase();
  }

  /**
   * Validate Canadian postal code format
   */
  isValidCanadianPostalCode(postalCode: string): boolean {
    const pattern = /^[A-Z]\d[A-Z]\s?\d[A-Z]\d$/i;
    return pattern.test(postalCode);
  }

  /**
   * Calculate distance between two coordinates
   */
  calculateDistance(coord1: Coordinates, coord2: Coordinates): number {
    const R = 6371; // Earth's radius in km
    const dLat = this.toRad(coord2.latitude - coord1.latitude);
    const dLon = this.toRad(coord2.longitude - coord1.longitude);
    
    const a = 
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRad(coord1.latitude)) * 
      Math.cos(this.toRad(coord2.latitude)) * 
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;
    
    return Math.round(distance * 10) / 10; // Round to 1 decimal place
  }

  /**
   * Convert degrees to radians
   */
  private toRad(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * Get from cache
   */
  private getFromCache(key: string): GeocodeResponse | null {
    const cached = this.cache.get(key);
    if (cached) {
      const cacheTime = new Date(cached.source || '').getTime();
      if (Date.now() - cacheTime < this.cacheExpiry) {
        return cached;
      }
      this.cache.delete(key);
    }
    return null;
  }

  /**
   * Add to cache
   */
  private addToCache(key: string, data: GeocodeResponse): void {
    this.cache.set(key, {
      ...data,
      source: new Date().toISOString()
    });
  }

  /**
   * Get province name from code
   */
  getProvinceName(code: string): string {
    const provinces: Record<string, string> = {
      'AB': 'Alberta',
      'BC': 'British Columbia',
      'MB': 'Manitoba',
      'NB': 'New Brunswick',
      'NL': 'Newfoundland and Labrador',
      'NS': 'Nova Scotia',
      'NT': 'Northwest Territories',
      'NU': 'Nunavut',
      'ON': 'Ontario',
      'PE': 'Prince Edward Island',
      'QC': 'Quebec',
      'SK': 'Saskatchewan',
      'YT': 'Yukon'
    };

    return provinces[code.toUpperCase()] || code;
  }

  /**
   * Get map URL for coordinates
   */
  getMapUrl(coordinates: Coordinates, zoom: number = 15): string {
    return `https://www.google.com/maps/@${coordinates.latitude},${coordinates.longitude},${zoom}z`;
  }

  /**
   * Get static map image URL
   */
  getStaticMapUrl(
    coordinates: Coordinates, 
    size: { width: number; height: number } = { width: 400, height: 300 },
    zoom: number = 14
  ): string {
    // This would use a map service API key in production
    return `https://maps.googleapis.com/maps/api/staticmap?center=${coordinates.latitude},${coordinates.longitude}&zoom=${zoom}&size=${size.width}x${size.height}&markers=${coordinates.latitude},${coordinates.longitude}`;
  }
}

export const geocodingService = new GeocodingService();