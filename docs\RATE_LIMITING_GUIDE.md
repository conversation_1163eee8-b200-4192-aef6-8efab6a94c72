# TutorAide Rate Limiting & Brute Force Protection Guide

## Overview

TutorAide implements a comprehensive rate limiting and brute force protection system to secure authentication endpoints against various attack vectors. The system features progressive delays, IP-based tracking, and intelligent attack detection.

## Key Features

### 1. Progressive Delay System
After failed login attempts, users experience increasing delays:
- 1st failed attempt: 1 second delay
- 2nd failed attempt: 2 seconds delay
- 3rd failed attempt: 4 seconds delay
- 4th failed attempt: 8 seconds delay
- 5th failed attempt: 16 seconds delay
- 6+ failed attempts: Account locked for 30 minutes

### 2. IP-Based Protection
- **Login attempts**: 20 attempts per hour per IP address
- **Registration**: 3 registrations per hour per IP
- **IP blocking**: 2 hours for suspicious activity
- **Cross-account tracking**: Prevents credential stuffing attacks

### 3. Account-Level Protection
- **Login**: 5 attempts per 15-minute window
- **Password reset**: 3 requests per hour
- **Email verification**: 5 requests per hour
- **Account lockout**: 30 minutes after 5 failed attempts

### 4. Admin Bypass
Emergency access for administrators:
- Configurable admin email list
- Bypasses rate limiting (logged for audit)
- Can unlock other accounts

## Implementation Components

### Core Components

#### 1. RateLimitValidator (`app/core/validation.py`)
Central rate limiting logic with:
- In-memory tracking (Redis recommended for production)
- Progressive delay calculation
- IP tracking and blocking
- Admin bypass functionality

#### 2. RateLimitingService (`app/services/rate_limiting_service.py`)
High-level service providing:
- Unified API for rate limit checks
- Security event logging
- Account status monitoring
- Brute force detection

#### 3. AccountLockoutMiddleware (`app/core/security_middleware.py`)
FastAPI middleware that:
- Intercepts login requests
- Enforces progressive delays
- Adds rate limiting headers
- Integrates with the rate limiting service

### Rate Limiting Headers

All protected endpoints return these headers:
```
X-RateLimit-Limit: 5              # Maximum attempts allowed
X-RateLimit-Remaining: 3          # Attempts remaining
X-RateLimit-Reset: **********     # Unix timestamp when limit resets
Retry-After: 16                   # Seconds until next attempt allowed
```

## Usage Examples

### 1. Login Rate Limiting

```python
# In auth endpoint
from app.services.rate_limiting_service import rate_limiting_service

try:
    # Check rate limits before authentication
    rate_limit_info = rate_limiting_service.check_login_rate_limit(
        email=email,
        ip_address=client_ip,
        user_agent=user_agent
    )
    
    # Proceed with authentication
    auth_response = await auth_service.authenticate_user(...)
    
    # Record success
    rate_limiting_service.record_login_success(email, client_ip)
    
except ValidationError:
    # Rate limit exceeded
    raise HTTPException(
        status_code=429,
        detail="Too many attempts"
    )
```

### 2. Failed Login Handling

```python
except AuthenticationError:
    # Record failed attempt
    rate_limiting_service.record_login_failure(
        email=email,
        ip_address=client_ip,
        failure_reason="invalid_credentials"
    )
    raise
```

### 3. Admin Operations

```python
# Add admin bypass
rate_limiting_service.add_admin_bypass("<EMAIL>", "<EMAIL>")

# Unlock account
rate_limiting_service.unlock_account("<EMAIL>", "<EMAIL>")

# Get account status
status = rate_limiting_service.get_account_status("<EMAIL>")
```

## Security Monitoring

### 1. Get Security Events
```python
# Get events from last 24 hours
events = rate_limiting_service.get_security_events(
    hours=24,
    event_types=["login_failure", "rate_limit_exceeded"],
    ip_address="***********"
)
```

### 2. Brute Force Summary
```python
summary = rate_limiting_service.get_brute_force_summary()
# Returns:
# {
#     "period": "24 hours",
#     "total_events": 156,
#     "event_counts": {
#         "login_failure": 120,
#         "rate_limit_exceeded": 36
#     },
#     "blocked_ips_count": 3,
#     "targeted_accounts_count": 15,
#     "blocked_ips": ["*************", ...],
#     "most_targeted_accounts": ["<EMAIL>", ...]
# }
```

### 3. IP Reputation Check
```python
reputation = rate_limiting_service.get_ip_reputation("*************")
# Returns:
# {
#     "ip_address": "*************",
#     "is_blocked": true,
#     "blocked_until": "2024-01-15T10:30:00",
#     "recent_events_24h": 45,
#     "security_events": [...]
# }
```

## Attack Scenarios Protected Against

### 1. Credential Stuffing
- IP blocked after 20 attempts across different accounts
- Progressive delays slow down attacks
- Cross-account tracking detects patterns

### 2. Password Spray
- Account lockout after 5 attempts
- IP tracking prevents distributed attempts
- Time windows prevent slow attacks

### 3. Brute Force
- Progressive delays make attacks impractical
- Account lockout protects individual accounts
- IP blocking stops persistent attackers

### 4. Registration Bombing
- Limited to 3 registrations per hour per IP
- Prevents database flooding
- Email verification required

### 5. Password Reset Abuse
- Limited to 3 requests per hour per email
- Always returns success (prevents email enumeration)
- Tokens expire after 1 hour

## Configuration

### Rate Limit Settings
Located in `RateLimitValidator.LIMITS`:

```python
LIMITS = {
    'login': {
        'max_attempts': 5,
        'window_minutes': 15,
        'block_minutes': 30,
        'progressive_delays': [1, 2, 4, 8, 16],
        'ip_max_attempts': 20,
        'ip_window_minutes': 60,
        'ip_block_minutes': 120
    },
    'register': {
        'max_attempts': 3,
        'window_minutes': 60,
        'block_minutes': 60
    },
    'password_reset': {
        'max_attempts': 3,
        'window_minutes': 60,
        'block_minutes': 60
    },
    'email_verification': {
        'max_attempts': 5,
        'window_minutes': 60,
        'block_minutes': 30
    }
}
```

### Admin Configuration
Add admin emails programmatically:
```python
RateLimitValidator.add_admin_bypass("<EMAIL>")
```

## Testing

### Unit Tests
Located in `tests/unit/test_rate_limiting.py`:
- 29 comprehensive tests
- Edge cases and error conditions
- Performance testing

### Integration Tests
Located in `tests/integration/test_auth_rate_limiting.py`:
- End-to-end auth flow testing
- Header verification
- Multi-endpoint scenarios

### Security Tests
Located in `tests/integration/test_brute_force_security.py`:
- Attack simulation
- Defense verification
- Monitoring validation

## Production Considerations

### 1. Storage Backend
Current implementation uses in-memory storage. For production:
- Use Redis for distributed rate limiting
- Implement cache expiration
- Handle Redis connection failures gracefully

### 2. Performance
- Rate limit checks add ~1-5ms latency
- Progressive delays intentionally slow failed attempts
- Monitor memory usage with high traffic

### 3. Monitoring
- Set up alerts for:
  - High rate of blocked IPs
  - Unusual patterns of failed logins
  - Admin bypass usage
- Regular review of security events
- Dashboard for real-time monitoring

### 4. Legal Compliance
- Log retention policies
- GDPR considerations for IP storage
- User notification of security measures

## Troubleshooting

### Common Issues

1. **Legitimate user locked out**
   - Use admin bypass to unlock
   - Check progressive delay status
   - Review security events for context

2. **False positive IP blocks**
   - Shared IPs (offices, schools)
   - Consider whitelisting
   - Adjust IP limits if needed

3. **Performance degradation**
   - Monitor memory usage
   - Implement cleanup routines
   - Consider Redis clustering

### Debug Commands

```python
# Check rate limit status
status = RateLimitValidator.get_rate_limit_status(email, 'login')

# Check IP status
ip_status = RateLimitValidator.get_ip_status(ip_address)

# Clear all attempts (development only)
RateLimitValidator._attempts.clear()
RateLimitValidator._failed_attempts.clear()
RateLimitValidator._blocked_ips.clear()
```

## Best Practices

1. **Always record both success and failure**
   - Helps establish patterns
   - Clears failed attempts on success

2. **Use consistent IP extraction**
   - Handle proxies/load balancers
   - Use X-Forwarded-For header

3. **Implement graceful degradation**
   - Don't block service if rate limiting fails
   - Log errors for investigation

4. **Regular security reviews**
   - Analyze attack patterns
   - Adjust limits based on data
   - Update admin bypass list

5. **User communication**
   - Clear error messages
   - Indicate wait times
   - Provide support contact