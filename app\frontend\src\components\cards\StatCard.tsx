import React from 'react';
import { clsx } from 'clsx';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';

interface StatCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: React.ReactNode;
  trend?: {
    value: number;
    label?: string;
  };
  variant?: 'default' | 'primary' | 'success' | 'warning' | 'error';
  loading?: boolean;
  onClick?: () => void;
  className?: string;
}

export const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  trend,
  variant = 'default',
  loading = false,
  onClick,
  className,
}) => {
  const variantStyles = {
    default: 'bg-white',
    primary: 'bg-gradient-to-br from-accent-red to-accent-red-light text-white',
    success: 'bg-gradient-to-br from-semantic-success to-green-400 text-white',
    warning: 'bg-gradient-to-br from-semantic-warning to-yellow-400 text-white',
    error: 'bg-gradient-to-br from-semantic-error to-red-400 text-white',
  };

  const getTrendIcon = () => {
    if (!trend) return null;
    if (trend.value > 0) return <TrendingUp className="w-4 h-4" />;
    if (trend.value < 0) return <TrendingDown className="w-4 h-4" />;
    return <Minus className="w-4 h-4" />;
  };

  const getTrendColor = () => {
    if (!trend) return '';
    if (trend.value > 0) return 'text-semantic-success';
    if (trend.value < 0) return 'text-semantic-error';
    return 'text-text-muted';
  };

  return (
    <div
      onClick={onClick}
      className={clsx(
        'relative p-6 rounded-2xl shadow-soft transition-all duration-200',
        variantStyles[variant],
        onClick && 'cursor-pointer hover:shadow-elevated hover:-translate-y-0.5',
        className
      )}
    >
      {loading ? (
        <div className="space-y-3">
          <div className="h-4 w-24 bg-gray-200 rounded animate-pulse" />
          <div className="h-8 w-32 bg-gray-200 rounded animate-pulse" />
          <div className="h-3 w-20 bg-gray-200 rounded animate-pulse" />
        </div>
      ) : (
        <>
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <p className={clsx(
                'text-sm font-medium',
                variant === 'default' ? 'text-text-secondary' : 'text-white/80'
              )}>
                {title}
              </p>
            </div>
            {icon && (
              <div className={clsx(
                'p-2 rounded-lg',
                variant === 'default' ? 'bg-red-50' : 'bg-white/10'
              )}>
                <span className={clsx(
                  'w-5 h-5',
                  variant === 'default' ? 'text-accent-red' : 'text-white'
                )}>
                  {icon}
                </span>
              </div>
            )}
          </div>
          
          <div>
            <p className={clsx(
              'text-3xl font-semibold',
              variant === 'default' ? 'text-text-primary' : 'text-white'
            )}>
              {value}
            </p>
            
            {(subtitle || trend) && (
              <div className="mt-2 flex items-center gap-3">
                {subtitle && (
                  <p className={clsx(
                    'text-sm',
                    variant === 'default' ? 'text-text-muted' : 'text-white/70'
                  )}>
                    {subtitle}
                  </p>
                )}
                
                {trend && variant === 'default' && (
                  <div className={clsx('flex items-center gap-1 text-sm', getTrendColor())}>
                    {getTrendIcon()}
                    <span className="font-medium">
                      {Math.abs(trend.value)}%
                    </span>
                    {trend.label && (
                      <span className="text-text-muted">{trend.label}</span>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

interface StatGridProps {
  children: React.ReactNode;
  columns?: 1 | 2 | 3 | 4;
  className?: string;
}

export const StatGrid: React.FC<StatGridProps> = ({
  children,
  columns = 4,
  className,
}) => {
  const columnClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
  };

  return (
    <div className={clsx('grid gap-6', columnClasses[columns], className)}>
      {children}
    </div>
  );
};

export default StatCard;