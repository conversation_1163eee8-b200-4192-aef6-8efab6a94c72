import React from 'react';
import { Globe } from 'lucide-react';

interface LanguageProficiencyGridProps {
  value: Record<string, string>;
  onChange: (value: Record<string, string>) => void;
  error?: string;
}

const LANGUAGES = [
  { code: 'en', name: 'English' },
  { code: 'fr', name: 'French' },
  { code: 'es', name: 'Spanish' },
  { code: 'zh', name: 'Chinese' },
  { code: 'ar', name: 'Arabic' },
  { code: 'pt', name: 'Portuguese' },
  { code: 'it', name: 'Italian' },
  { code: 'de', name: 'German' }
];

const PROFICIENCY_LEVELS = [
  { value: 'native', label: 'Native', color: 'bg-green-100 text-green-700' },
  { value: 'fluent', label: 'Fluent', color: 'bg-blue-100 text-blue-700' },
  { value: 'conversational', label: 'Conversational', color: 'bg-yellow-100 text-yellow-700' },
  { value: 'basic', label: 'Basic', color: 'bg-gray-100 text-gray-700' }
];

export const LanguageProficiencyGrid: React.FC<LanguageProficiencyGridProps> = ({
  value = {},
  onChange,
  error
}) => {
  const handleProficiencyChange = (langCode: string, proficiency: string) => {
    const newValue = { ...value };
    
    if (proficiency === '') {
      // Remove language if no proficiency selected
      delete newValue[langCode];
    } else {
      newValue[langCode] = proficiency;
    }
    
    onChange(newValue);
  };

  const getSelectedLanguages = () => {
    return Object.keys(value).filter(lang => value[lang]);
  };

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          <Globe className="inline-block w-4 h-4 mr-1" />
          Language Proficiency
        </label>
        
        {/* Summary of selected languages */}
        {getSelectedLanguages().length > 0 && (
          <div className="mb-4 p-3 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-600 mb-2">Teaching Languages:</p>
            <div className="flex flex-wrap gap-2">
              {getSelectedLanguages().map(langCode => {
                const language = LANGUAGES.find(l => l.code === langCode);
                const proficiencyLevel = PROFICIENCY_LEVELS.find(p => p.value === value[langCode]);
                return (
                  <span
                    key={langCode}
                    className={`px-3 py-1 rounded-full text-sm font-medium ${proficiencyLevel?.color || 'bg-gray-100'}`}
                  >
                    {language?.name || langCode}: {proficiencyLevel?.label || value[langCode]}
                  </span>
                );
              })}
            </div>
          </div>
        )}

        {/* Language grid */}
        <div className="border border-gray-200 rounded-lg overflow-hidden">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-px bg-gray-200">
            {LANGUAGES.map(language => (
              <div key={language.code} className="bg-white p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium text-gray-900">{language.name}</span>
                </div>
                <div className="flex gap-2 flex-wrap">
                  <button
                    type="button"
                    onClick={() => handleProficiencyChange(language.code, '')}
                    className={`px-2 py-1 text-xs rounded transition-colors ${
                      !value[language.code]
                        ? 'bg-gray-200 text-gray-700'
                        : 'bg-gray-100 text-gray-500 hover:bg-gray-200'
                    }`}
                  >
                    None
                  </button>
                  {PROFICIENCY_LEVELS.map(level => (
                    <button
                      key={level.value}
                      type="button"
                      onClick={() => handleProficiencyChange(language.code, level.value)}
                      className={`px-2 py-1 text-xs rounded transition-colors ${
                        value[language.code] === level.value
                          ? level.color
                          : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                      }`}
                    >
                      {level.label}
                    </button>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Help text */}
        <div className="mt-3 text-xs text-gray-500">
          <p className="mb-1">Select your proficiency level for each language you can teach in.</p>
          <ul className="space-y-1 ml-4">
            <li>• <strong>Native:</strong> First language or equivalent proficiency</li>
            <li>• <strong>Fluent:</strong> Can teach complex subjects with ease</li>
            <li>• <strong>Conversational:</strong> Can teach basic to intermediate topics</li>
            <li>• <strong>Basic:</strong> Can provide simple tutoring support</li>
          </ul>
        </div>

        {error && (
          <p className="mt-2 text-sm text-red-600">{error}</p>
        )}
      </div>
    </div>
  );
};