{"common": {"loading": "Chargement...", "error": "<PERSON><PERSON><PERSON>", "success": "Su<PERSON>ès", "save": "Enregistrer", "saving": "Enregistrement...", "cancel": "Annuler", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "create": "<PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "export": "Exporter", "import": "Importer", "refresh": "Actualiser", "actions": "Actions", "status": "Statut", "date": "Date", "time": "<PERSON><PERSON>", "name": "Nom", "email": "<PERSON><PERSON><PERSON>", "phone": "Téléphone", "address": "<PERSON><PERSON><PERSON>", "description": "Description", "notes": "Notes", "noData": "<PERSON><PERSON><PERSON> donnée disponible", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cet élément?", "yes": "O<PERSON>", "no": "Non", "switch_to": "Basculer vers", "all": "Tous", "view": "Voir", "download": "Télécharger", "filters": "Filtres", "clearFilters": "Effacer les filtres", "showing": "Affichage", "to": "à", "of": "de", "results": "résultats", "previous": "Précédent", "next": "Suivant", "days": "jours", "minutes": "minutes", "hours": "heures", "ago": "il y a", "optional": "Optionnel"}, "sidebar": {"search": "<PERSON><PERSON><PERSON>", "users": "Utilisateurs", "clients": "Clients", "tutors": "Tu<PERSON><PERSON>", "dependants": "Personnes à charge", "tutors_management": "Gestion des Tuteurs", "calendar": "<PERSON><PERSON><PERSON>", "map": "Carte des tuteurs", "appointments": "<PERSON><PERSON><PERSON>vous", "billing": "Facturation", "invoices": "Factures", "tutorPayments": "Paiements aux tuteurs", "subscriptions": "Abonnements", "packages": "Forfaits", "reports": "Rapports", "financial": "Financier", "performance": "Performance", "analytics": "Analytique", "usage": "Utilisation", "monthly": "<PERSON><PERSON><PERSON>", "messages": "Messages", "smsThreads": "Fils SMS", "inAppChat": "Chat intégré", "templates": "<PERSON><PERSON><PERSON><PERSON>", "broadcasts": "Diffusions", "settings": "Paramètres", "system": "Système", "userSettings": "Utilisateurs", "payments": "Paiements", "notifications": "Notifications", "api": "API", "services": "Paramètres de service", "ourServices": "Nos services", "paymentHistory": "Historique des paiements"}, "header": {"welcome": "Bienvenue, {{name}}", "profile": "Profil", "settings": "Paramètres", "help": "Aide", "logout": "Déconnexion", "activeRole": "Rôle actif", "switchRole": "<PERSON><PERSON> <PERSON>"}, "roles": {"manager": "Gestionnaire", "tutor": "<PERSON><PERSON><PERSON>", "client": "Client"}, "search": {"searchButton": "<PERSON><PERSON><PERSON>", "placeholder": "Rechercher des clients, tuteurs, rendez-vous...", "searching": "Recherche en cours...", "noResults": "Aucun résultat trouvé", "recent": "Recherches récentes", "shortcuts": "<PERSON><PERSON><PERSON><PERSON> clav<PERSON>"}, "quickActions": {"addClient": "Ajouter client", "addTutor": "Inviter tuteur", "bookSession": "Réserver séance", "createInvoice": "<PERSON><PERSON>er facture", "sendMessage": "Envoyer message", "findTutor": "Trouver tuteur"}, "auth": {"login": "Connexion", "loginTitle": "Connectez-vous à votre compte", "email": "<PERSON><PERSON><PERSON>", "orContinueWith": "Ou continuer avec", "continueWithGoogle": "Continuer avec Google", "password": "Mot de passe", "rememberMe": "Se souvenir de moi", "noAccount": "Vous n'avez pas de compte?", "signUp": "S'inscrire", "loginError": "<PERSON><PERSON><PERSON> ou mot de passe invalide", "loginSuccess": "Connexion réussie", "forgotPassword": {"title": "Mot de passe oublié?", "subtitle": "Entrez votre courriel et nous vous enverrons un lien de réinitialisation", "emailLabel": "<PERSON><PERSON><PERSON>", "emailPlaceholder": "Entrez votre adresse courriel", "emailRequired": "L'adresse courriel est requise", "invalidEmail": "<PERSON><PERSON><PERSON><PERSON> entrer une adresse courriel valide", "sendResetLink": "Envoyer le lien de réinitialisation", "sending": "Envoi en cours...", "emailSentTitle": "Vérifiez votre courriel", "emailSentMessage": "Si un compte existe pour {{email}}, nous avons envoyé les instructions de réinitialisation.", "checkSpam": "Si vous ne voyez pas le courriel, veuillez vérifier votre dossier de pourriels.", "backToLogin": "Retour à la connexion", "rememberPassword": "Vous vous souvenez de votre mot de passe?", "signIn": "Se connecter", "tooManyRequests": "Trop de demandes. Veuillez réessayer plus tard."}, "twoFactor": {"authenticatorTitle": "Authentification à deux facteurs", "authenticatorDescription": "Entrez le code de votre application d'authentification", "smsTitle": "Vérification par SMS", "emailTitle": "Vérification par courriel", "smsDescription": "Nous avons envoyé un code à {{phone}}", "smsDescriptionGeneric": "Nous avons envoyé un code à votre téléphone", "emailDescription": "Nous avons envoyé un code à {{email}}", "emailDescriptionGeneric": "Nous avons envoyé un code à votre courriel", "enterCode": "Entrez le code de vérification", "verify": "Vérifier", "didntReceive": "Vous n'avez pas reçu le code?", "resendCode": "Renvoyer le code", "resendIn": "<PERSON><PERSON><PERSON> dans {{seconds}}s", "codeSent": "Code de vérification envoyé", "invalidCode": "Code de vérification invalide", "trustDevice": "Faire confiance à cet appareil pendant 30 jours", "havingTrouble": "Vous avez des problèmes?", "useBackupCode": "Utiliser un code de secours", "resendError": "Échec de l'envoi du code"}, "session": {"timeoutTitle": "Avertissement de fin de session", "timeoutMessage": "Votre session est sur le point d'expirer en raison d'inactivité.", "timeRemaining": "Temps restant", "logout": "Déconnexion", "stayLoggedIn": "<PERSON><PERSON> connect<PERSON>", "securityNote": "Pour votre sécurité, les sessions expirent après des périodes d'inactivité."}, "resetPassword": {"title": "Réinitialiser le mot de passe", "subtitle": "Entrez votre nouveau mot de passe ci-dessous", "modalDescription": "Entrez votre adresse courriel et nous vous enverrons un lien pour réinitialiser votre mot de passe.", "emailRequired": "Veuillez entrer votre adresse courriel", "emailNotFound": "Aucun compte trouvé avec cette adresse courriel", "sendError": "Échec de l'envoi du courriel de réinitialisation. Veuillez réessayer.", "emailSent": "Courriel de réinitialisation envoy<PERSON> avec succès", "emailSentTitle": "Vérifiez votre courriel", "emailSentDescription": "Nous avons envoyé les instructions de réinitialisation à {{email}}", "emailPlaceholder": "Entrez votre courriel", "newPassword": "Nouveau mot de passe", "confirmPassword": "Confirmer le mot de passe", "passwordPlaceholder": "Entrez le nouveau mot de passe", "confirmPasswordPlaceholder": "Confirmez le nouveau mot de passe", "passwordTooShort": "Le mot de passe doit contenir au moins 8 caractères", "passwordRequirements": "Le mot de passe doit contenir des majuscules, des minuscules et un chiffre", "passwordMismatch": "Les mots de passe ne correspondent pas", "allFieldsRequired": "Tous les champs sont requis", "invalidToken": "Jeton de réinitialisation invalide ou manquant", "tokenExpired": "Ce lien de réinitialisation a expiré", "tokenInvalid": "Ce lien de réinitialisation est invalide", "verifyingToken": "Vérification du jeton de réinitialisation...", "invalidTokenTitle": "Lien de réinitialisation invalide", "backToLogin": "Retour à la connexion", "resetPassword": "Réinitialiser le mot de passe", "resetting": "Réinitialisation...", "resetError": "Échec de la réinitialisation du mot de passe. Veuillez réessayer.", "success": "Mot de passe réinitialisé avec succès", "successTitle": "Réinitialisation terminée", "successMessage": "Votre mot de passe a été réinitialisé avec succès. Vous pouvez maintenant vous connecter avec votre nouveau mot de passe.", "redirecting": "Redirection vers la connexion...", "sendEmail": "Envoyer le courriel de réinitialisation", "sending": "Envoi...", "securityNote": "Pour des raisons de sécurité, les liens de réinitialisation expirent après 24 heures."}}, "users": {"title": "Utilisateurs", "addUser": "Ajouter un utilisateur", "editUser": "Modifier l'utilisateur", "deleteUser": "Supprimer l'utilisateur", "userDetails": "Détails de l'utilisateur", "personalInfo": "Informations personnelles", "contactInfo": "Informations de contact", "accountInfo": "Informations du compte", "firstName": "Prénom", "lastName": "Nom de famille", "dateOfBirth": "Date de naissance", "role": "R<PERSON><PERSON>", "status": "Statut", "active": "Actif", "inactive": "Inactif", "verified": "Vérifié", "unverified": "Non vérifié", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "Mis à jour le", "lastLogin": "Dernière connexion"}, "clients": {"title": "Clients", "addClient": "Ajouter un client", "editClient": "Modifier le client", "clientProfile": "<PERSON>il du <PERSON>", "emergencyContact": "Contact d'urgence", "emergencyContactName": "Nom du contact", "emergencyContactPhone": "Téléphone du contact", "emergencyContactRelation": "Relation", "communicationPreferences": "Préférences de communication", "preferredLanguage": "Langue préférée", "smsNotifications": "Notifications SMS", "emailNotifications": "Notifications par courriel", "pushNotifications": "Notifications push", "internalNotes": "Notes internes"}, "client": {"profile": {"title": "Mon profil", "subtitle": "G<PERSON>rez vos informations personnelles et vos préférences", "fetchError": "Échec du chargement du profil", "updateSuccess": "Profil mis à jour avec succès", "updateError": "Échec de la mise à jour du profil", "notFound": "Profil introuvable", "tabs": {"personal": "Infos personnelles", "emergency": "Contact d'urgence", "communication": "Communication", "address": "<PERSON><PERSON><PERSON>"}, "fields": {"firstName": "Prénom", "lastName": "Nom de famille", "dateOfBirth": "Date de naissance", "phoneNumber": "Numéro de téléphone", "email": "<PERSON><PERSON><PERSON>", "emergencyContactName": "Nom du contact d'urgence", "emergencyContactPhone": "Téléphone du contact d'urgence", "emergencyContactRelation": "Relation", "addressLine1": "<PERSON><PERSON><PERSON>", "addressLine2": "Appartement, Suite, etc.", "city": "Ville", "province": "Province", "postalCode": "Code postal", "preferredLanguage": "Langue préférée"}, "placeholders": {"firstName": "Entrez votre prénom", "lastName": "Entrez votre nom de famille", "emergencyContactName": "Nom complet du contact d'urgence", "selectRelationship": "Sélectionner la relation", "addressLine1": "123 Rue Principale", "addressLine2": "App 4B", "city": "Montréal", "selectProvince": "Sélectionner la province"}, "errors": {"firstNameRequired": "Le prénom est requis", "lastNameRequired": "Le nom de famille est requis", "invalidPhone": "Veuillez entrer un numéro de téléphone valide à 10 chiffres", "futureDateOfBirth": "La date de naissance ne peut pas être dans le futur", "emergencyNameRequired": "Le nom du contact d'urgence est requis", "emergencyPhoneRequired": "Le téléphone du contact d'urgence est requis", "emergencyRelationRequired": "La relation du contact d'urgence est requise", "addressRequired": "L'adresse est requise", "cityRequired": "La ville est requise", "provinceRequired": "La province est requise", "postalCodeRequired": "Le code postal est requis", "invalidPostalCode": "Veuillez entrer un code postal canadien valide"}, "relationships": {"spouse": "Conjoint(e)", "parent": "Parent", "sibling": "<PERSON><PERSON>/Sœur", "child": "<PERSON><PERSON>", "friend": "Ami(e)", "other": "<PERSON><PERSON>"}, "emailCannotBeChanged": "Le courriel ne peut pas être modifié", "emergencyContactInfo": "Informations du contact d'urgence", "emergencyContactDescription": "Cette personne sera contactée en cas d'urgence", "addressInfo": "Informations d'adresse", "addressDescription": "Votre adresse nous aide à vous jumeler avec des tuteurs à proximité", "languagePreference": "Préférence linguistique", "languageChangeNote": "Changer votre langue actualisera la page", "notificationPreferences": "Préférences de notification", "notifications": {"email": {"label": "Notifications par courriel", "description": "<PERSON><PERSON>vez des rappels de rendez-vous et des mises à jour par courriel"}, "sms": {"label": "Notifications SMS", "description": "Recevez des messages texte pour les mises à jour urgentes et les rappels"}, "push": {"label": "Notifications push", "description": "Recevez des notifications de l'application sur votre appareil"}, "info": {"title": "Vous recevrez des notifications pour :", "appointment": "Confirmations et rappels de rendez-vous", "payment": "Confirmations de paiement et factures", "message": "Nouveaux messages des tuteurs", "update": "Mises à jour importantes du compte"}}, "accountActions": "Actions du compte", "changePassword": "Changer le mot de passe", "emailVerified": "<PERSON><PERSON><PERSON>"}}, "tutors": {"profiles": "<PERSON><PERSON> de Tuteurs", "invitations": "Invitations", "applications": "Candidatures"}, "dependants": {"title": "Personnes à charge", "addDependant": "Ajouter une personne à charge", "editDependant": "Modifier la personne à charge", "parentGuardian": "<PERSON><PERSON>/Tu<PERSON>ur", "relationship": "Relation", "parent": "Parent", "guardian": "<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON>", "grade": "Niveau scolaire", "school": "École", "specialNeeds": "Be<PERSON><PERSON> s<PERSON>", "allergies": "Allergies", "medications": "Médicaments", "learningGoals": "Objectifs d'apprentissage"}, "billing": {"payNow": "Payer {{amount}}", "paymentSuccess": "<PERSON><PERSON><PERSON>", "billingSuccess": "Facturation traitée avec succès", "invoiceDetails": "<PERSON>é<PERSON> de la facture", "tutorPaymentDetails": "<PERSON><PERSON><PERSON> du paiement tuteur", "details": "Détails", "auditLog": "Journal d'audit", "clientInfo": "Informations client", "status": "Statut", "issueDate": "Date d'émission", "dueDate": "Date d'échéance", "paidDate": "Date de paiement", "lineItems": "Articles de ligne", "description": "Description", "quantity": "Quantité", "rate": "<PERSON><PERSON><PERSON>", "amount": "<PERSON><PERSON>", "subtotal": "Sous-total", "tax": "Taxe", "total": "Total", "paymentInfo": "Informations de paiement", "method": "Méthode", "reference": "Référence", "paidBy": "Payé par", "parent1": "Parent 1", "parent2": "Parent 2", "noAuditLogs": "Aucun journal d'audit disponible", "paymentPeriod": "Période de paiement", "totalHours": "Heures totales", "totalAmount": "Montant total", "paymentBreakdown": "Détail du paiement", "baseAmount": "Montant de <PERSON>", "bonusAmount": "Montant bonus", "sessionDetails": "<PERSON><PERSON><PERSON> des sessions", "sessions": "sessions", "date": "Date", "client": "Client", "hours": "<PERSON><PERSON>", "processingInfo": "Informations de traitement", "approvedBy": "Approuvé par", "approvedAt": "Approuvé le", "stripePayoutId": "ID de versement Stripe", "paidAt": "<PERSON><PERSON> le", "actions": {"created": "<PERSON><PERSON><PERSON>", "updated": "Mis à jour", "paid": "<PERSON><PERSON>", "refunded": "Re<PERSON><PERSON><PERSON>", "cancelled": "<PERSON><PERSON><PERSON>", "approved": "Approu<PERSON><PERSON>", "rejected": "<PERSON><PERSON><PERSON>", "payout_processed": "<PERSON><PERSON><PERSON> trait<PERSON>", "hours_deducted": "Heures déduites", "subscription_purchased": "<PERSON><PERSON><PERSON>", "subscription_renewed": "Renouvelé", "subscription_cancelled": "<PERSON><PERSON><PERSON>", "payment_recorded": "Paiement enregistré", "auto_renew_updated": "Renouvellement auto mis à jour"}, "fields": {"status": "Statut", "amount": "<PERSON><PERSON>", "payment_method": "Méthode de paiement", "hours_remaining": "Heures restantes", "line_items": "Articles de ligne", "auto_renew": "Renouvellement automatique", "notes": "Notes"}, "paymentStatus": {"pending": "En attente", "approved": "Approu<PERSON><PERSON>", "processing": "En traitement", "paid": "<PERSON><PERSON>", "rejected": "<PERSON><PERSON><PERSON>"}, "errors": {"updateInvoiceStatus": "Échec de la mise à jour du statut de la facture"}, "tutorPayments": {"tutorDescription": "Consultez votre historique de paiements et vos gains", "notAvailableForRole": "Les paiements des tuteurs ne sont pas disponibles pour votre rôle", "yourPaymentHistory": "Votre historique de paiements"}}, "settings": {"title": "Paramètres", "language": {"title": "Préférences de langue", "current_status": "Statut actuel", "current_language": "Langue actuelle", "detected_language": "Langue détectée", "confidence": "confiance", "source": {"manual": "Sélection manuelle", "auto": "Détection automatique", "browser": "Détection navigateur", "system": "Défaut système", "user_manual": "<PERSON> util<PERSON>", "auto_detect": "Détection auto", "browser_detect": "Détection navigateur", "admin_override": "Remplacement admin"}, "last_updated": "Dernière mise à jour", "quebec_indicators": "Indicateurs français québécois", "preferences": "Paramètres de langue", "preferred_language": "Langue préférée", "auto_detect": "Détecter automatiquement la langue", "auto_detect_description": "Détecter automatiquement votre langue selon les paramètres du navigateur et la localisation", "quebec_french_preference": "Préférer le français québécois", "quebec_french_description": "Utiliser la terminologie et le formatage du français québécois quand le français est sélectionné", "auto_detect_now": "Détecter maintenant", "view_history": "Voir l'historique", "change_history": "Historique des changements de langue", "no_history": "Aucun changement de langue enregistré", "quick_switch": "Changement rapide de langue", "quick_switch_description": "Changer rapidement entre les langues pour cette session"}, "timezone": "<PERSON><PERSON> ho<PERSON>", "notifications": "Paramètres de notification", "appearance": "Apparence", "privacy": "Confidentialité", "security": "Sécurité"}, "plurals": {"appointment": {"one": "{count} rendez-vous", "other": "{count} rendez-vous"}, "client": {"one": "{count} client", "other": "{count} clients"}, "tutor": {"one": "{count} tuteur", "other": "{count} tuteurs"}, "hour": {"one": "{count} heure", "other": "{count} heures"}, "minute": {"one": "{count} minute", "other": "{count} minutes"}, "session": {"one": "{count} session", "other": "{count} sessions"}, "message": {"one": "{count} message", "other": "{count} messages"}, "payment": {"one": "{count} paiement", "other": "{count} paiements"}}, "quebec": {"languages": {"english": "<PERSON><PERSON><PERSON>", "french": "Français"}}, "success": {"language_switched": "Langue changée avec succès", "translations_reloaded": "Traductions rechargées avec succès"}, "errors": {"language_switch_failed": "Échec du changement de langue", "translations_load_failed": "Échec du chargement des traductions", "access_denied": "<PERSON><PERSON><PERSON>", "insufficient_permissions": "Vous n'avez pas la permission d'accéder à cette fonctionnalité", "fetch_invitations_failed": "Échec du chargement des invitations", "search_failed": "Échec de la recherche", "email_required": "Le courriel est requis", "user_already_exists": "Un utilisateur avec ce courriel existe déjà", "send_invitation_failed": "Échec de l'envoi de l'invitation", "resend_invitation_failed": "Échec du renvoi de l'invitation", "cancel_invitation_failed": "Échec de l'annulation de l'invitation"}, "packages": {"create": "Créer un forfait", "createNew": "<PERSON><PERSON>er un nouveau forfait", "availablePackages": "Forfaits disponibles", "myPackages": "Me<PERSON> forfaits", "packageName": "Nom du forfait", "packageType": "Type de forfait", "sessionCount": "Nombre de sessions", "sessionDuration": "Durée de session (minutes)", "price": "Prix", "isGroupPackage": "Forfait de groupe", "minParticipants": "Participants min", "maxParticipants": "Participants max", "validUntil": "<PERSON><PERSON> jusqu'au", "description": "Description", "termsAndConditions": "Te<PERSON><PERSON> et conditions", "sessions": "sessions", "minutes": "minutes", "participants": "participants", "purchase": "<PERSON><PERSON><PERSON>", "confirmPurchase": "Confirm<PERSON> l'achat", "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce forfait?", "noPurchases": "Vous n'avez encore aucun achat de forfait", "purchasedOn": "<PERSON><PERSON><PERSON> le", "for": "Pour", "sessionsRemaining": "sessions restantes", "expiresOn": "Expire le", "types": {"tecfee": "TECFEE", "bundle": "Forfait", "custom": "<PERSON><PERSON><PERSON><PERSON>"}, "status": {"active": "Actif", "completed": "Complété", "expired": "Expiré", "refunded": "Re<PERSON><PERSON><PERSON>"}}, "appointments": {"errors": {"fetchAppointment": "Échec du chargement des détails du rendez-vous", "completeAppointment": "Échec de la complétion du rendez-vous", "loadAuditLogs": "Échec du chargement de l'historique des changements"}, "details": {"title": "<PERSON><PERSON><PERSON> du rendez-vous", "tabs": {"details": "Détails", "audit": "Historique"}, "basicInfo": "Informations de base", "scheduleInfo": "Informations d'horaire", "notAssigned": "Non assigné", "notSpecified": "Non spécifié"}, "fields": {"client": "Client", "tutor": "<PERSON><PERSON><PERSON>", "subject": "<PERSON><PERSON>", "status": "Statut", "date": "Date", "time": "<PERSON><PERSON>", "location": "<PERSON><PERSON>", "duration": "<PERSON><PERSON><PERSON>", "notes": "Notes", "startTime": "<PERSON><PERSON> d<PERSON>", "endTime": "Heure de fin", "locationType": "Type de lieu"}, "audit": {"title": "Historique des changements", "noLogs": "Aucun historique disponible", "reason": "<PERSON>son", "actions": {"created": "<PERSON><PERSON><PERSON>", "updated": "<PERSON><PERSON><PERSON><PERSON>", "statusChanged": "Statut modifié", "cancelled": "<PERSON><PERSON><PERSON>", "deleted": "Supprimé", "rescheduled": "Reprogrammé"}}, "completeAppointment": "Compléter le rendez-vous", "confirmCompletion": "Confirmer la complétion", "confirmCompletionMessage": "V<PERSON><PERSON>z confirmer que ce rendez-vous a été complété avec succès.", "markAsCompleted": "Marquer comme complété", "completionSuccess": "<PERSON><PERSON><PERSON>vous marqué comme complété", "status": {"completed": "Complété", "scheduled": "Planifié", "cancelled": "<PERSON><PERSON><PERSON>"}, "adjustDuration": "Ajuster la durée", "durationAdjustmentInfo": "Ajustez la durée réelle si elle diffère de l'heure prévue", "actualDuration": "<PERSON><PERSON><PERSON> r<PERSON>", "scheduledDuration": "Durée prévue", "adjustmentReason": "Raison de l'ajustement", "adjustmentReasonPlaceholder": "ex.: Session prolongée pour terminer l'exercice", "tutorNoShow": "Absence du tuteur", "clientNoShow": "Absence du client", "completionNotes": "Notes de complétion", "completionNotesPlaceholder": "Notes optionnelles sur la session...", "durationAdjusted": "La durée a été ajustée avec succès", "durationWasAdjusted": "Ajustement de durée appliqué", "originalDuration": "Durée originale", "difference": "<PERSON>ff<PERSON><PERSON><PERSON>", "reason": "<PERSON>son"}, "messages": {"searchConversations": "Rechercher des conversations...", "noConversations": "Aucune conversation trouvée", "noMessages": "(Aucun message)", "typeMessage": "Tapez un message...", "smsCharges": "Des frais SMS peuvent s'appliquer. Appuyez sur Entrée pour envoyer, Maj+Entrée pour une nouvelle ligne.", "selectConversation": "Sélectionnez une conversation pour commencer à envoyer des messages", "startConversation": "<PERSON><PERSON><PERSON><PERSON> une conversation pour envoyer des messages", "errors": {"fetchConversations": "Échec du chargement des conversations", "fetchMessages": "Échec du chargement des messages", "sendMessage": "Échec de l'envoi du message"}}, "form": {"email": "<PERSON><PERSON><PERSON>", "first_name": "Prénom", "last_name": "Nom de famille", "phone": "Téléphone", "personal_message": "Message personnel", "first_name_placeholder": "Entrez le prénom", "last_name_placeholder": "Entrez le nom de famille"}, "invitations": {"title": "Invitations de Tuteurs", "description": "<PERSON><PERSON><PERSON> les invitations de tuteurs et suivre leur statut", "send_invitation": "Envoyer une Invitation", "send_first_invitation": "Envoyer la Première Invitation", "no_invitations": "Aucune invitation trouvée", "no_invitations_description": "Commencez par envoyer votre première invitation de tuteur", "search_placeholder": "Rechercher par courriel ou nom...", "only_mine": "Seulement mes invitations", "invited_by": "Invité par", "sent": "<PERSON><PERSON><PERSON>", "expires": "Expire", "expired": "expiré", "copy_link": "<PERSON><PERSON><PERSON>", "resend": "<PERSON><PERSON><PERSON>", "cancel": "Annuler", "sending": "Envoi en cours...", "send": "Envoyer", "sent_successfully": "Invitation envoy<PERSON> ave<PERSON> succès", "resent_successfully": "Invitation renvoy<PERSON> avec succès", "cancelled_successfully": "Invitation annulée avec succès", "link_copied": "Lien d'invitation copié dans le presse-papiers", "confirm_cancel": "Êtes-vous sûr de vouloir annuler cette invitation?", "message_placeholder": "Message personnel optionnel pour le tuteur invité...", "status": {"all": "Tous les Statuts", "pending": "En Attente", "accepted": "Accepté", "expired": "Expiré"}}, "invitation": {"invalid_token": "<PERSON><PERSON> d'invitation invalide", "token_expired_or_invalid": "Ce lien d'invitation a expiré ou est invalide", "verification_error": "Impossible de vérifier l'invitation", "verifying_token": "Vérification de l'invitation...", "invalid_invitation": "Invitation Invalide", "go_to_login": "Aller à la Connexion", "welcome_to_tutoraide": "Bienvenue chez Tutor<PERSON>!", "create_account_subtitle": "Complétez votre inscription pour commencer en tant que tuteur", "invitation_details": "Détails de l'Invitation", "invited_as": "Invité en tant que", "name": "Nom", "personal_message": "Message Personnel", "create_password": "<PERSON><PERSON><PERSON> un Mot de Passe", "password_placeholder": "Entrez votre mot de passe", "password_help": "Le mot de passe doit contenir au moins 8 caractères avec majuscule, minuscule et chiffre", "confirm_password": "Confirm<PERSON> le Mot de Passe", "confirm_password_placeholder": "Confirmez votre mot de passe", "password_required": "Le mot de passe est requis", "password_mismatch": "Les mots de passe ne correspondent pas", "password_min_length": "Le mot de passe doit contenir au moins 8 caractères", "password_requirements": "Le mot de passe doit contenir des caractères majuscules, minuscules et numériques", "validation_error": "Données invalides fournies", "acceptance_error": "Échec de l'acceptation de l'invitation", "creating_account": "Création du Compte...", "accept_and_create_account": "Accepter l'Invitation et Créer un Compte", "terms_notice": "En créant un compte, vous acceptez nos Conditions d'Utilisation et Politique de Confidentialité", "account_created_successfully": "Compte créé avec succès! Bienvenue chez TutorAide.", "account_created_login_prompt": "Compte créé avec succès! Veuillez vous connecter avec vos nouvelles informations d'identification."}, "consent": {"title": "Consentements et Ententes", "requiredAgreements": "Ententes requises", "mandatory": "Obligatoire", "alreadyAccepted": "Accepté", "version": "Version", "effectiveDate": "Date d'entrée en vigueur", "step": "Étape", "accepting": "Acceptation en cours...", "acceptSelected": "Accepter les ententes sélectionnées", "viewDocument": "Voir le document", "viewHistory": "Voir l'historique", "accept": "Accepter", "withdraw": {"title": "Retirer le consentement", "confirmation": "Êtes-vous sûr de vouloir retirer votre consentement pour {{consent}}?", "reasonLabel": "<PERSON><PERSON> (optionnelle)", "reasonPlaceholder": "Veuillez nous dire pourquoi vous retirez ce consentement..."}, "withdrawing": "Retrait en cours...", "confirmWithdraw": "Confirmer le retrait", "grantedOn": "Accord<PERSON> le", "withdrawnOn": "<PERSON><PERSON><PERSON> le", "reason": "<PERSON>son", "info": {"mandatory": "Les ententes obligatoires sont requises pour utiliser la plateforme et ne peuvent être retirées."}, "categories": {"legal": "Légal", "marketing": "Marketing", "analytics": "Analytique", "functional": "Fonctionnel"}, "types": {"terms_of_service": "Conditions d'utilisation", "privacy_policy": "Politique de confidentialité", "tutor_employment_agreement": "Entente de service du tuteur", "marketing_emails": "Communications marketing", "usage_analytics": "Analytiques d'utilisation"}, "status": {"granted": "Accepté", "withdrawn": "Retiré", "expired": "Expiré"}, "actions": {"granted": "Consentement accordé", "withdrawn": "Consentement retiré", "expired": "Consentement expiré"}, "errors": {"fetchFailed": "Échec du chargement des informations de consentement", "acceptFailed": "Échec de l'acceptation du consentement", "withdrawFailed": "Échec du retrait du consentement", "validationFailed": "Échec de la validation des consentements", "historyFetchFailed": "Échec du chargement de l'historique des consentements", "documentFetchFailed": "Échec du chargement du document"}, "success": {"accepted": "Consentement accepté avec succès", "withdrawn": "Consentement retiré avec succès"}, "management": {"title": "Gestion des consentements", "description": "Examinez et gérez vos préférences de confidentialité et vos paramètres de consentement", "infoTitle": "À propos de vos consentements", "infoDescription": "Vous pouvez examiner toutes les ententes et gérer les préférences optionnelles ici. Les ententes obligatoires sont requises pour l'accès à la plateforme.", "mandatoryConsents": "Ententes obligatoires", "optionalConsents": "Préférences optionnelles"}, "history": {"title": "Historique des consentements", "empty": "Aucun historique disponible pour ce consentement"}, "flow": {"title": "Examen des ententes requis", "clientDescription": "Veuillez examiner et accepter les ententes requises pour continuer à utiliser TutorAide.", "tutorDescription": "En tant que tuteur, vous devez examiner et accepter notre entente de service et les conditions de la plateforme.", "missingConsents": "Les ententes suivantes nécessitent votre acceptation:", "expiredConsents": "Les ententes suivantes ont expiré et doivent être renouvelées:", "reviewAndAccept": "Examiner et accepter les ententes", "remindLater": "Me rappeler plus tard", "infoText": "Votre vie privée est importante pour nous. Nous ne collectons que les informations nécessaires pour fournir nos services de tutorat."}}}