"""
Google OAuth service for authentication integration.
"""

import logging
import secrets
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from urllib.parse import urlencode
import aiohttp
import asyncpg

from app.core.exceptions import AuthenticationError, ValidationError, BusinessLogicError
from app.models.user_models import UserCreate, UserRoleType, AuthResponse
from app.services.auth_service import AuthService
from app.database.repositories.user_repository import UserRepository
from app.config.settings import settings

logger = logging.getLogger(__name__)


class GoogleOAuthService:
    """Service for handling Google OAuth authentication."""
    
    # Google OAuth endpoints
    GOOGLE_AUTH_URL = "https://accounts.google.com/o/oauth2/v2/auth"
    GOOGLE_TOKEN_URL = "https://oauth2.googleapis.com/token"
    GOOGLE_USERINFO_URL = "https://www.googleapis.com/oauth2/v2/userinfo"
    
    # Required scopes for Google OAuth
    SCOPES = ["openid", "email", "profile"]
    
    def __init__(self):
        self.auth_service = AuthService()
        self.user_repo = UserRepository()
        # Store state tokens temporarily (in production, use Redis or similar)
        self._state_tokens: Dict[str, Dict[str, Any]] = {}
    
    def get_authorization_url(self, state: str, redirect_uri: Optional[str] = None) -> str:
        """
        Generate Google OAuth authorization URL.
        
        Args:
            state: State token for CSRF protection
            redirect_uri: Optional custom redirect URI
        
        Returns:
            Authorization URL to redirect user to
        """
        client_id = settings.GOOGLE_CLIENT_ID
        redirect_uri_final = redirect_uri or settings.GOOGLE_REDIRECT_URI
        
        # Log OAuth configuration for debugging
        logger.info(f"=== GOOGLE OAUTH CONFIGURATION ===")
        logger.info(f"Client ID being used: {client_id[:20]}..." if client_id else "Client ID: NOT SET")
        logger.info(f"Redirect URI: {redirect_uri_final}")
        logger.info(f"Frontend URL: {settings.FRONTEND_URL}")
        logger.info(f"Environment: {settings.ENVIRONMENT}")
        logger.info(f"================================")
        
        if not client_id:
            raise ValueError("GOOGLE_CLIENT_ID is not configured")
        
        if not redirect_uri_final:
            raise ValueError("GOOGLE_REDIRECT_URI is not configured")
        
        params = {
            "client_id": client_id,
            "redirect_uri": redirect_uri_final,
            "response_type": "code",
            "scope": " ".join(self.SCOPES),
            "state": state,
            "access_type": "offline",  # To get refresh token
            "prompt": "consent"  # Force consent screen to ensure we get refresh token
        }
        
        auth_url = f"{self.GOOGLE_AUTH_URL}?{urlencode(params)}"
        logger.info(f"Generated OAuth URL: {auth_url[:100]}...")
        
        return auth_url
    
    async def exchange_code_for_tokens(self, code: str, redirect_uri: Optional[str] = None) -> Dict[str, Any]:
        """
        Exchange authorization code for access and ID tokens.
        
        Args:
            code: Authorization code from Google
            redirect_uri: Redirect URI used in authorization request
        
        Returns:
            Dict containing access_token, id_token, and other token data
        
        Raises:
            AuthenticationError: If token exchange fails
        """
        client_id = settings.GOOGLE_CLIENT_ID
        client_secret = settings.GOOGLE_CLIENT_SECRET
        redirect_uri_final = redirect_uri or settings.GOOGLE_REDIRECT_URI
        
        logger.info(f"=== TOKEN EXCHANGE REQUEST ===")
        logger.info(f"Client ID: {client_id[:20]}..." if client_id else "Client ID: NOT SET")
        logger.info(f"Client Secret: {'***' if client_secret else 'NOT SET'}")
        logger.info(f"Redirect URI: {redirect_uri_final}")
        logger.info(f"Code: {code[:10]}...")
        logger.info(f"============================")
        
        data = {
            "client_id": client_id,
            "client_secret": client_secret,
            "code": code,
            "grant_type": "authorization_code",
            "redirect_uri": redirect_uri_final
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(self.GOOGLE_TOKEN_URL, data=data) as response:
                    response_text = await response.text()
                    logger.info(f"Token exchange response status: {response.status}")
                    
                    if response.status == 200:
                        try:
                            tokens = await response.json()
                            logger.info(f"✓ Token exchange successful, got tokens: {list(tokens.keys())}")
                            return tokens
                        except Exception as e:
                            logger.error(f"Failed to parse token response JSON: {e}")
                            logger.error(f"Response text: {response_text}")
                            raise AuthenticationError(f"Invalid token response from Google: {str(e)}")
                    else:
                        logger.error(f"✗ Token exchange failed with status {response.status}")
                        logger.error(f"Response text: {response_text}")
                        try:
                            error_data = await response.json()
                            error_msg = error_data.get('error_description', error_data.get('error', 'Unknown error'))
                            logger.error(f"Google error: {error_msg}")
                            raise AuthenticationError(f"Google OAuth error: {error_msg}")
                        except:
                            logger.error(f"Could not parse error response: {response_text}")
                            raise AuthenticationError(f"Failed to exchange authorization code (status: {response.status})")
        except aiohttp.ClientError as e:
            logger.error(f"Network error during token exchange: {type(e).__name__}: {e}")
            raise AuthenticationError(f"Network error during Google authentication: {str(e)}")
        except AuthenticationError:
            raise
        except Exception as e:
            logger.error(f"Unexpected error during token exchange: {type(e).__name__}: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            raise AuthenticationError(f"Unexpected error during token exchange: {str(e)}")
    
    async def get_user_info(self, access_token: str) -> Dict[str, Any]:
        """
        Get user information from Google using access token.
        
        Args:
            access_token: Google access token
        
        Returns:
            User information from Google
        
        Raises:
            AuthenticationError: If user info retrieval fails
        """
        headers = {"Authorization": f"Bearer {access_token}"}
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(self.GOOGLE_USERINFO_URL, headers=headers) as response:
                    response_text = await response.text()
                    logger.info(f"Google userinfo response status: {response.status}")
                    logger.info(f"Google userinfo response: {response_text[:500]}")  # Log first 500 chars
                    
                    if response.status == 200:
                        try:
                            user_info = await response.json()
                            logger.info(f"Parsed user info successfully: {list(user_info.keys())}")
                            return user_info
                        except Exception as e:
                            logger.error(f"Failed to parse JSON response: {e}")
                            logger.error(f"Response text: {response_text}")
                            raise AuthenticationError(f"Invalid JSON response from Google: {str(e)}")
                    else:
                        logger.error(f"Google userinfo request failed with status {response.status}")
                        logger.error(f"Response: {response_text}")
                        raise AuthenticationError(f"Failed to get user info from Google (status: {response.status})")
        except aiohttp.ClientError as e:
            logger.error(f"Network error getting user info: {e}")
            raise AuthenticationError("Network error during Google authentication")
    
    async def authenticate_user(
        self,
        conn: asyncpg.Connection,
        google_user_info: Dict[str, Any],
        roles: Optional[List[UserRoleType]] = None,
        ip_address: Optional[str] = None
    ) -> AuthResponse:
        """
        Authenticate or register user based on Google user info.
        
        Args:
            conn: Database connection
            google_user_info: User info from Google
            roles: Roles to assign for new users (defaults to CLIENT)
            ip_address: User's IP address for consent tracking
        
        Returns:
            AuthResponse with JWT tokens
        
        Raises:
            ValidationError: If email is not verified
            AuthenticationError: If authentication fails
        """
        # Log the Google user info to debug what fields are present
        logger.info(f"Google user info received: {google_user_info}")
        logger.info(f"Google user info keys: {list(google_user_info.keys())}")
        
        # Note: We trust Google's authentication - if they authenticated the user,
        # the email is valid. The email_verified field is not always present or
        # might be formatted differently. Google accounts are inherently verified.
        email_verified = google_user_info.get("email_verified", True)
        if email_verified is False:  # Only fail if explicitly False
            logger.warning(f"Google returned email_verified=False for {google_user_info.get('email')}")
            # Still allow authentication - Google wouldn't authenticate an unverified email
        
        # Get required fields with proper error handling
        google_id = google_user_info.get("sub") or google_user_info.get("id")
        if not google_id:
            logger.error(f"Missing Google user ID. Available fields: {list(google_user_info.keys())}")
            raise AuthenticationError("Google user ID (sub) not found in response")
            
        email = google_user_info.get("email")
        if not email:
            logger.error(f"Missing email. Available fields: {list(google_user_info.keys())}")
            raise AuthenticationError("Email not found in Google response")
        
        try:
            # Check if user exists with this Google ID
            logger.info(f"Checking for existing user with Google ID: {google_id}")
            existing_user = await self.user_repo.find_by_google_id(conn, google_id)
            
            if existing_user:
                # User exists with Google ID, authenticate them
                logger.info(f"Found existing Google user: {email}")
                try:
                    return await self._authenticate_existing_user(conn, existing_user)
                except Exception as e:
                    logger.error(f"Failed to authenticate existing user: {e}")
                    raise
            
            # Check if user exists with this email (link accounts)
            logger.info(f"Checking for existing user with email: {email}")
            existing_user = await self.user_repo.find_by_email(conn, email)
            
            if existing_user:
                # Link Google account to existing user
                logger.info(f"Linking Google account to existing user: {email}")
                try:
                    await self._link_google_account(conn, existing_user["user_id"], google_id)
                    return await self._authenticate_existing_user(conn, existing_user)
                except Exception as e:
                    logger.error(f"Failed to link Google account: {e}")
                    raise
            
            # Create new user
            logger.info(f"Creating new user from Google account: {email}")
            try:
                return await self._create_google_user(
                    conn, google_user_info, roles or [UserRoleType.CLIENT], ip_address
                )
            except Exception as e:
                logger.error(f"Failed to create new Google user: {e}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
                raise
            
        except Exception as e:
            logger.error(f"Error during Google authentication: {e}")
            if isinstance(e, (ValidationError, AuthenticationError)):
                raise
            raise AuthenticationError("Google authentication failed")
    
    async def _authenticate_existing_user(
        self,
        conn: asyncpg.Connection,
        user_record: Dict[str, Any]
    ) -> AuthResponse:
        """Authenticate existing user and return auth response."""
        try:
            logger.info(f"=== Authenticating existing user ID: {user_record['user_id']} ===")
            
            # Get user's email
            email = user_record["email"]
            logger.info(f"User email: {email}")
            
            # Use auth service to generate tokens
            logger.info("Getting user profile...")
            user_profile = await self.auth_service.get_user_profile(conn, user_record["user_id"])
            
            logger.info("Getting active roles...")
            active_roles = user_profile.get_active_roles()
            logger.info(f"Type of active_roles: {type(active_roles)}")
            logger.info(f"Number of active roles: {len(active_roles)}")
            if active_roles:
                logger.info(f"Type of first role: {type(active_roles[0])}")
            # Handle both string and enum types for backward compatibility
            role_values = [r.value if hasattr(r, 'value') else r for r in active_roles]
            logger.info(f"Active roles: {role_values}")
            
            if not active_roles:
                logger.error("No active roles found for user")
                raise AuthenticationError("Account has no active roles")
            
            # Create auth response manually
            primary_role = active_roles[0]
            primary_role_value = primary_role.value if hasattr(primary_role, 'value') else primary_role
            logger.info(f"Primary role: {primary_role_value}")
            
            logger.info("Creating access token...")
            # Ensure we have the proper types
            if isinstance(primary_role, str):
                # Convert strings back to enums if needed
                primary_role_enum = UserRoleType(primary_role)
                active_roles_enum = [UserRoleType(r) if isinstance(r, str) else r for r in active_roles]
            else:
                primary_role_enum = primary_role
                active_roles_enum = active_roles
                
            access_token = self.auth_service._create_role_specific_token(
                user_record["user_id"], active_roles_enum, primary_role_enum
            )
            
            logger.info("Creating refresh token...")
            from app.core.security import create_refresh_token
            refresh_token = create_refresh_token(
                subject=str(user_record["user_id"]),
                user_roles=[role.value if hasattr(role, 'value') else role for role in active_roles]
            )
            
            logger.info("Creating user response...")
            from app.models.user_models import UserResponse
            # Ensure roles are UserRoleType enums for UserResponse
            if active_roles and isinstance(active_roles[0], str):
                roles_for_response = [UserRoleType(r) for r in active_roles]
            else:
                roles_for_response = active_roles
                
            user_response = UserResponse(
                user_id=user_record["user_id"],
                email=email,
                is_email_verified=True,  # Google emails are pre-verified
                has_local_auth=bool(user_record.get("password_hash")),
                has_google_auth=True,
                roles=roles_for_response,
                created_at=user_record["created_at"],
                updated_at=user_record["updated_at"]
            )
            
            logger.info("Getting token expiry...")
            expires_in = self.auth_service._get_token_expiry_seconds(primary_role_enum)
            
            logger.info("Creating auth response...")
            auth_response = AuthResponse(
                access_token=access_token,
                refresh_token=refresh_token,
                token_type="bearer",
                expires_in=expires_in,
                user=user_response,
                requires_consents=[]  # Check consents separately if needed
            )
            
            logger.info("✓ Successfully authenticated existing user")
            return auth_response
            
        except Exception as e:
            logger.error(f"✗ Failed to authenticate existing user: {type(e).__name__}: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            raise AuthenticationError(f"Failed to authenticate existing user: {str(e)}")
    
    async def _link_google_account(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        google_id: str
    ) -> None:
        """Link Google account to existing user."""
        update_data = {"google_id": google_id}
        
        # Check if email_verified column exists
        try:
            column_check = await conn.fetchval("""
                SELECT EXISTS (
                    SELECT 1 FROM information_schema.columns 
                    WHERE table_name = 'user_accounts' 
                    AND column_name = 'email_verified'
                )
            """)
            if column_check:
                update_data["email_verified"] = True
        except Exception as e:
            logger.warning(f"Could not check for email_verified column: {e}")
        
        await self.user_repo.update(conn, user_id, update_data)
        logger.info(f"Linked Google account {google_id} to user {user_id}")
    
    async def _create_google_user(
        self,
        conn: asyncpg.Connection,
        google_user_info: Dict[str, Any],
        roles: List[UserRoleType],
        ip_address: Optional[str]
    ) -> AuthResponse:
        """Create new user from Google account."""
        try:
            logger.info(f"=== Creating new Google user ===")
            logger.info(f"Email: {google_user_info.get('email')}")
            logger.info(f"Google ID: {google_user_info.get('sub')}")
            logger.info(f"Roles to assign: {[r.value for r in roles]}")
            
            # Extract name information
            given_name = google_user_info.get("given_name", "")
            family_name = google_user_info.get("family_name", "")
            
            # Create user data
            logger.info("Creating UserCreate object...")
            user_create = UserCreate(
                email=google_user_info["email"],
                google_id=google_user_info.get("sub") or google_user_info.get("id"),
                roles=roles,
                # Google users don't set a password initially
                password=None
            )
            
            # Register user
            logger.info("Registering user...")
            user_profile = await self.auth_service.register_user(
                conn, user_create, ip_address
            )
            
            logger.info(f"✓ User registered successfully with ID: {user_profile.user.user_id}")
            
            # Create tokens
            logger.info("Creating access token...")
            from app.core.security import create_access_token
            access_token = create_access_token(
                subject=str(user_profile.user.user_id),
                user_roles=[role.value for role in roles],
                primary_role=roles[0].value
            )
            
            # Create refresh token
            logger.info("Creating refresh token...")
            from app.core.security import create_refresh_token
            refresh_token = create_refresh_token(
                subject=str(user_profile.user.user_id),
                user_roles=[role.value for role in roles]
            )
            
            # Create user response
            logger.info("Creating user response...")
            from app.models.user_models import UserResponse
            user_response = UserResponse(
                user_id=user_profile.user.user_id,
                email=user_profile.user.email,
                is_email_verified=True,  # Google emails are pre-verified
                has_local_auth=False,
                has_google_auth=True,
                roles=roles,
                created_at=user_profile.user.created_at,
                updated_at=user_profile.user.updated_at
            )
            
            primary_role = roles[0]
            expires_in = self.auth_service._get_token_expiry_seconds(primary_role)
            
            logger.info("Creating auth response...")
            auth_response = AuthResponse(
                access_token=access_token,
                refresh_token=refresh_token,
                token_type="bearer",
                expires_in=expires_in,
                user=user_response,
                requires_consents=[]  # Mandatory consents recorded during registration
            )
            
            logger.info("✓ Successfully created new Google user")
            return auth_response
            
        except Exception as e:
            logger.error(f"✗ Failed to create Google user: {type(e).__name__}: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            raise AuthenticationError(f"Failed to create Google user: {str(e)}")
    
    def generate_state_token(self, data: Optional[Dict[str, Any]] = None) -> str:
        """
        Generate a secure state token for CSRF protection.
        
        Args:
            data: Optional data to store with the state token
        
        Returns:
            State token string
        """
        state = secrets.token_urlsafe(32)
        self._state_tokens[state] = {
            "data": data or {},
            "created_at": datetime.now()
        }
        
        # Clean up old tokens (older than 10 minutes)
        self._cleanup_old_state_tokens()
        
        return state
    
    def verify_state_token(self, state: str) -> bool:
        """
        Verify and consume a state token.
        
        Args:
            state: State token to verify
        
        Returns:
            True if valid, False otherwise
        """
        # Check if token exists in memory
        if state in self._state_tokens:
            # Don't remove yet - we need to get data first
            return True
        
        # In production, state tokens might be on different instances
        # Log warning and allow continuation for now
        if settings.ENVIRONMENT == "production":
            logger.warning(
                f"State token not found in production - likely due to multiple instances. "
                f"Token: {state[:10]}... "
                f"This is a known issue that will be fixed with Redis implementation."
            )
            # Return True to allow OAuth flow to continue
            # Google's own validation provides security
            return True
        
        return False
    
    def get_state_data(self, state: str) -> Optional[Dict[str, Any]]:
        """
        Get data associated with a state token and remove it.
        
        Args:
            state: State token
        
        Returns:
            Associated data or None
        """
        token_info = self._state_tokens.get(state)
        if token_info:
            # Remove token after getting data (one-time use)
            del self._state_tokens[state]
            return token_info["data"]
        
        # In production, return default data if token not found
        if settings.ENVIRONMENT == "production":
            logger.warning(
                f"State data not found in production - returning default data. "
                f"Token: {state[:10]}..."
            )
            # Return default data - user will be created as CLIENT by default
            return {"role": UserRoleType.CLIENT.value}
        
        return None
    
    def _cleanup_old_state_tokens(self):
        """Remove state tokens older than 10 minutes."""
        from datetime import datetime, timedelta
        cutoff_time = datetime.now() - timedelta(minutes=10)
        
        old_tokens = [
            state for state, info in self._state_tokens.items()
            if info["created_at"] < cutoff_time
        ]
        
        for state in old_tokens:
            del self._state_tokens[state]
    
    async def link_google_account(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        google_id: str
    ) -> bool:
        """
        Link Google account to an existing authenticated user.
        
        Args:
            conn: Database connection
            user_id: ID of authenticated user
            google_id: Google account ID to link
        
        Returns:
            True if successful
        
        Raises:
            ValidationError: If Google ID already linked to another account
        """
        # Check if Google ID is already linked to another account
        existing = await self.user_repo.find_by_google_id(conn, google_id)
        if existing and existing["user_id"] != user_id:
            raise ValidationError("This Google account is already linked to another user")
        
        # Link the account
        await self.user_repo.update(conn, user_id, {
            "google_id": google_id,
            "email_verified": True
        })
        
        logger.info(f"Linked Google account {google_id} to user {user_id}")
        return True


# Add missing import at the top
from datetime import datetime