import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Plus, Package, Users, Clock, DollarSign, Calendar, Edit, Trash2, ShoppingCart } from 'lucide-react';
import { format } from 'date-fns';
import Button from '../../components/common/Button';
import { Card } from '../../components/common/Card';
import { Modal } from '../../components/common/Modal';
import { Input } from '../../components/common/Input';
import { Select } from '../../components/common/Select';
import { Toggle } from '../../components/common/Toggle';
import { Badge } from '../../components/common/Badge';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import { useAuth } from '../../contexts/AuthContext';
import { useApi } from '../../hooks/useApi';

interface Package {
  package_id: number;
  package_name: string;
  package_type: 'tecfee' | 'bundle' | 'custom';
  service_id?: number;
  service_name?: string;
  subject_area?: string;
  session_count: number;
  session_duration_minutes: number;
  total_price: string;
  currency_code: string;
  is_group_package: boolean;
  min_participants?: number;
  max_participants?: number;
  is_active: boolean;
  valid_from?: string;
  valid_until?: string;
  description?: string;
  terms_and_conditions?: string;
  created_at: string;
  updated_at: string;
}

interface PackagePurchase {
  purchase_id: number;
  package_id: number;
  package_name: string;
  package_type: 'tecfee' | 'bundle' | 'custom';
  client_id: number;
  client_name: string;
  dependant_id?: number;
  dependant_name?: string;
  purchase_date: string;
  sessions_remaining: number;
  total_sessions: number;
  expire_date?: string;
  amount_paid: string;
  payment_method: string;
  stripe_payment_id?: string;
  status: 'active' | 'completed' | 'expired' | 'refunded';
  created_at: string;
}

const PackagesPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const api = useApi();
  
  const [packages, setPackages] = useState<Package[]>([]);
  const [myPurchases, setMyPurchases] = useState<PackagePurchase[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showPurchaseModal, setShowPurchaseModal] = useState(false);
  const [selectedPackage, setSelectedPackage] = useState<Package | null>(null);
  const [activeTab, setActiveTab] = useState<'available' | 'my-packages'>('available');
  
  // Form states
  const [formData, setFormData] = useState({
    package_name: '',
    package_type: 'bundle' as 'tecfee' | 'bundle' | 'custom',
    session_count: 10,
    session_duration_minutes: 60,
    total_price: '',
    is_group_package: false,
    min_participants: 1,
    max_participants: 10,
    description: '',
    terms_and_conditions: '',
    valid_until: ''
  });

  const isManager = user?.roles?.some(role => role.role === 'manager');
  const isClient = user?.roles?.some(role => role.role === 'client');

  useEffect(() => {
    loadPackages();
    if (isClient) {
      loadMyPurchases();
    }
  }, []);

  const loadPackages = async () => {
    try {
      setLoading(true);
      const response = await api.get('/packages', {
        params: { is_active: true }
      });
      setPackages(response.data);
    } catch (error) {
      console.error('Error loading packages:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadMyPurchases = async () => {
    try {
      const response = await api.get('/packages/purchases/my');
      setMyPurchases(response.data);
    } catch (error) {
      console.error('Error loading purchases:', error);
    }
  };

  const handleCreatePackage = async () => {
    try {
      await api.post('/packages', {
        ...formData,
        total_price: parseFloat(formData.total_price)
      });
      setShowCreateModal(false);
      loadPackages();
      // Reset form
      setFormData({
        package_name: '',
        package_type: 'bundle',
        session_count: 10,
        session_duration_minutes: 60,
        total_price: '',
        is_group_package: false,
        min_participants: 1,
        max_participants: 10,
        description: '',
        terms_and_conditions: '',
        valid_until: ''
      });
    } catch (error) {
      console.error('Error creating package:', error);
    }
  };

  const handlePurchasePackage = async (packageId: number) => {
    try {
      await api.post(`/packages/${packageId}/purchase`, {
        client_id: user?.user_id, // This should be the client_id, not user_id
        payment_method: 'stripe',
        notes: ''
      });
      setShowPurchaseModal(false);
      loadMyPurchases();
    } catch (error) {
      console.error('Error purchasing package:', error);
    }
  };

  const handleDeletePackage = async (packageId: number) => {
    if (window.confirm(t('packages.confirmDelete'))) {
      try {
        await api.delete(`/packages/${packageId}`);
        loadPackages();
      } catch (error) {
        console.error('Error deleting package:', error);
      }
    }
  };

  const getPackageTypeBadge = (type: string) => {
    const colors = {
      tecfee: 'blue',
      bundle: 'green',
      custom: 'purple'
    };
    return <Badge variant={colors[type] || 'gray'}>{t(`packages.types.${type}`)}</Badge>;
  };

  const getStatusBadge = (status: string) => {
    const colors = {
      active: 'green',
      completed: 'gray',
      expired: 'red',
      refunded: 'yellow'
    };
    return <Badge variant={colors[status] || 'gray'}>{t(`packages.status.${status}`)}</Badge>;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-text-primary">{t('sidebar.packages')}</h1>
        {isManager && (
          <Button onClick={() => setShowCreateModal(true)} icon={Plus}>
            {t('packages.create')}
          </Button>
        )}
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 mb-6 bg-gray-100 p-1 rounded-lg">
        <button
          onClick={() => setActiveTab('available')}
          className={`flex-1 py-2 px-4 rounded-md transition-colors ${
            activeTab === 'available'
              ? 'bg-white text-primary shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          {t('packages.availablePackages')}
        </button>
        {isClient && (
          <button
            onClick={() => setActiveTab('my-packages')}
            className={`flex-1 py-2 px-4 rounded-md transition-colors ${
              activeTab === 'my-packages'
                ? 'bg-white text-primary shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            {t('packages.myPackages')}
          </button>
        )}
      </div>

      {/* Available Packages */}
      {activeTab === 'available' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {packages.map((pkg) => (
            <Card key={pkg.package_id} className="hover:shadow-lg transition-shadow">
              <div className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-text-primary">{pkg.package_name}</h3>
                    {pkg.subject_area && (
                      <p className="text-sm text-text-secondary mt-1">{pkg.subject_area}</p>
                    )}
                  </div>
                  {getPackageTypeBadge(pkg.package_type)}
                </div>

                <div className="space-y-3 mb-4">
                  <div className="flex items-center text-sm">
                    <Package className="w-4 h-4 mr-2 text-gray-400" />
                    <span>{pkg.session_count} {t('packages.sessions')}</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <Clock className="w-4 h-4 mr-2 text-gray-400" />
                    <span>{pkg.session_duration_minutes} {t('packages.minutes')}</span>
                  </div>
                  {pkg.is_group_package && (
                    <div className="flex items-center text-sm">
                      <Users className="w-4 h-4 mr-2 text-gray-400" />
                      <span>{pkg.min_participants}-{pkg.max_participants} {t('packages.participants')}</span>
                    </div>
                  )}
                  {pkg.valid_until && (
                    <div className="flex items-center text-sm">
                      <Calendar className="w-4 h-4 mr-2 text-gray-400" />
                      <span>{t('packages.validUntil')} {format(new Date(pkg.valid_until), 'PP')}</span>
                    </div>
                  )}
                </div>

                {pkg.description && (
                  <p className="text-sm text-text-secondary mb-4">{pkg.description}</p>
                )}

                <div className="flex justify-between items-center pt-4 border-t">
                  <div className="text-2xl font-bold text-primary">
                    ${parseFloat(pkg.total_price).toFixed(2)} <span className="text-sm font-normal text-gray-500">{pkg.currency_code}</span>
                  </div>
                  <div className="flex space-x-2">
                    {isClient && (
                      <Button
                        size="sm"
                        onClick={() => {
                          setSelectedPackage(pkg);
                          setShowPurchaseModal(true);
                        }}
                        icon={ShoppingCart}
                      >
                        {t('packages.purchase')}
                      </Button>
                    )}
                    {isManager && (
                      <>
                        <Button
                          size="sm"
                          variant="secondary"
                          onClick={() => {
                            // TODO: Implement edit
                            console.log('Edit package:', pkg.package_id);
                          }}
                          icon={Edit}
                        />
                        <Button
                          size="sm"
                          variant="secondary"
                          onClick={() => handleDeletePackage(pkg.package_id)}
                          icon={Trash2}
                        />
                      </>
                    )}
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* My Packages */}
      {activeTab === 'my-packages' && isClient && (
        <div className="space-y-4">
          {myPurchases.length === 0 ? (
            <Card>
              <div className="p-8 text-center">
                <Package className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-500">{t('packages.noPurchases')}</p>
              </div>
            </Card>
          ) : (
            myPurchases.map((purchase) => (
              <Card key={purchase.purchase_id}>
                <div className="p-6">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="text-lg font-semibold text-text-primary">{purchase.package_name}</h3>
                      <p className="text-sm text-text-secondary mt-1">
                        {t('packages.purchasedOn')} {format(new Date(purchase.purchase_date), 'PP')}
                      </p>
                      {purchase.dependant_name && (
                        <p className="text-sm text-text-secondary">
                          {t('packages.for')}: {purchase.dependant_name}
                        </p>
                      )}
                    </div>
                    <div className="text-right">
                      {getStatusBadge(purchase.status)}
                      <div className="mt-2 text-2xl font-bold text-primary">
                        {purchase.sessions_remaining}/{purchase.total_sessions}
                      </div>
                      <div className="text-sm text-text-secondary">{t('packages.sessionsRemaining')}</div>
                    </div>
                  </div>
                  {purchase.expire_date && (
                    <div className="mt-4 pt-4 border-t">
                      <p className="text-sm text-text-secondary">
                        {t('packages.expiresOn')} {format(new Date(purchase.expire_date), 'PP')}
                      </p>
                    </div>
                  )}
                </div>
              </Card>
            ))
          )}
        </div>
      )}

      {/* Create Package Modal */}
      <Modal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        title={t('packages.createNew')}
      >
        <div className="space-y-4">
          <Input
            label={t('packages.packageName')}
            value={formData.package_name}
            onChange={(e) => setFormData({ ...formData, package_name: e.target.value })}
            required
          />
          
          <Select
            label={t('packages.packageType')}
            value={formData.package_type}
            onChange={(e) => setFormData({ ...formData, package_type: e.target.value as any })}
            options={[
              { value: 'tecfee', label: t('packages.types.tecfee') },
              { value: 'bundle', label: t('packages.types.bundle') },
              { value: 'custom', label: t('packages.types.custom') }
            ]}
          />
          
          <div className="grid grid-cols-2 gap-4">
            <Input
              type="number"
              label={t('packages.sessionCount')}
              value={formData.session_count}
              onChange={(e) => setFormData({ ...formData, session_count: parseInt(e.target.value) })}
              min={1}
              required
            />
            
            <Input
              type="number"
              label={t('packages.sessionDuration')}
              value={formData.session_duration_minutes}
              onChange={(e) => setFormData({ ...formData, session_duration_minutes: parseInt(e.target.value) })}
              min={15}
              step={15}
              required
            />
          </div>
          
          <Input
            type="number"
            label={t('packages.price')}
            value={formData.total_price}
            onChange={(e) => setFormData({ ...formData, total_price: e.target.value })}
            min={0}
            step={0.01}
            required
          />
          
          <div className="space-y-2">
            <Toggle
              label={t('packages.isGroupPackage')}
              checked={formData.is_group_package}
              onChange={(checked) => setFormData({ ...formData, is_group_package: checked })}
            />
            
            {formData.is_group_package && (
              <div className="grid grid-cols-2 gap-4 mt-2">
                <Input
                  type="number"
                  label={t('packages.minParticipants')}
                  value={formData.min_participants}
                  onChange={(e) => setFormData({ ...formData, min_participants: parseInt(e.target.value) })}
                  min={1}
                />
                <Input
                  type="number"
                  label={t('packages.maxParticipants')}
                  value={formData.max_participants}
                  onChange={(e) => setFormData({ ...formData, max_participants: parseInt(e.target.value) })}
                  min={formData.min_participants}
                />
              </div>
            )}
          </div>
          
          <Input
            type="date"
            label={t('packages.validUntil')}
            value={formData.valid_until}
            onChange={(e) => setFormData({ ...formData, valid_until: e.target.value })}
          />
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('packages.description')}
            </label>
            <textarea
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
              rows={3}
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('packages.termsAndConditions')}
            </label>
            <textarea
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
              rows={3}
              value={formData.terms_and_conditions}
              onChange={(e) => setFormData({ ...formData, terms_and_conditions: e.target.value })}
            />
          </div>
          
          <div className="flex justify-end space-x-2 pt-4">
            <Button variant="secondary" onClick={() => setShowCreateModal(false)}>
              {t('common.cancel')}
            </Button>
            <Button onClick={handleCreatePackage}>
              {t('common.create')}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Purchase Confirmation Modal */}
      <Modal
        isOpen={showPurchaseModal}
        onClose={() => setShowPurchaseModal(false)}
        title={t('packages.confirmPurchase')}
      >
        {selectedPackage && (
          <div>
            <div className="mb-4">
              <h3 className="font-semibold text-lg">{selectedPackage.package_name}</h3>
              <p className="text-gray-600">{selectedPackage.description}</p>
            </div>
            
            <div className="bg-gray-50 p-4 rounded-lg mb-4">
              <div className="flex justify-between items-center">
                <span>{selectedPackage.session_count} {t('packages.sessions')}</span>
                <span className="font-semibold">${parseFloat(selectedPackage.total_price).toFixed(2)} {selectedPackage.currency_code}</span>
              </div>
            </div>
            
            {selectedPackage.terms_and_conditions && (
              <div className="mb-4">
                <h4 className="font-medium mb-2">{t('packages.termsAndConditions')}</h4>
                <p className="text-sm text-gray-600">{selectedPackage.terms_and_conditions}</p>
              </div>
            )}
            
            <div className="flex justify-end space-x-2">
              <Button variant="secondary" onClick={() => setShowPurchaseModal(false)}>
                {t('common.cancel')}
              </Button>
              <Button onClick={() => handlePurchasePackage(selectedPackage.package_id)}>
                {t('packages.confirmPurchase')}
              </Button>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default PackagesPage;