-- Verification script to check auth_tokens table
-- Run this after creating the table to verify it exists and has the correct structure

-- Check if auth_tokens table exists
SELECT 
    EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'auth_tokens'
    ) as table_exists;

-- Show table structure
SELECT 
    column_name,
    data_type,
    character_maximum_length,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_name = 'auth_tokens'
ORDER BY ordinal_position;

-- Show indexes
SELECT 
    indexname,
    indexdef
FROM pg_indexes
WHERE tablename = 'auth_tokens';

-- Show constraints
SELECT 
    conname AS constraint_name,
    pg_get_constraintdef(oid) AS constraint_definition
FROM pg_constraint
WHERE conrelid = 'auth_tokens'::regclass;

-- Count existing tokens (should be 0 for new table)
SELECT 
    token_type,
    COUNT(*) as count
FROM auth_tokens
GROUP BY token_type;