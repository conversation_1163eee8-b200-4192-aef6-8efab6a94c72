"""
Comprehensive translation service for TutorAide Quebec market.
Handles English/French localization with Quebec-specific features.
"""

import json
import os
import re
from typing import Dict, Any, Optional, List, Union
from pathlib import Path
from functools import lru_cache
from datetime import datetime, date, time

from .constants import (
    SUPPORTED_LANGUAGES, DEFAULT_LANGUAGE, FALLBACK_LANGUAGE,
    PLURALIZATION_RULES, TEMPLATE_PATTERNS, TRANSLATION_VALIDATION,
    CONTENT_CATEGORIES, QUEBEC_FORMATTING
)


class TranslationService:
    """Comprehensive service for managing translations and Quebec-specific localization."""
    
    def __init__(self, locales_path: Optional[Path] = None):
        """Initialize translation service with comprehensive features."""
        if locales_path is None:
            locales_path = Path(__file__).parent
        
        self.locales_path = locales_path
        self._translations: Dict[str, Dict[str, Any]] = {}
        self._translation_cache: Dict[str, str] = {}
        self._missing_keys: Dict[str, List[str]] = {lang: [] for lang in SUPPORTED_LANGUAGES}
        self._load_translations()
        self._validate_translations()
    
    def _load_translations(self) -> None:
        """Load all translation files."""
        for language in SUPPORTED_LANGUAGES:
            translation_file = self.locales_path / f"{language}.json"
            if translation_file.exists():
                try:
                    with open(translation_file, 'r', encoding='utf-8') as f:
                        self._translations[language] = json.load(f)
                except (json.JSONDecodeError, IOError) as e:
                    print(f"Warning: Could not load translations for {language}: {e}")
                    self._translations[language] = {}
            else:
                print(f"Warning: Translation file not found: {translation_file}")
                self._translations[language] = {}
    
    def get_translation(
        self, 
        key: str, 
        language: str = DEFAULT_LANGUAGE,
        **kwargs
    ) -> str:
        """Get translation for a key in the specified language."""
        # Ensure language is supported
        if language not in SUPPORTED_LANGUAGES:
            language = DEFAULT_LANGUAGE
        
        # Get translation from loaded files
        translations = self._translations.get(language, {})
        
        # Support nested keys with dot notation (e.g., "auth.login.title")
        value = self._get_nested_value(translations, key)
        
        # Fallback to default language if translation not found
        if value is None and language != DEFAULT_LANGUAGE:
            default_translations = self._translations.get(DEFAULT_LANGUAGE, {})
            value = self._get_nested_value(default_translations, key)
        
        # Fallback to key itself if no translation found
        if value is None:
            value = key
        
        # Format with provided kwargs if it's a string
        if isinstance(value, str) and kwargs:
            try:
                return value.format(**kwargs)
            except (KeyError, ValueError):
                return value
        
        return str(value)
    
    def _get_nested_value(self, data: Dict[str, Any], key: str) -> Optional[Any]:
        """Get value from nested dictionary using dot notation."""
        keys = key.split('.')
        current = data
        
        for k in keys:
            if isinstance(current, dict) and k in current:
                current = current[k]
            else:
                return None
        
        return current
    
    def get_translations_for_namespace(
        self, 
        namespace: str, 
        language: str = DEFAULT_LANGUAGE
    ) -> Dict[str, Any]:
        """Get all translations for a specific namespace (e.g., 'auth', 'billing')."""
        if language not in SUPPORTED_LANGUAGES:
            language = DEFAULT_LANGUAGE
        
        translations = self._translations.get(language, {})
        return self._get_nested_value(translations, namespace) or {}
    
    def get_available_languages(self) -> Dict[str, str]:
        """Get list of available languages with their display names."""
        from .constants import LANGUAGE_NAMES
        
        available = {}
        for lang in SUPPORTED_LANGUAGES:
            if lang in self._translations and self._translations[lang]:
                available[lang] = LANGUAGE_NAMES.get(DEFAULT_LANGUAGE, {}).get(lang, lang)
        
        return available
    
    def reload_translations(self) -> None:
        """Reload translation files (useful for development)."""
        self._translations.clear()
        self._load_translations()
    
    def format_currency(self, amount: float, language: str = DEFAULT_LANGUAGE) -> str:
        """Format currency amount according to language conventions."""
        from .constants import CURRENCY_FORMATS
        
        if language not in SUPPORTED_LANGUAGES:
            language = DEFAULT_LANGUAGE
        
        currency_format = CURRENCY_FORMATS.get(language, CURRENCY_FORMATS[DEFAULT_LANGUAGE])
        
        # Format with proper decimal and thousands separators
        decimal_sep = currency_format["decimal_separator"]
        thousands_sep = currency_format["thousands_separator"]
        
        # Format the number
        formatted_amount = f"{amount:,.2f}".replace(",", "TEMP").replace(".", decimal_sep).replace("TEMP", thousands_sep)
        
        return currency_format["format"].format(amount=formatted_amount)
    
    def format_date(self, date_obj, format_type: str = "medium", language: str = DEFAULT_LANGUAGE) -> str:
        """Format date according to language conventions."""
        from datetime import date, datetime
        from .constants import DATE_FORMATS
        
        if language not in SUPPORTED_LANGUAGES:
            language = DEFAULT_LANGUAGE
        
        date_formats = DATE_FORMATS.get(language, DATE_FORMATS[DEFAULT_LANGUAGE])
        format_pattern = date_formats.get(format_type, date_formats["medium"])
        
        if isinstance(date_obj, (date, datetime)):
            return date_obj.strftime(format_pattern)
        
        return str(date_obj)
    
    def format_time(self, time_obj, format_type: str = "short", language: str = DEFAULT_LANGUAGE) -> str:
        """Format time according to language conventions."""
        from datetime import time, datetime
        from .constants import TIME_FORMATS
        
        if language not in SUPPORTED_LANGUAGES:
            language = DEFAULT_LANGUAGE
        
        time_formats = TIME_FORMATS.get(language, TIME_FORMATS[DEFAULT_LANGUAGE])
        format_pattern = time_formats.get(format_type, time_formats["short"])
        
        if isinstance(time_obj, (time, datetime)):
            return time_obj.strftime(format_pattern)
        
        return str(time_obj)
    
    def format_number(self, number: Union[int, float], language: str = DEFAULT_LANGUAGE) -> str:
        """Format number according to language conventions."""
        from .constants import NUMBER_FORMATS
        
        if language not in SUPPORTED_LANGUAGES:
            language = DEFAULT_LANGUAGE
        
        number_format = NUMBER_FORMATS.get(language, NUMBER_FORMATS[DEFAULT_LANGUAGE])
        
        # Format with proper decimal and thousands separators
        decimal_sep = number_format["decimal_separator"]
        thousands_sep = number_format["thousands_separator"]
        
        # Handle integer vs float
        if isinstance(number, int):
            formatted = f"{number:,}".replace(",", thousands_sep)
        else:
            formatted = f"{number:,.2f}".replace(",", "TEMP").replace(".", decimal_sep).replace("TEMP", thousands_sep)
        
        return formatted
    
    def pluralize(
        self, 
        key: str, 
        count: int, 
        language: str = DEFAULT_LANGUAGE, 
        **kwargs
    ) -> str:
        """Get pluralized translation based on count."""
        # Determine plural form based on language rules
        rules = PLURALIZATION_RULES.get(language, PLURALIZATION_RULES[DEFAULT_LANGUAGE])
        
        if count == 0:
            plural_key = f"{key}.{rules['zero']}"
        elif count == 1:
            plural_key = f"{key}.{rules['one']}"
        else:
            plural_key = f"{key}.{rules['other']}"
        
        # Try to get pluralized version, fallback to base key
        translation = self.get_translation(plural_key, language, count=count, **kwargs)
        if translation == plural_key:  # Key not found, try base key
            translation = self.get_translation(key, language, count=count, **kwargs)
        
        return translation
    
    def get_translation_with_fallback(
        self,
        key: str,
        language: str = DEFAULT_LANGUAGE,
        fallback_languages: Optional[List[str]] = None,
        **kwargs
    ) -> str:
        """Get translation with multiple fallback languages."""
        if fallback_languages is None:
            fallback_languages = [FALLBACK_LANGUAGE] if language != FALLBACK_LANGUAGE else []
        
        # Try primary language
        translation = self.get_translation(key, language, **kwargs)
        if translation != key:  # Found translation
            return translation
        
        # Try fallback languages
        for fallback_lang in fallback_languages:
            if fallback_lang in SUPPORTED_LANGUAGES:
                translation = self.get_translation(key, fallback_lang, **kwargs)
                if translation != key:
                    return translation
        
        # Log missing key for monitoring
        if key not in self._missing_keys.get(language, []):
            self._missing_keys[language].append(key)
        
        return key
    
    def validate_translation_completeness(self) -> Dict[str, List[str]]:
        """Validate that all required keys exist in all languages."""
        required_keys = TRANSLATION_VALIDATION["required_keys"]
        missing_by_language = {}
        
        for language in SUPPORTED_LANGUAGES:
            missing_keys = []
            for required_key in required_keys:
                if self._get_nested_value(self._translations.get(language, {}), required_key) is None:
                    missing_keys.append(required_key)
            missing_by_language[language] = missing_keys
        
        return missing_by_language
    
    def validate_template_variables(self, template: str, variables: Dict[str, Any]) -> Dict[str, Any]:
        """Validate template variables and return validation results."""
        # Extract variables from template
        variable_pattern = re.compile(TEMPLATE_PATTERNS["variable"])
        template_vars = set(variable_pattern.findall(template))
        
        provided_vars = set(variables.keys())
        
        return {
            "missing_variables": list(template_vars - provided_vars),
            "extra_variables": list(provided_vars - template_vars),
            "valid": len(template_vars - provided_vars) == 0
        }
    
    def render_template(
        self,
        template: str,
        variables: Dict[str, Any],
        language: str = DEFAULT_LANGUAGE
    ) -> str:
        """Render template with variables and language-specific formatting."""
        # Validate variables
        validation = self.validate_template_variables(template, variables)
        
        # Format variables according to language conventions
        formatted_vars = {}
        for key, value in variables.items():
            if isinstance(value, (int, float)) and key.endswith('_amount'):
                formatted_vars[key] = self.format_currency(value, language)
            elif isinstance(value, (int, float)):
                formatted_vars[key] = self.format_number(value, language)
            elif isinstance(value, (date, datetime)):
                formatted_vars[key] = self.format_date(value, "medium", language)
            elif isinstance(value, time):
                formatted_vars[key] = self.format_time(value, "short", language)
            else:
                formatted_vars[key] = str(value)
        
        # Render template
        try:
            return template.format(**formatted_vars)
        except KeyError as e:
            # Handle missing variables gracefully
            return template.format(**{**formatted_vars, str(e).strip("'"): f"[MISSING: {e}]"})
    
    def get_quebec_formatting(self, format_type: str) -> Dict[str, Any]:
        """Get Quebec-specific formatting rules."""
        return QUEBEC_FORMATTING.get(format_type, {})
    
    def format_quebec_phone(self, phone: str) -> str:
        """Format phone number according to Quebec standards."""
        # Remove all non-digits
        digits = re.sub(r'\D', '', phone)
        
        # Format as (XXX) XXX-XXXX for 10-digit numbers
        if len(digits) == 10:
            return f"({digits[:3]}) {digits[3:6]}-{digits[6:]}"
        elif len(digits) == 11 and digits[0] == '1':
            return f"1 ({digits[1:4]}) {digits[4:7]}-{digits[7:]}"
        
        return phone  # Return original if can't format
    
    def format_quebec_postal_code(self, postal_code: str) -> str:
        """Format postal code according to Quebec standards."""
        # Remove spaces and convert to uppercase
        clean_code = postal_code.replace(" ", "").upper()
        
        # Format as XXX XXX for 6-character codes
        if len(clean_code) == 6 and re.match(r'^[A-Z]\d[A-Z]\d[A-Z]\d$', clean_code):
            return f"{clean_code[:3]} {clean_code[3:]}"
        
        return postal_code  # Return original if can't format
    
    def _validate_translations(self) -> None:
        """Validate translation files for consistency and completeness."""
        # Check for forbidden HTML in translations
        forbidden_html = TRANSLATION_VALIDATION["forbidden_html"]
        
        for language, translations in self._translations.items():
            self._validate_nested_dict(translations, forbidden_html, language)
    
    def _validate_nested_dict(self, data: Dict[str, Any], forbidden_html: List[str], language: str, path: str = "") -> None:
        """Recursively validate nested translation dictionary."""
        for key, value in data.items():
            current_path = f"{path}.{key}" if path else key
            
            if isinstance(value, dict):
                self._validate_nested_dict(value, forbidden_html, language, current_path)
            elif isinstance(value, str):
                # Check for forbidden HTML
                for forbidden in forbidden_html:
                    if forbidden.lower() in value.lower():
                        print(f"Warning: Forbidden HTML '{forbidden}' found in {language}:{current_path}")
    
    def get_missing_translations(self, language: str) -> List[str]:
        """Get list of missing translation keys for a language."""
        return self._missing_keys.get(language, [])
    
    def get_translation_statistics(self) -> Dict[str, Any]:
        """Get comprehensive translation statistics."""
        stats = {
            "languages": SUPPORTED_LANGUAGES,
            "total_keys_by_language": {},
            "missing_keys_by_language": {},
            "completion_percentage": {},
            "categories": list(CONTENT_CATEGORIES)
        }
        
        for language in SUPPORTED_LANGUAGES:
            total_keys = self._count_nested_keys(self._translations.get(language, {}))
            missing_keys = len(self._missing_keys.get(language, []))
            
            stats["total_keys_by_language"][language] = total_keys
            stats["missing_keys_by_language"][language] = missing_keys
            
            if total_keys > 0:
                completion = ((total_keys - missing_keys) / total_keys) * 100
                stats["completion_percentage"][language] = round(completion, 2)
            else:
                stats["completion_percentage"][language] = 0.0
        
        return stats
    
    def _count_nested_keys(self, data: Dict[str, Any]) -> int:
        """Count total number of translation keys recursively."""
        count = 0
        for value in data.values():
            if isinstance(value, dict):
                count += self._count_nested_keys(value)
            else:
                count += 1
        return count


# Global translation service instance
_translation_service: Optional[TranslationService] = None


@lru_cache(maxsize=1)
def get_translation_service() -> TranslationService:
    """Get global translation service instance."""
    global _translation_service
    if _translation_service is None:
        _translation_service = TranslationService()
    return _translation_service


# Convenience functions for common translation operations

def t(key: str, language: str = DEFAULT_LANGUAGE, **kwargs) -> str:
    """Convenience function for getting translations."""
    return get_translation_service().get_translation(key, language, **kwargs)


def t_fallback(key: str, language: str = DEFAULT_LANGUAGE, fallback_languages: Optional[List[str]] = None, **kwargs) -> str:
    """Convenience function for getting translations with fallback."""
    return get_translation_service().get_translation_with_fallback(key, language, fallback_languages, **kwargs)


def t_plural(key: str, count: int, language: str = DEFAULT_LANGUAGE, **kwargs) -> str:
    """Convenience function for pluralized translations."""
    return get_translation_service().pluralize(key, count, language, **kwargs)


def format_currency(amount: float, language: str = DEFAULT_LANGUAGE) -> str:
    """Convenience function for formatting currency."""
    return get_translation_service().format_currency(amount, language)


def format_date(date_obj, format_type: str = "medium", language: str = DEFAULT_LANGUAGE) -> str:
    """Convenience function for formatting dates."""
    return get_translation_service().format_date(date_obj, format_type, language)


def format_time(time_obj, format_type: str = "short", language: str = DEFAULT_LANGUAGE) -> str:
    """Convenience function for formatting times."""
    return get_translation_service().format_time(time_obj, format_type, language)


def format_number(number: Union[int, float], language: str = DEFAULT_LANGUAGE) -> str:
    """Convenience function for formatting numbers."""
    return get_translation_service().format_number(number, language)


def render_template(template: str, variables: Dict[str, Any], language: str = DEFAULT_LANGUAGE) -> str:
    """Convenience function for rendering templates."""
    return get_translation_service().render_template(template, variables, language)


def format_quebec_phone(phone: str) -> str:
    """Convenience function for formatting Quebec phone numbers."""
    return get_translation_service().format_quebec_phone(phone)


def format_quebec_postal_code(postal_code: str) -> str:
    """Convenience function for formatting Quebec postal codes."""
    return get_translation_service().format_quebec_postal_code(postal_code)


def get_translation_stats() -> Dict[str, Any]:
    """Convenience function for getting translation statistics."""
    return get_translation_service().get_translation_statistics()