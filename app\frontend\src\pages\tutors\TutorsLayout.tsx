import React from 'react';
import { Outlet } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { TabNavigation, Tab } from '../../components/layout/TabNavigation';
import { GraduationCap, Mail, FileCheck } from 'lucide-react';

const TutorsLayout: React.FC = () => {
  const { t } = useTranslation();

  const tabs: Tab[] = [
    {
      id: 'profiles',
      label: t('tutors.profiles'),
      path: '/tutors/profiles',
      icon: <GraduationCap />,
    },
    {
      id: 'invitations',
      label: t('tutors.invitations'),
      path: '/tutors/invitations',
      icon: <Mail />,
    },
    {
      id: 'applications',
      label: t('tutors.applications'),
      path: '/tutors/applications',
      icon: <FileCheck />,
    },
  ];

  return (
    <div className="h-full flex flex-col">
      <TabNavigation tabs={tabs} />
      <div className="flex-1 overflow-hidden">
        <Outlet />
      </div>
    </div>
  );
};

export default TutorsLayout;