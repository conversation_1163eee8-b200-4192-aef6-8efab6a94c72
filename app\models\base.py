from datetime import datetime
from typing import Optional, Any, Dict, List
from decimal import Decimal
from enum import Enum
from pydantic import BaseModel, Field, ConfigDict, field_validator
from app.core.timezone import now_est


class UserRoleType(str, Enum):
    """User role types."""
    MANAGER = "manager"
    TUTOR = "tutor"
    CLIENT = "client"


class ConsentLevel(str, Enum):
    """Consent levels."""
    LEVEL_1 = "level_1"  # Mandatory
    LEVEL_2 = "level_2"  # Optional


class ConsentType(str, Enum):
    """Consent types."""
    TERMS_OF_SERVICE = "terms_of_service"
    PRIVACY_POLICY = "privacy_policy"
    MARKETING = "marketing"


class AppointmentStatus(str, Enum):
    """Appointment status values."""
    SCHEDULED = "scheduled"
    CONFIRMED = "confirmed"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    NO_SHOW = "no_show"


class PaymentStatus(str, Enum):
    """Payment status values."""
    PENDING = "pending"
    PROCESSING = "processing"
    PAID = "paid"
    FAILED = "failed"
    REFUNDED = "refunded"
    CANCELLED = "cancelled"


class TutorPaymentStatus(str, Enum):
    """Tutor payment status values."""
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    PROCESSING = "processing"
    PAID = "paid"
    FAILED = "failed"


class Currency(str, Enum):
    """Supported currencies."""
    CAD = "CAD"


class BaseEntity(BaseModel):
    """Base model for all entities with common fields."""
    
    model_config = ConfigDict(
        from_attributes=True,
        use_enum_values=True,
        json_encoders={
            datetime: lambda v: v.isoformat(),
            Decimal: lambda v: str(v)
        }
    )
    
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    deleted_at: Optional[datetime] = Field(None, description="Soft deletion timestamp")
    
    def is_deleted(self) -> bool:
        """Check if entity is soft deleted."""
        return self.deleted_at is not None
    
    def is_active(self) -> bool:
        """Check if entity is active (not soft deleted)."""
        return self.deleted_at is None


class IdentifiedEntity(BaseEntity):
    """Base model for entities with ID fields."""
    
    # Note: Specific ID field (user_id, client_id, etc.) should be defined in subclasses
    pass


class AddressModel(BaseModel):
    """Address information model."""
    
    model_config = ConfigDict(from_attributes=True)
    
    street: str = Field(..., description="Street address")
    city: str = Field(..., description="City")
    province: str = Field(..., description="Province/State")
    postal_code: str = Field(..., description="Postal/ZIP code")
    country: str = Field(default="Canada", description="Country")
    
    @field_validator('postal_code')
    @classmethod
    def validate_postal_code(cls, v: str) -> str:
        """Validate Canadian postal code format."""
        import re
        # Canadian postal code pattern: A1A 1A1
        postal_pattern = r'^[A-Z]\d[A-Z]\s?\d[A-Z]\d$'
        cleaned = v.upper().strip()
        if not re.match(postal_pattern, cleaned):
            raise ValueError('Invalid Canadian postal code format')
        # Ensure space in middle
        if len(cleaned) == 6:
            cleaned = f"{cleaned[:3]} {cleaned[3:]}"
        return cleaned


class ContactModel(BaseModel):
    """Contact information model."""
    
    model_config = ConfigDict(from_attributes=True)
    
    name: str = Field(..., description="Contact name")
    phone: str = Field(..., description="Phone number")
    relationship: str = Field(..., description="Relationship to user")
    
    @field_validator('phone')
    @classmethod
    def validate_phone(cls, v: str) -> str:
        """Validate phone number format."""
        import re
        # Remove all non-digit characters
        digits = re.sub(r'\D', '', v)
        
        # Check if it's a valid North American number
        if len(digits) == 10:
            return f"+1{digits}"
        elif len(digits) == 11 and digits.startswith('1'):
            return f"+{digits}"
        else:
            raise ValueError('Invalid phone number format')


class LocationDetails(BaseModel):
    """Location details for appointments."""
    
    model_config = ConfigDict(from_attributes=True)
    
    type: str = Field(..., description="Location type (online, in_person, library)")
    address: Optional[AddressModel] = Field(None, description="Physical address if applicable")
    meeting_link: Optional[str] = Field(None, description="Online meeting link")
    room_number: Optional[str] = Field(None, description="Room number or specific location")
    instructions: Optional[str] = Field(None, description="Additional location instructions")


class TimeSlot(BaseModel):
    """Time slot model for availability and scheduling."""
    
    model_config = ConfigDict(from_attributes=True)
    
    start_time: datetime = Field(..., description="Start time")
    end_time: datetime = Field(..., description="End time")
    is_available: bool = Field(True, description="Whether slot is available")
    reason: Optional[str] = Field(None, description="Reason if unavailable")
    
    @field_validator('end_time')
    @classmethod
    def validate_end_time(cls, v: datetime, info) -> datetime:
        """Validate end time is after start time."""
        if 'start_time' in info.data and v <= info.data['start_time']:
            raise ValueError('End time must be after start time')
        return v


class PaginatedResponse(BaseModel):
    """Standard pagination response."""
    
    items: list
    total: int = Field(..., description="Total number of items")
    page: int = Field(..., description="Current page number (1-based)")
    size: int = Field(..., description="Number of items per page")
    pages: int = Field(..., description="Total number of pages")
    has_next: bool = Field(..., description="Whether there is a next page")
    has_prev: bool = Field(..., description="Whether there is a previous page")
    
    @classmethod
    def create(cls, items: list, total: int, page: int, size: int):
        """Create paginated response."""
        pages = (total + size - 1) // size  # Ceiling division
        
        return cls(
            items=items,
            total=total,
            page=page,
            size=size,
            pages=pages,
            has_next=page < pages,
            has_prev=page > 1
        )


class SearchFilters(BaseModel):
    """Base search filters."""
    
    model_config = ConfigDict(from_attributes=True)
    
    query: Optional[str] = Field(None, description="Search query")
    page: int = Field(1, ge=1, description="Page number (1-based)")
    size: int = Field(20, ge=1, le=100, description="Items per page")
    sort_by: Optional[str] = Field(None, description="Sort field")
    sort_order: Optional[str] = Field("asc", description="Sort order")
    include_deleted: bool = Field(False, description="Include soft-deleted records")
    
    @field_validator('sort_order')
    @classmethod
    def validate_sort_order(cls, v: str) -> str:
        """Validate sort order."""
        if v.lower() not in ('asc', 'desc'):
            raise ValueError('Sort order must be "asc" or "desc"')
        return v.lower()
    
    def get_offset(self) -> int:
        """Get SQL offset for pagination."""
        return (self.page - 1) * self.size
    
    def get_limit(self) -> int:
        """Get SQL limit for pagination."""
        return self.size


class SuccessResponse(BaseModel):
    """Standard success response."""
    
    model_config = ConfigDict(from_attributes=True)
    
    success: bool = True
    message: str
    data: Optional[Dict[str, Any]] = None


class ErrorResponse(BaseModel):
    """Standard error response."""
    
    model_config = ConfigDict(from_attributes=True)
    
    success: bool = False
    error: str
    message: str
    details: Optional[Dict[str, Any]] = None
    timestamp: datetime = Field(default_factory=now_est)


class ValidationErrorResponse(BaseModel):
    """Validation error response."""
    
    model_config = ConfigDict(from_attributes=True)
    
    success: bool = False
    error: str = "validation_error"
    message: str = "Validation failed"
    field_errors: List[Dict[str, Any]] = Field(default_factory=list)
    timestamp: datetime = Field(default_factory=now_est)