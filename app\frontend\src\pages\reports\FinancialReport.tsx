import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  <PERSON><PERSON><PERSON>, Line, BarChart, Bar, PieChart, Pie, Cell,
  XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer
} from 'recharts';
import { Download, Calendar, TrendingUp, TrendingDown, DollarSign } from 'lucide-react';
import { format } from 'date-fns';
import { useApi } from '../../hooks/useApi';
import toast from 'react-hot-toast';

interface RevenueData {
  month: string;
  revenue: number;
  expenses: number;
  profit: number;
}

interface ExpenseBreakdown {
  name: string;
  value: number;
  color: string;
}

interface RevenueByService {
  service: string;
  revenue: number;
}

interface Transaction {
  transaction_id: number;
  date: string;
  description: string;
  category: 'revenue' | 'expense';
  amount: number;
}

interface FinancialMetrics {
  totalRevenue: number;
  totalExpenses: number;
  totalProfit: number;
  profitMargin: number;
  revenueGrowth: number;
  expenseGrowth: number;
  profitGrowth: number;
  profitMarginChange: number;
}

const FinancialReport: React.FC = () => {
  const { t } = useTranslation();
  const { get, loading } = useApi();
  
  const [dateRange, setDateRange] = useState('last6months');
  const [revenueData, setRevenueData] = useState<RevenueData[]>([]);
  const [expenseBreakdown, setExpenseBreakdown] = useState<ExpenseBreakdown[]>([]);
  const [revenueByService, setRevenueByService] = useState<RevenueByService[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [metrics, setMetrics] = useState<FinancialMetrics | null>(null);
  
  // Load financial report data
  const loadFinancialData = async () => {
    try {
      const [metricsResponse, revenueResponse, expensesResponse, servicesResponse, transactionsResponse] = await Promise.all([
        get<FinancialMetrics>('/reports/financial/metrics', { params: { period: dateRange } }),
        get<RevenueData[]>('/reports/financial/revenue-trend', { params: { period: dateRange } }),
        get<ExpenseBreakdown[]>('/reports/financial/expense-breakdown', { params: { period: dateRange } }),
        get<RevenueByService[]>('/reports/financial/revenue-by-service', { params: { period: dateRange } }),
        get<Transaction[]>('/reports/financial/recent-transactions', { params: { limit: 10 } })
      ]);
      
      setMetrics(metricsResponse);
      setRevenueData(revenueResponse);
      setExpenseBreakdown(expensesResponse);
      setRevenueByService(servicesResponse);
      setTransactions(transactionsResponse);
      
    } catch (error) {
      console.error('Error loading financial data:', error);
      toast.error('Failed to load financial report data');
    }
  };
  
  useEffect(() => {
    loadFinancialData();
  }, [dateRange]);
  
  const handleExport = async () => {
    try {
      const response = await get('/reports/financial/export', { 
        params: { period: dateRange, format: 'pdf' }
      });
      
      // Create download link
      const blob = new Blob([response], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `financial-report-${dateRange}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      toast.success('Financial report exported successfully');
    } catch (error) {
      console.error('Error exporting report:', error);
      toast.error('Failed to export financial report');
    }
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-semibold text-text-primary">
            {t('sidebar.financial')} Report
          </h1>
          <div className="flex items-center gap-3">
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="px-4 py-2 border border-primary-300 rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
              disabled={loading}
            >
              <option value="last30days">Last 30 days</option>
              <option value="last3months">Last 3 months</option>
              <option value="last6months">Last 6 months</option>
              <option value="lastyear">Last year</option>
            </select>
            <button 
              onClick={handleExport}
              disabled={loading}
              className="flex items-center gap-2 px-4 py-2 bg-accent-red text-white rounded-soft hover:bg-accent-red-dark transition-colors disabled:opacity-50"
            >
              <Download className="w-4 h-4" />
              Export
            </button>
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      {loading && !metrics ? (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white rounded-large shadow-soft p-6 animate-pulse">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-gray-200 rounded-medium"></div>
                <div className="w-16 h-4 bg-gray-200 rounded"></div>
              </div>
              <div className="w-20 h-8 bg-gray-200 rounded mb-2"></div>
              <div className="w-24 h-4 bg-gray-200 rounded"></div>
            </div>
          ))}
        </div>
      ) : metrics ? (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-large shadow-soft p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-accent-red bg-opacity-10 rounded-medium">
                <DollarSign className="w-6 h-6 text-accent-red" />
              </div>
              <span className={`flex items-center gap-1 text-sm ${
                metrics.revenueGrowth >= 0 ? 'text-accent-green' : 'text-accent-red'
              }`}>
                {metrics.revenueGrowth >= 0 ? <TrendingUp className="w-4 h-4" /> : <TrendingDown className="w-4 h-4" />}
                {metrics.revenueGrowth >= 0 ? '+' : ''}{metrics.revenueGrowth.toFixed(1)}%
              </span>
            </div>
            <h3 className="text-2xl font-semibold text-text-primary">
              ${metrics.totalRevenue.toLocaleString()}
            </h3>
            <p className="text-sm text-text-secondary mt-1">Total Revenue</p>
          </div>

          <div className="bg-white rounded-large shadow-soft p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-accent-orange bg-opacity-10 rounded-medium">
                <TrendingDown className="w-6 h-6 text-accent-orange" />
              </div>
              <span className={`flex items-center gap-1 text-sm ${
                metrics.expenseGrowth >= 0 ? 'text-accent-red' : 'text-accent-green'
              }`}>
                {metrics.expenseGrowth >= 0 ? <TrendingUp className="w-4 h-4" /> : <TrendingDown className="w-4 h-4" />}
                {metrics.expenseGrowth >= 0 ? '+' : ''}{metrics.expenseGrowth.toFixed(1)}%
              </span>
            </div>
            <h3 className="text-2xl font-semibold text-text-primary">
              ${metrics.totalExpenses.toLocaleString()}
            </h3>
            <p className="text-sm text-text-secondary mt-1">Total Expenses</p>
          </div>

          <div className="bg-white rounded-large shadow-soft p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-accent-green bg-opacity-10 rounded-medium">
                <TrendingUp className="w-6 h-6 text-accent-green" />
              </div>
              <span className={`flex items-center gap-1 text-sm ${
                metrics.profitGrowth >= 0 ? 'text-accent-green' : 'text-accent-red'
              }`}>
                {metrics.profitGrowth >= 0 ? <TrendingUp className="w-4 h-4" /> : <TrendingDown className="w-4 h-4" />}
                {metrics.profitGrowth >= 0 ? '+' : ''}{metrics.profitGrowth.toFixed(1)}%
              </span>
            </div>
            <h3 className="text-2xl font-semibold text-text-primary">
              ${metrics.totalProfit.toLocaleString()}
            </h3>
            <p className="text-sm text-text-secondary mt-1">Net Profit</p>
          </div>

          <div className="bg-white rounded-large shadow-soft p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-purple-500 bg-opacity-10 rounded-medium">
                <TrendingUp className="w-6 h-6 text-purple-500" />
              </div>
              <span className={`flex items-center gap-1 text-sm ${
                metrics.profitMarginChange >= 0 ? 'text-accent-green' : 'text-accent-red'
              }`}>
                {metrics.profitMarginChange >= 0 ? <TrendingUp className="w-4 h-4" /> : <TrendingDown className="w-4 h-4" />}
                {metrics.profitMarginChange >= 0 ? '+' : ''}{metrics.profitMarginChange.toFixed(1)}%
              </span>
            </div>
            <h3 className="text-2xl font-semibold text-text-primary">
              {metrics.profitMargin.toFixed(1)}%
            </h3>
            <p className="text-sm text-text-secondary mt-1">Profit Margin</p>
          </div>
        </div>
      ) : null}

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Trend */}
        <div className="bg-white rounded-large shadow-soft p-6">
          <h2 className="text-lg font-semibold text-text-primary mb-4">
            Revenue Trend
          </h2>
          {loading && revenueData.length === 0 ? (
            <div className="w-full h-[300px] bg-gray-100 rounded animate-pulse flex items-center justify-center">
              <span className="text-gray-400">Loading chart...</span>
            </div>
          ) : revenueData.length > 0 ? (
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={revenueData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#E9ECEF" />
              <XAxis dataKey="month" stroke="#6C757D" />
              <YAxis stroke="#6C757D" />
              <Tooltip />
              <Legend />
              <Line 
                type="monotone" 
                dataKey="revenue" 
                stroke="#007AFF" 
                strokeWidth={2}
                name="Revenue"
              />
              <Line 
                type="monotone" 
                dataKey="expenses" 
                stroke="#FF3B30" 
                strokeWidth={2}
                name="Expenses"
              />
              <Line 
                type="monotone" 
                dataKey="profit" 
                stroke="#34C759" 
                strokeWidth={2}
                name="Profit"
              />
              </LineChart>
            </ResponsiveContainer>
          ) : (
            <div className="w-full h-[300px] flex items-center justify-center text-gray-500">
              No revenue data available
            </div>
          )}
        </div>

        {/* Expense Breakdown */}
        <div className="bg-white rounded-large shadow-soft p-6">
          <h2 className="text-lg font-semibold text-text-primary mb-4">
            Expense Breakdown
          </h2>
          {loading && expenseBreakdown.length === 0 ? (
            <div className="w-full h-[300px] bg-gray-100 rounded animate-pulse flex items-center justify-center">
              <span className="text-gray-400">Loading chart...</span>
            </div>
          ) : expenseBreakdown.length > 0 ? (
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
              <Pie
                data={expenseBreakdown}
                cx="50%"
                cy="50%"
                outerRadius={100}
                fill="#8884d8"
                dataKey="value"
                label={({ name, value }) => `${name}: ${value}%`}
              >
                {expenseBreakdown.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          ) : (
            <div className="w-full h-[300px] flex items-center justify-center text-gray-500">
              No expense data available
            </div>
          )}
        </div>

        {/* Revenue by Service */}
        <div className="bg-white rounded-large shadow-soft p-6 lg:col-span-2">
          <h2 className="text-lg font-semibold text-text-primary mb-4">
            Revenue by Service
          </h2>
          {loading && revenueByService.length === 0 ? (
            <div className="w-full h-[300px] bg-gray-100 rounded animate-pulse flex items-center justify-center">
              <span className="text-gray-400">Loading chart...</span>
            </div>
          ) : revenueByService.length > 0 ? (
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={revenueByService}>
              <CartesianGrid strokeDasharray="3 3" stroke="#E9ECEF" />
              <XAxis dataKey="service" stroke="#6C757D" />
              <YAxis stroke="#6C757D" />
              <Tooltip />
              <Bar dataKey="revenue" fill="#007AFF" radius={[8, 8, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          ) : (
            <div className="w-full h-[300px] flex items-center justify-center text-gray-500">
              No service revenue data available
            </div>
          )}
        </div>
      </div>

      {/* Recent Transactions */}
      <div className="mt-8 bg-white rounded-large shadow-soft p-6">
        <h2 className="text-lg font-semibold text-text-primary mb-4">
          Recent Transactions
        </h2>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-primary-200">
                <th className="text-left py-3 px-4 text-sm font-medium text-text-secondary">
                  Date
                </th>
                <th className="text-left py-3 px-4 text-sm font-medium text-text-secondary">
                  Description
                </th>
                <th className="text-left py-3 px-4 text-sm font-medium text-text-secondary">
                  Category
                </th>
                <th className="text-right py-3 px-4 text-sm font-medium text-text-secondary">
                  Amount
                </th>
              </tr>
            </thead>
            <tbody>
              {loading && transactions.length === 0 ? (
                [...Array(5)].map((_, i) => (
                  <tr key={i} className="border-b border-primary-100 animate-pulse">
                    <td className="py-3 px-4">
                      <div className="w-20 h-4 bg-gray-200 rounded"></div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="w-32 h-4 bg-gray-200 rounded"></div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="w-16 h-6 bg-gray-200 rounded-full"></div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="w-16 h-4 bg-gray-200 rounded ml-auto"></div>
                    </td>
                  </tr>
                ))
              ) : transactions.length > 0 ? (
                transactions.map((transaction) => (
                  <tr key={transaction.transaction_id} className="border-b border-primary-100 hover:bg-background-secondary">
                    <td className="py-3 px-4 text-sm text-text-primary">
                      {format(new Date(transaction.date), 'MMM dd, yyyy')}
                    </td>
                    <td className="py-3 px-4 text-sm text-text-primary">
                      {transaction.description}
                    </td>
                    <td className="py-3 px-4 text-sm">
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        transaction.category === 'revenue' 
                          ? 'bg-accent-green bg-opacity-10 text-accent-green'
                          : 'bg-accent-red bg-opacity-10 text-accent-red'
                      }`}>
                        {transaction.category === 'revenue' ? 'Revenue' : 'Expense'}
                      </span>
                    </td>
                    <td className={`py-3 px-4 text-sm font-medium text-right ${
                      transaction.category === 'revenue' ? 'text-accent-green' : 'text-accent-red'
                    }`}>
                      {transaction.category === 'revenue' ? '+' : '-'}${Math.abs(transaction.amount).toFixed(2)}
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={4} className="py-8 text-center text-gray-500">
                    No recent transactions available
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default FinancialReport;