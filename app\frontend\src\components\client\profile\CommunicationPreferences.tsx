import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import Button from '../../common/Button';
import { Select } from '../../common/Select';
import { 
  Bell, 
  Mail, 
  MessageSquare, 
  Smartphone, 
  Globe,
  Check,
  X
} from 'lucide-react';

interface CommunicationPreferencesProps {
  profile: {
    preferred_language: 'en' | 'fr';
    sms_notifications_enabled: boolean;
    email_notifications_enabled: boolean;
    push_notifications_enabled: boolean;
  };
  onUpdate: (data: any) => void;
  saving: boolean;
}

export const CommunicationPreferences: React.FC<CommunicationPreferencesProps> = ({
  profile,
  onUpdate,
  saving
}) => {
  const { t, i18n } = useTranslation();
  const [formData, setFormData] = useState({
    preferred_language: profile.preferred_language || 'en',
    sms_notifications_enabled: profile.sms_notifications_enabled ?? true,
    email_notifications_enabled: profile.email_notifications_enabled ?? true,
    push_notifications_enabled: profile.push_notifications_enabled ?? true
  });

  const languageOptions = [
    { value: 'en', label: 'English' },
    { value: 'fr', label: 'Français' }
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // If language changed, update i18n
    if (formData.preferred_language !== profile.preferred_language) {
      i18n.changeLanguage(formData.preferred_language);
    }
    
    onUpdate(formData);
  };

  const NotificationToggle = ({ 
    enabled, 
    onChange, 
    label, 
    description, 
    icon: Icon 
  }: {
    enabled: boolean;
    onChange: (value: boolean) => void;
    label: string;
    description: string;
    icon: React.ElementType;
  }) => (
    <div className="flex items-start gap-4 p-4 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
      <div className="flex-shrink-0 mt-0.5">
        <Icon className="w-5 h-5 text-text-secondary" />
      </div>
      <div className="flex-1">
        <h4 className="text-sm font-medium text-text-primary">{label}</h4>
        <p className="text-sm text-text-secondary mt-1">{description}</p>
      </div>
      <button
        type="button"
        onClick={() => onChange(!enabled)}
        className={`
          relative inline-flex h-6 w-11 items-center rounded-full transition-colors
          ${enabled ? 'bg-accent-red' : 'bg-gray-200'}
        `}
      >
        <span className="sr-only">{enabled ? 'Disable' : 'Enable'} {label}</span>
        <span
          className={`
            inline-block h-4 w-4 transform rounded-full bg-white transition-transform
            ${enabled ? 'translate-x-6' : 'translate-x-1'}
          `}
        />
      </button>
    </div>
  );

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Language Preference */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-text-primary flex items-center gap-2">
          <Globe className="w-5 h-5" />
          {t('client.profile.languagePreference')}
        </h3>
        
        <div>
          <label className="block text-sm font-medium text-text-primary mb-2">
            {t('client.profile.fields.preferredLanguage')}
          </label>
          <Select
            value={formData.preferred_language}
            onChange={(value) => setFormData({ ...formData, preferred_language: value as 'en' | 'fr' })}
            options={languageOptions}
            leftIcon={<Globe className="w-4 h-4" />}
          />
          <p className="text-sm text-text-secondary mt-2">
            {t('client.profile.languageChangeNote')}
          </p>
        </div>
      </div>

      {/* Notification Preferences */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-text-primary flex items-center gap-2">
          <Bell className="w-5 h-5" />
          {t('client.profile.notificationPreferences')}
        </h3>
        
        <div className="space-y-3">
          <NotificationToggle
            enabled={formData.email_notifications_enabled}
            onChange={(value) => setFormData({ ...formData, email_notifications_enabled: value })}
            label={t('client.profile.notifications.email.label')}
            description={t('client.profile.notifications.email.description')}
            icon={Mail}
          />
          
          <NotificationToggle
            enabled={formData.sms_notifications_enabled}
            onChange={(value) => setFormData({ ...formData, sms_notifications_enabled: value })}
            label={t('client.profile.notifications.sms.label')}
            description={t('client.profile.notifications.sms.description')}
            icon={MessageSquare}
          />
          
          <NotificationToggle
            enabled={formData.push_notifications_enabled}
            onChange={(value) => setFormData({ ...formData, push_notifications_enabled: value })}
            label={t('client.profile.notifications.push.label')}
            description={t('client.profile.notifications.push.description')}
            icon={Smartphone}
          />
        </div>
      </div>

      {/* Notification Info */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <Bell className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
          <div className="text-sm text-blue-800">
            <p className="font-medium mb-1">{t('client.profile.notifications.info.title')}</p>
            <ul className="space-y-1 list-disc list-inside">
              <li>{t('client.profile.notifications.info.appointment')}</li>
              <li>{t('client.profile.notifications.info.payment')}</li>
              <li>{t('client.profile.notifications.info.message')}</li>
              <li>{t('client.profile.notifications.info.update')}</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="flex justify-end gap-3 pt-4">
        <Button
          type="button"
          variant="ghost"
          onClick={() => setFormData({
            preferred_language: profile.preferred_language || 'en',
            sms_notifications_enabled: profile.sms_notifications_enabled ?? true,
            email_notifications_enabled: profile.email_notifications_enabled ?? true,
            push_notifications_enabled: profile.push_notifications_enabled ?? true
          })}
          disabled={saving}
        >
          {t('common.cancel')}
        </Button>
        <Button
          type="submit"
          loading={saving}
          disabled={saving}
        >
          {t('common.save')}
        </Button>
      </div>
    </form>
  );
};