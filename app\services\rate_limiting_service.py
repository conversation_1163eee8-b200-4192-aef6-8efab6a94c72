"""
Centralized rate limiting service for TutorAide authentication.
Provides high-level rate limiting operations and monitoring.
"""

from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass

from app.core.validation import RateLimitValidator
from app.core.exceptions import Validation<PERSON>rror, SecurityError
from app.core.logging import <PERSON><PERSON><PERSON><PERSON><PERSON>og<PERSON>
from app.core.timezone import now_est


logger = TutorAideLogger.get_logger(__name__)


@dataclass
class RateLimitInfo:
    """Rate limiting information for responses."""
    
    attempts_remaining: int
    window_seconds: int
    reset_time: datetime
    is_locked: bool
    delay_seconds: int = 0


@dataclass
class SecurityEvent:
    """Security event for monitoring."""
    
    event_type: str
    identifier: str
    ip_address: Optional[str]
    timestamp: datetime
    details: Dict[str, Any]


class RateLimitingService:
    """Centralized rate limiting service with monitoring and management."""
    
    def __init__(self):
        """Initialize rate limiting service."""
        self.security_events: List[SecurityEvent] = []
        self.max_events = 1000  # Keep last 1000 events in memory
    
    def check_login_rate_limit(
        self, 
        email: str, 
        ip_address: str,
        user_agent: Optional[str] = None
    ) -> RateLimitInfo:
        """
        Check login rate limits and return detailed information.
        
        Args:
            email: User email address
            ip_address: Client IP address  
            user_agent: Client user agent (optional)
            
        Returns:
            RateLimitInfo with current status
            
        Raises:
            ValidationError: If rate limits exceeded
        """
        try:
            # Check rate limits
            RateLimitValidator.check_rate_limit(email, 'login', ip_address)
            
            # Get current status
            status = RateLimitValidator.get_rate_limit_status(email, 'login')
            config = RateLimitValidator.LIMITS['login']
            
            return RateLimitInfo(
                attempts_remaining=status['attempts_remaining'],
                window_seconds=config['window_minutes'] * 60,
                reset_time=now_est() + timedelta(minutes=config['window_minutes']),
                is_locked=status['is_locked'],
                delay_seconds=status['delay_remaining_seconds']
            )
            
        except ValidationError as e:
            # Log security event
            self._log_security_event(
                event_type='rate_limit_exceeded',
                identifier=email,
                ip_address=ip_address,
                details={
                    'action': 'login',
                    'user_agent': user_agent,
                    'error': str(e)
                }
            )
            raise
    
    def record_login_failure(
        self,
        email: str,
        ip_address: str,
        failure_reason: str,
        user_agent: Optional[str] = None
    ) -> None:
        """
        Record a failed login attempt with enhanced tracking.
        
        Args:
            email: User email address
            ip_address: Client IP address
            failure_reason: Reason for failure (invalid_password, account_locked, etc.)
            user_agent: Client user agent (optional)
        """
        try:
            # Record the failed attempt
            RateLimitValidator.record_failed_attempt(email, 'login', ip_address)
            
            # Log security event
            self._log_security_event(
                event_type='login_failure',
                identifier=email,
                ip_address=ip_address,
                details={
                    'failure_reason': failure_reason,
                    'user_agent': user_agent
                }
            )
            
        except ValidationError:
            # Rate limit exceeded, log as security event
            self._log_security_event(
                event_type='login_blocked',
                identifier=email,
                ip_address=ip_address,
                details={
                    'failure_reason': failure_reason,
                    'user_agent': user_agent,
                    'blocked_reason': 'rate_limit_exceeded'
                }
            )
            # Re-raise the exception
            raise
    
    def record_login_success(self, email: str, ip_address: str) -> None:
        """
        Record a successful login and clear rate limiting.
        
        Args:
            email: User email address
            ip_address: Client IP address
        """
        # Clear rate limiting attempts
        RateLimitValidator.clear_attempts(email)
        
        # Log security event
        self._log_security_event(
            event_type='login_success',
            identifier=email,
            ip_address=ip_address,
            details={}
        )
        
        logger.info(f"Successful login for {email} from {ip_address}")
    
    def get_account_status(self, email: str) -> Dict[str, Any]:
        """
        Get comprehensive account rate limiting status.
        
        Args:
            email: User email address
            
        Returns:
            Dictionary with detailed status information
        """
        status = RateLimitValidator.get_rate_limit_status(email, 'login')
        
        return {
            'email': email,
            'is_locked': status['is_locked'],
            'current_attempts': status['current_attempts'],
            'max_attempts': status['max_attempts'],
            'failed_attempts': status['failed_attempts'],
            'attempts_remaining': status['attempts_remaining'],
            'delay_remaining_seconds': status['delay_remaining_seconds'],
            'window_minutes': status['window_minutes'],
            'is_admin_bypass': email.lower() in RateLimitValidator.ADMIN_BYPASS_EMAILS
        }
    
    def get_ip_reputation(self, ip_address: str) -> Dict[str, Any]:
        """
        Get IP address reputation and blocking status.
        
        Args:
            ip_address: IP address to check
            
        Returns:
            Dictionary with IP reputation information
        """
        ip_status = RateLimitValidator.get_ip_status(ip_address)
        
        # Count recent security events for this IP
        now = now_est()
        recent_events = [
            event for event in self.security_events
            if event.ip_address == ip_address and 
               event.timestamp > now - timedelta(hours=24)
        ]
        
        return {
            **ip_status,
            'recent_events_24h': len(recent_events),
            'security_events': [
                {
                    'event_type': event.event_type,
                    'timestamp': event.timestamp.isoformat(),
                    'details': event.details
                }
                for event in recent_events[-10:]  # Last 10 events
            ]
        }
    
    def unlock_account(self, email: str, admin_email: str) -> bool:
        """
        Unlock an account (admin operation).
        
        Args:
            email: Email address to unlock
            admin_email: Admin performing the unlock
            
        Returns:
            True if successful
        """
        success = RateLimitValidator.unlock_account(email, admin_override=True)
        
        if success:
            self._log_security_event(
                event_type='account_unlocked',
                identifier=email,
                ip_address=None,
                details={
                    'admin_email': admin_email,
                    'unlock_time': now_est().isoformat()
                }
            )
            
            logger.security(f"Account {email} unlocked by admin {admin_email}")
        
        return success
    
    def add_admin_bypass(self, email: str, admin_email: str) -> None:
        """
        Add admin bypass for rate limiting.
        
        Args:
            email: Email to add bypass for
            admin_email: Admin performing the action
        """
        RateLimitValidator.add_admin_bypass(email)
        
        self._log_security_event(
            event_type='admin_bypass_added',
            identifier=email,
            ip_address=None,
            details={
                'admin_email': admin_email,
                'added_time': now_est().isoformat()
            }
        )
    
    def remove_admin_bypass(self, email: str, admin_email: str) -> None:
        """
        Remove admin bypass for rate limiting.
        
        Args:
            email: Email to remove bypass for
            admin_email: Admin performing the action
        """
        RateLimitValidator.remove_admin_bypass(email)
        
        self._log_security_event(
            event_type='admin_bypass_removed',
            identifier=email,
            ip_address=None,
            details={
                'admin_email': admin_email,
                'removed_time': now_est().isoformat()
            }
        )
    
    def get_security_events(
        self,
        hours: int = 24,
        event_types: Optional[List[str]] = None,
        ip_address: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Get recent security events for monitoring.
        
        Args:
            hours: Hours of history to return
            event_types: Filter by event types
            ip_address: Filter by IP address
            
        Returns:
            List of security events
        """
        now = now_est()
        cutoff = now - timedelta(hours=hours)
        
        filtered_events = []
        for event in self.security_events:
            if event.timestamp < cutoff:
                continue
            
            if event_types and event.event_type not in event_types:
                continue
            
            if ip_address and event.ip_address != ip_address:
                continue
            
            filtered_events.append({
                'event_type': event.event_type,
                'identifier': event.identifier,
                'ip_address': event.ip_address,
                'timestamp': event.timestamp.isoformat(),
                'details': event.details
            })
        
        return sorted(filtered_events, key=lambda x: x['timestamp'], reverse=True)
    
    def get_brute_force_summary(self) -> Dict[str, Any]:
        """
        Get summary of brute force activity.
        
        Returns:
            Summary of brute force attempts and blocks
        """
        now = now_est()
        last_24h = now - timedelta(hours=24)
        
        # Count events by type
        event_counts = {}
        blocked_ips = set()
        targeted_accounts = set()
        
        for event in self.security_events:
            if event.timestamp < last_24h:
                continue
            
            event_type = event.event_type
            event_counts[event_type] = event_counts.get(event_type, 0) + 1
            
            if event_type in ['login_blocked', 'rate_limit_exceeded']:
                if event.ip_address:
                    blocked_ips.add(event.ip_address)
                targeted_accounts.add(event.identifier)
        
        return {
            'period': '24 hours',
            'total_events': len([e for e in self.security_events if e.timestamp > last_24h]),
            'event_counts': event_counts,
            'blocked_ips_count': len(blocked_ips),
            'targeted_accounts_count': len(targeted_accounts),
            'blocked_ips': list(blocked_ips),
            'most_targeted_accounts': list(targeted_accounts)[:10]
        }
    
    def _log_security_event(
        self,
        event_type: str,
        identifier: str,
        ip_address: Optional[str],
        details: Dict[str, Any]
    ) -> None:
        """Log a security event for monitoring."""
        event = SecurityEvent(
            event_type=event_type,
            identifier=identifier,
            ip_address=ip_address,
            timestamp=now_est(),
            details=details
        )
        
        self.security_events.append(event)
        
        # Keep only the most recent events
        if len(self.security_events) > self.max_events:
            self.security_events = self.security_events[-self.max_events:]
        
        # Log to security log
        logger.security(f"Security event: {event_type} for {identifier} from {ip_address}", extra=details)


# Global instance
rate_limiting_service = RateLimitingService()