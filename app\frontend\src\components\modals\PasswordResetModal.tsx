import React, { useState } from 'react';
import { Modal } from '../common/Modal';
import Button from '../common/Button';
import { AlertCircle, Mail, Send, CheckCircle, User } from 'lucide-react';
import { authService } from '../../services/auth.service';
import toast from 'react-hot-toast';

interface PasswordResetModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: {
    id: number;
    email: string;
    firstName: string;
    lastName: string;
    userType: 'client' | 'tutor';
  };
}

export const PasswordResetModal: React.FC<PasswordResetModalProps> = ({
  isOpen,
  onClose,
  user
}) => {
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);

  const handleSendResetEmail = async () => {
    setLoading(true);
    try {
      await authService.sendPasswordResetEmail(
        user.id,
        user.email,
        'manager'
      );

      setSuccess(true);
      toast.success(`Password reset email sent to ${user.email}`);

      // Close modal after 2 seconds
      setTimeout(() => {
        setSuccess(false);
        onClose();
      }, 2000);

    } catch (error) {
      console.error('Error sending password reset:', error);
      toast.error('Failed to send password reset email');
    } finally {
      setLoading(false);
    }
  };

  const getUserTypeLabel = () => {
    return user.userType === 'client' ? 'Client' : 'Tutor';
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Send Password Reset"
      size="sm"
    >
      {!success ? (
        <div className="space-y-4">
          {/* User Info */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-accent-red/10 rounded-full flex items-center justify-center">
                  <User className="w-5 h-5 text-accent-red" />
                </div>
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-gray-900">
                  {user.firstName} {user.lastName}
                </h4>
                <p className="text-sm text-gray-600">{getUserTypeLabel()}</p>
                <p className="text-sm text-gray-600 flex items-center gap-1 mt-1">
                  <Mail className="w-3 h-3" />
                  {user.email}
                </p>
              </div>
            </div>
          </div>

          {/* Warning Message */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <AlertCircle className="w-5 h-5 text-yellow-600 flex-shrink-0 mt-0.5" />
              <div className="text-sm text-yellow-800">
                <p className="font-medium mb-1">Important</p>
                <ul className="space-y-1 list-disc list-inside">
                  <li>This will send a password reset link to the user's email</li>
                  <li>The link will expire in 24 hours</li>
                  <li>The user will be notified that a manager initiated this reset</li>
                  <li>Any existing sessions will remain active until the password is changed</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Email Preview */}
          <div className="border border-gray-200 rounded-lg p-4">
            <h5 className="text-sm font-medium text-gray-700 mb-2">Email Preview</h5>
            <div className="text-sm text-gray-600 space-y-2">
              <p><strong>Subject:</strong> Password Reset Request - TutorAide</p>
              <p><strong>To:</strong> {user.email}</p>
              <div className="mt-3 p-3 bg-gray-50 rounded border border-gray-200">
                <p className="mb-2">Hello {user.firstName},</p>
                <p className="mb-2">
                  A TutorAide administrator has requested a password reset for your account. 
                  Click the link below to create a new password:
                </p>
                <p className="text-accent-red underline mb-2">[Reset Password Link]</p>
                <p className="mb-2">This link will expire in 24 hours.</p>
                <p className="mb-2">
                  If you did not expect this email, please contact support <NAME_EMAIL>
                </p>
                <p>Best regards,<br />The TutorAide Team</p>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3 pt-4">
            <Button
              variant="ghost"
              onClick={onClose}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleSendResetEmail}
              disabled={loading}
              className="bg-accent-red hover:bg-accent-red-dark"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2" />
                  Sending...
                </>
              ) : (
                <>
                  <Send className="w-4 h-4 mr-2" />
                  Send Reset Email
                </>
              )}
            </Button>
          </div>
        </div>
      ) : (
        <div className="py-8 text-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
          <h4 className="text-lg font-medium text-gray-900 mb-2">
            Reset Email Sent Successfully
          </h4>
          <p className="text-sm text-gray-600">
            Password reset instructions have been sent to {user.email}
          </p>
        </div>
      )}
    </Modal>
  );
};