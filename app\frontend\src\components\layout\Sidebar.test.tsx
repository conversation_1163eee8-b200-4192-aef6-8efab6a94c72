import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { I18nextProvider } from 'react-i18next';
import Sidebar from './Sidebar';
import { AuthProvider } from '../../contexts/AuthContext';
import i18n from '../i18n';

// Mock auth context
const mockAuthContext = {
  user: {
    id: '1',
    email: '<EMAIL>',
    activeRole: 'manager',
    roles: ['manager'],
    firstName: 'Test',
    lastName: 'User',
  },
  isAuthenticated: true,
  isLoading: false,
  login: vi.fn(),
  logout: vi.fn(),
  switchRole: vi.fn(),
};

const MockAuthProvider = ({ children }: { children: React.ReactNode }) => (
  <AuthProvider value={mockAuthContext}>
    {children}
  </AuthProvider>
);

const renderSidebar = (props = {}) => {
  return render(
    <BrowserRouter>
      <I18nextProvider i18n={i18n}>
        <MockAuthProvider>
          <Sidebar {...props} />
        </MockAuthProvider>
      </I18nextProvider>
    </BrowserRouter>
  );
};

describe('Sidebar', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders sidebar with logo and navigation items', () => {
    renderSidebar();
    
    // Check logo is present
    const logo = screen.getByAltText('TutorAide');
    expect(logo).toBeInTheDocument();
    
    // Check main navigation sections
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Users')).toBeInTheDocument();
    expect(screen.getByText('Reports')).toBeInTheDocument();
    expect(screen.getByText('Messages')).toBeInTheDocument();
    expect(screen.getByText('Settings')).toBeInTheDocument();
  });

  it('shows role-specific navigation items for manager', () => {
    renderSidebar();
    
    // Manager should see all sections
    expect(screen.getByText('Users')).toBeInTheDocument();
    expect(screen.getByText('Billing')).toBeInTheDocument();
    expect(screen.getByText('Reports')).toBeInTheDocument();
    expect(screen.getByText('Messages')).toBeInTheDocument();
    expect(screen.getByText('Settings')).toBeInTheDocument();
  });

  it('shows limited navigation for client role', () => {
    const clientAuthContext = {
      ...mockAuthContext,
      user: {
        ...mockAuthContext.user,
        activeRole: 'client',
        roles: ['client'],
      },
    };

    render(
      <BrowserRouter>
        <I18nextProvider i18n={i18n}>
          <AuthProvider value={clientAuthContext}>
            <Sidebar />
          </AuthProvider>
        </I18nextProvider>
      </BrowserRouter>
    );

    // Client should see limited sections
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Billing')).toBeInTheDocument();
    expect(screen.getByText('Messages')).toBeInTheDocument();
    
    // Client should not see manager-only sections
    expect(screen.queryByText('Reports')).not.toBeInTheDocument();
    expect(screen.queryByText('Settings')).not.toBeInTheDocument();
  });

  it('shows tutor-specific navigation items', () => {
    const tutorAuthContext = {
      ...mockAuthContext,
      user: {
        ...mockAuthContext.user,
        activeRole: 'tutor',
        roles: ['tutor'],
      },
    };

    render(
      <BrowserRouter>
        <I18nextProvider i18n={i18n}>
          <AuthProvider value={tutorAuthContext}>
            <Sidebar />
          </AuthProvider>
        </I18nextProvider>
      </BrowserRouter>
    );

    // Tutor should see specific sections
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Schedule')).toBeInTheDocument();
    expect(screen.getByText('Students')).toBeInTheDocument();
    expect(screen.getByText('Messages')).toBeInTheDocument();
  });

  it('toggles sidebar collapse state', async () => {
    renderSidebar();
    
    const toggleButton = screen.getByRole('button', { name: /toggle sidebar/i });
    expect(toggleButton).toBeInTheDocument();
    
    // Initially expanded
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    
    // Click to collapse
    fireEvent.click(toggleButton);
    
    await waitFor(() => {
      // Text should be hidden when collapsed
      const dashboardText = screen.queryByText('Dashboard');
      expect(dashboardText).not.toBeVisible();
    });
  });

  it('expands section when clicked', async () => {
    renderSidebar();
    
    const usersSection = screen.getByText('Users').closest('button');
    expect(usersSection).toBeInTheDocument();
    
    fireEvent.click(usersSection!);
    
    await waitFor(() => {
      expect(screen.getByText('Clients')).toBeInTheDocument();
      expect(screen.getByText('Tutors')).toBeInTheDocument();
      expect(screen.getByText('Dependants')).toBeInTheDocument();
    });
  });

  it('highlights active navigation item', () => {
    // Mock location to be on users page
    Object.defineProperty(window, 'location', {
      value: { pathname: '/users' },
      writable: true,
    });

    renderSidebar();
    
    const usersLink = screen.getByText('Users').closest('a');
    expect(usersLink).toHaveClass('bg-accent-red');
  });

  it('shows keyboard shortcuts in tooltips', async () => {
    renderSidebar();
    
    const searchItem = screen.getByText('Search');
    fireEvent.mouseOver(searchItem);
    
    await waitFor(() => {
      expect(screen.getByText(/Ctrl\+K/)).toBeInTheDocument();
    });
  });

  it('handles responsive behavior on mobile', () => {
    // Mock mobile viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 768,
    });
    
    renderSidebar();
    
    // Should show mobile menu button
    const mobileToggle = screen.getByLabelText(/toggle navigation/i);
    expect(mobileToggle).toBeInTheDocument();
  });

  it('renders section icons correctly', () => {
    renderSidebar();
    
    // Check that icons are rendered for each section
    const sections = ['Dashboard', 'Users', 'Reports', 'Messages', 'Settings'];
    sections.forEach(section => {
      const sectionElement = screen.getByText(section);
      const icon = sectionElement.parentElement?.querySelector('svg');
      expect(icon).toBeInTheDocument();
    });
  });

  it('handles logo click navigation', () => {
    renderSidebar();
    
    const logoLink = screen.getByAltText('TutorAide').closest('a');
    expect(logoLink).toHaveAttribute('href', 'https://www.tutoraide.ca');
    expect(logoLink).toHaveAttribute('target', '_blank');
  });
});