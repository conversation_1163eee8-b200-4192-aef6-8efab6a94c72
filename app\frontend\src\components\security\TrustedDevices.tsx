import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Monitor, Smartphone, Tablet, Globe, MapPin, Clock, Trash2 } from 'lucide-react';
import Button from '../common/Button';
import { Badge } from '../common/Badge';
import LoadingSpinner from '../ui/LoadingSpinner';
import api from '../../services/api';
import toast from 'react-hot-toast';

interface TrustedDevice {
  deviceId: number;
  deviceName: string;
  deviceFingerprint: string;
  lastUsedAt: string;
  createdAt: string;
  expiresAt: string;
  ipAddress?: string;
  userAgent?: string;
  isCurrentDevice: boolean;
}

export const TrustedDevices: React.FC = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [devices, setDevices] = useState<TrustedDevice[]>([]);
  const [removingDevice, setRemovingDevice] = useState<number | null>(null);

  useEffect(() => {
    fetchTrustedDevices();
  }, []);

  const fetchTrustedDevices = async () => {
    try {
      setLoading(true);
      const response = await api.get<TrustedDevice[]>('/api/v1/users/trusted-devices');
      setDevices(response.data);
    } catch (error) {
      console.error('Error fetching trusted devices:', error);
      toast.error(t('settings.security.trustedDevices.errors.fetchFailed'));
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveDevice = async (deviceId: number) => {
    try {
      setRemovingDevice(deviceId);
      await api.delete(`/api/v1/users/trusted-devices/${deviceId}`);
      setDevices(devices.filter(d => d.deviceId !== deviceId));
      toast.success(t('settings.security.trustedDevices.removed'));
    } catch (error) {
      console.error('Error removing device:', error);
      toast.error(t('settings.security.trustedDevices.errors.removeFailed'));
    } finally {
      setRemovingDevice(null);
    }
  };

  const getDeviceIcon = (userAgent?: string) => {
    if (!userAgent) return <Monitor className="w-5 h-5" />;
    
    const ua = userAgent.toLowerCase();
    if (ua.includes('mobile') || ua.includes('android') || ua.includes('iphone')) {
      return <Smartphone className="w-5 h-5" />;
    }
    if (ua.includes('tablet') || ua.includes('ipad')) {
      return <Tablet className="w-5 h-5" />;
    }
    return <Monitor className="w-5 h-5" />;
  };

  const getDeviceType = (userAgent?: string) => {
    if (!userAgent) return t('settings.security.trustedDevices.types.desktop');
    
    const ua = userAgent.toLowerCase();
    if (ua.includes('mobile') || ua.includes('android') || ua.includes('iphone')) {
      return t('settings.security.trustedDevices.types.mobile');
    }
    if (ua.includes('tablet') || ua.includes('ipad')) {
      return t('settings.security.trustedDevices.types.tablet');
    }
    return t('settings.security.trustedDevices.types.desktop');
  };

  const getBrowserName = (userAgent?: string) => {
    if (!userAgent) return 'Unknown';
    
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';
    return 'Unknown';
  };

  const getRelativeTime = (date: string) => {
    const now = new Date();
    const then = new Date(date);
    const diffMs = now.getTime() - then.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return t('common.justNow');
    if (diffMins < 60) return t('common.minutesAgo', { count: diffMins });
    if (diffHours < 24) return t('common.hoursAgo', { count: diffHours });
    if (diffDays < 7) return t('common.daysAgo', { count: diffDays });
    
    return new Date(date).toLocaleDateString();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-32">
        <LoadingSpinner size="md" />
      </div>
    );
  }

  if (devices.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">
          {t('settings.security.trustedDevices.noDevices')}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {devices.map((device) => (
        <div
          key={device.deviceId}
          className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors"
        >
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-3">
              <div className="p-2 bg-gray-100 rounded-lg">
                {getDeviceIcon(device.userAgent)}
              </div>
              
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className="font-medium text-gray-900">
                    {device.deviceName || getDeviceType(device.userAgent)}
                  </h4>
                  {device.isCurrentDevice && (
                    <Badge variant="success" size="sm">
                      {t('settings.security.trustedDevices.currentDevice')}
                    </Badge>
                  )}
                </div>
                
                <div className="flex items-center gap-4 text-sm text-gray-600">
                  <div className="flex items-center gap-1">
                    <Globe className="w-3 h-3" />
                    <span>{getBrowserName(device.userAgent)}</span>
                  </div>
                  
                  {device.ipAddress && (
                    <div className="flex items-center gap-1">
                      <MapPin className="w-3 h-3" />
                      <span>{device.ipAddress}</span>
                    </div>
                  )}
                  
                  <div className="flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    <span>{t('settings.security.trustedDevices.lastUsed')}: {getRelativeTime(device.lastUsedAt)}</span>
                  </div>
                </div>
                
                <p className="text-xs text-gray-500 mt-2">
                  {t('settings.security.trustedDevices.expiresOn', {
                    date: new Date(device.expiresAt).toLocaleDateString()
                  })}
                </p>
              </div>
            </div>
            
            {!device.isCurrentDevice && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleRemoveDevice(device.deviceId)}
                disabled={removingDevice === device.deviceId}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                {removingDevice === device.deviceId ? (
                  <LoadingSpinner size="sm" />
                ) : (
                  <Trash2 className="w-4 h-4" />
                )}
              </Button>
            )}
          </div>
        </div>
      ))}
      
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <p className="text-sm text-blue-700">
          {t('settings.security.trustedDevices.info')}
        </p>
      </div>
    </div>
  );
};