import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Users, UserPlus, Search, Filter, Download, MoreVertical, Edit, Trash, Eye, CheckCircle, XCircle, AlertCircle, Plus } from 'lucide-react';
import { Card } from '../../components/common/Card';
import Button from '../../components/common/Button';
import { Input } from '../../components/common/Input';
import { Badge } from '../../components/common/Badge';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import { EmptyState } from '../../components/common/EmptyState';
import { clientService, ClientProfile } from '../../services/clientService';
import { tutorService, Tutor } from '../../services/tutorService';
import { dependantService, Dependant } from '../../services/dependantService';
import { useAuth } from '../../contexts/AuthContext';
import { UserRoleType } from '../../types/auth';
import { hasPermission } from '../../utils/permissions';
import toast from 'react-hot-toast';
import ClientDetailsModal from '../../components/modals/ClientDetailsModal';
import TutorDetailsModal from '../../components/modals/TutorDetailsModal';
import DependantDetailsModal from '../../components/modals/DependantDetailsModal';
import AddClientModal from '../../components/modals/AddClientModal';
import AddTutorModal from '../../components/modals/AddTutorModal';
import AddDependantModal from '../../components/modals/AddDependantModal';

type TabType = 'clients' | 'tutors' | 'dependants';

interface FilterState {
  search: string;
  status: string;
  city: string;
  dateRange: {
    start: string;
    end: string;
  };
}

const UserManagement: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<TabType>('clients');
  const [loading, setLoading] = useState(true);
  const [clients, setClients] = useState<ClientProfile[]>([]);
  const [tutors, setTutors] = useState<Tutor[]>([]);
  const [dependants, setDependants] = useState<Dependant[]>([]);
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    status: 'all',
    city: '',
    dateRange: {
      start: '',
      end: ''
    }
  });
  const [showFilters, setShowFilters] = useState(false);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);

  // Permissions
  const canAddClients = hasPermission(user?.activeRole, 'manageClients');
  const canEditClients = hasPermission(user?.activeRole, 'manageClients');
  const canDeleteClients = hasPermission(user?.activeRole, 'manageClients');
  const canAddTutors = hasPermission(user?.activeRole, 'manageTutors');
  const canEditTutors = hasPermission(user?.activeRole, 'manageTutors');
  const canDeleteTutors = hasPermission(user?.activeRole, 'manageTutors');
  const canManageDependants = hasPermission(user?.activeRole, 'manageDependants');
  const isManager = user?.activeRole === UserRoleType.MANAGER;
  const isClient = user?.activeRole === UserRoleType.CLIENT;
  const isTutor = user?.activeRole === UserRoleType.TUTOR;

  useEffect(() => {
    loadData();
  }, [activeTab, page, filters]);

  const loadData = async () => {
    setLoading(true);
    try {
      const params = {
        search: filters.search || undefined,
        is_active: filters.status === 'active' ? true : filters.status === 'inactive' ? false : undefined,
        city: filters.city || undefined,
        page,
        limit: 20
      };

      switch (activeTab) {
        case 'clients':
          if (isClient) {
            // Clients can only see their own profile
            const profile = await clientService.getClientByUserId(user?.userId || 0);
            setClients([profile]);
            setTotalItems(1);
            setTotalPages(1);
          } else {
            const response = await clientService.searchClients(params);
            setClients(response.items);
            setTotalItems(response.total);
            setTotalPages(response.total_pages);
          }
          break;

        case 'tutors':
          if (isTutor) {
            // Tutors can only see their own profile
            const profile = await tutorService.getTutorByUserId(user?.userId || 0);
            setTutors([profile]);
            setTotalItems(1);
            setTotalPages(1);
          } else {
            const response = await tutorService.searchTutors(params);
            setTutors(response.items);
            setTotalItems(response.total);
            setTotalPages(response.total_pages);
          }
          break;

        case 'dependants':
          if (isClient) {
            // Clients can only see their own dependants
            const deps = await dependantService.getDependantsByClient(user?.userId || 0);
            setDependants(deps);
            setTotalItems(deps.length);
            setTotalPages(1);
          } else {
            const response = await dependantService.searchDependants(params);
            setDependants(response.items);
            setTotalItems(response.total);
            setTotalPages(response.total_pages);
          }
          break;
      }
    } catch (error) {
      console.error('Error loading data:', error);
      toast.error(t('users.loadError'));
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value }));
    setPage(1);
  };

  const handleFilterChange = (key: keyof FilterState, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPage(1);
  };

  const handleViewDetails = (user: any) => {
    setSelectedUser(user);
    setShowDetailsModal(true);
  };

  const handleEdit = (user: any) => {
    // Navigate to edit page or open edit modal
    console.log('Edit user:', user);
  };

  const handleDelete = async (user: any) => {
    if (!confirm(t('users.confirmDelete'))) return;

    try {
      switch (activeTab) {
        case 'clients':
          await clientService.deleteClient(user.client_id);
          break;
        case 'tutors':
          await tutorService.deleteTutor(user.tutor_id);
          break;
        case 'dependants':
          await dependantService.deleteDependant(user.dependant_id);
          break;
      }
      toast.success(t('users.deleteSuccess'));
      loadData();
    } catch (error) {
      console.error('Error deleting user:', error);
      toast.error(t('users.deleteError'));
    }
  };

  const handleAddUser = () => {
    setShowAddModal(true);
  };

  const handleAddComplete = () => {
    setShowAddModal(false);
    loadData();
    toast.success(t(`users.${activeTab}.addSuccess`));
  };

  const getStatusBadge = (user: any) => {
    if (activeTab === 'tutors') {
      const tutor = user as Tutor;
      if (!tutor.is_active) {
        return <Badge variant="secondary" size="sm">{t('common.inactive')}</Badge>;
      }
      switch (tutor.verification_status) {
        case 'verified':
          return <Badge variant="success" size="sm" icon={<CheckCircle className="w-3 h-3" />}>{t('users.tutors.verified')}</Badge>;
        case 'pending':
          return <Badge variant="warning" size="sm" icon={<AlertCircle className="w-3 h-3" />}>{t('users.tutors.pending')}</Badge>;
        case 'rejected':
          return <Badge variant="danger" size="sm" icon={<XCircle className="w-3 h-3" />}>{t('users.tutors.rejected')}</Badge>;
        default:
          return null;
      }
    }
    
    return user.is_active ? 
      <Badge variant="success" size="sm">{t('common.active')}</Badge> : 
      <Badge variant="secondary" size="sm">{t('common.inactive')}</Badge>;
  };

  const renderClientRow = (client: ClientProfile) => (
    <tr key={client.client_id} className="hover:bg-gray-50 transition-colors">
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="flex items-center">
          <img
            className="h-10 w-10 rounded-full object-cover"
            src={client.profile_picture || '/default-avatar.png'}
            alt={`${client.first_name} ${client.last_name}`}
            onError={(e) => {
              (e.target as HTMLImageElement).src = '/default-avatar.png';
            }}
          />
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900">
              {client.first_name} {client.last_name}
            </div>
            <div className="text-sm text-gray-500">{client.email}</div>
          </div>
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="text-sm text-gray-900">{client.phone_number || '-'}</div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="text-sm text-gray-900">
          {client.address?.city || '-'}, {client.address?.province || '-'}
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        {getStatusBadge(client)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        <div className="flex items-center justify-end space-x-2">
          <button
            onClick={() => handleViewDetails(client)}
            className="text-gray-600 hover:text-gray-900"
            title={t('common.view')}
          >
            <Eye className="w-4 h-4" />
          </button>
          {canEditClients && (
            <button
              onClick={() => handleEdit(client)}
              className="text-blue-600 hover:text-blue-900"
              title={t('common.edit')}
            >
              <Edit className="w-4 h-4" />
            </button>
          )}
          {canDeleteClients && (
            <button
              onClick={() => handleDelete(client)}
              className="text-red-600 hover:text-red-900"
              title={t('common.delete')}
            >
              <Trash className="w-4 h-4" />
            </button>
          )}
        </div>
      </td>
    </tr>
  );

  const renderTutorRow = (tutor: Tutor) => (
    <tr key={tutor.tutor_id} className="hover:bg-gray-50 transition-colors">
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="flex items-center">
          <img
            className="h-10 w-10 rounded-full object-cover"
            src={tutor.profile_picture || '/default-avatar.png'}
            alt={`${tutor.first_name} ${tutor.last_name}`}
            onError={(e) => {
              (e.target as HTMLImageElement).src = '/default-avatar.png';
            }}
          />
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900">
              {tutor.first_name} {tutor.last_name}
            </div>
            <div className="text-sm text-gray-500">{tutor.email}</div>
          </div>
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="flex flex-wrap gap-1">
          {tutor.specializations?.slice(0, 2).map((spec, idx) => (
            <Badge key={idx} variant="info" size="sm">{spec}</Badge>
          ))}
          {(tutor.specializations?.length || 0) > 2 && (
            <Badge variant="secondary" size="sm">+{tutor.specializations!.length - 2}</Badge>
          )}
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="text-sm text-gray-900">${tutor.hourly_rate || 0}/hr</div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="text-sm text-gray-900">
          {tutor.city || '-'}, {tutor.province || '-'}
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        {getStatusBadge(tutor)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        <div className="flex items-center justify-end space-x-2">
          <button
            onClick={() => handleViewDetails(tutor)}
            className="text-gray-600 hover:text-gray-900"
            title={t('common.view')}
          >
            <Eye className="w-4 h-4" />
          </button>
          {canEditTutors && (
            <button
              onClick={() => handleEdit(tutor)}
              className="text-blue-600 hover:text-blue-900"
              title={t('common.edit')}
            >
              <Edit className="w-4 h-4" />
            </button>
          )}
          {canDeleteTutors && (
            <button
              onClick={() => handleDelete(tutor)}
              className="text-red-600 hover:text-red-900"
              title={t('common.delete')}
            >
              <Trash className="w-4 h-4" />
            </button>
          )}
        </div>
      </td>
    </tr>
  );

  const renderDependantRow = (dependant: Dependant) => (
    <tr key={dependant.dependant_id} className="hover:bg-gray-50 transition-colors">
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="text-sm font-medium text-gray-900">
          {dependant.first_name} {dependant.last_name}
        </div>
        <div className="text-sm text-gray-500">
          {t('users.dependants.age')}: {new Date().getFullYear() - new Date(dependant.date_of_birth).getFullYear()}
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="text-sm text-gray-900">{dependant.parent1_name || '-'}</div>
        {dependant.parent2_name && (
          <div className="text-sm text-gray-500">{dependant.parent2_name}</div>
        )}
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="text-sm text-gray-900">{dependant.grade_level || '-'}</div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="text-sm text-gray-900">{dependant.school_name || '-'}</div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        {getStatusBadge(dependant)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        <div className="flex items-center justify-end space-x-2">
          <button
            onClick={() => handleViewDetails(dependant)}
            className="text-gray-600 hover:text-gray-900"
            title={t('common.view')}
          >
            <Eye className="w-4 h-4" />
          </button>
          {canManageDependants && (
            <>
              <button
                onClick={() => handleEdit(dependant)}
                className="text-blue-600 hover:text-blue-900"
                title={t('common.edit')}
              >
                <Edit className="w-4 h-4" />
              </button>
              <button
                onClick={() => handleDelete(dependant)}
                className="text-red-600 hover:text-red-900"
                title={t('common.delete')}
              >
                <Trash className="w-4 h-4" />
              </button>
            </>
          )}
        </div>
      </td>
    </tr>
  );

  const renderTable = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center py-12">
          <LoadingSpinner size="lg" />
        </div>
      );
    }

    const items = activeTab === 'clients' ? clients : activeTab === 'tutors' ? tutors : dependants;
    
    if (items.length === 0) {
      return (
        <EmptyState
          icon={<Users className="w-12 h-12" />}
          title={t(`users.${activeTab}.empty.title`)}
          message={t(`users.${activeTab}.empty.message`)}
          action={
            (activeTab === 'clients' && canAddClients) ||
            (activeTab === 'tutors' && canAddTutors) ||
            (activeTab === 'dependants' && canManageDependants) ? (
              <Button variant="primary" onClick={handleAddUser}>
                <UserPlus className="w-4 h-4 mr-2" />
                {t(`users.${activeTab}.add`)}
              </Button>
            ) : undefined
          }
        />
      );
    }

    return (
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {activeTab === 'clients' && (
                <>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('users.clients.name')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('users.clients.phone')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('users.clients.location')}
                  </th>
                </>
              )}
              {activeTab === 'tutors' && (
                <>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('users.tutors.name')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('users.tutors.specializations')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('users.tutors.rate')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('users.tutors.location')}
                  </th>
                </>
              )}
              {activeTab === 'dependants' && (
                <>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('users.dependants.name')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('users.dependants.parents')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('users.dependants.grade')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('users.dependants.school')}
                  </th>
                </>
              )}
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {t('common.status')}
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                {t('common.actions')}
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {activeTab === 'clients' && clients.map(renderClientRow)}
            {activeTab === 'tutors' && tutors.map(renderTutorRow)}
            {activeTab === 'dependants' && dependants.map(renderDependantRow)}
          </tbody>
        </table>
      </div>
    );
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">{t('users.title')}</h1>
        <p className="text-gray-600">{t('users.subtitle')}</p>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => {
              setActiveTab('clients');
              setPage(1);
            }}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'clients'
                ? 'border-accent-red text-accent-red'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            {t('users.tabs.clients')}
            <Badge variant="secondary" size="sm" className="ml-2">
              {isClient ? 1 : totalItems}
            </Badge>
          </button>
          <button
            onClick={() => {
              setActiveTab('tutors');
              setPage(1);
            }}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'tutors'
                ? 'border-accent-red text-accent-red'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            {t('users.tabs.tutors')}
            <Badge variant="secondary" size="sm" className="ml-2">
              {isTutor ? 1 : totalItems}
            </Badge>
          </button>
          <button
            onClick={() => {
              setActiveTab('dependants');
              setPage(1);
            }}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'dependants'
                ? 'border-accent-red text-accent-red'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            {t('users.tabs.dependants')}
            <Badge variant="secondary" size="sm" className="ml-2">
              {totalItems}
            </Badge>
          </button>
        </nav>
      </div>

      {/* Actions Bar */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <div className="flex items-center gap-4 w-full sm:w-auto">
          {/* Search */}
          <div className="relative flex-1 sm:flex-initial">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <Input
              type="text"
              placeholder={t('users.search.placeholder')}
              value={filters.search}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10 pr-4 w-full sm:w-80"
            />
          </div>

          {/* Filters */}
          <Button
            variant="secondary"
            onClick={() => setShowFilters(!showFilters)}
            className="relative"
          >
            <Filter className="w-4 h-4 mr-2" />
            {t('common.filters')}
            {(filters.status !== 'all' || filters.city) && (
              <div className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></div>
            )}
          </Button>
        </div>

        {/* Add Button */}
        {((activeTab === 'clients' && canAddClients) ||
          (activeTab === 'tutors' && canAddTutors) ||
          (activeTab === 'dependants' && canManageDependants)) && (
          <Button variant="primary" onClick={handleAddUser}>
            <Plus className="w-4 h-4 mr-2" />
            {t(`users.${activeTab}.add`)}
          </Button>
        )}
      </div>

      {/* Filters Panel */}
      {showFilters && (
        <Card className="mb-6 animate-fadeInDown">
          <div className="p-4">
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('users.filters.status')}
                </label>
                <select
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-red"
                >
                  <option value="all">{t('common.all')}</option>
                  <option value="active">{t('common.active')}</option>
                  <option value="inactive">{t('common.inactive')}</option>
                  {activeTab === 'tutors' && (
                    <>
                      <option value="verified">{t('users.tutors.verified')}</option>
                      <option value="pending">{t('users.tutors.pending')}</option>
                    </>
                  )}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('users.filters.city')}
                </label>
                <Input
                  type="text"
                  placeholder={t('users.filters.cityPlaceholder')}
                  value={filters.city}
                  onChange={(e) => handleFilterChange('city', e.target.value)}
                />
              </div>

              <div className="flex items-end">
                <Button
                  variant="ghost"
                  onClick={() => {
                    setFilters({
                      search: '',
                      status: 'all',
                      city: '',
                      dateRange: { start: '', end: '' }
                    });
                    setPage(1);
                  }}
                  className="w-full"
                >
                  {t('common.clearFilters')}
                </Button>
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* Table */}
      <Card>
        {renderTable()}
        
        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-6 py-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                {t('common.showing')} {((page - 1) * 20) + 1} {t('common.to')} {Math.min(page * 20, totalItems)} {t('common.of')} {totalItems} {t('common.results')}
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => setPage(page - 1)}
                  disabled={page === 1}
                >
                  {t('common.previous')}
                </Button>
                <span className="text-sm text-gray-700">
                  {t('common.page')} {page} {t('common.of')} {totalPages}
                </span>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => setPage(page + 1)}
                  disabled={page === totalPages}
                >
                  {t('common.next')}
                </Button>
              </div>
            </div>
          </div>
        )}
      </Card>

      {/* Details Modals */}
      {showDetailsModal && selectedUser && activeTab === 'clients' && (
        <ClientDetailsModal
          isOpen={showDetailsModal}
          onClose={() => {
            setShowDetailsModal(false);
            setSelectedUser(null);
          }}
          client={selectedUser}
          onEdit={handleEdit}
          canEdit={canEditClients}
        />
      )}

      {showDetailsModal && selectedUser && activeTab === 'tutors' && (
        <TutorDetailsModal
          isOpen={showDetailsModal}
          onClose={() => {
            setShowDetailsModal(false);
            setSelectedUser(null);
          }}
          tutor={selectedUser}
          onEdit={handleEdit}
          canEdit={canEditTutors}
          canVerify={isManager}
        />
      )}

      {showDetailsModal && selectedUser && activeTab === 'dependants' && (
        <DependantDetailsModal
          isOpen={showDetailsModal}
          onClose={() => {
            setShowDetailsModal(false);
            setSelectedUser(null);
          }}
          dependant={selectedUser}
          onEdit={handleEdit}
          canEdit={canManageDependants}
        />
      )}

      {/* Add Modals */}
      {showAddModal && activeTab === 'clients' && (
        <AddClientModal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
          onSuccess={handleAddComplete}
        />
      )}

      {showAddModal && activeTab === 'tutors' && (
        <AddTutorModal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
          onSuccess={handleAddComplete}
        />
      )}

      {showAddModal && activeTab === 'dependants' && (
        <AddDependantModal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
          onSuccess={handleAddComplete}
          defaultClientId={isClient ? user?.userId : undefined}
        />
      )}
    </div>
  );
};

export default UserManagement;