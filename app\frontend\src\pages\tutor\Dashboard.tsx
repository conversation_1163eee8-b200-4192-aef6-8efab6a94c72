import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card } from '../../components/common/Card';
import { Calendar, Clock, DollarSign, Users, BookOpen, TrendingUp } from 'lucide-react';

const Dashboard: React.FC = () => {
  const { t } = useTranslation();

  // Placeholder data - will be replaced with API calls
  const stats = [
    {
      title: t('tutor.dashboard.stats.upcomingSessions'),
      value: '3',
      icon: Calendar,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: t('tutor.dashboard.stats.hoursThisWeek'),
      value: '12.5',
      icon: Clock,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: t('tutor.dashboard.stats.pendingPayments'),
      value: '$450.00',
      icon: DollarSign,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
    },
    {
      title: t('tutor.dashboard.stats.activeStudents'),
      value: '8',
      icon: Users,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
  ];

  const upcomingSessions = [
    {
      id: 1,
      studentName: '<PERSON>',
      subject: 'Mathematics',
      time: '2:00 PM - 3:00 PM',
      date: 'Today',
      type: 'Online',
    },
    {
      id: 2,
      studentName: 'Michael Chen',
      subject: 'Physics',
      time: '4:00 PM - 5:30 PM',
      date: 'Today',
      type: 'In-Person',
    },
    {
      id: 3,
      studentName: 'Emma Davis',
      subject: 'Chemistry',
      time: '10:00 AM - 11:00 AM',
      date: 'Tomorrow',
      type: 'Online',
    },
  ];

  const recentActivities = [
    {
      id: 1,
      action: t('tutor.dashboard.activities.sessionCompleted'),
      details: 'John Smith - English Literature',
      time: '2 hours ago',
    },
    {
      id: 2,
      action: t('tutor.dashboard.activities.paymentReceived'),
      details: '$150.00 for last week',
      time: 'Yesterday',
    },
    {
      id: 3,
      action: t('tutor.dashboard.activities.newStudentAssigned'),
      details: 'Emma Davis - Chemistry',
      time: '3 days ago',
    },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-semibold text-gray-900">
          {t('tutor.dashboard.title')}
        </h1>
        <p className="mt-1 text-sm text-gray-500">
          {t('tutor.dashboard.subtitle')}
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {stats.map((stat, index) => (
          <Card key={index} className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">{stat.title}</p>
                <p className="mt-2 text-3xl font-semibold text-gray-900">
                  {stat.value}
                </p>
              </div>
              <div className={`p-3 rounded-full ${stat.bgColor}`}>
                <stat.icon className={`h-6 w-6 ${stat.color}`} />
              </div>
            </div>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Upcoming Sessions */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-medium text-gray-900">
              {t('tutor.dashboard.upcomingSessions')}
            </h2>
            <BookOpen className="h-5 w-5 text-gray-400" />
          </div>
          <div className="space-y-4">
            {upcomingSessions.map((session) => (
              <div
                key={session.id}
                className="border-l-4 border-accent-red pl-4 py-3"
              >
                <div className="flex justify-between items-start">
                  <div>
                    <p className="font-medium text-gray-900">
                      {session.studentName}
                    </p>
                    <p className="text-sm text-gray-500">{session.subject}</p>
                    <p className="text-sm text-gray-500 mt-1">
                      {session.date} • {session.time}
                    </p>
                  </div>
                  <span
                    className={`px-2 py-1 text-xs rounded-full ${
                      session.type === 'Online'
                        ? 'bg-blue-100 text-blue-700'
                        : 'bg-green-100 text-green-700'
                    }`}
                  >
                    {session.type}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* Recent Activity */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-medium text-gray-900">
              {t('tutor.dashboard.recentActivity')}
            </h2>
            <TrendingUp className="h-5 w-5 text-gray-400" />
          </div>
          <div className="space-y-4">
            {recentActivities.map((activity) => (
              <div key={activity.id} className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-2 h-2 bg-accent-red rounded-full mt-2"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">
                    {activity.action}
                  </p>
                  <p className="text-sm text-gray-500">{activity.details}</p>
                  <p className="text-xs text-gray-400 mt-1">{activity.time}</p>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">
          {t('tutor.dashboard.quickActions')}
        </h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button className="p-4 text-center border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <Calendar className="h-6 w-6 mx-auto mb-2 text-gray-600" />
            <span className="text-sm text-gray-700">
              {t('tutor.dashboard.actions.viewSchedule')}
            </span>
          </button>
          <button className="p-4 text-center border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <Clock className="h-6 w-6 mx-auto mb-2 text-gray-600" />
            <span className="text-sm text-gray-700">
              {t('tutor.dashboard.actions.requestTimeOff')}
            </span>
          </button>
          <button className="p-4 text-center border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <Users className="h-6 w-6 mx-auto mb-2 text-gray-600" />
            <span className="text-sm text-gray-700">
              {t('tutor.dashboard.actions.viewStudents')}
            </span>
          </button>
          <button className="p-4 text-center border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <DollarSign className="h-6 w-6 mx-auto mb-2 text-gray-600" />
            <span className="text-sm text-gray-700">
              {t('tutor.dashboard.actions.viewEarnings')}
            </span>
          </button>
        </div>
      </Card>
    </div>
  );
};

export default Dashboard;