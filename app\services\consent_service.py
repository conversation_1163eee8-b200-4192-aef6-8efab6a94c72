"""
Consent management service for GDPR compliance and user agreement tracking.
Handles consent documents, user consents, and consent history.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import asyncpg

from app.database.repositories.consent_repository import (
    ConsentDocumentRepository, UserConsentRepository, ConsentHistoryRepository
)
from app.models.consent_models import (
    ConsentDocument, ConsentDocumentCreate,
    UserConsent, UserConsentCreate, UserConsentUpdate,
    ConsentLevel, ConsentStatus, ConsentCategory,
    ConsentAcceptanceRequest, ConsentWithdrawalRequest,
    ConsentStatusResponse, ConsentHistoryResponse, ConsentHistoryEntry,
    ConsentSummaryResponse, ConsentValidationResult,
    BulkConsentRequest, BulkConsentResponse
)
from app.core.exceptions import (
    ValidationError, AuthenticationError, BusinessLogicError, ResourceNotFoundError
)
from app.core.logging import TutorAideLogger
from app.core.timezone import now_est


class ConsentService:
    """Service for managing consent operations."""
    
    def __init__(self):
        self.document_repo = ConsentDocumentRepository()
        self.consent_repo = UserConsentRepository()
        self.history_repo = ConsentHistoryRepository()
        self.logger = TutorAideLogger.get_logger(__name__)
    
    async def accept_consent(
        self, 
        conn: asyncpg.Connection, 
        user_id: int, 
        request: ConsentAcceptanceRequest,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> ConsentStatusResponse:
        """Accept a consent for a user."""
        try:
            # Find the consent document
            document = await self.document_repo.find_active_by_type_and_language(
                conn, request.consent_type, request.language
            )
            
            if not document:
                raise ValidationError(
                    f"Consent document not found: {request.consent_type} ({request.language})"
                )
            
            # Check if user already has a consent for this type
            existing_consent = await self.consent_repo.find_user_consent_by_type(
                conn, user_id, request.consent_type
            )
            
            if existing_consent and existing_consent.status == ConsentStatus.GRANTED:
                raise ValidationError(f"Consent already granted for: {request.consent_type}")
            
            # Create new consent record
            consent_create = UserConsentCreate(
                user_id=user_id,
                document_id=document.document_id,
                status=ConsentStatus.GRANTED,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            user_consent = await self.consent_repo.create_user_consent(conn, consent_create)
            
            # Log the action
            self.logger.audit(
                f"consent_accepted: user_id={user_id}, consent_type={request.consent_type}, "
                f"document_version={document.version}, ip_address={ip_address}"
            )
            
            return ConsentStatusResponse(
                consent_type=request.consent_type,
                title=document.title,
                level=document.level,
                category=document.category,
                status=user_consent.status,
                granted_at=user_consent.granted_at,
                withdrawn_at=user_consent.withdrawn_at,
                expires_at=user_consent.expires_at,
                document_version=document.version,
                can_withdraw=(document.level != ConsentLevel.LEVEL_1_MANDATORY)
            )
            
        except Exception as e:
            self.logger.error(f"Failed to accept consent: {e}")
            if isinstance(e, (ValidationError, AuthenticationError)):
                raise
            raise BusinessLogicError(f"Failed to accept consent: {request.consent_type}")
    
    async def withdraw_consent(
        self, 
        conn: asyncpg.Connection, 
        user_id: int, 
        request: ConsentWithdrawalRequest,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> ConsentStatusResponse:
        """Withdraw a consent for a user."""
        try:
            # Find the current consent
            existing_consent = await self.consent_repo.find_user_consent_by_type(
                conn, user_id, request.consent_type
            )
            
            if not existing_consent:
                raise ResourceNotFoundError(f"No consent found for: {request.consent_type}")
            
            if existing_consent.status == ConsentStatus.WITHDRAWN:
                raise ValidationError(f"Consent already withdrawn for: {request.consent_type}")
            
            # Get the document to check if withdrawal is allowed
            document = await self.document_repo.find_by_id(conn, existing_consent.document_id)
            if not document:
                raise ResourceNotFoundError("Consent document not found")
            
            # Check if consent can be withdrawn (Level 1 mandatory cannot be withdrawn)
            if document.level == ConsentLevel.LEVEL_1_MANDATORY:
                raise ValidationError("Mandatory consents cannot be withdrawn")
            
            # Update consent status
            updated_consent = await self.consent_repo.update_consent_status(
                conn, 
                existing_consent.consent_id, 
                ConsentStatus.WITHDRAWN,
                ip_address,
                user_agent
            )
            
            if not updated_consent:
                raise BusinessLogicError("Failed to update consent status")
            
            # Log the action
            self.logger.audit(
                f"consent_withdrawn: user_id={user_id}, consent_type={request.consent_type}, "
                f"reason={request.reason}, ip_address={ip_address}"
            )
            
            return ConsentStatusResponse(
                consent_type=request.consent_type,
                title=document.title,
                level=document.level,
                category=document.category,
                status=updated_consent.status,
                granted_at=updated_consent.granted_at,
                withdrawn_at=updated_consent.withdrawn_at,
                expires_at=updated_consent.expires_at,
                document_version=document.version,
                can_withdraw=False
            )
            
        except Exception as e:
            self.logger.error(f"Failed to withdraw consent: {e}")
            if isinstance(e, (ValidationError, AuthenticationError, ResourceNotFoundError)):
                raise
            raise BusinessLogicError(f"Failed to withdraw consent: {request.consent_type}")
    
    async def get_user_consent_summary(
        self, 
        conn: asyncpg.Connection, 
        user_id: int,
        language: str = "en"
    ) -> ConsentSummaryResponse:
        """Get summary of all user consents."""
        try:
            # Get all active consent documents
            all_documents = await self.document_repo.find_all_active(conn, language)
            
            # Get user's current consents
            user_consents = await self.consent_repo.find_user_consents(conn, user_id)
            user_consent_map = {
                uc.document_id: uc for uc in user_consents 
                if uc.status != ConsentStatus.WITHDRAWN
            }
            
            # Build consent status list
            consent_statuses = []
            missing_mandatory = []
            
            for document in all_documents:
                user_consent = user_consent_map.get(document.document_id)
                
                if user_consent:
                    status = ConsentStatusResponse(
                        consent_type=document.consent_type,
                        title=document.title,
                        level=document.level,
                        category=document.category,
                        status=user_consent.status,
                        granted_at=user_consent.granted_at,
                        withdrawn_at=user_consent.withdrawn_at,
                        expires_at=user_consent.expires_at,
                        document_version=document.version,
                        can_withdraw=(document.level != ConsentLevel.LEVEL_1_MANDATORY)
                    )
                else:
                    # No consent found
                    if document.level == ConsentLevel.LEVEL_1_MANDATORY:
                        missing_mandatory.append(document.consent_type)
                    
                    status = ConsentStatusResponse(
                        consent_type=document.consent_type,
                        title=document.title,
                        level=document.level,
                        category=document.category,
                        status=ConsentStatus.WITHDRAWN,  # Default to withdrawn if not found
                        granted_at=None,
                        withdrawn_at=None,
                        expires_at=None,
                        document_version=document.version,
                        can_withdraw=(document.level != ConsentLevel.LEVEL_1_MANDATORY)
                    )
                
                consent_statuses.append(status)
            
            # Check if all mandatory consents are complete
            mandatory_complete = len(missing_mandatory) == 0
            
            return ConsentSummaryResponse(
                user_id=user_id,
                mandatory_consents_complete=mandatory_complete,
                consents=consent_statuses,
                missing_mandatory=missing_mandatory
            )
            
        except Exception as e:
            self.logger.error(f"Failed to get consent summary: {e}")
            raise BusinessLogicError("Failed to get consent summary")
    
    async def get_consent_history(
        self, 
        conn: asyncpg.Connection, 
        user_id: int, 
        consent_type: Optional[str] = None,
        limit: int = 50
    ) -> ConsentHistoryResponse:
        """Get consent history for a user."""
        try:
            # Get current consent status
            current_consent = None
            if consent_type:
                current_consent = await self.consent_repo.find_user_consent_by_type(
                    conn, user_id, consent_type
                )
            
            current_status = current_consent.status if current_consent else ConsentStatus.WITHDRAWN
            
            # Get history entries
            history_rows = await self.history_repo.find_user_consent_history(
                conn, user_id, consent_type, limit
            )
            
            # Convert to history entries
            history_entries = []
            for row in history_rows:
                entry = ConsentHistoryEntry(
                    action=row['action'],
                    timestamp=row['created_at'],
                    document_version=row['document_version'],
                    ip_address=row['ip_address'],
                    reason=row['reason']
                )
                history_entries.append(entry)
            
            return ConsentHistoryResponse(
                consent_type=consent_type or "all",
                current_status=current_status,
                history=history_entries
            )
            
        except Exception as e:
            self.logger.error(f"Failed to get consent history: {e}")
            raise BusinessLogicError("Failed to get consent history")
    
    async def validate_user_consents(
        self, 
        conn: asyncpg.Connection, 
        user_id: int,
        required_consents: Optional[List[str]] = None,
        language: str = "en"
    ) -> ConsentValidationResult:
        """Validate if user has all required consents."""
        try:
            # Get missing mandatory consents
            missing_mandatory = await self.consent_repo.get_missing_mandatory_consents(
                conn, user_id, language
            )
            
            # Check for expired consents (if any have expiration dates)
            user_consents = await self.consent_repo.find_user_consents(conn, user_id)
            expired_consents = []
            
            now = now_est()
            for consent in user_consents:
                if (consent.expires_at and consent.expires_at < now and 
                    consent.status == ConsentStatus.GRANTED):
                    # Get document to find consent type
                    document = await self.document_repo.find_by_id(conn, consent.document_id)
                    if document:
                        expired_consents.append(document.consent_type)
            
            # Check specific required consents if provided
            if required_consents:
                for consent_type in required_consents:
                    if consent_type not in missing_mandatory:
                        user_consent = await self.consent_repo.find_user_consent_by_type(
                            conn, user_id, consent_type
                        )
                        if not user_consent or user_consent.status != ConsentStatus.GRANTED:
                            if consent_type not in missing_mandatory:
                                missing_mandatory.append(consent_type)
            
            # Determine validation result
            is_valid = len(missing_mandatory) == 0 and len(expired_consents) == 0
            
            if is_valid:
                message = "All required consents are valid"
            else:
                issues = []
                if missing_mandatory:
                    issues.append(f"Missing mandatory consents: {', '.join(missing_mandatory)}")
                if expired_consents:
                    issues.append(f"Expired consents: {', '.join(expired_consents)}")
                message = "; ".join(issues)
            
            return ConsentValidationResult(
                is_valid=is_valid,
                missing_mandatory=missing_mandatory,
                expired_consents=expired_consents,
                message=message
            )
            
        except Exception as e:
            self.logger.error(f"Failed to validate consents: {e}")
            raise BusinessLogicError("Failed to validate consents")
    
    async def bulk_accept_consents(
        self, 
        conn: asyncpg.Connection, 
        user_id: int, 
        request: BulkConsentRequest,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> BulkConsentResponse:
        """Accept multiple consents in bulk."""
        successful = []
        failed = []
        
        for consent_request in request.consents:
            try:
                await self.accept_consent(
                    conn, user_id, consent_request, ip_address, user_agent
                )
                successful.append(consent_request.consent_type)
            except Exception as e:
                failed.append({
                    "consent_type": consent_request.consent_type,
                    "error": str(e)
                })
                self.logger.error(
                    f"Failed to accept consent in bulk: {e} (user_id={user_id}, "
                    f"consent_type={consent_request.consent_type})"
                )
        
        return BulkConsentResponse(
            successful=successful,
            failed=failed,
            total_processed=len(request.consents)
        )
    
    async def get_available_consent_documents(
        self, 
        conn: asyncpg.Connection,
        language: str = "en",
        level: Optional[ConsentLevel] = None
    ) -> List[ConsentDocument]:
        """Get available consent documents."""
        try:
            if level:
                return await self.document_repo.find_by_level(conn, level, language)
            else:
                return await self.document_repo.find_all_active(conn, language)
        except Exception as e:
            self.logger.error(f"Failed to get consent documents: {e}")
            raise BusinessLogicError("Failed to get consent documents")
    
    async def cleanup_expired_consents(self, conn: asyncpg.Connection) -> int:
        """Clean up expired consents by marking them as expired."""
        try:
            # Update expired consents
            query = """
                UPDATE user_consents 
                SET status = 'expired', updated_at = $1
                WHERE expires_at < $1 AND status = 'granted' AND deleted_at IS NULL
                RETURNING consent_id
            """
            
            now = now_est()
            rows = await conn.fetch(query, now)
            count = len(rows)
            
            if count > 0:
                self.logger.info(f"Marked {count} consents as expired")
            
            return count
            
        except Exception as e:
            self.logger.error(f"Failed to cleanup expired consents: {e}")
            raise BusinessLogicError("Failed to cleanup expired consents")