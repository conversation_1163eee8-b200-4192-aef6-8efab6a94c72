-- <PERSON><PERSON>t to create demo users for TutorAide application
-- Run this in DBeaver on your correct database

-- First, let's check if the users already exist and delete them if they do
DELETE FROM user_roles 
WHERE user_id IN (
    SELECT user_id FROM user_accounts 
    WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>')
);

DELETE FROM user_accounts 
WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>');

-- Now insert the demo users
-- Note: These are bcrypt hashes for 'password123'
-- The hash was generated using <PERSON>'s passlib library

-- Manager: <EMAIL>
INSERT INTO user_accounts (email, password_hash, is_email_verified, created_at, updated_at)
VALUES (
    '<EMAIL>',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewYpS3xMNwKMWARO',
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- Get the user_id for the manager
INSERT INTO user_roles (user_id, role_type, is_active, created_at)
SELECT user_id, 'manager', true, CURRENT_TIMESTAMP
FROM user_accounts
WHERE email = '<EMAIL>';

-- Tutor: <EMAIL>
INSERT INTO user_accounts (email, password_hash, is_email_verified, created_at, updated_at)
VALUES (
    '<EMAIL>',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewYpS3xMNwKMWARO',
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- Get the user_id for the tutor
INSERT INTO user_roles (user_id, role_type, is_active, created_at)
SELECT user_id, 'tutor', true, CURRENT_TIMESTAMP
FROM user_accounts
WHERE email = '<EMAIL>';

-- Client: <EMAIL>
INSERT INTO user_accounts (email, password_hash, is_email_verified, created_at, updated_at)
VALUES (
    '<EMAIL>',
    '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewYpS3xMNwKMWARO',
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- Get the user_id for the client
INSERT INTO user_roles (user_id, role_type, is_active, created_at)
SELECT user_id, 'client', true, CURRENT_TIMESTAMP
FROM user_accounts
WHERE email = '<EMAIL>';

-- Verify the users were created successfully
SELECT 
    u.user_id,
    u.email,
    u.is_email_verified,
    r.role_type,
    r.is_active
FROM user_accounts u
JOIN user_roles r ON u.user_id = r.user_id
WHERE u.email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>')
ORDER BY u.email;