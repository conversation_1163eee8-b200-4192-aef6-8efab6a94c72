/**
 * Customer Service Statistics Dashboard
 * 
 * Comprehensive analytics dashboard for customer service operations
 * with real-time metrics, performance tracking, and actionable insights.
 */

import React, { useState, useEffect, useMemo } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Grid,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  LinearProgress,
  CircularProgress,
  IconButton,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Alert,
  Tooltip,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Avatar,
  Badge,
  useTheme
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Schedule as ScheduleIcon,
  Message as MessageIcon,
  Person as PersonIcon,
  Star as StarIcon,
  CheckCircle as ResolvedIcon,
  Assignment as AssignedIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  FilterList as FilterIcon,
  Analytics as AnalyticsIcon,
  Speed as SpeedIcon,
  Group as GroupIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Chat as ChatIcon,
  Warning as WarningIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { formatDistanceToNow, format, subDays, startOfDay, endOfDay } from 'date-fns';

import api from '../../services/api';
import { useTranslation } from '../../hooks/useTranslation';
import { useFormatting } from '../../hooks/useFormatting';

// Types
interface DashboardMetrics {
  conversation_stats: ConversationStats;
  message_stats: MessageStats;
  agent_summaries: AgentSummary[];
  recent_conversations: ConversationSummary[];
  overdue_conversations: ConversationSummary[];
  channel_distribution: ChannelStats[];
  satisfaction_metrics: SatisfactionMetrics;
  response_time_trends: ResponseTimeTrend[];
  resolution_trends: ResolutionTrend[];
  peak_hours: PeakHourData[];
}

interface ConversationStats {
  total_conversations: number;
  open_conversations: number;
  assigned_conversations: number;
  resolved_today: number;
  overdue_responses: number;
  avg_response_time: number;
  satisfaction_average: number;
  satisfaction_count: number;
}

interface MessageStats {
  total_messages_today: number;
  inbound_messages: number;
  outbound_messages: number;
  automated_messages: number;
  delivery_rate: number;
}

interface AgentSummary {
  agent_id: number;
  agent_name: string;
  status: string;
  current_conversations: number;
  max_conversations: number;
  specialties: string[];
  languages: string[];
  performance_score: number;
  avg_response_time: number;
  resolved_today: number;
  satisfaction_rating: number;
}

interface ConversationSummary {
  conversation_id: number;
  participant_name: string;
  status: string;
  priority: string;
  created_at: string;
  last_activity_at: string;
  response_time_minutes: number;
  agent_name?: string;
}

interface ChannelStats {
  channel: string;
  conversation_count: number;
  message_count: number;
  avg_response_time: number;
  satisfaction_rate: number;
}

interface SatisfactionMetrics {
  overall_rating: number;
  rating_distribution: { rating: number; count: number }[];
  trending_up: boolean;
  feedback_highlights: string[];
}

interface ResponseTimeTrend {
  date: string;
  avg_response_time: number;
  target_time: number;
}

interface ResolutionTrend {
  date: string;
  resolved_count: number;
  escalated_count: number;
  satisfaction_rate: number;
}

interface PeakHourData {
  hour: number;
  conversation_count: number;
  message_count: number;
}

// Main Component
const CustomerServiceDashboard: React.FC = () => {
  const { t } = useTranslation();
  const { formatDuration, formatPercentage } = useFormatting();
  const queryClient = useQueryClient();
  const theme = useTheme();

  // State
  const [dateRange, setDateRange] = useState('7d');
  const [selectedAgent, setSelectedAgent] = useState<string>('all');
  const [refreshInterval, setRefreshInterval] = useState(30000); // 30 seconds

  // Queries
  const { data: metrics, isLoading, error, refetch } = useQuery({
    queryKey: ['customer-service-dashboard', dateRange, selectedAgent],
    queryFn: async () => {
      const params = new URLSearchParams();
      if (selectedAgent !== 'all') params.append('agent_id', selectedAgent);
      
      const endDate = new Date();
      const startDate = subDays(endDate, dateRange === '7d' ? 7 : dateRange === '30d' ? 30 : 1);
      params.append('start_date', format(startDate, 'yyyy-MM-dd'));
      params.append('end_date', format(endDate, 'yyyy-MM-dd'));
      
      return api.get(`/customer-service/dashboard?${params}`);
    },
    refetchInterval: refreshInterval
  });

  const { data: realTimeStats } = useQuery({
    queryKey: ['real-time-stats'],
    queryFn: () => api.get('/customer-service/stats/real-time'),
    refetchInterval: 5000 // Update every 5 seconds
  });

  const { data: trendsData } = useQuery({
    queryKey: ['dashboard-trends', dateRange],
    queryFn: async () => {
      const params = new URLSearchParams();
      const endDate = new Date();
      const startDate = subDays(endDate, dateRange === '7d' ? 7 : dateRange === '30d' ? 30 : 1);
      params.append('start_date', format(startDate, 'yyyy-MM-dd'));
      params.append('end_date', format(endDate, 'yyyy-MM-dd'));
      
      return api.get(`/customer-service/stats/trends?${params}`);
    }
  });

  // Colors for charts
  const chartColors = {
    primary: theme.palette.primary.main,
    secondary: theme.palette.secondary.main,
    success: theme.palette.success.main,
    warning: theme.palette.warning.main,
    error: theme.palette.error.main,
    info: theme.palette.info.main
  };

  // Get metric card color based on performance
  const getMetricColor = (value: number, target: number, inverse = false) => {
    const percentage = value / target;
    if (inverse) {
      return percentage <= 0.5 ? 'success' : percentage <= 0.8 ? 'warning' : 'error';
    }
    return percentage >= 1.2 ? 'success' : percentage >= 0.8 ? 'warning' : 'error';
  };

  // Render key metrics cards
  const renderMetricsCards = () => {
    if (!metrics) return null;

    const cards = [
      {
        title: t('dashboard.active_conversations'),
        value: metrics.conversation_stats.open_conversations + metrics.conversation_stats.assigned_conversations,
        change: realTimeStats?.conversation_change || 0,
        icon: <MessageIcon />,
        color: 'primary'
      },
      {
        title: t('dashboard.avg_response_time'),
        value: formatDuration(metrics.conversation_stats.avg_response_time * 60),
        change: realTimeStats?.response_time_change || 0,
        icon: <ScheduleIcon />,
        color: getMetricColor(metrics.conversation_stats.avg_response_time, 15, true)
      },
      {
        title: t('dashboard.resolved_today'),
        value: metrics.conversation_stats.resolved_today,
        change: realTimeStats?.resolved_change || 0,
        icon: <ResolvedIcon />,
        color: 'success'
      },
      {
        title: t('dashboard.satisfaction_rating'),
        value: `${(metrics.conversation_stats.satisfaction_average || 0).toFixed(1)}/5`,
        change: realTimeStats?.satisfaction_change || 0,
        icon: <StarIcon />,
        color: getMetricColor(metrics.conversation_stats.satisfaction_average || 0, 4)
      },
      {
        title: t('dashboard.messages_today'),
        value: metrics.message_stats.total_messages_today,
        change: realTimeStats?.message_change || 0,
        icon: <ChatIcon />,
        color: 'info'
      },
      {
        title: t('dashboard.overdue_responses'),
        value: metrics.conversation_stats.overdue_responses,
        change: realTimeStats?.overdue_change || 0,
        icon: <WarningIcon />,
        color: metrics.conversation_stats.overdue_responses > 0 ? 'error' : 'success'
      }
    ];

    return (
      <Grid container spacing={3}>
        {cards.map((card, index) => (
          <Grid item xs={12} sm={6} md={4} lg={2} key={index}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Box sx={{ 
                    p: 1, 
                    borderRadius: 1, 
                    bgcolor: `${card.color}.light`,
                    color: `${card.color}.contrastText`,
                    mr: 2
                  }}>
                    {card.icon}
                  </Box>
                  <Typography variant="h4" component="div" sx={{ flex: 1 }}>
                    {card.value}
                  </Typography>
                </Box>
                <Typography color="text.secondary" variant="body2" gutterBottom>
                  {card.title}
                </Typography>
                {card.change !== 0 && (
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                    {card.change > 0 ? (
                      <TrendingUpIcon color="success" fontSize="small" />
                    ) : (
                      <TrendingDownIcon color="error" fontSize="small" />
                    )}
                    <Typography 
                      variant="caption" 
                      color={card.change > 0 ? 'success.main' : 'error.main'}
                      sx={{ ml: 0.5 }}
                    >
                      {Math.abs(card.change)}% vs yesterday
                    </Typography>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    );
  };

  // Render agent performance table
  const renderAgentPerformance = () => {
    if (!metrics?.agent_summaries) return null;

    return (
      <Card>
        <CardHeader
          title={t('dashboard.agent_performance')}
          action={
            <Button size="small" startIcon={<AnalyticsIcon />}>
              {t('dashboard.detailed_view')}
            </Button>
          }
        />
        <CardContent>
          <TableContainer>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>{t('dashboard.agent')}</TableCell>
                  <TableCell align="center">{t('dashboard.status')}</TableCell>
                  <TableCell align="center">{t('dashboard.workload')}</TableCell>
                  <TableCell align="center">{t('dashboard.avg_response')}</TableCell>
                  <TableCell align="center">{t('dashboard.resolved_today')}</TableCell>
                  <TableCell align="center">{t('dashboard.satisfaction')}</TableCell>
                  <TableCell align="center">{t('dashboard.performance')}</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {metrics.agent_summaries.map((agent) => (
                  <TableRow key={agent.agent_id} hover>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar sx={{ mr: 1, width: 32, height: 32 }}>
                          {agent.agent_name[0]}
                        </Avatar>
                        <Box>
                          <Typography variant="body2" fontWeight="medium">
                            {agent.agent_name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {agent.specialties.slice(0, 2).join(', ')}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell align="center">
                      <Chip
                        size="small"
                        label={agent.status}
                        color={agent.status === 'available' ? 'success' : 'default'}
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                        <Typography variant="body2">
                          {agent.current_conversations}/{agent.max_conversations}
                        </Typography>
                        <LinearProgress
                          variant="determinate"
                          value={(agent.current_conversations / agent.max_conversations) * 100}
                          sx={{ ml: 1, width: 40, height: 4 }}
                          color={agent.current_conversations >= agent.max_conversations ? 'error' : 'primary'}
                        />
                      </Box>
                    </TableCell>
                    <TableCell align="center">
                      <Typography variant="body2">
                        {formatDuration(agent.avg_response_time * 60)}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Typography variant="body2" fontWeight="medium">
                        {agent.resolved_today}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                        <StarIcon fontSize="small" color="warning" />
                        <Typography variant="body2" sx={{ ml: 0.5 }}>
                          {agent.satisfaction_rating?.toFixed(1) || 'N/A'}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell align="center">
                      <Box sx={{ position: 'relative', display: 'inline-flex' }}>
                        <CircularProgress
                          variant="determinate"
                          value={agent.performance_score}
                          size={40}
                          color={agent.performance_score >= 80 ? 'success' : agent.performance_score >= 60 ? 'warning' : 'error'}
                        />
                        <Box sx={{
                          top: 0,
                          left: 0,
                          bottom: 0,
                          right: 0,
                          position: 'absolute',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}>
                          <Typography variant="caption" component="div" color="text.secondary">
                            {Math.round(agent.performance_score)}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    );
  };

  // Render response time trends chart
  const renderResponseTimeTrends = () => {
    if (!trendsData?.response_time_trends) return null;

    return (
      <Card>
        <CardHeader title={t('dashboard.response_time_trends')} />
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={trendsData.response_time_trends}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <RechartsTooltip 
                formatter={(value: number) => [formatDuration(value * 60), t('dashboard.response_time')]}
              />
              <Legend />
              <Line 
                type="monotone" 
                dataKey="avg_response_time" 
                stroke={chartColors.primary} 
                strokeWidth={2}
                name={t('dashboard.actual_time')}
              />
              <Line 
                type="monotone" 
                dataKey="target_time" 
                stroke={chartColors.warning} 
                strokeDasharray="5 5"
                name={t('dashboard.target_time')}
              />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    );
  };

  // Render channel distribution
  const renderChannelDistribution = () => {
    if (!metrics?.channel_distribution) return null;

    const channelIcons = {
      sms: <PhoneIcon />,
      email: <EmailIcon />,
      in_app: <ChatIcon />
    };

    return (
      <Card>
        <CardHeader title={t('dashboard.channel_distribution')} />
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
            <ResponsiveContainer width="60%" height={200}>
              <PieChart>
                <Pie
                  data={metrics.channel_distribution}
                  cx="50%"
                  cy="50%"
                  innerRadius={40}
                  outerRadius={80}
                  paddingAngle={5}
                  dataKey="conversation_count"
                >
                  {metrics.channel_distribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={Object.values(chartColors)[index]} />
                  ))}
                </Pie>
                <RechartsTooltip />
              </PieChart>
            </ResponsiveContainer>
            
            <Box sx={{ width: '35%' }}>
              <List dense>
                {metrics.channel_distribution.map((channel, index) => (
                  <ListItem key={channel.channel}>
                    <ListItemIcon>
                      <Box sx={{ color: Object.values(chartColors)[index] }}>
                        {channelIcons[channel.channel as keyof typeof channelIcons]}
                      </Box>
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
                            {channel.channel}
                          </Typography>
                          <Typography variant="body2" fontWeight="bold">
                            {channel.conversation_count}
                          </Typography>
                        </Box>
                      }
                      secondary={
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="caption">
                            {formatDuration(channel.avg_response_time * 60)} avg
                          </Typography>
                          <Typography variant="caption">
                            {formatPercentage(channel.satisfaction_rate)} satisfaction
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </Box>
          </Box>
        </CardContent>
      </Card>
    );
  };

  // Render recent activity
  const renderRecentActivity = () => {
    if (!metrics?.recent_conversations) return null;

    return (
      <Card>
        <CardHeader
          title={t('dashboard.recent_activity')}
          subheader={`${metrics.recent_conversations.length} recent conversations`}
        />
        <CardContent>
          <List>
            {metrics.recent_conversations.slice(0, 10).map((conversation, index) => (
              <React.Fragment key={conversation.conversation_id}>
                <ListItem>
                  <ListItemIcon>
                    <Avatar sx={{ width: 32, height: 32 }}>
                      {conversation.participant_name[0] || 'U'}
                    </Avatar>
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body2">
                          {conversation.participant_name}
                        </Typography>
                        <Chip
                          size="small"
                          label={conversation.status}
                          color={conversation.status === 'resolved' ? 'success' : 'default'}
                          variant="outlined"
                        />
                        {conversation.priority !== 'normal' && (
                          <Chip
                            size="small"
                            label={conversation.priority}
                            color={conversation.priority === 'urgent' ? 'error' : 'warning'}
                            variant="outlined"
                          />
                        )}
                      </Box>
                    }
                    secondary={
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 0.5 }}>
                        <Typography variant="caption">
                          {conversation.agent_name ? `Assigned to ${conversation.agent_name}` : 'Unassigned'}
                        </Typography>
                        <Typography variant="caption">
                          {formatDistanceToNow(new Date(conversation.last_activity_at), { addSuffix: true })}
                        </Typography>
                      </Box>
                    }
                  />
                </ListItem>
                {index < metrics.recent_conversations.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        </CardContent>
      </Card>
    );
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          {t('dashboard.error_loading')}
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          {t('dashboard.customer_service_dashboard')}
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 2 }}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>{t('dashboard.date_range')}</InputLabel>
            <Select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              label={t('dashboard.date_range')}
            >
              <MenuItem value="1d">{t('dashboard.today')}</MenuItem>
              <MenuItem value="7d">{t('dashboard.last_7_days')}</MenuItem>
              <MenuItem value="30d">{t('dashboard.last_30_days')}</MenuItem>
            </Select>
          </FormControl>
          
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>{t('dashboard.agent')}</InputLabel>
            <Select
              value={selectedAgent}
              onChange={(e) => setSelectedAgent(e.target.value)}
              label={t('dashboard.agent')}
            >
              <MenuItem value="all">{t('dashboard.all_agents')}</MenuItem>
              {metrics?.agent_summaries?.map((agent) => (
                <MenuItem key={agent.agent_id} value={agent.agent_id.toString()}>
                  {agent.agent_name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          
          <IconButton onClick={() => refetch()}>
            <RefreshIcon />
          </IconButton>
          
          <Button startIcon={<DownloadIcon />} variant="outlined" size="small">
            {t('dashboard.export')}
          </Button>
        </Box>
      </Box>

      {/* Real-time status indicator */}
      <Box sx={{ mb: 3 }}>
        <Alert 
          severity={realTimeStats?.system_status === 'healthy' ? 'success' : 'warning'}
          icon={<InfoIcon />}
        >
          {t('dashboard.last_updated')}: {formatDistanceToNow(new Date(), { addSuffix: true })} • 
          {realTimeStats?.active_agents || 0} agents online • 
          {realTimeStats?.active_conversations || 0} active conversations
        </Alert>
      </Box>

      {/* Key Metrics */}
      <Box sx={{ mb: 4 }}>
        {renderMetricsCards()}
      </Box>

      {/* Charts and Tables */}
      <Grid container spacing={3}>
        {/* Response Time Trends */}
        <Grid item xs={12} lg={8}>
          {renderResponseTimeTrends()}
        </Grid>
        
        {/* Channel Distribution */}
        <Grid item xs={12} lg={4}>
          {renderChannelDistribution()}
        </Grid>
        
        {/* Agent Performance */}
        <Grid item xs={12}>
          {renderAgentPerformance()}
        </Grid>
        
        {/* Recent Activity */}
        <Grid item xs={12} md={6}>
          {renderRecentActivity()}
        </Grid>
        
        {/* Overdue Conversations */}
        <Grid item xs={12} md={6}>
          {metrics?.overdue_conversations && metrics.overdue_conversations.length > 0 && (
            <Card>
              <CardHeader
                title={t('dashboard.overdue_conversations')}
                titleTypographyProps={{ color: 'error' }}
              />
              <CardContent>
                <List>
                  {metrics.overdue_conversations.slice(0, 5).map((conversation) => (
                    <ListItem key={conversation.conversation_id}>
                      <ListItemText
                        primary={conversation.participant_name}
                        secondary={`Overdue by ${formatDuration((conversation.response_time_minutes || 0) * 60)}`}
                      />
                      <Chip size="small" label={conversation.priority} color="error" />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          )}
        </Grid>
      </Grid>
    </Box>
  );
};

export default CustomerServiceDashboard;