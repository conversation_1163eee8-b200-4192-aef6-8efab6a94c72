import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Shield, Smartphone, Mail, Key, AlertCircle } from 'lucide-react';
import { Modal } from '../common/Modal';
import Button from '../common/Button';
import { Input } from '../common/Input';
import LoadingSpinner from '../ui/LoadingSpinner';
import api from '../../services/api';
import toast from 'react-hot-toast';

interface TwoFactorVerificationProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (token: string) => void;
  challengeId: string;
  methods: Array<{
    method: 'sms' | 'email' | 'totp';
    hint?: string;
  }>;
}

export const TwoFactorVerification: React.FC<TwoFactorVerificationProps> = ({
  isOpen,
  onClose,
  onSuccess,
  challengeId,
  methods
}) => {
  const { t } = useTranslation();
  const [selectedMethod, setSelectedMethod] = useState(methods[0]?.method || 'totp');
  const [verificationCode, setVerificationCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [resending, setResending] = useState(false);
  const [trustDevice, setTrustDevice] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleVerify = async () => {
    if (!verificationCode || verificationCode.length !== 6) {
      setError(t('auth.twoFactor.invalidCode'));
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const response = await api.post('/api/v1/auth/2fa/verify-login', {
        challengeId,
        method: selectedMethod,
        code: verificationCode,
        trustDevice
      });

      toast.success(t('auth.twoFactor.verificationSuccess'));
      onSuccess(response.data.token);
    } catch (error: any) {
      console.error('2FA verification error:', error);
      
      if (error.response?.status === 400) {
        setError(t('auth.twoFactor.invalidCode'));
      } else if (error.response?.status === 429) {
        setError(t('auth.twoFactor.tooManyAttempts'));
      } else {
        setError(t('auth.twoFactor.verificationError'));
      }
    } finally {
      setLoading(false);
    }
  };

  const handleResendCode = async () => {
    if (selectedMethod === 'totp') return;

    try {
      setResending(true);
      await api.post('/api/v1/auth/2fa/resend', {
        challengeId,
        method: selectedMethod
      });
      
      toast.success(
        selectedMethod === 'sms' 
          ? t('auth.twoFactor.smsSent')
          : t('auth.twoFactor.emailSent')
      );
    } catch (error) {
      console.error('Error resending code:', error);
      toast.error(t('auth.twoFactor.resendError'));
    } finally {
      setResending(false);
    }
  };

  const getMethodIcon = (method: string) => {
    switch (method) {
      case 'sms':
        return <Smartphone className="w-5 h-5" />;
      case 'email':
        return <Mail className="w-5 h-5" />;
      case 'totp':
        return <Key className="w-5 h-5" />;
      default:
        return <Shield className="w-5 h-5" />;
    }
  };

  const getMethodLabel = (method: string) => {
    switch (method) {
      case 'sms':
        return t('auth.twoFactor.methods.sms');
      case 'email':
        return t('auth.twoFactor.methods.email');
      case 'totp':
        return t('auth.twoFactor.methods.totp');
      default:
        return method;
    }
  };

  const getMethodDescription = (method: string, hint?: string) => {
    switch (method) {
      case 'sms':
        return hint ? t('auth.twoFactor.smsHint', { phone: hint }) : t('auth.twoFactor.smsDescription');
      case 'email':
        return hint ? t('auth.twoFactor.emailHint', { email: hint }) : t('auth.twoFactor.emailDescription');
      case 'totp':
        return t('auth.twoFactor.totpDescription');
      default:
        return '';
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={t('auth.twoFactor.title')}
      size="sm"
      showCloseButton={false}
      closeOnBackdrop={false}
      closeOnEscape={false}
    >
      <div className="space-y-6">
        {/* Method selection */}
        {methods.length > 1 && (
          <div className="space-y-3">
            <p className="text-sm text-gray-600">
              {t('auth.twoFactor.selectMethod')}
            </p>
            <div className="space-y-2">
              {methods.map((method) => (
                <button
                  key={method.method}
                  onClick={() => {
                    setSelectedMethod(method.method);
                    setVerificationCode('');
                    setError(null);
                  }}
                  className={`w-full flex items-center gap-3 p-3 rounded-lg border transition-colors ${
                    selectedMethod === method.method
                      ? 'border-primary-500 bg-primary-50'
                      : 'border-gray-200 hover:bg-gray-50'
                  }`}
                >
                  {getMethodIcon(method.method)}
                  <div className="text-left">
                    <p className="font-medium text-gray-900">
                      {getMethodLabel(method.method)}
                    </p>
                    <p className="text-sm text-gray-600">
                      {getMethodDescription(method.method, method.hint)}
                    </p>
                  </div>
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Current method description */}
        {methods.length === 1 && (
          <div className="flex items-center gap-3 p-4 bg-blue-50 rounded-lg">
            {getMethodIcon(selectedMethod)}
            <p className="text-sm text-blue-700">
              {getMethodDescription(
                selectedMethod,
                methods.find(m => m.method === selectedMethod)?.hint
              )}
            </p>
          </div>
        )}

        {/* Verification code input */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t('auth.twoFactor.enterCode')}
          </label>
          <Input
            type="text"
            value={verificationCode}
            onChange={(e) => {
              const value = e.target.value.replace(/\D/g, '').slice(0, 6);
              setVerificationCode(value);
              setError(null);
            }}
            placeholder="000000"
            maxLength={6}
            className="text-center text-2xl font-mono"
            autoFocus
          />
          {selectedMethod !== 'totp' && (
            <button
              onClick={handleResendCode}
              disabled={resending}
              className="mt-2 text-sm text-primary-600 hover:text-primary-700"
            >
              {resending ? (
                <span className="flex items-center gap-2">
                  <LoadingSpinner size="sm" />
                  {t('auth.twoFactor.resending')}
                </span>
              ) : (
                t('auth.twoFactor.resendCode')
              )}
            </button>
          )}
        </div>

        {/* Trust device option */}
        <label className="flex items-center gap-2">
          <input
            type="checkbox"
            checked={trustDevice}
            onChange={(e) => setTrustDevice(e.target.checked)}
            className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
          />
          <span className="text-sm text-gray-700">
            {t('auth.twoFactor.trustDevice')}
          </span>
        </label>

        {/* Error message */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3">
            <div className="flex">
              <AlertCircle className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-800">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex gap-3">
          <Button
            variant="ghost"
            onClick={onClose}
            disabled={loading}
            className="flex-1"
          >
            {t('common.cancel')}
          </Button>
          <Button
            variant="primary"
            onClick={handleVerify}
            disabled={loading || verificationCode.length !== 6}
            className="flex-1"
          >
            {loading ? (
              <>
                <LoadingSpinner size="sm" className="mr-2" />
                {t('auth.twoFactor.verifying')}
              </>
            ) : (
              t('auth.twoFactor.verify')
            )}
          </Button>
        </div>

        {/* Help text */}
        <p className="text-xs text-center text-gray-500">
          {t('auth.twoFactor.havingTrouble')}{' '}
          <a href="#" className="text-primary-600 hover:text-primary-700">
            {t('auth.twoFactor.useBackupCode')}
          </a>
        </p>
      </div>
    </Modal>
  );
};