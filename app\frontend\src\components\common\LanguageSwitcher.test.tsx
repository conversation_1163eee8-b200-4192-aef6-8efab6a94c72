/**
 * Unit Tests for LanguageSwitcher Component
 * 
 * Tests language switching UI, user preferences,
 * and integration with translation system.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { I18nextProvider } from 'react-i18next';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

import LanguageSwitcher from './LanguageSwitcher';
import { useTranslation } from '../../hooks/useTranslation';
import { useLanguage } from '../../hooks/useLanguage';
import i18n from '../i18n';

// Mock hooks
vi.mock('../../hooks/useTranslation');
vi.mock('../../hooks/useLanguage');

// Mock API
vi.mock('../../services/api', () => ({
  api: {
    post: vi.fn(),
    get: vi.fn()
  }
}));

describe('LanguageSwitcher', () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  });

  const mockT = vi.fn((key) => key);
  const mockSwitchLanguage = vi.fn();
  const mockDetectLanguage = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    
    (useTranslation as any).mockReturnValue({
      t: mockT,
      i18n: {
        language: 'en',
        changeLanguage: vi.fn()
      }
    });
    
    (useLanguage as any).mockReturnValue({
      currentLanguage: 'en',
      switchLanguage: mockSwitchLanguage,
      detectLanguage: mockDetectLanguage,
      isLoading: false,
      preferences: {
        auto_detect: false,
        quebec_french_preference: false
      }
    });
  });

  const renderComponent = (props = {}) => {
    return render(
      <QueryClientProvider client={queryClient}>
        <I18nextProvider i18n={i18n}>
          <LanguageSwitcher {...props} />
        </I18nextProvider>
      </QueryClientProvider>
    );
  };

  it('renders language switcher button', () => {
    renderComponent();
    
    const button = screen.getByRole('button', { name: /language/i });
    expect(button).toBeInTheDocument();
    expect(button).toHaveTextContent('EN');
  });

  it('shows dropdown menu on click', async () => {
    const user = userEvent.setup();
    renderComponent();
    
    const button = screen.getByRole('button', { name: /language/i });
    await user.click(button);
    
    expect(screen.getByText('English')).toBeInTheDocument();
    expect(screen.getByText('Français')).toBeInTheDocument();
  });

  it('switches language on selection', async () => {
    const user = userEvent.setup();
    renderComponent();
    
    // Open dropdown
    const button = screen.getByRole('button', { name: /language/i });
    await user.click(button);
    
    // Select French
    const frenchOption = screen.getByText('Français');
    await user.click(frenchOption);
    
    expect(mockSwitchLanguage).toHaveBeenCalledWith('fr', true);
  });

  it('shows current language with check mark', async () => {
    const user = userEvent.setup();
    renderComponent();
    
    const button = screen.getByRole('button', { name: /language/i });
    await user.click(button);
    
    const englishOption = screen.getByText('English').closest('div');
    expect(englishOption).toHaveClass('font-medium');
    expect(englishOption?.querySelector('svg')).toBeInTheDocument(); // Check mark
  });

  it('displays French when language is fr', () => {
    (useLanguage as any).mockReturnValue({
      currentLanguage: 'fr',
      switchLanguage: mockSwitchLanguage,
      detectLanguage: mockDetectLanguage,
      isLoading: false,
      preferences: {}
    });
    
    renderComponent();
    
    const button = screen.getByRole('button', { name: /language/i });
    expect(button).toHaveTextContent('FR');
  });

  it('shows loading state during language switch', () => {
    (useLanguage as any).mockReturnValue({
      currentLanguage: 'en',
      switchLanguage: mockSwitchLanguage,
      detectLanguage: mockDetectLanguage,
      isLoading: true,
      preferences: {}
    });
    
    renderComponent();
    
    const button = screen.getByRole('button', { name: /language/i });
    expect(button).toBeDisabled();
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });

  it('handles keyboard navigation', async () => {
    const user = userEvent.setup();
    renderComponent();
    
    // Focus button
    const button = screen.getByRole('button', { name: /language/i });
    button.focus();
    
    // Open with Enter
    await user.keyboard('{Enter}');
    expect(screen.getByText('English')).toBeInTheDocument();
    
    // Navigate with arrow keys
    await user.keyboard('{ArrowDown}');
    await user.keyboard('{Enter}');
    
    expect(mockSwitchLanguage).toHaveBeenCalledWith('fr', true);
  });

  it('closes dropdown when clicking outside', async () => {
    const user = userEvent.setup();
    renderComponent();
    
    // Open dropdown
    const button = screen.getByRole('button', { name: /language/i });
    await user.click(button);
    expect(screen.getByText('English')).toBeInTheDocument();
    
    // Click outside
    await user.click(document.body);
    
    await waitFor(() => {
      expect(screen.queryByText('English')).not.toBeInTheDocument();
    });
  });

  it('shows auto-detect option when enabled', async () => {
    (useLanguage as any).mockReturnValue({
      currentLanguage: 'en',
      switchLanguage: mockSwitchLanguage,
      detectLanguage: mockDetectLanguage,
      isLoading: false,
      preferences: {
        auto_detect: true
      }
    });
    
    const user = userEvent.setup();
    renderComponent();
    
    const button = screen.getByRole('button', { name: /language/i });
    await user.click(button);
    
    expect(screen.getByText('Auto-detect')).toBeInTheDocument();
  });

  it('triggers language detection on auto-detect click', async () => {
    (useLanguage as any).mockReturnValue({
      currentLanguage: 'en',
      switchLanguage: mockSwitchLanguage,
      detectLanguage: mockDetectLanguage,
      isLoading: false,
      preferences: {
        auto_detect: true
      }
    });
    
    const user = userEvent.setup();
    renderComponent();
    
    const button = screen.getByRole('button', { name: /language/i });
    await user.click(button);
    
    const autoDetectOption = screen.getByText('Auto-detect');
    await user.click(autoDetectOption);
    
    expect(mockDetectLanguage).toHaveBeenCalled();
  });

  it('shows Quebec French indicator when detected', () => {
    (useLanguage as any).mockReturnValue({
      currentLanguage: 'fr',
      switchLanguage: mockSwitchLanguage,
      detectLanguage: mockDetectLanguage,
      isLoading: false,
      preferences: {
        quebec_french_preference: true
      }
    });
    
    renderComponent();
    
    const button = screen.getByRole('button', { name: /language/i });
    expect(button).toHaveTextContent('FR-QC');
  });

  it('persists language preference by default', async () => {
    const user = userEvent.setup();
    renderComponent();
    
    const button = screen.getByRole('button', { name: /language/i });
    await user.click(button);
    
    const frenchOption = screen.getByText('Français');
    await user.click(frenchOption);
    
    // Should persist by default (second argument is true)
    expect(mockSwitchLanguage).toHaveBeenCalledWith('fr', true);
  });

  it('handles temporary language switch', async () => {
    const user = userEvent.setup();
    renderComponent({ persist: false });
    
    const button = screen.getByRole('button', { name: /language/i });
    await user.click(button);
    
    const frenchOption = screen.getByText('Français');
    await user.click(frenchOption);
    
    // Should not persist
    expect(mockSwitchLanguage).toHaveBeenCalledWith('fr', false);
  });

  it('shows error state on language switch failure', async () => {
    mockSwitchLanguage.mockRejectedValueOnce(new Error('Network error'));
    
    const user = userEvent.setup();
    renderComponent();
    
    const button = screen.getByRole('button', { name: /language/i });
    await user.click(button);
    
    const frenchOption = screen.getByText('Français');
    await user.click(frenchOption);
    
    await waitFor(() => {
      expect(screen.getByText(/error/i)).toBeInTheDocument();
    });
  });

  it('applies custom className', () => {
    renderComponent({ className: 'custom-class' });
    
    const container = screen.getByTestId('language-switcher');
    expect(container).toHaveClass('custom-class');
  });

  it('supports compact mode', () => {
    renderComponent({ compact: true });
    
    const button = screen.getByRole('button', { name: /language/i });
    expect(button).toHaveClass('px-2 py-1 text-sm');
  });

  it('shows language flags when enabled', async () => {
    const user = userEvent.setup();
    renderComponent({ showFlags: true });
    
    const button = screen.getByRole('button', { name: /language/i });
    await user.click(button);
    
    expect(screen.getByAltText('English flag')).toBeInTheDocument();
    expect(screen.getByAltText('French flag')).toBeInTheDocument();
  });

  it('supports custom language options', async () => {
    const customLanguages = [
      { code: 'en', name: 'English', flag: '🇺🇸' },
      { code: 'fr', name: 'Français', flag: '🇫🇷' },
      { code: 'es', name: 'Español', flag: '🇪🇸' }
    ];
    
    const user = userEvent.setup();
    renderComponent({ languages: customLanguages });
    
    const button = screen.getByRole('button', { name: /language/i });
    await user.click(button);
    
    expect(screen.getByText('Español')).toBeInTheDocument();
  });

  it('triggers onChange callback', async () => {
    const onChange = vi.fn();
    const user = userEvent.setup();
    renderComponent({ onChange });
    
    const button = screen.getByRole('button', { name: /language/i });
    await user.click(button);
    
    const frenchOption = screen.getByText('Français');
    await user.click(frenchOption);
    
    expect(onChange).toHaveBeenCalledWith('fr');
  });

  it('respects disabled state', () => {
    renderComponent({ disabled: true });
    
    const button = screen.getByRole('button', { name: /language/i });
    expect(button).toBeDisabled();
  });

  it('shows tooltip on hover', async () => {
    const user = userEvent.setup();
    renderComponent({ showTooltip: true });
    
    const button = screen.getByRole('button', { name: /language/i });
    await user.hover(button);
    
    await waitFor(() => {
      expect(screen.getByText(/change language/i)).toBeInTheDocument();
    });
  });

  it('integrates with user preferences', async () => {
    const mockPreferences = {
      preferred_language: 'fr',
      auto_detect: false,
      quebec_french_preference: true,
      language_history: [
        { language: 'en', timestamp: '2024-01-01' },
        { language: 'fr', timestamp: '2024-01-15' }
      ]
    };
    
    (useLanguage as any).mockReturnValue({
      currentLanguage: 'fr',
      switchLanguage: mockSwitchLanguage,
      detectLanguage: mockDetectLanguage,
      isLoading: false,
      preferences: mockPreferences
    });
    
    renderComponent({ showHistory: true });
    
    const button = screen.getByRole('button', { name: /language/i });
    await userEvent.click(button);
    
    expect(screen.getByText(/recently used/i)).toBeInTheDocument();
  });

  it('handles RTL languages properly', () => {
    (useLanguage as any).mockReturnValue({
      currentLanguage: 'ar',
      switchLanguage: mockSwitchLanguage,
      detectLanguage: mockDetectLanguage,
      isLoading: false,
      preferences: {},
      isRTL: true
    });
    
    renderComponent();
    
    const container = screen.getByTestId('language-switcher');
    expect(container).toHaveAttribute('dir', 'rtl');
  });
});

describe('LanguageSwitcher Accessibility', () => {
  const queryClient = new QueryClient();
  
  const renderComponent = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <I18nextProvider i18n={i18n}>
          <LanguageSwitcher />
        </I18nextProvider>
      </QueryClientProvider>
    );
  };

  it('has proper ARIA labels', () => {
    renderComponent();
    
    const button = screen.getByRole('button', { name: /language/i });
    expect(button).toHaveAttribute('aria-label');
    expect(button).toHaveAttribute('aria-expanded', 'false');
  });

  it('updates aria-expanded on dropdown toggle', async () => {
    const user = userEvent.setup();
    renderComponent();
    
    const button = screen.getByRole('button', { name: /language/i });
    
    await user.click(button);
    expect(button).toHaveAttribute('aria-expanded', 'true');
    
    await user.click(button);
    expect(button).toHaveAttribute('aria-expanded', 'false');
  });

  it('has proper focus management', async () => {
    const user = userEvent.setup();
    renderComponent();
    
    const button = screen.getByRole('button', { name: /language/i });
    await user.click(button);
    
    const firstOption = screen.getByText('English');
    expect(firstOption).toHaveAttribute('tabIndex', '0');
  });

  it('announces language changes to screen readers', async () => {
    const user = userEvent.setup();
    renderComponent();
    
    const button = screen.getByRole('button', { name: /language/i });
    await user.click(button);
    
    const frenchOption = screen.getByText('Français');
    await user.click(frenchOption);
    
    // Check for aria-live region
    const announcement = screen.getByRole('status');
    expect(announcement).toHaveTextContent(/language changed/i);
  });
});