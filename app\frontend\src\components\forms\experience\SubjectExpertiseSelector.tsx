import React from 'react';
import { BookOpen, Star } from 'lucide-react';

interface SubjectExpertiseSelectorProps {
  value: Record<string, string>;
  onChange: (value: Record<string, string>) => void;
  error?: string;
}

const SUBJECTS = [
  { value: 'mathematics', label: 'Mathematics', icon: '📐' },
  { value: 'physics', label: 'Physics', icon: '⚛️' },
  { value: 'chemistry', label: 'Chemistry', icon: '🧪' },
  { value: 'biology', label: 'Biology', icon: '🧬' },
  { value: 'english', label: 'English', icon: '📚' },
  { value: 'french', label: 'French', icon: '🇫🇷' },
  { value: 'spanish', label: 'Spanish', icon: '🇪🇸' },
  { value: 'history', label: 'History', icon: '📜' },
  { value: 'geography', label: 'Geography', icon: '🌍' },
  { value: 'computer_science', label: 'Computer Science', icon: '💻' },
  { value: 'economics', label: 'Economics', icon: '📊' },
  { value: 'psychology', label: 'Psychology', icon: '🧠' }
];

const EXPERTISE_LEVELS = [
  { 
    value: 'beginner', 
    label: 'Beginner',
    description: 'Can tutor elementary level',
    stars: 1,
    color: 'text-gray-400'
  },
  { 
    value: 'intermediate', 
    label: 'Intermediate',
    description: 'Can tutor up to high school',
    stars: 2,
    color: 'text-yellow-500'
  },
  { 
    value: 'advanced', 
    label: 'Advanced',
    description: 'Can tutor college prep/AP',
    stars: 3,
    color: 'text-orange-500'
  },
  { 
    value: 'expert', 
    label: 'Expert',
    description: 'Can tutor university level',
    stars: 4,
    color: 'text-red-600'
  }
];

export const SubjectExpertiseSelector: React.FC<SubjectExpertiseSelectorProps> = ({
  value = {},
  onChange,
  error
}) => {
  const handleExpertiseChange = (subject: string, expertise: string) => {
    const newValue = { ...value };
    
    if (expertise === '') {
      delete newValue[subject];
    } else {
      newValue[subject] = expertise;
    }
    
    onChange(newValue);
  };

  const getExpertiseLevel = (expertise: string) => {
    return EXPERTISE_LEVELS.find(level => level.value === expertise);
  };

  const renderStars = (count: number, color: string) => {
    return Array(4).fill(0).map((_, index) => (
      <Star
        key={index}
        className={`w-4 h-4 ${index < count ? color : 'text-gray-300'}`}
        fill={index < count ? 'currentColor' : 'none'}
      />
    ));
  };

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          <BookOpen className="inline-block w-4 h-4 mr-1" />
          Subject Expertise
        </label>

        {/* Selected subjects summary */}
        {Object.keys(value).length > 0 && (
          <div className="mb-4 p-3 bg-red-50 rounded-lg">
            <p className="text-sm font-medium text-gray-700 mb-2">Your Expertise:</p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {Object.entries(value).map(([subject, expertise]) => {
                const subjectInfo = SUBJECTS.find(s => s.value === subject);
                const level = getExpertiseLevel(expertise);
                return (
                  <div key={subject} className="flex items-center justify-between bg-white p-2 rounded">
                    <span className="text-sm">
                      {subjectInfo?.icon} {subjectInfo?.label || subject}
                    </span>
                    <div className="flex items-center gap-1">
                      {renderStars(level?.stars || 0, level?.color || 'text-gray-400')}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Subject grid */}
        <div className="border border-gray-200 rounded-lg divide-y divide-gray-200">
          {SUBJECTS.map(subject => {
            const currentLevel = value[subject.value];
            const expertiseLevel = currentLevel ? getExpertiseLevel(currentLevel) : null;

            return (
              <div key={subject.value} className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <span className="text-2xl">{subject.icon}</span>
                    <span className="font-medium text-gray-900">{subject.label}</span>
                  </div>
                  {expertiseLevel && (
                    <div className="flex items-center gap-1">
                      {renderStars(expertiseLevel.stars, expertiseLevel.color)}
                    </div>
                  )}
                </div>

                <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
                  <button
                    type="button"
                    onClick={() => handleExpertiseChange(subject.value, '')}
                    className={`px-3 py-2 text-xs rounded-lg transition-colors ${
                      !currentLevel
                        ? 'bg-gray-200 text-gray-700'
                        : 'bg-gray-100 text-gray-500 hover:bg-gray-200'
                    }`}
                  >
                    None
                  </button>
                  {EXPERTISE_LEVELS.map(level => (
                    <button
                      key={level.value}
                      type="button"
                      onClick={() => handleExpertiseChange(subject.value, level.value)}
                      className={`px-3 py-2 text-xs rounded-lg transition-colors ${
                        currentLevel === level.value
                          ? 'bg-red-600 text-white'
                          : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                      }`}
                      title={level.description}
                    >
                      <div className="flex flex-col items-center gap-1">
                        <span>{level.label}</span>
                        <div className="flex gap-0.5">
                          {Array(level.stars).fill(0).map((_, i) => (
                            <Star key={i} className="w-3 h-3" fill="currentColor" />
                          ))}
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            );
          })}
        </div>

        {/* Help text */}
        <div className="mt-3 text-xs text-gray-500">
          <p>Select your expertise level for each subject you can teach:</p>
          <ul className="mt-1 space-y-1">
            {EXPERTISE_LEVELS.map(level => (
              <li key={level.value} className="flex items-center gap-2">
                <div className="flex gap-0.5">
                  {renderStars(level.stars, level.color)}
                </div>
                <span>{level.label}: {level.description}</span>
              </li>
            ))}
          </ul>
        </div>

        {error && (
          <p className="mt-2 text-sm text-red-600">{error}</p>
        )}
      </div>
    </div>
  );
};