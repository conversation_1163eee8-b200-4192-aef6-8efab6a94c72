"""
Client repository for client profiles, dependants, and learning needs.
Enhanced with authorization filtering and user context.
"""

import asyncpg
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, date

from app.database.repositories.base import BaseRepository
from app.core.exceptions import ResourceNotFoundError, BusinessLogicError, PermissionDeniedError
from app.core.timezone import now_est
from app.models.user_models import User, UserRoleType
from app.core.permissions import permission_engine, ResourceType, PermissionAction

logger = logging.getLogger(__name__)


class ClientRepository(BaseRepository):
    """Repository for client profiles and related data."""
    
    def __init__(self):
        super().__init__(table_name="client_profiles", id_column="client_id")
    
    async def find_by_user_id(
        self, 
        conn: asyncpg.Connection, 
        user_id: int
    ) -> Optional[asyncpg.Record]:
        """
        Find client profile by user ID.
        
        Args:
            conn: Database connection
            user_id: User ID to search for
        
        Returns:
            Client profile record if found, None otherwise
        """
        query = f"""
            SELECT * FROM {self.table_name}
            WHERE user_id = $1 AND deleted_at IS NULL
        """
        
        try:
            result = await conn.fetchrow(query, user_id)
            return result
        except Exception as e:
            logger.error(f"Error finding client by user ID: {e}")
            raise
    
    async def find_by_id_with_authorization(
        self,
        conn: asyncpg.Connection,
        client_id: int,
        current_user: User,
        action: PermissionAction = PermissionAction.VIEW
    ) -> Optional[asyncpg.Record]:
        """
        Find client profile by ID with authorization check.
        
        Args:
            conn: Database connection
            client_id: Client ID to find
            current_user: User requesting access
            action: Action being performed
            
        Returns:
            Client profile record if found and authorized
            
        Raises:
            PermissionDeniedError: If user lacks permission
        """
        # Check permission first
        has_permission = await permission_engine.check_permission(
            user=current_user,
            resource_type=ResourceType.CLIENT_PROFILE,
            action=action,
            resource_id=client_id,
            db=conn
        )
        
        if not has_permission:
            raise PermissionDeniedError(
                f"User {current_user.user_id} cannot {action.value} client profile {client_id}",
                resource_type="client_profile",
                action=action.value,
                resource_id=client_id
            )
        
        return await self.find_by_id(conn, client_id)
    
    async def list_accessible_clients(
        self,
        conn: asyncpg.Connection,
        current_user: User,
        filters: Optional[Dict[str, Any]] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None
    ) -> List[asyncpg.Record]:
        """
        List client profiles accessible to the current user.
        
        Args:
            conn: Database connection
            current_user: User requesting access
            filters: Additional filters to apply
            limit: Maximum number of records to return
            offset: Number of records to skip
            
        Returns:
            List of accessible client profiles
        """
        # Get authorization filter
        auth_filter = await permission_engine.filter_accessible_resources(
            user=current_user,
            resource_type=ResourceType.CLIENT_PROFILE,
            action=PermissionAction.VIEW,
            db=conn
        )
        
        # Build base query
        query_parts = [f"SELECT * FROM {self.table_name}"]
        query_params = []
        param_count = 0
        
        # Add authorization filter
        where_conditions = [auth_filter, "deleted_at IS NULL"]
        
        # Add additional filters
        if filters:
            for field, value in filters.items():
                if value is not None:
                    param_count += 1
                    where_conditions.append(f"{field} = ${param_count}")
                    query_params.append(value)
        
        # Combine conditions
        if where_conditions:
            query_parts.append("WHERE " + " AND ".join(where_conditions))
        
        # Add ordering
        query_parts.append("ORDER BY last_name, first_name")
        
        # Add pagination
        if limit:
            param_count += 1
            query_parts.append(f"LIMIT ${param_count}")
            query_params.append(limit)
        
        if offset:
            param_count += 1
            query_parts.append(f"OFFSET ${param_count}")
            query_params.append(offset)
        
        query = " ".join(query_parts)
        
        try:
            results = await conn.fetch(query, *query_params)
            return results
        except Exception as e:
            logger.error(f"Error listing accessible clients: {e}")
            raise
    
    async def get_client_dependants(
        self, 
        conn: asyncpg.Connection, 
        client_id: int,
        include_relationships: bool = True
    ) -> List[asyncpg.Record]:
        """
        Get all dependants for a client.
        
        Args:
            conn: Database connection
            client_id: Client ID
            include_relationships: Whether to include relationship details
        
        Returns:
            List of dependant records
        """
        if include_relationships:
            query = """
                SELECT 
                    cd.*,
                    cr.relationship_type,
                    cr.is_primary,
                    CASE 
                        WHEN cd.user_id IS NOT NULL THEN 'has_account'
                        ELSE 'no_account'
                    END as account_status
                FROM client_dependants cd
                LEFT JOIN client_relationships cr ON cd.dependant_id = cr.dependant_id 
                    AND cr.client_id = $1
                WHERE cd.primary_client_id = $1 OR cd.secondary_client_id = $1
                ORDER BY cd.first_name, cd.last_name
            """
        else:
            query = """
                SELECT cd.*
                FROM client_dependants cd
                WHERE cd.primary_client_id = $1 OR cd.secondary_client_id = $1
                ORDER BY cd.first_name, cd.last_name
            """
        
        try:
            results = await conn.fetch(query, client_id)
            return results
        except Exception as e:
            logger.error(f"Error getting client dependants: {e}")
            raise
    
    async def create_dependant(
        self,
        conn: asyncpg.Connection,
        dependant_data: Dict[str, Any],
        primary_client_id: int,
        secondary_client_id: Optional[int] = None
    ) -> asyncpg.Record:
        """
        Create a dependant and establish client relationships.
        
        Args:
            conn: Database connection
            dependant_data: Dependant information
            primary_client_id: Primary client (main responsible parent)
            secondary_client_id: Optional secondary client (separated families)
        
        Returns:
            Created dependant record
        """
        try:
            async with conn.transaction():
                # Validate that primary and secondary clients are different
                if secondary_client_id and primary_client_id == secondary_client_id:
                    raise BusinessLogicError(
                        "Primary and secondary clients must be different",
                        {"primary_client_id": primary_client_id, "secondary_client_id": secondary_client_id}
                    )
                
                # Add client IDs to dependant data
                dependant_data.update({
                    "primary_client_id": primary_client_id,
                    "secondary_client_id": secondary_client_id,
                    "created_at": now_est(),
                    "updated_at": now_est()
                })
                
                # Create dependant
                dependant = await self._create_dependant_record(conn, dependant_data)
                
                # Create primary client relationship
                await self._create_client_relationship(
                    conn, primary_client_id, dependant['dependant_id'],
                    dependant_data.get('relationship_type', 'parent'), True
                )
                
                # Create secondary client relationship if provided
                if secondary_client_id:
                    await self._create_client_relationship(
                        conn, secondary_client_id, dependant['dependant_id'],
                        dependant_data.get('secondary_relationship_type', 'parent'), False
                    )
                
                logger.info(f"Created dependant {dependant['dependant_id']} for client {primary_client_id}")
                return dependant
                
        except Exception as e:
            logger.error(f"Error creating dependant: {e}")
            raise
    
    async def _create_dependant_record(
        self, 
        conn: asyncpg.Connection, 
        dependant_data: Dict[str, Any]
    ) -> asyncpg.Record:
        """Create dependant record in client_dependants table."""
        query = """
            INSERT INTO client_dependants 
            (user_id, primary_client_id, secondary_client_id, first_name, last_name, 
             birth_date, relationship_type, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            RETURNING *
        """
        
        result = await conn.fetchrow(
            query,
            dependant_data.get('user_id'),
            dependant_data['primary_client_id'],
            dependant_data.get('secondary_client_id'),
            dependant_data['first_name'],
            dependant_data['last_name'],
            dependant_data['birth_date'],
            dependant_data.get('relationship_type', 'parent'),
            dependant_data['created_at'],
            dependant_data['updated_at']
        )
        
        return result
    
    async def _create_client_relationship(
        self,
        conn: asyncpg.Connection,
        client_id: int,
        dependant_id: int,
        relationship_type: str,
        is_primary: bool
    ) -> asyncpg.Record:
        """Create client-dependant relationship record."""
        query = """
            INSERT INTO client_relationships 
            (client_id, dependant_id, relationship_type, is_primary, created_at)
            VALUES ($1, $2, $3, $4, $5)
            ON CONFLICT (client_id, dependant_id)
            DO UPDATE SET relationship_type = $3, is_primary = $4
            RETURNING *
        """
        
        result = await conn.fetchrow(
            query, client_id, dependant_id, relationship_type, 
            is_primary, now_est()
        )
        
        return result
    
    async def get_client_learning_needs(
        self, 
        conn: asyncpg.Connection, 
        client_id: Optional[int] = None,
        dependant_id: Optional[int] = None,
        active_only: bool = True
    ) -> List[asyncpg.Record]:
        """
        Get learning needs for a client or dependant.
        
        Args:
            conn: Database connection
            client_id: Client ID (for adult learner)
            dependant_id: Dependant ID (for child learner)
            active_only: Whether to return only active needs
        
        Returns:
            List of learning need records
        """
        if not client_id and not dependant_id:
            raise ValueError("Either client_id or dependant_id must be provided")
        
        query = """
            SELECT cn.*, sa.subject_name, st.type_name as service_type_name
            FROM client_needs cn
            LEFT JOIN subject_areas sa ON cn.subject_area = sa.subject_name
            LEFT JOIN service_types st ON cn.service_type = st.type_name
            WHERE 
        """
        
        params = []
        if client_id:
            query += "cn.client_id = $1"
            params.append(client_id)
        else:
            query += "cn.dependant_id = $1"
            params.append(dependant_id)
        
        if active_only:
            query += " AND cn.is_active = true"
        
        query += " ORDER BY cn.created_at DESC"
        
        try:
            results = await conn.fetch(query, *params)
            return results
        except Exception as e:
            logger.error(f"Error getting client learning needs: {e}")
            raise
    
    async def create_learning_need(
        self,
        conn: asyncpg.Connection,
        need_data: Dict[str, Any]
    ) -> asyncpg.Record:
        """
        Create a learning need for a client or dependant.
        
        Args:
            conn: Database connection
            need_data: Learning need information
        
        Returns:
            Created learning need record
        """
        # Validate that either client_id or dependant_id is provided
        if not need_data.get('client_id') and not need_data.get('dependant_id'):
            raise BusinessLogicError(
                "Either client_id or dependant_id must be provided",
                need_data
            )
        
        if need_data.get('client_id') and need_data.get('dependant_id'):
            raise BusinessLogicError(
                "Cannot specify both client_id and dependant_id",
                need_data
            )
        
        query = """
            INSERT INTO client_needs 
            (client_id, dependant_id, subject_area, service_type, location_preference,
             frequency_preference, duration_preference, notes, is_active, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
            RETURNING *
        """
        
        now = now_est()
        
        try:
            result = await conn.fetchrow(
                query,
                need_data.get('client_id'),
                need_data.get('dependant_id'),
                need_data['subject_area'],
                need_data['service_type'],
                need_data.get('location_preference'),
                need_data.get('frequency_preference'),
                need_data.get('duration_preference'),
                need_data.get('notes'),
                need_data.get('is_active', True),
                now,
                now
            )
            
            logger.info(f"Created learning need {result['need_id']}")
            return result
            
        except Exception as e:
            logger.error(f"Error creating learning need: {e}")
            raise
    
    async def find_clients_by_name(
        self, 
        conn: asyncpg.Connection, 
        search_term: str,
        limit: int = 20
    ) -> List[asyncpg.Record]:
        """
        Search clients by name (first name or last name).
        
        Args:
            conn: Database connection
            search_term: Search term to match against names
            limit: Maximum number of results
        
        Returns:
            List of matching client records
        """
        query = """
            SELECT cp.*, ua.email
            FROM client_profiles cp
            JOIN user_accounts ua ON cp.user_id = ua.user_id
            WHERE (
                cp.first_name ILIKE $1 OR 
                cp.last_name ILIKE $1 OR
                CONCAT(cp.first_name, ' ', cp.last_name) ILIKE $1
            )
            AND ua.deleted_at IS NULL
            ORDER BY cp.first_name, cp.last_name
            LIMIT $2
        """
        
        search_pattern = f"%{search_term.strip()}%"
        
        try:
            results = await conn.fetch(query, search_pattern, limit)
            return results
        except Exception as e:
            logger.error(f"Error searching clients by name: {e}")
            raise
    
    async def get_client_emergency_contacts(
        self, 
        conn: asyncpg.Connection, 
        client_id: int
    ) -> Optional[Dict[str, Any]]:
        """
        Get emergency contact information for a client.
        
        Args:
            conn: Database connection
            client_id: Client ID
        
        Returns:
            Emergency contact data as dictionary
        """
        query = f"""
            SELECT emergency_contact
            FROM {self.table_name}
            WHERE client_id = $1
        """
        
        try:
            result = await conn.fetchval(query, client_id)
            return result  # This will be a JSONB object
        except Exception as e:
            logger.error(f"Error getting client emergency contacts: {e}")
            raise
    
    async def update_client_address(
        self,
        conn: asyncpg.Connection,
        client_id: int,
        address_data: Dict[str, Any]
    ) -> Optional[asyncpg.Record]:
        """
        Update client address information.
        
        Args:
            conn: Database connection
            client_id: Client ID
            address_data: Address information
        
        Returns:
            Updated client record
        """
        query = f"""
            UPDATE {self.table_name}
            SET address = $1, updated_at = $2
            WHERE client_id = $3
            RETURNING *
        """
        
        try:
            result = await conn.fetchrow(
                query, address_data, now_est(), client_id
            )
            if result:
                logger.info(f"Updated address for client {client_id}")
            return result
        except Exception as e:
            logger.error(f"Error updating client address: {e}")
            raise
    
    async def get_dependants_by_age_range(
        self,
        conn: asyncpg.Connection,
        min_age: Optional[int] = None,
        max_age: Optional[int] = None
    ) -> List[asyncpg.Record]:
        """
        Get dependants within a specific age range.
        
        Args:
            conn: Database connection
            min_age: Minimum age in years
            max_age: Maximum age in years
        
        Returns:
            List of dependant records with age calculations
        """
        query = """
            SELECT 
                cd.*,
                EXTRACT(YEAR FROM AGE(cd.birth_date)) as age_years,
                cp_primary.first_name as primary_parent_first_name,
                cp_primary.last_name as primary_parent_last_name
            FROM client_dependants cd
            JOIN client_profiles cp_primary ON cd.primary_client_id = cp_primary.client_id
            WHERE 1=1
        """
        
        params = []
        param_count = 0
        
        if min_age is not None:
            param_count += 1
            query += f" AND EXTRACT(YEAR FROM AGE(cd.birth_date)) >= ${param_count}"
            params.append(min_age)
        
        if max_age is not None:
            param_count += 1
            query += f" AND EXTRACT(YEAR FROM AGE(cd.birth_date)) <= ${param_count}"
            params.append(max_age)
        
        query += " ORDER BY cd.birth_date DESC"
        
        try:
            results = await conn.fetch(query, *params)
            return results
        except Exception as e:
            logger.error(f"Error getting dependants by age range: {e}")
            raise