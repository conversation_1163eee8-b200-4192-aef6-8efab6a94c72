"""Tests for main application setup and root endpoints."""

import pytest
from app.config.settings import settings


def test_root_endpoint(test_client):
    """Test root endpoint returns API information."""
    response = test_client.get("/")
    if response.status_code != 200:
        print(f"Response status: {response.status_code}")
        print(f"Response body: {response.text}")
    assert response.status_code == 200
    
    data = response.json()
    assert data["name"] == settings.PROJECT_NAME
    assert data["version"] == settings.VERSION
    assert data["status"] == "operational"
    assert "api" in data
    assert data["api"]["v1"] == settings.API_V1_STR
    assert data["api"]["health"] == "/health"
    
    # Check contact information
    assert "contact" in data
    assert data["contact"]["support"] == settings.SUPPORT_EMAIL
    assert data["contact"]["billing"] == settings.BILLING_EMAIL
    assert data["contact"]["general"] == settings.GENERAL_EMAIL


def test_health_endpoint(test_client):
    """Test health check endpoint."""
    response = test_client.get("/health")
    assert response.status_code == 200
    
    data = response.json()
    assert data["status"] == "healthy"
    assert data["service"] == "tutoraide-api"


def test_api_v1_docs_redirect(test_client):
    """Test API v1 documentation endpoint exists."""
    # In production, docs are disabled, so we check if the API base exists
    response = test_client.get(f"{settings.API_V1_STR}/")
    # Should get 404 because there's no root handler for API v1
    assert response.status_code == 404


def test_favicon_endpoint(test_client):
    """Test favicon endpoint."""
    response = test_client.get("/favicon_tutoraide.png")
    assert response.status_code == 200
    # Will either be file response or JSON with detail message


def test_logo_endpoint(test_client):
    """Test logo endpoint."""
    response = test_client.get("/logo_tutoraide.jpg")
    assert response.status_code == 200
    # Will either be file response or JSON with detail message


def test_cors_headers(test_client):
    """Test CORS headers are properly set."""
    # Test a regular GET request with Origin header
    response = test_client.get(
        "/health",
        headers={
            "Origin": "http://localhost:3000"
        }
    )
    assert response.status_code == 200
    # Check if CORS headers are present in response
    assert "access-control-allow-origin" in response.headers or "Access-Control-Allow-Origin" in response.headers


def test_security_headers(test_client):
    """Test security headers are properly set."""
    response = test_client.get("/health")
    assert response.status_code == 200
    
    # Check security headers
    assert "x-content-type-options" in response.headers
    assert response.headers["x-content-type-options"] == "nosniff"
    assert "x-frame-options" in response.headers
    assert response.headers["x-frame-options"] == "DENY"


def test_root_endpoint_html(test_client):
    """Test root endpoint returns HTML when Accept header includes text/html."""
    response = test_client.get(
        "/",
        headers={"Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"}
    )
    assert response.status_code == 200
    assert "text/html" in response.headers.get("content-type", "")
    assert "<title>TutorAide API</title>" in response.text
    assert "Comprehensive Tutoring Management Platform" in response.text