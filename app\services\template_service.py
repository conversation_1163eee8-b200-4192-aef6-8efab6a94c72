"""
Enhanced Template Service for Email and SMS Templates with Multi-Language Support.
Handles template loading, rendering, and management for Quebec French and English.
"""

import os
import re
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
from jinja2 import Environment, FileSystemLoader, Template, TemplateNotFound
from dataclasses import dataclass
from enum import Enum

from app.config.settings import get_settings
from app.locales.translation_service import get_translation_service
from app.locales.constants import SUPPORTED_LANGUAGES, DEFAULT_LANGUAGE


class TemplateType(Enum):
    """Template types supported by the system."""
    EMAIL = "email"
    SMS = "sms"
    PUSH_NOTIFICATION = "push"


class NotificationCategory(Enum):
    """Categories of notifications for template organization."""
    AUTHENTICATION = "auth"
    APPOINTMENTS = "appointments"
    BILLING = "billing"
    SYSTEM = "system"
    MARKETING = "marketing"
    SUPPORT = "support"


@dataclass
class TemplateInfo:
    """Information about a template."""
    name: str
    category: NotificationCategory
    type: TemplateType
    languages: List[str]
    variables: List[str]
    description: str
    subject_template: Optional[str] = None  # For email templates


@dataclass
class RenderedTemplate:
    """Rendered template with content and metadata."""
    content: str
    subject: Optional[str] = None
    language: str = DEFAULT_LANGUAGE
    template_name: str = ""
    variables_used: List[str] = None


class TemplateService:
    """
    Service for managing and rendering email and SMS templates.
    Supports multi-language templates with Quebec French localization.
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.translation_service = get_translation_service()
        
        # Setup template directories
        self.template_base_path = Path(self.settings.BASE_DIR) / "app" / "templates"
        self.email_template_path = self.template_base_path / "emails"
        self.sms_template_path = self.template_base_path / "sms"
        
        # Setup Jinja2 environments
        self.email_env = Environment(
            loader=FileSystemLoader(str(self.email_template_path)),
            autoescape=True,
            trim_blocks=True,
            lstrip_blocks=True
        )
        
        self.sms_env = Environment(
            loader=FileSystemLoader(str(self.sms_template_path)),
            autoescape=False,  # SMS doesn't need HTML escaping
            trim_blocks=True,
            lstrip_blocks=True
        )
        
        # Template registry
        self._template_registry = self._build_template_registry()
    
    def _build_template_registry(self) -> Dict[str, TemplateInfo]:
        """Build registry of available templates."""
        registry = {}
        
        # Email templates
        email_templates = {
            "email_verification": TemplateInfo(
                name="email_verification",
                category=NotificationCategory.AUTHENTICATION,
                type=TemplateType.EMAIL,
                languages=["en", "fr"],
                variables=["company_name", "verify_link", "support_email", "user_email", "year"],
                description="Email verification template for new user accounts",
                subject_template="Verify Your Email - {{ company_name }}"
            ),
            "password_reset": TemplateInfo(
                name="password_reset",
                category=NotificationCategory.AUTHENTICATION,
                type=TemplateType.EMAIL,
                languages=["en", "fr"],
                variables=["company_name", "reset_link", "support_email", "user_email", "year", "expiry_hours"],
                description="Password reset email template",
                subject_template="Reset Your Password - {{ company_name }}"
            ),
            "password_changed": TemplateInfo(
                name="password_changed",
                category=NotificationCategory.AUTHENTICATION,
                type=TemplateType.EMAIL,
                languages=["en", "fr"],
                variables=["company_name", "user_name", "support_email", "user_email", "year", "change_time"],
                description="Password change confirmation email",
                subject_template="Password Changed - {{ company_name }}"
            ),
            "appointment_reminder": TemplateInfo(
                name="appointment_reminder",
                category=NotificationCategory.APPOINTMENTS,
                type=TemplateType.EMAIL,
                languages=["en", "fr"],
                variables=[
                    "company_name", "appointment_date", "appointment_time", "duration",
                    "tutor_name", "subject", "location", "confirm_link", "reschedule_link",
                    "cancel_link", "support_email", "support_phone", "user_email", "year"
                ],
                description="24-hour appointment reminder email",
                subject_template="Appointment Reminder - {{ appointment_date }} at {{ appointment_time }}"
            ),
            "tutor_invitation": TemplateInfo(
                name="tutor_invitation",
                category=NotificationCategory.SYSTEM,
                type=TemplateType.EMAIL,
                languages=["en", "fr"],
                variables=[
                    "company_name", "tutor_name", "invitation_link", "expiry_days",
                    "recruitment_email", "support_phone", "tutor_email", "support_email", "year"
                ],
                description="Tutor invitation email template",
                subject_template="Join Our Tutoring Team - {{ company_name }}"
            )
        }
        
        # SMS templates
        sms_templates = {
            "appointment_reminder": TemplateInfo(
                name="appointment_reminder",
                category=NotificationCategory.APPOINTMENTS,
                type=TemplateType.SMS,
                languages=["en", "fr"],
                variables=[
                    "tutor_name", "appointment_time", "location", "subject", "duration",
                    "confirm_link", "reschedule_link", "support_phone", "company_name"
                ],
                description="24-hour appointment reminder SMS"
            ),
            "appointment_confirmation": TemplateInfo(
                name="appointment_confirmation",
                category=NotificationCategory.APPOINTMENTS,
                type=TemplateType.SMS,
                languages=["en", "fr"],
                variables=[
                    "appointment_date", "appointment_time", "tutor_name", "subject",
                    "location", "session_link", "calendar_link", "company_name"
                ],
                description="Appointment confirmation SMS"
            ),
            "tutor_invitation": TemplateInfo(
                name="tutor_invitation",
                category=NotificationCategory.SYSTEM,
                type=TemplateType.SMS,
                languages=["en", "fr"],
                variables=[
                    "company_name", "subjects", "invitation_link", "expiry_days",
                    "support_phone", "company_website"
                ],
                description="Tutor invitation SMS"
            )
        }
        
        # Combine all templates
        registry.update(email_templates)
        registry.update(sms_templates)
        
        return registry
    
    def get_available_templates(
        self, 
        template_type: Optional[TemplateType] = None,
        category: Optional[NotificationCategory] = None,
        language: str = DEFAULT_LANGUAGE
    ) -> List[TemplateInfo]:
        """Get list of available templates with optional filtering."""
        templates = []
        
        for template_info in self._template_registry.values():
            # Filter by type
            if template_type and template_info.type != template_type:
                continue
            
            # Filter by category
            if category and template_info.category != category:
                continue
            
            # Filter by language availability
            if language not in template_info.languages:
                continue
            
            templates.append(template_info)
        
        return sorted(templates, key=lambda t: (t.category.value, t.name))
    
    def get_template_info(self, template_name: str) -> Optional[TemplateInfo]:
        """Get information about a specific template."""
        return self._template_registry.get(template_name)
    
    def render_email_template(
        self,
        template_name: str,
        variables: Dict[str, Any],
        language: str = DEFAULT_LANGUAGE,
        fallback_to_default: bool = True
    ) -> RenderedTemplate:
        """
        Render an email template with the provided variables.
        
        Args:
            template_name: Name of the template to render
            variables: Variables to substitute in the template
            language: Language code for the template
            fallback_to_default: Whether to fallback to default language if template not found
            
        Returns:
            RenderedTemplate with rendered content and subject
        """
        template_info = self._template_registry.get(template_name)
        if not template_info or template_info.type != TemplateType.EMAIL:
            raise ValueError(f"Email template '{template_name}' not found")
        
        # Try to load template in requested language
        template_filename = f"{template_name}_{language}.html"
        
        try:
            template = self.email_env.get_template(template_filename)
            used_language = language
        except TemplateNotFound:
            if fallback_to_default and language != DEFAULT_LANGUAGE:
                template_filename = f"{template_name}_{DEFAULT_LANGUAGE}.html"
                template = self.email_env.get_template(template_filename)
                used_language = DEFAULT_LANGUAGE
            else:
                raise TemplateNotFound(f"Template {template_filename} not found")
        
        # Enhance variables with localized content
        enhanced_variables = self._enhance_variables_with_localization(
            variables, used_language, template_info
        )
        
        # Render template content
        content = template.render(**enhanced_variables)
        
        # Render subject if available
        subject = None
        if template_info.subject_template:
            subject_template = Template(template_info.subject_template)
            subject = subject_template.render(**enhanced_variables)
        
        return RenderedTemplate(
            content=content,
            subject=subject,
            language=used_language,
            template_name=template_name,
            variables_used=list(enhanced_variables.keys())
        )
    
    def render_sms_template(
        self,
        template_name: str,
        variables: Dict[str, Any],
        language: str = DEFAULT_LANGUAGE,
        fallback_to_default: bool = True
    ) -> RenderedTemplate:
        """
        Render an SMS template with the provided variables.
        
        Args:
            template_name: Name of the template to render
            variables: Variables to substitute in the template
            language: Language code for the template
            fallback_to_default: Whether to fallback to default language if template not found
            
        Returns:
            RenderedTemplate with rendered SMS content
        """
        template_info = self._template_registry.get(template_name)
        if not template_info or template_info.type != TemplateType.SMS:
            raise ValueError(f"SMS template '{template_name}' not found")
        
        # Try to load template in requested language
        template_filename = f"{template_name}_{language}.txt"
        
        try:
            template = self.sms_env.get_template(template_filename)
            used_language = language
        except TemplateNotFound:
            if fallback_to_default and language != DEFAULT_LANGUAGE:
                template_filename = f"{template_name}_{DEFAULT_LANGUAGE}.txt"
                template = self.sms_env.get_template(template_filename)
                used_language = DEFAULT_LANGUAGE
            else:
                raise TemplateNotFound(f"Template {template_filename} not found")
        
        # Enhance variables with localized content
        enhanced_variables = self._enhance_variables_with_localization(
            variables, used_language, template_info
        )
        
        # Render template content
        content = template.render(**enhanced_variables)
        
        # Clean up SMS content (remove extra whitespace, normalize line breaks)
        content = self._clean_sms_content(content)
        
        return RenderedTemplate(
            content=content,
            language=used_language,
            template_name=template_name,
            variables_used=list(enhanced_variables.keys())
        )
    
    def _enhance_variables_with_localization(
        self,
        variables: Dict[str, Any],
        language: str,
        template_info: TemplateInfo
    ) -> Dict[str, Any]:
        """Enhance template variables with localized content."""
        enhanced = variables.copy()
        
        # Add standard variables if not provided
        if "company_name" not in enhanced:
            enhanced["company_name"] = self.translation_service.get_translation(
                "common.company_name", language
            ) or "TutorAide"
        
        if "support_email" not in enhanced:
            enhanced["support_email"] = "<EMAIL>"
        
        if "support_phone" not in enhanced:
            # Format phone number according to Quebec standards
            phone = "(*************"  # This would come from settings
            if language == "fr":
                enhanced["support_phone"] = phone
            else:
                enhanced["support_phone"] = phone
        
        if "year" not in enhanced:
            from datetime import datetime
            enhanced["year"] = datetime.now().year
        
        # Add localized common terms
        enhanced.update({
            "locale": language,
            "is_french": language == "fr",
            "is_quebec_french": language == "fr"  # All French in our system is Quebec French
        })
        
        return enhanced
    
    def _clean_sms_content(self, content: str) -> str:
        """Clean SMS content by removing extra whitespace and normalizing."""
        # Remove extra whitespace
        content = re.sub(r'\n\s*\n', '\n\n', content)  # Max 2 consecutive newlines
        content = re.sub(r' +', ' ', content)  # Multiple spaces to single space
        content = content.strip()
        
        # Ensure SMS doesn't exceed reasonable length (recommend under 160 chars per segment)
        if len(content) > 480:  # 3 SMS segments
            # Log warning about long SMS
            pass
        
        return content
    
    def validate_template_variables(
        self,
        template_name: str,
        variables: Dict[str, Any]
    ) -> Dict[str, List[str]]:
        """
        Validate that all required variables are provided for a template.
        
        Returns:
            Dict with 'missing' and 'extra' keys containing lists of variable names
        """
        template_info = self._template_registry.get(template_name)
        if not template_info:
            raise ValueError(f"Template '{template_name}' not found")
        
        required_vars = set(template_info.variables)
        provided_vars = set(variables.keys())
        
        missing = list(required_vars - provided_vars)
        extra = list(provided_vars - required_vars)
        
        return {
            "missing": missing,
            "extra": extra
        }
    
    def render_template_preview(
        self,
        template_name: str,
        language: str = DEFAULT_LANGUAGE
    ) -> RenderedTemplate:
        """
        Render a template with sample data for preview purposes.
        """
        template_info = self._template_registry.get(template_name)
        if not template_info:
            raise ValueError(f"Template '{template_name}' not found")
        
        # Generate sample variables
        sample_variables = self._generate_sample_variables(template_info)
        
        # Render based on template type
        if template_info.type == TemplateType.EMAIL:
            return self.render_email_template(template_name, sample_variables, language)
        elif template_info.type == TemplateType.SMS:
            return self.render_sms_template(template_name, sample_variables, language)
        else:
            raise ValueError(f"Unsupported template type: {template_info.type}")
    
    def _generate_sample_variables(self, template_info: TemplateInfo) -> Dict[str, Any]:
        """Generate sample variables for template preview."""
        from datetime import datetime, timedelta
        
        sample_data = {
            "company_name": "TutorAide",
            "user_name": "Jean Dupont" if template_info.languages[0] == "fr" else "John Doe",
            "tutor_name": "Marie Tremblay" if template_info.languages[0] == "fr" else "Sarah Johnson",
            "user_email": "<EMAIL>" if template_info.languages[0] == "fr" else "<EMAIL>",
            "tutor_email": "<EMAIL>" if template_info.languages[0] == "fr" else "<EMAIL>",
            "support_email": "<EMAIL>",
            "recruitment_email": "<EMAIL>",
            "support_phone": "(*************",
            "verify_link": "https://app.tutoraide.ca/verify?token=sample123",
            "reset_link": "https://app.tutoraide.ca/reset?token=sample456",
            "invitation_link": "https://app.tutoraide.ca/tutor/invite?token=sample789",
            "confirm_link": "https://app.tutoraide.ca/confirm?id=123",
            "reschedule_link": "https://app.tutoraide.ca/reschedule?id=123",
            "cancel_link": "https://app.tutoraide.ca/cancel?id=123",
            "session_link": "https://app.tutoraide.ca/session/123",
            "calendar_link": "https://app.tutoraide.ca/calendar/add?id=123",
            "appointment_date": "15 juin 2024" if template_info.languages[0] == "fr" else "June 15, 2024",
            "appointment_time": "14h00" if template_info.languages[0] == "fr" else "2:00 PM",
            "duration": "60",
            "subject": "Mathématiques" if template_info.languages[0] == "fr" else "Mathematics",
            "subjects": "mathématiques, français" if template_info.languages[0] == "fr" else "math, science",
            "location": "Bibliothèque centrale" if template_info.languages[0] == "fr" else "Central Library",
            "expiry_days": "7",
            "expiry_hours": "24",
            "change_time": datetime.now().strftime("%Y-%m-%d %H:%M"),
            "year": datetime.now().year,
            "company_website": "https://www.tutoraide.ca"
        }
        
        # Return only variables needed by this template
        return {var: sample_data.get(var, f"[{var}]") for var in template_info.variables}


# Global template service instance
_template_service = None


def get_template_service() -> TemplateService:
    """Get the global template service instance."""
    global _template_service
    if _template_service is None:
        _template_service = TemplateService()
    return _template_service