import React from 'react';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { MessageSquare, Users, FileText, Radio } from 'lucide-react';

interface Tab {
  id: string;
  label: string;
  path: string;
  icon: React.ReactNode;
}

const MessagesLayout: React.FC = () => {
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();

  const tabs: Tab[] = [
    { 
      id: 'sms', 
      label: 'SMS Threads', 
      path: '/messages/sms',
      icon: <MessageSquare className="w-4 h-4" />
    },
    { 
      id: 'chat', 
      label: 'In-App Chat', 
      path: '/messages/chat',
      icon: <Users className="w-4 h-4" />
    },
    { 
      id: 'templates', 
      label: 'Templates', 
      path: '/messages/templates',
      icon: <FileText className="w-4 h-4" />
    },
    { 
      id: 'broadcasts', 
      label: 'Broadcasts', 
      path: '/messages/broadcasts',
      icon: <Radio className="w-4 h-4" />
    },
  ];

  const currentTab = tabs.find(tab => location.pathname === tab.path) || tabs[0];

  return (
    <div className="h-full flex flex-col">
      {/* Tab Navigation */}
      <div className="bg-white shadow-soft px-6 py-4">
        <div className="flex items-center space-x-8 overflow-x-auto">
          {tabs.map(tab => (
            <button
              key={tab.id}
              onClick={() => navigate(tab.path)}
              className={`
                flex items-center gap-2 px-4 py-2 rounded-soft text-sm font-medium transition-all
                ${currentTab.id === tab.id
                  ? 'bg-accent-red text-white'
                  : 'text-text-secondary hover:bg-background-secondary'
                }
              `}
            >
              {tab.icon}
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-auto">
        <Outlet />
      </div>
    </div>
  );
};

export default MessagesLayout;