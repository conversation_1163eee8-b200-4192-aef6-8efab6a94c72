"""
Tests for analytics service.
"""

import pytest
from datetime import date, datetime, timedelta
from decimal import Decimal
from unittest.mock import Mock, AsyncMock, patch, ANY
import asyncpg

from app.services.analytics_service import AnalyticsService
from app.models.analytics_models import (
    TutorPerformanceMetrics, StudentLearningGoal, StudentProgressMetrics,
    PlatformAnalytics, SessionFeedback, PeriodType, GoalStatus, FeedbackRole,
    TutorDashboardData, StudentDashboardData, PlatformDashboardData,
    SessionFeedbackCreate, MetricType
)
from app.core.exceptions import ResourceNotFoundError, ValidationError, BusinessLogicError
from app.config.database import DatabaseManager


@pytest.fixture
def mock_db_manager():
    """Create mock database manager."""
    manager = Mock(spec=DatabaseManager)
    # Create a proper async context manager mock
    async_cm = AsyncMock()
    async_cm.__aenter__ = AsyncMock(return_value=AsyncMock())
    async_cm.__aexit__ = AsyncMock(return_value=None)
    manager.acquire = Mock(return_value=async_cm)
    return manager


@pytest.fixture
def analytics_service(mock_db_manager):
    """Create analytics service with mocked dependencies."""
    service = AnalyticsService(mock_db_manager)
    
    # Mock repositories
    service.tutor_perf_repo = Mock()
    service.student_progress_repo = Mock()
    service.feedback_repo = Mock()
    service.platform_repo = Mock()
    service.snapshot_repo = Mock()
    service.appointment_repo = Mock()
    service.payment_repo = Mock()
    service.user_repo = Mock()
    
    return service


class TestAnalyticsService:
    """Test AnalyticsService methods."""
    
    @pytest.mark.asyncio
    async def test_get_period_dates(self, analytics_service):
        """Test period date calculation."""
        today = date.today()
        
        # Test daily
        start, end = analytics_service._get_period_dates(PeriodType.DAILY)
        assert start == today
        assert end == today
        
        # Test weekly (Monday to Sunday)
        start, end = analytics_service._get_period_dates(PeriodType.WEEKLY)
        assert start.weekday() == 0  # Monday
        assert (end - start).days == 6
        
        # Test monthly
        start, end = analytics_service._get_period_dates(PeriodType.MONTHLY)
        assert start.day == 1
        assert start.month == today.month
        
        # Test quarterly
        start, end = analytics_service._get_period_dates(PeriodType.QUARTERLY)
        quarter = (today.month - 1) // 3
        assert start.month == quarter * 3 + 1
        assert start.day == 1
        
        # Test yearly
        start, end = analytics_service._get_period_dates(PeriodType.YEARLY)
        assert start == date(today.year, 1, 1)
        assert end == date(today.year, 12, 31)
    
    @pytest.mark.asyncio
    async def test_calculate_tutor_metrics(self, analytics_service, mock_db_manager):
        """Test tutor metrics calculation."""
        tutor_id = 1
        period_type = PeriodType.MONTHLY
        
        # Mock repository methods
        mock_metrics = TutorPerformanceMetrics(
            metric_id=1,
            tutor_id=tutor_id,
            period_start=date.today().replace(day=1),
            period_end=date.today(),
            period_type=period_type,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        analytics_service.tutor_perf_repo.get_or_create_metrics = AsyncMock(
            return_value=mock_metrics
        )
        analytics_service.tutor_perf_repo.update_session_metrics = AsyncMock(
            return_value=mock_metrics
        )
        analytics_service.tutor_perf_repo.update_rating_metrics = AsyncMock(
            return_value=mock_metrics
        )
        analytics_service.tutor_perf_repo.update_financial_metrics = AsyncMock(
            return_value=mock_metrics
        )
        
        # Mock helper methods
        analytics_service._calculate_session_metrics = AsyncMock(
            return_value={
                'total_sessions': 10,
                'completed_sessions': 8,
                'cancelled_sessions': 1,
                'no_show_sessions': 1,
                'total_hours': Decimal("15.0"),
                'unique_students': 5,
                'returning_students': 3,
                'new_students': 2,
                'subject_breakdown': {'Math': {'sessions': 5, 'hours': 7.5}}
            }
        )
        analytics_service._calculate_rating_metrics = AsyncMock(
            return_value={
                'average_rating': Decimal("4.5"),
                'total_ratings': 8,
                'five_star_count': 5
            }
        )
        analytics_service._calculate_financial_metrics = AsyncMock(
            return_value={
                'total_revenue': Decimal("800.00"),
                'total_hours': Decimal("15.0")
            }
        )
        analytics_service._save_metrics_snapshot = AsyncMock()
        
        # Execute
        result = await analytics_service.calculate_tutor_metrics(
            tutor_id, period_type
        )
        
        # Verify
        assert result == mock_metrics
        assert analytics_service.tutor_perf_repo.get_or_create_metrics.called
        assert analytics_service._calculate_session_metrics.called
        assert analytics_service._calculate_rating_metrics.called
        assert analytics_service._calculate_financial_metrics.called
    
    @pytest.mark.asyncio
    async def test_get_tutor_dashboard(self, analytics_service, mock_db_manager):
        """Test getting tutor dashboard data."""
        tutor_id = 1
        
        # Mock calculate_tutor_metrics
        mock_metrics = TutorPerformanceMetrics(
            metric_id=1,
            tutor_id=tutor_id,
            period_start=date.today().replace(day=1),
            period_end=date.today(),
            period_type=PeriodType.MONTHLY,
            total_sessions=10,
            completed_sessions=8,
            average_rating=Decimal("4.5"),
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        analytics_service.calculate_tutor_metrics = AsyncMock(
            return_value=mock_metrics
        )
        
        # Mock other methods
        analytics_service.feedback_repo.get_feedback_for_user = AsyncMock(
            return_value=[]
        )
        analytics_service._get_subject_performance = AsyncMock(
            return_value={'Math': {'total_sessions': 5, 'avg_rating': 4.5}}
        )
        analytics_service._get_tutor_student_progress = AsyncMock(
            return_value=[]
        )
        analytics_service._get_earnings_trend = AsyncMock(
            return_value=[{'month': '2024-01', 'total_earnings': 800.0}]
        )
        analytics_service._count_upcoming_sessions = AsyncMock(return_value=3)
        analytics_service._count_pending_feedback = AsyncMock(return_value=2)
        
        # Execute
        result = await analytics_service.get_tutor_dashboard(tutor_id)
        
        # Verify
        assert isinstance(result, TutorDashboardData)
        assert result.performance_metrics == mock_metrics
        assert result.upcoming_sessions == 3
        assert result.pending_feedback == 2
    
    @pytest.mark.asyncio
    async def test_create_learning_goal(self, analytics_service, mock_db_manager):
        """Test creating a learning goal."""
        student_id = 1
        created_by = 2
        goal_data = {
            'subject_area': 'Math',
            'goal_title': 'Master Algebra',
            'goal_description': 'Focus on linear equations',
            'target_date': date.today() + timedelta(days=30)
        }
        
        expected_goal = StudentLearningGoal(
            goal_id=1,
            student_id=student_id,
            subject_area='Math',
            goal_title='Master Algebra',
            goal_description='Focus on linear equations',
            target_date=goal_data['target_date'],
            created_by=created_by,
            status=GoalStatus.ACTIVE,
            progress_percentage=0,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        analytics_service.student_progress_repo.create_learning_goal = AsyncMock(
            return_value=expected_goal
        )
        
        # Execute
        result = await analytics_service.create_learning_goal(
            student_id, goal_data, created_by
        )
        
        # Verify
        assert result == expected_goal
        analytics_service.student_progress_repo.create_learning_goal.assert_called_once()
        call_args = analytics_service.student_progress_repo.create_learning_goal.call_args[0][1]
        assert call_args['student_id'] == student_id
        assert call_args['created_by'] == created_by
        assert call_args['subject_area'] == 'Math'
    
    @pytest.mark.asyncio
    async def test_update_goal_progress(self, analytics_service, mock_db_manager):
        """Test updating goal progress."""
        goal_id = 1
        progress_percentage = 75
        
        updated_goal = StudentLearningGoal(
            goal_id=goal_id,
            student_id=1,
            subject_area='Math',
            goal_title='Master Algebra',
            status=GoalStatus.ACTIVE,
            progress_percentage=progress_percentage,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        analytics_service.student_progress_repo.update_goal_progress = AsyncMock(
            return_value=updated_goal
        )
        
        # Test update without marking complete
        result = await analytics_service.update_goal_progress(
            goal_id, progress_percentage, mark_complete=False
        )
        
        assert result == updated_goal
        analytics_service.student_progress_repo.update_goal_progress.assert_called_with(
            ANY,  # Connection object
            goal_id,
            progress_percentage,
            None
        )
        
        # Test update with marking complete
        updated_goal.status = GoalStatus.COMPLETED
        await analytics_service.update_goal_progress(
            goal_id, 100, mark_complete=True
        )
        
        analytics_service.student_progress_repo.update_goal_progress.assert_called_with(
            ANY,  # Connection object
            goal_id,
            100,
            GoalStatus.COMPLETED
        )
    
    @pytest.mark.asyncio
    async def test_submit_session_feedback(self, analytics_service, mock_db_manager):
        """Test submitting session feedback."""
        appointment_id = 1
        user_id = 2
        feedback_data = SessionFeedbackCreate(
            appointment_id=appointment_id,
            overall_rating=5,
            punctuality_rating=5,
            preparation_rating=4,
            communication_rating=5,
            feedback_text="Great session!",
            improvement_areas=["More practice problems"],
            positive_highlights=["Clear explanations"]
        )
        
        # Mock appointment
        mock_appointment = {
            'appointment_id': appointment_id,
            'status': 'completed',
            'tutor_id': 3,
            'client_id': 4
        }
        
        analytics_service.appointment_repo.find_by_id = AsyncMock(
            return_value=mock_appointment
        )
        
        # Mock feedback role determination
        analytics_service._determine_feedback_role = AsyncMock(
            return_value=(FeedbackRole.STUDENT_TO_TUTOR, 3)
        )
        
        # Mock feedback creation
        created_feedback = SessionFeedback(
            feedback_id=1,
            appointment_id=appointment_id,
            given_by=user_id,
            given_to=3,
            role_type=FeedbackRole.STUDENT_TO_TUTOR,
            overall_rating=5,
            feedback_text="Great session!",
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        analytics_service.feedback_repo.create_feedback = AsyncMock(
            return_value=created_feedback
        )
        analytics_service._update_tutor_rating_metrics = AsyncMock()
        
        # Execute
        result = await analytics_service.submit_session_feedback(
            appointment_id, user_id, feedback_data
        )
        
        # Verify
        assert result == created_feedback
        analytics_service.appointment_repo.find_by_id.assert_called_once()
        analytics_service._determine_feedback_role.assert_called_once()
        analytics_service.feedback_repo.create_feedback.assert_called_once()
        analytics_service._update_tutor_rating_metrics.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_submit_feedback_incomplete_session(self, analytics_service, mock_db_manager):
        """Test submitting feedback for incomplete session."""
        appointment_id = 1
        user_id = 2
        feedback_data = SessionFeedbackCreate(
            appointment_id=appointment_id,
            overall_rating=5
        )
        
        # Mock appointment with non-completed status
        mock_appointment = {
            'appointment_id': appointment_id,
            'status': 'scheduled',
            'tutor_id': 3,
            'client_id': 4
        }
        
        analytics_service.appointment_repo.find_by_id = AsyncMock(
            return_value=mock_appointment
        )
        
        # Execute and expect validation error
        with pytest.raises(ValidationError, match="completed sessions"):
            await analytics_service.submit_session_feedback(
                appointment_id, user_id, feedback_data
            )
    
    @pytest.mark.asyncio
    async def test_calculate_daily_platform_metrics(self, analytics_service, mock_db_manager):
        """Test platform metrics calculation."""
        metric_date = date.today() - timedelta(days=1)
        
        # Mock helper methods
        analytics_service._calculate_user_activity_metrics = AsyncMock(
            return_value={
                'active_clients': 50,
                'active_tutors': 20,
                'active_students': 45,
                'new_registrations': 5
            }
        )
        analytics_service._calculate_session_metrics_platform = AsyncMock(
            return_value={
                'total_bookings': 30,
                'completed_sessions': 25,
                'booking_conversion_rate': Decimal("83.33"),
                'average_session_duration': Decimal("1.5")
            }
        )
        analytics_service._calculate_financial_metrics_platform = AsyncMock(
            return_value={
                'daily_revenue': Decimal("2500.00"),
                'average_session_value': Decimal("100.00")
            }
        )
        analytics_service._calculate_distribution_metrics = AsyncMock(
            return_value={
                'subject_distribution': {'Math': 10, 'Science': 8},
                'peak_hours': {'14': 5, '15': 8},
                'time_slot_distribution': {'morning': 8, 'afternoon': 15},
                'geographic_distribution': {'H3H': 10, 'H2X': 8}
            }
        )
        
        expected_analytics = PlatformAnalytics(
            analytics_id=1,
            metric_date=metric_date,
            metric_type=MetricType.USER_ACTIVITY,
            active_clients=50,
            active_tutors=20,
            active_students=45,
            new_registrations=5,
            total_bookings=30,
            completed_sessions=25,
            booking_conversion_rate=Decimal("83.33"),
            average_session_duration=Decimal("1.5"),
            daily_revenue=Decimal("2500.00"),
            average_session_value=Decimal("100.00")
        )
        
        analytics_service.platform_repo.record_daily_metrics = AsyncMock(
            return_value=expected_analytics
        )
        
        # Execute
        result = await analytics_service.calculate_daily_platform_metrics(metric_date)
        
        # Verify
        assert result == expected_analytics
        analytics_service._calculate_user_activity_metrics.assert_called_once()
        analytics_service._calculate_session_metrics_platform.assert_called_once()
        analytics_service._calculate_financial_metrics_platform.assert_called_once()
        analytics_service._calculate_distribution_metrics.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_platform_dashboard(self, analytics_service, mock_db_manager):
        """Test getting platform dashboard."""
        days = 30
        
        # Mock platform metrics
        mock_metrics = [
            PlatformAnalytics(
                analytics_id=i,
                metric_date=date.today() - timedelta(days=i),
                metric_type=MetricType.USER_ACTIVITY,
                active_clients=50 - i,
                active_tutors=20,
                active_students=45 - i,
                new_registrations=5,
                daily_revenue=Decimal("2500.00") - Decimal(str(i * 100))
            )
            for i in range(3)
        ]
        
        analytics_service.platform_repo.get_metrics_range = AsyncMock(
            return_value=mock_metrics
        )
        analytics_service.get_tutor_leaderboard = AsyncMock(return_value=[])
        
        # Mock aggregation methods
        analytics_service._aggregate_revenue_trends = Mock(
            return_value={'daily_trends': [], 'total_revenue': 7200.0}
        )
        analytics_service._aggregate_user_growth = Mock(
            return_value={'new_users_trend': [], 'total_new_users': 15}
        )
        analytics_service._aggregate_geographic_data = Mock(
            return_value={'top_areas': [], 'total_areas': 5}
        )
        analytics_service._aggregate_subject_popularity = Mock(
            return_value=[{'subject': 'Math', 'session_count': 30}]
        )
        analytics_service._aggregate_peak_usage = Mock(
            return_value=[{'hour': 15, 'session_count': 25}]
        )
        
        # Execute
        result = await analytics_service.get_platform_dashboard(days)
        
        # Verify
        assert isinstance(result, PlatformDashboardData)
        assert result.platform_metrics == mock_metrics[-1]  # Last item in list
        analytics_service.platform_repo.get_metrics_range.assert_called_once()
        analytics_service._aggregate_revenue_trends.assert_called_once()
    
    def test_aggregate_revenue_trends(self, analytics_service):
        """Test revenue trend aggregation."""
        metrics_list = [
            PlatformAnalytics(
                analytics_id=1,
                metric_date=date.today() - timedelta(days=2),
                metric_type=MetricType.FINANCIAL_METRICS,
                daily_revenue=Decimal("2000.00"),
                average_session_value=Decimal("100.00")
            ),
            PlatformAnalytics(
                analytics_id=2,
                metric_date=date.today() - timedelta(days=1),
                metric_type=MetricType.FINANCIAL_METRICS,
                daily_revenue=Decimal("2500.00"),
                average_session_value=Decimal("125.00")
            ),
            PlatformAnalytics(
                analytics_id=3,
                metric_date=date.today(),
                metric_type=MetricType.FINANCIAL_METRICS,
                daily_revenue=Decimal("3000.00"),
                average_session_value=Decimal("150.00")
            )
        ]
        
        result = analytics_service._aggregate_revenue_trends(metrics_list)
        
        assert result['total_revenue'] == 7500.0
        assert result['average_daily'] == 2500.0
        assert result['growth_percentage'] == 20.0  # (3000 - 2500) / 2500 * 100
        assert len(result['daily_trends']) == 3
    
    def test_aggregate_subject_popularity(self, analytics_service):
        """Test subject popularity aggregation."""
        metrics_list = [
            PlatformAnalytics(
                analytics_id=1,
                metric_date=date.today(),
                metric_type=MetricType.SESSION_METRICS,
                subject_distribution={'Math': 10, 'Science': 5, 'English': 8}
            ),
            PlatformAnalytics(
                analytics_id=2,
                metric_date=date.today() - timedelta(days=1),
                metric_type=MetricType.SESSION_METRICS,
                subject_distribution={'Math': 12, 'Science': 7, 'English': 6}
            )
        ]
        
        result = analytics_service._aggregate_subject_popularity(metrics_list)
        
        assert len(result) == 3
        assert result[0]['subject'] == 'Math'
        assert result[0]['session_count'] == 22
        assert result[0]['percentage'] == pytest.approx(46.8, rel=1e-1)
        assert result[1]['subject'] == 'English'
        assert result[1]['session_count'] == 14