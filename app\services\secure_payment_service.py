"""
Secure payment processing service with PCI DSS compliance.

This service implements security measures for payment processing:
- Card data tokenization (never store raw card data)
- Encryption for sensitive data at rest
- Secure communication with payment providers
- Audit logging for all payment operations
- Input validation and sanitization
- Rate limiting for payment operations
"""

import logging
import hashlib
import secrets
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, Optional, List
from decimal import Decimal
import json
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64

from app.config.settings import settings
from app.services.stripe_service import StripeService
from app.core.exceptions import (
    PaymentProcessingError, SecurityViolationError,
    ValidationError, RateLimitExceededError
)
from app.database.repositories.payment_security_repository import PaymentSecurityRepository
from app.core.logging import log_security_event

logger = logging.getLogger(__name__)


class SecurePaymentService:
    """
    Secure payment processing service implementing PCI DSS compliance.
    
    Security measures:
    1. No storage of card data - only tokens
    2. Encryption of sensitive data
    3. Secure key management
    4. Comprehensive audit logging
    5. Input validation and sanitization
    6. Rate limiting
    7. Fraud detection
    """
    
    def __init__(self):
        self.stripe_service = StripeService()
        self.security_repository = PaymentSecurityRepository()
        self._encryption_key = self._get_or_create_encryption_key()
        self._cipher_suite = Fernet(self._encryption_key)
    
    def _get_or_create_encryption_key(self) -> bytes:
        """Get or create encryption key for sensitive data."""
        # In production, this should be stored in a secure key management service
        # For now, derive from settings
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=settings.SECRET_KEY.encode()[:16],
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(settings.SECRET_KEY.encode()))
        return key
    
    def _encrypt_data(self, data: str) -> str:
        """Encrypt sensitive data."""
        return self._cipher_suite.encrypt(data.encode()).decode()
    
    def _decrypt_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive data."""
        return self._cipher_suite.decrypt(encrypted_data.encode()).decode()
    
    def _mask_card_number(self, card_number: str) -> str:
        """Mask card number for display (show only last 4 digits)."""
        if len(card_number) < 8:
            return "****"
        return f"****{card_number[-4:]}"
    
    def _validate_payment_data(self, payment_data: Dict[str, Any]) -> None:
        """
        Validate payment data for security and compliance.
        
        Checks:
        - Required fields present
        - Data types correct
        - Amount within acceptable range
        - No prohibited characters
        """
        required_fields = ['amount', 'currency', 'client_id']
        
        # Check required fields
        for field in required_fields:
            if field not in payment_data:
                raise ValidationError(f"Missing required field: {field}")
        
        # Validate amount
        amount = payment_data.get('amount')
        if not isinstance(amount, (int, float, Decimal)):
            raise ValidationError("Amount must be numeric")
        
        if amount <= 0:
            raise ValidationError("Amount must be positive")
        
        if amount > Decimal("10000.00"):  # Max transaction limit
            raise ValidationError("Amount exceeds maximum transaction limit")
        
        # Validate currency
        if payment_data.get('currency') not in ['CAD', 'USD']:
            raise ValidationError("Invalid currency")
        
        # Validate client ID
        if not isinstance(payment_data.get('client_id'), int):
            raise ValidationError("Invalid client ID")
    
    async def _check_rate_limit(self, client_id: int, operation: str) -> None:
        """Check rate limits for payment operations."""
        # Check recent attempts
        recent_attempts = await self.security_repository.get_recent_payment_attempts(
            client_id=client_id,
            operation=operation,
            time_window=timedelta(minutes=5)
        )
        
        # Rate limits by operation
        limits = {
            'create_payment': 5,  # 5 payments per 5 minutes
            'update_payment': 10,  # 10 updates per 5 minutes
            'add_payment_method': 3,  # 3 new payment methods per 5 minutes
        }
        
        limit = limits.get(operation, 10)
        
        if len(recent_attempts) >= limit:
            log_security_event(
                event_type="rate_limit_exceeded",
                user_id=client_id,
                details={"operation": operation, "attempts": len(recent_attempts)}
            )
            raise RateLimitExceededError(f"Rate limit exceeded for {operation}")
    
    async def _detect_fraud(self, payment_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Basic fraud detection checks.
        
        Returns risk assessment with score and flags.
        """
        risk_score = 0
        risk_flags = []
        
        amount = Decimal(str(payment_data.get('amount', 0)))
        client_id = payment_data.get('client_id')
        
        # Check unusual amount
        if amount > Decimal("1000.00"):
            risk_score += 20
            risk_flags.append("high_amount")
        
        # Check velocity (multiple payments in short time)
        recent_payments = await self.security_repository.get_recent_payments(
            client_id=client_id,
            time_window=timedelta(hours=1)
        )
        
        if len(recent_payments) > 3:
            risk_score += 30
            risk_flags.append("high_velocity")
        
        # Check for duplicate payments
        for payment in recent_payments:
            if payment['amount'] == amount:
                risk_score += 40
                risk_flags.append("potential_duplicate")
                break
        
        # Check failed payment history
        failed_attempts = await self.security_repository.get_failed_payment_attempts(
            client_id=client_id,
            time_window=timedelta(days=7)
        )
        
        if len(failed_attempts) > 5:
            risk_score += 25
            risk_flags.append("high_failure_rate")
        
        return {
            'risk_score': risk_score,
            'risk_level': 'high' if risk_score > 60 else 'medium' if risk_score > 30 else 'low',
            'risk_flags': risk_flags,
            'require_review': risk_score > 60
        }
    
    async def create_secure_payment(
        self,
        payment_data: Dict[str, Any],
        payment_method_token: Optional[str] = None,
        idempotency_key: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Create a secure payment with all compliance checks.
        
        Args:
            payment_data: Payment details (amount, currency, client_id, etc)
            payment_method_token: Stripe payment method token
            idempotency_key: Key for idempotent requests
            
        Returns:
            Secure payment result with masked sensitive data
        """
        try:
            # Generate idempotency key if not provided
            if not idempotency_key:
                idempotency_key = secrets.token_urlsafe(32)
            
            # Validate input data
            self._validate_payment_data(payment_data)
            
            # Check rate limits
            await self._check_rate_limit(
                payment_data['client_id'],
                'create_payment'
            )
            
            # Log payment attempt
            await self.security_repository.log_payment_attempt(
                client_id=payment_data['client_id'],
                operation='create_payment',
                ip_address=payment_data.get('ip_address'),
                user_agent=payment_data.get('user_agent')
            )
            
            # Fraud detection
            risk_assessment = await self._detect_fraud(payment_data)
            
            if risk_assessment['require_review']:
                log_security_event(
                    event_type="payment_flagged_for_review",
                    user_id=payment_data['client_id'],
                    details=risk_assessment
                )
                
                # In production, queue for manual review
                # For now, proceed with additional verification
            
            # Create payment intent with Stripe
            stripe_result = await self.stripe_service.create_payment_intent(
                amount=payment_data['amount'],
                currency=payment_data.get('currency', 'CAD'),
                client_id=payment_data['client_id'],
                invoice_id=payment_data.get('invoice_id'),
                payment_method_types=['card', 'acss_debit'],  # Canadian payment methods
                metadata={
                    'risk_score': str(risk_assessment['risk_score']),
                    'idempotency_key': idempotency_key
                }
            )
            
            # Store secure payment record
            secure_record = await self.security_repository.create_secure_payment_record(
                payment_intent_id=stripe_result['payment_intent_id'],
                client_id=payment_data['client_id'],
                amount=payment_data['amount'],
                currency=payment_data.get('currency', 'CAD'),
                risk_assessment=risk_assessment,
                idempotency_key=idempotency_key,
                encrypted_metadata=self._encrypt_data(json.dumps({
                    'invoice_id': payment_data.get('invoice_id'),
                    'ip_address': payment_data.get('ip_address'),
                    'timestamp': datetime.now().isoformat()
                }))
            )
            
            # Log successful creation
            log_security_event(
                event_type="secure_payment_created",
                user_id=payment_data['client_id'],
                details={
                    'payment_id': secure_record['secure_payment_id'],
                    'amount': str(payment_data['amount']),
                    'risk_level': risk_assessment['risk_level']
                }
            )
            
            # Return sanitized result
            return {
                'secure_payment_id': secure_record['secure_payment_id'],
                'payment_intent_id': stripe_result['payment_intent_id'],
                'client_secret': stripe_result['client_secret'],
                'amount': str(payment_data['amount']),
                'currency': payment_data.get('currency', 'CAD'),
                'status': stripe_result['status'],
                'risk_assessment': {
                    'level': risk_assessment['risk_level'],
                    'flags': risk_assessment['risk_flags']
                }
            }
            
        except ValidationError:
            raise
        except RateLimitExceededError:
            raise
        except Exception as e:
            logger.error(f"Secure payment creation failed: {e}")
            
            # Log security event
            log_security_event(
                event_type="payment_creation_failed",
                user_id=payment_data.get('client_id'),
                details={'error': str(e)}
            )
            
            raise PaymentProcessingError(f"Payment processing failed: {str(e)}")
    
    async def tokenize_payment_method(
        self,
        payment_method_data: Dict[str, Any],
        client_id: int
    ) -> Dict[str, Any]:
        """
        Tokenize payment method - never store raw card data.
        
        Args:
            payment_method_data: Payment method details from client
            client_id: Client ID
            
        Returns:
            Tokenized payment method reference
        """
        try:
            # Validate we're not receiving raw card numbers
            if 'card_number' in payment_method_data:
                log_security_event(
                    event_type="pci_violation_attempt",
                    user_id=client_id,
                    details={"violation": "raw_card_number_submitted"}
                )
                raise SecurityViolationError(
                    "Raw card numbers cannot be processed. Use tokenization."
                )
            
            # Check rate limit
            await self._check_rate_limit(client_id, 'add_payment_method')
            
            # Process through Stripe tokenization
            if 'stripe_token' not in payment_method_data:
                raise ValidationError("Payment method token required")
            
            # Create payment method in Stripe
            payment_method = await self.stripe_service.create_payment_method(
                stripe_token=payment_method_data['stripe_token'],
                client_id=client_id
            )
            
            # Store secure reference
            secure_method = await self.security_repository.create_secure_payment_method(
                client_id=client_id,
                stripe_payment_method_id=payment_method['id'],
                card_last_four=payment_method.get('card', {}).get('last4', '****'),
                card_brand=payment_method.get('card', {}).get('brand', 'unknown'),
                encrypted_metadata=self._encrypt_data(json.dumps({
                    'created_at': datetime.now().isoformat(),
                    'fingerprint': payment_method.get('card', {}).get('fingerprint', '')
                }))
            )
            
            # Log successful tokenization
            log_security_event(
                event_type="payment_method_tokenized",
                user_id=client_id,
                details={
                    'method_id': secure_method['secure_method_id'],
                    'last_four': secure_method['card_last_four']
                }
            )
            
            return {
                'secure_method_id': secure_method['secure_method_id'],
                'card_last_four': secure_method['card_last_four'],
                'card_brand': secure_method['card_brand'],
                'is_default': secure_method.get('is_default', False)
            }
            
        except (ValidationError, SecurityViolationError):
            raise
        except Exception as e:
            logger.error(f"Payment method tokenization failed: {e}")
            raise PaymentProcessingError(f"Failed to tokenize payment method: {str(e)}")
    
    async def process_payment_webhook(
        self,
        webhook_data: Dict[str, Any],
        webhook_signature: str
    ) -> Dict[str, Any]:
        """
        Securely process payment webhooks from Stripe.
        
        Args:
            webhook_data: Webhook payload
            webhook_signature: Stripe signature for verification
            
        Returns:
            Processing result
        """
        try:
            # Verify webhook signature
            if not self._verify_webhook_signature(webhook_data, webhook_signature):
                log_security_event(
                    event_type="webhook_verification_failed",
                    details={"webhook_type": webhook_data.get('type')}
                )
                raise SecurityViolationError("Invalid webhook signature")
            
            # Process based on event type
            event_type = webhook_data.get('type')
            event_data = webhook_data.get('data', {}).get('object', {})
            
            if event_type == 'payment_intent.succeeded':
                # Update payment record
                await self.security_repository.update_payment_status(
                    payment_intent_id=event_data['id'],
                    status='succeeded',
                    completed_at=datetime.now()
                )
                
                # Log success
                log_security_event(
                    event_type="payment_completed",
                    details={
                        'payment_intent_id': event_data['id'],
                        'amount': event_data['amount']
                    }
                )
                
            elif event_type == 'payment_intent.payment_failed':
                # Update payment record
                await self.security_repository.update_payment_status(
                    payment_intent_id=event_data['id'],
                    status='failed',
                    failure_reason=event_data.get('last_payment_error', {}).get('message')
                )
                
                # Log failure for risk assessment
                await self.security_repository.log_failed_payment(
                    payment_intent_id=event_data['id'],
                    client_id=event_data.get('metadata', {}).get('client_id'),
                    failure_reason=event_data.get('last_payment_error', {}).get('code')
                )
            
            return {
                'processed': True,
                'event_type': event_type,
                'payment_intent_id': event_data.get('id')
            }
            
        except SecurityViolationError:
            raise
        except Exception as e:
            logger.error(f"Webhook processing failed: {e}")
            raise PaymentProcessingError(f"Failed to process webhook: {str(e)}")
    
    def _verify_webhook_signature(
        self,
        webhook_data: Dict[str, Any],
        signature: str
    ) -> bool:
        """Verify webhook signature from Stripe."""
        # In production, use Stripe's webhook signature verification
        # For now, basic verification
        expected_signature = hashlib.sha256(
            f"{json.dumps(webhook_data, sort_keys=True)}{settings.STRIPE_WEBHOOK_SECRET}".encode()
        ).hexdigest()
        
        return secrets.compare_digest(signature, expected_signature)
    
    async def get_payment_audit_trail(
        self,
        payment_id: int,
        include_encrypted: bool = False
    ) -> List[Dict[str, Any]]:
        """
        Get complete audit trail for a payment.
        
        Args:
            payment_id: Secure payment ID
            include_encrypted: Whether to decrypt sensitive data
            
        Returns:
            List of audit events
        """
        try:
            # Get audit events
            events = await self.security_repository.get_payment_audit_trail(payment_id)
            
            # Process events
            processed_events = []
            for event in events:
                processed_event = {
                    'event_id': event['audit_id'],
                    'event_type': event['event_type'],
                    'timestamp': event['created_at'],
                    'user_id': event.get('user_id'),
                    'ip_address': event.get('ip_address')
                }
                
                # Decrypt metadata if requested and authorized
                if include_encrypted and event.get('encrypted_details'):
                    try:
                        decrypted = self._decrypt_data(event['encrypted_details'])
                        processed_event['details'] = json.loads(decrypted)
                    except Exception:
                        processed_event['details'] = {'error': 'decryption_failed'}
                
                processed_events.append(processed_event)
            
            return processed_events
            
        except Exception as e:
            logger.error(f"Failed to get audit trail: {e}")
            raise PaymentProcessingError(f"Failed to retrieve audit trail: {str(e)}")
    
    async def generate_pci_compliance_report(
        self,
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, Any]:
        """
        Generate PCI compliance report for audit.
        
        Args:
            start_date: Report start date
            end_date: Report end date
            
        Returns:
            Compliance report with security metrics
        """
        try:
            # Get security metrics
            metrics = await self.security_repository.get_security_metrics(
                start_date=start_date,
                end_date=end_date
            )
            
            # Get violation attempts
            violations = await self.security_repository.get_security_violations(
                start_date=start_date,
                end_date=end_date
            )
            
            # Calculate compliance score
            total_transactions = metrics.get('total_transactions', 0)
            secure_transactions = metrics.get('secure_transactions', 0)
            compliance_rate = (secure_transactions / total_transactions * 100) if total_transactions > 0 else 100
            
            return {
                'report_period': {
                    'start': start_date.isoformat(),
                    'end': end_date.isoformat()
                },
                'compliance_score': round(compliance_rate, 2),
                'metrics': {
                    'total_transactions': total_transactions,
                    'secure_transactions': secure_transactions,
                    'tokenized_payments': metrics.get('tokenized_payments', 0),
                    'encrypted_storage': metrics.get('encrypted_records', 0)
                },
                'security_events': {
                    'total_violations': len(violations),
                    'blocked_attempts': sum(1 for v in violations if v['blocked']),
                    'rate_limit_hits': metrics.get('rate_limit_violations', 0)
                },
                'recommendations': self._generate_security_recommendations(metrics, violations)
            }
            
        except Exception as e:
            logger.error(f"Failed to generate compliance report: {e}")
            raise PaymentProcessingError(f"Failed to generate report: {str(e)}")
    
    def _generate_security_recommendations(
        self,
        metrics: Dict[str, Any],
        violations: List[Dict[str, Any]]
    ) -> List[str]:
        """Generate security recommendations based on metrics."""
        recommendations = []
        
        # Check for high violation rate
        if len(violations) > 10:
            recommendations.append("High number of security violations detected. Review access controls.")
        
        # Check tokenization rate
        tokenization_rate = (metrics.get('tokenized_payments', 0) / metrics.get('total_transactions', 1)) * 100
        if tokenization_rate < 95:
            recommendations.append("Increase payment tokenization rate to improve security.")
        
        # Check for repeated violations from same sources
        violation_sources = {}
        for violation in violations:
            source = violation.get('source_ip', 'unknown')
            violation_sources[source] = violation_sources.get(source, 0) + 1
        
        for source, count in violation_sources.items():
            if count > 5:
                recommendations.append(f"Consider blocking suspicious source: {source}")
        
        return recommendations