"""
Search Repository - Handles all search and autocomplete operations
"""
from typing import List, Optional, Dict, Any, Union
from datetime import datetime, timedelta
from loguru import logger
import json

from app.database import Database
from app.domain.search.models import (
    SearchQuery, SearchFilters, SearchResult,
    ClientSearchResult, TutorSearchResult, DependantSearchResult,
    AutocompleteResult, PopularSearch, SearchHistory,
    SearchAnalytics, EntityType
)
from app.repositories.base import BaseRepository


class SearchRepository(BaseRepository):
    """Repository for search-related database operations"""
    
    def __init__(self, db: Database):
        super().__init__(db)
    
    # ==================== Main Search Operations ====================
    
    async def search(self, query: SearchQuery, user_id: Optional[int] = None) -> SearchResult:
        """Perform a comprehensive search across all entities"""
        # Log the search
        if user_id:
            await self._log_search(
                user_id=user_id,
                query=query.query,
                search_type="global" if not query.entity_type else query.entity_type,
                filters=query.filters.model_dump() if query.filters else {},
                session_id=query.session_id,
                ip_address=query.ip_address
            )
        
        # Refresh materialized view if needed (async)
        await self._refresh_search_view_if_needed()
        
        # Perform the search
        results = await self._fuzzy_search(
            query_text=query.query,
            entity_type=query.entity_type,
            filters=query.filters,
            limit=query.limit,
            offset=query.offset
        )
        
        # Update search count for results tracking
        if user_id and results.total_count > 0:
            await self._update_search_history_results(
                user_id=user_id,
                query=query.query,
                results_count=results.total_count
            )
        
        return results
    
    async def autocomplete(
        self, 
        query: str,
        entity_type: Optional[str] = None,
        limit: int = 10
    ) -> List[AutocompleteResult]:
        """Get autocomplete suggestions"""
        if len(query) < 2:
            return []
        
        query_text = f"{query}%"
        params = [query_text, limit]
        param_count = 3
        
        entity_clause = ""
        if entity_type:
            entity_clause = f"AND entity_type = ${param_count}"
            params.append(entity_type)
            param_count += 1
        
        sql = f"""
            SELECT 
                entity_type,
                entity_id,
                full_name,
                email,
                photo_url,
                tags
            FROM search_unified_view
            WHERE 
                is_active = true
                AND (
                    full_name ILIKE $1
                    OR email ILIKE $1
                    OR EXISTS (
                        SELECT 1 FROM unnest(tags) AS tag 
                        WHERE tag ILIKE $1
                    )
                )
                {entity_clause}
            ORDER BY 
                CASE 
                    WHEN full_name ILIKE $1 THEN 1
                    WHEN email ILIKE $1 THEN 2
                    ELSE 3
                END,
                completeness_score DESC,
                full_name
            LIMIT $2
        """
        
        rows = await self.db.fetch(sql, *params)
        
        results = []
        for row in rows:
            results.append(AutocompleteResult(
                entity_type=row['entity_type'],
                entity_id=row['entity_id'],
                display_name=row['full_name'],
                subtitle=row['email'] if row['email'] else None,
                photo_url=row['photo_url'] if row['photo_url'] else None,
                tags=row['tags'] if row['tags'] else []
            ))
        
        return results
    
    # ==================== Popular Searches ====================
    
    async def get_popular_searches(
        self, 
        limit: int = 10,
        days_back: int = 30
    ) -> List[PopularSearch]:
        """Get popular searches with time decay"""
        cutoff_date = datetime.utcnow() - timedelta(days=days_back)
        
        query = """
            SELECT 
                popular_search_id,
                search_query,
                search_count,
                last_searched,
                created_at
            FROM popular_searches
            WHERE last_searched >= $1
            ORDER BY 
                search_count * POWER(0.95, EXTRACT(DAY FROM CURRENT_TIMESTAMP - last_searched)) DESC
            LIMIT $2
        """
        
        rows = await self.db.fetch(query, cutoff_date, limit)
        
        searches = []
        for row in rows:
            searches.append(PopularSearch(**dict(row)))
        
        return searches
    
    # ==================== Search History ====================
    
    async def get_user_search_history(
        self,
        user_id: int,
        limit: int = 20,
        offset: int = 0
    ) -> List[SearchHistory]:
        """Get user's search history"""
        query = """
            SELECT 
                search_id,
                user_id,
                search_query,
                search_type,
                search_filters,
                results_count,
                selected_result_id,
                selected_result_type,
                search_timestamp,
                session_id
            FROM user_search_history
            WHERE user_id = $1
            ORDER BY search_timestamp DESC
            LIMIT $2 OFFSET $3
        """
        
        rows = await self.db.fetch(query, user_id, limit, offset)
        
        history = []
        for row in rows:
            history.append(SearchHistory(**dict(row)))
        
        return history
    
    async def clear_user_search_history(self, user_id: int) -> bool:
        """Clear user's search history"""
        query = """
            DELETE FROM user_search_history
            WHERE user_id = $1
        """
        
        await self.db.execute(query, user_id)
        return True
    
    # ==================== Search Analytics ====================
    
    async def get_search_analytics(
        self,
        start_date: datetime,
        end_date: datetime
    ) -> SearchAnalytics:
        """Get search analytics for a period"""
        # Total searches
        total_query = """
            SELECT COUNT(*) as count
            FROM user_search_history
            WHERE search_timestamp BETWEEN $1 AND $2
        """
        total_result = await self.db.fetchrow(total_query, start_date, end_date)
        total_searches = total_result['count']
        
        # Unique users
        users_query = """
            SELECT COUNT(DISTINCT user_id) as count
            FROM user_search_history
            WHERE search_timestamp BETWEEN $1 AND $2
        """
        users_result = await self.db.fetchrow(users_query, start_date, end_date)
        unique_users = users_result['count']
        
        # Average results per search
        avg_query = """
            SELECT AVG(results_count) as avg
            FROM user_search_history
            WHERE search_timestamp BETWEEN $1 AND $2
            AND results_count > 0
        """
        avg_result = await self.db.fetchrow(avg_query, start_date, end_date)
        avg_results = float(avg_result['avg']) if avg_result['avg'] else 0.0
        
        # Search success rate (searches with selections)
        success_query = """
            SELECT 
                COUNT(*) FILTER (WHERE selected_result_id IS NOT NULL) as with_selection,
                COUNT(*) as total
            FROM user_search_history
            WHERE search_timestamp BETWEEN $1 AND $2
        """
        success_result = await self.db.fetchrow(success_query, start_date, end_date)
        success_rate = (
            success_result['with_selection'] / success_result['total'] * 100
            if success_result['total'] > 0 else 0.0
        )
        
        # Top search terms
        terms_query = """
            SELECT 
                search_query,
                COUNT(*) as count
            FROM user_search_history
            WHERE search_timestamp BETWEEN $1 AND $2
            GROUP BY search_query
            ORDER BY count DESC
            LIMIT 20
        """
        terms_rows = await self.db.fetch(terms_query, start_date, end_date)
        top_search_terms = {row['search_query']: row['count'] for row in terms_rows}
        
        # Search types distribution
        types_query = """
            SELECT 
                COALESCE(search_type, 'global') as search_type,
                COUNT(*) as count
            FROM user_search_history
            WHERE search_timestamp BETWEEN $1 AND $2
            GROUP BY search_type
        """
        types_rows = await self.db.fetch(types_query, start_date, end_date)
        search_types = {row['search_type']: row['count'] for row in types_rows}
        
        # Zero result searches
        zero_query = """
            SELECT COUNT(*) as count
            FROM user_search_history
            WHERE search_timestamp BETWEEN $1 AND $2
            AND results_count = 0
        """
        zero_result = await self.db.fetchrow(zero_query, start_date, end_date)
        zero_result_searches = zero_result['count']
        
        return SearchAnalytics(
            total_searches=total_searches,
            unique_users=unique_users,
            avg_results_per_search=avg_results,
            search_success_rate=success_rate,
            top_search_terms=top_search_terms,
            search_types_distribution=search_types,
            zero_result_searches=zero_result_searches,
            period_start=start_date,
            period_end=end_date
        )
    
    # ==================== Helper Methods ====================
    
    async def _fuzzy_search(
        self,
        query_text: str,
        entity_type: Optional[str] = None,
        filters: Optional[SearchFilters] = None,
        limit: int = 50,
        offset: int = 0,
        threshold: float = 0.3
    ) -> SearchResult:
        """Perform fuzzy search using PostgreSQL functions"""
        # Build the search query
        params = [query_text, entity_type, limit, threshold]
        
        # Execute the fuzzy search function
        search_query = """
            SELECT * FROM fuzzy_search($1, $2, $3, $4)
        """
        
        rows = await self.db.fetch(search_query, *params)
        
        # Apply additional filters
        filtered_rows = []
        for row in rows:
            if await self._apply_filters(row, filters):
                filtered_rows.append(row)
        
        # Paginate results
        total_count = len(filtered_rows)
        paginated_rows = filtered_rows[offset:offset + limit]
        
        # Convert to appropriate result types
        clients = []
        tutors = []
        dependants = []
        
        for row in paginated_rows:
            if row['entity_type'] == 'client':
                result = await self._build_client_result(row)
                if result:
                    clients.append(result)
            elif row['entity_type'] == 'tutor':
                result = await self._build_tutor_result(row)
                if result:
                    tutors.append(result)
            elif row['entity_type'] == 'dependant':
                result = await self._build_dependant_result(row)
                if result:
                    dependants.append(result)
        
        return SearchResult(
            clients=clients,
            tutors=tutors,
            dependants=dependants,
            total_count=total_count,
            page=offset // limit + 1,
            page_size=limit,
            query=query_text,
            filters=filters
        )
    
    async def _apply_filters(
        self, row: Dict[str, Any], filters: Optional[SearchFilters]
    ) -> bool:
        """Apply additional filters to search results"""
        if not filters:
            return True
        
        # Language filter
        if filters.languages and row.get('language'):
            if row['language'] not in filters.languages:
                return False
        
        # Tags filter
        if filters.tags and row.get('tags'):
            if not any(tag in row['tags'] for tag in filters.tags):
                return False
        
        # Date range filter
        if filters.created_after:
            if row.get('created_at') and row['created_at'] < filters.created_after:
                return False
        
        if filters.created_before:
            if row.get('created_at') and row['created_at'] > filters.created_before:
                return False
        
        return True
    
    async def _build_client_result(self, row: Dict[str, Any]) -> Optional[ClientSearchResult]:
        """Build client search result"""
        # Get additional client info
        client_query = """
            SELECT 
                cp.date_of_birth,
                cp.gender,
                COUNT(DISTINCT dp.dependant_id) as dependant_count
            FROM client_profiles cp
            LEFT JOIN dependant_parents dp ON cp.client_id = dp.client_id AND dp.deleted_at IS NULL
            WHERE cp.client_id = $1
            GROUP BY cp.date_of_birth, cp.gender
        """
        
        client_info = await self.db.fetchrow(client_query, row['entity_id'])
        if not client_info:
            return None
        
        return ClientSearchResult(
            client_id=row['entity_id'],
            first_name=row['full_name'].split()[0] if row['full_name'] else '',
            last_name=' '.join(row['full_name'].split()[1:]) if row['full_name'] else '',
            email=row['email'],
            phone=row['phone'],
            profile_photo_url=row['photo_url'],
            date_of_birth=client_info['date_of_birth'],
            gender=client_info['gender'],
            dependant_count=client_info['dependant_count'],
            match_score=row['similarity_score']
        )
    
    async def _build_tutor_result(self, row: Dict[str, Any]) -> Optional[TutorSearchResult]:
        """Build tutor search result"""
        # Get additional tutor info
        tutor_query = """
            SELECT 
                tp.rating,
                tp.total_reviews,
                tp.hourly_rate_min,
                tp.hourly_rate_max,
                tp.years_of_experience,
                tp.verification_status,
                tp.bio,
                tp.headline
            FROM tutor_profiles tp
            WHERE tp.tutor_id = $1
        """
        
        tutor_info = await self.db.fetchrow(tutor_query, row['entity_id'])
        if not tutor_info:
            return None
        
        return TutorSearchResult(
            tutor_id=row['entity_id'],
            first_name=row['full_name'].split()[0] if row['full_name'] else '',
            last_name=' '.join(row['full_name'].split()[1:]) if row['full_name'] else '',
            email=row['email'],
            phone=row['phone'],
            profile_photo_url=row['photo_url'],
            rating=float(tutor_info['rating']) if tutor_info['rating'] else 0.0,
            total_reviews=tutor_info['total_reviews'],
            hourly_rate_min=float(tutor_info['hourly_rate_min']) if tutor_info['hourly_rate_min'] else None,
            hourly_rate_max=float(tutor_info['hourly_rate_max']) if tutor_info['hourly_rate_max'] else None,
            subjects=row['tags'] if row['tags'] else [],
            years_of_experience=tutor_info['years_of_experience'],
            verification_status=tutor_info['verification_status'],
            bio=tutor_info['bio'],
            headline=tutor_info['headline'],
            match_score=row['similarity_score']
        )
    
    async def _build_dependant_result(self, row: Dict[str, Any]) -> Optional[DependantSearchResult]:
        """Build dependant search result"""
        # Get additional dependant info
        dependant_query = """
            SELECT 
                d.date_of_birth,
                d.gender,
                d.preferred_name,
                de.grade_level,
                de.school_name,
                array_agg(DISTINCT cp.first_name || ' ' || cp.last_name) as parent_names
            FROM dependants d
            LEFT JOIN dependant_education de ON d.dependant_id = de.dependant_id
            LEFT JOIN dependant_parents dp ON d.dependant_id = dp.dependant_id AND dp.deleted_at IS NULL
            LEFT JOIN client_profiles cp ON dp.client_id = cp.client_id
            WHERE d.dependant_id = $1
            GROUP BY d.date_of_birth, d.gender, d.preferred_name, de.grade_level, de.school_name
        """
        
        dependant_info = await self.db.fetchrow(dependant_query, row['entity_id'])
        if not dependant_info:
            return None
        
        return DependantSearchResult(
            dependant_id=row['entity_id'],
            first_name=row['full_name'].split()[0] if row['full_name'] else '',
            last_name=' '.join(row['full_name'].split()[1:]) if row['full_name'] else '',
            preferred_name=dependant_info['preferred_name'],
            date_of_birth=dependant_info['date_of_birth'],
            gender=dependant_info['gender'],
            profile_photo_url=row['photo_url'],
            grade_level=dependant_info['grade_level'],
            school_name=dependant_info['school_name'],
            parent_names=dependant_info['parent_names'] if dependant_info['parent_names'] else [],
            match_score=row['similarity_score']
        )
    
    async def _log_search(
        self,
        user_id: int,
        query: str,
        search_type: str,
        filters: Dict[str, Any],
        session_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        results_count: int = 0
    ) -> None:
        """Log search to history"""
        log_query = """
            SELECT log_search($1, $2, $3, $4, $5, $6, $7)
        """
        
        await self.db.execute(
            log_query,
            user_id,
            query,
            search_type,
            json.dumps(filters),
            results_count,
            session_id,
            ip_address
        )
    
    async def _update_search_history_results(
        self,
        user_id: int,
        query: str,
        results_count: int
    ) -> None:
        """Update the results count for the most recent search"""
        update_query = """
            UPDATE user_search_history
            SET results_count = $3
            WHERE user_id = $1 
            AND search_query = $2
            AND search_timestamp > CURRENT_TIMESTAMP - INTERVAL '1 minute'
            AND results_count = 0
        """
        
        await self.db.execute(update_query, user_id, query, results_count)
    
    async def _refresh_search_view_if_needed(self) -> None:
        """Check if search view needs refresh and trigger if needed"""
        # Check last refresh time from cache
        cache_query = """
            SELECT created_at 
            FROM search_cache 
            WHERE cache_key = 'last_view_refresh'
            AND expires_at > CURRENT_TIMESTAMP
        """
        
        cache_result = await self.db.fetchrow(cache_query)
        
        # If no recent refresh, trigger one
        if not cache_result:
            # Notify for async refresh
            await self.db.execute("SELECT pg_notify('refresh_search_view', '')")
            
            # Update cache
            await self.db.execute("""
                INSERT INTO search_cache (cache_key, search_results, expires_at)
                VALUES ('last_view_refresh', '{"refreshed": true}'::jsonb, CURRENT_TIMESTAMP + INTERVAL '5 minutes')
                ON CONFLICT (cache_key) DO UPDATE
                SET search_results = EXCLUDED.search_results,
                    expires_at = EXCLUDED.expires_at,
                    created_at = CURRENT_TIMESTAMP
            """)
    
    async def record_search_selection(
        self,
        user_id: int,
        search_query: str,
        selected_entity_type: str,
        selected_entity_id: int
    ) -> None:
        """Record when a user selects a search result"""
        update_query = """
            UPDATE user_search_history
            SET selected_result_id = $3,
                selected_result_type = $4
            WHERE user_id = $1
            AND search_query = $2
            AND search_timestamp > CURRENT_TIMESTAMP - INTERVAL '5 minutes'
            AND selected_result_id IS NULL
            ORDER BY search_timestamp DESC
            LIMIT 1
        """
        
        await self.db.execute(
            update_query,
            user_id,
            search_query,
            selected_entity_id,
            selected_entity_type
        )