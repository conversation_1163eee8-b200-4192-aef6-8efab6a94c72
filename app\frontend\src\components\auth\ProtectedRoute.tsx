import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { hasPermission, hasAnyPermission } from '../../utils/permissions';
import { permissions } from '../../utils/permissions';
import { ConsentGuard } from './ConsentGuard';

interface ProtectedRouteProps {
  children: React.ReactNode;
  permission?: keyof typeof permissions;
  permissions?: (keyof typeof permissions)[];
  requireAny?: boolean; // If true, user needs ANY of the permissions, otherwise ALL
  fallbackPath?: string;
  checkConsents?: boolean; // If true, will check for mandatory consents
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  permission,
  permissions: multiplePermissions,
  requireAny = true,
  fallbackPath = '/dashboard',
  checkConsents = true
}) => {
  const { user, isAuthenticated } = useAuth();

  // Not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // Check permissions
  if (permission || multiplePermissions) {
    const permissionsToCheck = permission ? [permission] : (multiplePermissions || []);
    
    const hasAccess = requireAny
      ? hasAnyPermission(user?.activeRole, ...permissionsToCheck)
      : permissionsToCheck.every(p => hasPermission(user?.activeRole, p));

    if (!hasAccess) {
      return <Navigate to={fallbackPath} replace />;
    }
  }

  // Wrap in ConsentGuard if consent checking is enabled
  if (checkConsents) {
    return <ConsentGuard>{children}</ConsentGuard>;
  }

  return <>{children}</>;
};