"""
Repository for analytics and performance metrics database operations.
"""

from datetime import date, datetime, timedelta
from decimal import Decimal
from typing import List, Optional, Dict, Any
import json
import asyncpg
from asyncpg import Connection, Record

from app.database.repositories.base import BaseRepository
from app.models.analytics_models import (
    TutorPerformanceMetrics, StudentLearningGoal, StudentProgressMetrics,
    PlatformAnalytics, SessionFeedback, AnalyticsSnapshot,
    PeriodType, GoalStatus, FeedbackRole, MetricType,
    TutorLeaderboardEntry, StudentProgressSummary
)
from app.core.exceptions import (
    ResourceNotFoundError, DatabaseOperationError, ValidationError
)
from app.core.logging import TutorAideLogger

logger = TutorAideLogger(__name__)


class TutorPerformanceRepository(BaseRepository[TutorPerformanceMetrics]):
    """Repository for tutor performance metrics operations."""
    
    def __init__(self):
        """Initialize tutor performance repository."""
        super().__init__("tutor_performance_metrics", "metric_id")
        self.model_class = TutorPerformanceMetrics
    
    async def get_or_create_metrics(
        self,
        conn: Connection,
        tutor_id: int,
        period_start: date,
        period_end: date,
        period_type: PeriodType
    ) -> TutorPerformanceMetrics:
        """Get existing metrics or create new ones for a period."""
        try:
            # Try to get existing metrics
            query = """
                SELECT * FROM tutor_performance_metrics
                WHERE tutor_id = $1 AND period_start = $2 
                AND period_end = $3 AND period_type = $4
            """
            result = await conn.fetchrow(query, tutor_id, period_start, period_end, period_type.value)
            
            if result:
                return TutorPerformanceMetrics(**dict(result))
            
            # Create new metrics
            insert_query = """
                INSERT INTO tutor_performance_metrics (
                    tutor_id, period_start, period_end, period_type
                ) VALUES ($1, $2, $3, $4)
                RETURNING *
            """
            new_result = await conn.fetchrow(
                insert_query, tutor_id, period_start, period_end, period_type.value
            )
            return TutorPerformanceMetrics(**dict(new_result))
            
        except Exception as e:
            logger.error(f"Error getting/creating tutor metrics: {e}")
            raise DatabaseOperationError(f"Failed to get or create metrics: {str(e)}")
    
    async def update_session_metrics(
        self,
        conn: Connection,
        tutor_id: int,
        period_start: date,
        period_end: date,
        period_type: PeriodType,
        session_data: Dict[str, Any]
    ) -> TutorPerformanceMetrics:
        """Update session-related metrics."""
        try:
            # Calculate subject breakdown
            subject_breakdown = json.dumps(session_data.get('subject_breakdown', {}))
            
            query = """
                UPDATE tutor_performance_metrics
                SET total_sessions = $5,
                    completed_sessions = $6,
                    cancelled_sessions = $7,
                    no_show_sessions = $8,
                    total_hours = $9,
                    unique_students = $10,
                    returning_students = $11,
                    new_students = $12,
                    subject_breakdown = $13::jsonb,
                    updated_at = CURRENT_TIMESTAMP
                WHERE tutor_id = $1 AND period_start = $2 
                AND period_end = $3 AND period_type = $4
                RETURNING *
            """
            
            result = await conn.fetchrow(
                query,
                tutor_id, period_start, period_end, period_type.value,
                session_data.get('total_sessions', 0),
                session_data.get('completed_sessions', 0),
                session_data.get('cancelled_sessions', 0),
                session_data.get('no_show_sessions', 0),
                session_data.get('total_hours', Decimal("0")),
                session_data.get('unique_students', 0),
                session_data.get('returning_students', 0),
                session_data.get('new_students', 0),
                subject_breakdown
            )
            
            return TutorPerformanceMetrics(**dict(result))
            
        except Exception as e:
            logger.error(f"Error updating session metrics: {e}")
            raise DatabaseOperationError(f"Failed to update session metrics: {str(e)}")
    
    async def update_rating_metrics(
        self,
        conn: Connection,
        tutor_id: int,
        period_start: date,
        period_end: date,
        period_type: PeriodType,
        rating_data: Dict[str, Any]
    ) -> TutorPerformanceMetrics:
        """Update rating-related metrics."""
        try:
            query = """
                UPDATE tutor_performance_metrics
                SET average_rating = $5,
                    total_ratings = $6,
                    five_star_count = $7,
                    updated_at = CURRENT_TIMESTAMP
                WHERE tutor_id = $1 AND period_start = $2 
                AND period_end = $3 AND period_type = $4
                RETURNING *
            """
            
            result = await conn.fetchrow(
                query,
                tutor_id, period_start, period_end, period_type.value,
                rating_data.get('average_rating'),
                rating_data.get('total_ratings', 0),
                rating_data.get('five_star_count', 0)
            )
            
            return TutorPerformanceMetrics(**dict(result))
            
        except Exception as e:
            logger.error(f"Error updating rating metrics: {e}")
            raise DatabaseOperationError(f"Failed to update rating metrics: {str(e)}")
    
    async def update_financial_metrics(
        self,
        conn: Connection,
        tutor_id: int,
        period_start: date,
        period_end: date,
        period_type: PeriodType,
        financial_data: Dict[str, Any]
    ) -> TutorPerformanceMetrics:
        """Update financial metrics."""
        try:
            # Calculate average hourly rate
            total_revenue = financial_data.get('total_revenue', Decimal("0"))
            total_hours = financial_data.get('total_hours', Decimal("0"))
            avg_hourly_rate = None
            if total_hours > 0:
                avg_hourly_rate = total_revenue / total_hours
            
            query = """
                UPDATE tutor_performance_metrics
                SET total_revenue = $5,
                    total_hours = $6,
                    average_hourly_rate = $7,
                    updated_at = CURRENT_TIMESTAMP
                WHERE tutor_id = $1 AND period_start = $2 
                AND period_end = $3 AND period_type = $4
                RETURNING *
            """
            
            result = await conn.fetchrow(
                query,
                tutor_id, period_start, period_end, period_type.value,
                total_revenue,
                total_hours,
                avg_hourly_rate
            )
            
            return TutorPerformanceMetrics(**dict(result))
            
        except Exception as e:
            logger.error(f"Error updating financial metrics: {e}")
            raise DatabaseOperationError(f"Failed to update financial metrics: {str(e)}")
    
    async def get_leaderboard(
        self,
        conn: Connection,
        period_type: PeriodType = PeriodType.MONTHLY,
        limit: int = 10
    ) -> List[TutorLeaderboardEntry]:
        """Get tutor leaderboard."""
        try:
            query = """
                SELECT * FROM tutor_leaderboard
                ORDER BY rating_rank, session_rank, revenue_rank
                LIMIT $1
            """
            
            results = await conn.fetch(query, limit)
            return [TutorLeaderboardEntry(**dict(row)) for row in results]
            
        except Exception as e:
            logger.error(f"Error getting leaderboard: {e}")
            raise DatabaseOperationError(f"Failed to get leaderboard: {str(e)}")


class StudentProgressRepository(BaseRepository[StudentProgressMetrics]):
    """Repository for student progress tracking."""
    
    def __init__(self):
        """Initialize student progress repository."""
        super().__init__("student_progress_metrics", "metric_id")
        self.model_class = StudentProgressMetrics
    
    async def create_learning_goal(
        self,
        conn: Connection,
        goal_data: Dict[str, Any]
    ) -> StudentLearningGoal:
        """Create a new learning goal."""
        try:
            query = """
                INSERT INTO student_learning_goals (
                    student_id, subject_area, goal_title, goal_description,
                    target_date, created_by, assigned_tutor_id
                ) VALUES ($1, $2, $3, $4, $5, $6, $7)
                RETURNING *
            """
            
            result = await conn.fetchrow(
                query,
                goal_data['student_id'],
                goal_data['subject_area'],
                goal_data['goal_title'],
                goal_data.get('goal_description'),
                goal_data.get('target_date'),
                goal_data.get('created_by'),
                goal_data.get('assigned_tutor_id')
            )
            
            return StudentLearningGoal(**dict(result))
            
        except Exception as e:
            logger.error(f"Error creating learning goal: {e}")
            raise DatabaseOperationError(f"Failed to create learning goal: {str(e)}")
    
    async def update_goal_progress(
        self,
        conn: Connection,
        goal_id: int,
        progress_percentage: int,
        status: Optional[GoalStatus] = None
    ) -> StudentLearningGoal:
        """Update learning goal progress."""
        try:
            query_parts = [
                "UPDATE student_learning_goals",
                "SET progress_percentage = $2, updated_at = CURRENT_TIMESTAMP"
            ]
            params = [goal_id, progress_percentage]
            
            if status:
                query_parts[1] += ", status = $3"
                params.append(status.value)
                
                if status == GoalStatus.COMPLETED:
                    query_parts[1] += ", completed_at = CURRENT_TIMESTAMP"
            
            query_parts.extend([
                "WHERE goal_id = $1 AND deleted_at IS NULL",
                "RETURNING *"
            ])
            
            query = " ".join(query_parts)
            result = await conn.fetchrow(query, *params)
            
            if not result:
                raise ResourceNotFoundError(f"Learning goal {goal_id} not found")
            
            return StudentLearningGoal(**dict(result))
            
        except Exception as e:
            logger.error(f"Error updating goal progress: {e}")
            if isinstance(e, ResourceNotFoundError):
                raise
            raise DatabaseOperationError(f"Failed to update goal progress: {str(e)}")
    
    async def get_student_goals(
        self,
        conn: Connection,
        student_id: int,
        subject_area: Optional[str] = None,
        status: Optional[GoalStatus] = None
    ) -> List[StudentLearningGoal]:
        """Get student's learning goals."""
        try:
            query_parts = [
                "SELECT * FROM student_learning_goals",
                "WHERE student_id = $1 AND deleted_at IS NULL"
            ]
            params = [student_id]
            
            if subject_area:
                query_parts.append(f"AND subject_area = ${len(params) + 1}")
                params.append(subject_area)
            
            if status:
                query_parts.append(f"AND status = ${len(params) + 1}")
                params.append(status.value)
            
            query_parts.append("ORDER BY created_at DESC")
            
            query = " ".join(query_parts)
            results = await conn.fetch(query, *params)
            
            return [StudentLearningGoal(**dict(row)) for row in results]
            
        except Exception as e:
            logger.error(f"Error getting student goals: {e}")
            raise DatabaseOperationError(f"Failed to get student goals: {str(e)}")
    
    async def get_progress_summary(
        self,
        conn: Connection,
        student_id: int,
        days: int = 30
    ) -> List[StudentProgressSummary]:
        """Get student progress summary."""
        try:
            query = """
                SELECT * FROM student_progress_summary
                WHERE student_id = $1
                ORDER BY subject_area
            """
            
            results = await conn.fetch(query, student_id)
            return [StudentProgressSummary(**dict(row)) for row in results]
            
        except Exception as e:
            logger.error(f"Error getting progress summary: {e}")
            raise DatabaseOperationError(f"Failed to get progress summary: {str(e)}")


class SessionFeedbackRepository(BaseRepository[SessionFeedback]):
    """Repository for session feedback operations."""
    
    def __init__(self):
        """Initialize session feedback repository."""
        super().__init__("session_feedback", "feedback_id")
        self.model_class = SessionFeedback
    
    async def create_feedback(
        self,
        conn: Connection,
        feedback_data: Dict[str, Any]
    ) -> SessionFeedback:
        """Create session feedback."""
        try:
            # Convert lists to PostgreSQL arrays
            improvement_areas = feedback_data.get('improvement_areas', [])
            positive_highlights = feedback_data.get('positive_highlights', [])
            
            query = """
                INSERT INTO session_feedback (
                    appointment_id, given_by, given_to, role_type,
                    overall_rating, punctuality_rating, preparation_rating,
                    communication_rating, feedback_text, improvement_areas,
                    positive_highlights, requires_followup
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
                RETURNING *
            """
            
            result = await conn.fetchrow(
                query,
                feedback_data['appointment_id'],
                feedback_data['given_by'],
                feedback_data['given_to'],
                feedback_data['role_type'],
                feedback_data['overall_rating'],
                feedback_data.get('punctuality_rating'),
                feedback_data.get('preparation_rating'),
                feedback_data.get('communication_rating'),
                feedback_data.get('feedback_text'),
                improvement_areas,
                positive_highlights,
                feedback_data.get('requires_followup', False)
            )
            
            return SessionFeedback(**dict(result))
            
        except asyncpg.UniqueViolationError:
            raise ValidationError("Feedback already exists for this appointment")
        except Exception as e:
            logger.error(f"Error creating feedback: {e}")
            raise DatabaseOperationError(f"Failed to create feedback: {str(e)}")
    
    async def get_feedback_for_user(
        self,
        conn: Connection,
        user_id: int,
        role: str = 'given_to',
        limit: int = 10
    ) -> List[SessionFeedback]:
        """Get feedback for a user."""
        try:
            query = f"""
                SELECT * FROM session_feedback
                WHERE {role} = $1
                ORDER BY created_at DESC
                LIMIT $2
            """
            
            results = await conn.fetch(query, user_id, limit)
            return [SessionFeedback(**dict(row)) for row in results]
            
        except Exception as e:
            logger.error(f"Error getting user feedback: {e}")
            raise DatabaseOperationError(f"Failed to get user feedback: {str(e)}")
    
    async def get_pending_followups(
        self,
        conn: Connection,
        limit: int = 50
    ) -> List[SessionFeedback]:
        """Get feedback requiring followup."""
        try:
            query = """
                SELECT * FROM session_feedback
                WHERE requires_followup = TRUE AND followup_completed = FALSE
                ORDER BY created_at ASC
                LIMIT $1
            """
            
            results = await conn.fetch(query, limit)
            return [SessionFeedback(**dict(row)) for row in results]
            
        except Exception as e:
            logger.error(f"Error getting pending followups: {e}")
            raise DatabaseOperationError(f"Failed to get pending followups: {str(e)}")


class PlatformAnalyticsRepository(BaseRepository[PlatformAnalytics]):
    """Repository for platform analytics operations."""
    
    def __init__(self):
        """Initialize platform analytics repository."""
        super().__init__("platform_analytics", "analytics_id")
        self.model_class = PlatformAnalytics
    
    async def record_daily_metrics(
        self,
        conn: Connection,
        metric_date: date,
        metrics: Dict[str, Any]
    ) -> PlatformAnalytics:
        """Record daily platform metrics."""
        try:
            # Convert dictionaries to JSON
            subject_dist = json.dumps(metrics.get('subject_distribution', {}))
            peak_hours = json.dumps(metrics.get('peak_hours', {}))
            time_slots = json.dumps(metrics.get('time_slot_distribution', {}))
            geo_dist = json.dumps(metrics.get('geographic_distribution', {}))
            
            query = """
                INSERT INTO platform_analytics (
                    metric_date, metric_type, active_clients, active_tutors,
                    active_students, new_registrations, total_bookings,
                    completed_sessions, booking_conversion_rate,
                    average_session_duration, daily_revenue, average_session_value,
                    subject_distribution, peak_hours, time_slot_distribution,
                    geographic_distribution
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12,
                    $13::jsonb, $14::jsonb, $15::jsonb, $16::jsonb
                )
                ON CONFLICT (metric_date, metric_type) 
                DO UPDATE SET
                    active_clients = EXCLUDED.active_clients,
                    active_tutors = EXCLUDED.active_tutors,
                    active_students = EXCLUDED.active_students,
                    new_registrations = EXCLUDED.new_registrations,
                    total_bookings = EXCLUDED.total_bookings,
                    completed_sessions = EXCLUDED.completed_sessions,
                    booking_conversion_rate = EXCLUDED.booking_conversion_rate,
                    average_session_duration = EXCLUDED.average_session_duration,
                    daily_revenue = EXCLUDED.daily_revenue,
                    average_session_value = EXCLUDED.average_session_value,
                    subject_distribution = EXCLUDED.subject_distribution,
                    peak_hours = EXCLUDED.peak_hours,
                    time_slot_distribution = EXCLUDED.time_slot_distribution,
                    geographic_distribution = EXCLUDED.geographic_distribution
                RETURNING *
            """
            
            result = await conn.fetchrow(
                query,
                metric_date,
                metrics.get('metric_type', MetricType.USER_ACTIVITY.value),
                metrics.get('active_clients', 0),
                metrics.get('active_tutors', 0),
                metrics.get('active_students', 0),
                metrics.get('new_registrations', 0),
                metrics.get('total_bookings', 0),
                metrics.get('completed_sessions', 0),
                metrics.get('booking_conversion_rate'),
                metrics.get('average_session_duration'),
                metrics.get('daily_revenue', Decimal("0")),
                metrics.get('average_session_value'),
                subject_dist,
                peak_hours,
                time_slots,
                geo_dist
            )
            
            return PlatformAnalytics(**dict(result))
            
        except Exception as e:
            logger.error(f"Error recording platform metrics: {e}")
            raise DatabaseOperationError(f"Failed to record platform metrics: {str(e)}")
    
    async def get_metrics_range(
        self,
        conn: Connection,
        start_date: date,
        end_date: date,
        metric_type: Optional[MetricType] = None
    ) -> List[PlatformAnalytics]:
        """Get platform metrics for a date range."""
        try:
            query_parts = [
                "SELECT * FROM platform_analytics",
                "WHERE metric_date >= $1 AND metric_date <= $2"
            ]
            params = [start_date, end_date]
            
            if metric_type:
                query_parts.append(f"AND metric_type = ${len(params) + 1}")
                params.append(metric_type.value)
            
            query_parts.append("ORDER BY metric_date ASC")
            
            query = " ".join(query_parts)
            results = await conn.fetch(query, *params)
            
            return [PlatformAnalytics(**dict(row)) for row in results]
            
        except Exception as e:
            logger.error(f"Error getting platform metrics: {e}")
            raise DatabaseOperationError(f"Failed to get platform metrics: {str(e)}")


class AnalyticsSnapshotRepository(BaseRepository[AnalyticsSnapshot]):
    """Repository for analytics snapshot operations."""
    
    def __init__(self):
        """Initialize analytics snapshot repository."""
        super().__init__("analytics_snapshots", "snapshot_id")
        self.model_class = AnalyticsSnapshot
    
    async def save_snapshot(
        self,
        conn: Connection,
        snapshot_data: Dict[str, Any]
    ) -> AnalyticsSnapshot:
        """Save analytics snapshot."""
        try:
            metrics_json = json.dumps(snapshot_data.get('metrics', {}))
            
            query = """
                INSERT INTO analytics_snapshots (
                    snapshot_type, snapshot_date, entity_type,
                    entity_id, metrics, processing_time_ms
                ) VALUES ($1, $2, $3, $4, $5::jsonb, $6)
                ON CONFLICT (snapshot_type, snapshot_date, entity_type, entity_id)
                DO UPDATE SET
                    metrics = EXCLUDED.metrics,
                    processing_time_ms = EXCLUDED.processing_time_ms,
                    created_at = CURRENT_TIMESTAMP
                RETURNING *
            """
            
            result = await conn.fetchrow(
                query,
                snapshot_data['snapshot_type'],
                snapshot_data['snapshot_date'],
                snapshot_data['entity_type'],
                snapshot_data.get('entity_id'),
                metrics_json,
                snapshot_data.get('processing_time_ms')
            )
            
            return AnalyticsSnapshot(**dict(result))
            
        except Exception as e:
            logger.error(f"Error saving snapshot: {e}")
            raise DatabaseOperationError(f"Failed to save snapshot: {str(e)}")
    
    async def get_latest_snapshot(
        self,
        conn: Connection,
        snapshot_type: str,
        entity_type: str,
        entity_id: Optional[int] = None
    ) -> Optional[AnalyticsSnapshot]:
        """Get latest snapshot for an entity."""
        try:
            query = """
                SELECT * FROM analytics_snapshots
                WHERE snapshot_type = $1 AND entity_type = $2
            """
            params = [snapshot_type, entity_type]
            
            if entity_id is not None:
                query += " AND entity_id = $3"
                params.append(entity_id)
            else:
                query += " AND entity_id IS NULL"
            
            query += " ORDER BY snapshot_date DESC LIMIT 1"
            
            result = await conn.fetchrow(query, *params)
            if result:
                return AnalyticsSnapshot(**dict(result))
            return None
            
        except Exception as e:
            logger.error(f"Error getting latest snapshot: {e}")
            raise DatabaseOperationError(f"Failed to get latest snapshot: {str(e)}")