"""
Service for managing and executing quick actions.
"""

import asyncio
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import asyncpg

from app.models.quick_action_models import (
    QuickActionType,
    QuickActionDefinition,
    QuickActionContext,
    QuickActionResult,
    QuickActionUsage,
    UserQuickActions,
    get_actions_for_role,
    get_action_by_type
)
from app.models.user_models import UserRoleType, User
from app.core.logging import <PERSON><PERSON><PERSON><PERSON><PERSON>og<PERSON>
from app.core.exceptions import (
    ValidationError,
    AuthorizationError,
    BusinessLogicError
)
from app.core.timezone import now_est

logger = TutorAideLogger.get_logger(__name__)


class QuickActionService:
    """Service for managing quick actions."""
    
    def __init__(self):
        self.action_handlers = self._register_action_handlers()
    
    def _register_action_handlers(self) -> Dict[QuickActionType, callable]:
        """Register handlers for each action type."""
        return {
            # Manager actions
            QuickActionType.ADD_USER: self._handle_add_user,
            QuickActionType.INVITE_TUTOR: self._handle_invite_tutor,
            QuickActionType.CREATE_CLIENT: self._handle_create_client,
            QuickActionType.SCHEDULE_GROUP: self._handle_schedule_group,
            QuickActionType.GENERATE_REPORT: self._handle_generate_report,
            QuickActionType.SEND_BROADCAST: self._handle_send_broadcast,
            QuickActionType.VIEW_ANALYTICS: self._handle_view_analytics,
            QuickActionType.MANAGE_BILLING: self._handle_manage_billing,
            
            # Tutor actions
            QuickActionType.VIEW_SCHEDULE: self._handle_view_schedule,
            QuickActionType.REQUEST_TIME_OFF: self._handle_request_time_off,
            QuickActionType.UPDATE_AVAILABILITY: self._handle_update_availability,
            QuickActionType.SUBMIT_DOCUMENTS: self._handle_submit_documents,
            QuickActionType.VIEW_MY_STUDENTS: self._handle_view_my_students,
            QuickActionType.UPDATE_RATES: self._handle_update_rates,
            QuickActionType.VIEW_EARNINGS: self._handle_view_earnings,
            
            # Client actions
            QuickActionType.BOOK_SESSION: self._handle_book_session,
            QuickActionType.ADD_DEPENDANT: self._handle_add_dependant,
            QuickActionType.VIEW_INVOICES: self._handle_view_invoices,
            QuickActionType.MESSAGE_TUTOR: self._handle_message_tutor,
            QuickActionType.UPDATE_PROFILE: self._handle_update_profile,
            QuickActionType.VIEW_SESSIONS: self._handle_view_sessions,
            QuickActionType.MAKE_PAYMENT: self._handle_make_payment,
            
            # Common actions
            QuickActionType.SEARCH_USERS: self._handle_search_users,
            QuickActionType.VIEW_NOTIFICATIONS: self._handle_view_notifications,
            QuickActionType.CHANGE_SETTINGS: self._handle_change_settings,
        }
    
    async def get_user_quick_actions(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        active_role: UserRoleType
    ) -> UserQuickActions:
        """Get available quick actions for a user."""
        try:
            # Get available actions for the role
            available_actions = get_actions_for_role(active_role)
            
            # Get user's recent actions
            recent_actions = await self._get_recent_actions(conn, user_id)
            
            # Get user's pinned actions
            pinned_actions = await self._get_pinned_actions(conn, user_id)
            
            # Get suggested actions based on context
            suggested_actions = await self._get_suggested_actions(
                conn, user_id, active_role
            )
            
            return UserQuickActions(
                user_id=user_id,
                active_role=active_role,
                available_actions=available_actions,
                recent_actions=recent_actions,
                pinned_actions=pinned_actions,
                suggested_actions=suggested_actions
            )
            
        except Exception as e:
            logger.error(f"Error getting user quick actions: {e}")
            raise
    
    async def execute_action(
        self,
        conn: asyncpg.Connection,
        context: QuickActionContext
    ) -> QuickActionResult:
        """Execute a quick action."""
        start_time = datetime.utcnow()
        
        try:
            # Validate action is allowed for user's role
            action_def = get_action_by_type(context.action_type)
            if not action_def:
                raise ValidationError(f"Unknown action type: {context.action_type}")
            
            if context.user_role not in action_def.allowed_roles:
                raise AuthorizationError(
                    f"Action {context.action_type} not allowed for role {context.user_role}"
                )
            
            # Check if action requires entity selection
            if action_def.requires_selection and not context.selected_entity_id:
                raise ValidationError(
                    f"Action {context.action_type} requires selecting an entity first"
                )
            
            # Execute the action
            handler = self.action_handlers.get(context.action_type)
            if not handler:
                raise BusinessLogicError(
                    f"No handler registered for action {context.action_type}"
                )
            
            result = await handler(conn, context)
            
            # Track usage
            execution_time_ms = int((datetime.utcnow() - start_time).total_seconds() * 1000)
            await self._track_usage(
                conn,
                context,
                result.success,
                execution_time_ms,
                result.message
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Error executing quick action: {e}")
            
            # Track failed usage
            execution_time_ms = int((datetime.utcnow() - start_time).total_seconds() * 1000)
            await self._track_usage(
                conn,
                context,
                False,
                execution_time_ms,
                str(e)
            )
            
            raise
    
    async def pin_action(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        action_type: QuickActionType
    ) -> bool:
        """Pin a quick action for quick access."""
        try:
            # Check if already pinned
            existing = await conn.fetchval(
                """
                SELECT 1 FROM user_pinned_actions
                WHERE user_id = $1 AND action_type = $2
                """,
                user_id,
                action_type.value
            )
            
            if existing:
                return True
            
            # Pin the action
            await conn.execute(
                """
                INSERT INTO user_pinned_actions (user_id, action_type, pinned_at)
                VALUES ($1, $2, $3)
                """,
                user_id,
                action_type.value,
                now_est()
            )
            
            logger.info(f"User {user_id} pinned action {action_type}")
            return True
            
        except Exception as e:
            logger.error(f"Error pinning action: {e}")
            raise
    
    async def unpin_action(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        action_type: QuickActionType
    ) -> bool:
        """Unpin a quick action."""
        try:
            result = await conn.execute(
                """
                DELETE FROM user_pinned_actions
                WHERE user_id = $1 AND action_type = $2
                """,
                user_id,
                action_type.value
            )
            
            return result.split()[-1] != "0"
            
        except Exception as e:
            logger.error(f"Error unpinning action: {e}")
            raise
    
    async def get_action_analytics(
        self,
        conn: asyncpg.Connection,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        user_id: Optional[int] = None,
        role: Optional[UserRoleType] = None
    ) -> Dict[str, Any]:
        """Get analytics for quick action usage."""
        try:
            # Build query conditions
            conditions = []
            params = []
            param_count = 0
            
            if start_date:
                param_count += 1
                conditions.append(f"ua.executed_at >= ${param_count}")
                params.append(start_date)
            
            if end_date:
                param_count += 1
                conditions.append(f"ua.executed_at <= ${param_count}")
                params.append(end_date)
            
            if user_id:
                param_count += 1
                conditions.append(f"ua.user_id = ${param_count}")
                params.append(user_id)
            
            where_clause = " AND ".join(conditions) if conditions else "1=1"
            
            # Get usage statistics
            stats = await conn.fetch(
                f"""
                SELECT 
                    ua.action_type,
                    COUNT(*) as usage_count,
                    AVG(ua.execution_time_ms) as avg_execution_time,
                    SUM(CASE WHEN ua.success THEN 1 ELSE 0 END) as success_count,
                    COUNT(DISTINCT ua.user_id) as unique_users
                FROM quick_action_usage ua
                WHERE {where_clause}
                GROUP BY ua.action_type
                ORDER BY usage_count DESC
                """,
                *params
            )
            
            # Get most common errors
            errors = await conn.fetch(
                f"""
                SELECT 
                    action_type,
                    error_message,
                    COUNT(*) as error_count
                FROM quick_action_usage
                WHERE NOT success AND error_message IS NOT NULL
                    AND {where_clause}
                GROUP BY action_type, error_message
                ORDER BY error_count DESC
                LIMIT 10
                """,
                *params
            )
            
            return {
                "usage_by_action": [dict(s) for s in stats],
                "common_errors": [dict(e) for e in errors],
                "total_executions": sum(s["usage_count"] for s in stats),
                "average_success_rate": (
                    sum(s["success_count"] for s in stats) / 
                    sum(s["usage_count"] for s in stats) * 100
                    if stats else 0
                ),
                "period": {
                    "start": start_date.isoformat() if start_date else None,
                    "end": end_date.isoformat() if end_date else None
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting action analytics: {e}")
            raise
    
    # Private helper methods
    
    async def _get_recent_actions(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        limit: int = 5
    ) -> List[QuickActionType]:
        """Get user's recently used actions."""
        try:
            records = await conn.fetch(
                """
                SELECT DISTINCT action_type
                FROM quick_action_usage
                WHERE user_id = $1 AND success = true
                ORDER BY MAX(executed_at) DESC
                LIMIT $2
                """,
                user_id,
                limit
            )
            
            return [QuickActionType(r["action_type"]) for r in records]
            
        except Exception as e:
            logger.error(f"Error getting recent actions: {e}")
            return []
    
    async def _get_pinned_actions(
        self,
        conn: asyncpg.Connection,
        user_id: int
    ) -> List[QuickActionType]:
        """Get user's pinned actions."""
        try:
            records = await conn.fetch(
                """
                SELECT action_type
                FROM user_pinned_actions
                WHERE user_id = $1
                ORDER BY pinned_at DESC
                """,
                user_id
            )
            
            return [QuickActionType(r["action_type"]) for r in records]
            
        except Exception as e:
            logger.error(f"Error getting pinned actions: {e}")
            return []
    
    async def _get_suggested_actions(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        role: UserRoleType
    ) -> List[QuickActionType]:
        """Get suggested actions based on user context and behavior."""
        suggestions = []
        
        try:
            # Suggest based on time of day
            current_hour = now_est().hour
            
            if role == UserRoleType.TUTOR:
                if 6 <= current_hour <= 9:
                    suggestions.append(QuickActionType.VIEW_SCHEDULE)
                elif 15 <= current_hour <= 18:
                    suggestions.append(QuickActionType.UPDATE_AVAILABILITY)
            
            elif role == UserRoleType.CLIENT:
                # Check if user has upcoming sessions
                has_sessions = await conn.fetchval(
                    """
                    SELECT EXISTS(
                        SELECT 1 FROM appointments a
                        JOIN client_dependants cd ON cd.dependant_id = a.dependant_id
                        JOIN client_profiles cp ON cp.client_id = cd.parent1_client_id 
                            OR cp.client_id = cd.parent2_client_id
                        WHERE cp.user_id = $1 
                            AND a.scheduled_at > $2
                            AND a.status = 'scheduled'
                    )
                    """,
                    user_id,
                    now_est()
                )
                
                if not has_sessions:
                    suggestions.append(QuickActionType.BOOK_SESSION)
                else:
                    suggestions.append(QuickActionType.VIEW_SESSIONS)
            
            elif role == UserRoleType.MANAGER:
                # Suggest reports at end of week
                if now_est().weekday() >= 4:  # Thursday or Friday
                    suggestions.append(QuickActionType.GENERATE_REPORT)
                
                # Suggest user management in morning
                if 8 <= current_hour <= 11:
                    suggestions.append(QuickActionType.ADD_USER)
            
            # Limit suggestions
            return suggestions[:3]
            
        except Exception as e:
            logger.error(f"Error getting suggested actions: {e}")
            return []
    
    async def _track_usage(
        self,
        conn: asyncpg.Connection,
        context: QuickActionContext,
        success: bool,
        execution_time_ms: int,
        error_message: Optional[str] = None
    ):
        """Track quick action usage."""
        try:
            await conn.execute(
                """
                INSERT INTO quick_action_usage 
                (user_id, action_type, executed_at, execution_time_ms, 
                 success, error_message, context_data)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
                """,
                context.user_id,
                context.action_type.value,
                now_est(),
                execution_time_ms,
                success,
                error_message,
                {
                    "role": context.user_role.value,
                    "entity_id": context.selected_entity_id,
                    "entity_type": context.selected_entity_type,
                    "client_context": context.client_context
                }
            )
        except Exception as e:
            logger.error(f"Error tracking action usage: {e}")
    
    # Action handlers
    
    async def _handle_add_user(
        self,
        conn: asyncpg.Connection,
        context: QuickActionContext
    ) -> QuickActionResult:
        """Handle add user action."""
        return QuickActionResult(
            success=True,
            action_type=context.action_type,
            modal_component="AddUserModal",
            modal_props={
                "defaultRole": "client",
                "showRoleSelection": True
            }
        )
    
    async def _handle_invite_tutor(
        self,
        conn: asyncpg.Connection,
        context: QuickActionContext
    ) -> QuickActionResult:
        """Handle invite tutor action."""
        return QuickActionResult(
            success=True,
            action_type=context.action_type,
            modal_component="InviteTutorModal",
            modal_props={
                "showSpecializations": True,
                "defaultMessage": "We'd love to have you join our team of tutors!"
            }
        )
    
    async def _handle_create_client(
        self,
        conn: asyncpg.Connection,
        context: QuickActionContext
    ) -> QuickActionResult:
        """Handle create client action."""
        return QuickActionResult(
            success=True,
            action_type=context.action_type,
            redirect_url="/clients/new"
        )
    
    async def _handle_schedule_group(
        self,
        conn: asyncpg.Connection,
        context: QuickActionContext
    ) -> QuickActionResult:
        """Handle schedule group session action."""
        return QuickActionResult(
            success=True,
            action_type=context.action_type,
            modal_component="ScheduleGroupSessionModal",
            modal_props={
                "sessionTypes": ["TECFEE", "Study Group", "Workshop"],
                "maxParticipants": 10
            }
        )
    
    async def _handle_generate_report(
        self,
        conn: asyncpg.Connection,
        context: QuickActionContext
    ) -> QuickActionResult:
        """Handle generate report action."""
        return QuickActionResult(
            success=True,
            action_type=context.action_type,
            modal_component="GenerateReportModal",
            modal_props={
                "reportTypes": ["Financial", "Performance", "Usage", "Monthly"],
                "defaultDateRange": "last_month"
            }
        )
    
    async def _handle_send_broadcast(
        self,
        conn: asyncpg.Connection,
        context: QuickActionContext
    ) -> QuickActionResult:
        """Handle send broadcast action."""
        return QuickActionResult(
            success=True,
            action_type=context.action_type,
            modal_component="BroadcastMessageModal",
            modal_props={
                "recipientTypes": ["all_clients", "all_tutors", "specific_group"],
                "messageTypes": ["announcement", "reminder", "alert"]
            }
        )
    
    async def _handle_view_analytics(
        self,
        conn: asyncpg.Connection,
        context: QuickActionContext
    ) -> QuickActionResult:
        """Handle view analytics action."""
        return QuickActionResult(
            success=True,
            action_type=context.action_type,
            redirect_url="/dashboard/analytics"
        )
    
    async def _handle_manage_billing(
        self,
        conn: asyncpg.Connection,
        context: QuickActionContext
    ) -> QuickActionResult:
        """Handle manage billing action."""
        return QuickActionResult(
            success=True,
            action_type=context.action_type,
            redirect_url="/billing/manage"
        )
    
    async def _handle_view_schedule(
        self,
        conn: asyncpg.Connection,
        context: QuickActionContext
    ) -> QuickActionResult:
        """Handle view schedule action."""
        # Get tutor_id for the user
        tutor_id = await conn.fetchval(
            "SELECT tutor_id FROM tutor_profiles WHERE user_id = $1",
            context.user_id
        )
        
        return QuickActionResult(
            success=True,
            action_type=context.action_type,
            redirect_url=f"/tutors/schedule/{tutor_id}"
        )
    
    async def _handle_request_time_off(
        self,
        conn: asyncpg.Connection,
        context: QuickActionContext
    ) -> QuickActionResult:
        """Handle request time off action."""
        return QuickActionResult(
            success=True,
            action_type=context.action_type,
            modal_component="TimeOffRequestModal",
            modal_props={
                "minDays": 1,
                "maxDays": 30,
                "requiresReason": True
            }
        )
    
    async def _handle_update_availability(
        self,
        conn: asyncpg.Connection,
        context: QuickActionContext
    ) -> QuickActionResult:
        """Handle update availability action."""
        return QuickActionResult(
            success=True,
            action_type=context.action_type,
            redirect_url="/tutors/availability"
        )
    
    async def _handle_submit_documents(
        self,
        conn: asyncpg.Connection,
        context: QuickActionContext
    ) -> QuickActionResult:
        """Handle submit documents action."""
        return QuickActionResult(
            success=True,
            action_type=context.action_type,
            modal_component="DocumentUploadModal",
            modal_props={
                "acceptedTypes": ["pdf", "jpg", "png"],
                "requiredDocuments": ["resume", "degree", "id"]
            }
        )
    
    async def _handle_view_my_students(
        self,
        conn: asyncpg.Connection,
        context: QuickActionContext
    ) -> QuickActionResult:
        """Handle view my students action."""
        return QuickActionResult(
            success=True,
            action_type=context.action_type,
            redirect_url="/tutors/students"
        )
    
    async def _handle_update_rates(
        self,
        conn: asyncpg.Connection,
        context: QuickActionContext
    ) -> QuickActionResult:
        """Handle update rates action."""
        return QuickActionResult(
            success=True,
            action_type=context.action_type,
            redirect_url="/tutors/rates"
        )
    
    async def _handle_view_earnings(
        self,
        conn: asyncpg.Connection,
        context: QuickActionContext
    ) -> QuickActionResult:
        """Handle view earnings action."""
        return QuickActionResult(
            success=True,
            action_type=context.action_type,
            redirect_url="/tutors/earnings"
        )
    
    async def _handle_book_session(
        self,
        conn: asyncpg.Connection,
        context: QuickActionContext
    ) -> QuickActionResult:
        """Handle book session action."""
        return QuickActionResult(
            success=True,
            action_type=context.action_type,
            modal_component="BookSessionModal",
            modal_props={
                "showTutorSearch": True,
                "showSubjectFilter": True,
                "defaultDuration": 60
            }
        )
    
    async def _handle_add_dependant(
        self,
        conn: asyncpg.Connection,
        context: QuickActionContext
    ) -> QuickActionResult:
        """Handle add dependant action."""
        return QuickActionResult(
            success=True,
            action_type=context.action_type,
            modal_component="AddDependantModal",
            modal_props={
                "showSchoolSearch": True,
                "requiresBirthDate": True
            }
        )
    
    async def _handle_view_invoices(
        self,
        conn: asyncpg.Connection,
        context: QuickActionContext
    ) -> QuickActionResult:
        """Handle view invoices action."""
        return QuickActionResult(
            success=True,
            action_type=context.action_type,
            redirect_url="/billing/invoices"
        )
    
    async def _handle_message_tutor(
        self,
        conn: asyncpg.Connection,
        context: QuickActionContext
    ) -> QuickActionResult:
        """Handle message tutor action."""
        if not context.selected_entity_id:
            return QuickActionResult(
                success=False,
                action_type=context.action_type,
                message="Please select a tutor to message"
            )
        
        return QuickActionResult(
            success=True,
            action_type=context.action_type,
            modal_component="MessageModal",
            modal_props={
                "recipientId": context.selected_entity_id,
                "recipientType": "tutor"
            }
        )
    
    async def _handle_update_profile(
        self,
        conn: asyncpg.Connection,
        context: QuickActionContext
    ) -> QuickActionResult:
        """Handle update profile action."""
        return QuickActionResult(
            success=True,
            action_type=context.action_type,
            redirect_url="/profile/edit"
        )
    
    async def _handle_view_sessions(
        self,
        conn: asyncpg.Connection,
        context: QuickActionContext
    ) -> QuickActionResult:
        """Handle view sessions action."""
        return QuickActionResult(
            success=True,
            action_type=context.action_type,
            redirect_url="/sessions"
        )
    
    async def _handle_make_payment(
        self,
        conn: asyncpg.Connection,
        context: QuickActionContext
    ) -> QuickActionResult:
        """Handle make payment action."""
        return QuickActionResult(
            success=True,
            action_type=context.action_type,
            modal_component="PaymentModal",
            modal_props={
                "showSavedCards": True,
                "allowNewCard": True
            }
        )
    
    async def _handle_search_users(
        self,
        conn: asyncpg.Connection,
        context: QuickActionContext
    ) -> QuickActionResult:
        """Handle search users action."""
        return QuickActionResult(
            success=True,
            action_type=context.action_type,
            modal_component="GlobalSearchModal",
            modal_props={
                "autoFocus": True,
                "showFilters": True
            }
        )
    
    async def _handle_view_notifications(
        self,
        conn: asyncpg.Connection,
        context: QuickActionContext
    ) -> QuickActionResult:
        """Handle view notifications action."""
        return QuickActionResult(
            success=True,
            action_type=context.action_type,
            redirect_url="/notifications"
        )
    
    async def _handle_change_settings(
        self,
        conn: asyncpg.Connection,
        context: QuickActionContext
    ) -> QuickActionResult:
        """Handle change settings action."""
        return QuickActionResult(
            success=True,
            action_type=context.action_type,
            redirect_url="/settings"
        )


# Singleton instance
_quick_action_service = QuickActionService()


def get_quick_action_service() -> QuickActionService:
    """Get the quick action service instance."""
    return _quick_action_service