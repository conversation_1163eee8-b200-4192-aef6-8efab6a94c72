-- Migration: Search Enhancements
-- Description: Add search capabilities with full-text search and fuzzy matching

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS pg_trgm;
CREATE EXTENSION IF NOT EXISTS unaccent;

-- Search History Table
CREATE TABLE IF NOT EXISTS user_search_history (
    search_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    search_query VARCHAR(500) NOT NULL,
    search_type VARCHAR(50),
    search_filters JSONB DEFAULT '{}',
    results_count INTEGER DEFAULT 0,
    selected_result_id INTEGER,
    selected_result_type VARCHAR(50),
    search_timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    session_id VARCHAR(100),
    ip_address INET,
    user_agent TEXT
);

-- Popular Searches Table
CREATE TABLE IF NOT EXISTS popular_searches (
    popular_search_id SERIAL PRIMARY KEY,
    search_query VARCHAR(500) NOT NULL,
    search_count INTEGER DEFAULT 1,
    last_searched TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_search_query UNIQUE (search_query)
);

-- Search Cache Table (for performance)
CREATE TABLE IF NOT EXISTS search_cache (
    cache_id SERIAL PRIMARY KEY,
    cache_key VARCHAR(500) NOT NULL,
    search_results JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    CONSTRAINT unique_cache_key UNIQUE (cache_key)
);

-- Create search indexes for clients
CREATE INDEX IF NOT EXISTS idx_client_profiles_search ON client_profiles 
USING gin(to_tsvector('english', 
    COALESCE(first_name, '') || ' ' || 
    COALESCE(last_name, '') || ' ' || 
    COALESCE(email, '') || ' ' || 
    COALESCE(phone, '')
));

CREATE INDEX IF NOT EXISTS idx_client_profiles_trigram ON client_profiles 
USING gin((first_name || ' ' || last_name) gin_trgm_ops);

-- Create search indexes for tutors
CREATE INDEX IF NOT EXISTS idx_tutor_search ON tutor_profiles tp
USING gin(to_tsvector('english', 
    COALESCE((SELECT first_name || ' ' || last_name FROM users WHERE user_id = tp.user_id), '') || ' ' ||
    COALESCE(bio, '') || ' ' ||
    COALESCE(headline, '') || ' ' ||
    COALESCE(professional_summary, '')
));

CREATE INDEX IF NOT EXISTS idx_tutor_trigram ON users u
USING gin((first_name || ' ' || last_name) gin_trgm_ops)
WHERE EXISTS (SELECT 1 FROM tutor_profiles WHERE user_id = u.user_id);

-- Create search indexes for dependants
CREATE INDEX IF NOT EXISTS idx_dependants_search ON dependants 
USING gin(to_tsvector('english', 
    COALESCE(first_name, '') || ' ' || 
    COALESCE(last_name, '') || ' ' || 
    COALESCE(preferred_name, '')
))
WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS idx_dependants_trigram ON dependants 
USING gin((first_name || ' ' || last_name) gin_trgm_ops)
WHERE deleted_at IS NULL;

-- Create materialized view for unified search
CREATE MATERIALIZED VIEW IF NOT EXISTS search_unified_view AS
-- Clients
SELECT 
    'client' as entity_type,
    cp.client_id as entity_id,
    cp.first_name,
    cp.last_name,
    cp.first_name || ' ' || cp.last_name as full_name,
    cp.email,
    cp.phone,
    u.is_active,
    cp.created_at,
    cp.updated_at,
    to_tsvector('english', 
        COALESCE(cp.first_name, '') || ' ' || 
        COALESCE(cp.last_name, '') || ' ' || 
        COALESCE(cp.email, '') || ' ' || 
        COALESCE(cp.phone, '')
    ) as search_vector,
    COALESCE(cp.profile_photo_url, '') as photo_url,
    COALESCE(cl.preferred_language, 'en') as language,
    ARRAY[]::text[] as tags,
    cp.profile_completeness as completeness_score
FROM client_profiles cp
JOIN users u ON cp.user_id = u.user_id
LEFT JOIN client_preferences cl ON cp.client_id = cl.client_id
WHERE cp.deleted_at IS NULL

UNION ALL

-- Tutors
SELECT 
    'tutor' as entity_type,
    tp.tutor_id as entity_id,
    u.first_name,
    u.last_name,
    u.first_name || ' ' || u.last_name as full_name,
    u.email,
    tp.phone,
    u.is_active,
    tp.created_at,
    tp.updated_at,
    to_tsvector('english', 
        COALESCE(u.first_name, '') || ' ' || 
        COALESCE(u.last_name, '') || ' ' || 
        COALESCE(u.email, '') || ' ' || 
        COALESCE(tp.bio, '') || ' ' ||
        COALESCE(tp.headline, '')
    ) as search_vector,
    COALESCE(tp.profile_photo_url, '') as photo_url,
    COALESCE(u.preferred_language, 'en') as language,
    COALESCE(
        (SELECT array_agg(DISTINCT subject_area) 
         FROM tutor_specializations 
         WHERE tutor_id = tp.tutor_id), 
        ARRAY[]::text[]
    ) as tags,
    tp.profile_completeness as completeness_score
FROM tutor_profiles tp
JOIN users u ON tp.user_id = u.user_id
WHERE tp.deleted_at IS NULL

UNION ALL

-- Dependants
SELECT 
    'dependant' as entity_type,
    d.dependant_id as entity_id,
    d.first_name,
    d.last_name,
    d.first_name || ' ' || d.last_name as full_name,
    '' as email,
    '' as phone,
    true as is_active,
    d.created_at,
    d.updated_at,
    to_tsvector('english', 
        COALESCE(d.first_name, '') || ' ' || 
        COALESCE(d.last_name, '') || ' ' || 
        COALESCE(d.preferred_name, '')
    ) as search_vector,
    COALESCE(d.profile_photo_url, '') as photo_url,
    COALESCE(
        (SELECT preferred_language 
         FROM client_preferences 
         WHERE client_id = (SELECT client_id FROM dependant_parents WHERE dependant_id = d.dependant_id AND is_primary_contact = true LIMIT 1)
        ), 'en'
    ) as language,
    COALESCE(de.grade_level, ARRAY[]::text[]) as tags,
    0 as completeness_score
FROM dependants d
LEFT JOIN dependant_education de ON d.dependant_id = de.dependant_id
WHERE d.deleted_at IS NULL;

-- Create indexes on materialized view
CREATE INDEX idx_search_unified_entity ON search_unified_view(entity_type, entity_id);
CREATE INDEX idx_search_unified_fulltext ON search_unified_view USING gin(search_vector);
CREATE INDEX idx_search_unified_trigram ON search_unified_view USING gin(full_name gin_trgm_ops);
CREATE INDEX idx_search_unified_active ON search_unified_view(is_active) WHERE is_active = true;
CREATE INDEX idx_search_unified_created ON search_unified_view(created_at DESC);

-- Function to refresh search view
CREATE OR REPLACE FUNCTION refresh_search_view()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY search_unified_view;
END;
$$ LANGUAGE plpgsql;

-- Function for fuzzy search
CREATE OR REPLACE FUNCTION fuzzy_search(
    p_query TEXT,
    p_entity_type TEXT DEFAULT NULL,
    p_limit INTEGER DEFAULT 50,
    p_threshold REAL DEFAULT 0.3
)
RETURNS TABLE (
    entity_type TEXT,
    entity_id INTEGER,
    full_name TEXT,
    email TEXT,
    phone TEXT,
    photo_url TEXT,
    tags TEXT[],
    similarity_score REAL,
    rank REAL
) AS $$
BEGIN
    RETURN QUERY
    WITH search_results AS (
        SELECT 
            s.entity_type,
            s.entity_id,
            s.full_name,
            s.email,
            s.phone,
            s.photo_url,
            s.tags,
            similarity(s.full_name, p_query) as name_similarity,
            CASE 
                WHEN s.full_name ILIKE p_query || '%' THEN 1.0
                WHEN s.full_name ILIKE '%' || p_query || '%' THEN 0.8
                ELSE similarity(s.full_name, p_query)
            END as adjusted_similarity,
            ts_rank(s.search_vector, plainto_tsquery('english', p_query)) as text_rank
        FROM search_unified_view s
        WHERE 
            s.is_active = true
            AND (p_entity_type IS NULL OR s.entity_type = p_entity_type)
            AND (
                s.search_vector @@ plainto_tsquery('english', p_query)
                OR similarity(s.full_name, p_query) > p_threshold
                OR s.full_name ILIKE '%' || p_query || '%'
                OR s.email ILIKE p_query || '%'
                OR s.phone LIKE '%' || regexp_replace(p_query, '[^0-9]', '', 'g') || '%'
            )
    )
    SELECT 
        sr.entity_type,
        sr.entity_id,
        sr.full_name,
        sr.email,
        sr.phone,
        sr.photo_url,
        sr.tags,
        sr.adjusted_similarity,
        sr.text_rank + sr.adjusted_similarity as combined_rank
    FROM search_results sr
    ORDER BY 
        sr.adjusted_similarity DESC,
        sr.text_rank DESC,
        sr.full_name
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- Function for autocomplete
CREATE OR REPLACE FUNCTION autocomplete_search(
    p_query TEXT,
    p_entity_type TEXT DEFAULT NULL,
    p_limit INTEGER DEFAULT 10
)
RETURNS TABLE (
    entity_type TEXT,
    entity_id INTEGER,
    full_name TEXT,
    email TEXT,
    photo_url TEXT,
    tags TEXT[]
) AS $$
BEGIN
    -- Prioritize prefix matches for autocomplete
    RETURN QUERY
    SELECT DISTINCT
        s.entity_type,
        s.entity_id,
        s.full_name,
        s.email,
        s.photo_url,
        s.tags
    FROM search_unified_view s
    WHERE 
        s.is_active = true
        AND (p_entity_type IS NULL OR s.entity_type = p_entity_type)
        AND (
            s.full_name ILIKE p_query || '%'
            OR s.email ILIKE p_query || '%'
            OR EXISTS (
                SELECT 1 FROM unnest(s.tags) AS tag 
                WHERE tag ILIKE p_query || '%'
            )
        )
    ORDER BY 
        CASE 
            WHEN s.full_name ILIKE p_query || '%' THEN 1
            WHEN s.email ILIKE p_query || '%' THEN 2
            ELSE 3
        END,
        s.completeness_score DESC,
        s.full_name
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- Function to log search
CREATE OR REPLACE FUNCTION log_search(
    p_user_id INTEGER,
    p_query TEXT,
    p_search_type TEXT,
    p_filters JSONB,
    p_results_count INTEGER,
    p_session_id TEXT DEFAULT NULL,
    p_ip_address INET DEFAULT NULL
)
RETURNS void AS $$
BEGIN
    -- Log to search history
    INSERT INTO user_search_history (
        user_id, search_query, search_type, search_filters, 
        results_count, session_id, ip_address
    )
    VALUES (
        p_user_id, p_query, p_search_type, p_filters, 
        p_results_count, p_session_id, p_ip_address
    );
    
    -- Update popular searches
    INSERT INTO popular_searches (search_query, search_count, last_searched)
    VALUES (lower(p_query), 1, CURRENT_TIMESTAMP)
    ON CONFLICT (search_query) 
    DO UPDATE SET 
        search_count = popular_searches.search_count + 1,
        last_searched = CURRENT_TIMESTAMP;
END;
$$ LANGUAGE plpgsql;

-- Create indexes for search history
CREATE INDEX idx_user_search_history_user_id ON user_search_history(user_id);
CREATE INDEX idx_user_search_history_timestamp ON user_search_history(search_timestamp DESC);
CREATE INDEX idx_popular_searches_count ON popular_searches(search_count DESC);

-- Create trigger to refresh search view periodically
CREATE OR REPLACE FUNCTION trigger_refresh_search_view()
RETURNS trigger AS $$
BEGIN
    -- Refresh the materialized view asynchronously
    PERFORM pg_notify('refresh_search_view', '');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers on source tables
CREATE TRIGGER refresh_search_on_client_change
AFTER INSERT OR UPDATE OR DELETE ON client_profiles
FOR EACH STATEMENT EXECUTE FUNCTION trigger_refresh_search_view();

CREATE TRIGGER refresh_search_on_tutor_change
AFTER INSERT OR UPDATE OR DELETE ON tutor_profiles
FOR EACH STATEMENT EXECUTE FUNCTION trigger_refresh_search_view();

CREATE TRIGGER refresh_search_on_dependant_change
AFTER INSERT OR UPDATE OR DELETE ON dependants
FOR EACH STATEMENT EXECUTE FUNCTION trigger_refresh_search_view();

CREATE TRIGGER refresh_search_on_user_change
AFTER UPDATE ON users
FOR EACH STATEMENT EXECUTE FUNCTION trigger_refresh_search_view();