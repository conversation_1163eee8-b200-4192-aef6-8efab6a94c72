# Google OAuth Setup Guide for TutorAide

This guide explains how to set up and use Google OAuth authentication in the TutorAide application.

## Overview

Google OAuth integration allows users to:
- Sign up using their Google account
- Log in without creating a separate password
- Link their existing TutorAide account to Google
- Maintain the same multi-role capabilities (<PERSON>, <PERSON><PERSON>, Client)

## Google Cloud Console Setup

### 1. Create a Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Click "Create Project" or select an existing project
3. Give your project a name (e.g., "TutorAide")

### 2. Enable Required APIs

1. In the Cloud Console, go to "APIs & Services" > "Enable APIs and Services"
2. Search for and enable: **Google+ API** (required for OAuth 2.0)

### 3. Create OAuth 2.0 Credentials

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth client ID"
3. If prompted, configure the OAuth consent screen first:
   - Choose "External" for user type
   - Fill in required fields (App name, User support email, etc.)
   - Add your domain to authorized domains
   - Add scopes: `openid`, `email`, `profile`

4. For the OAuth client:
   - Application type: **Web application**
   - Name: "TutorAide Web Client"
   - Authorized redirect URIs:
     - Development: `http://localhost:8000/api/v1/auth/google/callback`
     - Production: `https://api.tutoraide.ca/api/v1/auth/google/callback`

5. Save the Client ID and Client Secret

## Environment Configuration

### 1. Update .env File

Add the following to your `.env` file:

```bash
# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-client-id-here.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-client-secret-here
GOOGLE_REDIRECT_URI=http://localhost:8000/api/v1/auth/google/callback
```

For production:
```bash
GOOGLE_REDIRECT_URI=https://api.tutoraide.ca/api/v1/auth/google/callback
```

### 2. Verify Configuration

Run the manual test to verify setup:
```bash
uv run python test_google_oauth_manual.py
```

## API Endpoints

### 1. Initiate Google Login
```
GET /api/v1/auth/google/login
```

Query Parameters:
- `next` (optional): URL to redirect after successful login
- `redirect_uri` (optional): Custom redirect URI

Example:
```bash
curl http://localhost:8000/api/v1/auth/google/login?next=/dashboard
```

### 2. Handle OAuth Callback
```
GET /api/v1/auth/google/callback
```

Query Parameters:
- `code`: Authorization code from Google
- `state`: CSRF protection state token
- `error` (optional): Error from Google
- `role` (optional): Requested role for new users (manager/tutor/client)

This endpoint is called by Google after user authorization.

### 3. Link Google Account
```
POST /api/v1/auth/google/link
```

Request Body:
```json
{
  "code": "authorization-code-from-google"
}
```

Links Google account to currently authenticated user.

## Frontend Integration

### 1. Login Button

```javascript
// Redirect to Google OAuth
const loginWithGoogle = () => {
  window.location.href = 'http://localhost:8000/api/v1/auth/google/login?next=/dashboard';
};
```

### 2. Handle Callback

The callback endpoint redirects to your frontend with tokens:
```
http://localhost:3000/auth/callback?token=JWT_TOKEN&refresh=REFRESH_TOKEN&expires_in=1800&next=/dashboard
```

Extract and store tokens:
```javascript
const params = new URLSearchParams(window.location.search);
const accessToken = params.get('token');
const refreshToken = params.get('refresh');
const expiresIn = params.get('expires_in');
const nextUrl = params.get('next') || '/';

// Store tokens securely
localStorage.setItem('access_token', accessToken);
localStorage.setItem('refresh_token', refreshToken);

// Redirect to intended page
window.location.href = nextUrl;
```

### 3. Link Existing Account

For authenticated users to link their Google account:

```javascript
const linkGoogleAccount = async () => {
  // First, redirect to Google OAuth with a flag
  window.location.href = 'http://localhost:8000/api/v1/auth/google/login?link=true';
};

// After OAuth callback, send the code to link endpoint
const handleLinkCallback = async (code) => {
  const response = await fetch('/api/v1/auth/google/link', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ code })
  });
  
  const result = await response.json();
  if (result.success) {
    alert('Google account linked successfully!');
  }
};
```

## User Flow Examples

### New User Registration via Google

1. User clicks "Sign up with Google"
2. Redirected to Google consent screen
3. After approval, redirected back with user info
4. New account created with:
   - Email from Google (verified)
   - No password required
   - Default role: CLIENT
   - Mandatory consents recorded
5. JWT tokens issued
6. User redirected to app

### Existing User Login via Google

1. User clicks "Login with Google"
2. If Google ID matches existing account, login proceeds
3. If email matches but no Google ID, account is linked
4. JWT tokens issued with appropriate role
5. User redirected to app

### Role Selection for New Users

Tutors being invited can specify their role:
```
/api/v1/auth/google/login?role=tutor
```

## Security Considerations

1. **State Token**: Used for CSRF protection, one-time use only
2. **Email Verification**: Google emails are pre-verified
3. **Account Linking**: Only links if email matches authenticated user
4. **Token Security**: Same JWT security as password-based auth
5. **HTTPS Required**: Always use HTTPS in production

## Troubleshooting

### Common Issues

1. **"Invalid state token"**
   - State tokens expire after 10 minutes
   - Each state can only be used once
   - Solution: Restart the OAuth flow

2. **"Email not verified with Google"**
   - Rare, but user's Google email isn't verified
   - Solution: User must verify email with Google first

3. **"Failed to exchange authorization code"**
   - Usually means redirect URI mismatch
   - Check that redirect URI matches exactly in Google Console

4. **Missing Client ID/Secret**
   - Ensure .env file has correct values
   - No quotes needed in .env file
   - Restart server after updating .env

### Testing

1. **Manual Test Script**:
   ```bash
   uv run python test_google_oauth_manual.py
   ```

2. **Unit Tests**:
   ```bash
   uv run pytest tests/unit/test_google_oauth_simple.py -v
   ```

3. **Check Endpoints**:
   ```bash
   curl http://localhost:8000/api/v1/auth/
   ```

## Production Deployment

1. Update Google Cloud Console:
   - Add production redirect URI
   - Verify domain ownership
   - Submit for OAuth verification if needed

2. Update environment:
   ```bash
   GOOGLE_REDIRECT_URI=https://api.tutoraide.ca/api/v1/auth/google/callback
   ```

3. Ensure HTTPS is configured

4. Monitor OAuth quota limits in Google Cloud Console

## API Response Examples

### Successful Login Response
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGc...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGc...",
  "token_type": "bearer",
  "expires_in": 1800,
  "user": {
    "user_id": 123,
    "email": "<EMAIL>",
    "is_email_verified": true,
    "has_local_auth": false,
    "has_google_auth": true,
    "roles": ["client"],
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  },
  "requires_consents": []
}
```

### Link Account Response
```json
{
  "success": true,
  "message": "Google account linked successfully"
}
```