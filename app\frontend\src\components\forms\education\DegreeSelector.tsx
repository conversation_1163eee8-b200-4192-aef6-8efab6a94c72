import React, { useState } from 'react';
import { Select } from '../../common/Select';
import { Input } from '../../common/Input';

interface DegreeSelectorProps {
  value: { level?: string; name?: string };
  onChange: (value: { level?: string; name?: string }) => void;
  error?: { level?: string; name?: string };
  required?: boolean;
}

export const DegreeSelector: React.FC<DegreeSelectorProps> = ({
  value,
  onChange,
  error,
  required = false
}) => {
  const [showCustom, setShowCustom] = useState(value.level === 'other');

  const degreeOptions = [
    { value: '', label: 'Select degree level...' },
    { value: 'high_school', label: 'High School Diploma' },
    { value: 'associate', label: 'Associate Degree' },
    { value: 'bachelor', label: "Bachelor's Degree" },
    { value: 'master', label: "Master's Degree" },
    { value: 'phd', label: 'PhD/Doctorate' },
    { value: 'professional', label: 'Professional Degree (JD, MD, etc.)' },
    { value: 'other', label: 'Other (specify)' }
  ];

  const handleLevelChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const level = e.target.value;
    setShowCustom(level === 'other');
    
    // Auto-populate degree name for common degrees
    let degreeName = value.name || '';
    if (level && level !== 'other') {
      const option = degreeOptions.find(opt => opt.value === level);
      if (option && !value.name) {
        degreeName = option.label;
      }
    }
    
    onChange({ level, name: degreeName });
  };

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange({ ...value, name: e.target.value });
  };

  return (
    <div className="space-y-3">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Highest Degree Level {required && <span className="text-red-500">*</span>}
        </label>
        <Select
          value={value.level || ''}
          onChange={handleLevelChange}
          options={degreeOptions}
          error={error?.level}
        />
      </div>

      {value.level && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Degree Name {showCustom && required && <span className="text-red-500">*</span>}
          </label>
          <Input
            value={value.name || ''}
            onChange={handleNameChange}
            placeholder={showCustom ? "Enter your degree name" : "e.g., Bachelor of Science"}
            error={error?.name}
            disabled={!showCustom && value.level !== ''}
          />
          {!showCustom && value.level && (
            <p className="mt-1 text-xs text-gray-500">
              You can edit this to match your exact degree name
            </p>
          )}
        </div>
      )}
    </div>
  );
};