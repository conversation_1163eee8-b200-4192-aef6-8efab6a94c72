-- Migration: Add client profiles table
-- Description: Create client_profiles table for storing client-specific information

-- Create client_profiles table
CREATE TABLE IF NOT EXISTS client_profiles (
    profile_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    
    -- Emergency contact information
    emergency_contact_name VA<PERSON>HAR(255),
    emergency_contact_phone VARCHAR(20),
    emergency_contact_relationship VARCHAR(100),
    
    -- Address information
    address_line1 VARCHAR(255),
    address_line2 VARCHAR(255),
    city VARCHAR(100),
    province VARCHAR(50),
    postal_code VARCHAR(10),
    
    -- Contact information
    phone VARCHAR(20),
    secondary_phone VARCHAR(20),
    
    -- Preferences
    preferred_language VARCHAR(10) DEFAULT 'en',
    timezone VARCHAR(50) DEFAULT 'America/Toronto',
    communication_preferences JSONB DEFAULT '{"email": true, "sms": true, "push": true}'::jsonb,
    
    -- Notes
    internal_notes TEXT,
    
    -- Timestamps
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    
    -- Constraints
    CONSTRAINT unique_user_client_profile UNIQUE (user_id),
    CONSTRAINT valid_province CHECK (province IN ('ON', 'QC', 'BC', 'AB', 'MB', 'SK', 'NS', 'NB', 'NL', 'PE', 'NT', 'YT', 'NU')),
    CONSTRAINT valid_language CHECK (preferred_language IN ('en', 'fr'))
);

-- Create indexes for performance
CREATE INDEX idx_client_profiles_user_id ON client_profiles(user_id);
CREATE INDEX idx_client_profiles_postal_code ON client_profiles(postal_code);
CREATE INDEX idx_client_profiles_city ON client_profiles(city);
CREATE INDEX idx_client_profiles_deleted_at ON client_profiles(deleted_at) WHERE deleted_at IS NULL;

-- Create trigger to update updated_at timestamp
CREATE TRIGGER update_client_profiles_updated_at
    BEFORE UPDATE ON client_profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE client_profiles IS 'Stores extended profile information for client users';
COMMENT ON COLUMN client_profiles.profile_id IS 'Unique identifier for the client profile';
COMMENT ON COLUMN client_profiles.user_id IS 'Reference to the user account';
COMMENT ON COLUMN client_profiles.emergency_contact_name IS 'Name of emergency contact';
COMMENT ON COLUMN client_profiles.emergency_contact_phone IS 'Phone number of emergency contact';
COMMENT ON COLUMN client_profiles.emergency_contact_relationship IS 'Relationship to client (e.g., spouse, parent, sibling)';
COMMENT ON COLUMN client_profiles.communication_preferences IS 'JSON object storing communication channel preferences';
COMMENT ON COLUMN client_profiles.internal_notes IS 'Internal notes visible only to managers';