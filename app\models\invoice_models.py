"""
Invoice domain models for billing system.
Handles client invoices and invoice items.
"""

from datetime import datetime, date, timedelta
from decimal import Decimal
from typing import Optional, List, Dict, Any
from enum import Enum

from pydantic import BaseModel, Field, field_validator, computed_field


class PaymentStatus(str, Enum):
    """Payment status enumeration."""
    PENDING = "pending"
    PROCESSING = "processing"
    PAID = "paid"
    FAILED = "failed"
    REFUNDED = "refunded"
    CANCELLED = "cancelled"


class ItemType(str, Enum):
    """Invoice item type enumeration."""
    SERVICE = "service"
    TRANSPORT = "transport"
    MATERIAL = "material"
    OTHER = "other"


class InvoiceItem(BaseModel):
    """Domain model for invoice line items."""
    item_id: Optional[int] = None
    invoice_id: Optional[int] = None
    appointment_id: Optional[int] = None
    
    # Item details
    item_type: ItemType
    description: str
    quantity: Decimal = Field(default=Decimal("1.00"), ge=0)
    unit_price: Decimal = Field(ge=0)
    amount: Decimal = Field(ge=0)
    
    # Surge pricing tracking
    surge_multiplier: Decimal = Field(default=Decimal("1.00"), ge=1)
    surge_amount: Decimal = Field(default=Decimal("0.00"), ge=0)
    
    created_at: Optional[datetime] = None
    
    @field_validator("amount", mode='before')
    @classmethod
    def calculate_amount(cls, v, info):
        """Calculate amount if not provided."""
        if v is None or v == 0:
            quantity = info.data.get("quantity", Decimal("1.00"))
            unit_price = info.data.get("unit_price", Decimal("0.00"))
            surge_multiplier = info.data.get("surge_multiplier", Decimal("1.00"))
            
            base_amount = quantity * unit_price
            surge_amount = base_amount * (surge_multiplier - 1)
            
            return base_amount + surge_amount
        return v
    
    class Config:
        json_encoders = {
            Decimal: lambda v: float(v),
            datetime: lambda v: v.isoformat() if v else None,
            date: lambda v: v.isoformat() if v else None
        }


class Invoice(BaseModel):
    """Domain model for client invoices."""
    invoice_id: Optional[int] = None
    client_id: int
    invoice_number: Optional[str] = None
    invoice_date: date
    due_date: date
    
    # Financial details
    subtotal: Decimal = Field(default=Decimal("0.00"), ge=0)
    tax_amount: Decimal = Field(default=Decimal("0.00"), ge=0)
    total_amount: Decimal = Field(default=Decimal("0.00"), ge=0)
    status: PaymentStatus = PaymentStatus.PENDING
    
    # Payment tracking
    paid_by_client_id: Optional[int] = None
    paid_date: Optional[datetime] = None
    payment_method: Optional[str] = None
    stripe_payment_intent_id: Optional[str] = None
    
    # Subscription related
    is_subscription_deduction: bool = False
    subscription_id: Optional[int] = None
    
    # Metadata
    notes: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    deleted_at: Optional[datetime] = None
    
    # Related items
    items: List[InvoiceItem] = Field(default_factory=list)
    
    @field_validator("due_date", mode='before')
    @classmethod
    def set_due_date(cls, v, info):
        """Set default due date to 30 days from invoice date if not provided."""
        if v is None:
            invoice_date = info.data.get("invoice_date")
            if invoice_date:
                return invoice_date + timedelta(days=30)
        return v
    
    @computed_field
    @property
    def is_paid(self) -> bool:
        """Check if invoice is paid."""
        return self.status == PaymentStatus.PAID
    
    @computed_field
    @property
    def is_overdue(self) -> bool:
        """Check if invoice is overdue."""
        if self.status == PaymentStatus.PENDING and self.due_date < date.today():
            return True
        return False
    
    @computed_field
    @property
    def days_overdue(self) -> int:
        """Calculate days overdue."""
        if self.is_overdue:
            return (date.today() - self.due_date).days
        return 0
    
    def add_item(self, item: InvoiceItem) -> None:
        """Add an item to the invoice and recalculate totals."""
        self.items.append(item)
        self.recalculate_totals()
    
    def recalculate_totals(self) -> None:
        """Recalculate invoice totals based on items."""
        self.subtotal = sum(item.amount for item in self.items)
        self.total_amount = self.subtotal + self.tax_amount
    
    def mark_as_paid(self, paid_by_client_id: Optional[int] = None, 
                     payment_method: str = "stripe") -> None:
        """Mark invoice as paid."""
        self.status = PaymentStatus.PAID
        self.paid_date = datetime.utcnow()
        self.payment_method = payment_method
        if paid_by_client_id:
            self.paid_by_client_id = paid_by_client_id
    
    def to_email_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for email templates."""
        return {
            "invoice_number": self.invoice_number,
            "invoice_date": self.invoice_date.strftime("%B %d, %Y"),
            "due_date": self.due_date.strftime("%B %d, %Y"),
            "total_amount": f"${self.total_amount:.2f}",
            "is_overdue": self.is_overdue,
            "days_overdue": self.days_overdue,
            "items": [
                {
                    "description": item.description,
                    "quantity": float(item.quantity),
                    "unit_price": f"${item.unit_price:.2f}",
                    "amount": f"${item.amount:.2f}"
                }
                for item in self.items
            ]
        }
    
    class Config:
        json_encoders = {
            Decimal: lambda v: float(v),
            datetime: lambda v: v.isoformat() if v else None,
            date: lambda v: v.isoformat() if v else None
        }


class InvoiceGenerationResult(BaseModel):
    """Result of invoice generation process."""
    invoices_created: int = 0
    appointments_processed: int = 0
    clients_invoiced: List[int] = Field(default_factory=list)
    errors: List[str] = Field(default_factory=list)
    total_amount: Decimal = Field(default=Decimal("0.00"))
    
    def add_invoice(self, invoice: Invoice) -> None:
        """Add an invoice to the result."""
        self.invoices_created += 1
        self.clients_invoiced.append(invoice.client_id)
        self.total_amount += invoice.total_amount
    
    def add_error(self, error: str) -> None:
        """Add an error to the result."""
        self.errors.append(error)
    
    class Config:
        json_encoders = {
            Decimal: lambda v: float(v)
        }


class InvoiceFilter(BaseModel):
    """Filter criteria for invoice queries."""
    client_id: Optional[int] = None
    status: Optional[PaymentStatus] = None
    date_from: Optional[date] = None
    date_to: Optional[date] = None
    is_overdue: Optional[bool] = None
    min_amount: Optional[Decimal] = None
    max_amount: Optional[Decimal] = None
    paid_by_client_id: Optional[int] = None
    limit: int = Field(default=100, ge=1, le=1000)
    offset: int = Field(default=0, ge=0)