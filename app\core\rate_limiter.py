"""
Rate limiting implementation for API endpoints.
Uses in-memory storage with optional Redis support.
"""

from typing import Dict, Optional
from datetime import datetime, timedelta
import asyncio
from collections import defaultdict
import time

class RateLimiter:
    """Simple rate limiter using in-memory storage."""
    
    def __init__(self):
        # Dictionary to store request counts
        # Format: {client_id: [(timestamp1, count1), (timestamp2, count2), ...]}
        self._request_counts: Dict[str, list] = defaultdict(list)
        self._lock = asyncio.Lock()
        
        # Start cleanup task
        asyncio.create_task(self._cleanup_old_entries())
    
    async def check_rate_limit(
        self,
        client_id: str,
        max_requests: int,
        window_seconds: int
    ) -> bool:
        """
        Check if a client has exceeded the rate limit.
        
        Args:
            client_id: Unique identifier for the client
            max_requests: Maximum number of requests allowed
            window_seconds: Time window in seconds
            
        Returns:
            True if request is allowed, False if rate limit exceeded
        """
        async with self._lock:
            current_time = time.time()
            window_start = current_time - window_seconds
            
            # Get or create request list for client
            requests = self._request_counts[client_id]
            
            # Remove old entries outside the window
            requests = [
                (timestamp, count) 
                for timestamp, count in requests 
                if timestamp > window_start
            ]
            
            # Count total requests in window
            total_requests = sum(count for _, count in requests)
            
            # Check if limit exceeded
            if total_requests >= max_requests:
                self._request_counts[client_id] = requests
                return False
            
            # Add new request
            # Group by second to avoid too many entries
            current_second = int(current_time)
            if requests and requests[-1][0] == current_second:
                # Increment count for current second
                requests[-1] = (current_second, requests[-1][1] + 1)
            else:
                # Add new entry
                requests.append((current_second, 1))
            
            self._request_counts[client_id] = requests
            return True
    
    async def get_remaining_requests(
        self,
        client_id: str,
        max_requests: int,
        window_seconds: int
    ) -> int:
        """Get the number of remaining requests for a client."""
        async with self._lock:
            current_time = time.time()
            window_start = current_time - window_seconds
            
            requests = self._request_counts.get(client_id, [])
            
            # Count requests in window
            total_requests = sum(
                count for timestamp, count in requests 
                if timestamp > window_start
            )
            
            return max(0, max_requests - total_requests)
    
    async def reset_client(self, client_id: str) -> None:
        """Reset rate limit for a specific client."""
        async with self._lock:
            if client_id in self._request_counts:
                del self._request_counts[client_id]
    
    async def _cleanup_old_entries(self) -> None:
        """Periodically clean up old entries to prevent memory leaks."""
        while True:
            try:
                await asyncio.sleep(3600)  # Run every hour
                
                async with self._lock:
                    current_time = time.time()
                    max_window = 86400  # Keep data for max 24 hours
                    cutoff_time = current_time - max_window
                    
                    # Clean up old entries
                    for client_id in list(self._request_counts.keys()):
                        requests = self._request_counts[client_id]
                        requests = [
                            (timestamp, count) 
                            for timestamp, count in requests 
                            if timestamp > cutoff_time
                        ]
                        
                        if requests:
                            self._request_counts[client_id] = requests
                        else:
                            del self._request_counts[client_id]
                            
            except Exception:
                # Don't crash the cleanup task
                pass


class RedisRateLimiter:
    """
    Redis-based rate limiter for distributed systems.
    This is a placeholder for future implementation.
    """
    
    def __init__(self, redis_url: str):
        self.redis_url = redis_url
        # TODO: Initialize Redis connection
    
    async def check_rate_limit(
        self,
        client_id: str,
        max_requests: int,
        window_seconds: int
    ) -> bool:
        """Check rate limit using Redis."""
        # TODO: Implement Redis-based rate limiting
        # This would use Redis INCR with TTL for atomic operations
        raise NotImplementedError("Redis rate limiter not implemented yet")