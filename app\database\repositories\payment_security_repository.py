"""
Repository for payment security and PCI compliance operations.
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from decimal import Decimal

from app.database.repositories.base import BaseRepository

logger = logging.getLogger(__name__)


class PaymentSecurityRepository(BaseRepository):
    """Repository for secure payment operations and audit logging."""
    
    async def create_secure_payment_record(
        self,
        payment_intent_id: str,
        client_id: int,
        amount: Decimal,
        currency: str,
        risk_assessment: Dict[str, Any],
        idempotency_key: str,
        encrypted_metadata: str
    ) -> Dict[str, Any]:
        """Create secure payment record with encryption."""
        try:
            query = """
                INSERT INTO secure_payment_records (
                    payment_intent_id, client_id, amount, currency,
                    risk_score, risk_level, risk_flags, idempotency_key,
                    encrypted_metadata, status, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                ) RETURNING secure_payment_id, payment_intent_id, status, created_at
            """
            
            params = (
                payment_intent_id, client_id, amount, currency,
                risk_assessment['risk_score'],
                risk_assessment['risk_level'],
                ','.join(risk_assessment['risk_flags']),
                idempotency_key, encrypted_metadata,
                'pending', datetime.now()
            )
            
            result = await self._fetchone(query, params)
            return dict(result)
            
        except Exception as e:
            logger.error(f"Error creating secure payment record: {e}")
            raise
    
    async def log_payment_attempt(
        self,
        client_id: int,
        operation: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> None:
        """Log payment attempt for rate limiting."""
        try:
            query = """
                INSERT INTO payment_attempts (
                    client_id, operation, ip_address, user_agent,
                    attempted_at
                ) VALUES (
                    %s, %s, %s, %s, %s
                )
            """
            
            params = (
                client_id, operation, ip_address, user_agent,
                datetime.now()
            )
            
            await self._execute(query, params)
            
        except Exception as e:
            logger.error(f"Error logging payment attempt: {e}")
            raise
    
    async def get_recent_payment_attempts(
        self,
        client_id: int,
        operation: str,
        time_window: timedelta
    ) -> List[Dict[str, Any]]:
        """Get recent payment attempts for rate limiting."""
        try:
            query = """
                SELECT * FROM payment_attempts
                WHERE client_id = %s
                AND operation = %s
                AND attempted_at >= %s
                ORDER BY attempted_at DESC
            """
            
            cutoff_time = datetime.now() - time_window
            params = (client_id, operation, cutoff_time)
            
            results = await self._fetchall(query, params)
            return [dict(row) for row in results]
            
        except Exception as e:
            logger.error(f"Error getting payment attempts: {e}")
            raise
    
    async def get_recent_payments(
        self,
        client_id: int,
        time_window: timedelta
    ) -> List[Dict[str, Any]]:
        """Get recent payments for velocity checks."""
        try:
            query = """
                SELECT amount, currency, created_at
                FROM secure_payment_records
                WHERE client_id = %s
                AND created_at >= %s
                AND status IN ('pending', 'succeeded')
                ORDER BY created_at DESC
            """
            
            cutoff_time = datetime.now() - time_window
            params = (client_id, cutoff_time)
            
            results = await self._fetchall(query, params)
            return [dict(row) for row in results]
            
        except Exception as e:
            logger.error(f"Error getting recent payments: {e}")
            raise
    
    async def get_failed_payment_attempts(
        self,
        client_id: int,
        time_window: timedelta
    ) -> List[Dict[str, Any]]:
        """Get failed payment attempts for risk assessment."""
        try:
            query = """
                SELECT * FROM secure_payment_records
                WHERE client_id = %s
                AND created_at >= %s
                AND status = 'failed'
                ORDER BY created_at DESC
            """
            
            cutoff_time = datetime.now() - time_window
            params = (client_id, cutoff_time)
            
            results = await self._fetchall(query, params)
            return [dict(row) for row in results]
            
        except Exception as e:
            logger.error(f"Error getting failed payments: {e}")
            raise
    
    async def create_secure_payment_method(
        self,
        client_id: int,
        stripe_payment_method_id: str,
        card_last_four: str,
        card_brand: str,
        encrypted_metadata: str
    ) -> Dict[str, Any]:
        """Create secure payment method record."""
        try:
            query = """
                INSERT INTO secure_payment_methods (
                    client_id, stripe_payment_method_id,
                    card_last_four, card_brand, encrypted_metadata,
                    is_active, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s
                ) RETURNING secure_method_id, card_last_four, card_brand, is_default
            """
            
            params = (
                client_id, stripe_payment_method_id,
                card_last_four, card_brand, encrypted_metadata,
                True, datetime.now()
            )
            
            result = await self._fetchone(query, params)
            return dict(result)
            
        except Exception as e:
            logger.error(f"Error creating secure payment method: {e}")
            raise
    
    async def update_payment_status(
        self,
        payment_intent_id: str,
        status: str,
        completed_at: Optional[datetime] = None,
        failure_reason: Optional[str] = None
    ) -> None:
        """Update payment status."""
        try:
            query = """
                UPDATE secure_payment_records
                SET status = %s,
                    updated_at = %s
            """
            params = [status, datetime.now()]
            
            if completed_at:
                query += ", completed_at = %s"
                params.append(completed_at)
            
            if failure_reason:
                query += ", failure_reason = %s"
                params.append(failure_reason)
            
            query += " WHERE payment_intent_id = %s"
            params.append(payment_intent_id)
            
            await self._execute(query, params)
            
        except Exception as e:
            logger.error(f"Error updating payment status: {e}")
            raise
    
    async def log_failed_payment(
        self,
        payment_intent_id: str,
        client_id: Optional[int],
        failure_reason: Optional[str]
    ) -> None:
        """Log failed payment for risk assessment."""
        try:
            query = """
                INSERT INTO payment_failures (
                    payment_intent_id, client_id, failure_reason,
                    failed_at
                ) VALUES (
                    %s, %s, %s, %s
                )
            """
            
            params = (
                payment_intent_id, client_id,
                failure_reason, datetime.now()
            )
            
            await self._execute(query, params)
            
        except Exception as e:
            logger.error(f"Error logging failed payment: {e}")
            raise
    
    async def get_payment_audit_trail(
        self,
        payment_id: int
    ) -> List[Dict[str, Any]]:
        """Get complete audit trail for a payment."""
        try:
            query = """
                SELECT 
                    audit_id, event_type, event_description,
                    user_id, ip_address, user_agent,
                    encrypted_details, created_at
                FROM payment_audit_log
                WHERE secure_payment_id = %s
                ORDER BY created_at ASC
            """
            
            results = await self._fetchall(query, (payment_id,))
            return [dict(row) for row in results]
            
        except Exception as e:
            logger.error(f"Error getting payment audit trail: {e}")
            raise
    
    async def log_security_event(
        self,
        event_type: str,
        event_description: str,
        user_id: Optional[int] = None,
        payment_id: Optional[int] = None,
        ip_address: Optional[str] = None,
        encrypted_details: Optional[str] = None
    ) -> None:
        """Log security event to audit log."""
        try:
            query = """
                INSERT INTO payment_audit_log (
                    secure_payment_id, event_type, event_description,
                    user_id, ip_address, encrypted_details, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s
                )
            """
            
            params = (
                payment_id, event_type, event_description,
                user_id, ip_address, encrypted_details,
                datetime.now()
            )
            
            await self._execute(query, params)
            
        except Exception as e:
            logger.error(f"Error logging security event: {e}")
            raise
    
    async def get_security_metrics(
        self,
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, Any]:
        """Get security metrics for compliance reporting."""
        try:
            # Total transactions
            total_query = """
                SELECT COUNT(*) as total_transactions
                FROM secure_payment_records
                WHERE created_at BETWEEN %s AND %s
            """
            total_result = await self._fetchone(total_query, (start_date, end_date))
            
            # Secure transactions (completed with tokenization)
            secure_query = """
                SELECT COUNT(*) as secure_transactions
                FROM secure_payment_records
                WHERE created_at BETWEEN %s AND %s
                AND status = 'succeeded'
                AND payment_intent_id IS NOT NULL
            """
            secure_result = await self._fetchone(secure_query, (start_date, end_date))
            
            # Tokenized payments
            tokenized_query = """
                SELECT COUNT(DISTINCT client_id) as tokenized_payments
                FROM secure_payment_methods
                WHERE created_at BETWEEN %s AND %s
            """
            tokenized_result = await self._fetchone(tokenized_query, (start_date, end_date))
            
            # Encrypted records
            encrypted_query = """
                SELECT COUNT(*) as encrypted_records
                FROM secure_payment_records
                WHERE created_at BETWEEN %s AND %s
                AND encrypted_metadata IS NOT NULL
            """
            encrypted_result = await self._fetchone(encrypted_query, (start_date, end_date))
            
            # Rate limit violations
            rate_limit_query = """
                SELECT COUNT(*) as rate_limit_violations
                FROM payment_audit_log
                WHERE created_at BETWEEN %s AND %s
                AND event_type = 'rate_limit_exceeded'
            """
            rate_limit_result = await self._fetchone(rate_limit_query, (start_date, end_date))
            
            return {
                'total_transactions': total_result['total_transactions'],
                'secure_transactions': secure_result['secure_transactions'],
                'tokenized_payments': tokenized_result['tokenized_payments'],
                'encrypted_records': encrypted_result['encrypted_records'],
                'rate_limit_violations': rate_limit_result['rate_limit_violations']
            }
            
        except Exception as e:
            logger.error(f"Error getting security metrics: {e}")
            raise
    
    async def get_security_violations(
        self,
        start_date: datetime,
        end_date: datetime
    ) -> List[Dict[str, Any]]:
        """Get security violations for compliance reporting."""
        try:
            query = """
                SELECT 
                    event_type, event_description,
                    user_id, ip_address, created_at,
                    CASE 
                        WHEN event_type IN ('pci_violation_attempt', 'webhook_verification_failed')
                        THEN true 
                        ELSE false 
                    END as blocked
                FROM payment_audit_log
                WHERE created_at BETWEEN %s AND %s
                AND event_type IN (
                    'pci_violation_attempt',
                    'webhook_verification_failed',
                    'rate_limit_exceeded',
                    'payment_flagged_for_review'
                )
                ORDER BY created_at DESC
            """
            
            results = await self._fetchall(query, (start_date, end_date))
            
            violations = []
            for row in results:
                violations.append({
                    'event_type': row['event_type'],
                    'description': row['event_description'],
                    'user_id': row['user_id'],
                    'source_ip': row['ip_address'],
                    'timestamp': row['created_at'],
                    'blocked': row['blocked']
                })
            
            return violations
            
        except Exception as e:
            logger.error(f"Error getting security violations: {e}")
            raise