"""
Tu<PERSON> profile API endpoints.
"""

from typing import List, Optional, Dict, Any
from datetime import date

from fastapi import APIRouter, Depends, HTTPException, Query, status

from app.models.tutor_models import (
    TutorProfile,
    TutorAvailability,
    TutorTimeOff
)
from app.models.tutor_invitation_models import (
    TutorInvitation,
    TutorInvitationCreate,
    TutorInvitationWithInviter,
    TutorInvitationAccept
)
from app.models.auth_models import TokenData
from app.services.tutor_service import TutorService
from app.services.tutor_invitation_service import TutorInvitationService
from app.services.rate_limiting_service import RateLimitingService
from app.core.dependencies import (
    get_db_manager,
    get_current_user,
    check_rate_limit
)
from app.core.exceptions import (
    ResourceNotFoundError,
    ForbiddenError,
    ValidationError,
    BusinessLogicError
)
from app.config.database import DatabaseManager


router = APIRouter(prefix="/tutors", tags=["tutors"])


@router.post(
    "/",
    response_model=TutorProfile,
    status_code=status.HTTP_201_CREATED,
    summary="Create tutor profile",
    description="Create a new tutor profile for a user"
)
async def create_tutor_profile(
    profile_data: Dict[str, Any],
    current_user: TokenData = Depends(get_current_user),
    db_manager: DatabaseManager = Depends(get_db_manager),
    _: None = Depends(check_rate_limit)
):
    """Create a new tutor profile."""
    service = TutorService(db_manager)
    
    try:
        # Extract user_id from profile data
        user_id = profile_data.get("user_id")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="user_id is required"
            )
        
        profile = await service.create_profile(
            user_id=user_id,
            profile_data=profile_data,
            current_user_id=int(current_user.user_id),
            current_user_role=current_user.role
        )
        return profile
    except BusinessLogicError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )
    except (ValidationError, ForbiddenError) as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except ResourceNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )


@router.get(
    "/profile/{tutor_id}",
    response_model=TutorProfile,
    summary="Get tutor profile",
    description="Get a tutor profile by ID"
)
async def get_tutor_profile(
    tutor_id: int,
    current_user: TokenData = Depends(get_current_user),
    db_manager: DatabaseManager = Depends(get_db_manager)
):
    """Get a tutor profile."""
    service = TutorService(db_manager)
    
    try:
        profile = await service.get_profile(
            tutor_id=tutor_id,
            current_user_id=int(current_user.user_id),
            current_user_role=current_user.role
        )
        return profile
    except ResourceNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )


@router.get(
    "/user/{user_id}",
    response_model=Optional[TutorProfile],
    summary="Get tutor profile by user ID",
    description="Get a tutor profile by user ID"
)
async def get_tutor_profile_by_user(
    user_id: int,
    current_user: TokenData = Depends(get_current_user),
    db_manager: DatabaseManager = Depends(get_db_manager)
):
    """Get tutor profile by user ID."""
    service = TutorService(db_manager)
    
    profile = await service.get_profile_by_user_id(
        user_id=user_id,
        current_user_id=int(current_user.user_id),
        current_user_role=current_user.role
    )
    return profile


@router.put(
    "/profile/{tutor_id}",
    response_model=TutorProfile,
    summary="Update tutor profile",
    description="Update an existing tutor profile"
)
async def update_tutor_profile(
    tutor_id: int,
    update_data: Dict[str, Any],
    current_user: TokenData = Depends(get_current_user),
    db_manager: DatabaseManager = Depends(get_db_manager),
    _: None = Depends(check_rate_limit)
):
    """Update a tutor profile."""
    service = TutorService(db_manager)
    
    try:
        profile = await service.update_profile(
            tutor_id=tutor_id,
            update_data=update_data,
            current_user_id=int(current_user.user_id),
            current_user_role=current_user.role
        )
        return profile
    except ResourceNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except ForbiddenError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )


@router.put(
    "/profile/{tutor_id}/verification",
    response_model=TutorProfile,
    summary="Update tutor verification status",
    description="Update tutor verification status (managers only)"
)
async def update_tutor_verification(
    tutor_id: int,
    status: str,
    current_user: TokenData = Depends(get_current_user),
    db_manager: DatabaseManager = Depends(get_db_manager)
):
    """Update tutor verification status."""
    service = TutorService(db_manager)
    
    try:
        profile = await service.update_verification_status(
            tutor_id=tutor_id,
            status=status,
            current_user_id=int(current_user.user_id),
            current_user_role=current_user.role
        )
        return profile
    except ResourceNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except (ForbiddenError, ValidationError) as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )


@router.get(
    "/search",
    response_model=List[TutorProfile],
    summary="Search tutors",
    description="Search tutors by name, subject, or location"
)
async def search_tutors(
    q: Optional[str] = Query(None, description="Search query"),
    postal_code: Optional[str] = Query(None, description="Search by proximity to postal code"),
    subject: Optional[str] = Query(None, description="Filter by subject"),
    limit: int = Query(10, ge=1, le=50, description="Maximum results"),
    current_user: TokenData = Depends(get_current_user),
    db_manager: DatabaseManager = Depends(get_db_manager),
    _: None = Depends(check_rate_limit)
):
    """Search tutors."""
    service = TutorService(db_manager)
    
    tutors = await service.search_tutors(
        query=q,
        postal_code=postal_code,
        subject=subject,
        limit=limit,
        current_user_role=current_user.role
    )
    return tutors


@router.get(
    "/profile/{tutor_id}/availability",
    response_model=List[TutorAvailability],
    summary="Get tutor availability",
    description="Get tutor's weekly availability schedule"
)
async def get_tutor_availability(
    tutor_id: int,
    current_user: TokenData = Depends(get_current_user),
    db_manager: DatabaseManager = Depends(get_db_manager)
):
    """Get tutor availability."""
    service = TutorService(db_manager)
    
    try:
        availability = await service.get_availability(tutor_id)
        return availability
    except ResourceNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )


@router.put(
    "/profile/{tutor_id}/availability",
    response_model=List[TutorAvailability],
    summary="Update tutor availability",
    description="Update tutor's weekly availability schedule"
)
async def update_tutor_availability(
    tutor_id: int,
    availability_data: List[Dict[str, Any]],
    current_user: TokenData = Depends(get_current_user),
    db_manager: DatabaseManager = Depends(get_db_manager)
):
    """Update tutor availability."""
    service = TutorService(db_manager)
    
    try:
        availability = await service.update_availability(
            tutor_id=tutor_id,
            availability_data=availability_data,
            current_user_id=int(current_user.user_id),
            current_user_role=current_user.role
        )
        return availability
    except ResourceNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except ForbiddenError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )


@router.post(
    "/profile/{tutor_id}/time-off",
    response_model=TutorTimeOff,
    status_code=status.HTTP_201_CREATED,
    summary="Request time off",
    description="Request time off for a tutor"
)
async def request_time_off(
    tutor_id: int,
    start_date: date,
    end_date: date,
    reason: str,
    current_user: TokenData = Depends(get_current_user),
    db_manager: DatabaseManager = Depends(get_db_manager)
):
    """Request time off."""
    service = TutorService(db_manager)
    
    try:
        time_off = await service.request_time_off(
            tutor_id=tutor_id,
            start_date=start_date,
            end_date=end_date,
            reason=reason,
            current_user_id=int(current_user.user_id)
        )
        return time_off
    except ResourceNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except ForbiddenError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )


@router.put(
    "/time-off/{time_off_id}/approve",
    response_model=TutorTimeOff,
    summary="Approve/reject time off",
    description="Approve or reject time off request (managers only)"
)
async def approve_time_off(
    time_off_id: int,
    approved: bool,
    current_user: TokenData = Depends(get_current_user),
    db_manager: DatabaseManager = Depends(get_db_manager)
):
    """Approve or reject time off request."""
    service = TutorService(db_manager)
    
    try:
        time_off = await service.approve_time_off(
            time_off_id=time_off_id,
            approved=approved,
            current_user_id=int(current_user.user_id),
            current_user_role=current_user.role
        )
        return time_off
    except ResourceNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except ForbiddenError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )