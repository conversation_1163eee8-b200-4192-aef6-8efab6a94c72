import asyncpg
import logging
from typing import Optional
from contextlib import asynccontextmanager

from app.config.settings import settings
from app.database.security import DatabaseSecurity, SecurityMonitor

logger = logging.getLogger(__name__)

# Global connection pool
_pool: Optional[asyncpg.Pool] = None


async def get_database_pool() -> asyncpg.Pool:
    """Get or create the secure database connection pool."""
    global _pool
    
    if _pool is None:
        try:
            # Get secure connection string with SSL encryption
            secure_connection_string = DatabaseSecurity.get_secure_connection_string()
            
            # Get secure connection limits
            limits = DatabaseSecurity.get_connection_limits()
            
            # Monitor connection attempt
            SecurityMonitor.monitor_connection_attempts({
                "host": settings.DATABASE_HOST,
                "database": settings.DATABASE_NAME,
                "ssl_enabled": not settings.DEBUG
            })
            
            _pool = await asyncpg.create_pool(
                secure_connection_string,
                min_size=limits["min_size"],
                max_size=limits["max_size"],
                max_queries=limits["max_queries"],
                max_inactive_connection_lifetime=limits["max_inactive_connection_lifetime"],
                timeout=limits["timeout"],
                command_timeout=limits["command_timeout"],
                server_settings={
                    'jit': 'off',  # Disable JIT for better connection performance
                    'application_name': 'tutoraide_api',
                    'log_statement': 'none',  # Don't log statements for security
                    'log_min_duration_statement': '1000'  # Only log slow queries
                }
            )
            
            # Apply security indexes after pool creation
            await _apply_security_indexes()
            
            logger.info("Secure database connection pool created successfully")
        except Exception as e:
            logger.error(f"Failed to create secure database pool: {e}")
            raise
    
    return _pool


async def _apply_security_indexes():
    """Apply security-focused database indexes."""
    if _pool is None:
        return
        
    security_indexes = DatabaseSecurity.create_security_indexes()
    
    async with _pool.acquire() as connection:
        for index_sql in security_indexes:
            try:
                await connection.execute(index_sql)
                logger.debug(f"Applied security index: {index_sql[:50]}...")
            except Exception as e:
                # Index creation might fail if already exists or other issues
                logger.warning(f"Failed to create index: {e}")
                continue
    
    logger.info("Security indexes applied successfully")


async def close_database_pool():
    """Close the database connection pool."""
    global _pool
    
    if _pool:
        await _pool.close()
        _pool = None
        logger.info("Database connection pool closed")


@asynccontextmanager
async def get_db_connection():
    """Context manager for database connections."""
    pool = await get_database_pool()
    
    async with pool.acquire() as connection:
        try:
            yield connection
        except Exception as e:
            logger.error(f"Database connection error: {e}")
            raise


@asynccontextmanager
async def get_db_transaction():
    """Context manager for database transactions."""
    pool = await get_database_pool()
    
    async with pool.acquire() as connection:
        async with connection.transaction():
            try:
                yield connection
            except Exception as e:
                logger.error(f"Database transaction error: {e}")
                raise


# Dependency for FastAPI
async def get_db():
    """FastAPI dependency for database connections."""
    async with get_db_connection() as conn:
        yield conn


class DatabaseManager:
    """Database manager for handling connections and transactions."""
    
    def __init__(self):
        """Initialize database manager."""
        pass
    
    async def acquire(self):
        """Get a database connection from the pool."""
        pool = await get_database_pool()
        return pool.acquire()
    
    async def transaction(self):
        """Get a transaction context manager."""
        return get_db_transaction()
    
    async def execute(self, query: str, *args):
        """Execute a query with connection management."""
        async with get_db_connection() as conn:
            return await conn.execute(query, *args)
    
    async def fetch(self, query: str, *args):
        """Fetch multiple rows with connection management."""
        async with get_db_connection() as conn:
            return await conn.fetch(query, *args)
    
    async def fetchrow(self, query: str, *args):
        """Fetch a single row with connection management."""
        async with get_db_connection() as conn:
            return await conn.fetchrow(query, *args)
    
    async def fetchval(self, query: str, *args):
        """Fetch a single value with connection management."""
        async with get_db_connection() as conn:
            return await conn.fetchval(query, *args)