-- Migration: Tutor Profile Enhancements
-- Description: Complete tutor profile system with verification, documents, and performance tracking

-- <PERSON><PERSON> Documents Table
CREATE TABLE IF NOT EXISTS tutor_documents (
    document_id SERIAL PRIMARY KEY,
    tutor_id INTEGER NOT NULL REFERENCES tutor_profiles(tutor_id) ON DELETE CASCADE,
    document_type VARCHAR(50) NOT NULL CHECK (document_type IN ('resume', 'certification', 'degree', 'transcript', 'background_check', 'reference', 'id_verification', 'other')),
    document_name VARCHAR(255) NOT NULL,
    file_url VARCHAR(500) NOT NULL,
    file_size INTEGER,
    mime_type VARCHAR(100),
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    uploaded_by INTEGER REFERENCES users(user_id),
    verified_at TIMESTAMP WITH TIME ZONE,
    verified_by INTEGER REFERENCES users(user_id),
    verification_status VARCHAR(20) DEFAULT 'pending' CHECK (verification_status IN ('pending', 'verified', 'rejected')),
    verification_notes TEXT,
    expiry_date DATE,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Tutor Education History
CREATE TABLE IF NOT EXISTS tutor_education (
    education_id SERIAL PRIMARY KEY,
    tutor_id INTEGER NOT NULL REFERENCES tutor_profiles(tutor_id) ON DELETE CASCADE,
    institution VARCHAR(200) NOT NULL,
    degree VARCHAR(100) NOT NULL,
    field_of_study VARCHAR(200) NOT NULL,
    start_date DATE,
    graduation_date DATE,
    is_current BOOLEAN DEFAULT FALSE,
    gpa DECIMAL(3, 2),
    honors_awards TEXT,
    relevant_coursework TEXT[] DEFAULT '{}',
    thesis_title VARCHAR(500),
    advisor_name VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    CONSTRAINT education_date_check CHECK (start_date IS NULL OR graduation_date IS NULL OR start_date <= graduation_date)
);

-- Tutor Work Experience
CREATE TABLE IF NOT EXISTS tutor_experience (
    experience_id SERIAL PRIMARY KEY,
    tutor_id INTEGER NOT NULL REFERENCES tutor_profiles(tutor_id) ON DELETE CASCADE,
    organization VARCHAR(200) NOT NULL,
    position VARCHAR(100) NOT NULL,
    employment_type VARCHAR(50) CHECK (employment_type IN ('full_time', 'part_time', 'contract', 'internship', 'volunteer')),
    start_date DATE NOT NULL,
    end_date DATE,
    is_current BOOLEAN DEFAULT FALSE,
    location VARCHAR(200),
    description TEXT,
    achievements TEXT[] DEFAULT '{}',
    skills_used TEXT[] DEFAULT '{}',
    students_taught INTEGER,
    age_groups_taught TEXT[] DEFAULT '{}',
    subjects_taught TEXT[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    CONSTRAINT experience_date_check CHECK (end_date IS NULL OR start_date <= end_date)
);

-- Tutor Specializations
CREATE TABLE IF NOT EXISTS tutor_specializations (
    specialization_id SERIAL PRIMARY KEY,
    tutor_id INTEGER NOT NULL REFERENCES tutor_profiles(tutor_id) ON DELETE CASCADE,
    subject_area VARCHAR(100) NOT NULL,
    level VARCHAR(50) NOT NULL CHECK (level IN ('elementary', 'middle_school', 'high_school', 'college', 'university', 'adult', 'all')),
    years_experience INTEGER DEFAULT 0,
    certification VARCHAR(200),
    certification_date DATE,
    certification_expiry DATE,
    proficiency_level VARCHAR(20) DEFAULT 'intermediate' CHECK (proficiency_level IN ('beginner', 'intermediate', 'advanced', 'expert')),
    max_students_per_session INTEGER DEFAULT 1,
    preferred_age_groups TEXT[] DEFAULT '{}',
    teaching_methods TEXT[] DEFAULT '{}',
    success_stories TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_tutor_subject_level UNIQUE (tutor_id, subject_area, level)
);

-- Tutor Verification Log
CREATE TABLE IF NOT EXISTS tutor_verification_log (
    log_id SERIAL PRIMARY KEY,
    tutor_id INTEGER NOT NULL REFERENCES tutor_profiles(tutor_id) ON DELETE CASCADE,
    verification_step VARCHAR(50) NOT NULL,
    previous_status VARCHAR(50),
    new_status VARCHAR(50) NOT NULL,
    verified_by INTEGER REFERENCES users(user_id),
    verification_method VARCHAR(50),
    notes TEXT,
    documents_reviewed TEXT[] DEFAULT '{}',
    issues_found TEXT[] DEFAULT '{}',
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Tutor Performance Metrics
CREATE TABLE IF NOT EXISTS tutor_performance_metrics (
    metric_id SERIAL PRIMARY KEY,
    tutor_id INTEGER NOT NULL REFERENCES tutor_profiles(tutor_id) ON DELETE CASCADE,
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    total_sessions INTEGER DEFAULT 0,
    completed_sessions INTEGER DEFAULT 0,
    cancelled_sessions INTEGER DEFAULT 0,
    no_show_sessions INTEGER DEFAULT 0,
    total_students INTEGER DEFAULT 0,
    new_students INTEGER DEFAULT 0,
    returning_students INTEGER DEFAULT 0,
    total_hours DECIMAL(10, 2) DEFAULT 0,
    avg_session_duration DECIMAL(5, 2),
    avg_rating DECIMAL(3, 2),
    total_ratings INTEGER DEFAULT 0,
    five_star_ratings INTEGER DEFAULT 0,
    four_star_ratings INTEGER DEFAULT 0,
    three_star_ratings INTEGER DEFAULT 0,
    two_star_ratings INTEGER DEFAULT 0,
    one_star_ratings INTEGER DEFAULT 0,
    on_time_rate DECIMAL(5, 2),
    response_rate DECIMAL(5, 2),
    response_time_hours DECIMAL(5, 2),
    revenue_generated DECIMAL(10, 2) DEFAULT 0,
    bonuses_earned DECIMAL(10, 2) DEFAULT 0,
    complaints_received INTEGER DEFAULT 0,
    compliments_received INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_tutor_period UNIQUE (tutor_id, period_start, period_end),
    CONSTRAINT metric_period_check CHECK (period_start <= period_end)
);

-- Add new columns to tutor_profiles
ALTER TABLE tutor_profiles 
ADD COLUMN IF NOT EXISTS headline VARCHAR(200),
ADD COLUMN IF NOT EXISTS professional_summary TEXT,
ADD COLUMN IF NOT EXISTS teaching_philosophy TEXT,
ADD COLUMN IF NOT EXISTS linkedin_url VARCHAR(500),
ADD COLUMN IF NOT EXISTS website_url VARCHAR(500),
ADD COLUMN IF NOT EXISTS years_of_experience INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS total_students_taught INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS preferred_student_age_min INTEGER,
ADD COLUMN IF NOT EXISTS preferred_student_age_max INTEGER,
ADD COLUMN IF NOT EXISTS accepts_special_needs BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS special_needs_experience TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS languages_spoken TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS availability_notes TEXT,
ADD COLUMN IF NOT EXISTS background_check_status VARCHAR(50) DEFAULT 'not_started',
ADD COLUMN IF NOT EXISTS background_check_date DATE,
ADD COLUMN IF NOT EXISTS background_check_expiry DATE,
ADD COLUMN IF NOT EXISTS profile_completeness INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS profile_views INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_active_date DATE,
ADD COLUMN IF NOT EXISTS is_featured BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS featured_until DATE;

-- Create indexes
CREATE INDEX idx_tutor_documents_tutor_id ON tutor_documents(tutor_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_tutor_documents_type ON tutor_documents(document_type) WHERE deleted_at IS NULL;
CREATE INDEX idx_tutor_documents_verification_status ON tutor_documents(verification_status) WHERE deleted_at IS NULL;
CREATE INDEX idx_tutor_education_tutor_id ON tutor_education(tutor_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_tutor_experience_tutor_id ON tutor_experience(tutor_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_tutor_specializations_tutor_id ON tutor_specializations(tutor_id);
CREATE INDEX idx_tutor_specializations_subject ON tutor_specializations(subject_area);
CREATE INDEX idx_tutor_verification_log_tutor_id ON tutor_verification_log(tutor_id);
CREATE INDEX idx_tutor_performance_metrics_tutor_id ON tutor_performance_metrics(tutor_id);
CREATE INDEX idx_tutor_performance_metrics_period ON tutor_performance_metrics(period_start, period_end);

-- Create triggers
CREATE TRIGGER update_tutor_documents_modtime BEFORE UPDATE ON tutor_documents FOR EACH ROW EXECUTE FUNCTION update_modified_column();
CREATE TRIGGER update_tutor_education_modtime BEFORE UPDATE ON tutor_education FOR EACH ROW EXECUTE FUNCTION update_modified_column();
CREATE TRIGGER update_tutor_experience_modtime BEFORE UPDATE ON tutor_experience FOR EACH ROW EXECUTE FUNCTION update_modified_column();
CREATE TRIGGER update_tutor_specializations_modtime BEFORE UPDATE ON tutor_specializations FOR EACH ROW EXECUTE FUNCTION update_modified_column();
CREATE TRIGGER update_tutor_performance_metrics_modtime BEFORE UPDATE ON tutor_performance_metrics FOR EACH ROW EXECUTE FUNCTION update_modified_column();

-- Function to calculate tutor profile completeness
CREATE OR REPLACE FUNCTION calculate_tutor_profile_completeness(p_tutor_id INTEGER)
RETURNS INTEGER AS $$
DECLARE
    v_completeness INTEGER := 0;
    v_has_photo BOOLEAN;
    v_has_bio BOOLEAN;
    v_has_education BOOLEAN;
    v_has_experience BOOLEAN;
    v_has_specialization BOOLEAN;
    v_has_documents BOOLEAN;
    v_has_availability BOOLEAN;
    v_has_rates BOOLEAN;
    v_has_background_check BOOLEAN;
BEGIN
    -- Basic profile (20%)
    SELECT 
        CASE WHEN profile_photo_url IS NOT NULL AND profile_photo_url != '' THEN TRUE ELSE FALSE END,
        CASE WHEN bio IS NOT NULL AND LENGTH(bio) > 50 THEN TRUE ELSE FALSE END
    INTO v_has_photo, v_has_bio
    FROM tutor_profiles 
    WHERE tutor_id = p_tutor_id;
    
    IF v_has_photo THEN v_completeness := v_completeness + 10; END IF;
    IF v_has_bio THEN v_completeness := v_completeness + 10; END IF;
    
    -- Education (15%)
    SELECT EXISTS(SELECT 1 FROM tutor_education WHERE tutor_id = p_tutor_id AND deleted_at IS NULL)
    INTO v_has_education;
    IF v_has_education THEN v_completeness := v_completeness + 15; END IF;
    
    -- Experience (15%)
    SELECT EXISTS(SELECT 1 FROM tutor_experience WHERE tutor_id = p_tutor_id AND deleted_at IS NULL)
    INTO v_has_experience;
    IF v_has_experience THEN v_completeness := v_completeness + 15; END IF;
    
    -- Specializations (15%)
    SELECT EXISTS(SELECT 1 FROM tutor_specializations WHERE tutor_id = p_tutor_id)
    INTO v_has_specialization;
    IF v_has_specialization THEN v_completeness := v_completeness + 15; END IF;
    
    -- Documents (10%)
    SELECT EXISTS(SELECT 1 FROM tutor_documents WHERE tutor_id = p_tutor_id AND verification_status = 'verified' AND deleted_at IS NULL)
    INTO v_has_documents;
    IF v_has_documents THEN v_completeness := v_completeness + 10; END IF;
    
    -- Availability (10%)
    SELECT EXISTS(SELECT 1 FROM tutor_availability WHERE tutor_id = p_tutor_id)
    INTO v_has_availability;
    IF v_has_availability THEN v_completeness := v_completeness + 10; END IF;
    
    -- Rates (10%)
    SELECT EXISTS(SELECT 1 FROM tutor_service_rates WHERE tutor_id = p_tutor_id)
    INTO v_has_rates;
    IF v_has_rates THEN v_completeness := v_completeness + 10; END IF;
    
    -- Background check (5%)
    SELECT CASE WHEN background_check_status = 'completed' THEN TRUE ELSE FALSE END
    INTO v_has_background_check
    FROM tutor_profiles 
    WHERE tutor_id = p_tutor_id;
    IF v_has_background_check THEN v_completeness := v_completeness + 5; END IF;
    
    RETURN v_completeness;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update profile completeness
CREATE OR REPLACE FUNCTION update_tutor_profile_completeness()
RETURNS TRIGGER AS $$
DECLARE
    v_tutor_id INTEGER;
BEGIN
    -- Get the tutor_id from the appropriate table
    IF TG_TABLE_NAME = 'tutor_profiles' THEN
        v_tutor_id := NEW.tutor_id;
    ELSE
        v_tutor_id := NEW.tutor_id;
    END IF;
    
    -- Update the profile completeness
    UPDATE tutor_profiles 
    SET profile_completeness = calculate_tutor_profile_completeness(v_tutor_id),
        updated_at = CURRENT_TIMESTAMP
    WHERE tutor_id = v_tutor_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for all related tables
CREATE TRIGGER update_completeness_on_tutor_education_change 
AFTER INSERT OR UPDATE OR DELETE ON tutor_education 
FOR EACH ROW EXECUTE FUNCTION update_tutor_profile_completeness();

CREATE TRIGGER update_completeness_on_tutor_experience_change 
AFTER INSERT OR UPDATE OR DELETE ON tutor_experience 
FOR EACH ROW EXECUTE FUNCTION update_tutor_profile_completeness();

CREATE TRIGGER update_completeness_on_tutor_specializations_change 
AFTER INSERT OR UPDATE OR DELETE ON tutor_specializations 
FOR EACH ROW EXECUTE FUNCTION update_tutor_profile_completeness();

CREATE TRIGGER update_completeness_on_tutor_documents_change 
AFTER INSERT OR UPDATE OR DELETE ON tutor_documents 
FOR EACH ROW EXECUTE FUNCTION update_tutor_profile_completeness();

-- View for tutor verification status
CREATE OR REPLACE VIEW tutor_verification_status AS
SELECT 
    tp.tutor_id,
    tp.user_id,
    u.first_name || ' ' || u.last_name as tutor_name,
    tp.verification_status,
    tp.background_check_status,
    tp.background_check_date,
    tp.profile_completeness,
    COUNT(DISTINCT td.document_id) FILTER (WHERE td.verification_status = 'verified') as verified_documents,
    COUNT(DISTINCT td.document_id) FILTER (WHERE td.verification_status = 'pending') as pending_documents,
    COUNT(DISTINCT te.education_id) as education_entries,
    COUNT(DISTINCT tex.experience_id) as experience_entries,
    COUNT(DISTINCT ts.specialization_id) as specializations,
    MAX(tvl.timestamp) as last_verification_action
FROM tutor_profiles tp
JOIN users u ON tp.user_id = u.user_id
LEFT JOIN tutor_documents td ON tp.tutor_id = td.tutor_id AND td.deleted_at IS NULL
LEFT JOIN tutor_education te ON tp.tutor_id = te.tutor_id AND te.deleted_at IS NULL
LEFT JOIN tutor_experience tex ON tp.tutor_id = tex.tutor_id AND tex.deleted_at IS NULL
LEFT JOIN tutor_specializations ts ON tp.tutor_id = ts.tutor_id
LEFT JOIN tutor_verification_log tvl ON tp.tutor_id = tvl.tutor_id
GROUP BY tp.tutor_id, tp.user_id, u.first_name, u.last_name, tp.verification_status, 
         tp.background_check_status, tp.background_check_date, tp.profile_completeness;