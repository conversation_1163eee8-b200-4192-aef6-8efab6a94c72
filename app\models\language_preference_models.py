"""
Language Preference Models for TutorAide Application.
Handles user language preferences with Quebec French support.
"""

from datetime import datetime, date
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field, field_validator
from enum import Enum

from app.locales.constants import SUPPORTED_LANGUAGES, DEFAULT_LANGUAGE


class LanguageSource(str, Enum):
    """Source of language preference."""
    AUTO = "auto"
    MANUAL = "manual"
    BROWSER = "browser"
    SYSTEM = "system"


class ChangeSource(str, Enum):
    """Source of language preference change."""
    USER_MANUAL = "user_manual"
    AUTO_DETECT = "auto_detect"
    BROWSER_DETECT = "browser_detect"
    ADMIN_OVERRIDE = "admin_override"
    SYSTEM_MIGRATION = "system_migration"


class UserLanguagePreference(BaseModel):
    """User language preference model."""
    user_id: int
    preferred_language: str = Field(default=DEFAULT_LANGUAGE)
    language_auto_detect: bool = Field(default=True)
    language_source: LanguageSource = Field(default=LanguageSource.AUTO)
    language_updated_at: datetime
    quebec_french_preference: bool = Field(default=True)
    
    @field_validator('preferred_language')
    def validate_language(cls, v):
        if v not in SUPPORTED_LANGUAGES:
            raise ValueError(f"Language must be one of {SUPPORTED_LANGUAGES}")
        return v
    
    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class LanguagePreferenceHistory(BaseModel):
    """Language preference change history model."""
    preference_history_id: int
    user_id: int
    previous_language: Optional[str]
    new_language: str
    change_source: ChangeSource
    change_reason: Optional[str]
    browser_language: Optional[str]
    ip_address: Optional[str]
    user_agent: Optional[str]
    session_id: Optional[str]
    created_at: datetime
    created_by: Optional[int]
    
    @field_validator('new_language')
    def validate_new_language(cls, v):
        if v not in SUPPORTED_LANGUAGES:
            raise ValueError(f"Language must be one of {SUPPORTED_LANGUAGES}")
        return v
    
    @field_validator('previous_language')
    def validate_previous_language(cls, v):
        if v is not None and v not in SUPPORTED_LANGUAGES:
            raise ValueError(f"Language must be one of {SUPPORTED_LANGUAGES}")
        return v
    
    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class LanguageUsageAnalytics(BaseModel):
    """Language usage analytics model."""
    analytics_id: int
    date_recorded: date
    language_code: str
    total_users: int = 0
    new_users: int = 0
    active_sessions: int = 0
    manual_switches: int = 0
    auto_detections: int = 0
    quebec_french_users: int = 0
    browser_preferences: Optional[Dict[str, Any]] = None
    geographic_data: Optional[Dict[str, Any]] = None
    created_at: datetime
    
    @field_validator('language_code')
    def validate_language_code(cls, v):
        if v not in SUPPORTED_LANGUAGES:
            raise ValueError(f"Language must be one of {SUPPORTED_LANGUAGES}")
        return v
    
    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            date: lambda v: v.isoformat()
        }


# Request/Response Models for API

class LanguagePreferenceUpdateRequest(BaseModel):
    """Request model for updating language preferences."""
    preferred_language: str
    language_auto_detect: Optional[bool] = None
    quebec_french_preference: Optional[bool] = None
    persist: bool = True
    
    @field_validator('preferred_language')
    def validate_language(cls, v):
        if v not in SUPPORTED_LANGUAGES:
            raise ValueError(f"Language must be one of {SUPPORTED_LANGUAGES}")
        return v


class LanguagePreferenceResponse(BaseModel):
    """Response model for language preference operations."""
    success: bool
    preferred_language: str
    language_auto_detect: bool
    language_source: LanguageSource
    quebec_french_preference: bool
    effective_language: str
    updated_at: datetime
    message: Optional[str] = None


class LanguagePreferenceHistoryResponse(BaseModel):
    """Response model for language preference history."""
    history: List[LanguagePreferenceHistory]
    total_changes: int
    date_range: Optional[Dict[str, str]] = None


class LanguageAnalyticsResponse(BaseModel):
    """Response model for language analytics."""
    analytics: List[LanguageUsageAnalytics]
    summary: Dict[str, Any]
    date_range: Dict[str, str]


class LanguageDetectionResult(BaseModel):
    """Result of language detection process."""
    detected_language: str
    confidence: float = Field(ge=0.0, le=1.0)
    source: str
    sources_tried: List[str]
    fallback_used: bool = False
    browser_languages: Optional[List[str]] = None
    quebec_indicators: Optional[List[str]] = None
    
    @field_validator('detected_language')
    def validate_detected_language(cls, v):
        if v not in SUPPORTED_LANGUAGES:
            raise ValueError(f"Language must be one of {SUPPORTED_LANGUAGES}")
        return v


class UserLanguageContext(BaseModel):
    """Complete user language context for decision making."""
    user_preference: Optional[UserLanguagePreference]
    detected_language: LanguageDetectionResult
    effective_language: str
    auto_detect_enabled: bool
    quebec_french_indicators: List[str]
    session_language: Optional[str]
    
    @field_validator('effective_language')
    def validate_effective_language(cls, v):
        if v not in SUPPORTED_LANGUAGES:
            raise ValueError(f"Language must be one of {SUPPORTED_LANGUAGES}")
        return v


class LanguagePreferenceStats(BaseModel):
    """Statistics about language preferences."""
    total_users: int
    language_distribution: Dict[str, int]
    auto_detect_enabled_count: int
    quebec_french_preference_count: int
    recent_changes_count: int
    most_common_source: LanguageSource
    average_changes_per_user: float


class BulkLanguageUpdate(BaseModel):
    """Model for bulk language preference updates."""
    user_ids: List[int]
    preferred_language: str
    language_auto_detect: Optional[bool] = None
    quebec_french_preference: Optional[bool] = None
    change_reason: str
    
    @field_validator('preferred_language')
    def validate_language(cls, v):
        if v not in SUPPORTED_LANGUAGES:
            raise ValueError(f"Language must be one of {SUPPORTED_LANGUAGES}")
        return v
    
    @field_validator('user_ids')
    def validate_user_ids(cls, v):
        if not v:
            raise ValueError("User IDs list cannot be empty")
        if len(v) > 1000:
            raise ValueError("Cannot update more than 1000 users at once")
        return v


class BulkLanguageUpdateResult(BaseModel):
    """Result of bulk language preference update."""
    success: bool
    updated_count: int
    failed_count: int
    failed_user_ids: List[int]
    errors: List[str]
    message: str