"""
Session tracking middleware for automatic activity updates.
"""

from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
import logging

from app.core.dependencies import get_database
from app.services.session_service import get_session_service
from app.core.security import verify_token

logger = logging.getLogger(__name__)


class SessionTrackingMiddleware(BaseHTTPMiddleware):
    """Middleware to track session activity automatically."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request and update session activity if authenticated."""
        # Skip session tracking for non-authenticated endpoints
        if request.url.path.startswith("/docs") or \
           request.url.path.startswith("/openapi") or \
           request.url.path.startswith("/health"):
            return await call_next(request)
        
        # Extract token from Authorization header
        auth_header = request.headers.get("Authorization", "")
        if not auth_header.startswith("Bearer "):
            return await call_next(request)
        
        try:
            # Get token
            token = auth_header.split(" ")[1]
            
            # Verify JWT token to get user info
            payload = verify_token(token)
            if not payload:
                return await call_next(request)
            
            # Get session token from request state if available
            session_token = getattr(request.state, "session_token", None)
            if not session_token:
                # For now, we'll skip activity tracking if no session token
                # In production, you'd store session token mapping with JWT
                return await call_next(request)
            
            # Get database connection
            async with get_database() as conn:
                session_service = get_session_service()
                
                # Validate session
                try:
                    session = await session_service.validate_session(conn, session_token)
                    
                    # Update activity with client IP
                    client_ip = request.client.host if request.client else None
                    await session_service.update_activity(
                        conn, 
                        session['session_id'],
                        ip_address=client_ip
                    )
                except Exception as e:
                    # Log but don't fail the request
                    logger.debug(f"Session activity update failed: {e}")
                    
        except Exception as e:
            # Log but don't fail the request
            logger.debug(f"Session tracking error: {e}")
        
        # Continue with request
        return await call_next(request)