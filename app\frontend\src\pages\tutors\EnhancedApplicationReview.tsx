import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { 
  ArrowLeft, User, Mail, Phone, MapPin, Calendar, School,
  Briefcase, Star, FileText, Download, Check, X, Clock,
  AlertCircle, MessageSquare, Send, Eye, Shield, Award,
  Languages, DollarSign, Car, Home, Video, Book
} from 'lucide-react';
import { Card } from '../../components/common/Card';
import Button from '../../components/common/Button';
import { Badge } from '../../components/common/Badge';
import { Input } from '../../components/common/Input';
import { Select } from '../../components/common/Select';
import { Modal } from '../../components/common/Modal';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '../../components/common/TabsAdapter';
import toast from 'react-hot-toast';
import { format } from 'date-fns';

interface TutorApplication {
  application_id: number;
  user_id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  province: string;
  postal_code: string;
  date_of_birth: string;
  emergency_contact_name: string;
  emergency_contact_phone: string;
  application_status: 'pending' | 'under_review' | 'approved' | 'rejected' | 'on_hold';
  submitted_date: string;
  reviewed_date?: string;
  reviewer_notes?: string;
  
  // Educational Background
  education_level: string;
  current_institution?: string;
  field_of_study?: string;
  graduation_year?: string;
  gpa?: number;
  
  // Teaching Experience
  teaching_experience_years: number;
  previous_tutoring_experience: boolean;
  experience_details?: string;
  age_groups_comfortable: string[];
  
  // Services & Availability
  subjects_to_teach: string[];
  service_types: string[];
  languages_spoken: string[];
  hourly_rate_requested: number;
  availability: AvailabilitySlot[];
  has_vehicle: boolean;
  willing_to_travel: boolean;
  max_travel_distance?: number;
  
  // Background Check
  background_check_consent: boolean;
  criminal_record_check?: 'clear' | 'pending' | 'issues';
  vulnerable_sector_check?: 'clear' | 'pending' | 'issues';
  reference_checks?: ReferenceCheck[];
  
  // Documents
  documents: ApplicationDocument[];
  
  // Additional Info
  why_tutor?: string;
  special_skills?: string;
  heard_about_us?: string;
}

interface AvailabilitySlot {
  day: string;
  start_time: string;
  end_time: string;
}

interface ReferenceCheck {
  name: string;
  relationship: string;
  phone: string;
  email: string;
  contacted: boolean;
  feedback?: string;
}

interface ApplicationDocument {
  document_id: number;
  document_type: string;
  file_name: string;
  uploaded_date: string;
  verified: boolean;
}

interface ReviewAction {
  action: 'approve' | 'reject' | 'hold';
  notes: string;
  conditions?: string[];
}

const EnhancedApplicationReview: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [application, setApplication] = useState<TutorApplication | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [showActionModal, setShowActionModal] = useState(false);
  const [selectedAction, setSelectedAction] = useState<'approve' | 'reject' | 'hold' | null>(null);
  const [reviewNotes, setReviewNotes] = useState('');
  const [selectedConditions, setSelectedConditions] = useState<string[]>([]);

  // Mock application data
  useEffect(() => {
    const mockApplication: TutorApplication = {
      application_id: Number(id),
      user_id: 101,
      first_name: 'Sarah',
      last_name: 'Johnson',
      email: '<EMAIL>',
      phone: '(*************',
      address: '123 Main Street',
      city: 'Montreal',
      province: 'QC',
      postal_code: 'H1A 0A1',
      date_of_birth: '1995-03-15',
      emergency_contact_name: 'John Johnson',
      emergency_contact_phone: '(*************',
      application_status: 'pending',
      submitted_date: '2024-11-18T10:30:00',
      
      education_level: "Master's Degree",
      current_institution: 'McGill University',
      field_of_study: 'Mathematics Education',
      graduation_year: '2022',
      gpa: 3.8,
      
      teaching_experience_years: 4,
      previous_tutoring_experience: true,
      experience_details: 'Taught high school math for 2 years, private tutoring for 4 years',
      age_groups_comfortable: ['13-17', '18+'],
      
      subjects_to_teach: ['Mathematics', 'Physics', 'Computer Science'],
      service_types: ['online', 'in_person', 'hybrid'],
      languages_spoken: ['English', 'French', 'Spanish'],
      hourly_rate_requested: 45,
      availability: [
        { day: 'Monday', start_time: '16:00', end_time: '20:00' },
        { day: 'Tuesday', start_time: '16:00', end_time: '20:00' },
        { day: 'Wednesday', start_time: '16:00', end_time: '20:00' },
        { day: 'Saturday', start_time: '10:00', end_time: '16:00' }
      ],
      has_vehicle: true,
      willing_to_travel: true,
      max_travel_distance: 20,
      
      background_check_consent: true,
      criminal_record_check: 'clear',
      vulnerable_sector_check: 'pending',
      reference_checks: [
        {
          name: 'Dr. Emily Chen',
          relationship: 'Former Supervisor',
          phone: '(*************',
          email: '<EMAIL>',
          contacted: true,
          feedback: 'Excellent teacher, highly recommended'
        },
        {
          name: 'Robert Smith',
          relationship: 'Colleague',
          phone: '(*************',
          email: '<EMAIL>',
          contacted: false
        }
      ],
      
      documents: [
        {
          document_id: 1,
          document_type: 'Resume',
          file_name: 'sarah_johnson_resume.pdf',
          uploaded_date: '2024-11-18',
          verified: true
        },
        {
          document_id: 2,
          document_type: 'Degree Certificate',
          file_name: 'masters_degree.pdf',
          uploaded_date: '2024-11-18',
          verified: true
        },
        {
          document_id: 3,
          document_type: 'Teaching Certificate',
          file_name: 'teaching_cert.pdf',
          uploaded_date: '2024-11-18',
          verified: false
        }
      ],
      
      why_tutor: 'I am passionate about helping students reach their full potential in mathematics and sciences. My experience has shown me the impact that personalized education can have.',
      special_skills: 'Experience with special needs students, fluent in three languages, certified in online teaching methodologies',
      heard_about_us: 'LinkedIn'
    };
    
    setApplication(mockApplication);
    setLoading(false);
  }, [id]);

  const handleAction = async (action: ReviewAction) => {
    setLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    if (application) {
      setApplication({
        ...application,
        application_status: action.action === 'approve' ? 'approved' : 
                          action.action === 'reject' ? 'rejected' : 'on_hold',
        reviewed_date: new Date().toISOString(),
        reviewer_notes: action.notes
      });
    }
    
    toast.success(
      action.action === 'approve' ? 'Application approved successfully' :
      action.action === 'reject' ? 'Application rejected' :
      'Application put on hold'
    );
    
    setShowActionModal(false);
    setLoading(false);
    
    // Navigate back after a delay
    setTimeout(() => navigate('/tutors/applications'), 1000);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: 'bg-yellow-100 text-yellow-700', icon: <Clock className="w-4 h-4" /> },
      under_review: { color: 'bg-blue-100 text-blue-700', icon: <Eye className="w-4 h-4" /> },
      approved: { color: 'bg-green-100 text-green-700', icon: <Check className="w-4 h-4" /> },
      rejected: { color: 'bg-red-100 text-red-700', icon: <X className="w-4 h-4" /> },
      on_hold: { color: 'bg-gray-100 text-gray-700', icon: <Clock className="w-4 h-4" /> }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    return (
      <Badge className={`inline-flex items-center gap-1 ${config.color}`}>
        {config.icon}
        <span className="capitalize">{status.replace('_', ' ')}</span>
      </Badge>
    );
  };

  const getBackgroundCheckBadge = (status?: string) => {
    if (!status) return <Badge className="bg-gray-100 text-gray-700">Not Started</Badge>;
    
    const statusConfig = {
      clear: { color: 'bg-green-100 text-green-700', icon: <Check className="w-4 h-4" /> },
      pending: { color: 'bg-yellow-100 text-yellow-700', icon: <Clock className="w-4 h-4" /> },
      issues: { color: 'bg-red-100 text-red-700', icon: <AlertCircle className="w-4 h-4" /> }
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    return (
      <Badge className={`inline-flex items-center gap-1 ${config.color}`}>
        {config.icon}
        <span className="capitalize">{status}</span>
      </Badge>
    );
  };

  if (loading || !application) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-accent-red"></div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            onClick={() => navigate('/tutors/applications')}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Applications
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {application.first_name} {application.last_name}
            </h1>
            <p className="text-gray-600">Application #{application.application_id}</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          {getStatusBadge(application.application_status)}
          <p className="text-sm text-gray-500">
            Submitted {format(new Date(application.submitted_date), 'MMM d, yyyy')}
          </p>
        </div>
      </div>

      {/* Quick Actions */}
      {application.application_status === 'pending' && (
        <Card className="p-4 bg-blue-50 border-blue-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <AlertCircle className="w-5 h-5 text-blue-600" />
              <div>
                <p className="font-medium text-blue-900">Action Required</p>
                <p className="text-sm text-blue-700">This application is pending review</p>
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                variant="secondary"
                onClick={() => {
                  setSelectedAction('hold');
                  setShowActionModal(true);
                }}
              >
                Put on Hold
              </Button>
              <Button
                variant="danger"
                onClick={() => {
                  setSelectedAction('reject');
                  setShowActionModal(true);
                }}
              >
                <X className="w-4 h-4 mr-2" />
                Reject
              </Button>
              <Button
                variant="primary"
                onClick={() => {
                  setSelectedAction('approve');
                  setShowActionModal(true);
                }}
              >
                <Check className="w-4 h-4 mr-2" />
                Approve
              </Button>
            </div>
          </div>
        </Card>
      )}

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="education">Education</TabsTrigger>
          <TabsTrigger value="experience">Experience</TabsTrigger>
          <TabsTrigger value="availability">Availability</TabsTrigger>
          <TabsTrigger value="background">Background</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6 mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Personal Information */}
            <Card className="p-6">
              <h2 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <User className="w-5 h-5" />
                Personal Information
              </h2>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Mail className="w-4 h-4 text-gray-400" />
                  <span className="text-sm">{application.email}</span>
                </div>
                <div className="flex items-center gap-3">
                  <Phone className="w-4 h-4 text-gray-400" />
                  <span className="text-sm">{application.phone}</span>
                </div>
                <div className="flex items-center gap-3">
                  <MapPin className="w-4 h-4 text-gray-400" />
                  <span className="text-sm">
                    {application.address}, {application.city}, {application.province} {application.postal_code}
                  </span>
                </div>
                <div className="flex items-center gap-3">
                  <Calendar className="w-4 h-4 text-gray-400" />
                  <span className="text-sm">
                    Born {format(new Date(application.date_of_birth), 'MMM d, yyyy')} 
                    ({new Date().getFullYear() - new Date(application.date_of_birth).getFullYear()} years old)
                  </span>
                </div>
                <div className="pt-3 border-t">
                  <p className="text-sm text-gray-600 mb-1">Emergency Contact</p>
                  <p className="text-sm font-medium">{application.emergency_contact_name}</p>
                  <p className="text-sm text-gray-600">{application.emergency_contact_phone}</p>
                </div>
              </div>
            </Card>

            {/* Services & Preferences */}
            <Card className="p-6">
              <h2 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Briefcase className="w-5 h-5" />
                Services & Preferences
              </h2>
              <div className="space-y-4">
                <div>
                  <p className="text-sm text-gray-600 mb-2">Subjects</p>
                  <div className="flex flex-wrap gap-2">
                    {application.subjects_to_teach.map(subject => (
                      <Badge key={subject} className="bg-blue-100 text-blue-700">
                        {subject}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <p className="text-sm text-gray-600 mb-2">Service Types</p>
                  <div className="flex gap-2">
                    {application.service_types.includes('online') && (
                      <Badge className="bg-green-100 text-green-700">
                        <Video className="w-3 h-3 mr-1" />
                        Online
                      </Badge>
                    )}
                    {application.service_types.includes('in_person') && (
                      <Badge className="bg-purple-100 text-purple-700">
                        <Home className="w-3 h-3 mr-1" />
                        In Person
                      </Badge>
                    )}
                    {application.service_types.includes('hybrid') && (
                      <Badge className="bg-orange-100 text-orange-700">
                        Hybrid
                      </Badge>
                    )}
                  </div>
                </div>
                <div>
                  <p className="text-sm text-gray-600 mb-2">Languages</p>
                  <div className="flex gap-2">
                    {application.languages_spoken.map(lang => (
                      <Badge key={lang} className="bg-gray-100 text-gray-700">
                        {lang}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">Hourly Rate</p>
                    <p className="font-semibold">${application.hourly_rate_requested}/hr</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Travel</p>
                    <p className="font-semibold">
                      {application.has_vehicle ? 'Has Vehicle' : 'No Vehicle'}
                      {application.willing_to_travel && ` • Up to ${application.max_travel_distance}km`}
                    </p>
                  </div>
                </div>
              </div>
            </Card>
          </div>

          {/* Motivation */}
          <Card className="p-6">
            <h2 className="font-semibold text-gray-900 mb-4">Why I Want to Tutor</h2>
            <p className="text-gray-700">{application.why_tutor}</p>
            {application.special_skills && (
              <>
                <h3 className="font-medium text-gray-900 mt-4 mb-2">Special Skills</h3>
                <p className="text-gray-700">{application.special_skills}</p>
              </>
            )}
          </Card>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card className="p-4 text-center">
              <School className="w-8 h-8 text-blue-600 mx-auto mb-2" />
              <p className="text-2xl font-bold">{application.education_level}</p>
              <p className="text-sm text-gray-600">Education</p>
            </Card>
            <Card className="p-4 text-center">
              <Briefcase className="w-8 h-8 text-green-600 mx-auto mb-2" />
              <p className="text-2xl font-bold">{application.teaching_experience_years} years</p>
              <p className="text-sm text-gray-600">Experience</p>
            </Card>
            <Card className="p-4 text-center">
              <Languages className="w-8 h-8 text-purple-600 mx-auto mb-2" />
              <p className="text-2xl font-bold">{application.languages_spoken.length}</p>
              <p className="text-sm text-gray-600">Languages</p>
            </Card>
            <Card className="p-4 text-center">
              <Award className="w-8 h-8 text-yellow-600 mx-auto mb-2" />
              <p className="text-2xl font-bold">{application.gpa || 'N/A'}</p>
              <p className="text-sm text-gray-600">GPA</p>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="education" className="space-y-6 mt-6">
          <Card className="p-6">
            <h2 className="font-semibold text-gray-900 mb-4">Educational Background</h2>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-600">Highest Level</p>
                  <p className="font-semibold">{application.education_level}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Institution</p>
                  <p className="font-semibold">{application.current_institution || 'N/A'}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Field of Study</p>
                  <p className="font-semibold">{application.field_of_study || 'N/A'}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Graduation Year</p>
                  <p className="font-semibold">{application.graduation_year || 'N/A'}</p>
                </div>
              </div>
              {application.gpa && (
                <div className="pt-4 border-t">
                  <p className="text-sm text-gray-600">GPA</p>
                  <div className="flex items-center gap-3 mt-1">
                    <p className="text-2xl font-bold">{application.gpa}</p>
                    <div className="flex-1 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-green-600 h-2 rounded-full"
                        style={{ width: `${(application.gpa / 4) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="experience" className="space-y-6 mt-6">
          <Card className="p-6">
            <h2 className="font-semibold text-gray-900 mb-4">Teaching Experience</h2>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-600">Years of Experience</p>
                  <p className="text-2xl font-bold">{application.teaching_experience_years}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Previous Tutoring</p>
                  <p className="font-semibold">{application.previous_tutoring_experience ? 'Yes' : 'No'}</p>
                </div>
              </div>
              
              {application.experience_details && (
                <div>
                  <p className="text-sm text-gray-600 mb-2">Experience Details</p>
                  <p className="text-gray-700">{application.experience_details}</p>
                </div>
              )}
              
              <div>
                <p className="text-sm text-gray-600 mb-2">Comfortable Teaching Ages</p>
                <div className="flex gap-2">
                  {application.age_groups_comfortable.map(age => (
                    <Badge key={age} className="bg-blue-100 text-blue-700">
                      {age} years
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="availability" className="space-y-6 mt-6">
          <Card className="p-6">
            <h2 className="font-semibold text-gray-900 mb-4">Weekly Availability</h2>
            <div className="space-y-3">
              {application.availability.map((slot, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span className="font-medium">{slot.day}</span>
                  <span className="text-gray-600">
                    {slot.start_time} - {slot.end_time}
                  </span>
                </div>
              ))}
            </div>
            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-800">
                Total availability: <span className="font-semibold">
                  {application.availability.reduce((total, slot) => {
                    const start = parseInt(slot.start_time.split(':')[0]);
                    const end = parseInt(slot.end_time.split(':')[0]);
                    return total + (end - start);
                  }, 0)} hours per week
                </span>
              </p>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="background" className="space-y-6 mt-6">
          <Card className="p-6">
            <h2 className="font-semibold text-gray-900 mb-4">Background Checks</h2>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <Shield className="w-5 h-5 text-gray-600" />
                  <div>
                    <p className="font-medium">Criminal Record Check</p>
                    <p className="text-sm text-gray-600">Standard background verification</p>
                  </div>
                </div>
                {getBackgroundCheckBadge(application.criminal_record_check)}
              </div>
              
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <Shield className="w-5 h-5 text-gray-600" />
                  <div>
                    <p className="font-medium">Vulnerable Sector Check</p>
                    <p className="text-sm text-gray-600">Required for working with minors</p>
                  </div>
                </div>
                {getBackgroundCheckBadge(application.vulnerable_sector_check)}
              </div>
            </div>
          </Card>

          {/* References */}
          <Card className="p-6">
            <h2 className="font-semibold text-gray-900 mb-4">References</h2>
            <div className="space-y-4">
              {application.reference_checks?.map((ref, index) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div>
                      <p className="font-medium">{ref.name}</p>
                      <p className="text-sm text-gray-600">{ref.relationship}</p>
                      <div className="flex gap-3 mt-2 text-sm text-gray-600">
                        <span>{ref.phone}</span>
                        <span>•</span>
                        <span>{ref.email}</span>
                      </div>
                    </div>
                    <Badge className={ref.contacted ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'}>
                      {ref.contacted ? 'Contacted' : 'Pending'}
                    </Badge>
                  </div>
                  {ref.feedback && (
                    <div className="mt-3 p-3 bg-blue-50 rounded">
                      <p className="text-sm text-blue-800">{ref.feedback}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="documents" className="space-y-6 mt-6">
          <Card className="p-6">
            <h2 className="font-semibold text-gray-900 mb-4">Uploaded Documents</h2>
            <div className="space-y-3">
              {application.documents.map((doc) => (
                <div key={doc.document_id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <FileText className="w-5 h-5 text-gray-600" />
                    <div>
                      <p className="font-medium">{doc.document_type}</p>
                      <p className="text-sm text-gray-600">{doc.file_name}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Badge className={doc.verified ? 'bg-green-100 text-green-700' : 'bg-yellow-100 text-yellow-700'}>
                      {doc.verified ? 'Verified' : 'Pending Verification'}
                    </Badge>
                    <Button variant="ghost" size="sm">
                      <Download className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Action Modal */}
      <Modal
        isOpen={showActionModal}
        onClose={() => {
          setShowActionModal(false);
          setSelectedAction(null);
          setReviewNotes('');
          setSelectedConditions([]);
        }}
        title={
          selectedAction === 'approve' ? 'Approve Application' :
          selectedAction === 'reject' ? 'Reject Application' :
          'Put Application on Hold'
        }
      >
        <div className="p-6 space-y-4">
          {selectedAction === 'approve' && (
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Select any conditions for approval:
              </p>
              <div className="space-y-2">
                {[
                  'Complete vulnerable sector check',
                  'Provide additional references',
                  'Complete training modules',
                  'Probationary period (3 months)'
                ].map(condition => (
                  <label key={condition} className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      className="rounded"
                      checked={selectedConditions.includes(condition)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedConditions([...selectedConditions, condition]);
                        } else {
                          setSelectedConditions(selectedConditions.filter(c => c !== condition));
                        }
                      }}
                    />
                    <span className="text-sm">{condition}</span>
                  </label>
                ))}
              </div>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Review Notes
            </label>
            <textarea
              className="w-full p-3 border rounded-lg"
              rows={4}
              value={reviewNotes}
              onChange={(e) => setReviewNotes(e.target.value)}
              placeholder={
                selectedAction === 'approve' ? 'Add any notes about the approval...' :
                selectedAction === 'reject' ? 'Please provide a reason for rejection...' :
                'Explain why this application is being put on hold...'
              }
              required={selectedAction === 'reject'}
            />
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <Button
              variant="ghost"
              onClick={() => {
                setShowActionModal(false);
                setSelectedAction(null);
                setReviewNotes('');
                setSelectedConditions([]);
              }}
            >
              Cancel
            </Button>
            <Button
              variant={selectedAction === 'reject' ? 'danger' : 'primary'}
              onClick={() => handleAction({
                action: selectedAction!,
                notes: reviewNotes,
                conditions: selectedConditions
              })}
              disabled={selectedAction === 'reject' && !reviewNotes}
            >
              {selectedAction === 'approve' ? 'Approve Application' :
               selectedAction === 'reject' ? 'Reject Application' :
               'Put on Hold'}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default EnhancedApplicationReview;