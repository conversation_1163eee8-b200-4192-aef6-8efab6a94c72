import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card } from '../../components/common/Card';
import Button from '../../components/common/Button';
import { Input } from '../../components/common/Input';
import { Select } from '../../components/common/Select';
import { Calendar, Clock, AlertCircle, CheckCircle, XCircle, Plus } from 'lucide-react';
import toast from 'react-hot-toast';

interface TimeOffRequest {
  id: number;
  startDate: string;
  endDate: string;
  reason: string;
  status: 'pending' | 'approved' | 'rejected';
  notes?: string;
  createdAt: string;
}

const TimeOff: React.FC = () => {
  const { t } = useTranslation();
  const [showNewRequestForm, setShowNewRequestForm] = useState(false);
  const [formData, setFormData] = useState({
    startDate: '',
    endDate: '',
    reason: '',
    notes: '',
  });

  // Placeholder data - will be replaced with API calls
  const timeOffRequests: TimeOffRequest[] = [
    {
      id: 1,
      startDate: '2024-12-24',
      endDate: '2024-12-26',
      reason: 'Holiday - Christmas',
      status: 'approved',
      notes: 'Family gathering',
      createdAt: '2024-12-01',
    },
    {
      id: 2,
      startDate: '2024-12-31',
      endDate: '2025-01-02',
      reason: 'Holiday - New Year',
      status: 'pending',
      notes: 'Out of town',
      createdAt: '2024-12-10',
    },
    {
      id: 3,
      startDate: '2024-11-15',
      endDate: '2024-11-15',
      reason: 'Personal',
      status: 'rejected',
      notes: 'Medical appointment',
      createdAt: '2024-11-10',
    },
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // TODO: Implement API call to submit time off request
    toast.success(t('tutor.timeOff.requestSubmitted'));
    setShowNewRequestForm(false);
    setFormData({
      startDate: '',
      endDate: '',
      reason: '',
      notes: '',
    });
  };

  const getStatusIcon = (status: TimeOffRequest['status']) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'rejected':
        return <XCircle className="h-5 w-5 text-red-600" />;
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-600" />;
    }
  };

  const getStatusBadge = (status: TimeOffRequest['status']) => {
    const baseClasses = 'px-2 py-1 text-xs rounded-full';
    switch (status) {
      case 'approved':
        return `${baseClasses} bg-green-100 text-green-700`;
      case 'rejected':
        return `${baseClasses} bg-red-100 text-red-700`;
      case 'pending':
        return `${baseClasses} bg-yellow-100 text-yellow-700`;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">
            {t('tutor.timeOff.title')}
          </h1>
          <p className="mt-1 text-sm text-gray-500">
            {t('tutor.timeOff.subtitle')}
          </p>
        </div>
        <Button
          onClick={() => setShowNewRequestForm(!showNewRequestForm)}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          {t('tutor.timeOff.newRequest')}
        </Button>
      </div>

      {/* New Request Form */}
      {showNewRequestForm && (
        <Card className="p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            {t('tutor.timeOff.requestTimeOff')}
          </h2>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('tutor.timeOff.startDate')}
                </label>
                <Input
                  type="date"
                  value={formData.startDate}
                  onChange={(e) =>
                    setFormData({ ...formData, startDate: e.target.value })
                  }
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('tutor.timeOff.endDate')}
                </label>
                <Input
                  type="date"
                  value={formData.endDate}
                  onChange={(e) =>
                    setFormData({ ...formData, endDate: e.target.value })
                  }
                  min={formData.startDate}
                  required
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('tutor.timeOff.reason')}
              </label>
              <Select
                value={formData.reason}
                onChange={(e) =>
                  setFormData({ ...formData, reason: e.target.value })
                }
                required
              >
                <option value="">{t('common.select')}</option>
                <option value="vacation">{t('tutor.timeOff.reasons.vacation')}</option>
                <option value="personal">{t('tutor.timeOff.reasons.personal')}</option>
                <option value="medical">{t('tutor.timeOff.reasons.medical')}</option>
                <option value="holiday">{t('tutor.timeOff.reasons.holiday')}</option>
                <option value="other">{t('tutor.timeOff.reasons.other')}</option>
              </Select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('tutor.timeOff.notes')} ({t('common.optional')})
              </label>
              <textarea
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-accent-red focus:border-accent-red"
                rows={3}
                value={formData.notes}
                onChange={(e) =>
                  setFormData({ ...formData, notes: e.target.value })
                }
                placeholder={t('tutor.timeOff.notesPlaceholder')}
              />
            </div>
            <div className="flex gap-2">
              <Button type="submit">{t('common.save')}</Button>
              <Button
                type="button"
                variant="secondary"
                onClick={() => setShowNewRequestForm(false)}
              >
                {t('common.cancel')}
              </Button>
            </div>
          </form>
        </Card>
      )}

      {/* Time Off Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">{t('tutor.timeOff.stats.pending')}</p>
              <p className="mt-1 text-2xl font-semibold text-gray-900">
                {timeOffRequests.filter((r) => r.status === 'pending').length}
              </p>
            </div>
            <Clock className="h-8 w-8 text-yellow-500" />
          </div>
        </Card>
        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">{t('tutor.timeOff.stats.approved')}</p>
              <p className="mt-1 text-2xl font-semibold text-gray-900">
                {timeOffRequests.filter((r) => r.status === 'approved').length}
              </p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-500" />
          </div>
        </Card>
        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">{t('tutor.timeOff.stats.daysOffThisMonth')}</p>
              <p className="mt-1 text-2xl font-semibold text-gray-900">3</p>
            </div>
            <Calendar className="h-8 w-8 text-blue-500" />
          </div>
        </Card>
      </div>

      {/* Time Off Requests List */}
      <Card className="overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">
            {t('tutor.timeOff.requestHistory')}
          </h2>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('tutor.timeOff.dates')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('tutor.timeOff.reason')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('tutor.timeOff.notes')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('common.status')}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('tutor.timeOff.requestedOn')}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {timeOffRequests.map((request) => (
                <tr key={request.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-sm text-gray-900">
                        {new Date(request.startDate).toLocaleDateString()} -{' '}
                        {new Date(request.endDate).toLocaleDateString()}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {request.reason}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500">
                    {request.notes || '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(request.status)}
                      <span className={getStatusBadge(request.status)}>
                        {t(`tutor.timeOff.status.${request.status}`)}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(request.createdAt).toLocaleDateString()}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>

      {/* Information Box */}
      <Card className="p-4 bg-blue-50 border-blue-200">
        <div className="flex">
          <AlertCircle className="h-5 w-5 text-blue-600 flex-shrink-0 mt-0.5" />
          <div className="ml-3">
            <p className="text-sm text-blue-800">
              {t('tutor.timeOff.info')}
            </p>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default TimeOff;