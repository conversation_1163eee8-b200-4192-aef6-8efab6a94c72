import axios from 'axios';
import { convertKeysToCamelCase, convertKeysToSnakeCase } from '../utils/case-converter';

// API Configuration
// In production, if VITE_API_URL is empty, use relative URLs (same domain)
// In development, default to localhost:8000
export const API_BASE_URL = import.meta.env.VITE_API_URL === '' 
  ? '' // Empty string means relative URLs (same domain)
  : (import.meta.env.VITE_API_URL || 'http://localhost:8000');

export const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests and convert request data to snake_case
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('access_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  
  // Convert request data from camelCase to snake_case
  if (config.data && typeof config.data === 'object') {
    config.data = convertKeysToSnakeCase(config.data);
  }
  
  return config;
});

// Handle auth errors and convert response data to camelCase
api.interceptors.response.use(
  (response) => {
    // Convert response data from snake_case to camelCase
    if (response.data && typeof response.data === 'object') {
      response.data = convertKeysToCamelCase(response.data);
    }
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      window.location.href = '/login';
    }
    
    // Convert error response data to camelCase as well
    if (error.response?.data && typeof error.response.data === 'object') {
      error.response.data = convertKeysToCamelCase(error.response.data);
    }
    
    return Promise.reject(error);
  }
);

export default api;