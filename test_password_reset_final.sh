#!/bin/bash
# Final test for password reset after all fixes

echo "Testing Password Reset - Final Version"
echo "======================================"
echo ""

# Your email for testing
EMAIL="<EMAIL>"
API_URL="https://tutoraide-production.up.railway.app"

echo "1. Testing password reset request for: $EMAIL"
echo "---------------------------------------------"

# Request password reset
response=$(curl -s -w "\n\nHTTP_STATUS:%{http_code}" -X POST "$API_URL/api/v1/auth/password/reset-request" \
  -H "Content-Type: application/json" \
  -d "{\"email\": \"$EMAIL\"}")

# Extract body and status
body=$(echo "$response" | sed -e 's/HTTP_STATUS:.*//g')
status=$(echo "$response" | tr -d '\n' | sed -e 's/.*HTTP_STATUS://')

echo "Response Status: $status"
echo "Response Body:"
echo "$body" | python3 -m json.tool 2>/dev/null || echo "$body"

echo ""
echo "2. Checking for common errors:"
echo "------------------------------"

if [[ "$body" == *"relation"*"does not exist"* ]]; then
    echo "❌ ERROR: Database tables not found!"
    echo "   ACTION: Update DATABASE_URL in Railway to use 'postgres' database"
elif [[ "$body" == *"validation error"* ]]; then
    echo "❌ ERROR: Validation errors in the code"
    echo "   ACTION: Check Railway logs for details"
elif [[ "$status" == "200" ]] || [[ "$status" == "201" ]]; then
    echo "✅ SUCCESS: Password reset request processed!"
    echo "   ACTION: Check your email for the reset link"
else
    echo "❌ ERROR: Unexpected response"
    echo "   ACTION: Check Railway logs for details"
fi

echo ""
echo "3. To view Railway logs:"
echo "-----------------------"
echo "railway logs --service=web | tail -50"
echo ""
echo "4. Once you receive the reset token email, test reset with:"
echo "-----------------------------------------------------------"
echo "curl -X POST \"$API_URL/api/v1/auth/password/reset/confirm\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -d '{\"token\": \"YOUR_TOKEN_HERE\", \"new_password\": \"NewSecurePass123!\", \"confirm_password\": \"NewSecurePass123!\"}'"