import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { I18nextProvider } from 'react-i18next';
import FinancialReport from './FinancialReport';
import i18n from '../../i18n';

// Mock recharts components
vi.mock('recharts', () => ({
  LineChart: ({ children, data }: any) => (
    <div data-testid="line-chart" data-chart-data={JSON.stringify(data)}>
      {children}
    </div>
  ),
  Line: ({ dataKey }: any) => <div data-testid={`line-${dataKey}`} />,
  PieChart: ({ children }: any) => <div data-testid="pie-chart">{children}</div>,
  Pie: ({ data }: any) => <div data-testid="pie" data-pie-data={JSON.stringify(data)} />,
  BarChart: ({ children, data }: any) => (
    <div data-testid="bar-chart" data-chart-data={JSON.stringify(data)}>
      {children}
    </div>
  ),
  Bar: ({ dataKey }: any) => <div data-testid={`bar-${dataKey}`} />,
  XAxis: () => <div data-testid="x-axis" />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="cartesian-grid" />,
  Tooltip: () => <div data-testid="tooltip" />,
  Legend: () => <div data-testid="legend" />,
  ResponsiveContainer: ({ children }: any) => (
    <div data-testid="responsive-container">{children}</div>
  ),
  Cell: () => <div data-testid="cell" />,
}));

const renderFinancialReport = () => {
  return render(
    <I18nextProvider i18n={i18n}>
      <FinancialReport />
    </I18nextProvider>
  );
};

describe('FinancialReport', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders financial report header', () => {
    renderFinancialReport();
    
    expect(screen.getByText('Financial Report')).toBeInTheDocument();
  });

  it('displays key metrics cards', () => {
    renderFinancialReport();
    
    // Check that all key metrics are displayed
    expect(screen.getByText('Total Revenue')).toBeInTheDocument();
    expect(screen.getByText('Total Expenses')).toBeInTheDocument();
    expect(screen.getByText('Net Profit')).toBeInTheDocument();
    expect(screen.getByText('Profit Margin')).toBeInTheDocument();
  });

  it('shows revenue values correctly formatted', () => {
    renderFinancialReport();
    
    // Check for formatted currency values
    expect(screen.getByText(/\$328,000/)).toBeInTheDocument(); // Total revenue
    expect(screen.getByText(/\$215,000/)).toBeInTheDocument(); // Total expenses
    expect(screen.getByText(/\$113,000/)).toBeInTheDocument(); // Net profit
  });

  it('displays percentage changes for metrics', () => {
    renderFinancialReport();
    
    // Check for percentage indicators
    expect(screen.getByText('+12.5%')).toBeInTheDocument();
    expect(screen.getByText('+8.3%')).toBeInTheDocument();
    expect(screen.getByText('+15.2%')).toBeInTheDocument();
  });

  it('renders date range selector', () => {
    renderFinancialReport();
    
    const dateSelector = screen.getByDisplayValue('Last 6 months');
    expect(dateSelector).toBeInTheDocument();
    
    // Check all date range options
    fireEvent.click(dateSelector);
    expect(screen.getByText('Last 30 days')).toBeInTheDocument();
    expect(screen.getByText('Last 3 months')).toBeInTheDocument();
    expect(screen.getByText('Last year')).toBeInTheDocument();
  });

  it('updates data when date range changes', async () => {
    renderFinancialReport();
    
    const dateSelector = screen.getByDisplayValue('Last 6 months');
    fireEvent.change(dateSelector, { target: { value: 'last30days' } });
    
    await waitFor(() => {
      expect(dateSelector).toHaveValue('last30days');
    });
  });

  it('renders revenue trend chart', () => {
    renderFinancialReport();
    
    expect(screen.getByText('Revenue Trend')).toBeInTheDocument();
    expect(screen.getByTestId('line-chart')).toBeInTheDocument();
    
    // Check that revenue, expenses, and profit lines are rendered
    expect(screen.getByTestId('line-revenue')).toBeInTheDocument();
    expect(screen.getByTestId('line-expenses')).toBeInTheDocument();
    expect(screen.getByTestId('line-profit')).toBeInTheDocument();
  });

  it('renders expense breakdown pie chart', () => {
    renderFinancialReport();
    
    expect(screen.getByText('Expense Breakdown')).toBeInTheDocument();
    expect(screen.getByTestId('pie-chart')).toBeInTheDocument();
    expect(screen.getByTestId('pie')).toBeInTheDocument();
  });

  it('renders revenue by service bar chart', () => {
    renderFinancialReport();
    
    expect(screen.getByText('Revenue by Service')).toBeInTheDocument();
    expect(screen.getByTestId('bar-chart')).toBeInTheDocument();
    expect(screen.getByTestId('bar-revenue')).toBeInTheDocument();
  });

  it('displays recent transactions table', () => {
    renderFinancialReport();
    
    expect(screen.getByText('Recent Transactions')).toBeInTheDocument();
    
    // Check table headers
    expect(screen.getByText('Date')).toBeInTheDocument();
    expect(screen.getByText('Description')).toBeInTheDocument();
    expect(screen.getByText('Category')).toBeInTheDocument();
    expect(screen.getByText('Amount')).toBeInTheDocument();
    
    // Check for sample transaction data
    expect(screen.getByText('Invoice #1234 - Marie Dubois')).toBeInTheDocument();
    expect(screen.getByText('Tutor Payment - John Smith')).toBeInTheDocument();
  });

  it('shows transaction categories with proper styling', () => {
    renderFinancialReport();
    
    const revenueTag = screen.getByText('Revenue');
    const expenseTag = screen.getByText('Expense');
    
    expect(revenueTag).toBeInTheDocument();
    expect(expenseTag).toBeInTheDocument();
    
    // Check styling classes
    expect(revenueTag).toHaveClass('text-accent-green');
    expect(expenseTag).toHaveClass('text-accent-red');
  });

  it('handles export functionality', () => {
    renderFinancialReport();
    
    const exportButton = screen.getByRole('button', { name: /export/i });
    expect(exportButton).toBeInTheDocument();
    
    fireEvent.click(exportButton);
    // Export functionality should be triggered (mocked in real implementation)
  });

  it('calculates profit margin correctly', () => {
    renderFinancialReport();
    
    // Revenue: 328,000, Expenses: 215,000, Profit: 113,000
    // Margin should be (113,000 / 328,000) * 100 = 34.5%
    expect(screen.getByText('34.5%')).toBeInTheDocument();
  });

  it('displays trends indicators correctly', () => {
    renderFinancialReport();
    
    // Check for trending up/down icons
    const trendingUpIcons = screen.getAllByTestId('trending-up');
    expect(trendingUpIcons).toHaveLength(4); // All metrics should be trending up
  });

  it('formats currency values consistently', () => {
    renderFinancialReport();
    
    // All currency values should include $ and proper formatting
    const currencyValues = screen.getAllByText(/\$[\d,]+/);
    expect(currencyValues.length).toBeGreaterThan(0);
    
    currencyValues.forEach(value => {
      expect(value.textContent).toMatch(/^\$[\d,]+$/);
    });
  });

  it('handles loading state gracefully', async () => {
    renderFinancialReport();
    
    // Component should render without errors even during data loading
    expect(screen.getByText('Financial Report')).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.getByText('Total Revenue')).toBeInTheDocument();
    });
  });
});