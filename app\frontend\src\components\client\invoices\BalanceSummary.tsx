import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card } from '../../common/Card';
import { DollarSign, AlertTriangle, CheckCircle, TrendingUp } from 'lucide-react';

interface BalanceSummaryProps {
  totalOutstanding: number;
  totalPaid: number;
  overdueCount: number;
}

export const BalanceSummary: React.FC<BalanceSummaryProps> = ({
  totalOutstanding,
  totalPaid,
  overdueCount
}) => {
  const { t } = useTranslation();

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD'
    }).format(amount);
  };

  const cards = [
    {
      title: t('client.invoices.summary.outstanding'),
      value: formatCurrency(totalOutstanding),
      icon: DollarSign,
      color: 'bg-amber-50 text-amber-600',
      iconBg: 'bg-amber-100',
      description: overdueCount > 0 
        ? t('client.invoices.summary.overdueCount', { count: overdueCount })
        : t('client.invoices.summary.allCurrent')
    },
    {
      title: t('client.invoices.summary.totalPaid'),
      value: formatCurrency(totalPaid),
      icon: CheckCircle,
      color: 'bg-green-50 text-green-600',
      iconBg: 'bg-green-100',
      description: t('client.invoices.summary.thisYear')
    },
    {
      title: t('client.invoices.summary.nextDue'),
      value: totalOutstanding > 0 ? formatCurrency(totalOutstanding) : formatCurrency(0),
      icon: TrendingUp,
      color: 'bg-blue-50 text-blue-600',
      iconBg: 'bg-blue-100',
      description: t('client.invoices.summary.dueThisMonth')
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
      {cards.map((card, index) => {
        const Icon = card.icon;
        return (
          <Card key={index} className="p-6">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-text-secondary mb-1">
                  {card.title}
                </p>
                <p className="text-2xl font-bold text-text-primary mb-2">
                  {card.value}
                </p>
                <p className="text-sm text-text-secondary">
                  {card.description}
                </p>
              </div>
              <div className={`p-3 rounded-lg ${card.iconBg}`}>
                <Icon className={`w-6 h-6 ${card.color.split(' ')[1]}`} />
              </div>
            </div>
          </Card>
        );
      })}
    </div>
  );
};