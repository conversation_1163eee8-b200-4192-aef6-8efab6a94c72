import { UserRoleType } from '../types/auth';

export interface PermissionConfig {
  roles: UserRoleType[];
  // Additional permission checks can be added here
}

// Define permissions for different features
export const permissions = {
  // User Management
  viewAllClients: { roles: [UserRoleType.MANAGER] },
  viewAssignedClients: { roles: [UserRoleType.TUTOR] },
  viewAllTutors: { roles: [UserRoleType.MANAGER] },
  viewTutorDetails: { roles: [UserRoleType.MANAGER, UserRoleType.TUTOR] },
  editTutorRates: { roles: [UserRoleType.MANAGER] },
  addUsers: { roles: [UserRoleType.MANAGER] },
  
  // Client Permissions
  viewOwnProfile: { roles: [UserRoleType.CLIENT, UserRoleType.MANAGER, UserRoleType.TUTOR] },
  editOwnProfile: { roles: [UserRoleType.CLIENT, UserRoleType.MANAGER, UserRoleType.TUTOR] },
  viewOwnDependants: { roles: [UserRoleType.CLIENT] },
  editOwnDependants: { roles: [UserRoleType.CLIENT] },
  
  // Billing
  viewAllInvoices: { roles: [UserRoleType.MANAGER] },
  viewOwnInvoices: { roles: [UserRoleType.CLIENT] },
  viewTutorPayments: { roles: [UserRoleType.MANAGER] },
  viewOwnPayments: { roles: [UserRoleType.TUTOR] },
  viewAllSubscriptions: { roles: [UserRoleType.MANAGER] },
  viewOwnSubscription: { roles: [UserRoleType.CLIENT] },
  managePackages: { roles: [UserRoleType.MANAGER] },
  
  // Calendar
  viewAllAppointments: { roles: [UserRoleType.MANAGER] },
  viewTutorAppointments: { roles: [UserRoleType.TUTOR] },
  viewOwnAppointments: { roles: [UserRoleType.CLIENT] },
  bookAppointment: { roles: [UserRoleType.CLIENT, UserRoleType.MANAGER] },
  confirmAppointment: { roles: [UserRoleType.TUTOR] },
  viewTutorSchedule: { roles: [UserRoleType.TUTOR] },
  
  // Reports
  viewReports: { roles: [UserRoleType.MANAGER] },
  
  // Messages
  viewAllMessages: { roles: [UserRoleType.MANAGER] },
  manageTemplates: { roles: [UserRoleType.MANAGER] },
  sendBroadcasts: { roles: [UserRoleType.MANAGER] },
  
  // Settings
  viewSystemSettings: { roles: [UserRoleType.MANAGER] },
  manageUserSettings: { roles: [UserRoleType.MANAGER] },
  viewPaymentSettings: { roles: [UserRoleType.MANAGER] },
  manageServiceSettings: { roles: [UserRoleType.MANAGER] },
  viewOwnNotificationSettings: { roles: [UserRoleType.CLIENT, UserRoleType.TUTOR, UserRoleType.MANAGER] },
  viewApiSettings: { roles: [UserRoleType.MANAGER] },
  
  // User Management
  resetUserPassword: { roles: [UserRoleType.MANAGER] },
};

// Check if user has permission
export const hasPermission = (
  userRole: UserRoleType | undefined,
  permission: keyof typeof permissions
): boolean => {
  if (!userRole) return false;
  const config = permissions[permission];
  return config.roles.includes(userRole);
};

// Check multiple permissions (AND logic)
export const hasAllPermissions = (
  userRole: UserRoleType | undefined,
  ...permissionKeys: (keyof typeof permissions)[]
): boolean => {
  return permissionKeys.every(permission => hasPermission(userRole, permission));
};

// Check multiple permissions (OR logic)
export const hasAnyPermission = (
  userRole: UserRoleType | undefined,
  ...permissionKeys: (keyof typeof permissions)[]
): boolean => {
  return permissionKeys.some(permission => hasPermission(userRole, permission));
};

// Get filtered data based on role
export const filterDataByRole = <T extends { userId?: number; user_id?: number; client_id?: number }>(
  data: T[],
  userRole: UserRoleType | undefined,
  userId: number | undefined
): T[] => {
  if (!userRole || !userId) return [];
  
  // Managers can see everything
  if (userRole === UserRoleType.MANAGER) return data;
  
  // Clients can only see their own data
  if (userRole === UserRoleType.CLIENT) {
    return data.filter(item => 
      item.userId === userId || 
      item.user_id === userId || 
      item.client_id === userId
    );
  }
  
  // Tutors can see their assigned data
  if (userRole === UserRoleType.TUTOR) {
    // This would need to be implemented based on tutor assignments
    return data;
  }
  
  return [];
};

// Sidebar filtering based on role
export const filterSidebarSections = (sections: any[], userRole: UserRoleType | undefined) => {
  if (!userRole) return [];
  
  // Filter sections and sub-items based on role
  return sections.filter(section => {
    // Check if section is allowed for this role
    if (!section.roles || !section.roles.includes(userRole)) {
      return false;
    }
    
    // For clients, filter out certain sub-items
    if (userRole === UserRoleType.CLIENT && section.subItems) {
      section.subItems = section.subItems.filter((item: any) => {
        // Clients can only see certain sub-items
        const clientAllowedPaths = [
          '/users/clients', // Only their own profile
          '/billing/invoices', // Only their invoices
          '/billing/subscriptions', // Only their subscription
          '/messages/chat', // Only chat, not SMS threads or broadcasts
          '/settings/notifications', // Only notification settings
        ];
        
        return clientAllowedPaths.includes(item.path);
      });
    }
    
    return true;
  });
};