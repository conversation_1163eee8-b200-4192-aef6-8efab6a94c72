"""
User search and filtering models for TutorAide application.
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from enum import Enum
from pydantic import BaseModel, Field, field_validator, ConfigDict

from app.models.user_models import UserRoleType


class SearchEntityType(str, Enum):
    """Types of entities that can be searched."""
    USER = "user"
    CLIENT = "client"
    TUTOR = "tutor"
    DEPENDANT = "dependant"
    ALL = "all"


class SearchSortBy(str, Enum):
    """Search sorting options."""
    RELEVANCE = "relevance"
    NAME = "name"
    EMAIL = "email"
    CREATED_DATE = "created_date"
    LAST_ACTIVITY = "last_activity"
    VERIFICATION_STATUS = "verification_status"


class SearchSortOrder(str, Enum):
    """Search sort order."""
    ASC = "asc"
    DESC = "desc"


class UserSearchRequest(BaseModel):
    """Request model for user search with filtering."""
    model_config = ConfigDict(from_attributes=True)
    
    # Basic search parameters
    query: Optional[str] = Field(None, description="Search query text", max_length=100)
    entity_type: SearchEntityType = Field(SearchEntityType.ALL, description="Type of entity to search")
    
    # Pagination
    page: int = Field(1, description="Page number (1-based)", ge=1)
    page_size: int = Field(20, description="Items per page", ge=1, le=100)
    
    # Sorting
    sort_by: SearchSortBy = Field(SearchSortBy.RELEVANCE, description="Sort field")
    sort_order: SearchSortOrder = Field(SearchSortOrder.ASC, description="Sort order")
    
    # Role-based filtering
    roles: Optional[List[UserRoleType]] = Field(None, description="Filter by user roles")
    
    # Status filtering
    active_only: bool = Field(True, description="Include only active users")
    verified_only: Optional[bool] = Field(None, description="Filter by verification status")
    
    # Location filtering (for tutors)
    postal_code: Optional[str] = Field(None, description="Filter by postal code", max_length=7)
    radius_km: Optional[int] = Field(None, description="Search radius in kilometers", ge=1, le=100)
    
    # Date filtering
    created_after: Optional[datetime] = Field(None, description="Filter by creation date (after)")
    created_before: Optional[datetime] = Field(None, description="Filter by creation date (before)")
    
    @field_validator('query')
    @classmethod
    def validate_query(cls, v: Optional[str]) -> Optional[str]:
        """Validate and sanitize search query."""
        if v is None:
            return v
        
        # Trim whitespace
        v = v.strip()
        if not v:
            return None
            
        # Basic SQL injection prevention
        dangerous_chars = [';', '--', '/*', '*/', 'xp_', 'sp_']
        for char in dangerous_chars:
            if char in v.lower():
                raise ValueError("Invalid characters in search query")
        
        return v
    
    @field_validator('postal_code')
    @classmethod
    def validate_postal_code(cls, v: Optional[str]) -> Optional[str]:
        """Validate Canadian postal code format."""
        if v is None:
            return v
        
        # Remove spaces and convert to uppercase
        v = v.replace(" ", "").upper()
        
        # Canadian postal code pattern: A1A 1A1
        import re
        pattern = r"^[A-Z]\d[A-Z]\d[A-Z]\d$"
        if not re.match(pattern, v):
            raise ValueError("Invalid Canadian postal code format")
        
        # Format with space
        return f"{v[:3]} {v[3:]}"


class AutocompleteRequest(BaseModel):
    """Request model for autocomplete suggestions."""
    model_config = ConfigDict(from_attributes=True)
    
    query: str = Field(..., description="Partial search query", min_length=1, max_length=50)
    entity_type: SearchEntityType = Field(SearchEntityType.ALL, description="Type of entity to search")
    limit: int = Field(10, description="Maximum suggestions to return", ge=1, le=50)
    
    @field_validator('query')
    @classmethod
    def validate_query(cls, v: str) -> str:
        """Validate and sanitize autocomplete query."""
        # Trim whitespace
        v = v.strip()
        if not v:
            raise ValueError("Query cannot be empty")
            
        # Basic SQL injection prevention
        dangerous_chars = [';', '--', '/*', '*/', 'xp_', 'sp_']
        for char in dangerous_chars:
            if char in v.lower():
                raise ValueError("Invalid characters in search query")
        
        return v


class UserSearchResult(BaseModel):
    """Individual user search result."""
    model_config = ConfigDict(from_attributes=True)
    
    user_id: int = Field(..., description="User ID")
    email: str = Field(..., description="User email")
    first_name: Optional[str] = Field(None, description="First name")
    last_name: Optional[str] = Field(None, description="Last name")
    full_name: Optional[str] = Field(None, description="Full name")
    roles: List[UserRoleType] = Field(..., description="User roles")
    
    # Client-specific fields
    client_id: Optional[int] = Field(None, description="Client profile ID")
    phone_number: Optional[str] = Field(None, description="Phone number")
    postal_code: Optional[str] = Field(None, description="Postal code")
    
    # Tutor-specific fields
    tutor_id: Optional[int] = Field(None, description="Tutor profile ID")
    verification_status: Optional[str] = Field(None, description="Tutor verification status")
    specializations: Optional[List[str]] = Field(None, description="Tutor specializations")
    
    # Dependant-specific fields
    dependant_id: Optional[int] = Field(None, description="Dependant ID")
    primary_client_name: Optional[str] = Field(None, description="Primary client name")
    
    # Metadata
    entity_type: SearchEntityType = Field(..., description="Type of entity")
    created_at: datetime = Field(..., description="Creation date")
    last_activity: Optional[datetime] = Field(None, description="Last activity")
    is_active: bool = Field(..., description="Whether user is active")


class AutocompleteSuggestion(BaseModel):
    """Autocomplete suggestion item."""
    model_config = ConfigDict(from_attributes=True)
    
    id: str = Field(..., description="Unique identifier for the suggestion")
    text: str = Field(..., description="Display text")
    subtitle: Optional[str] = Field(None, description="Secondary text")
    entity_type: SearchEntityType = Field(..., description="Type of entity")
    match_field: str = Field(..., description="Field that matched the query")


class UserSearchResponse(BaseModel):
    """Response model for user search."""
    model_config = ConfigDict(from_attributes=True)
    
    results: List[UserSearchResult] = Field(..., description="Search results")
    total_count: int = Field(..., description="Total number of matching results")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Items per page")
    total_pages: int = Field(..., description="Total number of pages")
    has_next: bool = Field(..., description="Whether there are more pages")
    has_previous: bool = Field(..., description="Whether there are previous pages")
    
    # Search metadata
    query: Optional[str] = Field(None, description="Original search query")
    entity_type: SearchEntityType = Field(..., description="Entity type searched")
    sort_by: SearchSortBy = Field(..., description="Sort field used")
    sort_order: SearchSortOrder = Field(..., description="Sort order used")
    search_duration_ms: Optional[float] = Field(None, description="Search execution time")


class AutocompleteResponse(BaseModel):
    """Response model for autocomplete suggestions."""
    model_config = ConfigDict(from_attributes=True)
    
    suggestions: List[AutocompleteSuggestion] = Field(..., description="Autocomplete suggestions")
    query: str = Field(..., description="Original query")
    total_found: int = Field(..., description="Total suggestions found")
    search_duration_ms: Optional[float] = Field(None, description="Search execution time")


class SearchFilters(BaseModel):
    """Advanced search filters."""
    model_config = ConfigDict(from_attributes=True)
    
    # Role filters
    include_managers: bool = Field(True, description="Include managers in results")
    include_tutors: bool = Field(True, description="Include tutors in results")
    include_clients: bool = Field(True, description="Include clients in results")
    
    # Status filters
    verification_statuses: Optional[List[str]] = Field(None, description="Tutor verification statuses")
    account_statuses: Optional[List[str]] = Field(None, description="Account statuses")
    
    # Geographic filters
    provinces: Optional[List[str]] = Field(None, description="Canadian provinces")
    cities: Optional[List[str]] = Field(None, description="Cities")
    
    # Service filters (for tutors)
    subjects: Optional[List[str]] = Field(None, description="Teaching subjects")
    service_types: Optional[List[str]] = Field(None, description="Service types")
    
    # Availability filters
    available_now: Optional[bool] = Field(None, description="Currently available")
    available_weekends: Optional[bool] = Field(None, description="Available on weekends")


class SearchAnalytics(BaseModel):
    """Search analytics data."""
    model_config = ConfigDict(from_attributes=True)
    
    search_id: str = Field(..., description="Unique search identifier")
    user_id: int = Field(..., description="User who performed the search")
    query: Optional[str] = Field(None, description="Search query")
    entity_type: SearchEntityType = Field(..., description="Entity type searched")
    results_count: int = Field(..., description="Number of results returned")
    duration_ms: float = Field(..., description="Search execution time")
    clicked_result_id: Optional[str] = Field(None, description="ID of clicked result")
    session_id: Optional[str] = Field(None, description="Session identifier")
    timestamp: datetime = Field(..., description="Search timestamp")