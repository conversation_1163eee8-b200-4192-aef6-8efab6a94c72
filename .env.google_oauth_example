# Google OAuth Configuration
# Get these values from Google Cloud Console: https://console.cloud.google.com/

# Google OAuth Client ID
GOOGLE_CLIENT_ID=your-client-id-here.apps.googleusercontent.com

# Google OAuth Client Secret  
GOOGLE_CLIENT_SECRET=your-client-secret-here

# Google OAuth Redirect URI
# This must match exactly what's configured in Google Cloud Console
# For local development:
GOOGLE_REDIRECT_URI=http://localhost:8000/api/v1/auth/google/callback

# For production:
# GOOGLE_REDIRECT_URI=https://api.tutoraide.ca/api/v1/auth/google/callback

# Instructions:
# 1. Go to Google Cloud Console
# 2. Create a new project or select existing
# 3. Enable Google+ API
# 4. Create OAuth 2.0 credentials
# 5. Add authorized redirect URIs:
#    - http://localhost:8000/api/v1/auth/google/callback (for development)
#    - https://api.tutoraide.ca/api/v1/auth/google/callback (for production)
# 6. Copy Client ID and Client Secret here
# 7. Rename this file to .env and update with your values