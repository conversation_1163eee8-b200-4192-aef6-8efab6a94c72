"""
Service layer for package management.
"""

from typing import Optional, Dict, Any
from datetime import date, datetime, timedelta
from decimal import Decimal
import asyncpg

from app.models.billing_models import (
    PackageCreate, PackageUpdate, PackagePurchaseCreate,
    PackageType, PackageStatus
)
from app.core.exceptions import ValidationError, ResourceNotFoundError, BusinessLogicError
from app.core.logging import <PERSON><PERSON><PERSON><PERSON><PERSON>og<PERSON>
from app.services.stripe_service import StripeService
from app.services.email_service import EmailService

logger = TutorAideLogger.get_logger(__name__)


class PackageService:
    def __init__(self, stripe_service: StripeService, email_service: EmailService):
        self.stripe_service = stripe_service
        self.email_service = email_service
    
    async def create_package(
        self,
        db: asyncpg.Connection,
        package_data: PackageCreate,
        created_by_user_id: int
    ) -> int:
        """Create a new package."""
        # Validate group package settings
        if package_data.is_group_package:
            if not package_data.min_participants or not package_data.max_participants:
                raise ValidationError(
                    "Group packages must specify min and max participants"
                )
            if package_data.min_participants > package_data.max_participants:
                raise ValidationError(
                    "Min participants cannot exceed max participants"
                )
        
        # Check if package name already exists
        existing = await db.fetchval(
            "SELECT package_id FROM billing_packages WHERE package_name = $1 AND deleted_at IS NULL",
            package_data.package_name
        )
        if existing:
            raise ValidationError(f"Package name '{package_data.package_name}' already exists")
        
        # Insert the package
        query = """
            INSERT INTO billing_packages (
                package_name, package_type, service_id,
                session_count, session_duration_minutes,
                total_price, currency_code,
                is_group_package, min_participants, max_participants,
                is_active, valid_from, valid_until,
                description, terms_and_conditions
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15
            ) RETURNING package_id
        """
        
        package_id = await db.fetchval(
            query,
            package_data.package_name,
            package_data.package_type.value,
            package_data.service_id,
            package_data.session_count,
            package_data.session_duration_minutes,
            str(package_data.total_price),
            package_data.currency_code,
            package_data.is_group_package,
            package_data.min_participants,
            package_data.max_participants,
            package_data.is_active,
            package_data.valid_from or date.today(),
            package_data.valid_until,
            package_data.description,
            package_data.terms_and_conditions
        )
        
        logger.info(f"Created package {package_id} by user {created_by_user_id}")
        return package_id
    
    async def update_package(
        self,
        db: asyncpg.Connection,
        package_id: int,
        package_data: PackageUpdate
    ) -> None:
        """Update an existing package."""
        # Check if package exists
        existing = await db.fetchrow(
            "SELECT * FROM billing_packages WHERE package_id = $1 AND deleted_at IS NULL",
            package_id
        )
        if not existing:
            raise ResourceNotFoundError(f"Package {package_id} not found")
        
        # Check for active purchases before allowing certain changes
        if package_data.session_count is not None or package_data.total_price is not None:
            active_purchases = await db.fetchval(
                """
                SELECT COUNT(*) FROM billing_package_purchases 
                WHERE package_id = $1 AND status = 'active'
                """,
                package_id
            )
            if active_purchases > 0:
                raise BusinessLogicError(
                    "Cannot change session count or price for packages with active purchases"
                )
        
        # Build update query dynamically
        updates = []
        params = [package_id]
        param_count = 1
        
        update_fields = {
            'package_name': package_data.package_name,
            'is_active': package_data.is_active,
            'valid_until': package_data.valid_until,
            'description': package_data.description,
            'terms_and_conditions': package_data.terms_and_conditions,
            'session_count': package_data.session_count,
            'session_duration_minutes': package_data.session_duration_minutes,
            'total_price': str(package_data.total_price) if package_data.total_price else None,
            'min_participants': package_data.min_participants,
            'max_participants': package_data.max_participants
        }
        
        for field, value in update_fields.items():
            if value is not None:
                param_count += 1
                updates.append(f"{field} = ${param_count}")
                params.append(value)
        
        if updates:
            updates.append("updated_at = NOW()")
            query = f"""
                UPDATE billing_packages 
                SET {', '.join(updates)}
                WHERE package_id = $1
            """
            await db.execute(query, *params)
            logger.info(f"Updated package {package_id}")
    
    async def delete_package(self, db: asyncpg.Connection, package_id: int) -> None:
        """Soft delete a package."""
        # Check for active purchases
        active_purchases = await db.fetchval(
            """
            SELECT COUNT(*) FROM billing_package_purchases 
            WHERE package_id = $1 AND status = 'active'
            """,
            package_id
        )
        if active_purchases > 0:
            raise BusinessLogicError(
                "Cannot delete package with active purchases"
            )
        
        # Soft delete
        result = await db.execute(
            """
            UPDATE billing_packages 
            SET deleted_at = NOW(), is_active = FALSE 
            WHERE package_id = $1 AND deleted_at IS NULL
            """,
            package_id
        )
        
        if result.split()[1] == '0':
            raise ResourceNotFoundError(f"Package {package_id} not found")
        
        logger.info(f"Soft deleted package {package_id}")
    
    async def purchase_package(
        self,
        db: asyncpg.Connection,
        package_id: int,
        purchase_data: PackagePurchaseCreate,
        purchased_by_user_id: int
    ) -> int:
        """Purchase a package for a client."""
        # Verify package exists and is active
        package = await db.fetchrow(
            """
            SELECT * FROM billing_packages 
            WHERE package_id = $1 AND deleted_at IS NULL AND is_active = TRUE
            """,
            package_id
        )
        if not package:
            raise ResourceNotFoundError("Package not found or not available")
        
        # Check validity dates
        today = date.today()
        if package['valid_from'] and today < package['valid_from']:
            raise BusinessLogicError("Package is not yet available")
        if package['valid_until'] and today > package['valid_until']:
            raise BusinessLogicError("Package has expired")
        
        # Process payment through Stripe
        try:
            payment_intent = await self.stripe_service.create_payment_intent(
                amount=int(package['total_price'] * 100),  # Convert to cents
                currency=package['currency_code'].lower(),
                metadata={
                    'package_id': str(package_id),
                    'client_id': str(purchase_data.client_id),
                    'type': 'package_purchase'
                }
            )
            stripe_payment_id = payment_intent.id
        except Exception as e:
            logger.error(f"Stripe payment failed: {str(e)}")
            raise BusinessLogicError("Payment processing failed")
        
        # Calculate expiry date (default 1 year for packages)
        expire_date = today + timedelta(days=365)
        if package['package_type'] == 'tecfee':
            expire_date = today + timedelta(days=180)  # 6 months for TECFEE
        
        # Create purchase record
        query = """
            INSERT INTO billing_package_purchases (
                package_id, client_id, dependant_id,
                purchase_date, sessions_remaining, expire_date,
                amount_paid, payment_method, stripe_payment_id,
                status, notes
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11
            ) RETURNING purchase_id
        """
        
        purchase_id = await db.fetchval(
            query,
            package_id,
            purchase_data.client_id,
            purchase_data.dependant_id,
            today,
            package['session_count'],
            expire_date,
            str(package['total_price']),
            purchase_data.payment_method,
            stripe_payment_id,
            'active',
            purchase_data.notes
        )
        
        # Send confirmation email
        await self._send_purchase_confirmation(
            db, purchase_id, package, purchase_data.client_id
        )
        
        logger.info(
            f"Package {package_id} purchased by client {purchase_data.client_id}, "
            f"purchase_id: {purchase_id}"
        )
        return purchase_id
    
    async def _send_purchase_confirmation(
        self,
        db: asyncpg.Connection,
        purchase_id: int,
        package: asyncpg.Record,
        client_id: int
    ) -> None:
        """Send purchase confirmation email to client."""
        # Get client details
        client = await db.fetchrow(
            """
            SELECT c.*, u.email 
            FROM client_profiles c
            JOIN user_accounts u ON c.user_id = u.user_id
            WHERE c.client_id = $1
            """,
            client_id
        )
        
        if client and client['email']:
            context = {
                'client_name': f"{client['first_name']} {client['last_name']}",
                'package_name': package['package_name'],
                'session_count': package['session_count'],
                'total_price': float(package['total_price']),
                'purchase_id': purchase_id
            }
            
            await self.email_service.send_template_email(
                to_email=client['email'],
                template_name='package_purchase_confirmation',
                context=context,
                language='fr' if client.get('preferred_language') == 'fr' else 'en'
            )


def get_package_service() -> PackageService:
    """Dependency injection for PackageService."""
    # For now, use placeholder services until Stripe is integrated
    from app.services.email_service import EmailService
    
    class MockStripeService:
        async def create_payment_intent(self, amount, currency, metadata):
            return type('obj', (object,), {'id': 'mock_payment_intent_123'})()
    
    stripe_service = MockStripeService()
    email_service = EmailService()
    
    return PackageService(stripe_service, email_service)