"""
AI-powered scheduling suggestions service.

Analyzes availability patterns, historical data, and constraints to provide
intelligent appointment scheduling recommendations.
"""

import logging
from datetime import datetime, timedelta, time
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import asyncio

from app.core.timezone import now_est, utc_to_est
from app.models.appointment_models import AppointmentStatus
from app.models.service_models import ServiceType, PackageType
from app.database.repositories.appointment_repository import AppointmentRepository
from app.database.repositories.tutor_repository import TutorRepository
from app.database.repositories.client_repository import ClientRepository

logger = logging.getLogger(__name__)


class SuggestionPriority(Enum):
    """Priority levels for scheduling suggestions."""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class SuggestionReason(Enum):
    """Reasons why a suggestion was made."""
    OPTIMAL_TIME = "optimal_time"
    TUTOR_PREFERENCE = "tutor_preference"
    CLIENT_HISTORY = "client_history"
    WORKLOAD_BALANCE = "workload_balance"
    TRAVEL_OPTIMIZATION = "travel_optimization"
    RECURRING_PATTERN = "recurring_pattern"
    CONFLICT_AVOIDANCE = "conflict_avoidance"


@dataclass
class SchedulingSuggestion:
    """Represents an AI-generated scheduling suggestion."""
    tutor_id: int
    client_id: int
    dependant_id: Optional[int]
    suggested_date: datetime
    suggested_start_time: time
    suggested_end_time: time
    duration: int  # minutes
    subject_area: str
    session_type: PackageType
    priority: SuggestionPriority
    confidence_score: float  # 0.0 to 1.0
    reasons: List[SuggestionReason]
    explanation: str
    alternative_slots: List[Dict[str, Any]]
    estimated_travel_time: Optional[int] = None
    cost_efficiency_score: float = 0.0


@dataclass
class AvailabilityPattern:
    """Represents a tutor's availability pattern."""
    tutor_id: int
    day_of_week: int  # 0=Monday, 6=Sunday
    start_time: time
    end_time: time
    preference_score: float  # 0.0 to 1.0
    historical_bookings: int
    success_rate: float
    average_rating: Optional[float]


@dataclass
class ClientPattern:
    """Represents a client's booking pattern."""
    client_id: int
    preferred_days: List[int]
    preferred_times: List[Tuple[time, time]]
    preferred_tutors: List[int]
    subject_preferences: Dict[str, float]
    session_frequency: float  # sessions per week
    average_duration: int
    cancellation_rate: float


class AISchedulingService:
    """AI-powered scheduling recommendation engine."""
    
    def __init__(
        self,
        appointment_repo: AppointmentRepository,
        tutor_repo: TutorRepository,
        client_repo: ClientRepository
    ):
        self.appointment_repo = appointment_repo
        self.tutor_repo = tutor_repo
        self.client_repo = client_repo
        
        # Configurable weights for scoring algorithm
        self.weights = {
            'tutor_preference': 0.25,
            'client_preference': 0.25,
            'historical_success': 0.20,
            'workload_balance': 0.15,
            'travel_optimization': 0.10,
            'time_efficiency': 0.05
        }
    
    async def get_scheduling_suggestions(
        self,
        client_id: int,
        subject_area: str,
        duration: int = 60,
        session_type: PackageType = PackageType.INDIVIDUAL,
        dependant_id: Optional[int] = None,
        preferred_date_range: Optional[Tuple[datetime, datetime]] = None,
        max_suggestions: int = 5
    ) -> List[SchedulingSuggestion]:
        """
        Generate AI-powered scheduling suggestions for a client.
        
        Args:
            client_id: ID of the client booking the session
            subject_area: Subject to be taught
            duration: Session duration in minutes
            session_type: Type of session (individual, group, tecfee)
            dependant_id: Optional dependant ID for the session
            preferred_date_range: Optional date range for suggestions
            max_suggestions: Maximum number of suggestions to return
        
        Returns:
            List of scheduling suggestions ordered by confidence score
        """
        try:
            logger.info(f"Generating scheduling suggestions for client {client_id}, subject {subject_area}")
            
            # Set default date range if not provided (next 14 days)
            if not preferred_date_range:
                start_date = now_est() + timedelta(hours=2)  # Allow 2-hour buffer
                end_date = start_date + timedelta(days=14)
                preferred_date_range = (start_date, end_date)
            
            # Gather data for analysis
            client_pattern = await self._analyze_client_pattern(client_id)
            suitable_tutors = await self._find_suitable_tutors(subject_area, session_type)
            availability_patterns = await self._analyze_tutor_availability_patterns(suitable_tutors)
            existing_appointments = await self._get_existing_appointments(preferred_date_range)
            
            # Generate suggestions
            suggestions = []
            
            for tutor_id in suitable_tutors:
                tutor_suggestions = await self._generate_tutor_suggestions(
                    tutor_id=tutor_id,
                    client_id=client_id,
                    dependant_id=dependant_id,
                    subject_area=subject_area,
                    duration=duration,
                    session_type=session_type,
                    client_pattern=client_pattern,
                    availability_patterns=availability_patterns.get(tutor_id, []),
                    existing_appointments=existing_appointments,
                    date_range=preferred_date_range
                )
                suggestions.extend(tutor_suggestions)
            
            # Score and rank suggestions
            scored_suggestions = await self._score_suggestions(suggestions)
            
            # Return top suggestions
            return sorted(scored_suggestions, key=lambda x: x.confidence_score, reverse=True)[:max_suggestions]
            
        except Exception as e:
            logger.error(f"Error generating scheduling suggestions: {e}")
            return []
    
    async def get_optimal_recurring_schedule(
        self,
        client_id: int,
        subject_area: str,
        sessions_per_week: int,
        duration: int = 60,
        weeks_ahead: int = 8
    ) -> List[SchedulingSuggestion]:
        """
        Generate optimal recurring schedule suggestions.
        
        Args:
            client_id: Client requesting recurring schedule
            subject_area: Subject to be taught
            sessions_per_week: Number of sessions per week
            duration: Session duration in minutes
            weeks_ahead: How many weeks to schedule ahead
        
        Returns:
            List of suggestions for recurring schedule
        """
        try:
            logger.info(f"Generating recurring schedule for client {client_id}: {sessions_per_week} sessions/week")
            
            client_pattern = await self._analyze_client_pattern(client_id)
            suitable_tutors = await self._find_suitable_tutors(subject_area, PackageType.INDIVIDUAL)
            
            # Find the best tutor for consistency
            best_tutor = await self._select_optimal_tutor_for_recurring(
                suitable_tutors, client_pattern, subject_area
            )
            
            if not best_tutor:
                return []
            
            # Generate recurring pattern
            return await self._generate_recurring_pattern(
                tutor_id=best_tutor,
                client_id=client_id,
                subject_area=subject_area,
                sessions_per_week=sessions_per_week,
                duration=duration,
                weeks_ahead=weeks_ahead,
                client_pattern=client_pattern
            )
            
        except Exception as e:
            logger.error(f"Error generating recurring schedule: {e}")
            return []
    
    async def suggest_appointment_rescheduling(
        self,
        appointment_id: int,
        reason: str = "conflict"
    ) -> List[SchedulingSuggestion]:
        """
        Suggest alternative times for rescheduling an appointment.
        
        Args:
            appointment_id: ID of appointment to reschedule
            reason: Reason for rescheduling
        
        Returns:
            List of alternative scheduling suggestions
        """
        try:
            # Get original appointment details
            appointment = await self.appointment_repo.get_by_id(appointment_id)
            if not appointment:
                return []
            
            # Generate suggestions with preference for similar time slots
            original_time = appointment.start_time.time()
            original_day = appointment.scheduled_date.weekday()
            
            suggestions = await self.get_scheduling_suggestions(
                client_id=appointment.client_id,
                subject_area=appointment.subject_area,
                duration=appointment.duration,
                session_type=appointment.session_type,
                dependant_id=appointment.dependant_id,
                preferred_date_range=(
                    now_est() + timedelta(hours=2),
                    now_est() + timedelta(days=14)
                )
            )
            
            # Boost scores for similar times and days
            for suggestion in suggestions:
                if suggestion.suggested_start_time.hour == original_time.hour:
                    suggestion.confidence_score += 0.1
                if suggestion.suggested_date.weekday() == original_day:
                    suggestion.confidence_score += 0.1
                
                suggestion.explanation += f" (Rescheduling due to {reason})"
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Error suggesting rescheduling for appointment {appointment_id}: {e}")
            return []
    
    async def _analyze_client_pattern(self, client_id: int) -> ClientPattern:
        """Analyze client's historical booking patterns."""
        try:
            # Get client's appointment history
            history = await self.appointment_repo.get_client_history(
                client_id, limit=50, days_back=180
            )
            
            if not history:
                # Return default pattern for new clients
                return ClientPattern(
                    client_id=client_id,
                    preferred_days=[1, 2, 3, 4, 5],  # Weekdays
                    preferred_times=[(time(16, 0), time(20, 0))],  # After school
                    preferred_tutors=[],
                    subject_preferences={},
                    session_frequency=1.0,
                    average_duration=60,
                    cancellation_rate=0.1
                )
            
            # Analyze patterns
            preferred_days = self._extract_preferred_days(history)
            preferred_times = self._extract_preferred_times(history)
            preferred_tutors = self._extract_preferred_tutors(history)
            subject_preferences = self._extract_subject_preferences(history)
            
            return ClientPattern(
                client_id=client_id,
                preferred_days=preferred_days,
                preferred_times=preferred_times,
                preferred_tutors=preferred_tutors,
                subject_preferences=subject_preferences,
                session_frequency=len(history) / 26.0,  # Sessions per week over 6 months
                average_duration=sum(apt.duration for apt in history) / len(history),
                cancellation_rate=len([apt for apt in history if apt.status == AppointmentStatus.CANCELLED]) / len(history)
            )
            
        except Exception as e:
            logger.error(f"Error analyzing client pattern for {client_id}: {e}")
            return ClientPattern(
                client_id=client_id,
                preferred_days=[1, 2, 3, 4, 5],
                preferred_times=[(time(16, 0), time(20, 0))],
                preferred_tutors=[],
                subject_preferences={},
                session_frequency=1.0,
                average_duration=60,
                cancellation_rate=0.1
            )
    
    async def _find_suitable_tutors(self, subject_area: str, session_type: PackageType) -> List[int]:
        """Find tutors suitable for the subject and session type."""
        # This would query the tutor repository for tutors who teach the subject
        # For now, return mock data
        return [1, 2, 3, 4, 5]
    
    async def _analyze_tutor_availability_patterns(self, tutor_ids: List[int]) -> Dict[int, List[AvailabilityPattern]]:
        """Analyze each tutor's availability patterns."""
        patterns = {}
        
        for tutor_id in tutor_ids:
            try:
                # Analyze tutor's historical availability and booking success
                tutor_patterns = []
                
                # Mock pattern analysis - in reality, this would analyze:
                # - Regular availability schedules
                # - Historical booking success rates
                # - Client satisfaction scores
                # - Preferred working hours
                
                for day in range(7):  # Monday to Sunday
                    if day < 5:  # Weekdays
                        tutor_patterns.extend([
                            AvailabilityPattern(
                                tutor_id=tutor_id,
                                day_of_week=day,
                                start_time=time(16, 0),
                                end_time=time(20, 0),
                                preference_score=0.8,
                                historical_bookings=50,
                                success_rate=0.95,
                                average_rating=4.5
                            )
                        ])
                    else:  # Weekends
                        tutor_patterns.extend([
                            AvailabilityPattern(
                                tutor_id=tutor_id,
                                day_of_week=day,
                                start_time=time(9, 0),
                                end_time=time(17, 0),
                                preference_score=0.6,
                                historical_bookings=20,
                                success_rate=0.90,
                                average_rating=4.3
                            )
                        ])
                
                patterns[tutor_id] = tutor_patterns
                
            except Exception as e:
                logger.error(f"Error analyzing patterns for tutor {tutor_id}: {e}")
                patterns[tutor_id] = []
        
        return patterns
    
    async def _get_existing_appointments(self, date_range: Tuple[datetime, datetime]) -> List[Any]:
        """Get existing appointments in the date range for conflict detection."""
        try:
            return await self.appointment_repo.get_appointments_in_range(
                date_range[0], date_range[1]
            )
        except Exception as e:
            logger.error(f"Error getting existing appointments: {e}")
            return []
    
    async def _generate_tutor_suggestions(
        self,
        tutor_id: int,
        client_id: int,
        dependant_id: Optional[int],
        subject_area: str,
        duration: int,
        session_type: PackageType,
        client_pattern: ClientPattern,
        availability_patterns: List[AvailabilityPattern],
        existing_appointments: List[Any],
        date_range: Tuple[datetime, datetime]
    ) -> List[SchedulingSuggestion]:
        """Generate suggestions for a specific tutor."""
        suggestions = []
        
        try:
            current_date = date_range[0].date()
            end_date = date_range[1].date()
            
            while current_date <= end_date:
                day_of_week = current_date.weekday()
                
                # Find availability patterns for this day
                day_patterns = [p for p in availability_patterns if p.day_of_week == day_of_week]
                
                for pattern in day_patterns:
                    # Generate time slots within the pattern
                    slots = self._generate_time_slots(
                        pattern.start_time,
                        pattern.end_time,
                        duration,
                        30  # 30-minute intervals
                    )
                    
                    for slot_start, slot_end in slots:
                        # Check for conflicts
                        if self._has_conflict(current_date, slot_start, slot_end, existing_appointments, tutor_id):
                            continue
                        
                        # Create suggestion
                        suggestion = SchedulingSuggestion(
                            tutor_id=tutor_id,
                            client_id=client_id,
                            dependant_id=dependant_id,
                            suggested_date=datetime.combine(current_date, slot_start),
                            suggested_start_time=slot_start,
                            suggested_end_time=slot_end,
                            duration=duration,
                            subject_area=subject_area,
                            session_type=session_type,
                            priority=SuggestionPriority.MEDIUM,
                            confidence_score=0.5,  # Will be calculated later
                            reasons=[],
                            explanation="",
                            alternative_slots=[]
                        )
                        
                        suggestions.append(suggestion)
                
                current_date += timedelta(days=1)
        
        except Exception as e:
            logger.error(f"Error generating tutor suggestions for {tutor_id}: {e}")
        
        return suggestions
    
    def _generate_time_slots(
        self,
        start_time: time,
        end_time: time,
        duration: int,
        interval: int = 30
    ) -> List[Tuple[time, time]]:
        """Generate available time slots within a time range."""
        slots = []
        
        current_time = datetime.combine(datetime.today(), start_time)
        end_datetime = datetime.combine(datetime.today(), end_time)
        
        while current_time + timedelta(minutes=duration) <= end_datetime:
            slot_start = current_time.time()
            slot_end = (current_time + timedelta(minutes=duration)).time()
            slots.append((slot_start, slot_end))
            current_time += timedelta(minutes=interval)
        
        return slots
    
    def _has_conflict(
        self,
        date: datetime.date,
        start_time: time,
        end_time: time,
        existing_appointments: List[Any],
        tutor_id: int
    ) -> bool:
        """Check if a time slot conflicts with existing appointments."""
        slot_start = datetime.combine(date, start_time)
        slot_end = datetime.combine(date, end_time)
        
        for appointment in existing_appointments:
            # Check if appointment is for the same tutor and overlaps
            if (appointment.tutor_id == tutor_id and 
                appointment.scheduled_date.date() == date):
                
                apt_start = datetime.combine(appointment.scheduled_date.date(), appointment.start_time)
                apt_end = datetime.combine(appointment.scheduled_date.date(), appointment.end_time)
                
                # Check for overlap
                if not (slot_end <= apt_start or slot_start >= apt_end):
                    return True
        
        return False
    
    async def _score_suggestions(self, suggestions: List[SchedulingSuggestion]) -> List[SchedulingSuggestion]:
        """Score and enhance suggestions with AI analysis."""
        for suggestion in suggestions:
            try:
                # Calculate base confidence score
                base_score = 0.5
                reasons = []
                explanation_parts = []
                
                # Time preference scoring
                if self._is_preferred_time(suggestion):
                    base_score += 0.2
                    reasons.append(SuggestionReason.CLIENT_HISTORY)
                    explanation_parts.append("matches your usual booking times")
                
                # Tutor quality scoring
                tutor_score = await self._get_tutor_quality_score(suggestion.tutor_id, suggestion.subject_area)
                base_score += tutor_score * 0.3
                if tutor_score > 0.8:
                    reasons.append(SuggestionReason.TUTOR_PREFERENCE)
                    explanation_parts.append("highly-rated tutor for this subject")
                
                # Workload balance
                if await self._is_balanced_workload(suggestion):
                    base_score += 0.1
                    reasons.append(SuggestionReason.WORKLOAD_BALANCE)
                    explanation_parts.append("optimal tutor workload")
                
                # Set final values
                suggestion.confidence_score = min(base_score, 1.0)
                suggestion.reasons = reasons
                suggestion.explanation = f"Recommended: {', '.join(explanation_parts)}"
                
                # Set priority based on score
                if suggestion.confidence_score >= 0.8:
                    suggestion.priority = SuggestionPriority.HIGH
                elif suggestion.confidence_score >= 0.6:
                    suggestion.priority = SuggestionPriority.MEDIUM
                else:
                    suggestion.priority = SuggestionPriority.LOW
                
            except Exception as e:
                logger.error(f"Error scoring suggestion: {e}")
                suggestion.confidence_score = 0.3
                suggestion.priority = SuggestionPriority.LOW
        
        return suggestions
    
    def _is_preferred_time(self, suggestion: SchedulingSuggestion) -> bool:
        """Check if suggestion time matches client preferences."""
        # This would check against client pattern analysis
        # For now, prefer afternoon/evening times
        hour = suggestion.suggested_start_time.hour
        return 16 <= hour <= 20
    
    async def _get_tutor_quality_score(self, tutor_id: int, subject_area: str) -> float:
        """Get tutor quality score for specific subject."""
        try:
            # This would calculate based on:
            # - Average ratings for this subject
            # - Success rate
            # - Client retention
            # - Specialization level
            return 0.85  # Mock score
        except Exception as e:
            logger.error(f"Error getting tutor quality score: {e}")
            return 0.5
    
    async def _is_balanced_workload(self, suggestion: SchedulingSuggestion) -> bool:
        """Check if tutor has balanced workload."""
        # This would check tutor's current appointment load
        return True
    
    # Helper methods for pattern analysis
    def _extract_preferred_days(self, history: List[Any]) -> List[int]:
        """Extract preferred days from appointment history."""
        day_counts = {}
        for appointment in history:
            day = appointment.scheduled_date.weekday()
            day_counts[day] = day_counts.get(day, 0) + 1
        
        # Return days with above-average frequency
        avg_count = len(history) / 7
        return [day for day, count in day_counts.items() if count > avg_count]
    
    def _extract_preferred_times(self, history: List[Any]) -> List[Tuple[time, time]]:
        """Extract preferred time ranges from appointment history."""
        # Analyze time patterns and return common ranges
        return [(time(16, 0), time(20, 0))]  # Mock data
    
    def _extract_preferred_tutors(self, history: List[Any]) -> List[int]:
        """Extract preferred tutors from appointment history."""
        tutor_counts = {}
        for appointment in history:
            tutor_id = appointment.tutor_id
            tutor_counts[tutor_id] = tutor_counts.get(tutor_id, 0) + 1
        
        # Return tutors with multiple sessions
        return [tutor_id for tutor_id, count in tutor_counts.items() if count > 2]
    
    def _extract_subject_preferences(self, history: List[Any]) -> Dict[str, float]:
        """Extract subject preferences and success rates."""
        subject_data = {}
        for appointment in history:
            subject = appointment.subject_area
            if subject not in subject_data:
                subject_data[subject] = {'count': 0, 'success': 0}
            
            subject_data[subject]['count'] += 1
            if appointment.status in [AppointmentStatus.COMPLETED]:
                subject_data[subject]['success'] += 1
        
        # Return success rates
        return {
            subject: data['success'] / data['count'] if data['count'] > 0 else 0.0
            for subject, data in subject_data.items()
        }
    
    async def _select_optimal_tutor_for_recurring(
        self,
        tutor_ids: List[int],
        client_pattern: ClientPattern,
        subject_area: str
    ) -> Optional[int]:
        """Select the best tutor for a recurring schedule."""
        if not tutor_ids:
            return None
        
        # Prefer tutors client has worked with before
        if client_pattern.preferred_tutors:
            for tutor_id in client_pattern.preferred_tutors:
                if tutor_id in tutor_ids:
                    return tutor_id
        
        # Otherwise, return first available (would be more sophisticated in reality)
        return tutor_ids[0]
    
    async def _generate_recurring_pattern(
        self,
        tutor_id: int,
        client_id: int,
        subject_area: str,
        sessions_per_week: int,
        duration: int,
        weeks_ahead: int,
        client_pattern: ClientPattern
    ) -> List[SchedulingSuggestion]:
        """Generate a recurring schedule pattern."""
        suggestions = []
        
        try:
            # Use client's preferred days
            preferred_days = client_pattern.preferred_days[:sessions_per_week]
            preferred_time = client_pattern.preferred_times[0][0] if client_pattern.preferred_times else time(17, 0)
            
            start_date = now_est().date() + timedelta(days=1)
            
            for week in range(weeks_ahead):
                week_start = start_date + timedelta(weeks=week)
                
                for day_offset in preferred_days:
                    session_date = week_start + timedelta(days=day_offset)
                    
                    suggestion = SchedulingSuggestion(
                        tutor_id=tutor_id,
                        client_id=client_id,
                        dependant_id=None,
                        suggested_date=datetime.combine(session_date, preferred_time),
                        suggested_start_time=preferred_time,
                        suggested_end_time=(datetime.combine(session_date, preferred_time) + timedelta(minutes=duration)).time(),
                        duration=duration,
                        subject_area=subject_area,
                        session_type=PackageType.INDIVIDUAL,
                        priority=SuggestionPriority.HIGH,
                        confidence_score=0.9,
                        reasons=[SuggestionReason.RECURRING_PATTERN, SuggestionReason.CLIENT_HISTORY],
                        explanation=f"Recurring session pattern based on your preferences",
                        alternative_slots=[]
                    )
                    
                    suggestions.append(suggestion)
            
        except Exception as e:
            logger.error(f"Error generating recurring pattern: {e}")
        
        return suggestions