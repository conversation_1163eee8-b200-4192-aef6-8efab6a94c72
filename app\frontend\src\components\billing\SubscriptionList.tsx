import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { format, differenceInDays } from 'date-fns';
import { 
  Search, Filter, Package, Clock, AlertCircle, 
  ChevronLeft, ChevronRight, User, Calendar, 
  BarChart, RefreshCw, Plus, Edit
} from 'lucide-react';
import api from '../../services/api';
import { Card } from '../common/Card';
import Button from '../common/Button';
import { Input } from '../common/Input';
import { Select } from '../common/Select';
import { Badge } from '../common/Badge';
import { Modal } from '../common/Modal';
import LoadingSpinner from '../ui/LoadingSpinner';
import { EmptyState } from '../common/EmptyState';
import toast from 'react-hot-toast';

interface SubscriptionPackage {
  package_id: number;
  name: string;
  description?: string;
  hours_included: number;
  price: number;
  billing_frequency: 'one_time' | 'monthly' | 'quarterly' | 'annual';
  is_active: boolean;
  subject_restrictions?: string[];
}

interface Subscription {
  subscription_id: number;
  client_id: number;
  client_name: string;
  package_id: number;
  package_name: string;
  status: 'active' | 'expired' | 'suspended' | 'cancelled' | 'depleted';
  start_date: string;
  end_date: string;
  hours_purchased: number;
  hours_used: number;
  hours_remaining: number;
  auto_renew: boolean;
  created_at: string;
  last_used_at?: string;
  billing_frequency: string;
  price_paid: number;
}

interface SubscriptionUsage {
  usage_id: number;
  appointment_id: number;
  hours_deducted: number;
  remaining_hours_after: number;
  usage_date: string;
  client_name?: string;
  tutor_name?: string;
  subject?: string;
}

interface SubscriptionListResponse {
  subscriptions: Subscription[];
  total: number;
  limit: number;
  offset: number;
  has_more: boolean;
}

const SubscriptionList: React.FC = () => {
  const { t } = useTranslation();
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [packages, setPackages] = useState<SubscriptionPackage[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20);
  
  // Filters
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [showFilters, setShowFilters] = useState(false);
  const [showExpiringOnly, setShowExpiringOnly] = useState(false);
  
  // Modal states
  const [selectedSubscription, setSelectedSubscription] = useState<number | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showRenewModal, setShowRenewModal] = useState(false);
  const [showUsageModal, setShowUsageModal] = useState(false);
  const [usageHistory, setUsageHistory] = useState<SubscriptionUsage[]>([]);

  // Fetch packages
  useEffect(() => {
    const fetchPackages = async () => {
      try {
        const response = await api.get<SubscriptionPackage[]>('/subscriptions/packages');
        setPackages(response.data);
      } catch (error) {
        console.error('Error fetching packages:', error);
      }
    };
    
    fetchPackages();
  }, []);

  const fetchSubscriptions = useCallback(async () => {
    try {
      setLoading(true);
      
      const params = new URLSearchParams();
      if (statusFilter) params.append('status', statusFilter);
      if (showExpiringOnly) params.append('expiring_days', '30');
      params.append('limit', pageSize.toString());
      params.append('offset', ((currentPage - 1) * pageSize).toString());

      // Fetch subscriptions
      const endpoint = '/subscriptions/';
      
      const response = await api.get<SubscriptionListResponse>(`${endpoint}?${params}`);
      
      // Filter by search term on client side if needed
      let filteredSubscriptions = response.data.subscriptions || response.data;
      if (searchTerm) {
        const term = searchTerm.toLowerCase();
        filteredSubscriptions = filteredSubscriptions.filter(sub =>
          sub.client_name.toLowerCase().includes(term) ||
          sub.package_name.toLowerCase().includes(term)
        );
      }
      
      setSubscriptions(filteredSubscriptions);
      setTotalCount(response.data.total || filteredSubscriptions.length);
    } catch (error) {
      console.error('Error fetching subscriptions:', error);
      toast.error(t('billing.errors.fetchSubscriptions'));
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize, statusFilter, showExpiringOnly, searchTerm, t]);

  useEffect(() => {
    fetchSubscriptions();
  }, [fetchSubscriptions]);

  const getStatusBadge = (status: string, endDate: string) => {
    const daysUntilExpiry = differenceInDays(new Date(endDate), new Date());
    
    switch (status) {
      case 'active':
        if (daysUntilExpiry <= 7) {
          return <Badge variant="warning">{t('billing.subscriptions.status.expiringSoon')}</Badge>;
        }
        return <Badge variant="success">{t('billing.subscriptions.status.active')}</Badge>;
      case 'expired':
        return <Badge variant="danger">{t('billing.subscriptions.status.expired')}</Badge>;
      case 'depleted':
        return <Badge variant="secondary">{t('billing.subscriptions.status.depleted')}</Badge>;
      case 'suspended':
        return <Badge variant="warning">{t('billing.subscriptions.status.suspended')}</Badge>;
      case 'cancelled':
        return <Badge variant="secondary">{t('billing.subscriptions.status.cancelled')}</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD'
    }).format(amount);
  };

  const handleRenewSubscription = async (subscriptionId: number) => {
    try {
      const response = await api.post(`/subscriptions/${subscriptionId}/renew`);
      toast.success(t('billing.subscriptions.renewalSuccess'));
      fetchSubscriptions();
      setShowRenewModal(false);
    } catch (error) {
      console.error('Error renewing subscription:', error);
      toast.error(t('billing.errors.renewSubscription'));
    }
  };

  const fetchUsageHistory = async (subscriptionId: number) => {
    try {
      const response = await api.get<SubscriptionUsage[]>(`/subscriptions/${subscriptionId}/usage`);
      setUsageHistory(response.data);
      setShowUsageModal(true);
    } catch (error) {
      console.error('Error fetching usage history:', error);
      toast.error(t('billing.errors.fetchUsageHistory'));
    }
  };

  const clearFilters = () => {
    setSearchTerm('');
    setStatusFilter('');
    setShowExpiringOnly(false);
    setCurrentPage(1);
  };

  const totalPages = Math.ceil(totalCount / pageSize);

  // Calculate summary stats
  const activeCount = subscriptions.filter(s => s.status === 'active').length;
  const expiringCount = subscriptions.filter(s => {
    if (s.status !== 'active') return false;
    const daysUntilExpiry = differenceInDays(new Date(s.end_date), new Date());
    return daysUntilExpiry <= 30;
  }).length;
  const totalHoursRemaining = subscriptions.reduce((sum, s) => sum + s.hours_remaining, 0);

  if (loading && subscriptions.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">{t('billing.subscriptions.activeSubscriptions')}</p>
              <p className="text-2xl font-semibold text-gray-900">{activeCount}</p>
            </div>
            <Package className="w-8 h-8 text-green-600" />
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">{t('billing.subscriptions.expiringThisMonth')}</p>
              <p className="text-2xl font-semibold text-gray-900">{expiringCount}</p>
            </div>
            <AlertCircle className="w-8 h-8 text-yellow-600" />
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">{t('billing.subscriptions.totalHoursRemaining')}</p>
              <p className="text-2xl font-semibold text-gray-900">{totalHoursRemaining.toFixed(1)}</p>
            </div>
            <Clock className="w-8 h-8 text-red-600" />
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">{t('billing.subscriptions.activePackages')}</p>
              <p className="text-2xl font-semibold text-gray-900">{packages.length}</p>
            </div>
            <BarChart className="w-8 h-8 text-accent-red" />
          </div>
        </Card>
      </div>

      {/* Header with Search and Filters */}
      <div className="bg-white rounded-xl shadow-soft p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <Input
                type="text"
                placeholder={t('billing.subscriptions.searchSubscriptions')}
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                  setCurrentPage(1);
                }}
                className="pl-10"
              />
            </div>
          </div>
          
          {/* Actions */}
          <div className="flex gap-3">
            <Button
              variant="secondary"
              leftIcon={<Filter className="w-4 h-4" />}
              onClick={() => setShowFilters(!showFilters)}
              className="relative"
            >
              {t('common.filters')}
              {(statusFilter || showExpiringOnly) && (
                <span className="absolute -top-1 -right-1 w-2 h-2 bg-accent-red rounded-full"></span>
              )}
            </Button>
            
            <Button
              variant="primary"
              leftIcon={<Plus className="w-4 h-4" />}
              onClick={() => toast.info('Create subscription modal to be implemented')}
            >
              {t('billing.subscriptions.newSubscription')}
            </Button>
          </div>
        </div>

        {/* Expanded Filters */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              {/* Status Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('billing.status.label')}
                </label>
                <Select
                  value={statusFilter}
                  onChange={(e) => {
                    setStatusFilter(e.target.value);
                    setCurrentPage(1);
                  }}
                >
                  <option value="">{t('common.all')}</option>
                  <option value="active">{t('billing.subscriptions.status.active')}</option>
                  <option value="expired">{t('billing.subscriptions.status.expired')}</option>
                  <option value="depleted">{t('billing.subscriptions.status.depleted')}</option>
                  <option value="suspended">{t('billing.subscriptions.status.suspended')}</option>
                  <option value="cancelled">{t('billing.subscriptions.status.cancelled')}</option>
                </Select>
              </div>

              {/* Quick Filters */}
              <div className="sm:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('billing.subscriptions.quickFilters')}
                </label>
                <div className="flex gap-3">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={showExpiringOnly}
                      onChange={(e) => {
                        setShowExpiringOnly(e.target.checked);
                        setCurrentPage(1);
                      }}
                      className="rounded border-gray-300 text-accent-red focus:ring-accent-red mr-2"
                    />
                    <span className="text-sm text-gray-700">{t('billing.subscriptions.showExpiringOnly')}</span>
                  </label>
                </div>
              </div>
            </div>

            {/* Clear Filters */}
            <div className="mt-4 flex justify-end">
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
              >
                {t('common.clearFilters')}
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Subscriptions List */}
      {subscriptions.length === 0 ? (
        <Card>
          <EmptyState
            icon={<Package className="w-12 h-12 text-gray-400" />}
            title={t('billing.subscriptions.noSubscriptions')}
            description={t('billing.subscriptions.noSubscriptionsDescription')}
            action={
              <Button
                variant="primary"
                leftIcon={<Plus className="w-4 h-4" />}
                onClick={() => toast.info('Create subscription modal to be implemented')}
              >
                {t('billing.subscriptions.createFirst')}
              </Button>
            }
          />
        </Card>
      ) : (
        <div className="bg-white rounded-xl shadow-soft overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('billing.subscriptions.client')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('billing.subscriptions.package')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('billing.subscriptions.period')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('billing.subscriptions.hoursUsage')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('billing.status.label')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('billing.subscriptions.autoRenew')}
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('common.actions')}
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {subscriptions.map((subscription) => {
                  const usagePercentage = (subscription.hours_used / subscription.hours_purchased) * 100;
                  const daysUntilExpiry = differenceInDays(new Date(subscription.end_date), new Date());
                  
                  return (
                    <tr key={subscription.subscription_id} className="hover:bg-gray-50 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <User className="w-4 h-4 text-gray-400 mr-2" />
                          <div>
                            <p className="text-sm font-medium text-gray-900">
                              {subscription.client_name}
                            </p>
                            <p className="text-xs text-gray-500">
                              ID: {subscription.subscription_id}
                            </p>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <p className="text-sm text-gray-900">{subscription.package_name}</p>
                          <p className="text-xs text-gray-500">
                            {formatCurrency(subscription.price_paid)}
                          </p>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {format(new Date(subscription.start_date), 'MMM d, yyyy')}
                        </div>
                        <div className="text-xs text-gray-500">
                          to {format(new Date(subscription.end_date), 'MMM d, yyyy')}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="flex items-center">
                            <div className="text-sm font-medium text-gray-900">
                              {subscription.hours_remaining.toFixed(1)} / {subscription.hours_purchased}h
                            </div>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                            <div
                              className={`h-2 rounded-full ${
                                usagePercentage > 80 ? 'bg-red-600' : 
                                usagePercentage > 50 ? 'bg-yellow-600' : 
                                'bg-green-600'
                              }`}
                              style={{ width: `${usagePercentage}%` }}
                            />
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(subscription.status, subscription.end_date)}
                        {subscription.status === 'active' && daysUntilExpiry <= 30 && (
                          <p className="text-xs text-yellow-600 mt-1">
                            {t('billing.subscriptions.expiresInDays', { days: daysUntilExpiry })}
                          </p>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {subscription.auto_renew ? (
                          <Badge variant="success">{t('common.yes')}</Badge>
                        ) : (
                          <Badge variant="secondary">{t('common.no')}</Badge>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedSubscription(subscription.subscription_id);
                              fetchUsageHistory(subscription.subscription_id);
                            }}
                          >
                            {t('billing.subscriptions.viewUsage')}
                          </Button>
                          {subscription.status === 'active' && (
                            <Button
                              variant="ghost"
                              size="sm"
                              leftIcon={<RefreshCw className="w-4 h-4" />}
                              onClick={() => {
                                setSelectedSubscription(subscription.subscription_id);
                                setShowRenewModal(true);
                              }}
                            >
                              {t('billing.subscriptions.renew')}
                            </Button>
                          )}
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="bg-gray-50 px-6 py-3 flex items-center justify-between border-t border-gray-200">
              <div className="flex-1 flex justify-between sm:hidden">
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                >
                  {t('common.previous')}
                </Button>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                >
                  {t('common.next')}
                </Button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    {t('common.showing')} <span className="font-medium">{((currentPage - 1) * pageSize) + 1}</span> {t('common.to')}{' '}
                    <span className="font-medium">{Math.min(currentPage * pageSize, totalCount)}</span> {t('common.of')}{' '}
                    <span className="font-medium">{totalCount}</span> {t('common.results')}
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <button
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <ChevronLeft className="h-5 w-5" />
                    </button>
                    {[...Array(Math.min(5, totalPages))].map((_, idx) => {
                      const pageNumber = currentPage - 2 + idx;
                      if (pageNumber < 1 || pageNumber > totalPages) return null;
                      return (
                        <button
                          key={pageNumber}
                          onClick={() => setCurrentPage(pageNumber)}
                          className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                            pageNumber === currentPage
                              ? 'z-10 bg-accent-red border-accent-red text-white'
                              : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                          }`}
                        >
                          {pageNumber}
                        </button>
                      );
                    })}
                    <button
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                      className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <ChevronRight className="h-5 w-5" />
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Renewal Modal */}
      <Modal
        isOpen={showRenewModal}
        onClose={() => {
          setShowRenewModal(false);
          setSelectedSubscription(null);
        }}
        title={t('billing.subscriptions.renewSubscription')}
      >
        <div className="p-6">
          <p className="text-gray-600 mb-6">
            {t('billing.subscriptions.confirmRenewalMessage')}
          </p>
          <div className="flex justify-end gap-3">
            <Button
              variant="secondary"
              onClick={() => setShowRenewModal(false)}
            >
              {t('common.cancel')}
            </Button>
            <Button
              variant="primary"
              leftIcon={<RefreshCw className="w-4 h-4" />}
              onClick={() => selectedSubscription && handleRenewSubscription(selectedSubscription)}
            >
              {t('billing.subscriptions.confirmRenewal')}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Usage History Modal */}
      <Modal
        isOpen={showUsageModal}
        onClose={() => {
          setShowUsageModal(false);
          setUsageHistory([]);
        }}
        title={t('billing.subscriptions.usageHistory')}
        size="xl"
      >
        <div className="p-6">
          {usageHistory.length === 0 ? (
            <p className="text-gray-600 text-center py-8">
              {t('billing.subscriptions.noUsageHistory')}
            </p>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                      {t('common.date')}
                    </th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                      {t('billing.subscriptions.session')}
                    </th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                      {t('billing.subscriptions.hoursUsed')}
                    </th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                      {t('billing.subscriptions.remainingAfter')}
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {usageHistory.map((usage) => (
                    <tr key={usage.usage_id}>
                      <td className="px-4 py-2 text-sm text-gray-900">
                        {format(new Date(usage.usage_date), 'MMM d, yyyy HH:mm')}
                      </td>
                      <td className="px-4 py-2 text-sm text-gray-900">
                        <div>
                          <p>{usage.subject}</p>
                          <p className="text-xs text-gray-500">
                            {usage.tutor_name}
                          </p>
                        </div>
                      </td>
                      <td className="px-4 py-2 text-sm text-gray-900">
                        {usage.hours_deducted.toFixed(1)}h
                      </td>
                      <td className="px-4 py-2 text-sm text-gray-900">
                        {usage.remaining_hours_after.toFixed(1)}h
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </Modal>
    </div>
  );
};

export default SubscriptionList;
