import { useState, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';

interface ApiOptions {
  params?: Record<string, any>;
  headers?: Record<string, string>;
  timeout?: number;
}

interface ApiResponse<T> {
  data: T;
  status: number;
  message?: string;
}

class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public response?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api/v1';
const DEFAULT_TIMEOUT = 30000; // 30 seconds

export const useApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { user, logout } = useAuth();

  const getAuthHeaders = useCallback(() => {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (user?.access_token) {
      headers['Authorization'] = `Bearer ${user.access_token}`;
    }

    return headers;
  }, [user?.access_token]);

  const buildUrl = useCallback((endpoint: string, params?: Record<string, any>) => {
    const url = new URL(`${API_BASE_URL}${endpoint}`);
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, String(value));
        }
      });
    }
    
    return url.toString();
  }, []);

  const handleResponse = useCallback(async <T>(response: Response): Promise<T> => {
    const contentType = response.headers.get('content-type');
    
    let responseData: any;
    if (contentType && contentType.includes('application/json')) {
      responseData = await response.json();
    } else {
      responseData = await response.text();
    }

    if (!response.ok) {
      // Handle 401 Unauthorized - token expired
      if (response.status === 401) {
        logout();
        throw new ApiError('Session expired. Please login again.', 401, responseData);
      }
      
      // Handle other errors
      const errorMessage = responseData?.detail || responseData?.message || 
        `HTTP ${response.status}: ${response.statusText}`;
      
      throw new ApiError(errorMessage, response.status, responseData);
    }

    return responseData;
  }, [logout]);

  const apiCall = useCallback(async <T>(
    method: string,
    endpoint: string,
    options: ApiOptions = {}
  ): Promise<T> => {
    setLoading(true);
    setError(null);

    try {
      const { params, headers: customHeaders, timeout = DEFAULT_TIMEOUT } = options;
      
      const url = method.toLowerCase() === 'get' 
        ? buildUrl(endpoint, params)
        : buildUrl(endpoint);
      
      const headers = {
        ...getAuthHeaders(),
        ...customHeaders,
      };

      const requestOptions: RequestInit = {
        method: method.toUpperCase(),
        headers,
        signal: AbortSignal.timeout(timeout),
      };

      // Add body for non-GET requests
      if (method.toLowerCase() !== 'get' && params) {
        requestOptions.body = JSON.stringify(params);
      }

      const response = await fetch(url, requestOptions);
      const data = await handleResponse<T>(response);
      
      return data;
    } catch (err) {
      const errorMessage = err instanceof ApiError 
        ? err.message 
        : err instanceof Error 
        ? err.message 
        : 'An unexpected error occurred';
      
      console.error('API Error:', {
        method,
        endpoint,
        error: err,
        timestamp: new Date().toISOString(),
      });
      
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [buildUrl, getAuthHeaders, handleResponse]);

  // Convenience methods
  const get = useCallback(<T>(endpoint: string, options?: ApiOptions) => {
    return apiCall<T>('GET', endpoint, options);
  }, [apiCall]);

  const post = useCallback(<T>(endpoint: string, data?: any, options?: Omit<ApiOptions, 'params'>) => {
    return apiCall<T>('POST', endpoint, { ...options, params: data });
  }, [apiCall]);

  const put = useCallback(<T>(endpoint: string, data?: any, options?: Omit<ApiOptions, 'params'>) => {
    return apiCall<T>('PUT', endpoint, { ...options, params: data });
  }, [apiCall]);

  const del = useCallback(<T>(endpoint: string, options?: ApiOptions) => {
    return apiCall<T>('DELETE', endpoint, options);
  }, [apiCall]);

  const patch = useCallback(<T>(endpoint: string, data?: any, options?: Omit<ApiOptions, 'params'>) => {
    return apiCall<T>('PATCH', endpoint, { ...options, params: data });
  }, [apiCall]);

  return { 
    apiCall, 
    get, 
    post, 
    put, 
    delete: del, 
    patch,
    loading, 
    error,
    clearError: () => setError(null)
  };
};