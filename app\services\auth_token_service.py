"""
Unified Authentication Token Service for TutorAide Application.
Manages all authentication tokens including sessions, password resets, 
email verification, and API keys in a single consolidated service.
"""

import hashlib
import secrets
from typing import Optional, List, Dict, Any, Tuple
from datetime import datetime, timedelta
from uuid import UUID, uuid4
import asyncpg

from app.database.repositories.auth_token_repository import AuthTokenRepository
from app.database.repositories.user_repository import UserRepository
from app.models.auth_token_models import (
    AuthToken, AuthTokenType, AuthTokenCreate, AuthTokenUpdate,
    AuthTokenResponse, SessionInfo, TokenValidationResult
)
from app.models.user_models import UserRoleType, User, PasswordStrength
from app.core.timezone import now_est
from app.core.exceptions import (
    AuthenticationError, ResourceNotFoundError, ValidationError, 
    BusinessLogicError, TokenExpiredError
)
from app.core.security import get_password_hash, verify_password
from app.core.logging import TutorAideLogger
from app.config.settings import settings

logger = TutorAideLogger.get_logger(__name__)


class AuthTokenService:
    """Unified service for managing all authentication tokens."""
    
    def __init__(self):
        self.token_repo = AuthTokenRepository()
        self.user_repo = UserRepository()
        
        # Token expiry configurations
        self.token_expiry_config = {
            AuthTokenType.SESSION: {
                UserRoleType.MANAGER: timedelta(hours=3),
                UserRoleType.TUTOR: timedelta(minutes=30),
                UserRoleType.CLIENT: timedelta(minutes=30)
            },
            AuthTokenType.PASSWORD_RESET: timedelta(hours=1),
            AuthTokenType.EMAIL_VERIFY: timedelta(hours=24),
            AuthTokenType.API_KEY: timedelta(days=90)
        }
        
        # Cooldown configurations
        self.cooldown_config = {
            AuthTokenType.PASSWORD_RESET: timedelta(minutes=5),
            AuthTokenType.EMAIL_VERIFY: timedelta(minutes=5)
        }
    
    def _hash_token(self, token: str) -> str:
        """Hash a token using SHA256."""
        return hashlib.sha256(token.encode()).hexdigest()
    
    def _generate_secure_token(self, length: int = 32) -> str:
        """Generate a secure random token."""
        return secrets.token_urlsafe(length)
    
    def _calculate_expiry(
        self,
        token_type: AuthTokenType,
        role_type: Optional[UserRoleType] = None,
        custom_duration: Optional[timedelta] = None
    ) -> datetime:
        """Calculate token expiry based on type and role."""
        
        if custom_duration:
            return now_est() + custom_duration
        
        if token_type == AuthTokenType.SESSION and role_type:
            duration = self.token_expiry_config[token_type].get(
                role_type, 
                timedelta(minutes=30)
            )
        else:
            duration = self.token_expiry_config.get(token_type, timedelta(hours=1))
        
        return now_est() + duration
    
    # ==================== SESSION MANAGEMENT ====================
    
    async def create_session(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        role_type: UserRoleType,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        device_info: Optional[Dict[str, Any]] = None
    ) -> Tuple[str, SessionInfo]:
        """Create a new session for a user."""
        
        try:
            # Generate session token
            token = self._generate_secure_token()
            token_hash = self._hash_token(token)
            
            # Calculate expiry based on role
            expires_at = self._calculate_expiry(AuthTokenType.SESSION, role_type)
            
            # Prepare metadata
            metadata = {
                'role_type': role_type.value,
                'session_id': str(uuid4()),
                'last_activity': now_est().isoformat(),
                'device_info': device_info or {}
            }
            
            # Create token
            auth_token = await self.token_repo.create_token(
                conn=conn,
                user_id=user_id,
                token_type=AuthTokenType.SESSION,
                token_hash=token_hash,
                expires_at=expires_at,
                metadata=metadata,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            # Create session info
            session_info = SessionInfo(
                session_id=UUID(metadata['session_id']),
                user_id=user_id,
                role_type=role_type,
                expires_at=expires_at,
                created_at=auth_token.created_at,
                last_activity=now_est(),
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            logger.info(f"Created session for user {user_id} with role {role_type}")
            return token, session_info
            
        except Exception as e:
            logger.error(f"Failed to create session for user {user_id}: {e}")
            raise BusinessLogicError(f"Failed to create session: {str(e)}")
    
    async def validate_session(
        self,
        conn: asyncpg.Connection,
        token: str,
        update_activity: bool = True
    ) -> SessionInfo:
        """Validate a session token and return session info."""
        
        try:
            token_hash = self._hash_token(token)
            
            # Get token
            auth_token = await self.token_repo.get_token_by_hash(
                conn=conn,
                token_hash=token_hash,
                token_type=AuthTokenType.SESSION
            )
            
            if not auth_token:
                raise AuthenticationError("Invalid session token")
            
            # Check if expired
            if auth_token.expires_at < now_est():
                raise TokenExpiredError("Session has expired")
            
            # Check if already used (sessions can be reused)
            if auth_token.used_at:
                raise AuthenticationError("Session has been invalidated")
            
            # Update last activity if requested
            if update_activity:
                metadata = auth_token.metadata or {}
                metadata['last_activity'] = now_est().isoformat()
                
                await self.token_repo.update_token(
                    conn=conn,
                    token_id=auth_token.token_id,
                    metadata=metadata
                )
            
            # Create session info
            metadata = auth_token.metadata or {}
            session_info = SessionInfo(
                session_id=UUID(metadata.get('session_id', str(uuid4()))),
                user_id=auth_token.user_id,
                role_type=UserRoleType(metadata.get('role_type', 'client')),
                expires_at=auth_token.expires_at,
                created_at=auth_token.created_at,
                last_activity=datetime.fromisoformat(metadata.get('last_activity', now_est().isoformat())),
                ip_address=auth_token.ip_address,
                user_agent=auth_token.user_agent
            )
            
            return session_info
            
        except (AuthenticationError, TokenExpiredError):
            raise
        except Exception as e:
            logger.error(f"Failed to validate session: {e}")
            raise AuthenticationError("Session validation failed")
    
    async def invalidate_session(
        self,
        conn: asyncpg.Connection,
        token: str
    ) -> bool:
        """Invalidate a session by marking it as used."""
        
        try:
            token_hash = self._hash_token(token)
            
            # Mark token as used
            success = await self.token_repo.mark_token_used(
                conn=conn,
                token_hash=token_hash,
                token_type=AuthTokenType.SESSION
            )
            
            if success:
                logger.info(f"Invalidated session token")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to invalidate session: {e}")
            return False
    
    async def invalidate_all_user_sessions(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        except_token: Optional[str] = None
    ) -> int:
        """Invalidate all sessions for a user, optionally except one."""
        
        try:
            except_hash = self._hash_token(except_token) if except_token else None
            
            count = await self.token_repo.invalidate_user_tokens(
                conn=conn,
                user_id=user_id,
                token_type=AuthTokenType.SESSION,
                except_hash=except_hash
            )
            
            logger.info(f"Invalidated {count} sessions for user {user_id}")
            return count
            
        except Exception as e:
            logger.error(f"Failed to invalidate user sessions: {e}")
            return 0
    
    # ==================== PASSWORD RESET ====================
    
    async def create_password_reset_token(
        self,
        conn: asyncpg.Connection,
        email: str,
        initiated_by: Optional[str] = None,
        initiated_by_user_id: Optional[int] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> Dict[str, Any]:
        """Create a password reset token for a user."""
        
        logger.info(f"=== AUTH TOKEN SERVICE: create_password_reset_token ===")
        logger.info(f"Looking up user with email: {email}")
        
        try:
            # Get user by email
            user = await self.user_repo.find_by_email(conn, email)
            logger.info(f"User lookup result: {user is not None}")
            if not user:
                # Return empty dict for security (don't reveal if email exists)
                logger.warning(f"Password reset requested for non-existent email: {email}")
                return {}
            
            # Log user details for debugging
            logger.info(f"Found user: ID={user.get('user_id')}, Email={user.get('email')}, Verified={user.get('email_verified')}")
            
            # Check cooldown
            logger.info("Checking token cooldown...")
            await self._check_token_cooldown(
                conn=conn,
                user_id=user['user_id'],
                token_type=AuthTokenType.PASSWORD_RESET
            )
            logger.info("Cooldown check passed")
            
            # Invalidate any existing password reset tokens
            logger.info("Invalidating existing tokens...")
            invalidated_count = await self.token_repo.invalidate_user_tokens(
                conn=conn,
                user_id=user['user_id'],
                token_type=AuthTokenType.PASSWORD_RESET
            )
            logger.info(f"Invalidated {invalidated_count} existing tokens")
            
            # Generate token
            token = self._generate_secure_token()
            token_hash = self._hash_token(token)
            expires_at = self._calculate_expiry(AuthTokenType.PASSWORD_RESET)
            
            logger.info(f"Generated token: hash_length={len(token_hash)}, expires_at={expires_at}")
            
            # Prepare metadata
            metadata = {
                'initiated_by': initiated_by or 'user',
                'initiated_by_user_id': initiated_by_user_id,
                'email': email
            }
            
            # Create token
            logger.info(f"Creating token in database for user_id={user['user_id']}...")
            auth_token = await self.token_repo.create_token(
                conn=conn,
                user_id=user['user_id'],
                token_type=AuthTokenType.PASSWORD_RESET,
                token_hash=token_hash,
                expires_at=expires_at,
                metadata=metadata,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            logger.info(f"Successfully created password reset token: token_id={auth_token.token_id}")
            
            return {
                'token': token,
                'expires_at': expires_at,
                'user_id': user['user_id'],
                'email': email
            }
            
        except ValidationError as e:
            logger.error(f"Validation error in password reset token creation: {e}")
            raise
        except Exception as e:
            logger.error(f"Failed to create password reset token: {type(e).__name__}: {e}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return {}
    
    async def validate_password_reset_token(
        self,
        conn: asyncpg.Connection,
        token: str
    ) -> Optional[User]:
        """Validate a password reset token and return the user."""
        
        logger.info("=== VALIDATING PASSWORD RESET TOKEN ===")
        logger.info(f"Token received: {token[:10]}...{token[-10:]}")
        
        try:
            token_hash = self._hash_token(token)
            logger.info(f"Token hash calculated: {token_hash}")
            
            # Get token
            logger.info(f"Looking up token in database with hash: {token_hash}")
            auth_token = await self.token_repo.get_token_by_hash(
                conn=conn,
                token_hash=token_hash,
                token_type=AuthTokenType.PASSWORD_RESET
            )
            
            if not auth_token:
                logger.error("Token not found in database")
                logger.error(f"Searched for hash: {token_hash}")
                raise ValidationError("Invalid password reset token")
            
            logger.info(f"Token found: token_id={auth_token.token_id}, user_id={auth_token.user_id}")
            logger.info(f"Token expires_at: {auth_token.expires_at}")
            logger.info(f"Current time: {now_est()}")
            logger.info(f"Token used_at: {auth_token.used_at}")
            
            # Check if expired
            if auth_token.expires_at < now_est():
                time_diff = now_est() - auth_token.expires_at
                logger.error(f"Token is expired by {time_diff}")
                raise TokenExpiredError("Password reset token has expired")
            
            # Check if already used
            if auth_token.used_at:
                logger.error(f"Token was already used at: {auth_token.used_at}")
                raise ValidationError("Password reset token has already been used")
            
            logger.info("Token validation passed, fetching user...")
            
            # Get user
            user = await self.user_repo.get_user_by_id(conn, auth_token.user_id)
            if not user:
                logger.error(f"User not found for user_id: {auth_token.user_id}")
                raise ResourceNotFoundError("User not found")
            
            logger.info(f"User found: {user.user_id}, email: {user.email}")
            logger.info("=== TOKEN VALIDATION SUCCESSFUL ===")
            return user
            
        except (ValidationError, TokenExpiredError, ResourceNotFoundError) as e:
            logger.error(f"Expected error during validation: {type(e).__name__}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error validating token: {type(e).__name__}: {e}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            raise ValidationError("Token validation failed")
    
    async def reset_password_with_token(
        self,
        conn: asyncpg.Connection,
        token: str,
        new_password: str,
        ip_address: Optional[str] = None
    ) -> bool:
        """Reset password using a valid token."""
        
        logger.info("=== RESET PASSWORD WITH TOKEN ===")
        logger.info(f"Token: {token[:10]}...{token[-10:]}")
        logger.info(f"New password length: {len(new_password)}")
        logger.info(f"IP address: {ip_address}")
        
        try:
            # Validate token and get user
            logger.info("Step 1: Validating token...")
            user = await self.validate_password_reset_token(conn, token)
            logger.info(f"Token validated successfully for user {user.user_id} ({user.email})")
            
            # Check password strength
            logger.info("Step 2: Checking password strength...")
            strength = PasswordStrength.check_strength(new_password)
            logger.info(f"Password strength score: {strength.score}/5")
            logger.info(f"Password strength feedback: {strength}")
            
            if strength.score < 3:
                logger.error(f"Password too weak: score={strength.score}, requirements: {strength}")
                raise ValidationError(f"Password is too weak. Score: {strength.score}/5. Requirements: minimum 8 characters, uppercase, lowercase, and digit.")
            
            # Update password
            logger.info("Step 3: Hashing new password...")
            password_hash = get_password_hash(new_password)
            logger.info(f"Password hash generated, length: {len(password_hash)}")
            
            logger.info("Step 4: Updating user password in database...")
            await self.user_repo.update_user_password(
                conn=conn,
                user_id=user.user_id,
                password_hash=password_hash
            )
            logger.info("Password updated successfully in database")
            
            # Mark token as used
            logger.info("Step 5: Marking token as used...")
            token_hash = self._hash_token(token)
            marked = await self.token_repo.mark_token_used(
                conn=conn,
                token_hash=token_hash,
                token_type=AuthTokenType.PASSWORD_RESET,
                used_ip=ip_address
            )
            logger.info(f"Token marked as used: {marked}")
            
            # Invalidate all user sessions for security
            logger.info("Step 6: Invalidating all user sessions...")
            invalidated = await self.invalidate_all_user_sessions(conn, user.user_id)
            logger.info(f"Invalidated {invalidated} user sessions")
            
            logger.info(f"=== PASSWORD RESET SUCCESSFUL for user {user.user_id} ===")
            return True
            
        except (ValidationError, TokenExpiredError) as e:
            logger.error(f"Expected error in password reset: {type(e).__name__}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error in password reset: {type(e).__name__}: {e}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            raise BusinessLogicError("Password reset failed")
    
    # ==================== EMAIL VERIFICATION ====================
    
    async def create_email_verification_token(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        email: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> Dict[str, Any]:
        """Create an email verification token."""
        
        try:
            # Check if email already verified
            user = await self.user_repo.get_user_by_id(conn, user_id)
            if not user:
                raise ResourceNotFoundError("User not found")
            
            if user.email_verified:
                raise ValidationError("Email already verified")
            
            # Check cooldown
            await self._check_token_cooldown(
                conn=conn,
                user_id=user_id,
                token_type=AuthTokenType.EMAIL_VERIFY
            )
            
            # Invalidate any existing verification tokens
            await self.token_repo.invalidate_user_tokens(
                conn=conn,
                user_id=user_id,
                token_type=AuthTokenType.EMAIL_VERIFY
            )
            
            # Generate token
            token = self._generate_secure_token()
            token_hash = self._hash_token(token)
            expires_at = self._calculate_expiry(AuthTokenType.EMAIL_VERIFY)
            
            # Prepare metadata
            metadata = {
                'email': email,
                'verification_requested_at': now_est().isoformat()
            }
            
            # Create token
            auth_token = await self.token_repo.create_token(
                conn=conn,
                user_id=user_id,
                token_type=AuthTokenType.EMAIL_VERIFY,
                token_hash=token_hash,
                expires_at=expires_at,
                metadata=metadata,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            logger.info(f"Created email verification token for user {user_id}")
            
            return {
                'token': token,
                'expires_at': expires_at,
                'email': email
            }
            
        except (ValidationError, ResourceNotFoundError):
            raise
        except Exception as e:
            logger.error(f"Failed to create email verification token: {e}")
            raise BusinessLogicError("Failed to create verification token")
    
    async def verify_email_with_token(
        self,
        conn: asyncpg.Connection,
        token: str,
        ip_address: Optional[str] = None
    ) -> bool:
        """Verify email using a valid token."""
        
        try:
            token_hash = self._hash_token(token)
            
            # Get token
            auth_token = await self.token_repo.get_token_by_hash(
                conn=conn,
                token_hash=token_hash,
                token_type=AuthTokenType.EMAIL_VERIFY
            )
            
            if not auth_token:
                raise ValidationError("Invalid verification token")
            
            # Check if expired
            if auth_token.expires_at < now_est():
                raise TokenExpiredError("Verification token has expired")
            
            # Check if already used
            if auth_token.used_at:
                raise ValidationError("Verification token has already been used")
            
            # Mark email as verified
            await self.user_repo.mark_email_verified(
                conn=conn,
                user_id=auth_token.user_id
            )
            
            # Mark token as used
            await self.token_repo.mark_token_used(
                conn=conn,
                token_hash=token_hash,
                token_type=AuthTokenType.EMAIL_VERIFY,
                used_ip=ip_address
            )
            
            logger.info(f"Email verified for user {auth_token.user_id}")
            return True
            
        except (ValidationError, TokenExpiredError):
            raise
        except Exception as e:
            logger.error(f"Failed to verify email: {e}")
            raise BusinessLogicError("Email verification failed")
    
    # ==================== API KEYS ====================
    
    async def create_api_key(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        name: str,
        permissions: List[str],
        expires_in_days: int = 90,
        ip_whitelist: Optional[List[str]] = None
    ) -> Tuple[str, AuthToken]:
        """Create an API key for programmatic access."""
        
        try:
            # Check user exists
            user = await self.user_repo.get_user_by_id(conn, user_id)
            if not user:
                raise ResourceNotFoundError("User not found")
            
            # Generate API key
            api_key = f"ta_{self._generate_secure_token(48)}"
            token_hash = self._hash_token(api_key)
            expires_at = now_est() + timedelta(days=expires_in_days)
            
            # Prepare metadata
            metadata = {
                'name': name,
                'permissions': permissions,
                'ip_whitelist': ip_whitelist or [],
                'last_used_at': None,
                'usage_count': 0
            }
            
            # Create token
            auth_token = await self.token_repo.create_token(
                conn=conn,
                user_id=user_id,
                token_type=AuthTokenType.API_KEY,
                token_hash=token_hash,
                expires_at=expires_at,
                metadata=metadata
            )
            
            logger.info(f"Created API key '{name}' for user {user_id}")
            return api_key, auth_token
            
        except ResourceNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Failed to create API key: {e}")
            raise BusinessLogicError("Failed to create API key")
    
    async def validate_api_key(
        self,
        conn: asyncpg.Connection,
        api_key: str,
        required_permission: Optional[str] = None,
        ip_address: Optional[str] = None
    ) -> Tuple[User, List[str]]:
        """Validate an API key and return user and permissions."""
        
        try:
            token_hash = self._hash_token(api_key)
            
            # Get token
            auth_token = await self.token_repo.get_token_by_hash(
                conn=conn,
                token_hash=token_hash,
                token_type=AuthTokenType.API_KEY
            )
            
            if not auth_token:
                raise AuthenticationError("Invalid API key")
            
            # Check if expired
            if auth_token.expires_at < now_est():
                raise TokenExpiredError("API key has expired")
            
            # Check if revoked (marked as used)
            if auth_token.used_at:
                raise AuthenticationError("API key has been revoked")
            
            # Check IP whitelist if configured
            metadata = auth_token.metadata or {}
            ip_whitelist = metadata.get('ip_whitelist', [])
            if ip_whitelist and ip_address and ip_address not in ip_whitelist:
                raise AuthenticationError("IP address not whitelisted")
            
            # Check permissions
            permissions = metadata.get('permissions', [])
            if required_permission and required_permission not in permissions:
                raise AuthenticationError(f"Missing required permission: {required_permission}")
            
            # Update usage stats
            metadata['last_used_at'] = now_est().isoformat()
            metadata['usage_count'] = metadata.get('usage_count', 0) + 1
            
            await self.token_repo.update_token(
                conn=conn,
                token_id=auth_token.token_id,
                metadata=metadata
            )
            
            # Get user
            user = await self.user_repo.get_user_by_id(conn, auth_token.user_id)
            if not user:
                raise ResourceNotFoundError("User not found")
            
            return user, permissions
            
        except (AuthenticationError, TokenExpiredError, ResourceNotFoundError):
            raise
        except Exception as e:
            logger.error(f"Failed to validate API key: {e}")
            raise AuthenticationError("API key validation failed")
    
    async def revoke_api_key(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        api_key: str
    ) -> bool:
        """Revoke an API key."""
        
        try:
            token_hash = self._hash_token(api_key)
            
            # Verify ownership
            auth_token = await self.token_repo.get_token_by_hash(
                conn=conn,
                token_hash=token_hash,
                token_type=AuthTokenType.API_KEY
            )
            
            if not auth_token or auth_token.user_id != user_id:
                raise AuthenticationError("API key not found or not owned by user")
            
            # Mark as used (revoked)
            success = await self.token_repo.mark_token_used(
                conn=conn,
                token_hash=token_hash,
                token_type=AuthTokenType.API_KEY
            )
            
            if success:
                logger.info(f"Revoked API key for user {user_id}")
            
            return success
            
        except AuthenticationError:
            raise
        except Exception as e:
            logger.error(f"Failed to revoke API key: {e}")
            return False
    
    # ==================== UTILITY METHODS ====================
    
    async def _check_token_cooldown(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        token_type: AuthTokenType
    ) -> None:
        """Check if user is within cooldown period for token creation."""
        
        cooldown = self.cooldown_config.get(token_type)
        if not cooldown:
            return
        
        # Get most recent token of this type
        recent_token = await self.token_repo.get_most_recent_token(
            conn=conn,
            user_id=user_id,
            token_type=token_type
        )
        
        if recent_token:
            time_since_last = now_est() - recent_token.created_at
            if time_since_last < cooldown:
                remaining = cooldown - time_since_last
                minutes = int(remaining.total_seconds() / 60)
                raise ValidationError(
                    f"Please wait {minutes} minutes before requesting another {token_type.value}"
                )
    
    async def cleanup_expired_tokens(
        self,
        conn: asyncpg.Connection,
        batch_size: int = 1000
    ) -> int:
        """Clean up expired tokens from the database."""
        
        try:
            total_deleted = await self.token_repo.delete_expired_tokens(
                conn=conn,
                batch_size=batch_size
            )
            
            logger.info(f"Cleaned up {total_deleted} expired tokens")
            return total_deleted
            
        except Exception as e:
            logger.error(f"Failed to cleanup expired tokens: {e}")
            return 0
    
    async def get_active_sessions_count(
        self,
        conn: asyncpg.Connection,
        user_id: Optional[int] = None
    ) -> int:
        """Get count of active sessions."""
        
        try:
            return await self.token_repo.count_active_tokens(
                conn=conn,
                token_type=AuthTokenType.SESSION,
                user_id=user_id
            )
        except Exception as e:
            logger.error(f"Failed to get active sessions count: {e}")
            return 0
    
    async def get_user_api_keys(
        self,
        conn: asyncpg.Connection,
        user_id: int
    ) -> List[Dict[str, Any]]:
        """Get all API keys for a user (without the actual keys)."""
        
        try:
            tokens = await self.token_repo.get_user_tokens(
                conn=conn,
                user_id=user_id,
                token_type=AuthTokenType.API_KEY,
                include_used=False
            )
            
            api_keys = []
            for token in tokens:
                metadata = token.metadata or {}
                api_keys.append({
                    'token_id': token.token_id,
                    'name': metadata.get('name', 'Unnamed'),
                    'permissions': metadata.get('permissions', []),
                    'created_at': token.created_at,
                    'expires_at': token.expires_at,
                    'last_used_at': metadata.get('last_used_at'),
                    'usage_count': metadata.get('usage_count', 0),
                    'ip_whitelist': metadata.get('ip_whitelist', [])
                })
            
            return api_keys
            
        except Exception as e:
            logger.error(f"Failed to get user API keys: {e}")
            return []


# Global service instance
_auth_token_service = None


def get_auth_token_service() -> AuthTokenService:
    """Get the global auth token service instance."""
    global _auth_token_service
    if _auth_token_service is None:
        _auth_token_service = AuthTokenService()
    return _auth_token_service