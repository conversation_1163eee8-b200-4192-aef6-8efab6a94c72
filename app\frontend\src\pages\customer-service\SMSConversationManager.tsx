/**
 * SMS Conversation Manager
 * 
 * Comprehensive interface for managers to handle SMS conversations,
 * assign agents, monitor performance, and manage customer interactions.
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  Avatar,
  Badge,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Paper,
  IconButton,
  Tooltip,
  Menu,
  MenuList,
  MenuItem as MenuOption,
  Tabs,
  Tab,
  CircularProgress,
  Alert,
  Fab,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  Send as SendIcon,
  Assignment as AssignmentIcon,
  MoreVert as MoreVertIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Refresh as RefreshIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Person as PersonIcon,
  Schedule as ScheduleIcon,
  Priority as PriorityIcon,
  Category as CategoryIcon,
  Add as AddIcon,
  Reply as ReplyIcon,
  Template as TemplateIcon,
  QuickReply as QuickReplyIcon,
  Analytics as AnalyticsIcon,
  NotificationImportant as UrgentIcon,
  CheckCircle as ResolvedIcon,
  Cancel as ClosedIcon
} from '@mui/icons-material';
import { formatDistanceToNow, format } from 'date-fns';
import { fr } from 'date-fns/locale';

import api from '../../services/api';
import { useTranslation } from '../../hooks/useTranslation';
import { useFormatting } from '../../hooks/useFormatting';
import { useWebSocket } from '../../hooks/useWebSocket';
import { useNotifications } from '../../hooks/useNotifications';

// Types
interface Conversation {
  conversation_id: number;
  thread_id: string;
  participant_name?: string;
  participant_phone?: string;
  participant_email?: string;
  assigned_agent_id?: number;
  status: 'open' | 'assigned' | 'resolved' | 'closed' | 'escalated';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  category?: string;
  last_activity_at: string;
  created_at: string;
  unread_count?: number;
  last_message?: string;
}

interface Message {
  message_id: number;
  conversation_id: number;
  message_content: string;
  message_direction: 'inbound' | 'outbound';
  sender_name?: string;
  sender_role?: string;
  sent_at: string;
  delivery_status: string;
  internal_note: boolean;
  template_id?: number;
}

interface Agent {
  agent_id: number;
  agent_name: string;
  status: 'available' | 'busy' | 'away' | 'offline';
  current_conversations: number;
  max_conversations: number;
  specialties: string[];
  languages: string[];
}

interface Template {
  template_id: number;
  template_name: string;
  template_key: string;
  category: string;
  message_content: string;
  variables?: Record<string, any>;
}

interface QuickResponse {
  quick_response_id: number;
  response_text: string;
  category?: string;
}

// Main Component
const SMSConversationManager: React.FC = () => {
  const { t } = useTranslation();
  const { formatDate, formatTime, formatPhone } = useFormatting();
  const { showNotification } = useNotifications();
  const queryClient = useQueryClient();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // State
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [newMessage, setNewMessage] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');
  const [agentFilter, setAgentFilter] = useState<string>('all');
  const [currentTab, setCurrentTab] = useState(0);
  const [assignDialogOpen, setAssignDialogOpen] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState<number | null>(null);
  const [templateDialogOpen, setTemplateDialogOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [templateVariables, setTemplateVariables] = useState<Record<string, string>>({});
  const [quickResponseMenuAnchor, setQuickResponseMenuAnchor] = useState<null | HTMLElement>(null);

  // WebSocket for real-time updates
  const { isConnected, sendMessage: sendWebSocketMessage } = useWebSocket({
    onMessage: (data) => {
      if (data.type === 'new_message' || data.type === 'conversation_update') {
        queryClient.invalidateQueries(['conversations']);
        if (selectedConversation && data.conversation_id === selectedConversation.conversation_id) {
          queryClient.invalidateQueries(['conversation', selectedConversation.conversation_id]);
        }
      }
    }
  });

  // Queries
  const { data: conversations = [], isLoading: conversationsLoading, refetch: refetchConversations } = useQuery({
    queryKey: ['conversations', statusFilter, priorityFilter, agentFilter, searchTerm],
    queryFn: async () => {
      const params = new URLSearchParams();
      if (statusFilter !== 'all') params.append('status', statusFilter);
      if (priorityFilter !== 'all') params.append('priority', priorityFilter);
      if (agentFilter !== 'all') params.append('agent_id', agentFilter);
      if (searchTerm) return api.get(`/customer-service/search?q=${encodeURIComponent(searchTerm)}&${params}`);
      return api.get(`/customer-service/conversations?${params}`);
    },
    refetchInterval: 30000 // Refresh every 30 seconds
  });

  const { data: messages = [], isLoading: messagesLoading } = useQuery({
    queryKey: ['conversation', selectedConversation?.conversation_id],
    queryFn: () => api.get(`/customer-service/conversations/${selectedConversation?.conversation_id}/messages?include_internal=true`),
    enabled: !!selectedConversation?.conversation_id,
    refetchInterval: 5000 // Refresh messages every 5 seconds
  });

  const { data: agents = [] } = useQuery({
    queryKey: ['agents'],
    queryFn: () => api.get('/customer-service/agents')
  });

  const { data: templates = [] } = useQuery({
    queryKey: ['templates'],
    queryFn: () => api.get('/customer-service/templates')
  });

  const { data: quickResponses = [] } = useQuery({
    queryKey: ['quick-responses'],
    queryFn: () => api.get('/customer-service/quick-responses')
  });

  // Mutations
  const sendMessageMutation = useMutation({
    mutationFn: (data: { conversation_id: number; message_content: string; internal_note?: boolean }) =>
      api.post(`/customer-service/conversations/${data.conversation_id}/messages`, {
        message_content: data.message_content,
        message_type: 'text',
        internal_note: data.internal_note || false
      }),
    onSuccess: () => {
      setNewMessage('');
      queryClient.invalidateQueries(['conversation', selectedConversation?.conversation_id]);
      queryClient.invalidateQueries(['conversations']);
    },
    onError: (error) => {
      showNotification('Failed to send message', 'error');
    }
  });

  const assignConversationMutation = useMutation({
    mutationFn: (data: { conversation_id: number; agent_id: number; assignment_reason?: string }) =>
      api.post(`/customer-service/conversations/${data.conversation_id}/assign`, data),
    onSuccess: () => {
      setAssignDialogOpen(false);
      setSelectedAgent(null);
      queryClient.invalidateQueries(['conversations']);
      queryClient.invalidateQueries(['conversation', selectedConversation?.conversation_id]);
      showNotification('Conversation assigned successfully', 'success');
    },
    onError: () => {
      showNotification('Failed to assign conversation', 'error');
    }
  });

  const updateStatusMutation = useMutation({
    mutationFn: (data: { conversation_id: number; status: string }) =>
      api.put(`/customer-service/conversations/${data.conversation_id}`, { status: data.status }),
    onSuccess: () => {
      queryClient.invalidateQueries(['conversations']);
      queryClient.invalidateQueries(['conversation', selectedConversation?.conversation_id]);
    }
  });

  const sendTemplateMutation = useMutation({
    mutationFn: (data: { conversation_id: number; template_key: string; variables?: Record<string, any> }) =>
      api.post(`/customer-service/conversations/${data.conversation_id}/messages/template`, data),
    onSuccess: () => {
      setTemplateDialogOpen(false);
      setSelectedTemplate(null);
      setTemplateVariables({});
      queryClient.invalidateQueries(['conversation', selectedConversation?.conversation_id]);
      queryClient.invalidateQueries(['conversations']);
    }
  });

  // Handlers
  const handleSendMessage = useCallback(() => {
    if (!selectedConversation || !newMessage.trim()) return;

    sendMessageMutation.mutate({
      conversation_id: selectedConversation.conversation_id,
      message_content: newMessage.trim()
    });
  }, [selectedConversation, newMessage, sendMessageMutation]);

  const handleAssignConversation = useCallback(() => {
    if (!selectedConversation || !selectedAgent) return;

    assignConversationMutation.mutate({
      conversation_id: selectedConversation.conversation_id,
      agent_id: selectedAgent,
      assignment_reason: 'Manual assignment by manager'
    });
  }, [selectedConversation, selectedAgent, assignConversationMutation]);

  const handleStatusChange = useCallback((status: string) => {
    if (!selectedConversation) return;

    updateStatusMutation.mutate({
      conversation_id: selectedConversation.conversation_id,
      status
    });
  }, [selectedConversation, updateStatusMutation]);

  const handleSendTemplate = useCallback(() => {
    if (!selectedConversation || !selectedTemplate) return;

    sendTemplateMutation.mutate({
      conversation_id: selectedConversation.conversation_id,
      template_key: selectedTemplate.template_key,
      variables: templateVariables
    });
  }, [selectedConversation, selectedTemplate, templateVariables, sendTemplateMutation]);

  const handleQuickResponse = useCallback((response: QuickResponse) => {
    setNewMessage(response.response_text);
    setQuickResponseMenuAnchor(null);
  }, []);

  // Filter conversations
  const filteredConversations = useMemo(() => {
    return conversations.filter((conv: Conversation) => {
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        return (
          conv.participant_name?.toLowerCase().includes(searchLower) ||
          conv.participant_phone?.includes(searchTerm) ||
          conv.participant_email?.toLowerCase().includes(searchLower) ||
          conv.last_message?.toLowerCase().includes(searchLower)
        );
      }
      return true;
    });
  }, [conversations, searchTerm]);

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'error';
      case 'high': return 'warning';
      case 'normal': return 'info';
      case 'low': return 'default';
      default: return 'default';
    }
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'urgent': return <UrgentIcon color="error" />;
      case 'resolved': return <ResolvedIcon color="success" />;
      case 'closed': return <ClosedIcon color="disabled" />;
      default: return <ScheduleIcon color="primary" />;
    }
  };

  // Render conversation list
  const renderConversationList = () => (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <CardContent sx={{ pb: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
          <Typography variant="h6" sx={{ flex: 1 }}>
            {t('customer_service.conversations')}
          </Typography>
          <IconButton onClick={() => refetchConversations()} size="small">
            <RefreshIcon />
          </IconButton>
        </Box>

        {/* Search and Filters */}
        <TextField
          fullWidth
          size="small"
          placeholder={t('customer_service.search_conversations')}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
          }}
          sx={{ mb: 2 }}
        />

        <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
          <FormControl size="small" sx={{ minWidth: 100 }}>
            <InputLabel>{t('common.status')}</InputLabel>
            <Select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              label={t('common.status')}
            >
              <MenuItem value="all">{t('common.all')}</MenuItem>
              <MenuItem value="open">{t('customer_service.status.open')}</MenuItem>
              <MenuItem value="assigned">{t('customer_service.status.assigned')}</MenuItem>
              <MenuItem value="resolved">{t('customer_service.status.resolved')}</MenuItem>
              <MenuItem value="closed">{t('customer_service.status.closed')}</MenuItem>
            </Select>
          </FormControl>

          <FormControl size="small" sx={{ minWidth: 100 }}>
            <InputLabel>{t('common.priority')}</InputLabel>
            <Select
              value={priorityFilter}
              onChange={(e) => setPriorityFilter(e.target.value)}
              label={t('common.priority')}
            >
              <MenuItem value="all">{t('common.all')}</MenuItem>
              <MenuItem value="urgent">{t('customer_service.priority.urgent')}</MenuItem>
              <MenuItem value="high">{t('customer_service.priority.high')}</MenuItem>
              <MenuItem value="normal">{t('customer_service.priority.normal')}</MenuItem>
              <MenuItem value="low">{t('customer_service.priority.low')}</MenuItem>
            </Select>
          </FormControl>

          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>{t('customer_service.agent')}</InputLabel>
            <Select
              value={agentFilter}
              onChange={(e) => setAgentFilter(e.target.value)}
              label={t('customer_service.agent')}
            >
              <MenuItem value="all">{t('common.all')}</MenuItem>
              <MenuItem value="unassigned">{t('customer_service.unassigned')}</MenuItem>
              {agents.map((agent: Agent) => (
                <MenuItem key={agent.agent_id} value={agent.agent_id.toString()}>
                  {agent.agent_name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      </CardContent>

      <Divider />

      {/* Conversation List */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        {conversationsLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : filteredConversations.length === 0 ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography color="text.secondary">
              {t('customer_service.no_conversations')}
            </Typography>
          </Box>
        ) : (
          <List sx={{ p: 0 }}>
            {filteredConversations.map((conversation: Conversation, index: number) => (
              <React.Fragment key={conversation.conversation_id}>
                <ListItem
                  button
                  selected={selectedConversation?.conversation_id === conversation.conversation_id}
                  onClick={() => setSelectedConversation(conversation)}
                  sx={{ py: 2 }}
                >
                  <ListItemAvatar>
                    <Badge
                      badgeContent={conversation.unread_count}
                      color="error"
                      invisible={!conversation.unread_count}
                    >
                      <Avatar sx={{ bgcolor: 'primary.main' }}>
                        {conversation.participant_phone ? <PhoneIcon /> : <PersonIcon />}
                      </Avatar>
                    </Badge>
                  </ListItemAvatar>

                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="subtitle2" sx={{ flex: 1 }} noWrap>
                          {conversation.participant_name || 
                           formatPhone(conversation.participant_phone || '') ||
                           conversation.participant_email ||
                           t('customer_service.unknown_participant')}
                        </Typography>
                        <Chip
                          size="small"
                          label={t(`customer_service.priority.${conversation.priority}`)}
                          color={getPriorityColor(conversation.priority) as any}
                          variant="outlined"
                        />
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary" noWrap sx={{ mb: 0.5 }}>
                          {conversation.last_message}
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {getStatusIcon(conversation.status)}
                          <Typography variant="caption" color="text.secondary">
                            {formatDistanceToNow(new Date(conversation.last_activity_at), { addSuffix: true })}
                          </Typography>
                          {conversation.category && (
                            <Chip size="small" label={conversation.category} variant="outlined" />
                          )}
                        </Box>
                      </Box>
                    }
                  />
                </ListItem>
                {index < filteredConversations.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        )}
      </Box>
    </Card>
  );

  // Render conversation detail
  const renderConversationDetail = () => {
    if (!selectedConversation) {
      return (
        <Card sx={{ height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <Typography color="text.secondary">
            {t('customer_service.select_conversation')}
          </Typography>
        </Card>
      );
    }

    return (
      <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Conversation Header */}
        <CardContent sx={{ pb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
            <Avatar sx={{ bgcolor: 'primary.main' }}>
              {selectedConversation.participant_phone ? <PhoneIcon /> : <PersonIcon />}
            </Avatar>
            
            <Box sx={{ flex: 1 }}>
              <Typography variant="h6">
                {selectedConversation.participant_name || 
                 formatPhone(selectedConversation.participant_phone || '') ||
                 selectedConversation.participant_email ||
                 t('customer_service.unknown_participant')}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                <Chip
                  size="small"
                  label={t(`customer_service.status.${selectedConversation.status}`)}
                  color={selectedConversation.status === 'resolved' ? 'success' : 'primary'}
                />
                <Chip
                  size="small"
                  label={t(`customer_service.priority.${selectedConversation.priority}`)}
                  color={getPriorityColor(selectedConversation.priority) as any}
                  variant="outlined"
                />
                {selectedConversation.category && (
                  <Chip size="small" label={selectedConversation.category} variant="outlined" />
                )}
              </Box>
            </Box>

            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                size="small"
                startIcon={<AssignmentIcon />}
                onClick={() => setAssignDialogOpen(true)}
                disabled={selectedConversation.status === 'closed'}
              >
                {t('customer_service.assign')}
              </Button>

              <Button
                size="small"
                onClick={() => handleStatusChange('resolved')}
                disabled={selectedConversation.status === 'closed'}
              >
                {t('customer_service.resolve')}
              </Button>

              <IconButton size="small">
                <MoreVertIcon />
              </IconButton>
            </Box>
          </Box>
        </CardContent>

        <Divider />

        {/* Messages */}
        <Box sx={{ flex: 1, overflow: 'auto', p: 1 }}>
          {messagesLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <List sx={{ p: 0 }}>
              {messages.map((message: Message) => (
                <ListItem
                  key={message.message_id}
                  sx={{
                    flexDirection: 'column',
                    alignItems: message.message_direction === 'outbound' ? 'flex-end' : 'flex-start',
                    py: 1
                  }}
                >
                  <Paper
                    sx={{
                      p: 2,
                      maxWidth: '70%',
                      bgcolor: message.message_direction === 'outbound' 
                        ? 'primary.main' 
                        : message.internal_note 
                          ? 'warning.light' 
                          : 'grey.100',
                      color: message.message_direction === 'outbound' ? 'primary.contrastText' : 'text.primary'
                    }}
                  >
                    <Typography variant="body2">{message.message_content}</Typography>
                    {message.internal_note && (
                      <Chip size="small" label={t('customer_service.internal_note')} sx={{ mt: 1 }} />
                    )}
                  </Paper>
                  
                  <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5 }}>
                    {message.sender_name && `${message.sender_name} • `}
                    {formatTime(new Date(message.sent_at))}
                    {message.delivery_status && ` • ${message.delivery_status}`}
                  </Typography>
                </ListItem>
              ))}
            </List>
          )}
        </Box>

        <Divider />

        {/* Message Input */}
        <CardContent sx={{ pt: 1 }}>
          <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>
            <TextField
              fullWidth
              multiline
              maxRows={4}
              placeholder={t('customer_service.type_message')}
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSendMessage();
                }
              }}
              disabled={selectedConversation.status === 'closed'}
            />
            
            <IconButton
              onClick={(e) => setQuickResponseMenuAnchor(e.currentTarget)}
              disabled={selectedConversation.status === 'closed'}
            >
              <QuickReplyIcon />
            </IconButton>
            
            <IconButton
              onClick={() => setTemplateDialogOpen(true)}
              disabled={selectedConversation.status === 'closed'}
            >
              <TemplateIcon />
            </IconButton>
            
            <Button
              variant="contained"
              startIcon={<SendIcon />}
              onClick={handleSendMessage}
              disabled={!newMessage.trim() || selectedConversation.status === 'closed'}
            >
              {t('common.send')}
            </Button>
          </Box>
        </CardContent>
      </Card>
    );
  };

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column', p: 3 }}>
      <Typography variant="h4" gutterBottom>
        {t('customer_service.sms_conversations')}
      </Typography>

      <Grid container spacing={3} sx={{ flex: 1 }}>
        <Grid item xs={12} md={4}>
          {renderConversationList()}
        </Grid>
        <Grid item xs={12} md={8}>
          {renderConversationDetail()}
        </Grid>
      </Grid>

      {/* Assignment Dialog */}
      <Dialog open={assignDialogOpen} onClose={() => setAssignDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>{t('customer_service.assign_conversation')}</DialogTitle>
        <DialogContent>
          <FormControl fullWidth sx={{ mt: 2 }}>
            <InputLabel>{t('customer_service.select_agent')}</InputLabel>
            <Select
              value={selectedAgent || ''}
              onChange={(e) => setSelectedAgent(e.target.value as number)}
              label={t('customer_service.select_agent')}
            >
              {agents
                .filter((agent: Agent) => agent.status === 'available')
                .map((agent: Agent) => (
                  <MenuItem key={agent.agent_id} value={agent.agent_id}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography>{agent.agent_name}</Typography>
                      <Chip
                        size="small"
                        label={`${agent.current_conversations}/${agent.max_conversations}`}
                        variant="outlined"
                      />
                    </Box>
                  </MenuItem>
                ))}
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAssignDialogOpen(false)}>{t('common.cancel')}</Button>
          <Button
            onClick={handleAssignConversation}
            variant="contained"
            disabled={!selectedAgent || assignConversationMutation.isLoading}
          >
            {t('customer_service.assign')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Template Dialog */}
      <Dialog open={templateDialogOpen} onClose={() => setTemplateDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>{t('customer_service.select_template')}</DialogTitle>
        <DialogContent>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <List>
                {templates.map((template: Template) => (
                  <ListItem
                    key={template.template_id}
                    button
                    selected={selectedTemplate?.template_id === template.template_id}
                    onClick={() => setSelectedTemplate(template)}
                  >
                    <ListItemText
                      primary={template.template_name}
                      secondary={template.category}
                    />
                  </ListItem>
                ))}
              </List>
            </Grid>
            <Grid item xs={12} md={6}>
              {selectedTemplate && (
                <Box>
                  <Typography variant="h6" gutterBottom>
                    {selectedTemplate.template_name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" paragraph>
                    {selectedTemplate.message_content}
                  </Typography>
                  
                  {selectedTemplate.variables && Object.keys(selectedTemplate.variables).map((varName) => (
                    <TextField
                      key={varName}
                      fullWidth
                      label={varName}
                      value={templateVariables[varName] || ''}
                      onChange={(e) => setTemplateVariables({
                        ...templateVariables,
                        [varName]: e.target.value
                      })}
                      sx={{ mb: 2 }}
                    />
                  ))}
                </Box>
              )}
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTemplateDialogOpen(false)}>{t('common.cancel')}</Button>
          <Button
            onClick={handleSendTemplate}
            variant="contained"
            disabled={!selectedTemplate || sendTemplateMutation.isLoading}
          >
            {t('common.send')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Quick Responses Menu */}
      <Menu
        anchorEl={quickResponseMenuAnchor}
        open={Boolean(quickResponseMenuAnchor)}
        onClose={() => setQuickResponseMenuAnchor(null)}
      >
        {quickResponses.map((response: QuickResponse) => (
          <MenuOption
            key={response.quick_response_id}
            onClick={() => handleQuickResponse(response)}
          >
            <ListItemText
              primary={response.response_text}
              secondary={response.category}
            />
          </MenuOption>
        ))}
      </Menu>

      {/* Real-time Connection Status */}
      {!isConnected && (
        <Alert severity="warning" sx={{ position: 'fixed', bottom: 16, right: 16, zIndex: 1000 }}>
          {t('customer_service.connection_lost')}
        </Alert>
      )}
    </Box>
  );
};

export default SMSConversationManager;