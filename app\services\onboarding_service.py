"""
Onboarding service for managing progressive user setup flows.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from app.database.repositories.onboarding_repository import OnboardingRepository, OnboardingStepRepository
from app.models.onboarding_models import (
    OnboardingFlow,
    OnboardingFlowCreate,
    OnboardingStep,
    OnboardingStepCreate,
    OnboardingStepUpdate,
    OnboardingFlowSummary,
    OnboardingStepSummary,
    OnboardingFlowTemplate,
    OnboardingRole,
    OnboardingStatus,
    OnboardingStepStatus,
    OnboardingStepType
)
from app.models.user_models import User
from app.core.logging import TutorAideLogger
from app.core.exceptions import ResourceNotFoundError, ForbiddenError, ValidationError
from app.config.database import DatabaseManager


class OnboardingService:
    """Service for managing user onboarding flows."""
    
    def __init__(self):
        self.flow_repository = OnboardingRepository()
        self.step_repository = OnboardingStepRepository()
        self.db_manager = DatabaseManager()
        self.logger = TutorAideLogger.get_logger(__name__)
    
    # Onboarding Flow Templates
    def get_client_onboarding_template(self) -> OnboardingFlowTemplate:
        """Get the default onboarding template for clients."""
        steps = [
            OnboardingStepCreate(
                flow_id=0,  # Will be set when creating actual flow
                step_order=1,
                step_type=OnboardingStepType.WELCOME,
                title="Welcome to TutorAide",
                description="Learn about our platform and what to expect",
                is_required=True,
                is_skippable=False,
                estimated_minutes=3,
                instructions="Watch the welcome video and read our getting started guide",
                metadata={"has_video": True, "video_duration": 180}
            ),
            OnboardingStepCreate(
                flow_id=0,
                step_order=2,
                step_type=OnboardingStepType.PROFILE_SETUP,
                title="Complete Your Profile",
                description="Add your basic information and contact details",
                is_required=True,
                is_skippable=False,
                estimated_minutes=10,
                instructions="Fill in your name, phone number, address, and communication preferences",
                validation_rules={
                    "required_fields": ["first_name", "last_name", "phone", "address"],
                    "phone_format": "canadian",
                    "postal_code_format": "canadian"
                }
            ),
            OnboardingStepCreate(
                flow_id=0,
                step_order=3,
                step_type=OnboardingStepType.EMERGENCY_CONTACTS,
                title="Emergency Contacts",
                description="Add emergency contact information for safety",
                is_required=True,
                is_skippable=False,
                estimated_minutes=5,
                instructions="Provide at least one emergency contact with phone number",
                validation_rules={
                    "min_contacts": 1,
                    "required_fields": ["name", "phone", "relationship"]
                }
            ),
            OnboardingStepCreate(
                flow_id=0,
                step_order=4,
                step_type=OnboardingStepType.CONSENT,
                title="Privacy & Consent",
                description="Review and accept our privacy policy and terms",
                is_required=True,
                is_skippable=False,
                estimated_minutes=5,
                instructions="Read and accept Level 1 mandatory consent requirements",
                validation_rules={"required_consents": ["privacy_policy", "terms_of_service"]}
            ),
            OnboardingStepCreate(
                flow_id=0,
                step_order=5,
                step_type=OnboardingStepType.TUTORIAL,
                title="Platform Tutorial",
                description="Learn how to use TutorAide effectively",
                is_required=False,
                is_skippable=True,
                estimated_minutes=10,
                instructions="Take a guided tour of the platform features",
                metadata={"has_interactive_tour": True}
            )
        ]
        
        return OnboardingFlowTemplate(
            name="Client Onboarding Flow",
            role=OnboardingRole.CLIENT,
            description="Complete onboarding process for new clients",
            steps=steps,
            estimated_total_minutes=33,
            is_default=True,
            metadata={"version": "1.0", "last_updated": "2025-01-10"}
        )
    
    def get_tutor_onboarding_template(self) -> OnboardingFlowTemplate:
        """Get the default onboarding template for tutors."""
        steps = [
            OnboardingStepCreate(
                flow_id=0,
                step_order=1,
                step_type=OnboardingStepType.WELCOME,
                title="Welcome to TutorAide",
                description="Welcome to our tutoring platform",
                is_required=True,
                is_skippable=False,
                estimated_minutes=5,
                instructions="Learn about our tutor requirements and expectations",
                metadata={"has_video": True, "video_duration": 300}
            ),
            OnboardingStepCreate(
                flow_id=0,
                step_order=2,
                step_type=OnboardingStepType.PROFILE_SETUP,
                title="Complete Your Tutor Profile",
                description="Add your professional information and qualifications",
                is_required=True,
                is_skippable=False,
                estimated_minutes=20,
                instructions="Fill in your education, experience, and teaching background",
                validation_rules={
                    "required_fields": ["education_level", "specializations", "experience_years"],
                    "min_specializations": 1
                }
            ),
            OnboardingStepCreate(
                flow_id=0,
                step_order=3,
                step_type=OnboardingStepType.VERIFICATION,
                title="Identity Verification",
                description="Complete identity and background verification",
                is_required=True,
                is_skippable=False,
                estimated_minutes=10,
                instructions="Upload required identification documents",
                validation_rules={
                    "required_documents": ["government_id", "education_certificate"],
                    "file_types": ["pdf", "jpg", "png"]
                }
            ),
            OnboardingStepCreate(
                flow_id=0,
                step_order=4,
                step_type=OnboardingStepType.SUBJECTS,
                title="Subject Areas & Rates",
                description="Set your teaching subjects and hourly rates",
                is_required=True,
                is_skippable=False,
                estimated_minutes=15,
                instructions="Select subjects you can teach and set your rates for each",
                validation_rules={
                    "min_subjects": 1,
                    "rate_range": {"min": 20, "max": 150},
                    "currency": "CAD"
                }
            ),
            OnboardingStepCreate(
                flow_id=0,
                step_order=5,
                step_type=OnboardingStepType.AVAILABILITY,
                title="Set Your Availability",
                description="Configure your weekly schedule and preferences",
                is_required=True,
                is_skippable=False,
                estimated_minutes=10,
                instructions="Set your available hours for each day of the week",
                validation_rules={"min_hours_per_week": 5}
            ),
            OnboardingStepCreate(
                flow_id=0,
                step_order=6,
                step_type=OnboardingStepType.LOCATION,
                title="Service Area & Location",
                description="Set your service area and location preferences",
                is_required=True,
                is_skippable=False,
                estimated_minutes=8,
                instructions="Specify whether you offer online, in-person, or both types of tutoring",
                validation_rules={
                    "required_service_types": ["online"],  # At least online required
                    "postal_code_required": True
                }
            ),
            OnboardingStepCreate(
                flow_id=0,
                step_order=7,
                step_type=OnboardingStepType.CONSENT,
                title="Terms & Background Check",
                description="Accept terms and authorize background verification",
                is_required=True,
                is_skippable=False,
                estimated_minutes=7,
                instructions="Review tutor agreement and consent to background check",
                validation_rules={"required_consents": ["tutor_agreement", "background_check"]}
            ),
            OnboardingStepCreate(
                flow_id=0,
                step_order=8,
                step_type=OnboardingStepType.TUTORIAL,
                title="Tutor Platform Training",
                description="Learn how to use the tutor features",
                is_required=False,
                is_skippable=True,
                estimated_minutes=15,
                instructions="Complete the tutor platform training modules",
                metadata={"has_modules": True, "module_count": 5}
            )
        ]
        
        return OnboardingFlowTemplate(
            name="Tutor Onboarding Flow",
            role=OnboardingRole.TUTOR,
            description="Complete onboarding process for new tutors",
            steps=steps,
            estimated_total_minutes=90,
            is_default=True,
            metadata={"version": "1.0", "requires_approval": True}
        )
    
    async def create_onboarding_flow(self, user_id: int, role: OnboardingRole, 
                                   current_user: User) -> OnboardingFlow:
        """Create a new onboarding flow for a user."""
        try:
            # Authorization check
            if not self._can_create_flow(current_user, user_id):
                raise ForbiddenError("Not authorized to create onboarding flow for this user")
            
            # Get appropriate template
            if role == OnboardingRole.CLIENT:
                template = self.get_client_onboarding_template()
            elif role == OnboardingRole.TUTOR:
                template = self.get_tutor_onboarding_template()
            else:
                raise ValidationError(f"Onboarding not available for role: {role}")
            
            async with self.db_manager.get_connection() as conn:
                # Check if user already has an active flow for this role
                existing_flows = await self.flow_repository.get_flows_by_user(conn, user_id, role)
                active_flows = [f for f in existing_flows if f.status != OnboardingStatus.COMPLETED]
                
                if active_flows:
                    self.logger.info(f"User {user_id} already has active {role} onboarding flow")
                    return active_flows[0]
                
                # Create new flow
                flow_data = OnboardingFlowCreate(
                    user_id=user_id,
                    role=role,
                    flow_name=template.name,
                    status=OnboardingStatus.NOT_STARTED,
                    current_step_order=None,
                    completion_percentage=0.0,
                    metadata=template.metadata
                )
                
                flow = await self.flow_repository.create_flow(conn, flow_data)
                
                # Create steps from template
                for step_template in template.steps:
                    step_data = OnboardingStepCreate(
                        flow_id=flow.flow_id,
                        step_order=step_template.step_order,
                        step_type=step_template.step_type,
                        title=step_template.title,
                        description=step_template.description,
                        is_required=step_template.is_required,
                        is_skippable=step_template.is_skippable,
                        estimated_minutes=step_template.estimated_minutes,
                        instructions=step_template.instructions,
                        validation_rules=step_template.validation_rules,
                        metadata=step_template.metadata
                    )
                    await self.step_repository.create_step(conn, step_data)
                
                self.logger.info(f"Created {role} onboarding flow {flow.flow_id} for user {user_id}")
                return flow
                
        except Exception as e:
            self.logger.error(f"Error creating onboarding flow: {e}")
            raise
    
    async def get_user_flows(self, user_id: int, current_user: User) -> List[OnboardingFlowSummary]:
        """Get all onboarding flows for a user."""
        try:
            # Authorization check
            if not self._can_view_flows(current_user, user_id):
                raise ForbiddenError("Not authorized to view onboarding flows for this user")
            
            async with self.db_manager.get_connection() as conn:
                flows = await self.flow_repository.get_flows_by_user(conn, user_id)
                
                summaries = []
                for flow in flows:
                    # Get current step info
                    current_step = await self.step_repository.get_current_step(conn, flow.flow_id)
                    stats = await self.step_repository.get_progress_stats(conn, flow.flow_id)
                    
                    summary = OnboardingFlowSummary(
                        flow_id=flow.flow_id,
                        user_id=flow.user_id,
                        role=flow.role,
                        flow_name=flow.flow_name,
                        status=flow.status,
                        completion_percentage=flow.completion_percentage,
                        current_step_title=current_step.title if current_step else None,
                        current_step_order=current_step.step_order if current_step else None,
                        total_steps=stats['total_steps'],
                        completed_steps_count=stats['completed_steps'],
                        estimated_time_remaining_minutes=self._calculate_time_remaining(current_step, stats),
                        started_at=flow.started_at,
                        last_activity_at=flow.updated_at
                    )
                    summaries.append(summary)
                
                return summaries
                
        except Exception as e:
            self.logger.error(f"Error getting user onboarding flows: {e}")
            raise
    
    async def start_onboarding_flow(self, flow_id: int, current_user: User) -> OnboardingFlow:
        """Start an onboarding flow."""
        try:
            async with self.db_manager.get_connection() as conn:
                flow = await self.flow_repository.get_flow_by_id(conn, flow_id)
                if not flow:
                    raise ResourceNotFoundError("Onboarding flow not found")
                
                # Authorization check
                if not self._can_modify_flow(current_user, flow.user_id):
                    raise ForbiddenError("Not authorized to modify this onboarding flow")
                
                # Check if flow can be started
                if flow.status not in [OnboardingStatus.NOT_STARTED, OnboardingStatus.PAUSED]:
                    raise ValidationError(f"Cannot start flow with status: {flow.status}")
                
                # Start the flow and set current step to first step
                steps = await self.step_repository.get_steps_by_flow(conn, flow_id)
                if not steps:
                    raise ValidationError("Onboarding flow has no steps")
                
                first_step = min(steps, key=lambda s: s.step_order)
                
                # Update flow status
                updated_flow = await self.flow_repository.update_flow_status(
                    conn, flow_id, OnboardingStatus.IN_PROGRESS, 
                    current_step_order=first_step.step_order
                )
                
                self.logger.info(f"Started onboarding flow {flow_id} for user {flow.user_id}")
                return updated_flow
                
        except Exception as e:
            self.logger.error(f"Error starting onboarding flow {flow_id}: {e}")
            raise
    
    async def get_flow_steps(self, flow_id: int, current_user: User) -> List[OnboardingStepSummary]:
        """Get all steps for an onboarding flow with accessibility info."""
        try:
            async with self.db_manager.get_connection() as conn:
                flow = await self.flow_repository.get_flow_by_id(conn, flow_id)
                if not flow:
                    raise ResourceNotFoundError("Onboarding flow not found")
                
                # Authorization check
                if not self._can_view_flows(current_user, flow.user_id):
                    raise ForbiddenError("Not authorized to view this onboarding flow")
                
                steps = await self.step_repository.get_steps_by_flow(conn, flow_id)
                
                # Determine step accessibility
                completed_steps = {s.step_order for s in steps if s.is_completed()}
                current_step_order = flow.current_step_order or 1
                
                summaries = []
                for step in steps:
                    is_accessible = self._is_step_accessible(step, completed_steps, current_step_order)
                    
                    summary = OnboardingStepSummary(
                        step_id=step.step_id,
                        step_order=step.step_order,
                        step_type=step.step_type,
                        title=step.title,
                        description=step.description,
                        status=step.status,
                        is_required=step.is_required,
                        is_skippable=step.is_skippable,
                        estimated_minutes=step.estimated_minutes,
                        is_accessible=is_accessible,
                        validation_errors=step.validation_errors,
                        completed_at=step.completed_at
                    )
                    summaries.append(summary)
                
                return summaries
                
        except Exception as e:
            self.logger.error(f"Error getting flow steps for {flow_id}: {e}")
            raise
    
    async def complete_step(self, step_id: int, completion_data: Dict[str, Any], 
                           current_user: User) -> OnboardingStep:
        """Complete an onboarding step."""
        try:
            async with self.db_manager.get_connection() as conn:
                step = await self.step_repository.get_step_by_id(conn, step_id)
                if not step:
                    raise ResourceNotFoundError("Onboarding step not found")
                
                flow = await self.flow_repository.get_flow_by_id(conn, step.flow_id)
                if not flow:
                    raise ResourceNotFoundError("Onboarding flow not found")
                
                # Authorization check
                if not self._can_modify_flow(current_user, flow.user_id):
                    raise ForbiddenError("Not authorized to modify this onboarding step")
                
                # Validate step can be completed
                if step.status not in [OnboardingStepStatus.PENDING, OnboardingStepStatus.IN_PROGRESS]:
                    raise ValidationError(f"Cannot complete step with status: {step.status}")
                
                # Validate completion data
                validation_errors = self._validate_step_completion(step, completion_data)
                if validation_errors:
                    raise ValidationError(f"Step validation failed: {'; '.join(validation_errors)}")
                
                # Update step status
                update_data = OnboardingStepUpdate(
                    status=OnboardingStepStatus.COMPLETED,
                    completion_data=completion_data
                )
                
                updated_step = await self.step_repository.update_step(conn, step_id, update_data)
                
                # Update flow progress
                await self._update_flow_progress(conn, flow.flow_id)
                
                self.logger.info(f"Completed onboarding step {step_id} for user {flow.user_id}")
                return updated_step
                
        except Exception as e:
            self.logger.error(f"Error completing onboarding step {step_id}: {e}")
            raise
    
    async def skip_step(self, step_id: int, skip_reason: str, current_user: User) -> OnboardingStep:
        """Skip an onboarding step."""
        try:
            async with self.db_manager.get_connection() as conn:
                step = await self.step_repository.get_step_by_id(conn, step_id)
                if not step:
                    raise ResourceNotFoundError("Onboarding step not found")
                
                flow = await self.flow_repository.get_flow_by_id(conn, step.flow_id)
                if not flow:
                    raise ResourceNotFoundError("Onboarding flow not found")
                
                # Authorization check
                if not self._can_modify_flow(current_user, flow.user_id):
                    raise ForbiddenError("Not authorized to modify this onboarding step")
                
                # Validate step can be skipped
                if not step.can_skip():
                    raise ValidationError("This step cannot be skipped")
                
                # Update step status
                update_data = OnboardingStepUpdate(
                    status=OnboardingStepStatus.SKIPPED,
                    skip_reason=skip_reason
                )
                
                updated_step = await self.step_repository.update_step(conn, step_id, update_data)
                
                # Update flow progress
                await self._update_flow_progress(conn, flow.flow_id)
                
                self.logger.info(f"Skipped onboarding step {step_id} for user {flow.user_id}: {skip_reason}")
                return updated_step
                
        except Exception as e:
            self.logger.error(f"Error skipping onboarding step {step_id}: {e}")
            raise
    
    # Helper methods
    def _can_create_flow(self, current_user: User, target_user_id: int) -> bool:
        """Check if user can create onboarding flow."""
        return (current_user.user_id == target_user_id or 
                'manager' in [role.role_name for role in current_user.roles])
    
    def _can_view_flows(self, current_user: User, target_user_id: int) -> bool:
        """Check if user can view onboarding flows."""
        return (current_user.user_id == target_user_id or 
                'manager' in [role.role_name for role in current_user.roles])
    
    def _can_modify_flow(self, current_user: User, target_user_id: int) -> bool:
        """Check if user can modify onboarding flow."""
        return (current_user.user_id == target_user_id or 
                'manager' in [role.role_name for role in current_user.roles])
    
    def _is_step_accessible(self, step: OnboardingStep, completed_steps: set, 
                           current_step_order: int) -> bool:
        """Check if a step is accessible based on progress."""
        # Step is accessible if:
        # 1. It's already completed or skipped
        # 2. It's the current step
        # 3. All previous required steps are completed
        
        if step.is_completed() or step.is_skipped():
            return True
        
        if step.step_order == current_step_order:
            return True
        
        if step.step_order > current_step_order:
            return False
        
        # Check if all previous required steps are completed
        for order in range(1, step.step_order):
            if order not in completed_steps:
                return False
        
        return True
    
    def _validate_step_completion(self, step: OnboardingStep, 
                                 completion_data: Dict[str, Any]) -> List[str]:
        """Validate step completion data."""
        errors = []
        
        if not step.validation_rules:
            return errors
        
        rules = step.validation_rules
        
        # Check required fields
        if 'required_fields' in rules:
            for field in rules['required_fields']:
                if field not in completion_data or not completion_data[field]:
                    errors.append(f"Required field '{field}' is missing")
        
        # Check minimum values
        if 'min_contacts' in rules:
            contacts = completion_data.get('contacts', [])
            if len(contacts) < rules['min_contacts']:
                errors.append(f"Minimum {rules['min_contacts']} contacts required")
        
        # Check required consents
        if 'required_consents' in rules:
            consents = completion_data.get('consents', {})
            for consent in rules['required_consents']:
                if not consents.get(consent):
                    errors.append(f"Consent for '{consent}' is required")
        
        return errors
    
    def _calculate_time_remaining(self, current_step: Optional[OnboardingStep], 
                                 stats: Dict[str, int]) -> Optional[int]:
        """Calculate estimated time remaining for onboarding."""
        if not current_step or stats['total_steps'] == 0:
            return None
        
        # This is a simplified calculation
        # In practice, you'd sum up estimated_minutes for remaining steps
        remaining_steps = stats['pending_steps'] + stats['in_progress_steps']
        
        if remaining_steps == 0:
            return 0
        
        # Estimate 10 minutes per remaining step (can be improved)
        return remaining_steps * 10
    
    async def _update_flow_progress(self, conn, flow_id: int) -> None:
        """Update onboarding flow progress based on completed steps."""
        try:
            stats = await self.step_repository.get_progress_stats(conn, flow_id)
            
            if stats['total_steps'] == 0:
                return
            
            # Calculate completion percentage
            completed_count = stats['completed_steps'] + stats['skipped_steps']
            completion_percentage = (completed_count / stats['total_steps']) * 100.0
            
            # Determine flow status
            if completion_percentage >= 100.0:
                status = OnboardingStatus.COMPLETED
            elif stats['in_progress_steps'] > 0 or stats['completed_steps'] > 0:
                status = OnboardingStatus.IN_PROGRESS
            else:
                status = OnboardingStatus.NOT_STARTED
            
            # Get next step order
            current_step = await self.step_repository.get_current_step(conn, flow_id)
            current_step_order = current_step.step_order if current_step else None
            
            # Update flow
            await self.flow_repository.update_flow_status(
                conn, flow_id, status, completion_percentage, current_step_order
            )
            
        except Exception as e:
            self.logger.error(f"Error updating flow progress for {flow_id}: {e}")
            raise
