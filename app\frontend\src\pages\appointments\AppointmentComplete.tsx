import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { CheckCircle, AlertCircle, Clock, Info } from 'lucide-react';
import api from '../../services/api';
import AppointmentBilling from '../../components/billing/AppointmentBilling';
import { Card } from '../../components/common/Card';
import Button from '../../components/common/Button';
import { Input } from '../../components/common/Input';
import { Toggle } from '../../components/common/Toggle';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import { useAuth } from '../../contexts/AuthContext';
import toast from 'react-hot-toast';

interface Appointment {
  appointment_id: number;
  client_id: number;
  client_name: string;
  tutor_id: number;
  tutor_name: string;
  subject: string;
  start_time: string;
  end_time: string;
  duration: number;
  rate: number;
  status: string;
  billing_status: string;
  actual_duration_minutes?: number;
  duration_adjusted?: boolean;
  duration_adjustment_reason?: string;
  original_duration_minutes?: number;
  duration_difference_minutes?: number;
}

const AppointmentComplete: React.FC = () => {
  const { appointmentId } = useParams<{ appointmentId: string }>();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [appointment, setAppointment] = useState<Appointment | null>(null);
  const [loading, setLoading] = useState(true);
  const [completing, setCompleting] = useState(false);
  const [showBilling, setShowBilling] = useState(false);
  const [showDurationAdjustment, setShowDurationAdjustment] = useState(false);
  const [actualDuration, setActualDuration] = useState<string>('');
  const [adjustmentReason, setAdjustmentReason] = useState('');
  const [tutorNoShow, setTutorNoShow] = useState(false);
  const [clientNoShow, setClientNoShow] = useState(false);
  const [completionNotes, setCompletionNotes] = useState('');
  
  const isManager = user?.activeRole === 'manager';
  const isTutor = user?.activeRole === 'tutor';

  useEffect(() => {
    if (appointmentId) {
      fetchAppointment();
    }
  }, [appointmentId]);

  const fetchAppointment = async () => {
    try {
      setLoading(true);
      const response = await api.get<Appointment>(`/appointments/${appointmentId}`);
      setAppointment(response.data);
      
      // Show billing if appointment is completed but not billed
      if (response.data.status === 'completed' && response.data.billing_status === 'pending') {
        setShowBilling(true);
      }
    } catch (error) {
      console.error('Error fetching appointment:', error);
      toast.error(t('appointments.errors.fetchAppointment'));
      navigate('/calendar');
    } finally {
      setLoading(false);
    }
  };

  const handleCompleteAppointment = async () => {
    if (!appointment) return;
    
    try {
      setCompleting(true);
      
      const completionData: any = {
        status: 'completed',
        tutor_no_show: tutorNoShow,
        client_no_show: clientNoShow,
        completion_notes: completionNotes || undefined
      };
      
      // Add duration adjustment if different from scheduled
      if (showDurationAdjustment && actualDuration) {
        const actualMinutes = parseInt(actualDuration);
        if (!isNaN(actualMinutes) && actualMinutes !== appointment.duration * 60) {
          completionData.actual_duration_minutes = actualMinutes;
          completionData.duration_adjustment = {
            actual_duration_minutes: actualMinutes,
            adjustment_reason: adjustmentReason || 'Duration adjusted during completion',
            notify_participants: true
          };
        }
      }
      
      const response = await api.post(`/appointments/${appointment.appointment_id}/complete`, completionData);
      
      toast.success(t('appointments.completionSuccess'));
      setAppointment({ ...appointment, ...response.data.appointment });
      
      if (response.data.duration_adjusted) {
        toast.info(t('appointments.durationAdjusted'));
      }
      
      setShowBilling(true);
    } catch (error) {
      console.error('Error completing appointment:', error);
      toast.error(t('appointments.errors.completeAppointment'));
    } finally {
      setCompleting(false);
    }
  };

  const handleBillingComplete = () => {
    toast.success(t('billing.appointment.billingSuccess'));
    navigate('/calendar');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!appointment) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Card className="p-6 max-w-md w-full">
          <div className="text-center">
            <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-lg font-semibold text-gray-900 mb-2">
              {t('billing.appointment.notFound')}
            </h2>
            <Button
              variant="primary"
              onClick={() => navigate('/calendar')}
            >
              {t('common.back')}
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-semibold text-gray-900 mb-6">
        {t('appointments.completeAppointment')}
      </h1>

      {/* Appointment Details */}
      <Card className="mb-6">
        <div className="p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            {t('billing.appointment.details')}
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-500">{t('billing.appointment.client')}</p>
              <p className="text-sm font-medium text-gray-900">{appointment.client_name}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">{t('billing.appointment.tutor')}</p>
              <p className="text-sm font-medium text-gray-900">{appointment.tutor_name}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">{t('billing.appointment.subject')}</p>
              <p className="text-sm font-medium text-gray-900">{appointment.subject}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">{t('billing.appointment.duration')}</p>
              <p className="text-sm font-medium text-gray-900">
                {appointment.duration} {t('common.hours')}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">{t('billing.appointment.rate')}</p>
              <p className="text-sm font-medium text-gray-900">
                ${appointment.rate}/hr
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">{t('common.status')}</p>
              <p className="text-sm font-medium text-gray-900">
                {appointment.status === 'completed' ? (
                  <span className="text-green-600 flex items-center">
                    <CheckCircle className="w-4 h-4 mr-1" />
                    {t('appointments.status.completed')}
                  </span>
                ) : (
                  t(`appointments.status.${appointment.status}`)
                )}
              </p>
            </div>
          </div>
        </div>
      </Card>

      {/* Complete Appointment Button */}
      {appointment.status !== 'completed' && (isManager || isTutor) && (
        <>
          <Card className="mb-6">
            <div className="p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                {t('appointments.confirmCompletion')}
              </h2>
              <p className="text-sm text-gray-600 mb-4">
                {t('appointments.confirmCompletionMessage')}
              </p>
              
              {/* Duration Adjustment Toggle */}
              <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                  <label className="text-sm font-medium text-gray-700">
                    {t('appointments.adjustDuration')}
                  </label>
                  <Toggle
                    enabled={showDurationAdjustment}
                    onChange={setShowDurationAdjustment}
                  />
                </div>
                {showDurationAdjustment && (
                  <div className="mt-4 space-y-4 p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <Info className="w-4 h-4" />
                      <span>{t('appointments.durationAdjustmentInfo')}</span>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {t('appointments.actualDuration')}
                        </label>
                        <div className="relative">
                          <Input
                            type="number"
                            value={actualDuration}
                            onChange={(e) => setActualDuration(e.target.value)}
                            placeholder={String(appointment.duration * 60)}
                            min="15"
                            max="480"
                            className="pr-16"
                          />
                          <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-sm text-gray-500">
                            {t('common.minutes')}
                          </span>
                        </div>
                        <p className="mt-1 text-xs text-gray-500">
                          {t('appointments.scheduledDuration')}: {appointment.duration * 60} {t('common.minutes')}
                        </p>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {t('appointments.adjustmentReason')}
                        </label>
                        <Input
                          type="text"
                          value={adjustmentReason}
                          onChange={(e) => setAdjustmentReason(e.target.value)}
                          placeholder={t('appointments.adjustmentReasonPlaceholder')}
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>
              
              {/* No-show Options */}
              <div className="mb-4 space-y-2">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={tutorNoShow}
                    onChange={(e) => setTutorNoShow(e.target.checked)}
                    className="rounded border-gray-300 text-red-600 focus:ring-red-500"
                  />
                  <span className="text-sm text-gray-700">{t('appointments.tutorNoShow')}</span>
                </label>
                
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={clientNoShow}
                    onChange={(e) => setClientNoShow(e.target.checked)}
                    className="rounded border-gray-300 text-red-600 focus:ring-red-500"
                  />
                  <span className="text-sm text-gray-700">{t('appointments.clientNoShow')}</span>
                </label>
              </div>
              
              {/* Completion Notes */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('appointments.completionNotes')}
                </label>
                <textarea
                  value={completionNotes}
                  onChange={(e) => setCompletionNotes(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                  placeholder={t('appointments.completionNotesPlaceholder')}
                />
              </div>
              
              <Button
                variant="primary"
                onClick={handleCompleteAppointment}
                loading={completing}
                disabled={completing || (showDurationAdjustment && actualDuration && !adjustmentReason)}
                leftIcon={<CheckCircle className="w-4 h-4" />}
              >
                {t('appointments.markAsCompleted')}
              </Button>
            </div>
          </Card>
        </>
      )}

      {/* Billing Section */}
      {showBilling && appointment.billing_status === 'pending' && isManager && (
        <AppointmentBilling
          appointmentId={appointment.appointment_id}
          onBillingComplete={handleBillingComplete}
        />
      )}

      {/* Duration Adjustment Info */}
      {appointment.duration_adjusted && (
        <Card className="mb-6 border-red-200 bg-red-50">
          <div className="p-6">
            <div className="flex items-start space-x-3">
              <Clock className="w-5 h-5 text-red-600 mt-0.5" />
              <div className="flex-1">
                <h3 className="text-sm font-semibold text-red-900 mb-2">
                  {t('appointments.durationWasAdjusted')}
                </h3>
                <div className="space-y-1 text-sm text-red-800">
                  <p>
                    <span className="font-medium">{t('appointments.originalDuration')}:</span>{' '}
                    {appointment.original_duration_minutes || appointment.duration * 60} {t('common.minutes')}
                  </p>
                  <p>
                    <span className="font-medium">{t('appointments.actualDuration')}:</span>{' '}
                    {appointment.actual_duration_minutes} {t('common.minutes')}
                  </p>
                  <p>
                    <span className="font-medium">{t('appointments.difference')}:</span>{' '}
                    {appointment.duration_difference_minutes && appointment.duration_difference_minutes > 0 ? '+' : ''}
                    {appointment.duration_difference_minutes} {t('common.minutes')}
                  </p>
                  {appointment.duration_adjustment_reason && (
                    <p>
                      <span className="font-medium">{t('appointments.reason')}:</span>{' '}
                      {appointment.duration_adjustment_reason}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* Already Billed Message */}
      {appointment.billing_status === 'billed' && (
        <Card className="mb-6">
          <div className="p-6">
            <div className="flex items-center text-green-600">
              <CheckCircle className="w-5 h-5 mr-2" />
              <span className="font-medium">{t('billing.appointment.alreadyBilled')}</span>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default AppointmentComplete;
