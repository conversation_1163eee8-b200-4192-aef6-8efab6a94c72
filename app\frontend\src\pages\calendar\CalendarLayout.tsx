import React, { useState } from 'react';
import { Outlet, useLocation, Link } from 'react-router-dom';
import { Calendar, Clock, Filter, Settings, Repeat, Plus } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import Button from '../../components/common/Button';

interface Tab {
  id: string;
  label: string;
  path: string;
  icon: React.ReactNode;
}

const CalendarLayout: React.FC = () => {
  const { t } = useTranslation();
  const location = useLocation();
  const [selectedDate, setSelectedDate] = useState(new Date());

  const tabs: Tab[] = [
    { id: 'day', label: t('calendar.dayView'), path: '/calendar/day', icon: <Calendar className="w-4 h-4" /> },
    { id: 'week', label: t('calendar.weekView'), path: '/calendar/week', icon: <Calendar className="w-4 h-4" /> },
    { id: 'month', label: t('calendar.monthView'), path: '/calendar/month', icon: <Calendar className="w-4 h-4" /> },
    { id: 'schedule', label: t('calendar.schedule'), path: '/calendar/schedule', icon: <Clock className="w-4 h-4" /> },
    { id: 'recurring', label: t('calendar.recurring'), path: '/calendar/recurring', icon: <Repeat className="w-4 h-4" /> },
  ];

  const currentTab = tabs.find(tab => location.pathname.startsWith(tab.path));

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">
              {t('sidebar.calendar')}
            </h1>
            <p className="text-sm text-gray-500 mt-1">
              {t('calendar.description')}
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-full text-gray-700 bg-white hover:bg-gray-50 transition-colors"
            >
              <Filter className="w-4 h-4 mr-2" />
              {t('common.filter')}
            </button>
            
            <button
              className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-full text-gray-700 bg-white hover:bg-gray-50 transition-colors"
            >
              <Settings className="w-4 h-4 mr-2" />
              {t('calendar.settings')}
            </button>
            
            <Button
              variant="primary"
              leftIcon={<Plus className="w-4 h-4" />}
            >
              {t('calendar.newAppointment')}
            </Button>
          </div>
        </div>

        {/* Tabs */}
        <div className="mt-4">
          <nav className="flex space-x-8" aria-label="Tabs">
            {tabs.map((tab) => {
              const isActive = currentTab?.id === tab.id;
              return (
                <Link
                  key={tab.id}
                  to={tab.path}
                  className={`${
                    isActive
                      ? 'border-accent-red text-accent-red'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 transition-colors duration-200`}
                >
                  {tab.icon}
                  <span>{tab.label}</span>
                </Link>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        <Outlet context={{ selectedDate, setSelectedDate }} />
      </div>
    </div>
  );
};

export default CalendarLayout;