import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Mail, Plus, Search, Filter, RefreshCw, XCircle, 
  Clock, CheckCircle, AlertCircle, Send, Trash2,
  User, Calendar, ExternalLink
} from 'lucide-react';
import api from '../../services/api';
import { Card } from '../../components/common/Card';
import Button from '../../components/common/Button';
import { Input } from '../../components/common/Input';
import { Select } from '../../components/common/Select';
import { Badge } from '../../components/common/Badge';
import { Modal } from '../../components/common/Modal';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import { EmptyState } from '../../components/common/EmptyState';
import { SearchBar } from '../../components/forms/SearchBar';
import toast from 'react-hot-toast';
import { useAuth } from '../../contexts/AuthContext';
import { hasPermission } from '../../utils/permissions';
import { formatDistanceToNow } from 'date-fns';

interface TutorInvitation {
  invitation_id: number;
  email: string;
  first_name?: string;
  last_name?: string;
  phone?: string;
  message?: string;
  status: string;
  token: string;
  invited_by: number;
  accepted_at?: string;
  accepted_user_id?: number;
  expires_at: string;
  created_at: string;
  updated_at: string;
}

interface TutorInvitationWithInviter extends TutorInvitation {
  inviter_email: string;
  inviter_first_name: string;
  inviter_last_name: string;
}

interface InvitationFormData {
  email: string;
  first_name: string;
  last_name: string;
  phone: string;
  message: string;
}

const TutorInvitations: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  
  const [invitations, setInvitations] = useState<TutorInvitationWithInviter[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [onlyMine, setOnlyMine] = useState(false);
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [sendingInvitation, setSendingInvitation] = useState(false);
  const [actionLoading, setActionLoading] = useState<number | null>(null);

  // Form state for new invitation
  const [formData, setFormData] = useState<InvitationFormData>({
    email: '',
    first_name: '',
    last_name: '',
    phone: '',
    message: ''
  });

  // Check permissions
  const canManageInvitations = hasPermission(user?.role, 'manage_tutors');

  useEffect(() => {
    if (canManageInvitations) {
      fetchInvitations();
    }
  }, [onlyMine, canManageInvitations]);

  const fetchInvitations = async () => {
    try {
      setLoading(true);
      const response = await api.get('/api/v1/tutors/invitations', {
        params: { only_mine: onlyMine }
      });
      setInvitations(response.data);
    } catch (error) {
      console.error('Error fetching invitations:', error);
      toast.error(t('errors.fetch_invitations_failed'));
    } finally {
      setLoading(false);
    }
  };

  const searchInvitations = async (query: string) => {
    if (!query.trim()) {
      fetchInvitations();
      return;
    }

    try {
      setLoading(true);
      const response = await api.get('/api/v1/tutors/invitations/search', {
        params: { q: query, limit: 50 }
      });
      setInvitations(response.data);
    } catch (error) {
      console.error('Error searching invitations:', error);
      toast.error(t('errors.search_failed'));
    } finally {
      setLoading(false);
    }
  };

  const handleSendInvitation = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.email) {
      toast.error(t('errors.email_required'));
      return;
    }

    try {
      setSendingInvitation(true);
      
      const invitationData = {
        email: formData.email,
        first_name: formData.first_name || null,
        last_name: formData.last_name || null,
        phone: formData.phone || null,
        message: formData.message || null
      };

      await api.post('/api/v1/tutors/invite', invitationData);
      
      toast.success(t('invitations.sent_successfully'));
      setShowInviteModal(false);
      setFormData({
        email: '',
        first_name: '',
        last_name: '',
        phone: '',
        message: ''
      });
      
      // Refresh invitations list
      fetchInvitations();
    } catch (error: any) {
      console.error('Error sending invitation:', error);
      if (error.response?.status === 409) {
        toast.error(t('errors.user_already_exists'));
      } else {
        toast.error(t('errors.send_invitation_failed'));
      }
    } finally {
      setSendingInvitation(false);
    }
  };

  const handleResendInvitation = async (invitationId: number) => {
    try {
      setActionLoading(invitationId);
      await api.post(`/api/v1/tutors/invitation/${invitationId}/resend`);
      toast.success(t('invitations.resent_successfully'));
      fetchInvitations();
    } catch (error) {
      console.error('Error resending invitation:', error);
      toast.error(t('errors.resend_invitation_failed'));
    } finally {
      setActionLoading(null);
    }
  };

  const handleCancelInvitation = async (invitationId: number) => {
    if (!confirm(t('invitations.confirm_cancel'))) {
      return;
    }

    try {
      setActionLoading(invitationId);
      await api.delete(`/api/v1/tutors/invitation/${invitationId}`);
      toast.success(t('invitations.cancelled_successfully'));
      fetchInvitations();
    } catch (error) {
      console.error('Error cancelling invitation:', error);
      toast.error(t('errors.cancel_invitation_failed'));
    } finally {
      setActionLoading(null);
    }
  };

  const getStatusBadge = (status: string, expiresAt: string) => {
    const isExpired = new Date(expiresAt) < new Date();
    
    if (isExpired && status === 'pending') {
      return <Badge variant="destructive" icon={<XCircle size={12} />}>
        {t('invitations.status.expired')}
      </Badge>;
    }

    switch (status) {
      case 'pending':
        return <Badge variant="warning" icon={<Clock size={12} />}>
          {t('invitations.status.pending')}
        </Badge>;
      case 'accepted':
        return <Badge variant="success" icon={<CheckCircle size={12} />}>
          {t('invitations.status.accepted')}
        </Badge>;
      case 'expired':
        return <Badge variant="destructive" icon={<XCircle size={12} />}>
          {t('invitations.status.expired')}
        </Badge>;
      default:
        return <Badge variant="secondary" icon={<AlertCircle size={12} />}>
          {status}
        </Badge>;
    }
  };

  const getInvitationLink = (token: string) => {
    return `${window.location.origin}/accept-invitation?token=${token}`;
  };

  const copyInvitationLink = (token: string) => {
    const link = getInvitationLink(token);
    navigator.clipboard.writeText(link);
    toast.success(t('invitations.link_copied'));
  };

  const filteredInvitations = invitations.filter(invitation => {
    const matchesStatus = statusFilter === 'all' || invitation.status === statusFilter;
    const matchesSearch = !searchQuery || 
      invitation.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (invitation.first_name && invitation.first_name.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (invitation.last_name && invitation.last_name.toLowerCase().includes(searchQuery.toLowerCase()));
    
    return matchesStatus && matchesSearch;
  });

  if (!canManageInvitations) {
    return (
      <EmptyState
        title={t('errors.access_denied')}
        description={t('errors.insufficient_permissions')}
        icon={<XCircle />}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            {t('invitations.title')}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {t('invitations.description')}
          </p>
        </div>
        <Button 
          onClick={() => setShowInviteModal(true)}
          icon={<Plus />}
          className="bg-blue-600 hover:bg-blue-700"
        >
          {t('invitations.send_invitation')}
        </Button>
      </div>

      {/* Filters and Search */}
      <Card>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="md:col-span-2">
            <SearchBar
              value={searchQuery}
              onChange={setSearchQuery}
              onSearch={() => searchInvitations(searchQuery)}
              placeholder={t('invitations.search_placeholder')}
            />
          </div>
          <Select
            value={statusFilter}
            onChange={setStatusFilter}
            options={[
              { value: 'all', label: t('invitations.status.all') },
              { value: 'pending', label: t('invitations.status.pending') },
              { value: 'accepted', label: t('invitations.status.accepted') },
              { value: 'expired', label: t('invitations.status.expired') }
            ]}
          />
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="only-mine"
              checked={onlyMine}
              onChange={(e) => setOnlyMine(e.target.checked)}
              className="rounded border-gray-300"
            />
            <label htmlFor="only-mine" className="text-sm text-gray-700 dark:text-gray-300">
              {t('invitations.only_mine')}
            </label>
          </div>
        </div>
      </Card>

      {/* Invitations List */}
      {loading ? (
        <div className="flex justify-center py-8">
          <LoadingSpinner />
        </div>
      ) : filteredInvitations.length === 0 ? (
        <EmptyState
          title={t('invitations.no_invitations')}
          description={t('invitations.no_invitations_description')}
          icon={<Mail />}
          action={
            <Button 
              onClick={() => setShowInviteModal(true)}
              icon={<Plus />}
            >
              {t('invitations.send_first_invitation')}
            </Button>
          }
        />
      ) : (
        <div className="space-y-4">
          {filteredInvitations.map((invitation) => {
            const isExpired = new Date(invitation.expires_at) < new Date();
            const isPending = invitation.status === 'pending' && !isExpired;
            
            return (
              <Card key={invitation.invitation_id}>
                <div className="flex items-center justify-between p-6">
                  <div className="flex-1">
                    <div className="flex items-center space-x-4">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <h3 className="font-medium text-gray-900 dark:text-white">
                            {invitation.first_name && invitation.last_name 
                              ? `${invitation.first_name} ${invitation.last_name}`
                              : invitation.email
                            }
                          </h3>
                          {getStatusBadge(invitation.status, invitation.expires_at)}
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {invitation.email}
                        </p>
                        {invitation.phone && (
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {invitation.phone}
                          </p>
                        )}
                        <div className="mt-2 space-y-1">
                          <p className="text-xs text-gray-500">
                            {t('invitations.invited_by')}: {invitation.inviter_first_name} {invitation.inviter_last_name}
                          </p>
                          <p className="text-xs text-gray-500">
                            {t('invitations.sent')}: {formatDistanceToNow(new Date(invitation.created_at))} {t('common.ago')}
                          </p>
                          <p className="text-xs text-gray-500">
                            {t('invitations.expires')}: {formatDistanceToNow(new Date(invitation.expires_at))} 
                            {isExpired ? ` (${t('invitations.expired')})` : ''}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    {isPending && (
                      <>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => copyInvitationLink(invitation.token)}
                          icon={<ExternalLink size={16} />}
                        >
                          {t('invitations.copy_link')}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleResendInvitation(invitation.invitation_id)}
                          disabled={actionLoading === invitation.invitation_id}
                          icon={actionLoading === invitation.invitation_id ? 
                            <LoadingSpinner size="sm" /> : <RefreshCw size={16} />
                          }
                        >
                          {t('invitations.resend')}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleCancelInvitation(invitation.invitation_id)}
                          disabled={actionLoading === invitation.invitation_id}
                          icon={actionLoading === invitation.invitation_id ? 
                            <LoadingSpinner size="sm" /> : <Trash2 size={16} />
                          }
                          className="text-red-600 hover:text-red-700"
                        >
                          {t('invitations.cancel')}
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              </Card>
            );
          })}
        </div>
      )}

      {/* Send Invitation Modal */}
      <Modal
        isOpen={showInviteModal}
        onClose={() => setShowInviteModal(false)}
        title={t('invitations.send_invitation')}
        maxWidth="md"
      >
        <form onSubmit={handleSendInvitation} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label={t('form.email')}
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              required
              placeholder="<EMAIL>"
            />
            <div></div>
            <Input
              label={t('form.first_name')}
              value={formData.first_name}
              onChange={(e) => setFormData({ ...formData, first_name: e.target.value })}
              placeholder={t('form.first_name_placeholder')}
            />
            <Input
              label={t('form.last_name')}
              value={formData.last_name}
              onChange={(e) => setFormData({ ...formData, last_name: e.target.value })}
              placeholder={t('form.last_name_placeholder')}
            />
          </div>
          
          <Input
            label={t('form.phone')}
            type="tel"
            value={formData.phone}
            onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
            placeholder="(*************"
          />
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('form.personal_message')}
            </label>
            <textarea
              value={formData.message}
              onChange={(e) => setFormData({ ...formData, message: e.target.value })}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              placeholder={t('invitations.message_placeholder')}
            />
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowInviteModal(false)}
            >
              {t('common.cancel')}
            </Button>
            <Button
              type="submit"
              disabled={sendingInvitation || !formData.email}
              icon={sendingInvitation ? <LoadingSpinner size="sm" /> : <Send />}
            >
              {sendingInvitation ? t('invitations.sending') : t('invitations.send')}
            </Button>
          </div>
        </form>
      </Modal>
    </div>
  );
};

export default TutorInvitations;