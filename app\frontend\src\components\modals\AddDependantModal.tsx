import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { X, Search } from 'lucide-react';
import { Modal } from '../common/Modal';
import Button from '../common/Button';
import { Input } from '../common/Input';
import { dependantService, DependantCreateRequest } from '../../services/dependantService';
import { clientService, ClientProfile } from '../../services/clientService';
import toast from 'react-hot-toast';

interface AddDependantModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  defaultClientId?: number;
}

const AddDependantModal: React.FC<AddDependantModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  defaultClientId
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [searchingClients, setSearchingClients] = useState(false);
  const [clientSearchTerm, setClientSearchTerm] = useState('');
  const [clientSearchResults, setClientSearchResults] = useState<ClientProfile[]>([]);
  const [selectedClient, setSelectedClient] = useState<ClientProfile | null>(null);
  
  const [formData, setFormData] = useState<DependantCreateRequest>({
    parent1_client_id: defaultClientId || 0,
    first_name: '',
    last_name: '',
    date_of_birth: '',
    grade_level: '',
    school_name: '',
    special_needs: '',
    medical_info: '',
    notes: ''
  });

  useEffect(() => {
    if (defaultClientId) {
      // Load the default client info
      loadDefaultClient();
    }
  }, [defaultClientId]);

  const loadDefaultClient = async () => {
    if (!defaultClientId) return;
    
    try {
      const client = await clientService.getClient(defaultClientId);
      setSelectedClient(client);
    } catch (error) {
      console.error('Error loading default client:', error);
    }
  };

  const searchClients = async () => {
    if (!clientSearchTerm) return;
    
    setSearchingClients(true);
    try {
      const response = await clientService.searchClients({
        search: clientSearchTerm,
        limit: 10
      });
      setClientSearchResults(response.items);
    } catch (error) {
      console.error('Error searching clients:', error);
      toast.error(t('users.dependants.clientSearchError'));
    } finally {
      setSearchingClients(false);
    }
  };

  const selectClient = (client: ClientProfile) => {
    setSelectedClient(client);
    setFormData(prev => ({
      ...prev,
      parent1_client_id: client.client_id
    }));
    setClientSearchResults([]);
    setClientSearchTerm('');
  };

  const handleInputChange = (field: keyof DependantCreateRequest, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const calculateAge = (dateOfBirth: string) => {
    if (!dateOfBirth) return null;
    const birthDate = new Date(dateOfBirth);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.first_name || !formData.last_name || !formData.date_of_birth || !formData.parent1_client_id) {
      toast.error(t('validation.requiredFields'));
      return;
    }

    // Validate age
    const age = calculateAge(formData.date_of_birth);
    if (age === null || age < 0 || age > 25) {
      toast.error(t('users.dependants.invalidAge'));
      return;
    }

    setLoading(true);
    try {
      await dependantService.createDependant(formData);
      toast.success(t('users.dependants.addSuccess'));
      onSuccess();
      onClose();
    } catch (error: any) {
      console.error('Error creating dependant:', error);
      toast.error(error.response?.data?.detail || t('users.dependants.addError'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="lg">
      <form onSubmit={handleSubmit} className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">
            {t('users.dependants.addTitle')}
          </h2>
          <button
            type="button"
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Form Fields */}
        <div className="space-y-6">
          {/* Parent Selection */}
          {!defaultClientId && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {t('users.dependants.parentInfo')}
              </h3>
              
              {selectedClient ? (
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-gray-900">
                        {selectedClient.first_name} {selectedClient.last_name}
                      </div>
                      <div className="text-sm text-gray-600">{selectedClient.email}</div>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setSelectedClient(null);
                        setFormData(prev => ({ ...prev, parent1_client_id: 0 }));
                      }}
                    >
                      {t('common.change')}
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="space-y-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <Input
                      type="text"
                      value={clientSearchTerm}
                      onChange={(e) => setClientSearchTerm(e.target.value)}
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          searchClients();
                        }
                      }}
                      placeholder={t('users.dependants.searchParent')}
                      className="pl-10"
                    />
                  </div>
                  
                  {clientSearchResults.length > 0 && (
                    <div className="border rounded-lg max-h-48 overflow-y-auto">
                      {clientSearchResults.map((client) => (
                        <button
                          key={client.client_id}
                          type="button"
                          onClick={() => selectClient(client)}
                          className="w-full text-left px-4 py-2 hover:bg-gray-50 border-b last:border-b-0"
                        >
                          <div className="font-medium">{client.first_name} {client.last_name}</div>
                          <div className="text-sm text-gray-600">{client.email}</div>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Basic Information */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {t('users.dependants.basicInfo')}
            </h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('users.dependants.firstName')} *
                </label>
                <Input
                  type="text"
                  value={formData.first_name}
                  onChange={(e) => handleInputChange('first_name', e.target.value)}
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('users.dependants.lastName')} *
                </label>
                <Input
                  type="text"
                  value={formData.last_name}
                  onChange={(e) => handleInputChange('last_name', e.target.value)}
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('users.dependants.dateOfBirth')} *
                </label>
                <Input
                  type="date"
                  value={formData.date_of_birth}
                  onChange={(e) => handleInputChange('date_of_birth', e.target.value)}
                  max={new Date().toISOString().split('T')[0]}
                  required
                />
                {formData.date_of_birth && (
                  <div className="text-sm text-gray-600 mt-1">
                    {t('users.dependants.age')}: {calculateAge(formData.date_of_birth)} {t('users.dependants.years')}
                  </div>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('users.dependants.gradeLevel')}
                </label>
                <select
                  value={formData.grade_level}
                  onChange={(e) => handleInputChange('grade_level', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-red"
                >
                  <option value="">{t('common.select')}</option>
                  <option value="Preschool">Preschool</option>
                  <option value="Kindergarten">Kindergarten</option>
                  {[...Array(12)].map((_, i) => (
                    <option key={i + 1} value={`Grade ${i + 1}`}>
                      Grade {i + 1}
                    </option>
                  ))}
                  <option value="College">College/CEGEP</option>
                  <option value="University">University</option>
                  <option value="Adult">Adult Education</option>
                </select>
              </div>
            </div>

            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('users.dependants.school')}
              </label>
              <Input
                type="text"
                value={formData.school_name}
                onChange={(e) => handleInputChange('school_name', e.target.value)}
                placeholder={t('users.dependants.schoolPlaceholder')}
              />
            </div>
          </div>

          {/* Special Needs */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {t('users.dependants.specialNeeds')}
            </h3>
            <textarea
              value={formData.special_needs}
              onChange={(e) => handleInputChange('special_needs', e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-red"
              placeholder={t('users.dependants.specialNeedsPlaceholder')}
            />
          </div>

          {/* Medical Information */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {t('users.dependants.medicalInfo')}
            </h3>
            <textarea
              value={formData.medical_info}
              onChange={(e) => handleInputChange('medical_info', e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-red"
              placeholder={t('users.dependants.medicalInfoPlaceholder')}
            />
          </div>

          {/* Additional Notes */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {t('users.dependants.notes')}
            </h3>
            <textarea
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-red"
              placeholder={t('users.dependants.notesPlaceholder')}
            />
          </div>
        </div>

        {/* Actions */}
        <div className="mt-8 flex justify-end space-x-3">
          <Button type="button" variant="secondary" onClick={onClose}>
            {t('common.cancel')}
          </Button>
          <Button 
            type="submit" 
            variant="primary" 
            loading={loading}
            disabled={!selectedClient && !defaultClientId}
          >
            {t('users.dependants.create')}
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default AddDependantModal;