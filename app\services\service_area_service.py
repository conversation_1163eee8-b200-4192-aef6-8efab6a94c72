"""
Service Area Management Service for managing tutor service areas and coverage zones.

This service handles tutor service area definitions including postal codes,
radius-based coverage, and service area validation and optimization.
"""

from typing import List, Optional, Dict, Any, Set
from dataclasses import dataclass
import logging
from datetime import datetime

from app.database.repositories.tutor_repository import TutorRepository
from app.services.geocoding_service import GeocodingService
from app.core.exceptions import ValidationError, ResourceNotFoundError, BusinessLogicError

logger = logging.getLogger(__name__)

@dataclass
class ServiceArea:
    """Represents a tutor's service area configuration."""
    tutor_id: int
    center_postal_code: str
    radius_km: float
    covered_postal_codes: List[str]
    service_types: List[str]  # 'online', 'in_person', 'library', 'hybrid'
    is_active: bool = True
    auto_expand: bool = False  # Automatically expand based on demand
    max_travel_time_minutes: Optional[int] = None
    
@dataclass
class ServiceAreaAnalysis:
    """Analysis results for a service area."""
    total_postal_codes: int
    estimated_population: int
    competition_level: str  # 'low', 'medium', 'high'
    demand_score: float
    coverage_gaps: List[str]
    optimization_suggestions: List[str]

class ServiceAreaService:
    """Service for managing tutor service areas and coverage optimization."""
    
    def __init__(self, tutor_repository: TutorRepository, geocoding_service: GeocodingService):
        self.tutor_repository = tutor_repository
        self.geocoding_service = geocoding_service
        
    async def create_service_area(
        self,
        tutor_id: int,
        center_postal_code: str,
        radius_km: float,
        service_types: List[str]
    ) -> ServiceArea:
        """
        Create a new service area for a tutor.
        
        Args:
            tutor_id: Tutor ID
            center_postal_code: Center postal code for the service area
            radius_km: Radius in kilometers
            service_types: Types of services offered
            
        Returns:
            Created service area
            
        Raises:
            ValidationError: If parameters are invalid
            BusinessLogicError: If service area overlaps excessively
        """
        try:
            # Validate tutor exists
            from app.config.database import get_db_connection
            async with get_db_connection() as conn:
                tutor = await self.tutor_repository.find_by_id(conn, tutor_id)
                if not tutor:
                    raise ResourceNotFoundError(f"Tutor not found: {tutor_id}")
            
            # Validate postal code
            center_coords = await self.geocoding_service.geocode_postal_code(center_postal_code)
            if not center_coords:
                raise ValidationError(f"Invalid postal code: {center_postal_code}")
            
            # Validate parameters
            if radius_km <= 0 or radius_km > 100:
                raise ValidationError("Radius must be between 1 and 100 kilometers")
            
            valid_service_types = {'online', 'in_person', 'library', 'hybrid'}
            if not service_types or not all(st in valid_service_types for st in service_types):
                raise ValidationError(f"Service types must be from: {valid_service_types}")
            
            # Find postal codes within radius
            covered_postal_codes = await self._find_postal_codes_in_radius(
                center_coords['latitude'],
                center_coords['longitude'],
                radius_km
            )
            
            logger.info(f"Found {len(covered_postal_codes)} postal codes within {radius_km}km of {center_postal_code}")
            
            # Create service area
            service_area = ServiceArea(
                tutor_id=tutor_id,
                center_postal_code=center_postal_code.upper(),
                radius_km=radius_km,
                covered_postal_codes=covered_postal_codes,
                service_types=service_types,
                is_active=True
            )
            
            # Store in database
            await self._save_service_area(service_area)
            
            return service_area
            
        except Exception as e:
            logger.error(f"Error creating service area: {str(e)}")
            raise
    
    async def update_service_area(
        self,
        tutor_id: int,
        center_postal_code: Optional[str] = None,
        radius_km: Optional[float] = None,
        service_types: Optional[List[str]] = None,
        is_active: Optional[bool] = None
    ) -> ServiceArea:
        """
        Update an existing service area.
        
        Args:
            tutor_id: Tutor ID
            center_postal_code: New center postal code (optional)
            radius_km: New radius (optional)
            service_types: New service types (optional)
            is_active: Active status (optional)
            
        Returns:
            Updated service area
        """
        try:
            # Get current service area
            current_area = await self.get_service_area(tutor_id)
            if not current_area:
                raise ResourceNotFoundError(f"No service area found for tutor {tutor_id}")
            
            # Update fields
            if center_postal_code:
                coords = await self.geocoding_service.geocode_postal_code(center_postal_code)
                if not coords:
                    raise ValidationError(f"Invalid postal code: {center_postal_code}")
                current_area.center_postal_code = center_postal_code.upper()
            
            if radius_km is not None:
                if radius_km <= 0 or radius_km > 100:
                    raise ValidationError("Radius must be between 1 and 100 kilometers")
                current_area.radius_km = radius_km
            
            if service_types is not None:
                valid_service_types = {'online', 'in_person', 'library', 'hybrid'}
                if not all(st in valid_service_types for st in service_types):
                    raise ValidationError(f"Service types must be from: {valid_service_types}")
                current_area.service_types = service_types
            
            if is_active is not None:
                current_area.is_active = is_active
            
            # Recalculate covered postal codes if center or radius changed
            if center_postal_code or radius_km is not None:
                center_coords = await self.geocoding_service.geocode_postal_code(current_area.center_postal_code)
                current_area.covered_postal_codes = await self._find_postal_codes_in_radius(
                    center_coords['latitude'],
                    center_coords['longitude'],
                    current_area.radius_km
                )
            
            # Save updated area
            await self._save_service_area(current_area)
            
            return current_area
            
        except Exception as e:
            logger.error(f"Error updating service area: {str(e)}")
            raise
    
    async def get_service_area(self, tutor_id: int) -> Optional[ServiceArea]:
        """
        Get service area for a tutor.
        
        Args:
            tutor_id: Tutor ID
            
        Returns:
            Service area if found, None otherwise
        """
        try:
            from app.config.database import get_db_connection
            async with get_db_connection() as conn:
                query = """
                    SELECT 
                        tutor_id,
                        center_postal_code,
                        radius_km,
                        covered_postal_codes,
                        service_types,
                        is_active,
                        auto_expand,
                        max_travel_time_minutes,
                        created_at,
                        updated_at
                    FROM tutor_service_areas
                    WHERE tutor_id = $1
                """
                
                result = await conn.fetchrow(query, tutor_id)
                if not result:
                    return None
                
                return ServiceArea(
                    tutor_id=result['tutor_id'],
                    center_postal_code=result['center_postal_code'],
                    radius_km=result['radius_km'],
                    covered_postal_codes=result['covered_postal_codes'],
                    service_types=result['service_types'],
                    is_active=result['is_active'],
                    auto_expand=result['auto_expand'],
                    max_travel_time_minutes=result['max_travel_time_minutes']
                )
                
        except Exception as e:
            logger.error(f"Error getting service area: {str(e)}")
            raise
    
    async def check_postal_code_coverage(self, postal_code: str) -> List[Dict[str, Any]]:
        """
        Check which tutors cover a specific postal code.
        
        Args:
            postal_code: Postal code to check
            
        Returns:
            List of tutors covering the postal code
        """
        try:
            from app.config.database import get_db_connection
            async with get_db_connection() as conn:
                query = """
                    SELECT 
                        tsa.tutor_id,
                        tsa.center_postal_code,
                        tsa.radius_km,
                        tsa.service_types,
                        tp.first_name,
                        tp.last_name,
                        tp.hourly_rate,
                        tp.specializations as specialties
                    FROM tutor_service_areas tsa
                    JOIN tutor_profiles tp ON tsa.tutor_id = tp.tutor_id
                    WHERE tsa.is_active = true
                    AND $1 = ANY(tsa.covered_postal_codes)
                    ORDER BY tsa.radius_km ASC
                """
                
                results = await conn.fetch(query, postal_code.upper())
                return [dict(record) for record in results]
                
        except Exception as e:
            logger.error(f"Error checking postal code coverage: {str(e)}")
            raise
    
    async def analyze_service_area(self, tutor_id: int) -> ServiceAreaAnalysis:
        """
        Analyze a tutor's service area for optimization opportunities.
        
        Args:
            tutor_id: Tutor ID
            
        Returns:
            Service area analysis
        """
        try:
            service_area = await self.get_service_area(tutor_id)
            if not service_area:
                raise ResourceNotFoundError(f"No service area found for tutor {tutor_id}")
            
            # Calculate basic metrics
            total_postal_codes = len(service_area.covered_postal_codes)
            
            # Estimate population (simplified - could integrate with census data)
            estimated_population = total_postal_codes * 2000  # Rough estimate
            
            # Analyze competition
            competition_counts = []
            for postal_code in service_area.covered_postal_codes[:10]:  # Sample for performance
                competitors = await self.check_postal_code_coverage(postal_code)
                competition_counts.append(len(competitors))
            
            avg_competition = sum(competition_counts) / len(competition_counts) if competition_counts else 0
            if avg_competition < 2:
                competition_level = 'low'
            elif avg_competition < 4:
                competition_level = 'medium'
            else:
                competition_level = 'high'
            
            # Generate suggestions
            suggestions = []
            if total_postal_codes < 20:
                suggestions.append("Consider expanding your service area radius")
            if competition_level == 'high':
                suggestions.append("High competition area - consider specializing in niche subjects")
            if 'online' not in service_area.service_types:
                suggestions.append("Adding online services could expand your reach significantly")
            
            return ServiceAreaAnalysis(
                total_postal_codes=total_postal_codes,
                estimated_population=estimated_population,
                competition_level=competition_level,
                demand_score=75.0,  # Placeholder - could integrate demand analytics
                coverage_gaps=[],  # Placeholder - could analyze uncovered areas
                optimization_suggestions=suggestions
            )
            
        except Exception as e:
            logger.error(f"Error analyzing service area: {str(e)}")
            raise
    
    async def optimize_service_area(self, tutor_id: int) -> ServiceArea:
        """
        Automatically optimize a tutor's service area based on analytics.
        
        Args:
            tutor_id: Tutor ID
            
        Returns:
            Optimized service area
        """
        try:
            analysis = await self.analyze_service_area(tutor_id)
            service_area = await self.get_service_area(tutor_id)
            
            # Apply optimizations
            optimized = False
            
            # Expand radius if too small
            if analysis.total_postal_codes < 15 and service_area.radius_km < 25:
                new_radius = min(service_area.radius_km + 5, 25)
                await self.update_service_area(tutor_id, radius_km=new_radius)
                optimized = True
                logger.info(f"Expanded service area radius to {new_radius}km for tutor {tutor_id}")
            
            # Add online services if not present and competition is high
            if analysis.competition_level == 'high' and 'online' not in service_area.service_types:
                new_service_types = service_area.service_types + ['online']
                await self.update_service_area(tutor_id, service_types=new_service_types)
                optimized = True
                logger.info(f"Added online services for tutor {tutor_id} due to high competition")
            
            if optimized:
                return await self.get_service_area(tutor_id)
            else:
                return service_area
                
        except Exception as e:
            logger.error(f"Error optimizing service area: {str(e)}")
            raise
    
    async def _find_postal_codes_in_radius(
        self,
        center_lat: float,
        center_lng: float,
        radius_km: float
    ) -> List[str]:
        """Find all postal codes within radius of center coordinates."""
        try:
            from app.config.database import get_db_connection
            async with get_db_connection() as conn:
                # Query Quebec postal codes within radius
                query = """
                    SELECT postal_code
                    FROM quebec_postal_codes
                    WHERE (6371 * acos(
                        cos(radians($1)) * cos(radians(latitude)) *
                        cos(radians(longitude) - radians($2)) +
                        sin(radians($1)) * sin(radians(latitude))
                    )) <= $3
                    ORDER BY postal_code
                """
                
                results = await conn.fetch(query, center_lat, center_lng, radius_km)
                return [row['postal_code'] for row in results]
                
        except Exception as e:
            logger.error(f"Error finding postal codes in radius: {str(e)}")
            # Fallback: return center postal code only
            center_postal = await self.geocoding_service.get_postal_code_from_coordinates(center_lat, center_lng)
            return [center_postal] if center_postal else []
    
    async def _save_service_area(self, service_area: ServiceArea) -> None:
        """Save service area to database."""
        try:
            from app.config.database import get_db_connection
            async with get_db_connection() as conn:
                query = """
                    INSERT INTO tutor_service_areas 
                    (tutor_id, center_postal_code, radius_km, covered_postal_codes, 
                     service_types, is_active, auto_expand, max_travel_time_minutes, 
                     created_at, updated_at)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                    ON CONFLICT (tutor_id)
                    DO UPDATE SET
                        center_postal_code = $2,
                        radius_km = $3,
                        covered_postal_codes = $4,
                        service_types = $5,
                        is_active = $6,
                        auto_expand = $7,
                        max_travel_time_minutes = $8,
                        updated_at = $10
                """
                
                now = datetime.now()
                await conn.execute(
                    query,
                    service_area.tutor_id,
                    service_area.center_postal_code,
                    service_area.radius_km,
                    service_area.covered_postal_codes,
                    service_area.service_types,
                    service_area.is_active,
                    service_area.auto_expand,
                    service_area.max_travel_time_minutes,
                    now,
                    now
                )
                
        except Exception as e:
            logger.error(f"Error saving service area: {str(e)}")
            raise

# Dependency injection helper
async def get_service_area_service() -> ServiceAreaService:
    """Get service area service instance with dependencies."""
    from app.database.repositories.tutor_repository import get_tutor_repository
    from app.services.geocoding_service import get_geocoding_service
    
    tutor_repo = await get_tutor_repository()
    geocoding_service = await get_geocoding_service()
    
    return ServiceAreaService(tutor_repo, geocoding_service)