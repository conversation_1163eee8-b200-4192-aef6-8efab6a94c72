import React from 'react';
import clsx from 'clsx';

interface MasterDetailLayoutProps {
  master: React.ReactNode;
  detail: React.ReactNode;
  className?: string;
  masterWidth?: 'narrow' | 'medium' | 'wide';
  showDetail?: boolean;
}

const masterWidthClasses = {
  narrow: 'lg:w-80',
  medium: 'lg:w-96',
  wide: 'lg:w-1/2',
};

export const MasterDetailLayout: React.FC<MasterDetailLayoutProps> = ({
  master,
  detail,
  className,
  masterWidth = 'medium',
  showDetail = true,
}) => {
  return (
    <div className={clsx('flex flex-col lg:flex-row h-full', className)}>
      {/* Master panel */}
      <div
        className={clsx(
          'flex-shrink-0 bg-white border-r border-primary-200 overflow-y-auto',
          masterWidthClasses[masterWidth],
          showDetail ? 'hidden lg:block' : 'w-full'
        )}
      >
        {master}
      </div>

      {/* Detail panel */}
      {showDetail && (
        <div className="flex-1 bg-background-secondary overflow-y-auto">
          {detail}
        </div>
      )}
    </div>
  );
};