"""
Dependant management API endpoints.
"""

from typing import List, Optional
from datetime import date

from fastapi import APIRouter, Depends, HTTPException, Query, status

from app.models.client_models import Dependant
from app.models.auth_models import TokenData, UserRole
from app.services.client_service import ClientService
from app.core.dependencies import (
    get_db_manager,
    get_current_user,
    check_rate_limit
)
from app.core.exceptions import (
    ResourceNotFoundError,
    ForbiddenError,
    ValidationError,
    BusinessLogicError
)
from app.config.database import DatabaseManager


router = APIRouter(prefix="/clients", tags=["dependants"])


@router.post(
    "/{client_id}/dependants",
    response_model=dict,
    status_code=status.HTTP_201_CREATED,
    summary="Create dependant",
    description="Create a new dependant for a client"
)
async def create_dependant(
    client_id: int,
    dependant_data: dict,
    secondary_client_id: Optional[int] = None,
    current_user: TokenData = Depends(get_current_user),
    db_manager: DatabaseManager = Depends(get_db_manager),
    _: None = Depends(check_rate_limit)
):
    """Create a new dependant."""
    service = ClientService(db_manager)
    
    try:
        # Check authorization - only the client themselves or managers can create dependants
        if (current_user.role != UserRole.MANAGER and 
            not await service._is_client_authorized(int(current_user.user_id), client_id)):
            raise ForbiddenError("You can only create dependants for your own account")
        
        # Validate secondary client if provided
        if secondary_client_id:
            if secondary_client_id == client_id:
                raise ValidationError("Primary and secondary clients must be different")
            
            # Verify secondary client exists and user has access
            if (current_user.role != UserRole.MANAGER and
                not await service._is_client_authorized(int(current_user.user_id), secondary_client_id)):
                raise ForbiddenError("You don't have access to the specified secondary client")
        
        dependant = await service.create_dependant(
            client_id=client_id,
            dependant_data=dependant_data,
            secondary_client_id=secondary_client_id,
            current_user_id=int(current_user.user_id)
        )
        return dependant
    except (ValidationError, BusinessLogicError) as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except ForbiddenError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except ResourceNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )


@router.get(
    "/{client_id}/dependants",
    response_model=List[dict],
    summary="Get client dependants",
    description="Get all dependants for a client"
)
async def get_client_dependants(
    client_id: int,
    current_user: TokenData = Depends(get_current_user),
    db_manager: DatabaseManager = Depends(get_db_manager)
):
    """Get client dependants."""
    service = ClientService(db_manager)
    
    try:
        # Check authorization
        if (current_user.role != UserRole.MANAGER and 
            not await service._is_client_authorized(int(current_user.user_id), client_id)):
            raise ForbiddenError("You can only view your own dependants")
        
        dependants = await service.get_client_dependants(client_id)
        return dependants
    except ForbiddenError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except ResourceNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )


@router.get(
    "/{client_id}/dependants/{dependant_id}",
    response_model=dict,
    summary="Get dependant details",
    description="Get details of a specific dependant"
)
async def get_dependant(
    client_id: int,
    dependant_id: int,
    current_user: TokenData = Depends(get_current_user),
    db_manager: DatabaseManager = Depends(get_db_manager)
):
    """Get dependant details."""
    service = ClientService(db_manager)
    
    try:
        # Check authorization
        if (current_user.role != UserRole.MANAGER and 
            not await service._is_client_authorized(int(current_user.user_id), client_id)):
            raise ForbiddenError("You can only view your own dependants")
        
        dependant = await service.get_dependant(dependant_id, client_id)
        return dependant
    except ForbiddenError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except ResourceNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )


@router.put(
    "/{client_id}/dependants/{dependant_id}",
    response_model=dict,
    summary="Update dependant",
    description="Update dependant information"
)
async def update_dependant(
    client_id: int,
    dependant_id: int,
    update_data: dict,
    current_user: TokenData = Depends(get_current_user),
    db_manager: DatabaseManager = Depends(get_db_manager),
    _: None = Depends(check_rate_limit)
):
    """Update dependant information."""
    service = ClientService(db_manager)
    
    try:
        # Check authorization
        if (current_user.role != UserRole.MANAGER and 
            not await service._is_client_authorized(int(current_user.user_id), client_id)):
            raise ForbiddenError("You can only update your own dependants")
        
        dependant = await service.update_dependant(
            dependant_id=dependant_id,
            client_id=client_id,
            update_data=update_data,
            current_user_id=int(current_user.user_id)
        )
        return dependant
    except (ValidationError, BusinessLogicError) as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except ForbiddenError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except ResourceNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )


@router.delete(
    "/{client_id}/dependants/{dependant_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete dependant",
    description="Soft delete a dependant (managers only)"
)
async def delete_dependant(
    client_id: int,
    dependant_id: int,
    current_user: TokenData = Depends(get_current_user),
    db_manager: DatabaseManager = Depends(get_db_manager)
):
    """Delete a dependant."""
    if current_user.role != UserRole.MANAGER:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only managers can delete dependants"
        )
    
    service = ClientService(db_manager)
    
    try:
        await service.delete_dependant(
            dependant_id=dependant_id,
            client_id=client_id,
            current_user_id=int(current_user.user_id)
        )
    except ResourceNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )


@router.get(
    "/dependants/search",
    response_model=List[dict],
    summary="Search dependants",
    description="Search dependants across all clients (managers only)"
)
async def search_dependants(
    q: str = Query(..., min_length=2, description="Search query"),
    limit: int = Query(10, ge=1, le=50, description="Maximum results"),
    current_user: TokenData = Depends(get_current_user),
    db_manager: DatabaseManager = Depends(get_db_manager),
    _: None = Depends(check_rate_limit)
):
    """Search dependants."""
    if current_user.role != UserRole.MANAGER:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only managers can search dependants"
        )
    
    service = ClientService(db_manager)
    
    try:
        dependants = await service.search_dependants(
            query=q,
            limit=limit
        )
        return dependants
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Search failed"
        )


@router.post(
    "/{client_id}/dependants/{dependant_id}/learning-needs",
    response_model=dict,
    status_code=status.HTTP_201_CREATED,
    summary="Create learning needs",
    description="Create learning needs assessment for a dependant"
)
async def create_dependant_learning_needs(
    client_id: int,
    dependant_id: int,
    needs_data: dict,
    current_user: TokenData = Depends(get_current_user),
    db_manager: DatabaseManager = Depends(get_db_manager),
    _: None = Depends(check_rate_limit)
):
    """Create learning needs for a dependant."""
    service = ClientService(db_manager)
    
    try:
        # Check authorization
        if (current_user.role != UserRole.MANAGER and 
            not await service._is_client_authorized(int(current_user.user_id), client_id)):
            raise ForbiddenError("You can only create learning needs for your own dependants")
        
        needs = await service.create_learning_needs(
            client_id=client_id,
            dependant_id=dependant_id,
            needs_data=needs_data,
            current_user_id=int(current_user.user_id)
        )
        return needs
    except (ValidationError, BusinessLogicError) as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except ForbiddenError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except ResourceNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )


@router.get(
    "/{client_id}/dependants/{dependant_id}/learning-needs",
    response_model=List[dict],
    summary="Get dependant learning needs",
    description="Get learning needs for a dependant"
)
async def get_dependant_learning_needs(
    client_id: int,
    dependant_id: int,
    current_user: TokenData = Depends(get_current_user),
    db_manager: DatabaseManager = Depends(get_db_manager)
):
    """Get learning needs for a dependant."""
    service = ClientService(db_manager)
    
    try:
        # Check authorization
        if (current_user.role != UserRole.MANAGER and 
            not await service._is_client_authorized(int(current_user.user_id), client_id)):
            raise ForbiddenError("You can only view learning needs for your own dependants")
        
        needs = await service.get_learning_needs(
            client_id=client_id,
            dependant_id=dependant_id
        )
        return needs
    except ForbiddenError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except ResourceNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )