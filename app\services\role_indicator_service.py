"""
Role indicator service for adding visual role indicators to messages.
"""

import logging
from typing import Dict, Any, Optional, List
from enum import Enum

from app.models.user_models import UserRole
from app.database.repositories.user_repository import UserRepository
from app.core.exceptions import ValidationError

logger = logging.getLogger(__name__)


class MessageContext(Enum):
    """Context where role indicators are used."""
    SMS = "sms"
    EMAIL = "email"
    WEBSOCKET = "websocket"
    PUSH_NOTIFICATION = "push_notification"


class RoleIndicatorService:
    """
    Service for managing role indicators in messages.
    
    Features:
    - Role-specific emoji indicators
    - Context-aware formatting
    - Multi-language support
    - Custom indicator configuration
    - Message formatting with roles
    """
    
    def __init__(self):
        self.user_repo = UserRepository()
        
        # Role indicators with emojis
        self.role_indicators = {
            UserRole.TUTOR: {
                'emoji': '👨‍🏫',
                'label_en': 'TUTOR',
                'label_fr': 'TUTEUR',
                'color': '#4F46E5',  # Indigo
                'priority': 1
            },
            UserRole.CLIENT: {
                'emoji': '👤',
                'label_en': 'CLIENT',
                'label_fr': 'CLIENT',
                'color': '#059669',  # Emerald
                'priority': 2
            },
            UserRole.MANAGER: {
                'emoji': '👔',
                'label_en': 'MANAGER',
                'label_fr': 'GESTIONNAIRE',
                'color': '#DC2626',  # Red
                'priority': 0
            }
        }
        
        # Context-specific formatting
        self.context_formats = {
            MessageContext.SMS: {
                'format': '{emoji} {label}',
                'separator': ' - ',
                'max_length': 20
            },
            MessageContext.EMAIL: {
                'format': '{emoji} {label}',
                'separator': ' | ',
                'max_length': None
            },
            MessageContext.WEBSOCKET: {
                'format': '{emoji} {label}',
                'separator': ' • ',
                'max_length': None
            },
            MessageContext.PUSH_NOTIFICATION: {
                'format': '{emoji} {label}',
                'separator': ' - ',
                'max_length': 15
            }
        }
    
    async def add_role_indicator(
        self,
        message: str,
        user_id: int,
        context: MessageContext = MessageContext.SMS,
        language: str = 'en',
        include_name: bool = False
    ) -> str:
        """
        Add role indicator to a message.
        
        Args:
            message: Original message content
            user_id: User ID to get role for
            context: Message context (SMS, email, etc.)
            language: Language for indicator (en/fr)
            include_name: Whether to include user name
            
        Returns:
            Message with role indicator prepended
        """
        try:
            # Get user info
            user = await self.user_repo.get_user(user_id)
            if not user:
                logger.warning(f"User {user_id} not found for role indicator")
                return message
            
            # Get role indicator
            indicator = self._get_role_indicator(
                roles=user.roles,
                context=context,
                language=language,
                user_name=f"{user.first_name} {user.last_name}" if include_name else None
            )
            
            if not indicator:
                return message
            
            # Format message with indicator
            context_format = self.context_formats[context]
            separator = context_format['separator']
            
            formatted_message = f"{indicator}{separator}{message}"
            
            # Trim if max length specified
            max_length = context_format.get('max_length')
            if max_length and len(formatted_message) > max_length:
                # Truncate message part, keep indicator
                available_length = max_length - len(indicator) - len(separator) - 3  # "..."
                if available_length > 0:
                    truncated_message = message[:available_length] + "..."
                    formatted_message = f"{indicator}{separator}{truncated_message}"
                else:
                    formatted_message = formatted_message[:max_length]
            
            return formatted_message
            
        except Exception as e:
            logger.error(f"Error adding role indicator: {e}")
            return message  # Return original message on error
    
    async def add_role_indicator_to_conversation(
        self,
        messages: List[Dict[str, Any]],
        context: MessageContext = MessageContext.SMS,
        language: str = 'en'
    ) -> List[Dict[str, Any]]:
        """
        Add role indicators to a list of messages.
        
        Args:
            messages: List of message dictionaries
            context: Message context
            language: Language for indicators
            
        Returns:
            Messages with role indicators added
        """
        try:
            # Cache user info to avoid repeated queries
            user_cache = {}
            
            for message in messages:
                user_id = message.get('user_id') or message.get('sender_id')
                
                if not user_id:
                    continue
                
                # Get user from cache or database
                if user_id not in user_cache:
                    user = await self.user_repo.get_user(user_id)
                    user_cache[user_id] = user
                else:
                    user = user_cache[user_id]
                
                if not user:
                    continue
                
                # Get role indicator
                indicator = self._get_role_indicator(
                    roles=user.roles,
                    context=context,
                    language=language
                )
                
                if indicator:
                    # Add indicator to message
                    message['role_indicator'] = indicator
                    message['sender_role'] = self._get_primary_role(user.roles)
                    
                    # Add to content if not already present
                    content = message.get('content') or message.get('message', '')
                    if content and not content.startswith(indicator):
                        separator = self.context_formats[context]['separator']
                        message['formatted_content'] = f"{indicator}{separator}{content}"
            
            return messages
            
        except Exception as e:
            logger.error(f"Error adding role indicators to conversation: {e}")
            return messages
    
    def get_role_indicator_config(
        self,
        role: UserRole,
        language: str = 'en'
    ) -> Dict[str, Any]:
        """
        Get role indicator configuration.
        
        Args:
            role: User role
            language: Language for label
            
        Returns:
            Role indicator configuration
        """
        try:
            config = self.role_indicators.get(role)
            if not config:
                return {}
            
            label_key = f'label_{language}'
            label = config.get(label_key, config.get('label_en', ''))
            
            return {
                'emoji': config['emoji'],
                'label': label,
                'color': config['color'],
                'priority': config['priority'],
                'full_indicator': f"{config['emoji']} {label}"
            }
            
        except Exception as e:
            logger.error(f"Error getting role indicator config: {e}")
            return {}
    
    def format_notification_with_role(
        self,
        title: str,
        message: str,
        sender_roles: List[UserRole],
        language: str = 'en'
    ) -> Dict[str, str]:
        """
        Format notification title and message with role indicator.
        
        Args:
            title: Notification title
            message: Notification message
            sender_roles: Sender's roles
            language: Language for formatting
            
        Returns:
            Formatted title and message
        """
        try:
            # Get role indicator
            indicator = self._get_role_indicator(
                roles=sender_roles,
                context=MessageContext.PUSH_NOTIFICATION,
                language=language
            )
            
            if not indicator:
                return {'title': title, 'message': message}
            
            # Format title with role
            formatted_title = f"{indicator} {title}"
            
            return {
                'title': formatted_title,
                'message': message
            }
            
        except Exception as e:
            logger.error(f"Error formatting notification with role: {e}")
            return {'title': title, 'message': message}
    
    def format_email_sender(
        self,
        sender_name: str,
        sender_email: str,
        sender_roles: List[UserRole],
        language: str = 'en'
    ) -> str:
        """
        Format email sender with role indicator.
        
        Args:
            sender_name: Sender's name
            sender_email: Sender's email
            sender_roles: Sender's roles
            language: Language for formatting
            
        Returns:
            Formatted sender string
        """
        try:
            # Get role indicator
            indicator = self._get_role_indicator(
                roles=sender_roles,
                context=MessageContext.EMAIL,
                language=language
            )
            
            if not indicator:
                return f"{sender_name} <{sender_email}>"
            
            # Format with role
            return f"{indicator} {sender_name} <{sender_email}>"
            
        except Exception as e:
            logger.error(f"Error formatting email sender: {e}")
            return f"{sender_name} <{sender_email}>"
    
    def _get_role_indicator(
        self,
        roles: List[UserRole],
        context: MessageContext,
        language: str = 'en',
        user_name: Optional[str] = None
    ) -> Optional[str]:
        """Get formatted role indicator for a user."""
        try:
            if not roles:
                return None
            
            # Get primary role (highest priority)
            primary_role = self._get_primary_role(roles)
            if not primary_role:
                return None
            
            # Get role config
            config = self.role_indicators.get(primary_role)
            if not config:
                return None
            
            # Get label in requested language
            label_key = f'label_{language}'
            label = config.get(label_key, config.get('label_en', ''))
            
            # Format according to context
            context_format = self.context_formats[context]['format']
            
            indicator = context_format.format(
                emoji=config['emoji'],
                label=label,
                name=user_name or ''
            ).strip()
            
            return indicator
            
        except Exception as e:
            logger.error(f"Error getting role indicator: {e}")
            return None
    
    def _get_primary_role(self, roles: List[UserRole]) -> Optional[UserRole]:
        """Get primary role based on priority."""
        if not roles:
            return None
        
        # Sort by priority (lower number = higher priority)
        sorted_roles = sorted(
            roles,
            key=lambda role: self.role_indicators.get(role, {}).get('priority', 999)
        )
        
        return sorted_roles[0] if sorted_roles else None
    
    def update_role_indicator_config(
        self,
        role: UserRole,
        config_updates: Dict[str, Any]
    ) -> bool:
        """
        Update role indicator configuration.
        
        Args:
            role: Role to update
            config_updates: Configuration updates
            
        Returns:
            Success status
        """
        try:
            if role not in self.role_indicators:
                return False
            
            # Update configuration
            self.role_indicators[role].update(config_updates)
            
            logger.info(f"Updated role indicator config for {role}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error updating role indicator config: {e}")
            return False
    
    def get_all_role_indicators(
        self,
        language: str = 'en'
    ) -> Dict[str, Dict[str, Any]]:
        """
        Get all role indicator configurations.
        
        Args:
            language: Language for labels
            
        Returns:
            All role indicator configurations
        """
        try:
            result = {}
            
            for role, config in self.role_indicators.items():
                label_key = f'label_{language}'
                label = config.get(label_key, config.get('label_en', ''))
                
                result[role.value] = {
                    'emoji': config['emoji'],
                    'label': label,
                    'color': config['color'],
                    'priority': config['priority'],
                    'full_indicator': f"{config['emoji']} {label}"
                }
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting all role indicators: {e}")
            return {}
    
    def validate_message_with_indicator(
        self,
        message: str,
        max_length: int,
        indicator_length: int
    ) -> Dict[str, Any]:
        """
        Validate if message fits with role indicator.
        
        Args:
            message: Original message
            max_length: Maximum allowed length
            indicator_length: Length of role indicator
            
        Returns:
            Validation result
        """
        try:
            total_length = len(message) + indicator_length + 3  # separator
            
            result = {
                'valid': total_length <= max_length,
                'current_length': total_length,
                'max_length': max_length,
                'available_length': max_length - indicator_length - 3,
                'needs_truncation': total_length > max_length
            }
            
            if result['needs_truncation']:
                result['truncated_length'] = max_length - indicator_length - 6  # "..." + separator
            
            return result
            
        except Exception as e:
            logger.error(f"Error validating message with indicator: {e}")
            return {'valid': False, 'error': str(e)}