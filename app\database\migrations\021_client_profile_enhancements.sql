-- Migration: Client Profile Enhancements
-- Description: Add comprehensive client profile management tables

-- Client Addresses Table
CREATE TABLE IF NOT EXISTS client_addresses (
    address_id SERIAL PRIMARY KEY,
    client_id INTEGER NOT NULL REFERENCES client_profiles(client_id) ON DELETE CASCADE,
    address_type VARCHAR(20) NOT NULL CHECK (address_type IN ('home', 'work', 'billing', 'other')),
    street VARCHAR(255) NOT NULL,
    city VARCHAR(100) NOT NULL,
    province VARCHAR(50) NOT NULL,
    postal_code VARCHAR(10) NOT NULL,
    country VARCHAR(50) DEFAULT 'Canada',
    is_primary BOOLEAN DEFAULT FALSE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    CONSTRAINT unique_primary_address_per_type UNIQUE (client_id, address_type, is_primary) WHERE is_primary = TRUE AND deleted_at IS NULL
);

-- Client Emergency Contacts Table
CREATE TABLE IF NOT EXISTS client_emergency_contacts (
    contact_id SERIAL PRIMARY KEY,
    client_id INTEGER NOT NULL REFERENCES client_profiles(client_id) ON DELETE CASCADE,
    contact_name VARCHAR(100) NOT NULL,
    relationship VARCHAR(50) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(255),
    is_primary BOOLEAN DEFAULT FALSE,
    can_pickup_dependant BOOLEAN DEFAULT FALSE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    CONSTRAINT unique_primary_emergency_contact UNIQUE (client_id, is_primary) WHERE is_primary = TRUE AND deleted_at IS NULL
);

-- Client Preferences Table
CREATE TABLE IF NOT EXISTS client_preferences (
    preference_id SERIAL PRIMARY KEY,
    client_id INTEGER NOT NULL REFERENCES client_profiles(client_id) ON DELETE CASCADE,
    preferred_language VARCHAR(5) DEFAULT 'en' CHECK (preferred_language IN ('en', 'fr')),
    timezone VARCHAR(50) DEFAULT 'America/Toronto',
    sms_enabled BOOLEAN DEFAULT TRUE,
    email_enabled BOOLEAN DEFAULT TRUE,
    push_enabled BOOLEAN DEFAULT TRUE,
    sms_reminder_hours INTEGER DEFAULT 24,
    email_reminder_hours INTEGER DEFAULT 24,
    preferred_contact_method VARCHAR(20) DEFAULT 'email' CHECK (preferred_contact_method IN ('email', 'sms', 'phone', 'in_app')),
    marketing_opt_in BOOLEAN DEFAULT FALSE,
    newsletter_opt_in BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT one_preference_per_client UNIQUE (client_id)
);

-- Client Learning Needs Table
CREATE TABLE IF NOT EXISTS client_learning_needs (
    learning_need_id SERIAL PRIMARY KEY,
    client_id INTEGER NOT NULL REFERENCES client_profiles(client_id) ON DELETE CASCADE,
    subject_areas TEXT[] DEFAULT '{}',
    learning_goals TEXT,
    special_accommodations TEXT,
    preferred_session_length INTEGER DEFAULT 60,
    preferred_session_frequency VARCHAR(20) DEFAULT 'weekly',
    preferred_tutoring_style VARCHAR(50),
    strengths TEXT,
    challenges TEXT,
    parent_involvement_level VARCHAR(20) DEFAULT 'moderate' CHECK (parent_involvement_level IN ('minimal', 'moderate', 'high')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT one_learning_need_per_client UNIQUE (client_id)
);

-- Add profile completeness to client_profiles
ALTER TABLE client_profiles 
ADD COLUMN IF NOT EXISTS profile_completeness INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_activity_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS internal_notes TEXT,
ADD COLUMN IF NOT EXISTS referral_source VARCHAR(100),
ADD COLUMN IF NOT EXISTS profile_photo_url VARCHAR(500);

-- Create indexes for better performance
CREATE INDEX idx_client_addresses_client_id ON client_addresses(client_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_client_addresses_type ON client_addresses(address_type) WHERE deleted_at IS NULL;
CREATE INDEX idx_client_emergency_contacts_client_id ON client_emergency_contacts(client_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_client_preferences_client_id ON client_preferences(client_id);
CREATE INDEX idx_client_learning_needs_client_id ON client_learning_needs(client_id);
CREATE INDEX idx_client_learning_needs_subjects ON client_learning_needs USING GIN(subject_areas);

-- Create update triggers
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_client_addresses_modtime BEFORE UPDATE ON client_addresses FOR EACH ROW EXECUTE FUNCTION update_modified_column();
CREATE TRIGGER update_client_emergency_contacts_modtime BEFORE UPDATE ON client_emergency_contacts FOR EACH ROW EXECUTE FUNCTION update_modified_column();
CREATE TRIGGER update_client_preferences_modtime BEFORE UPDATE ON client_preferences FOR EACH ROW EXECUTE FUNCTION update_modified_column();
CREATE TRIGGER update_client_learning_needs_modtime BEFORE UPDATE ON client_learning_needs FOR EACH ROW EXECUTE FUNCTION update_modified_column();

-- Function to calculate profile completeness
CREATE OR REPLACE FUNCTION calculate_client_profile_completeness(p_client_id INTEGER)
RETURNS INTEGER AS $$
DECLARE
    v_completeness INTEGER := 0;
    v_has_address BOOLEAN;
    v_has_emergency_contact BOOLEAN;
    v_has_preferences BOOLEAN;
    v_has_learning_needs BOOLEAN;
    v_has_phone BOOLEAN;
    v_has_photo BOOLEAN;
BEGIN
    -- Basic profile info (25%)
    SELECT 
        CASE WHEN phone IS NOT NULL AND phone != '' THEN TRUE ELSE FALSE END,
        CASE WHEN profile_photo_url IS NOT NULL AND profile_photo_url != '' THEN TRUE ELSE FALSE END
    INTO v_has_phone, v_has_photo
    FROM client_profiles 
    WHERE client_id = p_client_id;
    
    IF v_has_phone THEN v_completeness := v_completeness + 15; END IF;
    IF v_has_photo THEN v_completeness := v_completeness + 10; END IF;
    
    -- Address (25%)
    SELECT EXISTS(SELECT 1 FROM client_addresses WHERE client_id = p_client_id AND deleted_at IS NULL)
    INTO v_has_address;
    IF v_has_address THEN v_completeness := v_completeness + 25; END IF;
    
    -- Emergency contact (25%)
    SELECT EXISTS(SELECT 1 FROM client_emergency_contacts WHERE client_id = p_client_id AND deleted_at IS NULL)
    INTO v_has_emergency_contact;
    IF v_has_emergency_contact THEN v_completeness := v_completeness + 25; END IF;
    
    -- Preferences (15%)
    SELECT EXISTS(SELECT 1 FROM client_preferences WHERE client_id = p_client_id)
    INTO v_has_preferences;
    IF v_has_preferences THEN v_completeness := v_completeness + 15; END IF;
    
    -- Learning needs (10%)
    SELECT EXISTS(SELECT 1 FROM client_learning_needs WHERE client_id = p_client_id)
    INTO v_has_learning_needs;
    IF v_has_learning_needs THEN v_completeness := v_completeness + 10; END IF;
    
    RETURN v_completeness;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update profile completeness
CREATE OR REPLACE FUNCTION update_client_profile_completeness()
RETURNS TRIGGER AS $$
DECLARE
    v_client_id INTEGER;
BEGIN
    -- Get the client_id from the appropriate table
    IF TG_TABLE_NAME = 'client_profiles' THEN
        v_client_id := NEW.client_id;
    ELSE
        v_client_id := NEW.client_id;
    END IF;
    
    -- Update the profile completeness
    UPDATE client_profiles 
    SET profile_completeness = calculate_client_profile_completeness(v_client_id),
        updated_at = CURRENT_TIMESTAMP
    WHERE client_id = v_client_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for all related tables
CREATE TRIGGER update_completeness_on_address_change 
AFTER INSERT OR UPDATE OR DELETE ON client_addresses 
FOR EACH ROW EXECUTE FUNCTION update_client_profile_completeness();

CREATE TRIGGER update_completeness_on_emergency_contact_change 
AFTER INSERT OR UPDATE OR DELETE ON client_emergency_contacts 
FOR EACH ROW EXECUTE FUNCTION update_client_profile_completeness();

CREATE TRIGGER update_completeness_on_preferences_change 
AFTER INSERT OR UPDATE OR DELETE ON client_preferences 
FOR EACH ROW EXECUTE FUNCTION update_client_profile_completeness();

CREATE TRIGGER update_completeness_on_learning_needs_change 
AFTER INSERT OR UPDATE OR DELETE ON client_learning_needs 
FOR EACH ROW EXECUTE FUNCTION update_client_profile_completeness();