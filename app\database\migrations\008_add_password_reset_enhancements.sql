-- Add enhancements to password_reset_tokens table
-- Version: 008
-- Description: Adds IP address and user agent tracking to password_reset_tokens
-- Author: TutorAide Development Team
-- Date: 2025-01-10

-- Add new columns to password_reset_tokens table
ALTER TABLE password_reset_tokens
ADD COLUMN IF NOT EXISTS ip_address INET,
ADD COLUMN IF NOT EXISTS user_agent TEXT;

-- Add index for token lookup
CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_token 
ON password_reset_tokens(token) 
WHERE used_at IS NULL;

-- Add index for cleanup operations
CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_expires 
ON password_reset_tokens(expires_at) 
WHERE used_at IS NULL;

-- Comments for documentation
COMMENT ON COLUMN password_reset_tokens.ip_address IS 'IP address of the password reset request';
COMMENT ON COLUMN password_reset_tokens.user_agent IS 'User agent string of the password reset request';