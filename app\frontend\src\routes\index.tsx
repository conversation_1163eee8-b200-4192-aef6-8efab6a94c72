import React, { lazy, Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { UserRoleType } from '../types/auth';

// Lazy load pages
const Dashboard = lazy(() => import('../pages/Dashboard'));
const Login = lazy(() => import('../pages/Login'));
const OAuthCallback = lazy(() => import('../pages/auth/OAuthCallback'));
const Privacy = lazy(() => import('../pages/Privacy'));
const UsersLayout = lazy(() => import('../pages/users/UsersLayout'));
const ClientsPage = lazy(() => import('../pages/users/ClientsPage'));
const TutorsPage = lazy(() => import('../pages/users/TutorsPage'));
const DependantsPage = lazy(() => import('../pages/users/DependantsPage'));
const TutorsLayout = lazy(() => import('../pages/tutors/TutorsLayout'));
const TutorInvitations = lazy(() => import('../pages/tutors/TutorInvitations'));
const ApplicationsList = lazy(() => import('../pages/tutors/ApplicationsList'));
const ApplicationReview = lazy(() => import('../pages/tutors/ApplicationReview'));
const EnhancedApplicationReview = lazy(() => import('../pages/tutors/EnhancedApplicationReview'));
const AcceptInvitation = lazy(() => import('../pages/AcceptInvitation'));
const ResetPassword = lazy(() => import('../pages/auth/ResetPassword'));
const ForgotPassword = lazy(() => import('../pages/auth/ForgotPassword'));
const BillingLayout = lazy(() => import('../pages/billing/BillingLayout'));
const InvoicesPage = lazy(() => import('../pages/billing/InvoicesPage'));
const TutorPaymentsPage = lazy(() => import('../pages/billing/TutorPaymentsPage'));
const EnhancedInvoicesPage = lazy(() => import('../pages/billing/EnhancedInvoicesPage'));
const EnhancedTutorPaymentsPage = lazy(() => import('../pages/billing/EnhancedTutorPaymentsPage'));
const SubscriptionsPage = lazy(() => import('../pages/billing/SubscriptionsPage'));
const PackagesPage = lazy(() => import('../pages/billing/PackagesPage'));
const ReportsLayout = lazy(() => import('../pages/reports/ReportsLayout'));
const FinancialReport = lazy(() => import('../pages/reports/FinancialReport'));
const PerformanceReport = lazy(() => import('../pages/reports/PerformanceReport'));
const AnalyticsReport = lazy(() => import('../pages/reports/AnalyticsReport'));
const UsageReport = lazy(() => import('../pages/reports/UsageReport'));
const MonthlyReport = lazy(() => import('../pages/reports/MonthlyReport'));
const MessagesLayout = lazy(() => import('../pages/messages/MessagesLayout'));
const SMSThreads = lazy(() => import('../pages/messages/SMSThreads'));
const InAppChatUpdated = lazy(() => import('../pages/messages/InAppChatUpdated'));
const MessageTemplates = lazy(() => import('../pages/messages/MessageTemplates'));
const EnhancedMessageTemplates = lazy(() => import('../pages/messages/EnhancedMessageTemplates'));
const Broadcasts = lazy(() => import('../pages/messages/Broadcasts'));
const EnhancedBroadcasts = lazy(() => import('../pages/messages/EnhancedBroadcasts'));
const SettingsLayout = lazy(() => import('../pages/settings/SettingsLayout'));
const SystemSettings = lazy(() => import('../pages/settings/SystemSettings'));
const UserSettings = lazy(() => import('../pages/settings/UserSettings'));
const PaymentSettings = lazy(() => import('../pages/settings/PaymentSettings'));
const NotificationSettings = lazy(() => import('../pages/settings/NotificationSettings'));
const APISettings = lazy(() => import('../pages/settings/APISettings'));
const ServiceSettings = lazy(() => import('../pages/settings/ServiceSettings'));
const ServicePricingPage = lazy(() => import('../pages/settings/ServicePricingPage'));
const SecuritySettings = lazy(() => import('../pages/settings/SecuritySettings'));
const ConsentManagement = lazy(() => import('../pages/settings/ConsentManagement'));
const TwoFactorSetup = lazy(() => import('../pages/settings/TwoFactorSetup'));
const CalendarLayout = lazy(() => import('../pages/calendar/CalendarLayout'));
const CalendarDayView = lazy(() => import('../pages/calendar/CalendarDayView'));
const RecurringAppointments = lazy(() => import('../pages/calendar/RecurringAppointments'));
const TutorMapSimple = lazy(() => import('../pages/map/TutorMapSimple'));
const AppointmentComplete = lazy(() => import('../pages/appointments/AppointmentComplete'));
const PaymentHistoryPage = lazy(() => import('../pages/tutor/PaymentHistoryPage'));
const TutorProfile = lazy(() => import('../pages/tutor/Profile'));
const TutorDashboard = lazy(() => import('../pages/tutor/Dashboard'));
const TutorTimeOff = lazy(() => import('../pages/tutor/TimeOff'));
const ManagerDashboard = lazy(() => import('../pages/manager/ManagerDashboard'));
const ServicesPage = lazy(() => import('../pages/services/ServicesPage'));
const ClientProfile = lazy(() => import('../pages/client/Profile'));
const ClientDependants = lazy(() => import('../pages/client/DependantDashboard'));
const ClientInvoices = lazy(() => import('../pages/client/Invoices'));

// Loading component
const PageLoader = () => (
  <div className="flex items-center justify-center h-screen">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-accent-red"></div>
  </div>
);

// Protected route wrapper
interface ProtectedRouteProps {
  children: React.ReactNode;
  allowedRoles?: UserRoleType[];
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children, allowedRoles }) => {
  const { isAuthenticated, user, isLoading } = useAuth();

  if (isLoading) {
    return <PageLoader />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  if (allowedRoles && user && !allowedRoles.includes(user.activeRole)) {
    return <Navigate to="/unauthorized" replace />;
  }

  return <>{children}</>;
};

// Dashboard Router component that renders appropriate dashboard based on user role
const DashboardRouter: React.FC = () => {
  const { user } = useAuth();
  
  if (user?.activeRole === UserRoleType.MANAGER) {
    return <ManagerDashboard />;
  }
  
  return <Dashboard />;
};

// Invoice Router component that renders appropriate invoice page based on user role
const InvoiceRouter: React.FC = () => {
  const { user } = useAuth();
  
  if (user?.activeRole === UserRoleType.MANAGER) {
    return <EnhancedInvoicesPage />;
  }
  
  return <InvoicesPage />;
};

// Tutor Payment Router component that renders appropriate payment page based on user role
const TutorPaymentRouter: React.FC = () => {
  const { user } = useAuth();
  
  if (user?.activeRole === UserRoleType.MANAGER) {
    return <EnhancedTutorPaymentsPage />;
  }
  
  return <TutorPaymentsPage />;
};

// Message Templates Router component that renders appropriate templates page based on user role
const MessageTemplatesRouter: React.FC = () => {
  const { user } = useAuth();
  
  if (user?.activeRole === UserRoleType.MANAGER) {
    return <EnhancedMessageTemplates />;
  }
  
  return <MessageTemplates />;
};

// Broadcasts Router component that renders appropriate broadcasts page based on user role
const BroadcastsRouter: React.FC = () => {
  const { user } = useAuth();
  
  if (user?.activeRole === UserRoleType.MANAGER) {
    return <EnhancedBroadcasts />;
  }
  
  return <Broadcasts />;
};

export const AppRoutes: React.FC = () => {
  const { isLoading } = useAuth();

  if (isLoading) {
    return <PageLoader />;
  }

  return (
    <Suspense fallback={<PageLoader />}>
      <Routes>
        {/* Public routes */}
        <Route path="/login" element={<Login />} />
        <Route path="/auth/callback" element={<OAuthCallback />} />
        <Route path="/auth/forgot-password" element={<ForgotPassword />} />
        <Route path="/auth/reset-password" element={<ResetPassword />} />
        <Route path="/accept-invitation" element={<AcceptInvitation />} />
        <Route path="/privacy" element={<Privacy />} />

        {/* Protected routes */}
        <Route
          path="/"
          element={
            <ProtectedRoute>
              <Navigate to="/dashboard" replace />
            </ProtectedRoute>
          }
        />

        <Route
          path="/dashboard"
          element={
            <ProtectedRoute>
              <DashboardRouter />
            </ProtectedRoute>
          }
        />

        {/* Calendar routes */}
        <Route
          path="/calendar"
          element={
            <ProtectedRoute>
              <CalendarLayout />
            </ProtectedRoute>
          }
        >
          <Route index element={<Navigate to="day" replace />} />
          <Route path="day" element={<CalendarDayView selectedDate={new Date()} />} />
          <Route path="week" element={<div>Week View Coming Soon</div>} />
          <Route path="month" element={<div>Month View Coming Soon</div>} />
          <Route path="schedule" element={<div>Schedule View Coming Soon</div>} />
          <Route path="recurring" element={<RecurringAppointments />} />
        </Route>

        {/* Users routes */}
        <Route
          path="/users"
          element={
            <ProtectedRoute>
              <UsersLayout />
            </ProtectedRoute>
          }
        >
          <Route index element={<Navigate to="clients" replace />} />
          <Route path="clients" element={<ClientsPage />} />
          <Route path="tutors" element={<TutorsPage />} />
          <Route path="dependants" element={<DependantsPage />} />
        </Route>

        {/* Tutors management routes */}
        <Route
          path="/tutors"
          element={
            <ProtectedRoute allowedRoles={[UserRoleType.MANAGER]}>
              <TutorsLayout />
            </ProtectedRoute>
          }
        >
          <Route index element={<Navigate to="profiles" replace />} />
          <Route path="profiles" element={<TutorsPage />} />
          <Route path="invitations" element={<TutorInvitations />} />
          <Route path="applications" element={<ApplicationsList />} />
          <Route path="applications/:id" element={<EnhancedApplicationReview />} />
        </Route>

        {/* Billing routes */}
        <Route
          path="/billing"
          element={
            <ProtectedRoute allowedRoles={[UserRoleType.MANAGER, UserRoleType.CLIENT]}>
              <BillingLayout />
            </ProtectedRoute>
          }
        >
          <Route index element={<Navigate to="invoices" replace />} />
          <Route path="invoices" element={<InvoiceRouter />} />
          <Route
            path="tutor-payments"
            element={
              <ProtectedRoute allowedRoles={[UserRoleType.MANAGER]}>
                <TutorPaymentRouter />
              </ProtectedRoute>
            }
          />
          <Route path="subscriptions" element={<SubscriptionsPage />} />
          <Route path="packages" element={<PackagesPage />} />
        </Route>

        {/* Reports routes */}
        <Route
          path="/reports"
          element={
            <ProtectedRoute allowedRoles={[UserRoleType.MANAGER]}>
              <ReportsLayout />
            </ProtectedRoute>
          }
        >
          <Route index element={<Navigate to="financial" replace />} />
          <Route path="financial" element={<FinancialReport />} />
          <Route path="performance" element={<PerformanceReport />} />
          <Route path="analytics" element={<AnalyticsReport />} />
          <Route path="usage" element={<UsageReport />} />
          <Route path="monthly" element={<MonthlyReport />} />
        </Route>

        {/* Messages routes */}
        <Route
          path="/messages"
          element={
            <ProtectedRoute>
              <MessagesLayout />
            </ProtectedRoute>
          }
        >
          <Route index element={<Navigate to="sms" replace />} />
          <Route path="sms" element={<SMSThreads />} />
          <Route path="chat" element={<InAppChatUpdated />} />
          <Route path="templates" element={<MessageTemplatesRouter />} />
          <Route path="broadcasts" element={<BroadcastsRouter />} />
        </Route>

        {/* Services routes */}
        <Route path="/services" element={<ServicesPage />} />

        {/* Map routes */}
        <Route
          path="/map"
          element={
            <ProtectedRoute>
              <TutorMapSimple />
            </ProtectedRoute>
          }
        />

        {/* Appointment routes */}
        <Route
          path="/appointments/:appointmentId/complete"
          element={
            <ProtectedRoute allowedRoles={[UserRoleType.MANAGER, UserRoleType.TUTOR]}>
              <AppointmentComplete />
            </ProtectedRoute>
          }
        />

        {/* Tutor routes */}
        <Route
          path="/tutor/dashboard"
          element={
            <ProtectedRoute allowedRoles={[UserRoleType.TUTOR]}>
              <TutorDashboard />
            </ProtectedRoute>
          }
        />
        <Route
          path="/tutor/profile"
          element={
            <ProtectedRoute allowedRoles={[UserRoleType.TUTOR]}>
              <TutorProfile />
            </ProtectedRoute>
          }
        />
        <Route
          path="/tutor/time-off"
          element={
            <ProtectedRoute allowedRoles={[UserRoleType.TUTOR]}>
              <TutorTimeOff />
            </ProtectedRoute>
          }
        />
        <Route
          path="/tutor/payment-history"
          element={
            <ProtectedRoute allowedRoles={[UserRoleType.TUTOR]}>
              <PaymentHistoryPage />
            </ProtectedRoute>
          }
        />

        {/* Client routes */}
        <Route
          path="/client/profile"
          element={
            <ProtectedRoute allowedRoles={[UserRoleType.CLIENT]}>
              <ClientProfile />
            </ProtectedRoute>
          }
        />
        <Route
          path="/client/dependants"
          element={
            <ProtectedRoute allowedRoles={[UserRoleType.CLIENT]}>
              <ClientDependants />
            </ProtectedRoute>
          }
        />
        <Route
          path="/client/invoices"
          element={
            <ProtectedRoute allowedRoles={[UserRoleType.CLIENT]}>
              <ClientInvoices />
            </ProtectedRoute>
          }
        />

        {/* Settings routes */}
        <Route
          path="/settings"
          element={
            <ProtectedRoute allowedRoles={[UserRoleType.MANAGER]}>
              <SettingsLayout />
            </ProtectedRoute>
          }
        >
          <Route index element={<Navigate to="system" replace />} />
          <Route path="system" element={<SystemSettings />} />
          <Route path="users" element={<UserSettings />} />
          <Route path="security" element={<SecuritySettings />} />
          <Route path="payments" element={<PaymentSettings />} />
          <Route path="notifications" element={<NotificationSettings />} />
          <Route path="api" element={<APISettings />} />
          <Route path="services" element={<ServiceSettings />} />
          <Route path="pricing" element={<ServicePricingPage />} />
          <Route path="consent" element={<ConsentManagement />} />
          <Route path="two-factor" element={<TwoFactorSetup />} />
        </Route>

        {/* 404 */}
        <Route path="*" element={<Navigate to="/dashboard" replace />} />
      </Routes>
    </Suspense>
  );
};