#!/bin/bash
# Test password reset functionality on Railway production

echo "Testing Password Reset Functionality"
echo "==================================="

# Replace with your Railway production URL
API_URL="https://your-app.railway.app/api/v1"

# Test email - replace with a valid test email
TEST_EMAIL="<EMAIL>"

echo ""
echo "1. Requesting password reset for email: $TEST_EMAIL"
echo "---------------------------------------------------"

# Request password reset
curl -X POST "$API_URL/auth/password-reset/request" \
  -H "Content-Type: application/json" \
  -d "{\"email\": \"$TEST_EMAIL\"}" \
  -v

echo ""
echo ""
echo "2. Check your email for the reset token"
echo "--------------------------------------"
echo "Once you receive the email, you can test the reset confirmation with:"
echo ""
echo "curl -X POST \"$API_URL/auth/password-reset/confirm\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -d '{\"token\": \"YOUR_RESET_TOKEN\", \"new_password\": \"NewSecurePassword123!\"}'"
echo ""
echo "3. To check database state, run these queries in Railway:"
echo "--------------------------------------------------------"
echo "-- Check if auth_tokens table has the reset token:"
echo "SELECT token_id, user_id, token_type, expires_at, used_at, created_at"
echo "FROM auth_tokens"
echo "WHERE token_type = 'password_reset'"
echo "ORDER BY created_at DESC"
echo "LIMIT 5;"
echo ""
echo "-- Check email service logs:"
echo "railway logs --service=web | grep 'EmailService'"