/**
 * Dependant service for managing dependant profiles
 */

import api from './api';

export interface Dependant {
  dependant_id: number;
  first_name: string;
  last_name: string;
  date_of_birth: string;
  grade_level?: string;
  school_name?: string;
  special_needs?: string;
  medical_info?: string;
  parent1_client_id: number;
  parent2_client_id?: number;
  parent1_name?: string;
  parent2_name?: string;
  parent1_relationship?: string;
  parent2_relationship?: string;
  primary_contact_parent: 1 | 2;
  notes?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface DependantSearchParams {
  search?: string;
  client_id?: number;
  grade_level?: string;
  is_active?: boolean;
  page?: number;
  limit?: number;
}

export interface DependantListResponse {
  items: Dependant[];
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
}

export interface DependantCreateRequest {
  first_name: string;
  last_name: string;
  date_of_birth: string;
  grade_level?: string;
  school_name?: string;
  special_needs?: string;
  medical_info?: string;
  parent1_client_id: number;
  parent2_client_id?: number;
  parent1_relationship?: string;
  parent2_relationship?: string;
  primary_contact_parent?: 1 | 2;
  notes?: string;
}

export interface DependantUpdateRequest {
  first_name?: string;
  last_name?: string;
  date_of_birth?: string;
  grade_level?: string;
  school_name?: string;
  special_needs?: string;
  medical_info?: string;
  parent2_client_id?: number;
  parent1_relationship?: string;
  parent2_relationship?: string;
  primary_contact_parent?: 1 | 2;
  notes?: string;
}

export interface ParentAssignment {
  client_id: number;
  relationship: string;
  is_primary: boolean;
}

export const dependantService = {
  // Search and list dependants
  async searchDependants(params?: DependantSearchParams): Promise<DependantListResponse> {
    const response = await api.get<DependantListResponse>('/dependants', { params });
    return response.data;
  },

  // Get dependant by ID
  async getDependant(dependantId: number): Promise<Dependant> {
    const response = await api.get<Dependant>(`/dependants/${dependantId}`);
    return response.data;
  },

  // Get dependants by client ID (parent)
  async getDependantsByClient(clientId: number): Promise<Dependant[]> {
    const response = await api.get<Dependant[]>(`/clients/${clientId}/dependants`);
    return response.data;
  },

  // Create new dependant
  async createDependant(data: DependantCreateRequest): Promise<Dependant> {
    const response = await api.post<Dependant>('/dependants', data);
    return response.data;
  },

  // Update dependant
  async updateDependant(dependantId: number, data: DependantUpdateRequest): Promise<Dependant> {
    const response = await api.put<Dependant>(`/dependants/${dependantId}`, data);
    return response.data;
  },

  // Delete dependant (soft delete)
  async deleteDependant(dependantId: number): Promise<void> {
    await api.delete(`/dependants/${dependantId}`);
  },

  // Assign second parent
  async assignSecondParent(dependantId: number, clientId: number, relationship: string): Promise<Dependant> {
    const response = await api.post<Dependant>(`/dependants/${dependantId}/parents`, {
      client_id: clientId,
      relationship
    });
    return response.data;
  },

  // Remove second parent
  async removeSecondParent(dependantId: number): Promise<Dependant> {
    const response = await api.delete<Dependant>(`/dependants/${dependantId}/parents/2`);
    return response.data;
  },

  // Update primary contact parent
  async updatePrimaryContactParent(dependantId: number, parentNumber: 1 | 2): Promise<Dependant> {
    const response = await api.put<Dependant>(`/dependants/${dependantId}/primary-contact`, {
      primary_contact_parent: parentNumber
    });
    return response.data;
  },

  // Get dependant's appointments
  async getDependantAppointments(dependantId: number, params?: {
    status?: string;
    from_date?: string;
    to_date?: string;
    limit?: number;
  }): Promise<any> {
    const response = await api.get(`/dependants/${dependantId}/appointments`, { params });
    return response.data;
  },

  // Get dependant's learning progress
  async getDependantProgress(dependantId: number, subjectArea?: string): Promise<any> {
    const response = await api.get(`/dependants/${dependantId}/progress`, {
      params: { subject_area: subjectArea }
    });
    return response.data;
  },

  // Get dependant's tutors
  async getDependantTutors(dependantId: number): Promise<any[]> {
    const response = await api.get(`/dependants/${dependantId}/tutors`);
    return response.data;
  },

  // Validate parent access
  async validateParentAccess(dependantId: number, clientId: number): Promise<boolean> {
    try {
      const response = await api.get(`/dependants/${dependantId}/validate-access/${clientId}`);
      return response.data.has_access;
    } catch {
      return false;
    }
  }
};