-- Migration: 020_add_encryption_audit.sql
-- Description: Add encryption audit logging tables for financial data protection
-- Author: System
-- Date: 2024-01-15

-- Create encryption_audit_log table
CREATE TABLE IF NOT EXISTS encryption_audit_log (
    audit_id SERIAL PRIMARY KEY,
    event_type VARCHAR(100) NOT NULL, -- encrypted, decrypted, keys_rotated, etc.
    resource_type VARCHAR(50) NOT NULL, -- payment_method, bank_account, document, etc.
    resource_id VARCHAR(255) NOT NULL, -- ID of the encrypted resource
    user_id INTEGER REFERENCES user_accounts(user_id),
    metadata JSONB DEFAULT '{}', -- Additional event data
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create encrypted_fields_registry table
CREATE TABLE IF NOT EXISTS encrypted_fields_registry (
    registry_id SERIAL PRIMARY KEY,
    table_name VA<PERSON>HAR(100) NOT NULL,
    column_name VARCHAR(100) NOT NULL,
    encryption_key_id VARCHAR(50) NOT NULL,
    encryption_version VARCHAR(10) NOT NULL DEFAULT '1.0',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(table_name, column_name)
);

-- Create encryption_keys_metadata table (stores metadata only, not actual keys)
CREATE TABLE IF NOT EXISTS encryption_keys_metadata (
    key_id VARCHAR(50) PRIMARY KEY,
    key_purpose VARCHAR(100) NOT NULL,
    algorithm VARCHAR(50) NOT NULL DEFAULT 'AES-256-GCM',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    rotated_at TIMESTAMP WITH TIME ZONE,
    rotation_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP WITH TIME ZONE
);

-- Create tutor_bank_accounts table if not exists
CREATE TABLE IF NOT EXISTS tutor_bank_accounts (
    bank_account_id SERIAL PRIMARY KEY,
    tutor_id INTEGER NOT NULL REFERENCES tutors(tutor_id),
    account_holder_name VARCHAR(200) NOT NULL,
    bank_name VARCHAR(100),
    account_type VARCHAR(20) CHECK (account_type IN ('checking', 'savings')),
    account_number_masked VARCHAR(20), -- Only last 4 digits
    institution_number VARCHAR(3),
    transit_number VARCHAR(5),
    is_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Add encryption columns to sensitive tables
-- Payment methods
ALTER TABLE secure_payment_methods 
ADD COLUMN IF NOT EXISTS encryption_version VARCHAR(10) DEFAULT '1.0',
ADD COLUMN IF NOT EXISTS encrypted_at TIMESTAMP WITH TIME ZONE;

-- Bank accounts (for tutor payments)
ALTER TABLE tutor_bank_accounts 
ADD COLUMN IF NOT EXISTS account_number_encrypted TEXT,
ADD COLUMN IF NOT EXISTS routing_number_encrypted TEXT,
ADD COLUMN IF NOT EXISTS encryption_version VARCHAR(10) DEFAULT '1.0',
ADD COLUMN IF NOT EXISTS encrypted_at TIMESTAMP WITH TIME ZONE;

-- Financial documents
CREATE TABLE IF NOT EXISTS encrypted_financial_documents (
    document_id SERIAL PRIMARY KEY,
    owner_type VARCHAR(20) NOT NULL CHECK (owner_type IN ('client', 'tutor', 'platform')),
    owner_id INTEGER NOT NULL,
    document_type VARCHAR(50) NOT NULL, -- invoice, receipt, statement, tax_form
    document_name VARCHAR(255) NOT NULL,
    encrypted_data TEXT NOT NULL, -- Base64 encoded encrypted content
    encrypted_key TEXT NOT NULL, -- Encrypted document key
    document_hash VARCHAR(64) NOT NULL, -- SHA-256 hash for integrity
    file_size_bytes INTEGER NOT NULL,
    mime_type VARCHAR(100),
    encryption_metadata JSONB NOT NULL,
    uploaded_by INTEGER REFERENCES user_accounts(user_id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    accessed_at TIMESTAMP WITH TIME ZONE,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_encryption_audit_event ON encryption_audit_log(event_type, created_at);
CREATE INDEX IF NOT EXISTS idx_encryption_audit_resource ON encryption_audit_log(resource_type, resource_id);
CREATE INDEX IF NOT EXISTS idx_encryption_audit_user ON encryption_audit_log(user_id) WHERE user_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_encryption_audit_date ON encryption_audit_log(created_at);
CREATE INDEX IF NOT EXISTS idx_encrypted_docs_owner ON encrypted_financial_documents(owner_type, owner_id);
CREATE INDEX IF NOT EXISTS idx_encrypted_docs_type ON encrypted_financial_documents(document_type);

-- Add comments
COMMENT ON TABLE encryption_audit_log IS 'Audit trail for all encryption/decryption operations';
COMMENT ON TABLE encrypted_fields_registry IS 'Registry of which fields are encrypted in which tables';
COMMENT ON TABLE encryption_keys_metadata IS 'Metadata about encryption keys (keys stored separately)';
COMMENT ON TABLE encrypted_financial_documents IS 'Encrypted storage for sensitive financial documents';

COMMENT ON COLUMN encryption_audit_log.event_type IS 'Type of encryption event (encrypt, decrypt, rotate, etc)';
COMMENT ON COLUMN encrypted_fields_registry.encryption_key_id IS 'Reference to key used for this field';
COMMENT ON COLUMN encryption_keys_metadata.key_purpose IS 'Purpose of key (payment_methods, bank_accounts, etc)';
COMMENT ON COLUMN encrypted_financial_documents.encrypted_data IS 'Base64 encoded AES-256 encrypted document';
COMMENT ON COLUMN encrypted_financial_documents.document_hash IS 'SHA-256 hash of original document for integrity verification';

-- Insert default encryption field registry
INSERT INTO encrypted_fields_registry (table_name, column_name, encryption_key_id) VALUES
('secure_payment_methods', 'encrypted_metadata', 'payment_methods'),
('tutor_bank_accounts', 'account_number_encrypted', 'bank_accounts'),
('tutor_bank_accounts', 'routing_number_encrypted', 'bank_accounts'),
('client_payment_profiles', 'billing_address_encrypted', 'personal_data'),
('invoices', 'custom_fields_encrypted', 'invoice_data')
ON CONFLICT (table_name, column_name) DO NOTHING;

-- Insert default key metadata
INSERT INTO encryption_keys_metadata (key_id, key_purpose) VALUES
('payment_methods', 'Encryption of payment method data'),
('bank_accounts', 'Encryption of bank account information'),
('personal_data', 'Encryption of personal identifiable information'),
('invoice_data', 'Encryption of sensitive invoice fields'),
('documents', 'Encryption of financial documents')
ON CONFLICT (key_id) DO NOTHING;