import React from 'react';
import { useTranslation } from 'react-i18next';

interface DocumentsUploadProps {
  tutorId: number;
  profile: any;
}

export const DocumentsUpload: React.FC<DocumentsUploadProps> = ({ tutorId, profile }) => {
  const { t } = useTranslation();
  
  return (
    <div className="p-6 text-center">
      <p className="text-text-secondary">{t('common.comingSoon')}</p>
    </div>
  );
};