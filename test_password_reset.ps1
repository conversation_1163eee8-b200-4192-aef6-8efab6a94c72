# Test password reset functionality on Railway production
# Run this in PowerShell on Windows

Write-Host "Testing Password Reset Functionality" -ForegroundColor Green
Write-Host "===================================" -ForegroundColor Green

# Replace with your Railway production URL
$API_URL = "https://tutoraide-production.up.railway.app/api/v1"

# Test email - replace with a valid test email
$TEST_EMAIL = "<EMAIL>"

Write-Host ""
Write-Host "1. Requesting password reset for email: $TEST_EMAIL" -ForegroundColor Yellow
Write-Host "---------------------------------------------------"

# Request password reset
$body = @{
    email = $TEST_EMAIL
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "$API_URL/auth/password-reset/request" `
        -Method POST `
        -ContentType "application/json" `
        -Body $body `
        -Verbose
    
    Write-Host "Response: " -ForegroundColor Cyan
    $response | ConvertTo-Json -Depth 3
} catch {
    Write-Host "Error: $_" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host ""
Write-Host "2. Check your email for the reset token" -ForegroundColor Yellow
Write-Host "--------------------------------------"
Write-Host "Once you receive the email, you can test the reset confirmation with:" -ForegroundColor Cyan
Write-Host ""
Write-Host '$token = "YOUR_RESET_TOKEN"' -ForegroundColor White
Write-Host '$newPassword = "NewSecurePassword123!"' -ForegroundColor White
Write-Host ""
Write-Host '$confirmBody = @{' -ForegroundColor White
Write-Host '    token = $token' -ForegroundColor White
Write-Host '    new_password = $newPassword' -ForegroundColor White
Write-Host '} | ConvertTo-Json' -ForegroundColor White
Write-Host ""
Write-Host 'Invoke-RestMethod -Uri "$API_URL/auth/password-reset/confirm" `' -ForegroundColor White
Write-Host '    -Method POST `' -ForegroundColor White
Write-Host '    -ContentType "application/json" `' -ForegroundColor White
Write-Host '    -Body $confirmBody' -ForegroundColor White
Write-Host ""
Write-Host "3. To check database state, run these queries in Railway:" -ForegroundColor Yellow
Write-Host "--------------------------------------------------------"
Write-Host "-- Check if auth_tokens table has the reset token:" -ForegroundColor Gray
Write-Host "SELECT token_id, user_id, token_type, expires_at, used_at, created_at" -ForegroundColor Gray
Write-Host "FROM auth_tokens" -ForegroundColor Gray
Write-Host "WHERE token_type = 'password_reset'" -ForegroundColor Gray
Write-Host "ORDER BY created_at DESC" -ForegroundColor Gray
Write-Host "LIMIT 5;" -ForegroundColor Gray
Write-Host ""
Write-Host "-- Check email service logs:" -ForegroundColor Gray
Write-Host "railway logs --service=web | Select-String 'EmailService'" -ForegroundColor Gray