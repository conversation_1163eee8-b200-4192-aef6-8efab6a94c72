# TutorAide App Architecture

## Overview
TutorAide is a comprehensive tutoring management platform built with FastAPI, supporting multiple user roles (managers, tutors, clients) with features for appointment scheduling, billing, and multi-language support.

## Technology Stack
- **Backend**: FastAPI (Python)
- **Database**: PostgreSQL
- **Frontend**: Static files served from /app/frontend/public
- **API Version**: v1 (prefix: /api/v1)

## Core Architecture
- **Entry Point**: app/main.py
- **Middleware Stack**: Security → Input Validation → Headers → Logging → Localization → Authorization
- **CORS**: Configured for multiple origins
- **Static Assets**: Branding assets (favicon, logo) served from public folder

## Directory Structure
```
app/
├── api/           # API endpoints
│   └── v1/       # Version 1 API
├── backend/       # Backend logic
├── config/        # Configuration
├── core/          # Core utilities
├── database/      # Database connections
├── domain/        # Domain models
│   ├── client/
│   ├── dependant/
│   ├── search/
│   └── tutor/
├── frontend/      # Frontend assets
│   └── public/   # Static files
├── locales/       # i18n translations
├── models/        # Data models
├── repositories/  # Data access layer
├── services/      # Business logic
└── templates/     # Email/SMS templates

## Key Models
- User management (multi-role support)
- Appointments and scheduling
- Billing and invoicing
- Client profiles
- Tutor management
- Service configurations
- Subscriptions and packages
- Notifications and messaging
- Consent management
```