import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { format, differenceInDays } from 'date-fns';
import { 
  Package, Clock, Calendar, TrendingUp, 
  Plus, RefreshCw, AlertCircle, BarChart,
  History, ShoppingCart
} from 'lucide-react';
import api from '../../services/api';
import { Card } from '../common/Card';
import Button from '../common/Button';
import { Badge } from '../common/Badge';
import { Modal } from '../common/Modal';
import LoadingSpinner from '../ui/LoadingSpinner';
import { EmptyState } from '../common/EmptyState';
import { useAuth } from '../../contexts/AuthContext';
import SubscriptionPurchaseModal from '../billing/SubscriptionPurchaseModal';
import toast from 'react-hot-toast';

interface ClientSubscription {
  subscription_id: number;
  package_id: number;
  package_name: string;
  status: 'active' | 'expired' | 'suspended' | 'cancelled' | 'depleted';
  start_date: string;
  end_date: string;
  hours_purchased: number;
  hours_used: number;
  hours_remaining: number;
  auto_renew: boolean;
  price_paid: number;
  billing_frequency: string;
  created_at: string;
}

interface SubscriptionUsage {
  usage_id: number;
  appointment_id: number;
  hours_deducted: number;
  remaining_hours_after: number;
  usage_date: string;
  tutor_name: string;
  subject: string;
  notes?: string;
}

interface SubscriptionStatistics {
  total_subscriptions: number;
  active_subscriptions: number;
  total_hours_purchased: number;
  total_hours_used: number;
  total_spent: number;
  average_usage_per_month: number;
}

const ClientSubscriptions: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [subscriptions, setSubscriptions] = useState<ClientSubscription[]>([]);
  const [statistics, setStatistics] = useState<SubscriptionStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedSubscription, setSelectedSubscription] = useState<ClientSubscription | null>(null);
  const [usageHistory, setUsageHistory] = useState<SubscriptionUsage[]>([]);
  const [showUsageModal, setShowUsageModal] = useState(false);
  const [showPurchaseModal, setShowPurchaseModal] = useState(false);
  const [showAutoRenewModal, setShowAutoRenewModal] = useState(false);

  useEffect(() => {
    fetchSubscriptions();
  }, []);

  const fetchSubscriptions = async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      
      const [subsResponse, statsResponse] = await Promise.all([
        api.get<ClientSubscription[]>(`/subscriptions/clients/${user.userId}/subscriptions`),
        api.get<SubscriptionStatistics>(`/subscriptions/clients/${user.userId}/subscription-statistics`)
      ]);
      
      setSubscriptions(subsResponse.data);
      setStatistics(statsResponse.data);
      
    } catch (error) {
      console.error('Error fetching subscriptions:', error);
      toast.error(t('billing.errors.fetchSubscriptions'));
    } finally {
      setLoading(false);
    }
  };

  const fetchUsageHistory = async (subscriptionId: number) => {
    try {
      const response = await api.get<SubscriptionUsage[]>(`/subscriptions/${subscriptionId}/usage`);
      setUsageHistory(response.data);
      setShowUsageModal(true);
    } catch (error) {
      console.error('Error fetching usage history:', error);
      toast.error(t('billing.errors.fetchUsageHistory'));
    }
  };

  const toggleAutoRenew = async (subscription: ClientSubscription) => {
    try {
      await api.patch(`/subscriptions/${subscription.subscription_id}/auto-renew`, {
        auto_renew: !subscription.auto_renew
      });
      
      toast.success(
        subscription.auto_renew 
          ? t('billing.subscriptions.autoRenewDisabled')
          : t('billing.subscriptions.autoRenewEnabled')
      );
      
      fetchSubscriptions();
      setShowAutoRenewModal(false);
    } catch (error) {
      console.error('Error updating auto-renew:', error);
      toast.error(t('billing.errors.updateAutoRenew'));
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD'
    }).format(amount);
  };

  const getStatusBadge = (status: string, endDate: string) => {
    const daysUntilExpiry = differenceInDays(new Date(endDate), new Date());
    
    switch (status) {
      case 'active':
        if (daysUntilExpiry <= 7) {
          return <Badge variant="warning">{t('billing.subscriptions.status.expiringSoon')}</Badge>;
        }
        return <Badge variant="success">{t('billing.subscriptions.status.active')}</Badge>;
      case 'expired':
        return <Badge variant="danger">{t('billing.subscriptions.status.expired')}</Badge>;
      case 'depleted':
        return <Badge variant="secondary">{t('billing.subscriptions.status.depleted')}</Badge>;
      case 'suspended':
        return <Badge variant="warning">{t('billing.subscriptions.status.suspended')}</Badge>;
      case 'cancelled':
        return <Badge variant="secondary">{t('billing.subscriptions.status.cancelled')}</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getUsagePercentage = (used: number, total: number) => {
    return Math.min(100, Math.round((used / total) * 100));
  };

  const getUsageColor = (percentage: number) => {
    if (percentage > 80) return 'bg-red-600';
    if (percentage > 50) return 'bg-yellow-600';
    return 'bg-green-600';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  const activeSubscriptions = subscriptions.filter(s => s.status === 'active');

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      {statistics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">{t('billing.subscriptions.activeSubscriptions')}</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {statistics.active_subscriptions}
                </p>
              </div>
              <Package className="w-8 h-8 text-green-600" />
            </div>
          </Card>
          
          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">{t('billing.subscriptions.totalHoursRemaining')}</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {(statistics.total_hours_purchased - statistics.total_hours_used).toFixed(1)}h
                </p>
              </div>
              <Clock className="w-8 h-8 text-red-600" />
            </div>
          </Card>
          
          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">{t('billing.subscriptions.averageUsage')}</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {statistics.average_usage_per_month.toFixed(1)}h/mo
                </p>
              </div>
              <TrendingUp className="w-8 h-8 text-accent-red" />
            </div>
          </Card>
          
          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">{t('billing.subscriptions.totalSpent')}</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {formatCurrency(statistics.total_spent)}
                </p>
              </div>
              <BarChart className="w-8 h-8 text-purple-600" />
            </div>
          </Card>
        </div>
      )}

      {/* Quick Actions */}
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">
          {t('billing.subscriptions.yourSubscriptions')}
        </h2>
        <Button
          variant="primary"
          leftIcon={<Plus className="w-4 h-4" />}
          onClick={() => setShowPurchaseModal(true)}
        >
          {t('billing.subscriptions.purchaseNew')}
        </Button>
      </div>

      {/* Active Subscriptions */}
      {activeSubscriptions.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">
            {t('billing.subscriptions.activeSubscriptions')}
          </h3>
          {activeSubscriptions.map((subscription) => {
            const usagePercentage = getUsagePercentage(subscription.hours_used, subscription.hours_purchased);
            const daysUntilExpiry = differenceInDays(new Date(subscription.end_date), new Date());
            
            return (
              <Card key={subscription.subscription_id} className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900">
                      {subscription.package_name}
                    </h4>
                    <div className="flex items-center space-x-3 mt-1">
                      {getStatusBadge(subscription.status, subscription.end_date)}
                      {subscription.auto_renew && (
                        <Badge variant="info" size="sm">
                          <RefreshCw className="w-3 h-3 mr-1" />
                          {t('billing.subscriptions.autoRenew')}
                        </Badge>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-500">
                      {t('billing.subscriptions.purchasedOn')}
                    </p>
                    <p className="text-sm font-medium text-gray-900">
                      {format(new Date(subscription.created_at), 'MMM d, yyyy')}
                    </p>
                  </div>
                </div>

                {/* Usage Progress */}
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700">
                      {t('billing.subscriptions.hoursUsage')}
                    </span>
                    <span className="text-sm text-gray-600">
                      {subscription.hours_used.toFixed(1)} / {subscription.hours_purchased}h ({usagePercentage}%)
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div
                      className={`h-3 rounded-full transition-all ${getUsageColor(usagePercentage)}`}
                      style={{ width: `${usagePercentage}%` }}
                    />
                  </div>
                </div>

                {/* Subscription Details */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div>
                    <p className="text-sm text-gray-500">{t('billing.subscriptions.validUntil')}</p>
                    <p className="text-sm font-medium text-gray-900">
                      {format(new Date(subscription.end_date), 'MMM d, yyyy')}
                      {daysUntilExpiry <= 30 && (
                        <span className="text-yellow-600 ml-1">
                          ({t('billing.subscriptions.expiresInDays', { days: daysUntilExpiry })})
                        </span>
                      )}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">{t('billing.subscriptions.hoursRemaining')}</p>
                    <p className="text-sm font-medium text-gray-900">
                      {subscription.hours_remaining.toFixed(1)} {t('common.hours')}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">{t('billing.subscriptions.price')}</p>
                    <p className="text-sm font-medium text-gray-900">
                      {formatCurrency(subscription.price_paid)}
                    </p>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                  <div className="flex items-center space-x-3">
                    <Button
                      variant="secondary"
                      size="sm"
                      leftIcon={<History className="w-4 h-4" />}
                      onClick={() => {
                        setSelectedSubscription(subscription);
                        fetchUsageHistory(subscription.subscription_id);
                      }}
                    >
                      {t('billing.subscriptions.viewUsage')}
                    </Button>
                    <Button
                      variant="secondary"
                      size="sm"
                      leftIcon={<RefreshCw className="w-4 h-4" />}
                      onClick={() => {
                        setSelectedSubscription(subscription);
                        setShowAutoRenewModal(true);
                      }}
                    >
                      {subscription.auto_renew 
                        ? t('billing.subscriptions.disableAutoRenew')
                        : t('billing.subscriptions.enableAutoRenew')
                      }
                    </Button>
                  </div>
                  {daysUntilExpiry <= 30 && subscription.hours_remaining > 0 && (
                    <Button
                      variant="primary"
                      size="sm"
                      leftIcon={<ShoppingCart className="w-4 h-4" />}
                      onClick={() => setShowPurchaseModal(true)}
                    >
                      {t('billing.subscriptions.renewNow')}
                    </Button>
                  )}
                </div>
              </Card>
            );
          })}
        </div>
      )}

      {/* Past Subscriptions */}
      {subscriptions.filter(s => s.status !== 'active').length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">
            {t('billing.subscriptions.pastSubscriptions')}
          </h3>
          <Card className="overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('billing.subscriptions.package')}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('billing.subscriptions.period')}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('billing.subscriptions.hoursUsed')}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('billing.status.label')}
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('common.actions')}
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {subscriptions
                    .filter(s => s.status !== 'active')
                    .map((subscription) => (
                      <tr key={subscription.subscription_id} className="hover:bg-gray-50 transition-colors">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <p className="text-sm font-medium text-gray-900">
                            {subscription.package_name}
                          </p>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {format(new Date(subscription.start_date), 'MMM d, yyyy')} - {' '}
                          {format(new Date(subscription.end_date), 'MMM d, yyyy')}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {subscription.hours_used.toFixed(1)} / {subscription.hours_purchased}h
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {getStatusBadge(subscription.status, subscription.end_date)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedSubscription(subscription);
                              fetchUsageHistory(subscription.subscription_id);
                            }}
                          >
                            {t('billing.subscriptions.viewHistory')}
                          </Button>
                        </td>
                      </tr>
                    ))}
                </tbody>
              </table>
            </div>
          </Card>
        </div>
      )}

      {/* No Subscriptions */}
      {subscriptions.length === 0 && (
        <Card>
          <EmptyState
            icon={<Package className="w-12 h-12 text-gray-400" />}
            title={t('billing.subscriptions.noSubscriptions')}
            description={t('billing.subscriptions.noSubscriptionsClientDescription')}
            action={
              <Button
                variant="primary"
                leftIcon={<Plus className="w-4 h-4" />}
                onClick={() => setShowPurchaseModal(true)}
              >
                {t('billing.subscriptions.browsePackages')}
              </Button>
            }
          />
        </Card>
      )}

      {/* Usage History Modal */}
      <Modal
        isOpen={showUsageModal}
        onClose={() => {
          setShowUsageModal(false);
          setUsageHistory([]);
        }}
        title={t('billing.subscriptions.usageHistory')}
        size="xl"
      >
        <div className="p-6">
          {usageHistory.length === 0 ? (
            <p className="text-gray-600 text-center py-8">
              {t('billing.subscriptions.noUsageHistory')}
            </p>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                      {t('common.date')}
                    </th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                      {t('billing.subscriptions.session')}
                    </th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                      {t('billing.subscriptions.hoursUsed')}
                    </th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                      {t('billing.subscriptions.remainingAfter')}
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {usageHistory.map((usage) => (
                    <tr key={usage.usage_id}>
                      <td className="px-4 py-2 text-sm text-gray-900">
                        {format(new Date(usage.usage_date), 'MMM d, yyyy HH:mm')}
                      </td>
                      <td className="px-4 py-2 text-sm text-gray-900">
                        <div>
                          <p>{usage.subject}</p>
                          <p className="text-xs text-gray-500">{usage.tutor_name}</p>
                        </div>
                      </td>
                      <td className="px-4 py-2 text-sm text-gray-900">
                        {usage.hours_deducted.toFixed(1)}h
                      </td>
                      <td className="px-4 py-2 text-sm text-gray-900">
                        {usage.remaining_hours_after.toFixed(1)}h
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </Modal>

      {/* Auto-Renew Modal */}
      <Modal
        isOpen={showAutoRenewModal}
        onClose={() => {
          setShowAutoRenewModal(false);
          setSelectedSubscription(null);
        }}
        title={t('billing.subscriptions.autoRenewSettings')}
      >
        <div className="p-6 space-y-4">
          {selectedSubscription && (
            <>
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="text-sm text-gray-600">
                  <span className="font-medium">{t('billing.subscriptions.package')}:</span> {' '}
                  {selectedSubscription.package_name}
                </p>
                <p className="text-sm text-gray-600">
                  <span className="font-medium">{t('billing.subscriptions.currentStatus')}:</span> {' '}
                  {selectedSubscription.auto_renew 
                    ? t('billing.subscriptions.autoRenewEnabled')
                    : t('billing.subscriptions.autoRenewDisabled')
                  }
                </p>
              </div>
              
              <div className="bg-yellow-50 rounded-lg p-4">
                <div className="flex items-start">
                  <AlertCircle className="w-5 h-5 text-yellow-600 mr-2 mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-yellow-800">
                    {selectedSubscription.auto_renew 
                      ? t('billing.subscriptions.disableAutoRenewWarning')
                      : t('billing.subscriptions.enableAutoRenewInfo')
                    }
                  </div>
                </div>
              </div>
              
              <div className="flex justify-end space-x-3">
                <Button
                  variant="secondary"
                  onClick={() => setShowAutoRenewModal(false)}
                >
                  {t('common.cancel')}
                </Button>
                <Button
                  variant="primary"
                  onClick={() => toggleAutoRenew(selectedSubscription)}
                  leftIcon={<RefreshCw className="w-4 h-4" />}
                >
                  {selectedSubscription.auto_renew 
                    ? t('billing.subscriptions.disableAutoRenew')
                    : t('billing.subscriptions.enableAutoRenew')
                  }
                </Button>
              </div>
            </>
          )}
        </div>
      </Modal>

      {/* Purchase Modal */}
      <SubscriptionPurchaseModal
        isOpen={showPurchaseModal}
        onClose={() => setShowPurchaseModal(false)}
        onPurchaseComplete={() => {
          fetchSubscriptions();
          setShowPurchaseModal(false);
        }}
      />
    </div>
  );
};

export default ClientSubscriptions;
