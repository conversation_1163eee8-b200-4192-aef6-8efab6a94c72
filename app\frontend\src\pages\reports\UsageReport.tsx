import React from 'react';
import { useTranslation } from 'react-i18next';
import { 
  LineChart, Line, BarChart, Bar, PieChart, Pie, Cell, Scatter<PERSON>hart, Scatter,
  XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, ZAxis
} from 'recharts';
import { Activity, Smartphone, Monitor, Calendar, MapPin } from 'lucide-react';

// Mock data
const dailyUsage = [
  { hour: '00', desktop: 5, mobile: 12 },
  { hour: '02', desktop: 3, mobile: 8 },
  { hour: '04', desktop: 2, mobile: 5 },
  { hour: '06', desktop: 8, mobile: 15 },
  { hour: '08', desktop: 25, mobile: 35 },
  { hour: '10', desktop: 45, mobile: 38 },
  { hour: '12', desktop: 38, mobile: 42 },
  { hour: '14', desktop: 52, mobile: 48 },
  { hour: '16', desktop: 68, mobile: 72 },
  { hour: '18', desktop: 75, mobile: 85 },
  { hour: '20', desktop: 48, mobile: 62 },
  { hour: '22', desktop: 22, mobile: 35 },
];

const deviceBreakdown = [
  { device: 'Desktop', value: 45, color: '#007AFF' },
  { device: 'Mobile', value: 40, color: '#34C759' },
  { device: 'Tablet', value: 15, color: '#FF9500' },
];

const featureUsage = [
  { feature: 'Booking', usage: 89 },
  { feature: 'Messaging', usage: 76 },
  { feature: 'Reports', usage: 45 },
  { feature: 'Calendar', usage: 92 },
  { feature: 'Search', usage: 68 },
  { feature: 'Settings', usage: 34 },
];

const locationData = [
  { city: 'Montreal', users: 120, sessions: 450, avgDuration: 1.5 },
  { city: 'Quebec City', users: 85, sessions: 320, avgDuration: 1.3 },
  { city: 'Laval', users: 65, sessions: 245, avgDuration: 1.4 },
  { city: 'Gatineau', users: 45, sessions: 180, avgDuration: 1.6 },
  { city: 'Longueuil', users: 38, sessions: 142, avgDuration: 1.2 },
];

const UsageReport: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-text-primary">
          {t('sidebar.usage')} Report
        </h1>
        <p className="text-text-secondary mt-1">
          Platform usage patterns and user behavior analytics
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-large shadow-soft p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-accent-red bg-opacity-10 rounded-medium">
              <Activity className="w-6 h-6 text-accent-red" />
            </div>
            <span className="text-sm text-accent-green">+18%</span>
          </div>
          <h3 className="text-2xl font-semibold text-text-primary">3,456</h3>
          <p className="text-sm text-text-secondary mt-1">Daily Active Users</p>
        </div>

        <div className="bg-white rounded-large shadow-soft p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-accent-green bg-opacity-10 rounded-medium">
              <Calendar className="w-6 h-6 text-accent-green" />
            </div>
            <span className="text-sm text-accent-green">+22%</span>
          </div>
          <h3 className="text-2xl font-semibold text-text-primary">24.5k</h3>
          <p className="text-sm text-text-secondary mt-1">Weekly Sessions</p>
        </div>

        <div className="bg-white rounded-large shadow-soft p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-accent-orange bg-opacity-10 rounded-medium">
              <Monitor className="w-6 h-6 text-accent-orange" />
            </div>
            <span className="text-sm text-accent-red">-5%</span>
          </div>
          <h3 className="text-2xl font-semibold text-text-primary">32m</h3>
          <p className="text-sm text-text-secondary mt-1">Avg Session Time</p>
        </div>

        <div className="bg-white rounded-large shadow-soft p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-purple-500 bg-opacity-10 rounded-medium">
              <Smartphone className="w-6 h-6 text-purple-500" />
            </div>
            <span className="text-sm text-accent-green">+12%</span>
          </div>
          <h3 className="text-2xl font-semibold text-text-primary">55%</h3>
          <p className="text-sm text-text-secondary mt-1">Mobile Usage</p>
        </div>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Daily Usage Pattern */}
        <div className="bg-white rounded-large shadow-soft p-6">
          <h2 className="text-lg font-semibold text-text-primary mb-4">
            Daily Usage Pattern
          </h2>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={dailyUsage}>
              <CartesianGrid strokeDasharray="3 3" stroke="#E9ECEF" />
              <XAxis dataKey="hour" stroke="#6C757D" />
              <YAxis stroke="#6C757D" />
              <Tooltip />
              <Legend />
              <Line 
                type="monotone" 
                dataKey="desktop" 
                stroke="#007AFF" 
                strokeWidth={2}
                name="Desktop"
              />
              <Line 
                type="monotone" 
                dataKey="mobile" 
                stroke="#34C759" 
                strokeWidth={2}
                name="Mobile"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Device Breakdown */}
        <div className="bg-white rounded-large shadow-soft p-6">
          <h2 className="text-lg font-semibold text-text-primary mb-4">
            Device Breakdown
          </h2>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={deviceBreakdown}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={100}
                fill="#8884d8"
                paddingAngle={5}
                dataKey="value"
              >
                {deviceBreakdown.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </div>

        {/* Feature Usage */}
        <div className="bg-white rounded-large shadow-soft p-6">
          <h2 className="text-lg font-semibold text-text-primary mb-4">
            Feature Usage Rate
          </h2>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={featureUsage} layout="horizontal">
              <CartesianGrid strokeDasharray="3 3" stroke="#E9ECEF" />
              <XAxis type="number" stroke="#6C757D" domain={[0, 100]} />
              <YAxis dataKey="feature" type="category" stroke="#6C757D" width={80} />
              <Tooltip formatter={(value) => `${value}%`} />
              <Bar dataKey="usage" fill="#007AFF" radius={[0, 8, 8, 0]} />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Geographic Distribution */}
        <div className="bg-white rounded-large shadow-soft p-6">
          <h2 className="text-lg font-semibold text-text-primary mb-4">
            Geographic Distribution
          </h2>
          <ResponsiveContainer width="100%" height={300}>
            <ScatterChart>
              <CartesianGrid strokeDasharray="3 3" stroke="#E9ECEF" />
              <XAxis dataKey="users" name="Users" stroke="#6C757D" />
              <YAxis dataKey="sessions" name="Sessions" stroke="#6C757D" />
              <ZAxis dataKey="avgDuration" range={[50, 400]} name="Avg Duration" />
              <Tooltip cursor={{ strokeDasharray: '3 3' }} />
              <Scatter name="Cities" data={locationData} fill="#007AFF">
                {locationData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill="#007AFF" />
                ))}
              </Scatter>
            </ScatterChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Usage Heatmap */}
      <div className="mt-8 bg-white rounded-large shadow-soft p-6">
        <h2 className="text-lg font-semibold text-text-primary mb-4">
          Weekly Usage Heatmap
        </h2>
        <div className="grid grid-cols-8 gap-1">
          <div></div>
          {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day) => (
            <div key={day} className="text-center text-xs text-text-secondary font-medium">
              {day}
            </div>
          ))}
          {['Morning', 'Afternoon', 'Evening', 'Night'].map((time) => (
            <React.Fragment key={time}>
              <div className="text-xs text-text-secondary font-medium pr-2 flex items-center justify-end">
                {time}
              </div>
              {[1, 2, 3, 4, 5, 6, 7].map((day) => {
                const intensity = Math.random();
                return (
                  <div
                    key={`${time}-${day}`}
                    className="aspect-square rounded"
                    style={{
                      backgroundColor: `rgba(0, 122, 255, ${intensity})`,
                    }}
                    title={`${Math.round(intensity * 100)}% usage`}
                  />
                );
              })}
            </React.Fragment>
          ))}
        </div>
        <div className="flex items-center gap-4 mt-4">
          <span className="text-xs text-text-secondary">Low</span>
          <div className="flex gap-1">
            {[0.2, 0.4, 0.6, 0.8, 1].map((opacity) => (
              <div
                key={opacity}
                className="w-4 h-4 rounded"
                style={{ backgroundColor: `rgba(0, 122, 255, ${opacity})` }}
              />
            ))}
          </div>
          <span className="text-xs text-text-secondary">High</span>
        </div>
      </div>

      {/* Top Locations */}
      <div className="mt-8 bg-white rounded-large shadow-soft p-6">
        <h2 className="text-lg font-semibold text-text-primary mb-4">
          Top Locations
        </h2>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-primary-200">
                <th className="text-left py-3 px-4 text-sm font-medium text-text-secondary">
                  <MapPin className="w-4 h-4 inline mr-1" />
                  City
                </th>
                <th className="text-center py-3 px-4 text-sm font-medium text-text-secondary">
                  Users
                </th>
                <th className="text-center py-3 px-4 text-sm font-medium text-text-secondary">
                  Sessions
                </th>
                <th className="text-center py-3 px-4 text-sm font-medium text-text-secondary">
                  Avg Duration
                </th>
              </tr>
            </thead>
            <tbody>
              {locationData.map((location, index) => (
                <tr key={index} className="border-b border-primary-100 hover:bg-background-secondary">
                  <td className="py-3 px-4 text-sm text-text-primary font-medium">
                    {location.city}
                  </td>
                  <td className="py-3 px-4 text-sm text-text-primary text-center">
                    {location.users}
                  </td>
                  <td className="py-3 px-4 text-sm text-text-primary text-center">
                    {location.sessions}
                  </td>
                  <td className="py-3 px-4 text-sm text-text-primary text-center">
                    {location.avgDuration}h
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default UsageReport;