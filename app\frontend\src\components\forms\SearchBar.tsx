import React, { useState } from 'react';
import { Search } from 'lucide-react';
import { clsx } from 'clsx';

interface SearchBarProps {
  placeholder?: string;
  onSearch: (value: string) => void;
  loading?: boolean;
  className?: string;
}

export const SearchBar: React.FC<SearchBarProps> = ({
  placeholder = 'Search...',
  onSearch,
  loading = false,
  className
}) => {
  const [value, setValue] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(value);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      onSearch(value);
    }
  };

  return (
    <form onSubmit={handleSubmit} className={clsx('relative', className)}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-text-muted pointer-events-none" />
        <input
          type="text"
          value={value}
          onChange={(e) => setValue(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          disabled={loading}
          className={clsx(
            'w-full pl-10 pr-4 py-3 text-base border rounded-full bg-background-secondary',
            'transition-all duration-200 focus:outline-none focus:border-accent-red focus:bg-white focus:shadow-focus',
            'placeholder:text-text-muted',
            'border-border-primary hover:border-border-secondary',
            loading && 'opacity-60 cursor-not-allowed'
          )}
        />
        {loading && (
          <div className="absolute right-3 top-1/2 -translate-y-1/2">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-accent-red"></div>
          </div>
        )}
      </div>
    </form>
  );
};

export default SearchBar;