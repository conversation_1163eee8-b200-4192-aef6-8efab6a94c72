"""
Onboarding API endpoints for progressive user setup.
"""

from typing import Dict, List, Any
from fastapi import APIRouter, Depends, HTTPException, status, Body
from app.services.onboarding_service import OnboardingService
from app.models.onboarding_models import (
    OnboardingFlow,
    OnboardingFlowSummary,
    OnboardingStepSummary,
    OnboardingStep,
    OnboardingRole
)
from app.models.user_models import User
from app.core.dependencies import get_current_active_user
from app.core.logging import TutorAideLogger

router = APIRouter(prefix="/onboarding")
logger = TutorAideLogger.get_logger(__name__)


@router.post("/flows", response_model=OnboardingFlow)
async def create_onboarding_flow(
    user_id: int,
    role: OnboardingRole,
    current_user: User = Depends(get_current_active_user)
):
    """
    Create a new onboarding flow for a user.
    
    **Authorization:**
    - Users can create flows for themselves
    - Managers can create flows for any user
    
    **Parameters:**
    - user_id: ID of the user to create onboarding for
    - role: Role for the onboarding (client, tutor, manager)
    """
    try:
        service = OnboardingService()
        flow = await service.create_onboarding_flow(user_id, role, current_user)
        
        logger.info(f"Created {role} onboarding flow for user {user_id}")
        return flow
        
    except Exception as e:
        logger.error(f"Error creating onboarding flow: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/flows/user/{user_id}", response_model=List[OnboardingFlowSummary])
async def get_user_onboarding_flows(
    user_id: int,
    current_user: User = Depends(get_current_active_user)
):
    """
    Get all onboarding flows for a user.
    
    **Authorization:**
    - Users can view their own flows
    - Managers can view any user's flows
    """
    try:
        service = OnboardingService()
        flows = await service.get_user_flows(user_id, current_user)
        
        logger.debug(f"Retrieved {len(flows)} onboarding flows for user {user_id}")
        return flows
        
    except Exception as e:
        logger.error(f"Error getting user onboarding flows: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/flows/my", response_model=List[OnboardingFlowSummary])
async def get_my_onboarding_flows(
    current_user: User = Depends(get_current_active_user)
):
    """
    Get all onboarding flows for the current user.
    """
    try:
        service = OnboardingService()
        flows = await service.get_user_flows(current_user.user_id, current_user)
        
        logger.debug(f"Retrieved {len(flows)} onboarding flows for current user")
        return flows
        
    except Exception as e:
        logger.error(f"Error getting current user onboarding flows: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/flows/{flow_id}/start", response_model=OnboardingFlow)
async def start_onboarding_flow(
    flow_id: int,
    current_user: User = Depends(get_current_active_user)
):
    """
    Start an onboarding flow.
    
    **Authorization:**
    - Users can start their own flows
    - Managers can start any user's flows
    """
    try:
        service = OnboardingService()
        flow = await service.start_onboarding_flow(flow_id, current_user)
        
        logger.info(f"Started onboarding flow {flow_id}")
        return flow
        
    except Exception as e:
        logger.error(f"Error starting onboarding flow: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/flows/{flow_id}/steps", response_model=List[OnboardingStepSummary])
async def get_onboarding_steps(
    flow_id: int,
    current_user: User = Depends(get_current_active_user)
):
    """
    Get all steps for an onboarding flow with accessibility information.
    
    **Authorization:**
    - Users can view steps for their own flows
    - Managers can view steps for any flow
    """
    try:
        service = OnboardingService()
        steps = await service.get_flow_steps(flow_id, current_user)
        
        logger.debug(f"Retrieved {len(steps)} steps for flow {flow_id}")
        return steps
        
    except Exception as e:
        logger.error(f"Error getting onboarding steps: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/steps/{step_id}/complete", response_model=OnboardingStep)
async def complete_onboarding_step(
    step_id: int,
    completion_data: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_active_user)
):
    """
    Complete an onboarding step with provided data.
    
    **Authorization:**
    - Users can complete steps in their own flows
    - Managers can complete steps in any flow
    
    **Parameters:**
    - completion_data: Data collected during step completion
    
    **Example completion_data:**
    ```json
    {
        "first_name": "John",
        "last_name": "Doe",
        "phone": "******-555-0123",
        "address": "123 Main St, Montreal, QC",
        "emergency_contacts": [
            {
                "name": "Jane Doe",
                "phone": "******-555-0124",
                "relationship": "spouse"
            }
        ],
        "consents": {
            "privacy_policy": true,
            "terms_of_service": true
        }
    }
    ```
    """
    try:
        service = OnboardingService()
        step = await service.complete_step(step_id, completion_data, current_user)
        
        logger.info(f"Completed onboarding step {step_id}")
        return step
        
    except Exception as e:
        logger.error(f"Error completing onboarding step: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/steps/{step_id}/skip", response_model=OnboardingStep)
async def skip_onboarding_step(
    step_id: int,
    skip_reason: str = Body(..., embed=True),
    current_user: User = Depends(get_current_active_user)
):
    """
    Skip an onboarding step with a reason.
    
    **Authorization:**
    - Users can skip optional steps in their own flows
    - Managers can skip any skippable step
    
    **Parameters:**
    - skip_reason: Reason for skipping the step
    """
    try:
        service = OnboardingService()
        step = await service.skip_step(step_id, skip_reason, current_user)
        
        logger.info(f"Skipped onboarding step {step_id}: {skip_reason}")
        return step
        
    except Exception as e:
        logger.error(f"Error skipping onboarding step: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/templates/client")
async def get_client_onboarding_template(
    current_user: User = Depends(get_current_active_user)
):
    """
    Get the default client onboarding template.
    
    **Use case:** Preview what steps are involved in client onboarding
    """
    try:
        service = OnboardingService()
        template = service.get_client_onboarding_template()
        
        logger.debug("Retrieved client onboarding template")
        return template
        
    except Exception as e:
        logger.error(f"Error getting client onboarding template: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/templates/tutor")
async def get_tutor_onboarding_template(
    current_user: User = Depends(get_current_active_user)
):
    """
    Get the default tutor onboarding template.
    
    **Use case:** Preview what steps are involved in tutor onboarding
    """
    try:
        service = OnboardingService()
        template = service.get_tutor_onboarding_template()
        
        logger.debug("Retrieved tutor onboarding template")
        return template
        
    except Exception as e:
        logger.error(f"Error getting tutor onboarding template: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


# Health check for onboarding system
@router.get("/health")
async def onboarding_health():
    """
    Health check endpoint for onboarding system.
    """
    try:
        service = OnboardingService()
        
        # Test database connection by trying to get templates
        client_template = service.get_client_onboarding_template()
        tutor_template = service.get_tutor_onboarding_template()
        
        return {
            "status": "healthy",
            "service": "onboarding",
            "templates": {
                "client": {
                    "name": client_template.name,
                    "steps": len(client_template.steps),
                    "estimated_minutes": client_template.estimated_total_minutes
                },
                "tutor": {
                    "name": tutor_template.name,
                    "steps": len(tutor_template.steps),
                    "estimated_minutes": tutor_template.estimated_total_minutes
                }
            }
        }
        
    except Exception as e:
        logger.error(f"Onboarding health check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Onboarding service unavailable"
        )
