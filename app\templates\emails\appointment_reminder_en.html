<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Appointment Reminder - {{ company_name }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            padding: 40px 30px;
            text-align: center;
            color: white;
        }
        
        .header h1 {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .appointment-card {
            background-color: #f0f9ff;
            border: 2px solid #0ea5e9;
            border-radius: 12px;
            padding: 24px;
            margin: 24px 0;
        }
        
        .appointment-card h3 {
            color: #0369a1;
            font-size: 18px;
            margin-bottom: 16px;
            font-weight: 600;
        }
        
        .appointment-details {
            display: grid;
            gap: 12px;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e0f2fe;
        }
        
        .detail-label {
            font-weight: 600;
            color: #0369a1;
        }
        
        .detail-value {
            color: #374151;
        }
        
        .action-buttons {
            text-align: center;
            margin: 32px 0;
            display: flex;
            gap: 16px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            display: inline-block;
            text-decoration: none;
            padding: 14px 24px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            transition: transform 0.2s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            color: white;
        }
        
        .btn-secondary {
            background-color: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .reminder-note {
            background-color: #fef3c7;
            border-left: 4px solid #f59e0b;
            padding: 16px;
            margin: 24px 0;
            border-radius: 4px;
        }
        
        .reminder-note h3 {
            color: #d97706;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .reminder-note p {
            color: #92400e;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .footer {
            background-color: #f8f9fa;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e2e8f0;
        }
        
        .footer p {
            color: #6b7280;
            font-size: 14px;
            margin-bottom: 8px;
        }
        
        .footer a {
            color: #667eea;
            text-decoration: none;
        }
        
        .footer a:hover {
            text-decoration: underline;
        }
        
        @media only screen and (max-width: 600px) {
            .container {
                margin: 0;
                border-radius: 0;
            }
            
            .header, .content, .footer {
                padding: 24px 20px;
            }
            
            .appointment-card {
                padding: 20px;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .btn {
                margin: 4px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📅 Appointment Reminder</h1>
            <p>Your tutoring session is coming up!</p>
        </div>
        
        <div class="content">
            <div class="appointment-card">
                <h3>📚 Upcoming Session Details</h3>
                <div class="appointment-details">
                    <div class="detail-row">
                        <span class="detail-label">Date:</span>
                        <span class="detail-value">{{ appointment_date }}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Time:</span>
                        <span class="detail-value">{{ appointment_time }}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Duration:</span>
                        <span class="detail-value">{{ duration }} minutes</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Tutor:</span>
                        <span class="detail-value">{{ tutor_name }}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Subject:</span>
                        <span class="detail-value">{{ subject }}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Location:</span>
                        <span class="detail-value">{{ location }}</span>
                    </div>
                </div>
            </div>
            
            <div class="action-buttons">
                <a href="{{ confirm_link }}" class="btn btn-primary">✅ Confirm Attendance</a>
                <a href="{{ reschedule_link }}" class="btn btn-secondary">📅 Reschedule</a>
                <a href="{{ cancel_link }}" class="btn btn-secondary">❌ Cancel</a>
            </div>
            
            <div class="reminder-note">
                <h3>⏰ Important Reminder</h3>
                <p>
                    Please confirm your attendance or make changes at least 2 hours before your session. 
                    Late cancellations may incur fees according to our cancellation policy.
                </p>
            </div>
            
            <div style="margin: 24px 0; padding: 20px; background-color: #f9fafb; border-radius: 8px;">
                <h4 style="color: #374151; margin-bottom: 12px;">Need Help?</h4>
                <p style="color: #6b7280; font-size: 14px; line-height: 1.5;">
                    If you have any questions or need to make changes, contact us at 
                    <a href="mailto:{{ support_email }}" style="color: #667eea;">{{ support_email }}</a> 
                    or call {{ support_phone }}.
                </p>
            </div>
        </div>
        
        <div class="footer">
            <p>This reminder was sent to {{ user_email }}</p>
            <p>If you need help, contact us at <a href="mailto:{{ support_email }}">{{ support_email }}</a></p>
            <p>&copy; {{ year }} {{ company_name }}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>