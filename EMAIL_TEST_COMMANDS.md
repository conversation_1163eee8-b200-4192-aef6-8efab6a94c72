# Email Testing Commands for Railway

## 1. Test Background Tasks
Check if BackgroundTasks work at all in Railway:
```bash
curl https://tutoraide-production.up.railway.app/api/v1/auth/test-background-task
```
Look for these logs:
- "TEST BACKGROUND TASK ENDPOINT CALLED"
- "BA<PERSON><PERSON><PERSON>OUND TASK EXECUTED!" (if background tasks work)

## 2. Test Synchronous Password Reset
This bypasses BackgroundTasks completely:
```bash
curl -X POST https://tutoraide-production.up.railway.app/api/v1/auth/password/reset-request-sync \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'
```
Look for these logs:
- "SYNC PASSWORD RESET REQUEST"
- "SMTP EMAIL SEND ATTEMPT"
- SMTP connection details
- Any error messages

## 3. Test Normal Password Reset
Original endpoint with BackgroundTasks:
```bash
curl -X POST https://tutoraide-production.up.railway.app/api/v1/auth/password/reset-request \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'
```
Look for:
- "BACKGROUND TASK STARTED" (should appear if background tasks work)
- If no background task logs, then BackgroundTasks aren't executing

## 4. Check Email Configuration
```bash
curl https://tutoraide-production.up.railway.app/api/v1/auth/email/config-check
```
This requires authentication but shows current email config.

## Expected Log Patterns

### If SMTP Works:
```
=== SMTP EMAIL SEND ATTEMPT ===
SMTP Configuration:
  Host: smtp.gmail.com
  Port: 587
  Username: <EMAIL>
Connecting to SMTP server smtp.gmail.com:587
SMTP connection established
Starting TLS for secure connection
TLS started successfully
<NAME_EMAIL>
SMTP authentication successful
Sending <NAME_EMAIL>
Email sent successfully via SMTP
=== SMTP EMAIL SEND COMPLETE ===
```

### If SMTP Fails:
- Authentication error: Check app-specific password
- Connection error: Check network/firewall
- Timeout: Railway might be blocking SMTP

## Gmail Setup Checklist

1. **Enable 2-Factor Authentication**
   - Go to Google Account settings
   - Security → 2-Step Verification

2. **Generate App-Specific Password**
   - Go to https://myaccount.google.com/apppasswords
   - Select "Mail" and generate password
   - Use this password for SMTP_PASSWORD

3. **Environment Variables in Railway**
   ```
   SMTP_HOST=smtp.gmail.com
   SMTP_PORT=587
   SMTP_USERNAME=<EMAIL>
   SMTP_PASSWORD=<app-specific-password>
   SMTP_TLS=true
   ```

## Debugging Steps

1. First run test #1 to see if BackgroundTasks work
2. Then run test #2 to see if SMTP works at all
3. Check the Railway logs for detailed error messages
4. If SMTP fails with auth error, regenerate app-specific password