import { EventEmitter } from 'events';

// WebSocket Types and Interfaces
export enum WebSocketEvent {
  // Connection events
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
  ERROR = 'error',
  RECONNECTING = 'reconnecting',
  
  // Calendar events
  APPOINTMENT_CREATED = 'appointment.created',
  APPOINTMENT_UPDATED = 'appointment.updated',
  APPOINTMENT_CANCELLED = 'appointment.cancelled',
  APPOINTMENT_REMINDER = 'appointment.reminder',
  
  // Messaging events
  MESSAGE_RECEIVED = 'message.received',
  MESSAGE_SENT = 'message.sent',
  MESSAGE_READ = 'message.read',
  TYPING_START = 'typing.start',
  TYPING_STOP = 'typing.stop',
  
  // Notification events
  NOTIFICATION_NEW = 'notification.new',
  NOTIFICATION_READ = 'notification.read',
  
  // User events
  USER_ONLINE = 'user.online',
  USER_OFFLINE = 'user.offline',
  USER_STATUS_CHANGED = 'user.status_changed',
  
  // System events
  SYSTEM_ANNOUNCEMENT = 'system.announcement',
  MAINTENANCE_ALERT = 'maintenance.alert'
}

export interface WebSocketMessage<T = any> {
  event: WebSocketEvent;
  data: T;
  timestamp: string;
  id: string;
}

export interface WebSocketConfig {
  url: string;
  reconnect: boolean;
  reconnectInterval: number;
  reconnectMaxAttempts: number;
  heartbeatInterval: number;
  debug: boolean;
}

export interface ConnectionState {
  isConnected: boolean;
  reconnectAttempts: number;
  lastConnected?: Date;
  lastDisconnected?: Date;
  lastError?: string;
}

export interface SubscriptionOptions {
  room?: string;
  filters?: Record<string, any>;
}

class WebSocketService extends EventEmitter {
  private ws: WebSocket | null = null;
  private config: WebSocketConfig;
  private connectionState: ConnectionState;
  private reconnectTimer?: NodeJS.Timeout;
  private heartbeatTimer?: NodeJS.Timeout;
  private messageQueue: WebSocketMessage[] = [];
  private subscriptions: Map<string, SubscriptionOptions> = new Map();
  private authToken?: string;

  constructor(config?: Partial<WebSocketConfig>) {
    super();
    
    this.config = {
      url: this.buildWebSocketUrl(),
      reconnect: true,
      reconnectInterval: 5000,
      reconnectMaxAttempts: 10,
      heartbeatInterval: 30000,
      debug: false,
      ...config
    };

    this.connectionState = {
      isConnected: false,
      reconnectAttempts: 0
    };
  }

  /**
   * Connect to WebSocket server
   */
  connect(authToken?: string): void {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.log('Already connected');
      return;
    }

    this.authToken = authToken;
    this.connectionState.reconnectAttempts = 0;

    try {
      const url = authToken 
        ? `${this.config.url}?token=${encodeURIComponent(authToken)}`
        : this.config.url;

      this.ws = new WebSocket(url);
      this.setupEventListeners();
    } catch (error) {
      this.handleError('Connection failed', error);
    }
  }

  /**
   * Disconnect from WebSocket server
   */
  disconnect(): void {
    this.config.reconnect = false;
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = undefined;
    }

    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = undefined;
    }

    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }

    this.connectionState.isConnected = false;
    this.connectionState.lastDisconnected = new Date();
    this.emit(WebSocketEvent.DISCONNECTED);
  }

  /**
   * Send message through WebSocket
   */
  send<T = any>(event: WebSocketEvent, data: T): void {
    const message: WebSocketMessage<T> = {
      event,
      data,
      timestamp: new Date().toISOString(),
      id: this.generateMessageId()
    };

    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
      this.log('Message sent:', message);
    } else {
      // Queue message for later
      this.messageQueue.push(message);
      this.log('Message queued:', message);
    }
  }

  /**
   * Subscribe to a specific room or channel
   */
  subscribe(channel: string, options?: SubscriptionOptions): void {
    this.subscriptions.set(channel, options || {});
    
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.send(WebSocketEvent.CONNECTED, {
        action: 'subscribe',
        channel,
        ...options
      });
    }
  }

  /**
   * Unsubscribe from a channel
   */
  unsubscribe(channel: string): void {
    this.subscriptions.delete(channel);
    
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.send(WebSocketEvent.CONNECTED, {
        action: 'unsubscribe',
        channel
      });
    }
  }

  /**
   * Get connection state
   */
  getConnectionState(): ConnectionState {
    return { ...this.connectionState };
  }

  /**
   * Check if connected
   */
  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  /**
   * Setup WebSocket event listeners
   */
  private setupEventListeners(): void {
    if (!this.ws) return;

    this.ws.onopen = () => {
      this.log('WebSocket connected');
      this.connectionState.isConnected = true;
      this.connectionState.lastConnected = new Date();
      this.connectionState.reconnectAttempts = 0;
      
      this.emit(WebSocketEvent.CONNECTED);
      
      // Resubscribe to channels
      this.subscriptions.forEach((options, channel) => {
        this.subscribe(channel, options);
      });
      
      // Send queued messages
      this.flushMessageQueue();
      
      // Start heartbeat
      this.startHeartbeat();
    };

    this.ws.onclose = (event) => {
      this.log('WebSocket disconnected:', event.code, event.reason);
      this.connectionState.isConnected = false;
      this.connectionState.lastDisconnected = new Date();
      
      this.emit(WebSocketEvent.DISCONNECTED, {
        code: event.code,
        reason: event.reason
      });
      
      this.stopHeartbeat();
      
      // Attempt reconnection
      if (this.config.reconnect && this.connectionState.reconnectAttempts < this.config.reconnectMaxAttempts) {
        this.scheduleReconnect();
      }
    };

    this.ws.onerror = (error) => {
      this.handleError('WebSocket error', error);
    };

    this.ws.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data);
        this.log('Message received:', message);
        
        // Emit specific event
        this.emit(message.event, message.data);
        
        // Emit generic message event
        this.emit('message', message);
      } catch (error) {
        this.handleError('Failed to parse message', error);
      }
    };
  }

  /**
   * Schedule reconnection attempt
   */
  private scheduleReconnect(): void {
    this.connectionState.reconnectAttempts++;
    
    const delay = Math.min(
      this.config.reconnectInterval * Math.pow(2, this.connectionState.reconnectAttempts - 1),
      30000 // Max 30 seconds
    );
    
    this.log(`Reconnecting in ${delay}ms (attempt ${this.connectionState.reconnectAttempts})`);
    this.emit(WebSocketEvent.RECONNECTING, {
      attempt: this.connectionState.reconnectAttempts,
      maxAttempts: this.config.reconnectMaxAttempts,
      delay
    });
    
    this.reconnectTimer = setTimeout(() => {
      this.connect(this.authToken);
    }, delay);
  }

  /**
   * Start heartbeat to keep connection alive
   */
  private startHeartbeat(): void {
    this.stopHeartbeat();
    
    this.heartbeatTimer = setInterval(() => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify({ type: 'ping' }));
      }
    }, this.config.heartbeatInterval);
  }

  /**
   * Stop heartbeat
   */
  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = undefined;
    }
  }

  /**
   * Flush queued messages
   */
  private flushMessageQueue(): void {
    while (this.messageQueue.length > 0 && this.ws?.readyState === WebSocket.OPEN) {
      const message = this.messageQueue.shift();
      if (message) {
        this.ws.send(JSON.stringify(message));
      }
    }
  }

  /**
   * Build WebSocket URL
   */
  private buildWebSocketUrl(): string {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;
    const apiBase = import.meta.env.VITE_API_URL || '/api/v1';
    
    // Remove http(s):// and /api/v1 from apiBase if present
    const cleanBase = apiBase
      .replace(/^https?:\/\//, '')
      .replace(/\/api\/v\d+$/, '');
    
    return `${protocol}//${cleanBase || host}/ws`;
  }

  /**
   * Generate unique message ID
   */
  private generateMessageId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Handle errors
   */
  private handleError(message: string, error: any): void {
    const errorMessage = `${message}: ${error?.message || error}`;
    this.connectionState.lastError = errorMessage;
    
    this.log(errorMessage, 'error');
    this.emit(WebSocketEvent.ERROR, {
      message: errorMessage,
      error
    });
  }

  /**
   * Log messages (debug mode)
   */
  private log(message: string, level: 'info' | 'error' = 'info', ...args: any[]): void {
    if (!this.config.debug) return;
    
    const prefix = '[WebSocket]';
    if (level === 'error') {
      console.error(prefix, message, ...args);
    } else {
      console.log(prefix, message, ...args);
    }
  }
}

// Create singleton instance
const websocketService = new WebSocketService({
  debug: import.meta.env.DEV // Enable debug in development
});

// Calendar WebSocket service
export class CalendarWebSocketService {
  constructor(private ws: WebSocketService) {}

  subscribeToAppointments(tutorIds?: number[]): void {
    this.ws.subscribe('appointments', {
      filters: { tutor_ids: tutorIds }
    });
  }

  onAppointmentCreated(callback: (data: any) => void): () => void {
    this.ws.on(WebSocketEvent.APPOINTMENT_CREATED, callback);
    return () => this.ws.off(WebSocketEvent.APPOINTMENT_CREATED, callback);
  }

  onAppointmentUpdated(callback: (data: any) => void): () => void {
    this.ws.on(WebSocketEvent.APPOINTMENT_UPDATED, callback);
    return () => this.ws.off(WebSocketEvent.APPOINTMENT_UPDATED, callback);
  }

  onAppointmentCancelled(callback: (data: any) => void): () => void {
    this.ws.on(WebSocketEvent.APPOINTMENT_CANCELLED, callback);
    return () => this.ws.off(WebSocketEvent.APPOINTMENT_CANCELLED, callback);
  }
}

// Messaging WebSocket service
export class MessagingWebSocketService {
  constructor(private ws: WebSocketService) {}

  subscribeToConversation(conversationId: number): void {
    this.ws.subscribe(`conversation:${conversationId}`);
  }

  unsubscribeFromConversation(conversationId: number): void {
    this.ws.unsubscribe(`conversation:${conversationId}`);
  }

  sendTypingIndicator(conversationId: number, isTyping: boolean): void {
    this.ws.send(isTyping ? WebSocketEvent.TYPING_START : WebSocketEvent.TYPING_STOP, {
      conversation_id: conversationId
    });
  }

  onMessageReceived(callback: (data: any) => void): () => void {
    this.ws.on(WebSocketEvent.MESSAGE_RECEIVED, callback);
    return () => this.ws.off(WebSocketEvent.MESSAGE_RECEIVED, callback);
  }

  onTypingStart(callback: (data: any) => void): () => void {
    this.ws.on(WebSocketEvent.TYPING_START, callback);
    return () => this.ws.off(WebSocketEvent.TYPING_START, callback);
  }

  onTypingStop(callback: (data: any) => void): () => void {
    this.ws.on(WebSocketEvent.TYPING_STOP, callback);
    return () => this.ws.off(WebSocketEvent.TYPING_STOP, callback);
  }
}

// Export singleton instances
export { websocketService };
export const calendarWebSocket = new CalendarWebSocketService(websocketService);
export const messagingWebSocket = new MessagingWebSocketService(websocketService);