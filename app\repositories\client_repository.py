"""
Client Repository - Handles all database operations for clients
"""
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime
import asyncpg
from loguru import logger

from app.database import Database
from app.domain.client.models import (
    ClientProfile, ClientAddress, ClientEmergencyContact,
    ClientPreferences, ClientLearningNeeds,
    ClientCreate, ClientUpdate, AddressCreate, AddressUpdate,
    EmergencyContactCreate, EmergencyContactUpdate,
    PreferencesCreate, PreferencesUpdate,
    LearningNeedsCreate, LearningNeedsUpdate
)
from app.repositories.base import BaseRepository


class ClientRepository(BaseRepository):
    """Repository for client-related database operations"""
    
    def __init__(self, db: Database):
        super().__init__(db)
    
    # ==================== Client Profile Operations ====================
    
    async def create_client(self, user_id: int, data: ClientCreate) -> ClientProfile:
        """Create a new client profile"""
        async with self.db.transaction():
            query = """
                INSERT INTO client_profiles (
                    user_id, first_name, last_name, email, phone,
                    date_of_birth, gender, profile_photo_url, created_by
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                RETURNING *
            """
            
            row = await self.db.fetchrow(
                query, user_id, data.first_name, data.last_name,
                data.email, data.phone, data.date_of_birth,
                data.gender, data.profile_photo_url, user_id
            )
            
            return await self._get_full_client_profile(row['client_id'])
    
    async def get_client_by_id(self, client_id: int) -> Optional[ClientProfile]:
        """Get client by ID with all relationships"""
        return await self._get_full_client_profile(client_id)
    
    async def get_client_by_user_id(self, user_id: int) -> Optional[ClientProfile]:
        """Get client by user ID"""
        query = """
            SELECT client_id FROM client_profiles 
            WHERE user_id = $1 AND deleted_at IS NULL
        """
        row = await self.db.fetchrow(query, user_id)
        if row:
            return await self._get_full_client_profile(row['client_id'])
        return None
    
    async def update_client(self, client_id: int, data: ClientUpdate) -> Optional[ClientProfile]:
        """Update client profile"""
        update_fields = []
        params = []
        param_count = 1
        
        update_dict = data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            update_fields.append(f"{field} = ${param_count}")
            params.append(value)
            param_count += 1
        
        if not update_fields:
            return await self.get_client_by_id(client_id)
        
        params.append(client_id)
        query = f"""
            UPDATE client_profiles
            SET {', '.join(update_fields)}, updated_at = CURRENT_TIMESTAMP
            WHERE client_id = ${param_count} AND deleted_at IS NULL
            RETURNING *
        """
        
        await self.db.execute(query, *params)
        return await self._get_full_client_profile(client_id)
    
    async def delete_client(self, client_id: int) -> bool:
        """Soft delete client"""
        query = """
            UPDATE client_profiles
            SET deleted_at = CURRENT_TIMESTAMP
            WHERE client_id = $1 AND deleted_at IS NULL
            RETURNING client_id
        """
        result = await self.db.fetchrow(query, client_id)
        return result is not None
    
    async def search_clients(
        self, 
        query: Optional[str] = None,
        is_active: Optional[bool] = None,
        has_dependants: Optional[bool] = None,
        language: Optional[str] = None,
        offset: int = 0,
        limit: int = 50,
        sort_by: str = "created_at",
        sort_order: str = "DESC"
    ) -> Tuple[List[ClientProfile], int]:
        """Search clients with filters and pagination"""
        where_clauses = ["cp.deleted_at IS NULL"]
        params = []
        param_count = 1
        
        if query:
            where_clauses.append(f"""
                (cp.first_name ILIKE ${param_count} OR 
                 cp.last_name ILIKE ${param_count} OR 
                 cp.email ILIKE ${param_count} OR
                 cp.phone LIKE ${param_count})
            """)
            params.append(f"%{query}%")
            param_count += 1
        
        if is_active is not None:
            where_clauses.append(f"u.is_active = ${param_count}")
            params.append(is_active)
            param_count += 1
        
        if has_dependants is not None:
            if has_dependants:
                where_clauses.append("""
                    EXISTS (
                        SELECT 1 FROM dependant_parents dp
                        WHERE dp.client_id = cp.client_id AND dp.deleted_at IS NULL
                    )
                """)
            else:
                where_clauses.append("""
                    NOT EXISTS (
                        SELECT 1 FROM dependant_parents dp
                        WHERE dp.client_id = cp.client_id AND dp.deleted_at IS NULL
                    )
                """)
        
        if language:
            where_clauses.append(f"""
                EXISTS (
                    SELECT 1 FROM client_preferences pref
                    WHERE pref.client_id = cp.client_id AND pref.preferred_language = ${param_count}
                )
            """)
            params.append(language)
            param_count += 1
        
        where_clause = " AND ".join(where_clauses)
        
        # Count query
        count_query = f"""
            SELECT COUNT(*) as total
            FROM client_profiles cp
            JOIN users u ON cp.user_id = u.user_id
            WHERE {where_clause}
        """
        count_result = await self.db.fetchrow(count_query, *params)
        total_count = count_result['total']
        
        # Data query
        allowed_sort_fields = {
            "created_at": "cp.created_at",
            "updated_at": "cp.updated_at",
            "last_name": "cp.last_name",
            "first_name": "cp.first_name",
            "email": "cp.email"
        }
        sort_field = allowed_sort_fields.get(sort_by, "cp.created_at")
        sort_direction = "ASC" if sort_order.upper() == "ASC" else "DESC"
        
        params.extend([limit, offset])
        data_query = f"""
            SELECT cp.client_id
            FROM client_profiles cp
            JOIN users u ON cp.user_id = u.user_id
            WHERE {where_clause}
            ORDER BY {sort_field} {sort_direction}
            LIMIT ${param_count} OFFSET ${param_count + 1}
        """
        
        rows = await self.db.fetch(data_query, *params)
        clients = []
        for row in rows:
            client = await self._get_full_client_profile(row['client_id'])
            if client:
                clients.append(client)
        
        return clients, total_count
    
    # ==================== Address Operations ====================
    
    async def add_address(self, client_id: int, data: AddressCreate) -> ClientAddress:
        """Add address to client"""
        # If this is set as primary, unset other primary addresses
        if data.is_primary:
            await self.db.execute("""
                UPDATE client_addresses 
                SET is_primary = FALSE 
                WHERE client_id = $1 AND deleted_at IS NULL
            """, client_id)
        
        query = """
            INSERT INTO client_addresses (
                client_id, address_type, street_address, unit_number,
                city, province, postal_code, country, is_primary,
                special_instructions
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            RETURNING *
        """
        
        row = await self.db.fetchrow(
            query, client_id, data.address_type, data.street_address,
            data.unit_number, data.city, data.province, data.postal_code,
            data.country, data.is_primary, data.special_instructions
        )
        
        return ClientAddress(**dict(row))
    
    async def update_address(self, address_id: int, data: AddressUpdate) -> Optional[ClientAddress]:
        """Update address"""
        update_fields = []
        params = []
        param_count = 1
        
        update_dict = data.model_dump(exclude_unset=True)
        
        # Handle primary address logic
        if 'is_primary' in update_dict and update_dict['is_primary']:
            # Get client_id first
            client_row = await self.db.fetchrow(
                "SELECT client_id FROM client_addresses WHERE address_id = $1",
                address_id
            )
            if client_row:
                await self.db.execute("""
                    UPDATE client_addresses 
                    SET is_primary = FALSE 
                    WHERE client_id = $1 AND address_id != $2 AND deleted_at IS NULL
                """, client_row['client_id'], address_id)
        
        for field, value in update_dict.items():
            update_fields.append(f"{field} = ${param_count}")
            params.append(value)
            param_count += 1
        
        if not update_fields:
            return await self.get_address_by_id(address_id)
        
        params.append(address_id)
        query = f"""
            UPDATE client_addresses
            SET {', '.join(update_fields)}, updated_at = CURRENT_TIMESTAMP
            WHERE address_id = ${param_count} AND deleted_at IS NULL
            RETURNING *
        """
        
        row = await self.db.fetchrow(query, *params)
        return ClientAddress(**dict(row)) if row else None
    
    async def delete_address(self, address_id: int) -> bool:
        """Delete address"""
        query = """
            UPDATE client_addresses
            SET deleted_at = CURRENT_TIMESTAMP
            WHERE address_id = $1 AND deleted_at IS NULL
            RETURNING address_id
        """
        result = await self.db.fetchrow(query, address_id)
        return result is not None
    
    async def get_address_by_id(self, address_id: int) -> Optional[ClientAddress]:
        """Get address by ID"""
        query = """
            SELECT * FROM client_addresses
            WHERE address_id = $1 AND deleted_at IS NULL
        """
        row = await self.db.fetchrow(query, address_id)
        return ClientAddress(**dict(row)) if row else None
    
    # ==================== Emergency Contact Operations ====================
    
    async def add_emergency_contact(self, client_id: int, data: EmergencyContactCreate) -> ClientEmergencyContact:
        """Add emergency contact"""
        query = """
            INSERT INTO client_emergency_contacts (
                client_id, contact_name, relationship, phone_primary,
                phone_secondary, email, can_pickup_dependants, priority_order
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            RETURNING *
        """
        
        row = await self.db.fetchrow(
            query, client_id, data.contact_name, data.relationship,
            data.phone_primary, data.phone_secondary, data.email,
            data.can_pickup_dependants, data.priority_order
        )
        
        return ClientEmergencyContact(**dict(row))
    
    async def update_emergency_contact(
        self, contact_id: int, data: EmergencyContactUpdate
    ) -> Optional[ClientEmergencyContact]:
        """Update emergency contact"""
        update_fields = []
        params = []
        param_count = 1
        
        update_dict = data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            update_fields.append(f"{field} = ${param_count}")
            params.append(value)
            param_count += 1
        
        if not update_fields:
            return await self.get_emergency_contact_by_id(contact_id)
        
        params.append(contact_id)
        query = f"""
            UPDATE client_emergency_contacts
            SET {', '.join(update_fields)}, updated_at = CURRENT_TIMESTAMP
            WHERE contact_id = ${param_count} AND deleted_at IS NULL
            RETURNING *
        """
        
        row = await self.db.fetchrow(query, *params)
        return ClientEmergencyContact(**dict(row)) if row else None
    
    async def delete_emergency_contact(self, contact_id: int) -> bool:
        """Delete emergency contact"""
        query = """
            UPDATE client_emergency_contacts
            SET deleted_at = CURRENT_TIMESTAMP
            WHERE contact_id = $1 AND deleted_at IS NULL
            RETURNING contact_id
        """
        result = await self.db.fetchrow(query, contact_id)
        return result is not None
    
    async def get_emergency_contact_by_id(self, contact_id: int) -> Optional[ClientEmergencyContact]:
        """Get emergency contact by ID"""
        query = """
            SELECT * FROM client_emergency_contacts
            WHERE contact_id = $1 AND deleted_at IS NULL
        """
        row = await self.db.fetchrow(query, contact_id)
        return ClientEmergencyContact(**dict(row)) if row else None
    
    # ==================== Preferences Operations ====================
    
    async def set_preferences(self, client_id: int, data: PreferencesCreate) -> ClientPreferences:
        """Set or update client preferences"""
        query = """
            INSERT INTO client_preferences (
                client_id, preferred_language, timezone,
                notification_preferences, communication_preferences,
                privacy_settings, theme_preference
            ) VALUES ($1, $2, $3, $4, $5, $6, $7)
            ON CONFLICT (client_id) DO UPDATE SET
                preferred_language = EXCLUDED.preferred_language,
                timezone = EXCLUDED.timezone,
                notification_preferences = EXCLUDED.notification_preferences,
                communication_preferences = EXCLUDED.communication_preferences,
                privacy_settings = EXCLUDED.privacy_settings,
                theme_preference = EXCLUDED.theme_preference,
                updated_at = CURRENT_TIMESTAMP
            RETURNING *
        """
        
        row = await self.db.fetchrow(
            query, client_id, data.preferred_language, data.timezone,
            data.notification_preferences, data.communication_preferences,
            data.privacy_settings, data.theme_preference
        )
        
        return ClientPreferences(**dict(row))
    
    async def get_preferences(self, client_id: int) -> Optional[ClientPreferences]:
        """Get client preferences"""
        query = """
            SELECT * FROM client_preferences
            WHERE client_id = $1
        """
        row = await self.db.fetchrow(query, client_id)
        return ClientPreferences(**dict(row)) if row else None
    
    # ==================== Learning Needs Operations ====================
    
    async def set_learning_needs(self, client_id: int, data: LearningNeedsCreate) -> ClientLearningNeeds:
        """Set or update learning needs"""
        query = """
            INSERT INTO client_learning_needs (
                client_id, subjects_of_interest, learning_goals,
                preferred_session_length, preferred_frequency,
                preferred_days, preferred_times, online_preference,
                in_person_preference, group_session_preference,
                special_requirements, budget_range_min, budget_range_max,
                additional_notes
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
            ON CONFLICT (client_id) DO UPDATE SET
                subjects_of_interest = EXCLUDED.subjects_of_interest,
                learning_goals = EXCLUDED.learning_goals,
                preferred_session_length = EXCLUDED.preferred_session_length,
                preferred_frequency = EXCLUDED.preferred_frequency,
                preferred_days = EXCLUDED.preferred_days,
                preferred_times = EXCLUDED.preferred_times,
                online_preference = EXCLUDED.online_preference,
                in_person_preference = EXCLUDED.in_person_preference,
                group_session_preference = EXCLUDED.group_session_preference,
                special_requirements = EXCLUDED.special_requirements,
                budget_range_min = EXCLUDED.budget_range_min,
                budget_range_max = EXCLUDED.budget_range_max,
                additional_notes = EXCLUDED.additional_notes,
                updated_at = CURRENT_TIMESTAMP
            RETURNING *
        """
        
        row = await self.db.fetchrow(
            query, client_id, data.subjects_of_interest, data.learning_goals,
            data.preferred_session_length, data.preferred_frequency,
            data.preferred_days, data.preferred_times, data.online_preference,
            data.in_person_preference, data.group_session_preference,
            data.special_requirements, data.budget_range_min,
            data.budget_range_max, data.additional_notes
        )
        
        return ClientLearningNeeds(**dict(row))
    
    async def get_learning_needs(self, client_id: int) -> Optional[ClientLearningNeeds]:
        """Get learning needs"""
        query = """
            SELECT * FROM client_learning_needs
            WHERE client_id = $1
        """
        row = await self.db.fetchrow(query, client_id)
        return ClientLearningNeeds(**dict(row)) if row else None
    
    # ==================== Helper Methods ====================
    
    async def _get_full_client_profile(self, client_id: int) -> Optional[ClientProfile]:
        """Get complete client profile with all relationships"""
        # Get base profile
        profile_query = """
            SELECT cp.*, u.is_active, u.created_at as user_created_at
            FROM client_profiles cp
            JOIN users u ON cp.user_id = u.user_id
            WHERE cp.client_id = $1 AND cp.deleted_at IS NULL
        """
        profile_row = await self.db.fetchrow(profile_query, client_id)
        if not profile_row:
            return None
        
        # Get addresses
        addresses_query = """
            SELECT * FROM client_addresses
            WHERE client_id = $1 AND deleted_at IS NULL
            ORDER BY is_primary DESC, created_at DESC
        """
        address_rows = await self.db.fetch(addresses_query, client_id)
        addresses = [ClientAddress(**dict(row)) for row in address_rows]
        
        # Get emergency contacts
        contacts_query = """
            SELECT * FROM client_emergency_contacts
            WHERE client_id = $1 AND deleted_at IS NULL
            ORDER BY priority_order, created_at
        """
        contact_rows = await self.db.fetch(contacts_query, client_id)
        emergency_contacts = [ClientEmergencyContact(**dict(row)) for row in contact_rows]
        
        # Get preferences
        preferences = await self.get_preferences(client_id)
        
        # Get learning needs
        learning_needs = await self.get_learning_needs(client_id)
        
        # Get dependant count
        dependant_query = """
            SELECT COUNT(DISTINCT dp.dependant_id) as count
            FROM dependant_parents dp
            JOIN dependants d ON dp.dependant_id = d.dependant_id
            WHERE dp.client_id = $1 AND dp.deleted_at IS NULL AND d.deleted_at IS NULL
        """
        dependant_result = await self.db.fetchrow(dependant_query, client_id)
        dependant_count = dependant_result['count']
        
        # Get appointment count
        appointment_query = """
            SELECT COUNT(*) as count
            FROM appointments
            WHERE client_id = $1 AND deleted_at IS NULL
        """
        appointment_result = await self.db.fetchrow(appointment_query, client_id)
        appointment_count = appointment_result['count']
        
        # Build complete profile
        profile_data = dict(profile_row)
        profile_data['addresses'] = addresses
        profile_data['emergency_contacts'] = emergency_contacts
        profile_data['preferences'] = preferences
        profile_data['learning_needs'] = learning_needs
        profile_data['dependant_count'] = dependant_count
        profile_data['appointment_count'] = appointment_count
        profile_data['is_active'] = profile_row['is_active']
        
        return ClientProfile(**profile_data)
    
    async def calculate_and_update_completeness(self, client_id: int) -> int:
        """Calculate and update profile completeness"""
        query = """
            SELECT calculate_client_profile_completeness($1) as completeness
        """
        result = await self.db.fetchrow(query, client_id)
        completeness = result['completeness']
        
        # Update the profile
        await self.db.execute("""
            UPDATE client_profiles
            SET profile_completeness = $1, updated_at = CURRENT_TIMESTAMP
            WHERE client_id = $2
        """, completeness, client_id)
        
        return completeness
    
    async def get_clients_by_ids(self, client_ids: List[int]) -> List[ClientProfile]:
        """Get multiple clients by IDs"""
        if not client_ids:
            return []
        
        clients = []
        for client_id in client_ids:
            client = await self._get_full_client_profile(client_id)
            if client:
                clients.append(client)
        
        return clients