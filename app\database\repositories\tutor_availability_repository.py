"""
Tutor availability repository for managing weekly schedules and time-off requests.
"""

import asyncpg
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, date, time, timedelta

from app.database.repositories.base import BaseRepository
from app.core.exceptions import ResourceNotFoundError, BusinessLogicError, DatabaseOperationError
from app.core.timezone import now_est

logger = logging.getLogger(__name__)


class TutorAvailabilityRepository(BaseRepository):
    """Repository for tutor availability management."""
    
    def __init__(self):
        super().__init__(table_name="tutor_availability", id_column="availability_id")
    
    async def find_by_tutor_and_week(
        self,
        conn: asyncpg.Connection,
        tutor_id: int,
        week_start: date
    ) -> List[asyncpg.Record]:
        """
        Find tutor availability for a specific week.
        
        Args:
            conn: Database connection
            tutor_id: Tutor ID
            week_start: Start of the week (Monday)
        
        Returns:
            List of availability records
        """
        query = f"""
            SELECT *
            FROM {self.table_name}
            WHERE tutor_id = $1 
            AND effective_date <= $2
            AND (expiry_date IS NULL OR expiry_date >= $2)
            AND is_recurring = true
            AND deleted_at IS NULL
            ORDER BY day_of_week, start_time
        """
        
        try:
            results = await conn.fetch(query, tutor_id, week_start)
            return results
        except Exception as e:
            logger.error(f"Error finding tutor availability by week: {e}")
            raise
    
    async def find_available_slots(
        self,
        conn: asyncpg.Connection,
        tutor_id: int,
        target_date: date,
        duration_minutes: int = 60
    ) -> List[Dict[str, Any]]:
        """
        Find available time slots for a tutor on a specific date.
        
        Args:
            conn: Database connection
            tutor_id: Tutor ID
            target_date: Date to check availability
            duration_minutes: Required duration in minutes
        
        Returns:
            List of available time slots
        """
        day_of_week = target_date.weekday() + 1  # Convert to 1-7 format
        
        # Get availability windows
        availability_query = f"""
            SELECT start_time, end_time, break_start, break_end, hourly_rate
            FROM {self.table_name}
            WHERE tutor_id = $1 
            AND day_of_week = $2
            AND is_available = true
            AND effective_date <= $3
            AND (expiry_date IS NULL OR expiry_date >= $3)
            AND deleted_at IS NULL
            ORDER BY start_time
        """
        
        # Get existing appointments
        appointments_query = """
            SELECT start_time, end_time
            FROM appointment_sessions
            WHERE tutor_id = $1 
            AND scheduled_date = $2
            AND status NOT IN ('cancelled', 'no_show')
            ORDER BY start_time
        """
        
        # Get approved time-off
        time_off_query = """
            SELECT start_time as off_start, end_time as off_end
            FROM time_off_requests
            WHERE tutor_id = $1
            AND status = 'approved'
            AND start_date <= $2
            AND end_date >= $2
            AND deleted_at IS NULL
        """
        
        try:
            # Fetch all data
            availability_windows = await conn.fetch(availability_query, tutor_id, day_of_week, target_date)
            appointments = await conn.fetch(appointments_query, tutor_id, target_date)
            time_off_periods = await conn.fetch(time_off_query, tutor_id, target_date)
            
            available_slots = []
            
            for window in availability_windows:
                slots = self._calculate_available_slots(
                    window, appointments, time_off_periods, duration_minutes, target_date
                )
                available_slots.extend(slots)
            
            return available_slots
        except Exception as e:
            logger.error(f"Error finding available slots: {e}")
            raise
    
    def _calculate_available_slots(
        self,
        availability_window: asyncpg.Record,
        appointments: List[asyncpg.Record],
        time_off_periods: List[asyncpg.Record],
        duration_minutes: int,
        target_date: date
    ) -> List[Dict[str, Any]]:
        """Calculate available time slots within an availability window."""
        slots = []
        current_time = availability_window['start_time']
        end_time = availability_window['end_time']
        
        # Create list of blocked periods (appointments + time-off + breaks)
        blocked_periods = []
        
        # Add appointments
        for apt in appointments:
            blocked_periods.append((apt['start_time'], apt['end_time']))
        
        # Add time-off periods
        for time_off in time_off_periods:
            # If time_off has specific hours, use them; otherwise block the whole window
            if time_off['off_start'] and time_off['off_end']:
                blocked_periods.append((time_off['off_start'], time_off['off_end']))
            else:
                blocked_periods.append((current_time, end_time))
        
        # Add break periods
        if availability_window['break_start'] and availability_window['break_end']:
            blocked_periods.append((availability_window['break_start'], availability_window['break_end']))
        
        # Sort blocked periods by start time
        blocked_periods.sort(key=lambda x: x[0])
        
        # Find available slots
        slot_duration = timedelta(minutes=duration_minutes)
        
        while current_time < end_time:
            slot_end = (datetime.combine(target_date, current_time) + slot_duration).time()
            
            if slot_end > end_time:
                break
            
            # Check if this slot conflicts with any blocked period
            slot_conflicts = False
            for block_start, block_end in blocked_periods:
                if (current_time < block_end and slot_end > block_start):
                    slot_conflicts = True
                    # Jump to the end of this blocked period
                    current_time = block_end
                    break
            
            if not slot_conflicts:
                slots.append({
                    'start_time': current_time,
                    'end_time': slot_end,
                    'duration_minutes': duration_minutes,
                    'hourly_rate': availability_window['hourly_rate'],
                    'is_available': True
                })
                # Move to next 15-minute slot
                current_time = (datetime.combine(target_date, current_time) + timedelta(minutes=15)).time()
            else:
                # current_time was already moved by the conflict check
                continue
        
        return slots
    
    async def create_weekly_availability(
        self,
        conn: asyncpg.Connection,
        tutor_id: int,
        weekly_schedule: List[Dict[str, Any]],
        effective_date: date,
        expiry_date: Optional[date] = None
    ) -> List[asyncpg.Record]:
        """
        Create a complete weekly availability schedule for a tutor.
        
        Args:
            conn: Database connection
            tutor_id: Tutor ID
            weekly_schedule: List of daily availability data
            effective_date: When this schedule becomes effective
            expiry_date: When this schedule expires (optional)
        
        Returns:
            List of created availability records
        """
        try:
            async with conn.transaction():
                # Archive previous availability (soft delete)
                await self._archive_previous_availability(conn, tutor_id, effective_date)
                
                created_records = []
                now = now_est()
                
                for day_data in weekly_schedule:
                    if not day_data.get('is_active', False):
                        continue
                    
                    for time_slot in day_data.get('time_slots', []):
                        availability_data = {
                            'tutor_id': tutor_id,
                            'day_of_week': day_data['day_of_week'],
                            'start_time': time_slot['start_time'],
                            'end_time': time_slot['end_time'],
                            'is_available': time_slot.get('is_available', True),
                            'hourly_rate': time_slot.get('hourly_rate'),
                            'break_start': time_slot.get('break_start'),
                            'break_end': time_slot.get('break_end'),
                            'effective_date': effective_date,
                            'expiry_date': expiry_date,
                            'is_recurring': True,
                            'notes': time_slot.get('notes'),
                            'created_at': now,
                            'updated_at': now
                        }
                        
                        record = await self.create(conn, availability_data)
                        created_records.append(record)
                
                logger.info(f"Created {len(created_records)} availability records for tutor {tutor_id}")
                return created_records
        except Exception as e:
            logger.error(f"Error creating weekly availability: {e}")
            raise
    
    async def _archive_previous_availability(
        self,
        conn: asyncpg.Connection,
        tutor_id: int,
        effective_date: date
    ) -> None:
        """Archive (soft delete) previous availability records."""
        query = f"""
            UPDATE {self.table_name}
            SET deleted_at = $3
            WHERE tutor_id = $1 
            AND effective_date < $2
            AND deleted_at IS NULL
        """
        
        await conn.execute(query, tutor_id, effective_date, now_est())
    
    async def check_availability_conflict(
        self,
        conn: asyncpg.Connection,
        tutor_id: int,
        day_of_week: int,
        start_time: time,
        end_time: time,
        effective_date: date,
        exclude_availability_id: Optional[int] = None
    ) -> bool:
        """
        Check if a new availability slot conflicts with existing ones.
        
        Args:
            conn: Database connection
            tutor_id: Tutor ID
            day_of_week: Day of week (1-7)
            start_time: Start time
            end_time: End time
            effective_date: Effective date
            exclude_availability_id: ID to exclude from check
        
        Returns:
            True if there's a conflict, False otherwise
        """
        query = f"""
            SELECT COUNT(*)
            FROM {self.table_name}
            WHERE tutor_id = $1 
            AND day_of_week = $2
            AND effective_date <= $3
            AND (expiry_date IS NULL OR expiry_date >= $3)
            AND (
                (start_time < $5 AND end_time > $4) OR
                (start_time < $4 AND end_time > $4) OR
                (start_time < $5 AND end_time > $5) OR
                (start_time >= $4 AND end_time <= $5)
            )
            AND deleted_at IS NULL
        """
        
        params = [tutor_id, day_of_week, effective_date, start_time, end_time]
        
        if exclude_availability_id:
            query += f" AND {self.id_column} != $6"
            params.append(exclude_availability_id)
        
        try:
            count = await conn.fetchval(query, *params)
            return count > 0
        except Exception as e:
            logger.error(f"Error checking availability conflict: {e}")
            raise
    
    async def find_tutor_workload(
        self,
        conn: asyncpg.Connection,
        tutor_id: int,
        start_date: date,
        end_date: date
    ) -> Dict[str, Any]:
        """
        Calculate tutor workload statistics for a date range.
        
        Args:
            conn: Database connection
            tutor_id: Tutor ID
            start_date: Start of period
            end_date: End of period
        
        Returns:
            Workload statistics
        """
        # Get total available hours
        available_hours_query = """
            SELECT 
                day_of_week,
                SUM(
                    EXTRACT(EPOCH FROM (end_time - start_time)) / 3600 -
                    COALESCE(EXTRACT(EPOCH FROM (break_end - break_start)) / 3600, 0)
                ) as daily_hours
            FROM tutor_availability
            WHERE tutor_id = $1
            AND is_available = true
            AND effective_date <= $2
            AND (expiry_date IS NULL OR expiry_date >= $3)
            AND deleted_at IS NULL
            GROUP BY day_of_week
        """
        
        # Get scheduled hours
        scheduled_hours_query = """
            SELECT 
                EXTRACT(DOW FROM scheduled_date) + 1 as day_of_week,
                SUM(EXTRACT(EPOCH FROM (end_time - start_time)) / 3600) as daily_hours
            FROM appointment_sessions
            WHERE tutor_id = $1
            AND scheduled_date BETWEEN $2 AND $3
            AND status NOT IN ('cancelled', 'no_show')
            GROUP BY EXTRACT(DOW FROM scheduled_date) + 1
        """
        
        try:
            available_hours = await conn.fetch(available_hours_query, tutor_id, start_date, end_date)
            scheduled_hours = await conn.fetch(scheduled_hours_query, tutor_id, start_date, end_date)
            
            # Calculate statistics
            total_available = sum(record['daily_hours'] or 0 for record in available_hours)
            total_scheduled = sum(record['daily_hours'] or 0 for record in scheduled_hours)
            
            # Calculate utilization by day
            daily_utilization = {}
            for available in available_hours:
                day = available['day_of_week']
                scheduled = next((s['daily_hours'] for s in scheduled_hours if s['day_of_week'] == day), 0)
                available_hrs = available['daily_hours'] or 0
                
                if available_hrs > 0:
                    daily_utilization[day] = {
                        'available': available_hrs,
                        'scheduled': scheduled or 0,
                        'utilization_pct': (scheduled or 0) / available_hrs * 100
                    }
            
            return {
                'total_available_hours': total_available,
                'total_scheduled_hours': total_scheduled,
                'overall_utilization_pct': (total_scheduled / total_available * 100) if total_available > 0 else 0,
                'daily_utilization': daily_utilization,
                'period_start': start_date,
                'period_end': end_date
            }
        except Exception as e:
            logger.error(f"Error calculating tutor workload: {e}")
            raise


class TimeOffRequestRepository(BaseRepository):
    """Repository for time-off request management."""
    
    def __init__(self):
        super().__init__(table_name="time_off_requests", id_column="request_id")
    
    async def find_pending_requests(
        self,
        conn: asyncpg.Connection,
        tutor_id: Optional[int] = None
    ) -> List[asyncpg.Record]:
        """Find pending time-off requests."""
        query = f"""
            SELECT 
                tor.*,
                ua.first_name || ' ' || ua.last_name as tutor_name
            FROM {self.table_name} tor
            JOIN user_accounts ua ON tor.tutor_id = ua.user_id
            WHERE tor.status = 'pending'
            AND tor.deleted_at IS NULL
        """
        
        params = []
        if tutor_id:
            query += " AND tor.tutor_id = $1"
            params.append(tutor_id)
        
        query += " ORDER BY tor.created_at DESC"
        
        try:
            results = await conn.fetch(query, *params)
            return results
        except Exception as e:
            logger.error(f"Error finding pending time-off requests: {e}")
            raise
    
    async def find_approved_for_period(
        self,
        conn: asyncpg.Connection,
        tutor_id: int,
        start_date: date,
        end_date: date
    ) -> List[asyncpg.Record]:
        """Find approved time-off requests for a period."""
        query = f"""
            SELECT *
            FROM {self.table_name}
            WHERE tutor_id = $1
            AND status = 'approved'
            AND (
                (start_date <= $3 AND end_date >= $2)
            )
            AND deleted_at IS NULL
            ORDER BY start_date
        """
        
        try:
            results = await conn.fetch(query, tutor_id, start_date, end_date)
            return results
        except Exception as e:
            logger.error(f"Error finding approved time-off for period: {e}")
            raise
    
    async def approve_request(
        self,
        conn: asyncpg.Connection,
        request_id: int,
        approved_by: int,
        notes: Optional[str] = None
    ) -> asyncpg.Record:
        """Approve a time-off request."""
        update_data = {
            'status': 'approved',
            'approved_by': approved_by,
            'approved_at': now_est(),
            'updated_at': now_est()
        }
        
        if notes:
            update_data['notes'] = notes
        
        try:
            # Check for appointment conflicts
            request = await self.find_by_id(conn, request_id)
            if not request:
                raise ResourceNotFoundError(f"Time-off request {request_id} not found")
            
            conflicts = await self._find_appointment_conflicts(
                conn, request['tutor_id'], request['start_date'], request['end_date']
            )
            
            if conflicts:
                update_data['affects_appointments'] = True
                update_data['affected_appointment_ids'] = [c['appointment_id'] for c in conflicts]
            
            updated_request = await self.update(conn, request_id, update_data)
            
            logger.info(f"Approved time-off request {request_id} for tutor {request['tutor_id']}")
            return updated_request
        except Exception as e:
            logger.error(f"Error approving time-off request: {e}")
            raise
    
    async def _find_appointment_conflicts(
        self,
        conn: asyncpg.Connection,
        tutor_id: int,
        start_date: date,
        end_date: date
    ) -> List[asyncpg.Record]:
        """Find appointments that conflict with time-off request."""
        query = """
            SELECT appointment_id, scheduled_date, start_time, end_time
            FROM appointment_sessions
            WHERE tutor_id = $1
            AND scheduled_date BETWEEN $2 AND $3
            AND status NOT IN ('cancelled', 'completed')
        """
        
        try:
            results = await conn.fetch(query, tutor_id, start_date, end_date)
            return results
        except Exception as e:
            logger.error(f"Error finding appointment conflicts: {e}")
            raise