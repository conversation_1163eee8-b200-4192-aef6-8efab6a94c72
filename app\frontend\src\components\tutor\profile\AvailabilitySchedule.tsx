import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import Button from '../../common/Button';
import { Select } from '../../common/Select';
import { 
  Calendar,
  Clock,
  Plus,
  Trash2,
  Save,
  AlertTriangle
} from 'lucide-react';
import api from '../../../services/api';
import toast from 'react-hot-toast';

interface AvailabilityScheduleProps {
  tutorId: number;
}

interface TimeSlot {
  availability_id?: number;
  day_of_week: number;
  start_time: string;
  end_time: string;
  is_active: boolean;
}

const DAYS_OF_WEEK = [
  { value: 0, label: 'Sunday' },
  { value: 1, label: 'Monday' },
  { value: 2, label: 'Tuesday' },
  { value: 3, label: 'Wednesday' },
  { value: 4, label: 'Thursday' },
  { value: 5, label: 'Friday' },
  { value: 6, label: 'Saturday' }
];

export const AvailabilitySchedule: React.FC<AvailabilityScheduleProps> = ({
  tutorId
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [availability, setAvailability] = useState<TimeSlot[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    fetchAvailability();
  }, [tutorId]);

  const fetchAvailability = async () => {
    try {
      setLoading(true);
      const response = await api.get(`/api/v1/tutors/${tutorId}/availability`);
      setAvailability(response.data || []);
    } catch (error) {
      console.error('Error fetching availability:', error);
      toast.error(t('tutor.availability.fetchError'));
    } finally {
      setLoading(false);
    }
  };

  const generateTimeOptions = () => {
    const options = [];
    for (let hour = 6; hour <= 23; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const time = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
        const label = new Date(`2000-01-01T${time}`).toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true
        });
        options.push({ value: time, label });
      }
    }
    return options;
  };

  const timeOptions = generateTimeOptions();

  const addTimeSlot = (dayOfWeek: number) => {
    const newSlot: TimeSlot = {
      day_of_week: dayOfWeek,
      start_time: '09:00',
      end_time: '17:00',
      is_active: true
    };
    setAvailability([...availability, newSlot]);
  };

  const updateTimeSlot = (index: number, field: keyof TimeSlot, value: any) => {
    const updated = [...availability];
    updated[index] = { ...updated[index], [field]: value };
    setAvailability(updated);
  };

  const removeTimeSlot = (index: number) => {
    setAvailability(availability.filter((_, i) => i !== index));
  };

  const validateTimeSlots = () => {
    const newErrors: Record<string, string> = {};
    
    // Check for overlapping time slots
    for (let i = 0; i < availability.length; i++) {
      for (let j = i + 1; j < availability.length; j++) {
        if (availability[i].day_of_week === availability[j].day_of_week) {
          const start1 = availability[i].start_time;
          const end1 = availability[i].end_time;
          const start2 = availability[j].start_time;
          const end2 = availability[j].end_time;
          
          if ((start1 < end2 && end1 > start2) || (start2 < end1 && end2 > start1)) {
            newErrors[`overlap_${i}_${j}`] = t('tutor.availability.errors.overlapping');
          }
        }
      }
      
      // Check that end time is after start time
      if (availability[i].start_time >= availability[i].end_time) {
        newErrors[`time_${i}`] = t('tutor.availability.errors.invalidTimeRange');
      }
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateTimeSlots()) {
      toast.error(t('tutor.availability.errors.validation'));
      return;
    }
    
    setSaving(true);
    try {
      await api.put(`/api/v1/tutors/${tutorId}/availability`, {
        availability: availability
      });
      toast.success(t('tutor.availability.saveSuccess'));
      fetchAvailability(); // Refresh with server data
    } catch (error) {
      console.error('Error saving availability:', error);
      toast.error(t('tutor.availability.saveError'));
    } finally {
      setSaving(false);
    }
  };

  const getAvailabilityByDay = (day: number) => {
    return availability
      .filter(slot => slot.day_of_week === day)
      .sort((a, b) => a.start_time.localeCompare(b.start_time));
  };

  const hasAvailability = (day: number) => {
    return availability.some(slot => slot.day_of_week === day);
  };

  if (loading) {
    return (
      <div className="p-6 text-center">
        <Clock className="w-8 h-8 text-gray-400 mx-auto mb-2 animate-spin" />
        <p className="text-text-secondary">{t('common.loading')}</p>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-text-primary mb-2">
          {t('tutor.availability.title')}
        </h3>
        <p className="text-sm text-text-secondary">
          {t('tutor.availability.subtitle')}
        </p>
      </div>

      {Object.keys(errors).length > 0 && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-start gap-2">
            <AlertTriangle className="w-5 h-5 text-red-600 flex-shrink-0 mt-0.5" />
            <div>
              <p className="text-sm font-medium text-red-800">
                {t('tutor.availability.errors.title')}
              </p>
              <ul className="mt-1 text-sm text-red-700 list-disc list-inside">
                {Object.values(errors).map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}

      <div className="space-y-4">
        {DAYS_OF_WEEK.map((day) => (
          <div key={day.value} className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-sm font-medium text-text-primary">
                {t(`common.days.${day.label.toLowerCase()}`)}
              </h4>
              <Button
                type="button"
                variant="secondary"
                size="sm"
                onClick={() => addTimeSlot(day.value)}
                leftIcon={<Plus className="w-4 h-4" />}
              >
                {t('tutor.availability.addSlot')}
              </Button>
            </div>

            {hasAvailability(day.value) ? (
              <div className="space-y-2">
                {getAvailabilityByDay(day.value).map((slot, index) => {
                  const globalIndex = availability.findIndex(s => s === slot);
                  return (
                    <div key={index} className="flex items-center gap-3">
                      <Select
                        value={slot.start_time}
                        onChange={(value) => updateTimeSlot(globalIndex, 'start_time', value)}
                        options={timeOptions}
                        placeholder={t('tutor.availability.startTime')}
                        leftIcon={<Clock className="w-4 h-4" />}
                      />
                      <span className="text-text-secondary">-</span>
                      <Select
                        value={slot.end_time}
                        onChange={(value) => updateTimeSlot(globalIndex, 'end_time', value)}
                        options={timeOptions}
                        placeholder={t('tutor.availability.endTime')}
                        leftIcon={<Clock className="w-4 h-4" />}
                      />
                      <label className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          checked={slot.is_active}
                          onChange={(e) => updateTimeSlot(globalIndex, 'is_active', e.target.checked)}
                          className="w-4 h-4 text-accent-red focus:ring-accent-red/20 border-gray-300 rounded"
                        />
                        <span className="text-sm text-text-secondary">
                          {t('tutor.availability.active')}
                        </span>
                      </label>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeTimeSlot(globalIndex)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  );
                })}
              </div>
            ) : (
              <p className="text-sm text-text-secondary text-center py-2">
                {t('tutor.availability.noSlots')}
              </p>
            )}
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h4 className="text-sm font-medium text-text-primary mb-3">
          {t('tutor.availability.quickActions')}
        </h4>
        <div className="flex flex-wrap gap-2">
          <Button
            type="button"
            variant="secondary"
            size="sm"
            onClick={() => {
              // Set standard business hours
              const businessHours: TimeSlot[] = [];
              for (let day = 1; day <= 5; day++) {
                businessHours.push({
                  day_of_week: day,
                  start_time: '09:00',
                  end_time: '17:00',
                  is_active: true
                });
              }
              setAvailability(businessHours);
            }}
          >
            {t('tutor.availability.setBusinessHours')}
          </Button>
          <Button
            type="button"
            variant="secondary"
            size="sm"
            onClick={() => {
              // Set evening hours
              const eveningHours: TimeSlot[] = [];
              for (let day = 1; day <= 5; day++) {
                eveningHours.push({
                  day_of_week: day,
                  start_time: '18:00',
                  end_time: '21:00',
                  is_active: true
                });
              }
              setAvailability(eveningHours);
            }}
          >
            {t('tutor.availability.setEveningHours')}
          </Button>
          <Button
            type="button"
            variant="secondary"
            size="sm"
            onClick={() => setAvailability([])}
          >
            {t('tutor.availability.clearAll')}
          </Button>
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-end pt-6 border-t border-gray-200 mt-6">
        <Button
          onClick={handleSave}
          loading={saving}
          leftIcon={<Save className="w-4 h-4" />}
        >
          {t('common.save')}
        </Button>
      </div>
    </div>
  );
};