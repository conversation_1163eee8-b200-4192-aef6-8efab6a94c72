"""
Repository for notification log data access.
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from asyncpg import Connection
import json

from app.database.repositories.base import BaseRepository
from app.models.notification_models import (
    NotificationType, NotificationChannel,
    NotificationLog
)
from app.core.exceptions import DatabaseOperationError, ResourceNotFoundError
from app.core.timezone import now_est

logger = logging.getLogger(__name__)


class NotificationRepository(BaseRepository):
    """Repository for notification logging operations."""
    
    def __init__(self):
        """Initialize notification repository."""
        super().__init__(table_name="notification_logs", id_column="log_id")
    
    async def create_notification_log(
        self,
        user_id: Optional[int],
        notification_type: NotificationType,
        channel: NotificationChannel,
        reference_id: int,
        title: Optional[str] = None,
        preview: str = "",
        metadata: Optional[Dict[str, Any]] = None,
        conn: Optional[Connection] = None
    ) -> NotificationLog:
        """Create a notification log entry."""
        query = """
            INSERT INTO notification_logs (
                user_id, notification_type, channel,
                reference_id, title, preview,
                status, metadata, created_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            RETURNING *
        """
        
        now = now_est()
        row = await self.fetch_one(
            conn,
            query,
            user_id,
            notification_type.value,
            channel.value,
            reference_id,
            title,
            preview,
            'pending',
            json.dumps(metadata) if metadata else None,
            now
        )
        
        return NotificationLog(**dict(row)) if row else None
    
    async def update_notification_status(
        self,
        log_id: int,
        status: str,
        sent_at: Optional[datetime] = None,
        delivered_at: Optional[datetime] = None,
        read_at: Optional[datetime] = None,
        error_message: Optional[str] = None,
        conn: Optional[Connection] = None
    ) -> bool:
        """Update notification status."""
        update_fields = ["status = $1"]
        values = [status]
        param_count = 2
        
        if sent_at:
            update_fields.append(f"sent_at = ${param_count}")
            values.append(sent_at)
            param_count += 1
        
        if delivered_at:
            update_fields.append(f"delivered_at = ${param_count}")
            values.append(delivered_at)
            param_count += 1
        
        if read_at:
            update_fields.append(f"read_at = ${param_count}")
            values.append(read_at)
            param_count += 1
        
        if error_message:
            # Store error in metadata
            update_fields.append(f"""
                metadata = COALESCE(metadata, '{{}}'::jsonb) || 
                ${{param_count}}::jsonb
            """)
            values.append(json.dumps({"error_message": error_message}))
            param_count += 1
        
        values.append(log_id)
        
        query = f"""
            UPDATE notification_logs
            SET {', '.join(update_fields)}
            WHERE log_id = ${param_count}
            RETURNING log_id
        """
        
        result = await self.fetch_one(conn, query, *values)
        return result is not None
    
    async def get_user_notifications(
        self,
        user_id: int,
        limit: int = 50,
        offset: int = 0,
        unread_only: bool = False,
        notification_type: Optional[NotificationType] = None,
        conn: Optional[Connection] = None
    ) -> List[NotificationLog]:
        """Get notifications for a user."""
        query = """
            SELECT * FROM notification_logs
            WHERE user_id = $1
        """
        
        params = [user_id]
        param_count = 2
        
        if unread_only:
            query += " AND read_at IS NULL"
        
        if notification_type:
            query += f" AND notification_type = ${param_count}"
            params.append(notification_type.value)
            param_count += 1
        
        query += f" ORDER BY created_at DESC LIMIT ${param_count} OFFSET ${param_count + 1}"
        params.extend([limit, offset])
        
        rows = await self.fetch_many(conn, query, *params)
        return [NotificationLog(**dict(row)) for row in rows]
    
    async def mark_as_read(
        self,
        log_ids: List[int],
        conn: Optional[Connection] = None
    ) -> int:
        """Mark notifications as read."""
        if not log_ids:
            return 0
        
        # Create parameter placeholders
        placeholders = ', '.join(f'${i+2}' for i in range(len(log_ids)))
        
        query = f"""
            UPDATE notification_logs
            SET read_at = $1
            WHERE log_id IN ({placeholders})
            AND read_at IS NULL
            RETURNING log_id
        """
        
        rows = await self.fetch_many(conn, query, now_est(), *log_ids)
        return len(rows)
    
    async def get_notification_statistics(
        self,
        user_id: Optional[int] = None,
        date_range: Optional[tuple[datetime, datetime]] = None,
        conn: Optional[Connection] = None
    ) -> Dict[str, Any]:
        """Get notification statistics."""
        base_query = """
            SELECT 
                notification_type,
                channel,
                status,
                COUNT(*) as count,
                COUNT(CASE WHEN read_at IS NOT NULL THEN 1 END) as read_count
            FROM notification_logs
            WHERE 1=1
        """
        
        params = []
        param_count = 1
        
        if user_id:
            base_query += f" AND user_id = ${param_count}"
            params.append(user_id)
            param_count += 1
        
        if date_range:
            base_query += f" AND created_at BETWEEN ${param_count} AND ${param_count + 1}"
            params.extend(date_range)
            param_count += 2
        
        base_query += " GROUP BY notification_type, channel, status"
        
        rows = await self.fetch_many(conn, base_query, *params)
        
        # Aggregate statistics
        stats = {
            'total': 0,
            'by_type': {},
            'by_channel': {},
            'by_status': {},
            'read_rate': 0.0,
            'unread_count': 0
        }
        
        total_with_read_status = 0
        total_read = 0
        
        for row in rows:
            count = row['count']
            read_count = row['read_count']
            notif_type = row['notification_type']
            channel = row['channel']
            status = row['status']
            
            stats['total'] += count
            
            # By type
            if notif_type not in stats['by_type']:
                stats['by_type'][notif_type] = {'count': 0, 'read': 0}
            stats['by_type'][notif_type]['count'] += count
            stats['by_type'][notif_type]['read'] += read_count
            
            # By channel
            if channel not in stats['by_channel']:
                stats['by_channel'][channel] = {'count': 0, 'read': 0}
            stats['by_channel'][channel]['count'] += count
            stats['by_channel'][channel]['read'] += read_count
            
            # By status
            if status not in stats['by_status']:
                stats['by_status'][status] = 0
            stats['by_status'][status] += count
            
            # For read rate calculation
            if status == 'delivered':
                total_with_read_status += count
                total_read += read_count
        
        # Calculate read rate
        if total_with_read_status > 0:
            stats['read_rate'] = (total_read / total_with_read_status) * 100
        
        stats['unread_count'] = total_with_read_status - total_read
        
        return stats
    
    async def get_recent_notifications(
        self,
        hours: int = 24,
        conn: Optional[Connection] = None
    ) -> List[Dict[str, Any]]:
        """Get recent notifications."""
        since = now_est() - timedelta(hours=hours)
        
        query = """
            SELECT 
                n.*,
                u.email,
                u.username
            FROM notification_logs n
            LEFT JOIN user_accounts u ON n.user_id = u.user_id
            WHERE n.created_at >= $1
            ORDER BY n.created_at DESC
            LIMIT 100
        """
        
        rows = await self.fetch_many(conn, query, since)
        return [dict(row) for row in rows]
    
    async def cleanup_old_logs(
        self,
        days_to_keep: int = 90,
        conn: Optional[Connection] = None
    ) -> int:
        """Clean up old notification logs."""
        cutoff_date = now_est() - timedelta(days=days_to_keep)
        
        query = """
            DELETE FROM notification_logs
            WHERE created_at < $1
            RETURNING log_id
        """
        
        rows = await self.fetch_many(conn, query, cutoff_date)
        return len(rows)