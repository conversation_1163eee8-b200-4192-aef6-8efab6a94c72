import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  DollarSign, Settings, Plus, Edit, Trash2, Save, X, 
  TrendingUp, Calendar, Users, AlertCircle, CheckCircle,
  Package, Percent, Clock, Globe, Building, BookOpen
} from 'lucide-react';
import { Card } from '../../components/common/Card';
import Button from '../../components/common/Button';
import { Badge } from '../../components/common/Badge';
import { Input } from '../../components/common/Input';
import { Select } from '../../components/common/Select';
import { Modal } from '../../components/common/Modal';
import { Switch } from '../../components/common/Switch';
import toast from 'react-hot-toast';

interface ServicePricing {
  pricing_id: number;
  service_id: number;
  service_name: string;
  service_type: 'online' | 'in_person' | 'library' | 'hybrid';
  subject_area: 'mathematics' | 'science' | 'french' | 'english' | 'other';
  client_rate: number;
  tutor_base_rate: number;
  platform_commission: number;
  min_session_duration: number;
  max_session_duration: number;
  is_active: boolean;
  is_package_eligible: boolean;
  package_discount?: number;
  created_at: string;
  updated_at: string;
}

interface PricingRule {
  rule_id: number;
  rule_name: string;
  rule_type: 'surge' | 'discount' | 'bonus' | 'penalty';
  condition_type: 'time_based' | 'volume_based' | 'performance_based' | 'location_based';
  condition_value: string;
  adjustment_type: 'percentage' | 'fixed';
  adjustment_value: number;
  is_active: boolean;
  priority: number;
  description: string;
}

interface PricingSummary {
  totalServices: number;
  activeServices: number;
  avgClientRate: number;
  avgTutorRate: number;
  avgCommission: number;
  totalRules: number;
}

const ServicePricingPage: React.FC = () => {
  const { t } = useTranslation();
  const [services, setServices] = useState<ServicePricing[]>([]);
  const [pricingRules, setPricingRules] = useState<PricingRule[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedService, setSelectedService] = useState<ServicePricing | null>(null);
  const [showServiceModal, setShowServiceModal] = useState(false);
  const [showRuleModal, setShowRuleModal] = useState(false);
  const [selectedRule, setSelectedRule] = useState<PricingRule | null>(null);
  const [editMode, setEditMode] = useState(false);
  const [filterSubject, setFilterSubject] = useState('all');
  const [filterType, setFilterType] = useState('all');
  const [summary, setSummary] = useState<PricingSummary>({
    totalServices: 24,
    activeServices: 20,
    avgClientRate: 110,
    avgTutorRate: 85,
    avgCommission: 22.7,
    totalRules: 8
  });

  // Mock service data
  useEffect(() => {
    const mockServices: ServicePricing[] = [
      {
        pricing_id: 1,
        service_id: 1,
        service_name: 'Mathematics Tutoring - Online',
        service_type: 'online',
        subject_area: 'mathematics',
        client_rate: 110,
        tutor_base_rate: 85,
        platform_commission: 25,
        min_session_duration: 60,
        max_session_duration: 180,
        is_active: true,
        is_package_eligible: true,
        package_discount: 10,
        created_at: '2024-01-15',
        updated_at: '2024-11-20'
      },
      {
        pricing_id: 2,
        service_id: 2,
        service_name: 'Mathematics Tutoring - In Person',
        service_type: 'in_person',
        subject_area: 'mathematics',
        client_rate: 130,
        tutor_base_rate: 100,
        platform_commission: 30,
        min_session_duration: 60,
        max_session_duration: 120,
        is_active: true,
        is_package_eligible: true,
        package_discount: 10,
        created_at: '2024-01-15',
        updated_at: '2024-11-20'
      },
      {
        pricing_id: 3,
        service_id: 3,
        service_name: 'French Reading - Online',
        service_type: 'online',
        subject_area: 'french',
        client_rate: 100,
        tutor_base_rate: 75,
        platform_commission: 25,
        min_session_duration: 45,
        max_session_duration: 90,
        is_active: true,
        is_package_eligible: true,
        package_discount: 15,
        created_at: '2024-01-20',
        updated_at: '2024-11-18'
      },
      {
        pricing_id: 4,
        service_id: 4,
        service_name: 'Science Lab - Library',
        service_type: 'library',
        subject_area: 'science',
        client_rate: 120,
        tutor_base_rate: 90,
        platform_commission: 30,
        min_session_duration: 90,
        max_session_duration: 180,
        is_active: true,
        is_package_eligible: false,
        created_at: '2024-02-01',
        updated_at: '2024-11-15'
      }
    ];
    
    const mockRules: PricingRule[] = [
      {
        rule_id: 1,
        rule_name: 'Weekend Surge Pricing',
        rule_type: 'surge',
        condition_type: 'time_based',
        condition_value: 'weekend',
        adjustment_type: 'percentage',
        adjustment_value: 15,
        is_active: true,
        priority: 1,
        description: 'Apply 15% surge pricing for weekend sessions'
      },
      {
        rule_id: 2,
        rule_name: 'High Volume Discount',
        rule_type: 'discount',
        condition_type: 'volume_based',
        condition_value: '20+ sessions/month',
        adjustment_type: 'percentage',
        adjustment_value: 5,
        is_active: true,
        priority: 2,
        description: 'Give 5% discount to clients with 20+ sessions per month'
      },
      {
        rule_id: 3,
        rule_name: 'Top Tutor Bonus',
        rule_type: 'bonus',
        condition_type: 'performance_based',
        condition_value: '4.8+ rating',
        adjustment_type: 'fixed',
        adjustment_value: 10,
        is_active: true,
        priority: 3,
        description: 'Add $10 bonus per session for tutors with 4.8+ rating'
      }
    ];
    
    setServices(mockServices);
    setPricingRules(mockRules);
  }, []);

  const filteredServices = services.filter(service => {
    const matchesSubject = filterSubject === 'all' || service.subject_area === filterSubject;
    const matchesType = filterType === 'all' || service.service_type === filterType;
    return matchesSubject && matchesType;
  });

  const handleSaveService = async (serviceData: Partial<ServicePricing>) => {
    setLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    if (editMode && selectedService) {
      setServices(services.map(service => 
        service.pricing_id === selectedService.pricing_id 
          ? { ...service, ...serviceData, updated_at: new Date().toISOString() }
          : service
      ));
      toast.success('Service pricing updated successfully');
    } else {
      const newService: ServicePricing = {
        pricing_id: services.length + 1,
        service_id: services.length + 1,
        ...serviceData as ServicePricing,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      setServices([...services, newService]);
      toast.success('Service pricing created successfully');
    }
    
    setShowServiceModal(false);
    setSelectedService(null);
    setEditMode(false);
    setLoading(false);
  };

  const handleDeleteService = async (serviceId: number) => {
    if (!confirm('Are you sure you want to delete this service pricing?')) return;
    
    setLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    setServices(services.filter(service => service.pricing_id !== serviceId));
    toast.success('Service pricing deleted successfully');
    setLoading(false);
  };

  const handleToggleServiceStatus = async (serviceId: number) => {
    setServices(services.map(service => 
      service.pricing_id === serviceId 
        ? { ...service, is_active: !service.is_active }
        : service
    ));
    toast.success('Service status updated');
  };

  const getServiceTypeIcon = (type: string) => {
    switch (type) {
      case 'online':
        return <Globe className="w-4 h-4" />;
      case 'in_person':
        return <Users className="w-4 h-4" />;
      case 'library':
        return <Building className="w-4 h-4" />;
      case 'hybrid':
        return <Package className="w-4 h-4" />;
      default:
        return <BookOpen className="w-4 h-4" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD',
    }).format(amount);
  };

  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Service Pricing Management</h1>
          <p className="text-gray-600 mt-1">
            Configure platform pricing, commissions, and dynamic pricing rules
          </p>
        </div>
        <div className="flex gap-3">
          <Button
            variant="secondary"
            onClick={() => {
              setSelectedRule(null);
              setShowRuleModal(true);
            }}
          >
            <Settings className="w-4 h-4 mr-2" />
            Pricing Rules
          </Button>
          <Button
            variant="primary"
            onClick={() => {
              setSelectedService(null);
              setEditMode(false);
              setShowServiceModal(true);
            }}
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Service Pricing
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <Package className="w-8 h-8 text-blue-600" />
          </div>
          <p className="text-2xl font-bold">{summary.totalServices}</p>
          <p className="text-sm text-gray-600 mt-1">Total Services</p>
          <p className="text-xs text-green-600 mt-2">
            {summary.activeServices} active
          </p>
        </Card>
        
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <DollarSign className="w-8 h-8 text-green-600" />
          </div>
          <p className="text-2xl font-bold">{formatCurrency(summary.avgClientRate)}</p>
          <p className="text-sm text-gray-600 mt-1">Avg Client Rate</p>
          <p className="text-xs text-gray-500 mt-2">per session</p>
        </Card>
        
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <Users className="w-8 h-8 text-purple-600" />
          </div>
          <p className="text-2xl font-bold">{formatCurrency(summary.avgTutorRate)}</p>
          <p className="text-sm text-gray-600 mt-1">Avg Tutor Rate</p>
          <p className="text-xs text-gray-500 mt-2">per session</p>
        </Card>
        
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <Percent className="w-8 h-8 text-red-600" />
          </div>
          <p className="text-2xl font-bold">{summary.avgCommission}%</p>
          <p className="text-sm text-gray-600 mt-1">Avg Commission</p>
          <p className="text-xs text-gray-500 mt-2">platform revenue</p>
        </Card>
        
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <Settings className="w-8 h-8 text-gray-600" />
          </div>
          <p className="text-2xl font-bold">{summary.totalRules}</p>
          <p className="text-sm text-gray-600 mt-1">Pricing Rules</p>
          <p className="text-xs text-blue-600 mt-2">
            {pricingRules.filter(r => r.is_active).length} active
          </p>
        </Card>
      </div>

      {/* Filters */}
      <Card className="p-4">
        <div className="flex flex-col lg:flex-row gap-4 items-center">
          <div className="flex gap-3 flex-1">
            <Select
              value={filterSubject}
              onChange={(e) => setFilterSubject(e.target.value)}
              options={[
                { value: 'all', label: 'All Subjects' },
                { value: 'mathematics', label: 'Mathematics' },
                { value: 'science', label: 'Science' },
                { value: 'french', label: 'French' },
                { value: 'english', label: 'English' },
                { value: 'other', label: 'Other' }
              ]}
              className="w-48"
            />
            <Select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              options={[
                { value: 'all', label: 'All Types' },
                { value: 'online', label: 'Online' },
                { value: 'in_person', label: 'In Person' },
                { value: 'library', label: 'Library' },
                { value: 'hybrid', label: 'Hybrid' }
              ]}
              className="w-48"
            />
          </div>
          
          <div className="text-sm text-gray-600">
            Showing {filteredServices.length} services
          </div>
        </div>
      </Card>

      {/* Services List */}
      <Card className="overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Service
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Client Rate
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tutor Rate
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Commission
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Duration
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Package
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredServices.map((service) => (
                <tr key={service.pricing_id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <p className="text-sm font-medium text-gray-900">{service.service_name}</p>
                      <p className="text-xs text-gray-500 capitalize">{service.subject_area}</p>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      {getServiceTypeIcon(service.service_type)}
                      <span className="text-sm text-gray-900 capitalize">
                        {service.service_type.replace('_', ' ')}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <p className="text-sm font-medium text-gray-900">{formatCurrency(service.client_rate)}</p>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <p className="text-sm font-medium text-gray-900">{formatCurrency(service.tutor_base_rate)}</p>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      <div className="w-16 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-red-600 h-2 rounded-full"
                          style={{ width: `${(service.platform_commission / service.client_rate) * 100}%` }}
                        ></div>
                      </div>
                      <span className="text-sm text-gray-900">{service.platform_commission}%</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {service.min_session_duration}-{service.max_session_duration} min
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {service.is_package_eligible ? (
                      <Badge className="bg-blue-100 text-blue-700">
                        {service.package_discount}% off
                      </Badge>
                    ) : (
                      <span className="text-sm text-gray-400">N/A</span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Switch
                      checked={service.is_active}
                      onChange={() => handleToggleServiceStatus(service.pricing_id)}
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setSelectedService(service);
                          setEditMode(true);
                          setShowServiceModal(true);
                        }}
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteService(service.pricing_id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>

      {/* Active Pricing Rules */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="font-semibold text-gray-900">Active Pricing Rules</h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowRuleModal(true)}
          >
            View All
          </Button>
        </div>
        <div className="space-y-3">
          {pricingRules.filter(rule => rule.is_active).map((rule) => (
            <div key={rule.rule_id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                {rule.rule_type === 'surge' && <TrendingUp className="w-5 h-5 text-red-600" />}
                {rule.rule_type === 'discount' && <Percent className="w-5 h-5 text-green-600" />}
                {rule.rule_type === 'bonus' && <DollarSign className="w-5 h-5 text-blue-600" />}
                <div>
                  <p className="font-medium text-sm">{rule.rule_name}</p>
                  <p className="text-xs text-gray-600">{rule.description}</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Badge className={`
                  ${rule.adjustment_type === 'percentage' ? 'bg-purple-100 text-purple-700' : 'bg-blue-100 text-blue-700'}
                `}>
                  {rule.adjustment_type === 'percentage' ? `${rule.adjustment_value}%` : formatCurrency(rule.adjustment_value)}
                </Badge>
                <Badge className="bg-gray-100 text-gray-700">Priority {rule.priority}</Badge>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Service Pricing Modal */}
      <Modal
        isOpen={showServiceModal}
        onClose={() => {
          setShowServiceModal(false);
          setSelectedService(null);
          setEditMode(false);
        }}
        title={editMode ? 'Edit Service Pricing' : 'Add Service Pricing'}
        size="lg"
      >
        <ServicePricingForm
          service={selectedService}
          onSave={handleSaveService}
          onCancel={() => {
            setShowServiceModal(false);
            setSelectedService(null);
            setEditMode(false);
          }}
          loading={loading}
        />
      </Modal>

      {/* Pricing Rules Modal */}
      <Modal
        isOpen={showRuleModal}
        onClose={() => setShowRuleModal(false)}
        title="Pricing Rules Management"
        size="xl"
      >
        <PricingRulesManager
          rules={pricingRules}
          onClose={() => setShowRuleModal(false)}
        />
      </Modal>
    </div>
  );
};

// Service Pricing Form Component
interface ServicePricingFormProps {
  service: ServicePricing | null;
  onSave: (data: Partial<ServicePricing>) => void;
  onCancel: () => void;
  loading: boolean;
}

const ServicePricingForm: React.FC<ServicePricingFormProps> = ({ service, onSave, onCancel, loading }) => {
  const [formData, setFormData] = useState({
    service_name: service?.service_name || '',
    service_type: service?.service_type || 'online',
    subject_area: service?.subject_area || 'mathematics',
    client_rate: service?.client_rate || 100,
    tutor_base_rate: service?.tutor_base_rate || 75,
    platform_commission: service?.platform_commission || 25,
    min_session_duration: service?.min_session_duration || 60,
    max_session_duration: service?.max_session_duration || 120,
    is_active: service?.is_active ?? true,
    is_package_eligible: service?.is_package_eligible ?? true,
    package_discount: service?.package_discount || 10
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate commission
    const expectedCommission = formData.client_rate - formData.tutor_base_rate;
    if (Math.abs(expectedCommission - formData.platform_commission) > 0.01) {
      toast.error('Commission must equal the difference between client rate and tutor rate');
      return;
    }
    
    onSave(formData);
  };

  // Auto-calculate commission
  useEffect(() => {
    const commission = formData.client_rate - formData.tutor_base_rate;
    setFormData(prev => ({ ...prev, platform_commission: commission }));
  }, [formData.client_rate, formData.tutor_base_rate]);

  return (
    <form onSubmit={handleSubmit} className="space-y-6 p-6">
      <div className="grid grid-cols-2 gap-4">
        <div className="col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Service Name
          </label>
          <Input
            value={formData.service_name}
            onChange={(e) => setFormData({ ...formData, service_name: e.target.value })}
            placeholder="e.g., Mathematics Tutoring - Online"
            required
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Subject Area
          </label>
          <Select
            value={formData.subject_area}
            onChange={(e) => setFormData({ ...formData, subject_area: e.target.value as any })}
            options={[
              { value: 'mathematics', label: 'Mathematics' },
              { value: 'science', label: 'Science' },
              { value: 'french', label: 'French' },
              { value: 'english', label: 'English' },
              { value: 'other', label: 'Other' }
            ]}
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Service Type
          </label>
          <Select
            value={formData.service_type}
            onChange={(e) => setFormData({ ...formData, service_type: e.target.value as any })}
            options={[
              { value: 'online', label: 'Online' },
              { value: 'in_person', label: 'In Person' },
              { value: 'library', label: 'Library' },
              { value: 'hybrid', label: 'Hybrid' }
            ]}
          />
        </div>
      </div>

      <div className="border-t pt-6">
        <h3 className="font-medium text-gray-900 mb-4">Pricing Structure</h3>
        <div className="grid grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Client Rate (CAD)
            </label>
            <Input
              type="number"
              value={formData.client_rate}
              onChange={(e) => setFormData({ ...formData, client_rate: Number(e.target.value) })}
              min="0"
              step="5"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Tutor Base Rate (CAD)
            </label>
            <Input
              type="number"
              value={formData.tutor_base_rate}
              onChange={(e) => setFormData({ ...formData, tutor_base_rate: Number(e.target.value) })}
              min="0"
              step="5"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Platform Commission (CAD)
            </label>
            <Input
              type="number"
              value={formData.platform_commission}
              disabled
              className="bg-gray-100"
            />
            <p className="text-xs text-gray-500 mt-1">Auto-calculated</p>
          </div>
        </div>
      </div>

      <div className="border-t pt-6">
        <h3 className="font-medium text-gray-900 mb-4">Session Settings</h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Min Duration (minutes)
            </label>
            <Input
              type="number"
              value={formData.min_session_duration}
              onChange={(e) => setFormData({ ...formData, min_session_duration: Number(e.target.value) })}
              min="15"
              step="15"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Max Duration (minutes)
            </label>
            <Input
              type="number"
              value={formData.max_session_duration}
              onChange={(e) => setFormData({ ...formData, max_session_duration: Number(e.target.value) })}
              min="15"
              step="15"
              required
            />
          </div>
        </div>
      </div>

      <div className="border-t pt-6">
        <h3 className="font-medium text-gray-900 mb-4">Package Settings</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-sm">Package Eligible</p>
              <p className="text-xs text-gray-600">Allow this service in package deals</p>
            </div>
            <Switch
              checked={formData.is_package_eligible}
              onChange={(checked) => setFormData({ ...formData, is_package_eligible: checked })}
            />
          </div>
          
          {formData.is_package_eligible && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Package Discount (%)
              </label>
              <Input
                type="number"
                value={formData.package_discount}
                onChange={(e) => setFormData({ ...formData, package_discount: Number(e.target.value) })}
                min="0"
                max="50"
                step="5"
              />
            </div>
          )}
        </div>
      </div>

      <div className="flex items-center justify-between pt-6 border-t">
        <div className="flex items-center gap-2">
          <Switch
            checked={formData.is_active}
            onChange={(checked) => setFormData({ ...formData, is_active: checked })}
          />
          <span className="text-sm text-gray-700">Active</span>
        </div>
        
        <div className="flex gap-3">
          <Button
            variant="ghost"
            onClick={onCancel}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="primary"
            disabled={loading}
          >
            {loading ? 'Saving...' : (service ? 'Update' : 'Create')}
          </Button>
        </div>
      </div>
    </form>
  );
};

// Pricing Rules Manager Component
interface PricingRulesManagerProps {
  rules: PricingRule[];
  onClose: () => void;
}

const PricingRulesManager: React.FC<PricingRulesManagerProps> = ({ rules, onClose }) => {
  return (
    <div className="p-6">
      <div className="space-y-4 max-h-96 overflow-y-auto">
        {rules.map((rule) => (
          <div key={rule.rule_id} className="border rounded-lg p-4">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <h4 className="font-medium">{rule.rule_name}</h4>
                  <Badge className={`
                    ${rule.rule_type === 'surge' ? 'bg-red-100 text-red-700' : ''}
                    ${rule.rule_type === 'discount' ? 'bg-green-100 text-green-700' : ''}
                    ${rule.rule_type === 'bonus' ? 'bg-blue-100 text-blue-700' : ''}
                    ${rule.rule_type === 'penalty' ? 'bg-orange-100 text-orange-700' : ''}
                  `}>
                    {rule.rule_type}
                  </Badge>
                </div>
                <p className="text-sm text-gray-600 mb-2">{rule.description}</p>
                <div className="flex items-center gap-4 text-sm">
                  <span className="text-gray-500">Condition:</span>
                  <Badge className="bg-gray-100 text-gray-700">
                    {rule.condition_type}: {rule.condition_value}
                  </Badge>
                  <span className="text-gray-500">Adjustment:</span>
                  <Badge className="bg-purple-100 text-purple-700">
                    {rule.adjustment_type === 'percentage' ? `${rule.adjustment_value}%` : `$${rule.adjustment_value}`}
                  </Badge>
                  <span className="text-gray-500">Priority:</span>
                  <span className="font-medium">{rule.priority}</span>
                </div>
              </div>
              <Switch checked={rule.is_active} onChange={() => {}} />
            </div>
          </div>
        ))}
      </div>
      
      <div className="flex justify-between items-center mt-6 pt-6 border-t">
        <Button variant="secondary">
          <Plus className="w-4 h-4 mr-2" />
          Add Rule
        </Button>
        <Button variant="primary" onClick={onClose}>
          Done
        </Button>
      </div>
    </div>
  );
};

export default ServicePricingPage;