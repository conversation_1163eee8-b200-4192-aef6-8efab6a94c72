"""
Appointment domain models for scheduling, participants, and confirmations.
"""

from datetime import datetime, date, time
from typing import List, Optional, Dict, Any
from decimal import Decimal
from pydantic import BaseModel, Field, field_validator, ConfigDict

from app.models.base import (
    BaseEntity,
    IdentifiedEntity,
    AppointmentStatus,
    LocationDetails,
    Currency,
    SearchFilters,
    TimeSlot
)


class TutorAvailability(IdentifiedEntity):
    """Tutor availability model for weekly schedules."""
    
    availability_id: int = Field(..., description="Unique availability identifier")
    tutor_id: int = Field(..., description="Tutor ID")
    day_of_week: int = Field(..., description="Day of week (1=Monday, 7=Sunday)")
    start_time: time = Field(..., description="Available start time")
    end_time: time = Field(..., description="Available end time")
    is_available: bool = Field(True, description="Whether tutor is available")
    hourly_rate: Optional[Decimal] = Field(None, description="Special rate for this time slot")
    break_start: Optional[time] = Field(None, description="Break start time")
    break_end: Optional[time] = Field(None, description="Break end time")
    effective_date: date = Field(..., description="When this availability starts")
    expiry_date: Optional[date] = Field(None, description="When this availability expires")
    is_recurring: bool = Field(True, description="Whether this is a recurring schedule")
    notes: Optional[str] = Field(None, description="Availability notes")
    
    @field_validator('day_of_week')
    @classmethod
    def validate_day_of_week(cls, v: int) -> int:
        """Validate day of week."""
        if v < 1 or v > 7:
            raise ValueError('Day of week must be between 1 (Monday) and 7 (Sunday)')
        return v
    
    @field_validator('end_time')
    @classmethod
    def validate_end_time(cls, v: time, info) -> time:
        """Validate end time is after start time."""
        if 'start_time' in info.data and v <= info.data['start_time']:
            raise ValueError('End time must be after start time')
        return v


class TimeOffRequest(IdentifiedEntity):
    """Tutor time-off request model."""
    
    request_id: int = Field(..., description="Unique time-off request identifier")
    tutor_id: int = Field(..., description="Requesting tutor")
    start_date: date = Field(..., description="Time-off start date")
    end_date: date = Field(..., description="Time-off end date")
    start_time: Optional[time] = Field(None, description="Partial day start time")
    end_time: Optional[time] = Field(None, description="Partial day end time")
    reason: str = Field(..., description="Reason for time-off")
    request_type: str = Field(..., description="Type of time-off request")
    status: str = Field(default="pending", description="Request status")
    approved_by: Optional[int] = Field(None, description="Manager who approved")
    approved_at: Optional[datetime] = Field(None, description="Approval timestamp")
    denied_reason: Optional[str] = Field(None, description="Denial reason if applicable")
    affects_appointments: bool = Field(False, description="Whether existing appointments are affected")
    affected_appointment_ids: Optional[List[int]] = Field(None, description="List of affected appointments")
    
    @field_validator('request_type')
    @classmethod
    def validate_request_type(cls, v: str) -> str:
        """Validate request type."""
        valid_types = {'vacation', 'sick_leave', 'personal', 'holiday', 'emergency', 'other'}
        if v.lower() not in valid_types:
            raise ValueError(f'Request type must be one of: {", ".join(valid_types)}')
        return v.lower()
    
    @field_validator('status')
    @classmethod
    def validate_status(cls, v: str) -> str:
        """Validate request status."""
        valid_statuses = {'pending', 'approved', 'denied', 'cancelled', 'expired'}
        if v.lower() not in valid_statuses:
            raise ValueError(f'Status must be one of: {", ".join(valid_statuses)}')
        return v.lower()


class AppointmentConflict(IdentifiedEntity):
    """Appointment conflict tracking model."""
    
    conflict_id: int = Field(..., description="Unique conflict identifier")
    tutor_id: int = Field(..., description="Tutor with conflict")
    appointment_id_1: int = Field(..., description="First conflicting appointment")
    appointment_id_2: Optional[int] = Field(None, description="Second conflicting appointment")
    time_off_request_id: Optional[int] = Field(None, description="Conflicting time-off request")
    conflict_type: str = Field(..., description="Type of conflict")
    conflict_date: date = Field(..., description="Date of conflict")
    conflict_start: time = Field(..., description="Conflict start time")
    conflict_end: time = Field(..., description="Conflict end time")
    resolution_status: str = Field(default="unresolved", description="Resolution status")
    resolved_by: Optional[int] = Field(None, description="Who resolved the conflict")
    resolved_at: Optional[datetime] = Field(None, description="Resolution timestamp")
    resolution_notes: Optional[str] = Field(None, description="Resolution details")
    
    @field_validator('conflict_type')
    @classmethod
    def validate_conflict_type(cls, v: str) -> str:
        """Validate conflict type."""
        valid_types = {'double_booking', 'time_off_overlap', 'availability_conflict', 'travel_time', 'other'}
        if v.lower() not in valid_types:
            raise ValueError(f'Conflict type must be one of: {", ".join(valid_types)}')
        return v.lower()


class Appointment(IdentifiedEntity):
    """Appointment model."""
    
    appointment_id: int = Field(..., description="Unique appointment identifier")
    tutor_id: int = Field(..., description="Assigned tutor")
    client_id: int = Field(..., description="Primary client")
    dependant_id: Optional[int] = Field(None, description="Associated dependant if applicable")
    scheduled_date: date = Field(..., description="Appointment date")
    start_time: time = Field(..., description="Start time")
    end_time: time = Field(..., description="End time")
    status: AppointmentStatus = Field(default=AppointmentStatus.SCHEDULED, description="Appointment status")
    subject_area: str = Field(..., description="Subject area")
    location_details: Dict[str, Any] = Field(..., description="Location information")
    notes: Optional[str] = Field(None, description="Appointment notes")
    session_type: str = Field(default="individual", description="Session type")
    hourly_rate: Decimal = Field(..., description="Hourly rate for this session")
    currency: str = Field(default="CAD", description="Currency")
    confirmed_by_tutor: bool = Field(False, description="Tutor confirmation status")
    confirmed_at: Optional[datetime] = Field(None, description="When confirmed by tutor")
    cancellation_reason: Optional[str] = Field(None, description="Cancellation reason if applicable")
    cancelled_by: Optional[int] = Field(None, description="Who cancelled the appointment")
    cancelled_at: Optional[datetime] = Field(None, description="When cancelled")
    recurring_appointment_id: Optional[int] = Field(None, description="Parent recurring appointment if applicable")
    is_exception: bool = Field(False, description="Whether this is an exception to recurring pattern")
    buffer_time_before: int = Field(default=15, description="Buffer time before appointment (minutes)")
    buffer_time_after: int = Field(default=15, description="Buffer time after appointment (minutes)")
    max_participants: Optional[int] = Field(None, description="Maximum participants for group sessions")
    current_participants: int = Field(default=1, description="Current number of participants")
    travel_time_minutes: Optional[int] = Field(None, description="Travel time to next appointment")
    requires_confirmation: bool = Field(True, description="Whether appointment requires tutor confirmation")
    auto_confirm_hours: Optional[int] = Field(24, description="Hours before auto-confirmation")
    reminder_sent_24h: bool = Field(False, description="Whether 24h reminder was sent")
    reminder_sent_2h: bool = Field(False, description="Whether 2h reminder was sent")
    
    # Duration adjustment fields
    actual_start_time: Optional[time] = Field(None, description="Actual start time if different from scheduled")
    actual_end_time: Optional[time] = Field(None, description="Actual end time if different from scheduled")
    actual_duration_minutes: Optional[int] = Field(None, description="Actual duration in minutes")
    duration_adjusted: bool = Field(False, description="Whether duration was adjusted")
    duration_adjusted_by: Optional[int] = Field(None, description="User who adjusted duration")
    duration_adjusted_at: Optional[datetime] = Field(None, description="When duration was adjusted")
    duration_adjustment_reason: Optional[str] = Field(None, description="Reason for duration adjustment")
    original_duration_minutes: Optional[int] = Field(None, description="Original scheduled duration")
    duration_difference_minutes: Optional[int] = Field(None, description="Difference between actual and scheduled")
    
    @field_validator('end_time')
    @classmethod
    def validate_end_time(cls, v: time, info) -> time:
        """Validate end time is after start time."""
        if 'start_time' in info.data and v <= info.data['start_time']:
            raise ValueError('End time must be after start time')
        return v
    
    @field_validator('session_type')
    @classmethod
    def validate_session_type(cls, v: str) -> str:
        """Validate session type."""
        valid_types = {'individual', 'group', 'tecfee_program'}
        if v.lower() not in valid_types:
            raise ValueError(f'Session type must be one of: {", ".join(valid_types)}')
        return v.lower()
    
    @field_validator('hourly_rate')
    @classmethod
    def validate_hourly_rate(cls, v: Decimal) -> Decimal:
        """Validate hourly rate is positive."""
        if v <= 0:
            raise ValueError('Hourly rate must be positive')
        return v


class AppointmentParticipant(IdentifiedEntity):
    """Appointment participant model for group sessions."""
    
    participant_id: int = Field(..., description="Unique participant identifier")
    appointment_id: int = Field(..., description="Associated appointment")
    client_id: int = Field(..., description="Participant client")
    dependant_id: Optional[int] = Field(None, description="Participant dependant if applicable")
    attendance_status: str = Field(default="registered", description="Attendance status")
    joined_at: Optional[datetime] = Field(None, description="When participant joined")
    left_at: Optional[datetime] = Field(None, description="When participant left")
    
    @field_validator('attendance_status')
    @classmethod
    def validate_attendance_status(cls, v: str) -> str:
        """Validate attendance status."""
        valid_statuses = {'registered', 'present', 'absent', 'late', 'left_early'}
        if v.lower() not in valid_statuses:
            raise ValueError(f'Attendance status must be one of: {", ".join(valid_statuses)}')
        return v.lower()


class AppointmentReminder(IdentifiedEntity):
    """Appointment reminder model."""
    
    reminder_id: int = Field(..., description="Unique reminder identifier")
    appointment_id: int = Field(..., description="Associated appointment")
    reminder_type: str = Field(..., description="Type of reminder")
    recipient_id: int = Field(..., description="Recipient user")
    scheduled_for: datetime = Field(..., description="When reminder should be sent")
    sent_at: Optional[datetime] = Field(None, description="When reminder was sent")
    delivery_status: str = Field(default="pending", description="Delivery status")
    message_content: Optional[str] = Field(None, description="Reminder message content")
    
    @field_validator('reminder_type')
    @classmethod
    def validate_reminder_type(cls, v: str) -> str:
        """Validate reminder type."""
        valid_types = {'24_hour', '2_hour', 'confirmation_request', 'follow_up'}
        if v.lower() not in valid_types:
            raise ValueError(f'Reminder type must be one of: {", ".join(valid_types)}')
        return v.lower()
    
    @field_validator('delivery_status')
    @classmethod
    def validate_delivery_status(cls, v: str) -> str:
        """Validate delivery status."""
        valid_statuses = {'pending', 'sent', 'delivered', 'failed', 'cancelled'}
        if v.lower() not in valid_statuses:
            raise ValueError(f'Delivery status must be one of: {", ".join(valid_statuses)}')
        return v.lower()


class RecurringAppointment(IdentifiedEntity):
    """Recurring appointment template model."""
    
    recurring_id: int = Field(..., description="Unique recurring appointment identifier")
    tutor_id: int = Field(..., description="Assigned tutor")
    client_id: int = Field(..., description="Primary client")
    dependant_id: Optional[int] = Field(None, description="Associated dependant if applicable")
    day_of_week: int = Field(..., description="Day of week (1=Monday)")
    start_time: time = Field(..., description="Start time")
    end_time: time = Field(..., description="End time")
    subject_area: str = Field(..., description="Subject area")
    location_details: Dict[str, Any] = Field(..., description="Location information")
    hourly_rate: Decimal = Field(..., description="Hourly rate")
    currency: str = Field(default="CAD", description="Currency")
    start_date: date = Field(..., description="When recurring series starts")
    end_date: Optional[date] = Field(None, description="When recurring series ends")
    is_active: bool = Field(True, description="Whether recurring series is active")
    frequency_weeks: int = Field(default=1, description="Frequency in weeks (1=weekly, 2=biweekly)")
    
    @field_validator('day_of_week')
    @classmethod
    def validate_day_of_week(cls, v: int) -> int:
        """Validate day of week."""
        if v < 1 or v > 7:
            raise ValueError('Day of week must be between 1 (Monday) and 7 (Sunday)')
        return v
    
    @field_validator('frequency_weeks')
    @classmethod
    def validate_frequency_weeks(cls, v: int) -> int:
        """Validate frequency."""
        if v < 1:
            raise ValueError('Frequency must be at least 1 week')
        return v


class TECFEEProgram(IdentifiedEntity):
    """TECFEE program model."""
    
    program_id: int = Field(..., description="Unique program identifier")
    program_name: str = Field(..., description="Program name")
    description: str = Field(..., description="Program description")
    total_modules: int = Field(default=12, description="Total number of modules")
    price_per_module: Decimal = Field(..., description="Price per module")
    package_price: Decimal = Field(..., description="Full package price")
    currency: str = Field(default="CAD", description="Currency")
    is_active: bool = Field(True, description="Whether program is active")
    min_participants: int = Field(default=3, description="Minimum participants required")
    max_participants: int = Field(default=8, description="Maximum participants allowed")
    
    @field_validator('total_modules')
    @classmethod
    def validate_total_modules(cls, v: int) -> int:
        """Validate total modules."""
        if v < 1:
            raise ValueError('Total modules must be at least 1')
        return v


class TECFEEEnrollment(IdentifiedEntity):
    """TECFEE program enrollment model."""
    
    enrollment_id: int = Field(..., description="Unique enrollment identifier")
    program_id: int = Field(..., description="Associated program")
    client_id: int = Field(..., description="Enrolled client")
    dependant_id: Optional[int] = Field(None, description="Enrolled dependant if applicable")
    enrollment_date: date = Field(..., description="Enrollment date")
    completion_status: str = Field(default="enrolled", description="Completion status")
    modules_completed: int = Field(default=0, description="Number of modules completed")
    payment_status: str = Field(default="pending", description="Payment status")
    certificate_issued: bool = Field(False, description="Whether certificate was issued")
    certificate_date: Optional[date] = Field(None, description="Certificate issue date")
    
    @field_validator('completion_status')
    @classmethod
    def validate_completion_status(cls, v: str) -> str:
        """Validate completion status."""
        valid_statuses = {'enrolled', 'in_progress', 'completed', 'dropped', 'suspended'}
        if v.lower() not in valid_statuses:
            raise ValueError(f'Completion status must be one of: {", ".join(valid_statuses)}')
        return v.lower()


# ============================================
# Create/Update Schemas
# ============================================

class AppointmentCreate(BaseModel):
    """Schema for creating appointment."""
    
    model_config = ConfigDict(from_attributes=True)
    
    tutor_id: int = Field(..., description="Assigned tutor")
    client_id: int = Field(..., description="Primary client")
    dependant_id: Optional[int] = Field(None, description="Associated dependant")
    scheduled_date: date = Field(..., description="Appointment date")
    start_time: time = Field(..., description="Start time")
    end_time: time = Field(..., description="End time")
    subject_area: str = Field(..., description="Subject area")
    location_details: LocationDetails = Field(..., description="Location information")
    notes: Optional[str] = Field(None, description="Appointment notes")
    session_type: Optional[str] = Field("individual", description="Session type")
    hourly_rate: Decimal = Field(..., gt=0, description="Hourly rate")


class AppointmentUpdate(BaseModel):
    """Schema for updating appointment."""
    
    model_config = ConfigDict(from_attributes=True)
    
    scheduled_date: Optional[date] = Field(None, description="Appointment date")
    start_time: Optional[time] = Field(None, description="Start time")
    end_time: Optional[time] = Field(None, description="End time")
    subject_area: Optional[str] = Field(None, description="Subject area")
    location_details: Optional[LocationDetails] = Field(None, description="Location information")
    notes: Optional[str] = Field(None, description="Appointment notes")
    status: Optional[AppointmentStatus] = Field(None, description="Appointment status")


class AppointmentConfirmation(BaseModel):
    """Schema for appointment confirmation."""
    
    model_config = ConfigDict(from_attributes=True)
    
    appointment_id: int = Field(..., description="Appointment to confirm")
    confirmed: bool = Field(..., description="Confirmation status")
    notes: Optional[str] = Field(None, description="Confirmation notes")


class AppointmentCancellation(BaseModel):
    """Schema for appointment cancellation."""
    
    model_config = ConfigDict(from_attributes=True)
    
    appointment_id: int = Field(..., description="Appointment to cancel")
    reason: str = Field(..., min_length=5, description="Cancellation reason")
    notify_participants: bool = Field(True, description="Whether to notify participants")


class RecurringAppointmentCreate(BaseModel):
    """Schema for creating recurring appointment."""
    
    model_config = ConfigDict(from_attributes=True)
    
    tutor_id: int = Field(..., description="Assigned tutor")
    client_id: int = Field(..., description="Primary client")
    dependant_id: Optional[int] = Field(None, description="Associated dependant")
    day_of_week: int = Field(..., ge=1, le=7, description="Day of week")
    start_time: time = Field(..., description="Start time")
    end_time: time = Field(..., description="End time")
    subject_area: str = Field(..., description="Subject area")
    location_details: LocationDetails = Field(..., description="Location information")
    hourly_rate: Decimal = Field(..., gt=0, description="Hourly rate")
    start_date: date = Field(..., description="Series start date")
    end_date: Optional[date] = Field(None, description="Series end date")
    frequency_weeks: Optional[int] = Field(1, ge=1, description="Frequency in weeks")


class ParticipantAdd(BaseModel):
    """Schema for adding participant to group appointment."""
    
    model_config = ConfigDict(from_attributes=True)
    
    appointment_id: int = Field(..., description="Target appointment")
    client_id: int = Field(..., description="Participant client")
    dependant_id: Optional[int] = Field(None, description="Participant dependant")


class AttendanceUpdate(BaseModel):
    """Schema for updating attendance."""
    
    model_config = ConfigDict(from_attributes=True)
    
    participant_id: int = Field(..., description="Participant to update")
    attendance_status: str = Field(..., description="Attendance status")
    joined_at: Optional[datetime] = Field(None, description="Join time")
    left_at: Optional[datetime] = Field(None, description="Leave time")


class TECFEEProgramCreate(BaseModel):
    """Schema for creating TECFEE program."""
    
    model_config = ConfigDict(from_attributes=True)
    
    program_name: str = Field(..., min_length=1, description="Program name")
    description: str = Field(..., min_length=10, description="Program description")
    total_modules: Optional[int] = Field(12, ge=1, description="Total modules")
    price_per_module: Decimal = Field(..., gt=0, description="Price per module")
    package_price: Decimal = Field(..., gt=0, description="Full package price")
    min_participants: Optional[int] = Field(3, ge=1, description="Minimum participants")
    max_participants: Optional[int] = Field(8, ge=1, description="Maximum participants")


class TECFEEEnrollmentCreate(BaseModel):
    """Schema for creating TECFEE enrollment."""
    
    model_config = ConfigDict(from_attributes=True)
    
    program_id: int = Field(..., description="Program to enroll in")
    client_id: int = Field(..., description="Client enrolling")
    dependant_id: Optional[int] = Field(None, description="Dependant enrolling")


# ============================================
# Duration Adjustment Models
# ============================================

class DurationAdjustmentRequest(BaseModel):
    """Request to adjust appointment duration."""
    
    model_config = ConfigDict(from_attributes=True)
    
    actual_start_time: Optional[time] = Field(None, description="Actual start time")
    actual_end_time: Optional[time] = Field(None, description="Actual end time")
    actual_duration_minutes: Optional[int] = Field(None, ge=0, description="Actual duration in minutes (overrides times)")
    adjustment_reason: str = Field(..., min_length=5, max_length=500, description="Reason for adjustment")
    notify_participants: bool = Field(True, description="Whether to notify participants")
    
    @field_validator('actual_end_time')
    @classmethod
    def validate_actual_end_time(cls, v: time, info) -> time:
        """Validate actual end time is after actual start time."""
        if v and 'actual_start_time' in info.data and info.data['actual_start_time']:
            if v <= info.data['actual_start_time']:
                raise ValueError('Actual end time must be after actual start time')
        return v
    
    @field_validator('actual_duration_minutes')
    @classmethod
    def validate_duration(cls, v: Optional[int]) -> Optional[int]:
        """Validate duration is reasonable."""
        if v is not None:
            if v < 15:
                raise ValueError('Duration must be at least 15 minutes')
            if v > 480:  # 8 hours
                raise ValueError('Duration cannot exceed 8 hours')
        return v


class DurationAdjustmentHistory(BaseModel):
    """Duration adjustment history entry."""
    
    model_config = ConfigDict(from_attributes=True)
    
    adjustment_id: int
    appointment_id: int
    original_start_time: time
    original_end_time: time
    adjusted_start_time: time
    adjusted_end_time: time
    original_duration_minutes: int
    adjusted_duration_minutes: int
    adjustment_reason: str
    adjusted_by: int
    adjusted_by_name: Optional[str]
    adjusted_at: datetime
    billing_impact: Optional[Dict[str, Any]]
    notification_sent: bool


class AppointmentCompletion(BaseModel):
    """Request to complete an appointment with optional duration adjustment."""
    
    model_config = ConfigDict(from_attributes=True)
    
    status: AppointmentStatus = Field(AppointmentStatus.COMPLETED, description="New status")
    actual_duration_minutes: Optional[int] = Field(None, ge=0, description="Actual duration if different")
    tutor_no_show: bool = Field(False, description="Whether tutor was a no-show")
    client_no_show: bool = Field(False, description="Whether client was a no-show")
    completion_notes: Optional[str] = Field(None, max_length=1000, description="Completion notes")
    duration_adjustment: Optional[DurationAdjustmentRequest] = Field(None, description="Duration adjustment details")


# ============================================
# Response Schemas
# ============================================

class AppointmentResponse(BaseModel):
    """Appointment response schema."""
    
    model_config = ConfigDict(from_attributes=True)
    
    appointment_id: int
    tutor_id: int
    client_id: int
    dependant_id: Optional[int]
    scheduled_date: date
    start_time: time
    end_time: time
    status: AppointmentStatus
    subject_area: str
    location_details: Dict[str, Any]
    notes: Optional[str]
    session_type: str
    hourly_rate: Decimal
    currency: str
    duration_hours: float
    total_cost: Decimal
    confirmed_by_tutor: bool
    confirmed_at: Optional[datetime]
    tutor_name: Optional[str]  # For display
    client_name: Optional[str]  # For display
    dependant_name: Optional[str]  # For display
    created_at: datetime
    updated_at: datetime
    
    # Duration adjustment fields
    actual_start_time: Optional[time]
    actual_end_time: Optional[time]
    actual_duration_minutes: Optional[int]
    duration_adjusted: bool
    duration_adjusted_by: Optional[int]
    duration_adjusted_at: Optional[datetime]
    duration_adjustment_reason: Optional[str]
    original_duration_minutes: Optional[int]
    duration_difference_minutes: Optional[int]
    actual_duration_hours: Optional[float]  # Computed for display
    actual_total_cost: Optional[Decimal]  # Computed based on actual duration


class ParticipantResponse(BaseModel):
    """Participant response schema."""
    
    model_config = ConfigDict(from_attributes=True)
    
    participant_id: int
    appointment_id: int
    client_id: int
    dependant_id: Optional[int]
    attendance_status: str
    joined_at: Optional[datetime]
    left_at: Optional[datetime]
    client_name: Optional[str]  # For display
    dependant_name: Optional[str]  # For display
    created_at: datetime
    updated_at: datetime


class ReminderResponse(BaseModel):
    """Reminder response schema."""
    
    model_config = ConfigDict(from_attributes=True)
    
    reminder_id: int
    appointment_id: int
    reminder_type: str
    recipient_id: int
    scheduled_for: datetime
    sent_at: Optional[datetime]
    delivery_status: str
    message_content: Optional[str]
    created_at: datetime


class RecurringAppointmentResponse(BaseModel):
    """Recurring appointment response schema."""
    
    model_config = ConfigDict(from_attributes=True)
    
    recurring_id: int
    tutor_id: int
    client_id: int
    dependant_id: Optional[int]
    day_of_week: int
    day_name: str
    start_time: time
    end_time: time
    subject_area: str
    location_details: Dict[str, Any]
    hourly_rate: Decimal
    currency: str
    start_date: date
    end_date: Optional[date]
    is_active: bool
    frequency_weeks: int
    appointments_created: int
    next_appointment_date: Optional[date]
    tutor_name: Optional[str]  # For display
    client_name: Optional[str]  # For display
    created_at: datetime
    updated_at: datetime


class TECFEEProgramResponse(BaseModel):
    """TECFEE program response schema."""
    
    model_config = ConfigDict(from_attributes=True)
    
    program_id: int
    program_name: str
    description: str
    total_modules: int
    price_per_module: Decimal
    package_price: Decimal
    currency: str
    is_active: bool
    min_participants: int
    max_participants: int
    current_enrollments: int
    created_at: datetime
    updated_at: datetime


class TECFEEEnrollmentResponse(BaseModel):
    """TECFEE enrollment response schema."""
    
    model_config = ConfigDict(from_attributes=True)
    
    enrollment_id: int
    program_id: int
    client_id: int
    dependant_id: Optional[int]
    enrollment_date: date
    completion_status: str
    modules_completed: int
    total_modules: int
    completion_percentage: float
    payment_status: str
    certificate_issued: bool
    certificate_date: Optional[date]
    program_name: Optional[str]  # For display
    client_name: Optional[str]  # For display
    dependant_name: Optional[str]  # For display
    created_at: datetime
    updated_at: datetime


# ============================================
# Additional Models
# ============================================

class TutorAvailabilityCreate(BaseModel):
    """Schema for creating tutor availability."""
    
    model_config = ConfigDict(from_attributes=True)
    
    tutor_id: int = Field(..., description="Tutor ID")
    day_of_week: int = Field(..., ge=1, le=7, description="Day of week")
    start_time: time = Field(..., description="Available start time")
    end_time: time = Field(..., description="Available end time")
    hourly_rate: Optional[Decimal] = Field(None, gt=0, description="Special rate for this time slot")
    break_start: Optional[time] = Field(None, description="Break start time")
    break_end: Optional[time] = Field(None, description="Break end time")
    effective_date: date = Field(..., description="When this availability starts")
    expiry_date: Optional[date] = Field(None, description="When this availability expires")
    notes: Optional[str] = Field(None, description="Availability notes")


class TimeOffRequestCreate(BaseModel):
    """Schema for creating time-off request."""
    
    model_config = ConfigDict(from_attributes=True)
    
    tutor_id: int = Field(..., description="Requesting tutor")
    start_date: date = Field(..., description="Time-off start date")
    end_date: date = Field(..., description="Time-off end date")
    start_time: Optional[time] = Field(None, description="Partial day start time")
    end_time: Optional[time] = Field(None, description="Partial day end time")
    reason: str = Field(..., min_length=5, description="Reason for time-off")
    request_type: str = Field(..., description="Type of time-off request")


class ConflictCheck(BaseModel):
    """Schema for checking appointment conflicts."""
    
    model_config = ConfigDict(from_attributes=True)
    
    tutor_id: int = Field(..., description="Tutor to check")
    proposed_date: date = Field(..., description="Proposed appointment date")
    proposed_start: time = Field(..., description="Proposed start time")
    proposed_end: time = Field(..., description="Proposed end time")
    exclude_appointment_id: Optional[int] = Field(None, description="Appointment ID to exclude from conflict check")
    include_buffer: bool = Field(True, description="Include buffer time in conflict check")


class ConflictResult(BaseModel):
    """Result of conflict checking."""
    
    model_config = ConfigDict(from_attributes=True)
    
    has_conflict: bool = Field(..., description="Whether conflict exists")
    conflict_type: Optional[str] = Field(None, description="Type of conflict")
    conflicting_appointment_id: Optional[int] = Field(None, description="Conflicting appointment ID")
    conflicting_time_off_id: Optional[int] = Field(None, description="Conflicting time-off ID")
    suggested_times: List[TimeSlot] = Field(default_factory=list, description="Alternative available times")
    message: Optional[str] = Field(None, description="Human-readable conflict message")


class SchedulingSuggestion(BaseModel):
    """AI-powered scheduling suggestion."""
    
    model_config = ConfigDict(from_attributes=True)
    
    tutor_id: int = Field(..., description="Suggested tutor")
    tutor_name: str = Field(..., description="Tutor name")
    suggested_date: date = Field(..., description="Suggested date")
    suggested_start: time = Field(..., description="Suggested start time")
    suggested_end: time = Field(..., description="Suggested end time")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Confidence in suggestion")
    reasons: List[str] = Field(..., description="Reasons for suggestion")
    estimated_travel_time: Optional[int] = Field(None, description="Travel time from previous appointment")
    hourly_rate: Decimal = Field(..., description="Rate for this time slot")


class CalendarFilter(BaseModel):
    """Calendar filtering options."""
    
    model_config = ConfigDict(from_attributes=True)
    
    tutor_ids: Optional[List[int]] = Field(None, description="Filter by specific tutors")
    subject_areas: Optional[List[str]] = Field(None, description="Filter by subject areas")
    session_types: Optional[List[str]] = Field(None, description="Filter by session types")
    statuses: Optional[List[AppointmentStatus]] = Field(None, description="Filter by statuses")
    date_from: Optional[date] = Field(None, description="Start date filter")
    date_to: Optional[date] = Field(None, description="End date filter")
    time_from: Optional[time] = Field(None, description="Start time filter")
    time_to: Optional[time] = Field(None, description="End time filter")
    confirmed_only: bool = Field(False, description="Only confirmed appointments")
    include_cancelled: bool = Field(False, description="Include cancelled appointments")


class BulkAppointmentOperation(BaseModel):
    """Schema for bulk appointment operations."""
    
    model_config = ConfigDict(from_attributes=True)
    
    appointment_ids: List[int] = Field(..., min_items=1, description="Appointment IDs to operate on")
    operation: str = Field(..., description="Operation to perform")
    reason: Optional[str] = Field(None, description="Reason for operation")
    notify_participants: bool = Field(True, description="Whether to notify participants")
    
    @field_validator('operation')
    @classmethod
    def validate_operation(cls, v: str) -> str:
        """Validate operation type."""
        valid_operations = {'cancel', 'confirm', 'reschedule', 'update_status'}
        if v.lower() not in valid_operations:
            raise ValueError(f'Operation must be one of: {", ".join(valid_operations)}')
        return v.lower()


# ============================================
# Response Schemas
# ============================================

class TutorAvailabilityResponse(BaseModel):
    """Tutor availability response schema."""
    
    model_config = ConfigDict(from_attributes=True)
    
    availability_id: int
    tutor_id: int
    day_of_week: int
    day_name: str
    start_time: time
    end_time: time
    is_available: bool
    hourly_rate: Optional[Decimal]
    break_start: Optional[time]
    break_end: Optional[time]
    effective_date: date
    expiry_date: Optional[date]
    is_recurring: bool
    notes: Optional[str]
    tutor_name: Optional[str]  # For display
    created_at: datetime
    updated_at: datetime


class TimeOffRequestResponse(BaseModel):
    """Time-off request response schema."""
    
    model_config = ConfigDict(from_attributes=True)
    
    request_id: int
    tutor_id: int
    start_date: date
    end_date: date
    start_time: Optional[time]
    end_time: Optional[time]
    reason: str
    request_type: str
    status: str
    approved_by: Optional[int]
    approved_at: Optional[datetime]
    denied_reason: Optional[str]
    affects_appointments: bool
    affected_appointment_ids: Optional[List[int]]
    tutor_name: Optional[str]  # For display
    approver_name: Optional[str]  # For display
    days_requested: int
    is_partial_day: bool
    created_at: datetime
    updated_at: datetime


class AppointmentConflictResponse(BaseModel):
    """Appointment conflict response schema."""
    
    model_config = ConfigDict(from_attributes=True)
    
    conflict_id: int
    tutor_id: int
    appointment_id_1: int
    appointment_id_2: Optional[int]
    time_off_request_id: Optional[int]
    conflict_type: str
    conflict_date: date
    conflict_start: time
    conflict_end: time
    resolution_status: str
    resolved_by: Optional[int]
    resolved_at: Optional[datetime]
    resolution_notes: Optional[str]
    tutor_name: Optional[str]  # For display
    affected_appointments: List[int]
    created_at: datetime


class AppointmentSearchFilters(SearchFilters):
    """Appointment-specific search filters."""
    
    tutor_id: Optional[int] = Field(None, description="Filter by tutor")
    client_id: Optional[int] = Field(None, description="Filter by client")
    dependant_id: Optional[int] = Field(None, description="Filter by dependant")
    status: Optional[AppointmentStatus] = Field(None, description="Filter by status")
    subject_area: Optional[str] = Field(None, description="Filter by subject")
    session_type: Optional[str] = Field(None, description="Filter by session type")
    date_from: Optional[date] = Field(None, description="Start date filter")
    date_to: Optional[date] = Field(None, description="End date filter")
    confirmed_only: Optional[bool] = Field(None, description="Only confirmed appointments")
    upcoming_only: Optional[bool] = Field(None, description="Only upcoming appointments")