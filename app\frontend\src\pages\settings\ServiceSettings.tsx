import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Clock, MapPin, Package, Plus, Edit2, Trash2, 
  Save, X, AlertCircle, CheckCircle 
} from 'lucide-react';
import api from '../../services/api';
import { serviceApi } from '../../services/serviceApi';
import { Card } from '../../components/common/Card';
import Button from '../../components/common/Button';
import { Input } from '../../components/common/Input';
import { Select } from '../../components/common/Select';
import { Modal } from '../../components/common/Modal';
import { Badge } from '../../components/common/Badge';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import { EmptyState } from '../../components/common/EmptyState';
import { useAuth } from '../../contexts/AuthContext';
import toast from 'react-hot-toast';

interface ServicePreset {
  service_catalog_id: number;
  service_name: string;
  subject_area: string;
  service_type: 'online' | 'in_person' | 'library' | 'hybrid';
  service_level: string;
  min_duration_minutes: number;
  max_duration_minutes: number;
  base_rate_min: number;
  base_rate_max: number;
  is_active: boolean;
  description?: string;
}

interface ServicePackage {
  service_package_id: number;
  package_name: string;
  package_type: string;
  total_sessions: number;
  session_duration_minutes: number;
  package_price: number;
  valid_for_days: number;
  subject_areas: string[];
  service_types: string[];
  is_active: boolean;
  description?: string;
}

const ServiceSettings: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<'presets' | 'packages'>('presets');
  const [presets, setPresets] = useState<ServicePreset[]>([]);
  const [packages, setPackages] = useState<ServicePackage[]>([]);
  const [loading, setLoading] = useState(true);
  const [showPresetModal, setShowPresetModal] = useState(false);
  const [showPackageModal, setShowPackageModal] = useState(false);
  const [editingPreset, setEditingPreset] = useState<ServicePreset | null>(null);
  const [editingPackage, setEditingPackage] = useState<ServicePackage | null>(null);

  // Default presets
  const defaultPresets: Omit<ServicePreset, 'id'>[] = [
    {
      name: '30 minutes - Quick Review',
      duration_minutes: 30,
      service_type: 'online',
      subject_areas: ['all'],
      base_rate: 30,
      is_active: true,
      description: 'Quick homework help or concept review'
    },
    {
      name: '60 minutes - Standard Session',
      duration_minutes: 60,
      service_type: 'online',
      subject_areas: ['all'],
      base_rate: 50,
      is_active: true,
      description: 'Standard tutoring session for most subjects'
    },
    {
      name: '60 minutes - In Person',
      duration_minutes: 60,
      service_type: 'in_person',
      subject_areas: ['all'],
      base_rate: 60,
      is_active: true,
      description: 'In-person tutoring at client location'
    },
    {
      name: '90 minutes - Extended Session',
      duration_minutes: 90,
      service_type: 'online',
      subject_areas: ['mathematics', 'science', 'test_prep'],
      base_rate: 75,
      is_active: true,
      description: 'Extended session for complex topics or test preparation'
    },
    {
      name: '90 minutes - In Person Extended',
      duration_minutes: 90,
      service_type: 'in_person',
      subject_areas: ['mathematics', 'science', 'test_prep'],
      base_rate: 90,
      is_active: true,
      description: 'Extended in-person session'
    },
    {
      name: '120 minutes - Intensive',
      duration_minutes: 120,
      service_type: 'online',
      subject_areas: ['test_prep', 'university'],
      min_age: 16,
      base_rate: 100,
      is_active: true,
      description: 'Intensive session for university-level or exam preparation'
    }
  ];

  // Default packages
  const defaultPackages: Omit<ServicePackage, 'id'>[] = [
    {
      name: 'TECFEE Preparation - 12 Sessions',
      total_sessions: 12,
      session_duration_minutes: 60,
      package_price: 540,
      valid_for_days: 90,
      service_types: ['online', 'in_person'],
      is_active: true
    },
    {
      name: 'Monthly Package - 8 Sessions',
      total_sessions: 8,
      session_duration_minutes: 60,
      package_price: 360,
      valid_for_days: 30,
      service_types: ['online'],
      is_active: true
    },
    {
      name: 'Starter Package - 4 Sessions',
      total_sessions: 4,
      session_duration_minutes: 60,
      package_price: 190,
      valid_for_days: 30,
      service_types: ['online', 'in_person'],
      is_active: true
    }
  ];

  useEffect(() => {
    fetchServiceSettings();
  }, []);

  const fetchServiceSettings = async () => {
    try {
      setLoading(true);
      
      // Fetch from API
      const [servicesResponse, packagesResponse] = await Promise.all([
        api.get('/services/catalog'),
        api.get('/services/packages')
      ]);
      
      setPresets(servicesResponse.data);
      setPackages(packagesResponse.data);
      
    } catch (error) {
      console.error('Error fetching service settings:', error);
      toast.error(t('settings.service.errors.fetchSettings'));
      
      // Fallback to defaults if API fails
      setPresets([]);
      setPackages([]);
    } finally {
      setLoading(false);
    }
  };

  const handleSavePreset = async (preset: any) => {
    try {
      if (editingPreset) {
        // Update existing preset
        const response = await api.put(
          `/services/catalog/${editingPreset.service_catalog_id}`,
          preset
        );
        toast.success(t('settings.service.presetUpdated'));
      } else {
        // Add new preset
        const response = await api.post('/services/catalog', preset);
        toast.success(t('settings.service.presetCreated'));
      }
      
      await fetchServiceSettings(); // Refresh list
      setShowPresetModal(false);
      setEditingPreset(null);
    } catch (error) {
      console.error('Error saving preset:', error);
      toast.error(t('settings.service.errors.savePreset'));
    }
  };

  const handleDeletePreset = async (id: number) => {
    if (window.confirm(t('settings.service.confirmDeletePreset'))) {
      try {
        await api.delete(`/services/catalog/${id}`);
        toast.success(t('settings.service.presetDeleted'));
        await fetchServiceSettings(); // Refresh list
      } catch (error) {
        console.error('Error deleting preset:', error);
        toast.error(t('settings.service.errors.deletePreset'));
      }
    }
  };

  const handleSavePackage = async (pkg: any) => {
    try {
      if (editingPackage) {
        // Update existing package
        const response = await api.put(
          `/services/packages/${editingPackage.service_package_id}`,
          pkg
        );
        toast.success(t('settings.service.packageUpdated'));
      } else {
        // Add new package
        const response = await api.post('/services/packages', pkg);
        toast.success(t('settings.service.packageCreated'));
      }
      
      await fetchServiceSettings(); // Refresh list
      setShowPackageModal(false);
      setEditingPackage(null);
    } catch (error) {
      console.error('Error saving package:', error);
      toast.error(t('settings.service.errors.savePackage'));
    }
  };

  const handleDeletePackage = async (id: number) => {
    if (window.confirm(t('settings.service.confirmDeletePackage'))) {
      try {
        await api.delete(`/services/packages/${id}`);
        toast.success(t('settings.service.packageDeleted'));
        await fetchServiceSettings(); // Refresh list
      } catch (error) {
        console.error('Error deleting package:', error);
        toast.error(t('settings.service.errors.deletePackage'));
      }
    }
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0 && mins > 0) {
      return `${hours}h ${mins}min`;
    } else if (hours > 0) {
      return `${hours}h`;
    } else {
      return `${mins}min`;
    }
  };

  const getServiceTypeIcon = (type: string) => {
    switch (type) {
      case 'online':
        return '💻';
      case 'in_person':
        return '🏠';
      case 'library':
        return '📚';
      case 'hybrid':
        return '🔄';
      default:
        return '📍';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-semibold text-gray-900">
          {t('settings.service.title')}
        </h2>
        <p className="text-sm text-gray-500 mt-1">
          {t('settings.service.description')}
        </p>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('presets')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'presets'
                ? 'border-accent-red text-accent-red'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Clock className="w-4 h-4 inline-block mr-2" />
            {t('settings.service.sessionPresets')}
          </button>
          <button
            onClick={() => setActiveTab('packages')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'packages'
                ? 'border-accent-red text-accent-red'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Package className="w-4 h-4 inline-block mr-2" />
            {t('settings.service.packages')}
          </button>
        </nav>
      </div>

      {/* Content */}
      {activeTab === 'presets' && (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900">
              {t('settings.service.sessionPresets')}
            </h3>
            <Button
              variant="primary"
              leftIcon={<Plus className="w-4 h-4" />}
              onClick={() => setShowPresetModal(true)}
            >
              {t('settings.service.addPreset')}
            </Button>
          </div>

          {presets.length === 0 ? (
            <Card>
              <EmptyState
                icon={<Clock className="w-12 h-12 text-gray-400" />}
                title={t('settings.service.noPresets')}
                description={t('settings.service.noPresetsDescription')}
                action={
                  <Button
                    variant="primary"
                    leftIcon={<Plus className="w-4 h-4" />}
                    onClick={() => setShowPresetModal(true)}
                  >
                    {t('settings.service.createFirstPreset')}
                  </Button>
                }
              />
            </Card>
          ) : (
            <div className="grid gap-4">
              {presets.map((preset) => (
                <Card key={preset.id} className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <h4 className="text-lg font-medium text-gray-900">
                          {preset.name}
                        </h4>
                        <Badge variant={preset.is_active ? 'success' : 'secondary'}>
                          {preset.is_active ? t('common.active') : t('common.inactive')}
                        </Badge>
                      </div>
                      
                      {preset.description && (
                        <p className="text-sm text-gray-600 mt-1">
                          {preset.description}
                        </p>
                      )}
                      
                      <div className="flex items-center space-x-6 mt-3">
                        <div className="flex items-center text-sm text-gray-500">
                          <Clock className="w-4 h-4 mr-1" />
                          {formatDuration(preset.duration_minutes)}
                        </div>
                        <div className="flex items-center text-sm text-gray-500">
                          {getServiceTypeIcon(preset.service_type)}
                          <span className="ml-1 capitalize">
                            {preset.service_type.replace('_', ' ')}
                          </span>
                        </div>
                        <div className="text-sm text-gray-500">
                          ${preset.base_rate}/session
                        </div>
                      </div>
                      
                      {preset.subject_areas.length > 0 && preset.subject_areas[0] !== 'all' && (
                        <div className="flex items-center space-x-2 mt-2">
                          <span className="text-sm text-gray-500">
                            {t('settings.service.subjects')}:
                          </span>
                          {preset.subject_areas.map((subject) => (
                            <Badge key={subject} variant="info" size="sm">
                              {subject}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-2 ml-4">
                      <Button
                        variant="ghost"
                        size="sm"
                        leftIcon={<Edit2 className="w-4 h-4" />}
                        onClick={() => {
                          setEditingPreset(preset);
                          setShowPresetModal(true);
                        }}
                      >
                        {t('common.edit')}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        leftIcon={<Trash2 className="w-4 h-4" />}
                        onClick={() => handleDeletePreset(preset.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        {t('common.delete')}
                      </Button>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </div>
      )}

      {activeTab === 'packages' && (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900">
              {t('settings.service.packages')}
            </h3>
            <Button
              variant="primary"
              leftIcon={<Plus className="w-4 h-4" />}
              onClick={() => setShowPackageModal(true)}
            >
              {t('settings.service.addPackage')}
            </Button>
          </div>

          {packages.length === 0 ? (
            <Card>
              <EmptyState
                icon={<Package className="w-12 h-12 text-gray-400" />}
                title={t('settings.service.noPackages')}
                description={t('settings.service.noPackagesDescription')}
                action={
                  <Button
                    variant="primary"
                    leftIcon={<Plus className="w-4 h-4" />}
                    onClick={() => setShowPackageModal(true)}
                  >
                    {t('settings.service.createFirstPackage')}
                  </Button>
                }
              />
            </Card>
          ) : (
            <div className="grid gap-4">
              {packages.map((pkg) => (
                <Card key={pkg.id} className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <h4 className="text-lg font-medium text-gray-900">
                          {pkg.name}
                        </h4>
                        <Badge variant={pkg.is_active ? 'success' : 'secondary'}>
                          {pkg.is_active ? t('common.active') : t('common.inactive')}
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-3">
                        <div>
                          <p className="text-sm text-gray-500">
                            {t('settings.service.sessions')}
                          </p>
                          <p className="text-sm font-medium text-gray-900">
                            {pkg.total_sessions} sessions
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">
                            {t('settings.service.sessionDuration')}
                          </p>
                          <p className="text-sm font-medium text-gray-900">
                            {formatDuration(pkg.session_duration_minutes)}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">
                            {t('settings.service.packagePrice')}
                          </p>
                          <p className="text-sm font-medium text-gray-900">
                            ${pkg.package_price}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">
                            {t('settings.service.validity')}
                          </p>
                          <p className="text-sm font-medium text-gray-900">
                            {pkg.valid_for_days} days
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2 mt-3">
                        {pkg.service_types.map((type) => (
                          <Badge key={type} variant="secondary" size="sm">
                            {getServiceTypeIcon(type)} {type}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2 ml-4">
                      <Button
                        variant="ghost"
                        size="sm"
                        leftIcon={<Edit2 className="w-4 h-4" />}
                        onClick={() => {
                          setEditingPackage(pkg);
                          setShowPackageModal(true);
                        }}
                      >
                        {t('common.edit')}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        leftIcon={<Trash2 className="w-4 h-4" />}
                        onClick={() => handleDeletePackage(pkg.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        {t('common.delete')}
                      </Button>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Preset Modal */}
      <PresetModal
        isOpen={showPresetModal}
        onClose={() => {
          setShowPresetModal(false);
          setEditingPreset(null);
        }}
        onSave={handleSavePreset}
        preset={editingPreset}
      />

      {/* Package Modal */}
      <PackageModal
        isOpen={showPackageModal}
        onClose={() => {
          setShowPackageModal(false);
          setEditingPackage(null);
        }}
        onSave={handleSavePackage}
        package={editingPackage}
      />
    </div>
  );
};

// Preset Modal Component
const PresetModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onSave: (preset: Omit<ServicePreset, 'id'>) => void;
  preset?: ServicePreset | null;
}> = ({ isOpen, onClose, onSave, preset }) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState<Omit<ServicePreset, 'id'>>({
    name: '',
    duration_minutes: 60,
    service_type: 'online',
    subject_areas: ['all'],
    base_rate: 50,
    is_active: true,
    description: ''
  });

  useEffect(() => {
    if (preset) {
      setFormData(preset);
    } else {
      setFormData({
        name: '',
        duration_minutes: 60,
        service_type: 'online',
        subject_areas: ['all'],
        base_rate: 50,
        is_active: true,
        description: ''
      });
    }
  }, [preset]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  if (!isOpen) return null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={preset ? t('settings.service.editPreset') : t('settings.service.addPreset')}
    >
      <form onSubmit={handleSubmit} className="p-6 space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {t('settings.service.presetName')}
          </label>
          <Input
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            placeholder="e.g., 60 minutes - Standard Session"
            required
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('settings.service.duration')}
            </label>
            <Select
              value={formData.duration_minutes.toString()}
              onChange={(e) => setFormData({ ...formData, duration_minutes: parseInt(e.target.value) })}
            >
              <option value="30">30 minutes</option>
              <option value="45">45 minutes</option>
              <option value="60">60 minutes</option>
              <option value="90">90 minutes</option>
              <option value="120">120 minutes</option>
            </Select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('settings.service.serviceType')}
            </label>
            <Select
              value={formData.service_type}
              onChange={(e) => setFormData({ ...formData, service_type: e.target.value as any })}
            >
              <option value="online">Online</option>
              <option value="in_person">In Person</option>
              <option value="library">Library</option>
              <option value="hybrid">Hybrid</option>
            </Select>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {t('settings.service.baseRate')}
          </label>
          <Input
            type="number"
            value={formData.base_rate}
            onChange={(e) => setFormData({ ...formData, base_rate: parseFloat(e.target.value) })}
            min="0"
            step="5"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {t('settings.service.description')}
          </label>
          <textarea
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-accent-red focus:border-accent-red"
            rows={3}
            value={formData.description || ''}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            placeholder="Optional description for this preset"
          />
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="is_active"
            checked={formData.is_active}
            onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
            className="rounded border-gray-300 text-accent-red focus:ring-accent-red"
          />
          <label htmlFor="is_active" className="ml-2 text-sm text-gray-700">
            {t('settings.service.activePreset')}
          </label>
        </div>

        <div className="flex justify-end space-x-3 pt-4">
          <Button variant="secondary" onClick={onClose}>
            {t('common.cancel')}
          </Button>
          <Button type="submit" variant="primary">
            {preset ? t('common.save') : t('common.create')}
          </Button>
        </div>
      </form>
    </Modal>
  );
};

// Package Modal Component
const PackageModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onSave: (pkg: Omit<ServicePackage, 'id'>) => void;
  package?: ServicePackage | null;
}> = ({ isOpen, onClose, onSave, package: pkg }) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState<Omit<ServicePackage, 'id'>>({
    name: '',
    total_sessions: 8,
    session_duration_minutes: 60,
    package_price: 400,
    valid_for_days: 30,
    service_types: ['online'],
    is_active: true
  });

  useEffect(() => {
    if (pkg) {
      setFormData(pkg);
    } else {
      setFormData({
        name: '',
        total_sessions: 8,
        session_duration_minutes: 60,
        package_price: 400,
        valid_for_days: 30,
        service_types: ['online'],
        is_active: true
      });
    }
  }, [pkg]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  const toggleServiceType = (type: string) => {
    setFormData(prev => ({
      ...prev,
      service_types: prev.service_types.includes(type)
        ? prev.service_types.filter(t => t !== type)
        : [...prev.service_types, type]
    }));
  };

  if (!isOpen) return null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={pkg ? t('settings.service.editPackage') : t('settings.service.addPackage')}
    >
      <form onSubmit={handleSubmit} className="p-6 space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {t('settings.service.packageName')}
          </label>
          <Input
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            placeholder="e.g., Monthly Package - 8 Sessions"
            required
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('settings.service.totalSessions')}
            </label>
            <Input
              type="number"
              value={formData.total_sessions}
              onChange={(e) => setFormData({ ...formData, total_sessions: parseInt(e.target.value) })}
              min="1"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('settings.service.sessionDuration')}
            </label>
            <Select
              value={formData.session_duration_minutes.toString()}
              onChange={(e) => setFormData({ ...formData, session_duration_minutes: parseInt(e.target.value) })}
            >
              <option value="30">30 minutes</option>
              <option value="45">45 minutes</option>
              <option value="60">60 minutes</option>
              <option value="90">90 minutes</option>
              <option value="120">120 minutes</option>
            </Select>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('settings.service.packagePrice')}
            </label>
            <Input
              type="number"
              value={formData.package_price}
              onChange={(e) => setFormData({ ...formData, package_price: parseFloat(e.target.value) })}
              min="0"
              step="10"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('settings.service.validityDays')}
            </label>
            <Input
              type="number"
              value={formData.valid_for_days}
              onChange={(e) => setFormData({ ...formData, valid_for_days: parseInt(e.target.value) })}
              min="1"
              required
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {t('settings.service.allowedServiceTypes')}
          </label>
          <div className="flex flex-wrap gap-2">
            {['online', 'in_person', 'library', 'hybrid'].map((type) => (
              <button
                key={type}
                type="button"
                onClick={() => toggleServiceType(type)}
                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                  formData.service_types.includes(type)
                    ? 'bg-accent-red text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                {type.replace('_', ' ')}
              </button>
            ))}
          </div>
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="is_active_pkg"
            checked={formData.is_active}
            onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
            className="rounded border-gray-300 text-accent-red focus:ring-accent-red"
          />
          <label htmlFor="is_active_pkg" className="ml-2 text-sm text-gray-700">
            {t('settings.service.activePackage')}
          </label>
        </div>

        <div className="flex justify-end space-x-3 pt-4">
          <Button variant="secondary" onClick={onClose}>
            {t('common.cancel')}
          </Button>
          <Button type="submit" variant="primary">
            {pkg ? t('common.save') : t('common.create')}
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default ServiceSettings;