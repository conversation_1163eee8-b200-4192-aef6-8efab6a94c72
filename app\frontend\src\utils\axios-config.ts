import axios from 'axios';
import toast from 'react-hot-toast';

// Track if we've already shown an auth error to prevent multiple toasts
let authErrorShown = false;

export const setupAxiosInterceptors = (logout: () => void) => {
  // Response interceptor for the global axios instance
  axios.interceptors.response.use(
    (response) => response,
    (error) => {
      // Only handle 401 errors from actual API calls, not 404s or network errors
      if (error.response?.status === 401 && error.config?.url?.includes('/api/')) {
        const token = localStorage.getItem('accessToken');
        
        // Only logout if we had a token (meaning it expired)
        if (token && !authErrorShown) {
          authErrorShown = true;
          toast.error('Your session has expired. Please login again.');
          
          // Reset flag after a delay
          setTimeout(() => {
            authErrorShown = false;
          }, 5000);
          
          logout();
        }
      }
      
      return Promise.reject(error);
    }
  );
};

// Helper to check if an error is an authentication error
export const isAuthError = (error: any): boolean => {
  return error?.response?.status === 401;
};

// Helper to check if an error is a network error
export const isNetworkError = (error: any): boolean => {
  return !error.response && error.message === 'Network Error';
};