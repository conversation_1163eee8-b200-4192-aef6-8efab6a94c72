-- Rollback: Search Enhancements

-- Drop triggers
DROP TRIGGER IF EXISTS refresh_search_on_user_change ON users;
DROP TRIGGER IF EXISTS refresh_search_on_dependant_change ON dependants;
DROP TRIGGER IF EXISTS refresh_search_on_tutor_change ON tutor_profiles;
DROP TRIGGER IF EXISTS refresh_search_on_client_change ON client_profiles;

-- Drop functions
DROP FUNCTION IF EXISTS trigger_refresh_search_view();
DROP FUNCTION IF EXISTS log_search(INTEGER, TEXT, TEXT, JSONB, INTEGER, TEXT, INET);
DROP FUNCTION IF EXISTS autocomplete_search(TEXT, TEXT, INTEGER);
DROP FUNCTION IF EXISTS fuzzy_search(TEXT, TEXT, INTEGER, REAL);
DROP FUNCTION IF EXISTS refresh_search_view();

-- Drop indexes on materialized view
DROP INDEX IF EXISTS idx_search_unified_created;
DROP INDEX IF EXISTS idx_search_unified_active;
DROP INDEX IF EXISTS idx_search_unified_trigram;
DROP INDEX IF EXISTS idx_search_unified_fulltext;
DROP INDEX IF EXISTS idx_search_unified_entity;

-- Drop materialized view
DROP MATERIALIZED VIEW IF EXISTS search_unified_view;

-- Drop other indexes
DROP INDEX IF EXISTS idx_popular_searches_count;
DROP INDEX IF EXISTS idx_user_search_history_timestamp;
DROP INDEX IF EXISTS idx_user_search_history_user_id;
DROP INDEX IF EXISTS idx_dependants_trigram;
DROP INDEX IF EXISTS idx_dependants_search;
DROP INDEX IF EXISTS idx_tutor_trigram;
DROP INDEX IF EXISTS idx_tutor_search;
DROP INDEX IF EXISTS idx_client_profiles_trigram;
DROP INDEX IF EXISTS idx_client_profiles_search;

-- Drop tables
DROP TABLE IF EXISTS search_cache;
DROP TABLE IF EXISTS popular_searches;
DROP TABLE IF EXISTS user_search_history;

-- Drop extensions (be careful - only if not used elsewhere)
-- DROP EXTENSION IF EXISTS unaccent;
-- DROP EXTENSION IF EXISTS pg_trgm;