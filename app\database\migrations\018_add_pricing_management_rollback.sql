-- Rollback Migration: 018_add_pricing_management_rollback.sql
-- Description: Rollback pricing management tables
-- Author: System
-- Date: 2024-01-15

-- Drop indexes
DROP INDEX IF EXISTS idx_revenue_tracking_appointment;
DROP INDEX IF EXISTS idx_revenue_tracking_date;
DROP INDEX IF EXISTS idx_surge_rules_active;
DROP INDEX IF EXISTS idx_client_rates_client;
DROP INDEX IF EXISTS idx_tutor_rates_tutor;
DROP INDEX IF EXISTS idx_pricing_rules_dates;
DROP INDEX IF EXISTS idx_pricing_rules_service;

-- Drop tables in reverse order of dependencies
DROP TABLE IF EXISTS platform_revenue_tracking;
DROP TABLE IF EXISTS commission_tiers;
DROP TABLE IF EXISTS commission_structures;
DROP TABLE IF EXISTS surge_pricing_rules;
DROP TABLE IF EXISTS client_service_rates;
DROP TABLE IF EXISTS tutor_service_rates;
DROP TABLE IF EXISTS pricing_rules;