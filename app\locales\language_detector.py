"""
Language detection service for automatic language selection.
"""

import re
from typing import Op<PERSON>, List, Tuple
from fastapi import Request

from .constants import SUPPORTED_LANGUAGES, DEFAULT_LANGUAGE


class LanguageDetector:
    """Service for detecting user language preferences."""
    
    def detect_from_header(self, accept_language: str) -> str:
        """
        Detect language from Accept-Language header.
        
        Returns the best matching supported language.
        """
        if not accept_language:
            return DEFAULT_LANGUAGE
        
        # Parse Accept-Language header
        languages = self._parse_accept_language(accept_language)
        
        # Find best match from supported languages
        for lang_code, _ in languages:
            # Check exact match
            if lang_code in SUPPORTED_LANGUAGES:
                return lang_code
            
            # Check language family (e.g., 'fr-CA' -> 'fr')
            if '-' in lang_code:
                base_lang = lang_code.split('-')[0]
                if base_lang in SUPPORTED_LANGUAGES:
                    return base_lang
        
        return DEFAULT_LANGUAGE
    
    def detect_from_request(self, request: Request) -> str:
        """
        Detect language from FastAPI request.
        
        Priority:
        1. Query parameter 'lang' or 'language'
        2. User preference from database (if authenticated)
        3. Accept-Language header
        4. Default language
        """
        # Check query parameters
        lang_param = request.query_params.get('lang') or request.query_params.get('language')
        if lang_param and lang_param in SUPPORTED_LANGUAGES:
            return lang_param
        
        # Check Accept-Language header
        accept_language = request.headers.get('accept-language', '')
        return self.detect_from_header(accept_language)
    
    def detect_from_user_agent(self, user_agent: str) -> str:
        """
        Detect language hints from User-Agent string.
        
        This is a fallback method for basic language detection.
        """
        if not user_agent:
            return DEFAULT_LANGUAGE
        
        # Look for language indicators in User-Agent
        ua_lower = user_agent.lower()
        
        # Quebec/French indicators
        if any(indicator in ua_lower for indicator in ['fr-ca', 'quebec', 'québec', 'français']):
            return 'fr'
        
        # Default to English for North American contexts
        if any(indicator in ua_lower for indicator in ['en-ca', 'canada', 'english']):
            return 'en'
        
        return DEFAULT_LANGUAGE
    
    def get_language_variants(self, base_language: str) -> List[str]:
        """
        Get language variants for a base language.
        
        For Quebec market:
        - 'fr' -> ['fr-CA', 'fr-QC', 'fr']
        - 'en' -> ['en-CA', 'en-US', 'en']
        """
        variants = {
            'fr': ['fr-CA', 'fr-QC', 'fr'],
            'en': ['en-CA', 'en-US', 'en']
        }
        
        return variants.get(base_language, [base_language])
    
    def is_quebec_french_preferred(self, accept_language: str) -> bool:
        """
        Determine if user prefers Quebec French specifically.
        """
        if not accept_language:
            return False
        
        # Look for Quebec-specific language codes
        quebec_indicators = ['fr-ca', 'fr-qc', 'fr-quebec']
        accept_lower = accept_language.lower()
        
        return any(indicator in accept_lower for indicator in quebec_indicators)
    
    def _parse_accept_language(self, accept_language: str) -> List[Tuple[str, float]]:
        """
        Parse Accept-Language header into list of (language, quality) tuples.
        
        Example: "fr-CA,fr;q=0.9,en;q=0.8" -> [('fr-CA', 1.0), ('fr', 0.9), ('en', 0.8)]
        """
        languages = []
        
        # Split by comma and process each language
        for lang_spec in accept_language.split(','):
            lang_spec = lang_spec.strip()
            
            # Check for quality factor
            if ';q=' in lang_spec:
                lang_code, quality_str = lang_spec.split(';q=', 1)
                try:
                    quality = float(quality_str)
                except ValueError:
                    quality = 1.0
            else:
                lang_code = lang_spec
                quality = 1.0
            
            lang_code = lang_code.strip().lower()
            if lang_code:
                languages.append((lang_code, quality))
        
        # Sort by quality (highest first)
        languages.sort(key=lambda x: x[1], reverse=True)
        
        return languages
    
    def get_browser_language_preferences(self, accept_language: str) -> dict:
        """
        Get detailed browser language preferences for analytics.
        """
        parsed_languages = self._parse_accept_language(accept_language)
        
        preferences = {
            'primary_language': None,
            'languages': [],
            'quebec_french_detected': False,
            'canadian_context': False
        }
        
        if parsed_languages:
            preferences['primary_language'] = parsed_languages[0][0]
            preferences['languages'] = [lang for lang, _ in parsed_languages]
            
            # Check for Quebec French
            preferences['quebec_french_detected'] = any(
                'fr-ca' in lang or 'fr-qc' in lang 
                for lang, _ in parsed_languages
            )
            
            # Check for Canadian context
            preferences['canadian_context'] = any(
                'ca' in lang.split('-') 
                for lang, _ in parsed_languages
            )
        
        return preferences


# Global language detector instance
_language_detector: Optional[LanguageDetector] = None


def get_language_detector() -> LanguageDetector:
    """Get global language detector instance."""
    global _language_detector
    if _language_detector is None:
        _language_detector = LanguageDetector()
    return _language_detector


def detect_language_from_request(request: Request) -> str:
    """Convenience function for detecting language from request."""
    return get_language_detector().detect_from_request(request)


def detect_language_from_header(accept_language: str) -> str:
    """Convenience function for detecting language from header."""
    return get_language_detector().detect_from_header(accept_language)