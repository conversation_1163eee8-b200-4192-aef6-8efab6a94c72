import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Shield, Smartphone, Mail, Key, Copy, CheckCircle, AlertCircle } from 'lucide-react';
import { Modal } from '../common/Modal';
import Button from '../common/Button';
import { Input } from '../common/Input';
import { Card } from '../common/Card';
import LoadingSpinner from '../ui/LoadingSpinner';
import api from '../../services/api';
import toast from 'react-hot-toast';
import QRCode from 'qrcode.react';

interface TwoFactorSetupProps {
  method: 'sms' | 'email' | 'totp';
  onClose: () => void;
  onComplete: () => void;
}

interface SetupData {
  secret?: string;
  qrCode?: string;
  backupCodes?: string[];
  phoneNumber?: string;
  email?: string;
}

export const TwoFactorSetup: React.FC<TwoFactorSetupProps> = ({
  method,
  onClose,
  onComplete
}) => {
  const { t } = useTranslation();
  const [step, setStep] = useState<'setup' | 'verify' | 'backup'>('setup');
  const [loading, setLoading] = useState(false);
  const [setupData, setSetupData] = useState<SetupData>({});
  const [verificationCode, setVerificationCode] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [email, setEmail] = useState('');
  const [copiedSecret, setCopiedSecret] = useState(false);
  const [copiedBackupCodes, setCopiedBackupCodes] = useState(false);

  useEffect(() => {
    if (method === 'totp' && step === 'setup') {
      generateTOTPSetup();
    }
  }, [method, step]);

  const generateTOTPSetup = async () => {
    try {
      setLoading(true);
      const response = await api.post<SetupData>('/api/v1/auth/2fa/setup/totp');
      setSetupData(response.data);
    } catch (error) {
      console.error('Error generating TOTP setup:', error);
      toast.error(t('settings.security.setup.errors.generateFailed'));
    } finally {
      setLoading(false);
    }
  };

  const handleSMSSetup = async () => {
    if (!phoneNumber) {
      toast.error(t('settings.security.setup.phoneRequired'));
      return;
    }

    try {
      setLoading(true);
      await api.post('/api/v1/auth/2fa/setup/sms', { phoneNumber });
      setStep('verify');
      toast.success(t('settings.security.setup.smsSent'));
    } catch (error) {
      console.error('Error setting up SMS 2FA:', error);
      toast.error(t('settings.security.setup.errors.smsFailed'));
    } finally {
      setLoading(false);
    }
  };

  const handleEmailSetup = async () => {
    if (!email) {
      toast.error(t('settings.security.setup.emailRequired'));
      return;
    }

    try {
      setLoading(true);
      await api.post('/api/v1/auth/2fa/setup/email', { email });
      setStep('verify');
      toast.success(t('settings.security.setup.emailSent'));
    } catch (error) {
      console.error('Error setting up email 2FA:', error);
      toast.error(t('settings.security.setup.errors.emailFailed'));
    } finally {
      setLoading(false);
    }
  };

  const handleVerification = async () => {
    if (!verificationCode || verificationCode.length !== 6) {
      toast.error(t('settings.security.setup.invalidCode'));
      return;
    }

    try {
      setLoading(true);
      const response = await api.post<{ backupCodes?: string[] }>(
        '/api/v1/auth/2fa/verify',
        {
          method,
          code: verificationCode,
          phoneNumber: method === 'sms' ? phoneNumber : undefined,
          email: method === 'email' ? email : undefined
        }
      );

      if (response.data.backupCodes) {
        setSetupData({ ...setupData, backupCodes: response.data.backupCodes });
        setStep('backup');
      } else {
        toast.success(t('settings.security.setup.success'));
        onComplete();
      }
    } catch (error: any) {
      console.error('Error verifying 2FA code:', error);
      if (error.response?.status === 400) {
        toast.error(t('settings.security.setup.invalidCode'));
      } else {
        toast.error(t('settings.security.setup.errors.verifyFailed'));
      }
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = (text: string, type: 'secret' | 'backup') => {
    navigator.clipboard.writeText(text);
    if (type === 'secret') {
      setCopiedSecret(true);
      setTimeout(() => setCopiedSecret(false), 2000);
    } else {
      setCopiedBackupCodes(true);
      setTimeout(() => setCopiedBackupCodes(false), 2000);
    }
    toast.success(t('common.copiedToClipboard'));
  };

  const downloadBackupCodes = () => {
    if (!setupData.backupCodes) return;
    
    const content = setupData.backupCodes.join('\n');
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'tutoraide-backup-codes.txt';
    a.click();
    URL.revokeObjectURL(url);
  };

  const getMethodIcon = () => {
    switch (method) {
      case 'sms':
        return <Smartphone className="w-6 h-6" />;
      case 'email':
        return <Mail className="w-6 h-6" />;
      case 'totp':
        return <Key className="w-6 h-6" />;
    }
  };

  const getMethodTitle = () => {
    switch (method) {
      case 'sms':
        return t('settings.security.setup.sms.title');
      case 'email':
        return t('settings.security.setup.email.title');
      case 'totp':
        return t('settings.security.setup.totp.title');
    }
  };

  const renderSetupStep = () => {
    switch (method) {
      case 'sms':
        return (
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              {t('settings.security.setup.sms.description')}
            </p>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('settings.security.setup.phoneNumber')}
              </label>
              <Input
                type="tel"
                value={phoneNumber}
                onChange={(e) => setPhoneNumber(e.target.value)}
                placeholder="+****************"
                leftIcon={<Smartphone className="w-4 h-4" />}
              />
            </div>
            <Button
              onClick={handleSMSSetup}
              disabled={loading || !phoneNumber}
              className="w-full"
            >
              {loading ? <LoadingSpinner size="sm" /> : t('settings.security.setup.sendCode')}
            </Button>
          </div>
        );
      
      case 'email':
        return (
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              {t('settings.security.setup.email.description')}
            </p>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('settings.security.setup.emailAddress')}
              </label>
              <Input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                leftIcon={<Mail className="w-4 h-4" />}
              />
            </div>
            <Button
              onClick={handleEmailSetup}
              disabled={loading || !email}
              className="w-full"
            >
              {loading ? <LoadingSpinner size="sm" /> : t('settings.security.setup.sendCode')}
            </Button>
          </div>
        );
      
      case 'totp':
        return (
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              {t('settings.security.setup.totp.description')}
            </p>
            
            {loading ? (
              <div className="flex justify-center py-8">
                <LoadingSpinner size="lg" />
              </div>
            ) : setupData.qrCode ? (
              <>
                <div className="flex justify-center p-4 bg-white rounded-lg">
                  <QRCode value={setupData.qrCode} size={200} />
                </div>
                
                <div className="p-4 bg-gray-50 rounded-lg">
                  <p className="text-xs text-gray-600 mb-2">
                    {t('settings.security.setup.totp.manualEntry')}
                  </p>
                  <div className="flex items-center gap-2">
                    <code className="flex-1 text-xs font-mono bg-white px-2 py-1 rounded">
                      {setupData.secret}
                    </code>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(setupData.secret!, 'secret')}
                    >
                      {copiedSecret ? <CheckCircle className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                    </Button>
                  </div>
                </div>
                
                <Button
                  onClick={() => setStep('verify')}
                  className="w-full"
                >
                  {t('common.next')}
                </Button>
              </>
            ) : null}
          </div>
        );
    }
  };

  const renderVerifyStep = () => (
    <div className="space-y-4">
      <p className="text-sm text-gray-600">
        {t('settings.security.setup.verify.description')}
      </p>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {t('settings.security.setup.verify.code')}
        </label>
        <Input
          type="text"
          value={verificationCode}
          onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
          placeholder="000000"
          maxLength={6}
          className="text-center text-2xl font-mono"
        />
      </div>
      
      <Button
        onClick={handleVerification}
        disabled={loading || verificationCode.length !== 6}
        className="w-full"
      >
        {loading ? <LoadingSpinner size="sm" /> : t('settings.security.setup.verify.button')}
      </Button>
    </div>
  );

  const renderBackupStep = () => (
    <div className="space-y-4">
      <div className="p-4 bg-yellow-50 rounded-lg">
        <div className="flex items-start gap-3">
          <AlertCircle className="w-5 h-5 text-yellow-600 mt-0.5" />
          <div>
            <p className="text-sm font-medium text-yellow-800">
              {t('settings.security.setup.backup.warning')}
            </p>
            <p className="text-sm text-yellow-700 mt-1">
              {t('settings.security.setup.backup.description')}
            </p>
          </div>
        </div>
      </div>
      
      <Card className="p-4">
        <div className="grid grid-cols-2 gap-2">
          {setupData.backupCodes?.map((code, index) => (
            <code key={index} className="text-sm font-mono bg-gray-50 px-2 py-1 rounded">
              {code}
            </code>
          ))}
        </div>
      </Card>
      
      <div className="flex gap-2">
        <Button
          variant="secondary"
          onClick={() => copyToClipboard(setupData.backupCodes!.join('\n'), 'backup')}
          className="flex-1"
        >
          {copiedBackupCodes ? <CheckCircle className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
          {t('common.copy')}
        </Button>
        <Button
          variant="secondary"
          onClick={downloadBackupCodes}
          className="flex-1"
        >
          {t('common.download')}
        </Button>
      </div>
      
      <Button
        onClick={() => {
          toast.success(t('settings.security.setup.success'));
          onComplete();
        }}
        className="w-full"
      >
        {t('common.done')}
      </Button>
    </div>
  );

  return (
    <Modal
      isOpen={true}
      onClose={onClose}
      title={getMethodTitle()}
      size="md"
    >
      <div className="space-y-6">
        {/* Progress indicator */}
        <div className="flex items-center justify-center gap-2">
          <div className={`flex items-center gap-2 ${step === 'setup' ? 'text-primary-600' : 'text-gray-400'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
              step === 'setup' ? 'bg-primary-100' : 'bg-gray-100'
            }`}>
              1
            </div>
            <span className="text-sm font-medium">{t('settings.security.setup.steps.setup')}</span>
          </div>
          
          <div className="w-8 h-px bg-gray-300" />
          
          <div className={`flex items-center gap-2 ${step === 'verify' ? 'text-primary-600' : 'text-gray-400'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
              step === 'verify' ? 'bg-primary-100' : 'bg-gray-100'
            }`}>
              2
            </div>
            <span className="text-sm font-medium">{t('settings.security.setup.steps.verify')}</span>
          </div>
          
          {method === 'totp' && (
            <>
              <div className="w-8 h-px bg-gray-300" />
              
              <div className={`flex items-center gap-2 ${step === 'backup' ? 'text-primary-600' : 'text-gray-400'}`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  step === 'backup' ? 'bg-primary-100' : 'bg-gray-100'
                }`}>
                  3
                </div>
                <span className="text-sm font-medium">{t('settings.security.setup.steps.backup')}</span>
              </div>
            </>
          )}
        </div>

        {/* Step content */}
        {step === 'setup' && renderSetupStep()}
        {step === 'verify' && renderVerifyStep()}
        {step === 'backup' && renderBackupStep()}
      </div>
    </Modal>
  );
};