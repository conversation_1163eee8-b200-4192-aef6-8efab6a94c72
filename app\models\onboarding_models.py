"""
Onboarding models for progressive user setup flows.
"""

from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field, field_validator, model_validator
from app.models.base import BaseEntity


class OnboardingRole(str, Enum):
    """User roles for onboarding flows."""
    CLIENT = "client"
    TUTOR = "tutor"
    MANAGER = "manager"


class OnboardingStatus(str, Enum):
    """Onboarding completion status."""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    PAUSED = "paused"
    COMPLETED = "completed"
    SKIPPED = "skipped"


class OnboardingStepType(str, Enum):
    """Types of onboarding steps."""
    PROFILE_SETUP = "profile_setup"
    VERIFICATION = "verification"
    PREFERENCES = "preferences"
    DOCUMENT_UPLOAD = "document_upload"
    CONTACT_INFO = "contact_info"
    EMERGENCY_CONTACTS = "emergency_contacts"
    AVAILABILITY = "availability"
    SUBJECTS = "subjects"
    RATES = "rates"
    LOCATION = "location"
    CONSENT = "consent"
    WELCOME = "welcome"
    TUTORIAL = "tutorial"


class OnboardingStepStatus(str, Enum):
    """Status of individual onboarding steps."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    SKIPPED = "skipped"
    BLOCKED = "blocked"


class OnboardingFlowBase(BaseModel):
    """Base onboarding flow model."""
    user_id: int = Field(..., description="User ID")
    role: OnboardingRole = Field(..., description="User role for this onboarding")
    flow_name: str = Field(..., description="Name of the onboarding flow")
    status: OnboardingStatus = Field(OnboardingStatus.NOT_STARTED, description="Overall flow status")
    current_step_order: Optional[int] = Field(None, description="Current step order number")
    completion_percentage: float = Field(0.0, description="Completion percentage (0-100)")
    started_at: Optional[datetime] = Field(None, description="When onboarding was started")
    completed_at: Optional[datetime] = Field(None, description="When onboarding was completed")
    paused_at: Optional[datetime] = Field(None, description="When onboarding was paused")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional flow metadata")
    
    @field_validator('completion_percentage')
    @classmethod
    def validate_completion_percentage(cls, v: float) -> float:
        """Validate completion percentage is between 0 and 100."""
        if not 0.0 <= v <= 100.0:
            raise ValueError('Completion percentage must be between 0 and 100')
        return v
    
    @field_validator('flow_name')
    @classmethod
    def validate_flow_name(cls, v: str) -> str:
        """Validate flow name is not empty."""
        if not v or not v.strip():
            raise ValueError('Flow name cannot be empty')
        return v.strip()


class OnboardingFlowCreate(OnboardingFlowBase):
    """Model for creating new onboarding flows."""
    pass


class OnboardingFlow(OnboardingFlowBase, BaseEntity):
    """Complete onboarding flow model."""
    flow_id: int = Field(..., description="Onboarding flow ID")
    
    def is_completed(self) -> bool:
        """Check if the onboarding flow is completed."""
        return self.status == OnboardingStatus.COMPLETED
    
    def is_in_progress(self) -> bool:
        """Check if the onboarding flow is in progress."""
        return self.status == OnboardingStatus.IN_PROGRESS
    
    def can_resume(self) -> bool:
        """Check if the onboarding flow can be resumed."""
        return self.status in [OnboardingStatus.IN_PROGRESS, OnboardingStatus.PAUSED]


class OnboardingStepBase(BaseModel):
    """Base onboarding step model."""
    flow_id: int = Field(..., description="Parent flow ID")
    step_order: int = Field(..., description="Step order in the flow (1-based)")
    step_type: OnboardingStepType = Field(..., description="Type of onboarding step")
    title: str = Field(..., description="Step title")
    description: Optional[str] = Field(None, description="Step description")
    is_required: bool = Field(True, description="Whether this step is required")
    is_skippable: bool = Field(False, description="Whether this step can be skipped")
    estimated_minutes: Optional[int] = Field(None, description="Estimated completion time in minutes")
    instructions: Optional[str] = Field(None, description="Detailed instructions for the step")
    validation_rules: Dict[str, Any] = Field(default_factory=dict, description="Validation rules for step completion")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional step metadata")
    
    @field_validator('step_order')
    @classmethod
    def validate_step_order(cls, v: int) -> int:
        """Validate step order is positive."""
        if v < 1:
            raise ValueError('Step order must be positive')
        return v
    
    @field_validator('title')
    @classmethod
    def validate_title(cls, v: str) -> str:
        """Validate title is not empty."""
        if not v or not v.strip():
            raise ValueError('Step title cannot be empty')
        return v.strip()
    
    @field_validator('estimated_minutes')
    @classmethod
    def validate_estimated_minutes(cls, v: Optional[int]) -> Optional[int]:
        """Validate estimated minutes is positive if provided."""
        if v is not None and v < 1:
            raise ValueError('Estimated minutes must be positive')
        return v


class OnboardingStepCreate(OnboardingStepBase):
    """Model for creating new onboarding steps."""
    pass


class OnboardingStep(OnboardingStepBase, BaseEntity):
    """Complete onboarding step model."""
    step_id: int = Field(..., description="Onboarding step ID")
    status: OnboardingStepStatus = Field(OnboardingStepStatus.PENDING, description="Step completion status")
    started_at: Optional[datetime] = Field(None, description="When step was started")
    completed_at: Optional[datetime] = Field(None, description="When step was completed")
    skipped_at: Optional[datetime] = Field(None, description="When step was skipped")
    skip_reason: Optional[str] = Field(None, description="Reason for skipping the step")
    completion_data: Dict[str, Any] = Field(default_factory=dict, description="Data collected during step completion")
    validation_errors: List[str] = Field(default_factory=list, description="Current validation errors")
    
    def is_completed(self) -> bool:
        """Check if the step is completed."""
        return self.status == OnboardingStepStatus.COMPLETED
    
    def is_skipped(self) -> bool:
        """Check if the step was skipped."""
        return self.status == OnboardingStepStatus.SKIPPED
    
    def is_blocked(self) -> bool:
        """Check if the step is blocked."""
        return self.status == OnboardingStepStatus.BLOCKED
    
    def can_start(self) -> bool:
        """Check if the step can be started."""
        return self.status == OnboardingStepStatus.PENDING
    
    def can_skip(self) -> bool:
        """Check if the step can be skipped."""
        return self.is_skippable and self.status in [OnboardingStepStatus.PENDING, OnboardingStepStatus.IN_PROGRESS]


class OnboardingProgressBase(BaseModel):
    """Base model for onboarding progress tracking."""
    flow_id: int = Field(..., description="Parent flow ID")
    user_id: int = Field(..., description="User ID")
    current_step_id: Optional[int] = Field(None, description="Current step ID")
    completed_steps: List[int] = Field(default_factory=list, description="List of completed step IDs")
    skipped_steps: List[int] = Field(default_factory=list, description="List of skipped step IDs")
    blocked_steps: List[int] = Field(default_factory=list, description="List of blocked step IDs")
    total_steps: int = Field(..., description="Total number of steps in the flow")
    completion_percentage: float = Field(0.0, description="Overall completion percentage")
    estimated_time_remaining_minutes: Optional[int] = Field(None, description="Estimated time remaining")
    last_activity_at: Optional[datetime] = Field(None, description="Last activity timestamp")
    
    @field_validator('completion_percentage')
    @classmethod
    def validate_completion_percentage(cls, v: float) -> float:
        """Validate completion percentage."""
        if not 0.0 <= v <= 100.0:
            raise ValueError('Completion percentage must be between 0 and 100')
        return v
    
    @field_validator('total_steps')
    @classmethod
    def validate_total_steps(cls, v: int) -> int:
        """Validate total steps is positive."""
        if v < 1:
            raise ValueError('Total steps must be positive')
        return v


class OnboardingProgress(OnboardingProgressBase, BaseEntity):
    """Complete onboarding progress model."""
    progress_id: int = Field(..., description="Progress tracking ID")
    
    def get_next_step_id(self) -> Optional[int]:
        """Get the next step ID to complete."""
        return self.current_step_id
    
    def calculate_completion_percentage(self) -> float:
        """Calculate completion percentage based on completed steps."""
        if self.total_steps == 0:
            return 100.0
        completed_count = len(self.completed_steps)
        return (completed_count / self.total_steps) * 100.0
    
    def get_remaining_steps_count(self) -> int:
        """Get count of remaining steps."""
        return self.total_steps - len(self.completed_steps)
    
    def is_step_accessible(self, step_order: int) -> bool:
        """Check if a step is accessible based on current progress."""
        # Step is accessible if it's the current step or if all previous required steps are completed
        if self.current_step_id is None:
            return step_order == 1
        return step_order <= (self.current_step_id + 1)


class OnboardingStepUpdate(BaseModel):
    """Model for updating onboarding step progress."""
    status: Optional[OnboardingStepStatus] = Field(None, description="New step status")
    completion_data: Optional[Dict[str, Any]] = Field(None, description="Step completion data")
    skip_reason: Optional[str] = Field(None, description="Reason for skipping (if status is SKIPPED)")
    
    @model_validator(mode='after')
    def validate_skip_reason(self) -> 'OnboardingStepUpdate':
        """Validate skip reason is provided when status is SKIPPED."""
        if self.status == OnboardingStepStatus.SKIPPED and not self.skip_reason:
            raise ValueError('Skip reason is required when status is SKIPPED')
        return self


class OnboardingFlowSummary(BaseModel):
    """Summary model for onboarding flow status."""
    flow_id: int = Field(..., description="Flow ID")
    user_id: int = Field(..., description="User ID")
    role: OnboardingRole = Field(..., description="User role")
    flow_name: str = Field(..., description="Flow name")
    status: OnboardingStatus = Field(..., description="Flow status")
    completion_percentage: float = Field(..., description="Completion percentage")
    current_step_title: Optional[str] = Field(None, description="Current step title")
    current_step_order: Optional[int] = Field(None, description="Current step order")
    total_steps: int = Field(..., description="Total steps in flow")
    completed_steps_count: int = Field(..., description="Number of completed steps")
    estimated_time_remaining_minutes: Optional[int] = Field(None, description="Estimated time remaining")
    started_at: Optional[datetime] = Field(None, description="When flow was started")
    last_activity_at: Optional[datetime] = Field(None, description="Last activity")


class OnboardingStepSummary(BaseModel):
    """Summary model for onboarding step status."""
    step_id: int = Field(..., description="Step ID")
    step_order: int = Field(..., description="Step order")
    step_type: OnboardingStepType = Field(..., description="Step type")
    title: str = Field(..., description="Step title")
    description: Optional[str] = Field(None, description="Step description")
    status: OnboardingStepStatus = Field(..., description="Step status")
    is_required: bool = Field(..., description="Whether step is required")
    is_skippable: bool = Field(..., description="Whether step can be skipped")
    estimated_minutes: Optional[int] = Field(None, description="Estimated completion time")
    is_accessible: bool = Field(..., description="Whether step is currently accessible")
    validation_errors: List[str] = Field(default_factory=list, description="Current validation errors")
    completed_at: Optional[datetime] = Field(None, description="When step was completed")


class OnboardingFlowTemplate(BaseModel):
    """Template for creating onboarding flows."""
    name: str = Field(..., description="Template name")
    role: OnboardingRole = Field(..., description="Target user role")
    description: Optional[str] = Field(None, description="Template description")
    steps: List[OnboardingStepCreate] = Field(..., description="Template steps")
    estimated_total_minutes: Optional[int] = Field(None, description="Total estimated completion time")
    is_default: bool = Field(False, description="Whether this is the default template for the role")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Template metadata")
    
    @field_validator('steps')
    @classmethod
    def validate_steps_order(cls, v: List[OnboardingStepCreate]) -> List[OnboardingStepCreate]:
        """Validate steps have sequential order numbers."""
        if not v:
            raise ValueError('Template must have at least one step')
        
        orders = [step.step_order for step in v]
        expected_orders = list(range(1, len(v) + 1))
        
        if sorted(orders) != expected_orders:
            raise ValueError('Steps must have sequential order numbers starting from 1')
        
        return v
