import React from 'react';
import { Check } from 'lucide-react';

interface CheckboxProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type'> {
  label?: string;
  error?: string;
}

export const Checkbox: React.FC<CheckboxProps> = ({ 
  label, 
  error, 
  className = '',
  checked,
  ...props 
}) => {
  return (
    <div className={`flex items-start ${className}`}>
      <div className="relative">
        <input
          type="checkbox"
          checked={checked}
          className="sr-only"
          {...props}
        />
        <div className={`
          w-5 h-5 rounded border-2 cursor-pointer transition-all
          ${checked 
            ? 'bg-red-600 border-red-600' 
            : 'bg-white border-gray-300 hover:border-gray-400'
          }
          ${props.disabled ? 'opacity-50 cursor-not-allowed' : ''}
        `}>
          {checked && (
            <Check className="w-3 h-3 text-white absolute top-0.5 left-0.5" />
          )}
        </div>
      </div>
      {label && (
        <label 
          htmlFor={props.id}
          className={`ml-2 text-sm ${props.disabled ? 'text-gray-500' : 'text-gray-700'}`}
        >
          {label}
        </label>
      )}
      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}
    </div>
  );
};