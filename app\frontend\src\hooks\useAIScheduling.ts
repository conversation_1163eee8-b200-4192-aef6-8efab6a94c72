import { useState, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';

export interface AISchedulingSuggestion {
  tutor_id: number;
  tutor_name: string;
  client_id: number;
  dependant_id?: number;
  suggested_date: string;
  suggested_start_time: string;
  suggested_end_time: string;
  duration: number;
  subject_area: string;
  session_type: string;
  priority: 'high' | 'medium' | 'low';
  confidence_score: number;
  reasons: string[];
  explanation: string;
  alternative_slots: any[];
  estimated_cost?: number;
  estimated_travel_time?: number;
}

export interface AISchedulingSuggestionsResponse {
  suggestions: AISchedulingSuggestion[];
  total_count: number;
  generation_time_ms: number;
  analysis_summary: {
    total_tutors_analyzed: number;
    average_confidence: number;
    high_priority_count: number;
    date_range_days: number;
    subject_area: string;
  };
}

export interface SchedulingSuggestionRequest {
  client_id: number;
  subject_area: string;
  duration?: number;
  session_type?: string;
  dependant_id?: number;
  preferred_start_date?: string;
  preferred_end_date?: string;
  max_suggestions?: number;
}

export interface RecurringScheduleRequest {
  client_id: number;
  subject_area: string;
  sessions_per_week: number;
  duration?: number;
  weeks_ahead?: number;
}

export interface ReschedulingRequest {
  appointment_id: number;
  reason?: string;
}

export const useAIScheduling = () => {
  const { token } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const apiCall = useCallback(async (endpoint: string, data: any) => {
    if (!token) {
      throw new Error('Authentication required');
    }

    const response = await fetch(`/api/v1/ai-scheduling/${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `HTTP ${response.status}`);
    }

    return response.json();
  }, [token]);

  const getSchedulingSuggestions = useCallback(async (
    request: SchedulingSuggestionRequest
  ): Promise<AISchedulingSuggestionsResponse> => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiCall('suggestions', request);
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get suggestions';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [apiCall]);

  const getRecurringSchedule = useCallback(async (
    request: RecurringScheduleRequest
  ): Promise<AISchedulingSuggestionsResponse> => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiCall('recurring-schedule', request);
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get recurring schedule';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [apiCall]);

  const getReschedulingSuggestions = useCallback(async (
    request: ReschedulingRequest
  ): Promise<AISchedulingSuggestionsResponse> => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiCall('reschedule-suggestions', request);
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get rescheduling suggestions';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [apiCall]);

  const getTutorWorkloadAnalysis = useCallback(async (
    tutorId: number,
    daysAhead: number = 14
  ) => {
    if (!token) {
      throw new Error('Authentication required');
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(
        `/api/v1/ai-scheduling/tutor-workload-analysis/${tutorId}?days_ahead=${daysAhead}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP ${response.status}`);
      }

      return response.json();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get workload analysis';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [token]);

  const getSchedulingInsights = useCallback(async (
    clientId?: number,
    daysBack: number = 90
  ) => {
    if (!token) {
      throw new Error('Authentication required');
    }

    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        days_back: daysBack.toString()
      });
      
      if (clientId) {
        params.append('client_id', clientId.toString());
      }

      const response = await fetch(
        `/api/v1/ai-scheduling/scheduling-insights?${params}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP ${response.status}`);
      }

      return response.json();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get scheduling insights';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [token]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    loading,
    error,
    clearError,
    getSchedulingSuggestions,
    getRecurringSchedule,
    getReschedulingSuggestions,
    getTutorWorkloadAnalysis,
    getSchedulingInsights
  };
};

export default useAIScheduling;