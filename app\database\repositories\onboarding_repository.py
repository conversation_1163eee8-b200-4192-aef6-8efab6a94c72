"""
Repository for onboarding flow and step management.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
import asyncpg
from app.database.repositories.base import BaseRepository
from app.models.onboarding_models import (
    OnboardingFlow,
    OnboardingFlowCreate,
    OnboardingStep,
    OnboardingStepCreate,
    OnboardingProgress,
    OnboardingStepUpdate,
    OnboardingFlowSummary,
    OnboardingStepSummary,
    OnboardingRole,
    OnboardingStatus,
    OnboardingStepStatus,
    OnboardingStepType
)
from app.core.logging import TutorAideLogger


class OnboardingRepository(BaseRepository[OnboardingFlow]):
    """Repository for onboarding flow operations."""
    
    def __init__(self):
        super().__init__()
        self.logger = TutorAideLogger.get_logger(__name__)
    
    async def create_flow(self, conn: asyncpg.Connection, flow_data: OnboardingFlowCreate) -> OnboardingFlow:
        """Create a new onboarding flow."""
        try:
            query = """
                INSERT INTO onboarding_flows (
                    user_id, role, flow_name, status, current_step_order,
                    completion_percentage, started_at, completed_at, paused_at, metadata
                ) VALUES (
                    %(user_id)s, %(role)s, %(flow_name)s, %(status)s, %(current_step_order)s,
                    %(completion_percentage)s, %(started_at)s, %(completed_at)s, %(paused_at)s, %(metadata)s
                )
                RETURNING flow_id, created_at, updated_at
            """
            
            result = await conn.execute(query, {
                'user_id': flow_data.user_id,
                'role': flow_data.role.value,
                'flow_name': flow_data.flow_name,
                'status': flow_data.status.value,
                'current_step_order': flow_data.current_step_order,
                'completion_percentage': flow_data.completion_percentage,
                'started_at': flow_data.started_at,
                'completed_at': flow_data.completed_at,
                'paused_at': flow_data.paused_at,
                'metadata': flow_data.metadata
            })
            
            row = result.fetchone()
            if not row:
                raise ValueError("Failed to create onboarding flow")
            
            return OnboardingFlow(
                flow_id=row[0],
                user_id=flow_data.user_id,
                role=flow_data.role,
                flow_name=flow_data.flow_name,
                status=flow_data.status,
                current_step_order=flow_data.current_step_order,
                completion_percentage=flow_data.completion_percentage,
                started_at=flow_data.started_at,
                completed_at=flow_data.completed_at,
                paused_at=flow_data.paused_at,
                metadata=flow_data.metadata,
                created_at=row[1],
                updated_at=row[2]
            )
            
        except Exception as e:
            self.logger.error(f"Error creating onboarding flow: {e}")
            raise
    
    async def get_flow_by_id(self, conn: asyncpg.Connection, flow_id: int) -> Optional[OnboardingFlow]:
        """Get onboarding flow by ID."""
        try:
            query = """
                SELECT flow_id, user_id, role, flow_name, status, current_step_order,
                       completion_percentage, started_at, completed_at, paused_at, metadata,
                       created_at, updated_at, deleted_at
                FROM onboarding_flows
                WHERE flow_id = %(flow_id)s AND deleted_at IS NULL
            """
            
            result = await conn.execute(query, {'flow_id': flow_id})
            row = result.fetchone()
            
            if not row:
                return None
            
            return OnboardingFlow(
                flow_id=row[0],
                user_id=row[1],
                role=OnboardingRole(row[2]),
                flow_name=row[3],
                status=OnboardingStatus(row[4]),
                current_step_order=row[5],
                completion_percentage=row[6],
                started_at=row[7],
                completed_at=row[8],
                paused_at=row[9],
                metadata=row[10] or {},
                created_at=row[11],
                updated_at=row[12],
                deleted_at=row[13]
            )
            
        except Exception as e:
            self.logger.error(f"Error getting onboarding flow {flow_id}: {e}")
            raise
    
    async def get_flows_by_user(self, conn: asyncpg.Connection, user_id: int, 
                               role: Optional[OnboardingRole] = None) -> List[OnboardingFlow]:
        """Get all onboarding flows for a user."""
        try:
            query = """
                SELECT flow_id, user_id, role, flow_name, status, current_step_order,
                       completion_percentage, started_at, completed_at, paused_at, metadata,
                       created_at, updated_at, deleted_at
                FROM onboarding_flows
                WHERE user_id = %(user_id)s
            """
            params = {'user_id': user_id}
            
            if role:
                query += " AND role = %(role)s"
                params['role'] = role.value
            
            query += " AND deleted_at IS NULL ORDER BY created_at DESC"
            
            result = await conn.execute(query, params)
            rows = result.fetchall()
            
            return [
                OnboardingFlow(
                    flow_id=row[0],
                    user_id=row[1],
                    role=OnboardingRole(row[2]),
                    flow_name=row[3],
                    status=OnboardingStatus(row[4]),
                    current_step_order=row[5],
                    completion_percentage=row[6],
                    started_at=row[7],
                    completed_at=row[8],
                    paused_at=row[9],
                    metadata=row[10] or {},
                    created_at=row[11],
                    updated_at=row[12],
                    deleted_at=row[13]
                )
                for row in rows
            ]
            
        except Exception as e:
            self.logger.error(f"Error getting onboarding flows for user {user_id}: {e}")
            raise
    
    async def update_flow_status(self, conn: asyncpg.Connection, flow_id: int, 
                                status: OnboardingStatus, 
                                completion_percentage: Optional[float] = None,
                                current_step_order: Optional[int] = None) -> Optional[OnboardingFlow]:
        """Update onboarding flow status and progress."""
        try:
            # Build dynamic update query
            update_fields = ['status = %(status)s', 'updated_at = CURRENT_TIMESTAMP']
            params = {'flow_id': flow_id, 'status': status.value}
            
            if completion_percentage is not None:
                update_fields.append('completion_percentage = %(completion_percentage)s')
                params['completion_percentage'] = completion_percentage
            
            if current_step_order is not None:
                update_fields.append('current_step_order = %(current_step_order)s')
                params['current_step_order'] = current_step_order
            
            # Set timestamps based on status
            if status == OnboardingStatus.IN_PROGRESS:
                update_fields.append('started_at = COALESCE(started_at, CURRENT_TIMESTAMP)')
                update_fields.append('paused_at = NULL')
            elif status == OnboardingStatus.COMPLETED:
                update_fields.append('completed_at = CURRENT_TIMESTAMP')
                update_fields.append('paused_at = NULL')
            elif status == OnboardingStatus.PAUSED:
                update_fields.append('paused_at = CURRENT_TIMESTAMP')
            
            query = f"""
                UPDATE onboarding_flows
                SET {', '.join(update_fields)}
                WHERE flow_id = %(flow_id)s AND deleted_at IS NULL
            """
            
            await conn.execute(query, params)
            
            # Return updated flow
            return await self.get_flow_by_id(conn, flow_id)
            
        except Exception as e:
            self.logger.error(f"Error updating onboarding flow {flow_id}: {e}")
            raise
    
    async def delete_flow(self, conn: asyncpg.Connection, flow_id: int) -> bool:
        """Soft delete an onboarding flow."""
        try:
            query = """
                UPDATE onboarding_flows
                SET deleted_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
                WHERE flow_id = %(flow_id)s AND deleted_at IS NULL
            """
            
            result = await conn.execute(query, {'flow_id': flow_id})
            return result.rowcount > 0
            
        except Exception as e:
            self.logger.error(f"Error deleting onboarding flow {flow_id}: {e}")
            raise


class OnboardingStepRepository(BaseRepository[OnboardingStep]):
    """Repository for onboarding step operations."""
    
    def __init__(self):
        super().__init__()
        self.logger = TutorAideLogger.get_logger(__name__)
    
    async def create_step(self, conn: asyncpg.Connection, step_data: OnboardingStepCreate) -> OnboardingStep:
        """Create a new onboarding step."""
        try:
            query = """
                INSERT INTO onboarding_steps (
                    flow_id, step_order, step_type, title, description, is_required,
                    is_skippable, estimated_minutes, instructions, validation_rules, metadata
                ) VALUES (
                    %(flow_id)s, %(step_order)s, %(step_type)s, %(title)s, %(description)s,
                    %(is_required)s, %(is_skippable)s, %(estimated_minutes)s, %(instructions)s,
                    %(validation_rules)s, %(metadata)s
                )
                RETURNING step_id, created_at, updated_at
            """
            
            result = await conn.execute(query, {
                'flow_id': step_data.flow_id,
                'step_order': step_data.step_order,
                'step_type': step_data.step_type.value,
                'title': step_data.title,
                'description': step_data.description,
                'is_required': step_data.is_required,
                'is_skippable': step_data.is_skippable,
                'estimated_minutes': step_data.estimated_minutes,
                'instructions': step_data.instructions,
                'validation_rules': step_data.validation_rules,
                'metadata': step_data.metadata
            })
            
            row = result.fetchone()
            if not row:
                raise ValueError("Failed to create onboarding step")
            
            return OnboardingStep(
                step_id=row[0],
                flow_id=step_data.flow_id,
                step_order=step_data.step_order,
                step_type=step_data.step_type,
                title=step_data.title,
                description=step_data.description,
                is_required=step_data.is_required,
                is_skippable=step_data.is_skippable,
                estimated_minutes=step_data.estimated_minutes,
                instructions=step_data.instructions,
                validation_rules=step_data.validation_rules,
                metadata=step_data.metadata,
                status=OnboardingStepStatus.PENDING,
                started_at=None,
                completed_at=None,
                skipped_at=None,
                skip_reason=None,
                completion_data={},
                validation_errors=[],
                created_at=row[1],
                updated_at=row[2]
            )
            
        except Exception as e:
            self.logger.error(f"Error creating onboarding step: {e}")
            raise
    
    async def get_steps_by_flow(self, conn: asyncpg.Connection, flow_id: int) -> List[OnboardingStep]:
        """Get all steps for an onboarding flow."""
        try:
            query = """
                SELECT step_id, flow_id, step_order, step_type, title, description,
                       is_required, is_skippable, estimated_minutes, instructions,
                       validation_rules, metadata, status, started_at, completed_at,
                       skipped_at, skip_reason, completion_data, validation_errors,
                       created_at, updated_at, deleted_at
                FROM onboarding_steps
                WHERE flow_id = %(flow_id)s AND deleted_at IS NULL
                ORDER BY step_order ASC
            """
            
            result = await conn.execute(query, {'flow_id': flow_id})
            rows = result.fetchall()
            
            return [
                OnboardingStep(
                    step_id=row[0],
                    flow_id=row[1],
                    step_order=row[2],
                    step_type=OnboardingStepType(row[3]),
                    title=row[4],
                    description=row[5],
                    is_required=row[6],
                    is_skippable=row[7],
                    estimated_minutes=row[8],
                    instructions=row[9],
                    validation_rules=row[10] or {},
                    metadata=row[11] or {},
                    status=OnboardingStepStatus(row[12]),
                    started_at=row[13],
                    completed_at=row[14],
                    skipped_at=row[15],
                    skip_reason=row[16],
                    completion_data=row[17] or {},
                    validation_errors=row[18] or [],
                    created_at=row[19],
                    updated_at=row[20],
                    deleted_at=row[21]
                )
                for row in rows
            ]
            
        except Exception as e:
            self.logger.error(f"Error getting steps for flow {flow_id}: {e}")
            raise
    
    async def get_step_by_id(self, conn: asyncpg.Connection, step_id: int) -> Optional[OnboardingStep]:
        """Get onboarding step by ID."""
        try:
            query = """
                SELECT step_id, flow_id, step_order, step_type, title, description,
                       is_required, is_skippable, estimated_minutes, instructions,
                       validation_rules, metadata, status, started_at, completed_at,
                       skipped_at, skip_reason, completion_data, validation_errors,
                       created_at, updated_at, deleted_at
                FROM onboarding_steps
                WHERE step_id = %(step_id)s AND deleted_at IS NULL
            """
            
            result = await conn.execute(query, {'step_id': step_id})
            row = result.fetchone()
            
            if not row:
                return None
            
            return OnboardingStep(
                step_id=row[0],
                flow_id=row[1],
                step_order=row[2],
                step_type=OnboardingStepType(row[3]),
                title=row[4],
                description=row[5],
                is_required=row[6],
                is_skippable=row[7],
                estimated_minutes=row[8],
                instructions=row[9],
                validation_rules=row[10] or {},
                metadata=row[11] or {},
                status=OnboardingStepStatus(row[12]),
                started_at=row[13],
                completed_at=row[14],
                skipped_at=row[15],
                skip_reason=row[16],
                completion_data=row[17] or {},
                validation_errors=row[18] or [],
                created_at=row[19],
                updated_at=row[20],
                deleted_at=row[21]
            )
            
        except Exception as e:
            self.logger.error(f"Error getting onboarding step {step_id}: {e}")
            raise
    
    async def update_step(self, conn: asyncpg.Connection, step_id: int, 
                         update_data: OnboardingStepUpdate) -> Optional[OnboardingStep]:
        """Update onboarding step progress."""
        try:
            # Build dynamic update query
            update_fields = ['updated_at = CURRENT_TIMESTAMP']
            params = {'step_id': step_id}
            
            if update_data.status is not None:
                update_fields.append('status = %(status)s')
                params['status'] = update_data.status.value
                
                # Set timestamps based on status
                if update_data.status == OnboardingStepStatus.IN_PROGRESS:
                    update_fields.append('started_at = COALESCE(started_at, CURRENT_TIMESTAMP)')
                elif update_data.status == OnboardingStepStatus.COMPLETED:
                    update_fields.append('completed_at = CURRENT_TIMESTAMP')
                elif update_data.status == OnboardingStepStatus.SKIPPED:
                    update_fields.append('skipped_at = CURRENT_TIMESTAMP')
                    if update_data.skip_reason:
                        update_fields.append('skip_reason = %(skip_reason)s')
                        params['skip_reason'] = update_data.skip_reason
            
            if update_data.completion_data is not None:
                update_fields.append('completion_data = %(completion_data)s')
                params['completion_data'] = update_data.completion_data
            
            query = f"""
                UPDATE onboarding_steps
                SET {', '.join(update_fields)}
                WHERE step_id = %(step_id)s AND deleted_at IS NULL
            """
            
            await conn.execute(query, params)
            
            # Return updated step
            return await self.get_step_by_id(conn, step_id)
            
        except Exception as e:
            self.logger.error(f"Error updating onboarding step {step_id}: {e}")
            raise
    
    async def get_current_step(self, conn: asyncpg.Connection, flow_id: int) -> Optional[OnboardingStep]:
        """Get the current step for an onboarding flow."""
        try:
            query = """
                SELECT step_id, flow_id, step_order, step_type, title, description,
                       is_required, is_skippable, estimated_minutes, instructions,
                       validation_rules, metadata, status, started_at, completed_at,
                       skipped_at, skip_reason, completion_data, validation_errors,
                       created_at, updated_at, deleted_at
                FROM onboarding_steps
                WHERE flow_id = %(flow_id)s AND deleted_at IS NULL
                  AND status IN ('pending', 'in_progress')
                ORDER BY step_order ASC
                LIMIT 1
            """
            
            result = await conn.execute(query, {'flow_id': flow_id})
            row = result.fetchone()
            
            if not row:
                return None
            
            return OnboardingStep(
                step_id=row[0],
                flow_id=row[1],
                step_order=row[2],
                step_type=OnboardingStepType(row[3]),
                title=row[4],
                description=row[5],
                is_required=row[6],
                is_skippable=row[7],
                estimated_minutes=row[8],
                instructions=row[9],
                validation_rules=row[10] or {},
                metadata=row[11] or {},
                status=OnboardingStepStatus(row[12]),
                started_at=row[13],
                completed_at=row[14],
                skipped_at=row[15],
                skip_reason=row[16],
                completion_data=row[17] or {},
                validation_errors=row[18] or [],
                created_at=row[19],
                updated_at=row[20],
                deleted_at=row[21]
            )
            
        except Exception as e:
            self.logger.error(f"Error getting current step for flow {flow_id}: {e}")
            raise
    
    async def get_progress_stats(self, conn: asyncpg.Connection, flow_id: int) -> Dict[str, int]:
        """Get progress statistics for an onboarding flow."""
        try:
            query = """
                SELECT 
                    COUNT(*) as total_steps,
                    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_steps,
                    COUNT(CASE WHEN status = 'skipped' THEN 1 END) as skipped_steps,
                    COUNT(CASE WHEN status = 'blocked' THEN 1 END) as blocked_steps,
                    COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress_steps,
                    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_steps
                FROM onboarding_steps
                WHERE flow_id = %(flow_id)s AND deleted_at IS NULL
            """
            
            result = await conn.execute(query, {'flow_id': flow_id})
            row = result.fetchone()
            
            if not row:
                return {
                    'total_steps': 0,
                    'completed_steps': 0,
                    'skipped_steps': 0,
                    'blocked_steps': 0,
                    'in_progress_steps': 0,
                    'pending_steps': 0
                }
            
            return {
                'total_steps': row[0],
                'completed_steps': row[1],
                'skipped_steps': row[2],
                'blocked_steps': row[3],
                'in_progress_steps': row[4],
                'pending_steps': row[5]
            }
            
        except Exception as e:
            self.logger.error(f"Error getting progress stats for flow {flow_id}: {e}")
            raise
