import React from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { Heart } from 'lucide-react';

export const Footer: React.FC = () => {
  const { t } = useTranslation();
  const currentYear = new Date().getFullYear();

  return (
    <footer className="mt-auto border-t border-border-primary bg-white">
      <div className="max-w-7xl mx-auto px-6 py-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company info */}
          <div className="col-span-1 md:col-span-2">
            <img
              src="/logo_tutoraide.jpg"
              alt="TutorAide"
              className="h-8 w-auto mb-4"
            />
            <p className="text-sm text-text-secondary mb-4">
              {t('footer.description')}
            </p>
            <div className="flex items-center gap-4">
              <a
                href="https://www.tutoraide.ca"
                target="_blank"
                rel="noopener noreferrer"
                className="text-sm text-text-secondary hover:text-accent-red transition-colors"
              >
                {t('footer.website')}
              </a>
              <span className="text-text-muted">•</span>
              <a
                href="mailto:<EMAIL>"
                className="text-sm text-text-secondary hover:text-accent-red transition-colors"
              >
                <EMAIL>
              </a>
            </div>
          </div>

          {/* Quick links */}
          <div>
            <h3 className="text-sm font-semibold text-text-primary mb-4">
              {t('footer.quickLinks')}
            </h3>
            <ul className="space-y-2">
              <li>
                <a
                  href="/help"
                  className="text-sm text-text-secondary hover:text-accent-red transition-colors"
                >
                  {t('footer.help')}
                </a>
              </li>
              <li>
                <a
                  href="/privacy"
                  className="text-sm text-text-secondary hover:text-accent-red transition-colors"
                >
                  {t('footer.privacy')}
                </a>
              </li>
              <li>
                <a
                  href="/terms"
                  className="text-sm text-text-secondary hover:text-accent-red transition-colors"
                >
                  {t('footer.terms')}
                </a>
              </li>
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h3 className="text-sm font-semibold text-text-primary mb-4">
              {t('footer.contact')}
            </h3>
            <ul className="space-y-2 text-sm text-text-secondary">
              <li>{t('footer.support')}: <EMAIL></li>
              <li>{t('footer.billing')}: <EMAIL></li>
              <li>{t('footer.phone')}: 1-888-TUTOR-AI</li>
            </ul>
          </div>
        </div>

        <div className="mt-8 pt-8 border-t border-border-primary">
          <div className="flex flex-col md:flex-row items-center justify-between gap-4">
            <p className="text-sm text-text-muted">
              © {currentYear} TutorAide. {t('footer.rights')}
            </p>
            <p className="text-sm text-text-muted flex items-center gap-1">
              {t('footer.made')} <Heart className="w-3 h-3 text-accent-red" /> {t('footer.location')}
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;