"""
User Preference Models for the unified preference system.
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, field_validator
from enum import Enum


class UserPreference(BaseModel):
    """Complete user preferences model."""
    
    user_id: int
    
    # Language preferences
    preferred_language: str = 'en'
    quebec_french_preference: bool = True
    
    # Date and time formatting
    date_format: str = 'medium'  # short, medium, long, full
    time_format: str = 'auto'    # auto, 12h, 24h
    
    # Currency and numbers
    currency_display: str = 'symbol'  # symbol, code, name
    number_precision: int = 2
    
    # UI preferences (flexible JSON)
    ui_settings: Dict[str, Any] = Field(default_factory=dict)
    
    # Notification preferences (by type and channel)
    notification_settings: Dict[str, Dict[str, bool]] = Field(default_factory=dict)
    
    # Quick actions and pinned items
    pinned_actions: List[str] = Field(default_factory=list)
    
    # Timestamps
    created_at: datetime
    updated_at: datetime
    
    @field_validator('preferred_language')
    def validate_language(cls, v):
        if v not in ['en', 'fr']:
            raise ValueError('Language must be en or fr')
        return v
    
    @field_validator('date_format')
    def validate_date_format(cls, v):
        if v not in ['short', 'medium', 'long', 'full']:
            raise ValueError('Invalid date format')
        return v
    
    @field_validator('time_format')
    def validate_time_format(cls, v):
        if v not in ['auto', '12h', '24h']:
            raise ValueError('Invalid time format')
        return v
    
    @field_validator('currency_display')
    def validate_currency_display(cls, v):
        if v not in ['symbol', 'code', 'name']:
            raise ValueError('Invalid currency display')
        return v
    
    @field_validator('number_precision')
    def validate_precision(cls, v):
        if not 0 <= v <= 6:
            raise ValueError('Number precision must be between 0 and 6')
        return v


class UserPreferenceUpdate(BaseModel):
    """Model for updating user preferences."""
    
    # Language preferences
    preferred_language: Optional[str] = None
    quebec_french_preference: Optional[bool] = None
    language_auto_detect: Optional[bool] = None
    
    # Date and time formatting
    date_format: Optional[str] = None
    time_format: Optional[str] = None
    
    # Currency and numbers
    currency_display: Optional[str] = None
    number_precision: Optional[int] = None
    
    # UI preferences
    ui_settings: Optional[Dict[str, Any]] = None
    
    # Notification preferences
    notification_settings: Optional[Dict[str, Dict[str, bool]]] = None
    
    # Quick actions
    pinned_actions: Optional[List[str]] = None
    
    # Request options
    persist: bool = False  # Whether to persist in session


class UserPreferenceResponse(BaseModel):
    """Response model for user preference operations."""
    
    user_id: int
    preferences: Optional[UserPreference]
    effective_language: str
    detected_language: Optional['LanguageDetectionResult'] = None
    success: bool
    message: Optional[str] = None


class LanguageDetectionResult(BaseModel):
    """Result of language detection."""
    
    detected_language: str
    confidence: float
    source: str  # browser, ip, header, default
    sources_tried: List[str] = Field(default_factory=list)
    fallback_used: bool = False
    quebec_indicators: List[str] = Field(default_factory=list)


class NotificationSettings(BaseModel):
    """Notification settings by type."""
    
    push: bool = True
    sms: bool = True
    email: bool = True


class UISettings(BaseModel):
    """UI settings model."""
    
    theme: str = 'light'  # light, dark, auto
    sidebar_collapsed: bool = False
    show_tooltips: bool = True
    font_size: str = 'medium'  # small, medium, large
    high_contrast: bool = False
    reduce_motion: bool = False


class UserPreferenceStats(BaseModel):
    """Statistics about user preferences."""
    
    total_users: int
    language_distribution: Dict[str, int]
    quebec_french_users: int
    date_format_distribution: Dict[str, int]
    time_format_distribution: Dict[str, int]
    users_with_pinned_actions: int
    notification_channel_stats: Optional[Dict[str, Dict[str, int]]] = None


class BulkPreferenceUpdate(BaseModel):
    """Model for bulk preference updates."""
    
    user_ids: List[int]
    update_data: UserPreferenceUpdate
    reason: str


class BulkPreferenceUpdateResult(BaseModel):
    """Result of bulk preference update."""
    
    success: bool
    updated_count: int
    failed_count: int
    failed_user_ids: List[int]
    errors: List[str]
    message: str


# Re-export existing models that are used by the service
from app.models.quick_action_models import QuickActionType