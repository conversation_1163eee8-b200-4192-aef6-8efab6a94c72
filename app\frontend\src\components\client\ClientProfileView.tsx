import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import Button from '../common/Button';
import { Card } from '../common/Card';
import LoadingSpinner from '../ui/LoadingSpinner';
import { 
  User, Mail, Phone, MapPin, Calendar, Edit2, Save, X,
  AlertCircle, Baby, UserPlus
} from 'lucide-react';
import { Input } from '../common/Input';
import { Select } from '../common/Select';
import toast from 'react-hot-toast';

interface ClientProfile {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  postalCode: string;
  preferredLanguage: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  createdAt: string;
}

interface Dependant {
  id: number;
  name: string;
  age: number;
  grade: string;
  learningNeeds?: string;
}

export const ClientProfileView: React.FC = () => {
  const { user } = useAuth();
  const [profile, setProfile] = useState<ClientProfile | null>(null);
  const [dependants, setDependants] = useState<Dependant[]>([]);
  const [loading, setLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [editedProfile, setEditedProfile] = useState<ClientProfile | null>(null);
  const [showAddDependant, setShowAddDependant] = useState(false);
  const [newDependant, setNewDependant] = useState({
    name: '',
    age: '',
    grade: '',
    learningNeeds: ''
  });

  useEffect(() => {
    // Mock data - replace with API call
    setTimeout(() => {
      const mockProfile: ClientProfile = {
        id: 1,
        firstName: user?.firstName || 'John',
        lastName: user?.lastName || 'Doe',
        email: user?.email || '<EMAIL>',
        phone: '(*************',
        postalCode: 'H2X 1Y9',
        preferredLanguage: 'en',
        emergencyContactName: 'Jane Doe',
        emergencyContactPhone: '(*************',
        createdAt: '2024-01-15'
      };
      
      const mockDependants: Dependant[] = [
        {
          id: 1,
          name: 'Emma Doe',
          age: 10,
          grade: 'Grade 5',
          learningNeeds: 'Math support'
        },
        {
          id: 2,
          name: 'Jack Doe',
          age: 8,
          grade: 'Grade 3',
          learningNeeds: 'Reading comprehension'
        }
      ];
      
      setProfile(mockProfile);
      setEditedProfile(mockProfile);
      setDependants(mockDependants);
      setLoading(false);
    }, 1000);
  }, [user]);

  const handleSaveProfile = async () => {
    if (!editedProfile) return;
    
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setProfile(editedProfile);
      setIsEditing(false);
      toast.success('Profile updated successfully');
    } catch (error) {
      toast.error('Failed to update profile');
    }
  };

  const handleAddDependant = async () => {
    if (!newDependant.name || !newDependant.age || !newDependant.grade) {
      toast.error('Please fill all required fields');
      return;
    }

    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const dependant: Dependant = {
        id: Date.now(),
        name: newDependant.name,
        age: parseInt(newDependant.age),
        grade: newDependant.grade,
        learningNeeds: newDependant.learningNeeds
      };
      
      setDependants([...dependants, dependant]);
      setNewDependant({ name: '', age: '', grade: '', learningNeeds: '' });
      setShowAddDependant(false);
      toast.success('Dependant added successfully');
    } catch (error) {
      toast.error('Failed to add dependant');
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-96">
        <LoadingSpinner size="large" message="Loading profile..." />
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-500">Profile not found</p>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Profile Card */}
      <Card className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold text-gray-900">My Profile</h2>
          {!isEditing ? (
            <Button
              variant="secondary"
              size="sm"
              onClick={() => setIsEditing(true)}
            >
              <Edit2 className="w-4 h-4 mr-2" />
              Edit Profile
            </Button>
          ) : (
            <div className="flex gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setIsEditing(false);
                  setEditedProfile(profile);
                }}
              >
                <X className="w-4 h-4 mr-2" />
                Cancel
              </Button>
              <Button
                variant="primary"
                size="sm"
                onClick={handleSaveProfile}
              >
                <Save className="w-4 h-4 mr-2" />
                Save Changes
              </Button>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Personal Information */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-gray-700 uppercase tracking-wider">
              Personal Information
            </h3>
            
            <div className="space-y-3">
              <div>
                <label className="block text-sm text-gray-600 mb-1">First Name</label>
                {isEditing ? (
                  <Input
                    value={editedProfile?.firstName || ''}
                    onChange={(e) => setEditedProfile({
                      ...editedProfile!,
                      firstName: e.target.value
                    })}
                  />
                ) : (
                  <p className="font-medium">{profile.firstName}</p>
                )}
              </div>
              
              <div>
                <label className="block text-sm text-gray-600 mb-1">Last Name</label>
                {isEditing ? (
                  <Input
                    value={editedProfile?.lastName || ''}
                    onChange={(e) => setEditedProfile({
                      ...editedProfile!,
                      lastName: e.target.value
                    })}
                  />
                ) : (
                  <p className="font-medium">{profile.lastName}</p>
                )}
              </div>
              
              <div>
                <label className="block text-sm text-gray-600 mb-1">Preferred Language</label>
                {isEditing ? (
                  <Select
                    value={editedProfile?.preferredLanguage || ''}
                    onChange={(e) => setEditedProfile({
                      ...editedProfile!,
                      preferredLanguage: e.target.value
                    })}
                    options={[
                      { value: 'en', label: 'English' },
                      { value: 'fr', label: 'Français' }
                    ]}
                  />
                ) : (
                  <p className="font-medium">
                    {profile.preferredLanguage === 'fr' ? 'Français' : 'English'}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-gray-700 uppercase tracking-wider">
              Contact Information
            </h3>
            
            <div className="space-y-3">
              <div>
                <label className="block text-sm text-gray-600 mb-1">Email</label>
                <p className="font-medium flex items-center">
                  <Mail className="w-4 h-4 mr-2 text-gray-400" />
                  {profile.email}
                </p>
              </div>
              
              <div>
                <label className="block text-sm text-gray-600 mb-1">Phone</label>
                {isEditing ? (
                  <Input
                    value={editedProfile?.phone || ''}
                    onChange={(e) => setEditedProfile({
                      ...editedProfile!,
                      phone: e.target.value
                    })}
                    icon={Phone}
                  />
                ) : (
                  <p className="font-medium flex items-center">
                    <Phone className="w-4 h-4 mr-2 text-gray-400" />
                    {profile.phone}
                  </p>
                )}
              </div>
              
              <div>
                <label className="block text-sm text-gray-600 mb-1">Postal Code</label>
                {isEditing ? (
                  <Input
                    value={editedProfile?.postalCode || ''}
                    onChange={(e) => setEditedProfile({
                      ...editedProfile!,
                      postalCode: e.target.value
                    })}
                    icon={MapPin}
                  />
                ) : (
                  <p className="font-medium flex items-center">
                    <MapPin className="w-4 h-4 mr-2 text-gray-400" />
                    {profile.postalCode}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Emergency Contact */}
          <div className="space-y-4 md:col-span-2">
            <h3 className="text-sm font-medium text-gray-700 uppercase tracking-wider">
              Emergency Contact
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div>
                <label className="block text-sm text-gray-600 mb-1">Contact Name</label>
                {isEditing ? (
                  <Input
                    value={editedProfile?.emergencyContactName || ''}
                    onChange={(e) => setEditedProfile({
                      ...editedProfile!,
                      emergencyContactName: e.target.value
                    })}
                  />
                ) : (
                  <p className="font-medium">{profile.emergencyContactName || 'Not set'}</p>
                )}
              </div>
              
              <div>
                <label className="block text-sm text-gray-600 mb-1">Contact Phone</label>
                {isEditing ? (
                  <Input
                    value={editedProfile?.emergencyContactPhone || ''}
                    onChange={(e) => setEditedProfile({
                      ...editedProfile!,
                      emergencyContactPhone: e.target.value
                    })}
                  />
                ) : (
                  <p className="font-medium">{profile.emergencyContactPhone || 'Not set'}</p>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Account Info */}
        <div className="mt-6 pt-6 border-t border-gray-200">
          <div className="flex items-center text-sm text-gray-500">
            <Calendar className="w-4 h-4 mr-2" />
            Member since {new Date(profile.createdAt).toLocaleDateString()}
          </div>
        </div>
      </Card>

      {/* Dependants Card */}
      <Card className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold text-gray-900">My Dependants</h2>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => setShowAddDependant(true)}
          >
            <UserPlus className="w-4 h-4 mr-2" />
            Add Dependant
          </Button>
        </div>

        {dependants.length === 0 ? (
          <div className="text-center py-8">
            <Baby className="w-12 h-12 text-gray-300 mx-auto mb-3" />
            <p className="text-gray-500">No dependants added yet</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {dependants.map((dependant) => (
              <div key={dependant.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-medium text-gray-900">{dependant.name}</h4>
                  <Button variant="ghost" size="sm">
                    <Edit2 className="w-4 h-4" />
                  </Button>
                </div>
                <div className="space-y-1 text-sm text-gray-600">
                  <p>Age: {dependant.age}</p>
                  <p>Grade: {dependant.grade}</p>
                  {dependant.learningNeeds && (
                    <p>Learning Needs: {dependant.learningNeeds}</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Add Dependant Form */}
        {showAddDependant && (
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="font-medium text-gray-900 mb-4">Add New Dependant</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Name *"
                value={newDependant.name}
                onChange={(e) => setNewDependant({ ...newDependant, name: e.target.value })}
                placeholder="Enter name"
              />
              <Input
                label="Age *"
                type="number"
                value={newDependant.age}
                onChange={(e) => setNewDependant({ ...newDependant, age: e.target.value })}
                placeholder="Enter age"
              />
              <Input
                label="Grade *"
                value={newDependant.grade}
                onChange={(e) => setNewDependant({ ...newDependant, grade: e.target.value })}
                placeholder="e.g., Grade 5"
              />
              <Input
                label="Learning Needs"
                value={newDependant.learningNeeds}
                onChange={(e) => setNewDependant({ ...newDependant, learningNeeds: e.target.value })}
                placeholder="Optional"
              />
            </div>
            <div className="flex justify-end gap-2 mt-4">
              <Button
                variant="ghost"
                onClick={() => {
                  setShowAddDependant(false);
                  setNewDependant({ name: '', age: '', grade: '', learningNeeds: '' });
                }}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={handleAddDependant}
              >
                Add Dependant
              </Button>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
};