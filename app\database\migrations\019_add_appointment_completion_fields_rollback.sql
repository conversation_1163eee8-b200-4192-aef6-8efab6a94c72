-- Rollback: Remove appointment completion and SMS confirmation fields
-- Version: 019
-- Description: Rollback migration for appointment completion workflow
-- Author: TutorAide Development Team
-- Date: 2025-01-14

-- Drop indexes
DROP INDEX IF EXISTS idx_appointments_completed_at;
DROP INDEX IF EXISTS idx_appointments_sms_confirmed;
DROP INDEX IF EXISTS idx_sms_confirmations_expires;
DROP INDEX IF EXISTS idx_billing_items_status;

-- Drop tables
DROP TABLE IF EXISTS billing_invoice_items;
DROP TABLE IF EXISTS appointment_sms_confirmations;

-- Remove columns from appointment_sessions
ALTER TABLE appointment_sessions
    DROP COLUMN IF EXISTS completed_at,
    DROP COLUMN IF EXISTS completed_by,
    DROP COLUMN IF EXISTS tutor_no_show,
    DROP COLUMN IF EXISTS client_no_show,
    DROP COLUMN IF EXISTS sms_confirmed,
    DROP COLUMN IF EXISTS sms_confirmed_at;

-- Note: We're not removing client_id, dependant_id, subject_area, hourly_rate, and currency
-- as they might be used by other parts of the system