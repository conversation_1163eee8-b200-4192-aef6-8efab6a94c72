import React, { useState, useEffect } from 'react';
import { Clock, MapPin, GraduationCap, Users, Plus, Edit, Trash2 } from 'lucide-react';
import LoadingSpinner from '../../components/ui/LoadingSpinner';

// Simple service interface
interface Service {
  service_catalog_id?: number;
  service_id?: number;
  service_name?: string;
  name?: string;
  subject_area?: string;
  service_type?: string;
  service_level?: string;
  description?: string;
  default_client_rate?: number;
  is_active?: boolean;
}

const RobustServicesPage: React.FC = () => {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchServices();
  }, []);

  const fetchServices = async () => {
    try {
      setLoading(true);
      setError(null);
      
      console.log('Fetching services...');
      
      // Direct fetch to avoid any axios/api client issues
      const response = await fetch('http://localhost:8000/api/v1/services/catalog', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        },
      });

      console.log('Response status:', response.status);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Services data:', data);
      
      setServices(data || []);
      
    } catch (err) {
      console.error('Error fetching services:', err);
      setError(err instanceof Error ? err.message : 'Failed to load services');
    } finally {
      setLoading(false);
    }
  };

  const getServiceName = (service: Service) => {
    return service.service_name || service.name || 'Unknown Service';
  };

  const getServiceId = (service: Service) => {
    return service.service_catalog_id || service.service_id || 0;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-64">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-red-800 mb-2">Error Loading Services</h2>
          <p className="text-red-600 mb-4">{error}</p>
          <button 
            onClick={fetchServices}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Services Catalog</h1>
            <p className="text-gray-600">Manage and browse available tutoring services</p>
          </div>
          <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
            <Plus className="w-4 h-4 mr-2" />
            Add Service
          </button>
        </div>
      </div>

      {/* Services Grid */}
      {services.length === 0 ? (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <GraduationCap className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No services available</h3>
          <p className="text-gray-500">Get started by adding your first service.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {services.map((service, index) => (
            <div key={getServiceId(service) || index} className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
              {/* Service Header */}
              <div className="flex justify-between items-start mb-4">
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {getServiceName(service)}
                  </h3>
                  <div className="flex items-center text-sm text-gray-500 mb-2">
                    <MapPin className="w-4 h-4 mr-1" />
                    <span className="capitalize">{service.service_type || 'Unknown'}</span>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <button className="p-2 text-gray-400 hover:text-blue-600">
                    <Edit className="w-4 h-4" />
                  </button>
                  <button className="p-2 text-gray-400 hover:text-red-600">
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>

              {/* Service Details */}
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-500">Subject:</span>
                  <span className="font-medium capitalize">{service.subject_area || 'Unknown'}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-500">Level:</span>
                  <span className="font-medium capitalize">{service.service_level || 'Unknown'}</span>
                </div>

                {service.default_client_rate && (
                  <div className="flex justify-between">
                    <span className="text-gray-500">Rate:</span>
                    <span className="font-medium">${service.default_client_rate}/hour</span>
                  </div>
                )}

                <div className="flex justify-between">
                  <span className="text-gray-500">Status:</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    service.is_active 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {service.is_active ? 'Active' : 'Inactive'}
                  </span>
                </div>
              </div>

              {/* Description */}
              {service.description && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <p className="text-sm text-gray-600">{service.description}</p>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Footer Stats */}
      <div className="mt-8 text-center">
        <p className="text-gray-500">
          Showing {services.length} service{services.length !== 1 ? 's' : ''}
        </p>
      </div>
    </div>
  );
};

export default RobustServicesPage;
