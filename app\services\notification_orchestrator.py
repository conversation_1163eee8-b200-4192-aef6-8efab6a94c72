"""
Unified notification orchestration service for routing and managing all notifications.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from enum import Enum
import json
import uuid

from app.services.onesignal_service import OneSignalService
from app.services.twilio_service import TwilioService
from app.services.email_service import EmailService
from app.database.repositories.notification_repository import NotificationRepository
from app.database.repositories.user_preference_repository import UserPreferenceRepository
from app.models.notification_models import (
    NotificationType, NotificationChannel, NotificationPriority,
    NotificationStatus, NotificationPreference
)
from app.core.exceptions import ExternalServiceError
from app.core.redis_client import redis_client
from app.config.settings import settings
from celery import Celery

logger = logging.getLogger(__name__)

# Initialize Celery with Redis URL
redis_url = f"redis://{settings.REDIS_HOST}:{settings.REDIS_PORT}/0"
if settings.REDIS_PASSWORD:
    redis_url = f"redis://:{settings.REDIS_PASSWORD}@{settings.REDIS_HOST}:{settings.REDIS_PORT}/0"
celery_app = Celery('notifications', broker=redis_url)


class NotificationOrchestrator:
    """
    Central orchestration service for all notifications.
    
    Features:
    - Channel selection based on preferences
    - Fallback logic (push → SMS → email)
    - Queue management with retry logic
    - User preference checking
    - Quiet hours enforcement
    - Notification deduplication
    - Batch processing
    - Template rendering
    """
    
    def __init__(self):
        self.onesignal = OneSignalService()
        self.twilio = TwilioService()
        self.email = EmailService()
        self.notification_repo = NotificationRepository()
        self.preferences_repo = UserPreferenceRepository()
        self.redis = redis_client
        
        # Retry configuration
        self.max_retries = 3
        self.retry_delays = [60, 300, 900]  # 1min, 5min, 15min
        
        # Batch configuration
        self.batch_size = 100
        self.batch_delay = 0.1  # seconds between batches
    
    async def send_notification(
        self,
        user_id: Union[int, List[int]],
        notification_type: NotificationType,
        title: str,
        message: str,
        data: Optional[Dict[str, Any]] = None,
        priority: NotificationPriority = NotificationPriority.NORMAL,
        channels: Optional[List[NotificationChannel]] = None,
        schedule_for: Optional[datetime] = None,
        template_name: Optional[str] = None,
        template_variables: Optional[Dict[str, str]] = None,
        language: Optional[str] = None,
        idempotency_key: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Send notification through appropriate channels.
        
        Args:
            user_id: Single user ID or list of user IDs
            notification_type: Type of notification
            title: Notification title
            message: Notification message
            data: Additional notification data
            priority: Notification priority
            channels: Specific channels to use (overrides preferences)
            schedule_for: Schedule notification for future
            template_name: Template to use
            template_variables: Variables for template
            language: Language override
            idempotency_key: Key for deduplication
            
        Returns:
            Notification send results
        """
        try:
            # Generate idempotency key if not provided
            if not idempotency_key:
                idempotency_key = str(uuid.uuid4())
            
            # Check for duplicate notification
            if await self._is_duplicate(idempotency_key):
                logger.info(f"Duplicate notification skipped: {idempotency_key}")
                return {'status': 'duplicate', 'idempotency_key': idempotency_key}
            
            # Ensure user_id is a list
            user_ids = user_id if isinstance(user_id, list) else [user_id]
            
            # Create notification record
            notification = await self.notification_repo.create_notification(
                user_ids=user_ids,
                notification_type=notification_type,
                title=title,
                message=message,
                data=data,
                priority=priority,
                idempotency_key=idempotency_key,
                scheduled_for=schedule_for
            )
            
            # Queue notification for processing
            if schedule_for and schedule_for > datetime.now():
                # Schedule for later
                task = send_notification_task.apply_async(
                    args=[notification.notification_id],
                    eta=schedule_for
                )
            else:
                # Send immediately
                task = send_notification_task.apply_async(
                    args=[notification.notification_id],
                    priority=self._get_celery_priority(priority)
                )
            
            return {
                'notification_id': notification.notification_id,
                'task_id': task.id,
                'status': 'queued',
                'recipients': len(user_ids),
                'idempotency_key': idempotency_key
            }
            
        except Exception as e:
            logger.error(f"Error sending notification: {e}")
            raise ExternalServiceError(f"Failed to send notification: {str(e)}")
    
    async def process_notification(self, notification_id: int) -> Dict[str, Any]:
        """
        Process a queued notification.
        
        Args:
            notification_id: ID of notification to process
            
        Returns:
            Processing results
        """
        try:
            # Get notification
            notification = await self.notification_repo.get_notification(notification_id)
            if not notification:
                raise ExternalServiceError(f"Notification {notification_id} not found")
            
            # Update status
            await self.notification_repo.update_status(
                notification_id,
                NotificationStatus.PROCESSING
            )
            
            results = {
                'successful': [],
                'failed': [],
                'skipped': []
            }
            
            # Process each recipient
            for user_id in notification.user_ids:
                try:
                    # Check user preferences and quiet hours
                    if not await self._should_send_to_user(
                        user_id,
                        notification.notification_type,
                        notification.priority
                    ):
                        results['skipped'].append({
                            'user_id': user_id,
                            'reason': 'preferences_or_quiet_hours'
                        })
                        continue
                    
                    # Get user's preferred channels
                    channels = await self._get_user_channels(
                        user_id,
                        notification.notification_type,
                        notification.channels
                    )
                    
                    # Try sending through channels with fallback
                    sent = False
                    errors = []
                    
                    for channel in channels:
                        try:
                            await self._send_through_channel(
                                channel=channel,
                                user_id=user_id,
                                notification=notification
                            )
                            
                            results['successful'].append({
                                'user_id': user_id,
                                'channel': channel
                            })
                            sent = True
                            break
                            
                        except Exception as e:
                            logger.warning(f"Failed to send via {channel}: {e}")
                            errors.append(f"{channel}: {str(e)}")
                    
                    if not sent:
                        results['failed'].append({
                            'user_id': user_id,
                            'errors': errors
                        })
                        
                except Exception as e:
                    logger.error(f"Error processing user {user_id}: {e}")
                    results['failed'].append({
                        'user_id': user_id,
                        'error': str(e)
                    })
            
            # Update notification status
            final_status = NotificationStatus.SENT
            if results['failed'] and not results['successful']:
                final_status = NotificationStatus.FAILED
            elif results['failed']:
                final_status = NotificationStatus.PARTIAL
            
            await self.notification_repo.update_status(
                notification_id,
                final_status,
                results=results
            )
            
            return results
            
        except Exception as e:
            logger.error(f"Error processing notification: {e}")
            
            # Update status to failed
            await self.notification_repo.update_status(
                notification_id,
                NotificationStatus.FAILED,
                error=str(e)
            )
            
            raise
    
    async def send_batch_notifications(
        self,
        notifications: List[Dict[str, Any]],
        batch_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Send notifications in batches for efficiency.
        
        Args:
            notifications: List of notification configurations
            batch_config: Optional batch processing configuration
            
        Returns:
            Batch processing results
        """
        config = batch_config or {}
        batch_size = config.get('batch_size', self.batch_size)
        batch_delay = config.get('batch_delay', self.batch_delay)
        
        results = {
            'total': len(notifications),
            'queued': 0,
            'failed': 0,
            'batch_ids': []
        }
        
        # Process in batches
        for i in range(0, len(notifications), batch_size):
            batch = notifications[i:i + batch_size]
            
            try:
                # Create batch record
                batch_id = str(uuid.uuid4())
                
                # Queue each notification in batch
                for notif in batch:
                    try:
                        result = await self.send_notification(**notif)
                        results['queued'] += 1
                    except Exception as e:
                        logger.error(f"Failed to queue notification: {e}")
                        results['failed'] += 1
                
                results['batch_ids'].append(batch_id)
                
                # Delay between batches
                if i + batch_size < len(notifications):
                    await asyncio.sleep(batch_delay)
                    
            except Exception as e:
                logger.error(f"Batch processing error: {e}")
        
        return results
    
    async def retry_failed_notifications(
        self,
        since: Optional[datetime] = None,
        notification_ids: Optional[List[int]] = None
    ) -> Dict[str, Any]:
        """
        Retry failed notifications.
        
        Args:
            since: Retry notifications failed since this time
            notification_ids: Specific notification IDs to retry
            
        Returns:
            Retry results
        """
        try:
            # Get failed notifications
            if notification_ids:
                notifications = await self.notification_repo.get_notifications_by_ids(
                    notification_ids
                )
            else:
                notifications = await self.notification_repo.get_failed_notifications(
                    since=since or datetime.now() - timedelta(hours=24)
                )
            
            results = {
                'total': len(notifications),
                'retried': 0,
                'skipped': 0
            }
            
            for notification in notifications:
                # Check retry count
                if notification.retry_count >= self.max_retries:
                    results['skipped'] += 1
                    continue
                
                # Calculate retry delay
                retry_delay = self.retry_delays[
                    min(notification.retry_count, len(self.retry_delays) - 1)
                ]
                
                # Queue for retry
                send_notification_task.apply_async(
                    args=[notification.notification_id],
                    countdown=retry_delay
                )
                
                # Update retry count
                await self.notification_repo.increment_retry_count(
                    notification.notification_id
                )
                
                results['retried'] += 1
            
            return results
            
        except Exception as e:
            logger.error(f"Error retrying notifications: {e}")
            raise
    
    async def _is_duplicate(self, idempotency_key: str) -> bool:
        """Check if notification is duplicate using idempotency key."""
        # Check in Redis cache (24 hour TTL)
        cache_key = f"notif:idem:{idempotency_key}"
        
        if await self.redis.exists(cache_key):
            return True
        
        # Set key with TTL
        await self.redis.setex(cache_key, 86400, "1")
        
        # Also check database for recent notifications
        existing = await self.notification_repo.get_by_idempotency_key(
            idempotency_key,
            since=datetime.now() - timedelta(hours=24)
        )
        
        return existing is not None
    
    async def _should_send_to_user(
        self,
        user_id: int,
        notification_type: NotificationType,
        priority: NotificationPriority
    ) -> bool:
        """Check if notification should be sent to user."""
        # Always send urgent notifications
        if priority == NotificationPriority.URGENT:
            return True
        
        # Get user preferences
        preferences = await self.preferences_repo.get_user_preferences(user_id)
        
        if not preferences:
            # Default to sending if no preferences
            return True
        
        # Check if notification type is enabled
        type_prefs = preferences.get(notification_type)
        if not type_prefs or not type_prefs.enabled:
            return False
        
        # Check quiet hours
        now = datetime.now().time()
        if type_prefs.quiet_hours_start and type_prefs.quiet_hours_end:
            # Handle overnight quiet hours
            if type_prefs.quiet_hours_start > type_prefs.quiet_hours_end:
                if now >= type_prefs.quiet_hours_start or now <= type_prefs.quiet_hours_end:
                    return priority >= NotificationPriority.HIGH
            else:
                if type_prefs.quiet_hours_start <= now <= type_prefs.quiet_hours_end:
                    return priority >= NotificationPriority.HIGH
        
        return True
    
    async def _get_user_channels(
        self,
        user_id: int,
        notification_type: NotificationType,
        override_channels: Optional[List[NotificationChannel]] = None
    ) -> List[NotificationChannel]:
        """Get notification channels for user."""
        # Use override if provided
        if override_channels:
            return override_channels
        
        # Get user preferences
        preferences = await self.preferences_repo.get_user_preferences(user_id)
        
        if not preferences:
            # Default channels
            return [
                NotificationChannel.PUSH,
                NotificationChannel.EMAIL
            ]
        
        # Get enabled channels for notification type
        type_prefs = preferences.get(notification_type)
        if not type_prefs:
            return [NotificationChannel.PUSH, NotificationChannel.EMAIL]
        
        channels = []
        
        if type_prefs.push_enabled:
            channels.append(NotificationChannel.PUSH)
        
        if type_prefs.sms_enabled:
            channels.append(NotificationChannel.SMS)
        
        if type_prefs.email_enabled:
            channels.append(NotificationChannel.EMAIL)
        
        # Ensure at least one channel
        if not channels:
            channels = [NotificationChannel.EMAIL]
        
        return channels
    
    async def _send_through_channel(
        self,
        channel: NotificationChannel,
        user_id: int,
        notification: Any
    ) -> None:
        """Send notification through specific channel."""
        if channel == NotificationChannel.PUSH:
            await self.onesignal.send_notification(
                user_ids=[user_id],
                title=notification.title,
                message=notification.message,
                data=notification.data,
                priority=notification.priority
            )
            
        elif channel == NotificationChannel.SMS:
            # Get user's phone number
            user = await self.notification_repo.get_user(user_id)
            if not user or not user.phone_number:
                raise ExternalServiceError("User has no phone number")
            
            await self.twilio.send_sms(
                to_number=user.phone_number,
                message=f"{notification.title}\n\n{notification.message}",
                user_id=user_id,
                metadata={'notification_id': notification.notification_id}
            )
            
        elif channel == NotificationChannel.EMAIL:
            # Get user's email
            user = await self.notification_repo.get_user(user_id)
            if not user or not user.email:
                raise ExternalServiceError("User has no email")
            
            await self.email.send_email(
                to_email=user.email,
                subject=notification.title,
                body=notification.message,
                user_id=user_id,
                notification_type=notification.notification_type
            )
            
        else:
            raise ExternalServiceError(f"Unknown channel: {channel}")
    
    def _get_celery_priority(self, priority: NotificationPriority) -> int:
        """Map notification priority to Celery priority."""
        priority_map = {
            NotificationPriority.LOW: 1,
            NotificationPriority.NORMAL: 5,
            NotificationPriority.HIGH: 8,
            NotificationPriority.URGENT: 10
        }
        return priority_map.get(priority, 5)


# Celery tasks
@celery_app.task(bind=True, max_retries=3)
def send_notification_task(self, notification_id: int):
    """Celery task to process notification."""
    orchestrator = NotificationOrchestrator()
    
    try:
        # Run async function in sync context
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(
            orchestrator.process_notification(notification_id)
        )
        return result
        
    except Exception as e:
        logger.error(f"Notification task failed: {e}")
        
        # Retry with exponential backoff
        retry_in = 60 * (2 ** self.request.retries)
        raise self.retry(exc=e, countdown=retry_in)