import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';
import { 
  Send, Search, MoreVertical, Paperclip, Smile,
  Circle, CheckCircle, AlertCircle, Users
} from 'lucide-react';

interface ChatRoom {
  id: string;
  name: string;
  type: 'direct' | 'group';
  participants: string[];
  lastActivity: Date;
  unreadCount: number;
  isOnline?: boolean;
  avatar?: string;
}

interface ChatMessage {
  id: string;
  roomId: string;
  sender: {
    id: string;
    name: string;
    role: 'manager' | 'tutor' | 'client';
  };
  content: string;
  timestamp: Date;
  type: 'text' | 'file' | 'system';
  status: 'sending' | 'sent' | 'error';
  reactions?: string[];
}

// Mock data
const mockChatRooms: ChatRoom[] = [
  {
    id: '1',
    name: '<PERSON>',
    type: 'direct',
    participants: ['<PERSON>'],
    lastActivity: new Date(Date.now() - 1000 * 60 * 5), // 5 minutes ago
    unreadCount: 3,
    isOnline: true,
  },
  {
    id: '2',
    name: 'Math Tutors Group',
    type: 'group',
    participants: ['<PERSON>', '<PERSON>', '<PERSON>'],
    lastActivity: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
    unreadCount: 0,
  },
  {
    id: '3',
    name: 'Platform Updates',
    type: 'group',
    participants: ['All Users'],
    lastActivity: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
    unreadCount: 1,
  },
];

const mockMessages: ChatMessage[] = [
  {
    id: '1',
    roomId: '1',
    sender: { id: '1', name: 'Sarah Johnson', role: 'tutor' },
    content: 'Hi! I wanted to ask about the schedule for next week.',
    timestamp: new Date(Date.now() - 1000 * 60 * 10),
    type: 'text',
    status: 'sent',
  },
  {
    id: '2',
    roomId: '1',
    sender: { id: 'current', name: 'You', role: 'manager' },
    content: 'Sure! What specific days are you looking at?',
    timestamp: new Date(Date.now() - 1000 * 60 * 8),
    type: 'text',
    status: 'sent',
  },
  {
    id: '3',
    roomId: '1',
    sender: { id: '1', name: 'Sarah Johnson', role: 'tutor' },
    content: 'I\'m available Monday through Thursday, preferably afternoons.',
    timestamp: new Date(Date.now() - 1000 * 60 * 5),
    type: 'text',
    status: 'sent',
    reactions: ['👍'],
  },
];

const InAppChat: React.FC = () => {
  const { t } = useTranslation();
  const [selectedRoom, setSelectedRoom] = useState<ChatRoom | null>(mockChatRooms[0]);
  const [messages, setMessages] = useState<ChatMessage[]>(mockMessages);
  const [messageText, setMessageText] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Simulate real-time typing indicator
  useEffect(() => {
    if (messageText) {
      setIsTyping(true);
      const timer = setTimeout(() => setIsTyping(false), 1000);
      return () => clearTimeout(timer);
    }
  }, [messageText]);

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const sendMessage = () => {
    if (messageText.trim() && selectedRoom) {
      const newMessage: ChatMessage = {
        id: Date.now().toString(),
        roomId: selectedRoom.id,
        sender: { id: 'current', name: 'You', role: 'manager' },
        content: messageText,
        timestamp: new Date(),
        type: 'text',
        status: 'sending',
      };

      setMessages([...messages, newMessage]);
      setMessageText('');

      // Simulate message sent
      setTimeout(() => {
        setMessages(prev => 
          prev.map(msg => 
            msg.id === newMessage.id ? { ...msg, status: 'sent' } : msg
          )
        );
      }, 500);

      // Simulate response
      setTimeout(() => {
        const responseMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          roomId: selectedRoom.id,
          sender: { id: '1', name: selectedRoom.name, role: 'tutor' },
          content: 'Thanks for the quick response! I\'ll check my calendar.',
          timestamp: new Date(),
          type: 'text',
          status: 'sent',
        };
        setMessages(prev => [...prev, responseMessage]);
      }, 2000);
    }
  };

  const filteredRooms = mockChatRooms.filter(room =>
    room.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'manager': return 'text-purple-600';
      case 'tutor': return 'text-accent-red';
      case 'client': return 'text-accent-green';
      default: return 'text-text-primary';
    }
  };

  return (
    <div className="h-full flex bg-background-secondary">
      {/* Chat List */}
      <div className="w-1/3 bg-white border-r border-primary-200 flex flex-col">
        {/* Search */}
        <div className="p-4 border-b border-primary-200">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-text-secondary" />
            <input
              type="text"
              placeholder="Search chats..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-background-secondary rounded-soft focus:outline-none focus:ring-2 focus:ring-accent-red"
            />
          </div>
        </div>

        {/* Room List */}
        <div className="flex-1 overflow-y-auto">
          {filteredRooms.map(room => (
            <div
              key={room.id}
              onClick={() => setSelectedRoom(room)}
              className={`
                p-4 border-b border-primary-100 cursor-pointer transition-colors
                ${selectedRoom?.id === room.id ? 'bg-accent-red bg-opacity-5' : 'hover:bg-background-secondary'}
              `}
            >
              <div className="flex items-start gap-3">
                <div className="relative">
                  <div className="w-10 h-10 bg-accent-red bg-opacity-10 rounded-full flex items-center justify-center">
                    {room.type === 'group' ? (
                      <Users className="w-5 h-5 text-accent-red" />
                    ) : (
                      <span className="text-lg font-medium text-accent-red">
                        {room.name.charAt(0)}
                      </span>
                    )}
                  </div>
                  {room.isOnline && (
                    <Circle className="absolute bottom-0 right-0 w-3 h-3 text-accent-green fill-current" />
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <h3 className="font-medium text-text-primary truncate">
                      {room.name}
                    </h3>
                    <span className="text-xs text-text-secondary">
                      {format(room.lastActivity, 'HH:mm')}
                    </span>
                  </div>
                  <p className="text-sm text-text-secondary truncate">
                    {room.type === 'group' && `${room.participants.length} participants`}
                    {room.type === 'direct' && (room.isOnline ? 'Online' : 'Offline')}
                  </p>
                  {room.unreadCount > 0 && (
                    <span className="inline-block mt-1 bg-accent-red text-white text-xs rounded-full px-2 py-0.5">
                      {room.unreadCount} new
                    </span>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Chat View */}
      {selectedRoom ? (
        <div className="flex-1 flex flex-col">
          {/* Header */}
          <div className="bg-white p-4 border-b border-primary-200 shadow-sm">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="relative">
                  <div className="w-10 h-10 bg-accent-red bg-opacity-10 rounded-full flex items-center justify-center">
                    {selectedRoom.type === 'group' ? (
                      <Users className="w-5 h-5 text-accent-red" />
                    ) : (
                      <span className="text-lg font-medium text-accent-red">
                        {selectedRoom.name.charAt(0)}
                      </span>
                    )}
                  </div>
                  {selectedRoom.isOnline && (
                    <Circle className="absolute bottom-0 right-0 w-3 h-3 text-accent-green fill-current" />
                  )}
                </div>
                <div>
                  <h2 className="font-semibold text-text-primary">{selectedRoom.name}</h2>
                  <p className="text-sm text-text-secondary">
                    {selectedRoom.type === 'group' 
                      ? `${selectedRoom.participants.join(', ')}`
                      : selectedRoom.isOnline ? 'Active now' : 'Offline'
                    }
                  </p>
                </div>
              </div>
              <button className="p-2 hover:bg-background-secondary rounded-medium transition-colors">
                <MoreVertical className="w-5 h-5 text-text-secondary" />
              </button>
            </div>
          </div>

          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages
              .filter(msg => msg.roomId === selectedRoom.id)
              .map(message => (
                <div
                  key={message.id}
                  className={`flex ${message.sender.id === 'current' ? 'justify-end' : 'justify-start'}`}
                >
                  <div className={`max-w-[70%] ${message.sender.id === 'current' ? 'items-end' : 'items-start'}`}>
                    {message.sender.id !== 'current' && (
                      <p className={`text-xs font-medium mb-1 ${getRoleColor(message.sender.role)}`}>
                        {message.sender.name}
                      </p>
                    )}
                    <div
                      className={`
                        px-4 py-2 rounded-large inline-block
                        ${message.sender.id === 'current'
                          ? 'bg-accent-red text-white' 
                          : 'bg-white text-text-primary shadow-sm'
                        }
                      `}
                    >
                      <p className="text-sm">{message.content}</p>
                      {message.reactions && message.reactions.length > 0 && (
                        <div className="flex gap-1 mt-1">
                          {message.reactions.map((reaction, idx) => (
                            <span key={idx} className="text-sm">{reaction}</span>
                          ))}
                        </div>
                      )}
                    </div>
                    <div className={`flex items-center gap-1 mt-1 ${message.sender.id === 'current' ? 'justify-end' : ''}`}>
                      <span className="text-xs text-text-secondary">
                        {format(message.timestamp, 'HH:mm')}
                      </span>
                      {message.sender.id === 'current' && (
                        <>
                          {message.status === 'sending' && <Circle className="w-3 h-3 text-text-secondary" />}
                          {message.status === 'sent' && <CheckCircle className="w-3 h-3 text-accent-red" />}
                          {message.status === 'error' && <AlertCircle className="w-3 h-3 text-accent-red" />}
                        </>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            {isTyping && selectedRoom.type === 'direct' && (
              <div className="flex items-center gap-2 text-text-secondary">
                <div className="flex gap-1">
                  <div className="w-2 h-2 bg-text-secondary rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                  <div className="w-2 h-2 bg-text-secondary rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                  <div className="w-2 h-2 bg-text-secondary rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                </div>
                <span className="text-sm">{selectedRoom.name} is typing...</span>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Message Input */}
          <div className="bg-white p-4 border-t border-primary-200">
            <div className="flex items-end gap-3">
              <button className="p-2 hover:bg-background-secondary rounded-medium transition-colors">
                <Paperclip className="w-5 h-5 text-text-secondary" />
              </button>
              <div className="flex-1 relative">
                <textarea
                  value={messageText}
                  onChange={(e) => setMessageText(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      sendMessage();
                    }
                  }}
                  placeholder="Type a message..."
                  className="w-full px-4 py-2 bg-background-secondary rounded-soft resize-none focus:outline-none focus:ring-2 focus:ring-accent-red"
                  rows={1}
                />
              </div>
              <button className="p-2 hover:bg-background-secondary rounded-medium transition-colors">
                <Smile className="w-5 h-5 text-text-secondary" />
              </button>
              <button
                onClick={sendMessage}
                disabled={!messageText.trim()}
                className={`
                  p-2 rounded-soft transition-colors
                  ${messageText.trim()
                    ? 'bg-accent-red text-white hover:bg-accent-red-dark'
                    : 'bg-primary-200 text-text-secondary cursor-not-allowed'
                  }
                `}
              >
                <Send className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      ) : (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <Users className="w-12 h-12 text-text-secondary mx-auto mb-3" />
            <p className="text-text-secondary">Select a chat to start messaging</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default InAppChat;