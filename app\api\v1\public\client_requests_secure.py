"""
Enhanced client tutor request API with security measures.
Includes rate limiting, CORS, and additional validation.
"""

from typing import Dict, Any, List
from fastapi import APIRouter, HTTPException, Depends, Request, Response
from fastapi.security import HTT<PERSON><PERSON>earer
from pydantic import BaseModel, Field, EmailStr, field_validator
from datetime import datetime, timedelta
import re
import json
import hashlib
from app.config.database import get_db
from app.services.geocoding_service import GeocodingService

# Helper function to get database connection
async def get_db_connection():
    """Get database connection."""
    async for db in get_db():
        return db
from app.core.logging import logger
from app.core.rate_limiter import RateLimiter
from app.api.v1.public.client_requests import ClientTutorRequestCreate, _determine_region

# Initialize rate limiter
rate_limiter = RateLimiter()

# Security configuration
ALLOWED_ORIGINS = [
    "https://tutoraide.ca",
    "https://www.tutoraide.ca",
    "https://tutoraide-marketing.vercel.app",
    "http://localhost:3000",  # Development
]

# Rate limiting configuration
RATE_LIMIT_REQUESTS = 5  # requests
RATE_LIMIT_WINDOW = 3600  # 1 hour in seconds

router = APIRouter()

async def check_rate_limit(request: Request) -> None:
    """Check rate limiting for the request."""
    # Get client identifier (IP + User-Agent hash)
    client_ip = request.client.host if request.client else "unknown"
    user_agent = request.headers.get("user-agent", "")
    client_id = hashlib.md5(f"{client_ip}:{user_agent}".encode()).hexdigest()
    
    # Check rate limit
    is_allowed = await rate_limiter.check_rate_limit(
        client_id,
        RATE_LIMIT_REQUESTS,
        RATE_LIMIT_WINDOW
    )
    
    if not is_allowed:
        raise HTTPException(
            status_code=429,
            detail=f"Rate limit exceeded. Maximum {RATE_LIMIT_REQUESTS} requests per hour."
        )

async def verify_origin(request: Request) -> None:
    """Verify the request origin for CORS security."""
    origin = request.headers.get("origin", "")
    referer = request.headers.get("referer", "")
    
    # Check if origin is allowed
    origin_allowed = any(
        origin.startswith(allowed) for allowed in ALLOWED_ORIGINS
    )
    
    # Check if referer is from allowed domain
    referer_allowed = any(
        referer.startswith(allowed) for allowed in ALLOWED_ORIGINS
    )
    
    # In production, enforce origin check
    if not (origin_allowed or referer_allowed):
        # Log suspicious request
        logger.warning(
            "Blocked request from unauthorized origin",
            extra={
                "origin": origin,
                "referer": referer,
                "client_ip": request.client.host if request.client else "unknown"
            }
        )
        raise HTTPException(
            status_code=403,
            detail="Request not allowed from this origin"
        )

async def check_duplicate_submission(email: str, phone: str) -> None:
    """Check if a similar request was submitted recently."""
    conn = await get_db_connection()
    try:
        # Check for recent submissions (within 24 hours)
        query = """
            SELECT COUNT(*) as count
            FROM client_tutor_requests
            WHERE (parent_email = $1 OR parent_phone = $2)
            AND created_at > CURRENT_TIMESTAMP - INTERVAL '24 hours'
            AND status != 'cancelled'
        """
        result = await conn.fetchone(query, email, phone)
        
        if result['count'] > 0:
            raise HTTPException(
                status_code=400,
                detail="A request with this email or phone was already submitted in the last 24 hours. <NAME_EMAIL> if you need to update your request."
            )
    finally:
        await conn.close()

class ClientTutorRequestCreateSecure(ClientTutorRequestCreate):
    """Enhanced schema with additional validation."""
    
    # Honeypot field for bot detection
    website: str = Field("", max_length=1)
    
    @field_validator('website')
    def validate_honeypot(cls, v):
        """Honeypot validation - should be empty."""
        if v:
            raise ValueError('Invalid submission')
        return v
    
    @field_validator('parent_email')
    def validate_email_domain(cls, v):
        """Additional email validation."""
        # Block disposable email domains
        disposable_domains = [
            'tempmail.com', 'guerrillamail.com', '10minutemail.com',
            'mailinator.com', 'throwaway.email'
        ]
        domain = v.split('@')[1].lower()
        if domain in disposable_domains:
            raise ValueError('Please use a permanent email address')
        return v
    
    @field_validator('additional_info')
    def validate_content_length(cls, v):
        """Limit content length to prevent abuse."""
        if v and len(v) > 1000:
            raise ValueError('Additional information must be under 1000 characters')
        return v
    
    @field_validator('subjects')
    def validate_subject_count(cls, v):
        """Limit number of subjects."""
        if len(v) > 10:
            raise ValueError('Please select up to 10 subjects')
        return v

@router.post("/client-requests/secure", response_model=Dict[str, Any])
async def create_client_request_secure(
    request_data: ClientTutorRequestCreateSecure,
    request: Request,
    response: Response
) -> Dict[str, Any]:
    """
    Create a new client tutor request with enhanced security.
    
    Includes:
    - Rate limiting
    - Origin verification
    - Duplicate submission prevention
    - Input validation
    - Honeypot bot detection
    """
    # Security checks
    await check_rate_limit(request)
    await verify_origin(request)
    await check_duplicate_submission(
        request_data.parent_email,
        request_data.parent_phone
    )
    
    # Add CORS headers
    response.headers["Access-Control-Allow-Origin"] = request.headers.get("origin", "*")
    response.headers["Access-Control-Allow-Credentials"] = "true"
    
    conn = await get_db_connection()
    try:
        # Log request details for security monitoring
        client_ip = request.client.host if request.client else None
        user_agent = request.headers.get("user-agent", "")
        
        logger.info(
            "Processing secure client request",
            extra={
                "email": request_data.parent_email,
                "city": request_data.parent_city,
                "client_ip": client_ip,
                "user_agent": user_agent[:100]  # Truncate long user agents
            }
        )
        
        # Geocode the address
        geocoding_service = GeocodingService()
        full_address = f"{request_data.parent_address}, {request_data.parent_city}, {request_data.parent_province}, {request_data.parent_postal_code}"
        
        latitude = None
        longitude = None
        region = None
        
        try:
            geo_result = await geocoding_service.geocode_address(full_address)
            if geo_result and geo_result.get('success'):
                latitude = geo_result.get('latitude')
                longitude = geo_result.get('longitude')
                region = _determine_region(request_data.parent_city, latitude, longitude)
        except Exception as e:
            logger.warning(f"Geocoding failed: {e}")
        
        # Insert with additional security fields
        query = """
            INSERT INTO client_tutor_requests (
                parent_first_name, parent_last_name, parent_email, parent_phone,
                parent_address, parent_apartment, parent_city, parent_postal_code, parent_province,
                latitude, longitude, region,
                student_first_name, student_last_name, student_grade,
                subjects, frequency, preferred_schedule, location, additional_info,
                source, referrer_url, utm_source, utm_medium, utm_campaign,
                created_at, updated_at
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12,
                $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25,
                CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
            )
            RETURNING request_id, created_at
        """
        
        result = await conn.fetchone(
            query,
            # Sanitize inputs
            request_data.parent_first_name.strip(),
            request_data.parent_last_name.strip(),
            request_data.parent_email.lower().strip(),
            request_data.parent_phone.strip(),
            request_data.parent_address.strip(),
            request_data.parent_apartment.strip() if request_data.parent_apartment else None,
            request_data.parent_city.strip(),
            request_data.parent_postal_code.upper().strip(),
            request_data.parent_province.strip(),
            latitude,
            longitude,
            region,
            request_data.student_first_name.strip(),
            request_data.student_last_name.strip(),
            request_data.student_grade.strip(),
            request_data.subjects,
            request_data.frequency,
            request_data.preferred_schedule.strip() if request_data.preferred_schedule else None,
            request_data.location,
            request_data.additional_info.strip() if request_data.additional_info else None,
            'website_form_secure',
            request_data.referrer_url,
            request_data.utm_source,
            request_data.utm_medium,
            request_data.utm_campaign
        )
        
        request_id = result['request_id']
        created_at = result['created_at']
        
        # Log successful submission
        logger.info(
            f"Secure client request created: {request_id}",
            extra={
                "request_id": request_id,
                "region": region,
                "security_checks": "passed"
            }
        )
        
        # TODO: Trigger async notifications
        
        return {
            "success": True,
            "message": "Your tutor request has been submitted successfully. Our team will contact you within 24-48 hours.",
            "request_id": str(request_id),
            "submitted_at": created_at.isoformat(),
            "reference_number": f"TR-{request_id:06d}"  # Human-friendly reference
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error in secure client request: {str(e)}",
            extra={"error": str(e)}
        )
        raise HTTPException(
            status_code=500,
            detail="An error occurred. Please try again or contact support."
        )
    finally:
        await conn.close()

@router.options("/client-requests/secure")
async def options_client_request(request: Request, response: Response):
    """Handle CORS preflight requests."""
    origin = request.headers.get("origin", "*")
    if any(origin.startswith(allowed) for allowed in ALLOWED_ORIGINS):
        response.headers["Access-Control-Allow-Origin"] = origin
    else:
        response.headers["Access-Control-Allow-Origin"] = ALLOWED_ORIGINS[0]
    
    response.headers["Access-Control-Allow-Methods"] = "POST, OPTIONS"
    response.headers["Access-Control-Allow-Headers"] = "Content-Type, Accept"
    response.headers["Access-Control-Allow-Credentials"] = "true"
    response.headers["Access-Control-Max-Age"] = "3600"
    
    return {"message": "OK"}