"""
Security middleware for request validation and protection.
"""

import os
import time
import json
from typing import Dict, Any, Optional
from fastapi import Request, Response, HTTPException, status
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import <PERSON><PERSON><PERSON><PERSON>ponse
from app.core.validation import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SecurityValidator
from app.core.logging import <PERSON><PERSON><PERSON><PERSON><PERSON>og<PERSON>
from app.services.rate_limiting_service import RateLimitingService


class SecurityMiddleware(BaseHTTPMiddleware):
    """Comprehensive security middleware for request validation."""
    
    def __init__(self, app, max_request_size: int = 10 * 1024 * 1024):  # 10MB default
        super().__init__(app)
        self.max_request_size = max_request_size
        self.logger = TutorAideLogger.get_logger(__name__)
        self.rate_limiter = RateLimitingService()
        
        # Paths that bypass security checks
        self.bypass_paths = [
            "/health",
            "/docs",
            "/openapi.json",
            "/favicon.ico",
            "/api/v1/auth/google/"  # Google OAuth endpoints need to work without strict validation
        ]
        
        # File upload endpoints
        self.file_upload_paths = [
            "/api/v1/documents/upload",
            "/api/v1/tutor-invitations/documents"
        ]
    
    async def dispatch(self, request: Request, call_next):
        """Main security middleware handler."""
        start_time = time.time()
        
        try:
            # Skip security checks for bypass paths
            if any(request.url.path.startswith(path) for path in self.bypass_paths):
                return await call_next(request)
            
            # Apply security validations
            await self._validate_request_size(request)
            await self._validate_headers(request)
            await self._validate_query_params(request)
            await self._check_rate_limiting(request)
            
            # Validate request body if present
            if request.method in ["POST", "PUT", "PATCH"]:
                await self._validate_request_body(request)
            
            # Process request
            response = await call_next(request)
            
            # Add security headers to response
            self._add_security_headers(response)
            
            # Log request
            self._log_request(request, response, time.time() - start_time)
            
            return response
            
        except HTTPException as e:
            self.logger.warning(
                f"Security middleware blocked request: {request.method} {request.url.path} - {e.detail}"
            )
            return JSONResponse(
                status_code=e.status_code,
                content={"detail": e.detail}
            )
        except Exception as e:
            self.logger.error(f"Security middleware error: {e}")
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={"detail": "Internal security error"}
            )
    
    async def _validate_request_size(self, request: Request):
        """Validate request content length."""
        content_length = request.headers.get("content-length")
        if content_length:
            size = int(content_length)
            if size > self.max_request_size:
                raise HTTPException(
                    status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                    detail=f"Request too large: {size} bytes exceeds limit of {self.max_request_size} bytes"
                )
    
    async def _validate_headers(self, request: Request):
        """Validate HTTP headers for security issues."""
        # Skip strict validation in development mode or if explicitly disabled
        if os.environ.get('DISABLE_STRICT_SECURITY', 'false').lower() == 'true':
            return
        
        # In production behind a proxy (Railway), allow proxy headers
        if os.environ.get('ENVIRONMENT', 'development') == 'production':
            # Skip header validation for Railway proxy headers
            return
            
        # Check for malicious headers
        # Note: x-forwarded-* headers are commonly used by proxies and load balancers
        # Only block truly dangerous headers that could be used for attacks
        dangerous_headers = [
            "x-original-url",
            "x-rewrite-url"
        ]
        
        # Allow common proxy headers in development or if behind a trusted proxy
        if os.environ.get('ALLOW_PROXY_HEADERS', 'false').lower() == 'true':
            dangerous_headers = [
                "x-rewrite-url"  # Still block URL rewriting
            ]
        
        for header_name, header_value in request.headers.items():
            # Skip common browser headers that might trigger false positives
            if header_name.lower() in ['user-agent', 'accept', 'accept-language', 'accept-encoding']:
                continue
                
            # Sanitize header values
            sanitized_value = InputSanitizer.sanitize_text(header_value)
            
            # Check for malicious patterns in headers
            issues = InputSanitizer.check_malicious_patterns(sanitized_value)
            if issues:
                self.logger.warning(f"Malicious header detected: {header_name}={header_value}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid header content detected"
                )
            
            # Block dangerous headers
            if header_name.lower() in dangerous_headers:
                self.logger.warning(f"Dangerous header blocked: {header_name}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Dangerous header not allowed"
                )
        
        # Validate User-Agent
        user_agent = request.headers.get("user-agent", "")
        if self._is_suspicious_user_agent(user_agent):
            self.logger.warning(f"Suspicious user agent blocked: {user_agent}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid user agent"
            )
    
    async def _validate_query_params(self, request: Request):
        """Validate query parameters for security issues."""
        for param_name, param_value in request.query_params.items():
            # Sanitize parameter value
            sanitized_value = InputSanitizer.sanitize_text(str(param_value))
            
            # Check for malicious patterns
            issues = InputSanitizer.check_malicious_patterns(sanitized_value)
            if issues:
                self.logger.warning(
                    f"Malicious query parameter detected: {param_name}={param_value}, issues: {issues}"
                )
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid query parameter: {param_name}"
                )
            
            # Check parameter length
            if len(str(param_value)) > 1000:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Query parameter too long: {param_name}"
                )
    
    async def _validate_request_body(self, request: Request):
        """Validate request body for security issues."""
        content_type = request.headers.get("content-type", "")
        
        if "application/json" in content_type:
            await self._validate_json_body(request)
        elif "multipart/form-data" in content_type:
            await self._validate_file_upload(request)
        elif "application/x-www-form-urlencoded" in content_type:
            await self._validate_form_data(request)
    
    async def _validate_json_body(self, request: Request):
        """Validate JSON request body."""
        try:
            # Read the body
            body = await request.body()
            
            # IMPORTANT: Set the body back on the request so FastAPI can read it
            # This is necessary because reading the body consumes it
            async def receive():
                return {"type": "http.request", "body": body}
            request._receive = receive
            
            if len(body) > 100000:  # 100KB limit for JSON
                raise HTTPException(
                    status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                    detail="JSON payload too large"
                )
            
            if body:
                # Parse and validate JSON
                try:
                    json_data = json.loads(body)
                    
                    # Sanitize JSON data
                    sanitized_data = InputSanitizer.sanitize_json(json_data)
                    
                    # Check for deeply nested objects (DoS protection)
                    if self._check_json_depth(json_data, max_depth=10):
                        raise HTTPException(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            detail="JSON too deeply nested"
                        )
                    
                except json.JSONDecodeError:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Invalid JSON format"
                    )
                
        except UnicodeDecodeError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid request encoding"
            )
    
    async def _validate_file_upload(self, request: Request):
        """Validate file upload requests."""
        # Check if this is a file upload endpoint
        if not any(request.url.path.startswith(path) for path in self.file_upload_paths):
            return
        
        # Additional file upload validation would go here
        # This is handled in the specific upload endpoints
        pass
    
    async def _validate_form_data(self, request: Request):
        """Validate form data requests."""
        # Form data validation
        try:
            # Store the original body first
            body = await request.body()
            
            # Set the body back on the request so FastAPI can read it
            async def receive():
                return {"type": "http.request", "body": body}
            request._receive = receive
            
            # Now parse the form for validation
            form = await request.form()
            for field_name, field_value in form.items():
                if isinstance(field_value, str):
                    # Sanitize form field
                    sanitized_value = InputSanitizer.sanitize_text(field_value)
                    
                    # Check for malicious patterns
                    issues = InputSanitizer.check_malicious_patterns(sanitized_value)
                    if issues:
                        self.logger.warning(
                            f"Malicious form field detected: {field_name}={field_value}"
                        )
                        raise HTTPException(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            detail=f"Invalid form field: {field_name}"
                        )
        except Exception as e:
            self.logger.error(f"Form validation error: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid form data"
            )
    
    async def _check_rate_limiting(self, request: Request):
        """Check rate limiting for the request."""
        # Skip rate limiting for certain paths
        if any(request.url.path.startswith(path) for path in self.bypass_paths):
            return
            
        # For now, skip rate limiting until it's properly implemented
        # TODO: Implement proper rate limiting for API endpoints
        return
    
    def _add_security_headers(self, response: Response):
        """Add security headers to response."""
        security_headers = {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; connect-src 'self'",
            "Referrer-Policy": "strict-origin-when-cross-origin",
            "Permissions-Policy": "geolocation=(), microphone=(), camera=()"
        }
        
        for header_name, header_value in security_headers.items():
            response.headers[header_name] = header_value
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address safely."""
        # Check for forwarded IP headers (in order of preference)
        forwarded_headers = [
            "X-Forwarded-For",
            "X-Real-IP",
            "CF-Connecting-IP",  # Cloudflare
            "X-Client-IP"
        ]
        
        for header in forwarded_headers:
            ip = request.headers.get(header)
            if ip:
                # Take first IP if comma-separated
                ip = ip.split(',')[0].strip()
                if self._is_valid_ip(ip):
                    return ip
        
        # Fall back to direct client IP
        return getattr(request.client, 'host', '127.0.0.1')
    
    def _is_valid_ip(self, ip: str) -> bool:
        """Validate IP address format."""
        try:
            import ipaddress
            ipaddress.ip_address(ip)
            return True
        except ValueError:
            return False
    
    def _is_suspicious_user_agent(self, user_agent: str) -> bool:
        """Check if user agent is suspicious."""
        if not user_agent:
            return True
        
        # Common bot patterns to block
        suspicious_patterns = [
            r'bot',
            r'crawler',
            r'spider',
            r'scraper',
            r'scan',
            r'test',
            r'curl',
            r'wget',
            r'python',
            r'java',
            r'perl',
            r'ruby',
            r'php'
        ]
        
        user_agent_lower = user_agent.lower()
        for pattern in suspicious_patterns:
            if pattern in user_agent_lower:
                return True
        
        return False
    
    def _check_json_depth(self, obj: Any, current_depth: int = 0, max_depth: int = 10) -> bool:
        """Check if JSON object is too deeply nested."""
        if current_depth > max_depth:
            return True
        
        if isinstance(obj, dict):
            for value in obj.values():
                if self._check_json_depth(value, current_depth + 1, max_depth):
                    return True
        elif isinstance(obj, list):
            for item in obj:
                if self._check_json_depth(item, current_depth + 1, max_depth):
                    return True
        
        return False
    
    def _log_request(self, request: Request, response: Response, duration: float):
        """Log request for security monitoring."""
        self.logger.info(
            f"Request: {request.method} {request.url.path} - "
            f"Status: {response.status_code} - "
            f"Duration: {duration:.3f}s - "
            f"IP: {self._get_client_ip(request)} - "
            f"User-Agent: {request.headers.get('user-agent', 'Unknown')}"
        )


class InputValidationMiddleware(BaseHTTPMiddleware):
    """Middleware for comprehensive input validation."""
    
    def __init__(self, app):
        super().__init__(app)
        self.logger = TutorAideLogger.get_logger(__name__)
    
    async def dispatch(self, request: Request, call_next):
        """Input validation handler."""
        try:
            # Validate all string inputs in the request
            await self._validate_request_inputs(request)
            
            return await call_next(request)
            
        except ValueError as e:
            self.logger.warning(f"Input validation failed: {e}")
            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content={"detail": str(e)}
            )
        except HTTPException as e:
            return JSONResponse(
                status_code=e.status_code,
                content={"detail": e.detail}
            )
    
    async def _validate_request_inputs(self, request: Request):
        """Validate all inputs in the request."""
        # Validate path parameters
        for param_name, param_value in request.path_params.items():
            if isinstance(param_value, str):
                self._validate_string_input(param_value, f"path parameter {param_name}")
        
        # Validate query parameters
        for param_name, param_value in request.query_params.items():
            self._validate_string_input(str(param_value), f"query parameter {param_name}")
    
    def _validate_string_input(self, value: str, context: str):
        """Validate a string input."""
        # Check for null bytes
        if '\x00' in value:
            raise ValueError(f"Null byte detected in {context}")
        
        # Check for extremely long inputs (DoS protection)
        if len(value) > 10000:
            raise ValueError(f"Input too long in {context}")
        
        # Check for malicious patterns
        issues = InputSanitizer.check_malicious_patterns(value)
        if issues:
            self.logger.warning(f"Malicious input detected in {context}: {issues}")
            raise ValueError(f"Invalid content detected in {context}")