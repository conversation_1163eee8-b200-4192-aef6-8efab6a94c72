"""
Authentication service for multi-role user management and role switching.
"""

import logging
from typing import Dict, List, Optional, Set, Tuple
from datetime import datetime, timedelta
import asyncpg
from uuid import uuid4

from app.core.exceptions import (
    AuthenticationError, 
    AuthorizationError, 
    ValidationError,
    ResourceNotFoundError,
    BusinessLogicError
)
from app.core.security import (
    verify_password, 
    get_password_hash, 
    create_access_token,
    create_refresh_token,
    verify_token
)
from app.core.timezone import now_est
from app.database.repositories.user_repository import UserRepository
from app.models.user_models import (
    User, UserRole, UserProfile, UserCreate, UserResponse, 
    AuthResponse, UserRoleType, ConsentLevel, ConsentType,
    PasswordChange, ConsentAcceptance, UserConsent
)
from app.config.settings import settings
from app.services.auth_token_service import get_auth_token_service

logger = logging.getLogger(__name__)


class AuthService:
    """Authentication service for multi-role user management."""
    
    def __init__(self):
        self.user_repo = UserRepository()
        self.auth_token_service = get_auth_token_service()
    
    async def register_user(
        self,
        conn: asyncpg.Connection,
        user_data: UserCreate,
        ip_address: Optional[str] = None
    ) -> UserProfile:
        """
        Register a new user with initial role and mandatory consents.
        
        Args:
            conn: Database connection
            user_data: User registration data
            ip_address: User's IP address for consent tracking
        
        Returns:
            UserProfile object containing user, roles, and consents
        
        Raises:
            ValidationError: If email already exists or validation fails
            BusinessLogicError: If role assignment fails
        """
        try:
            # Check if email already exists
            if await self.user_repo.email_exists(conn, user_data.email):
                raise ValidationError("Email address is already registered")
            
            # Validate at least one authentication method
            if not user_data.password and not user_data.google_id:
                raise ValidationError("Either password or Google authentication is required")
            
            # Validate initial roles
            if not user_data.roles:
                raise ValidationError("At least one initial role is required")
            
            valid_roles = {UserRoleType.MANAGER, UserRoleType.TUTOR, UserRoleType.CLIENT}
            invalid_roles = set(user_data.roles) - valid_roles
            if invalid_roles:
                raise ValidationError(f"Invalid roles: {invalid_roles}")
            
            # Create user account data
            account_data = {
                "email": user_data.email.lower().strip(),
                "password_hash": get_password_hash(user_data.password) if user_data.password else None,
                "google_id": user_data.google_id,
                "email_verified": bool(user_data.google_id),  # Google accounts are pre-verified
                "created_at": now_est(),
                "updated_at": now_est()
            }
            
            async with conn.transaction():
                # Create user account
                user_record = await self.user_repo.create(conn, account_data)
                user_id = user_record['user_id']
                
                # Add initial roles
                for role in user_data.roles:
                    await self.user_repo.add_user_role(conn, user_id, role.value)
                
                # Record mandatory consents (Level 1)
                mandatory_consents = [
                    ConsentType.TERMS_OF_SERVICE,
                    ConsentType.PRIVACY_POLICY
                ]
                
                for consent_type in mandatory_consents:
                    await self.user_repo.record_user_consent(
                        conn, user_id, ConsentLevel.LEVEL_1.value, 
                        consent_type.value, ip_address
                    )
                
                # Get complete user profile
                user_profile = await self.get_user_profile(conn, user_id)
                
                # Create access token with roles
                primary_role = user_data.roles[0]  # Use first role as primary
                access_token = self._create_role_specific_token(user_id, [primary_role], primary_role)
                
                # Create persistent session using auth_token_service
                session_token, session_info = await self.auth_token_service.create_session(
                    conn=conn,
                    user_id=user_id,
                    role_type=primary_role,
                    ip_address=ip_address,
                    user_agent=None
                )
                
                logger.info(f"Registered user {user_id} with roles {user_data.roles}")
                return user_profile
                
        except Exception as e:
            logger.error(f"Error registering user: {e}")
            if isinstance(e, (ValidationError, BusinessLogicError)):
                raise
            raise BusinessLogicError(f"Registration failed: {str(e)}")
    
    async def authenticate_user(
        self,
        conn: asyncpg.Connection,
        email: str,
        password: str,
        requested_role: Optional[UserRoleType] = None
    ) -> AuthResponse:
        """
        Authenticate user with email/password and return auth response.
        
        Args:
            conn: Database connection
            email: User email
            password: User password
            requested_role: Specific role to authenticate as
        
        Returns:
            AuthResponse with token and user info
        
        Raises:
            AuthenticationError: If credentials are invalid
            AuthorizationError: If requested role is not available
        """
        try:
            # Find user by email
            user_record = await self.user_repo.find_by_email(conn, email)
            if not user_record:
                raise AuthenticationError("Invalid email or password")
            
            # Verify password
            if not user_record['password_hash']:
                raise AuthenticationError("Password authentication not available for this account")
            
            if not verify_password(password, user_record['password_hash']):
                raise AuthenticationError("Invalid email or password")
            
            # Get user profile with roles
            user_profile = await self.get_user_profile(conn, user_record['user_id'])
            
            # Check if user has any active roles
            active_roles = user_profile.get_active_roles()
            if not active_roles:
                raise AuthenticationError("Account has no active roles")
            
            # Validate requested role
            if requested_role:
                if requested_role not in active_roles:
                    raise AuthorizationError(f"User does not have {requested_role.value} role")
                primary_role = requested_role
            else:
                # Use first active role as default
                primary_role = active_roles[0]
            
            # Check mandatory consents
            required_consents = self._check_required_consents(user_profile)
            
            # Create persistent session using auth_token_service
            session_token, session_info = await self.auth_token_service.create_session(
                conn=conn,
                user_id=user_record['user_id'],
                role_type=primary_role,
                ip_address=None,  # Can be added later
                user_agent=None   # Can be added later
            )
            
            # Create access token
            access_token = self._create_role_specific_token(
                user_record['user_id'], active_roles, primary_role
            )
            
            # Create response
            user_response = UserResponse(
                user_id=user_record['user_id'],
                email=user_record['email'],
                is_email_verified=user_record.get('email_verified', False),
                has_local_auth=bool(user_record['password_hash']),
                has_google_auth=bool(user_record['google_id']),
                roles=active_roles,
                created_at=user_record['created_at'],
                updated_at=user_record['updated_at']
            )
            
            expires_in = self._get_token_expiry_seconds(primary_role)
            
            auth_response = AuthResponse(
                access_token=access_token,
                token_type="bearer",
                expires_in=expires_in,
                user=user_response,
                requires_consents=required_consents
            )
            
            logger.info(f"Authenticated user {user_record['user_id']} as {primary_role.value}")
            return auth_response
            
        except Exception as e:
            logger.error(f"Error authenticating user: {e}")
            if isinstance(e, (AuthenticationError, AuthorizationError)):
                raise
            raise AuthenticationError("Authentication failed")
    
    async def switch_user_role(
        self,
        conn: asyncpg.Connection,
        current_token: str,
        target_role: UserRoleType,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> AuthResponse:
        """
        Switch user to a different role without re-authentication.
        
        Args:
            conn: Database connection
            current_token: Current valid access token
            target_role: Role to switch to
        
        Returns:
            AuthResponse with new token for target role
        
        Raises:
            AuthenticationError: If current token is invalid
            AuthorizationError: If user doesn't have target role
        """
        try:
            # Verify current token
            payload = verify_token(current_token)
            user_id = int(payload.get("sub"))
            current_roles = payload.get("roles", [])
            
            # Get fresh user profile to ensure roles are still valid
            user_profile = await self.get_user_profile(conn, user_id)
            active_roles = user_profile.get_active_roles()
            
            # Validate target role
            if target_role not in active_roles:
                raise AuthorizationError(f"User does not have {target_role.value} role")
            
            # Create new persistent session for new role using auth_token_service
            new_session_token, session_info = await self.auth_token_service.create_session(
                conn=conn,
                user_id=user_id,
                role_type=target_role,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            # Create new token with target role as primary
            new_token = self._create_role_specific_token(user_id, active_roles, target_role)
            
            # Invalidate all other sessions for this user
            await self.auth_token_service.invalidate_all_user_sessions(
                conn=conn,
                user_id=user_id,
                except_token=None  # Invalidate all sessions
            )
            
            # Create response
            user_response = UserResponse(
                user_id=user_profile.user.user_id,
                email=user_profile.user.email,
                is_email_verified=user_profile.user.is_email_verified,
                has_local_auth=user_profile.user.has_local_auth(),
                has_google_auth=user_profile.user.has_google_auth(),
                roles=active_roles,
                created_at=user_profile.user.created_at,
                updated_at=user_profile.user.updated_at
            )
            
            expires_in = self._get_token_expiry_seconds(target_role)
            
            auth_response = AuthResponse(
                access_token=new_token,
                token_type="bearer",
                expires_in=expires_in,
                user=user_response,
                requires_consents=[]  # Assume consents already handled
            )
            
            logger.info(f"User {user_id} switched from role to {target_role.value}")
            return auth_response
            
        except Exception as e:
            logger.error(f"Error switching user role: {e}")
            if isinstance(e, (AuthenticationError, AuthorizationError)):
                raise
            raise AuthenticationError("Role switch failed")
    
    async def get_user_profile(
        self,
        conn: asyncpg.Connection,
        user_id: int
    ) -> UserProfile:
        """
        Get complete user profile with roles and consents.
        
        Args:
            conn: Database connection
            user_id: User ID
        
        Returns:
            UserProfile object
        
        Raises:
            ResourceNotFoundError: If user not found
        """
        try:
            # Get user account
            user_record = await self.user_repo.find_by_id(conn, user_id)
            if not user_record:
                raise ResourceNotFoundError(f"User {user_id} not found")
            
            # Get user roles
            role_records = await self.user_repo.get_user_roles(conn, user_id)
            
            # Extract role types for User model
            active_role_types = [
                UserRoleType(role['role_type']) 
                for role in role_records 
                if role['is_active']
            ]
            
            user = User(
                user_id=user_record['user_id'],
                email=user_record['email'],
                password_hash=user_record['password_hash'],
                google_id=user_record['google_id'],
                is_email_verified=user_record['email_verified'],
                created_at=user_record['created_at'],
                updated_at=user_record['updated_at'],
                deleted_at=user_record['deleted_at'],
                roles=active_role_types
            )
            
            # Create UserRole objects for UserProfile
            roles = [
                UserRole(
                    user_role_id=role['user_role_id'],
                    user_id=role['user_id'],
                    role_type=UserRoleType(role['role_type']),
                    is_active=role['is_active'],
                    created_at=role['created_at'],
                    # Use created_at as updated_at if column doesn't exist
                    updated_at=role.get('updated_at', role['created_at']),
                    deleted_at=role.get('deleted_at', None)
                )
                for role in role_records
            ]
            
            # Get user consents
            consent_records = await self.user_repo.get_user_consents(conn, user_id)
            # Since get_user_consents returns empty list due to schema changes, handle gracefully
            consents = []
            if consent_records:
                try:
                    consents = [
                        UserConsent(
                            consent_id=consent['consent_id'],
                            user_id=consent['user_id'],
                            consent_level=ConsentLevel(consent['consent_level']),
                            consent_type=ConsentType(consent['consent_type']),
                            accepted_at=consent['accepted_at'],
                            ip_address=str(consent['ip_address']) if consent['ip_address'] else None,
                            created_at=consent.get('created_at', consent['accepted_at']),
                            updated_at=consent.get('updated_at', consent['accepted_at']),
                            deleted_at=consent.get('deleted_at')
                        )
                        for consent in consent_records
                    ]
                except Exception as e:
                    logger.warning(f"Failed to parse consent records: {e}")
                    consents = []
            
            # Note: Notification preferences would be added here when implemented
            notification_preferences = []
            
            return UserProfile(
                user=user,
                roles=roles,
                consents=consents,
                notification_preferences=notification_preferences
            )
            
        except Exception as e:
            logger.error(f"Error getting user profile: {e}")
            if isinstance(e, ResourceNotFoundError):
                raise
            raise BusinessLogicError(f"Failed to get user profile: {str(e)}")
    
    async def logout_user(self, conn: asyncpg.Connection, session_token: str) -> bool:
        """
        Logout user by invalidating their session.
        
        Args:
            conn: Database connection
            session_token: Session token to invalidate
        
        Returns:
            True if session was invalidated (always returns True for security)
        """
        try:
            await self.auth_token_service.invalidate_session(conn, session_token)
            return True  # Always return True for security reasons
        except Exception as e:
            logger.error(f"Error logging out user: {e}")
            return True  # Still return True for security
    
    async def is_session_valid(self, conn: asyncpg.Connection, session_token: str) -> bool:
        """
        Check if a session token is still valid.
        
        Args:
            conn: Database connection
            session_token: Session token to check
        
        Returns:
            True if session is valid
        """
        try:
            session_info = await self.auth_token_service.validate_session(
                conn=conn,
                token=session_token,
                update_activity=True
            )
            return session_info is not None
        except Exception:
            return False
    
    def _create_role_specific_token(
        self,
        user_id: int,
        available_roles: List[UserRoleType],
        primary_role: UserRoleType
    ) -> str:
        """Create JWT token with role-specific expiration."""
        expires_delta = timedelta(minutes=self._get_token_expiry_minutes(primary_role))
        # Handle both string and enum types for backward compatibility
        if available_roles and isinstance(available_roles[0], str):
            logger.warning(f"Received string roles instead of enums: {available_roles}")
            role_strings = available_roles  # Already strings
        else:
            role_strings = [role.value for role in available_roles]
        
        return create_access_token(
            subject=str(user_id),
            expires_delta=expires_delta,
            user_roles=role_strings
        )
    
    def _get_token_expiry_minutes(self, role: UserRoleType) -> int:
        """Get token expiry minutes based on role."""
        # Handle both string and enum types
        if isinstance(role, str):
            role_value = role
        else:
            role_value = role.value
            
        if role_value == 'manager':
            return settings.MANAGER_TOKEN_EXPIRE_MINUTES
        else:
            return settings.ACCESS_TOKEN_EXPIRE_MINUTES
    
    def _get_token_expiry_seconds(self, role: UserRoleType) -> int:
        """Get token expiry in seconds for API response."""
        return self._get_token_expiry_minutes(role) * 60
    
    async def cleanup_expired_sessions(self, conn: asyncpg.Connection) -> int:
        """
        Clean up expired sessions from database.
        
        Returns:
            Number of sessions cleaned up
        """
        try:
            return await self.auth_token_service.cleanup_expired_tokens(
                conn=conn,
                batch_size=1000
            )
        except Exception as e:
            logger.error(f"Error cleaning up sessions: {e}")
            return 0
    
    def _check_required_consents(self, user_profile: UserProfile) -> List[ConsentType]:
        """Check what consents are still required."""
        if user_profile.has_mandatory_consents():
            return []
        
        # Determine missing mandatory consents
        given_consents = {
            consent.consent_type for consent in user_profile.consents
            if consent.consent_level == ConsentLevel.LEVEL_1
        }
        
        required_consents = {ConsentType.TERMS_OF_SERVICE, ConsentType.PRIVACY_POLICY}
        missing_consents = required_consents - given_consents
        
        return list(missing_consents)
    


# Dependency injection for FastAPI
async def get_auth_service() -> AuthService:
    """Dependency injection for AuthService."""
    return AuthService()