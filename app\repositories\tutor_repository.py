"""
Tutor Repository - Handles all database operations for tutors
"""
from typing import List, Optional, Dict, Any, <PERSON><PERSON>
from datetime import datetime, date
from decimal import Decimal
from loguru import logger

from app.database import Database
from app.domain.tutor.models import (
    TutorProfile, TutorDocument, TutorEducation, TutorExperience,
    TutorSpecialization, TutorVerificationLog, TutorPerformanceMetrics,
    DocumentCreate, DocumentUpdate, EducationCreate, EducationUpdate,
    ExperienceCreate, ExperienceUpdate, SpecializationCreate,
    SpecializationUpdate, TutorProfileUpdate, VerificationLogCreate,
    DocumentType, DocumentVerificationStatus, VerificationStatus
)
from app.repositories.base import BaseRepository


class TutorRepository(BaseRepository):
    """Repository for tutor-related database operations"""
    
    def __init__(self, db: Database):
        super().__init__(db)
    
    # ==================== Tutor Profile Operations ====================
    
    async def get_tutor_by_id(self, tutor_id: int) -> Optional[TutorProfile]:
        """Get tutor by ID with all relationships"""
        return await self._get_full_tutor_profile(tutor_id)
    
    async def get_tutor_by_user_id(self, user_id: int) -> Optional[TutorProfile]:
        """Get tutor by user ID"""
        query = """
            SELECT tutor_id FROM tutor_profiles 
            WHERE user_id = $1 AND deleted_at IS NULL
        """
        row = await self.db.fetchrow(query, user_id)
        if row:
            return await self._get_full_tutor_profile(row['tutor_id'])
        return None
    
    async def update_tutor_profile(
        self, tutor_id: int, data: TutorProfileUpdate
    ) -> Optional[TutorProfile]:
        """Update tutor profile"""
        update_fields = []
        params = []
        param_count = 1
        
        update_dict = data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            update_fields.append(f"{field} = ${param_count}")
            params.append(value)
            param_count += 1
        
        if not update_fields:
            return await self.get_tutor_by_id(tutor_id)
        
        params.append(tutor_id)
        query = f"""
            UPDATE tutor_profiles
            SET {', '.join(update_fields)}, updated_at = CURRENT_TIMESTAMP
            WHERE tutor_id = ${param_count} AND deleted_at IS NULL
            RETURNING *
        """
        
        await self.db.execute(query, *params)
        
        # Update profile completeness
        await self.calculate_and_update_completeness(tutor_id)
        
        return await self._get_full_tutor_profile(tutor_id)
    
    async def search_tutors(
        self,
        query: Optional[str] = None,
        subject_area: Optional[str] = None,
        verification_status: Optional[str] = None,
        is_active: Optional[bool] = None,
        is_featured: Optional[bool] = None,
        min_rating: Optional[float] = None,
        languages: Optional[List[str]] = None,
        accepts_special_needs: Optional[bool] = None,
        offset: int = 0,
        limit: int = 50,
        sort_by: str = "created_at",
        sort_order: str = "DESC"
    ) -> Tuple[List[TutorProfile], int]:
        """Search tutors with filters and pagination"""
        where_clauses = ["tp.deleted_at IS NULL"]
        params = []
        param_count = 1
        
        if query:
            where_clauses.append(f"""
                (u.first_name ILIKE ${param_count} OR 
                 u.last_name ILIKE ${param_count} OR 
                 u.email ILIKE ${param_count} OR
                 tp.bio ILIKE ${param_count} OR
                 tp.headline ILIKE ${param_count})
            """)
            params.append(f"%{query}%")
            param_count += 1
        
        if subject_area:
            where_clauses.append(f"""
                EXISTS (
                    SELECT 1 FROM tutor_specializations ts
                    WHERE ts.tutor_id = tp.tutor_id 
                    AND ts.subject_area = ${param_count}
                )
            """)
            params.append(subject_area)
            param_count += 1
        
        if verification_status:
            where_clauses.append(f"tp.verification_status = ${param_count}")
            params.append(verification_status)
            param_count += 1
        
        if is_active is not None:
            where_clauses.append(f"u.is_active = ${param_count}")
            params.append(is_active)
            param_count += 1
        
        if is_featured is not None:
            where_clauses.append(f"tp.is_featured = ${param_count}")
            params.append(is_featured)
            param_count += 1
        
        if min_rating is not None:
            where_clauses.append(f"tp.rating >= ${param_count}")
            params.append(min_rating)
            param_count += 1
        
        if languages:
            where_clauses.append(f"tp.languages_spoken && ${param_count}::text[]")
            params.append(languages)
            param_count += 1
        
        if accepts_special_needs is not None:
            where_clauses.append(f"tp.accepts_special_needs = ${param_count}")
            params.append(accepts_special_needs)
            param_count += 1
        
        where_clause = " AND ".join(where_clauses)
        
        # Count query
        count_query = f"""
            SELECT COUNT(*) as total
            FROM tutor_profiles tp
            JOIN users u ON tp.user_id = u.user_id
            WHERE {where_clause}
        """
        count_result = await self.db.fetchrow(count_query, *params)
        total_count = count_result['total']
        
        # Data query
        allowed_sort_fields = {
            "created_at": "tp.created_at",
            "updated_at": "tp.updated_at",
            "rating": "tp.rating",
            "total_reviews": "tp.total_reviews",
            "years_of_experience": "tp.years_of_experience",
            "profile_completeness": "tp.profile_completeness"
        }
        sort_field = allowed_sort_fields.get(sort_by, "tp.created_at")
        sort_direction = "ASC" if sort_order.upper() == "ASC" else "DESC"
        
        params.extend([limit, offset])
        data_query = f"""
            SELECT tp.tutor_id
            FROM tutor_profiles tp
            JOIN users u ON tp.user_id = u.user_id
            WHERE {where_clause}
            ORDER BY {sort_field} {sort_direction}
            LIMIT ${param_count} OFFSET ${param_count + 1}
        """
        
        rows = await self.db.fetch(data_query, *params)
        tutors = []
        for row in rows:
            tutor = await self._get_full_tutor_profile(row['tutor_id'])
            if tutor:
                tutors.append(tutor)
        
        return tutors, total_count
    
    # ==================== Document Operations ====================
    
    async def add_document(self, tutor_id: int, data: DocumentCreate) -> TutorDocument:
        """Add document to tutor profile"""
        query = """
            INSERT INTO tutor_documents (
                tutor_id, document_type, document_name, file_url,
                file_size, mime_type, uploaded_by, expiry_date, is_public
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            RETURNING *
        """
        
        row = await self.db.fetchrow(
            query, tutor_id, data.document_type, data.document_name,
            data.file_url, data.file_size, data.mime_type,
            data.uploaded_by, data.expiry_date, data.is_public
        )
        
        return TutorDocument(**dict(row))
    
    async def update_document(
        self, document_id: int, data: DocumentUpdate
    ) -> Optional[TutorDocument]:
        """Update document"""
        update_fields = []
        params = []
        param_count = 1
        
        update_dict = data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            update_fields.append(f"{field} = ${param_count}")
            params.append(value)
            param_count += 1
        
        if not update_fields:
            return await self.get_document_by_id(document_id)
        
        params.append(document_id)
        query = f"""
            UPDATE tutor_documents
            SET {', '.join(update_fields)}, updated_at = CURRENT_TIMESTAMP
            WHERE document_id = ${param_count} AND deleted_at IS NULL
            RETURNING *
        """
        
        row = await self.db.fetchrow(query, *params)
        return TutorDocument(**dict(row)) if row else None
    
    async def verify_document(
        self, document_id: int, 
        verified_by: int,
        status: DocumentVerificationStatus,
        notes: Optional[str] = None
    ) -> Optional[TutorDocument]:
        """Verify or reject a document"""
        query = """
            UPDATE tutor_documents
            SET verification_status = $1,
                verified_by = $2,
                verified_at = CURRENT_TIMESTAMP,
                verification_notes = $3,
                updated_at = CURRENT_TIMESTAMP
            WHERE document_id = $4 AND deleted_at IS NULL
            RETURNING *
        """
        
        row = await self.db.fetchrow(query, status, verified_by, notes, document_id)
        return TutorDocument(**dict(row)) if row else None
    
    async def delete_document(self, document_id: int) -> bool:
        """Delete document"""
        query = """
            UPDATE tutor_documents
            SET deleted_at = CURRENT_TIMESTAMP
            WHERE document_id = $1 AND deleted_at IS NULL
            RETURNING document_id
        """
        result = await self.db.fetchrow(query, document_id)
        return result is not None
    
    async def get_document_by_id(self, document_id: int) -> Optional[TutorDocument]:
        """Get document by ID"""
        query = """
            SELECT * FROM tutor_documents
            WHERE document_id = $1 AND deleted_at IS NULL
        """
        row = await self.db.fetchrow(query, document_id)
        return TutorDocument(**dict(row)) if row else None
    
    # ==================== Education Operations ====================
    
    async def add_education(self, tutor_id: int, data: EducationCreate) -> TutorEducation:
        """Add education entry"""
        query = """
            INSERT INTO tutor_education (
                tutor_id, institution, degree, field_of_study,
                start_date, graduation_date, is_current, gpa,
                honors_awards, relevant_coursework, thesis_title, advisor_name
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
            RETURNING *
        """
        
        row = await self.db.fetchrow(
            query, tutor_id, data.institution, data.degree,
            data.field_of_study, data.start_date, data.graduation_date,
            data.is_current, data.gpa, data.honors_awards,
            data.relevant_coursework, data.thesis_title, data.advisor_name
        )
        
        return TutorEducation(**dict(row))
    
    async def update_education(
        self, education_id: int, data: EducationUpdate
    ) -> Optional[TutorEducation]:
        """Update education entry"""
        update_fields = []
        params = []
        param_count = 1
        
        update_dict = data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            update_fields.append(f"{field} = ${param_count}")
            params.append(value)
            param_count += 1
        
        if not update_fields:
            return await self.get_education_by_id(education_id)
        
        params.append(education_id)
        query = f"""
            UPDATE tutor_education
            SET {', '.join(update_fields)}, updated_at = CURRENT_TIMESTAMP
            WHERE education_id = ${param_count} AND deleted_at IS NULL
            RETURNING *
        """
        
        row = await self.db.fetchrow(query, *params)
        return TutorEducation(**dict(row)) if row else None
    
    async def delete_education(self, education_id: int) -> bool:
        """Delete education entry"""
        query = """
            UPDATE tutor_education
            SET deleted_at = CURRENT_TIMESTAMP
            WHERE education_id = $1 AND deleted_at IS NULL
            RETURNING education_id
        """
        result = await self.db.fetchrow(query, education_id)
        return result is not None
    
    async def get_education_by_id(self, education_id: int) -> Optional[TutorEducation]:
        """Get education by ID"""
        query = """
            SELECT * FROM tutor_education
            WHERE education_id = $1 AND deleted_at IS NULL
        """
        row = await self.db.fetchrow(query, education_id)
        return TutorEducation(**dict(row)) if row else None
    
    # ==================== Experience Operations ====================
    
    async def add_experience(self, tutor_id: int, data: ExperienceCreate) -> TutorExperience:
        """Add experience entry"""
        query = """
            INSERT INTO tutor_experience (
                tutor_id, organization, position, employment_type,
                start_date, end_date, is_current, location, description,
                achievements, skills_used, students_taught,
                age_groups_taught, subjects_taught
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
            RETURNING *
        """
        
        row = await self.db.fetchrow(
            query, tutor_id, data.organization, data.position,
            data.employment_type, data.start_date, data.end_date,
            data.is_current, data.location, data.description,
            data.achievements, data.skills_used, data.students_taught,
            data.age_groups_taught, data.subjects_taught
        )
        
        return TutorExperience(**dict(row))
    
    async def update_experience(
        self, experience_id: int, data: ExperienceUpdate
    ) -> Optional[TutorExperience]:
        """Update experience entry"""
        update_fields = []
        params = []
        param_count = 1
        
        update_dict = data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            update_fields.append(f"{field} = ${param_count}")
            params.append(value)
            param_count += 1
        
        if not update_fields:
            return await self.get_experience_by_id(experience_id)
        
        params.append(experience_id)
        query = f"""
            UPDATE tutor_experience
            SET {', '.join(update_fields)}, updated_at = CURRENT_TIMESTAMP
            WHERE experience_id = ${param_count} AND deleted_at IS NULL
            RETURNING *
        """
        
        row = await self.db.fetchrow(query, *params)
        return TutorExperience(**dict(row)) if row else None
    
    async def delete_experience(self, experience_id: int) -> bool:
        """Delete experience entry"""
        query = """
            UPDATE tutor_experience
            SET deleted_at = CURRENT_TIMESTAMP
            WHERE experience_id = $1 AND deleted_at IS NULL
            RETURNING experience_id
        """
        result = await self.db.fetchrow(query, experience_id)
        return result is not None
    
    async def get_experience_by_id(self, experience_id: int) -> Optional[TutorExperience]:
        """Get experience by ID"""
        query = """
            SELECT * FROM tutor_experience
            WHERE experience_id = $1 AND deleted_at IS NULL
        """
        row = await self.db.fetchrow(query, experience_id)
        return TutorExperience(**dict(row)) if row else None
    
    # ==================== Specialization Operations ====================
    
    async def add_specialization(
        self, tutor_id: int, data: SpecializationCreate
    ) -> TutorSpecialization:
        """Add specialization"""
        query = """
            INSERT INTO tutor_specializations (
                tutor_id, subject_area, level, years_experience,
                certification, certification_date, certification_expiry,
                proficiency_level, max_students_per_session,
                preferred_age_groups, teaching_methods, success_stories
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
            RETURNING *
        """
        
        row = await self.db.fetchrow(
            query, tutor_id, data.subject_area, data.level,
            data.years_experience, data.certification,
            data.certification_date, data.certification_expiry,
            data.proficiency_level, data.max_students_per_session,
            data.preferred_age_groups, data.teaching_methods,
            data.success_stories
        )
        
        return TutorSpecialization(**dict(row))
    
    async def update_specialization(
        self, specialization_id: int, data: SpecializationUpdate
    ) -> Optional[TutorSpecialization]:
        """Update specialization"""
        update_fields = []
        params = []
        param_count = 1
        
        update_dict = data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            update_fields.append(f"{field} = ${param_count}")
            params.append(value)
            param_count += 1
        
        if not update_fields:
            return await self.get_specialization_by_id(specialization_id)
        
        params.append(specialization_id)
        query = f"""
            UPDATE tutor_specializations
            SET {', '.join(update_fields)}, updated_at = CURRENT_TIMESTAMP
            WHERE specialization_id = ${param_count}
            RETURNING *
        """
        
        row = await self.db.fetchrow(query, *params)
        return TutorSpecialization(**dict(row)) if row else None
    
    async def delete_specialization(self, specialization_id: int) -> bool:
        """Delete specialization"""
        query = """
            DELETE FROM tutor_specializations
            WHERE specialization_id = $1
            RETURNING specialization_id
        """
        result = await self.db.fetchrow(query, specialization_id)
        return result is not None
    
    async def get_specialization_by_id(
        self, specialization_id: int
    ) -> Optional[TutorSpecialization]:
        """Get specialization by ID"""
        query = """
            SELECT * FROM tutor_specializations
            WHERE specialization_id = $1
        """
        row = await self.db.fetchrow(query, specialization_id)
        return TutorSpecialization(**dict(row)) if row else None
    
    # ==================== Verification Operations ====================
    
    async def log_verification_action(
        self, tutor_id: int, data: VerificationLogCreate
    ) -> TutorVerificationLog:
        """Log verification action"""
        query = """
            INSERT INTO tutor_verification_log (
                tutor_id, verification_step, previous_status,
                new_status, verified_by, verification_method,
                notes, documents_reviewed, issues_found
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            RETURNING *
        """
        
        row = await self.db.fetchrow(
            query, tutor_id, data.verification_step, data.previous_status,
            data.new_status, data.verified_by, data.verification_method,
            data.notes, data.documents_reviewed, data.issues_found
        )
        
        return TutorVerificationLog(**dict(row))
    
    async def update_verification_status(
        self, tutor_id: int, 
        status: VerificationStatus,
        verified_by: int,
        notes: Optional[str] = None
    ) -> bool:
        """Update tutor verification status"""
        # Get current status
        current = await self.db.fetchrow("""
            SELECT verification_status FROM tutor_profiles
            WHERE tutor_id = $1
        """, tutor_id)
        
        if not current:
            return False
        
        # Log the change
        await self.log_verification_action(tutor_id, VerificationLogCreate(
            verification_step="profile_verification",
            previous_status=current['verification_status'],
            new_status=status,
            verified_by=verified_by,
            notes=notes
        ))
        
        # Update the status
        query = """
            UPDATE tutor_profiles
            SET verification_status = $1,
                verified_at = CASE WHEN $1 = 'verified' THEN CURRENT_TIMESTAMP ELSE verified_at END,
                updated_at = CURRENT_TIMESTAMP
            WHERE tutor_id = $2
        """
        
        await self.db.execute(query, status, tutor_id)
        return True
    
    # ==================== Performance Metrics Operations ====================
    
    async def get_performance_metrics(
        self, tutor_id: int, 
        period_start: date,
        period_end: date
    ) -> Optional[TutorPerformanceMetrics]:
        """Get performance metrics for a specific period"""
        query = """
            SELECT * FROM tutor_performance_metrics
            WHERE tutor_id = $1 AND period_start = $2 AND period_end = $3
        """
        row = await self.db.fetchrow(query, tutor_id, period_start, period_end)
        return TutorPerformanceMetrics(**dict(row)) if row else None
    
    async def update_performance_metrics(
        self, tutor_id: int,
        period_start: date,
        period_end: date,
        metrics: Dict[str, Any]
    ) -> TutorPerformanceMetrics:
        """Update or create performance metrics"""
        # Build the update query
        update_fields = []
        insert_fields = ["tutor_id", "period_start", "period_end"]
        insert_values = ["$1", "$2", "$3"]
        params = [tutor_id, period_start, period_end]
        param_count = 4
        
        for field, value in metrics.items():
            update_fields.append(f"{field} = EXCLUDED.{field}")
            insert_fields.append(field)
            insert_values.append(f"${param_count}")
            params.append(value)
            param_count += 1
        
        query = f"""
            INSERT INTO tutor_performance_metrics ({', '.join(insert_fields)})
            VALUES ({', '.join(insert_values)})
            ON CONFLICT (tutor_id, period_start, period_end)
            DO UPDATE SET {', '.join(update_fields)}, updated_at = CURRENT_TIMESTAMP
            RETURNING *
        """
        
        row = await self.db.fetchrow(query, *params)
        return TutorPerformanceMetrics(**dict(row))
    
    # ==================== Helper Methods ====================
    
    async def _get_full_tutor_profile(self, tutor_id: int) -> Optional[TutorProfile]:
        """Get complete tutor profile with all relationships"""
        # Get base profile with user info
        profile_query = """
            SELECT tp.*, u.first_name, u.last_name, u.email, 
                   u.is_active, u.preferred_language
            FROM tutor_profiles tp
            JOIN users u ON tp.user_id = u.user_id
            WHERE tp.tutor_id = $1 AND tp.deleted_at IS NULL
        """
        profile_row = await self.db.fetchrow(profile_query, tutor_id)
        if not profile_row:
            return None
        
        # Get documents
        documents_query = """
            SELECT * FROM tutor_documents
            WHERE tutor_id = $1 AND deleted_at IS NULL
            ORDER BY created_at DESC
        """
        document_rows = await self.db.fetch(documents_query, tutor_id)
        documents = [TutorDocument(**dict(row)) for row in document_rows]
        
        # Get education
        education_query = """
            SELECT * FROM tutor_education
            WHERE tutor_id = $1 AND deleted_at IS NULL
            ORDER BY graduation_date DESC NULLS FIRST, start_date DESC
        """
        education_rows = await self.db.fetch(education_query, tutor_id)
        education = [TutorEducation(**dict(row)) for row in education_rows]
        
        # Get experience
        experience_query = """
            SELECT * FROM tutor_experience
            WHERE tutor_id = $1 AND deleted_at IS NULL
            ORDER BY end_date DESC NULLS FIRST, start_date DESC
        """
        experience_rows = await self.db.fetch(experience_query, tutor_id)
        experience = [TutorExperience(**dict(row)) for row in experience_rows]
        
        # Get specializations
        specializations_query = """
            SELECT * FROM tutor_specializations
            WHERE tutor_id = $1
            ORDER BY years_experience DESC, subject_area
        """
        specialization_rows = await self.db.fetch(specializations_query, tutor_id)
        specializations = [TutorSpecialization(**dict(row)) for row in specialization_rows]
        
        # Get verification logs
        logs_query = """
            SELECT * FROM tutor_verification_log
            WHERE tutor_id = $1
            ORDER BY timestamp DESC
            LIMIT 10
        """
        log_rows = await self.db.fetch(logs_query, tutor_id)
        verification_logs = [TutorVerificationLog(**dict(row)) for row in log_rows]
        
        # Get latest performance metrics
        metrics_query = """
            SELECT * FROM tutor_performance_metrics
            WHERE tutor_id = $1
            ORDER BY period_end DESC
            LIMIT 1
        """
        metrics_row = await self.db.fetchrow(metrics_query, tutor_id)
        latest_metrics = TutorPerformanceMetrics(**dict(metrics_row)) if metrics_row else None
        
        # Build complete profile
        profile_data = dict(profile_row)
        profile_data['documents'] = documents
        profile_data['education'] = education
        profile_data['experience'] = experience
        profile_data['specializations'] = specializations
        profile_data['verification_logs'] = verification_logs
        profile_data['latest_performance_metrics'] = latest_metrics
        profile_data['is_active'] = profile_row['is_active']
        
        return TutorProfile(**profile_data)
    
    async def calculate_and_update_completeness(self, tutor_id: int) -> int:
        """Calculate and update profile completeness"""
        query = """
            SELECT calculate_tutor_profile_completeness($1) as completeness
        """
        result = await self.db.fetchrow(query, tutor_id)
        completeness = result['completeness']
        
        # Update the profile
        await self.db.execute("""
            UPDATE tutor_profiles
            SET profile_completeness = $1, updated_at = CURRENT_TIMESTAMP
            WHERE tutor_id = $2
        """, completeness, tutor_id)
        
        return completeness
    
    async def get_tutors_by_ids(self, tutor_ids: List[int]) -> List[TutorProfile]:
        """Get multiple tutors by IDs"""
        if not tutor_ids:
            return []
        
        tutors = []
        for tutor_id in tutor_ids:
            tutor = await self._get_full_tutor_profile(tutor_id)
            if tutor:
                tutors.append(tutor)
        
        return tutors
    
    async def get_featured_tutors(self, limit: int = 10) -> List[TutorProfile]:
        """Get featured tutors"""
        query = """
            SELECT tutor_id FROM tutor_profiles
            WHERE is_featured = TRUE 
            AND deleted_at IS NULL
            AND (featured_until IS NULL OR featured_until > CURRENT_DATE)
            ORDER BY rating DESC, total_reviews DESC
            LIMIT $1
        """
        
        rows = await self.db.fetch(query, limit)
        tutors = []
        for row in rows:
            tutor = await self._get_full_tutor_profile(row['tutor_id'])
            if tutor:
                tutors.append(tutor)
        
        return tutors