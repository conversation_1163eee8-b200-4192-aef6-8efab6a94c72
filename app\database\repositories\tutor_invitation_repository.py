"""
Repository for tutor invitation database operations.
"""

from datetime import datetime, timedelta
from typing import Optional, List
import secrets

from asyncpg import Connection, Record

from app.database.repositories.base import BaseRepository
from app.models.tutor_invitation_models import (
    TutorInvitation,
    TutorInvitationCreate,
    TutorInvitationWithInviter
)
from app.core.exceptions import (
    ResourceNotFoundError,
    DatabaseOperationError,
    DuplicateResourceError
)
from app.core.logging import TutorAideLogger


logger = TutorAideLogger(__name__)


class TutorInvitationRepository(BaseRepository):
    """Repository for tutor invitation database operations."""
    
    def __init__(self):
        super().__init__(table_name="tutor_invitations", id_column="invitation_id")
    
    async def create_invitation(
        self,
        conn: Connection,
        data: TutorInvitationCreate,
        invited_by: int,
        expiry_days: int = 7
    ) -> TutorInvitation:
        """Create a new tutor invitation."""
        try:
            # Check if there's already a pending invitation for this email
            existing = await conn.fetchone(
                """
                SELECT invitation_id 
                FROM tutor_invitations 
                WHERE email = $1 AND status = 'pending' AND expires_at > NOW()
                """,
                data.email
            )
            
            if existing:
                raise DuplicateResourceError(
                    "A pending invitation already exists for this email"
                )
            
            # Generate secure token
            token = secrets.token_urlsafe(32)
            expires_at = datetime.utcnow() + timedelta(days=expiry_days)
            
            query = """
                INSERT INTO tutor_invitations (
                    email,
                    token,
                    invited_by,
                    first_name,
                    last_name,
                    phone,
                    message,
                    expires_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                RETURNING *
            """
            
            record = await conn.fetchrow(
                query,
                data.email,
                token,
                invited_by,
                data.first_name,
                data.last_name,
                data.phone,
                data.message,
                expires_at
            )
            
            return self._record_to_model(record)
            
        except DuplicateResourceError:
            raise
        except Exception as e:
            logger.error(f"Error creating tutor invitation: {str(e)}")
            raise DatabaseOperationError(f"Failed to create invitation: {str(e)}")
    
    async def get_by_token(
        self,
        conn: Connection,
        token: str
    ) -> Optional[TutorInvitation]:
        """Get invitation by token."""
        try:
            query = """
                SELECT * FROM tutor_invitations
                WHERE token = $1
            """
            
            record = await conn.fetchrow(query, token)
            return self._record_to_model(record) if record else None
            
        except Exception as e:
            logger.error(f"Error fetching invitation by token: {str(e)}")
            raise DatabaseOperationError(f"Failed to fetch invitation: {str(e)}")
    
    async def get_with_inviter(
        self,
        conn: Connection,
        invitation_id: int
    ) -> Optional[TutorInvitationWithInviter]:
        """Get invitation with inviter information."""
        try:
            query = """
                SELECT 
                    ti.*,
                    u.email as inviter_email,
                    u.first_name as inviter_first_name,
                    u.last_name as inviter_last_name
                FROM tutor_invitations ti
                JOIN user_accounts u ON ti.invited_by = u.user_id
                WHERE ti.invitation_id = $1
            """
            
            record = await conn.fetchrow(query, invitation_id)
            if not record:
                return None
            
            return TutorInvitationWithInviter(**dict(record))
            
        except Exception as e:
            logger.error(f"Error fetching invitation with inviter: {str(e)}")
            raise DatabaseOperationError(f"Failed to fetch invitation: {str(e)}")
    
    async def get_pending_invitations(
        self,
        conn: Connection,
        invited_by: Optional[int] = None,
        limit: int = 50
    ) -> List[TutorInvitation]:
        """Get pending invitations, optionally filtered by inviter."""
        try:
            if invited_by:
                query = """
                    SELECT * FROM tutor_invitations
                    WHERE status = 'pending' 
                        AND expires_at > NOW()
                        AND invited_by = $1
                    ORDER BY created_at DESC
                    LIMIT $2
                """
                records = await conn.fetch(query, invited_by, limit)
            else:
                query = """
                    SELECT * FROM tutor_invitations
                    WHERE status = 'pending' AND expires_at > NOW()
                    ORDER BY created_at DESC
                    LIMIT $1
                """
                records = await conn.fetch(query, limit)
            
            return [self._record_to_model(record) for record in records]
            
        except Exception as e:
            logger.error(f"Error fetching pending invitations: {str(e)}")
            raise DatabaseOperationError(f"Failed to fetch invitations: {str(e)}")
    
    async def accept_invitation(
        self,
        conn: Connection,
        token: str,
        user_id: int
    ) -> Optional[TutorInvitation]:
        """Mark invitation as accepted."""
        try:
            query = """
                UPDATE tutor_invitations
                SET status = 'accepted',
                    accepted_at = NOW(),
                    accepted_user_id = $2,
                    updated_at = NOW()
                WHERE token = $1 
                    AND status = 'pending'
                    AND expires_at > NOW()
                RETURNING *
            """
            
            record = await conn.fetchrow(query, token, user_id)
            return self._record_to_model(record) if record else None
            
        except Exception as e:
            logger.error(f"Error accepting invitation: {str(e)}")
            raise DatabaseOperationError(f"Failed to accept invitation: {str(e)}")
    
    async def expire_old_invitations(self, conn: Connection) -> int:
        """Mark expired invitations as expired."""
        try:
            query = """
                UPDATE tutor_invitations
                SET status = 'expired',
                    updated_at = NOW()
                WHERE status = 'pending' 
                    AND expires_at <= NOW()
            """
            
            result = await conn.execute(query)
            count = int(result.split()[-1]) if result else 0
            
            if count > 0:
                logger.info(f"Expired {count} old invitations")
            
            return count
            
        except Exception as e:
            logger.error(f"Error expiring invitations: {str(e)}")
            raise DatabaseOperationError(f"Failed to expire invitations: {str(e)}")
    
    async def search_invitations(
        self,
        conn: Connection,
        query: str,
        limit: int = 20
    ) -> List[TutorInvitationWithInviter]:
        """Search invitations by email, name, or inviter."""
        try:
            search_query = """
                SELECT 
                    ti.*,
                    u.email as inviter_email,
                    u.first_name as inviter_first_name,
                    u.last_name as inviter_last_name
                FROM tutor_invitations ti
                JOIN user_accounts u ON ti.invited_by = u.user_id
                WHERE (
                    ti.email ILIKE $1
                    OR ti.first_name ILIKE $1
                    OR ti.last_name ILIKE $1
                    OR u.email ILIKE $1
                    OR u.first_name ILIKE $1
                    OR u.last_name ILIKE $1
                )
                ORDER BY ti.created_at DESC
                LIMIT $2
            """
            
            search_pattern = f"%{query}%"
            records = await conn.fetch(search_query, search_pattern, limit)
            
            return [
                TutorInvitationWithInviter(**dict(record))
                for record in records
            ]
            
        except Exception as e:
            logger.error(f"Error searching invitations: {str(e)}")
            raise DatabaseOperationError(f"Failed to search invitations: {str(e)}")
    
    def _record_to_model(self, record: Optional[Record]) -> Optional[TutorInvitation]:
        """Convert database record to model."""
        if not record:
            return None
        
        return TutorInvitation(**dict(record))