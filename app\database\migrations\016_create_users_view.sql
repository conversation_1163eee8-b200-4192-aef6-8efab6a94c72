-- Migration 016: Create users view for backward compatibility
-- Creates a users view that maps to user_accounts table

-- Create users view as an alias for user_accounts
CREATE OR REPLACE VIEW users AS
SELECT 
    user_id,
    email,
    password_hash,
    google_id,
    created_at,
    updated_at,
    deleted_at
FROM user_accounts;

-- Create rules to make the view updatable
CREATE OR REPLACE RULE users_insert AS
    ON INSERT TO users
    DO INSTEAD
    INSERT INTO user_accounts (email, password_hash, google_id, created_at, updated_at, deleted_at)
    VALUES (NEW.email, NEW.password_hash, NEW.google_id, NEW.created_at, NEW.updated_at, NEW.deleted_at);

CREATE OR REPLACE RULE users_update AS
    ON UPDATE TO users
    DO INSTEAD
    UPDATE user_accounts
    SET email = NEW.email,
        password_hash = NEW.password_hash,
        google_id = NEW.google_id,
        created_at = NEW.created_at,
        updated_at = NEW.updated_at,
        deleted_at = NEW.deleted_at
    WHERE user_id = OLD.user_id;

CREATE OR REPLACE RULE users_delete AS
    ON DELETE TO users
    DO INSTEAD
    DELETE FROM user_accounts
    WHERE user_id = OLD.user_id;

-- Add comment explaining the purpose
COMMENT ON VIEW users IS 'Backward compatibility view for user_accounts table';