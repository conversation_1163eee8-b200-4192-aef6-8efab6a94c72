import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { CheckCircle, XCircle, Info, Shield, Mail, BarChart, Settings } from 'lucide-react';
import { Modal } from '../common/Modal';
import Button from '../common/Button';
import { Card } from '../common/Card';
import { Badge } from '../common/Badge';
import LoadingSpinner from '../ui/LoadingSpinner';
import api from '../../services/api';
import toast from 'react-hot-toast';

interface ConsentDocument {
  consent_type: string;
  title: string;
  content: string;
  version: string;
  level: 'level_1_mandatory' | 'level_2_optional';
  category: 'legal' | 'marketing' | 'analytics' | 'functional';
  language: string;
  is_active: boolean;
  effective_date: string;
}

interface ConsentStatus {
  consent_type: string;
  title: string;
  level: string;
  category: string;
  status: 'granted' | 'withdrawn' | 'expired';
  granted_at?: string;
  withdrawn_at?: string;
  expires_at?: string;
  document_version: string;
  can_withdraw: boolean;
}

interface ConsentModalProps {
  isOpen: boolean;
  onClose: () => void;
  requiredConsents?: string[];
  onConsentComplete?: () => void;
  userRole?: 'client' | 'tutor' | 'manager';
  allowClose?: boolean;
}

export const ConsentModal: React.FC<ConsentModalProps> = ({
  isOpen,
  onClose,
  requiredConsents,
  onConsentComplete,
  userRole = 'client',
  allowClose = true
}) => {
  const { t, i18n } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [documents, setDocuments] = useState<ConsentDocument[]>([]);
  const [currentConsents, setCurrentConsents] = useState<ConsentStatus[]>([]);
  const [selectedConsents, setSelectedConsents] = useState<Set<string>>(new Set());
  const [activeDocument, setActiveDocument] = useState<ConsentDocument | null>(null);
  const [currentStep, setCurrentStep] = useState(0);

  useEffect(() => {
    if (isOpen) {
      fetchConsentData();
    }
  }, [isOpen, i18n.language]);

  const fetchConsentData = async () => {
    try {
      setLoading(true);
      
      // Fetch available consent documents
      const docsResponse = await api.get<ConsentDocument[]>(
        `/consent/documents?language=${i18n.language}`
      );
      
      // Fetch user's current consent status
      const statusResponse = await api.get<{ consents: ConsentStatus[] }>(
        `/consent/summary`
      );

      // Filter documents based on required consents or user role
      let filteredDocs = docsResponse.data;
      if (requiredConsents && requiredConsents.length > 0) {
        filteredDocs = filteredDocs.filter(doc => 
          requiredConsents.includes(doc.consent_type)
        );
      } else if (userRole === 'tutor') {
        // For tutors, include employment agreement
        filteredDocs = filteredDocs.filter(doc => 
          ['terms_of_service', 'privacy_policy', 'tutor_employment_agreement'].includes(doc.consent_type)
        );
      }

      setDocuments(filteredDocs);
      setCurrentConsents(statusResponse.data.consents);
      
      // Pre-select mandatory consents
      const mandatoryConsents = filteredDocs
        .filter(doc => doc.level === 'level_1_mandatory')
        .map(doc => doc.consent_type);
      setSelectedConsents(new Set(mandatoryConsents));
      
      // Set first document as active
      if (filteredDocs.length > 0) {
        setActiveDocument(filteredDocs[0]);
      }
    } catch (error) {
      console.error('Error fetching consent data:', error);
      toast.error(t('consent.errors.fetchFailed'));
    } finally {
      setLoading(false);
    }
  };

  const handleConsentToggle = (consentType: string, isMandatory: boolean) => {
    if (isMandatory) return; // Can't uncheck mandatory consents
    
    const newSelected = new Set(selectedConsents);
    if (newSelected.has(consentType)) {
      newSelected.delete(consentType);
    } else {
      newSelected.add(consentType);
    }
    setSelectedConsents(newSelected);
  };

  const handleSubmit = async () => {
    try {
      setSubmitting(true);
      
      const consentsToAccept = Array.from(selectedConsents).map(consentType => ({
        consent_type: consentType,
        language: i18n.language
      }));

      await api.post('/consent/bulk-accept', {
        consents: consentsToAccept
      });

      toast.success(t('consent.success.accepted'));
      
      if (onConsentComplete) {
        onConsentComplete();
      }
      onClose();
    } catch (error) {
      console.error('Error accepting consents:', error);
      toast.error(t('consent.errors.acceptFailed'));
    } finally {
      setSubmitting(false);
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'legal':
        return <Shield className="w-5 h-5" />;
      case 'marketing':
        return <Mail className="w-5 h-5" />;
      case 'analytics':
        return <BarChart className="w-5 h-5" />;
      case 'functional':
        return <Settings className="w-5 h-5" />;
      default:
        return <Info className="w-5 h-5" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'legal':
        return 'primary';
      case 'marketing':
        return 'info';
      case 'analytics':
        return 'warning';
      case 'functional':
        return 'secondary';
      default:
        return 'secondary';
    }
  };

  const isConsentGranted = (consentType: string) => {
    const consent = currentConsents.find(c => c.consent_type === consentType);
    return consent?.status === 'granted';
  };

  if (loading) {
    return (
      <Modal 
        isOpen={isOpen} 
        onClose={onClose} 
        title={t('consent.title')} 
        size="lg"
        closeOnBackdrop={allowClose}
        closeOnEscape={allowClose}
        showCloseButton={allowClose}
      >
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      </Modal>
    );
  }

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose} 
      title={t('consent.title')}
      size="xl"
      showCloseButton={allowClose}
      closeOnBackdrop={allowClose}
      closeOnEscape={allowClose}
    >
      <div className="flex h-[600px]">
        {/* Left sidebar with consent list */}
        <div className="w-1/3 border-r border-gray-200 pr-6">
          <h3 className="text-sm font-medium text-gray-900 mb-4">
            {t('consent.requiredAgreements')}
          </h3>
          
          <div className="space-y-3">
            {documents.map((doc, index) => {
              const isMandatory = doc.level === 'level_1_mandatory';
              const isSelected = selectedConsents.has(doc.consent_type);
              const isGranted = isConsentGranted(doc.consent_type);
              
              return (
                <Card
                  key={doc.consent_type}
                  className={`p-4 cursor-pointer transition-all ${
                    activeDocument?.consent_type === doc.consent_type
                      ? 'ring-2 ring-primary-500 bg-primary-50'
                      : 'hover:bg-gray-50'
                  }`}
                  onClick={() => {
                    setActiveDocument(doc);
                    setCurrentStep(index);
                  }}
                >
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 mt-1">
                      {isGranted ? (
                        <CheckCircle className="w-5 h-5 text-green-500" />
                      ) : (
                        <input
                          type="checkbox"
                          checked={isSelected}
                          onChange={(e) => {
                            e.stopPropagation();
                            handleConsentToggle(doc.consent_type, isMandatory);
                          }}
                          disabled={isMandatory}
                          className={`h-4 w-4 text-primary-600 rounded border-gray-300 
                            ${isMandatory ? 'cursor-not-allowed' : 'cursor-pointer'}
                            focus:ring-primary-500`}
                        />
                      )}
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        {getCategoryIcon(doc.category)}
                        <h4 className="text-sm font-medium text-gray-900">
                          {doc.title}
                        </h4>
                      </div>
                      
                      <div className="flex items-center gap-2 mt-2">
                        <Badge 
                          variant={getCategoryColor(doc.category)}
                          size="sm"
                        >
                          {t(`consent.categories.${doc.category}`)}
                        </Badge>
                        
                        {isMandatory && (
                          <Badge variant="error" size="sm">
                            {t('consent.mandatory')}
                          </Badge>
                        )}
                        
                        {isGranted && (
                          <Badge variant="success" size="sm">
                            {t('consent.alreadyAccepted')}
                          </Badge>
                        )}
                      </div>
                      
                      <p className="text-xs text-gray-500 mt-1">
                        {t('consent.version')}: {doc.version}
                      </p>
                    </div>
                  </div>
                </Card>
              );
            })}
          </div>
          
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <div className="flex items-start">
              <Info className="w-5 h-5 text-blue-400 mt-0.5" />
              <p className="ml-3 text-sm text-blue-700">
                {t('consent.info.mandatory')}
              </p>
            </div>
          </div>
        </div>

        {/* Right content area */}
        <div className="flex-1 pl-6 flex flex-col">
          {activeDocument && (
            <>
              <div className="flex-1 overflow-y-auto">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  {activeDocument.title}
                </h2>
                
                <div className="prose prose-sm max-w-none">
                  <div className="whitespace-pre-wrap text-gray-700 leading-relaxed">
                    {activeDocument.content}
                  </div>
                </div>
                
                <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                  <p className="text-sm text-gray-600">
                    <strong>{t('consent.effectiveDate')}:</strong>{' '}
                    {new Date(activeDocument.effective_date).toLocaleDateString()}
                  </p>
                </div>
              </div>
              
              {/* Action buttons */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <div className="flex justify-between items-center">
                  <div className="text-sm text-gray-500">
                    {t('consent.step')} {currentStep + 1} {t('common.of')} {documents.length}
                  </div>
                  
                  <div className="flex gap-3">
                    {currentStep > 0 && (
                      <Button
                        variant="secondary"
                        onClick={() => {
                          setCurrentStep(currentStep - 1);
                          setActiveDocument(documents[currentStep - 1]);
                        }}
                      >
                        {t('common.previous')}
                      </Button>
                    )}
                    
                    {currentStep < documents.length - 1 ? (
                      <Button
                        variant="primary"
                        onClick={() => {
                          setCurrentStep(currentStep + 1);
                          setActiveDocument(documents[currentStep + 1]);
                        }}
                      >
                        {t('common.next')}
                      </Button>
                    ) : (
                      <Button
                        variant="primary"
                        onClick={handleSubmit}
                        disabled={submitting || selectedConsents.size === 0}
                        leftIcon={submitting ? <LoadingSpinner size="sm" /> : <CheckCircle />}
                      >
                        {submitting ? t('consent.accepting') : t('consent.acceptSelected')}
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default ConsentModal;