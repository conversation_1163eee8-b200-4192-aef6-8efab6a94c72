"""
Service for managing tutor invitations.
"""

from typing import List, Optional
import asyncpg

from app.database.repositories.tutor_invitation_repository import TutorInvitationRepository
from app.database.repositories.user_repository import UserRepository
from app.models.tutor_invitation_models import (
    TutorInvitation,
    TutorInvitationCreate,
    TutorInvitationWithInviter,
    TutorInvitationAccept
)
from app.models.auth_models import UserRole
from app.models.user_models import UserCreate
from app.services.auth_service import AuthService
from app.services.email_service import EmailService
from app.core.exceptions import (
    ResourceNotFoundError,
    ForbiddenError,
    ValidationError,
    DuplicateResourceError,
    BusinessLogicError
)
from app.core.logging import TutorAideLogger
from app.config.database import DatabaseManager


logger = TutorAideLogger(__name__)


class TutorInvitationService:
    """Service for managing tutor invitations."""
    
    def __init__(self, db_manager: DatabaseManager):
        """Initialize the service."""
        self.db_manager = db_manager
        self.invitation_repo = TutorInvitationRepository()
        self.user_repo = UserRepository()
        self.auth_service = AuthService(db_manager)
        self.email_service = EmailService()
    
    async def create_invitation(
        self,
        invitation_data: TutorInvitationCreate,
        current_user_id: int,
        current_user_role: UserRole
    ) -> TutorInvitation:
        """Create and send a tutor invitation (managers only)."""
        # Only managers can send invitations
        if current_user_role != UserRole.MANAGER:
            raise ForbiddenError("Only managers can send tutor invitations")
        
        async with self.db_manager.acquire() as conn:
            # Check if user already exists with this email
            existing_user = await self.user_repo.find_by_email(conn, invitation_data.email)
            if existing_user:
                raise BusinessLogicError(
                    "A user with this email already exists. "
                    "Please add tutor role to existing user instead."
                )
            
            # Create invitation
            invitation = await self.invitation_repo.create_invitation(
                conn, invitation_data, current_user_id
            )
            
            # Get inviter information for email
            inviter = await self.user_repo.get(conn, current_user_id)
            
            # Send invitation email
            await self._send_invitation_email(invitation, inviter)
            
            logger.info(
                f"Tutor invitation sent to {invitation.email} by manager {current_user_id}"
            )
            
            return invitation
    
    async def get_invitation(
        self,
        invitation_id: int,
        current_user_id: int,
        current_user_role: UserRole
    ) -> TutorInvitationWithInviter:
        """Get invitation details (managers only)."""
        if current_user_role != UserRole.MANAGER:
            raise ForbiddenError("Only managers can view invitations")
        
        async with self.db_manager.acquire() as conn:
            invitation = await self.invitation_repo.get_with_inviter(conn, invitation_id)
            if not invitation:
                raise ResourceNotFoundError("Invitation not found")
            
            return invitation
    
    async def get_invitation_by_token(
        self,
        token: str
    ) -> TutorInvitation:
        """Get invitation by token (public endpoint for invitation acceptance)."""
        async with self.db_manager.acquire() as conn:
            invitation = await self.invitation_repo.get_by_token(conn, token)
            if not invitation:
                raise ResourceNotFoundError("Invalid invitation token")
            
            # Check if invitation is still valid
            if invitation.status != "pending":
                raise BusinessLogicError(f"Invitation has already been {invitation.status}")
            
            if invitation.expires_at < datetime.utcnow():
                # Mark as expired
                await self.invitation_repo.expire_old_invitations(conn)
                raise BusinessLogicError("Invitation has expired")
            
            return invitation
    
    async def list_invitations(
        self,
        current_user_id: int,
        current_user_role: UserRole,
        only_mine: bool = False
    ) -> List[TutorInvitation]:
        """List pending invitations."""
        if current_user_role != UserRole.MANAGER:
            raise ForbiddenError("Only managers can view invitations")
        
        async with self.db_manager.acquire() as conn:
            # Expire old invitations first
            await self.invitation_repo.expire_old_invitations(conn)
            
            # Get invitations
            invited_by = current_user_id if only_mine else None
            return await self.invitation_repo.get_pending_invitations(
                conn, invited_by
            )
    
    async def search_invitations(
        self,
        query: str,
        current_user_role: UserRole,
        limit: int = 20
    ) -> List[TutorInvitationWithInviter]:
        """Search invitations."""
        if current_user_role != UserRole.MANAGER:
            raise ForbiddenError("Only managers can search invitations")
        
        async with self.db_manager.acquire() as conn:
            return await self.invitation_repo.search_invitations(
                conn, query, limit
            )
    
    async def accept_invitation(
        self,
        accept_data: TutorInvitationAccept
    ) -> dict:
        """Accept an invitation and create tutor account."""
        async with self.db_manager.acquire() as conn:
            # Get invitation
            invitation = await self.invitation_repo.get_by_token(conn, accept_data.token)
            if not invitation:
                raise ResourceNotFoundError("Invalid invitation token")
            
            # Validate invitation
            if invitation.status != "pending":
                raise BusinessLogicError(f"Invitation has already been {invitation.status}")
            
            if invitation.expires_at < datetime.utcnow():
                raise BusinessLogicError("Invitation has expired")
            
            # Check if user already exists
            existing_user = await self.user_repo.find_by_email(conn, invitation.email)
            if existing_user:
                raise BusinessLogicError("User already exists with this email")
            
            # Create user account with tutor role
            user_data = UserCreate(
                email=invitation.email,
                password=accept_data.password,
                first_name=invitation.first_name or "",
                last_name=invitation.last_name or "",
                roles=[UserRole.TUTOR]
            )
            
            # Create user through auth service
            user = await self.auth_service.register_user(user_data)
            
            # Mark invitation as accepted
            await self.invitation_repo.accept_invitation(
                conn, accept_data.token, user.user_id
            )
            
            # Create tutor profile
            from app.services.tutor_service import TutorService
            tutor_service = TutorService(self.db_manager)
            
            # Base profile data
            profile_data = {
                "first_name": invitation.first_name or "",
                "last_name": invitation.last_name or "",
                "phone": invitation.phone
            }
            
            # If this was an application (not just invitation), populate all fields
            if hasattr(invitation, 'application_data') and invitation.application_data:
                app_data = invitation.application_data
                
                # Add personal info
                if 'personal' in app_data:
                    personal = app_data['personal']
                    profile_data.update({
                        "date_of_birth": personal.get('date_of_birth'),
                        "address": personal.get('address'),
                        "apartment": personal.get('apartment'),
                        "city": personal.get('city'),
                        "postal_code": personal.get('postal_code'),
                        "province": personal.get('province')
                    })
                
                # Add education info
                if 'education' in app_data:
                    education = app_data['education']
                    profile_data.update({
                        "highest_degree_level": education.get('highest_degree_level') or education.get('current_level'),
                        "highest_degree_name": education.get('highest_degree_name') or education.get('program'),
                        "degree_major": education.get('degree_major') or education.get('program'),
                        "university": education.get('university') or education.get('institution'),
                        "graduation_year": education.get('graduation_year'),
                        "gpa": education.get('gpa'),
                        "certifications": education.get('certifications', [])
                    })
                
                # Add experience info
                if 'experience' in app_data:
                    experience = app_data['experience']
                    profile_data.update({
                        "years_teaching_total": experience.get('years_teaching_total', 0),
                        "years_teaching_online": experience.get('years_teaching_online', 0),
                        "years_tutoring": experience.get('years_tutoring', 0),
                        "current_occupation": experience.get('current_occupation'),
                        "previous_positions": experience.get('previous_positions', []),
                        "languages_spoken": experience.get('languages_spoken', {}),
                        "teaching_methodologies": experience.get('teaching_methodologies', []),
                        "special_needs_experience": experience.get('special_needs_experience', False),
                        "age_groups_comfortable": experience.get('age_groups_comfortable', []),
                        "bio": experience.get('motivation', ''),
                        "strengths": experience.get('strengths', '')
                    })
                
                # Add preferences as subject expertise
                if 'preferences' in app_data:
                    preferences = app_data['preferences']
                    # Convert subjects to subject expertise
                    subject_expertise = {}
                    for subject in preferences.get('subjects', []):
                        subject_expertise[subject] = 5  # Default to expert level
                    profile_data['subject_expertise_levels'] = subject_expertise
                    profile_data['preferred_service_areas'] = [preferences.get('preferred_location', '')]
                    profile_data['transport_method'] = preferences.get('transport_method')
                    profile_data['weekly_availability'] = preferences.get('availability', {})
                
                # Add references info
                if 'references' in app_data:
                    references = app_data['references']
                    profile_data['references_text'] = references.get('text', '')
                    # Note: Structured references will be stored separately for security
                
                # Add documents
                if 'documents' in app_data:
                    documents = app_data['documents']
                    profile_data['resume_url'] = documents.get('resume_url')
            
            tutor_profile = await tutor_service.create_profile(
                user_id=user.user_id,
                profile_data=profile_data,
                current_user_id=user.user_id,
                current_user_role=UserRole.TUTOR
            )
            
            # Process availability data and insert into tutor_availability table
            application_data = getattr(invitation, 'application_data', None)
            if application_data:
                await self._process_tutor_availability(
                    conn, tutor_profile.tutor_id, application_data
                )
            
            logger.info(
                f"Tutor invitation accepted: {invitation.email} -> user_id: {user.user_id}"
            )
            
            # Generate login token for immediate access
            from app.services.auth_service import create_access_token
            token_data = {
                "user_id": str(user.user_id),
                "email": user.email,
                "role": UserRole.TUTOR
            }
            access_token = create_access_token(token_data)
            
            return {
                "user": user,
                "access_token": access_token,
                "token_type": "bearer"
            }
    
    async def resend_invitation(
        self,
        invitation_id: int,
        current_user_id: int,
        current_user_role: UserRole
    ) -> TutorInvitation:
        """Resend an invitation email."""
        if current_user_role != UserRole.MANAGER:
            raise ForbiddenError("Only managers can resend invitations")
        
        async with self.db_manager.acquire() as conn:
            invitation = await self.invitation_repo.get(conn, invitation_id)
            if not invitation:
                raise ResourceNotFoundError("Invitation not found")
            
            if invitation.status != "pending":
                raise BusinessLogicError(f"Cannot resend {invitation.status} invitation")
            
            # Get inviter information
            inviter = await self.user_repo.get(conn, invitation.invited_by)
            
            # Resend email
            await self._send_invitation_email(invitation, inviter)
            
            logger.info(
                f"Invitation {invitation_id} resent by manager {current_user_id}"
            )
            
            return invitation
    
    async def cancel_invitation(
        self,
        invitation_id: int,
        current_user_id: int,
        current_user_role: UserRole
    ) -> None:
        """Cancel a pending invitation."""
        if current_user_role != UserRole.MANAGER:
            raise ForbiddenError("Only managers can cancel invitations")
        
        async with self.db_manager.acquire() as conn:
            invitation = await self.invitation_repo.get(conn, invitation_id)
            if not invitation:
                raise ResourceNotFoundError("Invitation not found")
            
            if invitation.status != "pending":
                raise BusinessLogicError(f"Cannot cancel {invitation.status} invitation")
            
            # Mark as expired
            await self.invitation_repo.update(
                conn, invitation_id, {"status": "expired"}
            )
            
            logger.info(
                f"Invitation {invitation_id} cancelled by manager {current_user_id}"
            )
    
    async def _send_invitation_email(
        self,
        invitation: TutorInvitation,
        inviter: dict
    ) -> None:
        """Send invitation email to tutor."""
        # Build invitation link
        from app.config.settings import get_settings
        settings = get_settings()
        
        invitation_link = f"{settings.frontend_url}/accept-invitation?token={invitation.token}"
        
        # Prepare email data
        email_data = {
            "to_email": invitation.email,
            "subject": "Invitation to join TutorAide as a Tutor",
            "template": "tutor_invitation",
            "context": {
                "first_name": invitation.first_name or "Tutor",
                "inviter_name": f"{inviter.get('first_name', '')} {inviter.get('last_name', '')}",
                "invitation_link": invitation_link,
                "message": invitation.message,
                "expires_in_days": 7
            }
        }
        
        await self.email_service.send_email(**email_data)
    
    async def _process_tutor_availability(
        self,
        conn: asyncpg.Connection,
        tutor_id: int,
        application_data: dict
    ) -> None:
        """Process application availability data and insert into tutor_availability table."""
        try:
            # Validate input data
            if not isinstance(application_data, dict):
                logger.warning(f"Invalid application_data type for tutor {tutor_id}: expected dict, got {type(application_data)}")
                return
                
            preferences = application_data.get('preferences', {})
            availability_data = preferences.get('availability', {})
            
            if not availability_data:
                logger.info(f"No availability data found for tutor {tutor_id}")
                return
                
            logger.info(f"Processing availability data for tutor {tutor_id}: {list(availability_data.keys())}")
            
            # Time ranges for different periods (EST timezone)
            time_periods = {
                'morning': ('08:00:00', '12:00:00'),
                'afternoon': ('12:00:00', '17:00:00'), 
                'evening': ('17:00:00', '21:00:00')
            }
            
            # Day mapping (form uses full day names, DB uses 0-6)
            day_mapping = {
                'monday': 1,     # Monday = 1
                'tuesday': 2,    # Tuesday = 2
                'wednesday': 3,  # Wednesday = 3
                'thursday': 4,   # Thursday = 4
                'friday': 5,     # Friday = 5
                'saturday': 6,   # Saturday = 6
                'sunday': 0      # Sunday = 0
            }
            
            # Insert availability records
            insert_query = """
                INSERT INTO tutor_availability 
                (tutor_id, day_of_week, start_time, end_time, is_active)
                VALUES ($1, $2, $3, $4, $5)
                ON CONFLICT (tutor_id, day_of_week, start_time) 
                DO UPDATE SET 
                    end_time = EXCLUDED.end_time,
                    is_active = EXCLUDED.is_active,
                    updated_at = CURRENT_TIMESTAMP
            """
            
            availability_records = []
            
            for day_name, periods in availability_data.items():
                if day_name not in day_mapping:
                    continue
                    
                day_of_week = day_mapping[day_name]
                
                for period, is_available in periods.items():
                    if period not in time_periods or not is_available:
                        continue
                        
                    start_time, end_time = time_periods[period]
                    availability_records.append((
                        tutor_id, day_of_week, start_time, end_time, True
                    ))
            
            # Insert all availability records
            if availability_records:
                await conn.executemany(insert_query, availability_records)
                logger.info(
                    f"Successfully inserted {len(availability_records)} availability records for tutor {tutor_id}"
                )
                
                # Log the specific periods for debugging
                periods_summary = {}
                for record in availability_records:
                    day_names = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
                    day_name = day_names[record[1]]
                    if day_name not in periods_summary:
                        periods_summary[day_name] = []
                    period_map = {'08:00:00': 'morning', '12:00:00': 'afternoon', '17:00:00': 'evening'}
                    period_name = period_map.get(record[2], record[2])
                    periods_summary[day_name].append(period_name)
                
                logger.info(f"Tutor {tutor_id} availability summary: {periods_summary}")
            else:
                logger.warning(f"No valid availability periods found for tutor {tutor_id}. Input data: {availability_data}")
                
        except asyncpg.UniqueViolationError as e:
            logger.warning(f"Some availability records already exist for tutor {tutor_id}: {e}")
        except Exception as e:
            logger.error(f"Error processing tutor availability for tutor {tutor_id}: {e}")
            # Don't raise the exception as availability processing shouldn't block account creation


# Import datetime at the top of the file
from datetime import datetime
import asyncpg