-- =====================================================
-- Migration: Enhance Tutor Profiles with Education & Experience
-- Version: 031
-- Description: Add comprehensive education, experience, and verification fields to tutor_profiles
-- Date: 2024-01-24
-- =====================================================

-- Add education fields to tutor_profiles
ALTER TABLE tutor_profiles
ADD COLUMN IF NOT EXISTS highest_degree_level VARCHAR(20) 
    CHECK (highest_degree_level IN ('high_school', 'associate', 'bachelor', 'master', 'phd', 'professional', 'other')),
ADD COLUMN IF NOT EXISTS highest_degree_name VARCHAR(100),
ADD COLUMN IF NOT EXISTS degree_major VARCHAR(100),
ADD COLUMN IF NOT EXISTS university_name VARCHAR(200),
ADD COLUMN IF NOT EXISTS graduation_year INTEGER CHECK (graduation_year >= 1950 AND graduation_year <= EXTRACT(YEAR FROM CURRENT_DATE)),
ADD COLUMN IF NOT EXISTS gpa DECIMAL(3,2) CHECK (gpa >= 0 AND gpa <= 4.0),
ADD COLUMN IF NOT EXISTS additional_education JSONB DEFAULT '[]'::jsonb,
ADD COLUMN IF NOT EXISTS teaching_certifications TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS certification_details JSONB DEFAULT '{}'::jsonb;

-- Add experience fields
ALTER TABLE tutor_profiles
ADD COLUMN IF NOT EXISTS years_of_experience INTEGER DEFAULT 0 CHECK (years_of_experience >= 0),
ADD COLUMN IF NOT EXISTS years_online_teaching INTEGER DEFAULT 0 CHECK (years_online_teaching >= 0),
ADD COLUMN IF NOT EXISTS years_tutoring INTEGER DEFAULT 0 CHECK (years_tutoring >= 0),
ADD COLUMN IF NOT EXISTS students_taught_total INTEGER DEFAULT 0 CHECK (students_taught_total >= 0),
ADD COLUMN IF NOT EXISTS current_occupation VARCHAR(200),
ADD COLUMN IF NOT EXISTS previous_teaching_positions JSONB DEFAULT '[]'::jsonb,
ADD COLUMN IF NOT EXISTS subject_expertise JSONB DEFAULT '{}'::jsonb,
ADD COLUMN IF NOT EXISTS success_stories TEXT;

-- Add professional background fields
ALTER TABLE tutor_profiles
ADD COLUMN IF NOT EXISTS teaching_languages TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS language_proficiency JSONB DEFAULT '{}'::jsonb,
ADD COLUMN IF NOT EXISTS teaching_methodology TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS special_needs_experience BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS age_groups_experience TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS last_training_date DATE,
ADD COLUMN IF NOT EXISTS professional_memberships TEXT[] DEFAULT '{}';

-- Add portfolio and references fields
ALTER TABLE tutor_profiles
ADD COLUMN IF NOT EXISTS portfolio_url VARCHAR(500),
ADD COLUMN IF NOT EXISTS sample_lesson_urls TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS achievement_highlights TEXT,
ADD COLUMN IF NOT EXISTS has_references BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS reference_check_completed BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS reference_summary TEXT;

-- Add background verification fields
ALTER TABLE tutor_profiles
ADD COLUMN IF NOT EXISTS background_check_date DATE,
ADD COLUMN IF NOT EXISTS background_check_provider VARCHAR(100),
ADD COLUMN IF NOT EXISTS background_check_status VARCHAR(50) 
    CHECK (background_check_status IN ('not_started', 'pending', 'passed', 'requires_review', 'failed')),
ADD COLUMN IF NOT EXISTS working_with_children_clearance BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS police_check_expiry DATE,
ADD COLUMN IF NOT EXISTS has_liability_insurance BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS insurance_provider VARCHAR(100),
ADD COLUMN IF NOT EXISTS insurance_expiry DATE,
ADD COLUMN IF NOT EXISTS work_permit_status VARCHAR(50) 
    CHECK (work_permit_status IN ('citizen', 'permanent_resident', 'work_permit', 'student_visa', 'other')),
ADD COLUMN IF NOT EXISTS work_permit_expiry DATE;

-- Create secure table for tutor references
CREATE TABLE IF NOT EXISTS tutor_references (
    reference_id SERIAL PRIMARY KEY,
    tutor_id INTEGER NOT NULL REFERENCES tutor_profiles(tutor_id) ON DELETE CASCADE,
    reference_name VARCHAR(100) NOT NULL,
    reference_title VARCHAR(100),
    reference_organization VARCHAR(200),
    reference_email VARCHAR(255),
    reference_phone VARCHAR(20),
    relationship VARCHAR(100) NOT NULL, -- 'supervisor', 'colleague', 'client', 'other'
    years_known INTEGER CHECK (years_known > 0),
    verified_date DATE,
    verified_by INTEGER REFERENCES user_accounts(user_id),
    verification_status VARCHAR(50) DEFAULT 'pending' 
        CHECK (verification_status IN ('pending', 'contacted', 'verified', 'unable_to_verify')),
    verification_notes TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_tutor_degree_level ON tutor_profiles(highest_degree_level);
CREATE INDEX IF NOT EXISTS idx_tutor_years_experience ON tutor_profiles(years_of_experience);
CREATE INDEX IF NOT EXISTS idx_tutor_verification_status ON tutor_profiles(verification_status);
CREATE INDEX IF NOT EXISTS idx_tutor_background_check ON tutor_profiles(background_check_status);
CREATE INDEX IF NOT EXISTS idx_tutor_teaching_languages ON tutor_profiles USING GIN(teaching_languages);
CREATE INDEX IF NOT EXISTS idx_tutor_certifications ON tutor_profiles USING GIN(teaching_certifications);
CREATE INDEX IF NOT EXISTS idx_tutor_references_tutor ON tutor_references(tutor_id);

-- Add trigger to update updated_at timestamp for tutor_references
CREATE TRIGGER update_tutor_references_updated_at 
    BEFORE UPDATE ON tutor_references 
    FOR EACH ROW 
    EXECUTE PROCEDURE update_updated_at_column();

-- Add comments for documentation
COMMENT ON COLUMN tutor_profiles.highest_degree_level IS 'Standardized degree level for filtering';
COMMENT ON COLUMN tutor_profiles.highest_degree_name IS 'Full degree name as written on diploma';
COMMENT ON COLUMN tutor_profiles.additional_education IS 'Array of additional degrees/diplomas as JSONB';
COMMENT ON COLUMN tutor_profiles.certification_details IS 'Detailed certification info including expiry dates';
COMMENT ON COLUMN tutor_profiles.subject_expertise IS 'JSON mapping subjects to expertise levels';
COMMENT ON COLUMN tutor_profiles.language_proficiency IS 'JSON mapping languages to proficiency levels';
COMMENT ON COLUMN tutor_profiles.previous_teaching_positions IS 'Array of previous teaching positions as JSONB';
COMMENT ON TABLE tutor_references IS 'Secure storage for tutor reference contacts - manager access only';

-- Sample data structure comments
COMMENT ON COLUMN tutor_profiles.additional_education IS 
'Example: [{"degree": "Bachelor of Education", "major": "Secondary Education", "university": "Concordia University", "year": 2018}]';

COMMENT ON COLUMN tutor_profiles.subject_expertise IS 
'Example: {"mathematics": "expert", "physics": "advanced", "chemistry": "intermediate"}';

COMMENT ON COLUMN tutor_profiles.language_proficiency IS 
'Example: {"english": "native", "french": "fluent", "spanish": "conversational"}';

COMMENT ON COLUMN tutor_profiles.certification_details IS 
'Example: {"qc_teaching_license": {"issuer": "MELS Quebec", "issued_date": "2019-06-15", "expiry_date": "2024-06-15", "number": "QC12345"}}';

-- Grant appropriate permissions
GRANT SELECT ON tutor_references TO authenticated_user;
GRANT INSERT, UPDATE, DELETE ON tutor_references TO manager_role;
GRANT USAGE ON SEQUENCE tutor_references_reference_id_seq TO manager_role;