-- ================================================
-- Migration: Drop Old Authentication Tables
-- ================================================
-- This migration completes the consolidation to auth_tokens table
-- by dropping the old user_sessions table that was replaced
--
-- Version: 031
-- Date: 2025-06-28
-- ================================================

-- ============================================
-- Step 1: Verify Data Migration
-- ============================================

-- Check if there are any active sessions that haven't been migrated
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 
        FROM user_sessions 
        WHERE is_active = true 
        AND expires_at > CURRENT_TIMESTAMP
        AND NOT EXISTS (
            SELECT 1 
            FROM auth_tokens 
            WHERE auth_tokens.token_hash = user_sessions.token_hash
            AND auth_tokens.token_type = 'session'
        )
    ) THEN
        RAISE EXCEPTION 'Active sessions exist that have not been migrated to auth_tokens table';
    END IF;
END $$;

-- ============================================
-- Step 2: Drop Indexes
-- ============================================

-- Drop indexes on user_sessions table
DROP INDEX IF EXISTS idx_user_sessions_token_hash;
DROP INDEX IF EXISTS idx_user_sessions_user_id;
DROP INDEX IF EXISTS idx_user_sessions_expires_at;
DROP INDEX IF EXISTS idx_user_sessions_last_activity;

-- ============================================
-- Step 3: Drop Table
-- ============================================

-- Drop the user_sessions table
DROP TABLE IF EXISTS user_sessions CASCADE;

-- ============================================
-- Step 4: Update Comments
-- ============================================

COMMENT ON TABLE auth_tokens IS 'Unified authentication tokens table for all token types including sessions, password resets, email verification, and API keys';

-- ============================================
-- Step 5: Create Helper View (Optional)
-- ============================================

-- Create a view for easier session queries if needed
CREATE OR REPLACE VIEW active_sessions AS
SELECT 
    token_id,
    user_id,
    token_hash,
    expires_at,
    metadata->>'session_id' as session_id,
    metadata->>'role_type' as role_type,
    (metadata->>'last_activity')::timestamp with time zone as last_activity,
    ip_address,
    user_agent,
    created_at
FROM auth_tokens
WHERE token_type = 'session'
AND used_at IS NULL
AND expires_at > CURRENT_TIMESTAMP;

COMMENT ON VIEW active_sessions IS 'View of active user sessions from the unified auth_tokens table';

-- ============================================
-- Migration Complete
-- ============================================
-- The authentication system now uses a single auth_tokens table
-- for all token types, improving maintainability and consistency.