import React, { useState, useEffect } from 'react';

const TestServicesPage: React.FC = () => {
  const [status, setStatus] = useState('Loading...');
  const [services, setServices] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    testAPI();
  }, []);

  const testAPI = async () => {
    try {
      setStatus('Testing API connection...');
      
      // Test direct fetch to the API
      const response = await fetch('http://localhost:8000/api/v1/services/catalog', {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Content-Type': 'application/json'
        }
      });
      
      setStatus(`API Response: ${response.status}`);
      
      if (response.ok) {
        const data = await response.json();
        setServices(data);
        setStatus(`✅ Success! Found ${data.length} services`);
      } else {
        const errorText = await response.text();
        setError(`API Error ${response.status}: ${errorText}`);
        setStatus('❌ API Error');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(`Network Error: ${errorMessage}`);
      setStatus('❌ Network Error');
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">API Test Page</h1>
      
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Connection Status</h2>
        <p className="text-lg mb-4">{status}</p>
        
        {error && (
          <div className="bg-red-50 border border-red-200 rounded p-4 mb-4">
            <h3 className="font-semibold text-red-800">Error Details:</h3>
            <p className="text-red-600">{error}</p>
          </div>
        )}
        
        <button 
          onClick={testAPI}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Test Again
        </button>
      </div>

      {services.length > 0 && (
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Services Found</h2>
          <div className="space-y-4">
            {services.map((service, index) => (
              <div key={index} className="border border-gray-200 rounded p-4">
                <h3 className="font-semibold">
                  {service.service_name || service.name || 'Unknown Service'}
                </h3>
                <p className="text-gray-600">
                  Subject: {service.subject_area || 'Unknown'}
                </p>
                <p className="text-gray-600">
                  Type: {service.service_type || 'Unknown'}
                </p>
                <p className="text-gray-600 text-sm">
                  Description: {service.description || 'No description'}
                </p>
              </div>
            ))}
          </div>
        </div>
      )}
      
      <div className="mt-6 bg-gray-50 rounded p-4">
        <h3 className="font-semibold mb-2">Debug Info:</h3>
        <p>Frontend URL: {window.location.href}</p>
        <p>API Base URL: http://localhost:8000/api/v1</p>
        <p>Test Endpoint: /services/catalog</p>
      </div>
    </div>
  );
};

export default TestServicesPage;
