"""
Authentication API endpoints for JWT token management.
"""

from typing import Annotated, Optional, Dict, Any, List
from datetime import timed<PERSON><PERSON>
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, status, Response, Request, Query
from fastapi.responses import RedirectResponse
from fastapi.security import OAuth2PasswordRequestForm
import asyncpg
from urllib.parse import urlencode

from app.core.dependencies import get_database, get_current_active_user
from app.core.exceptions import ValidationError, AuthenticationError, AuthorizationError, BusinessLogicError, ResourceNotFoundError
from app.core.security import create_access_token, verify_token, create_refresh_token
from app.services.auth_service import AuthService, get_auth_service
from app.services.google_oauth_service import GoogleOAuthService
# Email verification is now handled by AuthTokenService
from app.services.email_service import get_email_service
from app.models.user_models import (
    User, UserCreate, UserResponse, AuthResponse, TokenResponse,
    UserRoleType, ConsentAcceptance, LoginRequest, RefreshTokenRequest
)
from app.models.auth_models import (
    PasswordResetRequest, 
    PasswordResetConfirm,
    EmailVerificationRequest,
    EmailVerificationConfirm,
    EmailVerificationResponse,
    EmailVerificationStatus
)
from app.models.auth_token_models import AuthTokenType
from app.models.consent_models import (
    ConsentAcceptanceRequest,
    ConsentWithdrawalRequest,
    ConsentStatusResponse,
    ConsentSummaryResponse,
    ConsentHistoryResponse,
    ConsentValidationResult,
    BulkConsentRequest,
    BulkConsentResponse,
    ConsentDocument
)
from app.services.consent_service import ConsentService
from app.core.logging import TutorAideLogger
from app.config.settings import settings

router = APIRouter(prefix="/auth", tags=["authentication"])
logger = TutorAideLogger.get_logger(__name__)


@router.post("/register", response_model=AuthResponse, status_code=status.HTTP_201_CREATED)
async def register(
    user_data: UserCreate,
    request: Request,
    response: Response,
    db: Annotated[asyncpg.Connection, Depends(get_database)],
    auth_service: Annotated[AuthService, Depends(get_auth_service)]
):
    """
    Register a new user account.
    
    - **email**: Valid email address
    - **password**: Strong password (min 8 chars, upper, lower, digit)
    - **google_id**: Optional Google OAuth ID
    - **roles**: List of initial roles (manager, tutor, client)
    
    Returns JWT access token and user info. Automatically records mandatory consents.
    """
    # Get client IP for rate limiting and consent tracking
    client_ip = request.client.host if request.client else '127.0.0.1'
    
    # Import rate limiting service
    from app.services.rate_limiting_service import rate_limiting_service
    from app.core.validation import RateLimitValidator
    
    try:
        # Check registration rate limits
        try:
            RateLimitValidator.check_rate_limit(
                identifier=client_ip,  # Use IP for registration rate limiting
                action='register',
                ip_address=client_ip
            )
            
            # Add rate limit headers to response
            response.headers["X-RateLimit-Limit"] = "3"  # 3 registrations per hour
            response.headers["X-RateLimit-Window"] = "3600"  # 1 hour window
            
        except ValidationError as e:
            # Rate limit exceeded
            logger.security(f"Registration rate limit exceeded from {client_ip}")
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Too many registration attempts. Please try again later.",
                headers={
                    "Retry-After": "3600",
                    "X-RateLimit-Limit": "3",
                    "X-RateLimit-Remaining": "0"
                }
            )
        
        # Register user with initial consents
        user, access_token = await auth_service.register_user(
            db, user_data, client_ip
        )
        
        # Create refresh token
        refresh_token = create_refresh_token(
            subject=str(user.user_id),
            user_roles=[role.value for role in user_data.roles]
        )
        
        # Get primary role for token expiry
        primary_role = user_data.roles[0] if user_data.roles else UserRoleType.CLIENT
        expires_in = auth_service._get_token_expiry_seconds(primary_role)
        
        # Create response
        user_response = UserResponse(
            user_id=user.user_id,
            email=user.email,
            is_email_verified=user.is_email_verified,
            has_local_auth=user.has_local_auth(),
            has_google_auth=user.has_google_auth(),
            roles=user_data.roles,
            created_at=user.created_at,
            updated_at=user.updated_at
        )
        
        auth_response = AuthResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer",
            expires_in=expires_in,
            user=user_response,
            requires_consents=[]  # Mandatory consents already recorded
        )
        
        logger.info(f"User registered successfully: {user.email}")
        return auth_response
        
    except ValidationError as e:
        # Record failed registration attempt if email validation failed
        if "email" in str(e).lower():
            RateLimitValidator.record_failed_attempt(
                identifier=client_ip,
                action='register',
                ip_address=client_ip
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        # Re-raise HTTP exceptions (like rate limit)
        raise
    except Exception as e:
        logger.error(f"Registration error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )


@router.post("/login", response_model=AuthResponse)
async def login(
    form_data: Annotated[OAuth2PasswordRequestForm, Depends()],
    request: Request,
    response: Response,
    db: Annotated[asyncpg.Connection, Depends(get_database)],
    auth_service: Annotated[AuthService, Depends(get_auth_service)]
):
    """
    Login with email and password.
    
    OAuth2 compatible login endpoint. Use form data with:
    - **username**: Email address
    - **password**: User password
    - **scope**: Optional role to login as (manager, tutor, client)
    
    Returns JWT access token and refresh token.
    """
    # Get client IP for rate limiting
    client_ip = request.client.host if request.client else '127.0.0.1'
    
    # Import rate limiting service
    from app.services.rate_limiting_service import rate_limiting_service
    
    try:
        # Check rate limits before authentication
        try:
            rate_limit_info = rate_limiting_service.check_login_rate_limit(
                email=form_data.username,
                ip_address=client_ip,
                user_agent=request.headers.get('User-Agent')
            )
            
            # Add rate limit headers to response
            response.headers["X-RateLimit-Limit"] = "5"
            response.headers["X-RateLimit-Remaining"] = str(rate_limit_info.attempts_remaining)
            response.headers["X-RateLimit-Reset"] = str(int(rate_limit_info.reset_time.timestamp()))
            
            if rate_limit_info.delay_seconds > 0:
                response.headers["Retry-After"] = str(rate_limit_info.delay_seconds)
                
        except ValidationError as e:
            # Rate limit exceeded
            logger.security(f"Login rate limit exceeded for {form_data.username} from {client_ip}")
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=str(e),
                headers={
                    "Retry-After": "60",
                    "X-RateLimit-Limit": "5",
                    "X-RateLimit-Remaining": "0"
                }
            )
        
        # Parse requested role from scope
        requested_role = None
        if form_data.scope:
            scopes = form_data.scope.split()
            if scopes:
                try:
                    requested_role = UserRoleType(scopes[0])
                except ValueError:
                    pass
        
        # Authenticate user
        try:
            auth_response = await auth_service.authenticate_user(
                db,
                email=form_data.username,  # OAuth2 uses 'username' field
                password=form_data.password,
                requested_role=requested_role
            )
            
            # Record successful login
            rate_limiting_service.record_login_success(
                email=form_data.username,
                ip_address=client_ip
            )
            
        except AuthenticationError as e:
            # Record failed login attempt
            rate_limiting_service.record_login_failure(
                email=form_data.username,
                ip_address=client_ip,
                failure_reason="invalid_credentials",
                user_agent=request.headers.get('User-Agent')
            )
            
            # Re-raise the authentication error
            raise
        
        # Create refresh token
        refresh_token = create_refresh_token(
            subject=str(auth_response.user.user_id),
            user_roles=[role.value for role in auth_response.user.roles]
        )
        
        # Add refresh token to response
        auth_response.refresh_token = refresh_token
        
        logger.info(f"User logged in: {form_data.username}")
        return auth_response
        
    except AuthenticationError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e),
            headers={"WWW-Authenticate": "Bearer"}
        )
    except HTTPException:
        # Re-raise HTTP exceptions (like rate limit)
        raise
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )


@router.post("/login/json", response_model=AuthResponse)
async def login_json(
    login_data: LoginRequest,
    request: Request,
    response: Response,
    db: Annotated[asyncpg.Connection, Depends(get_database)],
    auth_service: Annotated[AuthService, Depends(get_auth_service)]
):
    """
    Login with JSON body (alternative to OAuth2 form).
    
    - **email**: User email address
    - **password**: User password
    - **requested_role**: Optional specific role to login as
    
    Returns JWT access token and refresh token.
    """
    # Get client IP for rate limiting
    client_ip = request.client.host if request.client else '127.0.0.1'
    
    # Import rate limiting service
    from app.services.rate_limiting_service import rate_limiting_service
    
    try:
        # Check rate limits before authentication
        try:
            rate_limit_info = rate_limiting_service.check_login_rate_limit(
                email=login_data.email,
                ip_address=client_ip,
                user_agent=request.headers.get('User-Agent')
            )
            
            # Add rate limit headers to response
            response.headers["X-RateLimit-Limit"] = "5"
            response.headers["X-RateLimit-Remaining"] = str(rate_limit_info.attempts_remaining)
            response.headers["X-RateLimit-Reset"] = str(int(rate_limit_info.reset_time.timestamp()))
            
            if rate_limit_info.delay_seconds > 0:
                response.headers["Retry-After"] = str(rate_limit_info.delay_seconds)
                
        except ValidationError as e:
            # Rate limit exceeded
            logger.security(f"Login rate limit exceeded for {login_data.email} from {client_ip}")
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=str(e),
                headers={
                    "Retry-After": "60",
                    "X-RateLimit-Limit": "5",
                    "X-RateLimit-Remaining": "0"
                }
            )
        
        # Authenticate user
        try:
            auth_response = await auth_service.authenticate_user(
                db,
                email=login_data.email,
                password=login_data.password,
                requested_role=login_data.requested_role
            )
            
            # Record successful login
            rate_limiting_service.record_login_success(
                email=login_data.email,
                ip_address=client_ip
            )
            
        except AuthenticationError as e:
            # Record failed login attempt
            rate_limiting_service.record_login_failure(
                email=login_data.email,
                ip_address=client_ip,
                failure_reason="invalid_credentials",
                user_agent=request.headers.get('User-Agent')
            )
            
            # Re-raise the authentication error
            raise
        
        # Create refresh token
        refresh_token = create_refresh_token(
            subject=str(auth_response.user.user_id),
            user_roles=[role.value for role in auth_response.user.roles]
        )
        
        # Add refresh token to response
        auth_response.refresh_token = refresh_token
        
        logger.info(f"User logged in via JSON: {login_data.email}")
        return auth_response
        
    except AuthenticationError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )
    except HTTPException:
        # Re-raise HTTP exceptions (like rate limit)
        raise
    except Exception as e:
        logger.error(f"JSON login error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )


@router.post("/refresh", response_model=TokenResponse)
async def refresh_token(
    refresh_data: RefreshTokenRequest,
    db: Annotated[asyncpg.Connection, Depends(get_database)],
    auth_service: Annotated[AuthService, Depends(get_auth_service)]
):
    """
    Refresh access token using refresh token.
    
    - **refresh_token**: Valid refresh token
    
    Returns new access token with same roles and permissions.
    """
    try:
        # Verify refresh token
        payload = verify_token(refresh_data.refresh_token)
        user_id = int(payload.get("sub"))
        token_type = payload.get("type")
        
        if token_type != "refresh":
            raise AuthenticationError("Invalid token type")
        
        # Get user profile to ensure still active
        user_profile = await auth_service.get_user_profile(db, user_id)
        
        # Check if user is still active
        if user_profile.user.deleted_at:
            raise AuthenticationError("User account is inactive")
        
        # Get active roles
        active_roles = user_profile.get_active_roles()
        if not active_roles:
            raise AuthenticationError("User has no active roles")
        
        # Determine primary role from refresh token or use first active
        current_roles = payload.get("roles", [])
        primary_role = active_roles[0]
        if current_roles and current_roles[0] in [r.value for r in active_roles]:
            primary_role = UserRoleType(current_roles[0])
        
        # Create new access token
        access_token = auth_service._create_role_specific_token(
            user_id, active_roles, primary_role
        )
        
        expires_in = auth_service._get_token_expiry_seconds(primary_role)
        
        response = TokenResponse(
            access_token=access_token,
            token_type="bearer",
            expires_in=expires_in
        )
        
        logger.info(f"Token refreshed for user {user_id}")
        return response
        
    except AuthenticationError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Token refresh error: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )


@router.post("/logout", status_code=status.HTTP_204_NO_CONTENT)
async def logout(
    response: Response,
    current_user: Annotated[User, Depends(get_current_active_user)],
    auth_service: Annotated[AuthService, Depends(get_auth_service)]
):
    """
    Logout current user.
    
    Invalidates the current session. Always returns success for security.
    """
    try:
        # Get token from request context
        # In production, you'd extract the actual token from the Authorization header
        # For now, we'll just log the logout action
        logger.info(f"User logged out: {current_user.email}")
        
        # In a real implementation, you might want to:
        # 1. Add the token to a blacklist
        # 2. Clear any server-side session data
        # 3. Invalidate refresh tokens
        
        # Clear any cookies if used
        response.delete_cookie("access_token")
        response.delete_cookie("refresh_token")
        
        return None
        
    except Exception as e:
        logger.error(f"Logout error: {e}")
        # Always return success for security
        return None


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: Annotated[User, Depends(get_current_active_user)],
    db: Annotated[asyncpg.Connection, Depends(get_database)],
    auth_service: Annotated[AuthService, Depends(get_auth_service)]
):
    """
    Get current user information.
    
    Returns the authenticated user's profile information.
    """
    try:
        # Get full user profile
        user_profile = await auth_service.get_user_profile(db, current_user.user_id)
        
        # Get active roles
        active_roles = user_profile.get_active_roles()
        
        # Get current active role from token or default to first available role
        current_active_role = current_user.roles[0] if current_user.roles else (active_roles[0] if active_roles else UserRoleType.CLIENT)
        
        response = UserResponse(
            user_id=user_profile.user.user_id,
            email=user_profile.user.email,
            is_email_verified=user_profile.user.is_email_verified,
            has_local_auth=user_profile.user.has_local_auth(),
            has_google_auth=user_profile.user.has_google_auth(),
            roles=active_roles,
            active_role=current_active_role,
            created_at=user_profile.user.created_at,
            updated_at=user_profile.user.updated_at
        )
        
        return response
        
    except Exception as e:
        logger.error(f"Error getting user info: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user information"
        )


@router.post("/switch-role/{role}", response_model=AuthResponse)
async def switch_role(
    role: UserRoleType,
    current_user: Annotated[User, Depends(get_current_active_user)],
    request: Request,
    db: Annotated[asyncpg.Connection, Depends(get_database)],
    auth_service: Annotated[AuthService, Depends(get_auth_service)]
):
    """
    Switch to a different role.
    
    - **role**: Target role to switch to (must be an active role for the user)
    
    Returns new JWT tokens for the specified role.
    """
    try:
        # Get current token from Authorization header
        auth_header = request.headers.get("Authorization", "")
        if not auth_header.startswith("Bearer "):
            raise AuthenticationError("Invalid authorization header")
        
        current_token = auth_header.split(" ")[1]
        
        # Switch role
        auth_response = await auth_service.switch_user_role(
            db, current_token, role
        )
        
        # Create new refresh token for the new role
        refresh_token = create_refresh_token(
            subject=str(auth_response.user.user_id),
            user_roles=[role.value for role in auth_response.user.roles],
            primary_role=role.value
        )
        
        # Add refresh token to response
        auth_response.refresh_token = refresh_token
        
        logger.info(f"User {current_user.email} switched to role {role.value}")
        return auth_response
        
    except (AuthenticationError, AuthorizationError) as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Role switch error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Role switch failed"
        )


@router.post("/consent", status_code=status.HTTP_204_NO_CONTENT)
async def accept_consent(
    consent_data: ConsentAcceptance,
    request: Request,
    current_user: Annotated[User, Depends(get_current_active_user)],
    db: Annotated[asyncpg.Connection, Depends(get_database)],
    auth_service: Annotated[AuthService, Depends(get_auth_service)]
):
    """
    Accept user consent.
    
    - **consent_level**: Level 1 (mandatory) or Level 2 (optional)
    - **consent_type**: Type of consent (terms_of_service, privacy_policy, marketing)
    
    Records consent acceptance with timestamp and IP address.
    """
    try:
        # Get client IP
        client_ip = consent_data.ip_address or (request.client.host if request.client else None)
        
        # Record consent
        await auth_service.user_repo.record_user_consent(
            db,
            current_user.user_id,
            consent_data.consent_level.value,
            consent_data.consent_type.value,
            client_ip
        )
        
        logger.info(
            f"User {current_user.email} accepted {consent_data.consent_level.value} "
            f"consent: {consent_data.consent_type.value}"
        )
        
        return None
        
    except Exception as e:
        logger.error(f"Consent recording error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to record consent"
        )


@router.get("/")
async def auth_info():
    """Get authentication endpoints information."""
    return {
        "message": "TutorAide Authentication API",
        "version": "1.0",
        "endpoints": {
            "register": "POST /auth/register - Create new account",
            "login": "POST /auth/login - OAuth2 form login",
            "login_json": "POST /auth/login/json - JSON body login",
            "refresh": "POST /auth/refresh - Refresh access token",
            "logout": "POST /auth/logout - End session",
            "me": "GET /auth/me - Get current user info",
            "switch_role": "POST /auth/switch-role/{role} - Switch active role",
            "consent": "POST /auth/consent - Accept user consent",
            "google_login": "GET /auth/google/login - Initiate Google OAuth",
            "google_callback": "GET /auth/google/callback - Google OAuth callback",
            "google_link": "POST /auth/google/link - Link Google account",
            "sessions": "GET /auth/sessions - Get all active sessions",
            "invalidate_session": "DELETE /auth/sessions/{id} - Invalidate specific session",
            "invalidate_all": "DELETE /auth/sessions - Invalidate all sessions"
        },
        "token_expiry": {
            "manager": "3 hours",
            "tutor": "30 minutes",
            "client": "30 minutes"
        }
    }


# Google OAuth endpoints
@router.get("/google/login")
async def google_login(
    next: Optional[str] = Query(None, description="URL to redirect to after login"),
    redirect_uri: Optional[str] = Query(None, description="Custom redirect URI")
):
    """
    Initiate Google OAuth login flow.
    
    - **next**: Optional URL to redirect to after successful login
    - **redirect_uri**: Optional custom redirect URI (defaults to configured value)
    
    Redirects user to Google OAuth consent screen.
    """
    try:
        oauth_service = GoogleOAuthService()
        
        # Generate state token with next URL if provided
        state_data = {"next": next} if next else None
        state = oauth_service.generate_state_token(state_data)
        
        # Get authorization URL
        auth_url = oauth_service.get_authorization_url(state, redirect_uri)
        
        logger.info("Initiating Google OAuth login")
        return RedirectResponse(url=auth_url, status_code=status.HTTP_302_FOUND)
        
    except Exception as e:
        logger.error(f"Google OAuth initiation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to initiate Google login"
        )


@router.get("/google/callback")
async def google_callback(
    request: Request,
    db: Annotated[asyncpg.Connection, Depends(get_database)],
    code: str = Query(..., description="Authorization code from Google"),
    state: str = Query(..., description="State token for CSRF protection"),
    error: Optional[str] = Query(None, description="Error from Google"),
    role: Optional[str] = Query(None, description="Requested role for new users")
):
    """
    Handle Google OAuth callback.
    
    - **code**: Authorization code from Google
    - **state**: State token for CSRF verification
    - **error**: Error message if Google authentication failed
    - **role**: Optional role to assign for new users (defaults to CLIENT)
    
    Completes OAuth flow and returns JWT tokens.
    """
    try:
        # Check for Google errors
        if error:
            logger.error(f"Google OAuth error: {error}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=f"Google authentication failed: {error}"
            )
        
        oauth_service = GoogleOAuthService()
        
        # Verify state token
        if not oauth_service.verify_state_token(state):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid state token"
            )
        
        # Get state data (includes next URL if provided)
        state_data = oauth_service.get_state_data(state) or {}
        next_url = state_data.get("next", "/")
        
        # Exchange code for tokens
        try:
            tokens = await oauth_service.exchange_code_for_tokens(code)
        except AuthenticationError as e:
            logger.error(f"Token exchange failed: {e}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Failed to authenticate with Google"
            )
        
        # Get user info from Google
        try:
            google_user_info = await oauth_service.get_user_info(tokens["access_token"])
        except AuthenticationError as e:
            logger.error(f"Failed to get user info: {e}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Failed to get user information from Google"
            )
        
        # Determine roles for new users
        roles = []
        if role:
            try:
                roles = [UserRoleType(role.lower())]
            except ValueError:
                logger.warning(f"Invalid role specified: {role}")
                roles = [UserRoleType.CLIENT]
        
        # Get client IP
        client_ip = request.client.host if request.client else None
        
        # Authenticate or create user
        try:
            auth_response = await oauth_service.authenticate_user(
                db, google_user_info, roles=roles or None, ip_address=client_ip
            )
        except ValidationError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )
        except AuthenticationError as e:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=str(e)
            )
        
        # Redirect to frontend with tokens
        # In production, you might want to use more secure methods like:
        # 1. Server-side session storage
        # 2. Secure HTTP-only cookies
        # 3. Post-message to parent window for popup flows
        
        frontend_url = settings.CORS_ORIGINS[0] if settings.CORS_ORIGINS else "http://localhost:3000"
        redirect_params = {
            "token": auth_response.access_token,
            "refresh": auth_response.refresh_token,
            "expires_in": str(auth_response.expires_in),
            "next": next_url
        }
        
        redirect_url = f"{frontend_url}/auth/callback?{urlencode(redirect_params)}"
        
        logger.info(f"Google OAuth successful for user: {auth_response.user.email}")
        return RedirectResponse(url=redirect_url, status_code=status.HTTP_302_FOUND)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Google OAuth callback error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Google authentication failed"
        )


@router.post("/google/link", response_model=Dict[str, Any])
async def link_google_account(
    request: Request,
    current_user: Annotated[User, Depends(get_current_active_user)],
    db: Annotated[asyncpg.Connection, Depends(get_database)]
):
    """
    Link Google account to authenticated user.
    
    Requires an authorization code from Google OAuth flow.
    Links the Google account to the currently authenticated user.
    """
    try:
        # Get request body
        body = await request.json()
        code = body.get("code")
        
        if not code:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Missing authorization code"
            )
        
        oauth_service = GoogleOAuthService()
        
        # Exchange code for tokens
        try:
            tokens = await oauth_service.exchange_code_for_tokens(code)
        except AuthenticationError as e:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Failed to authenticate with Google"
            )
        
        # Get user info
        try:
            google_user_info = await oauth_service.get_user_info(tokens["access_token"])
        except AuthenticationError as e:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Failed to get user information from Google"
            )
        
        # Link account
        try:
            success = await oauth_service.link_google_account(
                db, current_user.user_id, google_user_info["sub"]
            )
        except ValidationError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )
        
        logger.info(f"Linked Google account to user: {current_user.email}")
        return {"success": success, "message": "Google account linked successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Google account linking error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to link Google account"
        )


# Session management endpoints
@router.get("/sessions", response_model=Dict[str, Any])
async def get_user_sessions(
    current_user: Annotated[User, Depends(get_current_active_user)],
    db: Annotated[asyncpg.Connection, Depends(get_database)]
):
    """
    Get all active sessions for the current user.
    
    Returns list of active sessions with details like IP, device, last activity.
    """
    try:
        from app.services.session_service import get_session_service
        session_service = get_session_service()
        
        sessions = await session_service.get_user_sessions(db, current_user.user_id)
        
        # Format sessions for response
        formatted_sessions = []
        for session in sessions:
            formatted_sessions.append({
                "session_id": str(session['session_id']),
                "role_type": session['role_type'],
                "ip_address": session.get('ip_address'),
                "user_agent": session.get('user_agent'),
                "last_activity": session['last_activity'].isoformat(),
                "created_at": session['created_at'].isoformat()
            })
        
        return {
            "sessions": formatted_sessions,
            "count": len(formatted_sessions)
        }
        
    except Exception as e:
        logger.error(f"Error getting user sessions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve sessions"
        )


@router.delete("/sessions/{session_id}", status_code=status.HTTP_204_NO_CONTENT)
async def invalidate_specific_session(
    session_id: str,
    current_user: Annotated[User, Depends(get_current_active_user)],
    db: Annotated[asyncpg.Connection, Depends(get_database)]
):
    """
    Invalidate a specific session.
    
    - **session_id**: UUID of the session to invalidate
    
    Users can only invalidate their own sessions.
    """
    try:
        from app.services.session_service import get_session_service
        from uuid import UUID
        
        session_service = get_session_service()
        
        # Validate UUID format
        try:
            session_uuid = UUID(session_id)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid session ID format"
            )
        
        # Get session to verify ownership
        session = await session_service.session_repo.find_by_id(db, session_uuid)
        
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Session not found"
            )
        
        # Verify user owns this session
        if session['user_id'] != current_user.user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Cannot invalidate another user's session"
            )
        
        # Invalidate the session
        await session_service.session_repo.invalidate_session(db, session_uuid)
        
        logger.info(f"User {current_user.user_id} invalidated session {session_id}")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error invalidating session: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to invalidate session"
        )


@router.delete("/sessions", status_code=status.HTTP_204_NO_CONTENT)
async def invalidate_all_sessions(
    current_user: Annotated[User, Depends(get_current_active_user)],
    db: Annotated[asyncpg.Connection, Depends(get_database)],
    except_current: bool = Query(True, description="Keep current session active")
):
    """
    Invalidate all sessions for the current user.
    
    - **except_current**: If true, keeps the current session active (default: true)
    
    Useful for security purposes or when user wants to log out all devices.
    """
    try:
        from app.services.session_service import get_session_service
        
        session_service = get_session_service()
        
        # TODO: Get current session ID from request context
        # For now, invalidate all sessions
        current_session_id = None
        
        count = await session_service.invalidate_user_sessions(
            db, current_user.user_id, 
            except_session_id=current_session_id if except_current else None
        )
        
        logger.info(f"User {current_user.user_id} invalidated {count} sessions")
        
    except Exception as e:
        logger.error(f"Error invalidating all sessions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to invalidate sessions"
        )


# Password reset endpoints
@router.post("/password/reset-request", response_model=Dict[str, str])
async def request_password_reset(
    reset_request: PasswordResetRequest,
    request: Request,
    response: Response,
    db: Annotated[asyncpg.Connection, Depends(get_database)]
):
    """
    Request a password reset link.
    
    - **email**: Email address to send reset link to
    
    Returns success message regardless of whether email exists (security).
    """
    # Get client IP for rate limiting
    client_ip = request.client.host if request.client else '127.0.0.1'
    
    # Import rate limiting validator
    from app.core.validation import RateLimitValidator
    
    try:
        # Check password reset rate limits
        try:
            RateLimitValidator.check_rate_limit(
                identifier=reset_request.email,
                action='password_reset',
                ip_address=client_ip
            )
            
            # Add rate limit headers to response
            response.headers["X-RateLimit-Limit"] = "3"  # 3 reset requests per hour
            response.headers["X-RateLimit-Window"] = "3600"  # 1 hour window
            
        except ValidationError as e:
            # Rate limit exceeded
            logger.security(f"Password reset rate limit exceeded for {reset_request.email} from {client_ip}")
            # Still return success message for security
            response.headers["Retry-After"] = "3600"
            response.headers["X-RateLimit-Limit"] = "3"
            response.headers["X-RateLimit-Remaining"] = "0"
            return {"message": "If the email exists, a password reset link has been sent"}
        
        from app.services.password_reset_service import get_password_reset_service
        from app.services.email_service import get_email_service
        
        password_reset_service = get_password_reset_service()
        email_service = get_email_service()
        
        # Get client info
        user_agent = request.headers.get("User-Agent")
        
        # Request reset
        result = await password_reset_service.request_password_reset(
            conn=db,
            email=reset_request.email,
            ip_address=client_ip,
            user_agent=user_agent
        )
        
        # Send email if token was created
        if "token" in result:
            # Determine user's preferred language (default to English)
            language = request.headers.get("Accept-Language", "en")[:2]
            if language not in ["en", "fr"]:
                language = "en"
            
            await email_service.send_password_reset_email(
                email=result["email"],
                reset_token=result["token"],
                language=language
            )
        
        # Always return success for security
        return {"message": "If the email exists, a password reset link has been sent"}
        
    except Exception as e:
        logger.error(f"Password reset request error: {e}")
        # Still return success for security
        return {"message": "If the email exists, a password reset link has been sent"}


@router.post("/password/reset-confirm", response_model=Dict[str, str])
async def confirm_password_reset(
    reset_confirm: PasswordResetConfirm,
    request: Request,
    db: Annotated[asyncpg.Connection, Depends(get_database)]
):
    """
    Confirm password reset with token.
    
    - **token**: Password reset token from email
    - **new_password**: New password (min 8 chars, upper, lower, digit)
    
    Resets password and invalidates all sessions for security.
    """
    try:
        from app.services.password_reset_service import get_password_reset_service
        from app.services.email_service import get_email_service
        from app.services.session_service import get_session_service
        
        password_reset_service = get_password_reset_service()
        email_service = get_email_service()
        session_service = get_session_service()
        
        # Get client IP
        ip_address = request.client.host if request.client else None
        
        # Reset password
        success = await password_reset_service.reset_password(
            conn=db,
            token=reset_confirm.token,
            new_password=reset_confirm.new_password,
            ip_address=ip_address
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired reset token"
            )
        
        # Get user info for email and session invalidation
        token_info = await password_reset_service.validate_reset_token(db, reset_confirm.token)
        if token_info:
            # Invalidate all user sessions for security
            await session_service.invalidate_user_sessions(db, token_info["user_id"])
            
            # Send confirmation email
            language = request.headers.get("Accept-Language", "en")[:2]
            if language not in ["en", "fr"]:
                language = "en"
            
            await email_service.send_password_changed_email(
                email=token_info["email"],
                language=language
            )
        
        logger.info("Password reset completed successfully")
        return {"message": "Password has been reset successfully"}
        
    except HTTPException:
        raise
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Password reset confirmation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to reset password"
        )


@router.get("/password/reset-validate/{token}", response_model=Dict[str, Any])
async def validate_reset_token(
    token: UUID,
    db: Annotated[asyncpg.Connection, Depends(get_database)]
):
    """
    Validate a password reset token.
    
    - **token**: Reset token to validate
    
    Returns token validity and associated email if valid.
    """
    try:
        from app.services.password_reset_service import get_password_reset_service
        
        password_reset_service = get_password_reset_service()
        
        # Validate token
        token_info = await password_reset_service.validate_reset_token(db, token)
        
        if not token_info:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired token"
            )
        
        return {
            "valid": True,
            "email": token_info["email"],
            "expires_at": token_info["expires_at"].isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Token validation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to validate token"
        )


# Email Verification Endpoints

@router.post("/email/verification/request", response_model=dict)
async def request_email_verification(
    request_data: EmailVerificationRequest,
    request: Request,
    response: Response,
    db: Annotated[asyncpg.Connection, Depends(get_database)],
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    """
    Request email verification for current user.
    
    This endpoint allows users to request email verification for a specific
    email address. A verification email will be sent with a secure token.
    """
    # Get client IP for rate limiting
    client_ip = request.client.host if request.client else '127.0.0.1'
    
    # Import rate limiting validator
    from app.core.validation import RateLimitValidator
    
    try:
        # Check email verification rate limits
        try:
            RateLimitValidator.check_rate_limit(
                identifier=current_user.email,
                action='email_verification',
                ip_address=client_ip
            )
            
            # Add rate limit headers to response
            response.headers["X-RateLimit-Limit"] = "5"  # 5 verification requests per hour
            response.headers["X-RateLimit-Window"] = "3600"  # 1 hour window
            
        except ValidationError as e:
            # Rate limit exceeded
            logger.security(f"Email verification rate limit exceeded for {current_user.email} from {client_ip}")
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Too many verification requests. Please try again later.",
                headers={
                    "Retry-After": "1800",  # 30 minutes
                    "X-RateLimit-Limit": "5",
                    "X-RateLimit-Remaining": "0"
                }
            )
        
        email_verification_service = get_email_verification_service()
        email_service = get_email_service()
        
        # Get client info
        user_agent = request.headers.get("user-agent")
        
        # Request verification token
        token_info = await email_verification_service.request_verification(
            conn=db,
            user_id=current_user.user_id,
            email=request_data.email,
            ip_address=client_ip,
            user_agent=user_agent
        )
        
        # Send verification email
        email_sent = await email_service.send_email_verification(
            email=request_data.email,
            verification_token=token_info['token'],
            language=request_data.language
        )
        
        if not email_sent:
            logger.warning(f"Failed to send verification email to {request_data.email}")
        
        return {
            "message": "Verification email sent successfully",
            "email": request_data.email,
            "expires_at": token_info['expires_at'].isoformat()
        }
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except BusinessLogicError as e:
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=str(e)
        )
    except HTTPException:
        # Re-raise HTTP exceptions (like rate limit)
        raise
    except Exception as e:
        logger.error(f"Email verification request error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to request email verification"
        )


@router.post("/email/verification/confirm", response_model=EmailVerificationResponse)
async def confirm_email_verification(
    confirm_data: EmailVerificationConfirm,
    request: Request,
    db: Annotated[asyncpg.Connection, Depends(get_database)]
):
    """
    Confirm email verification using token.
    
    This endpoint verifies an email address using the token sent via email.
    Once verified, the user's email_verified status will be updated.
    """
    try:
        email_verification_service = get_email_verification_service()
        
        # Get client info
        client_ip = request.client.host
        
        # Verify email
        result = await email_verification_service.verify_email(
            conn=db,
            token=confirm_data.token,
            ip_address=client_ip
        )
        
        return result
        
    except AuthenticationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Email verification confirm error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Email verification failed"
        )


@router.get("/email/verification/validate/{token}")
async def validate_verification_token(
    token: UUID,
    db: Annotated[asyncpg.Connection, Depends(get_database)]
):
    """
    Validate email verification token without confirming.
    
    This endpoint checks if a verification token is valid and returns
    information about it without actually verifying the email.
    """
    try:
        email_verification_service = get_email_verification_service()
        
        token_info = await email_verification_service.validate_token(
            conn=db,
            token=token
        )
        
        return token_info
        
    except AuthenticationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Token validation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to validate token"
        )


@router.get("/email/verification/status", response_model=EmailVerificationStatus)
async def get_email_verification_status(
    db: Annotated[asyncpg.Connection, Depends(get_database)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    email: str = Query(..., description="Email address to check")
):
    """
    Get email verification status for current user.
    
    This endpoint returns the verification status of an email address
    for the current user, including whether verification is pending.
    """
    try:
        email_verification_service = get_email_verification_service()
        
        status_info = await email_verification_service.get_verification_status(
            conn=db,
            user_id=current_user.user_id,
            email=email
        )
        
        return status_info
        
    except Exception as e:
        logger.error(f"Get verification status error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get verification status"
        )


@router.post("/email/verification/resend", response_model=dict)
async def resend_email_verification(
    request_data: EmailVerificationRequest,
    request: Request,
    response: Response,
    db: Annotated[asyncpg.Connection, Depends(get_database)],
    current_user: Annotated[User, Depends(get_current_active_user)]
):
    """
    Resend email verification.
    
    This endpoint allows users to resend verification email if the previous
    one was not received or has expired. Subject to cooldown limits.
    """
    # Get client IP for rate limiting
    client_ip = request.client.host if request.client else '127.0.0.1'
    
    # Import rate limiting validator
    from app.core.validation import RateLimitValidator
    
    try:
        # Check email verification rate limits (same as request)
        try:
            RateLimitValidator.check_rate_limit(
                identifier=current_user.email,
                action='email_verification',
                ip_address=client_ip
            )
            
            # Add rate limit headers to response
            response.headers["X-RateLimit-Limit"] = "5"  # 5 verification requests per hour
            response.headers["X-RateLimit-Window"] = "3600"  # 1 hour window
            
        except ValidationError as e:
            # Rate limit exceeded
            logger.security(f"Email verification resend rate limit exceeded for {current_user.email} from {client_ip}")
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Too many verification requests. Please try again later.",
                headers={
                    "Retry-After": "1800",  # 30 minutes
                    "X-RateLimit-Limit": "5",
                    "X-RateLimit-Remaining": "0"
                }
            )
        
        email_verification_service = get_email_verification_service()
        email_service = get_email_service()
        
        # Get client info
        user_agent = request.headers.get("user-agent")
        
        # Resend verification
        token_info = await email_verification_service.resend_verification(
            conn=db,
            user_id=current_user.user_id,
            email=request_data.email,
            ip_address=client_ip,
            user_agent=user_agent
        )
        
        # Send verification email
        email_sent = await email_service.send_email_verification(
            email=request_data.email,
            verification_token=token_info['token'],
            language=request_data.language
        )
        
        if not email_sent:
            logger.warning(f"Failed to resend verification email to {request_data.email}")
        
        return {
            "message": "Verification email resent successfully",
            "email": request_data.email,
            "expires_at": token_info['expires_at'].isoformat()
        }
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except BusinessLogicError as e:
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=str(e)
        )
    except HTTPException:
        # Re-raise HTTP exceptions (like rate limit)
        raise
    except Exception as e:
        logger.error(f"Resend verification error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to resend email verification"
        )


# Consent Management Endpoints

def get_consent_service() -> ConsentService:
    """Get consent service instance."""
    return ConsentService()


@router.post("/consent/accept", response_model=ConsentStatusResponse)
async def accept_consent(
    consent_request: ConsentAcceptanceRequest,
    request: Request,
    db: Annotated[asyncpg.Connection, Depends(get_database)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    consent_service: Annotated[ConsentService, Depends(get_consent_service)]
):
    """
    Accept a consent for the current user.
    
    - **consent_type**: Type of consent to accept (e.g., "terms_of_service", "privacy_policy")
    - **document_version**: Optional specific version to accept (defaults to latest)
    - **language**: Language preference for the consent document
    
    Records the user's acceptance of the specified consent with audit trail.
    """
    try:
        # Get client info for audit trail
        client_ip = request.client.host if request.client else None
        user_agent = request.headers.get("User-Agent")
        
        # Accept the consent
        result = await consent_service.accept_consent(
            conn=db,
            user_id=current_user.user_id,
            request=consent_request,
            ip_address=client_ip,
            user_agent=user_agent
        )
        
        logger.info(
            f"User {current_user.user_id} accepted consent: {consent_request.consent_type}",
            user_id=current_user.user_id,
            consent_type=consent_request.consent_type
        )
        
        return result
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Consent acceptance error: {e}", user_id=current_user.user_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to accept consent"
        )


@router.post("/consent/withdraw", response_model=ConsentStatusResponse)
async def withdraw_consent(
    withdrawal_request: ConsentWithdrawalRequest,
    request: Request,
    db: Annotated[asyncpg.Connection, Depends(get_database)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    consent_service: Annotated[ConsentService, Depends(get_consent_service)]
):
    """
    Withdraw a consent for the current user.
    
    - **consent_type**: Type of consent to withdraw
    - **reason**: Optional reason for withdrawal
    
    Note: Level 1 mandatory consents (Terms of Service, Privacy Policy) cannot be withdrawn.
    """
    try:
        # Get client info for audit trail
        client_ip = request.client.host if request.client else None
        user_agent = request.headers.get("User-Agent")
        
        # Withdraw the consent
        result = await consent_service.withdraw_consent(
            conn=db,
            user_id=current_user.user_id,
            request=withdrawal_request,
            ip_address=client_ip,
            user_agent=user_agent
        )
        
        logger.info(
            f"User {current_user.user_id} withdrew consent: {withdrawal_request.consent_type}",
            user_id=current_user.user_id,
            consent_type=withdrawal_request.consent_type,
            reason=withdrawal_request.reason
        )
        
        return result
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except ResourceNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Consent withdrawal error: {e}", user_id=current_user.user_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to withdraw consent"
        )


@router.get("/consent/summary", response_model=ConsentSummaryResponse)
async def get_consent_summary(
    db: Annotated[asyncpg.Connection, Depends(get_database)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    consent_service: Annotated[ConsentService, Depends(get_consent_service)],
    language: str = Query(default="en", description="Language for consent documents")
):
    """
    Get a summary of all consent statuses for the current user.
    
    - **language**: Language preference for consent documents (en/fr)
    
    Returns the status of all available consents and indicates if mandatory consents are complete.
    """
    try:
        summary = await consent_service.get_user_consent_summary(
            conn=db,
            user_id=current_user.user_id,
            language=language
        )
        
        return summary
        
    except Exception as e:
        logger.error(f"Consent summary error: {e}", user_id=current_user.user_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get consent summary"
        )


@router.get("/consent/history", response_model=ConsentHistoryResponse)
async def get_consent_history(
    db: Annotated[asyncpg.Connection, Depends(get_database)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    consent_service: Annotated[ConsentService, Depends(get_consent_service)],
    consent_type: Optional[str] = Query(None, description="Filter by specific consent type"),
    limit: int = Query(default=50, ge=1, le=100, description="Maximum number of history entries")
):
    """
    Get consent history for the current user.
    
    - **consent_type**: Optional filter for specific consent type
    - **limit**: Maximum number of history entries to return (1-100)
    
    Returns the history of consent actions with timestamps and audit information.
    """
    try:
        history = await consent_service.get_consent_history(
            conn=db,
            user_id=current_user.user_id,
            consent_type=consent_type,
            limit=limit
        )
        
        return history
        
    except Exception as e:
        logger.error(f"Consent history error: {e}", user_id=current_user.user_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get consent history"
        )


@router.get("/consent/validate", response_model=ConsentValidationResult)
async def validate_user_consents(
    db: Annotated[asyncpg.Connection, Depends(get_database)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    consent_service: Annotated[ConsentService, Depends(get_consent_service)],
    language: str = Query(default="en", description="Language for validation"),
    required_consents: Optional[str] = Query(None, description="Comma-separated list of required consent types")
):
    """
    Validate if the current user has all required consents.
    
    - **language**: Language preference for validation
    - **required_consents**: Optional comma-separated list of specific consent types to validate
    
    Returns validation result indicating if all required consents are valid.
    """
    try:
        required_list = None
        if required_consents:
            required_list = [ct.strip() for ct in required_consents.split(",") if ct.strip()]
        
        validation_result = await consent_service.validate_user_consents(
            conn=db,
            user_id=current_user.user_id,
            required_consents=required_list,
            language=language
        )
        
        return validation_result
        
    except Exception as e:
        logger.error(f"Consent validation error: {e}", user_id=current_user.user_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to validate consents"
        )


@router.post("/consent/bulk-accept", response_model=BulkConsentResponse)
async def bulk_accept_consents(
    bulk_request: BulkConsentRequest,
    request: Request,
    db: Annotated[asyncpg.Connection, Depends(get_database)],
    current_user: Annotated[User, Depends(get_current_active_user)],
    consent_service: Annotated[ConsentService, Depends(get_consent_service)]
):
    """
    Accept multiple consents in a single request.
    
    - **consents**: List of consent acceptance requests
    
    Useful for accepting all mandatory consents during registration or onboarding.
    Returns summary of successful and failed consent acceptances.
    """
    try:
        # Get client info for audit trail
        client_ip = request.client.host if request.client else None
        user_agent = request.headers.get("User-Agent")
        
        # Process bulk consent acceptance
        result = await consent_service.bulk_accept_consents(
            conn=db,
            user_id=current_user.user_id,
            request=bulk_request,
            ip_address=client_ip,
            user_agent=user_agent
        )
        
        logger.info(
            f"User {current_user.user_id} bulk accepted {len(result.successful)} consents",
            user_id=current_user.user_id,
            successful_count=len(result.successful),
            failed_count=len(result.failed)
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Bulk consent acceptance error: {e}", user_id=current_user.user_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process bulk consent acceptance"
        )


@router.get("/consent/documents", response_model=List[ConsentDocument])
async def get_available_consent_documents(
    db: Annotated[asyncpg.Connection, Depends(get_database)],
    consent_service: Annotated[ConsentService, Depends(get_consent_service)],
    language: str = Query(default="en", description="Language for documents"),
    level: Optional[str] = Query(None, description="Filter by consent level (level_1_mandatory, level_2_optional)")
):
    """
    Get available consent documents.
    
    - **language**: Language preference for documents (en/fr)
    - **level**: Optional filter by consent level
    
    Returns list of available consent documents with content and metadata.
    """
    try:
        from app.models.consent_models import ConsentLevel
        
        level_filter = None
        if level:
            try:
                level_filter = ConsentLevel(level)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid consent level: {level}"
                )
        
        documents = await consent_service.get_available_consent_documents(
            conn=db,
            language=language,
            level=level_filter
        )
        
        return documents
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get consent documents error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get consent documents"
        )


# Password Reset Endpoints
@router.post("/password/reset-request", status_code=status.HTTP_200_OK)
async def request_password_reset(
    reset_request: PasswordResetRequest,
    request: Request,
    db: Annotated[asyncpg.Connection, Depends(get_database)],
    auth_service: Annotated[AuthService, Depends(get_auth_service)]
):
    """
    Request a password reset email.
    
    - **email**: Email address of the account
    
    Always returns success for security reasons (doesn't reveal if email exists).
    Sends reset link to email if account exists.
    """
    try:
        # Get client IP for rate limiting
        client_ip = request.client.host if request.client else '127.0.0.1'
        
        # Import services
        from app.services.auth_token_service import get_auth_token_service
        from app.services.email_service import get_email_service
        from app.services.rate_limiting_service import rate_limiting_service
        
        # Check rate limits
        try:
            rate_limiting_service.check_password_reset_rate_limit(
                email=reset_request.email,
                ip_address=client_ip
            )
        except ValidationError:
            logger.warning(f"Password reset rate limit exceeded for {reset_request.email} from {client_ip}")
            # Still return success for security
            return {"message": "If an account exists with this email, a password reset link has been sent."}
        
        # Check if user exists
        user_record = await auth_service.user_repo.find_by_email(db, reset_request.email)
        
        if user_record:
            # Get auth token service
            auth_token_service = get_auth_token_service()
            
            # Create password reset token
            token = auth_token_service._generate_secure_token()
            token_hash = auth_token_service._hash_token(token)
            
            # Store token in database
            await auth_token_service.token_repo.create_token(
                conn=db,
                user_id=user_record['user_id'],
                token_type=AuthTokenType.PASSWORD_RESET,
                token_hash=token_hash,
                expires_at=now_est() + timedelta(hours=1),
                ip_address=client_ip,
                user_agent=request.headers.get('User-Agent')
            )
            
            # Send reset email
            email_service = get_email_service()
            reset_url = f"{settings.FRONTEND_URL}/auth/reset-password?token={token}"
            
            await email_service.send_password_reset_email(
                to_email=user_record['email'],
                reset_url=reset_url,
                user_name=user_record.get('email', '').split('@')[0]
            )
            
            logger.info(f"Password reset requested for user {user_record['user_id']}")
        else:
            logger.info(f"Password reset requested for non-existent email: {reset_request.email}")
        
        # Always return success for security
        return {"message": "If an account exists with this email, a password reset link has been sent."}
        
    except Exception as e:
        logger.error(f"Password reset request error: {e}")
        # Still return success for security
        return {"message": "If an account exists with this email, a password reset link has been sent."}


@router.post("/password/reset-confirm", status_code=status.HTTP_200_OK)
async def confirm_password_reset(
    reset_confirm: PasswordResetConfirm,
    request: Request,
    db: Annotated[asyncpg.Connection, Depends(get_database)],
    auth_service: Annotated[AuthService, Depends(get_auth_service)]
):
    """
    Confirm password reset with token.
    
    - **token**: Reset token from email
    - **new_password**: New password (must meet security requirements)
    
    Validates token and updates password if valid.
    """
    try:
        # Get client IP
        client_ip = request.client.host if request.client else '127.0.0.1'
        
        # Import services
        from app.services.auth_token_service import get_auth_token_service
        from app.services.email_service import get_email_service
        from app.core.validation import PasswordValidator
        
        # Validate new password
        PasswordValidator.validate_password_strength(reset_confirm.new_password)
        
        # Get auth token service
        auth_token_service = get_auth_token_service()
        
        # Validate token
        token_hash = auth_token_service._hash_token(reset_confirm.token)
        
        # Find token in database
        token_record = await auth_token_service.token_repo.get_token_by_hash(
            conn=db,
            token_hash=token_hash,
            token_type=auth_token_service.AuthTokenType.PASSWORD_RESET
        )
        
        if not token_record:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired reset token"
            )
        
        # Check if token is expired
        if token_record.expires_at < now_est():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Reset token has expired"
            )
        
        # Check if token was already used
        if token_record.used_at:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Reset token has already been used"
            )
        
        # Update password
        new_password_hash = get_password_hash(reset_confirm.new_password)
        
        await auth_service.user_repo.update_password(
            conn=db,
            user_id=token_record.user_id,
            password_hash=new_password_hash
        )
        
        # Mark token as used
        await auth_token_service.token_repo.mark_token_used(
            conn=db,
            token_id=token_record.token_id,
            used_from_ip=client_ip
        )
        
        # Get user for email
        user = await auth_service.user_repo.find_by_id(db, token_record.user_id)
        
        # Send confirmation email
        if user:
            email_service = get_email_service()
            await email_service.send_password_changed_email(
                to_email=user['email'],
                user_name=user['email'].split('@')[0],
                changed_at=now_est(),
                ip_address=client_ip
            )
        
        logger.info(f"Password reset completed for user {token_record.user_id}")
        
        return {
            "message": "Password has been reset successfully",
            "email": user['email'] if user else None
        }
        
    except HTTPException:
        raise
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Password reset confirmation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to reset password"
        )