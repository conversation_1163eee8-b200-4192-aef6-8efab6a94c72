-- Migration: Add tutor invitations table
-- Description: Create tutor_invitations table for manager-initiated tutor onboarding

-- Create tutor_invitations table
CREATE TABLE IF NOT EXISTS tutor_invitations (
    invitation_id SERIAL PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    token VARCHAR(255) UNIQUE NOT NULL,
    invited_by INTEGER NOT NULL REFERENCES user_accounts(user_id),
    
    -- Invitation details
    first_name <PERSON><PERSON><PERSON><PERSON>(100),
    last_name VA<PERSON><PERSON><PERSON>(100),
    phone VARCHAR(20),
    message TEXT,
    
    -- Status tracking
    status VARCHAR(20) DEFAULT 'pending', -- pending, accepted, expired
    accepted_at TIMESTAMP,
    accepted_user_id INTEGER REFERENCES user_accounts(user_id),
    
    -- Expiration
    expires_at TIMESTAMP NOT NULL,
    
    -- Timestamps
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT valid_status CHECK (status IN ('pending', 'accepted', 'expired')),
    CONSTRAINT email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- Create indexes for performance
CREATE INDEX idx_tutor_invitations_email ON tutor_invitations(email);
CREATE INDEX idx_tutor_invitations_token ON tutor_invitations(token);
CREATE INDEX idx_tutor_invitations_status ON tutor_invitations(status) WHERE status = 'pending';
CREATE INDEX idx_tutor_invitations_expires ON tutor_invitations(expires_at) WHERE status = 'pending';

-- Create trigger to update updated_at timestamp
CREATE TRIGGER update_tutor_invitations_updated_at
    BEFORE UPDATE ON tutor_invitations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE tutor_invitations IS 'Stores tutor invitation tokens sent by managers';
COMMENT ON COLUMN tutor_invitations.invitation_id IS 'Unique identifier for the invitation';
COMMENT ON COLUMN tutor_invitations.email IS 'Email address the invitation was sent to';
COMMENT ON COLUMN tutor_invitations.token IS 'Unique token for accepting the invitation';
COMMENT ON COLUMN tutor_invitations.invited_by IS 'Manager who sent the invitation';
COMMENT ON COLUMN tutor_invitations.status IS 'Current status of the invitation';
COMMENT ON COLUMN tutor_invitations.expires_at IS 'When the invitation expires';