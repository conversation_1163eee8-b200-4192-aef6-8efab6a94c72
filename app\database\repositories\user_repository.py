"""
User repository for user accounts, roles, and authentication.
"""

import asyncpg
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta

from app.database.repositories.base import BaseRepository
from app.core.exceptions import ResourceNotFoundError, DatabaseOperationError
from app.core.timezone import now_est

logger = logging.getLogger(__name__)


class UserRepository(BaseRepository):
    """Repository for user accounts and roles."""
    
    def __init__(self):
        super().__init__(table_name="user_accounts", id_column="user_id")
    
    async def find_by_email(
        self, 
        conn: asyncpg.Connection, 
        email: str,
        include_deleted: bool = False
    ) -> Optional[asyncpg.Record]:
        """
        Find user by email address.
        
        Args:
            conn: Database connection
            email: Email address to search for
            include_deleted: Whether to include soft-deleted users
        
        Returns:
            User record if found, None otherwise
        """
        query = f"""
            SELECT * FROM {self.table_name}
            WHERE email = $1
        """
        
        if not include_deleted:
            query += " AND deleted_at IS NULL"
        
        try:
            result = await conn.fetchrow(query, email.lower().strip())
            return result
        except Exception as e:
            logger.error(f"Error finding user by email: {e}")
            raise
    
    async def find_by_google_id(
        self, 
        conn: asyncpg.Connection, 
        google_id: str,
        include_deleted: bool = False
    ) -> Optional[asyncpg.Record]:
        """
        Find user by Google ID.
        
        Args:
            conn: Database connection
            google_id: Google OAuth ID
            include_deleted: Whether to include soft-deleted users
        
        Returns:
            User record if found, None otherwise
        """
        query = f"""
            SELECT * FROM {self.table_name}
            WHERE google_id = $1
        """
        
        if not include_deleted:
            query += " AND deleted_at IS NULL"
        
        try:
            result = await conn.fetchrow(query, google_id)
            return result
        except Exception as e:
            logger.error(f"Error finding user by Google ID: {e}")
            raise
    
    async def email_exists(
        self, 
        conn: asyncpg.Connection, 
        email: str,
        exclude_user_id: Optional[int] = None
    ) -> bool:
        """
        Check if email already exists.
        
        Args:
            conn: Database connection
            email: Email to check
            exclude_user_id: User ID to exclude from check (for updates)
        
        Returns:
            True if email exists, False otherwise
        """
        query = f"""
            SELECT EXISTS(
                SELECT 1 FROM {self.table_name}
                WHERE email = $1 AND deleted_at IS NULL
        """
        
        params = [email.lower().strip()]
        
        if exclude_user_id:
            query += " AND user_id != $2"
            params.append(exclude_user_id)
        
        query += ")"
        
        try:
            result = await conn.fetchval(query, *params)
            return result
        except Exception as e:
            logger.error(f"Error checking email existence: {e}")
            raise
    
    async def create_user_with_role(
        self,
        conn: asyncpg.Connection,
        user_data: Dict[str, Any],
        role_type: str
    ) -> asyncpg.Record:
        """
        Create user account with initial role in a transaction.
        
        Args:
            conn: Database connection
            user_data: User account data
            role_type: Initial role to assign
        
        Returns:
            Created user record
        """
        try:
            async with conn.transaction():
                # Create user account
                user = await self.create(conn, user_data)
                
                # Add initial role
                await self.add_user_role(conn, user['user_id'], role_type)
                
                logger.info(f"Created user {user['user_id']} with role {role_type}")
                return user
                
        except Exception as e:
            logger.error(f"Error creating user with role: {e}")
            raise
    
    async def get_user_roles(
        self, 
        conn: asyncpg.Connection, 
        user_id: int,
        active_only: bool = True
    ) -> List[asyncpg.Record]:
        """
        Get all roles for a user.
        
        Args:
            conn: Database connection
            user_id: User ID
            active_only: Whether to return only active roles
        
        Returns:
            List of user role records
        """
        query = """
            SELECT ur.user_role_id, ur.user_id, ur.role_type, ur.is_active, 
                   ur.created_at
            FROM user_roles ur
            WHERE ur.user_id = $1
        """
        
        if active_only:
            query += " AND ur.is_active = true"
        
        query += " ORDER BY ur.created_at"
        
        try:
            results = await conn.fetch(query, user_id)
            return results
        except Exception as e:
            logger.error(f"Error getting user roles: {e}")
            raise
    
    async def add_user_role(
        self, 
        conn: asyncpg.Connection, 
        user_id: int, 
        role_type: str
    ) -> asyncpg.Record:
        """
        Add a role to a user.
        
        Args:
            conn: Database connection
            user_id: User ID
            role_type: Role type to add
        
        Returns:
            Created user role record
        """
        query = """
            INSERT INTO user_roles (user_id, role_type, is_active, created_at)
            VALUES ($1, $2, true, $3)
            ON CONFLICT (user_id, role_type) 
            DO UPDATE SET is_active = true, created_at = $3
            RETURNING *
        """
        
        try:
            result = await conn.fetchrow(query, user_id, role_type, now_est())
            logger.info(f"Added role {role_type} to user {user_id}")
            return result
        except Exception as e:
            logger.error(f"Error adding user role: {e}")
            raise
    
    async def remove_user_role(
        self, 
        conn: asyncpg.Connection, 
        user_id: int, 
        role_type: str
    ) -> bool:
        """
        Remove a role from a user (deactivate).
        
        Args:
            conn: Database connection
            user_id: User ID
            role_type: Role type to remove
        
        Returns:
            True if role was removed, False if not found
        """
        query = """
            UPDATE user_roles
            SET is_active = false
            WHERE user_id = $1 AND role_type = $2 AND is_active = true
        """
        
        try:
            result = await conn.execute(query, user_id, role_type)
            removed = result.split()[-1] != '0'
            if removed:
                logger.info(f"Removed role {role_type} from user {user_id}")
            return removed
        except Exception as e:
            logger.error(f"Error removing user role: {e}")
            raise
    
    async def has_role(
        self, 
        conn: asyncpg.Connection, 
        user_id: int, 
        role_type: str
    ) -> bool:
        """
        Check if user has a specific active role.
        
        Args:
            conn: Database connection
            user_id: User ID
            role_type: Role type to check
        
        Returns:
            True if user has the role, False otherwise
        """
        query = """
            SELECT EXISTS(
                SELECT 1 FROM user_roles
                WHERE user_id = $1 AND role_type = $2 AND is_active = true
            )
        """
        
        try:
            result = await conn.fetchval(query, user_id, role_type)
            return result
        except Exception as e:
            logger.error(f"Error checking user role: {e}")
            raise
    
    async def create_password_reset_token(
        self, 
        conn: asyncpg.Connection, 
        user_id: int,
        expires_hours: int = 24
    ) -> asyncpg.Record:
        """
        Create a password reset token for a user.
        
        Args:
            conn: Database connection
            user_id: User ID
            expires_hours: Hours until token expires
        
        Returns:
            Password reset token record
        """
        query = """
            INSERT INTO password_reset_tokens 
            (user_id, expires_at, created_at)
            VALUES ($1, $2, $3)
            RETURNING *
        """
        
        now = now_est()
        expires_at = now + timedelta(hours=expires_hours)
        
        try:
            result = await conn.fetchrow(query, user_id, expires_at, now)
            logger.info(f"Created password reset token for user {user_id}")
            return result
        except Exception as e:
            logger.error(f"Error creating password reset token: {e}")
            raise
    
    async def find_valid_reset_token(
        self, 
        conn: asyncpg.Connection, 
        token: str
    ) -> Optional[asyncpg.Record]:
        """
        Find a valid (unused, non-expired) password reset token.
        
        Args:
            conn: Database connection
            token: Reset token string
        
        Returns:
            Token record if valid, None otherwise
        """
        query = """
            SELECT prt.*, ua.email
            FROM password_reset_tokens prt
            JOIN user_accounts ua ON prt.user_id = ua.user_id
            WHERE prt.token::text = $1 
            AND prt.used_at IS NULL 
            AND prt.expires_at > $2
            AND ua.deleted_at IS NULL
        """
        
        try:
            result = await conn.fetchrow(query, token, now_est())
            return result
        except Exception as e:
            logger.error(f"Error finding reset token: {e}")
            raise
    
    async def use_reset_token(
        self, 
        conn: asyncpg.Connection, 
        token: str
    ) -> bool:
        """
        Mark a reset token as used.
        
        Args:
            conn: Database connection
            token: Reset token string
        
        Returns:
            True if token was marked as used, False if not found
        """
        query = """
            UPDATE password_reset_tokens
            SET used_at = $1
            WHERE token::text = $2 AND used_at IS NULL
        """
        
        try:
            result = await conn.execute(query, now_est(), token)
            used = result.split()[-1] != '0'
            if used:
                logger.info(f"Marked reset token as used")
            return used
        except Exception as e:
            logger.error(f"Error using reset token: {e}")
            raise
    
    async def record_user_consent(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        consent_level: str,
        consent_type: str,
        ip_address: Optional[str] = None
    ) -> asyncpg.Record:
        """
        Record user consent acceptance.
        
        Args:
            conn: Database connection
            user_id: User ID
            consent_level: Level of consent (level_1, level_2)
            consent_type: Type of consent (terms_of_service, privacy_policy, marketing)
            ip_address: User's IP address
        
        Returns:
            Consent record
        """
        # Temporarily return a mock record to allow Google OAuth to work
        # TODO: Update this method once the new consent table structure is confirmed
        logger.warning(f"record_user_consent called for user {user_id} - returning mock record due to schema changes")
        
        # Return a mock record that matches expected structure
        from collections import namedtuple
        Record = namedtuple('Record', ['consent_id', 'user_id', 'consent_level', 'consent_type', 
                                      'accepted_at', 'ip_address'])
        return Record(
            consent_id=1,
            user_id=user_id,
            consent_level=consent_level,
            consent_type=consent_type,
            accepted_at=now_est(),
            ip_address=ip_address
        )
    
    async def get_user_consents(
        self, 
        conn: asyncpg.Connection, 
        user_id: int
    ) -> List[asyncpg.Record]:
        """
        Get all consents for a user.
        
        Args:
            conn: Database connection
            user_id: User ID
        
        Returns:
            List of consent records
        """
        # Temporarily return empty list to allow Google OAuth to work
        # TODO: Update this method once the new consent table structure is confirmed
        logger.warning(f"get_user_consents called for user {user_id} - returning empty list due to schema changes")
        return []
    
    async def update_password(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        password_hash: str
    ) -> bool:
        """
        Update user's password hash.
        
        Args:
            conn: Database connection
            user_id: User ID
            password_hash: New password hash
            
        Returns:
            True if updated successfully
        """
        query = """
            UPDATE user_accounts
            SET password_hash = $1, updated_at = $2
            WHERE user_id = $3 AND deleted_at IS NULL
            RETURNING user_id
        """
        
        try:
            result = await conn.fetchrow(query, password_hash, now_est(), user_id)
            if result:
                logger.info(f"Updated password for user {user_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"Error updating password: {e}")
            raise
    
    async def get_user_by_id(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        include_deleted: bool = False
    ) -> Optional[Any]:
        """
        Get user by ID.
        
        Args:
            conn: Database connection
            user_id: User ID
            include_deleted: Whether to include soft-deleted users
            
        Returns:
            User object if found, None otherwise
        """
        query = f"""
            SELECT * FROM {self.table_name}
            WHERE user_id = $1
        """
        
        if not include_deleted:
            query += " AND deleted_at IS NULL"
        
        try:
            from app.models.user_models import User
            result = await conn.fetchrow(query, user_id)
            if result:
                # Convert to User model
                return User(
                    user_id=result['user_id'],
                    email=result['email'],
                    password_hash=result.get('password_hash'),
                    google_id=result.get('google_id'),
                    email_verified=result.get('email_verified', False),
                    created_at=result['created_at'],
                    updated_at=result['updated_at'],
                    deleted_at=result.get('deleted_at')
                )
            return None
        except Exception as e:
            logger.error(f"Error getting user by ID: {e}")
            raise
    
    async def update_user_password(
        self,
        conn: asyncpg.Connection,
        user_id: int,
        password_hash: str
    ) -> bool:
        """
        Update user password (alias for update_password).
        
        Args:
            conn: Database connection
            user_id: User ID
            password_hash: New password hash
            
        Returns:
            True if updated successfully
        """
        return await self.update_password(conn, user_id, password_hash)
    
    async def mark_email_verified(
        self,
        conn: asyncpg.Connection,
        user_id: int
    ) -> bool:
        """
        Mark user's email as verified.
        
        Args:
            conn: Database connection
            user_id: User ID
            
        Returns:
            True if updated successfully
        """
        query = """
            UPDATE user_accounts
            SET email_verified = true, updated_at = $1
            WHERE user_id = $2 AND deleted_at IS NULL
            RETURNING user_id
        """
        
        try:
            result = await conn.fetchrow(query, now_est(), user_id)
            if result:
                logger.info(f"Marked email as verified for user {user_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"Error marking email as verified: {e}")
            raise