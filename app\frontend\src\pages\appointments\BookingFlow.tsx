import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Calendar, Clock, User, MapPin, CreditCard, Check } from 'lucide-react';
import Button from '../../components/common/Button';
import { Card } from '../../components/common/Card';
import { Input } from '../../components/common/Input';
import { Select } from '../../components/common/Select';
import { DatePicker } from '../../components/forms/DatePicker';
import { TimePicker } from '../../components/forms/TimePicker';
import { UserCard } from '../../components/cards/UserCard';
import { Badge } from '../../components/common/Badge';
import { FormSection } from '../../components/forms/FormSection';
import { FormField } from '../../components/forms/FormField';
import { serviceApi, ServicePreset } from '../../services/serviceApi';
import { appointmentService } from '../../services/appointmentService';
import { LocationType, PackageType } from '../../types/appointment';
import LoadingSpinner from '../../components/ui/LoadingSpinner';
import { tutorService } from '../../services/tutorService';
import { useAuth } from '../../contexts/AuthContext';
import toast from 'react-hot-toast';

interface BookingStep {
  id: string;
  title: string;
  icon: React.ReactNode;
  completed: boolean;
}

const BookingFlow: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(0);
  const [servicePresets, setServicePresets] = useState<ServicePreset[]>([]);
  const [loadingPresets, setLoadingPresets] = useState(true);
  const { user } = useAuth();
  const [tutors, setTutors] = useState<any[]>([]);
  const [loadingTutors, setLoadingTutors] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [checkingConflicts, setCheckingConflicts] = useState(false);
  const [bookingData, setBookingData] = useState({
    tutorId: '',
    subject: '',
    date: '',
    time: '',
    duration: '60',
    locationType: LocationType.ONLINE,
    locationDetails: '',
    notes: '',
    paymentMethod: 'card',
  });

  useEffect(() => {
    fetchServicePresets();
    fetchTutors();
  }, []);

  const fetchServicePresets = async () => {
    try {
      setLoadingPresets(true);
      const presets = await serviceApi.getServicePresets({ is_active_only: true });
      setServicePresets(presets);
    } catch (error) {
      console.error('Error fetching service presets:', error);
      toast.error(t('appointments.errors.fetchPresets'));
    } finally {
      setLoadingPresets(false);
    }
  };

  const getDurationOptions = () => {
    return serviceApi.getDurationOptions(bookingData.locationType, servicePresets);
  };

  const getSelectedPreset = () => {
    return servicePresets.find(
      preset => preset.duration_minutes.toString() === bookingData.duration && 
      preset.service_type === bookingData.locationType
    );
  };

  const steps: BookingStep[] = [
    {
      id: 'tutor',
      title: 'Select Tutor',
      icon: <User className="w-5 h-5" />,
      completed: currentStep > 0,
    },
    {
      id: 'details',
      title: 'Session Details',
      icon: <Calendar className="w-5 h-5" />,
      completed: currentStep > 1,
    },
    {
      id: 'location',
      title: 'Location',
      icon: <MapPin className="w-5 h-5" />,
      completed: currentStep > 2,
    },
    {
      id: 'payment',
      title: 'Payment',
      icon: <CreditCard className="w-5 h-5" />,
      completed: currentStep > 3,
    },
    {
      id: 'confirm',
      title: 'Confirm',
      icon: <Check className="w-5 h-5" />,
      completed: false,
    },
  ];

  const fetchTutors = async () => {
    try {
      setLoadingTutors(true);
      // Fetch tutors who teach the selected subject if available
      const response = await tutorService.searchTutors({
        is_active: true,
        subject_area: bookingData.subject || undefined,
        limit: 20
      });
      setTutors(response.items.map(tutor => ({
        id: tutor.tutor_id.toString(),
        name: `${tutor.first_name} ${tutor.last_name}`,
        email: tutor.email,
        avatar: tutor.profile_picture || '/avatars/default.jpg',
        role: tutor.specializations?.join(', ') || 'Tutor',
        status: 'online' as const,
        location: `${tutor.city || 'Unknown'}, ${tutor.province || 'QC'}`,
        hourlyRate: tutor.hourly_rate
      })));
    } catch (error) {
      console.error('Error fetching tutors:', error);
      toast.error(t('appointments.errors.fetchTutors'));
    } finally {
      setLoadingTutors(false);
    }
  };

  // Refresh tutors when subject changes
  useEffect(() => {
    if (bookingData.subject) {
      fetchTutors();
    }
  }, [bookingData.subject]);

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      // Submit booking
      handleSubmit();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    try {
      setSubmitting(true);
      
      // Validate required fields
      if (!bookingData.tutorId || !bookingData.subject || !bookingData.date || !bookingData.time) {
        toast.error(t('appointments.errors.requiredFields'));
        return;
      }

      // Calculate end time based on duration
      const startTime = bookingData.time;
      const [hours, minutes] = startTime.split(':').map(Number);
      const durationMinutes = parseInt(bookingData.duration);
      const endHours = Math.floor((hours * 60 + minutes + durationMinutes) / 60);
      const endMinutes = (hours * 60 + minutes + durationMinutes) % 60;
      const endTime = `${endHours.toString().padStart(2, '0')}:${endMinutes.toString().padStart(2, '0')}`;

      // Check for conflicts first
      setCheckingConflicts(true);
      const conflictCheck = await appointmentService.checkConflicts({
        tutor_id: parseInt(bookingData.tutorId),
        proposed_date: bookingData.date,
        proposed_start: startTime,
        proposed_end: endTime,
        include_buffer: true
      });

      if (conflictCheck.has_conflict) {
        toast.error(conflictCheck.message || t('appointments.errors.conflict'));
        setCheckingConflicts(false);
        return;
      }
      setCheckingConflicts(false);

      // Create appointment
      const selectedPreset = getSelectedPreset();
      const appointment = await appointmentService.createAppointment({
        tutor_id: parseInt(bookingData.tutorId),
        client_id: user?.userId || 0,
        scheduled_date: bookingData.date,
        start_time: startTime,
        end_time: endTime,
        subject_area: bookingData.subject,
        session_type: bookingData.locationType === LocationType.ONLINE ? PackageType.INDIVIDUAL : PackageType.INDIVIDUAL,
        location_type: bookingData.locationType as LocationType,
        location_details: bookingData.locationDetails || undefined,
        notes: bookingData.notes || undefined,
        send_notifications: true,
        require_confirmation: true
      });

      toast.success(t('appointments.bookingSuccess'));
      navigate(`/appointments/${appointment.appointment_id}`);
    } catch (error) {
      console.error('Error creating appointment:', error);
      toast.error(t('appointments.errors.createFailed'));
    } finally {
      setSubmitting(false);
      setCheckingConflicts(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-text-primary mb-4">
              Choose your tutor
            </h3>
            <div className="grid gap-4">
              {loadingTutors ? (
                <div className="flex items-center justify-center py-8">
                  <LoadingSpinner size="lg" />
                </div>
              ) : tutors.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <User className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                  <p>{t('appointments.noTutorsAvailable')}</p>
                </div>
              ) : (
                tutors.map((tutor) => (
                <UserCard
                  key={tutor.id}
                  user={tutor}
                  variant="compact"
                  onClick={() => setBookingData({ ...bookingData, tutorId: tutor.id })}
                  className={bookingData.tutorId === tutor.id ? 'border-accent-red bg-red-50' : ''}
                />
                ))
              )}
            </div>
          </div>
        );

      case 1:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-text-primary">
              Session Details
            </h3>
            <FormSection>
              <FormField label="Subject" required>
                <Select
                  value={bookingData.subject}
                  onChange={(value) => setBookingData({ ...bookingData, subject: value })}
                  options={[
                    { value: 'mathematics', label: 'Mathematics' },
                    { value: 'english', label: 'English' },
                    { value: 'french', label: 'French' },
                    { value: 'science', label: 'Science' },
                  ]}
                  placeholder="Select a subject"
                />
              </FormField>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField label="Date" required>
                  <DatePicker
                    value={bookingData.date}
                    onChange={(date) => setBookingData({ ...bookingData, date })}
                    minDate={new Date()}
                  />
                </FormField>

                <FormField label="Time" required>
                  <TimePicker
                    value={bookingData.time}
                    onChange={(time) => setBookingData({ ...bookingData, time })}
                  />
                </FormField>
              </div>

              <FormField label="Service Type" required>
                <Select
                  value={bookingData.locationType}
                  onChange={(value) => setBookingData({ ...bookingData, locationType: value })}
                  options={[
                    { value: 'online', label: 'Online' },
                    { value: 'in_person', label: 'In Person' },
                    { value: 'library', label: 'Library' },
                    { value: 'hybrid', label: 'Hybrid' },
                  ]}
                />
              </FormField>

              <FormField label="Duration" required>
                {loadingPresets ? (
                  <div className="flex items-center justify-center p-3">
                    <LoadingSpinner size="sm" />
                  </div>
                ) : (
                  <Select
                    value={bookingData.duration}
                    onChange={(value) => setBookingData({ ...bookingData, duration: value })}
                    options={getDurationOptions()}
                  />
                )}
              </FormField>
            </FormSection>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-text-primary">
              Location
            </h3>
            <FormSection>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {['online', 'in_person', 'library'].map((type) => (
                  <button
                    key={type}
                    onClick={() => setBookingData({ ...bookingData, locationType: type })}
                    className={`p-4 rounded-xl border-2 transition-all ${
                      bookingData.locationType === type
                        ? 'border-accent-red bg-red-50'
                        : 'border-border-primary hover:border-gray-300'
                    }`}
                  >
                    <div className="text-center">
                      {type === 'online' && <Calendar className="w-8 h-8 mx-auto mb-2 text-accent-red" />}
                      {type === 'in_person' && <MapPin className="w-8 h-8 mx-auto mb-2 text-accent-red" />}
                      {type === 'library' && <User className="w-8 h-8 mx-auto mb-2 text-accent-red" />}
                      <p className="font-medium capitalize">{type.replace('_', ' ')}</p>
                    </div>
                  </button>
                ))}
              </div>

              {bookingData.locationType !== 'online' && (
                <FormField label="Location Details" required>
                  <Input
                    value={bookingData.locationDetails}
                    onChange={(e) => setBookingData({ ...bookingData, locationDetails: e.target.value })}
                    placeholder="Enter address or location details"
                  />
                </FormField>
              )}

              <FormField label="Additional Notes">
                <textarea
                  value={bookingData.notes}
                  onChange={(e) => setBookingData({ ...bookingData, notes: e.target.value })}
                  className="w-full px-4 py-3 border border-border-primary rounded-xl focus:outline-none focus:ring-2 focus:ring-accent-red resize-none"
                  rows={4}
                  placeholder="Any special requirements or topics to cover..."
                />
              </FormField>
            </FormSection>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-text-primary">
              Payment Method
            </h3>
            <div className="grid gap-4">
              {[
                { id: 'card', label: 'Credit/Debit Card', icon: <CreditCard /> },
                { id: 'subscription', label: 'Use Subscription Hours', icon: <Clock /> },
              ].map((method) => (
                <button
                  key={method.id}
                  onClick={() => setBookingData({ ...bookingData, paymentMethod: method.id })}
                  className={`p-4 rounded-xl border-2 transition-all flex items-center gap-4 ${
                    bookingData.paymentMethod === method.id
                      ? 'border-accent-red bg-red-50'
                      : 'border-border-primary hover:border-gray-300'
                  }`}
                >
                  <span className="text-accent-red">{method.icon}</span>
                  <span className="font-medium">{method.label}</span>
                </button>
              ))}
            </div>

            {bookingData.paymentMethod === 'subscription' && (
              <Card className="bg-yellow-50 border-yellow-200">
                <div className="p-4 flex items-start gap-3">
                  <Clock className="w-5 h-5 text-yellow-600 flex-shrink-0 mt-0.5" />
                  <div>
                    <p className="font-medium text-yellow-900">Subscription Hours</p>
                    <p className="text-sm text-yellow-700 mt-1">
                      You have 12 hours remaining in your subscription
                    </p>
                  </div>
                </div>
              </Card>
            )}
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-text-primary">
              Confirm Your Booking
            </h3>
            <Card>
              <div className="p-6 space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-text-secondary">Tutor</span>
                  <span className="font-medium">
                    {tutors.find(t => t.id === bookingData.tutorId)?.name}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-text-secondary">Subject</span>
                  <span className="font-medium capitalize">{bookingData.subject}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-text-secondary">Date & Time</span>
                  <span className="font-medium">
                    {bookingData.date} at {bookingData.time}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-text-secondary">Duration</span>
                  <span className="font-medium">{bookingData.duration} minutes</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-text-secondary">Location</span>
                  <Badge variant="info" size="sm">
                    {bookingData.locationType.replace('_', ' ')}
                  </Badge>
                </div>
                <div className="border-t pt-4 mt-4">
                  <div className="flex items-center justify-between">
                    <span className="text-lg font-semibold">Total</span>
                    <span className="text-lg font-semibold text-accent-red">
                      ${getSelectedPreset()?.base_rate || 60}.00
                    </span>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Progress Steps */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => (
            <React.Fragment key={step.id}>
              <div className="flex flex-col items-center">
                <button
                  onClick={() => index < currentStep && setCurrentStep(index)}
                  disabled={index > currentStep}
                  className={`w-12 h-12 rounded-full flex items-center justify-center transition-all ${
                    index === currentStep
                      ? 'bg-accent-red text-white shadow-soft'
                      : step.completed
                      ? 'bg-green-500 text-white'
                      : 'bg-gray-200 text-gray-400'
                  } ${index < currentStep ? 'cursor-pointer hover:shadow-elevated' : ''}`}
                >
                  {step.completed ? <Check className="w-5 h-5" /> : step.icon}
                </button>
                <span className="text-xs mt-2 text-center hidden md:block">
                  {step.title}
                </span>
              </div>
              {index < steps.length - 1 && (
                <div className={`flex-1 h-0.5 mx-4 ${
                  step.completed ? 'bg-green-500' : 'bg-gray-200'
                }`} />
              )}
            </React.Fragment>
          ))}
        </div>
      </div>

      {/* Step Content */}
      <Card className="animate-fadeInUp">
        <div className="p-6 md:p-8">
          {renderStepContent()}
        </div>

        {/* Navigation */}
        <div className="border-t border-border-primary px-6 py-4 flex items-center justify-between">
          <Button
            variant="ghost"
            onClick={handlePrevious}
            disabled={currentStep === 0}
          >
            Previous
          </Button>
          <span className="text-sm text-text-secondary">
            Step {currentStep + 1} of {steps.length}
          </span>
          <Button
            variant="primary"
            onClick={handleNext}
            disabled={
              (currentStep === 0 && !bookingData.tutorId) ||
              (currentStep === 1 && (!bookingData.subject || !bookingData.date || !bookingData.time)) ||
              submitting || checkingConflicts
            }
            loading={submitting || checkingConflicts}
          >
            {currentStep === steps.length - 1 ? 
              (checkingConflicts ? 'Checking availability...' : 
               submitting ? 'Creating booking...' : 'Confirm Booking') : 
              'Next'
            }
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default BookingFlow;