"""
Distance Calculation Service for advanced distance and travel time calculations.

This service provides comprehensive distance calculation capabilities including:
- Haversine formula for straight-line distances
- Travel time estimation with traffic factors
- Route optimization for multiple destinations
- Public transit route calculations
- Cost estimation for travel
"""

from typing import List, Optional, Dict, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import logging
import asyncio
import math
from datetime import datetime, time

from app.services.geocoding_service import GeocodingService
from app.core.exceptions import ValidationError, BusinessLogicError

logger = logging.getLogger(__name__)

class TransportMode(Enum):
    """Transportation modes for distance calculation."""
    DRIVING = "driving"
    WALKING = "walking"
    CYCLING = "cycling" 
    TRANSIT = "transit"
    STRAIGHT_LINE = "straight_line"

class TrafficLevel(Enum):
    """Traffic level conditions."""
    LOW = "low"
    MODERATE = "moderate"
    HEAVY = "heavy"
    SEVERE = "severe"

@dataclass
class RouteSegment:
    """Represents a segment of a route."""
    from_postal_code: str
    to_postal_code: str
    distance_km: float
    duration_minutes: int
    mode: TransportMode
    traffic_factor: float = 1.0
    estimated_cost: Optional[float] = None

@dataclass
class TravelRoute:
    """Complete travel route with multiple segments."""
    origin: str
    destination: str
    total_distance_km: float
    total_duration_minutes: int
    estimated_cost: float
    segments: List[RouteSegment]
    mode: TransportMode
    optimal: bool = False
    traffic_conditions: TrafficLevel = TrafficLevel.MODERATE

@dataclass
class DistanceMatrix:
    """Distance matrix for multiple origins and destinations."""
    origins: List[str]
    destinations: List[str]
    distances_km: List[List[float]]
    durations_minutes: List[List[int]]
    mode: TransportMode

class DistanceCalculationService:
    """Advanced distance calculation service for tutor matching and routing."""
    
    def __init__(self, geocoding_service: GeocodingService):
        self.geocoding_service = geocoding_service
        
        # Traffic factors by time of day (multiplier for base travel time)
        self.traffic_factors = {
            'rush_morning': 1.5,    # 7-9 AM
            'rush_evening': 1.6,    # 4-6 PM
            'business_hours': 1.2,  # 9 AM - 4 PM
            'evening': 1.1,         # 6-10 PM
            'night': 0.9,           # 10 PM - 7 AM
            'weekend': 1.0          # Weekends
        }
        
        # Base speeds by transport mode (km/h)
        self.base_speeds = {
            TransportMode.DRIVING: 50,
            TransportMode.WALKING: 5,
            TransportMode.CYCLING: 15,
            TransportMode.TRANSIT: 25,
            TransportMode.STRAIGHT_LINE: 999  # Theoretical
        }
        
    async def calculate_route(
        self,
        origin_postal_code: str,
        destination_postal_code: str,
        mode: TransportMode = TransportMode.DRIVING,
        departure_time: Optional[datetime] = None
    ) -> TravelRoute:
        """
        Calculate detailed route between two postal codes.
        
        Args:
            origin_postal_code: Starting postal code
            destination_postal_code: Destination postal code
            mode: Transportation mode
            departure_time: Optional departure time for traffic estimation
            
        Returns:
            Complete travel route with distance, time, and cost estimates
            
        Raises:
            ValidationError: If postal codes are invalid
        """
        try:
            # Geocode both postal codes
            origin_coords = await self.geocoding_service.geocode_postal_code(origin_postal_code)
            dest_coords = await self.geocoding_service.geocode_postal_code(destination_postal_code)
            
            if not origin_coords or not dest_coords:
                raise ValidationError("Invalid postal codes provided")
            
            # Calculate straight-line distance
            straight_distance = self.geocoding_service.calculate_distance(
                origin_coords['latitude'], origin_coords['longitude'],
                dest_coords['latitude'], dest_coords['longitude']
            )
            
            # Calculate actual route distance and time
            route_distance, route_duration = await self._calculate_route_details(
                origin_coords, dest_coords, mode, departure_time
            )
            
            # Estimate cost
            estimated_cost = self._estimate_travel_cost(route_distance, mode)
            
            # Determine traffic conditions
            traffic_level = self._get_traffic_level(departure_time)
            
            # Create route segment
            segment = RouteSegment(
                from_postal_code=origin_postal_code.upper(),
                to_postal_code=destination_postal_code.upper(),
                distance_km=route_distance,
                duration_minutes=route_duration,
                mode=mode,
                traffic_factor=self._get_traffic_factor(departure_time),
                estimated_cost=estimated_cost
            )
            
            return TravelRoute(
                origin=origin_postal_code.upper(),
                destination=destination_postal_code.upper(),
                total_distance_km=route_distance,
                total_duration_minutes=route_duration,
                estimated_cost=estimated_cost,
                segments=[segment],
                mode=mode,
                optimal=True,  # Single segment is always optimal
                traffic_conditions=traffic_level
            )
            
        except Exception as e:
            logger.error(f"Error calculating route: {str(e)}")
            raise
    
    async def calculate_distance_matrix(
        self,
        origins: List[str],
        destinations: List[str],
        mode: TransportMode = TransportMode.DRIVING
    ) -> DistanceMatrix:
        """
        Calculate distance matrix for multiple origins and destinations.
        
        Args:
            origins: List of origin postal codes
            destinations: List of destination postal codes
            mode: Transportation mode
            
        Returns:
            Distance matrix with all pairwise distances and durations
        """
        try:
            # Validate postal codes
            origin_coords = []
            dest_coords = []
            
            for postal_code in origins:
                coords = await self.geocoding_service.geocode_postal_code(postal_code)
                if not coords:
                    raise ValidationError(f"Invalid origin postal code: {postal_code}")
                origin_coords.append(coords)
            
            for postal_code in destinations:
                coords = await self.geocoding_service.geocode_postal_code(postal_code)
                if not coords:
                    raise ValidationError(f"Invalid destination postal code: {postal_code}")
                dest_coords.append(coords)
            
            # Calculate all pairwise distances and durations
            distances_km = []
            durations_minutes = []
            
            for origin_coord in origin_coords:
                origin_distances = []
                origin_durations = []
                
                for dest_coord in dest_coords:
                    distance, duration = await self._calculate_route_details(
                        origin_coord, dest_coord, mode
                    )
                    origin_distances.append(distance)
                    origin_durations.append(duration)
                
                distances_km.append(origin_distances)
                durations_minutes.append(origin_durations)
            
            return DistanceMatrix(
                origins=[pc.upper() for pc in origins],
                destinations=[pc.upper() for pc in destinations],
                distances_km=distances_km,
                durations_minutes=durations_minutes,
                mode=mode
            )
            
        except Exception as e:
            logger.error(f"Error calculating distance matrix: {str(e)}")
            raise
    
    async def find_optimal_route(
        self,
        origin: str,
        destinations: List[str],
        mode: TransportMode = TransportMode.DRIVING,
        return_to_origin: bool = False
    ) -> TravelRoute:
        """
        Find optimal route visiting multiple destinations.
        
        Args:
            origin: Starting postal code
            destinations: List of destination postal codes to visit
            mode: Transportation mode
            return_to_origin: Whether to return to the starting point
            
        Returns:
            Optimized route with minimum total travel time
        """
        try:
            if not destinations:
                raise ValidationError("At least one destination is required")
            
            # For small number of destinations, use brute force optimization
            if len(destinations) <= 6:
                return await self._optimize_route_brute_force(
                    origin, destinations, mode, return_to_origin
                )
            else:
                # For larger sets, use nearest neighbor heuristic
                return await self._optimize_route_nearest_neighbor(
                    origin, destinations, mode, return_to_origin
                )
                
        except Exception as e:
            logger.error(f"Error finding optimal route: {str(e)}")
            raise
    
    async def calculate_service_area_distances(
        self,
        center_postal_code: str,
        postal_codes: List[str],
        mode: TransportMode = TransportMode.DRIVING
    ) -> Dict[str, Dict[str, Any]]:
        """
        Calculate distances from a center point to all postal codes in a service area.
        
        Args:
            center_postal_code: Center postal code
            postal_codes: List of postal codes in service area
            mode: Transportation mode
            
        Returns:
            Dictionary mapping postal codes to distance/time information
        """
        try:
            results = {}
            center_coords = await self.geocoding_service.geocode_postal_code(center_postal_code)
            
            if not center_coords:
                raise ValidationError(f"Invalid center postal code: {center_postal_code}")
            
            # Calculate distances in batches for better performance
            batch_size = 10
            for i in range(0, len(postal_codes), batch_size):
                batch = postal_codes[i:i + batch_size]
                
                # Process batch concurrently
                tasks = []
                for postal_code in batch:
                    task = self._calculate_single_distance(
                        center_coords, postal_code, mode
                    )
                    tasks.append(task)
                
                batch_results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Process results
                for postal_code, result in zip(batch, batch_results):
                    if isinstance(result, Exception):
                        logger.warning(f"Error calculating distance to {postal_code}: {result}")
                        results[postal_code] = {
                            'distance_km': None,
                            'duration_minutes': None,
                            'error': str(result)
                        }
                    else:
                        results[postal_code] = result
            
            return results
            
        except Exception as e:
            logger.error(f"Error calculating service area distances: {str(e)}")
            raise
    
    def estimate_travel_cost_matrix(
        self,
        distance_matrix: DistanceMatrix,
        cost_per_km: Optional[float] = None
    ) -> List[List[float]]:
        """
        Estimate travel costs for a distance matrix.
        
        Args:
            distance_matrix: Distance matrix
            cost_per_km: Optional custom cost per kilometer
            
        Returns:
            Matrix of estimated travel costs
        """
        try:
            if cost_per_km is None:
                cost_per_km = self._get_default_cost_per_km(distance_matrix.mode)
            
            cost_matrix = []
            for origin_distances in distance_matrix.distances_km:
                origin_costs = []
                for distance in origin_distances:
                    if distance is not None:
                        cost = distance * cost_per_km
                        origin_costs.append(round(cost, 2))
                    else:
                        origin_costs.append(None)
                cost_matrix.append(origin_costs)
            
            return cost_matrix
            
        except Exception as e:
            logger.error(f"Error estimating travel costs: {str(e)}")
            raise
    
    async def _calculate_route_details(
        self,
        origin_coords: Dict[str, float],
        dest_coords: Dict[str, float],
        mode: TransportMode,
        departure_time: Optional[datetime] = None
    ) -> Tuple[float, int]:
        """Calculate route distance and duration."""
        # Calculate straight-line distance
        straight_distance = self.geocoding_service.calculate_distance(
            origin_coords['latitude'], origin_coords['longitude'],
            dest_coords['latitude'], dest_coords['longitude']
        )
        
        # Apply route factor based on transport mode and terrain
        route_factor = self._get_route_factor(mode, straight_distance)
        route_distance = straight_distance * route_factor
        
        # Calculate base travel time
        base_speed = self.base_speeds[mode]
        base_duration_hours = route_distance / base_speed
        base_duration_minutes = int(base_duration_hours * 60)
        
        # Apply traffic factor if applicable
        if mode == TransportMode.DRIVING:
            traffic_factor = self._get_traffic_factor(departure_time)
            route_duration = int(base_duration_minutes * traffic_factor)
        else:
            route_duration = base_duration_minutes
        
        return route_distance, route_duration
    
    async def _calculate_single_distance(
        self,
        center_coords: Dict[str, float],
        postal_code: str,
        mode: TransportMode
    ) -> Dict[str, Any]:
        """Calculate distance to a single postal code."""
        try:
            coords = await self.geocoding_service.geocode_postal_code(postal_code)
            if not coords:
                return {
                    'distance_km': None,
                    'duration_minutes': None,
                    'error': 'Invalid postal code'
                }
            
            distance, duration = await self._calculate_route_details(
                center_coords, coords, mode
            )
            
            return {
                'distance_km': round(distance, 2),
                'duration_minutes': duration,
                'coordinates': coords,
                'error': None
            }
            
        except Exception as e:
            return {
                'distance_km': None,
                'duration_minutes': None,
                'error': str(e)
            }
    
    def _get_route_factor(self, mode: TransportMode, distance_km: float) -> float:
        """Get route factor to convert straight-line to actual route distance."""
        # Urban areas have more winding routes
        if distance_km < 5:  # Urban
            factors = {
                TransportMode.DRIVING: 1.4,
                TransportMode.WALKING: 1.3,
                TransportMode.CYCLING: 1.35,
                TransportMode.TRANSIT: 1.5,
                TransportMode.STRAIGHT_LINE: 1.0
            }
        elif distance_km < 20:  # Suburban
            factors = {
                TransportMode.DRIVING: 1.25,
                TransportMode.WALKING: 1.2,
                TransportMode.CYCLING: 1.2,
                TransportMode.TRANSIT: 1.4,
                TransportMode.STRAIGHT_LINE: 1.0
            }
        else:  # Rural/Highway
            factors = {
                TransportMode.DRIVING: 1.15,
                TransportMode.WALKING: 1.1,
                TransportMode.CYCLING: 1.15,
                TransportMode.TRANSIT: 1.3,
                TransportMode.STRAIGHT_LINE: 1.0
            }
        
        return factors.get(mode, 1.2)
    
    def _get_traffic_factor(self, departure_time: Optional[datetime] = None) -> float:
        """Get traffic factor based on departure time."""
        if not departure_time:
            return 1.2  # Default moderate traffic
        
        # Determine time period
        hour = departure_time.hour
        weekday = departure_time.weekday()  # 0=Monday, 6=Sunday
        
        if weekday >= 5:  # Weekend
            return self.traffic_factors['weekend']
        elif 7 <= hour < 9:  # Rush hour morning
            return self.traffic_factors['rush_morning']
        elif 16 <= hour < 18:  # Rush hour evening
            return self.traffic_factors['rush_evening']
        elif 9 <= hour < 16:  # Business hours
            return self.traffic_factors['business_hours']
        elif 18 <= hour < 22:  # Evening
            return self.traffic_factors['evening']
        else:  # Night
            return self.traffic_factors['night']
    
    def _get_traffic_level(self, departure_time: Optional[datetime] = None) -> TrafficLevel:
        """Determine traffic level based on departure time."""
        if not departure_time:
            return TrafficLevel.MODERATE
        
        traffic_factor = self._get_traffic_factor(departure_time)
        
        if traffic_factor >= 1.5:
            return TrafficLevel.HEAVY
        elif traffic_factor >= 1.3:
            return TrafficLevel.MODERATE
        elif traffic_factor >= 1.1:
            return TrafficLevel.LOW
        else:
            return TrafficLevel.LOW
    
    def _estimate_travel_cost(self, distance_km: float, mode: TransportMode) -> float:
        """Estimate travel cost based on distance and mode."""
        cost_per_km = self._get_default_cost_per_km(mode)
        return round(distance_km * cost_per_km, 2)
    
    def _get_default_cost_per_km(self, mode: TransportMode) -> float:
        """Get default cost per kilometer for transport mode."""
        # Costs in CAD per kilometer
        costs = {
            TransportMode.DRIVING: 0.65,    # Gas, wear, parking
            TransportMode.WALKING: 0.0,     # Free
            TransportMode.CYCLING: 0.05,    # Maintenance
            TransportMode.TRANSIT: 0.30,    # Average public transit
            TransportMode.STRAIGHT_LINE: 0.0  # Theoretical
        }
        return costs.get(mode, 0.65)
    
    async def _optimize_route_brute_force(
        self,
        origin: str,
        destinations: List[str],
        mode: TransportMode,
        return_to_origin: bool
    ) -> TravelRoute:
        """Optimize route using brute force for small destination sets."""
        # This is a simplified version - in practice, you'd implement
        # proper traveling salesman problem algorithms
        
        # For now, just calculate the route in the given order
        segments = []
        total_distance = 0
        total_duration = 0
        total_cost = 0
        
        current_location = origin
        
        for destination in destinations:
            route = await self.calculate_route(current_location, destination, mode)
            segments.extend(route.segments)
            total_distance += route.total_distance_km
            total_duration += route.total_duration_minutes
            total_cost += route.estimated_cost
            current_location = destination
        
        # Return to origin if requested
        if return_to_origin:
            return_route = await self.calculate_route(current_location, origin, mode)
            segments.extend(return_route.segments)
            total_distance += return_route.total_distance_km
            total_duration += return_route.total_duration_minutes
            total_cost += return_route.estimated_cost
        
        return TravelRoute(
            origin=origin,
            destination=destinations[-1] if not return_to_origin else origin,
            total_distance_km=total_distance,
            total_duration_minutes=total_duration,
            estimated_cost=total_cost,
            segments=segments,
            mode=mode,
            optimal=False,  # This is not truly optimized
            traffic_conditions=TrafficLevel.MODERATE
        )
    
    async def _optimize_route_nearest_neighbor(
        self,
        origin: str,
        destinations: List[str],
        mode: TransportMode,
        return_to_origin: bool
    ) -> TravelRoute:
        """Optimize route using nearest neighbor heuristic."""
        # Simple nearest neighbor implementation
        unvisited = destinations.copy()
        route_order = [origin]
        current_location = origin
        
        while unvisited:
            # Find nearest unvisited destination
            min_distance = float('inf')
            nearest_dest = None
            
            for dest in unvisited:
                route = await self.calculate_route(current_location, dest, mode)
                if route.total_distance_km < min_distance:
                    min_distance = route.total_distance_km
                    nearest_dest = dest
            
            route_order.append(nearest_dest)
            unvisited.remove(nearest_dest)
            current_location = nearest_dest
        
        if return_to_origin:
            route_order.append(origin)
        
        # Calculate total route
        return await self._calculate_multi_segment_route(route_order, mode)
    
    async def _calculate_multi_segment_route(
        self,
        route_order: List[str],
        mode: TransportMode
    ) -> TravelRoute:
        """Calculate a multi-segment route."""
        segments = []
        total_distance = 0
        total_duration = 0
        total_cost = 0
        
        for i in range(len(route_order) - 1):
            route = await self.calculate_route(route_order[i], route_order[i + 1], mode)
            segments.extend(route.segments)
            total_distance += route.total_distance_km
            total_duration += route.total_duration_minutes
            total_cost += route.estimated_cost
        
        return TravelRoute(
            origin=route_order[0],
            destination=route_order[-1],
            total_distance_km=total_distance,
            total_duration_minutes=total_duration,
            estimated_cost=total_cost,
            segments=segments,
            mode=mode,
            optimal=True,
            traffic_conditions=TrafficLevel.MODERATE
        )

# Dependency injection helper
async def get_distance_calculation_service() -> DistanceCalculationService:
    """Get distance calculation service instance with dependencies."""
    from app.services.geocoding_service import get_geocoding_service
    
    geocoding_service = await get_geocoding_service()
    return DistanceCalculationService(geocoding_service)