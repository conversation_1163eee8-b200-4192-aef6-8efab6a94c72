{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "save": "Save", "saving": "Saving...", "cancel": "Cancel", "back": "Back", "copiedToClipboard": "Copied to clipboard", "delete": "Delete", "edit": "Edit", "create": "Create", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "refresh": "Refresh", "actions": "Actions", "status": "Status", "date": "Date", "time": "Time", "name": "Name", "email": "Email", "phone": "Phone", "address": "Address", "description": "Description", "notes": "Notes", "noData": "No data available", "confirmDelete": "Are you sure you want to delete this item?", "yes": "Yes", "no": "No", "switch_to": "Switch to", "all": "All", "view": "View", "download": "Download", "filters": "Filters", "clearFilters": "Clear Filters", "showing": "Showing", "to": "to", "of": "of", "results": "results", "previous": "Previous", "next": "Next", "days": {"sunday": "Sunday", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday"}, "minutes": "minutes", "hours": "hours", "today": "Today", "yesterday": "Yesterday", "daysAgo": "days ago", "more": "more", "select": "Select", "ago": "ago", "optional": "Optional", "add": "Add", "comingSoon": "Coming Soon", "viewProgress": "View Progress", "viewFullProfile": "View Full Profile"}, "sidebar": {"search": "Search", "users": "Users", "clients": "Clients", "tutors": "Tutors", "dependants": "Dependants", "tutors_management": "Tutor Management", "calendar": "Calendar", "map": "Tutor <PERSON>", "appointments": "Appointments", "billing": "Billing", "invoices": "Invoices", "tutorPayments": "Tutor Payments", "subscriptions": "Subscriptions", "packages": "Packages", "reports": "Reports", "financial": "Financial", "performance": "Performance", "analytics": "Analytics", "usage": "Usage", "monthly": "Monthly", "messages": "Messages", "smsThreads": "SMS Threads", "inAppChat": "In-<PERSON><PERSON>", "templates": "Templates", "broadcasts": "Broadcasts", "settings": "Settings", "system": "System", "userSettings": "Users", "payments": "Payments", "notifications": "Notifications", "api": "API", "services": "Service Settings", "ourServices": "Our Services", "paymentHistory": "Payment History"}, "header": {"welcome": "Welcome, {{name}}", "profile": "Profile", "settings": "Settings", "help": "Help", "logout": "Logout", "activeRole": "Active Role", "switchRole": "Switch Role"}, "roles": {"manager": "Manager", "tutor": "Tutor", "client": "Client"}, "search": {"searchButton": "Search", "placeholder": "Search for clients, tutors, appointments...", "searching": "Searching...", "noResults": "No results found", "recent": "Recent Searches", "shortcuts": "Keyboard Shortcuts"}, "quickActions": {"addClient": "Add Client", "addTutor": "<PERSON><PERSON><PERSON>", "bookSession": "Book Session", "createInvoice": "Create Invoice", "sendMessage": "Send Message", "findTutor": "<PERSON>"}, "auth": {"login": "<PERSON><PERSON>", "loginTitle": "Sign in to your account", "email": "Email address", "orContinueWith": "Or continue with", "continueWithGoogle": "Continue with Google", "password": "Password", "rememberMe": "Remember me", "noAccount": "Don't have an account?", "signUp": "Sign up", "loginError": "Invalid email or password", "loginSuccess": "Login successful", "forgotPassword": {"title": "Forgot Password?", "subtitle": "Enter your email and we'll send you a reset link", "emailLabel": "Email Address", "emailPlaceholder": "Enter your email address", "emailRequired": "Email address is required", "invalidEmail": "Please enter a valid email address", "sendResetLink": "Send Reset Link", "sending": "Sending...", "emailSentTitle": "Check Your Email", "emailSentMessage": "If an account exists for {{email}}, we've sent password reset instructions.", "checkSpam": "If you don't see the email, please check your spam folder.", "backToLogin": "Back to Login", "rememberPassword": "Remember your password?", "signIn": "Sign In", "tooManyRequests": "Too many requests. Please try again later."}, "resetPassword": {"title": "Reset Password", "subtitle": "Enter your new password below", "modalDescription": "Enter your email address and we'll send you a link to reset your password.", "emailRequired": "Please enter your email address", "emailNotFound": "No account found with this email address", "sendError": "Failed to send reset email. Please try again.", "emailSent": "Password reset email sent successfully", "emailSentTitle": "Check Your Email", "emailSentDescription": "We've sent password reset instructions to {{email}}", "emailPlaceholder": "Enter your email", "newPassword": "New Password", "confirmPassword": "Confirm Password", "passwordPlaceholder": "Enter new password", "confirmPasswordPlaceholder": "Confirm new password", "passwordTooShort": "Password must be at least 8 characters", "passwordRequirements": "Password must contain uppercase, lowercase, and a number", "passwordMismatch": "Passwords do not match", "allFieldsRequired": "All fields are required", "invalidToken": "Invalid or missing reset token", "tokenExpired": "This password reset link has expired", "tokenInvalid": "This password reset link is invalid", "verifyingToken": "Verifying reset token...", "invalidTokenTitle": "Invalid Reset Link", "backToLogin": "Back to Login", "resetPassword": "Reset Password", "resetting": "Resetting...", "resetError": "Failed to reset password. Please try again.", "success": "Password reset successfully", "successTitle": "Password Reset Complete", "successMessage": "Your password has been reset successfully. You can now log in with your new password.", "redirecting": "Redirecting to login...", "sendEmail": "Send Reset Email", "sending": "Sending...", "securityNote": "For security, password reset links expire after 24 hours."}, "passwordStrength": {"minLength": "At least 8 characters", "uppercase": "One uppercase letter", "lowercase": "One lowercase letter", "number": "One number", "strengthWeak": "Weak", "strengthFair": "Fair", "strengthGood": "Good", "strengthStrong": "Strong"}, "twoFactor": {"title": "Two-Factor Authentication", "selectMethod": "Select verification method", "enterCode": "Enter verification code", "invalidCode": "Invalid verification code", "tooManyAttempts": "Too many attempts. Please try again later.", "verificationError": "Verification failed. Please try again.", "verificationSuccess": "Verification successful", "verify": "Verify", "verifying": "Verifying...", "resendCode": "Resend code", "resending": "Resending...", "trustDevice": "Trust this device for 30 days", "havingTrouble": "Having trouble?", "useBackupCode": "Use a backup code", "smsSent": "Code sent to your phone", "emailSent": "Code sent to your email", "resendError": "Failed to resend code", "methods": {"authenticator": "Authenticator App", "sms": "SMS Text Message", "email": "Email"}, "smsDescription": "We've sent a code to {{phone}}", "emailDescription": "We've sent a code to {{email}}", "totpDescription": "Use your authenticator app", "smsHint": "Code will be sent to {{phone}}", "emailHint": "Code will be sent to {{email}}", "authenticatorTitle": "Two-Factor Authentication", "authenticatorDescription": "Enter the code from your authenticator app", "smsTitle": "SMS Verification", "emailTitle": "Email Verification", "smsDescriptionGeneric": "We've sent a code to your phone", "emailDescriptionGeneric": "We've sent a code to your email", "didntReceive": "Didn't receive the code?", "resendIn": "Resend in {{seconds}}s", "codeSent": "Verification code sent", "subtitle": "Add an extra layer of security to your account", "statusEnabled": "Two-factor authentication is enabled", "statusDescription": "Your account is protected with an additional security layer", "preferred": "Preferred", "setup": "Set Up", "disable": "Disable", "makePreferred": "Make Preferred", "setupTitle": "Set up {{method}}", "scanQRCode": "Scan this QR code with your authenticator app", "manualEntry": "Or enter this code manually:", "smsVerification": "We'll send verification codes to your registered phone number", "emailVerification": "We'll send verification codes to your registered email address", "enableMethod": "Enable Method", "methodEnabled": "Two-factor method enabled successfully", "methodDisabled": "{{method}} disabled", "preferredMethodSet": "Preferred method updated", "verificationFailed": "Verification failed. Please try again.", "backupCodesTitle": "Backup Codes", "backupCodesDescription": "Save these codes in a safe place. You can use them to access your account if you lose your device.", "generateBackupCodes": "Generate Backup Codes"}, "session": {"timeoutTitle": "Session Timeout Warning", "timeoutMessage": "Your session is about to expire due to inactivity.", "timeRemaining": "Time remaining", "logout": "Log Out", "stayLoggedIn": "Stay Logged In", "securityNote": "For your security, sessions expire after periods of inactivity."}}, "users": {"title": "Users", "addUser": "Add User", "editUser": "Edit User", "deleteUser": "Delete User", "userDetails": "User Details", "personalInfo": "Personal Information", "contactInfo": "Contact Information", "accountInfo": "Account Information", "firstName": "First Name", "lastName": "Last Name", "dateOfBirth": "Date of Birth", "role": "Role", "status": "Status", "active": "Active", "inactive": "Inactive", "verified": "Verified", "unverified": "Unverified", "createdAt": "Created At", "updatedAt": "Updated At", "lastLogin": "Last Login"}, "clients": {"title": "Clients", "addClient": "Add Client", "editClient": "Edit Client", "clientProfile": "Client Profile", "emergencyContact": "Emergency Contact", "emergencyContactName": "Contact Name", "emergencyContactPhone": "Contact Phone", "emergencyContactRelation": "Relationship", "communicationPreferences": "Communication Preferences", "preferredLanguage": "Preferred Language", "smsNotifications": "SMS Notifications", "emailNotifications": "Email Notifications", "pushNotifications": "Push Notifications", "internalNotes": "Internal Notes"}, "client": {"profile": {"title": "My Profile", "subtitle": "Manage your personal information and preferences", "fetchError": "Failed to load profile", "updateSuccess": "Profile updated successfully", "updateError": "Failed to update profile", "notFound": "Profile not found", "tabs": {"personal": "Personal Info", "emergency": "Emergency Contact", "communication": "Communication", "address": "Address"}, "fields": {"firstName": "First Name", "lastName": "Last Name", "dateOfBirth": "Date of Birth", "phoneNumber": "Phone Number", "email": "Email Address", "emergencyContactName": "Emergency Contact Name", "emergencyContactPhone": "Emergency Contact Phone", "emergencyContactRelation": "Relationship", "addressLine1": "Street Address", "addressLine2": "Apartment, Suite, etc.", "city": "City", "province": "Province", "postalCode": "Postal Code", "preferredLanguage": "Preferred Language"}, "placeholders": {"firstName": "Enter your first name", "lastName": "Enter your last name", "emergencyContactName": "Emergency contact full name", "selectRelationship": "Select relationship", "addressLine1": "123 Main Street", "addressLine2": "Apt 4B", "city": "Montreal", "selectProvince": "Select province"}, "errors": {"firstNameRequired": "First name is required", "lastNameRequired": "Last name is required", "invalidPhone": "Please enter a valid 10-digit phone number", "futureDateOfBirth": "Date of birth cannot be in the future", "emergencyNameRequired": "Emergency contact name is required", "emergencyPhoneRequired": "Emergency contact phone is required", "emergencyRelationRequired": "Emergency contact relationship is required", "addressRequired": "Street address is required", "cityRequired": "City is required", "provinceRequired": "Province is required", "postalCodeRequired": "Postal code is required", "invalidPostalCode": "Please enter a valid Canadian postal code"}, "relationships": {"spouse": "Spouse", "parent": "Parent", "sibling": "Sibling", "child": "Child", "friend": "Friend", "other": "Other"}, "emailCannotBeChanged": "Email cannot be changed", "emergencyContactInfo": "Emergency Contact Information", "emergencyContactDescription": "This person will be contacted in case of emergencies", "addressInfo": "Address Information", "addressDescription": "Your address helps us match you with nearby tutors", "languagePreference": "Language Preference", "languageChangeNote": "Changing your language will refresh the page", "notificationPreferences": "Notification Preferences", "notifications": {"email": {"label": "Email Notifications", "description": "Receive appointment reminders and updates via email"}, "sms": {"label": "SMS Notifications", "description": "Get text messages for urgent updates and reminders"}, "push": {"label": "Push Notifications", "description": "Receive app notifications on your device"}, "info": {"title": "You'll receive notifications for:", "appointment": "Appointment confirmations and reminders", "payment": "Payment confirmations and invoices", "message": "New messages from tutors", "update": "Important account updates"}}, "accountActions": "Account Actions", "changePassword": "Change Password", "emailVerified": "<PERSON><PERSON>"}, "dependants": {"title": "My Dependants", "subtitle": "Manage dependant profiles and information", "addDependant": "Add Dependant", "noResults": "No dependants found matching your search", "noDependants": "No dependants yet", "noDependantsDescription": "Add dependants to manage their tutoring sessions", "fetchError": "Failed to load dependants", "createSuccess": "Dependant added successfully", "updateSuccess": "Dependant updated successfully", "deleteSuccess": "Dependant removed successfully", "createError": "Failed to add dependant", "updateError": "Failed to update dependant", "deleteError": "Failed to remove dependant", "searchPlaceholder": "Search by name...", "confirmDelete": "Are you sure you want to remove this dependant?", "age": "Age {{age}}", "viewDetails": "View Details", "grade": "Grade", "specialNeeds": "Special Needs", "allergies": "Allergies", "medications": "Medications", "fields": {"firstName": "First Name", "lastName": "Last Name", "dateOfBirth": "Date of Birth", "gradeLevel": "Grade Level", "school": "School", "learningGoals": "Learning Goals", "specialNeeds": "Special Needs", "allergies": "Allergies", "medications": "Medications"}, "placeholders": {"firstName": "Enter first name", "lastName": "Enter last name", "selectGrade": "Select grade level", "school": "Enter school name", "learningGoals": "Describe learning goals and objectives...", "specialNeeds": "Any special needs or accommodations required...", "allergies": "List any allergies...", "medications": "List any medications..."}, "tabs": {"basic": "Basic Info", "education": "Education", "health": "Health & Safety"}, "errors": {"firstNameRequired": "First name is required", "lastNameRequired": "Last name is required", "dateOfBirthRequired": "Date of birth is required", "futureDateOfBirth": "Date of birth cannot be in the future", "tooOld": "Age must be 25 or under"}, "grades": {"preK": "Pre-K", "kindergarten": "Kindergarten", "grade1": "Grade 1", "grade2": "Grade 2", "grade3": "Grade 3", "grade4": "Grade 4", "grade5": "Grade 5", "grade6": "Grade 6", "grade7": "Grade 7", "grade8": "Grade 8", "grade9": "Grade 9", "grade10": "Grade 10", "grade11": "Grade 11", "grade12": "Grade 12", "college": "College", "university": "University"}, "healthInfo": "Health Information", "healthInfoDescription": "This information helps tutors provide safe and appropriate learning environments", "learningGoalsHelp": "What are the main areas you'd like tutoring to focus on?"}, "invoices": {"title": "Invoice Center", "subtitle": "View and manage your invoices and payment history", "fetchError": "Failed to load invoices", "downloadError": "Failed to download invoice", "downloadSuccess": "Invoice downloaded successfully", "tabs": {"invoices": "Invoices", "payments": "Payment History"}, "searchPlaceholder": "Search by invoice number...", "noInvoices": "No invoices yet", "noInvoicesDescription": "Your invoices will appear here after completed sessions", "invoice": "Invoice", "download": "Download", "payNow": "Pay Now", "thankYou": "Thank you for your business!", "billingInfo": "Billing Information", "invoiceDetails": "Invoice Details", "invoiceNumber": "Invoice Number", "issueDate": "Issue Date", "dueDate": "Due Date", "paidDate": "Paid <PERSON>", "paymentMethod": "Payment Method", "paidBy": "<PERSON><PERSON>", "parent1": "Parent 1", "parent2": "Parent 2", "lineItems": "Line Items", "description": "Description", "quantity": "Quantity", "rate": "Rate", "amount": "Amount", "subtotal": "Subtotal", "tax": "Tax", "total": "Total", "tutor": "Tutor", "noLineItems": "No line items available", "status": {"all": "All Statuses", "pending": "Pending", "paid": "Paid", "overdue": "Overdue", "cancelled": "Cancelled"}, "dateRange": {"all": "All Time", "last30": "Last 30 Days", "last60": "Last 60 Days", "last90": "Last 90 Days"}, "table": {"invoiceNumber": "Invoice #", "issueDate": "Issue Date", "dueDate": "Due Date", "amount": "Amount", "status": "Status", "paidBy": "<PERSON><PERSON>", "actions": "Actions"}, "summary": {"outstanding": "Outstanding Balance", "totalPaid": "Total Paid", "nextDue": "Next Payment Due", "overdueCount": "{{count}} overdue invoices", "allCurrent": "All invoices current", "thisYear": "This year", "dueThisMonth": "Due this month"}}, "payments": {"noPayments": "No payments yet", "noPaymentsDescription": "Your payment history will appear here", "totalPaid": "Total Paid", "parent1Total": "Parent 1 Total", "parent2Total": "Parent 2 Total", "payments": "payments", "exportPayments": "Export Payments", "parent1": "Parent 1", "parent2": "Parent 2", "table": {"date": "Date", "invoice": "Invoice", "method": "Method", "reference": "Reference #", "paidBy": "<PERSON><PERSON>", "amount": "Amount", "actions": "Actions"}}}, "tutor": {"profile": {"title": "My Profile", "subtitle": "Manage your professional information and teaching preferences", "fetchError": "Failed to load profile", "updateSuccess": "Profile updated successfully", "updateError": "Failed to update profile", "notFound": "Profile not found", "tabs": {"personal": "Personal Info", "education": "Education", "experience": "Experience", "availability": "Availability", "rates": "Service Rates", "areas": "Service Areas", "references": "References", "documents": "Documents"}, "verificationStatus": {"pending": "Pending Verification", "documents_submitted": "Documents Submitted", "under_review": "Under Review", "verified": "Verified"}, "personalInfo": "Personal Information", "fields": {"firstName": "First Name", "lastName": "Last Name", "email": "Email Address", "phoneNumber": "Phone Number", "bio": "Professional Bio", "languagesSpoken": "Languages Spoken", "teachingLanguages": "Teaching Languages"}, "placeholders": {"firstName": "Enter your first name", "lastName": "Enter your last name", "bio": "Tell students and parents about your teaching experience, approach, and what makes you a great tutor..."}, "errors": {"firstNameRequired": "First name is required", "lastNameRequired": "Last name is required", "invalidPhone": "Please enter a valid 10-digit phone number", "bioRequired": "Professional bio is required", "bioTooShort": "Bio must be at least 100 characters", "languagesRequired": "Please select at least one language you speak", "teachingLanguagesRequired": "Please select at least one teaching language"}, "emailCannotBeChanged": "Email cannot be changed", "bioHelp": "{{chars}} characters (minimum 100)", "teachingLanguagesHelp": "Select the languages you can teach in"}, "languages": {"english": "English", "french": "French", "spanish": "Spanish", "mandarin": "Mandarin", "arabic": "Arabic", "hindi": "Hindi", "portuguese": "Portuguese", "russian": "Russian", "german": "German", "italian": "Italian", "other": "Other"}, "education": {"title": "Education Background", "primaryDegree": "Primary Degree", "additionalDegrees": "Additional Degrees", "certifications": "Teaching Certifications", "addDegree": "Add Degree", "fields": {"degreeLevel": "Degree Level", "degreeName": "Degree Name", "major": "Major/Field of Study", "university": "University/Institution", "graduationYear": "Graduation Year", "gpa": "GPA"}, "placeholders": {"selectDegree": "Select degree level", "degreeName": "e.g., Bachelor of Science", "major": "e.g., Mathematics", "university": "e.g., McGill University", "selectYear": "Select year", "degree": "Degree", "year": "Year", "certification": "e.g., Quebec Teaching License"}, "degrees": {"highSchool": "High School", "associate": "Associate", "bachelor": "Bachelor", "master": "Master", "phd": "PhD", "professional": "Professional", "other": "Other"}, "errors": {"degreeRequired": "Degree level is required", "degreeNameRequired": "Degree name is required", "majorRequired": "Major is required", "universityRequired": "University is required", "yearRequired": "Graduation year is required", "invalidGPA": "GPA must be between 0 and 4.0"}}, "experience": {"title": "Teaching Experience", "fields": {"totalYears": "Total Years of Experience", "onlineYears": "Years Teaching Online", "tutoringYears": "Years Tutoring", "totalStudents": "Total Students Taught", "currentOccupation": "Current Occupation", "methodology": "Teaching Methodology", "ageGroups": "Age Groups Experience", "specialNeeds": "Special Needs Experience", "subjectExpertise": "Subject Expertise", "successStories": "Success Stories"}, "placeholders": {"occupation": "e.g., High School Math Teacher", "selectLevel": "Select expertise level", "successStories": "Share examples of how you've helped students succeed..."}, "methodology": {"oneOnOne": "One-on-One", "groupSessions": "Group Sessions", "interactive": "Interactive Learning", "lectureBased": "Lecture-Based", "projectBased": "Project-Based", "flippedClassroom": "Flipped Classroom", "gamification": "Gamification", "differentiated": "Differentiated Instruction"}, "ageGroups": {"preschool": "Preschool (3-5)", "elementary": "Elementary (6-11)", "middleSchool": "Middle School (12-14)", "highSchool": "High School (15-18)", "college": "College/University", "adult": "Adult Learners"}, "expertise": {"beginner": "<PERSON><PERSON><PERSON>", "intermediate": "Intermediate", "advanced": "Advanced", "expert": "Expert"}, "notTaught": "Not taught", "errors": {"invalidYears": "Online and tutoring years cannot exceed total experience", "occupationRequired": "Current occupation is required", "methodologyRequired": "Please select at least one teaching methodology", "ageGroupsRequired": "Please select at least one age group", "subjectsRequired": "Please select at least one subject expertise"}, "successStoriesHelp": "Optional: Share specific examples of student achievements"}, "subjects": {"mathematics": "Mathematics", "science": "Science", "english": "English", "french": "French", "history": "History", "geography": "Geography", "computerScience": "Computer Science", "other": "Other"}, "availability": {"title": "Weekly Availability", "subtitle": "Set your regular teaching hours for each day of the week", "fetchError": "Failed to load availability", "saveSuccess": "Availability saved successfully", "saveError": "Failed to save availability", "addSlot": "Add Time Slot", "startTime": "Start Time", "endTime": "End Time", "active": "Active", "noSlots": "No availability set for this day", "quickActions": "Quick Actions", "setBusinessHours": "Set Business Hours (9-5)", "setEveningHours": "Set Evening Hours (6-9)", "clearAll": "Clear All", "errors": {"title": "Please fix the following errors:", "overlapping": "Time slots cannot overlap", "invalidTimeRange": "End time must be after start time", "validation": "Please fix validation errors before saving"}}, "dashboard": {"title": "Tutor Dashboard", "subtitle": "Welcome back! Here's your teaching overview", "upcomingSessions": "Upcoming Sessions", "recentActivity": "Recent Activity", "quickActions": "Quick Actions", "stats": {"upcomingSessions": "Upcoming Sessions", "hoursThisWeek": "Hours This Week", "pendingPayments": "Pending Payments", "activeStudents": "Active Students"}, "activities": {"sessionCompleted": "Session Completed", "paymentReceived": "Payment Received", "newStudentAssigned": "New Student Assigned"}, "actions": {"viewSchedule": "View Schedule", "requestTimeOff": "Request Time Off", "viewStudents": "View Students", "viewEarnings": "View Earnings"}}, "timeOff": {"title": "Time Off Management", "subtitle": "Request and manage your time off", "newRequest": "New Request", "requestTimeOff": "Request Time Off", "requestSubmitted": "Time off request submitted successfully", "requestHistory": "Request History", "dates": "Dates", "startDate": "Start Date", "endDate": "End Date", "reason": "Reason", "notes": "Notes", "notesPlaceholder": "Additional details about your time off request", "requestedOn": "Requested On", "info": "Time off requests are reviewed within 24-48 hours. You'll receive a notification once your request is processed.", "reasons": {"vacation": "Vacation", "personal": "Personal", "medical": "Medical", "holiday": "Holiday", "other": "Other"}, "status": {"pending": "Pending", "approved": "Approved", "rejected": "Rejected"}, "stats": {"pending": "Pending Requests", "approved": "Approved This Month", "daysOffThisMonth": "Days Off This Month"}}, "rates": {"title": "Service Rates", "subtitle": "Set your hourly rates for different services", "perHour": "/hour", "addService": "Add Service", "noRates": "No service rates configured", "noRatesDescription": "Add services to set your hourly rates", "saveSuccess": "Service rates saved successfully", "saveError": "Failed to save service rates", "info": "These are your base rates. Final client rates may include platform fees. Rates are displayed in Canadian dollars (CAD).", "errors": {"invalidRates": "Please enter valid rates for all services (must be greater than 0)"}}, "areas": {"title": "Service Areas", "subtitle": "Define the postal codes where you're willing to teach", "maxDistance": "Max distance", "addArea": "Add Service Area", "addServiceArea": "Add Service Area", "postalCodePlaceholder": "e.g., H3H 2R9", "distanceHelp": "Enter the maximum distance (1-50 km) you're willing to travel from this postal code", "noAreas": "No service areas configured", "noAreasDescription": "Add postal codes where you can provide tutoring services", "mapPreview": "Service Area Map", "mapComingSoon": "Interactive map visualization coming soon", "saveSuccess": "Service areas saved successfully", "saveError": "Failed to save service areas", "info": "Service areas help match you with students in your preferred locations. You can specify how far you're willing to travel from each postal code.", "errors": {"postalCodeRequired": "Postal code is required", "invalidDistance": "Distance must be between 1 and 50 km", "duplicatePostalCode": "This postal code is already in your service areas"}}}, "tutors": {"profiles": "Tutor Profiles", "invitations": "Invitations", "applications": "Applications"}, "dependants": {"title": "Dependants", "addDependant": "Add Dependant", "editDependant": "Edit Dependant", "parentGuardian": "Parent/Guardian", "relationship": "Relationship", "parent": "Parent", "guardian": "Guardian", "other": "Other", "grade": "Grade", "school": "School", "specialNeeds": "Special Needs", "allergies": "Allergies", "medications": "Medications", "learningGoals": "Learning Goals"}, "billing": {"title": "Billing", "invoices": "Invoices", "invoicesDescription": "Manage and track client invoices", "amount": "Amount", "dueDate": "Due Date", "paidDate": "Paid <PERSON>", "issueDate": "Issue Date", "dateFrom": "From Date", "dateTo": "To Date", "paymentMethod": "Payment Method", "paymentStatus": {"pending": "Pending", "approved": "Approved", "processing": "Processing", "paid": "Paid", "rejected": "Rejected"}, "pending": "Pending", "paid": "Paid", "overdue": "Overdue", "cancelled": "Cancelled", "refunded": "Refunded", "invoice": "Invoice", "invoiceNumber": "Invoice Number", "viewInvoice": "View Invoice", "downloadInvoice": "Download Invoice", "sendInvoice": "Send Invoice", "markAsPaid": "<PERSON> as <PERSON><PERSON>", "recordPayment": "Record Payment", "createInvoice": "Create Invoice", "searchInvoices": "Search invoices by number or client name", "noInvoices": "No invoices found", "noInvoicesDescription": "No invoices match your search criteria", "client": "Client", "items": "items", "invoiceDownloaded": "Invoice downloaded successfully", "status": "Status", "errors": {"fetchBillingOptions": "Failed to fetch billing options", "processBilling": "Failed to process billing", "fetchPackages": "Failed to fetch packages", "paymentFailed": "Payment failed", "fetchPaymentHistory": "Failed to fetch payment history", "downloadStatement": "Failed to download statement", "updateAutoRenew": "Failed to update auto-renewal", "updateInvoiceStatus": "Failed to update invoice status"}, "tutorPayments": {"tutorDescription": "View your payment history and earnings", "notAvailableForRole": "Tutor payments are not available for your role", "paymentCycle": "Payment Cycle", "paymentMethod": "Payment Method", "directDeposit": "Direct Deposit", "processingTime": "Processing Time", "within48Hours": "Within 48 hours", "yourPaymentHistory": "Your Payment History", "tutorViewComingSoon": "Payment history view coming soon"}, "subscriptions": {"title": "Subscriptions", "managerDescription": "Manage client subscription packages and usage", "clientDescription": "View and manage your subscription packages", "howItWorks": "How Subscriptions Work", "dualBillingExplanation": "Clients can pay through subscriptions OR invoices", "autoDeductionExplanation": "Hours are automatically deducted from active subscriptions", "noSubscriptionExplanation": "If no subscription, appointments are added to monthly invoices", "prepaidHours": "Pre-paid Hours", "buyInAdvance": "Buy in Advance", "automaticBilling": "Automatic Billing", "noInvoices": "No Invoices", "trackUsage": "Track Usage", "realTimeTracking": "Real-time Tracking", "yourSubscriptions": "Your Subscriptions", "clientViewComingSoon": "Client subscription view coming soon", "notAvailableForRole": "Subscriptions are not available for your role", "activeSubscriptions": "Active Subscriptions", "expiringThisMonth": "Expiring This Month", "totalHoursRemaining": "Total Hours Remaining", "activePackages": "Active Packages", "searchSubscriptions": "Search by client name or package", "newSubscription": "New Subscription", "quickFilters": "Quick Filters", "showExpiringOnly": "Show expiring only", "noSubscriptions": "No subscriptions found", "noSubscriptionsDescription": "No subscriptions match your criteria", "createFirst": "Create First Subscription", "client": "Client", "package": "Package", "period": "Period", "hoursUsage": "Hours Usage", "autoRenew": "Auto Renew", "viewUsage": "View Usage", "renew": "<PERSON>w", "renewSubscription": "Renew Subscription", "confirmRenewalMessage": "Are you sure you want to renew this subscription?", "confirmRenewal": "Confirm <PERSON>wal", "renewalSuccess": "Subscription renewed successfully", "usageHistory": "Usage History", "noUsageHistory": "No usage history available", "session": "Session", "hoursUsed": "Hours Used", "remainingAfter": "Remaining After", "expiresInDays": "Expires in {{days}} days", "status": {"active": "Active", "expired": "Expired", "depleted": "Depleted", "suspended": "Suspended", "cancelled": "Cancelled", "expiringSoon": "Expiring Soon"}, "purchaseNew": "Purchase New", "purchaseSuccess": "Subscription purchased successfully", "completePurchase": "Complete Purchase", "payment": "Payment", "selectPackage": "Select Package", "popular": "Popular", "validFor": "Valid for", "hoursIncluded": "hours included", "subjectRestrictions": "Subject restrictions", "benefits": "Subscription Benefits", "benefit1": "Hours never expire during validity period", "benefit2": "Automatic billing for completed sessions", "benefit3": "Priority booking with tutors", "proceedToPayment": "Proceed to Payment", "oneTime": "One-time", "monthly": "Monthly", "quarterly": "Quarterly", "annual": "Annual", "price": "Price", "purchasedOn": "Purchased on", "validUntil": "Valid until", "averageUsage": "Average Usage", "totalSpent": "Total Spent", "pastSubscriptions": "Past Subscriptions", "viewHistory": "View History", "noSubscriptionsClientDescription": "Start saving with pre-paid tutoring hours", "browsePackages": "Browse Packages", "autoRenewEnabled": "Auto-renewal enabled", "autoRenewDisabled": "Auto-renewal disabled", "enableAutoRenew": "Enable Auto-Renewal", "disableAutoRenew": "Disable Auto-Renewal", "autoRenewSettings": "Auto-Renewal Settings", "currentStatus": "Current status", "disableAutoRenewWarning": "Disabling auto-renewal means you'll need to manually renew when your subscription expires", "enableAutoRenewInfo": "Enable auto-renewal to automatically renew your subscription when it expires", "hoursRemaining": "Hours Remaining", "renewNow": "Renew Now"}, "appointment": {"details": "Appointment Details", "client": "Client", "tutor": "Tutor", "dateTime": "Date & Time", "duration": "Duration", "subject": "Subject", "rate": "Rate", "billingOptions": "Billing Options", "selectBillingMethod": "Select how you'd like to bill this appointment", "useSubscription": "Use Subscription Hours", "createInvoice": "Create Invoice", "recommended": "Recommended", "remaining": "remaining", "expiresOn": "Expires on", "estimatedCost": "Estimated cost", "howItWorks": "How Dual Billing Works", "dualBillingInfo1": "Subscriptions are checked first for available hours", "dualBillingInfo2": "If no subscription or insufficient hours, an invoice is created", "dualBillingInfo3": "You can always choose to invoice instead of using subscription", "processBilling": "Process Billing", "confirmBilling": "Confirm Billing", "method": "Method", "subscriptionDeduction": "Subscription hour deduction", "invoiceCreation": "Invoice creation", "amount": "Amount", "remainingAfter": "Remaining after", "confirmationWarning": "This action cannot be undone. Please confirm the billing details are correct.", "confirmProcess": "Confirm & Process", "alreadyBilled": "This appointment has already been billed", "alreadyProcessed": "Billing Already Processed", "billingMethod": "Billing method", "subscription": "Subscription", "invoice": "Invoice", "notFound": "Appointment not found", "subscriptionRecommended": "Save money by using your subscription hours", "noSubscriptionAvailable": "No active subscription available", "invoiceAlternative": "Create invoice instead of using subscription", "subscriptionBillingSuccess": "Successfully deducted {{hours}} hours. {{remaining}} hours remaining.", "invoiceBillingSuccess": "Invoice created successfully"}, "payment": "Payment", "saveCardForFuture": "Save card for future payments", "securityNotice": "Your payment information is encrypted and secure. We never store your card details.", "paymentSuccess": "Payment successful", "totalAmount": "Total Amount", "selectPaymentMethod": "Select Payment Method", "useNewCard": "Use New Card", "expiresOn": "Expires", "billingInformation": "Billing Information", "cardholderName": "Cardholder Name", "cardholderNamePlaceholder": "<PERSON>", "email": "Email", "emailPlaceholder": "<EMAIL>", "cardDetails": "Card Details", "billingAddress": "Billing Address", "streetAddress": "Street Address", "streetAddressPlaceholder": "123 Main St", "city": "City", "cityPlaceholder": "Montreal", "province": "Province", "selectProvince": "Select province", "postalCode": "Postal Code", "securePayment": "Secure Payment", "encryptedConnection": "256-bit SSL encryption", "pciCompliant": "PCI DSS compliant", "poweredByStripe": "Powered by <PERSON>e", "payNow": "Pay {{amount}}", "billingSuccess": "Billing processed successfully", "invoiceDetails": "Invoice Details", "tutorPaymentDetails": "Tutor Payment Details", "details": "Details", "auditLog": "<PERSON>t Log", "clientInfo": "Client Information", "lineItems": "Line Items", "description": "Description", "quantity": "Quantity", "rate": "Rate", "subtotal": "Subtotal", "tax": "Tax", "total": "Total", "paymentInfo": "Payment Information", "method": "Method", "reference": "Reference", "paidBy": "<PERSON><PERSON>", "parent1": "Parent 1", "parent2": "Parent 2", "noAuditLogs": "No audit logs available", "paymentPeriod": "Payment Period", "totalHours": "Total Hours", "paymentBreakdown": "Payment Breakdown", "baseAmount": "Base Amount", "bonusAmount": "Bonus Amount", "sessionDetails": "Session Details", "sessions": "sessions", "date": "Date", "hours": "Hours", "processingInfo": "Processing Information", "approvedBy": "Approved By", "approvedAt": "Approved At", "stripePayoutId": "Stripe Payout ID", "paidAt": "<PERSON><PERSON>", "actions": {"created": "Created", "updated": "Updated", "paid": "Paid", "refunded": "Refunded", "cancelled": "Cancelled", "approved": "Approved", "rejected": "Rejected", "payout_processed": "Payout Processed", "hours_deducted": "Hours Deducted", "subscription_purchased": "Purchased", "subscription_renewed": "Renewed", "subscription_cancelled": "Cancelled", "payment_recorded": "Payment Recorded", "auto_renew_updated": "Auto-Renew Updated"}, "fields": {"status": "Status", "amount": "Amount", "payment_method": "Payment Method", "hours_remaining": "Hours Remaining", "line_items": "Line Items", "auto_renew": "Auto Renewal", "notes": "Notes"}}, "services": {"hero": {"title": "Professional Tutoring Services", "description": "Choose from our wide range of tutoring services designed to help students excel in their academic journey.", "getStarted": "Get Started"}, "stats": {"availableServices": "Available Services", "qualifiedTutors": "Qualified Tutors", "averageRating": "Average Rating", "successRate": "Success Rate"}, "tabs": {"individualSessions": "Individual Sessions", "packages": "Package Deals"}, "filters": {"title": "Filter by", "allTypes": "All Service Types", "online": "Online", "inPerson": "In Person", "library": "Library", "hybrid": "Hybrid", "allSubjects": "All Subjects"}, "noServices": "No services found", "noServicesDescription": "Try adjusting your filters to see more services", "noPackages": "No packages available", "noPackagesDescription": "Check back soon for special package deals", "startingFrom": "Starting from", "session": "session", "bookNow": "Book Now", "packages": {"title": "Save with Package Deals", "description": "Purchase bundled sessions for better value and guaranteed availability", "popular": "Popular", "sessions": "sessions", "perSession": "per session", "validFor": "Valid for", "perSessionPrice": "per session", "save": "Save", "purchase": "Purchase Package", "signInToPurchase": "Sign In to Purchase"}, "cta": {"title": "Ready to Start Learning?", "description": "Join thousands of students who have improved their grades with our expert tutors.", "bookSession": "Book a Session", "findTutor": "Find a <PERSON>tor"}, "errors": {"fetchServices": "Failed to load services"}}, "appointments": {"errors": {"fetchAppointment": "Failed to fetch appointment details", "completeAppointment": "Failed to complete appointment", "loadAuditLogs": "Failed to load audit logs"}, "details": {"title": "Appointment Details", "tabs": {"details": "Details", "audit": "<PERSON>t Log"}, "basicInfo": "Basic Information", "scheduleInfo": "Schedule Information", "notAssigned": "Not assigned", "notSpecified": "Not specified"}, "fields": {"client": "Client", "tutor": "Tutor", "subject": "Subject", "status": "Status", "date": "Date", "time": "Time", "location": "Location", "duration": "Duration", "notes": "Notes", "startTime": "Start Time", "endTime": "End Time", "locationType": "Location Type"}, "audit": {"title": "Change History", "noLogs": "No audit logs available", "reason": "Reason", "actions": {"created": "Created", "updated": "Updated", "statusChanged": "Status Changed", "cancelled": "Cancelled", "deleted": "Deleted", "rescheduled": "Rescheduled"}}, "completeAppointment": "Complete Appointment", "confirmCompletion": "Confirm Completion", "confirmCompletionMessage": "Please confirm that this appointment has been completed successfully.", "markAsCompleted": "<PERSON> as Completed", "completionSuccess": "Appointment marked as completed", "status": {"completed": "Completed", "scheduled": "Scheduled", "cancelled": "Cancelled"}, "adjustDuration": "Adjust Duration", "durationAdjustmentInfo": "Adjust the actual duration if it differed from the scheduled time", "actualDuration": "Actual Duration", "scheduledDuration": "Scheduled Duration", "adjustmentReason": "Adjustment Reason", "adjustmentReasonPlaceholder": "e.g., Session extended to complete exercise", "tutorNoShow": "Tutor No-<PERSON>", "clientNoShow": "Client No-Show", "completionNotes": "Completion Notes", "completionNotesPlaceholder": "Optional notes about the session...", "durationAdjusted": "Duration has been adjusted successfully", "durationWasAdjusted": "Duration Adjustment Applied", "originalDuration": "Original Duration", "difference": "Difference", "reason": "Reason"}, "settings": {"title": "Settings", "language": {"title": "Language Preferences", "current_status": "Current Status", "current_language": "Current Language", "detected_language": "Detected Language", "confidence": "confidence", "source": {"manual": "Manual Selection", "auto": "Auto Detection", "browser": "Browser Detection", "system": "System Default", "user_manual": "User Manual", "auto_detect": "Auto Detect", "browser_detect": "<PERSON><PERSON><PERSON> Detect", "admin_override": "<PERSON><PERSON> Override"}, "last_updated": "Last Updated", "quebec_indicators": "Quebec French Indicators", "preferences": "Language Settings", "preferred_language": "Preferred Language", "auto_detect": "Automatically detect language", "auto_detect_description": "Automatically detect your language based on browser settings and location", "quebec_french_preference": "Prefer Quebec French", "quebec_french_description": "Use Quebec French terminology and formatting when French is selected", "auto_detect_now": "Auto-Detect Now", "view_history": "View History", "change_history": "Language Change History", "no_history": "No language changes recorded", "quick_switch": "Quick Language Switch", "quick_switch_description": "Quickly switch between languages for this session"}, "timezone": "Timezone", "notifications": "Notification Settings", "appearance": "Appearance", "privacy": "Privacy", "security": {"title": "Security Settings", "twoFactor": {"title": "Two-Factor Authentication", "description": "Add an extra layer of security to your account", "enabled": "Two-factor authentication enabled", "disabled": "Two-factor authentication disabled", "gracePeriod": "You have until {{date}} to set up two-factor authentication", "noMethodEnabled": "Please set up at least one authentication method first"}, "methods": {"title": "Authentication Methods", "sms": "SMS", "email": "Email", "totp": "Authenticator App", "setup": "Set Up", "manage": "Manage", "lastUsed": "Last used"}, "trustedDevices": {"title": "Trusted Devices", "description": "You have {{count}} trusted devices", "noDevices": "No trusted devices found", "currentDevice": "This device", "lastUsed": "Last used", "expiresOn": "Expires on {{date}}", "removed": "Device removed successfully", "info": "Trusted devices don't require two-factor authentication for 30 days", "types": {"desktop": "Desktop", "mobile": "Mobile", "tablet": "Tablet"}, "errors": {"fetchFailed": "Failed to load trusted devices", "removeFailed": "Failed to remove device"}}, "tips": {"title": "Security Tips", "useStrongPassword": "Use a strong, unique password for your account", "enableTwoFactor": "Enable two-factor authentication for better security", "reviewDevices": "Regularly review your trusted devices", "bewarePhishing": "Be cautious of phishing emails asking for your password"}, "setup": {"steps": {"setup": "Setup", "verify": "Verify", "backup": "Backup"}, "sms": {"title": "Set Up SMS Authentication", "description": "We'll send a code to your phone when you sign in"}, "email": {"title": "Set Up Email Authentication", "description": "We'll send a code to your email when you sign in"}, "totp": {"title": "Set Up Authenticator App", "description": "Use an app like Google Authenticator or Authy", "manualEntry": "Can't scan? Enter this code manually:"}, "phoneNumber": "Phone Number", "emailAddress": "Email Address", "phoneRequired": "Please enter your phone number", "emailRequired": "Please enter your email address", "sendCode": "Send Code", "smsSent": "Verification code sent to your phone", "emailSent": "Verification code sent to your email", "invalidCode": "Please enter a valid 6-digit code", "verify": {"description": "Enter the 6-digit code from your device", "code": "Verification Code", "button": "Verify"}, "backup": {"warning": "Important: Save Your Backup Codes", "description": "Use these codes to access your account if you lose your device"}, "success": "Two-factor authentication set up successfully", "errors": {"generateFailed": "Failed to generate setup data", "smsFailed": "Failed to send SMS", "emailFailed": "Failed to send email", "verifyFailed": "Failed to verify code"}}, "errors": {"fetchFailed": "Failed to load security settings", "updateFailed": "Failed to update security settings", "noMethodEnabled": "Please set up at least one authentication method first"}}, "service": {"title": "Service Settings", "description": "Description", "sessionPresets": "Session Presets", "packages": "Service Packages", "addPreset": "Add Preset", "editPreset": "Edit Preset", "noPresets": "No presets configured", "noPresetsDescription": "Create presets to make booking appointments faster", "createFirstPreset": "Create First Preset", "presetCreated": "Preset created successfully", "presetUpdated": "Preset updated successfully", "presetDeleted": "Preset deleted successfully", "confirmDeletePreset": "Are you sure you want to delete this preset?", "presetName": "Preset Name", "duration": "Duration", "serviceType": "Service Type", "baseRate": "Base Rate ($/session)", "activePreset": "Active preset", "subjects": "Subjects", "addPackage": "Add Package", "editPackage": "Edit Package", "noPackages": "No packages configured", "noPackagesDescription": "Create packages to offer bundled sessions at discounted rates", "createFirstPackage": "Create First Package", "packageCreated": "Package created successfully", "packageUpdated": "Package updated successfully", "packageDeleted": "Package deleted successfully", "confirmDeletePackage": "Are you sure you want to delete this package?", "packageName": "Package Name", "totalSessions": "Total Sessions", "sessions": "Sessions", "sessionDuration": "Session Duration", "packagePrice": "Package Price", "validity": "Validity", "validityDays": "Validity (days)", "allowedServiceTypes": "Allowed Service Types", "activePackage": "Active package", "errors": {"fetchSettings": "Failed to fetch service settings", "savePreset": "Failed to save preset", "deletePreset": "Failed to delete preset", "savePackage": "Failed to save package", "deletePackage": "Failed to delete package"}}}, "plurals": {"appointment": {"one": "{count} appointment", "other": "{count} appointments"}, "client": {"one": "{count} client", "other": "{count} clients"}, "tutor": {"one": "{count} tutor", "other": "{count} tutors"}, "hour": {"one": "{count} hour", "other": "{count} hours"}, "minute": {"one": "{count} minute", "other": "{count} minutes"}, "session": {"one": "{count} session", "other": "{count} sessions"}, "message": {"one": "{count} message", "other": "{count} messages"}, "payment": {"one": "{count} payment", "other": "{count} payments"}}, "quebec": {"languages": {"english": "English", "french": "French"}}, "success": {"language_switched": "Language switched successfully", "translations_reloaded": "Translations reloaded successfully"}, "errors": {"language_switch_failed": "Failed to switch language", "translations_load_failed": "Failed to load translations", "access_denied": "Access Denied", "insufficient_permissions": "You don't have permission to access this feature", "fetch_invitations_failed": "Failed to fetch invitations", "search_failed": "Search failed", "email_required": "Email is required", "user_already_exists": "A user with this email already exists", "send_invitation_failed": "Failed to send invitation", "resend_invitation_failed": "Failed to resend invitation", "cancel_invitation_failed": "Failed to cancel invitation"}, "packages": {"create": "Create Package", "createNew": "Create New Package", "availablePackages": "Available Packages", "myPackages": "My Packages", "packageName": "Package Name", "packageType": "Package Type", "sessionCount": "Number of Sessions", "sessionDuration": "Session Duration (minutes)", "price": "Price", "isGroupPackage": "Group Package", "minParticipants": "Min Participants", "maxParticipants": "Max Participants", "validUntil": "<PERSON>id <PERSON>", "description": "Description", "termsAndConditions": "Terms and Conditions", "sessions": "sessions", "minutes": "minutes", "participants": "participants", "purchase": "Purchase", "confirmPurchase": "Confirm Purchase", "confirmDelete": "Are you sure you want to delete this package?", "noPurchases": "You have no package purchases yet", "purchasedOn": "Purchased on", "for": "For", "sessionsRemaining": "sessions remaining", "expiresOn": "Expires on", "types": {"tecfee": "TECFEE", "bundle": "Bundle", "custom": "Custom"}, "status": {"active": "Active", "completed": "Completed", "expired": "Expired", "refunded": "Refunded"}}, "messages": {"searchConversations": "Search conversations...", "noConversations": "No conversations found", "noMessages": "(No messages)", "typeMessage": "Type a message...", "smsCharges": "SMS charges may apply. Press Enter to send, Shift+Enter for new line.", "selectConversation": "Select a conversation to start messaging", "startConversation": "Start a conversation to send messages", "errors": {"fetchConversations": "Failed to fetch conversations", "fetchMessages": "Failed to fetch messages", "sendMessage": "Failed to send message"}}, "form": {"email": "Email", "first_name": "First Name", "last_name": "Last Name", "phone": "Phone", "personal_message": "Personal Message", "first_name_placeholder": "Enter first name", "last_name_placeholder": "Enter last name"}, "invitations": {"title": "Tutor <PERSON>s", "description": "Manage tutor invitations and track their status", "send_invitation": "Send Invitation", "send_first_invitation": "Send First Invitation", "no_invitations": "No invitations found", "no_invitations_description": "Start by sending your first tutor invitation", "search_placeholder": "Search by email or name...", "only_mine": "Only my invitations", "invited_by": "Invited by", "sent": "<PERSON><PERSON>", "expires": "Expires", "expired": "expired", "copy_link": "Copy Link", "resend": "Resend", "cancel": "Cancel", "sending": "Sending...", "send": "Send", "sent_successfully": "Invitation sent successfully", "resent_successfully": "Invitation resent successfully", "cancelled_successfully": "Invitation cancelled successfully", "link_copied": "Invitation link copied to clipboard", "confirm_cancel": "Are you sure you want to cancel this invitation?", "message_placeholder": "Optional personal message for the invited tutor...", "status": {"all": "All Statuses", "pending": "Pending", "accepted": "Accepted", "expired": "Expired"}}, "invitation": {"invalid_token": "Invalid invitation token", "token_expired_or_invalid": "This invitation link has expired or is invalid", "verification_error": "Unable to verify invitation", "verifying_token": "Verifying invitation...", "invalid_invitation": "Invalid Invitation", "go_to_login": "Go to Login", "welcome_to_tutoraide": "Welcome to TutorAide!", "create_account_subtitle": "Complete your registration to get started as a tutor", "invitation_details": "Invitation Details", "invited_as": "Invited as", "name": "Name", "personal_message": "Personal Message", "create_password": "Create Password", "password_placeholder": "Enter your password", "password_help": "Password must be at least 8 characters with uppercase, lowercase, and number", "confirm_password": "Confirm Password", "confirm_password_placeholder": "Confirm your password", "password_required": "Password is required", "password_mismatch": "Passwords do not match", "password_min_length": "Password must be at least 8 characters long", "password_requirements": "Password must contain uppercase, lowercase, and numeric characters", "validation_error": "Invalid data provided", "acceptance_error": "Failed to accept invitation", "creating_account": "Creating Account...", "accept_and_create_account": "Accept Invitation & Create Account", "terms_notice": "By creating an account, you agree to our Terms of Service and Privacy Policy", "account_created_successfully": "Account created successfully! Welcome to TutorAide.", "account_created_login_prompt": "Account created successfully! Please log in with your new credentials."}, "consent": {"title": "Consent & Agreements", "requiredAgreements": "Required Agreements", "mandatory": "Mandatory", "alreadyAccepted": "Accepted", "version": "Version", "effectiveDate": "Effective Date", "step": "Step", "accepting": "Accepting...", "acceptSelected": "Accept Selected Agreements", "viewDocument": "View Document", "viewHistory": "View History", "accept": "Accept", "withdraw": {"title": "Withdraw Consent", "confirmation": "Are you sure you want to withdraw your consent for {{consent}}?", "reasonLabel": "Reason (optional)", "reasonPlaceholder": "Please tell us why you're withdrawing this consent..."}, "withdrawing": "Withdrawing...", "confirmWithdraw": "Confirm <PERSON>", "grantedOn": "Granted on", "withdrawnOn": "Withdrawn on", "reason": "Reason", "info": {"mandatory": "Mandatory agreements are required to use the platform and cannot be withdrawn."}, "categories": {"legal": "Legal", "marketing": "Marketing", "analytics": "Analytics", "functional": "Functional"}, "types": {"terms_of_service": "Terms of Service", "privacy_policy": "Privacy Policy", "tutor_employment_agreement": "Tutor Service Agreement", "marketing_emails": "Marketing Communications", "usage_analytics": "Usage Analytics"}, "status": {"granted": "Accepted", "withdrawn": "Withdrawn", "expired": "Expired"}, "actions": {"granted": "Consent Granted", "withdrawn": "Consent Withdrawn", "expired": "Consent Expired"}, "errors": {"fetchFailed": "Failed to load consent information", "acceptFailed": "Failed to accept consent", "withdrawFailed": "Failed to withdraw consent", "validationFailed": "Failed to validate consents", "historyFetchFailed": "Failed to load consent history", "documentFetchFailed": "Failed to load document"}, "success": {"accepted": "<PERSON><PERSON> successfully accepted", "withdrawn": "<PERSON><PERSON> successfully withdrawn"}, "management": {"title": "Consent Management", "description": "Review and manage your privacy preferences and consent settings", "infoTitle": "About Your Consents", "infoDescription": "You can review all agreements and manage optional preferences here. Mandatory agreements are required for platform access.", "mandatoryConsents": "Mandatory Agreements", "optionalConsents": "Optional Preferences"}, "history": {"title": "Consent History", "empty": "No history available for this consent"}, "flow": {"title": "Agreement Review Required", "clientDescription": "Please review and accept the required agreements to continue using TutorAide.", "tutorDescription": "As a tutor, you must review and accept our service agreement and platform terms.", "missingConsents": "The following agreements need your acceptance:", "expiredConsents": "The following agreements have expired and need renewal:", "reviewAndAccept": "Review & Accept Agreements", "remindLater": "Remind Me Later", "infoText": "Your privacy is important to us. We only collect necessary information to provide our tutoring services."}}}