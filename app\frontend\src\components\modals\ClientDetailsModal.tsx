import React from 'react';
import { useTranslation } from 'react-i18next';
import { X, User, Phone, Mail, MapPin, AlertCircle, Calendar, DollarSign } from 'lucide-react';
import { Modal } from '../common/Modal';
import { Badge } from '../common/Badge';
import Button from '../common/Button';
import { ClientProfile } from '../../services/clientService';
import { format } from 'date-fns';

interface ClientDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  client: ClientProfile;
  onEdit?: (client: ClientProfile) => void;
  canEdit?: boolean;
}

const ClientDetailsModal: React.FC<ClientDetailsModalProps> = ({
  isOpen,
  onClose,
  client,
  onEdit,
  canEdit = false
}) => {
  const { t } = useTranslation();

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="lg">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-6">
          <div className="flex items-center">
            <img
              className="h-16 w-16 rounded-full object-cover"
              src={client.profile_picture || '/default-avatar.png'}
              alt={`${client.first_name} ${client.last_name}`}
              onError={(e) => {
                (e.target as HTMLImageElement).src = '/default-avatar.png';
              }}
            />
            <div className="ml-4">
              <h2 className="text-2xl font-bold text-gray-900">
                {client.first_name} {client.last_name}
              </h2>
              <Badge variant={client.is_active ? 'success' : 'secondary'} size="sm">
                {client.is_active ? t('common.active') : t('common.inactive')}
              </Badge>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="space-y-6">
          {/* Contact Information */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-3">
              {t('users.clients.contactInfo')}
            </h3>
            <div className="space-y-2">
              <div className="flex items-center text-gray-600">
                <Mail className="w-5 h-5 mr-3" />
                <span>{client.email}</span>
              </div>
              {client.phone_number && (
                <div className="flex items-center text-gray-600">
                  <Phone className="w-5 h-5 mr-3" />
                  <span>{client.phone_number}</span>
                </div>
              )}
              {client.address && (
                <div className="flex items-start text-gray-600">
                  <MapPin className="w-5 h-5 mr-3 mt-0.5" />
                  <div>
                    {client.address.street && <div>{client.address.street}</div>}
                    <div>
                      {client.address.city}, {client.address.province} {client.address.postal_code}
                    </div>
                    {client.address.country && <div>{client.address.country}</div>}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Emergency Contacts */}
          {client.emergency_contacts && client.emergency_contacts.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                {t('users.clients.emergencyContacts')}
              </h3>
              <div className="space-y-3">
                {client.emergency_contacts.map((contact, index) => (
                  <div key={index} className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div>
                        <div className="font-medium text-gray-900">
                          {contact.name}
                          {contact.is_primary && (
                            <Badge variant="info" size="sm" className="ml-2">
                              {t('common.primary')}
                            </Badge>
                          )}
                        </div>
                        <div className="text-sm text-gray-600 mt-1">
                          {contact.relationship}
                        </div>
                        <div className="text-sm text-gray-600 mt-2 space-y-1">
                          <div className="flex items-center">
                            <Phone className="w-4 h-4 mr-2" />
                            {contact.phone_number}
                          </div>
                          {contact.email && (
                            <div className="flex items-center">
                              <Mail className="w-4 h-4 mr-2" />
                              {contact.email}
                            </div>
                          )}
                        </div>
                      </div>
                      {contact.is_primary && (
                        <AlertCircle className="w-5 h-5 text-red-500" />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Communication Preferences */}
          {client.communication_preferences && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                {t('users.clients.communicationPreferences')}
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">{t('users.clients.emailNotifications')}</span>
                  <Badge variant={client.communication_preferences.email_notifications ? 'success' : 'secondary'} size="sm">
                    {client.communication_preferences.email_notifications ? t('common.enabled') : t('common.disabled')}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">{t('users.clients.smsNotifications')}</span>
                  <Badge variant={client.communication_preferences.sms_notifications ? 'success' : 'secondary'} size="sm">
                    {client.communication_preferences.sms_notifications ? t('common.enabled') : t('common.disabled')}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">{t('users.clients.pushNotifications')}</span>
                  <Badge variant={client.communication_preferences.push_notifications ? 'success' : 'secondary'} size="sm">
                    {client.communication_preferences.push_notifications ? t('common.enabled') : t('common.disabled')}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">{t('users.clients.language')}</span>
                  <Badge variant="info" size="sm">
                    {client.communication_preferences.language.toUpperCase()}
                  </Badge>
                </div>
              </div>
            </div>
          )}

          {/* Account Information */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-3">
              {t('users.clients.accountInfo')}
            </h3>
            <div className="space-y-2 text-sm">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">{t('users.clients.userId')}</span>
                <span className="font-medium">{client.user_id}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">{t('users.clients.clientId')}</span>
                <span className="font-medium">{client.client_id}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">{t('users.clients.createdAt')}</span>
                <span className="font-medium">{format(new Date(client.created_at), 'PPP')}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">{t('users.clients.updatedAt')}</span>
                <span className="font-medium">{format(new Date(client.updated_at), 'PPP')}</span>
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-3 gap-4 pt-4 border-t">
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <User className="w-8 h-8 text-gray-400" />
              </div>
              <div className="text-2xl font-bold text-gray-900">0</div>
              <div className="text-sm text-gray-600">{t('users.clients.dependants')}</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Calendar className="w-8 h-8 text-gray-400" />
              </div>
              <div className="text-2xl font-bold text-gray-900">0</div>
              <div className="text-sm text-gray-600">{t('users.clients.appointments')}</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <DollarSign className="w-8 h-8 text-gray-400" />
              </div>
              <div className="text-2xl font-bold text-gray-900">$0</div>
              <div className="text-sm text-gray-600">{t('users.clients.totalSpent')}</div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="mt-6 pt-6 border-t flex justify-end space-x-3">
          <Button variant="secondary" onClick={onClose}>
            {t('common.close')}
          </Button>
          {canEdit && onEdit && (
            <Button variant="primary" onClick={() => onEdit(client)}>
              {t('common.edit')}
            </Button>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default ClientDetailsModal;