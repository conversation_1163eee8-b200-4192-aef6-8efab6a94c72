"""
Base repository pattern for all database operations.
Implements raw SQL with proper error handling and security.
"""

import asyncpg
import logging
from typing import Dict, List, Optional, Any, TypeVar, Generic
from datetime import datetime

from app.database.security import DatabaseSecurity, SecurityMonitor
from app.core.timezone import now_est

logger = logging.getLogger(__name__)

T = TypeVar('T')


class BaseRepository(Generic[T]):
    """Base repository with common database operations using raw SQL."""
    
    def __init__(self, table_name: str, id_column: str = "id"):
        """
        Initialize base repository with security validation.
        
        Args:
            table_name: Name of the database table
            id_column: Name of the ID column (e.g., 'user_id', 'client_id')
        """
        # Sanitize table and column names for security
        self.table_name = DatabaseSecurity.sanitize_sql_identifier(table_name)
        self.id_column = DatabaseSecurity.sanitize_sql_identifier(id_column)
    
    async def find_by_id(
        self, 
        conn: asyncpg.Connection, 
        id_value: Any,
        include_deleted: bool = False
    ) -> Optional[asyncpg.Record]:
        """
        Find a record by ID.
        
        Args:
            conn: Database connection
            id_value: Value of the ID to search for
            include_deleted: Whether to include soft-deleted records
        
        Returns:
            Record if found, None otherwise
        """
        query = f"""
            SELECT * FROM {self.table_name}
            WHERE {self.id_column} = $1
        """
        
        if not include_deleted:
            query += " AND deleted_at IS NULL"
        
        try:
            result = await conn.fetchrow(query, id_value)
            return result
        except Exception as e:
            logger.error(f"Error finding {self.table_name} by ID: {e}")
            raise
    
    async def find_all(
        self,
        conn: asyncpg.Connection,
        filters: Optional[Dict[str, Any]] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        order_by: Optional[str] = None,
        include_deleted: bool = False
    ) -> List[asyncpg.Record]:
        """
        Find all records with optional filtering and pagination.
        
        Args:
            conn: Database connection
            filters: Dictionary of column-value pairs to filter by
            limit: Maximum number of records to return
            offset: Number of records to skip
            order_by: Column to order by (e.g., 'created_at DESC')
            include_deleted: Whether to include soft-deleted records
        
        Returns:
            List of records
        """
        # Validate and sanitize inputs
        if filters:
            filters = DatabaseSecurity.validate_query_parameters(filters)
        
        # Validate limit and offset
        limit, offset = QueryValidator.validate_limit_offset(limit, offset)
        
        query_parts = [f"SELECT * FROM {self.table_name}"]
        params = []
        param_count = 0
        
        # Build WHERE clause
        where_conditions = []
        if not include_deleted:
            where_conditions.append("deleted_at IS NULL")
        
        if filters:
            for column, value in filters.items():
                # Column name already sanitized by validate_query_parameters
                param_count += 1
                where_conditions.append(f"{column} = ${param_count}")
                params.append(value)
        
        if where_conditions:
            query_parts.append("WHERE " + " AND ".join(where_conditions))
        
        # Add ORDER BY with validation
        if order_by:
            # Parse order by to separate column and direction
            order_parts = order_by.split()
            if len(order_parts) == 2:
                column, direction = QueryValidator.validate_order_by(order_parts[0], order_parts[1])
                query_parts.append(f"ORDER BY {column} {direction}")
            else:
                column, direction = QueryValidator.validate_order_by(order_parts[0])
                query_parts.append(f"ORDER BY {column} {direction}")
        else:
            query_parts.append("ORDER BY created_at DESC")
        
        # Add LIMIT and OFFSET
        param_count += 1
        query_parts.append(f"LIMIT ${param_count}")
        params.append(limit)
        
        param_count += 1
        query_parts.append(f"OFFSET ${param_count}")
        params.append(offset)
        
        query = " ".join(query_parts)
        
        # Security check for suspicious patterns
        if not SecurityMonitor.check_query_patterns(query, filters or {}):
            raise ValueError("Suspicious query pattern detected")
        
        try:
            results = await conn.fetch(query, *params)
            return results
        except Exception as e:
            logger.error(f"Error finding all {self.table_name}: {e}")
            raise
    
    async def create(
        self,
        conn: asyncpg.Connection,
        data: Dict[str, Any]
    ) -> asyncpg.Record:
        """
        Create a new record with security validation.
        
        Args:
            conn: Database connection
            data: Dictionary of column-value pairs
        
        Returns:
            Created record
        """
        # Validate and sanitize input data
        data = DatabaseSecurity.validate_query_parameters(data)
        
        # Add timestamps
        # Convert to naive datetime for PostgreSQL TIMESTAMP columns
        current_time = now_est().replace(tzinfo=None)
        data['created_at'] = current_time
        data['updated_at'] = current_time
        
        columns = list(data.keys())
        values = list(data.values())
        placeholders = [f"${i+1}" for i in range(len(values))]
        
        query = f"""
            INSERT INTO {self.table_name} ({', '.join(columns)})
            VALUES ({', '.join(placeholders)})
            RETURNING *
        """
        
        # Security check for suspicious patterns
        if not SecurityMonitor.check_query_patterns(query, data):
            raise ValueError("Suspicious query pattern detected")
        
        try:
            result = await conn.fetchrow(query, *values)
            logger.info(f"Created {self.table_name} with {self.id_column}={result[self.id_column]}")
            
            # Log security event for audit trail
            DatabaseSecurity.log_security_event(
                "record_created",
                {"table": self.table_name, "id": result[self.id_column]}
            )
            
            return result
        except Exception as e:
            logger.error(f"Error creating {self.table_name}: {e}")
            raise
    
    async def update(
        self,
        conn: asyncpg.Connection,
        id_value: Any,
        data: Dict[str, Any]
    ) -> Optional[asyncpg.Record]:
        """
        Update a record by ID with security validation.
        
        Args:
            conn: Database connection
            id_value: Value of the ID to update
            data: Dictionary of column-value pairs to update
        
        Returns:
            Updated record if found, None otherwise
        """
        # Validate and sanitize input data
        data = DatabaseSecurity.validate_query_parameters(data)
        
        # Add updated timestamp
        # Convert to naive datetime for PostgreSQL TIMESTAMP columns
        data['updated_at'] = now_est().replace(tzinfo=None)
        
        set_clauses = []
        values = []
        
        for i, (column, value) in enumerate(data.items(), 1):
            set_clauses.append(f"{column} = ${i}")
            values.append(value)
        
        values.append(id_value)
        
        query = f"""
            UPDATE {self.table_name}
            SET {', '.join(set_clauses)}
            WHERE {self.id_column} = ${len(values)}
            AND deleted_at IS NULL
            RETURNING *
        """
        
        # Security check for suspicious patterns
        if not SecurityMonitor.check_query_patterns(query, data):
            raise ValueError("Suspicious query pattern detected")
        
        try:
            result = await conn.fetchrow(query, *values)
            if result:
                logger.info(f"Updated {self.table_name} with {self.id_column}={id_value}")
                
                # Log security event for audit trail
                DatabaseSecurity.log_security_event(
                    "record_updated",
                    {"table": self.table_name, "id": id_value}
                )
            return result
        except Exception as e:
            logger.error(f"Error updating {self.table_name}: {e}")
            raise
    
    async def soft_delete(
        self,
        conn: asyncpg.Connection,
        id_value: Any
    ) -> Optional[asyncpg.Record]:
        """
        Soft delete a record by ID.
        
        Args:
            conn: Database connection
            id_value: Value of the ID to soft delete
        
        Returns:
            Deleted record if found, None otherwise
        """
        query = f"""
            UPDATE {self.table_name}
            SET deleted_at = $1, updated_at = $2
            WHERE {self.id_column} = $3
            AND deleted_at IS NULL
            RETURNING *
        """
        
        # Convert to naive datetime for PostgreSQL TIMESTAMP columns
        now = now_est().replace(tzinfo=None)
        
        try:
            result = await conn.fetchrow(query, now, now, id_value)
            if result:
                logger.info(f"Soft deleted {self.table_name} with {self.id_column}={id_value}")
            return result
        except Exception as e:
            logger.error(f"Error soft deleting {self.table_name}: {e}")
            raise
    
    async def hard_delete(
        self,
        conn: asyncpg.Connection,
        id_value: Any
    ) -> bool:
        """
        Permanently delete a record by ID.
        
        Args:
            conn: Database connection
            id_value: Value of the ID to delete
        
        Returns:
            True if deleted, False otherwise
        """
        query = f"""
            DELETE FROM {self.table_name}
            WHERE {self.id_column} = $1
        """
        
        try:
            result = await conn.execute(query, id_value)
            deleted = result.split()[-1] != '0'
            if deleted:
                logger.info(f"Hard deleted {self.table_name} with {self.id_column}={id_value}")
            return deleted
        except Exception as e:
            logger.error(f"Error hard deleting {self.table_name}: {e}")
            raise
    
    async def count(
        self,
        conn: asyncpg.Connection,
        filters: Optional[Dict[str, Any]] = None,
        include_deleted: bool = False
    ) -> int:
        """
        Count records with optional filtering.
        
        Args:
            conn: Database connection
            filters: Dictionary of column-value pairs to filter by
            include_deleted: Whether to include soft-deleted records
        
        Returns:
            Count of records
        """
        query_parts = [f"SELECT COUNT(*) FROM {self.table_name}"]
        params = []
        param_count = 0
        
        # Build WHERE clause
        where_conditions = []
        if not include_deleted:
            where_conditions.append("deleted_at IS NULL")
        
        if filters:
            for column, value in filters.items():
                param_count += 1
                where_conditions.append(f"{column} = ${param_count}")
                params.append(value)
        
        if where_conditions:
            query_parts.append("WHERE " + " AND ".join(where_conditions))
        
        query = " ".join(query_parts)
        
        try:
            result = await conn.fetchval(query, *params)
            return result
        except Exception as e:
            logger.error(f"Error counting {self.table_name}: {e}")
            raise
    
    async def exists(
        self,
        conn: asyncpg.Connection,
        filters: Dict[str, Any],
        include_deleted: bool = False
    ) -> bool:
        """
        Check if a record exists with given filters.
        
        Args:
            conn: Database connection
            filters: Dictionary of column-value pairs to check
            include_deleted: Whether to include soft-deleted records
        
        Returns:
            True if exists, False otherwise
        """
        query_parts = [f"SELECT EXISTS(SELECT 1 FROM {self.table_name}"]
        params = []
        param_count = 0
        
        # Build WHERE clause
        where_conditions = []
        if not include_deleted:
            where_conditions.append("deleted_at IS NULL")
        
        for column, value in filters.items():
            param_count += 1
            where_conditions.append(f"{column} = ${param_count}")
            params.append(value)
        
        if where_conditions:
            query_parts.append("WHERE " + " AND ".join(where_conditions))
        
        query_parts.append(")")
        query = " ".join(query_parts)
        
        try:
            result = await conn.fetchval(query, *params)
            return result
        except Exception as e:
            logger.error(f"Error checking existence in {self.table_name}: {e}")
            raise