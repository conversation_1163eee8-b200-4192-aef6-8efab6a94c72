-- Migration 018: Add User Formatting Preferences
-- 
-- This migration adds support for user-specific formatting preferences
-- including date formats, currency display, number formatting, and
-- Quebec-specific preferences for the locale formatting system.

-- Create user formatting preferences table
CREATE TABLE user_formatting_preferences (
    formatting_preference_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    
    -- Date and time formatting
    date_format VARCHAR(20) DEFAULT 'medium' CHECK (date_format IN ('short', 'medium', 'long', 'full')),
    time_format VARCHAR(10) DEFAULT 'auto' CHECK (time_format IN ('auto', '12h', '24h')),
    
    -- Currency formatting
    currency_display VARCHAR(20) DEFAULT 'symbol' CHECK (currency_display IN ('symbol', 'code', 'name')),
    number_precision INTEGER DEFAULT 2 CHECK (number_precision >= 0 AND number_precision <= 6),
    
    -- Quebec-specific preferences
    show_quebec_indicators BOOLEAN DEFAULT true,
    use_relative_dates BOOLEAN DEFAULT true,
    
    -- Contact formatting
    phone_format VARCHAR(20) DEFAULT 'national' CHECK (phone_format IN ('national', 'international')),
    address_format VARCHAR(20) DEFAULT 'standard' CHECK (address_format IN ('standard', 'compact', 'multiline')),
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Ensure one preference set per user
    UNIQUE(user_id)
);

-- Create index for user lookups
CREATE INDEX idx_formatting_preferences_user_id ON user_formatting_preferences(user_id);

-- Create index for audit queries
CREATE INDEX idx_formatting_preferences_updated_at ON user_formatting_preferences(updated_at);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_formatting_preferences_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_formatting_preferences_updated_at
    BEFORE UPDATE ON user_formatting_preferences
    FOR EACH ROW
    EXECUTE FUNCTION update_formatting_preferences_updated_at();

-- Add formatting preferences history table for audit trail
CREATE TABLE user_formatting_preferences_history (
    history_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES user_accounts(user_id) ON DELETE CASCADE,
    formatting_preference_id INTEGER REFERENCES user_formatting_preferences(formatting_preference_id) ON DELETE SET NULL,
    
    -- Changed fields
    field_name VARCHAR(50) NOT NULL,
    old_value TEXT,
    new_value TEXT NOT NULL,
    
    -- Change context
    change_source VARCHAR(50) DEFAULT 'user_action' CHECK (change_source IN ('user_action', 'admin_override', 'system_update', 'language_change')),
    change_reason TEXT,
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by_user_id INTEGER REFERENCES user_accounts(user_id) ON DELETE SET NULL,
    
    -- Session tracking
    session_id VARCHAR(255),
    user_agent TEXT,
    ip_address INET
);

-- Create indexes for history queries
CREATE INDEX idx_formatting_preferences_history_user_id ON user_formatting_preferences_history(user_id);
CREATE INDEX idx_formatting_preferences_history_created_at ON user_formatting_preferences_history(created_at);
CREATE INDEX idx_formatting_preferences_history_field_name ON user_formatting_preferences_history(field_name);

-- Create function to log formatting preference changes
CREATE OR REPLACE FUNCTION log_formatting_preference_change()
RETURNS TRIGGER AS $$
DECLARE
    field_name_var VARCHAR(50);
    old_value_var TEXT;
    new_value_var TEXT;
BEGIN
    -- Log each changed field
    IF TG_OP = 'UPDATE' THEN
        -- Date format change
        IF OLD.date_format != NEW.date_format THEN
            INSERT INTO user_formatting_preferences_history 
                (user_id, formatting_preference_id, field_name, old_value, new_value, created_by_user_id)
            VALUES 
                (NEW.user_id, NEW.formatting_preference_id, 'date_format', OLD.date_format, NEW.date_format, NEW.user_id);
        END IF;
        
        -- Time format change
        IF OLD.time_format != NEW.time_format THEN
            INSERT INTO user_formatting_preferences_history 
                (user_id, formatting_preference_id, field_name, old_value, new_value, created_by_user_id)
            VALUES 
                (NEW.user_id, NEW.formatting_preference_id, 'time_format', OLD.time_format, NEW.time_format, NEW.user_id);
        END IF;
        
        -- Currency display change
        IF OLD.currency_display != NEW.currency_display THEN
            INSERT INTO user_formatting_preferences_history 
                (user_id, formatting_preference_id, field_name, old_value, new_value, created_by_user_id)
            VALUES 
                (NEW.user_id, NEW.formatting_preference_id, 'currency_display', OLD.currency_display, NEW.currency_display, NEW.user_id);
        END IF;
        
        -- Number precision change
        IF OLD.number_precision != NEW.number_precision THEN
            INSERT INTO user_formatting_preferences_history 
                (user_id, formatting_preference_id, field_name, old_value, new_value, created_by_user_id)
            VALUES 
                (NEW.user_id, NEW.formatting_preference_id, 'number_precision', OLD.number_precision::TEXT, NEW.number_precision::TEXT, NEW.user_id);
        END IF;
        
        -- Quebec indicators change
        IF OLD.show_quebec_indicators != NEW.show_quebec_indicators THEN
            INSERT INTO user_formatting_preferences_history 
                (user_id, formatting_preference_id, field_name, old_value, new_value, created_by_user_id)
            VALUES 
                (NEW.user_id, NEW.formatting_preference_id, 'show_quebec_indicators', OLD.show_quebec_indicators::TEXT, NEW.show_quebec_indicators::TEXT, NEW.user_id);
        END IF;
        
        -- Relative dates change
        IF OLD.use_relative_dates != NEW.use_relative_dates THEN
            INSERT INTO user_formatting_preferences_history 
                (user_id, formatting_preference_id, field_name, old_value, new_value, created_by_user_id)
            VALUES 
                (NEW.user_id, NEW.formatting_preference_id, 'use_relative_dates', OLD.use_relative_dates::TEXT, NEW.use_relative_dates::TEXT, NEW.user_id);
        END IF;
        
        -- Phone format change
        IF OLD.phone_format != NEW.phone_format THEN
            INSERT INTO user_formatting_preferences_history 
                (user_id, formatting_preference_id, field_name, old_value, new_value, created_by_user_id)
            VALUES 
                (NEW.user_id, NEW.formatting_preference_id, 'phone_format', OLD.phone_format, NEW.phone_format, NEW.user_id);
        END IF;
        
        -- Address format change
        IF OLD.address_format != NEW.address_format THEN
            INSERT INTO user_formatting_preferences_history 
                (user_id, formatting_preference_id, field_name, old_value, new_value, created_by_user_id)
            VALUES 
                (NEW.user_id, NEW.formatting_preference_id, 'address_format', OLD.address_format, NEW.address_format, NEW.user_id);
        END IF;
        
    ELSIF TG_OP = 'INSERT' THEN
        -- Log initial preference creation
        INSERT INTO user_formatting_preferences_history 
            (user_id, formatting_preference_id, field_name, old_value, new_value, created_by_user_id, change_source)
        VALUES 
            (NEW.user_id, NEW.formatting_preference_id, 'preferences_created', NULL, 'initial_setup', NEW.user_id, 'system_update');
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for formatting preference changes
CREATE TRIGGER trigger_log_formatting_preference_change
    AFTER INSERT OR UPDATE ON user_formatting_preferences
    FOR EACH ROW
    EXECUTE FUNCTION log_formatting_preference_change();

-- Create view for current user formatting preferences with language info
CREATE VIEW user_formatting_preferences_with_language AS
SELECT 
    ufp.*,
    u.email,
    u.preferred_language,
    u.quebec_french_preference,
    u.language_auto_detect
FROM user_formatting_preferences ufp
JOIN users u ON ufp.user_id = u.user_id
WHERE u.deleted_at IS NULL;

-- Grant permissions for the application user

-- Insert default formatting preferences for existing users
-- This ensures all existing users have formatting preferences
INSERT INTO user_formatting_preferences (user_id, date_format, time_format, currency_display, number_precision, show_quebec_indicators, use_relative_dates, phone_format, address_format)
SELECT 
    u.user_id,
    'medium' as date_format,
    CASE 
        WHEN u.preferred_language = 'fr' THEN '24h'
        ELSE '12h'
    END as time_format,
    'symbol' as currency_display,
    2 as number_precision,
    true as show_quebec_indicators,
    true as use_relative_dates,
    'national' as phone_format,
    'standard' as address_format
FROM users u
WHERE u.deleted_at IS NULL
  AND NOT EXISTS (
    SELECT 1 FROM user_formatting_preferences ufp 
    WHERE ufp.user_id = u.user_id
  );

-- Create analytics view for formatting preference usage
CREATE VIEW formatting_preferences_analytics AS
SELECT 
    date_format,
    COUNT(*) as users_count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as percentage
FROM user_formatting_preferences_with_language
GROUP BY date_format
UNION ALL
SELECT 
    'time_format_' || time_format as preference_type,
    COUNT(*) as users_count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as percentage
FROM user_formatting_preferences_with_language
GROUP BY time_format
UNION ALL
SELECT 
    'currency_' || currency_display as preference_type,
    COUNT(*) as users_count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as percentage
FROM user_formatting_preferences_with_language
GROUP BY currency_display;

-- Grant read access to analytics view

-- Add comments for documentation
COMMENT ON TABLE user_formatting_preferences IS 'User-specific formatting preferences for dates, currency, numbers, and Quebec-specific formatting';
COMMENT ON TABLE user_formatting_preferences_history IS 'Audit trail for formatting preference changes';
COMMENT ON VIEW user_formatting_preferences_with_language IS 'User formatting preferences joined with language preferences';
COMMENT ON VIEW formatting_preferences_analytics IS 'Analytics view showing formatting preference distribution';

COMMENT ON COLUMN user_formatting_preferences.date_format IS 'Date display format: short, medium, long, or full';
COMMENT ON COLUMN user_formatting_preferences.time_format IS 'Time display format: auto (based on language), 12h, or 24h';
COMMENT ON COLUMN user_formatting_preferences.currency_display IS 'Currency display style: symbol ($), code (CAD), or name (Canadian Dollar)';
COMMENT ON COLUMN user_formatting_preferences.number_precision IS 'Number of decimal places for currency and numbers (0-6)';
COMMENT ON COLUMN user_formatting_preferences.show_quebec_indicators IS 'Show Quebec-specific indicators (area codes, etc.)';
COMMENT ON COLUMN user_formatting_preferences.use_relative_dates IS 'Use relative date formatting (2 days ago vs specific date)';
COMMENT ON COLUMN user_formatting_preferences.phone_format IS 'Phone number format: national or international';
COMMENT ON COLUMN user_formatting_preferences.address_format IS 'Address display format: standard, compact, or multiline';