import React from 'react';
import { useTranslation } from 'react-i18next';
import { X, Mail, Phone, MapPin, Star, Clock, DollarSign, CheckCircle, XCircle, AlertCircle, FileText, Calendar, Award } from 'lucide-react';
import { Modal } from '../common/Modal';
import { Badge } from '../common/Badge';
import Button from '../common/Button';
import { Tutor, VerificationDocument } from '../../services/tutorService';
import { format } from 'date-fns';

interface TutorDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  tutor: Tutor;
  onEdit?: (tutor: Tu<PERSON>) => void;
  canEdit?: boolean;
  canVerify?: boolean;
}

const TutorDetailsModal: React.FC<TutorDetailsModalProps> = ({
  isOpen,
  onClose,
  tutor,
  onEdit,
  canEdit = false,
  canVerify = false
}) => {
  const { t } = useTranslation();

  const getVerificationBadge = () => {
    if (!tutor.is_active) {
      return <Badge variant="secondary" size="sm">{t('common.inactive')}</Badge>;
    }
    switch (tutor.verification_status) {
      case 'verified':
        return <Badge variant="success" size="sm" icon={<CheckCircle className="w-3 h-3" />}>{t('users.tutors.verified')}</Badge>;
      case 'pending':
        return <Badge variant="warning" size="sm" icon={<AlertCircle className="w-3 h-3" />}>{t('users.tutors.pending')}</Badge>;
      case 'rejected':
        return <Badge variant="danger" size="sm" icon={<XCircle className="w-3 h-3" />}>{t('users.tutors.rejected')}</Badge>;
      default:
        return null;
    }
  };

  const getDocumentStatusBadge = (doc: VerificationDocument) => {
    switch (doc.status) {
      case 'approved':
        return <Badge variant="success" size="sm">{t('common.approved')}</Badge>;
      case 'pending':
        return <Badge variant="warning" size="sm">{t('common.pending')}</Badge>;
      case 'rejected':
        return <Badge variant="danger" size="sm">{t('common.rejected')}</Badge>;
      default:
        return null;
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-6">
          <div className="flex items-center">
            <img
              className="h-16 w-16 rounded-full object-cover"
              src={tutor.profile_picture || '/default-avatar.png'}
              alt={`${tutor.first_name} ${tutor.last_name}`}
              onError={(e) => {
                (e.target as HTMLImageElement).src = '/default-avatar.png';
              }}
            />
            <div className="ml-4">
              <h2 className="text-2xl font-bold text-gray-900">
                {tutor.first_name} {tutor.last_name}
              </h2>
              <div className="flex items-center space-x-2">
                {getVerificationBadge()}
                <span className="text-sm text-gray-600">
                  {t('users.tutors.experience', { years: tutor.experience_years || 0 })}
                </span>
              </div>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left Column */}
          <div className="space-y-6">
            {/* Contact Information */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                {t('users.tutors.contactInfo')}
              </h3>
              <div className="space-y-2">
                <div className="flex items-center text-gray-600">
                  <Mail className="w-5 h-5 mr-3" />
                  <span>{tutor.email}</span>
                </div>
                {tutor.phone_number && (
                  <div className="flex items-center text-gray-600">
                    <Phone className="w-5 h-5 mr-3" />
                    <span>{tutor.phone_number}</span>
                  </div>
                )}
                <div className="flex items-center text-gray-600">
                  <MapPin className="w-5 h-5 mr-3" />
                  <span>
                    {tutor.city || 'Not specified'}, {tutor.province || 'QC'} {tutor.postal_code}
                  </span>
                </div>
              </div>
            </div>

            {/* Bio */}
            {tutor.bio && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  {t('users.tutors.bio')}
                </h3>
                <p className="text-gray-600">{tutor.bio}</p>
              </div>
            )}

            {/* Specializations */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                {t('users.tutors.specializations')}
              </h3>
              <div className="flex flex-wrap gap-2">
                {tutor.specializations?.map((spec, idx) => (
                  <Badge key={idx} variant="info">{spec}</Badge>
                ))}
              </div>
            </div>

            {/* Languages */}
            {tutor.languages && tutor.languages.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  {t('users.tutors.languages')}
                </h3>
                <div className="flex flex-wrap gap-2">
                  {tutor.languages.map((lang, idx) => (
                    <Badge key={idx} variant="secondary">{lang}</Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Rate */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                {t('users.tutors.hourlyRate')}
              </h3>
              <div className="flex items-center">
                <DollarSign className="w-6 h-6 text-gray-400 mr-1" />
                <span className="text-2xl font-bold text-gray-900">
                  {tutor.hourly_rate || 0}
                </span>
                <span className="text-gray-600 ml-1">/hr CAD</span>
              </div>
            </div>
          </div>

          {/* Right Column */}
          <div className="space-y-6">
            {/* Verification Documents */}
            {tutor.verification_documents && tutor.verification_documents.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  {t('users.tutors.verificationDocuments')}
                </h3>
                <div className="space-y-2">
                  {tutor.verification_documents.map((doc) => (
                    <div key={doc.document_id} className="bg-gray-50 rounded-lg p-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <FileText className="w-5 h-5 text-gray-400 mr-2" />
                          <div>
                            <div className="font-medium text-gray-900">{doc.document_name}</div>
                            <div className="text-sm text-gray-600">
                              {t(`users.tutors.documentTypes.${doc.document_type}`)}
                            </div>
                          </div>
                        </div>
                        {getDocumentStatusBadge(doc)}
                      </div>
                      {doc.reviewer_notes && (
                        <div className="mt-2 text-sm text-gray-600 italic">
                          {doc.reviewer_notes}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Education */}
            {tutor.education && tutor.education.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  {t('users.tutors.education')}
                </h3>
                <div className="space-y-3">
                  {tutor.education.map((edu) => (
                    <div key={edu.education_id} className="bg-gray-50 rounded-lg p-3">
                      <div className="font-medium text-gray-900">{edu.degree}</div>
                      <div className="text-sm text-gray-600">{edu.field_of_study}</div>
                      <div className="text-sm text-gray-500 mt-1">
                        {edu.institution} • {edu.start_year} - {edu.is_current ? t('common.present') : edu.end_year}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Experience */}
            {tutor.experience && tutor.experience.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  {t('users.tutors.experience')}
                </h3>
                <div className="space-y-3">
                  {tutor.experience.map((exp) => (
                    <div key={exp.experience_id} className="bg-gray-50 rounded-lg p-3">
                      <div className="font-medium text-gray-900">{exp.position}</div>
                      <div className="text-sm text-gray-600">{exp.organization}</div>
                      {exp.description && (
                        <div className="text-sm text-gray-600 mt-1">{exp.description}</div>
                      )}
                      <div className="text-sm text-gray-500 mt-1">
                        {format(new Date(exp.start_date), 'MMM yyyy')} - {
                          exp.is_current ? t('common.present') : format(new Date(exp.end_date!), 'MMM yyyy')
                        }
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Service Areas */}
            {tutor.service_areas && tutor.service_areas.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  {t('users.tutors.serviceAreas')}
                </h3>
                <div className="space-y-2">
                  {tutor.service_areas.map((area, idx) => (
                    <div key={idx} className="flex items-center text-gray-600">
                      <MapPin className="w-4 h-4 mr-2" />
                      <span>{area.postal_code} - {area.radius_km}km radius</span>
                      {area.is_primary && (
                        <Badge variant="info" size="sm" className="ml-2">
                          {t('common.primary')}
                        </Badge>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-4 gap-4 mt-6 pt-6 border-t">
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Calendar className="w-8 h-8 text-gray-400" />
            </div>
            <div className="text-2xl font-bold text-gray-900">0</div>
            <div className="text-sm text-gray-600">{t('users.tutors.totalSessions')}</div>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Star className="w-8 h-8 text-gray-400" />
            </div>
            <div className="text-2xl font-bold text-gray-900">0.0</div>
            <div className="text-sm text-gray-600">{t('users.tutors.avgRating')}</div>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Clock className="w-8 h-8 text-gray-400" />
            </div>
            <div className="text-2xl font-bold text-gray-900">0</div>
            <div className="text-sm text-gray-600">{t('users.tutors.hoursThisMonth')}</div>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Award className="w-8 h-8 text-gray-400" />
            </div>
            <div className="text-2xl font-bold text-gray-900">0</div>
            <div className="text-sm text-gray-600">{t('users.tutors.activeStudents')}</div>
          </div>
        </div>

        {/* Actions */}
        <div className="mt-6 pt-6 border-t flex justify-between">
          <div className="space-x-3">
            {canVerify && tutor.verification_status === 'pending' && (
              <>
                <Button variant="success" size="sm">
                  <CheckCircle className="w-4 h-4 mr-2" />
                  {t('users.tutors.approve')}
                </Button>
                <Button variant="danger" size="sm">
                  <XCircle className="w-4 h-4 mr-2" />
                  {t('users.tutors.reject')}
                </Button>
              </>
            )}
          </div>
          <div className="space-x-3">
            <Button variant="secondary" onClick={onClose}>
              {t('common.close')}
            </Button>
            {canEdit && onEdit && (
              <Button variant="primary" onClick={() => onEdit(tutor)}>
                {t('common.edit')}
              </Button>
            )}
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default TutorDetailsModal;