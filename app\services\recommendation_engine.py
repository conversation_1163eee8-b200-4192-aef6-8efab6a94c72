"""
Recommendation engine for personalized service suggestions.
"""

import logging
from datetime import datetime, date, timedelta
from decimal import Decimal
from typing import List, Dict, Any, Optional, Tuple
from collections import defaultdict
import numpy as np
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from app.models.service_models import (
    SubjectArea, ServiceType, ServiceLevel,
    ServiceRecommendationRequest
)
from app.core.exceptions import DatabaseError
from app.core.timezone import now_est

logger = logging.getLogger(__name__)


class ServiceRecommendationEngine:
    """Engine for generating personalized service recommendations."""
    
    def __init__(self, db_session: AsyncSession):
        self.db = db_session
        
        # Recommendation weights
        self.weights = {
            'past_performance': 0.25,      # How well student did with similar services
            'tutor_match': 0.20,           # Compatibility with past tutors
            'schedule_fit': 0.15,          # Fits with preferred schedule
            'peer_similarity': 0.15,       # What similar students chose
            'academic_progression': 0.15,  # Logical academic progression
            'price_sensitivity': 0.10      # Price within usual range
        }
    
    async def generate_recommendations(
        self,
        request: ServiceRecommendationRequest
    ) -> List[Dict[str, Any]]:
        """Generate personalized service recommendations."""
        try:
            # Get client history and preferences
            client_profile = await self._get_client_profile(request.client_id)
            past_appointments = await self._get_past_appointments(request.client_id, request.dependant_id)
            
            # Get available services
            available_services = await self._get_available_services(
                request.preferred_subject_areas,
                request.preferred_service_types
            )
            
            # Score each service
            scored_services = []
            for service in available_services:
                score = await self._calculate_recommendation_score(
                    service,
                    client_profile,
                    past_appointments,
                    request
                )
                
                if score > 0.3:  # Minimum threshold
                    # Find suitable tutors
                    suitable_tutors = await self._find_suitable_tutors(
                        service,
                        client_profile,
                        past_appointments,
                        request
                    )
                    
                    if suitable_tutors:
                        scored_services.append({
                            'service': service,
                            'score': score,
                            'tutors': suitable_tutors[:5],  # Top 5 tutors
                            'reasons': self._generate_recommendation_reasons(
                                service, score, client_profile
                            )
                        })
            
            # Sort by score and take top recommendations
            scored_services.sort(key=lambda x: x['score'], reverse=True)
            
            # Format recommendations
            recommendations = []
            for item in scored_services[:request.max_recommendations]:
                recommendations.append({
                    'service': item['service'],
                    'recommended_tutors': item['tutors'],
                    'match_score': round(item['score'], 2),
                    'reasons': item['reasons'],
                    'estimated_cost': self._estimate_cost(item['service'], item['tutors']),
                    'availability_summary': self._get_availability_summary(item['tutors'])
                })
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            raise
    
    async def _get_client_profile(self, client_id: int) -> Dict[str, Any]:
        """Get comprehensive client profile for recommendations."""
        try:
            query = text("""
                SELECT 
                    c.client_id,
                    c.preferred_language,
                    c.timezone,
                    u.created_at as member_since,
                    cp.emergency_contact_name,
                    cp.special_instructions,
                    -- Location preferences
                    lp.postal_code,
                    lp.max_travel_distance_km,
                    -- Scheduling preferences
                    sp.preferred_frequency,
                    sp.preferred_duration,
                    sp.preferred_time_slots,
                    sp.preferred_days,
                    -- Financial indicators (anonymized)
                    COUNT(DISTINCT pp.package_purchase_id) as packages_purchased,
                    AVG(CASE WHEN a.status = 'completed' THEN 
                        EXTRACT(EPOCH FROM (a.end_time - a.start_time))/3600 * tsr.hourly_rate 
                    END) as avg_session_spend
                FROM clients c
                JOIN users u ON c.user_id = u.user_id
                LEFT JOIN client_profiles cp ON c.client_id = cp.client_id
                LEFT JOIN location_preferences lp ON c.client_id = lp.client_id
                LEFT JOIN scheduling_preferences sp ON c.client_id = sp.client_id
                LEFT JOIN package_purchases pp ON c.client_id = pp.client_id
                LEFT JOIN appointments a ON c.client_id = a.client_id
                LEFT JOIN tutor_service_rates tsr ON a.tutor_service_rate_id = tsr.tutor_service_rate_id
                WHERE c.client_id = :client_id
                GROUP BY c.client_id, c.preferred_language, c.timezone, u.created_at,
                         cp.emergency_contact_name, cp.special_instructions,
                         lp.postal_code, lp.max_travel_distance_km,
                         sp.preferred_frequency, sp.preferred_duration, 
                         sp.preferred_time_slots, sp.preferred_days
            """)
            
            result = await self.db.execute(query, {'client_id': client_id})
            row = result.fetchone()
            
            if row:
                return dict(row._mapping)
            else:
                return {'client_id': client_id}  # Minimal profile for new clients
                
        except Exception as e:
            logger.error(f"Error getting client profile: {e}")
            return {'client_id': client_id}
    
    async def _get_past_appointments(
        self,
        client_id: int,
        dependant_id: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """Get past appointment history for analysis."""
        try:
            conditions = ["a.client_id = :client_id"]
            params = {'client_id': client_id}
            
            if dependant_id:
                conditions.append("a.dependant_id = :dependant_id")
                params['dependant_id'] = dependant_id
            
            query = text(f"""
                SELECT 
                    a.appointment_id,
                    a.tutor_id,
                    a.scheduled_date,
                    a.status,
                    a.subject_area,
                    a.session_type,
                    a.location_type,
                    EXTRACT(EPOCH FROM (a.end_time - a.start_time))/60 as duration_minutes,
                    -- Tutor info
                    t.specialties,
                    -- Service info
                    sc.service_name,
                    sc.service_level,
                    -- Rating if exists
                    ar.rating,
                    ar.would_recommend,
                    -- Cost
                    tsr.hourly_rate
                FROM appointments a
                JOIN tutors t ON a.tutor_id = t.tutor_id
                LEFT JOIN service_catalog sc ON a.service_catalog_id = sc.service_catalog_id
                LEFT JOIN appointment_reviews ar ON a.appointment_id = ar.appointment_id
                LEFT JOIN tutor_service_rates tsr ON a.tutor_service_rate_id = tsr.tutor_service_rate_id
                WHERE {' AND '.join(conditions)}
                  AND a.status IN ('completed', 'cancelled')
                  AND a.scheduled_date >= :date_cutoff
                ORDER BY a.scheduled_date DESC
                LIMIT 50
            """)
            
            params['date_cutoff'] = date.today() - timedelta(days=180)  # Last 6 months
            
            result = await self.db.execute(query, params)
            return [dict(row._mapping) for row in result.fetchall()]
            
        except Exception as e:
            logger.error(f"Error getting past appointments: {e}")
            return []
    
    async def _get_available_services(
        self,
        subject_areas: Optional[List[SubjectArea]] = None,
        service_types: Optional[List[ServiceType]] = None
    ) -> List[Dict[str, Any]]:
        """Get available services matching criteria."""
        try:
            conditions = ["sc.is_active = true", "sc.deleted_at IS NULL"]
            params = {}
            
            if subject_areas:
                placeholders = ','.join([f':subject_{i}' for i in range(len(subject_areas))])
                conditions.append(f"sc.subject_area IN ({placeholders})")
                for i, subject in enumerate(subject_areas):
                    params[f'subject_{i}'] = subject.value
            
            if service_types:
                type_placeholders = ','.join([f':type_{i}' for i in range(len(service_types))])
                conditions.append(f"sc.service_type IN ({type_placeholders})")
                for i, stype in enumerate(service_types):
                    params[f'type_{i}'] = stype.value
            
            query = text(f"""
                SELECT 
                    sc.*,
                    -- Popularity metrics
                    COUNT(DISTINCT a.appointment_id) as total_bookings,
                    AVG(ar.rating) as avg_rating,
                    COUNT(DISTINCT tsr.tutor_id) as available_tutors,
                    MIN(tsr.hourly_rate) as min_rate,
                    AVG(tsr.hourly_rate) as avg_rate
                FROM service_catalog sc
                LEFT JOIN tutor_service_rates tsr ON sc.service_catalog_id = tsr.service_catalog_id
                    AND tsr.is_available = true AND tsr.deleted_at IS NULL
                LEFT JOIN appointments a ON sc.service_catalog_id = a.service_catalog_id
                    AND a.status = 'completed'
                LEFT JOIN appointment_reviews ar ON a.appointment_id = ar.appointment_id
                WHERE {' AND '.join(conditions)}
                GROUP BY sc.service_catalog_id
                HAVING COUNT(DISTINCT tsr.tutor_id) > 0
            """)
            
            result = await self.db.execute(query, params)
            return [dict(row._mapping) for row in result.fetchall()]
            
        except Exception as e:
            logger.error(f"Error getting available services: {e}")
            return []
    
    async def _calculate_recommendation_score(
        self,
        service: Dict[str, Any],
        client_profile: Dict[str, Any],
        past_appointments: List[Dict[str, Any]],
        request: ServiceRecommendationRequest
    ) -> float:
        """Calculate recommendation score for a service."""
        scores = {}
        
        # 1. Past Performance Score
        scores['past_performance'] = self._calculate_past_performance_score(
            service, past_appointments
        )
        
        # 2. Tutor Match Score
        scores['tutor_match'] = await self._calculate_tutor_match_score(
            service, past_appointments, request.include_past_tutors
        )
        
        # 3. Schedule Fit Score
        scores['schedule_fit'] = self._calculate_schedule_fit_score(
            service, client_profile
        )
        
        # 4. Peer Similarity Score
        scores['peer_similarity'] = await self._calculate_peer_similarity_score(
            service, client_profile
        )
        
        # 5. Academic Progression Score
        scores['academic_progression'] = self._calculate_progression_score(
            service, past_appointments, client_profile
        )
        
        # 6. Price Sensitivity Score
        scores['price_sensitivity'] = self._calculate_price_score(
            service, client_profile
        )
        
        # Calculate weighted total
        total_score = sum(
            score * self.weights[factor]
            for factor, score in scores.items()
        )
        
        return total_score
    
    def _calculate_past_performance_score(
        self,
        service: Dict[str, Any],
        past_appointments: List[Dict[str, Any]]
    ) -> float:
        """Score based on past performance in similar subjects."""
        if not past_appointments:
            return 0.5  # Neutral score for new clients
        
        # Find appointments in same or related subjects
        related_appointments = [
            apt for apt in past_appointments
            if apt['subject_area'] == service['subject_area']
        ]
        
        if not related_appointments:
            # Look for related subjects
            subject_relations = {
                'mathematics': ['physics', 'chemistry', 'computer_science'],
                'physics': ['mathematics', 'chemistry'],
                'chemistry': ['physics', 'biology'],
                'french': ['english'],
                'english': ['french']
            }
            
            related_subjects = subject_relations.get(service['subject_area'], [])
            related_appointments = [
                apt for apt in past_appointments
                if apt['subject_area'] in related_subjects
            ]
        
        if not related_appointments:
            return 0.5
        
        # Calculate score based on ratings and completion rate
        ratings = [apt['rating'] for apt in related_appointments if apt['rating']]
        completion_rate = sum(
            1 for apt in related_appointments
            if apt['status'] == 'completed'
        ) / len(related_appointments)
        
        avg_rating = sum(ratings) / len(ratings) if ratings else 3.0
        
        # Normalize to 0-1 scale
        rating_score = (avg_rating - 1) / 4  # 1-5 scale to 0-1
        
        return 0.7 * rating_score + 0.3 * completion_rate
    
    async def _calculate_tutor_match_score(
        self,
        service: Dict[str, Any],
        past_appointments: List[Dict[str, Any]],
        include_past_tutors: bool
    ) -> float:
        """Score based on tutor compatibility."""
        if not include_past_tutors:
            return 0.5
        
        # Get tutors client has worked with successfully
        successful_tutors = set()
        tutor_ratings = defaultdict(list)
        
        for apt in past_appointments:
            if apt['status'] == 'completed' and apt['rating']:
                successful_tutors.add(apt['tutor_id'])
                tutor_ratings[apt['tutor_id']].append(apt['rating'])
        
        if not successful_tutors:
            return 0.5
        
        # Check how many of these tutors offer this service
        query = text("""
            SELECT COUNT(DISTINCT tutor_id) as matching_tutors
            FROM tutor_service_rates
            WHERE service_catalog_id = :service_id
              AND tutor_id IN :tutor_ids
              AND is_available = true
              AND deleted_at IS NULL
        """)
        
        result = await self.db.execute(query, {
            'service_id': service['service_catalog_id'],
            'tutor_ids': tuple(successful_tutors)
        })
        
        row = result.fetchone()
        matching_tutors = row.matching_tutors if row else 0
        
        if matching_tutors == 0:
            return 0.3  # Lower score if no past tutors offer this
        
        # Calculate average rating with these tutors
        total_ratings = []
        for tutor_id in successful_tutors:
            total_ratings.extend(tutor_ratings[tutor_id])
        
        avg_rating = sum(total_ratings) / len(total_ratings)
        
        # Score based on availability and past success
        availability_score = min(matching_tutors / 3, 1.0)  # Up to 3 is good
        success_score = (avg_rating - 1) / 4
        
        return 0.6 * success_score + 0.4 * availability_score
    
    def _calculate_schedule_fit_score(
        self,
        service: Dict[str, Any],
        client_profile: Dict[str, Any]
    ) -> float:
        """Score based on schedule compatibility."""
        # Default schedule preferences
        preferred_times = client_profile.get('preferred_time_slots', ['afternoon', 'evening'])
        preferred_days = client_profile.get('preferred_days', [])
        preferred_duration = client_profile.get('preferred_duration', '60')
        
        score = 0.5  # Base score
        
        # Check if service duration matches preference
        if service['default_duration_minutes'] == int(preferred_duration):
            score += 0.2
        elif abs(service['default_duration_minutes'] - int(preferred_duration)) <= 30:
            score += 0.1
        
        # Check if service type fits schedule
        if service['service_type'] == 'online':
            score += 0.2  # Online is most flexible
        elif service['service_type'] == 'hybrid':
            score += 0.1
        
        # Adjust based on service level and typical scheduling
        if service['service_level'] == 'elementary':
            if 'afternoon' in preferred_times or 'early_afternoon' in preferred_times:
                score += 0.1
        elif service['service_level'] in ['college', 'university', 'adult']:
            if 'evening' in preferred_times:
                score += 0.1
        
        return min(score, 1.0)
    
    async def _calculate_peer_similarity_score(
        self,
        service: Dict[str, Any],
        client_profile: Dict[str, Any]
    ) -> float:
        """Score based on what similar clients chose."""
        try:
            # Find similar clients based on basic profile
            
            query = text("""
                SELECT COUNT(DISTINCT c.client_id) as similar_clients
                FROM clients c
                JOIN appointments a ON c.client_id = a.client_id
                WHERE a.service_catalog_id = :service_id
                  AND a.status = 'completed'
                  AND c.client_id != :client_id
            """)
            
            result = await self.db.execute(query, {
                'service_id': service['service_catalog_id'],
                'client_id': client_profile['client_id']
            })
            
            row = result.fetchone()
            similar_clients = row.similar_clients if row else 0
            
            # Normalize to 0-1 scale
            return min(similar_clients / 10, 1.0)  # 10+ similar clients = max score
            
        except Exception:
            return 0.5  # Default neutral score
    
    def _calculate_progression_score(
        self,
        service: Dict[str, Any],
        past_appointments: List[Dict[str, Any]],
        client_profile: Dict[str, Any]
    ) -> float:
        """Score based on logical academic progression."""
        # Academic progression paths
        progression_paths = {
            'elementary': ['high_school'],
            'high_school': ['college', 'university'],
            'college': ['university', 'adult'],
            'university': ['adult']
        }
        
        # Subject progression
        subject_progression = {
            'mathematics': ['physics', 'chemistry', 'computer_science', 'economics'],
            'physics': ['chemistry', 'engineering'],
            'chemistry': ['biology'],
            'french': ['french'],  # Continue same subject
            'english': ['english']
        }
        
        # Get current level from past appointments
        if past_appointments:
            recent_levels = [
                apt.get('service_level', 'high_school')
                for apt in past_appointments[:5]
                if apt.get('service_level')
            ]
            current_level = max(set(recent_levels), key=recent_levels.count) if recent_levels else 'high_school'
        else:
            current_level = 'high_school'  # Default level
        
        # Check if service is appropriate progression
        score = 0.5
        
        # Level progression
        if service['service_level'] == current_level:
            score = 0.8  # Same level is usually good
        elif service['service_level'] in progression_paths.get(current_level, []):
            score = 0.9  # Natural progression
        
        # Subject progression
        if past_appointments:
            recent_subjects = [apt['subject_area'] for apt in past_appointments[:10]]
            if recent_subjects:
                most_common_subject = max(set(recent_subjects), key=recent_subjects.count)
                
                if service['subject_area'] == most_common_subject:
                    score = min(score + 0.1, 1.0)  # Continuing same subject
                elif service['subject_area'] in subject_progression.get(most_common_subject, []):
                    score = min(score + 0.05, 1.0)  # Related subject
        
        return score
    
    def _calculate_price_score(
        self,
        service: Dict[str, Any],
        client_profile: Dict[str, Any]
    ) -> float:
        """Score based on price sensitivity."""
        avg_spend = client_profile.get('avg_session_spend', 50.0)
        service_avg_rate = float(service.get('avg_rate', 45.0))
        
        if avg_spend == 0:
            # New client, use middle-range pricing
            return 0.7 if 30 <= service_avg_rate <= 60 else 0.5
        
        # Calculate price difference ratio
        price_ratio = service_avg_rate / avg_spend if avg_spend > 0 else 1.0
        
        if 0.8 <= price_ratio <= 1.2:
            return 1.0  # Within 20% of usual spend
        elif 0.6 <= price_ratio <= 1.4:
            return 0.7  # Within 40%
        elif 0.4 <= price_ratio <= 1.6:
            return 0.4  # Getting expensive/cheap
        else:
            return 0.2  # Too far from usual range
    
    async def _find_suitable_tutors(
        self,
        service: Dict[str, Any],
        client_profile: Dict[str, Any],
        past_appointments: List[Dict[str, Any]],
        request: ServiceRecommendationRequest
    ) -> List[Dict[str, Any]]:
        """Find suitable tutors for the service."""
        try:
            # Get location constraint
            max_distance = None
            if request.location_postal_code and service['service_type'] in ['in_person', 'library']:
                max_distance = client_profile.get('max_travel_distance_km', 15)
            
            # Base query for available tutors
            query = text("""
                SELECT 
                    t.tutor_id,
                    u.first_name,
                    u.last_name,
                    t.specialties,
                    tsr.hourly_rate,
                    tsr.pricing_tier,
                    tsr.max_distance_km,
                    -- Performance metrics
                    COUNT(DISTINCT a.appointment_id) as total_sessions,
                    AVG(ar.rating) as avg_rating,
                    COUNT(DISTINCT CASE WHEN a.client_id = :client_id THEN a.appointment_id END) as past_sessions_with_client,
                    -- Availability indicator (simplified)
                    COUNT(DISTINCT CASE 
                        WHEN a.scheduled_date >= CURRENT_DATE 
                        AND a.scheduled_date < CURRENT_DATE + INTERVAL '7 days'
                        AND a.status != 'cancelled'
                        THEN a.appointment_id 
                    END) as sessions_this_week
                FROM tutors t
                JOIN users u ON t.user_id = u.user_id
                JOIN tutor_service_rates tsr ON t.tutor_id = tsr.tutor_id
                LEFT JOIN appointments a ON t.tutor_id = a.tutor_id
                LEFT JOIN appointment_reviews ar ON a.appointment_id = ar.appointment_id
                WHERE tsr.service_catalog_id = :service_id
                  AND tsr.is_available = true
                  AND tsr.deleted_at IS NULL
                  AND t.is_active = true
                GROUP BY t.tutor_id, u.first_name, u.last_name, t.specialties,
                         tsr.hourly_rate, tsr.pricing_tier, tsr.max_distance_km
                HAVING AVG(ar.rating) IS NULL OR AVG(ar.rating) >= 3.5
            """)
            
            result = await self.db.execute(query, {
                'service_id': service['service_catalog_id'],
                'client_id': client_profile.get('client_id', 0)
            })
            
            tutors = []
            for row in result.fetchall():
                tutor = dict(row._mapping)
                
                # Calculate match score for this tutor
                tutor['match_score'] = self._calculate_tutor_score(
                    tutor, past_appointments, client_profile
                )
                
                # Add availability status
                if tutor['sessions_this_week'] < 10:
                    tutor['availability_status'] = 'available'
                elif tutor['sessions_this_week'] < 15:
                    tutor['availability_status'] = 'limited'
                else:
                    tutor['availability_status'] = 'busy'
                
                tutors.append(tutor)
            
            # Sort by match score
            tutors.sort(key=lambda t: t['match_score'], reverse=True)
            
            return tutors
            
        except Exception as e:
            logger.error(f"Error finding suitable tutors: {e}")
            return []
    
    def _calculate_tutor_score(
        self,
        tutor: Dict[str, Any],
        past_appointments: List[Dict[str, Any]],
        client_profile: Dict[str, Any]
    ) -> float:
        """Calculate match score for a specific tutor."""
        score = 0.5  # Base score
        
        # Past experience with client
        if tutor['past_sessions_with_client'] > 0:
            score += 0.2
            
            # Check ratings from past sessions
            past_ratings = [
                apt['rating'] for apt in past_appointments
                if apt['tutor_id'] == tutor['tutor_id'] and apt['rating']
            ]
            if past_ratings:
                avg_past_rating = sum(past_ratings) / len(past_ratings)
                score += 0.1 * (avg_past_rating - 3) / 2  # -0.1 to +0.1
        
        # Overall rating
        if tutor['avg_rating']:
            rating_bonus = (float(tutor['avg_rating']) - 3) / 2 * 0.2  # -0.2 to +0.2
            score += rating_bonus
        
        # Experience level
        if tutor['total_sessions'] >= 50:
            score += 0.1
        elif tutor['total_sessions'] >= 20:
            score += 0.05
        
        # Availability
        if tutor['availability_status'] == 'available':
            score += 0.05
        elif tutor['availability_status'] == 'busy':
            score -= 0.05
        
        return min(max(score, 0), 1)  # Clamp to 0-1
    
    def _generate_recommendation_reasons(
        self,
        service: Dict[str, Any],
        score: float,
        client_profile: Dict[str, Any]
    ) -> List[str]:
        """Generate human-readable reasons for recommendation."""
        reasons = []
        
        if score > 0.8:
            reasons.append("Highly recommended based on your profile")
        elif score > 0.6:
            reasons.append("Good match for your needs")
        
        # Subject-based reasons
        if service['avg_rating'] and float(service['avg_rating']) >= 4.5:
            reasons.append(f"Highly rated service ({float(service['avg_rating']):.1f}/5.0)")
        
        if service['total_bookings'] and service['total_bookings'] > 50:
            reasons.append("Popular choice among students")
        
        # Level-based reasons
        level = client_profile.get('current_level')
        if level and service['service_level'] == level:
            reasons.append(f"Matches your current {level} level")
        
        # Availability reasons
        if service['available_tutors'] and service['available_tutors'] > 10:
            reasons.append(f"{service['available_tutors']} qualified tutors available")
        
        return reasons
    
    def _estimate_cost(
        self,
        service: Dict[str, Any],
        tutors: List[Dict[str, Any]]
    ) -> Dict[str, float]:
        """Estimate cost range for the service."""
        if not tutors:
            return {
                'min_hourly_rate': float(service.get('min_rate', 40)),
                'avg_hourly_rate': float(service.get('avg_rate', 50)),
                'estimated_monthly': float(service.get('avg_rate', 50)) * 4
            }
        
        rates = [float(t['hourly_rate']) for t in tutors[:10]]  # Top 10 tutors
        
        return {
            'min_hourly_rate': min(rates),
            'avg_hourly_rate': sum(rates) / len(rates),
            'max_hourly_rate': max(rates),
            'estimated_monthly': (sum(rates) / len(rates)) * 4  # 4 sessions/month
        }
    
    def _get_availability_summary(
        self,
        tutors: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Summarize tutor availability."""
        if not tutors:
            return {
                'immediate_availability': False,
                'available_tutors': 0,
                'avg_response_time': 'Unknown'
            }
        
        available_count = sum(
            1 for t in tutors
            if t.get('availability_status') == 'available'
        )
        
        return {
            'immediate_availability': available_count > 0,
            'available_tutors': len(tutors),
            'tutors_with_openings': available_count,
            'avg_response_time': '2-4 hours' if available_count > 3 else '24 hours'
        }