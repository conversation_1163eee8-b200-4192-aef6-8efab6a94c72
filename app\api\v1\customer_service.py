"""
Customer Service API Endpoints

Comprehensive REST API for customer service operations including conversations,
messages, agent management, templates, and analytics.
"""

from typing import List, Optional, Dict, Any
from datetime import date
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body
from fastapi.security import HTT<PERSON><PERSON>earer
from app.services.customer_service import CustomerServiceService
from app.models.customer_service_models import *
from app.core.dependencies import get_current_user
from app.core.auth_decorators import require_roles
from app.core.exceptions import ValidationError, ResourceNotFoundError, BusinessLogicError
from app.core.logging import logger
from app.locales.dependencies import get_current_language

router = APIRouter(prefix="/customer-service", tags=["Customer Service"])
security = HTTPBearer()

# Initialize service
cs_service = CustomerServiceService()


# Agent Management Endpoints

@router.get("/agents", response_model=List[AgentSummary])
async def get_agents(
    active_only: bool = Query(True, description="Show only active agents"),
    current_user = Depends(get_current_user)
):
    """Get all customer service agents"""
    try:
        agents = await cs_service.get_all_agents(active_only)
        
        # Convert to summaries with workload info
        agent_summaries = []
        for agent in agents:
            workload = await cs_service.repository.get_agent_workload(agent.agent_id)
            summary = AgentSummary(
                agent_id=agent.agent_id,
                agent_name=agent.agent_name,
                status=agent.status,
                current_conversations=workload.get('active_conversations', 0),
                max_conversations=agent.max_concurrent_conversations,
                specialties=agent.specialties,
                languages=agent.languages
            )
            agent_summaries.append(summary)
        
        return agent_summaries
        
    except Exception as e:
        logger.error(f"Error fetching agents: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch agents")


@router.put("/agents/{agent_id}/status")
async def update_agent_status(
    agent_id: int = Path(..., description="Agent ID"),
    status: AgentStatus = Body(..., embed=True),
    current_user = Depends(get_current_user)
):
    """Update agent availability status"""
    try:
        # Verify agent exists and user has permission
        agent = await cs_service.repository.get_agent_by_user_id(current_user.user_id)
        if not agent:
            # Check if user is manager
            if "manager" not in current_user.roles:
                raise HTTPException(status_code=403, detail="Not authorized")
            # Manager can update any agent status
        elif agent.agent_id != agent_id and "manager" not in current_user.roles:
            raise HTTPException(status_code=403, detail="Can only update own status")
        
        success = await cs_service.update_agent_status(agent_id, status)
        if not success:
            raise HTTPException(status_code=404, detail="Agent not found")
        
        return {"success": True, "message": f"Agent status updated to {status.value}"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating agent status: {e}")
        raise HTTPException(status_code=500, detail="Failed to update agent status")


@router.get("/agents/available", response_model=List[AgentSummary])
async def get_available_agents(
    specialties: Optional[List[str]] = Query(None, description="Required specialties"),
    current_user = Depends(get_current_user)
):
    """Get available agents for assignment"""
    try:
        agents = await cs_service.get_available_agents(specialties)
        
        # Convert to summaries
        agent_summaries = []
        for agent in agents:
            workload = await cs_service.repository.get_agent_workload(agent.agent_id)
            summary = AgentSummary(
                agent_id=agent.agent_id,
                agent_name=agent.agent_name,
                status=agent.status,
                current_conversations=workload.get('active_conversations', 0),
                max_conversations=agent.max_concurrent_conversations,
                specialties=agent.specialties,
                languages=agent.languages
            )
            agent_summaries.append(summary)
        
        return agent_summaries
        
    except Exception as e:
        logger.error(f"Error fetching available agents: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch available agents")


# Conversation Management Endpoints

@router.post("/conversations", response_model=CSConversation)
async def create_conversation(
    request: CreateConversationRequest,
    current_user = Depends(get_current_user)
):
    """Create a new customer service conversation"""
    try:
        conversation = await cs_service.create_conversation(
            participant_phone=request.participant_phone,
            participant_email=request.participant_email,
            participant_name=request.participant_name,
            conversation_type=request.conversation_type,
            subject=request.subject,
            priority=request.priority,
            category=request.category,
            initial_message=request.initial_message,
            language=request.language
        )
        
        return conversation
        
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating conversation: {e}")
        raise HTTPException(status_code=500, detail="Failed to create conversation")


@router.get("/conversations/{conversation_id}", response_model=ConversationResponse)
async def get_conversation(
    conversation_id: int = Path(..., description="Conversation ID"),
    current_user = Depends(get_current_user)
):
    """Get conversation details with messages"""
    try:
        conversation_response = await cs_service.get_conversation_details(conversation_id)
        if not conversation_response:
            raise HTTPException(status_code=404, detail="Conversation not found")
        
        # Check permission to view conversation
        agent = await cs_service.get_agent_by_user_id(current_user.user_id)
        if agent and conversation_response.conversation.assigned_agent_id != agent.agent_id:
            if "manager" not in current_user.roles:
                raise HTTPException(status_code=403, detail="Not authorized to view this conversation")
        
        return conversation_response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching conversation: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch conversation")


@router.get("/conversations", response_model=List[CSConversation])
async def get_conversations(
    status: Optional[ConversationStatus] = Query(None, description="Filter by status"),
    agent_id: Optional[int] = Query(None, description="Filter by assigned agent"),
    priority: Optional[Priority] = Query(None, description="Filter by priority"),
    category: Optional[str] = Query(None, description="Filter by category"),
    limit: int = Query(50, ge=1, le=100, description="Number of conversations to return"),
    offset: int = Query(0, ge=0, description="Number of conversations to skip"),
    current_user = Depends(get_current_user)
):
    """Get conversations with filtering and pagination"""
    try:
        # Determine which conversations to show
        user_agent = await cs_service.get_agent_by_user_id(current_user.user_id)
        
        if user_agent and "manager" not in current_user.roles:
            # Regular agents can only see their own conversations
            agent_id = user_agent.agent_id
        
        if agent_id:
            conversations = await cs_service.repository.get_conversations_by_agent(
                agent_id, status, limit, offset
            )
        elif status == ConversationStatus.OPEN and not agent_id:
            # Show unassigned conversations
            conversations = await cs_service.repository.get_unassigned_conversations(
                priority, category, limit
            )
        else:
            # Managers can see all conversations (would need a repository method for this)
            conversations = []
        
        return conversations
        
    except Exception as e:
        logger.error(f"Error fetching conversations: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch conversations")


@router.put("/conversations/{conversation_id}")
async def update_conversation(
    conversation_id: int = Path(..., description="Conversation ID"),
    request: UpdateConversationRequest = Body(...),
    current_user = Depends(get_current_user)
):
    """Update conversation details"""
    try:
        # Verify permission
        conversation = await cs_service.repository.get_conversation_by_id(conversation_id)
        if not conversation:
            raise HTTPException(status_code=404, detail="Conversation not found")
        
        user_agent = await cs_service.get_agent_by_user_id(current_user.user_id)
        if user_agent and conversation.assigned_agent_id != user_agent.agent_id:
            if "manager" not in current_user.roles:
                raise HTTPException(status_code=403, detail="Not authorized")
        
        success = await cs_service.repository.update_conversation(
            conversation_id=conversation_id,
            status=request.status,
            priority=request.priority,
            category=request.category,
            internal_notes=request.internal_notes,
            tags=request.tags
        )
        
        if not success:
            raise HTTPException(status_code=400, detail="Failed to update conversation")
        
        return {"success": True, "message": "Conversation updated successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating conversation: {e}")
        raise HTTPException(status_code=500, detail="Failed to update conversation")


@router.post("/conversations/{conversation_id}/assign")
async def assign_conversation(
    conversation_id: int = Path(..., description="Conversation ID"),
    request: AssignConversationRequest = Body(...),
    current_user = Depends(get_current_user)
):
    """Manually assign conversation to an agent"""
    try:
        success = await cs_service.assign_conversation_manually(
            conversation_id=request.conversation_id,
            agent_id=request.agent_id,
            assigned_by=current_user.user_id,
            assignment_reason=request.assignment_reason
        )
        
        if not success:
            raise HTTPException(status_code=400, detail="Failed to assign conversation")
        
        return {"success": True, "message": "Conversation assigned successfully"}
        
    except BusinessLogicError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error assigning conversation: {e}")
        raise HTTPException(status_code=500, detail="Failed to assign conversation")


@router.post("/conversations/{conversation_id}/auto-assign")
async def auto_assign_conversation(
    conversation_id: int = Path(..., description="Conversation ID"),
    specialties: Optional[List[str]] = Query(None, description="Required specialties"),
    current_user = Depends(get_current_user)
):
    """Automatically assign conversation to best available agent"""
    try:
        agent = await cs_service.assign_conversation_automatically(conversation_id, specialties)
        
        if not agent:
            raise HTTPException(status_code=400, detail="No available agents for assignment")
        
        return {
            "success": True,
            "message": f"Conversation assigned to {agent.agent_name}",
            "agent": {
                "agent_id": agent.agent_id,
                "agent_name": agent.agent_name
            }
        }
        
    except Exception as e:
        logger.error(f"Error auto-assigning conversation: {e}")
        raise HTTPException(status_code=500, detail="Failed to auto-assign conversation")


# Message Management Endpoints

@router.post("/conversations/{conversation_id}/messages", response_model=CSMessage)
async def send_message(
    conversation_id: int = Path(..., description="Conversation ID"),
    request: SendMessageRequest = Body(...),
    current_user = Depends(get_current_user)
):
    """Send a message in a conversation"""
    try:
        # Verify permission
        conversation = await cs_service.repository.get_conversation_by_id(conversation_id)
        if not conversation:
            raise HTTPException(status_code=404, detail="Conversation not found")
        
        user_agent = await cs_service.get_agent_by_user_id(current_user.user_id)
        if user_agent and conversation.assigned_agent_id != user_agent.agent_id:
            if "manager" not in current_user.roles:
                raise HTTPException(status_code=403, detail="Not authorized")
        
        # Determine channel
        channel = "in_app"  # Default to in-app for agent messages
        if conversation.conversation_type == ConversationType.SMS:
            channel = "sms"
        elif conversation.conversation_type == ConversationType.EMAIL:
            channel = "email"
        
        message = await cs_service.add_message(
            conversation_id=conversation_id,
            message_content=request.message_content,
            message_direction=MessageDirection.OUTBOUND,
            channel=channel,
            sender_agent_id=user_agent.agent_id if user_agent else None,
            sender_user_id=current_user.user_id,
            message_type=request.message_type,
            template_id=request.template_id,
            internal_note=request.internal_note,
            attachments=request.attachments
        )
        
        return message
        
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending message: {e}")
        raise HTTPException(status_code=500, detail="Failed to send message")


@router.get("/conversations/{conversation_id}/messages", response_model=List[CSMessage])
async def get_conversation_messages(
    conversation_id: int = Path(..., description="Conversation ID"),
    include_internal: bool = Query(False, description="Include internal notes"),
    limit: int = Query(100, ge=1, le=500, description="Number of messages to return"),
    offset: int = Query(0, ge=0, description="Number of messages to skip"),
    current_user = Depends(get_current_user)
):
    """Get messages for a conversation"""
    try:
        # Verify permission
        conversation = await cs_service.repository.get_conversation_by_id(conversation_id)
        if not conversation:
            raise HTTPException(status_code=404, detail="Conversation not found")
        
        user_agent = await cs_service.get_agent_by_user_id(current_user.user_id)
        if user_agent and conversation.assigned_agent_id != user_agent.agent_id:
            if "manager" not in current_user.roles:
                raise HTTPException(status_code=403, detail="Not authorized")
        
        messages = await cs_service.repository.get_conversation_messages(
            conversation_id, include_internal, limit, offset
        )
        
        return messages
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching messages: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch messages")


@router.post("/conversations/{conversation_id}/messages/template", response_model=CSMessage)
async def send_template_message(
    conversation_id: int = Path(..., description="Conversation ID"),
    template_key: str = Body(..., embed=True),
    variables: Optional[Dict[str, Any]] = Body(None, embed=True),
    language: str = Depends(get_current_language),
    current_user = Depends(get_current_user)
):
    """Send a message using a template"""
    try:
        user_agent = await cs_service.get_agent_by_user_id(current_user.user_id)
        
        message = await cs_service.send_template_message(
            conversation_id=conversation_id,
            template_key=template_key,
            variables=variables,
            sender_agent_id=user_agent.agent_id if user_agent else None,
            language=language
        )
        
        return message
        
    except (ValidationError, ResourceNotFoundError) as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error sending template message: {e}")
        raise HTTPException(status_code=500, detail="Failed to send template message")


# Template Management Endpoints

@router.get("/templates", response_model=List[CSMessageTemplate])
async def get_templates(
    category: Optional[str] = Query(None, description="Filter by category"),
    department: Optional[str] = Query(None, description="Filter by department"),
    language: str = Depends(get_current_language),
    search: Optional[str] = Query(None, description="Search term"),
    current_user = Depends(get_current_user)
):
    """Get available message templates"""
    try:
        templates = await cs_service.get_available_templates(category, language, department, search)
        return templates
        
    except Exception as e:
        logger.error(f"Error fetching templates: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch templates")


@router.post("/templates", response_model=CSMessageTemplate)
async def create_template(
    request: CreateTemplateRequest,
    current_user = Depends(get_current_user)
):
    """Create a new message template"""
    try:
        template = await cs_service.create_template(
            template_name=request.template_name,
            template_key=request.template_key,
            category=request.category,
            message_content=request.message_content,
            created_by=current_user.user_id,
            subject=request.subject,
            variables=request.variables,
            language=request.language,
            channel=request.channel,
            department=request.department
        )
        
        return template
        
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating template: {e}")
        raise HTTPException(status_code=500, detail="Failed to create template")


@router.put("/templates/{template_id}")
async def update_template(
    template_id: int = Path(..., description="Template ID"),
    template_name: Optional[str] = Body(None, embed=True),
    template_key: Optional[str] = Body(None, embed=True),
    category: Optional[str] = Body(None, embed=True),
    subject: Optional[str] = Body(None, embed=True),
    message_content: Optional[str] = Body(None, embed=True),
    variables: Optional[Dict[str, Any]] = Body(None, embed=True),
    language: Optional[str] = Body(None, embed=True),
    channel: Optional[str] = Body(None, embed=True),
    department: Optional[str] = Body(None, embed=True),
    current_user = Depends(get_current_user)
):
    """Update message template"""
    try:
        success = await cs_service.update_template(
            template_id=template_id,
            template_name=template_name,
            template_key=template_key,
            category=category,
            subject=subject,
            message_content=message_content,
            variables=variables,
            language=language,
            channel=channel,
            department=department,
            updated_by=current_user.user_id
        )
        
        if not success:
            raise HTTPException(status_code=404, detail="Template not found")
        
        return {"success": True, "message": "Template updated successfully"}
        
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating template: {e}")
        raise HTTPException(status_code=500, detail="Failed to update template")


@router.delete("/templates/{template_id}")
async def delete_template(
    template_id: int = Path(..., description="Template ID"),
    current_user = Depends(get_current_user)
):
    """Delete message template"""
    try:
        success = await cs_service.delete_template(template_id, current_user.user_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="Template not found")
        
        return {"success": True, "message": "Template deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting template: {e}")
        raise HTTPException(status_code=500, detail="Failed to delete template")


@router.get("/quick-responses", response_model=List[CSQuickResponse])
async def get_quick_responses(
    category: Optional[str] = Query(None, description="Filter by category"),
    language: str = Depends(get_current_language),
    current_user = Depends(get_current_user)
):
    """Get quick responses for agents"""
    try:
        responses = await cs_service.get_quick_responses(category, language)
        return responses
        
    except Exception as e:
        logger.error(f"Error fetching quick responses: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch quick responses")


@router.post("/quick-responses")
async def create_quick_response(
    response_text: str = Body(..., embed=True),
    trigger_keywords: List[str] = Body(..., embed=True),
    category: str = Body(..., embed=True),
    language: str = Body(..., embed=True),
    current_user = Depends(get_current_user)
):
    """Create a new quick response"""
    try:
        response = await cs_service.create_quick_response(
            response_text=response_text,
            trigger_keywords=trigger_keywords,
            category=category,
            language=language,
            created_by=current_user.user_id
        )
        
        return response
        
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating quick response: {e}")
        raise HTTPException(status_code=500, detail="Failed to create quick response")


@router.put("/quick-responses/{response_id}")
async def update_quick_response(
    response_id: int = Path(..., description="Quick Response ID"),
    response_text: Optional[str] = Body(None, embed=True),
    trigger_keywords: Optional[List[str]] = Body(None, embed=True),
    category: Optional[str] = Body(None, embed=True),
    language: Optional[str] = Body(None, embed=True),
    is_active: Optional[bool] = Body(None, embed=True),
    current_user = Depends(get_current_user)
):
    """Update quick response"""
    try:
        success = await cs_service.update_quick_response(
            response_id=response_id,
            response_text=response_text,
            trigger_keywords=trigger_keywords,
            category=category,
            language=language,
            is_active=is_active,
            updated_by=current_user.user_id
        )
        
        if not success:
            raise HTTPException(status_code=404, detail="Quick response not found")
        
        return {"success": True, "message": "Quick response updated successfully"}
        
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating quick response: {e}")
        raise HTTPException(status_code=500, detail="Failed to update quick response")


@router.delete("/quick-responses/{response_id}")
async def delete_quick_response(
    response_id: int = Path(..., description="Quick Response ID"),
    current_user = Depends(get_current_user)
):
    """Delete quick response"""
    try:
        success = await cs_service.delete_quick_response(response_id, current_user.user_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="Quick response not found")
        
        return {"success": True, "message": "Quick response deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting quick response: {e}")
        raise HTTPException(status_code=500, detail="Failed to delete quick response")


@router.get("/template-categories")
async def get_template_categories(
    current_user = Depends(get_current_user)
):
    """Get available template categories with counts"""
    try:
        categories = await cs_service.get_template_categories()
        return categories
        
    except Exception as e:
        logger.error(f"Error fetching template categories: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch template categories")


@router.get("/template-variables")
async def get_template_variables(
    current_user = Depends(get_current_user)
):
    """Get available template variables"""
    try:
        variables = await cs_service.get_template_variables()
        return variables
        
    except Exception as e:
        logger.error(f"Error fetching template variables: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch template variables")


@router.post("/templates/{template_id}/approve")
async def approve_template(
    template_id: int = Path(..., description="Template ID"),
    current_user = Depends(get_current_user)
):
    """Approve a template for use"""
    try:
        success = await cs_service.approve_template(template_id, current_user.user_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="Template not found")
        
        return {"success": True, "message": "Template approved successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error approving template: {e}")
        raise HTTPException(status_code=500, detail="Failed to approve template")


@router.post("/quick-responses/suggest")
async def suggest_quick_responses(
    message_content: str = Body(..., embed=True),
    category: Optional[str] = Body(None, embed=True),
    language: str = Depends(get_current_language),
    current_user = Depends(get_current_user)
):
    """Get suggested quick responses based on message content"""
    try:
        suggestions = await cs_service.suggest_quick_responses(
            message_content=message_content,
            category=category,
            language=language
        )
        
        return {"suggestions": suggestions}
        
    except Exception as e:
        logger.error(f"Error getting quick response suggestions: {e}")
        raise HTTPException(status_code=500, detail="Failed to get suggestions")


# Dashboard and Analytics Endpoints

@router.get("/dashboard", response_model=DashboardResponse)
async def get_dashboard(
    start_date: Optional[date] = Query(None, description="Start date for statistics"),
    end_date: Optional[date] = Query(None, description="End date for statistics"),
    current_user = Depends(get_current_user)
):
    """Get customer service dashboard data"""
    try:
        # Determine if filtering by agent
        user_agent = await cs_service.get_agent_by_user_id(current_user.user_id)
        agent_id = None
        
        if user_agent and "manager" not in current_user.roles:
            # Regular agents see only their own data
            agent_id = user_agent.agent_id
        
        dashboard = await cs_service.get_dashboard_data(agent_id, start_date, end_date)
        return dashboard
        
    except Exception as e:
        logger.error(f"Error fetching dashboard: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch dashboard data")


@router.get("/search", response_model=List[CSConversation])
async def search_conversations(
    q: str = Query(..., min_length=2, description="Search term"),
    status: Optional[ConversationStatus] = Query(None, description="Filter by status"),
    agent_id: Optional[int] = Query(None, description="Filter by agent"),
    start_date: Optional[date] = Query(None, description="Start date"),
    end_date: Optional[date] = Query(None, description="End date"),
    limit: int = Query(50, ge=1, le=100, description="Number of results"),
    current_user = Depends(get_current_user)
):
    """Search conversations"""
    try:
        # Build filters
        filters = {"limit": limit}
        if status:
            filters["status"] = status.value
        if agent_id:
            filters["agent_id"] = agent_id
        if start_date:
            filters["start_date"] = start_date
        if end_date:
            filters["end_date"] = end_date
        
        # Regular agents can only search their own conversations
        user_agent = await cs_service.get_agent_by_user_id(current_user.user_id)
        if user_agent and "manager" not in current_user.roles:
            filters["agent_id"] = user_agent.agent_id
        
        conversations = await cs_service.search_conversations(q, filters)
        return conversations
        
    except Exception as e:
        logger.error(f"Error searching conversations: {e}")
        raise HTTPException(status_code=500, detail="Failed to search conversations")


# SMS Integration Endpoints

@router.post("/sms/incoming")
async def handle_incoming_sms(
    from_phone: str = Body(..., embed=True),
    message_content: str = Body(..., embed=True),
    external_message_id: str = Body(..., embed=True),
    # This endpoint would typically be called by Twilio webhook
    # Authentication would be handled via webhook signature verification
):
    """Handle incoming SMS message (webhook endpoint)"""
    try:
        conversation = await cs_service.handle_incoming_sms(
            from_phone, message_content, external_message_id
        )
        
        return {
            "success": True,
            "conversation_id": conversation.conversation_id,
            "thread_id": conversation.thread_id
        }
        
    except Exception as e:
        logger.error(f"Error handling incoming SMS: {e}")
        raise HTTPException(status_code=500, detail="Failed to process SMS")


@router.post("/conversations/{conversation_id}/sms")
async def send_sms_response(
    conversation_id: int = Path(..., description="Conversation ID"),
    message_content: str = Body(..., embed=True),
    current_user = Depends(get_current_user)
):
    """Send SMS response to customer"""
    try:
        user_agent = await cs_service.get_agent_by_user_id(current_user.user_id)
        if not user_agent:
            raise HTTPException(status_code=403, detail="Only agents can send SMS")
        
        success = await cs_service.send_sms_response(
            conversation_id, message_content, user_agent.agent_id
        )
        
        if not success:
            raise HTTPException(status_code=400, detail="Failed to send SMS")
        
        return {"success": True, "message": "SMS sent successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending SMS: {e}")
        raise HTTPException(status_code=500, detail="Failed to send SMS")


# In-App Messaging Endpoints

@router.get("/conversations/{conversation_id}/participants")
async def get_conversation_participants(
    conversation_id: int = Path(..., description="Conversation ID"),
    current_user = Depends(get_current_user)
):
    """Get participants in a conversation"""
    try:
        # Verify permission to view conversation
        conversation = await cs_service.repository.get_conversation_by_id(conversation_id)
        if not conversation:
            raise HTTPException(status_code=404, detail="Conversation not found")
        
        # Check if user is participant or agent/manager
        user_agent = await cs_service.get_agent_by_user_id(current_user.user_id)
        is_participant = conversation.participant_user_id == current_user.user_id
        is_agent_or_manager = user_agent or "manager" in current_user.roles
        
        if not (is_participant or is_agent_or_manager):
            raise HTTPException(status_code=403, detail="Not authorized to view this conversation")
        
        # Get participants (would need implementation in repository)
        participants = await cs_service.repository.get_conversation_participants(conversation_id)
        return participants
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching conversation participants: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch participants")


@router.post("/conversations/{conversation_id}/mark-read")
async def mark_conversation_read(
    conversation_id: int = Path(..., description="Conversation ID"),
    current_user = Depends(get_current_user)
):
    """Mark all messages in conversation as read"""
    try:
        # Verify permission
        conversation = await cs_service.repository.get_conversation_by_id(conversation_id)
        if not conversation:
            raise HTTPException(status_code=404, detail="Conversation not found")
        
        user_agent = await cs_service.get_agent_by_user_id(current_user.user_id)
        is_participant = conversation.participant_user_id == current_user.user_id
        is_agent_or_manager = user_agent or "manager" in current_user.roles
        
        if not (is_participant or is_agent_or_manager):
            raise HTTPException(status_code=403, detail="Not authorized")
        
        # Mark messages as read
        success = await cs_service.repository.mark_conversation_messages_read(
            conversation_id, current_user.user_id
        )
        
        if not success:
            raise HTTPException(status_code=400, detail="Failed to mark messages as read")
        
        return {"success": True, "message": "Messages marked as read"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error marking conversation as read: {e}")
        raise HTTPException(status_code=500, detail="Failed to mark conversation as read")


@router.post("/conversations/{conversation_id}/typing")
async def send_typing_indicator(
    conversation_id: int = Path(..., description="Conversation ID"),
    is_typing: bool = Body(..., embed=True),
    current_user = Depends(get_current_user)
):
    """Send typing indicator to other participants"""
    try:
        # Verify permission
        conversation = await cs_service.repository.get_conversation_by_id(conversation_id)
        if not conversation:
            raise HTTPException(status_code=404, detail="Conversation not found")
        
        user_agent = await cs_service.get_agent_by_user_id(current_user.user_id)
        is_participant = conversation.participant_user_id == current_user.user_id
        is_agent_or_manager = user_agent or "manager" in current_user.roles
        
        if not (is_participant or is_agent_or_manager):
            raise HTTPException(status_code=403, detail="Not authorized")
        
        # Send typing indicator via WebSocket (would integrate with WebSocket service)
        # This is a placeholder - actual implementation would use WebSocket broadcast
        return {"success": True, "message": "Typing indicator sent"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending typing indicator: {e}")
        raise HTTPException(status_code=500, detail="Failed to send typing indicator")


@router.get("/conversations/{conversation_id}/attachments")
async def get_conversation_attachments(
    conversation_id: int = Path(..., description="Conversation ID"),
    current_user = Depends(get_current_user)
):
    """Get all attachments from a conversation"""
    try:
        # Verify permission
        conversation = await cs_service.repository.get_conversation_by_id(conversation_id)
        if not conversation:
            raise HTTPException(status_code=404, detail="Conversation not found")
        
        user_agent = await cs_service.get_agent_by_user_id(current_user.user_id)
        is_participant = conversation.participant_user_id == current_user.user_id
        is_agent_or_manager = user_agent or "manager" in current_user.roles
        
        if not (is_participant or is_agent_or_manager):
            raise HTTPException(status_code=403, detail="Not authorized")
        
        # Get attachments (would need implementation in repository)
        attachments = await cs_service.repository.get_conversation_attachments(conversation_id)
        return attachments
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching conversation attachments: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch attachments")


# Conversation Threading and History Endpoints

@router.get("/threads")
async def get_conversation_threads(
    status: Optional[str] = Query(None, description="Filter by thread status"),
    include_archived: bool = Query(False, description="Include archived threads"),
    search: Optional[str] = Query(None, description="Search term"),
    start_date: Optional[date] = Query(None, description="Start date"),
    end_date: Optional[date] = Query(None, description="End date"),
    limit: int = Query(50, ge=1, le=100, description="Number of threads"),
    offset: int = Query(0, ge=0, description="Offset for pagination"),
    current_user = Depends(get_current_user)
):
    """Get conversation threads with optional filtering"""
    try:
        threads = await cs_service.repository.get_conversation_threads(
            status=status,
            include_archived=include_archived,
            search_term=search,
            start_date=start_date,
            end_date=end_date,
            limit=limit,
            offset=offset
        )
        return threads
        
    except Exception as e:
        logger.error(f"Error fetching conversation threads: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch conversation threads")


@router.get("/threads/{thread_id}")
async def get_thread_details(
    thread_id: str = Path(..., description="Thread ID"),
    current_user = Depends(get_current_user)
):
    """Get detailed information about a specific thread"""
    try:
        thread_details = await cs_service.repository.get_thread_details(thread_id)
        if not thread_details:
            raise HTTPException(status_code=404, detail="Thread not found")
        
        return thread_details
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching thread details: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch thread details")


@router.get("/threads/{thread_id}/history")
async def get_thread_history(
    thread_id: str = Path(..., description="Thread ID"),
    current_user = Depends(get_current_user)
):
    """Get complete history for a conversation thread"""
    try:
        history = await cs_service.repository.get_thread_history(thread_id)
        return history
        
    except Exception as e:
        logger.error(f"Error fetching thread history: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch thread history")


@router.get("/threads/{thread_id}/analytics")
async def get_thread_analytics(
    thread_id: str = Path(..., description="Thread ID"),
    current_user = Depends(get_current_user)
):
    """Get analytics data for a conversation thread"""
    try:
        analytics = await cs_service.repository.get_thread_analytics(thread_id)
        return analytics
        
    except Exception as e:
        logger.error(f"Error fetching thread analytics: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch thread analytics")


@router.post("/threads/{thread_id}/archive")
async def archive_thread(
    thread_id: str = Path(..., description="Thread ID"),
    current_user = Depends(get_current_user)
):
    """Archive a conversation thread"""
    try:
        success = await cs_service.repository.archive_thread(thread_id, current_user.user_id)
        if not success:
            raise HTTPException(status_code=404, detail="Thread not found or already archived")
        
        return {"success": True, "message": "Thread archived successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error archiving thread: {e}")
        raise HTTPException(status_code=500, detail="Failed to archive thread")


@router.post("/threads/{thread_id}/unarchive")
async def unarchive_thread(
    thread_id: str = Path(..., description="Thread ID"),
    current_user = Depends(get_current_user)
):
    """Unarchive a conversation thread"""
    try:
        success = await cs_service.repository.unarchive_thread(thread_id, current_user.user_id)
        if not success:
            raise HTTPException(status_code=404, detail="Thread not found or not archived")
        
        return {"success": True, "message": "Thread unarchived successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error unarchiving thread: {e}")
        raise HTTPException(status_code=500, detail="Failed to unarchive thread")


@router.get("/conversations/{conversation_id}/history")
async def get_conversation_history(
    conversation_id: int = Path(..., description="Conversation ID"),
    current_user = Depends(get_current_user)
):
    """Get detailed history for a specific conversation"""
    try:
        # Verify permission to view conversation
        conversation = await cs_service.repository.get_conversation_by_id(conversation_id)
        if not conversation:
            raise HTTPException(status_code=404, detail="Conversation not found")
        
        user_agent = await cs_service.get_agent_by_user_id(current_user.user_id)
        if user_agent and conversation.assigned_agent_id != user_agent.agent_id:
            if "manager" not in current_user.roles:
                raise HTTPException(status_code=403, detail="Not authorized to view this conversation")
        
        history = await cs_service.repository.get_conversation_history(conversation_id)
        return history
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching conversation history: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch conversation history")


@router.post("/conversations/merge")
async def merge_conversations(
    primary_conversation_id: int = Body(..., embed=True),
    conversation_ids: List[int] = Body(..., embed=True),
    current_user = Depends(get_current_user)
):
    """Merge multiple conversations into a single thread"""
    try:
        if primary_conversation_id in conversation_ids:
            raise HTTPException(status_code=400, detail="Primary conversation cannot be in merge list")
        
        result = await cs_service.merge_conversations(
            primary_conversation_id=primary_conversation_id,
            conversation_ids=conversation_ids,
            merged_by=current_user.user_id
        )
        
        return {
            "success": True,
            "message": f"Merged {len(conversation_ids)} conversations",
            "merged_thread_id": result.get("thread_id")
        }
        
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error merging conversations: {e}")
        raise HTTPException(status_code=500, detail="Failed to merge conversations")


@router.post("/threads/{thread_id}/export")
async def export_thread(
    thread_id: str = Path(..., description="Thread ID"),
    format: str = Body(..., embed=True),
    include_messages: bool = Body(True, embed=True),
    current_user = Depends(get_current_user)
):
    """Export thread data in various formats"""
    try:
        if format not in ['pdf', 'csv', 'json']:
            raise HTTPException(status_code=400, detail="Supported formats: pdf, csv, json")
        
        export_data = await cs_service.export_thread(
            thread_id=thread_id,
            format=format,
            include_messages=include_messages,
            exported_by=current_user.user_id
        )
        
        return export_data
        
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error exporting thread: {e}")
        raise HTTPException(status_code=500, detail="Failed to export thread")


# Statistics and Reporting

@router.get("/stats/conversations")
async def get_conversation_statistics(
    start_date: Optional[date] = Query(None, description="Start date"),
    end_date: Optional[date] = Query(None, description="End date"),
    agent_id: Optional[int] = Query(None, description="Filter by agent"),
    current_user = Depends(get_current_user)
):
    """Get conversation statistics"""
    try:
        stats = await cs_service.repository.get_conversation_stats(start_date, end_date, agent_id)
        return stats
        
    except Exception as e:
        logger.error(f"Error fetching conversation stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch statistics")


@router.get("/stats/messages")
async def get_message_statistics(
    start_date: Optional[date] = Query(None, description="Start date"),
    end_date: Optional[date] = Query(None, description="End date"),
    agent_id: Optional[int] = Query(None, description="Filter by agent"),
    current_user = Depends(get_current_user)
):
    """Get message statistics"""
    try:
        stats = await cs_service.repository.get_message_stats(start_date, end_date, agent_id)
        return stats
        
    except Exception as e:
        logger.error(f"Error fetching message stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch statistics")


@router.get("/stats/real-time")
async def get_real_time_statistics(
    current_user = Depends(get_current_user)
):
    """Get real-time system statistics"""
    try:
        stats = await cs_service.repository.get_real_time_stats()
        return stats
        
    except Exception as e:
        logger.error(f"Error fetching real-time stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch real-time statistics")


@router.get("/stats/trends")
async def get_statistics_trends(
    start_date: Optional[date] = Query(None, description="Start date"),
    end_date: Optional[date] = Query(None, description="End date"),
    current_user = Depends(get_current_user)
):
    """Get historical trends for dashboard charts"""
    try:
        trends = await cs_service.repository.get_dashboard_trends(start_date, end_date)
        return trends
        
    except Exception as e:
        logger.error(f"Error fetching trends: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch trends")


@router.get("/stats/channel-distribution")
async def get_channel_distribution(
    start_date: Optional[date] = Query(None, description="Start date"),
    end_date: Optional[date] = Query(None, description="End date"),
    current_user = Depends(get_current_user)
):
    """Get conversation distribution by channel"""
    try:
        distribution = await cs_service.repository.get_channel_distribution(start_date, end_date)
        return distribution
        
    except Exception as e:
        logger.error(f"Error fetching channel distribution: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch channel distribution")


@router.get("/stats/peak-hours")
async def get_peak_hours_analysis(
    start_date: Optional[date] = Query(None, description="Start date"),
    end_date: Optional[date] = Query(None, description="End date"),
    current_user = Depends(get_current_user)
):
    """Get peak hours analysis for resource planning"""
    try:
        peak_hours = await cs_service.repository.get_peak_hours_analysis(start_date, end_date)
        return peak_hours
        
    except Exception as e:
        logger.error(f"Error fetching peak hours: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch peak hours analysis")


# Broadcast Messaging Endpoints

@router.post("/broadcasts")
async def create_broadcast(
    title: str = Body(..., embed=True),
    content: str = Body(..., embed=True),
    message_type: str = Body(..., embed=True),
    priority: str = Body(..., embed=True),
    channels: List[str] = Body(..., embed=True),
    target_audience: Dict[str, Any] = Body(..., embed=True),
    scheduled_at: Optional[str] = Body(None, embed=True),
    template_id: Optional[int] = Body(None, embed=True),
    current_user = Depends(get_current_user)
):
    """Create a new broadcast message"""
    try:
        # Parse scheduled_at if provided
        scheduled_datetime = None
        if scheduled_at:
            from datetime import datetime
            scheduled_datetime = datetime.fromisoformat(scheduled_at.replace('Z', '+00:00'))
        
        broadcast = await cs_service.repository.create_broadcast_message(
            title=title,
            content=content,
            message_type=message_type,
            priority=priority,
            channels=channels,
            target_audience=target_audience,
            created_by=current_user.user_id,
            scheduled_at=scheduled_datetime,
            template_id=template_id
        )
        
        return broadcast
        
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating broadcast: {e}")
        raise HTTPException(status_code=500, detail="Failed to create broadcast")


@router.get("/broadcasts")
async def get_broadcasts(
    status: Optional[str] = Query(None, description="Filter by status"),
    limit: int = Query(50, ge=1, le=100, description="Number of broadcasts to return"),
    offset: int = Query(0, ge=0, description="Number of broadcasts to skip"),
    current_user = Depends(get_current_user)
):
    """Get broadcast messages with filtering"""
    try:
        broadcasts = await cs_service.repository.get_broadcast_messages(
            status=status,
            limit=limit,
            offset=offset
        )
        return broadcasts
        
    except Exception as e:
        logger.error(f"Error fetching broadcasts: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch broadcasts")


@router.get("/broadcasts/{broadcast_id}")
async def get_broadcast(
    broadcast_id: int = Path(..., description="Broadcast ID"),
    current_user = Depends(get_current_user)
):
    """Get broadcast message details"""
    try:
        broadcast = await cs_service.repository.get_broadcast_message_by_id(broadcast_id)
        if not broadcast:
            raise HTTPException(status_code=404, detail="Broadcast not found")
        
        return broadcast
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching broadcast: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch broadcast")


@router.put("/broadcasts/{broadcast_id}")
async def update_broadcast(
    broadcast_id: int = Path(..., description="Broadcast ID"),
    title: Optional[str] = Body(None, embed=True),
    content: Optional[str] = Body(None, embed=True),
    message_type: Optional[str] = Body(None, embed=True),
    priority: Optional[str] = Body(None, embed=True),
    channels: Optional[List[str]] = Body(None, embed=True),
    target_audience: Optional[Dict[str, Any]] = Body(None, embed=True),
    scheduled_at: Optional[str] = Body(None, embed=True),
    template_id: Optional[int] = Body(None, embed=True),
    current_user = Depends(get_current_user)
):
    """Update broadcast message"""
    try:
        # Parse scheduled_at if provided
        scheduled_datetime = None
        if scheduled_at:
            from datetime import datetime
            scheduled_datetime = datetime.fromisoformat(scheduled_at.replace('Z', '+00:00'))
        
        success = await cs_service.repository.update_broadcast_message(
            broadcast_id=broadcast_id,
            title=title,
            content=content,
            message_type=message_type,
            priority=priority,
            channels=channels,
            target_audience=target_audience,
            scheduled_at=scheduled_datetime,
            template_id=template_id
        )
        
        if not success:
            raise HTTPException(status_code=404, detail="Broadcast not found or cannot be updated")
        
        return {"success": True, "message": "Broadcast updated successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating broadcast: {e}")
        raise HTTPException(status_code=500, detail="Failed to update broadcast")


@router.post("/broadcasts/{broadcast_id}/send")
async def send_broadcast(
    broadcast_id: int = Path(..., description="Broadcast ID"),
    current_user = Depends(get_current_user)
):
    """Send broadcast message immediately"""
    try:
        # Get broadcast details
        broadcast = await cs_service.repository.get_broadcast_message_by_id(broadcast_id)
        if not broadcast:
            raise HTTPException(status_code=404, detail="Broadcast not found")
        
        if broadcast['status'] not in ['draft', 'scheduled']:
            raise HTTPException(status_code=400, detail="Broadcast cannot be sent")
        
        # Get audience
        audience = await cs_service.repository.get_audience_preview(broadcast['target_audience'])
        if not audience:
            raise HTTPException(status_code=400, detail="No recipients found for broadcast")
        
        # Update status to sending
        await cs_service.repository.update_broadcast_status(broadcast_id, 'sending')
        
        # Send to each recipient (simplified - in production would use background task)
        sent_count = 0
        failed_count = 0
        
        for recipient in audience:
            try:
                # Log delivery attempt
                await cs_service.repository.create_broadcast_delivery_log(
                    broadcast_id=broadcast_id,
                    user_id=recipient['user_id'],
                    channel='in_app',  # Simplified for now
                    status='sent'
                )
                sent_count += 1
            except Exception as e:
                logger.error(f"Failed to send to user {recipient['user_id']}: {e}")
                await cs_service.repository.create_broadcast_delivery_log(
                    broadcast_id=broadcast_id,
                    user_id=recipient['user_id'],
                    channel='in_app',
                    status='failed',
                    error_message=str(e)
                )
                failed_count += 1
        
        # Update final status and stats
        delivery_stats = {
            "total_recipients": len(audience),
            "sent_count": sent_count,
            "failed_count": failed_count,
            "delivered_count": sent_count,  # Simplified
            "opened_count": 0,
            "clicked_count": 0
        }
        
        await cs_service.repository.update_broadcast_status(
            broadcast_id, 'sent', delivery_stats
        )
        
        return {
            "success": True,
            "message": f"Broadcast sent to {sent_count} recipients",
            "delivery_stats": delivery_stats
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending broadcast: {e}")
        raise HTTPException(status_code=500, detail="Failed to send broadcast")


@router.post("/broadcasts/{broadcast_id}/cancel")
async def cancel_broadcast(
    broadcast_id: int = Path(..., description="Broadcast ID"),
    current_user = Depends(get_current_user)
):
    """Cancel scheduled broadcast"""
    try:
        broadcast = await cs_service.repository.get_broadcast_message_by_id(broadcast_id)
        if not broadcast:
            raise HTTPException(status_code=404, detail="Broadcast not found")
        
        if broadcast['status'] != 'scheduled':
            raise HTTPException(status_code=400, detail="Only scheduled broadcasts can be cancelled")
        
        success = await cs_service.repository.update_broadcast_status(broadcast_id, 'cancelled')
        
        if not success:
            raise HTTPException(status_code=400, detail="Failed to cancel broadcast")
        
        return {"success": True, "message": "Broadcast cancelled successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cancelling broadcast: {e}")
        raise HTTPException(status_code=500, detail="Failed to cancel broadcast")


@router.post("/audience-preview")
async def preview_audience(
    target_audience: Dict[str, Any] = Body(...),
    current_user = Depends(get_current_user)
):
    """Preview audience for broadcast targeting"""
    try:
        users = await cs_service.repository.get_audience_preview(target_audience)
        return {"users": users, "count": len(users)}
        
    except Exception as e:
        logger.error(f"Error previewing audience: {e}")
        raise HTTPException(status_code=500, detail="Failed to preview audience")


@router.get("/broadcast-templates")
async def get_broadcast_templates(
    current_user = Depends(get_current_user)
):
    """Get available broadcast templates"""
    try:
        templates = await cs_service.repository.get_broadcast_templates()
        return templates
        
    except Exception as e:
        logger.error(f"Error fetching broadcast templates: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch templates")


@router.get("/audience-stats")
async def get_audience_statistics(
    current_user = Depends(get_current_user)
):
    """Get audience statistics for broadcast targeting"""
    try:
        stats = await cs_service.repository.get_audience_statistics()
        return stats
        
    except Exception as e:
        logger.error(f"Error fetching audience stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch audience statistics")


@router.get("/broadcasts/{broadcast_id}/stats")
async def get_broadcast_stats(
    broadcast_id: int = Path(..., description="Broadcast ID"),
    current_user = Depends(get_current_user)
):
    """Get delivery statistics for a broadcast"""
    try:
        stats = await cs_service.repository.get_broadcast_delivery_stats(broadcast_id)
        return stats
        
    except Exception as e:
        logger.error(f"Error fetching broadcast stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch broadcast statistics")