import { useState, useEffect, useCallback } from 'react';
import api from '../services/api';

export interface LanguageInfo {
  language: string;
  source: string;
  quebec_french: boolean;
  fallback_used: boolean;
  browser_preferences?: any;
}

export interface LanguageResponse {
  current_language: string;
  supported_languages: string[];
  language_info: LanguageInfo;
  available_translations: Record<string, string>;
}

export interface TranslationStats {
  languages: string[];
  total_keys_by_language: Record<string, number>;
  completion_percentage: Record<string, number>;
  missing_keys_count: Record<string, number>;
  categories: string[];
}

export interface ValidationResult {
  is_valid: boolean;
  errors: string[];
  warnings: string[];
  missing_keys: Record<string, string[]>;
  statistics: Record<string, any>;
}

export const useLanguage = () => {
  const [currentLanguage, setCurrentLanguage] = useState<string>('en');
  const [languageInfo, setLanguageInfo] = useState<LanguageInfo | null>(null);
  const [supportedLanguages, setSupportedLanguages] = useState<string[]>(['en', 'fr']);
  const [availableTranslations, setAvailableTranslations] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch current language information
  const fetchLanguageInfo = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await api.get<LanguageResponse>('/language/current');
      
      setCurrentLanguage(response.data.current_language);
      setLanguageInfo(response.data.language_info);
      setSupportedLanguages(response.data.supported_languages);
      setAvailableTranslations(response.data.available_translations);
    } catch (err) {
      // Fallback to localStorage language
      const savedLanguage = localStorage.getItem('language') || 'en';
      setCurrentLanguage(savedLanguage);
      setLanguageInfo({
        language: savedLanguage,
        source: 'localStorage',
        quebec_french: false,
        fallback_used: true
      });
      setSupportedLanguages(['en', 'fr']);
      setAvailableTranslations({ en: 'English', fr: 'Français' });
      
      // Only log in development
      if (import.meta.env.DEV) {
        console.warn('Language API not available, using fallback:', err);
      }
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Switch language
  const switchLanguage = useCallback(async (language: string, persist: boolean = true) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await api.post('/api/v1/language/switch', {
        language,
        persist
      });

      if (response.data.success) {
        setCurrentLanguage(language);
        
        // Update language info
        setLanguageInfo(prev => prev ? {
          ...prev,
          language,
          source: 'manual_switch',
          preference_updated: response.data.preference_updated
        } : null);

        // Store language preference update info
        if (response.data.preference_info) {
          localStorage.setItem('language_preference_info', JSON.stringify(response.data.preference_info));
        }

        // Trigger page reload to apply language changes
        if (persist) {
          window.location.reload();
        }
        
        return true;
      }
      
      return false;
    } catch (err: any) {
      const errorMessage = err.response?.data?.detail || 'Failed to switch language';
      setError(errorMessage);
      console.error('Language switch error:', err);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Get translations for a specific namespace
  const getNamespaceTranslations = useCallback(async (namespace: string) => {
    try {
      const response = await api.get(`/api/v1/language/translations/${namespace}`);
      return response.data.translations;
    } catch (err) {
      console.error(`Failed to fetch translations for namespace ${namespace}:`, err);
      return {};
    }
  }, []);

  // Get language detection info (for debugging)
  const getDetectionInfo = useCallback(async () => {
    try {
      const response = await api.get('/api/v1/language/detect');
      return response.data;
    } catch (err) {
      console.error('Failed to fetch detection info:', err);
      return null;
    }
  }, []);

  // Get translation statistics (manager only)
  const getTranslationStats = useCallback(async (): Promise<TranslationStats | null> => {
    try {
      const response = await api.get<{data: TranslationStats}>('/api/v1/admin/translations/stats');
      return response.data.data;
    } catch (err) {
      console.error('Failed to fetch translation stats:', err);
      return null;
    }
  }, []);

  // Validate translations (manager only)
  const validateTranslations = useCallback(async (): Promise<ValidationResult | null> => {
    try {
      const response = await api.post<{data: ValidationResult}>('/api/v1/admin/translations/validate');
      return response.data.data;
    } catch (err) {
      console.error('Failed to validate translations:', err);
      return null;
    }
  }, []);

  // Reload translations (manager only)
  const reloadTranslations = useCallback(async () => {
    try {
      const response = await api.post('/api/v1/admin/translations/reload');
      return response.data.success;
    } catch (err) {
      console.error('Failed to reload translations:', err);
      return false;
    }
  }, []);

  // Admin translation management functions
  const getTranslationKeys = useCallback(async (params?: {
    category?: string;
    search?: string;
    missing_only?: boolean;
    limit?: number;
    offset?: number;
  }) => {
    try {
      const queryParams = new URLSearchParams();
      if (params?.category) queryParams.append('category', params.category);
      if (params?.search) queryParams.append('search', params.search);
      if (params?.missing_only) queryParams.append('missing_only', 'true');
      if (params?.limit) queryParams.append('limit', params.limit.toString());
      if (params?.offset) queryParams.append('offset', params.offset.toString());

      const response = await api.get(`/api/v1/admin/translations/keys?${queryParams}`);
      return response.data.data;
    } catch (err) {
      console.error('Failed to fetch translation keys:', err);
      return null;
    }
  }, []);

  const updateTranslationKey = useCallback(async (key: string, translations: Record<string, string>) => {
    try {
      const response = await api.put(`/api/v1/admin/translations/keys/${encodeURIComponent(key)}`, translations);
      return response.data.success;
    } catch (err) {
      console.error('Failed to update translation key:', err);
      return false;
    }
  }, []);

  const createTranslationKey = useCallback(async (keyData: {
    key: string;
    category: string;
    en?: string;
    fr?: string;
    description?: string;
  }) => {
    try {
      const response = await api.post('/api/v1/admin/translations/keys', keyData);
      return response.data.success;
    } catch (err) {
      console.error('Failed to create translation key:', err);
      return false;
    }
  }, []);

  const deleteTranslationKey = useCallback(async (key: string) => {
    try {
      const response = await api.delete(`/api/v1/admin/translations/keys/${encodeURIComponent(key)}`);
      return response.data.success;
    } catch (err) {
      console.error('Failed to delete translation key:', err);
      return false;
    }
  }, []);

  const exportTranslations = useCallback(async (format: string = 'json', languages: string[] = ['en', 'fr']) => {
    try {
      const response = await api.post('/api/v1/admin/translations/export', {
        format,
        languages
      });
      return response.data.data;
    } catch (err) {
      console.error('Failed to export translations:', err);
      return null;
    }
  }, []);

  // Get missing translations for a language (manager only)
  const getMissingTranslations = useCallback(async (language: string) => {
    try {
      const response = await api.get(`/api/v1/language/missing/${language}`);
      return response.data;
    } catch (err) {
      console.error(`Failed to fetch missing translations for ${language}:`, err);
      return null;
    }
  }, []);

  // Update language preferences (comprehensive)
  const updateLanguagePreferences = useCallback(async (preferences: {
    preferred_language: string;
    language_auto_detect?: boolean;
    quebec_french_preference?: boolean;
    persist?: boolean;
  }) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await api.put('/api/v1/language/preferences', preferences);
      
      if (response.data.success) {
        setCurrentLanguage(response.data.preferred_language);
        setLanguageInfo(prev => ({
          ...prev,
          language: response.data.preferred_language,
          source: 'user_preference',
          auto_detect_enabled: response.data.language_auto_detect,
          quebec_french: response.data.quebec_french_preference
        }));
        
        return response.data;
      }
      
      return null;
    } catch (err: any) {
      const errorMessage = err.response?.data?.detail || 'Failed to update language preferences';
      setError(errorMessage);
      console.error('Language preference update error:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Get comprehensive language preferences
  const getLanguagePreferences = useCallback(async () => {
    try {
      const response = await api.get('/api/v1/language/preferences');
      return response.data;
    } catch (err) {
      console.error('Failed to fetch language preferences:', err);
      return null;
    }
  }, []);

  // Auto-detect language
  const autoDetectLanguage = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await api.post('/api/v1/language/auto-detect');
      
      if (response.data.success) {
        setCurrentLanguage(response.data.preferred_language);
        setLanguageInfo(prev => ({
          ...prev,
          language: response.data.preferred_language,
          source: 'auto_detection',
          confidence: response.data.confidence
        }));
        
        return response.data;
      }
      
      return null;
    } catch (err: any) {
      const errorMessage = err.response?.data?.detail || 'Failed to auto-detect language';
      setError(errorMessage);
      console.error('Language auto-detection error:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Get language preference history
  const getLanguageHistory = useCallback(async (limit: number = 50, offset: number = 0) => {
    try {
      const response = await api.get(`/api/v1/language/preferences/history?limit=${limit}&offset=${offset}`);
      return response.data;
    } catch (err) {
      console.error('Failed to fetch language history:', err);
      return null;
    }
  }, []);

  // Initialize on mount
  useEffect(() => {
    fetchLanguageInfo();
  }, [fetchLanguageInfo]);

  // Listen for language changes from other tabs/windows
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'language_changed') {
        fetchLanguageInfo();
      }
    };

    const handleLanguageChange = () => {
      fetchLanguageInfo();
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('languagechange', handleLanguageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('languagechange', handleLanguageChange);
    };
  }, [fetchLanguageInfo]);

  return {
    currentLanguage,
    languageInfo,
    supportedLanguages,
    availableTranslations,
    isLoading,
    error,
    switchLanguage,
    getNamespaceTranslations,
    getDetectionInfo,
    getTranslationStats,
    validateTranslations,
    reloadTranslations,
    getMissingTranslations,
    updateLanguagePreferences,
    getLanguagePreferences,
    autoDetectLanguage,
    getLanguageHistory,
    refreshLanguageInfo: fetchLanguageInfo,
    // Admin functions
    getTranslationKeys,
    updateTranslationKey,
    createTranslationKey,
    deleteTranslationKey,
    exportTranslations
  };
};