"""
Pricing and commission models for platform revenue management.
"""

from datetime import datetime, date
from typing import List, Optional, Dict, Any
from decimal import Decimal
from pydantic import BaseModel, Field, field_validator, ConfigDict

from app.models.base import (
    BaseEntity,
    IdentifiedEntity,
    SearchFilters
)


class PricingRule(IdentifiedEntity):
    """Pricing rule for services."""
    
    rule_id: int = Field(..., description="Unique pricing rule identifier")
    rule_name: str = Field(..., description="Rule name")
    rule_code: str = Field(..., description="Unique rule code")
    description: Optional[str] = Field(None, description="Rule description")
    service_type: str = Field(..., description="Service type (online, in-person, etc)")
    subject_area: Optional[str] = Field(None, description="Specific subject area")
    location_type: Optional[str] = Field(None, description="Location type for in-person")
    base_client_rate: Decimal = Field(..., description="Base rate charged to clients")
    base_tutor_rate: Decimal = Field(..., description="Base rate paid to tutors")
    commission_percentage: Decimal = Field(..., description="Platform commission percentage")
    commission_amount: Decimal = Field(..., description="Platform commission amount per hour")
    priority: int = Field(default=100, description="Rule priority (higher = more important)")
    is_active: bool = Field(default=True, description="Whether rule is active")
    effective_date: date = Field(..., description="When rule becomes effective")
    expiry_date: Optional[date] = Field(None, description="When rule expires")
    
    @field_validator('base_client_rate', 'base_tutor_rate')
    @classmethod
    def validate_rates(cls, v: Decimal) -> Decimal:
        """Validate rates are positive."""
        if v <= 0:
            raise ValueError('Rate must be positive')
        return v
    
    @field_validator('commission_percentage')
    @classmethod
    def validate_commission(cls, v: Decimal) -> Decimal:
        """Validate commission percentage."""
        if v < 0 or v > 100:
            raise ValueError('Commission percentage must be between 0 and 100')
        return v


class ServicePricing(IdentifiedEntity):
    """Custom pricing for specific services."""
    
    pricing_id: int = Field(..., description="Unique pricing identifier")
    tutor_id: Optional[int] = Field(None, description="Tutor with custom pricing")
    client_id: Optional[int] = Field(None, description="Client with negotiated pricing")
    service_type: str = Field(..., description="Service type")
    subject_area: Optional[str] = Field(None, description="Subject area")
    custom_client_rate: Optional[Decimal] = Field(None, description="Custom client rate")
    custom_tutor_rate: Optional[Decimal] = Field(None, description="Custom tutor rate")
    rate_type: str = Field(..., description="Type of custom rate (client_rate, tutor_rate)")
    commission_override_percentage: Optional[Decimal] = Field(None, description="Override commission %")
    is_active: bool = Field(default=True, description="Whether pricing is active")
    effective_date: date = Field(..., description="When pricing becomes effective")
    expiry_date: Optional[date] = Field(None, description="When pricing expires")
    approved_by: Optional[int] = Field(None, description="Manager who approved custom rate")
    notes: Optional[str] = Field(None, description="Pricing notes")


class CommissionStructure(IdentifiedEntity):
    """Commission structure with performance tiers."""
    
    structure_id: int = Field(..., description="Unique structure identifier")
    structure_name: str = Field(..., description="Structure name")
    description: Optional[str] = Field(None, description="Structure description")
    base_commission_rate: Decimal = Field(..., description="Base commission rate")
    tiers: List['CommissionTier'] = Field(default_factory=list, description="Commission tiers")
    is_active: bool = Field(default=True, description="Whether structure is active")


class CommissionTier(BaseModel):
    """Commission tier based on performance."""
    
    model_config = ConfigDict(from_attributes=True)
    
    tier_id: int = Field(..., description="Unique tier identifier")
    structure_id: int = Field(..., description="Parent structure ID")
    tier_name: str = Field(..., description="Tier name")
    min_monthly_revenue: Decimal = Field(..., description="Minimum monthly revenue for tier")
    max_monthly_revenue: Optional[Decimal] = Field(None, description="Maximum monthly revenue")
    commission_rate: Decimal = Field(..., description="Commission rate for this tier")
    
    @field_validator('commission_rate')
    @classmethod
    def validate_rate(cls, v: Decimal) -> Decimal:
        """Validate commission rate."""
        if v < 0 or v > 1:
            raise ValueError('Commission rate must be between 0 and 1')
        return v


# ============================================
# Create/Update Schemas
# ============================================

class PricingRuleCreate(BaseModel):
    """Schema for creating pricing rule."""
    
    model_config = ConfigDict(from_attributes=True)
    
    rule_name: str = Field(..., min_length=1, max_length=100)
    rule_code: str = Field(..., min_length=1, max_length=50)
    description: Optional[str] = Field(None, max_length=500)
    service_type: str = Field(..., min_length=1, max_length=50)
    subject_area: Optional[str] = Field(None, max_length=100)
    location_type: Optional[str] = Field(None, max_length=50)
    base_client_rate: Decimal = Field(..., gt=0)
    base_tutor_rate: Decimal = Field(..., gt=0)
    priority: Optional[int] = Field(100, ge=0, le=1000)
    effective_date: Optional[date] = Field(None)
    expiry_date: Optional[date] = Field(None)
    
    @field_validator('base_tutor_rate')
    @classmethod
    def validate_tutor_rate(cls, v: Decimal, info) -> Decimal:
        """Validate tutor rate is less than client rate."""
        if 'base_client_rate' in info.data and v >= info.data['base_client_rate']:
            raise ValueError('Tutor rate must be less than client rate')
        return v


class PricingRuleUpdate(BaseModel):
    """Schema for updating pricing rule."""
    
    model_config = ConfigDict(from_attributes=True)
    
    rule_name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    base_client_rate: Optional[Decimal] = Field(None, gt=0)
    base_tutor_rate: Optional[Decimal] = Field(None, gt=0)
    priority: Optional[int] = Field(None, ge=0, le=1000)
    is_active: Optional[bool] = Field(None)
    expiry_date: Optional[date] = Field(None)


class ServicePricingCreate(BaseModel):
    """Schema for creating custom service pricing."""
    
    model_config = ConfigDict(from_attributes=True)
    
    tutor_id: Optional[int] = Field(None)
    client_id: Optional[int] = Field(None)
    service_type: str = Field(..., min_length=1, max_length=50)
    subject_area: Optional[str] = Field(None, max_length=100)
    custom_client_rate: Optional[Decimal] = Field(None, gt=0)
    custom_tutor_rate: Optional[Decimal] = Field(None, gt=0)
    rate_type: str = Field(..., description="client_rate or tutor_rate")
    commission_override_percentage: Optional[Decimal] = Field(None, ge=0, le=100)
    effective_date: Optional[date] = Field(None)
    expiry_date: Optional[date] = Field(None)
    notes: Optional[str] = Field(None, max_length=500)


# ============================================
# Response/Calculation Schemas
# ============================================

class PricingCalculation(BaseModel):
    """Result of pricing calculation."""
    
    model_config = ConfigDict(from_attributes=True)
    
    service_type: str
    subject_area: str
    location_type: Optional[str]
    duration_hours: Decimal
    client_rate_per_hour: Decimal
    tutor_rate_per_hour: Decimal
    client_total_amount: Decimal
    tutor_total_amount: Decimal
    platform_commission_amount: Decimal
    platform_commission_percentage: Decimal
    surge_multiplier: Decimal = Field(default=Decimal("1.00"))
    pricing_rule_applied: str
    breakdown: Dict[str, Any]


class PlatformRevenue(BaseModel):
    """Platform revenue summary."""
    
    model_config = ConfigDict(from_attributes=True)
    
    period_start: date
    period_end: date
    gross_revenue: Decimal
    tutor_payments: Decimal
    platform_commission: Decimal
    net_revenue: Decimal
    average_commission_rate: Decimal
    session_count: int
    unique_clients: int
    active_tutors: int
    total_hours: Decimal
    revenue_by_service: Dict[str, Dict[str, Any]]
    revenue_by_subject: Dict[str, Dict[str, Any]]
    top_performing_tutors: List[Dict[str, Any]]


class PricingSearchFilters(SearchFilters):
    """Pricing-specific search filters."""
    
    service_type: Optional[str] = Field(None)
    subject_area: Optional[str] = Field(None)
    location_type: Optional[str] = Field(None)
    is_active: Optional[bool] = Field(None)
    effective_date: Optional[date] = Field(None)